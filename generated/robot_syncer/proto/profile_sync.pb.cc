// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: robot_syncer/proto/profile_sync.proto

#include "robot_syncer/proto/profile_sync.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace robot_syncer {
namespace profile_sync {
constexpr GetProfileSyncDataRequest::GetProfileSyncDataRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetProfileSyncDataRequestDefaultTypeInternal {
  constexpr GetProfileSyncDataRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfileSyncDataRequestDefaultTypeInternal() {}
  union {
    GetProfileSyncDataRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfileSyncDataRequestDefaultTypeInternal _GetProfileSyncDataRequest_default_instance_;
constexpr GetProfileSyncDataResponse_ProfilesEntry_DoNotUse::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetProfileSyncDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal {
  constexpr GetProfileSyncDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfileSyncDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetProfileSyncDataResponse_ProfilesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfileSyncDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal _GetProfileSyncDataResponse_ProfilesEntry_DoNotUse_default_instance_;
constexpr GetProfileSyncDataResponse::GetProfileSyncDataResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : profiles_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct GetProfileSyncDataResponseDefaultTypeInternal {
  constexpr GetProfileSyncDataResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfileSyncDataResponseDefaultTypeInternal() {}
  union {
    GetProfileSyncDataResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfileSyncDataResponseDefaultTypeInternal _GetProfileSyncDataResponse_default_instance_;
constexpr UploadProfileRequest::UploadProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , last_update_time_ms_(int64_t{0})
  , _oneof_case_{}{}
struct UploadProfileRequestDefaultTypeInternal {
  constexpr UploadProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UploadProfileRequestDefaultTypeInternal() {}
  union {
    UploadProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UploadProfileRequestDefaultTypeInternal _UploadProfileRequest_default_instance_;
constexpr GetProfileRequest::GetProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetProfileRequestDefaultTypeInternal {
  constexpr GetProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfileRequestDefaultTypeInternal() {}
  union {
    GetProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfileRequestDefaultTypeInternal _GetProfileRequest_default_instance_;
constexpr GetProfileResponse::GetProfileResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct GetProfileResponseDefaultTypeInternal {
  constexpr GetProfileResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfileResponseDefaultTypeInternal() {}
  union {
    GetProfileResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfileResponseDefaultTypeInternal _GetProfileResponse_default_instance_;
constexpr DeleteProfileRequest::DeleteProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DeleteProfileRequestDefaultTypeInternal {
  constexpr DeleteProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteProfileRequestDefaultTypeInternal() {}
  union {
    DeleteProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteProfileRequestDefaultTypeInternal _DeleteProfileRequest_default_instance_;
constexpr PurgeProfileRequest::PurgeProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct PurgeProfileRequestDefaultTypeInternal {
  constexpr PurgeProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PurgeProfileRequestDefaultTypeInternal() {}
  union {
    PurgeProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PurgeProfileRequestDefaultTypeInternal _PurgeProfileRequest_default_instance_;
}  // namespace profile_sync
}  // namespace robot_syncer
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[8];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto = nullptr;

const uint32_t TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest, robot_serial_),
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse, profiles_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::UploadProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::UploadProfileRequest, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::UploadProfileRequest, last_update_time_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::UploadProfileRequest, robot_serial_),
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::UploadProfileRequest, profile_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileRequest, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileResponse, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::GetProfileResponse, profile_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::DeleteProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::DeleteProfileRequest, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::PurgeProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::robot_syncer::profile_sync::PurgeProfileRequest, uuid_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest)},
  { 7, 15, -1, sizeof(::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse)},
  { 17, -1, -1, sizeof(::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse)},
  { 24, -1, -1, sizeof(::carbon::robot_syncer::profile_sync::UploadProfileRequest)},
  { 41, -1, -1, sizeof(::carbon::robot_syncer::profile_sync::GetProfileRequest)},
  { 48, -1, -1, sizeof(::carbon::robot_syncer::profile_sync::GetProfileResponse)},
  { 63, -1, -1, sizeof(::carbon::robot_syncer::profile_sync::DeleteProfileRequest)},
  { 70, -1, -1, sizeof(::carbon::robot_syncer::profile_sync::PurgeProfileRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::robot_syncer::profile_sync::_GetProfileSyncDataRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::robot_syncer::profile_sync::_GetProfileSyncDataResponse_ProfilesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::robot_syncer::profile_sync::_GetProfileSyncDataResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::robot_syncer::profile_sync::_UploadProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::robot_syncer::profile_sync::_GetProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::robot_syncer::profile_sync::_GetProfileResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::robot_syncer::profile_sync::_DeleteProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::robot_syncer::profile_sync::_PurgeProfileRequest_default_instance_),
};

const char descriptor_table_protodef_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n%robot_syncer/proto/profile_sync.proto\022"
  " carbon.robot_syncer.profile_sync\032\027porta"
  "l/proto/util.proto\032!frontend/proto/profi"
  "le_sync.proto\032\033proto/almanac/almanac.pro"
  "to\032\035proto/thinning/thinning.proto\032\034front"
  "end/proto/banding.proto\032\?proto/target_ve"
  "locity_estimator/target_velocity_estimat"
  "or.proto\032\035category/proto/category.proto\""
  "1\n\031GetProfileSyncDataRequest\022\024\n\014robot_se"
  "rial\030\001 \001(\t\"\332\001\n\032GetProfileSyncDataRespons"
  "e\022\\\n\010profiles\030\001 \003(\0132J.carbon.robot_synce"
  "r.profile_sync.GetProfileSyncDataRespons"
  "e.ProfilesEntry\032^\n\rProfilesEntry\022\013\n\003key\030"
  "\001 \001(\t\022<\n\005value\030\002 \001(\0132-.carbon.frontend.p"
  "rofile_sync.ProfileSyncData:\0028\001\"\316\004\n\024Uplo"
  "adProfileRequest\022\033\n\023last_update_time_ms\030"
  "\001 \001(\003\022\024\n\014robot_serial\030\002 \001(\t\0227\n\007almanac\030\003"
  " \001(\0132$.carbon.aimbot.almanac.AlmanacConf"
  "igH\000\022C\n\rdiscriminator\030\004 \001(\0132*.carbon.aim"
  "bot.almanac.DiscriminatorConfigH\000\022\?\n\013mod"
  "elinator\030\005 \001(\0132(.carbon.aimbot.almanac.M"
  "odelinatorConfigH\000\0226\n\007banding\030\006 \001(\0132#.ca"
  "rbon.frontend.banding.BandingDefH\000\0225\n\010th"
  "inning\030\007 \001(\0132!.carbon.thinning.ConfigDef"
  "initionH\000\022X\n\031target_velocity_estimator\030\010"
  " \001(\01323.carbon.aimbot.target_velocity_est"
  "imator.TVEProfileH\000\022A\n\022categoryCollectio"
  "n\030\t \001(\0132#.carbon.category.CategoryCollec"
  "tionH\000\022-\n\010category\030\n \001(\0132\031.carbon.catego"
  "ry.CategoryH\000B\t\n\007profile\"!\n\021GetProfileRe"
  "quest\022\014\n\004uuid\030\001 \001(\t\"\231\004\n\022GetProfileRespon"
  "se\0227\n\007almanac\030\001 \001(\0132$.carbon.aimbot.alma"
  "nac.AlmanacConfigH\000\022C\n\rdiscriminator\030\002 \001"
  "(\0132*.carbon.aimbot.almanac.Discriminator"
  "ConfigH\000\022\?\n\013modelinator\030\003 \001(\0132(.carbon.a"
  "imbot.almanac.ModelinatorConfigH\000\0226\n\007ban"
  "ding\030\004 \001(\0132#.carbon.frontend.banding.Ban"
  "dingDefH\000\0225\n\010thinning\030\005 \001(\0132!.carbon.thi"
  "nning.ConfigDefinitionH\000\022X\n\031target_veloc"
  "ity_estimator\030\006 \001(\01323.carbon.aimbot.targ"
  "et_velocity_estimator.TVEProfileH\000\022A\n\022ca"
  "tegoryCollection\030\007 \001(\0132#.carbon.category"
  ".CategoryCollectionH\000\022-\n\010category\030\010 \001(\0132"
  "\031.carbon.category.CategoryH\000B\t\n\007profile\""
  "$\n\024DeleteProfileRequest\022\014\n\004uuid\030\001 \001(\t\"#\n"
  "\023PurgeProfileRequest\022\014\n\004uuid\030\001 \001(\t2\315\004\n\026R"
  "oSyProfileSyncService\022\217\001\n\022GetProfileSync"
  "Data\022;.carbon.robot_syncer.profile_sync."
  "GetProfileSyncDataRequest\032<.carbon.robot"
  "_syncer.profile_sync.GetProfileSyncDataR"
  "esponse\022b\n\rUploadProfile\0226.carbon.robot_"
  "syncer.profile_sync.UploadProfileRequest"
  "\032\031.carbon.portal.util.Empty\022w\n\nGetProfil"
  "e\0223.carbon.robot_syncer.profile_sync.Get"
  "ProfileRequest\0324.carbon.robot_syncer.pro"
  "file_sync.GetProfileResponse\022b\n\rDeletePr"
  "ofile\0226.carbon.robot_syncer.profile_sync"
  ".DeleteProfileRequest\032\031.carbon.portal.ut"
  "il.Empty\022`\n\014PurgeProfile\0225.carbon.robot_"
  "syncer.profile_sync.PurgeProfileRequest\032"
  "\031.carbon.portal.util.EmptyB\024Z\022proto/robo"
  "t_syncerb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_deps[7] = {
  &::descriptor_table_category_2fproto_2fcategory_2eproto,
  &::descriptor_table_frontend_2fproto_2fbanding_2eproto,
  &::descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto,
  &::descriptor_table_portal_2fproto_2futil_2eproto,
  &::descriptor_table_proto_2falmanac_2falmanac_2eproto,
  &::descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto,
  &::descriptor_table_proto_2fthinning_2fthinning_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto = {
  false, false, 2456, descriptor_table_protodef_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto, "robot_syncer/proto/profile_sync.proto", 
  &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once, descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_deps, 7, 8,
  schemas, file_default_instances, TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto::offsets,
  file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto, file_level_enum_descriptors_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto, file_level_service_descriptors_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_getter() {
  return &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto(&descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto);
namespace carbon {
namespace robot_syncer {
namespace profile_sync {

// ===================================================================

class GetProfileSyncDataRequest::_Internal {
 public:
};

GetProfileSyncDataRequest::GetProfileSyncDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
}
GetProfileSyncDataRequest::GetProfileSyncDataRequest(const GetProfileSyncDataRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_serial().empty()) {
    robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_serial(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
}

inline void GetProfileSyncDataRequest::SharedCtor() {
robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetProfileSyncDataRequest::~GetProfileSyncDataRequest() {
  // @@protoc_insertion_point(destructor:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetProfileSyncDataRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetProfileSyncDataRequest::ArenaDtor(void* object) {
  GetProfileSyncDataRequest* _this = reinterpret_cast< GetProfileSyncDataRequest* >(object);
  (void)_this;
}
void GetProfileSyncDataRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetProfileSyncDataRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetProfileSyncDataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_serial_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetProfileSyncDataRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string robot_serial = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_robot_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest.robot_serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetProfileSyncDataRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string robot_serial = 1;
  if (!this->_internal_robot_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_serial().data(), static_cast<int>(this->_internal_robot_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest.robot_serial");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_robot_serial(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
  return target;
}

size_t GetProfileSyncDataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot_serial = 1;
  if (!this->_internal_robot_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_serial());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetProfileSyncDataRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetProfileSyncDataRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetProfileSyncDataRequest::GetClassData() const { return &_class_data_; }

void GetProfileSyncDataRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetProfileSyncDataRequest *>(to)->MergeFrom(
      static_cast<const GetProfileSyncDataRequest &>(from));
}


void GetProfileSyncDataRequest::MergeFrom(const GetProfileSyncDataRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot_serial().empty()) {
    _internal_set_robot_serial(from._internal_robot_serial());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetProfileSyncDataRequest::CopyFrom(const GetProfileSyncDataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProfileSyncDataRequest::IsInitialized() const {
  return true;
}

void GetProfileSyncDataRequest::InternalSwap(GetProfileSyncDataRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_serial_, lhs_arena,
      &other->robot_serial_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetProfileSyncDataRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_getter, &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once,
      file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[0]);
}

// ===================================================================

GetProfileSyncDataResponse_ProfilesEntry_DoNotUse::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse() {}
GetProfileSyncDataResponse_ProfilesEntry_DoNotUse::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetProfileSyncDataResponse_ProfilesEntry_DoNotUse::MergeFrom(const GetProfileSyncDataResponse_ProfilesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetProfileSyncDataResponse_ProfilesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_getter, &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once,
      file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[1]);
}

// ===================================================================

class GetProfileSyncDataResponse::_Internal {
 public:
};

void GetProfileSyncDataResponse::clear_profiles() {
  profiles_.Clear();
}
GetProfileSyncDataResponse::GetProfileSyncDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  profiles_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
}
GetProfileSyncDataResponse::GetProfileSyncDataResponse(const GetProfileSyncDataResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  profiles_.MergeFrom(from.profiles_);
  // @@protoc_insertion_point(copy_constructor:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
}

inline void GetProfileSyncDataResponse::SharedCtor() {
}

GetProfileSyncDataResponse::~GetProfileSyncDataResponse() {
  // @@protoc_insertion_point(destructor:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetProfileSyncDataResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetProfileSyncDataResponse::ArenaDtor(void* object) {
  GetProfileSyncDataResponse* _this = reinterpret_cast< GetProfileSyncDataResponse* >(object);
  (void)_this;
  _this->profiles_. ~MapField();
}
inline void GetProfileSyncDataResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetProfileSyncDataResponse::ArenaDtor);
  }
}
void GetProfileSyncDataResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetProfileSyncDataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  profiles_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetProfileSyncDataResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&profiles_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetProfileSyncDataResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
  if (!this->_internal_profiles().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_profiles().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_profiles().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::const_iterator
          it = this->_internal_profiles().begin();
          it != this->_internal_profiles().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetProfileSyncDataResponse_ProfilesEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::const_iterator
          it = this->_internal_profiles().begin();
          it != this->_internal_profiles().end(); ++it) {
        target = GetProfileSyncDataResponse_ProfilesEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
  return target;
}

size_t GetProfileSyncDataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_profiles_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::const_iterator
      it = this->_internal_profiles().begin();
      it != this->_internal_profiles().end(); ++it) {
    total_size += GetProfileSyncDataResponse_ProfilesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetProfileSyncDataResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetProfileSyncDataResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetProfileSyncDataResponse::GetClassData() const { return &_class_data_; }

void GetProfileSyncDataResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetProfileSyncDataResponse *>(to)->MergeFrom(
      static_cast<const GetProfileSyncDataResponse &>(from));
}


void GetProfileSyncDataResponse::MergeFrom(const GetProfileSyncDataResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  profiles_.MergeFrom(from.profiles_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetProfileSyncDataResponse::CopyFrom(const GetProfileSyncDataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProfileSyncDataResponse::IsInitialized() const {
  return true;
}

void GetProfileSyncDataResponse::InternalSwap(GetProfileSyncDataResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  profiles_.InternalSwap(&other->profiles_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetProfileSyncDataResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_getter, &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once,
      file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[2]);
}

// ===================================================================

class UploadProfileRequest::_Internal {
 public:
  static const ::carbon::aimbot::almanac::AlmanacConfig& almanac(const UploadProfileRequest* msg);
  static const ::carbon::aimbot::almanac::DiscriminatorConfig& discriminator(const UploadProfileRequest* msg);
  static const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator(const UploadProfileRequest* msg);
  static const ::carbon::frontend::banding::BandingDef& banding(const UploadProfileRequest* msg);
  static const ::carbon::thinning::ConfigDefinition& thinning(const UploadProfileRequest* msg);
  static const ::carbon::aimbot::target_velocity_estimator::TVEProfile& target_velocity_estimator(const UploadProfileRequest* msg);
  static const ::carbon::category::CategoryCollection& categorycollection(const UploadProfileRequest* msg);
  static const ::carbon::category::Category& category(const UploadProfileRequest* msg);
};

const ::carbon::aimbot::almanac::AlmanacConfig&
UploadProfileRequest::_Internal::almanac(const UploadProfileRequest* msg) {
  return *msg->profile_.almanac_;
}
const ::carbon::aimbot::almanac::DiscriminatorConfig&
UploadProfileRequest::_Internal::discriminator(const UploadProfileRequest* msg) {
  return *msg->profile_.discriminator_;
}
const ::carbon::aimbot::almanac::ModelinatorConfig&
UploadProfileRequest::_Internal::modelinator(const UploadProfileRequest* msg) {
  return *msg->profile_.modelinator_;
}
const ::carbon::frontend::banding::BandingDef&
UploadProfileRequest::_Internal::banding(const UploadProfileRequest* msg) {
  return *msg->profile_.banding_;
}
const ::carbon::thinning::ConfigDefinition&
UploadProfileRequest::_Internal::thinning(const UploadProfileRequest* msg) {
  return *msg->profile_.thinning_;
}
const ::carbon::aimbot::target_velocity_estimator::TVEProfile&
UploadProfileRequest::_Internal::target_velocity_estimator(const UploadProfileRequest* msg) {
  return *msg->profile_.target_velocity_estimator_;
}
const ::carbon::category::CategoryCollection&
UploadProfileRequest::_Internal::categorycollection(const UploadProfileRequest* msg) {
  return *msg->profile_.categorycollection_;
}
const ::carbon::category::Category&
UploadProfileRequest::_Internal::category(const UploadProfileRequest* msg) {
  return *msg->profile_.category_;
}
void UploadProfileRequest::set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (almanac) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(almanac));
    if (message_arena != submessage_arena) {
      almanac = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, almanac, submessage_arena);
    }
    set_has_almanac();
    profile_.almanac_ = almanac;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.almanac)
}
void UploadProfileRequest::clear_almanac() {
  if (_internal_has_almanac()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.almanac_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (discriminator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(discriminator));
    if (message_arena != submessage_arena) {
      discriminator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, discriminator, submessage_arena);
    }
    set_has_discriminator();
    profile_.discriminator_ = discriminator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.discriminator)
}
void UploadProfileRequest::clear_discriminator() {
  if (_internal_has_discriminator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.discriminator_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (modelinator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator));
    if (message_arena != submessage_arena) {
      modelinator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, modelinator, submessage_arena);
    }
    set_has_modelinator();
    profile_.modelinator_ = modelinator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.modelinator)
}
void UploadProfileRequest::clear_modelinator() {
  if (_internal_has_modelinator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.modelinator_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_banding(::carbon::frontend::banding::BandingDef* banding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (banding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(banding));
    if (message_arena != submessage_arena) {
      banding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, banding, submessage_arena);
    }
    set_has_banding();
    profile_.banding_ = banding;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.banding)
}
void UploadProfileRequest::clear_banding() {
  if (_internal_has_banding()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.banding_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (thinning) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(thinning));
    if (message_arena != submessage_arena) {
      thinning = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, thinning, submessage_arena);
    }
    set_has_thinning();
    profile_.thinning_ = thinning;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.thinning)
}
void UploadProfileRequest::clear_thinning() {
  if (_internal_has_thinning()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.thinning_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_target_velocity_estimator(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (target_velocity_estimator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(target_velocity_estimator));
    if (message_arena != submessage_arena) {
      target_velocity_estimator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, target_velocity_estimator, submessage_arena);
    }
    set_has_target_velocity_estimator();
    profile_.target_velocity_estimator_ = target_velocity_estimator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.target_velocity_estimator)
}
void UploadProfileRequest::clear_target_velocity_estimator() {
  if (_internal_has_target_velocity_estimator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.target_velocity_estimator_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_categorycollection(::carbon::category::CategoryCollection* categorycollection) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (categorycollection) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(categorycollection));
    if (message_arena != submessage_arena) {
      categorycollection = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, categorycollection, submessage_arena);
    }
    set_has_categorycollection();
    profile_.categorycollection_ = categorycollection;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.categoryCollection)
}
void UploadProfileRequest::clear_categorycollection() {
  if (_internal_has_categorycollection()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.categorycollection_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_category(::carbon::category::Category* category) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (category) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(category));
    if (message_arena != submessage_arena) {
      category = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, category, submessage_arena);
    }
    set_has_category();
    profile_.category_ = category;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.category)
}
void UploadProfileRequest::clear_category() {
  if (_internal_has_category()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.category_;
    }
    clear_has_profile();
  }
}
UploadProfileRequest::UploadProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.robot_syncer.profile_sync.UploadProfileRequest)
}
UploadProfileRequest::UploadProfileRequest(const UploadProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_serial().empty()) {
    robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_serial(), 
      GetArenaForAllocation());
  }
  last_update_time_ms_ = from.last_update_time_ms_;
  clear_has_profile();
  switch (from.profile_case()) {
    case kAlmanac: {
      _internal_mutable_almanac()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_almanac());
      break;
    }
    case kDiscriminator: {
      _internal_mutable_discriminator()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_discriminator());
      break;
    }
    case kModelinator: {
      _internal_mutable_modelinator()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator());
      break;
    }
    case kBanding: {
      _internal_mutable_banding()->::carbon::frontend::banding::BandingDef::MergeFrom(from._internal_banding());
      break;
    }
    case kThinning: {
      _internal_mutable_thinning()->::carbon::thinning::ConfigDefinition::MergeFrom(from._internal_thinning());
      break;
    }
    case kTargetVelocityEstimator: {
      _internal_mutable_target_velocity_estimator()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_target_velocity_estimator());
      break;
    }
    case kCategoryCollection: {
      _internal_mutable_categorycollection()->::carbon::category::CategoryCollection::MergeFrom(from._internal_categorycollection());
      break;
    }
    case kCategory: {
      _internal_mutable_category()->::carbon::category::Category::MergeFrom(from._internal_category());
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.robot_syncer.profile_sync.UploadProfileRequest)
}

inline void UploadProfileRequest::SharedCtor() {
robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
last_update_time_ms_ = int64_t{0};
clear_has_profile();
}

UploadProfileRequest::~UploadProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.robot_syncer.profile_sync.UploadProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UploadProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (has_profile()) {
    clear_profile();
  }
}

void UploadProfileRequest::ArenaDtor(void* object) {
  UploadProfileRequest* _this = reinterpret_cast< UploadProfileRequest* >(object);
  (void)_this;
}
void UploadProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UploadProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UploadProfileRequest::clear_profile() {
// @@protoc_insertion_point(one_of_clear_start:carbon.robot_syncer.profile_sync.UploadProfileRequest)
  switch (profile_case()) {
    case kAlmanac: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.almanac_;
      }
      break;
    }
    case kDiscriminator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.discriminator_;
      }
      break;
    }
    case kModelinator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.modelinator_;
      }
      break;
    }
    case kBanding: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.banding_;
      }
      break;
    }
    case kThinning: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.thinning_;
      }
      break;
    }
    case kTargetVelocityEstimator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.target_velocity_estimator_;
      }
      break;
    }
    case kCategoryCollection: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.categorycollection_;
      }
      break;
    }
    case kCategory: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.category_;
      }
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = PROFILE_NOT_SET;
}


void UploadProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.robot_syncer.profile_sync.UploadProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_serial_.ClearToEmpty();
  last_update_time_ms_ = int64_t{0};
  clear_profile();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UploadProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 last_update_time_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          last_update_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string robot_serial = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_robot_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.robot_syncer.profile_sync.UploadProfileRequest.robot_serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_almanac(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_discriminator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_modelinator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.banding.BandingDef banding = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_banding(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.ConfigDefinition thinning = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_thinning(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_target_velocity_estimator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.category.CategoryCollection categoryCollection = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_categorycollection(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.category.Category category = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_category(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UploadProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.robot_syncer.profile_sync.UploadProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 last_update_time_ms = 1;
  if (this->_internal_last_update_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_last_update_time_ms(), target);
  }

  // string robot_serial = 2;
  if (!this->_internal_robot_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_serial().data(), static_cast<int>(this->_internal_robot_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.robot_syncer.profile_sync.UploadProfileRequest.robot_serial");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_robot_serial(), target);
  }

  // .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
  if (_internal_has_almanac()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::almanac(this), target, stream);
  }

  // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
  if (_internal_has_discriminator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::discriminator(this), target, stream);
  }

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
  if (_internal_has_modelinator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::modelinator(this), target, stream);
  }

  // .carbon.frontend.banding.BandingDef banding = 6;
  if (_internal_has_banding()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::banding(this), target, stream);
  }

  // .carbon.thinning.ConfigDefinition thinning = 7;
  if (_internal_has_thinning()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::thinning(this), target, stream);
  }

  // .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 8;
  if (_internal_has_target_velocity_estimator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::target_velocity_estimator(this), target, stream);
  }

  // .carbon.category.CategoryCollection categoryCollection = 9;
  if (_internal_has_categorycollection()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::categorycollection(this), target, stream);
  }

  // .carbon.category.Category category = 10;
  if (_internal_has_category()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::category(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.robot_syncer.profile_sync.UploadProfileRequest)
  return target;
}

size_t UploadProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.robot_syncer.profile_sync.UploadProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot_serial = 2;
  if (!this->_internal_robot_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_serial());
  }

  // int64 last_update_time_ms = 1;
  if (this->_internal_last_update_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_last_update_time_ms());
  }

  switch (profile_case()) {
    // .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
    case kAlmanac: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.almanac_);
      break;
    }
    // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
    case kDiscriminator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.discriminator_);
      break;
    }
    // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
    case kModelinator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.modelinator_);
      break;
    }
    // .carbon.frontend.banding.BandingDef banding = 6;
    case kBanding: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.banding_);
      break;
    }
    // .carbon.thinning.ConfigDefinition thinning = 7;
    case kThinning: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.thinning_);
      break;
    }
    // .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 8;
    case kTargetVelocityEstimator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.target_velocity_estimator_);
      break;
    }
    // .carbon.category.CategoryCollection categoryCollection = 9;
    case kCategoryCollection: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.categorycollection_);
      break;
    }
    // .carbon.category.Category category = 10;
    case kCategory: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.category_);
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UploadProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UploadProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UploadProfileRequest::GetClassData() const { return &_class_data_; }

void UploadProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UploadProfileRequest *>(to)->MergeFrom(
      static_cast<const UploadProfileRequest &>(from));
}


void UploadProfileRequest::MergeFrom(const UploadProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.robot_syncer.profile_sync.UploadProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot_serial().empty()) {
    _internal_set_robot_serial(from._internal_robot_serial());
  }
  if (from._internal_last_update_time_ms() != 0) {
    _internal_set_last_update_time_ms(from._internal_last_update_time_ms());
  }
  switch (from.profile_case()) {
    case kAlmanac: {
      _internal_mutable_almanac()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_almanac());
      break;
    }
    case kDiscriminator: {
      _internal_mutable_discriminator()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_discriminator());
      break;
    }
    case kModelinator: {
      _internal_mutable_modelinator()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator());
      break;
    }
    case kBanding: {
      _internal_mutable_banding()->::carbon::frontend::banding::BandingDef::MergeFrom(from._internal_banding());
      break;
    }
    case kThinning: {
      _internal_mutable_thinning()->::carbon::thinning::ConfigDefinition::MergeFrom(from._internal_thinning());
      break;
    }
    case kTargetVelocityEstimator: {
      _internal_mutable_target_velocity_estimator()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_target_velocity_estimator());
      break;
    }
    case kCategoryCollection: {
      _internal_mutable_categorycollection()->::carbon::category::CategoryCollection::MergeFrom(from._internal_categorycollection());
      break;
    }
    case kCategory: {
      _internal_mutable_category()->::carbon::category::Category::MergeFrom(from._internal_category());
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UploadProfileRequest::CopyFrom(const UploadProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.robot_syncer.profile_sync.UploadProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UploadProfileRequest::IsInitialized() const {
  return true;
}

void UploadProfileRequest::InternalSwap(UploadProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_serial_, lhs_arena,
      &other->robot_serial_, rhs_arena
  );
  swap(last_update_time_ms_, other->last_update_time_ms_);
  swap(profile_, other->profile_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata UploadProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_getter, &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once,
      file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[3]);
}

// ===================================================================

class GetProfileRequest::_Internal {
 public:
};

GetProfileRequest::GetProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.robot_syncer.profile_sync.GetProfileRequest)
}
GetProfileRequest::GetProfileRequest(const GetProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.robot_syncer.profile_sync.GetProfileRequest)
}

inline void GetProfileRequest::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetProfileRequest::~GetProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.robot_syncer.profile_sync.GetProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetProfileRequest::ArenaDtor(void* object) {
  GetProfileRequest* _this = reinterpret_cast< GetProfileRequest* >(object);
  (void)_this;
}
void GetProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.robot_syncer.profile_sync.GetProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.robot_syncer.profile_sync.GetProfileRequest.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.robot_syncer.profile_sync.GetProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.robot_syncer.profile_sync.GetProfileRequest.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.robot_syncer.profile_sync.GetProfileRequest)
  return target;
}

size_t GetProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.robot_syncer.profile_sync.GetProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetProfileRequest::GetClassData() const { return &_class_data_; }

void GetProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetProfileRequest *>(to)->MergeFrom(
      static_cast<const GetProfileRequest &>(from));
}


void GetProfileRequest::MergeFrom(const GetProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.robot_syncer.profile_sync.GetProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetProfileRequest::CopyFrom(const GetProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.robot_syncer.profile_sync.GetProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProfileRequest::IsInitialized() const {
  return true;
}

void GetProfileRequest::InternalSwap(GetProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_getter, &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once,
      file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[4]);
}

// ===================================================================

class GetProfileResponse::_Internal {
 public:
  static const ::carbon::aimbot::almanac::AlmanacConfig& almanac(const GetProfileResponse* msg);
  static const ::carbon::aimbot::almanac::DiscriminatorConfig& discriminator(const GetProfileResponse* msg);
  static const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator(const GetProfileResponse* msg);
  static const ::carbon::frontend::banding::BandingDef& banding(const GetProfileResponse* msg);
  static const ::carbon::thinning::ConfigDefinition& thinning(const GetProfileResponse* msg);
  static const ::carbon::aimbot::target_velocity_estimator::TVEProfile& target_velocity_estimator(const GetProfileResponse* msg);
  static const ::carbon::category::CategoryCollection& categorycollection(const GetProfileResponse* msg);
  static const ::carbon::category::Category& category(const GetProfileResponse* msg);
};

const ::carbon::aimbot::almanac::AlmanacConfig&
GetProfileResponse::_Internal::almanac(const GetProfileResponse* msg) {
  return *msg->profile_.almanac_;
}
const ::carbon::aimbot::almanac::DiscriminatorConfig&
GetProfileResponse::_Internal::discriminator(const GetProfileResponse* msg) {
  return *msg->profile_.discriminator_;
}
const ::carbon::aimbot::almanac::ModelinatorConfig&
GetProfileResponse::_Internal::modelinator(const GetProfileResponse* msg) {
  return *msg->profile_.modelinator_;
}
const ::carbon::frontend::banding::BandingDef&
GetProfileResponse::_Internal::banding(const GetProfileResponse* msg) {
  return *msg->profile_.banding_;
}
const ::carbon::thinning::ConfigDefinition&
GetProfileResponse::_Internal::thinning(const GetProfileResponse* msg) {
  return *msg->profile_.thinning_;
}
const ::carbon::aimbot::target_velocity_estimator::TVEProfile&
GetProfileResponse::_Internal::target_velocity_estimator(const GetProfileResponse* msg) {
  return *msg->profile_.target_velocity_estimator_;
}
const ::carbon::category::CategoryCollection&
GetProfileResponse::_Internal::categorycollection(const GetProfileResponse* msg) {
  return *msg->profile_.categorycollection_;
}
const ::carbon::category::Category&
GetProfileResponse::_Internal::category(const GetProfileResponse* msg) {
  return *msg->profile_.category_;
}
void GetProfileResponse::set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (almanac) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(almanac));
    if (message_arena != submessage_arena) {
      almanac = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, almanac, submessage_arena);
    }
    set_has_almanac();
    profile_.almanac_ = almanac;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.almanac)
}
void GetProfileResponse::clear_almanac() {
  if (_internal_has_almanac()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.almanac_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (discriminator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(discriminator));
    if (message_arena != submessage_arena) {
      discriminator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, discriminator, submessage_arena);
    }
    set_has_discriminator();
    profile_.discriminator_ = discriminator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.discriminator)
}
void GetProfileResponse::clear_discriminator() {
  if (_internal_has_discriminator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.discriminator_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (modelinator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator));
    if (message_arena != submessage_arena) {
      modelinator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, modelinator, submessage_arena);
    }
    set_has_modelinator();
    profile_.modelinator_ = modelinator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.modelinator)
}
void GetProfileResponse::clear_modelinator() {
  if (_internal_has_modelinator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.modelinator_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_banding(::carbon::frontend::banding::BandingDef* banding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (banding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(banding));
    if (message_arena != submessage_arena) {
      banding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, banding, submessage_arena);
    }
    set_has_banding();
    profile_.banding_ = banding;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.banding)
}
void GetProfileResponse::clear_banding() {
  if (_internal_has_banding()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.banding_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (thinning) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(thinning));
    if (message_arena != submessage_arena) {
      thinning = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, thinning, submessage_arena);
    }
    set_has_thinning();
    profile_.thinning_ = thinning;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.thinning)
}
void GetProfileResponse::clear_thinning() {
  if (_internal_has_thinning()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.thinning_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_target_velocity_estimator(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (target_velocity_estimator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(target_velocity_estimator));
    if (message_arena != submessage_arena) {
      target_velocity_estimator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, target_velocity_estimator, submessage_arena);
    }
    set_has_target_velocity_estimator();
    profile_.target_velocity_estimator_ = target_velocity_estimator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.target_velocity_estimator)
}
void GetProfileResponse::clear_target_velocity_estimator() {
  if (_internal_has_target_velocity_estimator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.target_velocity_estimator_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_categorycollection(::carbon::category::CategoryCollection* categorycollection) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (categorycollection) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(categorycollection));
    if (message_arena != submessage_arena) {
      categorycollection = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, categorycollection, submessage_arena);
    }
    set_has_categorycollection();
    profile_.categorycollection_ = categorycollection;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.categoryCollection)
}
void GetProfileResponse::clear_categorycollection() {
  if (_internal_has_categorycollection()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.categorycollection_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_category(::carbon::category::Category* category) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (category) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(category));
    if (message_arena != submessage_arena) {
      category = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, category, submessage_arena);
    }
    set_has_category();
    profile_.category_ = category;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.category)
}
void GetProfileResponse::clear_category() {
  if (_internal_has_category()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.category_;
    }
    clear_has_profile();
  }
}
GetProfileResponse::GetProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.robot_syncer.profile_sync.GetProfileResponse)
}
GetProfileResponse::GetProfileResponse(const GetProfileResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_profile();
  switch (from.profile_case()) {
    case kAlmanac: {
      _internal_mutable_almanac()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_almanac());
      break;
    }
    case kDiscriminator: {
      _internal_mutable_discriminator()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_discriminator());
      break;
    }
    case kModelinator: {
      _internal_mutable_modelinator()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator());
      break;
    }
    case kBanding: {
      _internal_mutable_banding()->::carbon::frontend::banding::BandingDef::MergeFrom(from._internal_banding());
      break;
    }
    case kThinning: {
      _internal_mutable_thinning()->::carbon::thinning::ConfigDefinition::MergeFrom(from._internal_thinning());
      break;
    }
    case kTargetVelocityEstimator: {
      _internal_mutable_target_velocity_estimator()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_target_velocity_estimator());
      break;
    }
    case kCategoryCollection: {
      _internal_mutable_categorycollection()->::carbon::category::CategoryCollection::MergeFrom(from._internal_categorycollection());
      break;
    }
    case kCategory: {
      _internal_mutable_category()->::carbon::category::Category::MergeFrom(from._internal_category());
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.robot_syncer.profile_sync.GetProfileResponse)
}

inline void GetProfileResponse::SharedCtor() {
clear_has_profile();
}

GetProfileResponse::~GetProfileResponse() {
  // @@protoc_insertion_point(destructor:carbon.robot_syncer.profile_sync.GetProfileResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetProfileResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_profile()) {
    clear_profile();
  }
}

void GetProfileResponse::ArenaDtor(void* object) {
  GetProfileResponse* _this = reinterpret_cast< GetProfileResponse* >(object);
  (void)_this;
}
void GetProfileResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetProfileResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetProfileResponse::clear_profile() {
// @@protoc_insertion_point(one_of_clear_start:carbon.robot_syncer.profile_sync.GetProfileResponse)
  switch (profile_case()) {
    case kAlmanac: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.almanac_;
      }
      break;
    }
    case kDiscriminator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.discriminator_;
      }
      break;
    }
    case kModelinator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.modelinator_;
      }
      break;
    }
    case kBanding: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.banding_;
      }
      break;
    }
    case kThinning: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.thinning_;
      }
      break;
    }
    case kTargetVelocityEstimator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.target_velocity_estimator_;
      }
      break;
    }
    case kCategoryCollection: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.categorycollection_;
      }
      break;
    }
    case kCategory: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.category_;
      }
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = PROFILE_NOT_SET;
}


void GetProfileResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.robot_syncer.profile_sync.GetProfileResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_profile();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetProfileResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_almanac(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_discriminator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_modelinator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.banding.BandingDef banding = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_banding(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.ConfigDefinition thinning = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_thinning(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_target_velocity_estimator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.category.CategoryCollection categoryCollection = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_categorycollection(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.category.Category category = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_category(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetProfileResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.robot_syncer.profile_sync.GetProfileResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
  if (_internal_has_almanac()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::almanac(this), target, stream);
  }

  // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
  if (_internal_has_discriminator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::discriminator(this), target, stream);
  }

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
  if (_internal_has_modelinator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::modelinator(this), target, stream);
  }

  // .carbon.frontend.banding.BandingDef banding = 4;
  if (_internal_has_banding()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::banding(this), target, stream);
  }

  // .carbon.thinning.ConfigDefinition thinning = 5;
  if (_internal_has_thinning()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::thinning(this), target, stream);
  }

  // .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 6;
  if (_internal_has_target_velocity_estimator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::target_velocity_estimator(this), target, stream);
  }

  // .carbon.category.CategoryCollection categoryCollection = 7;
  if (_internal_has_categorycollection()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::categorycollection(this), target, stream);
  }

  // .carbon.category.Category category = 8;
  if (_internal_has_category()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::category(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.robot_syncer.profile_sync.GetProfileResponse)
  return target;
}

size_t GetProfileResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.robot_syncer.profile_sync.GetProfileResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (profile_case()) {
    // .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
    case kAlmanac: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.almanac_);
      break;
    }
    // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
    case kDiscriminator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.discriminator_);
      break;
    }
    // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
    case kModelinator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.modelinator_);
      break;
    }
    // .carbon.frontend.banding.BandingDef banding = 4;
    case kBanding: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.banding_);
      break;
    }
    // .carbon.thinning.ConfigDefinition thinning = 5;
    case kThinning: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.thinning_);
      break;
    }
    // .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 6;
    case kTargetVelocityEstimator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.target_velocity_estimator_);
      break;
    }
    // .carbon.category.CategoryCollection categoryCollection = 7;
    case kCategoryCollection: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.categorycollection_);
      break;
    }
    // .carbon.category.Category category = 8;
    case kCategory: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.category_);
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetProfileResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetProfileResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetProfileResponse::GetClassData() const { return &_class_data_; }

void GetProfileResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetProfileResponse *>(to)->MergeFrom(
      static_cast<const GetProfileResponse &>(from));
}


void GetProfileResponse::MergeFrom(const GetProfileResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.robot_syncer.profile_sync.GetProfileResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.profile_case()) {
    case kAlmanac: {
      _internal_mutable_almanac()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_almanac());
      break;
    }
    case kDiscriminator: {
      _internal_mutable_discriminator()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_discriminator());
      break;
    }
    case kModelinator: {
      _internal_mutable_modelinator()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator());
      break;
    }
    case kBanding: {
      _internal_mutable_banding()->::carbon::frontend::banding::BandingDef::MergeFrom(from._internal_banding());
      break;
    }
    case kThinning: {
      _internal_mutable_thinning()->::carbon::thinning::ConfigDefinition::MergeFrom(from._internal_thinning());
      break;
    }
    case kTargetVelocityEstimator: {
      _internal_mutable_target_velocity_estimator()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_target_velocity_estimator());
      break;
    }
    case kCategoryCollection: {
      _internal_mutable_categorycollection()->::carbon::category::CategoryCollection::MergeFrom(from._internal_categorycollection());
      break;
    }
    case kCategory: {
      _internal_mutable_category()->::carbon::category::Category::MergeFrom(from._internal_category());
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetProfileResponse::CopyFrom(const GetProfileResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.robot_syncer.profile_sync.GetProfileResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProfileResponse::IsInitialized() const {
  return true;
}

void GetProfileResponse::InternalSwap(GetProfileResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(profile_, other->profile_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetProfileResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_getter, &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once,
      file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[5]);
}

// ===================================================================

class DeleteProfileRequest::_Internal {
 public:
};

DeleteProfileRequest::DeleteProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
}
DeleteProfileRequest::DeleteProfileRequest(const DeleteProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
}

inline void DeleteProfileRequest::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeleteProfileRequest::~DeleteProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeleteProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeleteProfileRequest::ArenaDtor(void* object) {
  DeleteProfileRequest* _this = reinterpret_cast< DeleteProfileRequest* >(object);
  (void)_this;
}
void DeleteProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeleteProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeleteProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeleteProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.robot_syncer.profile_sync.DeleteProfileRequest.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeleteProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.robot_syncer.profile_sync.DeleteProfileRequest.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
  return target;
}

size_t DeleteProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeleteProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteProfileRequest::GetClassData() const { return &_class_data_; }

void DeleteProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeleteProfileRequest *>(to)->MergeFrom(
      static_cast<const DeleteProfileRequest &>(from));
}


void DeleteProfileRequest::MergeFrom(const DeleteProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeleteProfileRequest::CopyFrom(const DeleteProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteProfileRequest::IsInitialized() const {
  return true;
}

void DeleteProfileRequest::InternalSwap(DeleteProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DeleteProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_getter, &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once,
      file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[6]);
}

// ===================================================================

class PurgeProfileRequest::_Internal {
 public:
};

PurgeProfileRequest::PurgeProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
}
PurgeProfileRequest::PurgeProfileRequest(const PurgeProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
}

inline void PurgeProfileRequest::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

PurgeProfileRequest::~PurgeProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PurgeProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PurgeProfileRequest::ArenaDtor(void* object) {
  PurgeProfileRequest* _this = reinterpret_cast< PurgeProfileRequest* >(object);
  (void)_this;
}
void PurgeProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PurgeProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PurgeProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PurgeProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.robot_syncer.profile_sync.PurgeProfileRequest.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PurgeProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.robot_syncer.profile_sync.PurgeProfileRequest.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
  return target;
}

size_t PurgeProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PurgeProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PurgeProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PurgeProfileRequest::GetClassData() const { return &_class_data_; }

void PurgeProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PurgeProfileRequest *>(to)->MergeFrom(
      static_cast<const PurgeProfileRequest &>(from));
}


void PurgeProfileRequest::MergeFrom(const PurgeProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PurgeProfileRequest::CopyFrom(const PurgeProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PurgeProfileRequest::IsInitialized() const {
  return true;
}

void PurgeProfileRequest::InternalSwap(PurgeProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata PurgeProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_getter, &descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto_once,
      file_level_metadata_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto[7]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace profile_sync
}  // namespace robot_syncer
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest* Arena::CreateMaybeMessage< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse* Arena::CreateMaybeMessage< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::robot_syncer::profile_sync::UploadProfileRequest* Arena::CreateMaybeMessage< ::carbon::robot_syncer::profile_sync::UploadProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::robot_syncer::profile_sync::UploadProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::robot_syncer::profile_sync::GetProfileRequest* Arena::CreateMaybeMessage< ::carbon::robot_syncer::profile_sync::GetProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::robot_syncer::profile_sync::GetProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::robot_syncer::profile_sync::GetProfileResponse* Arena::CreateMaybeMessage< ::carbon::robot_syncer::profile_sync::GetProfileResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::robot_syncer::profile_sync::GetProfileResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::robot_syncer::profile_sync::DeleteProfileRequest* Arena::CreateMaybeMessage< ::carbon::robot_syncer::profile_sync::DeleteProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::robot_syncer::profile_sync::DeleteProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::robot_syncer::profile_sync::PurgeProfileRequest* Arena::CreateMaybeMessage< ::carbon::robot_syncer::profile_sync::PurgeProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::robot_syncer::profile_sync::PurgeProfileRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: golang/simulator/hardware/proto/sim/sim.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto;
namespace carbon {
namespace simulator {
class Empty;
struct EmptyDefaultTypeInternal;
extern EmptyDefaultTypeInternal _Empty_default_instance_;
class GetNextPredictionsRequest;
struct GetNextPredictionsRequestDefaultTypeInternal;
extern GetNextPredictionsRequestDefaultTypeInternal _GetNextPredictionsRequest_default_instance_;
class GetNextPredictionsResponse;
struct GetNextPredictionsResponseDefaultTypeInternal;
extern GetNextPredictionsResponseDefaultTypeInternal _GetNextPredictionsResponse_default_instance_;
class Prediction;
struct PredictionDefaultTypeInternal;
extern PredictionDefaultTypeInternal _Prediction_default_instance_;
class Prediction_DetectionClassesEntry_DoNotUse;
struct Prediction_DetectionClassesEntry_DoNotUseDefaultTypeInternal;
extern Prediction_DetectionClassesEntry_DoNotUseDefaultTypeInternal _Prediction_DetectionClassesEntry_DoNotUse_default_instance_;
class Prediction_WeedDetectionClassesEntry_DoNotUse;
struct Prediction_WeedDetectionClassesEntry_DoNotUseDefaultTypeInternal;
extern Prediction_WeedDetectionClassesEntry_DoNotUseDefaultTypeInternal _Prediction_WeedDetectionClassesEntry_DoNotUse_default_instance_;
}  // namespace simulator
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::simulator::Empty* Arena::CreateMaybeMessage<::carbon::simulator::Empty>(Arena*);
template<> ::carbon::simulator::GetNextPredictionsRequest* Arena::CreateMaybeMessage<::carbon::simulator::GetNextPredictionsRequest>(Arena*);
template<> ::carbon::simulator::GetNextPredictionsResponse* Arena::CreateMaybeMessage<::carbon::simulator::GetNextPredictionsResponse>(Arena*);
template<> ::carbon::simulator::Prediction* Arena::CreateMaybeMessage<::carbon::simulator::Prediction>(Arena*);
template<> ::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse>(Arena*);
template<> ::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace simulator {

// ===================================================================

class Empty final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.simulator.Empty) */ {
 public:
  inline Empty() : Empty(nullptr) {}
  explicit constexpr Empty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Empty(const Empty& from);
  Empty(Empty&& from) noexcept
    : Empty() {
    *this = ::std::move(from);
  }

  inline Empty& operator=(const Empty& from) {
    CopyFrom(from);
    return *this;
  }
  inline Empty& operator=(Empty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Empty& default_instance() {
    return *internal_default_instance();
  }
  static inline const Empty* internal_default_instance() {
    return reinterpret_cast<const Empty*>(
               &_Empty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Empty& a, Empty& b) {
    a.Swap(&b);
  }
  inline void Swap(Empty* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Empty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Empty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Empty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const Empty& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const Empty& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator.Empty";
  }
  protected:
  explicit Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.simulator.Empty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto;
};
// -------------------------------------------------------------------

class GetNextPredictionsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator.GetNextPredictionsRequest) */ {
 public:
  inline GetNextPredictionsRequest() : GetNextPredictionsRequest(nullptr) {}
  ~GetNextPredictionsRequest() override;
  explicit constexpr GetNextPredictionsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextPredictionsRequest(const GetNextPredictionsRequest& from);
  GetNextPredictionsRequest(GetNextPredictionsRequest&& from) noexcept
    : GetNextPredictionsRequest() {
    *this = ::std::move(from);
  }

  inline GetNextPredictionsRequest& operator=(const GetNextPredictionsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextPredictionsRequest& operator=(GetNextPredictionsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextPredictionsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextPredictionsRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextPredictionsRequest*>(
               &_GetNextPredictionsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetNextPredictionsRequest& a, GetNextPredictionsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextPredictionsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextPredictionsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextPredictionsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextPredictionsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextPredictionsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextPredictionsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextPredictionsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator.GetNextPredictionsRequest";
  }
  protected:
  explicit GetNextPredictionsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kTimestampMsFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int64 timestamp_ms = 2;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.simulator.GetNextPredictionsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int64_t timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto;
};
// -------------------------------------------------------------------

class Prediction_DetectionClassesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Prediction_DetectionClassesEntry_DoNotUse, 
    std::string, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Prediction_DetectionClassesEntry_DoNotUse, 
    std::string, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> SuperType;
  Prediction_DetectionClassesEntry_DoNotUse();
  explicit constexpr Prediction_DetectionClassesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit Prediction_DetectionClassesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const Prediction_DetectionClassesEntry_DoNotUse& other);
  static const Prediction_DetectionClassesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Prediction_DetectionClassesEntry_DoNotUse*>(&_Prediction_DetectionClassesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.simulator.Prediction.DetectionClassesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class Prediction_WeedDetectionClassesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Prediction_WeedDetectionClassesEntry_DoNotUse, 
    std::string, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Prediction_WeedDetectionClassesEntry_DoNotUse, 
    std::string, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> SuperType;
  Prediction_WeedDetectionClassesEntry_DoNotUse();
  explicit constexpr Prediction_WeedDetectionClassesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit Prediction_WeedDetectionClassesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const Prediction_WeedDetectionClassesEntry_DoNotUse& other);
  static const Prediction_WeedDetectionClassesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Prediction_WeedDetectionClassesEntry_DoNotUse*>(&_Prediction_WeedDetectionClassesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.simulator.Prediction.WeedDetectionClassesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class Prediction final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator.Prediction) */ {
 public:
  inline Prediction() : Prediction(nullptr) {}
  ~Prediction() override;
  explicit constexpr Prediction(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Prediction(const Prediction& from);
  Prediction(Prediction&& from) noexcept
    : Prediction() {
    *this = ::std::move(from);
  }

  inline Prediction& operator=(const Prediction& from) {
    CopyFrom(from);
    return *this;
  }
  inline Prediction& operator=(Prediction&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Prediction& default_instance() {
    return *internal_default_instance();
  }
  static inline const Prediction* internal_default_instance() {
    return reinterpret_cast<const Prediction*>(
               &_Prediction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Prediction& a, Prediction& b) {
    a.Swap(&b);
  }
  inline void Swap(Prediction* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Prediction* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Prediction* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Prediction>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Prediction& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Prediction& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Prediction* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator.Prediction";
  }
  protected:
  explicit Prediction(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kDetectionClassesFieldNumber = 5,
    kWeedDetectionClassesFieldNumber = 11,
    kEmbeddingFieldNumber = 12,
    kMaskIntersectionClassesFieldNumber = 14,
    kTypeFieldNumber = 10,
    kXPxFieldNumber = 1,
    kYPxFieldNumber = 2,
    kSizePxFieldNumber = 3,
    kScoreFieldNumber = 4,
    kIsWeedFieldNumber = 6,
    kIsRealFieldNumber = 7,
    kWeedScoreFieldNumber = 8,
    kCropScoreFieldNumber = 9,
    kPlantScoreFieldNumber = 13,
  };
  // map<string, float> detection_classes = 5;
  int detection_classes_size() const;
  private:
  int _internal_detection_classes_size() const;
  public:
  void clear_detection_classes();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
      _internal_detection_classes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
      _internal_mutable_detection_classes();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
      detection_classes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
      mutable_detection_classes();

  // map<string, float> weed_detection_classes = 11;
  int weed_detection_classes_size() const;
  private:
  int _internal_weed_detection_classes_size() const;
  public:
  void clear_weed_detection_classes();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
      _internal_weed_detection_classes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
      _internal_mutable_weed_detection_classes();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
      weed_detection_classes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
      mutable_weed_detection_classes();

  // repeated float embedding = 12;
  int embedding_size() const;
  private:
  int _internal_embedding_size() const;
  public:
  void clear_embedding();
  private:
  float _internal_embedding(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_embedding() const;
  void _internal_add_embedding(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_embedding();
  public:
  float embedding(int index) const;
  void set_embedding(int index, float value);
  void add_embedding(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      embedding() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_embedding();

  // repeated string mask_intersection_classes = 14;
  int mask_intersection_classes_size() const;
  private:
  int _internal_mask_intersection_classes_size() const;
  public:
  void clear_mask_intersection_classes();
  const std::string& mask_intersection_classes(int index) const;
  std::string* mutable_mask_intersection_classes(int index);
  void set_mask_intersection_classes(int index, const std::string& value);
  void set_mask_intersection_classes(int index, std::string&& value);
  void set_mask_intersection_classes(int index, const char* value);
  void set_mask_intersection_classes(int index, const char* value, size_t size);
  std::string* add_mask_intersection_classes();
  void add_mask_intersection_classes(const std::string& value);
  void add_mask_intersection_classes(std::string&& value);
  void add_mask_intersection_classes(const char* value);
  void add_mask_intersection_classes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& mask_intersection_classes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_mask_intersection_classes();
  private:
  const std::string& _internal_mask_intersection_classes(int index) const;
  std::string* _internal_add_mask_intersection_classes();
  public:

  // string type = 10;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // uint32 x_px = 1;
  void clear_x_px();
  uint32_t x_px() const;
  void set_x_px(uint32_t value);
  private:
  uint32_t _internal_x_px() const;
  void _internal_set_x_px(uint32_t value);
  public:

  // uint32 y_px = 2;
  void clear_y_px();
  uint32_t y_px() const;
  void set_y_px(uint32_t value);
  private:
  uint32_t _internal_y_px() const;
  void _internal_set_y_px(uint32_t value);
  public:

  // uint32 size_px = 3;
  void clear_size_px();
  uint32_t size_px() const;
  void set_size_px(uint32_t value);
  private:
  uint32_t _internal_size_px() const;
  void _internal_set_size_px(uint32_t value);
  public:

  // float score = 4;
  void clear_score();
  float score() const;
  void set_score(float value);
  private:
  float _internal_score() const;
  void _internal_set_score(float value);
  public:

  // bool is_weed = 6;
  void clear_is_weed();
  bool is_weed() const;
  void set_is_weed(bool value);
  private:
  bool _internal_is_weed() const;
  void _internal_set_is_weed(bool value);
  public:

  // bool is_real = 7;
  void clear_is_real();
  bool is_real() const;
  void set_is_real(bool value);
  private:
  bool _internal_is_real() const;
  void _internal_set_is_real(bool value);
  public:

  // float weed_score = 8;
  void clear_weed_score();
  float weed_score() const;
  void set_weed_score(float value);
  private:
  float _internal_weed_score() const;
  void _internal_set_weed_score(float value);
  public:

  // float crop_score = 9;
  void clear_crop_score();
  float crop_score() const;
  void set_crop_score(float value);
  private:
  float _internal_crop_score() const;
  void _internal_set_crop_score(float value);
  public:

  // float plant_score = 13;
  void clear_plant_score();
  float plant_score() const;
  void set_plant_score(float value);
  private:
  float _internal_plant_score() const;
  void _internal_set_plant_score(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.simulator.Prediction)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      Prediction_DetectionClassesEntry_DoNotUse,
      std::string, float,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> detection_classes_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      Prediction_WeedDetectionClassesEntry_DoNotUse,
      std::string, float,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> weed_detection_classes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > embedding_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> mask_intersection_classes_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  uint32_t x_px_;
  uint32_t y_px_;
  uint32_t size_px_;
  float score_;
  bool is_weed_;
  bool is_real_;
  float weed_score_;
  float crop_score_;
  float plant_score_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto;
};
// -------------------------------------------------------------------

class GetNextPredictionsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.simulator.GetNextPredictionsResponse) */ {
 public:
  inline GetNextPredictionsResponse() : GetNextPredictionsResponse(nullptr) {}
  ~GetNextPredictionsResponse() override;
  explicit constexpr GetNextPredictionsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextPredictionsResponse(const GetNextPredictionsResponse& from);
  GetNextPredictionsResponse(GetNextPredictionsResponse&& from) noexcept
    : GetNextPredictionsResponse() {
    *this = ::std::move(from);
  }

  inline GetNextPredictionsResponse& operator=(const GetNextPredictionsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextPredictionsResponse& operator=(GetNextPredictionsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextPredictionsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextPredictionsResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextPredictionsResponse*>(
               &_GetNextPredictionsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GetNextPredictionsResponse& a, GetNextPredictionsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextPredictionsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextPredictionsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextPredictionsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextPredictionsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextPredictionsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextPredictionsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextPredictionsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.simulator.GetNextPredictionsResponse";
  }
  protected:
  explicit GetNextPredictionsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPredictionsFieldNumber = 1,
    kTimestampMsFieldNumber = 2,
  };
  // repeated .carbon.simulator.Prediction predictions = 1;
  int predictions_size() const;
  private:
  int _internal_predictions_size() const;
  public:
  void clear_predictions();
  ::carbon::simulator::Prediction* mutable_predictions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator::Prediction >*
      mutable_predictions();
  private:
  const ::carbon::simulator::Prediction& _internal_predictions(int index) const;
  ::carbon::simulator::Prediction* _internal_add_predictions();
  public:
  const ::carbon::simulator::Prediction& predictions(int index) const;
  ::carbon::simulator::Prediction* add_predictions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator::Prediction >&
      predictions() const;

  // int64 timestamp_ms = 2;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.simulator.GetNextPredictionsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator::Prediction > predictions_;
  int64_t timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Empty

// -------------------------------------------------------------------

// GetNextPredictionsRequest

// string name = 1;
inline void GetNextPredictionsRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& GetNextPredictionsRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.GetNextPredictionsRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextPredictionsRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.simulator.GetNextPredictionsRequest.name)
}
inline std::string* GetNextPredictionsRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.simulator.GetNextPredictionsRequest.name)
  return _s;
}
inline const std::string& GetNextPredictionsRequest::_internal_name() const {
  return name_.Get();
}
inline void GetNextPredictionsRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextPredictionsRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextPredictionsRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.simulator.GetNextPredictionsRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextPredictionsRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.simulator.GetNextPredictionsRequest.name)
}

// int64 timestamp_ms = 2;
inline void GetNextPredictionsRequest::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t GetNextPredictionsRequest::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t GetNextPredictionsRequest::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.GetNextPredictionsRequest.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void GetNextPredictionsRequest::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void GetNextPredictionsRequest::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.GetNextPredictionsRequest.timestamp_ms)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Prediction

// uint32 x_px = 1;
inline void Prediction::clear_x_px() {
  x_px_ = 0u;
}
inline uint32_t Prediction::_internal_x_px() const {
  return x_px_;
}
inline uint32_t Prediction::x_px() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.x_px)
  return _internal_x_px();
}
inline void Prediction::_internal_set_x_px(uint32_t value) {
  
  x_px_ = value;
}
inline void Prediction::set_x_px(uint32_t value) {
  _internal_set_x_px(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.x_px)
}

// uint32 y_px = 2;
inline void Prediction::clear_y_px() {
  y_px_ = 0u;
}
inline uint32_t Prediction::_internal_y_px() const {
  return y_px_;
}
inline uint32_t Prediction::y_px() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.y_px)
  return _internal_y_px();
}
inline void Prediction::_internal_set_y_px(uint32_t value) {
  
  y_px_ = value;
}
inline void Prediction::set_y_px(uint32_t value) {
  _internal_set_y_px(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.y_px)
}

// uint32 size_px = 3;
inline void Prediction::clear_size_px() {
  size_px_ = 0u;
}
inline uint32_t Prediction::_internal_size_px() const {
  return size_px_;
}
inline uint32_t Prediction::size_px() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.size_px)
  return _internal_size_px();
}
inline void Prediction::_internal_set_size_px(uint32_t value) {
  
  size_px_ = value;
}
inline void Prediction::set_size_px(uint32_t value) {
  _internal_set_size_px(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.size_px)
}

// float score = 4;
inline void Prediction::clear_score() {
  score_ = 0;
}
inline float Prediction::_internal_score() const {
  return score_;
}
inline float Prediction::score() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.score)
  return _internal_score();
}
inline void Prediction::_internal_set_score(float value) {
  
  score_ = value;
}
inline void Prediction::set_score(float value) {
  _internal_set_score(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.score)
}

// map<string, float> detection_classes = 5;
inline int Prediction::_internal_detection_classes_size() const {
  return detection_classes_.size();
}
inline int Prediction::detection_classes_size() const {
  return _internal_detection_classes_size();
}
inline void Prediction::clear_detection_classes() {
  detection_classes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
Prediction::_internal_detection_classes() const {
  return detection_classes_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
Prediction::detection_classes() const {
  // @@protoc_insertion_point(field_map:carbon.simulator.Prediction.detection_classes)
  return _internal_detection_classes();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
Prediction::_internal_mutable_detection_classes() {
  return detection_classes_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
Prediction::mutable_detection_classes() {
  // @@protoc_insertion_point(field_mutable_map:carbon.simulator.Prediction.detection_classes)
  return _internal_mutable_detection_classes();
}

// bool is_weed = 6;
inline void Prediction::clear_is_weed() {
  is_weed_ = false;
}
inline bool Prediction::_internal_is_weed() const {
  return is_weed_;
}
inline bool Prediction::is_weed() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.is_weed)
  return _internal_is_weed();
}
inline void Prediction::_internal_set_is_weed(bool value) {
  
  is_weed_ = value;
}
inline void Prediction::set_is_weed(bool value) {
  _internal_set_is_weed(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.is_weed)
}

// bool is_real = 7;
inline void Prediction::clear_is_real() {
  is_real_ = false;
}
inline bool Prediction::_internal_is_real() const {
  return is_real_;
}
inline bool Prediction::is_real() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.is_real)
  return _internal_is_real();
}
inline void Prediction::_internal_set_is_real(bool value) {
  
  is_real_ = value;
}
inline void Prediction::set_is_real(bool value) {
  _internal_set_is_real(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.is_real)
}

// float weed_score = 8;
inline void Prediction::clear_weed_score() {
  weed_score_ = 0;
}
inline float Prediction::_internal_weed_score() const {
  return weed_score_;
}
inline float Prediction::weed_score() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.weed_score)
  return _internal_weed_score();
}
inline void Prediction::_internal_set_weed_score(float value) {
  
  weed_score_ = value;
}
inline void Prediction::set_weed_score(float value) {
  _internal_set_weed_score(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.weed_score)
}

// float crop_score = 9;
inline void Prediction::clear_crop_score() {
  crop_score_ = 0;
}
inline float Prediction::_internal_crop_score() const {
  return crop_score_;
}
inline float Prediction::crop_score() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.crop_score)
  return _internal_crop_score();
}
inline void Prediction::_internal_set_crop_score(float value) {
  
  crop_score_ = value;
}
inline void Prediction::set_crop_score(float value) {
  _internal_set_crop_score(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.crop_score)
}

// string type = 10;
inline void Prediction::clear_type() {
  type_.ClearToEmpty();
}
inline const std::string& Prediction::type() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Prediction::set_type(ArgT0&& arg0, ArgT... args) {
 
 type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.type)
}
inline std::string* Prediction::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:carbon.simulator.Prediction.type)
  return _s;
}
inline const std::string& Prediction::_internal_type() const {
  return type_.Get();
}
inline void Prediction::_internal_set_type(const std::string& value) {
  
  type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Prediction::_internal_mutable_type() {
  
  return type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Prediction::release_type() {
  // @@protoc_insertion_point(field_release:carbon.simulator.Prediction.type)
  return type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Prediction::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.simulator.Prediction.type)
}

// map<string, float> weed_detection_classes = 11;
inline int Prediction::_internal_weed_detection_classes_size() const {
  return weed_detection_classes_.size();
}
inline int Prediction::weed_detection_classes_size() const {
  return _internal_weed_detection_classes_size();
}
inline void Prediction::clear_weed_detection_classes() {
  weed_detection_classes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
Prediction::_internal_weed_detection_classes() const {
  return weed_detection_classes_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
Prediction::weed_detection_classes() const {
  // @@protoc_insertion_point(field_map:carbon.simulator.Prediction.weed_detection_classes)
  return _internal_weed_detection_classes();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
Prediction::_internal_mutable_weed_detection_classes() {
  return weed_detection_classes_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
Prediction::mutable_weed_detection_classes() {
  // @@protoc_insertion_point(field_mutable_map:carbon.simulator.Prediction.weed_detection_classes)
  return _internal_mutable_weed_detection_classes();
}

// repeated float embedding = 12;
inline int Prediction::_internal_embedding_size() const {
  return embedding_.size();
}
inline int Prediction::embedding_size() const {
  return _internal_embedding_size();
}
inline void Prediction::clear_embedding() {
  embedding_.Clear();
}
inline float Prediction::_internal_embedding(int index) const {
  return embedding_.Get(index);
}
inline float Prediction::embedding(int index) const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.embedding)
  return _internal_embedding(index);
}
inline void Prediction::set_embedding(int index, float value) {
  embedding_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.embedding)
}
inline void Prediction::_internal_add_embedding(float value) {
  embedding_.Add(value);
}
inline void Prediction::add_embedding(float value) {
  _internal_add_embedding(value);
  // @@protoc_insertion_point(field_add:carbon.simulator.Prediction.embedding)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
Prediction::_internal_embedding() const {
  return embedding_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
Prediction::embedding() const {
  // @@protoc_insertion_point(field_list:carbon.simulator.Prediction.embedding)
  return _internal_embedding();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
Prediction::_internal_mutable_embedding() {
  return &embedding_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
Prediction::mutable_embedding() {
  // @@protoc_insertion_point(field_mutable_list:carbon.simulator.Prediction.embedding)
  return _internal_mutable_embedding();
}

// float plant_score = 13;
inline void Prediction::clear_plant_score() {
  plant_score_ = 0;
}
inline float Prediction::_internal_plant_score() const {
  return plant_score_;
}
inline float Prediction::plant_score() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.plant_score)
  return _internal_plant_score();
}
inline void Prediction::_internal_set_plant_score(float value) {
  
  plant_score_ = value;
}
inline void Prediction::set_plant_score(float value) {
  _internal_set_plant_score(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.plant_score)
}

// repeated string mask_intersection_classes = 14;
inline int Prediction::_internal_mask_intersection_classes_size() const {
  return mask_intersection_classes_.size();
}
inline int Prediction::mask_intersection_classes_size() const {
  return _internal_mask_intersection_classes_size();
}
inline void Prediction::clear_mask_intersection_classes() {
  mask_intersection_classes_.Clear();
}
inline std::string* Prediction::add_mask_intersection_classes() {
  std::string* _s = _internal_add_mask_intersection_classes();
  // @@protoc_insertion_point(field_add_mutable:carbon.simulator.Prediction.mask_intersection_classes)
  return _s;
}
inline const std::string& Prediction::_internal_mask_intersection_classes(int index) const {
  return mask_intersection_classes_.Get(index);
}
inline const std::string& Prediction::mask_intersection_classes(int index) const {
  // @@protoc_insertion_point(field_get:carbon.simulator.Prediction.mask_intersection_classes)
  return _internal_mask_intersection_classes(index);
}
inline std::string* Prediction::mutable_mask_intersection_classes(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.simulator.Prediction.mask_intersection_classes)
  return mask_intersection_classes_.Mutable(index);
}
inline void Prediction::set_mask_intersection_classes(int index, const std::string& value) {
  mask_intersection_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.mask_intersection_classes)
}
inline void Prediction::set_mask_intersection_classes(int index, std::string&& value) {
  mask_intersection_classes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.simulator.Prediction.mask_intersection_classes)
}
inline void Prediction::set_mask_intersection_classes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  mask_intersection_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.simulator.Prediction.mask_intersection_classes)
}
inline void Prediction::set_mask_intersection_classes(int index, const char* value, size_t size) {
  mask_intersection_classes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.simulator.Prediction.mask_intersection_classes)
}
inline std::string* Prediction::_internal_add_mask_intersection_classes() {
  return mask_intersection_classes_.Add();
}
inline void Prediction::add_mask_intersection_classes(const std::string& value) {
  mask_intersection_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.simulator.Prediction.mask_intersection_classes)
}
inline void Prediction::add_mask_intersection_classes(std::string&& value) {
  mask_intersection_classes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.simulator.Prediction.mask_intersection_classes)
}
inline void Prediction::add_mask_intersection_classes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  mask_intersection_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.simulator.Prediction.mask_intersection_classes)
}
inline void Prediction::add_mask_intersection_classes(const char* value, size_t size) {
  mask_intersection_classes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.simulator.Prediction.mask_intersection_classes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Prediction::mask_intersection_classes() const {
  // @@protoc_insertion_point(field_list:carbon.simulator.Prediction.mask_intersection_classes)
  return mask_intersection_classes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Prediction::mutable_mask_intersection_classes() {
  // @@protoc_insertion_point(field_mutable_list:carbon.simulator.Prediction.mask_intersection_classes)
  return &mask_intersection_classes_;
}

// -------------------------------------------------------------------

// GetNextPredictionsResponse

// repeated .carbon.simulator.Prediction predictions = 1;
inline int GetNextPredictionsResponse::_internal_predictions_size() const {
  return predictions_.size();
}
inline int GetNextPredictionsResponse::predictions_size() const {
  return _internal_predictions_size();
}
inline void GetNextPredictionsResponse::clear_predictions() {
  predictions_.Clear();
}
inline ::carbon::simulator::Prediction* GetNextPredictionsResponse::mutable_predictions(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.simulator.GetNextPredictionsResponse.predictions)
  return predictions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator::Prediction >*
GetNextPredictionsResponse::mutable_predictions() {
  // @@protoc_insertion_point(field_mutable_list:carbon.simulator.GetNextPredictionsResponse.predictions)
  return &predictions_;
}
inline const ::carbon::simulator::Prediction& GetNextPredictionsResponse::_internal_predictions(int index) const {
  return predictions_.Get(index);
}
inline const ::carbon::simulator::Prediction& GetNextPredictionsResponse::predictions(int index) const {
  // @@protoc_insertion_point(field_get:carbon.simulator.GetNextPredictionsResponse.predictions)
  return _internal_predictions(index);
}
inline ::carbon::simulator::Prediction* GetNextPredictionsResponse::_internal_add_predictions() {
  return predictions_.Add();
}
inline ::carbon::simulator::Prediction* GetNextPredictionsResponse::add_predictions() {
  ::carbon::simulator::Prediction* _add = _internal_add_predictions();
  // @@protoc_insertion_point(field_add:carbon.simulator.GetNextPredictionsResponse.predictions)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::simulator::Prediction >&
GetNextPredictionsResponse::predictions() const {
  // @@protoc_insertion_point(field_list:carbon.simulator.GetNextPredictionsResponse.predictions)
  return predictions_;
}

// int64 timestamp_ms = 2;
inline void GetNextPredictionsResponse::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t GetNextPredictionsResponse::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t GetNextPredictionsResponse::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.simulator.GetNextPredictionsResponse.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void GetNextPredictionsResponse::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void GetNextPredictionsResponse::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.simulator.GetNextPredictionsResponse.timestamp_ms)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace simulator
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto

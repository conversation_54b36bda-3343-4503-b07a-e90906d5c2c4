# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: golang/simulator/hardware/proto/sim/sim.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='golang/simulator/hardware/proto/sim/sim.proto',
  package='carbon.simulator',
  syntax='proto3',
  serialized_options=b'Z\tproto/sim',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n-golang/simulator/hardware/proto/sim/sim.proto\x12\x10\x63\x61rbon.simulator\"\x07\n\x05\x45mpty\"?\n\x19GetNextPredictionsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"\x88\x04\n\nPrediction\x12\x0c\n\x04x_px\x18\x01 \x01(\r\x12\x0c\n\x04y_px\x18\x02 \x01(\r\x12\x0f\n\x07size_px\x18\x03 \x01(\r\x12\r\n\x05score\x18\x04 \x01(\x02\x12M\n\x11\x64\x65tection_classes\x18\x05 \x03(\x0b\x32\x32.carbon.simulator.Prediction.DetectionClassesEntry\x12\x0f\n\x07is_weed\x18\x06 \x01(\x08\x12\x0f\n\x07is_real\x18\x07 \x01(\x08\x12\x12\n\nweed_score\x18\x08 \x01(\x02\x12\x12\n\ncrop_score\x18\t \x01(\x02\x12\x0c\n\x04type\x18\n \x01(\t\x12V\n\x16weed_detection_classes\x18\x0b \x03(\x0b\x32\x36.carbon.simulator.Prediction.WeedDetectionClassesEntry\x12\x11\n\tembedding\x18\x0c \x03(\x02\x12\x13\n\x0bplant_score\x18\r \x01(\x02\x12!\n\x19mask_intersection_classes\x18\x0e \x03(\t\x1a\x37\n\x15\x44\x65tectionClassesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\x1a;\n\x19WeedDetectionClassesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"e\n\x1aGetNextPredictionsResponse\x12\x31\n\x0bpredictions\x18\x01 \x03(\x0b\x32\x1c.carbon.simulator.Prediction\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x32\xbd\x01\n\x10SimulatorService\x12\x38\n\x04Ping\x12\x17.carbon.simulator.Empty\x1a\x17.carbon.simulator.Empty\x12o\n\x12GetNextPredictions\x12+.carbon.simulator.GetNextPredictionsRequest\x1a,.carbon.simulator.GetNextPredictionsResponseB\x0bZ\tproto/simb\x06proto3'
)




_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='carbon.simulator.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=67,
  serialized_end=74,
)


_GETNEXTPREDICTIONSREQUEST = _descriptor.Descriptor(
  name='GetNextPredictionsRequest',
  full_name='carbon.simulator.GetNextPredictionsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.simulator.GetNextPredictionsRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.simulator.GetNextPredictionsRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=76,
  serialized_end=139,
)


_PREDICTION_DETECTIONCLASSESENTRY = _descriptor.Descriptor(
  name='DetectionClassesEntry',
  full_name='carbon.simulator.Prediction.DetectionClassesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.simulator.Prediction.DetectionClassesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.simulator.Prediction.DetectionClassesEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=546,
  serialized_end=601,
)

_PREDICTION_WEEDDETECTIONCLASSESENTRY = _descriptor.Descriptor(
  name='WeedDetectionClassesEntry',
  full_name='carbon.simulator.Prediction.WeedDetectionClassesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.simulator.Prediction.WeedDetectionClassesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.simulator.Prediction.WeedDetectionClassesEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=603,
  serialized_end=662,
)

_PREDICTION = _descriptor.Descriptor(
  name='Prediction',
  full_name='carbon.simulator.Prediction',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x_px', full_name='carbon.simulator.Prediction.x_px', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_px', full_name='carbon.simulator.Prediction.y_px', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size_px', full_name='carbon.simulator.Prediction.size_px', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='score', full_name='carbon.simulator.Prediction.score', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='detection_classes', full_name='carbon.simulator.Prediction.detection_classes', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_weed', full_name='carbon.simulator.Prediction.is_weed', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_real', full_name='carbon.simulator.Prediction.is_real', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_score', full_name='carbon.simulator.Prediction.weed_score', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_score', full_name='carbon.simulator.Prediction.crop_score', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.simulator.Prediction.type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_detection_classes', full_name='carbon.simulator.Prediction.weed_detection_classes', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='embedding', full_name='carbon.simulator.Prediction.embedding', index=11,
      number=12, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plant_score', full_name='carbon.simulator.Prediction.plant_score', index=12,
      number=13, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mask_intersection_classes', full_name='carbon.simulator.Prediction.mask_intersection_classes', index=13,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_PREDICTION_DETECTIONCLASSESENTRY, _PREDICTION_WEEDDETECTIONCLASSESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=142,
  serialized_end=662,
)


_GETNEXTPREDICTIONSRESPONSE = _descriptor.Descriptor(
  name='GetNextPredictionsResponse',
  full_name='carbon.simulator.GetNextPredictionsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='predictions', full_name='carbon.simulator.GetNextPredictionsResponse.predictions', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.simulator.GetNextPredictionsResponse.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=664,
  serialized_end=765,
)

_PREDICTION_DETECTIONCLASSESENTRY.containing_type = _PREDICTION
_PREDICTION_WEEDDETECTIONCLASSESENTRY.containing_type = _PREDICTION
_PREDICTION.fields_by_name['detection_classes'].message_type = _PREDICTION_DETECTIONCLASSESENTRY
_PREDICTION.fields_by_name['weed_detection_classes'].message_type = _PREDICTION_WEEDDETECTIONCLASSESENTRY
_GETNEXTPREDICTIONSRESPONSE.fields_by_name['predictions'].message_type = _PREDICTION
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['GetNextPredictionsRequest'] = _GETNEXTPREDICTIONSREQUEST
DESCRIPTOR.message_types_by_name['Prediction'] = _PREDICTION
DESCRIPTOR.message_types_by_name['GetNextPredictionsResponse'] = _GETNEXTPREDICTIONSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'golang.simulator.hardware.proto.sim.sim_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator.Empty)
  })
_sym_db.RegisterMessage(Empty)

GetNextPredictionsRequest = _reflection.GeneratedProtocolMessageType('GetNextPredictionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTPREDICTIONSREQUEST,
  '__module__' : 'golang.simulator.hardware.proto.sim.sim_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator.GetNextPredictionsRequest)
  })
_sym_db.RegisterMessage(GetNextPredictionsRequest)

Prediction = _reflection.GeneratedProtocolMessageType('Prediction', (_message.Message,), {

  'DetectionClassesEntry' : _reflection.GeneratedProtocolMessageType('DetectionClassesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PREDICTION_DETECTIONCLASSESENTRY,
    '__module__' : 'golang.simulator.hardware.proto.sim.sim_pb2'
    # @@protoc_insertion_point(class_scope:carbon.simulator.Prediction.DetectionClassesEntry)
    })
  ,

  'WeedDetectionClassesEntry' : _reflection.GeneratedProtocolMessageType('WeedDetectionClassesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PREDICTION_WEEDDETECTIONCLASSESENTRY,
    '__module__' : 'golang.simulator.hardware.proto.sim.sim_pb2'
    # @@protoc_insertion_point(class_scope:carbon.simulator.Prediction.WeedDetectionClassesEntry)
    })
  ,
  'DESCRIPTOR' : _PREDICTION,
  '__module__' : 'golang.simulator.hardware.proto.sim.sim_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator.Prediction)
  })
_sym_db.RegisterMessage(Prediction)
_sym_db.RegisterMessage(Prediction.DetectionClassesEntry)
_sym_db.RegisterMessage(Prediction.WeedDetectionClassesEntry)

GetNextPredictionsResponse = _reflection.GeneratedProtocolMessageType('GetNextPredictionsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTPREDICTIONSRESPONSE,
  '__module__' : 'golang.simulator.hardware.proto.sim.sim_pb2'
  # @@protoc_insertion_point(class_scope:carbon.simulator.GetNextPredictionsResponse)
  })
_sym_db.RegisterMessage(GetNextPredictionsResponse)


DESCRIPTOR._options = None
_PREDICTION_DETECTIONCLASSESENTRY._options = None
_PREDICTION_WEEDDETECTIONCLASSESENTRY._options = None

_SIMULATORSERVICE = _descriptor.ServiceDescriptor(
  name='SimulatorService',
  full_name='carbon.simulator.SimulatorService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=768,
  serialized_end=957,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='carbon.simulator.SimulatorService.Ping',
    index=0,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextPredictions',
    full_name='carbon.simulator.SimulatorService.GetNextPredictions',
    index=1,
    containing_service=None,
    input_type=_GETNEXTPREDICTIONSREQUEST,
    output_type=_GETNEXTPREDICTIONSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_SIMULATORSERVICE)

DESCRIPTOR.services_by_name['SimulatorService'] = _SIMULATORSERVICE

# @@protoc_insertion_point(module_scope)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

class GetNextPredictionsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextPredictionsRequest = GetNextPredictionsRequest

class Prediction(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class DetectionClassesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___DetectionClassesEntry = DetectionClassesEntry

    class WeedDetectionClassesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___WeedDetectionClassesEntry = WeedDetectionClassesEntry

    x_px: builtin___int = ...
    y_px: builtin___int = ...
    size_px: builtin___int = ...
    score: builtin___float = ...
    is_weed: builtin___bool = ...
    is_real: builtin___bool = ...
    weed_score: builtin___float = ...
    crop_score: builtin___float = ...
    type: typing___Text = ...
    embedding: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    plant_score: builtin___float = ...
    mask_intersection_classes: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    @property
    def detection_classes(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    @property
    def weed_detection_classes(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    def __init__(self,
        *,
        x_px : typing___Optional[builtin___int] = None,
        y_px : typing___Optional[builtin___int] = None,
        size_px : typing___Optional[builtin___int] = None,
        score : typing___Optional[builtin___float] = None,
        detection_classes : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        is_weed : typing___Optional[builtin___bool] = None,
        is_real : typing___Optional[builtin___bool] = None,
        weed_score : typing___Optional[builtin___float] = None,
        crop_score : typing___Optional[builtin___float] = None,
        type : typing___Optional[typing___Text] = None,
        weed_detection_classes : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        embedding : typing___Optional[typing___Iterable[builtin___float]] = None,
        plant_score : typing___Optional[builtin___float] = None,
        mask_intersection_classes : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_score",b"crop_score",u"detection_classes",b"detection_classes",u"embedding",b"embedding",u"is_real",b"is_real",u"is_weed",b"is_weed",u"mask_intersection_classes",b"mask_intersection_classes",u"plant_score",b"plant_score",u"score",b"score",u"size_px",b"size_px",u"type",b"type",u"weed_detection_classes",b"weed_detection_classes",u"weed_score",b"weed_score",u"x_px",b"x_px",u"y_px",b"y_px"]) -> None: ...
type___Prediction = Prediction

class GetNextPredictionsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    @property
    def predictions(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Prediction]: ...

    def __init__(self,
        *,
        predictions : typing___Optional[typing___Iterable[type___Prediction]] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"predictions",b"predictions",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextPredictionsResponse = GetNextPredictionsResponse

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: golang/simulator/hardware/proto/sim/sim.proto

#include "golang/simulator/hardware/proto/sim/sim.pb.h"
#include "golang/simulator/hardware/proto/sim/sim.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace simulator {

static const char* SimulatorService_method_names[] = {
  "/carbon.simulator.SimulatorService/Ping",
  "/carbon.simulator.SimulatorService/GetNextPredictions",
};

std::unique_ptr< SimulatorService::Stub> SimulatorService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< SimulatorService::Stub> stub(new SimulatorService::Stub(channel, options));
  return stub;
}

SimulatorService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Ping_(SimulatorService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextPredictions_(SimulatorService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status SimulatorService::Stub::Ping(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::carbon::simulator::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::simulator::Empty, ::carbon::simulator::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Ping_, context, request, response);
}

void SimulatorService::Stub::async::Ping(::grpc::ClientContext* context, const ::carbon::simulator::Empty* request, ::carbon::simulator::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::simulator::Empty, ::carbon::simulator::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, std::move(f));
}

void SimulatorService::Stub::async::Ping(::grpc::ClientContext* context, const ::carbon::simulator::Empty* request, ::carbon::simulator::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator::Empty>* SimulatorService::Stub::PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::simulator::Empty, ::carbon::simulator::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Ping_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator::Empty>* SimulatorService::Stub::AsyncPingRaw(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status SimulatorService::Stub::GetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::carbon::simulator::GetNextPredictionsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::simulator::GetNextPredictionsRequest, ::carbon::simulator::GetNextPredictionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextPredictions_, context, request, response);
}

void SimulatorService::Stub::async::GetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest* request, ::carbon::simulator::GetNextPredictionsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::simulator::GetNextPredictionsRequest, ::carbon::simulator::GetNextPredictionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPredictions_, context, request, response, std::move(f));
}

void SimulatorService::Stub::async::GetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest* request, ::carbon::simulator::GetNextPredictionsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPredictions_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator::GetNextPredictionsResponse>* SimulatorService::Stub::PrepareAsyncGetNextPredictionsRaw(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::simulator::GetNextPredictionsResponse, ::carbon::simulator::GetNextPredictionsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextPredictions_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::simulator::GetNextPredictionsResponse>* SimulatorService::Stub::AsyncGetNextPredictionsRaw(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextPredictionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

SimulatorService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SimulatorService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SimulatorService::Service, ::carbon::simulator::Empty, ::carbon::simulator::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SimulatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::simulator::Empty* req,
             ::carbon::simulator::Empty* resp) {
               return service->Ping(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SimulatorService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SimulatorService::Service, ::carbon::simulator::GetNextPredictionsRequest, ::carbon::simulator::GetNextPredictionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SimulatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::simulator::GetNextPredictionsRequest* req,
             ::carbon::simulator::GetNextPredictionsResponse* resp) {
               return service->GetNextPredictions(ctx, req, resp);
             }, this)));
}

SimulatorService::Service::~Service() {
}

::grpc::Status SimulatorService::Service::Ping(::grpc::ServerContext* context, const ::carbon::simulator::Empty* request, ::carbon::simulator::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status SimulatorService::Service::GetNextPredictions(::grpc::ServerContext* context, const ::carbon::simulator::GetNextPredictionsRequest* request, ::carbon::simulator::GetNextPredictionsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace simulator


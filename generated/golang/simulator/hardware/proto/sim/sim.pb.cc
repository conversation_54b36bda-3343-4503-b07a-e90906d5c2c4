// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: golang/simulator/hardware/proto/sim/sim.proto

#include "golang/simulator/hardware/proto/sim/sim.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace simulator {
constexpr Empty::Empty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct EmptyDefaultTypeInternal {
  constexpr EmptyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EmptyDefaultTypeInternal() {}
  union {
    Empty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EmptyDefaultTypeInternal _Empty_default_instance_;
constexpr GetNextPredictionsRequest::GetNextPredictionsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0}){}
struct GetNextPredictionsRequestDefaultTypeInternal {
  constexpr GetNextPredictionsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextPredictionsRequestDefaultTypeInternal() {}
  union {
    GetNextPredictionsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextPredictionsRequestDefaultTypeInternal _GetNextPredictionsRequest_default_instance_;
constexpr Prediction_DetectionClassesEntry_DoNotUse::Prediction_DetectionClassesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct Prediction_DetectionClassesEntry_DoNotUseDefaultTypeInternal {
  constexpr Prediction_DetectionClassesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~Prediction_DetectionClassesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    Prediction_DetectionClassesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT Prediction_DetectionClassesEntry_DoNotUseDefaultTypeInternal _Prediction_DetectionClassesEntry_DoNotUse_default_instance_;
constexpr Prediction_WeedDetectionClassesEntry_DoNotUse::Prediction_WeedDetectionClassesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct Prediction_WeedDetectionClassesEntry_DoNotUseDefaultTypeInternal {
  constexpr Prediction_WeedDetectionClassesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~Prediction_WeedDetectionClassesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    Prediction_WeedDetectionClassesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT Prediction_WeedDetectionClassesEntry_DoNotUseDefaultTypeInternal _Prediction_WeedDetectionClassesEntry_DoNotUse_default_instance_;
constexpr Prediction::Prediction(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : detection_classes_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , weed_detection_classes_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , embedding_()
  , mask_intersection_classes_()
  , type_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , x_px_(0u)
  , y_px_(0u)
  , size_px_(0u)
  , score_(0)
  , is_weed_(false)
  , is_real_(false)
  , weed_score_(0)
  , crop_score_(0)
  , plant_score_(0){}
struct PredictionDefaultTypeInternal {
  constexpr PredictionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PredictionDefaultTypeInternal() {}
  union {
    Prediction _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PredictionDefaultTypeInternal _Prediction_default_instance_;
constexpr GetNextPredictionsResponse::GetNextPredictionsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : predictions_()
  , timestamp_ms_(int64_t{0}){}
struct GetNextPredictionsResponseDefaultTypeInternal {
  constexpr GetNextPredictionsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextPredictionsResponseDefaultTypeInternal() {}
  union {
    GetNextPredictionsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextPredictionsResponseDefaultTypeInternal _GetNextPredictionsResponse_default_instance_;
}  // namespace simulator
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto = nullptr;

const uint32_t TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Empty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::GetNextPredictionsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::GetNextPredictionsRequest, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::GetNextPredictionsRequest, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, x_px_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, y_px_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, size_px_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, score_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, detection_classes_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, is_weed_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, is_real_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, weed_score_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, crop_score_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, weed_detection_classes_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, embedding_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, plant_score_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::Prediction, mask_intersection_classes_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::GetNextPredictionsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::GetNextPredictionsResponse, predictions_),
  PROTOBUF_FIELD_OFFSET(::carbon::simulator::GetNextPredictionsResponse, timestamp_ms_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::simulator::Empty)},
  { 6, -1, -1, sizeof(::carbon::simulator::GetNextPredictionsRequest)},
  { 14, 22, -1, sizeof(::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse)},
  { 24, 32, -1, sizeof(::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse)},
  { 34, -1, -1, sizeof(::carbon::simulator::Prediction)},
  { 54, -1, -1, sizeof(::carbon::simulator::GetNextPredictionsResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator::_Empty_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator::_GetNextPredictionsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator::_Prediction_DetectionClassesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator::_Prediction_WeedDetectionClassesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator::_Prediction_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::simulator::_GetNextPredictionsResponse_default_instance_),
};

const char descriptor_table_protodef_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n-golang/simulator/hardware/proto/sim/si"
  "m.proto\022\020carbon.simulator\"\007\n\005Empty\"\?\n\031Ge"
  "tNextPredictionsRequest\022\014\n\004name\030\001 \001(\t\022\024\n"
  "\014timestamp_ms\030\002 \001(\003\"\210\004\n\nPrediction\022\014\n\004x_"
  "px\030\001 \001(\r\022\014\n\004y_px\030\002 \001(\r\022\017\n\007size_px\030\003 \001(\r\022"
  "\r\n\005score\030\004 \001(\002\022M\n\021detection_classes\030\005 \003("
  "\01322.carbon.simulator.Prediction.Detectio"
  "nClassesEntry\022\017\n\007is_weed\030\006 \001(\010\022\017\n\007is_rea"
  "l\030\007 \001(\010\022\022\n\nweed_score\030\010 \001(\002\022\022\n\ncrop_scor"
  "e\030\t \001(\002\022\014\n\004type\030\n \001(\t\022V\n\026weed_detection_"
  "classes\030\013 \003(\01326.carbon.simulator.Predict"
  "ion.WeedDetectionClassesEntry\022\021\n\tembeddi"
  "ng\030\014 \003(\002\022\023\n\013plant_score\030\r \001(\002\022!\n\031mask_in"
  "tersection_classes\030\016 \003(\t\0327\n\025DetectionCla"
  "ssesEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\002:\0028"
  "\001\032;\n\031WeedDetectionClassesEntry\022\013\n\003key\030\001 "
  "\001(\t\022\r\n\005value\030\002 \001(\002:\0028\001\"e\n\032GetNextPredict"
  "ionsResponse\0221\n\013predictions\030\001 \003(\0132\034.carb"
  "on.simulator.Prediction\022\024\n\014timestamp_ms\030"
  "\002 \001(\0032\275\001\n\020SimulatorService\0228\n\004Ping\022\027.car"
  "bon.simulator.Empty\032\027.carbon.simulator.E"
  "mpty\022o\n\022GetNextPredictions\022+.carbon.simu"
  "lator.GetNextPredictionsRequest\032,.carbon"
  ".simulator.GetNextPredictionsResponseB\013Z"
  "\tproto/simb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto = {
  false, false, 978, descriptor_table_protodef_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto, "golang/simulator/hardware/proto/sim/sim.proto", 
  &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_once, nullptr, 0, 6,
  schemas, file_default_instances, TableStruct_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto::offsets,
  file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto, file_level_enum_descriptors_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto, file_level_service_descriptors_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_getter() {
  return &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto(&descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto);
namespace carbon {
namespace simulator {

// ===================================================================

class Empty::_Internal {
 public:
};

Empty::Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.simulator.Empty)
}
Empty::Empty(const Empty& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.simulator.Empty)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Empty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Empty::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata Empty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto[0]);
}

// ===================================================================

class GetNextPredictionsRequest::_Internal {
 public:
};

GetNextPredictionsRequest::GetNextPredictionsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator.GetNextPredictionsRequest)
}
GetNextPredictionsRequest::GetNextPredictionsRequest(const GetNextPredictionsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:carbon.simulator.GetNextPredictionsRequest)
}

inline void GetNextPredictionsRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
timestamp_ms_ = int64_t{0};
}

GetNextPredictionsRequest::~GetNextPredictionsRequest() {
  // @@protoc_insertion_point(destructor:carbon.simulator.GetNextPredictionsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextPredictionsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetNextPredictionsRequest::ArenaDtor(void* object) {
  GetNextPredictionsRequest* _this = reinterpret_cast< GetNextPredictionsRequest* >(object);
  (void)_this;
}
void GetNextPredictionsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextPredictionsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextPredictionsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator.GetNextPredictionsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  timestamp_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextPredictionsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.simulator.GetNextPredictionsRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 timestamp_ms = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextPredictionsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator.GetNextPredictionsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.simulator.GetNextPredictionsRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // int64 timestamp_ms = 2;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_timestamp_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator.GetNextPredictionsRequest)
  return target;
}

size_t GetNextPredictionsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator.GetNextPredictionsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // int64 timestamp_ms = 2;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextPredictionsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextPredictionsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextPredictionsRequest::GetClassData() const { return &_class_data_; }

void GetNextPredictionsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextPredictionsRequest *>(to)->MergeFrom(
      static_cast<const GetNextPredictionsRequest &>(from));
}


void GetNextPredictionsRequest::MergeFrom(const GetNextPredictionsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator.GetNextPredictionsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextPredictionsRequest::CopyFrom(const GetNextPredictionsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator.GetNextPredictionsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextPredictionsRequest::IsInitialized() const {
  return true;
}

void GetNextPredictionsRequest::InternalSwap(GetNextPredictionsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  swap(timestamp_ms_, other->timestamp_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextPredictionsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto[1]);
}

// ===================================================================

Prediction_DetectionClassesEntry_DoNotUse::Prediction_DetectionClassesEntry_DoNotUse() {}
Prediction_DetectionClassesEntry_DoNotUse::Prediction_DetectionClassesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void Prediction_DetectionClassesEntry_DoNotUse::MergeFrom(const Prediction_DetectionClassesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata Prediction_DetectionClassesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto[2]);
}

// ===================================================================

Prediction_WeedDetectionClassesEntry_DoNotUse::Prediction_WeedDetectionClassesEntry_DoNotUse() {}
Prediction_WeedDetectionClassesEntry_DoNotUse::Prediction_WeedDetectionClassesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void Prediction_WeedDetectionClassesEntry_DoNotUse::MergeFrom(const Prediction_WeedDetectionClassesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata Prediction_WeedDetectionClassesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto[3]);
}

// ===================================================================

class Prediction::_Internal {
 public:
};

Prediction::Prediction(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  detection_classes_(arena),
  weed_detection_classes_(arena),
  embedding_(arena),
  mask_intersection_classes_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator.Prediction)
}
Prediction::Prediction(const Prediction& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      embedding_(from.embedding_),
      mask_intersection_classes_(from.mask_intersection_classes_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  detection_classes_.MergeFrom(from.detection_classes_);
  weed_detection_classes_.MergeFrom(from.weed_detection_classes_);
  type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_type().empty()) {
    type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_type(), 
      GetArenaForAllocation());
  }
  ::memcpy(&x_px_, &from.x_px_,
    static_cast<size_t>(reinterpret_cast<char*>(&plant_score_) -
    reinterpret_cast<char*>(&x_px_)) + sizeof(plant_score_));
  // @@protoc_insertion_point(copy_constructor:carbon.simulator.Prediction)
}

inline void Prediction::SharedCtor() {
type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_px_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&plant_score_) -
    reinterpret_cast<char*>(&x_px_)) + sizeof(plant_score_));
}

Prediction::~Prediction() {
  // @@protoc_insertion_point(destructor:carbon.simulator.Prediction)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Prediction::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void Prediction::ArenaDtor(void* object) {
  Prediction* _this = reinterpret_cast< Prediction* >(object);
  (void)_this;
  _this->detection_classes_. ~MapField();
  _this->weed_detection_classes_. ~MapField();
}
inline void Prediction::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &Prediction::ArenaDtor);
  }
}
void Prediction::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Prediction::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator.Prediction)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  detection_classes_.Clear();
  weed_detection_classes_.Clear();
  embedding_.Clear();
  mask_intersection_classes_.Clear();
  type_.ClearToEmpty();
  ::memset(&x_px_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&plant_score_) -
      reinterpret_cast<char*>(&x_px_)) + sizeof(plant_score_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Prediction::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 x_px = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_px_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 y_px = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          y_px_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 size_px = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          size_px_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float score = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // map<string, float> detection_classes = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&detection_classes_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool is_weed = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          is_weed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_real = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          is_real_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float weed_score = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          weed_score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float crop_score = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          crop_score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // string type = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.simulator.Prediction.type"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, float> weed_detection_classes = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&weed_detection_classes_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<90>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated float embedding = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_embedding(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 101) {
          _internal_add_embedding(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float plant_score = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 109)) {
          plant_score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated string mask_intersection_classes = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_mask_intersection_classes();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.simulator.Prediction.mask_intersection_classes"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<114>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Prediction::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator.Prediction)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 x_px = 1;
  if (this->_internal_x_px() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_x_px(), target);
  }

  // uint32 y_px = 2;
  if (this->_internal_y_px() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_y_px(), target);
  }

  // uint32 size_px = 3;
  if (this->_internal_size_px() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_size_px(), target);
  }

  // float score = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_score(), target);
  }

  // map<string, float> detection_classes = 5;
  if (!this->_internal_detection_classes().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.simulator.Prediction.DetectionClassesEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_detection_classes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_detection_classes().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
          it = this->_internal_detection_classes().begin();
          it != this->_internal_detection_classes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = Prediction_DetectionClassesEntry_DoNotUse::Funcs::InternalSerialize(5, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
          it = this->_internal_detection_classes().begin();
          it != this->_internal_detection_classes().end(); ++it) {
        target = Prediction_DetectionClassesEntry_DoNotUse::Funcs::InternalSerialize(5, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // bool is_weed = 6;
  if (this->_internal_is_weed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_is_weed(), target);
  }

  // bool is_real = 7;
  if (this->_internal_is_real() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_is_real(), target);
  }

  // float weed_score = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weed_score = this->_internal_weed_score();
  uint32_t raw_weed_score;
  memcpy(&raw_weed_score, &tmp_weed_score, sizeof(tmp_weed_score));
  if (raw_weed_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_weed_score(), target);
  }

  // float crop_score = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crop_score = this->_internal_crop_score();
  uint32_t raw_crop_score;
  memcpy(&raw_crop_score, &tmp_crop_score, sizeof(tmp_crop_score));
  if (raw_crop_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_crop_score(), target);
  }

  // string type = 10;
  if (!this->_internal_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_type().data(), static_cast<int>(this->_internal_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.simulator.Prediction.type");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_type(), target);
  }

  // map<string, float> weed_detection_classes = 11;
  if (!this->_internal_weed_detection_classes().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.simulator.Prediction.WeedDetectionClassesEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_weed_detection_classes().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_weed_detection_classes().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
          it = this->_internal_weed_detection_classes().begin();
          it != this->_internal_weed_detection_classes().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = Prediction_WeedDetectionClassesEntry_DoNotUse::Funcs::InternalSerialize(11, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
          it = this->_internal_weed_detection_classes().begin();
          it != this->_internal_weed_detection_classes().end(); ++it) {
        target = Prediction_WeedDetectionClassesEntry_DoNotUse::Funcs::InternalSerialize(11, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // repeated float embedding = 12;
  if (this->_internal_embedding_size() > 0) {
    target = stream->WriteFixedPacked(12, _internal_embedding(), target);
  }

  // float plant_score = 13;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_plant_score = this->_internal_plant_score();
  uint32_t raw_plant_score;
  memcpy(&raw_plant_score, &tmp_plant_score, sizeof(tmp_plant_score));
  if (raw_plant_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(13, this->_internal_plant_score(), target);
  }

  // repeated string mask_intersection_classes = 14;
  for (int i = 0, n = this->_internal_mask_intersection_classes_size(); i < n; i++) {
    const auto& s = this->_internal_mask_intersection_classes(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.simulator.Prediction.mask_intersection_classes");
    target = stream->WriteString(14, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator.Prediction)
  return target;
}

size_t Prediction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator.Prediction)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, float> detection_classes = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_detection_classes_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
      it = this->_internal_detection_classes().begin();
      it != this->_internal_detection_classes().end(); ++it) {
    total_size += Prediction_DetectionClassesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, float> weed_detection_classes = 11;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_weed_detection_classes_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
      it = this->_internal_weed_detection_classes().begin();
      it != this->_internal_weed_detection_classes().end(); ++it) {
    total_size += Prediction_WeedDetectionClassesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // repeated float embedding = 12;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_embedding_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated string mask_intersection_classes = 14;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(mask_intersection_classes_.size());
  for (int i = 0, n = mask_intersection_classes_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      mask_intersection_classes_.Get(i));
  }

  // string type = 10;
  if (!this->_internal_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_type());
  }

  // uint32 x_px = 1;
  if (this->_internal_x_px() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_x_px());
  }

  // uint32 y_px = 2;
  if (this->_internal_y_px() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_y_px());
  }

  // uint32 size_px = 3;
  if (this->_internal_size_px() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_size_px());
  }

  // float score = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    total_size += 1 + 4;
  }

  // bool is_weed = 6;
  if (this->_internal_is_weed() != 0) {
    total_size += 1 + 1;
  }

  // bool is_real = 7;
  if (this->_internal_is_real() != 0) {
    total_size += 1 + 1;
  }

  // float weed_score = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weed_score = this->_internal_weed_score();
  uint32_t raw_weed_score;
  memcpy(&raw_weed_score, &tmp_weed_score, sizeof(tmp_weed_score));
  if (raw_weed_score != 0) {
    total_size += 1 + 4;
  }

  // float crop_score = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crop_score = this->_internal_crop_score();
  uint32_t raw_crop_score;
  memcpy(&raw_crop_score, &tmp_crop_score, sizeof(tmp_crop_score));
  if (raw_crop_score != 0) {
    total_size += 1 + 4;
  }

  // float plant_score = 13;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_plant_score = this->_internal_plant_score();
  uint32_t raw_plant_score;
  memcpy(&raw_plant_score, &tmp_plant_score, sizeof(tmp_plant_score));
  if (raw_plant_score != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Prediction::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Prediction::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Prediction::GetClassData() const { return &_class_data_; }

void Prediction::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Prediction *>(to)->MergeFrom(
      static_cast<const Prediction &>(from));
}


void Prediction::MergeFrom(const Prediction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator.Prediction)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  detection_classes_.MergeFrom(from.detection_classes_);
  weed_detection_classes_.MergeFrom(from.weed_detection_classes_);
  embedding_.MergeFrom(from.embedding_);
  mask_intersection_classes_.MergeFrom(from.mask_intersection_classes_);
  if (!from._internal_type().empty()) {
    _internal_set_type(from._internal_type());
  }
  if (from._internal_x_px() != 0) {
    _internal_set_x_px(from._internal_x_px());
  }
  if (from._internal_y_px() != 0) {
    _internal_set_y_px(from._internal_y_px());
  }
  if (from._internal_size_px() != 0) {
    _internal_set_size_px(from._internal_size_px());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = from._internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    _internal_set_score(from._internal_score());
  }
  if (from._internal_is_weed() != 0) {
    _internal_set_is_weed(from._internal_is_weed());
  }
  if (from._internal_is_real() != 0) {
    _internal_set_is_real(from._internal_is_real());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weed_score = from._internal_weed_score();
  uint32_t raw_weed_score;
  memcpy(&raw_weed_score, &tmp_weed_score, sizeof(tmp_weed_score));
  if (raw_weed_score != 0) {
    _internal_set_weed_score(from._internal_weed_score());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crop_score = from._internal_crop_score();
  uint32_t raw_crop_score;
  memcpy(&raw_crop_score, &tmp_crop_score, sizeof(tmp_crop_score));
  if (raw_crop_score != 0) {
    _internal_set_crop_score(from._internal_crop_score());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_plant_score = from._internal_plant_score();
  uint32_t raw_plant_score;
  memcpy(&raw_plant_score, &tmp_plant_score, sizeof(tmp_plant_score));
  if (raw_plant_score != 0) {
    _internal_set_plant_score(from._internal_plant_score());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Prediction::CopyFrom(const Prediction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator.Prediction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Prediction::IsInitialized() const {
  return true;
}

void Prediction::InternalSwap(Prediction* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  detection_classes_.InternalSwap(&other->detection_classes_);
  weed_detection_classes_.InternalSwap(&other->weed_detection_classes_);
  embedding_.InternalSwap(&other->embedding_);
  mask_intersection_classes_.InternalSwap(&other->mask_intersection_classes_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &type_, lhs_arena,
      &other->type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Prediction, plant_score_)
      + sizeof(Prediction::plant_score_)
      - PROTOBUF_FIELD_OFFSET(Prediction, x_px_)>(
          reinterpret_cast<char*>(&x_px_),
          reinterpret_cast<char*>(&other->x_px_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Prediction::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto[4]);
}

// ===================================================================

class GetNextPredictionsResponse::_Internal {
 public:
};

GetNextPredictionsResponse::GetNextPredictionsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  predictions_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.simulator.GetNextPredictionsResponse)
}
GetNextPredictionsResponse::GetNextPredictionsResponse(const GetNextPredictionsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      predictions_(from.predictions_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:carbon.simulator.GetNextPredictionsResponse)
}

inline void GetNextPredictionsResponse::SharedCtor() {
timestamp_ms_ = int64_t{0};
}

GetNextPredictionsResponse::~GetNextPredictionsResponse() {
  // @@protoc_insertion_point(destructor:carbon.simulator.GetNextPredictionsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextPredictionsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetNextPredictionsResponse::ArenaDtor(void* object) {
  GetNextPredictionsResponse* _this = reinterpret_cast< GetNextPredictionsResponse* >(object);
  (void)_this;
}
void GetNextPredictionsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextPredictionsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextPredictionsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.simulator.GetNextPredictionsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  predictions_.Clear();
  timestamp_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextPredictionsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.simulator.Prediction predictions = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_predictions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int64 timestamp_ms = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextPredictionsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.simulator.GetNextPredictionsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.simulator.Prediction predictions = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_predictions_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_predictions(i), target, stream);
  }

  // int64 timestamp_ms = 2;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_timestamp_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.simulator.GetNextPredictionsResponse)
  return target;
}

size_t GetNextPredictionsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.simulator.GetNextPredictionsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.simulator.Prediction predictions = 1;
  total_size += 1UL * this->_internal_predictions_size();
  for (const auto& msg : this->predictions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int64 timestamp_ms = 2;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextPredictionsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextPredictionsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextPredictionsResponse::GetClassData() const { return &_class_data_; }

void GetNextPredictionsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextPredictionsResponse *>(to)->MergeFrom(
      static_cast<const GetNextPredictionsResponse &>(from));
}


void GetNextPredictionsResponse::MergeFrom(const GetNextPredictionsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.simulator.GetNextPredictionsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  predictions_.MergeFrom(from.predictions_);
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextPredictionsResponse::CopyFrom(const GetNextPredictionsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.simulator.GetNextPredictionsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextPredictionsResponse::IsInitialized() const {
  return true;
}

void GetNextPredictionsResponse::InternalSwap(GetNextPredictionsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  predictions_.InternalSwap(&other->predictions_);
  swap(timestamp_ms_, other->timestamp_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextPredictionsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto[5]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace simulator
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::simulator::Empty* Arena::CreateMaybeMessage< ::carbon::simulator::Empty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator::Empty >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator::GetNextPredictionsRequest* Arena::CreateMaybeMessage< ::carbon::simulator::GetNextPredictionsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator::GetNextPredictionsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator::Prediction_DetectionClassesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator::Prediction_WeedDetectionClassesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator::Prediction* Arena::CreateMaybeMessage< ::carbon::simulator::Prediction >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator::Prediction >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::simulator::GetNextPredictionsResponse* Arena::CreateMaybeMessage< ::carbon::simulator::GetNextPredictionsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::simulator::GetNextPredictionsResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

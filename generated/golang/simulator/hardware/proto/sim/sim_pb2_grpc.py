# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.golang.simulator.hardware.proto.sim import sim_pb2 as golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2


class SimulatorServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/carbon.simulator.SimulatorService/Ping',
                request_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.Empty.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.Empty.FromString,
                )
        self.GetNextPredictions = channel.unary_unary(
                '/carbon.simulator.SimulatorService/GetNextPredictions',
                request_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.GetNextPredictionsRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.GetNextPredictionsResponse.FromString,
                )


class SimulatorServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextPredictions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SimulatorServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.Empty.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.Empty.SerializeToString,
            ),
            'GetNextPredictions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextPredictions,
                    request_deserializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.GetNextPredictionsRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.GetNextPredictionsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.simulator.SimulatorService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class SimulatorService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.simulator.SimulatorService/Ping',
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.Empty.SerializeToString,
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextPredictions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.simulator.SimulatorService/GetNextPredictions',
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.GetNextPredictionsRequest.SerializeToString,
            golang_dot_simulator_dot_hardware_dot_proto_dot_sim_dot_sim__pb2.GetNextPredictionsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

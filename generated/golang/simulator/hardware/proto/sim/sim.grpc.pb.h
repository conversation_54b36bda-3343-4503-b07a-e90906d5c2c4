// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: golang/simulator/hardware/proto/sim/sim.proto
#ifndef GRPC_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto__INCLUDED
#define GRPC_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto__INCLUDED

#include "golang/simulator/hardware/proto/sim/sim.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace simulator {

class SimulatorService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.simulator.SimulatorService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Ping(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::carbon::simulator::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::Empty>> AsyncPing(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::Empty>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::Empty>> PrepareAsyncPing(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::Empty>>(PrepareAsyncPingRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::carbon::simulator::GetNextPredictionsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::GetNextPredictionsResponse>> AsyncGetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::GetNextPredictionsResponse>>(AsyncGetNextPredictionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::GetNextPredictionsResponse>> PrepareAsyncGetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::GetNextPredictionsResponse>>(PrepareAsyncGetNextPredictionsRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Ping(::grpc::ClientContext* context, const ::carbon::simulator::Empty* request, ::carbon::simulator::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Ping(::grpc::ClientContext* context, const ::carbon::simulator::Empty* request, ::carbon::simulator::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest* request, ::carbon::simulator::GetNextPredictionsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest* request, ::carbon::simulator::GetNextPredictionsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::Empty>* AsyncPingRaw(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::Empty>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::GetNextPredictionsResponse>* AsyncGetNextPredictionsRaw(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::simulator::GetNextPredictionsResponse>* PrepareAsyncGetNextPredictionsRaw(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Ping(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::carbon::simulator::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator::Empty>> AsyncPing(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator::Empty>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator::Empty>> PrepareAsyncPing(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator::Empty>>(PrepareAsyncPingRaw(context, request, cq));
    }
    ::grpc::Status GetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::carbon::simulator::GetNextPredictionsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator::GetNextPredictionsResponse>> AsyncGetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator::GetNextPredictionsResponse>>(AsyncGetNextPredictionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator::GetNextPredictionsResponse>> PrepareAsyncGetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::simulator::GetNextPredictionsResponse>>(PrepareAsyncGetNextPredictionsRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Ping(::grpc::ClientContext* context, const ::carbon::simulator::Empty* request, ::carbon::simulator::Empty* response, std::function<void(::grpc::Status)>) override;
      void Ping(::grpc::ClientContext* context, const ::carbon::simulator::Empty* request, ::carbon::simulator::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest* request, ::carbon::simulator::GetNextPredictionsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextPredictions(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest* request, ::carbon::simulator::GetNextPredictionsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator::Empty>* AsyncPingRaw(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator::Empty>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::carbon::simulator::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator::GetNextPredictionsResponse>* AsyncGetNextPredictionsRaw(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::simulator::GetNextPredictionsResponse>* PrepareAsyncGetNextPredictionsRaw(::grpc::ClientContext* context, const ::carbon::simulator::GetNextPredictionsRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Ping_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextPredictions_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Ping(::grpc::ServerContext* context, const ::carbon::simulator::Empty* request, ::carbon::simulator::Empty* response);
    virtual ::grpc::Status GetNextPredictions(::grpc::ServerContext* context, const ::carbon::simulator::GetNextPredictionsRequest* request, ::carbon::simulator::GetNextPredictionsResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Ping() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::simulator::Empty* /*request*/, ::carbon::simulator::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::carbon::simulator::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::simulator::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextPredictions() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPredictions(::grpc::ServerContext* /*context*/, const ::carbon::simulator::GetNextPredictionsRequest* /*request*/, ::carbon::simulator::GetNextPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPredictions(::grpc::ServerContext* context, ::carbon::simulator::GetNextPredictionsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::simulator::GetNextPredictionsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Ping<WithAsyncMethod_GetNextPredictions<Service > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::simulator::Empty, ::carbon::simulator::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::simulator::Empty* request, ::carbon::simulator::Empty* response) { return this->Ping(context, request, response); }));}
    void SetMessageAllocatorFor_Ping(
        ::grpc::MessageAllocator< ::carbon::simulator::Empty, ::carbon::simulator::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::simulator::Empty, ::carbon::simulator::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::simulator::Empty* /*request*/, ::carbon::simulator::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::simulator::Empty* /*request*/, ::carbon::simulator::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextPredictions() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::simulator::GetNextPredictionsRequest, ::carbon::simulator::GetNextPredictionsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::simulator::GetNextPredictionsRequest* request, ::carbon::simulator::GetNextPredictionsResponse* response) { return this->GetNextPredictions(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextPredictions(
        ::grpc::MessageAllocator< ::carbon::simulator::GetNextPredictionsRequest, ::carbon::simulator::GetNextPredictionsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::simulator::GetNextPredictionsRequest, ::carbon::simulator::GetNextPredictionsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPredictions(::grpc::ServerContext* /*context*/, const ::carbon::simulator::GetNextPredictionsRequest* /*request*/, ::carbon::simulator::GetNextPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPredictions(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::simulator::GetNextPredictionsRequest* /*request*/, ::carbon::simulator::GetNextPredictionsResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Ping<WithCallbackMethod_GetNextPredictions<Service > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Ping() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::simulator::Empty* /*request*/, ::carbon::simulator::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextPredictions() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPredictions(::grpc::ServerContext* /*context*/, const ::carbon::simulator::GetNextPredictionsRequest* /*request*/, ::carbon::simulator::GetNextPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Ping() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::simulator::Empty* /*request*/, ::carbon::simulator::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextPredictions() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPredictions(::grpc::ServerContext* /*context*/, const ::carbon::simulator::GetNextPredictionsRequest* /*request*/, ::carbon::simulator::GetNextPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextPredictions(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Ping(context, request, response); }));
    }
    ~WithRawCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::simulator::Empty* /*request*/, ::carbon::simulator::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextPredictions() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextPredictions(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextPredictions(::grpc::ServerContext* /*context*/, const ::carbon::simulator::GetNextPredictionsRequest* /*request*/, ::carbon::simulator::GetNextPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextPredictions(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Ping() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::simulator::Empty, ::carbon::simulator::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::simulator::Empty, ::carbon::simulator::Empty>* streamer) {
                       return this->StreamedPing(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::simulator::Empty* /*request*/, ::carbon::simulator::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPing(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::simulator::Empty,::carbon::simulator::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextPredictions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextPredictions() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::simulator::GetNextPredictionsRequest, ::carbon::simulator::GetNextPredictionsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::simulator::GetNextPredictionsRequest, ::carbon::simulator::GetNextPredictionsResponse>* streamer) {
                       return this->StreamedGetNextPredictions(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextPredictions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextPredictions(::grpc::ServerContext* /*context*/, const ::carbon::simulator::GetNextPredictionsRequest* /*request*/, ::carbon::simulator::GetNextPredictionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextPredictions(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::simulator::GetNextPredictionsRequest,::carbon::simulator::GetNextPredictionsResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_GetNextPredictions<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_GetNextPredictions<Service > > StreamedService;
};

}  // namespace simulator
}  // namespace carbon


#endif  // GRPC_golang_2fsimulator_2fhardware_2fproto_2fsim_2fsim_2eproto__INCLUDED

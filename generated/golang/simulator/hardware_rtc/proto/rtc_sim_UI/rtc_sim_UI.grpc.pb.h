// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto
#ifndef GRPC_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto__INCLUDED
#define GRPC_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto__INCLUDED

#include "golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace rtc_sim_UI {

class RTCSimulatorUIService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.rtc_sim_UI.RTCSimulatorUIService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status SetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetInCabSwitchRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetInCabSwitchRaw(context, request, cq));
    }
    virtual ::grpc::Status SetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetSafetySensorsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetSafetySensorsRaw(context, request, cq));
    }
    virtual ::grpc::Status SetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetSpeedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetSpeedRaw(context, request, cq));
    }
    virtual ::grpc::Status SetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetGearRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetGearRaw(context, request, cq));
    }
    virtual ::grpc::Status SetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetLightsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetLightsRaw(context, request, cq));
    }
    virtual ::grpc::Status SetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetEstopRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetEstopRaw(context, request, cq));
    }
    virtual ::grpc::Status SetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetEngineRpmRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetEngineRpmRaw(context, request, cq));
    }
    virtual ::grpc::Status SetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetFrontPtoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetFrontPtoRaw(context, request, cq));
    }
    virtual ::grpc::Status SetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetRearPtoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetRearPtoRaw(context, request, cq));
    }
    virtual ::grpc::Status SetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetErrorFlagRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetErrorFlagRaw(context, request, cq));
    }
    virtual ::grpc::Status SetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::carbon::rtc_sim_UI::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> AsyncSetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(AsyncSetFuelLevelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetFuelLevelRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void SetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetInCabSwitchRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetInCabSwitchRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetSafetySensorsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetSafetySensorsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetSpeedRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetSpeedRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetGearRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetGearRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetLightsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetLightsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetEstopRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetEstopRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetEngineRpmRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetEngineRpmRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetFrontPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetFrontPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetRearPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetRearPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetErrorFlagRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetErrorFlagRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* AsyncSetFuelLevelRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetFuelLevelRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status SetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetInCabSwitchRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetInCabSwitchRaw(context, request, cq));
    }
    ::grpc::Status SetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetSafetySensorsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetSafetySensorsRaw(context, request, cq));
    }
    ::grpc::Status SetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetSpeedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetSpeedRaw(context, request, cq));
    }
    ::grpc::Status SetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetGearRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetGearRaw(context, request, cq));
    }
    ::grpc::Status SetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetLightsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetLightsRaw(context, request, cq));
    }
    ::grpc::Status SetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetEstopRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetEstopRaw(context, request, cq));
    }
    ::grpc::Status SetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetEngineRpmRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetEngineRpmRaw(context, request, cq));
    }
    ::grpc::Status SetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetFrontPtoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetFrontPtoRaw(context, request, cq));
    }
    ::grpc::Status SetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetRearPtoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetRearPtoRaw(context, request, cq));
    }
    ::grpc::Status SetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetErrorFlagRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetErrorFlagRaw(context, request, cq));
    }
    ::grpc::Status SetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::carbon::rtc_sim_UI::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> AsyncSetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(AsyncSetFuelLevelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>> PrepareAsyncSetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>>(PrepareAsyncSetFuelLevelRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void SetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetInCabSwitchRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetInCabSwitchRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetSafetySensorsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetSafetySensorsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetSpeedRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetSpeedRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetGearRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetGearRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetLightsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetLightsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetEstopRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetEstopRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetEngineRpmRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetEngineRpmRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetFrontPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetFrontPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetRearPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetRearPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetErrorFlagRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetErrorFlagRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* AsyncSetFuelLevelRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* PrepareAsyncSetFuelLevelRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_SetInCabSwitch_;
    const ::grpc::internal::RpcMethod rpcmethod_SetSafetySensors_;
    const ::grpc::internal::RpcMethod rpcmethod_SetSpeed_;
    const ::grpc::internal::RpcMethod rpcmethod_SetGear_;
    const ::grpc::internal::RpcMethod rpcmethod_SetLights_;
    const ::grpc::internal::RpcMethod rpcmethod_SetEstop_;
    const ::grpc::internal::RpcMethod rpcmethod_SetEngineRpm_;
    const ::grpc::internal::RpcMethod rpcmethod_SetFrontPto_;
    const ::grpc::internal::RpcMethod rpcmethod_SetRearPto_;
    const ::grpc::internal::RpcMethod rpcmethod_SetErrorFlag_;
    const ::grpc::internal::RpcMethod rpcmethod_SetFuelLevel_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status SetInCabSwitch(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetSafetySensors(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetSpeed(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetGear(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetGearRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetLights(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetEstop(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetEngineRpm(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetFrontPto(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetRearPto(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetErrorFlag(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::carbon::rtc_sim_UI::Empty* response);
    virtual ::grpc::Status SetFuelLevel(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::carbon::rtc_sim_UI::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_SetInCabSwitch : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetInCabSwitch() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_SetInCabSwitch() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetInCabSwitch(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetInCabSwitch(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::EnableRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetSafetySensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetSafetySensors() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_SetSafetySensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSafetySensors(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SafetySensorsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetSafetySensors(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetSpeed() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_SetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSpeed(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetSpeedRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetSpeed(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetGear : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetGear() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_SetGear() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetGear(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetGearRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetGear(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::SetGearRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetLights : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetLights() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_SetLights() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLights(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetLightsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLights(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::SetLightsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetEstop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetEstop() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_SetEstop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEstop(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetEstop(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::EnableRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetEngineRpm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetEngineRpm() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_SetEngineRpm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEngineRpm(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetEngineRpm(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetFrontPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetFrontPto() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_SetFrontPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFrontPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetFrontPto(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::EnableRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetRearPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetRearPto() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_SetRearPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRearPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetRearPto(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::EnableRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetErrorFlag : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetErrorFlag() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_SetErrorFlag() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetErrorFlag(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetErrorFlag(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetFuelLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetFuelLevel() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_SetFuelLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFuelLevel(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetFuelLevel(::grpc::ServerContext* context, ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc_sim_UI::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_SetInCabSwitch<WithAsyncMethod_SetSafetySensors<WithAsyncMethod_SetSpeed<WithAsyncMethod_SetGear<WithAsyncMethod_SetLights<WithAsyncMethod_SetEstop<WithAsyncMethod_SetEngineRpm<WithAsyncMethod_SetFrontPto<WithAsyncMethod_SetRearPto<WithAsyncMethod_SetErrorFlag<WithAsyncMethod_SetFuelLevel<Service > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_SetInCabSwitch : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetInCabSwitch() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetInCabSwitch(context, request, response); }));}
    void SetMessageAllocatorFor_SetInCabSwitch(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetInCabSwitch() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetInCabSwitch(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetInCabSwitch(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetSafetySensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetSafetySensors() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SafetySensorsRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetSafetySensors(context, request, response); }));}
    void SetMessageAllocatorFor_SetSafetySensors(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::SafetySensorsRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SafetySensorsRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetSafetySensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSafetySensors(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SafetySensorsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetSafetySensors(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::SafetySensorsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetSpeed() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetSpeedRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetSpeed(context, request, response); }));}
    void SetMessageAllocatorFor_SetSpeed(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::SetSpeedRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetSpeedRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSpeed(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetSpeedRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetSpeed(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetSpeedRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetGear : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetGear() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetGearRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::SetGearRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetGear(context, request, response); }));}
    void SetMessageAllocatorFor_SetGear(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::SetGearRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetGearRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetGear() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetGear(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetGearRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetGear(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetGearRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetLights : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetLights() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetLightsRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetLights(context, request, response); }));}
    void SetMessageAllocatorFor_SetLights(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::SetLightsRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetLightsRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetLights() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLights(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetLightsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLights(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetLightsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetEstop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetEstop() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetEstop(context, request, response); }));}
    void SetMessageAllocatorFor_SetEstop(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetEstop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEstop(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetEstop(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetEngineRpm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetEngineRpm() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetEngineRpmRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetEngineRpm(context, request, response); }));}
    void SetMessageAllocatorFor_SetEngineRpm(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::SetEngineRpmRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetEngineRpmRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetEngineRpm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEngineRpm(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetEngineRpm(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetFrontPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetFrontPto() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetFrontPto(context, request, response); }));}
    void SetMessageAllocatorFor_SetFrontPto(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetFrontPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFrontPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetFrontPto(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetRearPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetRearPto() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetRearPto(context, request, response); }));}
    void SetMessageAllocatorFor_SetRearPto(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetRearPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRearPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetRearPto(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetErrorFlag : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetErrorFlag() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetErrorFlagRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetErrorFlag(context, request, response); }));}
    void SetMessageAllocatorFor_SetErrorFlag(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::SetErrorFlagRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetErrorFlagRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetErrorFlag() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetErrorFlag(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetErrorFlag(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetFuelLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetFuelLevel() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetFuelLevelRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::carbon::rtc_sim_UI::Empty* response) { return this->SetFuelLevel(context, request, response); }));}
    void SetMessageAllocatorFor_SetFuelLevel(
        ::grpc::MessageAllocator< ::carbon::rtc_sim_UI::SetFuelLevelRequest, ::carbon::rtc_sim_UI::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc_sim_UI::SetFuelLevelRequest, ::carbon::rtc_sim_UI::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetFuelLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFuelLevel(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetFuelLevel(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_SetInCabSwitch<WithCallbackMethod_SetSafetySensors<WithCallbackMethod_SetSpeed<WithCallbackMethod_SetGear<WithCallbackMethod_SetLights<WithCallbackMethod_SetEstop<WithCallbackMethod_SetEngineRpm<WithCallbackMethod_SetFrontPto<WithCallbackMethod_SetRearPto<WithCallbackMethod_SetErrorFlag<WithCallbackMethod_SetFuelLevel<Service > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_SetInCabSwitch : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetInCabSwitch() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_SetInCabSwitch() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetInCabSwitch(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetSafetySensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetSafetySensors() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_SetSafetySensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSafetySensors(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SafetySensorsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetSpeed() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_SetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSpeed(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetSpeedRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetGear : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetGear() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_SetGear() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetGear(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetGearRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetLights : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetLights() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_SetLights() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLights(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetLightsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetEstop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetEstop() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_SetEstop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEstop(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetEngineRpm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetEngineRpm() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_SetEngineRpm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEngineRpm(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetFrontPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetFrontPto() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_SetFrontPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFrontPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetRearPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetRearPto() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_SetRearPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRearPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetErrorFlag : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetErrorFlag() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_SetErrorFlag() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetErrorFlag(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetFuelLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetFuelLevel() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_SetFuelLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFuelLevel(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetInCabSwitch : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetInCabSwitch() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_SetInCabSwitch() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetInCabSwitch(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetInCabSwitch(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetSafetySensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetSafetySensors() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_SetSafetySensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSafetySensors(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SafetySensorsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetSafetySensors(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetSpeed() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_SetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSpeed(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetSpeedRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetSpeed(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetGear : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetGear() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_SetGear() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetGear(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetGearRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetGear(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetLights : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetLights() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_SetLights() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLights(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetLightsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLights(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetEstop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetEstop() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_SetEstop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEstop(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetEstop(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetEngineRpm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetEngineRpm() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_SetEngineRpm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEngineRpm(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetEngineRpm(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetFrontPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetFrontPto() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_SetFrontPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFrontPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetFrontPto(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetRearPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetRearPto() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_SetRearPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRearPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetRearPto(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetErrorFlag : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetErrorFlag() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_SetErrorFlag() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetErrorFlag(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetErrorFlag(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetFuelLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetFuelLevel() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_SetFuelLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFuelLevel(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetFuelLevel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetInCabSwitch : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetInCabSwitch() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetInCabSwitch(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetInCabSwitch() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetInCabSwitch(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetInCabSwitch(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetSafetySensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetSafetySensors() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetSafetySensors(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetSafetySensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSafetySensors(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SafetySensorsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetSafetySensors(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetSpeed() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetSpeed(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetSpeed(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetSpeedRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetSpeed(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetGear : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetGear() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetGear(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetGear() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetGear(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetGearRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetGear(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetLights : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetLights() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetLights(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetLights() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLights(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetLightsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLights(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetEstop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetEstop() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetEstop(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetEstop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEstop(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetEstop(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetEngineRpm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetEngineRpm() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetEngineRpm(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetEngineRpm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEngineRpm(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetEngineRpm(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetFrontPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetFrontPto() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetFrontPto(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetFrontPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFrontPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetFrontPto(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetRearPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetRearPto() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetRearPto(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetRearPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRearPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetRearPto(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetErrorFlag : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetErrorFlag() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetErrorFlag(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetErrorFlag() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetErrorFlag(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetErrorFlag(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetFuelLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetFuelLevel() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetFuelLevel(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetFuelLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetFuelLevel(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetFuelLevel(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetInCabSwitch : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetInCabSwitch() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetInCabSwitch(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetInCabSwitch() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetInCabSwitch(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetInCabSwitch(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::EnableRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetSafetySensors : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetSafetySensors() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::SafetySensorsRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::SafetySensorsRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetSafetySensors(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetSafetySensors() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetSafetySensors(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SafetySensorsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetSafetySensors(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::SafetySensorsRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetSpeed : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetSpeed() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::SetSpeedRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::SetSpeedRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetSpeed(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetSpeed() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetSpeed(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetSpeedRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetSpeed(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::SetSpeedRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetGear : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetGear() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::SetGearRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::SetGearRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetGear(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetGear() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetGear(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetGearRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetGear(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::SetGearRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetLights : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetLights() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::SetLightsRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::SetLightsRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetLights(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetLights() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetLights(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetLightsRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetLights(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::SetLightsRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetEstop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetEstop() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetEstop(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetEstop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetEstop(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetEstop(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::EnableRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetEngineRpm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetEngineRpm() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::SetEngineRpmRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::SetEngineRpmRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetEngineRpm(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetEngineRpm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetEngineRpm(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetEngineRpm(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::SetEngineRpmRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetFrontPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetFrontPto() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetFrontPto(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetFrontPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetFrontPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetFrontPto(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::EnableRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetRearPto : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetRearPto() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetRearPto(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetRearPto() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetRearPto(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::EnableRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetRearPto(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::EnableRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetErrorFlag : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetErrorFlag() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::SetErrorFlagRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::SetErrorFlagRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetErrorFlag(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetErrorFlag() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetErrorFlag(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetErrorFlag(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::SetErrorFlagRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetFuelLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetFuelLevel() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc_sim_UI::SetFuelLevelRequest, ::carbon::rtc_sim_UI::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc_sim_UI::SetFuelLevelRequest, ::carbon::rtc_sim_UI::Empty>* streamer) {
                       return this->StreamedSetFuelLevel(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetFuelLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetFuelLevel(::grpc::ServerContext* /*context*/, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* /*request*/, ::carbon::rtc_sim_UI::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetFuelLevel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc_sim_UI::SetFuelLevelRequest,::carbon::rtc_sim_UI::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_SetInCabSwitch<WithStreamedUnaryMethod_SetSafetySensors<WithStreamedUnaryMethod_SetSpeed<WithStreamedUnaryMethod_SetGear<WithStreamedUnaryMethod_SetLights<WithStreamedUnaryMethod_SetEstop<WithStreamedUnaryMethod_SetEngineRpm<WithStreamedUnaryMethod_SetFrontPto<WithStreamedUnaryMethod_SetRearPto<WithStreamedUnaryMethod_SetErrorFlag<WithStreamedUnaryMethod_SetFuelLevel<Service > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_SetInCabSwitch<WithStreamedUnaryMethod_SetSafetySensors<WithStreamedUnaryMethod_SetSpeed<WithStreamedUnaryMethod_SetGear<WithStreamedUnaryMethod_SetLights<WithStreamedUnaryMethod_SetEstop<WithStreamedUnaryMethod_SetEngineRpm<WithStreamedUnaryMethod_SetFrontPto<WithStreamedUnaryMethod_SetRearPto<WithStreamedUnaryMethod_SetErrorFlag<WithStreamedUnaryMethod_SetFuelLevel<Service > > > > > > > > > > > StreamedService;
};

}  // namespace rtc_sim_UI
}  // namespace carbon


#endif  // GRPC_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto

#include "golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace rtc_sim_UI {
constexpr Empty::Empty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct EmptyDefaultTypeInternal {
  constexpr EmptyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EmptyDefaultTypeInternal() {}
  union {
    Empty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EmptyDefaultTypeInternal _Empty_default_instance_;
constexpr EnableRequest::EnableRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabled_(false){}
struct EnableRequestDefaultTypeInternal {
  constexpr EnableRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EnableRequestDefaultTypeInternal() {}
  union {
    EnableRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EnableRequestDefaultTypeInternal _EnableRequest_default_instance_;
constexpr SafetySensorsRequest::SafetySensorsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : sensor_1_(false)
  , sensor_2_(false)
  , sensor_3_(false)
  , sensor_4_(false){}
struct SafetySensorsRequestDefaultTypeInternal {
  constexpr SafetySensorsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SafetySensorsRequestDefaultTypeInternal() {}
  union {
    SafetySensorsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SafetySensorsRequestDefaultTypeInternal _SafetySensorsRequest_default_instance_;
constexpr SetSpeedRequest::SetSpeedRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : speed_mph_(0){}
struct SetSpeedRequestDefaultTypeInternal {
  constexpr SetSpeedRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetSpeedRequestDefaultTypeInternal() {}
  union {
    SetSpeedRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetSpeedRequestDefaultTypeInternal _SetSpeedRequest_default_instance_;
constexpr SetGearRequest::SetGearRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : gear_(0)
{}
struct SetGearRequestDefaultTypeInternal {
  constexpr SetGearRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetGearRequestDefaultTypeInternal() {}
  union {
    SetGearRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetGearRequestDefaultTypeInternal _SetGearRequest_default_instance_;
constexpr SetLightsRequest::SetLightsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lights_(0)
{}
struct SetLightsRequestDefaultTypeInternal {
  constexpr SetLightsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetLightsRequestDefaultTypeInternal() {}
  union {
    SetLightsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetLightsRequestDefaultTypeInternal _SetLightsRequest_default_instance_;
constexpr SetEngineRpmRequest::SetEngineRpmRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : rpms_(0){}
struct SetEngineRpmRequestDefaultTypeInternal {
  constexpr SetEngineRpmRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetEngineRpmRequestDefaultTypeInternal() {}
  union {
    SetEngineRpmRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetEngineRpmRequestDefaultTypeInternal _SetEngineRpmRequest_default_instance_;
constexpr SetErrorFlagRequest::SetErrorFlagRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : error_flag_(0){}
struct SetErrorFlagRequestDefaultTypeInternal {
  constexpr SetErrorFlagRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetErrorFlagRequestDefaultTypeInternal() {}
  union {
    SetErrorFlagRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetErrorFlagRequestDefaultTypeInternal _SetErrorFlagRequest_default_instance_;
constexpr SetFuelLevelRequest::SetFuelLevelRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : fuel_level_(0){}
struct SetFuelLevelRequestDefaultTypeInternal {
  constexpr SetFuelLevelRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetFuelLevelRequestDefaultTypeInternal() {}
  union {
    SetFuelLevelRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetFuelLevelRequestDefaultTypeInternal _SetFuelLevelRequest_default_instance_;
}  // namespace rtc_sim_UI
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[9];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto = nullptr;

const uint32_t TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::Empty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::EnableRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::EnableRequest, enabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SafetySensorsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SafetySensorsRequest, sensor_1_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SafetySensorsRequest, sensor_2_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SafetySensorsRequest, sensor_3_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SafetySensorsRequest, sensor_4_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetSpeedRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetSpeedRequest, speed_mph_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetGearRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetGearRequest, gear_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetLightsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetLightsRequest, lights_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetEngineRpmRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetEngineRpmRequest, rpms_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetErrorFlagRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetErrorFlagRequest, error_flag_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetFuelLevelRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc_sim_UI::SetFuelLevelRequest, fuel_level_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::rtc_sim_UI::Empty)},
  { 6, -1, -1, sizeof(::carbon::rtc_sim_UI::EnableRequest)},
  { 13, -1, -1, sizeof(::carbon::rtc_sim_UI::SafetySensorsRequest)},
  { 23, -1, -1, sizeof(::carbon::rtc_sim_UI::SetSpeedRequest)},
  { 30, -1, -1, sizeof(::carbon::rtc_sim_UI::SetGearRequest)},
  { 37, -1, -1, sizeof(::carbon::rtc_sim_UI::SetLightsRequest)},
  { 44, -1, -1, sizeof(::carbon::rtc_sim_UI::SetEngineRpmRequest)},
  { 51, -1, -1, sizeof(::carbon::rtc_sim_UI::SetErrorFlagRequest)},
  { 58, -1, -1, sizeof(::carbon::rtc_sim_UI::SetFuelLevelRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc_sim_UI::_Empty_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc_sim_UI::_EnableRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc_sim_UI::_SafetySensorsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc_sim_UI::_SetSpeedRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc_sim_UI::_SetGearRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc_sim_UI::_SetLightsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc_sim_UI::_SetEngineRpmRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc_sim_UI::_SetErrorFlagRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc_sim_UI::_SetFuelLevelRequest_default_instance_),
};

const char descriptor_table_protodef_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\?golang/simulator/hardware_rtc/proto/rt"
  "c_sim_UI/rtc_sim_UI.proto\022\021carbon.rtc_si"
  "m_UI\"\007\n\005Empty\" \n\rEnableRequest\022\017\n\007enable"
  "d\030\001 \001(\010\"^\n\024SafetySensorsRequest\022\020\n\010senso"
  "r_1\030\001 \001(\010\022\020\n\010sensor_2\030\002 \001(\010\022\020\n\010sensor_3\030"
  "\003 \001(\010\022\020\n\010sensor_4\030\004 \001(\010\"$\n\017SetSpeedReque"
  "st\022\021\n\tspeed_mph\030\001 \001(\002\"7\n\016SetGearRequest\022"
  "%\n\004gear\030\001 \001(\0162\027.carbon.rtc_sim_UI.Gear\"="
  "\n\020SetLightsRequest\022)\n\006lights\030\001 \001(\0162\031.car"
  "bon.rtc_sim_UI.Lights\"#\n\023SetEngineRpmReq"
  "uest\022\014\n\004rpms\030\001 \001(\005\")\n\023SetErrorFlagReques"
  "t\022\022\n\nerror_flag\030\001 \001(\005\")\n\023SetFuelLevelReq"
  "uest\022\022\n\nfuel_level\030\001 \001(\002*_\n\004Gear\022\r\n\tGEAR"
  "_PARK\020\000\022\020\n\014GEAR_REVERSE\020\001\022\020\n\014GEAR_NEUTRA"
  "L\020\002\022\020\n\014GEAR_FORWARD\020\003\022\022\n\016GEAR_POWERZERO\020"
  "\004*9\n\006Lights\022\016\n\nLIGHTS_OFF\020\000\022\016\n\nLIGHTS_LO"
  "W\020\001\022\017\n\013LIGHTS_HIGH\020\0022\203\007\n\025RTCSimulatorUIS"
  "ervice\022N\n\016SetInCabSwitch\022 .carbon.rtc_si"
  "m_UI.EnableRequest\032\030.carbon.rtc_sim_UI.E"
  "mpty\"\000\022W\n\020SetSafetySensors\022\'.carbon.rtc_"
  "sim_UI.SafetySensorsRequest\032\030.carbon.rtc"
  "_sim_UI.Empty\"\000\022J\n\010SetSpeed\022\".carbon.rtc"
  "_sim_UI.SetSpeedRequest\032\030.carbon.rtc_sim"
  "_UI.Empty\"\000\022H\n\007SetGear\022!.carbon.rtc_sim_"
  "UI.SetGearRequest\032\030.carbon.rtc_sim_UI.Em"
  "pty\"\000\022L\n\tSetLights\022#.carbon.rtc_sim_UI.S"
  "etLightsRequest\032\030.carbon.rtc_sim_UI.Empt"
  "y\"\000\022H\n\010SetEstop\022 .carbon.rtc_sim_UI.Enab"
  "leRequest\032\030.carbon.rtc_sim_UI.Empty\"\000\022R\n"
  "\014SetEngineRpm\022&.carbon.rtc_sim_UI.SetEng"
  "ineRpmRequest\032\030.carbon.rtc_sim_UI.Empty\""
  "\000\022K\n\013SetFrontPto\022 .carbon.rtc_sim_UI.Ena"
  "bleRequest\032\030.carbon.rtc_sim_UI.Empty\"\000\022J"
  "\n\nSetRearPto\022 .carbon.rtc_sim_UI.EnableR"
  "equest\032\030.carbon.rtc_sim_UI.Empty\"\000\022R\n\014Se"
  "tErrorFlag\022&.carbon.rtc_sim_UI.SetErrorF"
  "lagRequest\032\030.carbon.rtc_sim_UI.Empty\"\000\022R"
  "\n\014SetFuelLevel\022&.carbon.rtc_sim_UI.SetFu"
  "elLevelRequest\032\030.carbon.rtc_sim_UI.Empty"
  "\"\000B\022Z\020proto/rtc_sim_UIb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto = {
  false, false, 1590, descriptor_table_protodef_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto, "golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto", 
  &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once, nullptr, 0, 9,
  schemas, file_default_instances, TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto::offsets,
  file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto, file_level_enum_descriptors_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto, file_level_service_descriptors_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter() {
  return &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto(&descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto);
namespace carbon {
namespace rtc_sim_UI {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Gear_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto);
  return file_level_enum_descriptors_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[0];
}
bool Gear_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lights_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto);
  return file_level_enum_descriptors_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[1];
}
bool Lights_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class Empty::_Internal {
 public:
};

Empty::Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.rtc_sim_UI.Empty)
}
Empty::Empty(const Empty& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc_sim_UI.Empty)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Empty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Empty::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata Empty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[0]);
}

// ===================================================================

class EnableRequest::_Internal {
 public:
};

EnableRequest::EnableRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc_sim_UI.EnableRequest)
}
EnableRequest::EnableRequest(const EnableRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  enabled_ = from.enabled_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc_sim_UI.EnableRequest)
}

inline void EnableRequest::SharedCtor() {
enabled_ = false;
}

EnableRequest::~EnableRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc_sim_UI.EnableRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EnableRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EnableRequest::ArenaDtor(void* object) {
  EnableRequest* _this = reinterpret_cast< EnableRequest* >(object);
  (void)_this;
}
void EnableRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EnableRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EnableRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc_sim_UI.EnableRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enabled_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EnableRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* EnableRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc_sim_UI.EnableRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc_sim_UI.EnableRequest)
  return target;
}

size_t EnableRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc_sim_UI.EnableRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EnableRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EnableRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EnableRequest::GetClassData() const { return &_class_data_; }

void EnableRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<EnableRequest *>(to)->MergeFrom(
      static_cast<const EnableRequest &>(from));
}


void EnableRequest::MergeFrom(const EnableRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc_sim_UI.EnableRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_enabled() != 0) {
    _internal_set_enabled(from._internal_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EnableRequest::CopyFrom(const EnableRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc_sim_UI.EnableRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EnableRequest::IsInitialized() const {
  return true;
}

void EnableRequest::InternalSwap(EnableRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(enabled_, other->enabled_);
}

::PROTOBUF_NAMESPACE_ID::Metadata EnableRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[1]);
}

// ===================================================================

class SafetySensorsRequest::_Internal {
 public:
};

SafetySensorsRequest::SafetySensorsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc_sim_UI.SafetySensorsRequest)
}
SafetySensorsRequest::SafetySensorsRequest(const SafetySensorsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&sensor_1_, &from.sensor_1_,
    static_cast<size_t>(reinterpret_cast<char*>(&sensor_4_) -
    reinterpret_cast<char*>(&sensor_1_)) + sizeof(sensor_4_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc_sim_UI.SafetySensorsRequest)
}

inline void SafetySensorsRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&sensor_1_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&sensor_4_) -
    reinterpret_cast<char*>(&sensor_1_)) + sizeof(sensor_4_));
}

SafetySensorsRequest::~SafetySensorsRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc_sim_UI.SafetySensorsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SafetySensorsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SafetySensorsRequest::ArenaDtor(void* object) {
  SafetySensorsRequest* _this = reinterpret_cast< SafetySensorsRequest* >(object);
  (void)_this;
}
void SafetySensorsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SafetySensorsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SafetySensorsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc_sim_UI.SafetySensorsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&sensor_1_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&sensor_4_) -
      reinterpret_cast<char*>(&sensor_1_)) + sizeof(sensor_4_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SafetySensorsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool sensor_1 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          sensor_1_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool sensor_2 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          sensor_2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool sensor_3 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          sensor_3_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool sensor_4 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          sensor_4_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SafetySensorsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc_sim_UI.SafetySensorsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool sensor_1 = 1;
  if (this->_internal_sensor_1() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_sensor_1(), target);
  }

  // bool sensor_2 = 2;
  if (this->_internal_sensor_2() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_sensor_2(), target);
  }

  // bool sensor_3 = 3;
  if (this->_internal_sensor_3() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_sensor_3(), target);
  }

  // bool sensor_4 = 4;
  if (this->_internal_sensor_4() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_sensor_4(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc_sim_UI.SafetySensorsRequest)
  return target;
}

size_t SafetySensorsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc_sim_UI.SafetySensorsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool sensor_1 = 1;
  if (this->_internal_sensor_1() != 0) {
    total_size += 1 + 1;
  }

  // bool sensor_2 = 2;
  if (this->_internal_sensor_2() != 0) {
    total_size += 1 + 1;
  }

  // bool sensor_3 = 3;
  if (this->_internal_sensor_3() != 0) {
    total_size += 1 + 1;
  }

  // bool sensor_4 = 4;
  if (this->_internal_sensor_4() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SafetySensorsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SafetySensorsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SafetySensorsRequest::GetClassData() const { return &_class_data_; }

void SafetySensorsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SafetySensorsRequest *>(to)->MergeFrom(
      static_cast<const SafetySensorsRequest &>(from));
}


void SafetySensorsRequest::MergeFrom(const SafetySensorsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc_sim_UI.SafetySensorsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_sensor_1() != 0) {
    _internal_set_sensor_1(from._internal_sensor_1());
  }
  if (from._internal_sensor_2() != 0) {
    _internal_set_sensor_2(from._internal_sensor_2());
  }
  if (from._internal_sensor_3() != 0) {
    _internal_set_sensor_3(from._internal_sensor_3());
  }
  if (from._internal_sensor_4() != 0) {
    _internal_set_sensor_4(from._internal_sensor_4());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SafetySensorsRequest::CopyFrom(const SafetySensorsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc_sim_UI.SafetySensorsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SafetySensorsRequest::IsInitialized() const {
  return true;
}

void SafetySensorsRequest::InternalSwap(SafetySensorsRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SafetySensorsRequest, sensor_4_)
      + sizeof(SafetySensorsRequest::sensor_4_)
      - PROTOBUF_FIELD_OFFSET(SafetySensorsRequest, sensor_1_)>(
          reinterpret_cast<char*>(&sensor_1_),
          reinterpret_cast<char*>(&other->sensor_1_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SafetySensorsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[2]);
}

// ===================================================================

class SetSpeedRequest::_Internal {
 public:
};

SetSpeedRequest::SetSpeedRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc_sim_UI.SetSpeedRequest)
}
SetSpeedRequest::SetSpeedRequest(const SetSpeedRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  speed_mph_ = from.speed_mph_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc_sim_UI.SetSpeedRequest)
}

inline void SetSpeedRequest::SharedCtor() {
speed_mph_ = 0;
}

SetSpeedRequest::~SetSpeedRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc_sim_UI.SetSpeedRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetSpeedRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetSpeedRequest::ArenaDtor(void* object) {
  SetSpeedRequest* _this = reinterpret_cast< SetSpeedRequest* >(object);
  (void)_this;
}
void SetSpeedRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetSpeedRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetSpeedRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc_sim_UI.SetSpeedRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  speed_mph_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetSpeedRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float speed_mph = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          speed_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetSpeedRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc_sim_UI.SetSpeedRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float speed_mph = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed_mph = this->_internal_speed_mph();
  uint32_t raw_speed_mph;
  memcpy(&raw_speed_mph, &tmp_speed_mph, sizeof(tmp_speed_mph));
  if (raw_speed_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_speed_mph(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc_sim_UI.SetSpeedRequest)
  return target;
}

size_t SetSpeedRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc_sim_UI.SetSpeedRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float speed_mph = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed_mph = this->_internal_speed_mph();
  uint32_t raw_speed_mph;
  memcpy(&raw_speed_mph, &tmp_speed_mph, sizeof(tmp_speed_mph));
  if (raw_speed_mph != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetSpeedRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetSpeedRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetSpeedRequest::GetClassData() const { return &_class_data_; }

void SetSpeedRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetSpeedRequest *>(to)->MergeFrom(
      static_cast<const SetSpeedRequest &>(from));
}


void SetSpeedRequest::MergeFrom(const SetSpeedRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc_sim_UI.SetSpeedRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed_mph = from._internal_speed_mph();
  uint32_t raw_speed_mph;
  memcpy(&raw_speed_mph, &tmp_speed_mph, sizeof(tmp_speed_mph));
  if (raw_speed_mph != 0) {
    _internal_set_speed_mph(from._internal_speed_mph());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetSpeedRequest::CopyFrom(const SetSpeedRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc_sim_UI.SetSpeedRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetSpeedRequest::IsInitialized() const {
  return true;
}

void SetSpeedRequest::InternalSwap(SetSpeedRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(speed_mph_, other->speed_mph_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetSpeedRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[3]);
}

// ===================================================================

class SetGearRequest::_Internal {
 public:
};

SetGearRequest::SetGearRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc_sim_UI.SetGearRequest)
}
SetGearRequest::SetGearRequest(const SetGearRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  gear_ = from.gear_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc_sim_UI.SetGearRequest)
}

inline void SetGearRequest::SharedCtor() {
gear_ = 0;
}

SetGearRequest::~SetGearRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc_sim_UI.SetGearRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetGearRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetGearRequest::ArenaDtor(void* object) {
  SetGearRequest* _this = reinterpret_cast< SetGearRequest* >(object);
  (void)_this;
}
void SetGearRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetGearRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetGearRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc_sim_UI.SetGearRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  gear_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetGearRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc_sim_UI.Gear gear = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_gear(static_cast<::carbon::rtc_sim_UI::Gear>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetGearRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc_sim_UI.SetGearRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc_sim_UI.Gear gear = 1;
  if (this->_internal_gear() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_gear(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc_sim_UI.SetGearRequest)
  return target;
}

size_t SetGearRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc_sim_UI.SetGearRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc_sim_UI.Gear gear = 1;
  if (this->_internal_gear() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_gear());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetGearRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetGearRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetGearRequest::GetClassData() const { return &_class_data_; }

void SetGearRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetGearRequest *>(to)->MergeFrom(
      static_cast<const SetGearRequest &>(from));
}


void SetGearRequest::MergeFrom(const SetGearRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc_sim_UI.SetGearRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_gear() != 0) {
    _internal_set_gear(from._internal_gear());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetGearRequest::CopyFrom(const SetGearRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc_sim_UI.SetGearRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetGearRequest::IsInitialized() const {
  return true;
}

void SetGearRequest::InternalSwap(SetGearRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(gear_, other->gear_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetGearRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[4]);
}

// ===================================================================

class SetLightsRequest::_Internal {
 public:
};

SetLightsRequest::SetLightsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc_sim_UI.SetLightsRequest)
}
SetLightsRequest::SetLightsRequest(const SetLightsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  lights_ = from.lights_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc_sim_UI.SetLightsRequest)
}

inline void SetLightsRequest::SharedCtor() {
lights_ = 0;
}

SetLightsRequest::~SetLightsRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc_sim_UI.SetLightsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetLightsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetLightsRequest::ArenaDtor(void* object) {
  SetLightsRequest* _this = reinterpret_cast< SetLightsRequest* >(object);
  (void)_this;
}
void SetLightsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetLightsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetLightsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc_sim_UI.SetLightsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lights_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetLightsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc_sim_UI.Lights lights = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_lights(static_cast<::carbon::rtc_sim_UI::Lights>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetLightsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc_sim_UI.SetLightsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc_sim_UI.Lights lights = 1;
  if (this->_internal_lights() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_lights(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc_sim_UI.SetLightsRequest)
  return target;
}

size_t SetLightsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc_sim_UI.SetLightsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc_sim_UI.Lights lights = 1;
  if (this->_internal_lights() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_lights());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetLightsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetLightsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetLightsRequest::GetClassData() const { return &_class_data_; }

void SetLightsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetLightsRequest *>(to)->MergeFrom(
      static_cast<const SetLightsRequest &>(from));
}


void SetLightsRequest::MergeFrom(const SetLightsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc_sim_UI.SetLightsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_lights() != 0) {
    _internal_set_lights(from._internal_lights());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetLightsRequest::CopyFrom(const SetLightsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc_sim_UI.SetLightsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetLightsRequest::IsInitialized() const {
  return true;
}

void SetLightsRequest::InternalSwap(SetLightsRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(lights_, other->lights_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetLightsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[5]);
}

// ===================================================================

class SetEngineRpmRequest::_Internal {
 public:
};

SetEngineRpmRequest::SetEngineRpmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc_sim_UI.SetEngineRpmRequest)
}
SetEngineRpmRequest::SetEngineRpmRequest(const SetEngineRpmRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  rpms_ = from.rpms_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc_sim_UI.SetEngineRpmRequest)
}

inline void SetEngineRpmRequest::SharedCtor() {
rpms_ = 0;
}

SetEngineRpmRequest::~SetEngineRpmRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc_sim_UI.SetEngineRpmRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetEngineRpmRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetEngineRpmRequest::ArenaDtor(void* object) {
  SetEngineRpmRequest* _this = reinterpret_cast< SetEngineRpmRequest* >(object);
  (void)_this;
}
void SetEngineRpmRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetEngineRpmRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetEngineRpmRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc_sim_UI.SetEngineRpmRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rpms_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetEngineRpmRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 rpms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          rpms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetEngineRpmRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc_sim_UI.SetEngineRpmRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 rpms = 1;
  if (this->_internal_rpms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_rpms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc_sim_UI.SetEngineRpmRequest)
  return target;
}

size_t SetEngineRpmRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc_sim_UI.SetEngineRpmRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 rpms = 1;
  if (this->_internal_rpms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_rpms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetEngineRpmRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetEngineRpmRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetEngineRpmRequest::GetClassData() const { return &_class_data_; }

void SetEngineRpmRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetEngineRpmRequest *>(to)->MergeFrom(
      static_cast<const SetEngineRpmRequest &>(from));
}


void SetEngineRpmRequest::MergeFrom(const SetEngineRpmRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc_sim_UI.SetEngineRpmRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_rpms() != 0) {
    _internal_set_rpms(from._internal_rpms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetEngineRpmRequest::CopyFrom(const SetEngineRpmRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc_sim_UI.SetEngineRpmRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetEngineRpmRequest::IsInitialized() const {
  return true;
}

void SetEngineRpmRequest::InternalSwap(SetEngineRpmRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(rpms_, other->rpms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetEngineRpmRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[6]);
}

// ===================================================================

class SetErrorFlagRequest::_Internal {
 public:
};

SetErrorFlagRequest::SetErrorFlagRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc_sim_UI.SetErrorFlagRequest)
}
SetErrorFlagRequest::SetErrorFlagRequest(const SetErrorFlagRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  error_flag_ = from.error_flag_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc_sim_UI.SetErrorFlagRequest)
}

inline void SetErrorFlagRequest::SharedCtor() {
error_flag_ = 0;
}

SetErrorFlagRequest::~SetErrorFlagRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc_sim_UI.SetErrorFlagRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetErrorFlagRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetErrorFlagRequest::ArenaDtor(void* object) {
  SetErrorFlagRequest* _this = reinterpret_cast< SetErrorFlagRequest* >(object);
  (void)_this;
}
void SetErrorFlagRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetErrorFlagRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetErrorFlagRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc_sim_UI.SetErrorFlagRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  error_flag_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetErrorFlagRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 error_flag = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          error_flag_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetErrorFlagRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc_sim_UI.SetErrorFlagRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 error_flag = 1;
  if (this->_internal_error_flag() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_error_flag(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc_sim_UI.SetErrorFlagRequest)
  return target;
}

size_t SetErrorFlagRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc_sim_UI.SetErrorFlagRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 error_flag = 1;
  if (this->_internal_error_flag() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_error_flag());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetErrorFlagRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetErrorFlagRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetErrorFlagRequest::GetClassData() const { return &_class_data_; }

void SetErrorFlagRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetErrorFlagRequest *>(to)->MergeFrom(
      static_cast<const SetErrorFlagRequest &>(from));
}


void SetErrorFlagRequest::MergeFrom(const SetErrorFlagRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc_sim_UI.SetErrorFlagRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_error_flag() != 0) {
    _internal_set_error_flag(from._internal_error_flag());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetErrorFlagRequest::CopyFrom(const SetErrorFlagRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc_sim_UI.SetErrorFlagRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetErrorFlagRequest::IsInitialized() const {
  return true;
}

void SetErrorFlagRequest::InternalSwap(SetErrorFlagRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(error_flag_, other->error_flag_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetErrorFlagRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[7]);
}

// ===================================================================

class SetFuelLevelRequest::_Internal {
 public:
};

SetFuelLevelRequest::SetFuelLevelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc_sim_UI.SetFuelLevelRequest)
}
SetFuelLevelRequest::SetFuelLevelRequest(const SetFuelLevelRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  fuel_level_ = from.fuel_level_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc_sim_UI.SetFuelLevelRequest)
}

inline void SetFuelLevelRequest::SharedCtor() {
fuel_level_ = 0;
}

SetFuelLevelRequest::~SetFuelLevelRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc_sim_UI.SetFuelLevelRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetFuelLevelRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetFuelLevelRequest::ArenaDtor(void* object) {
  SetFuelLevelRequest* _this = reinterpret_cast< SetFuelLevelRequest* >(object);
  (void)_this;
}
void SetFuelLevelRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetFuelLevelRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetFuelLevelRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc_sim_UI.SetFuelLevelRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  fuel_level_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetFuelLevelRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float fuel_level = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          fuel_level_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetFuelLevelRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc_sim_UI.SetFuelLevelRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float fuel_level = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fuel_level = this->_internal_fuel_level();
  uint32_t raw_fuel_level;
  memcpy(&raw_fuel_level, &tmp_fuel_level, sizeof(tmp_fuel_level));
  if (raw_fuel_level != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_fuel_level(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc_sim_UI.SetFuelLevelRequest)
  return target;
}

size_t SetFuelLevelRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc_sim_UI.SetFuelLevelRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float fuel_level = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fuel_level = this->_internal_fuel_level();
  uint32_t raw_fuel_level;
  memcpy(&raw_fuel_level, &tmp_fuel_level, sizeof(tmp_fuel_level));
  if (raw_fuel_level != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetFuelLevelRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetFuelLevelRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetFuelLevelRequest::GetClassData() const { return &_class_data_; }

void SetFuelLevelRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetFuelLevelRequest *>(to)->MergeFrom(
      static_cast<const SetFuelLevelRequest &>(from));
}


void SetFuelLevelRequest::MergeFrom(const SetFuelLevelRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc_sim_UI.SetFuelLevelRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fuel_level = from._internal_fuel_level();
  uint32_t raw_fuel_level;
  memcpy(&raw_fuel_level, &tmp_fuel_level, sizeof(tmp_fuel_level));
  if (raw_fuel_level != 0) {
    _internal_set_fuel_level(from._internal_fuel_level());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetFuelLevelRequest::CopyFrom(const SetFuelLevelRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc_sim_UI.SetFuelLevelRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetFuelLevelRequest::IsInitialized() const {
  return true;
}

void SetFuelLevelRequest::InternalSwap(SetFuelLevelRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(fuel_level_, other->fuel_level_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetFuelLevelRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_getter, &descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto_once,
      file_level_metadata_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto[8]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace rtc_sim_UI
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::rtc_sim_UI::Empty* Arena::CreateMaybeMessage< ::carbon::rtc_sim_UI::Empty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc_sim_UI::Empty >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc_sim_UI::EnableRequest* Arena::CreateMaybeMessage< ::carbon::rtc_sim_UI::EnableRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc_sim_UI::EnableRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc_sim_UI::SafetySensorsRequest* Arena::CreateMaybeMessage< ::carbon::rtc_sim_UI::SafetySensorsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc_sim_UI::SafetySensorsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc_sim_UI::SetSpeedRequest* Arena::CreateMaybeMessage< ::carbon::rtc_sim_UI::SetSpeedRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc_sim_UI::SetSpeedRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc_sim_UI::SetGearRequest* Arena::CreateMaybeMessage< ::carbon::rtc_sim_UI::SetGearRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc_sim_UI::SetGearRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc_sim_UI::SetLightsRequest* Arena::CreateMaybeMessage< ::carbon::rtc_sim_UI::SetLightsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc_sim_UI::SetLightsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc_sim_UI::SetEngineRpmRequest* Arena::CreateMaybeMessage< ::carbon::rtc_sim_UI::SetEngineRpmRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc_sim_UI::SetEngineRpmRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc_sim_UI::SetErrorFlagRequest* Arena::CreateMaybeMessage< ::carbon::rtc_sim_UI::SetErrorFlagRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc_sim_UI::SetErrorFlagRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc_sim_UI::SetFuelLevelRequest* Arena::CreateMaybeMessage< ::carbon::rtc_sim_UI::SetFuelLevelRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc_sim_UI::SetFuelLevelRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

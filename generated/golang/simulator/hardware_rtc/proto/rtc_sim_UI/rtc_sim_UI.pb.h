// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[9]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
namespace carbon {
namespace rtc_sim_UI {
class Empty;
struct EmptyDefaultTypeInternal;
extern EmptyDefaultTypeInternal _Empty_default_instance_;
class EnableRequest;
struct EnableRequestDefaultTypeInternal;
extern EnableRequestDefaultTypeInternal _EnableRequest_default_instance_;
class SafetySensorsRequest;
struct SafetySensorsRequestDefaultTypeInternal;
extern SafetySensorsRequestDefaultTypeInternal _SafetySensorsRequest_default_instance_;
class SetEngineRpmRequest;
struct SetEngineRpmRequestDefaultTypeInternal;
extern SetEngineRpmRequestDefaultTypeInternal _SetEngineRpmRequest_default_instance_;
class SetErrorFlagRequest;
struct SetErrorFlagRequestDefaultTypeInternal;
extern SetErrorFlagRequestDefaultTypeInternal _SetErrorFlagRequest_default_instance_;
class SetFuelLevelRequest;
struct SetFuelLevelRequestDefaultTypeInternal;
extern SetFuelLevelRequestDefaultTypeInternal _SetFuelLevelRequest_default_instance_;
class SetGearRequest;
struct SetGearRequestDefaultTypeInternal;
extern SetGearRequestDefaultTypeInternal _SetGearRequest_default_instance_;
class SetLightsRequest;
struct SetLightsRequestDefaultTypeInternal;
extern SetLightsRequestDefaultTypeInternal _SetLightsRequest_default_instance_;
class SetSpeedRequest;
struct SetSpeedRequestDefaultTypeInternal;
extern SetSpeedRequestDefaultTypeInternal _SetSpeedRequest_default_instance_;
}  // namespace rtc_sim_UI
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::rtc_sim_UI::Empty* Arena::CreateMaybeMessage<::carbon::rtc_sim_UI::Empty>(Arena*);
template<> ::carbon::rtc_sim_UI::EnableRequest* Arena::CreateMaybeMessage<::carbon::rtc_sim_UI::EnableRequest>(Arena*);
template<> ::carbon::rtc_sim_UI::SafetySensorsRequest* Arena::CreateMaybeMessage<::carbon::rtc_sim_UI::SafetySensorsRequest>(Arena*);
template<> ::carbon::rtc_sim_UI::SetEngineRpmRequest* Arena::CreateMaybeMessage<::carbon::rtc_sim_UI::SetEngineRpmRequest>(Arena*);
template<> ::carbon::rtc_sim_UI::SetErrorFlagRequest* Arena::CreateMaybeMessage<::carbon::rtc_sim_UI::SetErrorFlagRequest>(Arena*);
template<> ::carbon::rtc_sim_UI::SetFuelLevelRequest* Arena::CreateMaybeMessage<::carbon::rtc_sim_UI::SetFuelLevelRequest>(Arena*);
template<> ::carbon::rtc_sim_UI::SetGearRequest* Arena::CreateMaybeMessage<::carbon::rtc_sim_UI::SetGearRequest>(Arena*);
template<> ::carbon::rtc_sim_UI::SetLightsRequest* Arena::CreateMaybeMessage<::carbon::rtc_sim_UI::SetLightsRequest>(Arena*);
template<> ::carbon::rtc_sim_UI::SetSpeedRequest* Arena::CreateMaybeMessage<::carbon::rtc_sim_UI::SetSpeedRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace rtc_sim_UI {

enum Gear : int {
  GEAR_PARK = 0,
  GEAR_REVERSE = 1,
  GEAR_NEUTRAL = 2,
  GEAR_FORWARD = 3,
  GEAR_POWERZERO = 4,
  Gear_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Gear_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Gear_IsValid(int value);
constexpr Gear Gear_MIN = GEAR_PARK;
constexpr Gear Gear_MAX = GEAR_POWERZERO;
constexpr int Gear_ARRAYSIZE = Gear_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Gear_descriptor();
template<typename T>
inline const std::string& Gear_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Gear>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Gear_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Gear_descriptor(), enum_t_value);
}
inline bool Gear_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Gear* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Gear>(
    Gear_descriptor(), name, value);
}
enum Lights : int {
  LIGHTS_OFF = 0,
  LIGHTS_LOW = 1,
  LIGHTS_HIGH = 2,
  Lights_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Lights_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Lights_IsValid(int value);
constexpr Lights Lights_MIN = LIGHTS_OFF;
constexpr Lights Lights_MAX = LIGHTS_HIGH;
constexpr int Lights_ARRAYSIZE = Lights_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lights_descriptor();
template<typename T>
inline const std::string& Lights_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Lights>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Lights_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Lights_descriptor(), enum_t_value);
}
inline bool Lights_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Lights* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Lights>(
    Lights_descriptor(), name, value);
}
// ===================================================================

class Empty final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.rtc_sim_UI.Empty) */ {
 public:
  inline Empty() : Empty(nullptr) {}
  explicit constexpr Empty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Empty(const Empty& from);
  Empty(Empty&& from) noexcept
    : Empty() {
    *this = ::std::move(from);
  }

  inline Empty& operator=(const Empty& from) {
    CopyFrom(from);
    return *this;
  }
  inline Empty& operator=(Empty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Empty& default_instance() {
    return *internal_default_instance();
  }
  static inline const Empty* internal_default_instance() {
    return reinterpret_cast<const Empty*>(
               &_Empty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Empty& a, Empty& b) {
    a.Swap(&b);
  }
  inline void Swap(Empty* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Empty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Empty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Empty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const Empty& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const Empty& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc_sim_UI.Empty";
  }
  protected:
  explicit Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.Empty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class EnableRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc_sim_UI.EnableRequest) */ {
 public:
  inline EnableRequest() : EnableRequest(nullptr) {}
  ~EnableRequest() override;
  explicit constexpr EnableRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EnableRequest(const EnableRequest& from);
  EnableRequest(EnableRequest&& from) noexcept
    : EnableRequest() {
    *this = ::std::move(from);
  }

  inline EnableRequest& operator=(const EnableRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnableRequest& operator=(EnableRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EnableRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const EnableRequest* internal_default_instance() {
    return reinterpret_cast<const EnableRequest*>(
               &_EnableRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(EnableRequest& a, EnableRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(EnableRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EnableRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EnableRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EnableRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EnableRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EnableRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnableRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc_sim_UI.EnableRequest";
  }
  protected:
  explicit EnableRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
  };
  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.EnableRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SafetySensorsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc_sim_UI.SafetySensorsRequest) */ {
 public:
  inline SafetySensorsRequest() : SafetySensorsRequest(nullptr) {}
  ~SafetySensorsRequest() override;
  explicit constexpr SafetySensorsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SafetySensorsRequest(const SafetySensorsRequest& from);
  SafetySensorsRequest(SafetySensorsRequest&& from) noexcept
    : SafetySensorsRequest() {
    *this = ::std::move(from);
  }

  inline SafetySensorsRequest& operator=(const SafetySensorsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SafetySensorsRequest& operator=(SafetySensorsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SafetySensorsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SafetySensorsRequest* internal_default_instance() {
    return reinterpret_cast<const SafetySensorsRequest*>(
               &_SafetySensorsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SafetySensorsRequest& a, SafetySensorsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SafetySensorsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SafetySensorsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SafetySensorsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SafetySensorsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SafetySensorsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SafetySensorsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SafetySensorsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc_sim_UI.SafetySensorsRequest";
  }
  protected:
  explicit SafetySensorsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSensor1FieldNumber = 1,
    kSensor2FieldNumber = 2,
    kSensor3FieldNumber = 3,
    kSensor4FieldNumber = 4,
  };
  // bool sensor_1 = 1;
  void clear_sensor_1();
  bool sensor_1() const;
  void set_sensor_1(bool value);
  private:
  bool _internal_sensor_1() const;
  void _internal_set_sensor_1(bool value);
  public:

  // bool sensor_2 = 2;
  void clear_sensor_2();
  bool sensor_2() const;
  void set_sensor_2(bool value);
  private:
  bool _internal_sensor_2() const;
  void _internal_set_sensor_2(bool value);
  public:

  // bool sensor_3 = 3;
  void clear_sensor_3();
  bool sensor_3() const;
  void set_sensor_3(bool value);
  private:
  bool _internal_sensor_3() const;
  void _internal_set_sensor_3(bool value);
  public:

  // bool sensor_4 = 4;
  void clear_sensor_4();
  bool sensor_4() const;
  void set_sensor_4(bool value);
  private:
  bool _internal_sensor_4() const;
  void _internal_set_sensor_4(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SafetySensorsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool sensor_1_;
  bool sensor_2_;
  bool sensor_3_;
  bool sensor_4_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetSpeedRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc_sim_UI.SetSpeedRequest) */ {
 public:
  inline SetSpeedRequest() : SetSpeedRequest(nullptr) {}
  ~SetSpeedRequest() override;
  explicit constexpr SetSpeedRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetSpeedRequest(const SetSpeedRequest& from);
  SetSpeedRequest(SetSpeedRequest&& from) noexcept
    : SetSpeedRequest() {
    *this = ::std::move(from);
  }

  inline SetSpeedRequest& operator=(const SetSpeedRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetSpeedRequest& operator=(SetSpeedRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetSpeedRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetSpeedRequest* internal_default_instance() {
    return reinterpret_cast<const SetSpeedRequest*>(
               &_SetSpeedRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SetSpeedRequest& a, SetSpeedRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetSpeedRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetSpeedRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetSpeedRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetSpeedRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetSpeedRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetSpeedRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetSpeedRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc_sim_UI.SetSpeedRequest";
  }
  protected:
  explicit SetSpeedRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSpeedMphFieldNumber = 1,
  };
  // float speed_mph = 1;
  void clear_speed_mph();
  float speed_mph() const;
  void set_speed_mph(float value);
  private:
  float _internal_speed_mph() const;
  void _internal_set_speed_mph(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetSpeedRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float speed_mph_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetGearRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc_sim_UI.SetGearRequest) */ {
 public:
  inline SetGearRequest() : SetGearRequest(nullptr) {}
  ~SetGearRequest() override;
  explicit constexpr SetGearRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetGearRequest(const SetGearRequest& from);
  SetGearRequest(SetGearRequest&& from) noexcept
    : SetGearRequest() {
    *this = ::std::move(from);
  }

  inline SetGearRequest& operator=(const SetGearRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetGearRequest& operator=(SetGearRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetGearRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetGearRequest* internal_default_instance() {
    return reinterpret_cast<const SetGearRequest*>(
               &_SetGearRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SetGearRequest& a, SetGearRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetGearRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetGearRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetGearRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetGearRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetGearRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetGearRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetGearRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc_sim_UI.SetGearRequest";
  }
  protected:
  explicit SetGearRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGearFieldNumber = 1,
  };
  // .carbon.rtc_sim_UI.Gear gear = 1;
  void clear_gear();
  ::carbon::rtc_sim_UI::Gear gear() const;
  void set_gear(::carbon::rtc_sim_UI::Gear value);
  private:
  ::carbon::rtc_sim_UI::Gear _internal_gear() const;
  void _internal_set_gear(::carbon::rtc_sim_UI::Gear value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetGearRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int gear_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetLightsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc_sim_UI.SetLightsRequest) */ {
 public:
  inline SetLightsRequest() : SetLightsRequest(nullptr) {}
  ~SetLightsRequest() override;
  explicit constexpr SetLightsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetLightsRequest(const SetLightsRequest& from);
  SetLightsRequest(SetLightsRequest&& from) noexcept
    : SetLightsRequest() {
    *this = ::std::move(from);
  }

  inline SetLightsRequest& operator=(const SetLightsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetLightsRequest& operator=(SetLightsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetLightsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetLightsRequest* internal_default_instance() {
    return reinterpret_cast<const SetLightsRequest*>(
               &_SetLightsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(SetLightsRequest& a, SetLightsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetLightsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetLightsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetLightsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetLightsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetLightsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetLightsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetLightsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc_sim_UI.SetLightsRequest";
  }
  protected:
  explicit SetLightsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLightsFieldNumber = 1,
  };
  // .carbon.rtc_sim_UI.Lights lights = 1;
  void clear_lights();
  ::carbon::rtc_sim_UI::Lights lights() const;
  void set_lights(::carbon::rtc_sim_UI::Lights value);
  private:
  ::carbon::rtc_sim_UI::Lights _internal_lights() const;
  void _internal_set_lights(::carbon::rtc_sim_UI::Lights value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetLightsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int lights_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetEngineRpmRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc_sim_UI.SetEngineRpmRequest) */ {
 public:
  inline SetEngineRpmRequest() : SetEngineRpmRequest(nullptr) {}
  ~SetEngineRpmRequest() override;
  explicit constexpr SetEngineRpmRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetEngineRpmRequest(const SetEngineRpmRequest& from);
  SetEngineRpmRequest(SetEngineRpmRequest&& from) noexcept
    : SetEngineRpmRequest() {
    *this = ::std::move(from);
  }

  inline SetEngineRpmRequest& operator=(const SetEngineRpmRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetEngineRpmRequest& operator=(SetEngineRpmRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetEngineRpmRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetEngineRpmRequest* internal_default_instance() {
    return reinterpret_cast<const SetEngineRpmRequest*>(
               &_SetEngineRpmRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(SetEngineRpmRequest& a, SetEngineRpmRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetEngineRpmRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetEngineRpmRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetEngineRpmRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetEngineRpmRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetEngineRpmRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetEngineRpmRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetEngineRpmRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc_sim_UI.SetEngineRpmRequest";
  }
  protected:
  explicit SetEngineRpmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRpmsFieldNumber = 1,
  };
  // int32 rpms = 1;
  void clear_rpms();
  int32_t rpms() const;
  void set_rpms(int32_t value);
  private:
  int32_t _internal_rpms() const;
  void _internal_set_rpms(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetEngineRpmRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t rpms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetErrorFlagRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc_sim_UI.SetErrorFlagRequest) */ {
 public:
  inline SetErrorFlagRequest() : SetErrorFlagRequest(nullptr) {}
  ~SetErrorFlagRequest() override;
  explicit constexpr SetErrorFlagRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetErrorFlagRequest(const SetErrorFlagRequest& from);
  SetErrorFlagRequest(SetErrorFlagRequest&& from) noexcept
    : SetErrorFlagRequest() {
    *this = ::std::move(from);
  }

  inline SetErrorFlagRequest& operator=(const SetErrorFlagRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetErrorFlagRequest& operator=(SetErrorFlagRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetErrorFlagRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetErrorFlagRequest* internal_default_instance() {
    return reinterpret_cast<const SetErrorFlagRequest*>(
               &_SetErrorFlagRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(SetErrorFlagRequest& a, SetErrorFlagRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetErrorFlagRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetErrorFlagRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetErrorFlagRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetErrorFlagRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetErrorFlagRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetErrorFlagRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetErrorFlagRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc_sim_UI.SetErrorFlagRequest";
  }
  protected:
  explicit SetErrorFlagRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorFlagFieldNumber = 1,
  };
  // int32 error_flag = 1;
  void clear_error_flag();
  int32_t error_flag() const;
  void set_error_flag(int32_t value);
  private:
  int32_t _internal_error_flag() const;
  void _internal_set_error_flag(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetErrorFlagRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t error_flag_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
};
// -------------------------------------------------------------------

class SetFuelLevelRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc_sim_UI.SetFuelLevelRequest) */ {
 public:
  inline SetFuelLevelRequest() : SetFuelLevelRequest(nullptr) {}
  ~SetFuelLevelRequest() override;
  explicit constexpr SetFuelLevelRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetFuelLevelRequest(const SetFuelLevelRequest& from);
  SetFuelLevelRequest(SetFuelLevelRequest&& from) noexcept
    : SetFuelLevelRequest() {
    *this = ::std::move(from);
  }

  inline SetFuelLevelRequest& operator=(const SetFuelLevelRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetFuelLevelRequest& operator=(SetFuelLevelRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetFuelLevelRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetFuelLevelRequest* internal_default_instance() {
    return reinterpret_cast<const SetFuelLevelRequest*>(
               &_SetFuelLevelRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(SetFuelLevelRequest& a, SetFuelLevelRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetFuelLevelRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetFuelLevelRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetFuelLevelRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetFuelLevelRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetFuelLevelRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetFuelLevelRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetFuelLevelRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc_sim_UI.SetFuelLevelRequest";
  }
  protected:
  explicit SetFuelLevelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFuelLevelFieldNumber = 1,
  };
  // float fuel_level = 1;
  void clear_fuel_level();
  float fuel_level() const;
  void set_fuel_level(float value);
  private:
  float _internal_fuel_level() const;
  void _internal_set_fuel_level(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetFuelLevelRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float fuel_level_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Empty

// -------------------------------------------------------------------

// EnableRequest

// bool enabled = 1;
inline void EnableRequest::clear_enabled() {
  enabled_ = false;
}
inline bool EnableRequest::_internal_enabled() const {
  return enabled_;
}
inline bool EnableRequest::enabled() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.EnableRequest.enabled)
  return _internal_enabled();
}
inline void EnableRequest::_internal_set_enabled(bool value) {
  
  enabled_ = value;
}
inline void EnableRequest::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.EnableRequest.enabled)
}

// -------------------------------------------------------------------

// SafetySensorsRequest

// bool sensor_1 = 1;
inline void SafetySensorsRequest::clear_sensor_1() {
  sensor_1_ = false;
}
inline bool SafetySensorsRequest::_internal_sensor_1() const {
  return sensor_1_;
}
inline bool SafetySensorsRequest::sensor_1() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SafetySensorsRequest.sensor_1)
  return _internal_sensor_1();
}
inline void SafetySensorsRequest::_internal_set_sensor_1(bool value) {
  
  sensor_1_ = value;
}
inline void SafetySensorsRequest::set_sensor_1(bool value) {
  _internal_set_sensor_1(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SafetySensorsRequest.sensor_1)
}

// bool sensor_2 = 2;
inline void SafetySensorsRequest::clear_sensor_2() {
  sensor_2_ = false;
}
inline bool SafetySensorsRequest::_internal_sensor_2() const {
  return sensor_2_;
}
inline bool SafetySensorsRequest::sensor_2() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SafetySensorsRequest.sensor_2)
  return _internal_sensor_2();
}
inline void SafetySensorsRequest::_internal_set_sensor_2(bool value) {
  
  sensor_2_ = value;
}
inline void SafetySensorsRequest::set_sensor_2(bool value) {
  _internal_set_sensor_2(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SafetySensorsRequest.sensor_2)
}

// bool sensor_3 = 3;
inline void SafetySensorsRequest::clear_sensor_3() {
  sensor_3_ = false;
}
inline bool SafetySensorsRequest::_internal_sensor_3() const {
  return sensor_3_;
}
inline bool SafetySensorsRequest::sensor_3() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SafetySensorsRequest.sensor_3)
  return _internal_sensor_3();
}
inline void SafetySensorsRequest::_internal_set_sensor_3(bool value) {
  
  sensor_3_ = value;
}
inline void SafetySensorsRequest::set_sensor_3(bool value) {
  _internal_set_sensor_3(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SafetySensorsRequest.sensor_3)
}

// bool sensor_4 = 4;
inline void SafetySensorsRequest::clear_sensor_4() {
  sensor_4_ = false;
}
inline bool SafetySensorsRequest::_internal_sensor_4() const {
  return sensor_4_;
}
inline bool SafetySensorsRequest::sensor_4() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SafetySensorsRequest.sensor_4)
  return _internal_sensor_4();
}
inline void SafetySensorsRequest::_internal_set_sensor_4(bool value) {
  
  sensor_4_ = value;
}
inline void SafetySensorsRequest::set_sensor_4(bool value) {
  _internal_set_sensor_4(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SafetySensorsRequest.sensor_4)
}

// -------------------------------------------------------------------

// SetSpeedRequest

// float speed_mph = 1;
inline void SetSpeedRequest::clear_speed_mph() {
  speed_mph_ = 0;
}
inline float SetSpeedRequest::_internal_speed_mph() const {
  return speed_mph_;
}
inline float SetSpeedRequest::speed_mph() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SetSpeedRequest.speed_mph)
  return _internal_speed_mph();
}
inline void SetSpeedRequest::_internal_set_speed_mph(float value) {
  
  speed_mph_ = value;
}
inline void SetSpeedRequest::set_speed_mph(float value) {
  _internal_set_speed_mph(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SetSpeedRequest.speed_mph)
}

// -------------------------------------------------------------------

// SetGearRequest

// .carbon.rtc_sim_UI.Gear gear = 1;
inline void SetGearRequest::clear_gear() {
  gear_ = 0;
}
inline ::carbon::rtc_sim_UI::Gear SetGearRequest::_internal_gear() const {
  return static_cast< ::carbon::rtc_sim_UI::Gear >(gear_);
}
inline ::carbon::rtc_sim_UI::Gear SetGearRequest::gear() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SetGearRequest.gear)
  return _internal_gear();
}
inline void SetGearRequest::_internal_set_gear(::carbon::rtc_sim_UI::Gear value) {
  
  gear_ = value;
}
inline void SetGearRequest::set_gear(::carbon::rtc_sim_UI::Gear value) {
  _internal_set_gear(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SetGearRequest.gear)
}

// -------------------------------------------------------------------

// SetLightsRequest

// .carbon.rtc_sim_UI.Lights lights = 1;
inline void SetLightsRequest::clear_lights() {
  lights_ = 0;
}
inline ::carbon::rtc_sim_UI::Lights SetLightsRequest::_internal_lights() const {
  return static_cast< ::carbon::rtc_sim_UI::Lights >(lights_);
}
inline ::carbon::rtc_sim_UI::Lights SetLightsRequest::lights() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SetLightsRequest.lights)
  return _internal_lights();
}
inline void SetLightsRequest::_internal_set_lights(::carbon::rtc_sim_UI::Lights value) {
  
  lights_ = value;
}
inline void SetLightsRequest::set_lights(::carbon::rtc_sim_UI::Lights value) {
  _internal_set_lights(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SetLightsRequest.lights)
}

// -------------------------------------------------------------------

// SetEngineRpmRequest

// int32 rpms = 1;
inline void SetEngineRpmRequest::clear_rpms() {
  rpms_ = 0;
}
inline int32_t SetEngineRpmRequest::_internal_rpms() const {
  return rpms_;
}
inline int32_t SetEngineRpmRequest::rpms() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SetEngineRpmRequest.rpms)
  return _internal_rpms();
}
inline void SetEngineRpmRequest::_internal_set_rpms(int32_t value) {
  
  rpms_ = value;
}
inline void SetEngineRpmRequest::set_rpms(int32_t value) {
  _internal_set_rpms(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SetEngineRpmRequest.rpms)
}

// -------------------------------------------------------------------

// SetErrorFlagRequest

// int32 error_flag = 1;
inline void SetErrorFlagRequest::clear_error_flag() {
  error_flag_ = 0;
}
inline int32_t SetErrorFlagRequest::_internal_error_flag() const {
  return error_flag_;
}
inline int32_t SetErrorFlagRequest::error_flag() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SetErrorFlagRequest.error_flag)
  return _internal_error_flag();
}
inline void SetErrorFlagRequest::_internal_set_error_flag(int32_t value) {
  
  error_flag_ = value;
}
inline void SetErrorFlagRequest::set_error_flag(int32_t value) {
  _internal_set_error_flag(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SetErrorFlagRequest.error_flag)
}

// -------------------------------------------------------------------

// SetFuelLevelRequest

// float fuel_level = 1;
inline void SetFuelLevelRequest::clear_fuel_level() {
  fuel_level_ = 0;
}
inline float SetFuelLevelRequest::_internal_fuel_level() const {
  return fuel_level_;
}
inline float SetFuelLevelRequest::fuel_level() const {
  // @@protoc_insertion_point(field_get:carbon.rtc_sim_UI.SetFuelLevelRequest.fuel_level)
  return _internal_fuel_level();
}
inline void SetFuelLevelRequest::_internal_set_fuel_level(float value) {
  
  fuel_level_ = value;
}
inline void SetFuelLevelRequest::set_fuel_level(float value) {
  _internal_set_fuel_level(value);
  // @@protoc_insertion_point(field_set:carbon.rtc_sim_UI.SetFuelLevelRequest.fuel_level)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace rtc_sim_UI
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::rtc_sim_UI::Gear> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::rtc_sim_UI::Gear>() {
  return ::carbon::rtc_sim_UI::Gear_descriptor();
}
template <> struct is_proto_enum< ::carbon::rtc_sim_UI::Lights> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::rtc_sim_UI::Lights>() {
  return ::carbon::rtc_sim_UI::Lights_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_golang_2fsimulator_2fhardware_5frtc_2fproto_2frtc_5fsim_5fUI_2frtc_5fsim_5fUI_2eproto

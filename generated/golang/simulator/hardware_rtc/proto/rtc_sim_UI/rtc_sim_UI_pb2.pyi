"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

GearValue = typing___NewType('GearValue', builtin___int)
type___GearValue = GearValue
Gear: _Gear
class _Gear(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[GearValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    GEAR_PARK = typing___cast(GearValue, 0)
    GEAR_REVERSE = typing___cast(GearValue, 1)
    GEAR_NEUTRAL = typing___cast(GearValue, 2)
    GEAR_FORWARD = typing___cast(GearValue, 3)
    GEAR_POWERZERO = typing___cast(GearValue, 4)
GEAR_PARK = typing___cast(GearValue, 0)
GEAR_REVERSE = typing___cast(GearValue, 1)
GEAR_NEUTRAL = typing___cast(GearValue, 2)
GEAR_FORWARD = typing___cast(GearValue, 3)
GEAR_POWERZERO = typing___cast(GearValue, 4)

LightsValue = typing___NewType('LightsValue', builtin___int)
type___LightsValue = LightsValue
Lights: _Lights
class _Lights(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[LightsValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    LIGHTS_OFF = typing___cast(LightsValue, 0)
    LIGHTS_LOW = typing___cast(LightsValue, 1)
    LIGHTS_HIGH = typing___cast(LightsValue, 2)
LIGHTS_OFF = typing___cast(LightsValue, 0)
LIGHTS_LOW = typing___cast(LightsValue, 1)
LIGHTS_HIGH = typing___cast(LightsValue, 2)

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

class EnableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___EnableRequest = EnableRequest

class SafetySensorsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    sensor_1: builtin___bool = ...
    sensor_2: builtin___bool = ...
    sensor_3: builtin___bool = ...
    sensor_4: builtin___bool = ...

    def __init__(self,
        *,
        sensor_1 : typing___Optional[builtin___bool] = None,
        sensor_2 : typing___Optional[builtin___bool] = None,
        sensor_3 : typing___Optional[builtin___bool] = None,
        sensor_4 : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"sensor_1",b"sensor_1",u"sensor_2",b"sensor_2",u"sensor_3",b"sensor_3",u"sensor_4",b"sensor_4"]) -> None: ...
type___SafetySensorsRequest = SafetySensorsRequest

class SetSpeedRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    speed_mph: builtin___float = ...

    def __init__(self,
        *,
        speed_mph : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"speed_mph",b"speed_mph"]) -> None: ...
type___SetSpeedRequest = SetSpeedRequest

class SetGearRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    gear: type___GearValue = ...

    def __init__(self,
        *,
        gear : typing___Optional[type___GearValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"gear",b"gear"]) -> None: ...
type___SetGearRequest = SetGearRequest

class SetLightsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lights: type___LightsValue = ...

    def __init__(self,
        *,
        lights : typing___Optional[type___LightsValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lights",b"lights"]) -> None: ...
type___SetLightsRequest = SetLightsRequest

class SetEngineRpmRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    rpms: builtin___int = ...

    def __init__(self,
        *,
        rpms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"rpms",b"rpms"]) -> None: ...
type___SetEngineRpmRequest = SetEngineRpmRequest

class SetErrorFlagRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    error_flag: builtin___int = ...

    def __init__(self,
        *,
        error_flag : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"error_flag",b"error_flag"]) -> None: ...
type___SetErrorFlagRequest = SetErrorFlagRequest

class SetFuelLevelRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    fuel_level: builtin___float = ...

    def __init__(self,
        *,
        fuel_level : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"fuel_level",b"fuel_level"]) -> None: ...
type___SetFuelLevelRequest = SetFuelLevelRequest

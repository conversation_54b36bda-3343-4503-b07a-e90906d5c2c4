# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto',
  package='carbon.rtc_sim_UI',
  syntax='proto3',
  serialized_options=b'Z\020proto/rtc_sim_UI',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n?golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto\x12\x11\x63\x61rbon.rtc_sim_UI\"\x07\n\x05\x45mpty\" \n\rEnableRequest\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"^\n\x14SafetySensorsRequest\x12\x10\n\x08sensor_1\x18\x01 \x01(\x08\x12\x10\n\x08sensor_2\x18\x02 \x01(\x08\x12\x10\n\x08sensor_3\x18\x03 \x01(\x08\x12\x10\n\x08sensor_4\x18\x04 \x01(\x08\"$\n\x0fSetSpeedRequest\x12\x11\n\tspeed_mph\x18\x01 \x01(\x02\"7\n\x0eSetGearRequest\x12%\n\x04gear\x18\x01 \x01(\x0e\x32\x17.carbon.rtc_sim_UI.Gear\"=\n\x10SetLightsRequest\x12)\n\x06lights\x18\x01 \x01(\x0e\x32\x19.carbon.rtc_sim_UI.Lights\"#\n\x13SetEngineRpmRequest\x12\x0c\n\x04rpms\x18\x01 \x01(\x05\")\n\x13SetErrorFlagRequest\x12\x12\n\nerror_flag\x18\x01 \x01(\x05\")\n\x13SetFuelLevelRequest\x12\x12\n\nfuel_level\x18\x01 \x01(\x02*_\n\x04Gear\x12\r\n\tGEAR_PARK\x10\x00\x12\x10\n\x0cGEAR_REVERSE\x10\x01\x12\x10\n\x0cGEAR_NEUTRAL\x10\x02\x12\x10\n\x0cGEAR_FORWARD\x10\x03\x12\x12\n\x0eGEAR_POWERZERO\x10\x04*9\n\x06Lights\x12\x0e\n\nLIGHTS_OFF\x10\x00\x12\x0e\n\nLIGHTS_LOW\x10\x01\x12\x0f\n\x0bLIGHTS_HIGH\x10\x02\x32\x83\x07\n\x15RTCSimulatorUIService\x12N\n\x0eSetInCabSwitch\x12 .carbon.rtc_sim_UI.EnableRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12W\n\x10SetSafetySensors\x12\'.carbon.rtc_sim_UI.SafetySensorsRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12J\n\x08SetSpeed\x12\".carbon.rtc_sim_UI.SetSpeedRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12H\n\x07SetGear\x12!.carbon.rtc_sim_UI.SetGearRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12L\n\tSetLights\x12#.carbon.rtc_sim_UI.SetLightsRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12H\n\x08SetEstop\x12 .carbon.rtc_sim_UI.EnableRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12R\n\x0cSetEngineRpm\x12&.carbon.rtc_sim_UI.SetEngineRpmRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12K\n\x0bSetFrontPto\x12 .carbon.rtc_sim_UI.EnableRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12J\n\nSetRearPto\x12 .carbon.rtc_sim_UI.EnableRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12R\n\x0cSetErrorFlag\x12&.carbon.rtc_sim_UI.SetErrorFlagRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x12R\n\x0cSetFuelLevel\x12&.carbon.rtc_sim_UI.SetFuelLevelRequest\x1a\x18.carbon.rtc_sim_UI.Empty\"\x00\x42\x12Z\x10proto/rtc_sim_UIb\x06proto3'
)

_GEAR = _descriptor.EnumDescriptor(
  name='Gear',
  full_name='carbon.rtc_sim_UI.Gear',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='GEAR_PARK', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GEAR_REVERSE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GEAR_NEUTRAL', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GEAR_FORWARD', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GEAR_POWERZERO', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=506,
  serialized_end=601,
)
_sym_db.RegisterEnumDescriptor(_GEAR)

Gear = enum_type_wrapper.EnumTypeWrapper(_GEAR)
_LIGHTS = _descriptor.EnumDescriptor(
  name='Lights',
  full_name='carbon.rtc_sim_UI.Lights',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LIGHTS_OFF', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LIGHTS_LOW', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LIGHTS_HIGH', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=603,
  serialized_end=660,
)
_sym_db.RegisterEnumDescriptor(_LIGHTS)

Lights = enum_type_wrapper.EnumTypeWrapper(_LIGHTS)
GEAR_PARK = 0
GEAR_REVERSE = 1
GEAR_NEUTRAL = 2
GEAR_FORWARD = 3
GEAR_POWERZERO = 4
LIGHTS_OFF = 0
LIGHTS_LOW = 1
LIGHTS_HIGH = 2



_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='carbon.rtc_sim_UI.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=86,
  serialized_end=93,
)


_ENABLEREQUEST = _descriptor.Descriptor(
  name='EnableRequest',
  full_name='carbon.rtc_sim_UI.EnableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.rtc_sim_UI.EnableRequest.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=95,
  serialized_end=127,
)


_SAFETYSENSORSREQUEST = _descriptor.Descriptor(
  name='SafetySensorsRequest',
  full_name='carbon.rtc_sim_UI.SafetySensorsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor_1', full_name='carbon.rtc_sim_UI.SafetySensorsRequest.sensor_1', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor_2', full_name='carbon.rtc_sim_UI.SafetySensorsRequest.sensor_2', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor_3', full_name='carbon.rtc_sim_UI.SafetySensorsRequest.sensor_3', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor_4', full_name='carbon.rtc_sim_UI.SafetySensorsRequest.sensor_4', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=129,
  serialized_end=223,
)


_SETSPEEDREQUEST = _descriptor.Descriptor(
  name='SetSpeedRequest',
  full_name='carbon.rtc_sim_UI.SetSpeedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='speed_mph', full_name='carbon.rtc_sim_UI.SetSpeedRequest.speed_mph', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=225,
  serialized_end=261,
)


_SETGEARREQUEST = _descriptor.Descriptor(
  name='SetGearRequest',
  full_name='carbon.rtc_sim_UI.SetGearRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gear', full_name='carbon.rtc_sim_UI.SetGearRequest.gear', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=263,
  serialized_end=318,
)


_SETLIGHTSREQUEST = _descriptor.Descriptor(
  name='SetLightsRequest',
  full_name='carbon.rtc_sim_UI.SetLightsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lights', full_name='carbon.rtc_sim_UI.SetLightsRequest.lights', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=320,
  serialized_end=381,
)


_SETENGINERPMREQUEST = _descriptor.Descriptor(
  name='SetEngineRpmRequest',
  full_name='carbon.rtc_sim_UI.SetEngineRpmRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='rpms', full_name='carbon.rtc_sim_UI.SetEngineRpmRequest.rpms', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=383,
  serialized_end=418,
)


_SETERRORFLAGREQUEST = _descriptor.Descriptor(
  name='SetErrorFlagRequest',
  full_name='carbon.rtc_sim_UI.SetErrorFlagRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error_flag', full_name='carbon.rtc_sim_UI.SetErrorFlagRequest.error_flag', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=420,
  serialized_end=461,
)


_SETFUELLEVELREQUEST = _descriptor.Descriptor(
  name='SetFuelLevelRequest',
  full_name='carbon.rtc_sim_UI.SetFuelLevelRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fuel_level', full_name='carbon.rtc_sim_UI.SetFuelLevelRequest.fuel_level', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=463,
  serialized_end=504,
)

_SETGEARREQUEST.fields_by_name['gear'].enum_type = _GEAR
_SETLIGHTSREQUEST.fields_by_name['lights'].enum_type = _LIGHTS
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['EnableRequest'] = _ENABLEREQUEST
DESCRIPTOR.message_types_by_name['SafetySensorsRequest'] = _SAFETYSENSORSREQUEST
DESCRIPTOR.message_types_by_name['SetSpeedRequest'] = _SETSPEEDREQUEST
DESCRIPTOR.message_types_by_name['SetGearRequest'] = _SETGEARREQUEST
DESCRIPTOR.message_types_by_name['SetLightsRequest'] = _SETLIGHTSREQUEST
DESCRIPTOR.message_types_by_name['SetEngineRpmRequest'] = _SETENGINERPMREQUEST
DESCRIPTOR.message_types_by_name['SetErrorFlagRequest'] = _SETERRORFLAGREQUEST
DESCRIPTOR.message_types_by_name['SetFuelLevelRequest'] = _SETFUELLEVELREQUEST
DESCRIPTOR.enum_types_by_name['Gear'] = _GEAR
DESCRIPTOR.enum_types_by_name['Lights'] = _LIGHTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'golang.simulator.hardware_rtc.proto.rtc_sim_UI.rtc_sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.Empty)
  })
_sym_db.RegisterMessage(Empty)

EnableRequest = _reflection.GeneratedProtocolMessageType('EnableRequest', (_message.Message,), {
  'DESCRIPTOR' : _ENABLEREQUEST,
  '__module__' : 'golang.simulator.hardware_rtc.proto.rtc_sim_UI.rtc_sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.EnableRequest)
  })
_sym_db.RegisterMessage(EnableRequest)

SafetySensorsRequest = _reflection.GeneratedProtocolMessageType('SafetySensorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SAFETYSENSORSREQUEST,
  '__module__' : 'golang.simulator.hardware_rtc.proto.rtc_sim_UI.rtc_sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SafetySensorsRequest)
  })
_sym_db.RegisterMessage(SafetySensorsRequest)

SetSpeedRequest = _reflection.GeneratedProtocolMessageType('SetSpeedRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETSPEEDREQUEST,
  '__module__' : 'golang.simulator.hardware_rtc.proto.rtc_sim_UI.rtc_sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetSpeedRequest)
  })
_sym_db.RegisterMessage(SetSpeedRequest)

SetGearRequest = _reflection.GeneratedProtocolMessageType('SetGearRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETGEARREQUEST,
  '__module__' : 'golang.simulator.hardware_rtc.proto.rtc_sim_UI.rtc_sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetGearRequest)
  })
_sym_db.RegisterMessage(SetGearRequest)

SetLightsRequest = _reflection.GeneratedProtocolMessageType('SetLightsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETLIGHTSREQUEST,
  '__module__' : 'golang.simulator.hardware_rtc.proto.rtc_sim_UI.rtc_sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetLightsRequest)
  })
_sym_db.RegisterMessage(SetLightsRequest)

SetEngineRpmRequest = _reflection.GeneratedProtocolMessageType('SetEngineRpmRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETENGINERPMREQUEST,
  '__module__' : 'golang.simulator.hardware_rtc.proto.rtc_sim_UI.rtc_sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetEngineRpmRequest)
  })
_sym_db.RegisterMessage(SetEngineRpmRequest)

SetErrorFlagRequest = _reflection.GeneratedProtocolMessageType('SetErrorFlagRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETERRORFLAGREQUEST,
  '__module__' : 'golang.simulator.hardware_rtc.proto.rtc_sim_UI.rtc_sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetErrorFlagRequest)
  })
_sym_db.RegisterMessage(SetErrorFlagRequest)

SetFuelLevelRequest = _reflection.GeneratedProtocolMessageType('SetFuelLevelRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETFUELLEVELREQUEST,
  '__module__' : 'golang.simulator.hardware_rtc.proto.rtc_sim_UI.rtc_sim_UI_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc_sim_UI.SetFuelLevelRequest)
  })
_sym_db.RegisterMessage(SetFuelLevelRequest)


DESCRIPTOR._options = None

_RTCSIMULATORUISERVICE = _descriptor.ServiceDescriptor(
  name='RTCSimulatorUIService',
  full_name='carbon.rtc_sim_UI.RTCSimulatorUIService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=663,
  serialized_end=1562,
  methods=[
  _descriptor.MethodDescriptor(
    name='SetInCabSwitch',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetInCabSwitch',
    index=0,
    containing_service=None,
    input_type=_ENABLEREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetSafetySensors',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetSafetySensors',
    index=1,
    containing_service=None,
    input_type=_SAFETYSENSORSREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetSpeed',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetSpeed',
    index=2,
    containing_service=None,
    input_type=_SETSPEEDREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetGear',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetGear',
    index=3,
    containing_service=None,
    input_type=_SETGEARREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetLights',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetLights',
    index=4,
    containing_service=None,
    input_type=_SETLIGHTSREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetEstop',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetEstop',
    index=5,
    containing_service=None,
    input_type=_ENABLEREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetEngineRpm',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetEngineRpm',
    index=6,
    containing_service=None,
    input_type=_SETENGINERPMREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetFrontPto',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetFrontPto',
    index=7,
    containing_service=None,
    input_type=_ENABLEREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetRearPto',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetRearPto',
    index=8,
    containing_service=None,
    input_type=_ENABLEREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetErrorFlag',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetErrorFlag',
    index=9,
    containing_service=None,
    input_type=_SETERRORFLAGREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetFuelLevel',
    full_name='carbon.rtc_sim_UI.RTCSimulatorUIService.SetFuelLevel',
    index=10,
    containing_service=None,
    input_type=_SETFUELLEVELREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_RTCSIMULATORUISERVICE)

DESCRIPTOR.services_by_name['RTCSimulatorUIService'] = _RTCSIMULATORUISERVICE

# @@protoc_insertion_point(module_scope)

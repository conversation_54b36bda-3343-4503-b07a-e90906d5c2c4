// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.proto

#include "golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.pb.h"
#include "golang/simulator/hardware_rtc/proto/rtc_sim_UI/rtc_sim_UI.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace rtc_sim_UI {

static const char* RTCSimulatorUIService_method_names[] = {
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetInCabSwitch",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetSafetySensors",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetSpeed",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetGear",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetLights",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetEstop",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetEngineRpm",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetFrontPto",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetRearPto",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetErrorFlag",
  "/carbon.rtc_sim_UI.RTCSimulatorUIService/SetFuelLevel",
};

std::unique_ptr< RTCSimulatorUIService::Stub> RTCSimulatorUIService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< RTCSimulatorUIService::Stub> stub(new RTCSimulatorUIService::Stub(channel, options));
  return stub;
}

RTCSimulatorUIService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_SetInCabSwitch_(RTCSimulatorUIService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetSafetySensors_(RTCSimulatorUIService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetSpeed_(RTCSimulatorUIService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetGear_(RTCSimulatorUIService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetLights_(RTCSimulatorUIService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetEstop_(RTCSimulatorUIService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetEngineRpm_(RTCSimulatorUIService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetFrontPto_(RTCSimulatorUIService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetRearPto_(RTCSimulatorUIService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetErrorFlag_(RTCSimulatorUIService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetFuelLevel_(RTCSimulatorUIService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status RTCSimulatorUIService::Stub::SetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetInCabSwitch_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetInCabSwitch_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetInCabSwitch(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetInCabSwitch_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetInCabSwitchRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::EnableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetInCabSwitch_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetInCabSwitchRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetInCabSwitchRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::SafetySensorsRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetSafetySensors_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::SafetySensorsRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetSafetySensors_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetSafetySensors(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetSafetySensors_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetSafetySensorsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::SafetySensorsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetSafetySensors_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetSafetySensorsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetSafetySensorsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::SetSpeedRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetSpeed_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::SetSpeedRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetSpeed_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetSpeed(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetSpeed_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetSpeedRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::SetSpeedRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetSpeed_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetSpeedRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetSpeedRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::SetGearRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetGear_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::SetGearRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetGear_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetGear(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetGear_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetGearRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::SetGearRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetGear_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetGearRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetGearRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetGearRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::SetLightsRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetLights_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::SetLightsRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLights_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetLights(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLights_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetLightsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::SetLightsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetLights_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetLightsRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetLightsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetEstop_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetEstop_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetEstop(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetEstop_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetEstopRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::EnableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetEstop_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetEstopRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetEstopRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::SetEngineRpmRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetEngineRpm_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::SetEngineRpmRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetEngineRpm_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetEngineRpm(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetEngineRpm_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetEngineRpmRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::SetEngineRpmRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetEngineRpm_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetEngineRpmRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetEngineRpmRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetFrontPto_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetFrontPto_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetFrontPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetFrontPto_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetFrontPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::EnableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetFrontPto_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetFrontPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetFrontPtoRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetRearPto_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetRearPto_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetRearPto(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetRearPto_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetRearPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::EnableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetRearPto_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetRearPtoRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::EnableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetRearPtoRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::SetErrorFlagRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetErrorFlag_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::SetErrorFlagRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetErrorFlag_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetErrorFlag(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetErrorFlag_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetErrorFlagRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::SetErrorFlagRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetErrorFlag_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetErrorFlagRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetErrorFlagRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RTCSimulatorUIService::Stub::SetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::carbon::rtc_sim_UI::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc_sim_UI::SetFuelLevelRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetFuelLevel_, context, request, response);
}

void RTCSimulatorUIService::Stub::async::SetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::carbon::rtc_sim_UI::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc_sim_UI::SetFuelLevelRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetFuelLevel_, context, request, response, std::move(f));
}

void RTCSimulatorUIService::Stub::async::SetFuelLevel(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::carbon::rtc_sim_UI::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetFuelLevel_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::PrepareAsyncSetFuelLevelRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc_sim_UI::Empty, ::carbon::rtc_sim_UI::SetFuelLevelRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetFuelLevel_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc_sim_UI::Empty>* RTCSimulatorUIService::Stub::AsyncSetFuelLevelRaw(::grpc::ClientContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetFuelLevelRaw(context, request, cq);
  result->StartCall();
  return result;
}

RTCSimulatorUIService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::EnableRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetInCabSwitch(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::SafetySensorsRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::SafetySensorsRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetSafetySensors(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::SetSpeedRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::SetSpeedRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetSpeed(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::SetGearRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::SetGearRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetGear(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::SetLightsRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::SetLightsRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetLights(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::EnableRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetEstop(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::SetEngineRpmRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::SetEngineRpmRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetEngineRpm(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::EnableRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetFrontPto(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::EnableRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::EnableRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetRearPto(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::SetErrorFlagRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::SetErrorFlagRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetErrorFlag(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RTCSimulatorUIService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RTCSimulatorUIService::Service, ::carbon::rtc_sim_UI::SetFuelLevelRequest, ::carbon::rtc_sim_UI::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RTCSimulatorUIService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc_sim_UI::SetFuelLevelRequest* req,
             ::carbon::rtc_sim_UI::Empty* resp) {
               return service->SetFuelLevel(ctx, req, resp);
             }, this)));
}

RTCSimulatorUIService::Service::~Service() {
}

::grpc::Status RTCSimulatorUIService::Service::SetInCabSwitch(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetSafetySensors(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SafetySensorsRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetSpeed(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetSpeedRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetGear(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetGearRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetLights(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetLightsRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetEstop(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetEngineRpm(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetEngineRpmRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetFrontPto(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetRearPto(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::EnableRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetErrorFlag(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetErrorFlagRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RTCSimulatorUIService::Service::SetFuelLevel(::grpc::ServerContext* context, const ::carbon::rtc_sim_UI::SetFuelLevelRequest* request, ::carbon::rtc_sim_UI::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace rtc_sim_UI


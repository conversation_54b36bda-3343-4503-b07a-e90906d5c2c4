# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.golang.simulator.hardware_rtc.proto.rtc_sim_UI import rtc_sim_UI_pb2 as golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2


class RTCSimulatorUIServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetInCabSwitch = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetInCabSwitch',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetSafetySensors = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetSafetySensors',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SafetySensorsRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetSpeed = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetSpeed',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetSpeedRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetGear = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetGear',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetGearRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetLights = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetLights',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetLightsRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetEstop = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetEstop',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetEngineRpm = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetEngineRpm',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetEngineRpmRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetFrontPto = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetFrontPto',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetRearPto = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetRearPto',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetErrorFlag = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetErrorFlag',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetErrorFlagRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )
        self.SetFuelLevel = channel.unary_unary(
                '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetFuelLevel',
                request_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetFuelLevelRequest.SerializeToString,
                response_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
                )


class RTCSimulatorUIServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SetInCabSwitch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetSafetySensors(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetSpeed(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetGear(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetLights(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetEstop(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetEngineRpm(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetFrontPto(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetRearPto(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetErrorFlag(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetFuelLevel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RTCSimulatorUIServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetInCabSwitch': grpc.unary_unary_rpc_method_handler(
                    servicer.SetInCabSwitch,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetSafetySensors': grpc.unary_unary_rpc_method_handler(
                    servicer.SetSafetySensors,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SafetySensorsRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetSpeed': grpc.unary_unary_rpc_method_handler(
                    servicer.SetSpeed,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetSpeedRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetGear': grpc.unary_unary_rpc_method_handler(
                    servicer.SetGear,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetGearRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetLights': grpc.unary_unary_rpc_method_handler(
                    servicer.SetLights,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetLightsRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetEstop': grpc.unary_unary_rpc_method_handler(
                    servicer.SetEstop,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetEngineRpm': grpc.unary_unary_rpc_method_handler(
                    servicer.SetEngineRpm,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetEngineRpmRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetFrontPto': grpc.unary_unary_rpc_method_handler(
                    servicer.SetFrontPto,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetRearPto': grpc.unary_unary_rpc_method_handler(
                    servicer.SetRearPto,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetErrorFlag': grpc.unary_unary_rpc_method_handler(
                    servicer.SetErrorFlag,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetErrorFlagRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
            'SetFuelLevel': grpc.unary_unary_rpc_method_handler(
                    servicer.SetFuelLevel,
                    request_deserializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetFuelLevelRequest.FromString,
                    response_serializer=golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.rtc_sim_UI.RTCSimulatorUIService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class RTCSimulatorUIService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SetInCabSwitch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetInCabSwitch',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetSafetySensors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetSafetySensors',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SafetySensorsRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetSpeed(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetSpeed',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetSpeedRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetGear(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetGear',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetGearRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetLights(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetLights',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetLightsRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetEstop(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetEstop',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetEngineRpm(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetEngineRpm',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetEngineRpmRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetFrontPto(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetFrontPto',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetRearPto(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetRearPto',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.EnableRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetErrorFlag(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetErrorFlag',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetErrorFlagRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetFuelLevel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc_sim_UI.RTCSimulatorUIService/SetFuelLevel',
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.SetFuelLevelRequest.SerializeToString,
            golang_dot_simulator_dot_hardware__rtc_dot_proto_dot_rtc__sim__UI_dot_rtc__sim__UI__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

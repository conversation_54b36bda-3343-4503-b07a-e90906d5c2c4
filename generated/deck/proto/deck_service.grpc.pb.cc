// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: deck/proto/deck_service.proto

#include "deck/proto/deck_service.pb.h"
#include "deck/proto/deck_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace deck {

static const char* DeckService_method_names[] = {
  "/deck.DeckService/Ping",
  "/deck.DeckService/SetConfigs",
  "/deck.DeckService/GetConfig",
};

std::unique_ptr< DeckService::Stub> DeckService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< DeckService::Stub> stub(new DeckService::Stub(channel, options));
  return stub;
}

DeckService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Ping_(DeckService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetConfigs_(DeckService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetConfig_(DeckService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status DeckService::Stub::Ping(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::deck::PongMsg* response) {
  return ::grpc::internal::BlockingUnaryCall< ::deck::PingMsg, ::deck::PongMsg, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Ping_, context, request, response);
}

void DeckService::Stub::async::Ping(::grpc::ClientContext* context, const ::deck::PingMsg* request, ::deck::PongMsg* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::deck::PingMsg, ::deck::PongMsg, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, std::move(f));
}

void DeckService::Stub::async::Ping(::grpc::ClientContext* context, const ::deck::PingMsg* request, ::deck::PongMsg* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::deck::PongMsg>* DeckService::Stub::PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::deck::PongMsg, ::deck::PingMsg, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Ping_, context, request);
}

::grpc::ClientAsyncResponseReader< ::deck::PongMsg>* DeckService::Stub::AsyncPingRaw(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DeckService::Stub::SetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::deck::SetConfigsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::deck::SetConfigsRequest, ::deck::SetConfigsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetConfigs_, context, request, response);
}

void DeckService::Stub::async::SetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest* request, ::deck::SetConfigsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::deck::SetConfigsRequest, ::deck::SetConfigsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetConfigs_, context, request, response, std::move(f));
}

void DeckService::Stub::async::SetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest* request, ::deck::SetConfigsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetConfigs_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::deck::SetConfigsResponse>* DeckService::Stub::PrepareAsyncSetConfigsRaw(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::deck::SetConfigsResponse, ::deck::SetConfigsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetConfigs_, context, request);
}

::grpc::ClientAsyncResponseReader< ::deck::SetConfigsResponse>* DeckService::Stub::AsyncSetConfigsRaw(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetConfigsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DeckService::Stub::GetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::deck::GetConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::deck::GetConfigRequest, ::deck::GetConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetConfig_, context, request, response);
}

void DeckService::Stub::async::GetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest* request, ::deck::GetConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::deck::GetConfigRequest, ::deck::GetConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConfig_, context, request, response, std::move(f));
}

void DeckService::Stub::async::GetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest* request, ::deck::GetConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::deck::GetConfigResponse>* DeckService::Stub::PrepareAsyncGetConfigRaw(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::deck::GetConfigResponse, ::deck::GetConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::deck::GetConfigResponse>* DeckService::Stub::AsyncGetConfigRaw(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

DeckService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DeckService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DeckService::Service, ::deck::PingMsg, ::deck::PongMsg, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DeckService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::deck::PingMsg* req,
             ::deck::PongMsg* resp) {
               return service->Ping(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DeckService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DeckService::Service, ::deck::SetConfigsRequest, ::deck::SetConfigsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DeckService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::deck::SetConfigsRequest* req,
             ::deck::SetConfigsResponse* resp) {
               return service->SetConfigs(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DeckService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DeckService::Service, ::deck::GetConfigRequest, ::deck::GetConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DeckService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::deck::GetConfigRequest* req,
             ::deck::GetConfigResponse* resp) {
               return service->GetConfig(ctx, req, resp);
             }, this)));
}

DeckService::Service::~Service() {
}

::grpc::Status DeckService::Service::Ping(::grpc::ServerContext* context, const ::deck::PingMsg* request, ::deck::PongMsg* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DeckService::Service::SetConfigs(::grpc::ServerContext* context, const ::deck::SetConfigsRequest* request, ::deck::SetConfigsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DeckService::Service::GetConfig(::grpc::ServerContext* context, const ::deck::GetConfigRequest* request, ::deck::GetConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace deck


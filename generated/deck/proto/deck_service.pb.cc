// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deck/proto/deck_service.proto

#include "deck/proto/deck_service.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace deck {
constexpr Config::Config(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , _oneof_case_{}{}
struct ConfigDefaultTypeInternal {
  constexpr ConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigDefaultTypeInternal() {}
  union {
    Config _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigDefaultTypeInternal _Config_default_instance_;
constexpr SetConfigsRequest::SetConfigsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : configs_()
  , camera_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SetConfigsRequestDefaultTypeInternal {
  constexpr SetConfigsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetConfigsRequestDefaultTypeInternal() {}
  union {
    SetConfigsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetConfigsRequestDefaultTypeInternal _SetConfigsRequest_default_instance_;
constexpr SetConfigsResponse::SetConfigsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetConfigsResponseDefaultTypeInternal {
  constexpr SetConfigsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetConfigsResponseDefaultTypeInternal() {}
  union {
    SetConfigsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetConfigsResponseDefaultTypeInternal _SetConfigsResponse_default_instance_;
constexpr GetConfigRequest::GetConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : camera_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , type_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetConfigRequestDefaultTypeInternal {
  constexpr GetConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetConfigRequestDefaultTypeInternal() {}
  union {
    GetConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetConfigRequestDefaultTypeInternal _GetConfigRequest_default_instance_;
constexpr GetConfigResponse::GetConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : config_(nullptr){}
struct GetConfigResponseDefaultTypeInternal {
  constexpr GetConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetConfigResponseDefaultTypeInternal() {}
  union {
    GetConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetConfigResponseDefaultTypeInternal _GetConfigResponse_default_instance_;
constexpr PingMsg::PingMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0u){}
struct PingMsgDefaultTypeInternal {
  constexpr PingMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PingMsgDefaultTypeInternal() {}
  union {
    PingMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PingMsgDefaultTypeInternal _PingMsg_default_instance_;
constexpr PongMsg::PongMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0u){}
struct PongMsgDefaultTypeInternal {
  constexpr PongMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PongMsgDefaultTypeInternal() {}
  union {
    PongMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PongMsgDefaultTypeInternal _PongMsg_default_instance_;
}  // namespace deck
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_deck_2fproto_2fdeck_5fservice_2eproto[7];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_deck_2fproto_2fdeck_5fservice_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_deck_2fproto_2fdeck_5fservice_2eproto = nullptr;

const uint32_t TableStruct_deck_2fproto_2fdeck_5fservice_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::deck::Config, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::deck::Config, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::deck::Config, key_),
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::deck::Config, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::deck::SetConfigsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::deck::SetConfigsRequest, camera_),
  PROTOBUF_FIELD_OFFSET(::deck::SetConfigsRequest, configs_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::deck::SetConfigsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::deck::GetConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::deck::GetConfigRequest, camera_),
  PROTOBUF_FIELD_OFFSET(::deck::GetConfigRequest, key_),
  PROTOBUF_FIELD_OFFSET(::deck::GetConfigRequest, type_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::deck::GetConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::deck::GetConfigResponse, config_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::deck::PingMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::deck::PingMsg, x_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::deck::PongMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::deck::PongMsg, x_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::deck::Config)},
  { 12, -1, -1, sizeof(::deck::SetConfigsRequest)},
  { 20, -1, -1, sizeof(::deck::SetConfigsResponse)},
  { 26, -1, -1, sizeof(::deck::GetConfigRequest)},
  { 35, -1, -1, sizeof(::deck::GetConfigResponse)},
  { 42, -1, -1, sizeof(::deck::PingMsg)},
  { 49, -1, -1, sizeof(::deck::PongMsg)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::deck::_Config_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::deck::_SetConfigsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::deck::_SetConfigsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::deck::_GetConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::deck::_GetConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::deck::_PingMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::deck::_PongMsg_default_instance_),
};

const char descriptor_table_protodef_deck_2fproto_2fdeck_5fservice_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035deck/proto/deck_service.proto\022\004deck\"m\n"
  "\006Config\022\013\n\003key\030\001 \001(\t\022\022\n\010bool_val\030\002 \001(\010H\000"
  "\022\021\n\007int_val\030\003 \001(\003H\000\022\023\n\tfloat_val\030\004 \001(\001H\000"
  "\022\021\n\007str_val\030\005 \001(\tH\000B\007\n\005value\"B\n\021SetConfi"
  "gsRequest\022\016\n\006camera\030\001 \001(\t\022\035\n\007configs\030\002 \003"
  "(\0132\014.deck.Config\"\024\n\022SetConfigsResponse\"="
  "\n\020GetConfigRequest\022\016\n\006camera\030\001 \001(\t\022\013\n\003ke"
  "y\030\002 \001(\t\022\014\n\004type\030\003 \001(\t\"1\n\021GetConfigRespon"
  "se\022\034\n\006config\030\001 \001(\0132\014.deck.Config\"\024\n\007Ping"
  "Msg\022\t\n\001x\030\001 \001(\r\"\024\n\007PongMsg\022\t\n\001x\030\001 \001(\r2\270\001\n"
  "\013DeckService\022&\n\004Ping\022\r.deck.PingMsg\032\r.de"
  "ck.PongMsg\"\000\022A\n\nSetConfigs\022\027.deck.SetCon"
  "figsRequest\032\030.deck.SetConfigsResponse\"\000\022"
  ">\n\tGetConfig\022\026.deck.GetConfigRequest\032\027.d"
  "eck.GetConfigResponse\"\000B\014Z\nproto/deckb\006p"
  "roto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto = {
  false, false, 605, descriptor_table_protodef_deck_2fproto_2fdeck_5fservice_2eproto, "deck/proto/deck_service.proto", 
  &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_once, nullptr, 0, 7,
  schemas, file_default_instances, TableStruct_deck_2fproto_2fdeck_5fservice_2eproto::offsets,
  file_level_metadata_deck_2fproto_2fdeck_5fservice_2eproto, file_level_enum_descriptors_deck_2fproto_2fdeck_5fservice_2eproto, file_level_service_descriptors_deck_2fproto_2fdeck_5fservice_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_getter() {
  return &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_deck_2fproto_2fdeck_5fservice_2eproto(&descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto);
namespace deck {

// ===================================================================

class Config::_Internal {
 public:
};

Config::Config(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:deck.Config)
}
Config::Config(const Config& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  clear_has_value();
  switch (from.value_case()) {
    case kBoolVal: {
      _internal_set_bool_val(from._internal_bool_val());
      break;
    }
    case kIntVal: {
      _internal_set_int_val(from._internal_int_val());
      break;
    }
    case kFloatVal: {
      _internal_set_float_val(from._internal_float_val());
      break;
    }
    case kStrVal: {
      _internal_set_str_val(from._internal_str_val());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:deck.Config)
}

inline void Config::SharedCtor() {
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
clear_has_value();
}

Config::~Config() {
  // @@protoc_insertion_point(destructor:deck.Config)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Config::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (has_value()) {
    clear_value();
  }
}

void Config::ArenaDtor(void* object) {
  Config* _this = reinterpret_cast< Config* >(object);
  (void)_this;
}
void Config::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Config::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Config::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:deck.Config)
  switch (value_case()) {
    case kBoolVal: {
      // No need to clear
      break;
    }
    case kIntVal: {
      // No need to clear
      break;
    }
    case kFloatVal: {
      // No need to clear
      break;
    }
    case kStrVal: {
      value_.str_val_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void Config::Clear() {
// @@protoc_insertion_point(message_clear_start:deck.Config)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty();
  clear_value();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Config::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "deck.Config.key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool bool_val = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _internal_set_bool_val(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 int_val = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _internal_set_int_val(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double float_val = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _internal_set_float_val(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // string str_val = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_str_val();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "deck.Config.str_val"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Config::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:deck.Config)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "deck.Config.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  // bool bool_val = 2;
  if (_internal_has_bool_val()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_bool_val(), target);
  }

  // int64 int_val = 3;
  if (_internal_has_int_val()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_int_val(), target);
  }

  // double float_val = 4;
  if (_internal_has_float_val()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_float_val(), target);
  }

  // string str_val = 5;
  if (_internal_has_str_val()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_str_val().data(), static_cast<int>(this->_internal_str_val().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "deck.Config.str_val");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_str_val(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:deck.Config)
  return target;
}

size_t Config::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:deck.Config)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  switch (value_case()) {
    // bool bool_val = 2;
    case kBoolVal: {
      total_size += 1 + 1;
      break;
    }
    // int64 int_val = 3;
    case kIntVal: {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_int_val());
      break;
    }
    // double float_val = 4;
    case kFloatVal: {
      total_size += 1 + 8;
      break;
    }
    // string str_val = 5;
    case kStrVal: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_str_val());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Config::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Config::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Config::GetClassData() const { return &_class_data_; }

void Config::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Config *>(to)->MergeFrom(
      static_cast<const Config &>(from));
}


void Config::MergeFrom(const Config& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:deck.Config)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  switch (from.value_case()) {
    case kBoolVal: {
      _internal_set_bool_val(from._internal_bool_val());
      break;
    }
    case kIntVal: {
      _internal_set_int_val(from._internal_int_val());
      break;
    }
    case kFloatVal: {
      _internal_set_float_val(from._internal_float_val());
      break;
    }
    case kStrVal: {
      _internal_set_str_val(from._internal_str_val());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Config::CopyFrom(const Config& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:deck.Config)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Config::IsInitialized() const {
  return true;
}

void Config::InternalSwap(Config* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, lhs_arena,
      &other->key_, rhs_arena
  );
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata Config::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_getter, &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_once,
      file_level_metadata_deck_2fproto_2fdeck_5fservice_2eproto[0]);
}

// ===================================================================

class SetConfigsRequest::_Internal {
 public:
};

SetConfigsRequest::SetConfigsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  configs_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:deck.SetConfigsRequest)
}
SetConfigsRequest::SetConfigsRequest(const SetConfigsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      configs_(from.configs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  camera_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    camera_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_camera().empty()) {
    camera_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_camera(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:deck.SetConfigsRequest)
}

inline void SetConfigsRequest::SharedCtor() {
camera_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  camera_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SetConfigsRequest::~SetConfigsRequest() {
  // @@protoc_insertion_point(destructor:deck.SetConfigsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetConfigsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  camera_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetConfigsRequest::ArenaDtor(void* object) {
  SetConfigsRequest* _this = reinterpret_cast< SetConfigsRequest* >(object);
  (void)_this;
}
void SetConfigsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetConfigsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetConfigsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:deck.SetConfigsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  configs_.Clear();
  camera_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetConfigsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string camera = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_camera();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "deck.SetConfigsRequest.camera"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .deck.Config configs = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_configs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetConfigsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:deck.SetConfigsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string camera = 1;
  if (!this->_internal_camera().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_camera().data(), static_cast<int>(this->_internal_camera().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "deck.SetConfigsRequest.camera");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_camera(), target);
  }

  // repeated .deck.Config configs = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_configs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_configs(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:deck.SetConfigsRequest)
  return target;
}

size_t SetConfigsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:deck.SetConfigsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .deck.Config configs = 2;
  total_size += 1UL * this->_internal_configs_size();
  for (const auto& msg : this->configs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string camera = 1;
  if (!this->_internal_camera().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_camera());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetConfigsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetConfigsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetConfigsRequest::GetClassData() const { return &_class_data_; }

void SetConfigsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetConfigsRequest *>(to)->MergeFrom(
      static_cast<const SetConfigsRequest &>(from));
}


void SetConfigsRequest::MergeFrom(const SetConfigsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:deck.SetConfigsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  configs_.MergeFrom(from.configs_);
  if (!from._internal_camera().empty()) {
    _internal_set_camera(from._internal_camera());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetConfigsRequest::CopyFrom(const SetConfigsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:deck.SetConfigsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetConfigsRequest::IsInitialized() const {
  return true;
}

void SetConfigsRequest::InternalSwap(SetConfigsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  configs_.InternalSwap(&other->configs_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &camera_, lhs_arena,
      &other->camera_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SetConfigsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_getter, &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_once,
      file_level_metadata_deck_2fproto_2fdeck_5fservice_2eproto[1]);
}

// ===================================================================

class SetConfigsResponse::_Internal {
 public:
};

SetConfigsResponse::SetConfigsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:deck.SetConfigsResponse)
}
SetConfigsResponse::SetConfigsResponse(const SetConfigsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:deck.SetConfigsResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetConfigsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetConfigsResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetConfigsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_getter, &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_once,
      file_level_metadata_deck_2fproto_2fdeck_5fservice_2eproto[2]);
}

// ===================================================================

class GetConfigRequest::_Internal {
 public:
};

GetConfigRequest::GetConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:deck.GetConfigRequest)
}
GetConfigRequest::GetConfigRequest(const GetConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  camera_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    camera_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_camera().empty()) {
    camera_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_camera(), 
      GetArenaForAllocation());
  }
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_type().empty()) {
    type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_type(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:deck.GetConfigRequest)
}

inline void GetConfigRequest::SharedCtor() {
camera_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  camera_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetConfigRequest::~GetConfigRequest() {
  // @@protoc_insertion_point(destructor:deck.GetConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  camera_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetConfigRequest::ArenaDtor(void* object) {
  GetConfigRequest* _this = reinterpret_cast< GetConfigRequest* >(object);
  (void)_this;
}
void GetConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:deck.GetConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  camera_.ClearToEmpty();
  key_.ClearToEmpty();
  type_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string camera = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_camera();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "deck.GetConfigRequest.camera"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string key = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "deck.GetConfigRequest.key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "deck.GetConfigRequest.type"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:deck.GetConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string camera = 1;
  if (!this->_internal_camera().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_camera().data(), static_cast<int>(this->_internal_camera().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "deck.GetConfigRequest.camera");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_camera(), target);
  }

  // string key = 2;
  if (!this->_internal_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "deck.GetConfigRequest.key");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_key(), target);
  }

  // string type = 3;
  if (!this->_internal_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_type().data(), static_cast<int>(this->_internal_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "deck.GetConfigRequest.type");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:deck.GetConfigRequest)
  return target;
}

size_t GetConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:deck.GetConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string camera = 1;
  if (!this->_internal_camera().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_camera());
  }

  // string key = 2;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  // string type = 3;
  if (!this->_internal_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetConfigRequest::GetClassData() const { return &_class_data_; }

void GetConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetConfigRequest *>(to)->MergeFrom(
      static_cast<const GetConfigRequest &>(from));
}


void GetConfigRequest::MergeFrom(const GetConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:deck.GetConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_camera().empty()) {
    _internal_set_camera(from._internal_camera());
  }
  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  if (!from._internal_type().empty()) {
    _internal_set_type(from._internal_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetConfigRequest::CopyFrom(const GetConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:deck.GetConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetConfigRequest::IsInitialized() const {
  return true;
}

void GetConfigRequest::InternalSwap(GetConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &camera_, lhs_arena,
      &other->camera_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, lhs_arena,
      &other->key_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &type_, lhs_arena,
      &other->type_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_getter, &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_once,
      file_level_metadata_deck_2fproto_2fdeck_5fservice_2eproto[3]);
}

// ===================================================================

class GetConfigResponse::_Internal {
 public:
  static const ::deck::Config& config(const GetConfigResponse* msg);
};

const ::deck::Config&
GetConfigResponse::_Internal::config(const GetConfigResponse* msg) {
  return *msg->config_;
}
GetConfigResponse::GetConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:deck.GetConfigResponse)
}
GetConfigResponse::GetConfigResponse(const GetConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_config()) {
    config_ = new ::deck::Config(*from.config_);
  } else {
    config_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:deck.GetConfigResponse)
}

inline void GetConfigResponse::SharedCtor() {
config_ = nullptr;
}

GetConfigResponse::~GetConfigResponse() {
  // @@protoc_insertion_point(destructor:deck.GetConfigResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetConfigResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete config_;
}

void GetConfigResponse::ArenaDtor(void* object) {
  GetConfigResponse* _this = reinterpret_cast< GetConfigResponse* >(object);
  (void)_this;
}
void GetConfigResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetConfigResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetConfigResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:deck.GetConfigResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetConfigResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .deck.Config config = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetConfigResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:deck.GetConfigResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .deck.Config config = 1;
  if (this->_internal_has_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:deck.GetConfigResponse)
  return target;
}

size_t GetConfigResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:deck.GetConfigResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .deck.Config config = 1;
  if (this->_internal_has_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *config_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetConfigResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetConfigResponse::GetClassData() const { return &_class_data_; }

void GetConfigResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetConfigResponse *>(to)->MergeFrom(
      static_cast<const GetConfigResponse &>(from));
}


void GetConfigResponse::MergeFrom(const GetConfigResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:deck.GetConfigResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_config()) {
    _internal_mutable_config()->::deck::Config::MergeFrom(from._internal_config());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetConfigResponse::CopyFrom(const GetConfigResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:deck.GetConfigResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetConfigResponse::IsInitialized() const {
  return true;
}

void GetConfigResponse::InternalSwap(GetConfigResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(config_, other->config_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_getter, &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_once,
      file_level_metadata_deck_2fproto_2fdeck_5fservice_2eproto[4]);
}

// ===================================================================

class PingMsg::_Internal {
 public:
};

PingMsg::PingMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:deck.PingMsg)
}
PingMsg::PingMsg(const PingMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  x_ = from.x_;
  // @@protoc_insertion_point(copy_constructor:deck.PingMsg)
}

inline void PingMsg::SharedCtor() {
x_ = 0u;
}

PingMsg::~PingMsg() {
  // @@protoc_insertion_point(destructor:deck.PingMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PingMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PingMsg::ArenaDtor(void* object) {
  PingMsg* _this = reinterpret_cast< PingMsg* >(object);
  (void)_this;
}
void PingMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PingMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PingMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:deck.PingMsg)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  x_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PingMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PingMsg::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:deck.PingMsg)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_x(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:deck.PingMsg)
  return target;
}

size_t PingMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:deck.PingMsg)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_x());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PingMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PingMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PingMsg::GetClassData() const { return &_class_data_; }

void PingMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PingMsg *>(to)->MergeFrom(
      static_cast<const PingMsg &>(from));
}


void PingMsg::MergeFrom(const PingMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:deck.PingMsg)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x() != 0) {
    _internal_set_x(from._internal_x());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PingMsg::CopyFrom(const PingMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:deck.PingMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PingMsg::IsInitialized() const {
  return true;
}

void PingMsg::InternalSwap(PingMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(x_, other->x_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PingMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_getter, &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_once,
      file_level_metadata_deck_2fproto_2fdeck_5fservice_2eproto[5]);
}

// ===================================================================

class PongMsg::_Internal {
 public:
};

PongMsg::PongMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:deck.PongMsg)
}
PongMsg::PongMsg(const PongMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  x_ = from.x_;
  // @@protoc_insertion_point(copy_constructor:deck.PongMsg)
}

inline void PongMsg::SharedCtor() {
x_ = 0u;
}

PongMsg::~PongMsg() {
  // @@protoc_insertion_point(destructor:deck.PongMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PongMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PongMsg::ArenaDtor(void* object) {
  PongMsg* _this = reinterpret_cast< PongMsg* >(object);
  (void)_this;
}
void PongMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PongMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PongMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:deck.PongMsg)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  x_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PongMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PongMsg::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:deck.PongMsg)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_x(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:deck.PongMsg)
  return target;
}

size_t PongMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:deck.PongMsg)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_x());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PongMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PongMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PongMsg::GetClassData() const { return &_class_data_; }

void PongMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PongMsg *>(to)->MergeFrom(
      static_cast<const PongMsg &>(from));
}


void PongMsg::MergeFrom(const PongMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:deck.PongMsg)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x() != 0) {
    _internal_set_x(from._internal_x());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PongMsg::CopyFrom(const PongMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:deck.PongMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PongMsg::IsInitialized() const {
  return true;
}

void PongMsg::InternalSwap(PongMsg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(x_, other->x_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PongMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_getter, &descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto_once,
      file_level_metadata_deck_2fproto_2fdeck_5fservice_2eproto[6]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace deck
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::deck::Config* Arena::CreateMaybeMessage< ::deck::Config >(Arena* arena) {
  return Arena::CreateMessageInternal< ::deck::Config >(arena);
}
template<> PROTOBUF_NOINLINE ::deck::SetConfigsRequest* Arena::CreateMaybeMessage< ::deck::SetConfigsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::deck::SetConfigsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::deck::SetConfigsResponse* Arena::CreateMaybeMessage< ::deck::SetConfigsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::deck::SetConfigsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::deck::GetConfigRequest* Arena::CreateMaybeMessage< ::deck::GetConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::deck::GetConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::deck::GetConfigResponse* Arena::CreateMaybeMessage< ::deck::GetConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::deck::GetConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::deck::PingMsg* Arena::CreateMaybeMessage< ::deck::PingMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::deck::PingMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::deck::PongMsg* Arena::CreateMaybeMessage< ::deck::PongMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::deck::PongMsg >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: deck/proto/deck_service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='deck/proto/deck_service.proto',
  package='deck',
  syntax='proto3',
  serialized_options=b'Z\nproto/deck',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1d\x64\x65\x63k/proto/deck_service.proto\x12\x04\x64\x65\x63k\"m\n\x06\x43onfig\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x12\n\x08\x62ool_val\x18\x02 \x01(\x08H\x00\x12\x11\n\x07int_val\x18\x03 \x01(\x03H\x00\x12\x13\n\tfloat_val\x18\x04 \x01(\x01H\x00\x12\x11\n\x07str_val\x18\x05 \x01(\tH\x00\x42\x07\n\x05value\"B\n\x11SetConfigsRequest\x12\x0e\n\x06\x63\x61mera\x18\x01 \x01(\t\x12\x1d\n\x07\x63onfigs\x18\x02 \x03(\x0b\x32\x0c.deck.Config\"\x14\n\x12SetConfigsResponse\"=\n\x10GetConfigRequest\x12\x0e\n\x06\x63\x61mera\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\"1\n\x11GetConfigResponse\x12\x1c\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x0c.deck.Config\"\x14\n\x07PingMsg\x12\t\n\x01x\x18\x01 \x01(\r\"\x14\n\x07PongMsg\x12\t\n\x01x\x18\x01 \x01(\r2\xb8\x01\n\x0b\x44\x65\x63kService\x12&\n\x04Ping\x12\r.deck.PingMsg\x1a\r.deck.PongMsg\"\x00\x12\x41\n\nSetConfigs\x12\x17.deck.SetConfigsRequest\x1a\x18.deck.SetConfigsResponse\"\x00\x12>\n\tGetConfig\x12\x16.deck.GetConfigRequest\x1a\x17.deck.GetConfigResponse\"\x00\x42\x0cZ\nproto/deckb\x06proto3'
)




_CONFIG = _descriptor.Descriptor(
  name='Config',
  full_name='deck.Config',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='deck.Config.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bool_val', full_name='deck.Config.bool_val', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='int_val', full_name='deck.Config.int_val', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='float_val', full_name='deck.Config.float_val', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='str_val', full_name='deck.Config.str_val', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='deck.Config.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=39,
  serialized_end=148,
)


_SETCONFIGSREQUEST = _descriptor.Descriptor(
  name='SetConfigsRequest',
  full_name='deck.SetConfigsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='camera', full_name='deck.SetConfigsRequest.camera', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='configs', full_name='deck.SetConfigsRequest.configs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=150,
  serialized_end=216,
)


_SETCONFIGSRESPONSE = _descriptor.Descriptor(
  name='SetConfigsResponse',
  full_name='deck.SetConfigsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=218,
  serialized_end=238,
)


_GETCONFIGREQUEST = _descriptor.Descriptor(
  name='GetConfigRequest',
  full_name='deck.GetConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='camera', full_name='deck.GetConfigRequest.camera', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='key', full_name='deck.GetConfigRequest.key', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='deck.GetConfigRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=240,
  serialized_end=301,
)


_GETCONFIGRESPONSE = _descriptor.Descriptor(
  name='GetConfigResponse',
  full_name='deck.GetConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='deck.GetConfigResponse.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=303,
  serialized_end=352,
)


_PINGMSG = _descriptor.Descriptor(
  name='PingMsg',
  full_name='deck.PingMsg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='deck.PingMsg.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=354,
  serialized_end=374,
)


_PONGMSG = _descriptor.Descriptor(
  name='PongMsg',
  full_name='deck.PongMsg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='deck.PongMsg.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=376,
  serialized_end=396,
)

_CONFIG.oneofs_by_name['value'].fields.append(
  _CONFIG.fields_by_name['bool_val'])
_CONFIG.fields_by_name['bool_val'].containing_oneof = _CONFIG.oneofs_by_name['value']
_CONFIG.oneofs_by_name['value'].fields.append(
  _CONFIG.fields_by_name['int_val'])
_CONFIG.fields_by_name['int_val'].containing_oneof = _CONFIG.oneofs_by_name['value']
_CONFIG.oneofs_by_name['value'].fields.append(
  _CONFIG.fields_by_name['float_val'])
_CONFIG.fields_by_name['float_val'].containing_oneof = _CONFIG.oneofs_by_name['value']
_CONFIG.oneofs_by_name['value'].fields.append(
  _CONFIG.fields_by_name['str_val'])
_CONFIG.fields_by_name['str_val'].containing_oneof = _CONFIG.oneofs_by_name['value']
_SETCONFIGSREQUEST.fields_by_name['configs'].message_type = _CONFIG
_GETCONFIGRESPONSE.fields_by_name['config'].message_type = _CONFIG
DESCRIPTOR.message_types_by_name['Config'] = _CONFIG
DESCRIPTOR.message_types_by_name['SetConfigsRequest'] = _SETCONFIGSREQUEST
DESCRIPTOR.message_types_by_name['SetConfigsResponse'] = _SETCONFIGSRESPONSE
DESCRIPTOR.message_types_by_name['GetConfigRequest'] = _GETCONFIGREQUEST
DESCRIPTOR.message_types_by_name['GetConfigResponse'] = _GETCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['PingMsg'] = _PINGMSG
DESCRIPTOR.message_types_by_name['PongMsg'] = _PONGMSG
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'deck.proto.deck_service_pb2'
  # @@protoc_insertion_point(class_scope:deck.Config)
  })
_sym_db.RegisterMessage(Config)

SetConfigsRequest = _reflection.GeneratedProtocolMessageType('SetConfigsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETCONFIGSREQUEST,
  '__module__' : 'deck.proto.deck_service_pb2'
  # @@protoc_insertion_point(class_scope:deck.SetConfigsRequest)
  })
_sym_db.RegisterMessage(SetConfigsRequest)

SetConfigsResponse = _reflection.GeneratedProtocolMessageType('SetConfigsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETCONFIGSRESPONSE,
  '__module__' : 'deck.proto.deck_service_pb2'
  # @@protoc_insertion_point(class_scope:deck.SetConfigsResponse)
  })
_sym_db.RegisterMessage(SetConfigsResponse)

GetConfigRequest = _reflection.GeneratedProtocolMessageType('GetConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCONFIGREQUEST,
  '__module__' : 'deck.proto.deck_service_pb2'
  # @@protoc_insertion_point(class_scope:deck.GetConfigRequest)
  })
_sym_db.RegisterMessage(GetConfigRequest)

GetConfigResponse = _reflection.GeneratedProtocolMessageType('GetConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCONFIGRESPONSE,
  '__module__' : 'deck.proto.deck_service_pb2'
  # @@protoc_insertion_point(class_scope:deck.GetConfigResponse)
  })
_sym_db.RegisterMessage(GetConfigResponse)

PingMsg = _reflection.GeneratedProtocolMessageType('PingMsg', (_message.Message,), {
  'DESCRIPTOR' : _PINGMSG,
  '__module__' : 'deck.proto.deck_service_pb2'
  # @@protoc_insertion_point(class_scope:deck.PingMsg)
  })
_sym_db.RegisterMessage(PingMsg)

PongMsg = _reflection.GeneratedProtocolMessageType('PongMsg', (_message.Message,), {
  'DESCRIPTOR' : _PONGMSG,
  '__module__' : 'deck.proto.deck_service_pb2'
  # @@protoc_insertion_point(class_scope:deck.PongMsg)
  })
_sym_db.RegisterMessage(PongMsg)


DESCRIPTOR._options = None

_DECKSERVICE = _descriptor.ServiceDescriptor(
  name='DeckService',
  full_name='deck.DeckService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=399,
  serialized_end=583,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='deck.DeckService.Ping',
    index=0,
    containing_service=None,
    input_type=_PINGMSG,
    output_type=_PONGMSG,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetConfigs',
    full_name='deck.DeckService.SetConfigs',
    index=1,
    containing_service=None,
    input_type=_SETCONFIGSREQUEST,
    output_type=_SETCONFIGSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetConfig',
    full_name='deck.DeckService.GetConfig',
    index=2,
    containing_service=None,
    input_type=_GETCONFIGREQUEST,
    output_type=_GETCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_DECKSERVICE)

DESCRIPTOR.services_by_name['DeckService'] = _DECKSERVICE

# @@protoc_insertion_point(module_scope)

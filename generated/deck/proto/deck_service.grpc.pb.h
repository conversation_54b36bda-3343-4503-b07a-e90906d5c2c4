// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: deck/proto/deck_service.proto
#ifndef GRPC_deck_2fproto_2fdeck_5fservice_2eproto__INCLUDED
#define GRPC_deck_2fproto_2fdeck_5fservice_2eproto__INCLUDED

#include "deck/proto/deck_service.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace deck {

class DeckService final {
 public:
  static constexpr char const* service_full_name() {
    return "deck.DeckService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Ping(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::deck::PongMsg* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::PongMsg>> AsyncPing(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::PongMsg>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::PongMsg>> PrepareAsyncPing(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::PongMsg>>(PrepareAsyncPingRaw(context, request, cq));
    }
    virtual ::grpc::Status SetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::deck::SetConfigsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::SetConfigsResponse>> AsyncSetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::SetConfigsResponse>>(AsyncSetConfigsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::SetConfigsResponse>> PrepareAsyncSetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::SetConfigsResponse>>(PrepareAsyncSetConfigsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::deck::GetConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::GetConfigResponse>> AsyncGetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::GetConfigResponse>>(AsyncGetConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::GetConfigResponse>> PrepareAsyncGetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::deck::GetConfigResponse>>(PrepareAsyncGetConfigRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Ping(::grpc::ClientContext* context, const ::deck::PingMsg* request, ::deck::PongMsg* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Ping(::grpc::ClientContext* context, const ::deck::PingMsg* request, ::deck::PongMsg* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest* request, ::deck::SetConfigsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest* request, ::deck::SetConfigsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest* request, ::deck::GetConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest* request, ::deck::GetConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::deck::PongMsg>* AsyncPingRaw(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::deck::PongMsg>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::deck::SetConfigsResponse>* AsyncSetConfigsRaw(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::deck::SetConfigsResponse>* PrepareAsyncSetConfigsRaw(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::deck::GetConfigResponse>* AsyncGetConfigRaw(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::deck::GetConfigResponse>* PrepareAsyncGetConfigRaw(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Ping(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::deck::PongMsg* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::PongMsg>> AsyncPing(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::PongMsg>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::PongMsg>> PrepareAsyncPing(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::PongMsg>>(PrepareAsyncPingRaw(context, request, cq));
    }
    ::grpc::Status SetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::deck::SetConfigsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::SetConfigsResponse>> AsyncSetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::SetConfigsResponse>>(AsyncSetConfigsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::SetConfigsResponse>> PrepareAsyncSetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::SetConfigsResponse>>(PrepareAsyncSetConfigsRaw(context, request, cq));
    }
    ::grpc::Status GetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::deck::GetConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::GetConfigResponse>> AsyncGetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::GetConfigResponse>>(AsyncGetConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::GetConfigResponse>> PrepareAsyncGetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::deck::GetConfigResponse>>(PrepareAsyncGetConfigRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Ping(::grpc::ClientContext* context, const ::deck::PingMsg* request, ::deck::PongMsg* response, std::function<void(::grpc::Status)>) override;
      void Ping(::grpc::ClientContext* context, const ::deck::PingMsg* request, ::deck::PongMsg* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest* request, ::deck::SetConfigsResponse* response, std::function<void(::grpc::Status)>) override;
      void SetConfigs(::grpc::ClientContext* context, const ::deck::SetConfigsRequest* request, ::deck::SetConfigsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest* request, ::deck::GetConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void GetConfig(::grpc::ClientContext* context, const ::deck::GetConfigRequest* request, ::deck::GetConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::deck::PongMsg>* AsyncPingRaw(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::deck::PongMsg>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::deck::PingMsg& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::deck::SetConfigsResponse>* AsyncSetConfigsRaw(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::deck::SetConfigsResponse>* PrepareAsyncSetConfigsRaw(::grpc::ClientContext* context, const ::deck::SetConfigsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::deck::GetConfigResponse>* AsyncGetConfigRaw(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::deck::GetConfigResponse>* PrepareAsyncGetConfigRaw(::grpc::ClientContext* context, const ::deck::GetConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Ping_;
    const ::grpc::internal::RpcMethod rpcmethod_SetConfigs_;
    const ::grpc::internal::RpcMethod rpcmethod_GetConfig_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Ping(::grpc::ServerContext* context, const ::deck::PingMsg* request, ::deck::PongMsg* response);
    virtual ::grpc::Status SetConfigs(::grpc::ServerContext* context, const ::deck::SetConfigsRequest* request, ::deck::SetConfigsResponse* response);
    virtual ::grpc::Status GetConfig(::grpc::ServerContext* context, const ::deck::GetConfigRequest* request, ::deck::GetConfigResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Ping() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::deck::PingMsg* /*request*/, ::deck::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::deck::PingMsg* request, ::grpc::ServerAsyncResponseWriter< ::deck::PongMsg>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetConfigs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetConfigs() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_SetConfigs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetConfigs(::grpc::ServerContext* /*context*/, const ::deck::SetConfigsRequest* /*request*/, ::deck::SetConfigsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetConfigs(::grpc::ServerContext* context, ::deck::SetConfigsRequest* request, ::grpc::ServerAsyncResponseWriter< ::deck::SetConfigsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetConfig() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::deck::GetConfigRequest* /*request*/, ::deck::GetConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConfig(::grpc::ServerContext* context, ::deck::GetConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::deck::GetConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Ping<WithAsyncMethod_SetConfigs<WithAsyncMethod_GetConfig<Service > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::deck::PingMsg, ::deck::PongMsg>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::deck::PingMsg* request, ::deck::PongMsg* response) { return this->Ping(context, request, response); }));}
    void SetMessageAllocatorFor_Ping(
        ::grpc::MessageAllocator< ::deck::PingMsg, ::deck::PongMsg>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::deck::PingMsg, ::deck::PongMsg>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::deck::PingMsg* /*request*/, ::deck::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::deck::PingMsg* /*request*/, ::deck::PongMsg* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetConfigs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetConfigs() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::deck::SetConfigsRequest, ::deck::SetConfigsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::deck::SetConfigsRequest* request, ::deck::SetConfigsResponse* response) { return this->SetConfigs(context, request, response); }));}
    void SetMessageAllocatorFor_SetConfigs(
        ::grpc::MessageAllocator< ::deck::SetConfigsRequest, ::deck::SetConfigsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::deck::SetConfigsRequest, ::deck::SetConfigsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetConfigs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetConfigs(::grpc::ServerContext* /*context*/, const ::deck::SetConfigsRequest* /*request*/, ::deck::SetConfigsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetConfigs(
      ::grpc::CallbackServerContext* /*context*/, const ::deck::SetConfigsRequest* /*request*/, ::deck::SetConfigsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetConfig() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::deck::GetConfigRequest, ::deck::GetConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::deck::GetConfigRequest* request, ::deck::GetConfigResponse* response) { return this->GetConfig(context, request, response); }));}
    void SetMessageAllocatorFor_GetConfig(
        ::grpc::MessageAllocator< ::deck::GetConfigRequest, ::deck::GetConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::deck::GetConfigRequest, ::deck::GetConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::deck::GetConfigRequest* /*request*/, ::deck::GetConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::deck::GetConfigRequest* /*request*/, ::deck::GetConfigResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Ping<WithCallbackMethod_SetConfigs<WithCallbackMethod_GetConfig<Service > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Ping() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::deck::PingMsg* /*request*/, ::deck::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetConfigs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetConfigs() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_SetConfigs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetConfigs(::grpc::ServerContext* /*context*/, const ::deck::SetConfigsRequest* /*request*/, ::deck::SetConfigsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetConfig() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::deck::GetConfigRequest* /*request*/, ::deck::GetConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Ping() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::deck::PingMsg* /*request*/, ::deck::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetConfigs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetConfigs() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_SetConfigs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetConfigs(::grpc::ServerContext* /*context*/, const ::deck::SetConfigsRequest* /*request*/, ::deck::SetConfigsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetConfigs(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetConfig() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::deck::GetConfigRequest* /*request*/, ::deck::GetConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Ping(context, request, response); }));
    }
    ~WithRawCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::deck::PingMsg* /*request*/, ::deck::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetConfigs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetConfigs() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetConfigs(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetConfigs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetConfigs(::grpc::ServerContext* /*context*/, const ::deck::SetConfigsRequest* /*request*/, ::deck::SetConfigsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetConfigs(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetConfig() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::deck::GetConfigRequest* /*request*/, ::deck::GetConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Ping() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::deck::PingMsg, ::deck::PongMsg>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::deck::PingMsg, ::deck::PongMsg>* streamer) {
                       return this->StreamedPing(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::deck::PingMsg* /*request*/, ::deck::PongMsg* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPing(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::deck::PingMsg,::deck::PongMsg>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetConfigs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetConfigs() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::deck::SetConfigsRequest, ::deck::SetConfigsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::deck::SetConfigsRequest, ::deck::SetConfigsResponse>* streamer) {
                       return this->StreamedSetConfigs(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetConfigs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetConfigs(::grpc::ServerContext* /*context*/, const ::deck::SetConfigsRequest* /*request*/, ::deck::SetConfigsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetConfigs(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::deck::SetConfigsRequest,::deck::SetConfigsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetConfig() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::deck::GetConfigRequest, ::deck::GetConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::deck::GetConfigRequest, ::deck::GetConfigResponse>* streamer) {
                       return this->StreamedGetConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetConfig(::grpc::ServerContext* /*context*/, const ::deck::GetConfigRequest* /*request*/, ::deck::GetConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::deck::GetConfigRequest,::deck::GetConfigResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_SetConfigs<WithStreamedUnaryMethod_GetConfig<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_SetConfigs<WithStreamedUnaryMethod_GetConfig<Service > > > StreamedService;
};

}  // namespace deck


#endif  // GRPC_deck_2fproto_2fdeck_5fservice_2eproto__INCLUDED

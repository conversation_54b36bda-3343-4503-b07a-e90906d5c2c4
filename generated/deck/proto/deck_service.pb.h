// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deck/proto/deck_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_deck_2fproto_2fdeck_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_deck_2fproto_2fdeck_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_deck_2fproto_2fdeck_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_deck_2fproto_2fdeck_5fservice_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[7]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deck_2fproto_2fdeck_5fservice_2eproto;
namespace deck {
class Config;
struct ConfigDefaultTypeInternal;
extern ConfigDefaultTypeInternal _Config_default_instance_;
class GetConfigRequest;
struct GetConfigRequestDefaultTypeInternal;
extern GetConfigRequestDefaultTypeInternal _GetConfigRequest_default_instance_;
class GetConfigResponse;
struct GetConfigResponseDefaultTypeInternal;
extern GetConfigResponseDefaultTypeInternal _GetConfigResponse_default_instance_;
class PingMsg;
struct PingMsgDefaultTypeInternal;
extern PingMsgDefaultTypeInternal _PingMsg_default_instance_;
class PongMsg;
struct PongMsgDefaultTypeInternal;
extern PongMsgDefaultTypeInternal _PongMsg_default_instance_;
class SetConfigsRequest;
struct SetConfigsRequestDefaultTypeInternal;
extern SetConfigsRequestDefaultTypeInternal _SetConfigsRequest_default_instance_;
class SetConfigsResponse;
struct SetConfigsResponseDefaultTypeInternal;
extern SetConfigsResponseDefaultTypeInternal _SetConfigsResponse_default_instance_;
}  // namespace deck
PROTOBUF_NAMESPACE_OPEN
template<> ::deck::Config* Arena::CreateMaybeMessage<::deck::Config>(Arena*);
template<> ::deck::GetConfigRequest* Arena::CreateMaybeMessage<::deck::GetConfigRequest>(Arena*);
template<> ::deck::GetConfigResponse* Arena::CreateMaybeMessage<::deck::GetConfigResponse>(Arena*);
template<> ::deck::PingMsg* Arena::CreateMaybeMessage<::deck::PingMsg>(Arena*);
template<> ::deck::PongMsg* Arena::CreateMaybeMessage<::deck::PongMsg>(Arena*);
template<> ::deck::SetConfigsRequest* Arena::CreateMaybeMessage<::deck::SetConfigsRequest>(Arena*);
template<> ::deck::SetConfigsResponse* Arena::CreateMaybeMessage<::deck::SetConfigsResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace deck {

// ===================================================================

class Config final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:deck.Config) */ {
 public:
  inline Config() : Config(nullptr) {}
  ~Config() override;
  explicit constexpr Config(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Config(const Config& from);
  Config(Config&& from) noexcept
    : Config() {
    *this = ::std::move(from);
  }

  inline Config& operator=(const Config& from) {
    CopyFrom(from);
    return *this;
  }
  inline Config& operator=(Config&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Config& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kBoolVal = 2,
    kIntVal = 3,
    kFloatVal = 4,
    kStrVal = 5,
    VALUE_NOT_SET = 0,
  };

  static inline const Config* internal_default_instance() {
    return reinterpret_cast<const Config*>(
               &_Config_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Config& a, Config& b) {
    a.Swap(&b);
  }
  inline void Swap(Config* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Config* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Config* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Config>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Config& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Config& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Config* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "deck.Config";
  }
  protected:
  explicit Config(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kBoolValFieldNumber = 2,
    kIntValFieldNumber = 3,
    kFloatValFieldNumber = 4,
    kStrValFieldNumber = 5,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // bool bool_val = 2;
  bool has_bool_val() const;
  private:
  bool _internal_has_bool_val() const;
  public:
  void clear_bool_val();
  bool bool_val() const;
  void set_bool_val(bool value);
  private:
  bool _internal_bool_val() const;
  void _internal_set_bool_val(bool value);
  public:

  // int64 int_val = 3;
  bool has_int_val() const;
  private:
  bool _internal_has_int_val() const;
  public:
  void clear_int_val();
  int64_t int_val() const;
  void set_int_val(int64_t value);
  private:
  int64_t _internal_int_val() const;
  void _internal_set_int_val(int64_t value);
  public:

  // double float_val = 4;
  bool has_float_val() const;
  private:
  bool _internal_has_float_val() const;
  public:
  void clear_float_val();
  double float_val() const;
  void set_float_val(double value);
  private:
  double _internal_float_val() const;
  void _internal_set_float_val(double value);
  public:

  // string str_val = 5;
  bool has_str_val() const;
  private:
  bool _internal_has_str_val() const;
  public:
  void clear_str_val();
  const std::string& str_val() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_str_val(ArgT0&& arg0, ArgT... args);
  std::string* mutable_str_val();
  PROTOBUF_NODISCARD std::string* release_str_val();
  void set_allocated_str_val(std::string* str_val);
  private:
  const std::string& _internal_str_val() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_str_val(const std::string& value);
  std::string* _internal_mutable_str_val();
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:deck.Config)
 private:
  class _Internal;
  void set_has_bool_val();
  void set_has_int_val();
  void set_has_float_val();
  void set_has_str_val();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    bool bool_val_;
    int64_t int_val_;
    double float_val_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr str_val_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_deck_2fproto_2fdeck_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SetConfigsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:deck.SetConfigsRequest) */ {
 public:
  inline SetConfigsRequest() : SetConfigsRequest(nullptr) {}
  ~SetConfigsRequest() override;
  explicit constexpr SetConfigsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetConfigsRequest(const SetConfigsRequest& from);
  SetConfigsRequest(SetConfigsRequest&& from) noexcept
    : SetConfigsRequest() {
    *this = ::std::move(from);
  }

  inline SetConfigsRequest& operator=(const SetConfigsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetConfigsRequest& operator=(SetConfigsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetConfigsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetConfigsRequest* internal_default_instance() {
    return reinterpret_cast<const SetConfigsRequest*>(
               &_SetConfigsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SetConfigsRequest& a, SetConfigsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetConfigsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetConfigsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetConfigsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetConfigsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetConfigsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetConfigsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetConfigsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "deck.SetConfigsRequest";
  }
  protected:
  explicit SetConfigsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigsFieldNumber = 2,
    kCameraFieldNumber = 1,
  };
  // repeated .deck.Config configs = 2;
  int configs_size() const;
  private:
  int _internal_configs_size() const;
  public:
  void clear_configs();
  ::deck::Config* mutable_configs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::deck::Config >*
      mutable_configs();
  private:
  const ::deck::Config& _internal_configs(int index) const;
  ::deck::Config* _internal_add_configs();
  public:
  const ::deck::Config& configs(int index) const;
  ::deck::Config* add_configs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::deck::Config >&
      configs() const;

  // string camera = 1;
  void clear_camera();
  const std::string& camera() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_camera(ArgT0&& arg0, ArgT... args);
  std::string* mutable_camera();
  PROTOBUF_NODISCARD std::string* release_camera();
  void set_allocated_camera(std::string* camera);
  private:
  const std::string& _internal_camera() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_camera(const std::string& value);
  std::string* _internal_mutable_camera();
  public:

  // @@protoc_insertion_point(class_scope:deck.SetConfigsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::deck::Config > configs_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr camera_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deck_2fproto_2fdeck_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SetConfigsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:deck.SetConfigsResponse) */ {
 public:
  inline SetConfigsResponse() : SetConfigsResponse(nullptr) {}
  explicit constexpr SetConfigsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetConfigsResponse(const SetConfigsResponse& from);
  SetConfigsResponse(SetConfigsResponse&& from) noexcept
    : SetConfigsResponse() {
    *this = ::std::move(from);
  }

  inline SetConfigsResponse& operator=(const SetConfigsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetConfigsResponse& operator=(SetConfigsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetConfigsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetConfigsResponse* internal_default_instance() {
    return reinterpret_cast<const SetConfigsResponse*>(
               &_SetConfigsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SetConfigsResponse& a, SetConfigsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetConfigsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetConfigsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetConfigsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetConfigsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetConfigsResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetConfigsResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "deck.SetConfigsResponse";
  }
  protected:
  explicit SetConfigsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:deck.SetConfigsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deck_2fproto_2fdeck_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:deck.GetConfigRequest) */ {
 public:
  inline GetConfigRequest() : GetConfigRequest(nullptr) {}
  ~GetConfigRequest() override;
  explicit constexpr GetConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetConfigRequest(const GetConfigRequest& from);
  GetConfigRequest(GetConfigRequest&& from) noexcept
    : GetConfigRequest() {
    *this = ::std::move(from);
  }

  inline GetConfigRequest& operator=(const GetConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetConfigRequest& operator=(GetConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetConfigRequest* internal_default_instance() {
    return reinterpret_cast<const GetConfigRequest*>(
               &_GetConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetConfigRequest& a, GetConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "deck.GetConfigRequest";
  }
  protected:
  explicit GetConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCameraFieldNumber = 1,
    kKeyFieldNumber = 2,
    kTypeFieldNumber = 3,
  };
  // string camera = 1;
  void clear_camera();
  const std::string& camera() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_camera(ArgT0&& arg0, ArgT... args);
  std::string* mutable_camera();
  PROTOBUF_NODISCARD std::string* release_camera();
  void set_allocated_camera(std::string* camera);
  private:
  const std::string& _internal_camera() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_camera(const std::string& value);
  std::string* _internal_mutable_camera();
  public:

  // string key = 2;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // string type = 3;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // @@protoc_insertion_point(class_scope:deck.GetConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr camera_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deck_2fproto_2fdeck_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:deck.GetConfigResponse) */ {
 public:
  inline GetConfigResponse() : GetConfigResponse(nullptr) {}
  ~GetConfigResponse() override;
  explicit constexpr GetConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetConfigResponse(const GetConfigResponse& from);
  GetConfigResponse(GetConfigResponse&& from) noexcept
    : GetConfigResponse() {
    *this = ::std::move(from);
  }

  inline GetConfigResponse& operator=(const GetConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetConfigResponse& operator=(GetConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetConfigResponse* internal_default_instance() {
    return reinterpret_cast<const GetConfigResponse*>(
               &_GetConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetConfigResponse& a, GetConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "deck.GetConfigResponse";
  }
  protected:
  explicit GetConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigFieldNumber = 1,
  };
  // .deck.Config config = 1;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::deck::Config& config() const;
  PROTOBUF_NODISCARD ::deck::Config* release_config();
  ::deck::Config* mutable_config();
  void set_allocated_config(::deck::Config* config);
  private:
  const ::deck::Config& _internal_config() const;
  ::deck::Config* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::deck::Config* config);
  ::deck::Config* unsafe_arena_release_config();

  // @@protoc_insertion_point(class_scope:deck.GetConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::deck::Config* config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deck_2fproto_2fdeck_5fservice_2eproto;
};
// -------------------------------------------------------------------

class PingMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:deck.PingMsg) */ {
 public:
  inline PingMsg() : PingMsg(nullptr) {}
  ~PingMsg() override;
  explicit constexpr PingMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PingMsg(const PingMsg& from);
  PingMsg(PingMsg&& from) noexcept
    : PingMsg() {
    *this = ::std::move(from);
  }

  inline PingMsg& operator=(const PingMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline PingMsg& operator=(PingMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PingMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const PingMsg* internal_default_instance() {
    return reinterpret_cast<const PingMsg*>(
               &_PingMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(PingMsg& a, PingMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(PingMsg* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PingMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PingMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PingMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PingMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PingMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PingMsg* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "deck.PingMsg";
  }
  protected:
  explicit PingMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
  };
  // uint32 x = 1;
  void clear_x();
  uint32_t x() const;
  void set_x(uint32_t value);
  private:
  uint32_t _internal_x() const;
  void _internal_set_x(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:deck.PingMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t x_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deck_2fproto_2fdeck_5fservice_2eproto;
};
// -------------------------------------------------------------------

class PongMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:deck.PongMsg) */ {
 public:
  inline PongMsg() : PongMsg(nullptr) {}
  ~PongMsg() override;
  explicit constexpr PongMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PongMsg(const PongMsg& from);
  PongMsg(PongMsg&& from) noexcept
    : PongMsg() {
    *this = ::std::move(from);
  }

  inline PongMsg& operator=(const PongMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline PongMsg& operator=(PongMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PongMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const PongMsg* internal_default_instance() {
    return reinterpret_cast<const PongMsg*>(
               &_PongMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(PongMsg& a, PongMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(PongMsg* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PongMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PongMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PongMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PongMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PongMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PongMsg* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "deck.PongMsg";
  }
  protected:
  explicit PongMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
  };
  // uint32 x = 1;
  void clear_x();
  uint32_t x() const;
  void set_x(uint32_t value);
  private:
  uint32_t _internal_x() const;
  void _internal_set_x(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:deck.PongMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t x_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deck_2fproto_2fdeck_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Config

// string key = 1;
inline void Config::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& Config::key() const {
  // @@protoc_insertion_point(field_get:deck.Config.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Config::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:deck.Config.key)
}
inline std::string* Config::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:deck.Config.key)
  return _s;
}
inline const std::string& Config::_internal_key() const {
  return key_.Get();
}
inline void Config::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Config::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Config::release_key() {
  // @@protoc_insertion_point(field_release:deck.Config.key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Config::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:deck.Config.key)
}

// bool bool_val = 2;
inline bool Config::_internal_has_bool_val() const {
  return value_case() == kBoolVal;
}
inline bool Config::has_bool_val() const {
  return _internal_has_bool_val();
}
inline void Config::set_has_bool_val() {
  _oneof_case_[0] = kBoolVal;
}
inline void Config::clear_bool_val() {
  if (_internal_has_bool_val()) {
    value_.bool_val_ = false;
    clear_has_value();
  }
}
inline bool Config::_internal_bool_val() const {
  if (_internal_has_bool_val()) {
    return value_.bool_val_;
  }
  return false;
}
inline void Config::_internal_set_bool_val(bool value) {
  if (!_internal_has_bool_val()) {
    clear_value();
    set_has_bool_val();
  }
  value_.bool_val_ = value;
}
inline bool Config::bool_val() const {
  // @@protoc_insertion_point(field_get:deck.Config.bool_val)
  return _internal_bool_val();
}
inline void Config::set_bool_val(bool value) {
  _internal_set_bool_val(value);
  // @@protoc_insertion_point(field_set:deck.Config.bool_val)
}

// int64 int_val = 3;
inline bool Config::_internal_has_int_val() const {
  return value_case() == kIntVal;
}
inline bool Config::has_int_val() const {
  return _internal_has_int_val();
}
inline void Config::set_has_int_val() {
  _oneof_case_[0] = kIntVal;
}
inline void Config::clear_int_val() {
  if (_internal_has_int_val()) {
    value_.int_val_ = int64_t{0};
    clear_has_value();
  }
}
inline int64_t Config::_internal_int_val() const {
  if (_internal_has_int_val()) {
    return value_.int_val_;
  }
  return int64_t{0};
}
inline void Config::_internal_set_int_val(int64_t value) {
  if (!_internal_has_int_val()) {
    clear_value();
    set_has_int_val();
  }
  value_.int_val_ = value;
}
inline int64_t Config::int_val() const {
  // @@protoc_insertion_point(field_get:deck.Config.int_val)
  return _internal_int_val();
}
inline void Config::set_int_val(int64_t value) {
  _internal_set_int_val(value);
  // @@protoc_insertion_point(field_set:deck.Config.int_val)
}

// double float_val = 4;
inline bool Config::_internal_has_float_val() const {
  return value_case() == kFloatVal;
}
inline bool Config::has_float_val() const {
  return _internal_has_float_val();
}
inline void Config::set_has_float_val() {
  _oneof_case_[0] = kFloatVal;
}
inline void Config::clear_float_val() {
  if (_internal_has_float_val()) {
    value_.float_val_ = 0;
    clear_has_value();
  }
}
inline double Config::_internal_float_val() const {
  if (_internal_has_float_val()) {
    return value_.float_val_;
  }
  return 0;
}
inline void Config::_internal_set_float_val(double value) {
  if (!_internal_has_float_val()) {
    clear_value();
    set_has_float_val();
  }
  value_.float_val_ = value;
}
inline double Config::float_val() const {
  // @@protoc_insertion_point(field_get:deck.Config.float_val)
  return _internal_float_val();
}
inline void Config::set_float_val(double value) {
  _internal_set_float_val(value);
  // @@protoc_insertion_point(field_set:deck.Config.float_val)
}

// string str_val = 5;
inline bool Config::_internal_has_str_val() const {
  return value_case() == kStrVal;
}
inline bool Config::has_str_val() const {
  return _internal_has_str_val();
}
inline void Config::set_has_str_val() {
  _oneof_case_[0] = kStrVal;
}
inline void Config::clear_str_val() {
  if (_internal_has_str_val()) {
    value_.str_val_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
    clear_has_value();
  }
}
inline const std::string& Config::str_val() const {
  // @@protoc_insertion_point(field_get:deck.Config.str_val)
  return _internal_str_val();
}
template <typename ArgT0, typename... ArgT>
inline void Config::set_str_val(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_str_val()) {
    clear_value();
    set_has_str_val();
    value_.str_val_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_val_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:deck.Config.str_val)
}
inline std::string* Config::mutable_str_val() {
  std::string* _s = _internal_mutable_str_val();
  // @@protoc_insertion_point(field_mutable:deck.Config.str_val)
  return _s;
}
inline const std::string& Config::_internal_str_val() const {
  if (_internal_has_str_val()) {
    return value_.str_val_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void Config::_internal_set_str_val(const std::string& value) {
  if (!_internal_has_str_val()) {
    clear_value();
    set_has_str_val();
    value_.str_val_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_val_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Config::_internal_mutable_str_val() {
  if (!_internal_has_str_val()) {
    clear_value();
    set_has_str_val();
    value_.str_val_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.str_val_.Mutable(
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Config::release_str_val() {
  // @@protoc_insertion_point(field_release:deck.Config.str_val)
  if (_internal_has_str_val()) {
    clear_has_value();
    return value_.str_val_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
  } else {
    return nullptr;
  }
}
inline void Config::set_allocated_str_val(std::string* str_val) {
  if (has_value()) {
    clear_value();
  }
  if (str_val != nullptr) {
    set_has_str_val();
    value_.str_val_.UnsafeSetDefault(str_val);
    ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaForAllocation();
    if (arena != nullptr) {
      arena->Own(str_val);
    }
  }
  // @@protoc_insertion_point(field_set_allocated:deck.Config.str_val)
}

inline bool Config::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void Config::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline Config::ValueCase Config::value_case() const {
  return Config::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// SetConfigsRequest

// string camera = 1;
inline void SetConfigsRequest::clear_camera() {
  camera_.ClearToEmpty();
}
inline const std::string& SetConfigsRequest::camera() const {
  // @@protoc_insertion_point(field_get:deck.SetConfigsRequest.camera)
  return _internal_camera();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetConfigsRequest::set_camera(ArgT0&& arg0, ArgT... args) {
 
 camera_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:deck.SetConfigsRequest.camera)
}
inline std::string* SetConfigsRequest::mutable_camera() {
  std::string* _s = _internal_mutable_camera();
  // @@protoc_insertion_point(field_mutable:deck.SetConfigsRequest.camera)
  return _s;
}
inline const std::string& SetConfigsRequest::_internal_camera() const {
  return camera_.Get();
}
inline void SetConfigsRequest::_internal_set_camera(const std::string& value) {
  
  camera_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetConfigsRequest::_internal_mutable_camera() {
  
  return camera_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetConfigsRequest::release_camera() {
  // @@protoc_insertion_point(field_release:deck.SetConfigsRequest.camera)
  return camera_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetConfigsRequest::set_allocated_camera(std::string* camera) {
  if (camera != nullptr) {
    
  } else {
    
  }
  camera_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), camera,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (camera_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    camera_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:deck.SetConfigsRequest.camera)
}

// repeated .deck.Config configs = 2;
inline int SetConfigsRequest::_internal_configs_size() const {
  return configs_.size();
}
inline int SetConfigsRequest::configs_size() const {
  return _internal_configs_size();
}
inline void SetConfigsRequest::clear_configs() {
  configs_.Clear();
}
inline ::deck::Config* SetConfigsRequest::mutable_configs(int index) {
  // @@protoc_insertion_point(field_mutable:deck.SetConfigsRequest.configs)
  return configs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::deck::Config >*
SetConfigsRequest::mutable_configs() {
  // @@protoc_insertion_point(field_mutable_list:deck.SetConfigsRequest.configs)
  return &configs_;
}
inline const ::deck::Config& SetConfigsRequest::_internal_configs(int index) const {
  return configs_.Get(index);
}
inline const ::deck::Config& SetConfigsRequest::configs(int index) const {
  // @@protoc_insertion_point(field_get:deck.SetConfigsRequest.configs)
  return _internal_configs(index);
}
inline ::deck::Config* SetConfigsRequest::_internal_add_configs() {
  return configs_.Add();
}
inline ::deck::Config* SetConfigsRequest::add_configs() {
  ::deck::Config* _add = _internal_add_configs();
  // @@protoc_insertion_point(field_add:deck.SetConfigsRequest.configs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::deck::Config >&
SetConfigsRequest::configs() const {
  // @@protoc_insertion_point(field_list:deck.SetConfigsRequest.configs)
  return configs_;
}

// -------------------------------------------------------------------

// SetConfigsResponse

// -------------------------------------------------------------------

// GetConfigRequest

// string camera = 1;
inline void GetConfigRequest::clear_camera() {
  camera_.ClearToEmpty();
}
inline const std::string& GetConfigRequest::camera() const {
  // @@protoc_insertion_point(field_get:deck.GetConfigRequest.camera)
  return _internal_camera();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetConfigRequest::set_camera(ArgT0&& arg0, ArgT... args) {
 
 camera_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:deck.GetConfigRequest.camera)
}
inline std::string* GetConfigRequest::mutable_camera() {
  std::string* _s = _internal_mutable_camera();
  // @@protoc_insertion_point(field_mutable:deck.GetConfigRequest.camera)
  return _s;
}
inline const std::string& GetConfigRequest::_internal_camera() const {
  return camera_.Get();
}
inline void GetConfigRequest::_internal_set_camera(const std::string& value) {
  
  camera_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetConfigRequest::_internal_mutable_camera() {
  
  return camera_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetConfigRequest::release_camera() {
  // @@protoc_insertion_point(field_release:deck.GetConfigRequest.camera)
  return camera_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetConfigRequest::set_allocated_camera(std::string* camera) {
  if (camera != nullptr) {
    
  } else {
    
  }
  camera_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), camera,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (camera_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    camera_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:deck.GetConfigRequest.camera)
}

// string key = 2;
inline void GetConfigRequest::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& GetConfigRequest::key() const {
  // @@protoc_insertion_point(field_get:deck.GetConfigRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetConfigRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:deck.GetConfigRequest.key)
}
inline std::string* GetConfigRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:deck.GetConfigRequest.key)
  return _s;
}
inline const std::string& GetConfigRequest::_internal_key() const {
  return key_.Get();
}
inline void GetConfigRequest::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetConfigRequest::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetConfigRequest::release_key() {
  // @@protoc_insertion_point(field_release:deck.GetConfigRequest.key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetConfigRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:deck.GetConfigRequest.key)
}

// string type = 3;
inline void GetConfigRequest::clear_type() {
  type_.ClearToEmpty();
}
inline const std::string& GetConfigRequest::type() const {
  // @@protoc_insertion_point(field_get:deck.GetConfigRequest.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetConfigRequest::set_type(ArgT0&& arg0, ArgT... args) {
 
 type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:deck.GetConfigRequest.type)
}
inline std::string* GetConfigRequest::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:deck.GetConfigRequest.type)
  return _s;
}
inline const std::string& GetConfigRequest::_internal_type() const {
  return type_.Get();
}
inline void GetConfigRequest::_internal_set_type(const std::string& value) {
  
  type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetConfigRequest::_internal_mutable_type() {
  
  return type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetConfigRequest::release_type() {
  // @@protoc_insertion_point(field_release:deck.GetConfigRequest.type)
  return type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetConfigRequest::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), type,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:deck.GetConfigRequest.type)
}

// -------------------------------------------------------------------

// GetConfigResponse

// .deck.Config config = 1;
inline bool GetConfigResponse::_internal_has_config() const {
  return this != internal_default_instance() && config_ != nullptr;
}
inline bool GetConfigResponse::has_config() const {
  return _internal_has_config();
}
inline void GetConfigResponse::clear_config() {
  if (GetArenaForAllocation() == nullptr && config_ != nullptr) {
    delete config_;
  }
  config_ = nullptr;
}
inline const ::deck::Config& GetConfigResponse::_internal_config() const {
  const ::deck::Config* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::deck::Config&>(
      ::deck::_Config_default_instance_);
}
inline const ::deck::Config& GetConfigResponse::config() const {
  // @@protoc_insertion_point(field_get:deck.GetConfigResponse.config)
  return _internal_config();
}
inline void GetConfigResponse::unsafe_arena_set_allocated_config(
    ::deck::Config* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:deck.GetConfigResponse.config)
}
inline ::deck::Config* GetConfigResponse::release_config() {
  
  ::deck::Config* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::deck::Config* GetConfigResponse::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:deck.GetConfigResponse.config)
  
  ::deck::Config* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::deck::Config* GetConfigResponse::_internal_mutable_config() {
  
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::deck::Config>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::deck::Config* GetConfigResponse::mutable_config() {
  ::deck::Config* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:deck.GetConfigResponse.config)
  return _msg;
}
inline void GetConfigResponse::set_allocated_config(::deck::Config* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete config_;
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::deck::Config>::GetOwningArena(config);
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    
  } else {
    
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:deck.GetConfigResponse.config)
}

// -------------------------------------------------------------------

// PingMsg

// uint32 x = 1;
inline void PingMsg::clear_x() {
  x_ = 0u;
}
inline uint32_t PingMsg::_internal_x() const {
  return x_;
}
inline uint32_t PingMsg::x() const {
  // @@protoc_insertion_point(field_get:deck.PingMsg.x)
  return _internal_x();
}
inline void PingMsg::_internal_set_x(uint32_t value) {
  
  x_ = value;
}
inline void PingMsg::set_x(uint32_t value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:deck.PingMsg.x)
}

// -------------------------------------------------------------------

// PongMsg

// uint32 x = 1;
inline void PongMsg::clear_x() {
  x_ = 0u;
}
inline uint32_t PongMsg::_internal_x() const {
  return x_;
}
inline uint32_t PongMsg::x() const {
  // @@protoc_insertion_point(field_get:deck.PongMsg.x)
  return _internal_x();
}
inline void PongMsg::_internal_set_x(uint32_t value) {
  
  x_ = value;
}
inline void PongMsg::set_x(uint32_t value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:deck.PongMsg.x)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace deck

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_deck_2fproto_2fdeck_5fservice_2eproto

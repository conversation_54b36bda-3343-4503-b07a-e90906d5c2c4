# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.deck.proto import deck_service_pb2 as deck_dot_proto_dot_deck__service__pb2


class DeckServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/deck.DeckService/Ping',
                request_serializer=deck_dot_proto_dot_deck__service__pb2.PingMsg.SerializeToString,
                response_deserializer=deck_dot_proto_dot_deck__service__pb2.PongMsg.FromString,
                )
        self.SetConfigs = channel.unary_unary(
                '/deck.DeckService/SetConfigs',
                request_serializer=deck_dot_proto_dot_deck__service__pb2.SetConfigsRequest.SerializeToString,
                response_deserializer=deck_dot_proto_dot_deck__service__pb2.SetConfigsResponse.FromString,
                )
        self.GetConfig = channel.unary_unary(
                '/deck.DeckService/GetConfig',
                request_serializer=deck_dot_proto_dot_deck__service__pb2.GetConfigRequest.SerializeToString,
                response_deserializer=deck_dot_proto_dot_deck__service__pb2.GetConfigResponse.FromString,
                )


class DeckServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetConfigs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DeckServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=deck_dot_proto_dot_deck__service__pb2.PingMsg.FromString,
                    response_serializer=deck_dot_proto_dot_deck__service__pb2.PongMsg.SerializeToString,
            ),
            'SetConfigs': grpc.unary_unary_rpc_method_handler(
                    servicer.SetConfigs,
                    request_deserializer=deck_dot_proto_dot_deck__service__pb2.SetConfigsRequest.FromString,
                    response_serializer=deck_dot_proto_dot_deck__service__pb2.SetConfigsResponse.SerializeToString,
            ),
            'GetConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConfig,
                    request_deserializer=deck_dot_proto_dot_deck__service__pb2.GetConfigRequest.FromString,
                    response_serializer=deck_dot_proto_dot_deck__service__pb2.GetConfigResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'deck.DeckService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DeckService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deck.DeckService/Ping',
            deck_dot_proto_dot_deck__service__pb2.PingMsg.SerializeToString,
            deck_dot_proto_dot_deck__service__pb2.PongMsg.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetConfigs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deck.DeckService/SetConfigs',
            deck_dot_proto_dot_deck__service__pb2.SetConfigsRequest.SerializeToString,
            deck_dot_proto_dot_deck__service__pb2.SetConfigsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deck.DeckService/GetConfig',
            deck_dot_proto_dot_deck__service__pb2.GetConfigRequest.SerializeToString,
            deck_dot_proto_dot_deck__service__pb2.GetConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

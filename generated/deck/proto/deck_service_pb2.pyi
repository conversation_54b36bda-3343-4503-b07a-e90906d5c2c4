"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Config(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    key: typing___Text = ...
    bool_val: builtin___bool = ...
    int_val: builtin___int = ...
    float_val: builtin___float = ...
    str_val: typing___Text = ...

    def __init__(self,
        *,
        key : typing___Optional[typing___Text] = None,
        bool_val : typing___Optional[builtin___bool] = None,
        int_val : typing___Optional[builtin___int] = None,
        float_val : typing___Optional[builtin___float] = None,
        str_val : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bool_val",b"bool_val",u"float_val",b"float_val",u"int_val",b"int_val",u"str_val",b"str_val",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bool_val",b"bool_val",u"float_val",b"float_val",u"int_val",b"int_val",u"key",b"key",u"str_val",b"str_val",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["bool_val","int_val","float_val","str_val"]: ...
type___Config = Config

class SetConfigsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    camera: typing___Text = ...

    @property
    def configs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Config]: ...

    def __init__(self,
        *,
        camera : typing___Optional[typing___Text] = None,
        configs : typing___Optional[typing___Iterable[type___Config]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"camera",b"camera",u"configs",b"configs"]) -> None: ...
type___SetConfigsRequest = SetConfigsRequest

class SetConfigsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetConfigsResponse = SetConfigsResponse

class GetConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    camera: typing___Text = ...
    key: typing___Text = ...
    type: typing___Text = ...

    def __init__(self,
        *,
        camera : typing___Optional[typing___Text] = None,
        key : typing___Optional[typing___Text] = None,
        type : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"camera",b"camera",u"key",b"key",u"type",b"type"]) -> None: ...
type___GetConfigRequest = GetConfigRequest

class GetConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Config: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___GetConfigResponse = GetConfigResponse

class PingMsg(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PingMsg = PingMsg

class PongMsg(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PongMsg = PongMsg

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.weed_tracking.proto import weed_tracking_pb2 as weed__tracking_dot_proto_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto',
  package='aimbot',
  syntax='proto3',
  serialized_options=b'Z\014proto/aimbot',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\nHcore/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto\x12\x06\x61imbot\x1a\'weed_tracking/proto/weed_tracking.proto\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\r\"\x16\n\tPongReply\x12\t\n\x01x\x18\x01 \x01(\r\"\x07\n\x05\x45mpty\"C\n\x0eTargetingState\x12\x17\n\x0fweeding_enabled\x18\x01 \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x02 \x01(\x08\"\xdd\x01\n\x0b\x41imbotState\x12\x11\n\talgorithm\x18\x01 \x01(\t\x12\x0f\n\x07running\x18\x02 \x01(\x08\x12\r\n\x05\x61rmed\x18\x03 \x01(\x08\x12/\n\x0ftargeting_state\x18\x04 \x01(\x0b\x32\x16.aimbot.TargetingState\x12\r\n\x05ready\x18\x05 \x01(\x08\x12:\n\x15safety_override_state\x18\x06 \x01(\x0e\x32\x1b.aimbot.SafetyOverrideState\x12\x1f\n\x17\x61\x63tuation_tasks_running\x18\x07 \x01(\x08\"\x17\n\x15TargetVelocityRequest\"A\n\x13TargetVelocityReply\x12\x14\n\x0cvelocity_min\x18\x01 \x01(\x02\x12\x14\n\x0cvelocity_max\x18\x02 \x01(\x02\"A\n\x16LaserTestActuationTask\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x13\n\x0b\x64uration_ms\x18\x02 \x01(\r\"@\n\x16ImageDrawActuationTask\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x12\n\nspeed_mmps\x18\x02 \x01(\x02\"@\n\x16RangeDrawActuationTask\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x12\n\nduration_s\x18\x02 \x01(\x02\"\xc0\x01\n\x14\x41\x63tuationTaskRequest\x12\x34\n\nlaser_test\x18\x01 \x01(\x0b\x32\x1e.aimbot.LaserTestActuationTaskH\x00\x12\x34\n\nimage_draw\x18\x02 \x01(\x0b\x32\x1e.aimbot.ImageDrawActuationTaskH\x00\x12\x34\n\nrange_draw\x18\x03 \x01(\x0b\x32\x1e.aimbot.RangeDrawActuationTaskH\x00\x42\x06\n\x04task\"3\n\x0eLensSetRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r\"\x0e\n\x0cLensSetReply\"$\n\x0eLensGetRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"\x96\x01\n\x0cLensGetReply\x12\r\n\x05value\x18\x01 \x01(\r\x12\x11\n\tmin_value\x18\x02 \x01(\r\x12\x11\n\tmax_value\x18\x03 \x01(\r\x12 \n\x18manual_autofocus_percent\x18\x04 \x01(\x02\x12\x1b\n\x13manual_autofocusing\x18\x05 \x01(\x08\x12\x12\n\nscanner_id\x18\x06 \x01(\r\"<\n\x0fLensGetAllReply\x12)\n\x0blens_status\x18\x01 \x03(\x0b\x32\x14.aimbot.LensGetReply\"*\n\x14LensAutoFocusRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"\x14\n\x12LensAutoFocusReply\".\n\x18StopLensAutoFocusRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"4\n\x0fLaserArmRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\r\n\x05\x61rmed\x18\x02 \x01(\x08\"\x0f\n\rLaserArmReply\"9\n\x12LaserEnableRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"3\n\x10LaserEnableReply\x12\x0e\n\x06status\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"4\n\x10LaserFireRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x0c\n\x04\x66ire\x18\x02 \x01(\x08\"\x10\n\x0eLaserFireReply\"1\n\x0fLaserSetRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\n\n\x02on\x18\x02 \x01(\x08\"\x0f\n\rLaserSetReply\"i\n\x1a\x42urnIdividualImagesRequest\x12\x12\n\nscanner_id\x18\x01 \x03(\r\x12\x12\n\nspeed_mmps\x18\x02 \x01(\x02\x12\x11\n\tintensity\x18\x03 \x01(\x02\x12\x10\n\x08json_img\x18\x04 \x01(\t\"\xc7\x01\n\nLaserState\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x0e\n\x06\x66iring\x18\x02 \x01(\x08\x12\r\n\x05\x65rror\x18\x03 \x01(\x08\x12\x16\n\nerror_code\x18\x04 \x01(\tB\x02\x18\x01\x12\x19\n\rerror_message\x18\x05 \x01(\tB\x02\x18\x01\x12\r\n\x05power\x18\x06 \x01(\x08\x12\x12\n\ndelta_temp\x18\x07 \x01(\x02\x12\x0f\n\x07\x63urrent\x18\x08 \x01(\x02\x12\r\n\x05\x61rced\x18\t \x01(\x08\x12\x13\n\x0bpower_level\x18\n \x01(\x02\"\xb7\x01\n\x10ServoGoToRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x36\n\nservo_type\x18\x02 \x01(\x0e\x32\".aimbot.ServoGoToRequest.ServoType\x12\x10\n\x08position\x18\x03 \x01(\x05\x12\x0f\n\x07time_ms\x18\x04 \x01(\r\x12\x14\n\x0c\x61wait_settle\x18\x05 \x01(\x08\"\x1e\n\tServoType\x12\x07\n\x03PAN\x10\x00\x12\x08\n\x04TILT\x10\x01\"\x10\n\x0eServoGoToReply\"+\n\x15ServoGetPosVelRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"o\n\x13ServoGetPosVelReply\x12\x14\n\x0cpan_position\x18\x01 \x01(\x05\x12\x15\n\rtilt_position\x18\x02 \x01(\x05\x12\x14\n\x0cpan_velocity\x18\x03 \x01(\r\x12\x15\n\rtilt_velocity\x18\x04 \x01(\r\"+\n\x15ServoGetLimitsRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\"[\n\x13ServoGetLimitsReply\x12\x0f\n\x07pan_min\x18\x01 \x01(\x05\x12\x0f\n\x07pan_max\x18\x02 \x01(\x05\x12\x10\n\x08tilt_min\x18\x03 \x01(\x05\x12\x10\n\x08tilt_max\x18\x04 \x01(\x05\"\x80\x01\n\x0bTuningParam\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x06v_uint\x18\x02 \x01(\rH\x00\x12\x0f\n\x05v_int\x18\x03 \x01(\x05H\x00\x12\x10\n\x06v_bool\x18\x04 \x01(\x08H\x00\x12\x11\n\x07v_float\x18\x05 \x01(\x02H\x00\x12\x12\n\x08v_string\x18\x06 \x01(\tH\x00\x42\x07\n\x05value\"@\n\x19TuningParamsUpdateRequest\x12#\n\x06params\x18\x01 \x03(\x0b\x32\x13.aimbot.TuningParam\"\x19\n\x17TuningParamsUpdateReply\"&\n\x16TuningParamsGetRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\";\n\x14TuningParamsGetReply\x12#\n\x06params\x18\x01 \x03(\x0b\x32\x13.aimbot.TuningParam\"\x18\n\x16GetLoadEstimateRequest\"A\n\x14GetLoadEstimateReply\x12\x14\n\x0c\x63urrent_load\x18\x01 \x01(\x02\x12\x13\n\x0btarget_load\x18\x02 \x01(\x02\"\x16\n\x14GetDiagnosticRequest\"(\n\x12GetDiagnosticReply\x12\x12\n\ndiagnostic\x18\x01 \x01(\t\"(\n\x13ResetDevicesRequest\x12\x11\n\tdevice_id\x18\x01 \x01(\t\"\x13\n\x11ResetDevicesReply\"?\n\x13ResetScannerRequest\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12\x14\n\x0cmetrics_only\x18\x02 \x01(\x08\"\x13\n\x11ResetScannerReply\"\x1f\n\x11ScannerDescriptor\x12\n\n\x02id\x18\x01 \x01(\r\"d\n\x15ScannerTargetPosition\x12\x35\n\x12scanner_descriptor\x18\x01 \x01(\x0b\x32\x19.aimbot.ScannerDescriptor\x12\t\n\x01x\x18\x02 \x01(\r\x12\t\n\x01y\x18\x03 \x01(\r\"W\n\x0e\x43rosshairState\x12\t\n\x01x\x18\x01 \x01(\r\x12\t\n\x01y\x18\x02 \x01(\r\x12\x13\n\x0b\x63\x61librating\x18\x03 \x01(\x08\x12\x1a\n\x12\x63\x61libration_failed\x18\x04 \x01(\x08\"\xaa\x02\n\x0cScannerState\x12\x35\n\x12scanner_descriptor\x18\x01 \x01(\x0b\x32\x19.aimbot.ScannerDescriptor\x12\'\n\x0blaser_state\x18\x02 \x01(\x0b\x32\x12.aimbot.LaserState\x12/\n\x0f\x63rosshair_state\x18\x03 \x01(\x0b\x32\x16.aimbot.CrosshairState\x12\x15\n\rscanner_error\x18\x04 \x01(\x08\x12\x12\n\nerror_code\x18\x05 \x01(\t\x12\x15\n\rerror_message\x18\x06 \x01(\t\x12\x1c\n\x14target_trajectory_id\x18\x07 \x01(\r\x12\x13\n\x0bpan_failure\x18\x08 \x01(\x08\x12\x14\n\x0ctilt_failure\x18\t \x01(\x08\"E\n\x1c\x41utoXHairCalibrationProgress\x12\x13\n\x0bin_progress\x18\x01 \x01(\x08\x12\x10\n\x08progress\x18\x02 \x01(\x02\"y\n\x12ScannerStatusReply\x12$\n\x06states\x18\x01 \x03(\x0b\x32\x14.aimbot.ScannerState\x12=\n\x0fx_hair_progress\x18\x02 \x01(\x0b\x32$.aimbot.AutoXHairCalibrationProgress\"\x1d\n\x0b\x42ootedReply\x12\x0e\n\x06\x62ooted\x18\x01 \x01(\x08\"a\n\x0cTrackerState\x12\n\n\x02id\x18\x01 \x01(\r\x12\x15\n\rat_weed_limit\x18\x02 \x01(\x08\x12\x16\n\x0erotary_timeout\x18\x03 \x01(\x08\x12\x16\n\x0e\x64\x65\x65pweed_error\x18\x04 \x01(\x08\"\'\n\x0eSchedulerState\x12\x15\n\rover_capacity\x18\x01 \x01(\x08\"f\n\rTrackingState\x12$\n\x06states\x18\x01 \x03(\x0b\x32\x14.aimbot.TrackerState\x12/\n\x0fscheduler_state\x18\x02 \x01(\x0b\x32\x16.aimbot.SchedulerState\"`\n\x13\x42\x65\x64topHeightProfile\x12\x1b\n\x13weed_height_columns\x18\x01 \x03(\x01\x12\x1b\n\x13\x63rop_height_columns\x18\x02 \x03(\x01\x12\x0f\n\x07pcam_id\x18\x03 \x01(\t\"b\n\x1aTrackerBedtopHeightProfile\x12-\n\x08profiles\x18\x01 \x03(\x0b\x32\x1b.aimbot.BedtopHeightProfile\x12\x15\n\rbbh_offset_mm\x18\x02 \x01(\x02\"t\n\x15GetDimensionsResponse\x12\x10\n\x08min_x_mm\x18\x01 \x01(\x01\x12\x10\n\x08max_x_mm\x18\x02 \x01(\x01\x12\x10\n\x08min_y_mm\x18\x03 \x01(\x01\x12\x10\n\x08max_y_mm\x18\x04 \x01(\x01\x12\x13\n\x0b\x63\x65nter_x_mm\x18\x05 \x01(\x01\"*\n\x15GetTargetCamSNRequest\x12\x11\n\tcamera_id\x18\x01 \x01(\t\"/\n\x16GetTargetCamSNResponse\x12\x15\n\rserial_number\x18\x01 \x01(\t\"\x1b\n\x19ReloadThinningConfRequest\"\x1a\n\x18ReloadAlmanacConfRequest\" \n\x1eReloadDiscriminatorConfRequest\"\x1e\n\x1cReloadModelinatorConfRequest\"\x19\n\x17ReloadTVEProfileRequest\"z\n\x16\x46ixLaserMetricsRequest\x12*\n\x07scanner\x18\x01 \x01(\x0b\x32\x19.aimbot.ScannerDescriptor\x12\x18\n\x10total_fire_count\x18\x02 \x01(\x03\x12\x1a\n\x12total_fire_time_ms\x18\x03 \x01(\x03\"%\n\x13TrackedItemsRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"W\n\x12TrackedItemHistory\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12+\n\tdetection\x18\x02 \x01(\x0b\x32\x18.weed_tracking.Detection\"F\n\x0bTrackedItem\x12\n\n\x02id\x18\x01 \x01(\x03\x12+\n\x07history\x18\x02 \x03(\x0b\x32\x1a.aimbot.TrackedItemHistory\"B\n\x14TrackedItemsResponse\x12*\n\rtracked_items\x18\x01 \x03(\x0b\x32\x13.aimbot.TrackedItem\"2\n\x15ParticipationResponse\x12\x19\n\x11running_as_leader\x18\x01 \x01(\x08*M\n\x13SafetyOverrideState\x12\x16\n\x12SafetyOverrideNone\x10\x00\x12\x1e\n\x1aSafetyOverrideVelocityStop\x10\x01\x32\xe2\x19\n\rAimbotService\x12\x30\n\x04Ping\x12\x13.aimbot.PingRequest\x1a\x11.aimbot.PongReply\"\x00\x12\x31\n\tGetBooted\x12\r.aimbot.Empty\x1a\x13.aimbot.BootedReply\"\x00\x12+\n\tArmLasers\x12\r.aimbot.Empty\x1a\r.aimbot.Empty\"\x00\x12.\n\x0c\x44isarmLasers\x12\r.aimbot.Empty\x1a\r.aimbot.Empty\"\x00\x12\x36\n\x0eGetAimbotState\x12\r.aimbot.Empty\x1a\x13.aimbot.AimbotState\"\x00\x12<\n\x11SetTargetingState\x12\x16.aimbot.TargetingState\x1a\r.aimbot.Empty\"\x00\x12\x43\n\x12StartActuationTask\x12\x1c.aimbot.ActuationTaskRequest\x1a\r.aimbot.Empty\"\x00\x12\x35\n\x13\x43\x61ncelActuationTask\x12\r.aimbot.Empty\x1a\r.aimbot.Empty\"\x00\x12\x39\n\x07LensSet\x12\x16.aimbot.LensSetRequest\x1a\x14.aimbot.LensSetReply\"\x00\x12\x39\n\x07LensGet\x12\x16.aimbot.LensGetRequest\x1a\x14.aimbot.LensGetReply\"\x00\x12\x36\n\nLensGetAll\x12\r.aimbot.Empty\x1a\x17.aimbot.LensGetAllReply\"\x00\x12K\n\rLensAutoFocus\x12\x1c.aimbot.LensAutoFocusRequest\x1a\x1a.aimbot.LensAutoFocusReply\"\x00\x12\x46\n\x11StopLensAutoFocus\x12 .aimbot.StopLensAutoFocusRequest\x1a\r.aimbot.Empty\"\x00\x12<\n\x08LaserArm\x12\x17.aimbot.LaserArmRequest\x1a\x15.aimbot.LaserArmReply\"\x00\x12<\n\x08LaserSet\x12\x17.aimbot.LaserSetRequest\x1a\x15.aimbot.LaserSetReply\"\x00\x12\x45\n\x0bLaserEnable\x12\x1a.aimbot.LaserEnableRequest\x1a\x18.aimbot.LaserEnableReply\"\x00\x12?\n\tLaserFire\x12\x18.aimbot.LaserFireRequest\x1a\x16.aimbot.LaserFireReply\"\x00\x12?\n\x11ResetLaserMetrics\x12\x19.aimbot.ScannerDescriptor\x1a\r.aimbot.Empty\"\x00\x12\x42\n\x0f\x46ixLaserMetrics\x12\x1e.aimbot.FixLaserMetricsRequest\x1a\r.aimbot.Empty\"\x00\x12I\n\x12\x42urnIdividualImage\x12\".aimbot.BurnIdividualImagesRequest\x1a\r.aimbot.Empty\"\x00\x12?\n\tServoGoTo\x12\x18.aimbot.ServoGoToRequest\x1a\x16.aimbot.ServoGoToReply\"\x00\x12N\n\x0eServoGetPosVel\x12\x1d.aimbot.ServoGetPosVelRequest\x1a\x1b.aimbot.ServoGetPosVelReply\"\x00\x12N\n\x0eServoGetLimits\x12\x1d.aimbot.ServoGetLimitsRequest\x1a\x1b.aimbot.ServoGetLimitsReply\"\x00\x12Z\n\x12TuningParamsUpdate\x12!.aimbot.TuningParamsUpdateRequest\x1a\x1f.aimbot.TuningParamsUpdateReply\"\x00\x12Q\n\x0fTuningParamsGet\x12\x1e.aimbot.TuningParamsGetRequest\x1a\x1c.aimbot.TuningParamsGetReply\"\x00\x12Q\n\x0fGetLoadEstimate\x12\x1e.aimbot.GetLoadEstimateRequest\x1a\x1c.aimbot.GetLoadEstimateReply\"\x00\x12K\n\rGetDiagnostic\x12\x1c.aimbot.GetDiagnosticRequest\x1a\x1a.aimbot.GetDiagnosticReply\"\x00\x12H\n\x0cResetDevices\x12\x1b.aimbot.ResetDevicesRequest\x1a\x19.aimbot.ResetDevicesReply\"\x00\x12H\n\x0cResetScanner\x12\x1b.aimbot.ResetScannerRequest\x1a\x19.aimbot.ResetScannerReply\"\x00\x12G\n\x1bStartAutoCalibrateCrosshair\x12\x19.aimbot.ScannerDescriptor\x1a\r.aimbot.Empty\x12?\n\x1fStartAutoCalibrateAllCrosshairs\x12\r.aimbot.Empty\x1a\r.aimbot.Empty\x12\x31\n\x11StopAutoCalibrate\x12\r.aimbot.Empty\x1a\r.aimbot.Empty\x12\x44\n\x14SetCrosshairPosition\x12\x1d.aimbot.ScannerTargetPosition\x1a\r.aimbot.Empty\x12;\n\x0bMoveScanner\x12\x1d.aimbot.ScannerTargetPosition\x1a\r.aimbot.Empty\x12Q\n\x1aGetAutoCalibrationProgress\x12\r.aimbot.Empty\x1a$.aimbot.AutoXHairCalibrationProgress\x12=\n\x10GetScannerStatus\x12\r.aimbot.Empty\x1a\x1a.aimbot.ScannerStatusReply\x12O\n\x11GetTargetVelocity\x12\x1d.aimbot.TargetVelocityRequest\x1a\x1b.aimbot.TargetVelocityReply\x12\x38\n\x10GetTrackingState\x12\r.aimbot.Empty\x1a\x15.aimbot.TrackingState\x12K\n\x16GetBedtopHeightProfile\x12\r.aimbot.Empty\x1a\".aimbot.TrackerBedtopHeightProfile\x12=\n\rGetDimensions\x12\r.aimbot.Empty\x1a\x1d.aimbot.GetDimensionsResponse\x12O\n\x0eGetTargetCamSN\x12\x1d.aimbot.GetTargetCamSNRequest\x1a\x1e.aimbot.GetTargetCamSNResponse\x12\x46\n\x12ReloadThinningConf\x12!.aimbot.ReloadThinningConfRequest\x1a\r.aimbot.Empty\x12\x44\n\x11ReloadAlmanacConf\x12 .aimbot.ReloadAlmanacConfRequest\x1a\r.aimbot.Empty\x12P\n\x17ReloadDiscriminatorConf\x12&.aimbot.ReloadDiscriminatorConfRequest\x1a\r.aimbot.Empty\x12L\n\x15ReloadModelinatorConf\x12$.aimbot.ReloadModelinatorConfRequest\x1a\r.aimbot.Empty\x12\x42\n\x10ReloadTVEProfile\x12\x1f.aimbot.ReloadTVEProfileRequest\x1a\r.aimbot.Empty\x12T\n\x17GetDistanceTrackedItems\x12\x1b.aimbot.TrackedItemsRequest\x1a\x1c.aimbot.TrackedItemsResponse\x12@\n\x10GetParticipation\x12\r.aimbot.Empty\x1a\x1d.aimbot.ParticipationResponseB\x0eZ\x0cproto/aimbotb\x06proto3'
  ,
  dependencies=[weed__tracking_dot_proto_dot_weed__tracking__pb2.DESCRIPTOR,])

_SAFETYOVERRIDESTATE = _descriptor.EnumDescriptor(
  name='SafetyOverrideState',
  full_name='aimbot.SafetyOverrideState',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SafetyOverrideNone', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SafetyOverrideVelocityStop', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=5161,
  serialized_end=5238,
)
_sym_db.RegisterEnumDescriptor(_SAFETYOVERRIDESTATE)

SafetyOverrideState = enum_type_wrapper.EnumTypeWrapper(_SAFETYOVERRIDESTATE)
SafetyOverrideNone = 0
SafetyOverrideVelocityStop = 1


_SERVOGOTOREQUEST_SERVOTYPE = _descriptor.EnumDescriptor(
  name='ServoType',
  full_name='aimbot.ServoGoToRequest.ServoType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PAN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TILT', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2185,
  serialized_end=2215,
)
_sym_db.RegisterEnumDescriptor(_SERVOGOTOREQUEST_SERVOTYPE)


_PINGREQUEST = _descriptor.Descriptor(
  name='PingRequest',
  full_name='aimbot.PingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='aimbot.PingRequest.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=125,
  serialized_end=149,
)


_PONGREPLY = _descriptor.Descriptor(
  name='PongReply',
  full_name='aimbot.PongReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='aimbot.PongReply.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=151,
  serialized_end=173,
)


_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='aimbot.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=175,
  serialized_end=182,
)


_TARGETINGSTATE = _descriptor.Descriptor(
  name='TargetingState',
  full_name='aimbot.TargetingState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weeding_enabled', full_name='aimbot.TargetingState.weeding_enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_enabled', full_name='aimbot.TargetingState.thinning_enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=184,
  serialized_end=251,
)


_AIMBOTSTATE = _descriptor.Descriptor(
  name='AimbotState',
  full_name='aimbot.AimbotState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='algorithm', full_name='aimbot.AimbotState.algorithm', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='running', full_name='aimbot.AimbotState.running', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='armed', full_name='aimbot.AimbotState.armed', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='targeting_state', full_name='aimbot.AimbotState.targeting_state', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ready', full_name='aimbot.AimbotState.ready', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_override_state', full_name='aimbot.AimbotState.safety_override_state', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='actuation_tasks_running', full_name='aimbot.AimbotState.actuation_tasks_running', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=254,
  serialized_end=475,
)


_TARGETVELOCITYREQUEST = _descriptor.Descriptor(
  name='TargetVelocityRequest',
  full_name='aimbot.TargetVelocityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=477,
  serialized_end=500,
)


_TARGETVELOCITYREPLY = _descriptor.Descriptor(
  name='TargetVelocityReply',
  full_name='aimbot.TargetVelocityReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='velocity_min', full_name='aimbot.TargetVelocityReply.velocity_min', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity_max', full_name='aimbot.TargetVelocityReply.velocity_max', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=502,
  serialized_end=567,
)


_LASERTESTACTUATIONTASK = _descriptor.Descriptor(
  name='LaserTestActuationTask',
  full_name='aimbot.LaserTestActuationTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.LaserTestActuationTask.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_ms', full_name='aimbot.LaserTestActuationTask.duration_ms', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=569,
  serialized_end=634,
)


_IMAGEDRAWACTUATIONTASK = _descriptor.Descriptor(
  name='ImageDrawActuationTask',
  full_name='aimbot.ImageDrawActuationTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.ImageDrawActuationTask.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_mmps', full_name='aimbot.ImageDrawActuationTask.speed_mmps', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=636,
  serialized_end=700,
)


_RANGEDRAWACTUATIONTASK = _descriptor.Descriptor(
  name='RangeDrawActuationTask',
  full_name='aimbot.RangeDrawActuationTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.RangeDrawActuationTask.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_s', full_name='aimbot.RangeDrawActuationTask.duration_s', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=702,
  serialized_end=766,
)


_ACTUATIONTASKREQUEST = _descriptor.Descriptor(
  name='ActuationTaskRequest',
  full_name='aimbot.ActuationTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='laser_test', full_name='aimbot.ActuationTaskRequest.laser_test', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='image_draw', full_name='aimbot.ActuationTaskRequest.image_draw', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='range_draw', full_name='aimbot.ActuationTaskRequest.range_draw', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='task', full_name='aimbot.ActuationTaskRequest.task',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=769,
  serialized_end=961,
)


_LENSSETREQUEST = _descriptor.Descriptor(
  name='LensSetRequest',
  full_name='aimbot.LensSetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.LensSetRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='aimbot.LensSetRequest.value', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=963,
  serialized_end=1014,
)


_LENSSETREPLY = _descriptor.Descriptor(
  name='LensSetReply',
  full_name='aimbot.LensSetReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1016,
  serialized_end=1030,
)


_LENSGETREQUEST = _descriptor.Descriptor(
  name='LensGetRequest',
  full_name='aimbot.LensGetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.LensGetRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1032,
  serialized_end=1068,
)


_LENSGETREPLY = _descriptor.Descriptor(
  name='LensGetReply',
  full_name='aimbot.LensGetReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='aimbot.LensGetReply.value', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_value', full_name='aimbot.LensGetReply.min_value', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_value', full_name='aimbot.LensGetReply.max_value', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='manual_autofocus_percent', full_name='aimbot.LensGetReply.manual_autofocus_percent', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='manual_autofocusing', full_name='aimbot.LensGetReply.manual_autofocusing', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.LensGetReply.scanner_id', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1071,
  serialized_end=1221,
)


_LENSGETALLREPLY = _descriptor.Descriptor(
  name='LensGetAllReply',
  full_name='aimbot.LensGetAllReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lens_status', full_name='aimbot.LensGetAllReply.lens_status', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1223,
  serialized_end=1283,
)


_LENSAUTOFOCUSREQUEST = _descriptor.Descriptor(
  name='LensAutoFocusRequest',
  full_name='aimbot.LensAutoFocusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.LensAutoFocusRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1285,
  serialized_end=1327,
)


_LENSAUTOFOCUSREPLY = _descriptor.Descriptor(
  name='LensAutoFocusReply',
  full_name='aimbot.LensAutoFocusReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1329,
  serialized_end=1349,
)


_STOPLENSAUTOFOCUSREQUEST = _descriptor.Descriptor(
  name='StopLensAutoFocusRequest',
  full_name='aimbot.StopLensAutoFocusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.StopLensAutoFocusRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1351,
  serialized_end=1397,
)


_LASERARMREQUEST = _descriptor.Descriptor(
  name='LaserArmRequest',
  full_name='aimbot.LaserArmRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.LaserArmRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='armed', full_name='aimbot.LaserArmRequest.armed', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1399,
  serialized_end=1451,
)


_LASERARMREPLY = _descriptor.Descriptor(
  name='LaserArmReply',
  full_name='aimbot.LaserArmReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1453,
  serialized_end=1468,
)


_LASERENABLEREQUEST = _descriptor.Descriptor(
  name='LaserEnableRequest',
  full_name='aimbot.LaserEnableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.LaserEnableRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='aimbot.LaserEnableRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1470,
  serialized_end=1527,
)


_LASERENABLEREPLY = _descriptor.Descriptor(
  name='LaserEnableReply',
  full_name='aimbot.LaserEnableReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='aimbot.LaserEnableReply.status', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='message', full_name='aimbot.LaserEnableReply.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1529,
  serialized_end=1580,
)


_LASERFIREREQUEST = _descriptor.Descriptor(
  name='LaserFireRequest',
  full_name='aimbot.LaserFireRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.LaserFireRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fire', full_name='aimbot.LaserFireRequest.fire', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1582,
  serialized_end=1634,
)


_LASERFIREREPLY = _descriptor.Descriptor(
  name='LaserFireReply',
  full_name='aimbot.LaserFireReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1636,
  serialized_end=1652,
)


_LASERSETREQUEST = _descriptor.Descriptor(
  name='LaserSetRequest',
  full_name='aimbot.LaserSetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.LaserSetRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='on', full_name='aimbot.LaserSetRequest.on', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1654,
  serialized_end=1703,
)


_LASERSETREPLY = _descriptor.Descriptor(
  name='LaserSetReply',
  full_name='aimbot.LaserSetReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1705,
  serialized_end=1720,
)


_BURNIDIVIDUALIMAGESREQUEST = _descriptor.Descriptor(
  name='BurnIdividualImagesRequest',
  full_name='aimbot.BurnIdividualImagesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.BurnIdividualImagesRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_mmps', full_name='aimbot.BurnIdividualImagesRequest.speed_mmps', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intensity', full_name='aimbot.BurnIdividualImagesRequest.intensity', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='json_img', full_name='aimbot.BurnIdividualImagesRequest.json_img', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1722,
  serialized_end=1827,
)


_LASERSTATE = _descriptor.Descriptor(
  name='LaserState',
  full_name='aimbot.LaserState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='aimbot.LaserState.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='firing', full_name='aimbot.LaserState.firing', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error', full_name='aimbot.LaserState.error', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_code', full_name='aimbot.LaserState.error_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_message', full_name='aimbot.LaserState.error_message', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='aimbot.LaserState.power', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='delta_temp', full_name='aimbot.LaserState.delta_temp', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current', full_name='aimbot.LaserState.current', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='arced', full_name='aimbot.LaserState.arced', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_level', full_name='aimbot.LaserState.power_level', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1830,
  serialized_end=2029,
)


_SERVOGOTOREQUEST = _descriptor.Descriptor(
  name='ServoGoToRequest',
  full_name='aimbot.ServoGoToRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.ServoGoToRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='servo_type', full_name='aimbot.ServoGoToRequest.servo_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position', full_name='aimbot.ServoGoToRequest.position', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time_ms', full_name='aimbot.ServoGoToRequest.time_ms', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='await_settle', full_name='aimbot.ServoGoToRequest.await_settle', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _SERVOGOTOREQUEST_SERVOTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2032,
  serialized_end=2215,
)


_SERVOGOTOREPLY = _descriptor.Descriptor(
  name='ServoGoToReply',
  full_name='aimbot.ServoGoToReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2217,
  serialized_end=2233,
)


_SERVOGETPOSVELREQUEST = _descriptor.Descriptor(
  name='ServoGetPosVelRequest',
  full_name='aimbot.ServoGetPosVelRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.ServoGetPosVelRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2235,
  serialized_end=2278,
)


_SERVOGETPOSVELREPLY = _descriptor.Descriptor(
  name='ServoGetPosVelReply',
  full_name='aimbot.ServoGetPosVelReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan_position', full_name='aimbot.ServoGetPosVelReply.pan_position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt_position', full_name='aimbot.ServoGetPosVelReply.tilt_position', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pan_velocity', full_name='aimbot.ServoGetPosVelReply.pan_velocity', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt_velocity', full_name='aimbot.ServoGetPosVelReply.tilt_velocity', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2280,
  serialized_end=2391,
)


_SERVOGETLIMITSREQUEST = _descriptor.Descriptor(
  name='ServoGetLimitsRequest',
  full_name='aimbot.ServoGetLimitsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.ServoGetLimitsRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2393,
  serialized_end=2436,
)


_SERVOGETLIMITSREPLY = _descriptor.Descriptor(
  name='ServoGetLimitsReply',
  full_name='aimbot.ServoGetLimitsReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan_min', full_name='aimbot.ServoGetLimitsReply.pan_min', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pan_max', full_name='aimbot.ServoGetLimitsReply.pan_max', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt_min', full_name='aimbot.ServoGetLimitsReply.tilt_min', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt_max', full_name='aimbot.ServoGetLimitsReply.tilt_max', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2438,
  serialized_end=2529,
)


_TUNINGPARAM = _descriptor.Descriptor(
  name='TuningParam',
  full_name='aimbot.TuningParam',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='aimbot.TuningParam.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='v_uint', full_name='aimbot.TuningParam.v_uint', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='v_int', full_name='aimbot.TuningParam.v_int', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='v_bool', full_name='aimbot.TuningParam.v_bool', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='v_float', full_name='aimbot.TuningParam.v_float', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='v_string', full_name='aimbot.TuningParam.v_string', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='aimbot.TuningParam.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2532,
  serialized_end=2660,
)


_TUNINGPARAMSUPDATEREQUEST = _descriptor.Descriptor(
  name='TuningParamsUpdateRequest',
  full_name='aimbot.TuningParamsUpdateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='params', full_name='aimbot.TuningParamsUpdateRequest.params', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2662,
  serialized_end=2726,
)


_TUNINGPARAMSUPDATEREPLY = _descriptor.Descriptor(
  name='TuningParamsUpdateReply',
  full_name='aimbot.TuningParamsUpdateReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2728,
  serialized_end=2753,
)


_TUNINGPARAMSGETREQUEST = _descriptor.Descriptor(
  name='TuningParamsGetRequest',
  full_name='aimbot.TuningParamsGetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='aimbot.TuningParamsGetRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2755,
  serialized_end=2793,
)


_TUNINGPARAMSGETREPLY = _descriptor.Descriptor(
  name='TuningParamsGetReply',
  full_name='aimbot.TuningParamsGetReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='params', full_name='aimbot.TuningParamsGetReply.params', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2795,
  serialized_end=2854,
)


_GETLOADESTIMATEREQUEST = _descriptor.Descriptor(
  name='GetLoadEstimateRequest',
  full_name='aimbot.GetLoadEstimateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2856,
  serialized_end=2880,
)


_GETLOADESTIMATEREPLY = _descriptor.Descriptor(
  name='GetLoadEstimateReply',
  full_name='aimbot.GetLoadEstimateReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_load', full_name='aimbot.GetLoadEstimateReply.current_load', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_load', full_name='aimbot.GetLoadEstimateReply.target_load', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2882,
  serialized_end=2947,
)


_GETDIAGNOSTICREQUEST = _descriptor.Descriptor(
  name='GetDiagnosticRequest',
  full_name='aimbot.GetDiagnosticRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2949,
  serialized_end=2971,
)


_GETDIAGNOSTICREPLY = _descriptor.Descriptor(
  name='GetDiagnosticReply',
  full_name='aimbot.GetDiagnosticReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='diagnostic', full_name='aimbot.GetDiagnosticReply.diagnostic', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2973,
  serialized_end=3013,
)


_RESETDEVICESREQUEST = _descriptor.Descriptor(
  name='ResetDevicesRequest',
  full_name='aimbot.ResetDevicesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='device_id', full_name='aimbot.ResetDevicesRequest.device_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3015,
  serialized_end=3055,
)


_RESETDEVICESREPLY = _descriptor.Descriptor(
  name='ResetDevicesReply',
  full_name='aimbot.ResetDevicesReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3057,
  serialized_end=3076,
)


_RESETSCANNERREQUEST = _descriptor.Descriptor(
  name='ResetScannerRequest',
  full_name='aimbot.ResetScannerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='aimbot.ResetScannerRequest.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metrics_only', full_name='aimbot.ResetScannerRequest.metrics_only', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3078,
  serialized_end=3141,
)


_RESETSCANNERREPLY = _descriptor.Descriptor(
  name='ResetScannerReply',
  full_name='aimbot.ResetScannerReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3143,
  serialized_end=3162,
)


_SCANNERDESCRIPTOR = _descriptor.Descriptor(
  name='ScannerDescriptor',
  full_name='aimbot.ScannerDescriptor',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='aimbot.ScannerDescriptor.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3164,
  serialized_end=3195,
)


_SCANNERTARGETPOSITION = _descriptor.Descriptor(
  name='ScannerTargetPosition',
  full_name='aimbot.ScannerTargetPosition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_descriptor', full_name='aimbot.ScannerTargetPosition.scanner_descriptor', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x', full_name='aimbot.ScannerTargetPosition.x', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='aimbot.ScannerTargetPosition.y', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3197,
  serialized_end=3297,
)


_CROSSHAIRSTATE = _descriptor.Descriptor(
  name='CrosshairState',
  full_name='aimbot.CrosshairState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='aimbot.CrosshairState.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='aimbot.CrosshairState.y', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='calibrating', full_name='aimbot.CrosshairState.calibrating', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='calibration_failed', full_name='aimbot.CrosshairState.calibration_failed', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3299,
  serialized_end=3386,
)


_SCANNERSTATE = _descriptor.Descriptor(
  name='ScannerState',
  full_name='aimbot.ScannerState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_descriptor', full_name='aimbot.ScannerState.scanner_descriptor', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_state', full_name='aimbot.ScannerState.laser_state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crosshair_state', full_name='aimbot.ScannerState.crosshair_state', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_error', full_name='aimbot.ScannerState.scanner_error', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_code', full_name='aimbot.ScannerState.error_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_message', full_name='aimbot.ScannerState.error_message', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_trajectory_id', full_name='aimbot.ScannerState.target_trajectory_id', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pan_failure', full_name='aimbot.ScannerState.pan_failure', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt_failure', full_name='aimbot.ScannerState.tilt_failure', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3389,
  serialized_end=3687,
)


_AUTOXHAIRCALIBRATIONPROGRESS = _descriptor.Descriptor(
  name='AutoXHairCalibrationProgress',
  full_name='aimbot.AutoXHairCalibrationProgress',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='in_progress', full_name='aimbot.AutoXHairCalibrationProgress.in_progress', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='progress', full_name='aimbot.AutoXHairCalibrationProgress.progress', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3689,
  serialized_end=3758,
)


_SCANNERSTATUSREPLY = _descriptor.Descriptor(
  name='ScannerStatusReply',
  full_name='aimbot.ScannerStatusReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='states', full_name='aimbot.ScannerStatusReply.states', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x_hair_progress', full_name='aimbot.ScannerStatusReply.x_hair_progress', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3760,
  serialized_end=3881,
)


_BOOTEDREPLY = _descriptor.Descriptor(
  name='BootedReply',
  full_name='aimbot.BootedReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='booted', full_name='aimbot.BootedReply.booted', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3883,
  serialized_end=3912,
)


_TRACKERSTATE = _descriptor.Descriptor(
  name='TrackerState',
  full_name='aimbot.TrackerState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='aimbot.TrackerState.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='at_weed_limit', full_name='aimbot.TrackerState.at_weed_limit', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rotary_timeout', full_name='aimbot.TrackerState.rotary_timeout', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed_error', full_name='aimbot.TrackerState.deepweed_error', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3914,
  serialized_end=4011,
)


_SCHEDULERSTATE = _descriptor.Descriptor(
  name='SchedulerState',
  full_name='aimbot.SchedulerState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='over_capacity', full_name='aimbot.SchedulerState.over_capacity', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4013,
  serialized_end=4052,
)


_TRACKINGSTATE = _descriptor.Descriptor(
  name='TrackingState',
  full_name='aimbot.TrackingState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='states', full_name='aimbot.TrackingState.states', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scheduler_state', full_name='aimbot.TrackingState.scheduler_state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4054,
  serialized_end=4156,
)


_BEDTOPHEIGHTPROFILE = _descriptor.Descriptor(
  name='BedtopHeightProfile',
  full_name='aimbot.BedtopHeightProfile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weed_height_columns', full_name='aimbot.BedtopHeightProfile.weed_height_columns', index=0,
      number=1, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_height_columns', full_name='aimbot.BedtopHeightProfile.crop_height_columns', index=1,
      number=2, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pcam_id', full_name='aimbot.BedtopHeightProfile.pcam_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4158,
  serialized_end=4254,
)


_TRACKERBEDTOPHEIGHTPROFILE = _descriptor.Descriptor(
  name='TrackerBedtopHeightProfile',
  full_name='aimbot.TrackerBedtopHeightProfile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='profiles', full_name='aimbot.TrackerBedtopHeightProfile.profiles', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bbh_offset_mm', full_name='aimbot.TrackerBedtopHeightProfile.bbh_offset_mm', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4256,
  serialized_end=4354,
)


_GETDIMENSIONSRESPONSE = _descriptor.Descriptor(
  name='GetDimensionsResponse',
  full_name='aimbot.GetDimensionsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min_x_mm', full_name='aimbot.GetDimensionsResponse.min_x_mm', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_x_mm', full_name='aimbot.GetDimensionsResponse.max_x_mm', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_y_mm', full_name='aimbot.GetDimensionsResponse.min_y_mm', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_y_mm', full_name='aimbot.GetDimensionsResponse.max_y_mm', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_x_mm', full_name='aimbot.GetDimensionsResponse.center_x_mm', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4356,
  serialized_end=4472,
)


_GETTARGETCAMSNREQUEST = _descriptor.Descriptor(
  name='GetTargetCamSNRequest',
  full_name='aimbot.GetTargetCamSNRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='camera_id', full_name='aimbot.GetTargetCamSNRequest.camera_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4474,
  serialized_end=4516,
)


_GETTARGETCAMSNRESPONSE = _descriptor.Descriptor(
  name='GetTargetCamSNResponse',
  full_name='aimbot.GetTargetCamSNResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='serial_number', full_name='aimbot.GetTargetCamSNResponse.serial_number', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4518,
  serialized_end=4565,
)


_RELOADTHINNINGCONFREQUEST = _descriptor.Descriptor(
  name='ReloadThinningConfRequest',
  full_name='aimbot.ReloadThinningConfRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4567,
  serialized_end=4594,
)


_RELOADALMANACCONFREQUEST = _descriptor.Descriptor(
  name='ReloadAlmanacConfRequest',
  full_name='aimbot.ReloadAlmanacConfRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4596,
  serialized_end=4622,
)


_RELOADDISCRIMINATORCONFREQUEST = _descriptor.Descriptor(
  name='ReloadDiscriminatorConfRequest',
  full_name='aimbot.ReloadDiscriminatorConfRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4624,
  serialized_end=4656,
)


_RELOADMODELINATORCONFREQUEST = _descriptor.Descriptor(
  name='ReloadModelinatorConfRequest',
  full_name='aimbot.ReloadModelinatorConfRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4658,
  serialized_end=4688,
)


_RELOADTVEPROFILEREQUEST = _descriptor.Descriptor(
  name='ReloadTVEProfileRequest',
  full_name='aimbot.ReloadTVEProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4690,
  serialized_end=4715,
)


_FIXLASERMETRICSREQUEST = _descriptor.Descriptor(
  name='FixLaserMetricsRequest',
  full_name='aimbot.FixLaserMetricsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner', full_name='aimbot.FixLaserMetricsRequest.scanner', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='total_fire_count', full_name='aimbot.FixLaserMetricsRequest.total_fire_count', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='total_fire_time_ms', full_name='aimbot.FixLaserMetricsRequest.total_fire_time_ms', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4717,
  serialized_end=4839,
)


_TRACKEDITEMSREQUEST = _descriptor.Descriptor(
  name='TrackedItemsRequest',
  full_name='aimbot.TrackedItemsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='aimbot.TrackedItemsRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4841,
  serialized_end=4878,
)


_TRACKEDITEMHISTORY = _descriptor.Descriptor(
  name='TrackedItemHistory',
  full_name='aimbot.TrackedItemHistory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='aimbot.TrackedItemHistory.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='detection', full_name='aimbot.TrackedItemHistory.detection', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4880,
  serialized_end=4967,
)


_TRACKEDITEM = _descriptor.Descriptor(
  name='TrackedItem',
  full_name='aimbot.TrackedItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='aimbot.TrackedItem.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='history', full_name='aimbot.TrackedItem.history', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4969,
  serialized_end=5039,
)


_TRACKEDITEMSRESPONSE = _descriptor.Descriptor(
  name='TrackedItemsResponse',
  full_name='aimbot.TrackedItemsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tracked_items', full_name='aimbot.TrackedItemsResponse.tracked_items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5041,
  serialized_end=5107,
)


_PARTICIPATIONRESPONSE = _descriptor.Descriptor(
  name='ParticipationResponse',
  full_name='aimbot.ParticipationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='running_as_leader', full_name='aimbot.ParticipationResponse.running_as_leader', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5109,
  serialized_end=5159,
)

_AIMBOTSTATE.fields_by_name['targeting_state'].message_type = _TARGETINGSTATE
_AIMBOTSTATE.fields_by_name['safety_override_state'].enum_type = _SAFETYOVERRIDESTATE
_ACTUATIONTASKREQUEST.fields_by_name['laser_test'].message_type = _LASERTESTACTUATIONTASK
_ACTUATIONTASKREQUEST.fields_by_name['image_draw'].message_type = _IMAGEDRAWACTUATIONTASK
_ACTUATIONTASKREQUEST.fields_by_name['range_draw'].message_type = _RANGEDRAWACTUATIONTASK
_ACTUATIONTASKREQUEST.oneofs_by_name['task'].fields.append(
  _ACTUATIONTASKREQUEST.fields_by_name['laser_test'])
_ACTUATIONTASKREQUEST.fields_by_name['laser_test'].containing_oneof = _ACTUATIONTASKREQUEST.oneofs_by_name['task']
_ACTUATIONTASKREQUEST.oneofs_by_name['task'].fields.append(
  _ACTUATIONTASKREQUEST.fields_by_name['image_draw'])
_ACTUATIONTASKREQUEST.fields_by_name['image_draw'].containing_oneof = _ACTUATIONTASKREQUEST.oneofs_by_name['task']
_ACTUATIONTASKREQUEST.oneofs_by_name['task'].fields.append(
  _ACTUATIONTASKREQUEST.fields_by_name['range_draw'])
_ACTUATIONTASKREQUEST.fields_by_name['range_draw'].containing_oneof = _ACTUATIONTASKREQUEST.oneofs_by_name['task']
_LENSGETALLREPLY.fields_by_name['lens_status'].message_type = _LENSGETREPLY
_SERVOGOTOREQUEST.fields_by_name['servo_type'].enum_type = _SERVOGOTOREQUEST_SERVOTYPE
_SERVOGOTOREQUEST_SERVOTYPE.containing_type = _SERVOGOTOREQUEST
_TUNINGPARAM.oneofs_by_name['value'].fields.append(
  _TUNINGPARAM.fields_by_name['v_uint'])
_TUNINGPARAM.fields_by_name['v_uint'].containing_oneof = _TUNINGPARAM.oneofs_by_name['value']
_TUNINGPARAM.oneofs_by_name['value'].fields.append(
  _TUNINGPARAM.fields_by_name['v_int'])
_TUNINGPARAM.fields_by_name['v_int'].containing_oneof = _TUNINGPARAM.oneofs_by_name['value']
_TUNINGPARAM.oneofs_by_name['value'].fields.append(
  _TUNINGPARAM.fields_by_name['v_bool'])
_TUNINGPARAM.fields_by_name['v_bool'].containing_oneof = _TUNINGPARAM.oneofs_by_name['value']
_TUNINGPARAM.oneofs_by_name['value'].fields.append(
  _TUNINGPARAM.fields_by_name['v_float'])
_TUNINGPARAM.fields_by_name['v_float'].containing_oneof = _TUNINGPARAM.oneofs_by_name['value']
_TUNINGPARAM.oneofs_by_name['value'].fields.append(
  _TUNINGPARAM.fields_by_name['v_string'])
_TUNINGPARAM.fields_by_name['v_string'].containing_oneof = _TUNINGPARAM.oneofs_by_name['value']
_TUNINGPARAMSUPDATEREQUEST.fields_by_name['params'].message_type = _TUNINGPARAM
_TUNINGPARAMSGETREPLY.fields_by_name['params'].message_type = _TUNINGPARAM
_SCANNERTARGETPOSITION.fields_by_name['scanner_descriptor'].message_type = _SCANNERDESCRIPTOR
_SCANNERSTATE.fields_by_name['scanner_descriptor'].message_type = _SCANNERDESCRIPTOR
_SCANNERSTATE.fields_by_name['laser_state'].message_type = _LASERSTATE
_SCANNERSTATE.fields_by_name['crosshair_state'].message_type = _CROSSHAIRSTATE
_SCANNERSTATUSREPLY.fields_by_name['states'].message_type = _SCANNERSTATE
_SCANNERSTATUSREPLY.fields_by_name['x_hair_progress'].message_type = _AUTOXHAIRCALIBRATIONPROGRESS
_TRACKINGSTATE.fields_by_name['states'].message_type = _TRACKERSTATE
_TRACKINGSTATE.fields_by_name['scheduler_state'].message_type = _SCHEDULERSTATE
_TRACKERBEDTOPHEIGHTPROFILE.fields_by_name['profiles'].message_type = _BEDTOPHEIGHTPROFILE
_FIXLASERMETRICSREQUEST.fields_by_name['scanner'].message_type = _SCANNERDESCRIPTOR
_TRACKEDITEMHISTORY.fields_by_name['detection'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._DETECTION
_TRACKEDITEM.fields_by_name['history'].message_type = _TRACKEDITEMHISTORY
_TRACKEDITEMSRESPONSE.fields_by_name['tracked_items'].message_type = _TRACKEDITEM
DESCRIPTOR.message_types_by_name['PingRequest'] = _PINGREQUEST
DESCRIPTOR.message_types_by_name['PongReply'] = _PONGREPLY
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['TargetingState'] = _TARGETINGSTATE
DESCRIPTOR.message_types_by_name['AimbotState'] = _AIMBOTSTATE
DESCRIPTOR.message_types_by_name['TargetVelocityRequest'] = _TARGETVELOCITYREQUEST
DESCRIPTOR.message_types_by_name['TargetVelocityReply'] = _TARGETVELOCITYREPLY
DESCRIPTOR.message_types_by_name['LaserTestActuationTask'] = _LASERTESTACTUATIONTASK
DESCRIPTOR.message_types_by_name['ImageDrawActuationTask'] = _IMAGEDRAWACTUATIONTASK
DESCRIPTOR.message_types_by_name['RangeDrawActuationTask'] = _RANGEDRAWACTUATIONTASK
DESCRIPTOR.message_types_by_name['ActuationTaskRequest'] = _ACTUATIONTASKREQUEST
DESCRIPTOR.message_types_by_name['LensSetRequest'] = _LENSSETREQUEST
DESCRIPTOR.message_types_by_name['LensSetReply'] = _LENSSETREPLY
DESCRIPTOR.message_types_by_name['LensGetRequest'] = _LENSGETREQUEST
DESCRIPTOR.message_types_by_name['LensGetReply'] = _LENSGETREPLY
DESCRIPTOR.message_types_by_name['LensGetAllReply'] = _LENSGETALLREPLY
DESCRIPTOR.message_types_by_name['LensAutoFocusRequest'] = _LENSAUTOFOCUSREQUEST
DESCRIPTOR.message_types_by_name['LensAutoFocusReply'] = _LENSAUTOFOCUSREPLY
DESCRIPTOR.message_types_by_name['StopLensAutoFocusRequest'] = _STOPLENSAUTOFOCUSREQUEST
DESCRIPTOR.message_types_by_name['LaserArmRequest'] = _LASERARMREQUEST
DESCRIPTOR.message_types_by_name['LaserArmReply'] = _LASERARMREPLY
DESCRIPTOR.message_types_by_name['LaserEnableRequest'] = _LASERENABLEREQUEST
DESCRIPTOR.message_types_by_name['LaserEnableReply'] = _LASERENABLEREPLY
DESCRIPTOR.message_types_by_name['LaserFireRequest'] = _LASERFIREREQUEST
DESCRIPTOR.message_types_by_name['LaserFireReply'] = _LASERFIREREPLY
DESCRIPTOR.message_types_by_name['LaserSetRequest'] = _LASERSETREQUEST
DESCRIPTOR.message_types_by_name['LaserSetReply'] = _LASERSETREPLY
DESCRIPTOR.message_types_by_name['BurnIdividualImagesRequest'] = _BURNIDIVIDUALIMAGESREQUEST
DESCRIPTOR.message_types_by_name['LaserState'] = _LASERSTATE
DESCRIPTOR.message_types_by_name['ServoGoToRequest'] = _SERVOGOTOREQUEST
DESCRIPTOR.message_types_by_name['ServoGoToReply'] = _SERVOGOTOREPLY
DESCRIPTOR.message_types_by_name['ServoGetPosVelRequest'] = _SERVOGETPOSVELREQUEST
DESCRIPTOR.message_types_by_name['ServoGetPosVelReply'] = _SERVOGETPOSVELREPLY
DESCRIPTOR.message_types_by_name['ServoGetLimitsRequest'] = _SERVOGETLIMITSREQUEST
DESCRIPTOR.message_types_by_name['ServoGetLimitsReply'] = _SERVOGETLIMITSREPLY
DESCRIPTOR.message_types_by_name['TuningParam'] = _TUNINGPARAM
DESCRIPTOR.message_types_by_name['TuningParamsUpdateRequest'] = _TUNINGPARAMSUPDATEREQUEST
DESCRIPTOR.message_types_by_name['TuningParamsUpdateReply'] = _TUNINGPARAMSUPDATEREPLY
DESCRIPTOR.message_types_by_name['TuningParamsGetRequest'] = _TUNINGPARAMSGETREQUEST
DESCRIPTOR.message_types_by_name['TuningParamsGetReply'] = _TUNINGPARAMSGETREPLY
DESCRIPTOR.message_types_by_name['GetLoadEstimateRequest'] = _GETLOADESTIMATEREQUEST
DESCRIPTOR.message_types_by_name['GetLoadEstimateReply'] = _GETLOADESTIMATEREPLY
DESCRIPTOR.message_types_by_name['GetDiagnosticRequest'] = _GETDIAGNOSTICREQUEST
DESCRIPTOR.message_types_by_name['GetDiagnosticReply'] = _GETDIAGNOSTICREPLY
DESCRIPTOR.message_types_by_name['ResetDevicesRequest'] = _RESETDEVICESREQUEST
DESCRIPTOR.message_types_by_name['ResetDevicesReply'] = _RESETDEVICESREPLY
DESCRIPTOR.message_types_by_name['ResetScannerRequest'] = _RESETSCANNERREQUEST
DESCRIPTOR.message_types_by_name['ResetScannerReply'] = _RESETSCANNERREPLY
DESCRIPTOR.message_types_by_name['ScannerDescriptor'] = _SCANNERDESCRIPTOR
DESCRIPTOR.message_types_by_name['ScannerTargetPosition'] = _SCANNERTARGETPOSITION
DESCRIPTOR.message_types_by_name['CrosshairState'] = _CROSSHAIRSTATE
DESCRIPTOR.message_types_by_name['ScannerState'] = _SCANNERSTATE
DESCRIPTOR.message_types_by_name['AutoXHairCalibrationProgress'] = _AUTOXHAIRCALIBRATIONPROGRESS
DESCRIPTOR.message_types_by_name['ScannerStatusReply'] = _SCANNERSTATUSREPLY
DESCRIPTOR.message_types_by_name['BootedReply'] = _BOOTEDREPLY
DESCRIPTOR.message_types_by_name['TrackerState'] = _TRACKERSTATE
DESCRIPTOR.message_types_by_name['SchedulerState'] = _SCHEDULERSTATE
DESCRIPTOR.message_types_by_name['TrackingState'] = _TRACKINGSTATE
DESCRIPTOR.message_types_by_name['BedtopHeightProfile'] = _BEDTOPHEIGHTPROFILE
DESCRIPTOR.message_types_by_name['TrackerBedtopHeightProfile'] = _TRACKERBEDTOPHEIGHTPROFILE
DESCRIPTOR.message_types_by_name['GetDimensionsResponse'] = _GETDIMENSIONSRESPONSE
DESCRIPTOR.message_types_by_name['GetTargetCamSNRequest'] = _GETTARGETCAMSNREQUEST
DESCRIPTOR.message_types_by_name['GetTargetCamSNResponse'] = _GETTARGETCAMSNRESPONSE
DESCRIPTOR.message_types_by_name['ReloadThinningConfRequest'] = _RELOADTHINNINGCONFREQUEST
DESCRIPTOR.message_types_by_name['ReloadAlmanacConfRequest'] = _RELOADALMANACCONFREQUEST
DESCRIPTOR.message_types_by_name['ReloadDiscriminatorConfRequest'] = _RELOADDISCRIMINATORCONFREQUEST
DESCRIPTOR.message_types_by_name['ReloadModelinatorConfRequest'] = _RELOADMODELINATORCONFREQUEST
DESCRIPTOR.message_types_by_name['ReloadTVEProfileRequest'] = _RELOADTVEPROFILEREQUEST
DESCRIPTOR.message_types_by_name['FixLaserMetricsRequest'] = _FIXLASERMETRICSREQUEST
DESCRIPTOR.message_types_by_name['TrackedItemsRequest'] = _TRACKEDITEMSREQUEST
DESCRIPTOR.message_types_by_name['TrackedItemHistory'] = _TRACKEDITEMHISTORY
DESCRIPTOR.message_types_by_name['TrackedItem'] = _TRACKEDITEM
DESCRIPTOR.message_types_by_name['TrackedItemsResponse'] = _TRACKEDITEMSRESPONSE
DESCRIPTOR.message_types_by_name['ParticipationResponse'] = _PARTICIPATIONRESPONSE
DESCRIPTOR.enum_types_by_name['SafetyOverrideState'] = _SAFETYOVERRIDESTATE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PingRequest = _reflection.GeneratedProtocolMessageType('PingRequest', (_message.Message,), {
  'DESCRIPTOR' : _PINGREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.PingRequest)
  })
_sym_db.RegisterMessage(PingRequest)

PongReply = _reflection.GeneratedProtocolMessageType('PongReply', (_message.Message,), {
  'DESCRIPTOR' : _PONGREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.PongReply)
  })
_sym_db.RegisterMessage(PongReply)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.Empty)
  })
_sym_db.RegisterMessage(Empty)

TargetingState = _reflection.GeneratedProtocolMessageType('TargetingState', (_message.Message,), {
  'DESCRIPTOR' : _TARGETINGSTATE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TargetingState)
  })
_sym_db.RegisterMessage(TargetingState)

AimbotState = _reflection.GeneratedProtocolMessageType('AimbotState', (_message.Message,), {
  'DESCRIPTOR' : _AIMBOTSTATE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.AimbotState)
  })
_sym_db.RegisterMessage(AimbotState)

TargetVelocityRequest = _reflection.GeneratedProtocolMessageType('TargetVelocityRequest', (_message.Message,), {
  'DESCRIPTOR' : _TARGETVELOCITYREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TargetVelocityRequest)
  })
_sym_db.RegisterMessage(TargetVelocityRequest)

TargetVelocityReply = _reflection.GeneratedProtocolMessageType('TargetVelocityReply', (_message.Message,), {
  'DESCRIPTOR' : _TARGETVELOCITYREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TargetVelocityReply)
  })
_sym_db.RegisterMessage(TargetVelocityReply)

LaserTestActuationTask = _reflection.GeneratedProtocolMessageType('LaserTestActuationTask', (_message.Message,), {
  'DESCRIPTOR' : _LASERTESTACTUATIONTASK,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserTestActuationTask)
  })
_sym_db.RegisterMessage(LaserTestActuationTask)

ImageDrawActuationTask = _reflection.GeneratedProtocolMessageType('ImageDrawActuationTask', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEDRAWACTUATIONTASK,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ImageDrawActuationTask)
  })
_sym_db.RegisterMessage(ImageDrawActuationTask)

RangeDrawActuationTask = _reflection.GeneratedProtocolMessageType('RangeDrawActuationTask', (_message.Message,), {
  'DESCRIPTOR' : _RANGEDRAWACTUATIONTASK,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.RangeDrawActuationTask)
  })
_sym_db.RegisterMessage(RangeDrawActuationTask)

ActuationTaskRequest = _reflection.GeneratedProtocolMessageType('ActuationTaskRequest', (_message.Message,), {
  'DESCRIPTOR' : _ACTUATIONTASKREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ActuationTaskRequest)
  })
_sym_db.RegisterMessage(ActuationTaskRequest)

LensSetRequest = _reflection.GeneratedProtocolMessageType('LensSetRequest', (_message.Message,), {
  'DESCRIPTOR' : _LENSSETREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LensSetRequest)
  })
_sym_db.RegisterMessage(LensSetRequest)

LensSetReply = _reflection.GeneratedProtocolMessageType('LensSetReply', (_message.Message,), {
  'DESCRIPTOR' : _LENSSETREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LensSetReply)
  })
_sym_db.RegisterMessage(LensSetReply)

LensGetRequest = _reflection.GeneratedProtocolMessageType('LensGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _LENSGETREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LensGetRequest)
  })
_sym_db.RegisterMessage(LensGetRequest)

LensGetReply = _reflection.GeneratedProtocolMessageType('LensGetReply', (_message.Message,), {
  'DESCRIPTOR' : _LENSGETREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LensGetReply)
  })
_sym_db.RegisterMessage(LensGetReply)

LensGetAllReply = _reflection.GeneratedProtocolMessageType('LensGetAllReply', (_message.Message,), {
  'DESCRIPTOR' : _LENSGETALLREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LensGetAllReply)
  })
_sym_db.RegisterMessage(LensGetAllReply)

LensAutoFocusRequest = _reflection.GeneratedProtocolMessageType('LensAutoFocusRequest', (_message.Message,), {
  'DESCRIPTOR' : _LENSAUTOFOCUSREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LensAutoFocusRequest)
  })
_sym_db.RegisterMessage(LensAutoFocusRequest)

LensAutoFocusReply = _reflection.GeneratedProtocolMessageType('LensAutoFocusReply', (_message.Message,), {
  'DESCRIPTOR' : _LENSAUTOFOCUSREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LensAutoFocusReply)
  })
_sym_db.RegisterMessage(LensAutoFocusReply)

StopLensAutoFocusRequest = _reflection.GeneratedProtocolMessageType('StopLensAutoFocusRequest', (_message.Message,), {
  'DESCRIPTOR' : _STOPLENSAUTOFOCUSREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.StopLensAutoFocusRequest)
  })
_sym_db.RegisterMessage(StopLensAutoFocusRequest)

LaserArmRequest = _reflection.GeneratedProtocolMessageType('LaserArmRequest', (_message.Message,), {
  'DESCRIPTOR' : _LASERARMREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserArmRequest)
  })
_sym_db.RegisterMessage(LaserArmRequest)

LaserArmReply = _reflection.GeneratedProtocolMessageType('LaserArmReply', (_message.Message,), {
  'DESCRIPTOR' : _LASERARMREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserArmReply)
  })
_sym_db.RegisterMessage(LaserArmReply)

LaserEnableRequest = _reflection.GeneratedProtocolMessageType('LaserEnableRequest', (_message.Message,), {
  'DESCRIPTOR' : _LASERENABLEREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserEnableRequest)
  })
_sym_db.RegisterMessage(LaserEnableRequest)

LaserEnableReply = _reflection.GeneratedProtocolMessageType('LaserEnableReply', (_message.Message,), {
  'DESCRIPTOR' : _LASERENABLEREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserEnableReply)
  })
_sym_db.RegisterMessage(LaserEnableReply)

LaserFireRequest = _reflection.GeneratedProtocolMessageType('LaserFireRequest', (_message.Message,), {
  'DESCRIPTOR' : _LASERFIREREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserFireRequest)
  })
_sym_db.RegisterMessage(LaserFireRequest)

LaserFireReply = _reflection.GeneratedProtocolMessageType('LaserFireReply', (_message.Message,), {
  'DESCRIPTOR' : _LASERFIREREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserFireReply)
  })
_sym_db.RegisterMessage(LaserFireReply)

LaserSetRequest = _reflection.GeneratedProtocolMessageType('LaserSetRequest', (_message.Message,), {
  'DESCRIPTOR' : _LASERSETREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserSetRequest)
  })
_sym_db.RegisterMessage(LaserSetRequest)

LaserSetReply = _reflection.GeneratedProtocolMessageType('LaserSetReply', (_message.Message,), {
  'DESCRIPTOR' : _LASERSETREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserSetReply)
  })
_sym_db.RegisterMessage(LaserSetReply)

BurnIdividualImagesRequest = _reflection.GeneratedProtocolMessageType('BurnIdividualImagesRequest', (_message.Message,), {
  'DESCRIPTOR' : _BURNIDIVIDUALIMAGESREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.BurnIdividualImagesRequest)
  })
_sym_db.RegisterMessage(BurnIdividualImagesRequest)

LaserState = _reflection.GeneratedProtocolMessageType('LaserState', (_message.Message,), {
  'DESCRIPTOR' : _LASERSTATE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.LaserState)
  })
_sym_db.RegisterMessage(LaserState)

ServoGoToRequest = _reflection.GeneratedProtocolMessageType('ServoGoToRequest', (_message.Message,), {
  'DESCRIPTOR' : _SERVOGOTOREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ServoGoToRequest)
  })
_sym_db.RegisterMessage(ServoGoToRequest)

ServoGoToReply = _reflection.GeneratedProtocolMessageType('ServoGoToReply', (_message.Message,), {
  'DESCRIPTOR' : _SERVOGOTOREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ServoGoToReply)
  })
_sym_db.RegisterMessage(ServoGoToReply)

ServoGetPosVelRequest = _reflection.GeneratedProtocolMessageType('ServoGetPosVelRequest', (_message.Message,), {
  'DESCRIPTOR' : _SERVOGETPOSVELREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ServoGetPosVelRequest)
  })
_sym_db.RegisterMessage(ServoGetPosVelRequest)

ServoGetPosVelReply = _reflection.GeneratedProtocolMessageType('ServoGetPosVelReply', (_message.Message,), {
  'DESCRIPTOR' : _SERVOGETPOSVELREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ServoGetPosVelReply)
  })
_sym_db.RegisterMessage(ServoGetPosVelReply)

ServoGetLimitsRequest = _reflection.GeneratedProtocolMessageType('ServoGetLimitsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SERVOGETLIMITSREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ServoGetLimitsRequest)
  })
_sym_db.RegisterMessage(ServoGetLimitsRequest)

ServoGetLimitsReply = _reflection.GeneratedProtocolMessageType('ServoGetLimitsReply', (_message.Message,), {
  'DESCRIPTOR' : _SERVOGETLIMITSREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ServoGetLimitsReply)
  })
_sym_db.RegisterMessage(ServoGetLimitsReply)

TuningParam = _reflection.GeneratedProtocolMessageType('TuningParam', (_message.Message,), {
  'DESCRIPTOR' : _TUNINGPARAM,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TuningParam)
  })
_sym_db.RegisterMessage(TuningParam)

TuningParamsUpdateRequest = _reflection.GeneratedProtocolMessageType('TuningParamsUpdateRequest', (_message.Message,), {
  'DESCRIPTOR' : _TUNINGPARAMSUPDATEREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TuningParamsUpdateRequest)
  })
_sym_db.RegisterMessage(TuningParamsUpdateRequest)

TuningParamsUpdateReply = _reflection.GeneratedProtocolMessageType('TuningParamsUpdateReply', (_message.Message,), {
  'DESCRIPTOR' : _TUNINGPARAMSUPDATEREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TuningParamsUpdateReply)
  })
_sym_db.RegisterMessage(TuningParamsUpdateReply)

TuningParamsGetRequest = _reflection.GeneratedProtocolMessageType('TuningParamsGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _TUNINGPARAMSGETREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TuningParamsGetRequest)
  })
_sym_db.RegisterMessage(TuningParamsGetRequest)

TuningParamsGetReply = _reflection.GeneratedProtocolMessageType('TuningParamsGetReply', (_message.Message,), {
  'DESCRIPTOR' : _TUNINGPARAMSGETREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TuningParamsGetReply)
  })
_sym_db.RegisterMessage(TuningParamsGetReply)

GetLoadEstimateRequest = _reflection.GeneratedProtocolMessageType('GetLoadEstimateRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETLOADESTIMATEREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.GetLoadEstimateRequest)
  })
_sym_db.RegisterMessage(GetLoadEstimateRequest)

GetLoadEstimateReply = _reflection.GeneratedProtocolMessageType('GetLoadEstimateReply', (_message.Message,), {
  'DESCRIPTOR' : _GETLOADESTIMATEREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.GetLoadEstimateReply)
  })
_sym_db.RegisterMessage(GetLoadEstimateReply)

GetDiagnosticRequest = _reflection.GeneratedProtocolMessageType('GetDiagnosticRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDIAGNOSTICREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.GetDiagnosticRequest)
  })
_sym_db.RegisterMessage(GetDiagnosticRequest)

GetDiagnosticReply = _reflection.GeneratedProtocolMessageType('GetDiagnosticReply', (_message.Message,), {
  'DESCRIPTOR' : _GETDIAGNOSTICREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.GetDiagnosticReply)
  })
_sym_db.RegisterMessage(GetDiagnosticReply)

ResetDevicesRequest = _reflection.GeneratedProtocolMessageType('ResetDevicesRequest', (_message.Message,), {
  'DESCRIPTOR' : _RESETDEVICESREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ResetDevicesRequest)
  })
_sym_db.RegisterMessage(ResetDevicesRequest)

ResetDevicesReply = _reflection.GeneratedProtocolMessageType('ResetDevicesReply', (_message.Message,), {
  'DESCRIPTOR' : _RESETDEVICESREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ResetDevicesReply)
  })
_sym_db.RegisterMessage(ResetDevicesReply)

ResetScannerRequest = _reflection.GeneratedProtocolMessageType('ResetScannerRequest', (_message.Message,), {
  'DESCRIPTOR' : _RESETSCANNERREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ResetScannerRequest)
  })
_sym_db.RegisterMessage(ResetScannerRequest)

ResetScannerReply = _reflection.GeneratedProtocolMessageType('ResetScannerReply', (_message.Message,), {
  'DESCRIPTOR' : _RESETSCANNERREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ResetScannerReply)
  })
_sym_db.RegisterMessage(ResetScannerReply)

ScannerDescriptor = _reflection.GeneratedProtocolMessageType('ScannerDescriptor', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERDESCRIPTOR,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ScannerDescriptor)
  })
_sym_db.RegisterMessage(ScannerDescriptor)

ScannerTargetPosition = _reflection.GeneratedProtocolMessageType('ScannerTargetPosition', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERTARGETPOSITION,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ScannerTargetPosition)
  })
_sym_db.RegisterMessage(ScannerTargetPosition)

CrosshairState = _reflection.GeneratedProtocolMessageType('CrosshairState', (_message.Message,), {
  'DESCRIPTOR' : _CROSSHAIRSTATE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.CrosshairState)
  })
_sym_db.RegisterMessage(CrosshairState)

ScannerState = _reflection.GeneratedProtocolMessageType('ScannerState', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERSTATE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ScannerState)
  })
_sym_db.RegisterMessage(ScannerState)

AutoXHairCalibrationProgress = _reflection.GeneratedProtocolMessageType('AutoXHairCalibrationProgress', (_message.Message,), {
  'DESCRIPTOR' : _AUTOXHAIRCALIBRATIONPROGRESS,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.AutoXHairCalibrationProgress)
  })
_sym_db.RegisterMessage(AutoXHairCalibrationProgress)

ScannerStatusReply = _reflection.GeneratedProtocolMessageType('ScannerStatusReply', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERSTATUSREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ScannerStatusReply)
  })
_sym_db.RegisterMessage(ScannerStatusReply)

BootedReply = _reflection.GeneratedProtocolMessageType('BootedReply', (_message.Message,), {
  'DESCRIPTOR' : _BOOTEDREPLY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.BootedReply)
  })
_sym_db.RegisterMessage(BootedReply)

TrackerState = _reflection.GeneratedProtocolMessageType('TrackerState', (_message.Message,), {
  'DESCRIPTOR' : _TRACKERSTATE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TrackerState)
  })
_sym_db.RegisterMessage(TrackerState)

SchedulerState = _reflection.GeneratedProtocolMessageType('SchedulerState', (_message.Message,), {
  'DESCRIPTOR' : _SCHEDULERSTATE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.SchedulerState)
  })
_sym_db.RegisterMessage(SchedulerState)

TrackingState = _reflection.GeneratedProtocolMessageType('TrackingState', (_message.Message,), {
  'DESCRIPTOR' : _TRACKINGSTATE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TrackingState)
  })
_sym_db.RegisterMessage(TrackingState)

BedtopHeightProfile = _reflection.GeneratedProtocolMessageType('BedtopHeightProfile', (_message.Message,), {
  'DESCRIPTOR' : _BEDTOPHEIGHTPROFILE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.BedtopHeightProfile)
  })
_sym_db.RegisterMessage(BedtopHeightProfile)

TrackerBedtopHeightProfile = _reflection.GeneratedProtocolMessageType('TrackerBedtopHeightProfile', (_message.Message,), {
  'DESCRIPTOR' : _TRACKERBEDTOPHEIGHTPROFILE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TrackerBedtopHeightProfile)
  })
_sym_db.RegisterMessage(TrackerBedtopHeightProfile)

GetDimensionsResponse = _reflection.GeneratedProtocolMessageType('GetDimensionsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETDIMENSIONSRESPONSE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.GetDimensionsResponse)
  })
_sym_db.RegisterMessage(GetDimensionsResponse)

GetTargetCamSNRequest = _reflection.GeneratedProtocolMessageType('GetTargetCamSNRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTARGETCAMSNREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.GetTargetCamSNRequest)
  })
_sym_db.RegisterMessage(GetTargetCamSNRequest)

GetTargetCamSNResponse = _reflection.GeneratedProtocolMessageType('GetTargetCamSNResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETTARGETCAMSNRESPONSE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.GetTargetCamSNResponse)
  })
_sym_db.RegisterMessage(GetTargetCamSNResponse)

ReloadThinningConfRequest = _reflection.GeneratedProtocolMessageType('ReloadThinningConfRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELOADTHINNINGCONFREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ReloadThinningConfRequest)
  })
_sym_db.RegisterMessage(ReloadThinningConfRequest)

ReloadAlmanacConfRequest = _reflection.GeneratedProtocolMessageType('ReloadAlmanacConfRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELOADALMANACCONFREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ReloadAlmanacConfRequest)
  })
_sym_db.RegisterMessage(ReloadAlmanacConfRequest)

ReloadDiscriminatorConfRequest = _reflection.GeneratedProtocolMessageType('ReloadDiscriminatorConfRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELOADDISCRIMINATORCONFREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ReloadDiscriminatorConfRequest)
  })
_sym_db.RegisterMessage(ReloadDiscriminatorConfRequest)

ReloadModelinatorConfRequest = _reflection.GeneratedProtocolMessageType('ReloadModelinatorConfRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELOADMODELINATORCONFREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ReloadModelinatorConfRequest)
  })
_sym_db.RegisterMessage(ReloadModelinatorConfRequest)

ReloadTVEProfileRequest = _reflection.GeneratedProtocolMessageType('ReloadTVEProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELOADTVEPROFILEREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ReloadTVEProfileRequest)
  })
_sym_db.RegisterMessage(ReloadTVEProfileRequest)

FixLaserMetricsRequest = _reflection.GeneratedProtocolMessageType('FixLaserMetricsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FIXLASERMETRICSREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.FixLaserMetricsRequest)
  })
_sym_db.RegisterMessage(FixLaserMetricsRequest)

TrackedItemsRequest = _reflection.GeneratedProtocolMessageType('TrackedItemsRequest', (_message.Message,), {
  'DESCRIPTOR' : _TRACKEDITEMSREQUEST,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TrackedItemsRequest)
  })
_sym_db.RegisterMessage(TrackedItemsRequest)

TrackedItemHistory = _reflection.GeneratedProtocolMessageType('TrackedItemHistory', (_message.Message,), {
  'DESCRIPTOR' : _TRACKEDITEMHISTORY,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TrackedItemHistory)
  })
_sym_db.RegisterMessage(TrackedItemHistory)

TrackedItem = _reflection.GeneratedProtocolMessageType('TrackedItem', (_message.Message,), {
  'DESCRIPTOR' : _TRACKEDITEM,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TrackedItem)
  })
_sym_db.RegisterMessage(TrackedItem)

TrackedItemsResponse = _reflection.GeneratedProtocolMessageType('TrackedItemsResponse', (_message.Message,), {
  'DESCRIPTOR' : _TRACKEDITEMSRESPONSE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.TrackedItemsResponse)
  })
_sym_db.RegisterMessage(TrackedItemsResponse)

ParticipationResponse = _reflection.GeneratedProtocolMessageType('ParticipationResponse', (_message.Message,), {
  'DESCRIPTOR' : _PARTICIPATIONRESPONSE,
  '__module__' : 'core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2'
  # @@protoc_insertion_point(class_scope:aimbot.ParticipationResponse)
  })
_sym_db.RegisterMessage(ParticipationResponse)


DESCRIPTOR._options = None
_LASERSTATE.fields_by_name['error_code']._options = None
_LASERSTATE.fields_by_name['error_message']._options = None

_AIMBOTSERVICE = _descriptor.ServiceDescriptor(
  name='AimbotService',
  full_name='aimbot.AimbotService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=5241,
  serialized_end=8539,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='aimbot.AimbotService.Ping',
    index=0,
    containing_service=None,
    input_type=_PINGREQUEST,
    output_type=_PONGREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetBooted',
    full_name='aimbot.AimbotService.GetBooted',
    index=1,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_BOOTEDREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ArmLasers',
    full_name='aimbot.AimbotService.ArmLasers',
    index=2,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DisarmLasers',
    full_name='aimbot.AimbotService.DisarmLasers',
    index=3,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetAimbotState',
    full_name='aimbot.AimbotService.GetAimbotState',
    index=4,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_AIMBOTSTATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetTargetingState',
    full_name='aimbot.AimbotService.SetTargetingState',
    index=5,
    containing_service=None,
    input_type=_TARGETINGSTATE,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartActuationTask',
    full_name='aimbot.AimbotService.StartActuationTask',
    index=6,
    containing_service=None,
    input_type=_ACTUATIONTASKREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CancelActuationTask',
    full_name='aimbot.AimbotService.CancelActuationTask',
    index=7,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LensSet',
    full_name='aimbot.AimbotService.LensSet',
    index=8,
    containing_service=None,
    input_type=_LENSSETREQUEST,
    output_type=_LENSSETREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LensGet',
    full_name='aimbot.AimbotService.LensGet',
    index=9,
    containing_service=None,
    input_type=_LENSGETREQUEST,
    output_type=_LENSGETREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LensGetAll',
    full_name='aimbot.AimbotService.LensGetAll',
    index=10,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_LENSGETALLREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LensAutoFocus',
    full_name='aimbot.AimbotService.LensAutoFocus',
    index=11,
    containing_service=None,
    input_type=_LENSAUTOFOCUSREQUEST,
    output_type=_LENSAUTOFOCUSREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StopLensAutoFocus',
    full_name='aimbot.AimbotService.StopLensAutoFocus',
    index=12,
    containing_service=None,
    input_type=_STOPLENSAUTOFOCUSREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LaserArm',
    full_name='aimbot.AimbotService.LaserArm',
    index=13,
    containing_service=None,
    input_type=_LASERARMREQUEST,
    output_type=_LASERARMREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LaserSet',
    full_name='aimbot.AimbotService.LaserSet',
    index=14,
    containing_service=None,
    input_type=_LASERSETREQUEST,
    output_type=_LASERSETREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LaserEnable',
    full_name='aimbot.AimbotService.LaserEnable',
    index=15,
    containing_service=None,
    input_type=_LASERENABLEREQUEST,
    output_type=_LASERENABLEREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LaserFire',
    full_name='aimbot.AimbotService.LaserFire',
    index=16,
    containing_service=None,
    input_type=_LASERFIREREQUEST,
    output_type=_LASERFIREREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ResetLaserMetrics',
    full_name='aimbot.AimbotService.ResetLaserMetrics',
    index=17,
    containing_service=None,
    input_type=_SCANNERDESCRIPTOR,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FixLaserMetrics',
    full_name='aimbot.AimbotService.FixLaserMetrics',
    index=18,
    containing_service=None,
    input_type=_FIXLASERMETRICSREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='BurnIdividualImage',
    full_name='aimbot.AimbotService.BurnIdividualImage',
    index=19,
    containing_service=None,
    input_type=_BURNIDIVIDUALIMAGESREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ServoGoTo',
    full_name='aimbot.AimbotService.ServoGoTo',
    index=20,
    containing_service=None,
    input_type=_SERVOGOTOREQUEST,
    output_type=_SERVOGOTOREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ServoGetPosVel',
    full_name='aimbot.AimbotService.ServoGetPosVel',
    index=21,
    containing_service=None,
    input_type=_SERVOGETPOSVELREQUEST,
    output_type=_SERVOGETPOSVELREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ServoGetLimits',
    full_name='aimbot.AimbotService.ServoGetLimits',
    index=22,
    containing_service=None,
    input_type=_SERVOGETLIMITSREQUEST,
    output_type=_SERVOGETLIMITSREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='TuningParamsUpdate',
    full_name='aimbot.AimbotService.TuningParamsUpdate',
    index=23,
    containing_service=None,
    input_type=_TUNINGPARAMSUPDATEREQUEST,
    output_type=_TUNINGPARAMSUPDATEREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='TuningParamsGet',
    full_name='aimbot.AimbotService.TuningParamsGet',
    index=24,
    containing_service=None,
    input_type=_TUNINGPARAMSGETREQUEST,
    output_type=_TUNINGPARAMSGETREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetLoadEstimate',
    full_name='aimbot.AimbotService.GetLoadEstimate',
    index=25,
    containing_service=None,
    input_type=_GETLOADESTIMATEREQUEST,
    output_type=_GETLOADESTIMATEREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDiagnostic',
    full_name='aimbot.AimbotService.GetDiagnostic',
    index=26,
    containing_service=None,
    input_type=_GETDIAGNOSTICREQUEST,
    output_type=_GETDIAGNOSTICREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ResetDevices',
    full_name='aimbot.AimbotService.ResetDevices',
    index=27,
    containing_service=None,
    input_type=_RESETDEVICESREQUEST,
    output_type=_RESETDEVICESREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ResetScanner',
    full_name='aimbot.AimbotService.ResetScanner',
    index=28,
    containing_service=None,
    input_type=_RESETSCANNERREQUEST,
    output_type=_RESETSCANNERREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartAutoCalibrateCrosshair',
    full_name='aimbot.AimbotService.StartAutoCalibrateCrosshair',
    index=29,
    containing_service=None,
    input_type=_SCANNERDESCRIPTOR,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartAutoCalibrateAllCrosshairs',
    full_name='aimbot.AimbotService.StartAutoCalibrateAllCrosshairs',
    index=30,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StopAutoCalibrate',
    full_name='aimbot.AimbotService.StopAutoCalibrate',
    index=31,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetCrosshairPosition',
    full_name='aimbot.AimbotService.SetCrosshairPosition',
    index=32,
    containing_service=None,
    input_type=_SCANNERTARGETPOSITION,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='MoveScanner',
    full_name='aimbot.AimbotService.MoveScanner',
    index=33,
    containing_service=None,
    input_type=_SCANNERTARGETPOSITION,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetAutoCalibrationProgress',
    full_name='aimbot.AimbotService.GetAutoCalibrationProgress',
    index=34,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_AUTOXHAIRCALIBRATIONPROGRESS,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetScannerStatus',
    full_name='aimbot.AimbotService.GetScannerStatus',
    index=35,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_SCANNERSTATUSREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTargetVelocity',
    full_name='aimbot.AimbotService.GetTargetVelocity',
    index=36,
    containing_service=None,
    input_type=_TARGETVELOCITYREQUEST,
    output_type=_TARGETVELOCITYREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTrackingState',
    full_name='aimbot.AimbotService.GetTrackingState',
    index=37,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_TRACKINGSTATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetBedtopHeightProfile',
    full_name='aimbot.AimbotService.GetBedtopHeightProfile',
    index=38,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_TRACKERBEDTOPHEIGHTPROFILE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDimensions',
    full_name='aimbot.AimbotService.GetDimensions',
    index=39,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_GETDIMENSIONSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTargetCamSN',
    full_name='aimbot.AimbotService.GetTargetCamSN',
    index=40,
    containing_service=None,
    input_type=_GETTARGETCAMSNREQUEST,
    output_type=_GETTARGETCAMSNRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReloadThinningConf',
    full_name='aimbot.AimbotService.ReloadThinningConf',
    index=41,
    containing_service=None,
    input_type=_RELOADTHINNINGCONFREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReloadAlmanacConf',
    full_name='aimbot.AimbotService.ReloadAlmanacConf',
    index=42,
    containing_service=None,
    input_type=_RELOADALMANACCONFREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReloadDiscriminatorConf',
    full_name='aimbot.AimbotService.ReloadDiscriminatorConf',
    index=43,
    containing_service=None,
    input_type=_RELOADDISCRIMINATORCONFREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReloadModelinatorConf',
    full_name='aimbot.AimbotService.ReloadModelinatorConf',
    index=44,
    containing_service=None,
    input_type=_RELOADMODELINATORCONFREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReloadTVEProfile',
    full_name='aimbot.AimbotService.ReloadTVEProfile',
    index=45,
    containing_service=None,
    input_type=_RELOADTVEPROFILEREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDistanceTrackedItems',
    full_name='aimbot.AimbotService.GetDistanceTrackedItems',
    index=46,
    containing_service=None,
    input_type=_TRACKEDITEMSREQUEST,
    output_type=_TRACKEDITEMSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetParticipation',
    full_name='aimbot.AimbotService.GetParticipation',
    index=47,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_PARTICIPATIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_AIMBOTSERVICE)

DESCRIPTOR.services_by_name['AimbotService'] = _AIMBOTSERVICE

# @@protoc_insertion_point(module_scope)

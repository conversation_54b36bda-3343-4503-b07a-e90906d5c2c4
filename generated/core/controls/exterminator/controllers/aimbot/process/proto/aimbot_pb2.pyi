"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)

from generated.weed_tracking.proto.weed_tracking_pb2 import (
    Detection as weed_tracking___proto___weed_tracking_pb2___Detection,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

SafetyOverrideStateValue = typing___NewType('SafetyOverrideStateValue', builtin___int)
type___SafetyOverrideStateValue = SafetyOverrideStateValue
SafetyOverrideState: _SafetyOverrideState
class _SafetyOverrideState(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[SafetyOverrideStateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    SafetyOverrideNone = typing___cast(SafetyOverrideStateValue, 0)
    SafetyOverrideVelocityStop = typing___cast(SafetyOverrideStateValue, 1)
SafetyOverrideNone = typing___cast(SafetyOverrideStateValue, 0)
SafetyOverrideVelocityStop = typing___cast(SafetyOverrideStateValue, 1)

class PingRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PingRequest = PingRequest

class PongReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PongReply = PongReply

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

class TargetingState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weeding_enabled: builtin___bool = ...
    thinning_enabled: builtin___bool = ...

    def __init__(self,
        *,
        weeding_enabled : typing___Optional[builtin___bool] = None,
        thinning_enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"thinning_enabled",b"thinning_enabled",u"weeding_enabled",b"weeding_enabled"]) -> None: ...
type___TargetingState = TargetingState

class AimbotState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    algorithm: typing___Text = ...
    running: builtin___bool = ...
    armed: builtin___bool = ...
    ready: builtin___bool = ...
    safety_override_state: type___SafetyOverrideStateValue = ...
    actuation_tasks_running: builtin___bool = ...

    @property
    def targeting_state(self) -> type___TargetingState: ...

    def __init__(self,
        *,
        algorithm : typing___Optional[typing___Text] = None,
        running : typing___Optional[builtin___bool] = None,
        armed : typing___Optional[builtin___bool] = None,
        targeting_state : typing___Optional[type___TargetingState] = None,
        ready : typing___Optional[builtin___bool] = None,
        safety_override_state : typing___Optional[type___SafetyOverrideStateValue] = None,
        actuation_tasks_running : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"targeting_state",b"targeting_state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"actuation_tasks_running",b"actuation_tasks_running",u"algorithm",b"algorithm",u"armed",b"armed",u"ready",b"ready",u"running",b"running",u"safety_override_state",b"safety_override_state",u"targeting_state",b"targeting_state"]) -> None: ...
type___AimbotState = AimbotState

class TargetVelocityRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___TargetVelocityRequest = TargetVelocityRequest

class TargetVelocityReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    velocity_min: builtin___float = ...
    velocity_max: builtin___float = ...

    def __init__(self,
        *,
        velocity_min : typing___Optional[builtin___float] = None,
        velocity_max : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"velocity_max",b"velocity_max",u"velocity_min",b"velocity_min"]) -> None: ...
type___TargetVelocityReply = TargetVelocityReply

class LaserTestActuationTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    duration_ms: builtin___int = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        duration_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"duration_ms",b"duration_ms",u"scanner_id",b"scanner_id"]) -> None: ...
type___LaserTestActuationTask = LaserTestActuationTask

class ImageDrawActuationTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    speed_mmps: builtin___float = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        speed_mmps : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner_id",b"scanner_id",u"speed_mmps",b"speed_mmps"]) -> None: ...
type___ImageDrawActuationTask = ImageDrawActuationTask

class RangeDrawActuationTask(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    duration_s: builtin___float = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        duration_s : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"duration_s",b"duration_s",u"scanner_id",b"scanner_id"]) -> None: ...
type___RangeDrawActuationTask = RangeDrawActuationTask

class ActuationTaskRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def laser_test(self) -> type___LaserTestActuationTask: ...

    @property
    def image_draw(self) -> type___ImageDrawActuationTask: ...

    @property
    def range_draw(self) -> type___RangeDrawActuationTask: ...

    def __init__(self,
        *,
        laser_test : typing___Optional[type___LaserTestActuationTask] = None,
        image_draw : typing___Optional[type___ImageDrawActuationTask] = None,
        range_draw : typing___Optional[type___RangeDrawActuationTask] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"image_draw",b"image_draw",u"laser_test",b"laser_test",u"range_draw",b"range_draw",u"task",b"task"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"image_draw",b"image_draw",u"laser_test",b"laser_test",u"range_draw",b"range_draw",u"task",b"task"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"task",b"task"]) -> typing_extensions___Literal["laser_test","image_draw","range_draw"]: ...
type___ActuationTaskRequest = ActuationTaskRequest

class LensSetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    value: builtin___int = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        value : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner_id",b"scanner_id",u"value",b"value"]) -> None: ...
type___LensSetRequest = LensSetRequest

class LensSetReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___LensSetReply = LensSetReply

class LensGetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner_id",b"scanner_id"]) -> None: ...
type___LensGetRequest = LensGetRequest

class LensGetReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    value: builtin___int = ...
    min_value: builtin___int = ...
    max_value: builtin___int = ...
    manual_autofocus_percent: builtin___float = ...
    manual_autofocusing: builtin___bool = ...
    scanner_id: builtin___int = ...

    def __init__(self,
        *,
        value : typing___Optional[builtin___int] = None,
        min_value : typing___Optional[builtin___int] = None,
        max_value : typing___Optional[builtin___int] = None,
        manual_autofocus_percent : typing___Optional[builtin___float] = None,
        manual_autofocusing : typing___Optional[builtin___bool] = None,
        scanner_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"manual_autofocus_percent",b"manual_autofocus_percent",u"manual_autofocusing",b"manual_autofocusing",u"max_value",b"max_value",u"min_value",b"min_value",u"scanner_id",b"scanner_id",u"value",b"value"]) -> None: ...
type___LensGetReply = LensGetReply

class LensGetAllReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def lens_status(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___LensGetReply]: ...

    def __init__(self,
        *,
        lens_status : typing___Optional[typing___Iterable[type___LensGetReply]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lens_status",b"lens_status"]) -> None: ...
type___LensGetAllReply = LensGetAllReply

class LensAutoFocusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner_id",b"scanner_id"]) -> None: ...
type___LensAutoFocusRequest = LensAutoFocusRequest

class LensAutoFocusReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___LensAutoFocusReply = LensAutoFocusReply

class StopLensAutoFocusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner_id",b"scanner_id"]) -> None: ...
type___StopLensAutoFocusRequest = StopLensAutoFocusRequest

class LaserArmRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    armed: builtin___bool = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        armed : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"armed",b"armed",u"scanner_id",b"scanner_id"]) -> None: ...
type___LaserArmRequest = LaserArmRequest

class LaserArmReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___LaserArmReply = LaserArmReply

class LaserEnableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"scanner_id",b"scanner_id"]) -> None: ...
type___LaserEnableRequest = LaserEnableRequest

class LaserEnableReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    status: builtin___bool = ...
    message: typing___Text = ...

    def __init__(self,
        *,
        status : typing___Optional[builtin___bool] = None,
        message : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"message",b"message",u"status",b"status"]) -> None: ...
type___LaserEnableReply = LaserEnableReply

class LaserFireRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    fire: builtin___bool = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        fire : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"fire",b"fire",u"scanner_id",b"scanner_id"]) -> None: ...
type___LaserFireRequest = LaserFireRequest

class LaserFireReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___LaserFireReply = LaserFireReply

class LaserSetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    on: builtin___bool = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        on : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"on",b"on",u"scanner_id",b"scanner_id"]) -> None: ...
type___LaserSetRequest = LaserSetRequest

class LaserSetReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___LaserSetReply = LaserSetReply

class BurnIdividualImagesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    speed_mmps: builtin___float = ...
    intensity: builtin___float = ...
    json_img: typing___Text = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[typing___Iterable[builtin___int]] = None,
        speed_mmps : typing___Optional[builtin___float] = None,
        intensity : typing___Optional[builtin___float] = None,
        json_img : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"intensity",b"intensity",u"json_img",b"json_img",u"scanner_id",b"scanner_id",u"speed_mmps",b"speed_mmps"]) -> None: ...
type___BurnIdividualImagesRequest = BurnIdividualImagesRequest

class LaserState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...
    firing: builtin___bool = ...
    error: builtin___bool = ...
    error_code: typing___Text = ...
    error_message: typing___Text = ...
    power: builtin___bool = ...
    delta_temp: builtin___float = ...
    current: builtin___float = ...
    arced: builtin___bool = ...
    power_level: builtin___float = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        firing : typing___Optional[builtin___bool] = None,
        error : typing___Optional[builtin___bool] = None,
        error_code : typing___Optional[typing___Text] = None,
        error_message : typing___Optional[typing___Text] = None,
        power : typing___Optional[builtin___bool] = None,
        delta_temp : typing___Optional[builtin___float] = None,
        current : typing___Optional[builtin___float] = None,
        arced : typing___Optional[builtin___bool] = None,
        power_level : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"arced",b"arced",u"current",b"current",u"delta_temp",b"delta_temp",u"enabled",b"enabled",u"error",b"error",u"error_code",b"error_code",u"error_message",b"error_message",u"firing",b"firing",u"power",b"power",u"power_level",b"power_level"]) -> None: ...
type___LaserState = LaserState

class ServoGoToRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ServoTypeValue = typing___NewType('ServoTypeValue', builtin___int)
    type___ServoTypeValue = ServoTypeValue
    ServoType: _ServoType
    class _ServoType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ServoGoToRequest.ServoTypeValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        PAN = typing___cast(ServoGoToRequest.ServoTypeValue, 0)
        TILT = typing___cast(ServoGoToRequest.ServoTypeValue, 1)
    PAN = typing___cast(ServoGoToRequest.ServoTypeValue, 0)
    TILT = typing___cast(ServoGoToRequest.ServoTypeValue, 1)

    scanner_id: builtin___int = ...
    servo_type: type___ServoGoToRequest.ServoTypeValue = ...
    position: builtin___int = ...
    time_ms: builtin___int = ...
    await_settle: builtin___bool = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        servo_type : typing___Optional[type___ServoGoToRequest.ServoTypeValue] = None,
        position : typing___Optional[builtin___int] = None,
        time_ms : typing___Optional[builtin___int] = None,
        await_settle : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"await_settle",b"await_settle",u"position",b"position",u"scanner_id",b"scanner_id",u"servo_type",b"servo_type",u"time_ms",b"time_ms"]) -> None: ...
type___ServoGoToRequest = ServoGoToRequest

class ServoGoToReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ServoGoToReply = ServoGoToReply

class ServoGetPosVelRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner_id",b"scanner_id"]) -> None: ...
type___ServoGetPosVelRequest = ServoGetPosVelRequest

class ServoGetPosVelReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pan_position: builtin___int = ...
    tilt_position: builtin___int = ...
    pan_velocity: builtin___int = ...
    tilt_velocity: builtin___int = ...

    def __init__(self,
        *,
        pan_position : typing___Optional[builtin___int] = None,
        tilt_position : typing___Optional[builtin___int] = None,
        pan_velocity : typing___Optional[builtin___int] = None,
        tilt_velocity : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan_position",b"pan_position",u"pan_velocity",b"pan_velocity",u"tilt_position",b"tilt_position",u"tilt_velocity",b"tilt_velocity"]) -> None: ...
type___ServoGetPosVelReply = ServoGetPosVelReply

class ServoGetLimitsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner_id",b"scanner_id"]) -> None: ...
type___ServoGetLimitsRequest = ServoGetLimitsRequest

class ServoGetLimitsReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pan_min: builtin___int = ...
    pan_max: builtin___int = ...
    tilt_min: builtin___int = ...
    tilt_max: builtin___int = ...

    def __init__(self,
        *,
        pan_min : typing___Optional[builtin___int] = None,
        pan_max : typing___Optional[builtin___int] = None,
        tilt_min : typing___Optional[builtin___int] = None,
        tilt_max : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan_max",b"pan_max",u"pan_min",b"pan_min",u"tilt_max",b"tilt_max",u"tilt_min",b"tilt_min"]) -> None: ...
type___ServoGetLimitsReply = ServoGetLimitsReply

class TuningParam(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    v_uint: builtin___int = ...
    v_int: builtin___int = ...
    v_bool: builtin___bool = ...
    v_float: builtin___float = ...
    v_string: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        v_uint : typing___Optional[builtin___int] = None,
        v_int : typing___Optional[builtin___int] = None,
        v_bool : typing___Optional[builtin___bool] = None,
        v_float : typing___Optional[builtin___float] = None,
        v_string : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"v_bool",b"v_bool",u"v_float",b"v_float",u"v_int",b"v_int",u"v_string",b"v_string",u"v_uint",b"v_uint",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"v_bool",b"v_bool",u"v_float",b"v_float",u"v_int",b"v_int",u"v_string",b"v_string",u"v_uint",b"v_uint",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["v_uint","v_int","v_bool","v_float","v_string"]: ...
type___TuningParam = TuningParam

class TuningParamsUpdateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def params(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TuningParam]: ...

    def __init__(self,
        *,
        params : typing___Optional[typing___Iterable[type___TuningParam]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"params",b"params"]) -> None: ...
type___TuningParamsUpdateRequest = TuningParamsUpdateRequest

class TuningParamsUpdateReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___TuningParamsUpdateReply = TuningParamsUpdateReply

class TuningParamsGetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name"]) -> None: ...
type___TuningParamsGetRequest = TuningParamsGetRequest

class TuningParamsGetReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def params(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TuningParam]: ...

    def __init__(self,
        *,
        params : typing___Optional[typing___Iterable[type___TuningParam]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"params",b"params"]) -> None: ...
type___TuningParamsGetReply = TuningParamsGetReply

class GetLoadEstimateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetLoadEstimateRequest = GetLoadEstimateRequest

class GetLoadEstimateReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current_load: builtin___float = ...
    target_load: builtin___float = ...

    def __init__(self,
        *,
        current_load : typing___Optional[builtin___float] = None,
        target_load : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_load",b"current_load",u"target_load",b"target_load"]) -> None: ...
type___GetLoadEstimateReply = GetLoadEstimateReply

class GetDiagnosticRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetDiagnosticRequest = GetDiagnosticRequest

class GetDiagnosticReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    diagnostic: typing___Text = ...

    def __init__(self,
        *,
        diagnostic : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"diagnostic",b"diagnostic"]) -> None: ...
type___GetDiagnosticReply = GetDiagnosticReply

class ResetDevicesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    device_id: typing___Text = ...

    def __init__(self,
        *,
        device_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"device_id",b"device_id"]) -> None: ...
type___ResetDevicesRequest = ResetDevicesRequest

class ResetDevicesReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ResetDevicesReply = ResetDevicesReply

class ResetScannerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...
    metrics_only: builtin___bool = ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        metrics_only : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"metrics_only",b"metrics_only",u"scanner_id",b"scanner_id"]) -> None: ...
type___ResetScannerRequest = ResetScannerRequest

class ResetScannerReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ResetScannerReply = ResetScannerReply

class ScannerDescriptor(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___ScannerDescriptor = ScannerDescriptor

class ScannerTargetPosition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...
    y: builtin___int = ...

    @property
    def scanner_descriptor(self) -> type___ScannerDescriptor: ...

    def __init__(self,
        *,
        scanner_descriptor : typing___Optional[type___ScannerDescriptor] = None,
        x : typing___Optional[builtin___int] = None,
        y : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"scanner_descriptor",b"scanner_descriptor"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner_descriptor",b"scanner_descriptor",u"x",b"x",u"y",b"y"]) -> None: ...
type___ScannerTargetPosition = ScannerTargetPosition

class CrosshairState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...
    y: builtin___int = ...
    calibrating: builtin___bool = ...
    calibration_failed: builtin___bool = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        y : typing___Optional[builtin___int] = None,
        calibrating : typing___Optional[builtin___bool] = None,
        calibration_failed : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"calibrating",b"calibrating",u"calibration_failed",b"calibration_failed",u"x",b"x",u"y",b"y"]) -> None: ...
type___CrosshairState = CrosshairState

class ScannerState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_error: builtin___bool = ...
    error_code: typing___Text = ...
    error_message: typing___Text = ...
    target_trajectory_id: builtin___int = ...
    pan_failure: builtin___bool = ...
    tilt_failure: builtin___bool = ...

    @property
    def scanner_descriptor(self) -> type___ScannerDescriptor: ...

    @property
    def laser_state(self) -> type___LaserState: ...

    @property
    def crosshair_state(self) -> type___CrosshairState: ...

    def __init__(self,
        *,
        scanner_descriptor : typing___Optional[type___ScannerDescriptor] = None,
        laser_state : typing___Optional[type___LaserState] = None,
        crosshair_state : typing___Optional[type___CrosshairState] = None,
        scanner_error : typing___Optional[builtin___bool] = None,
        error_code : typing___Optional[typing___Text] = None,
        error_message : typing___Optional[typing___Text] = None,
        target_trajectory_id : typing___Optional[builtin___int] = None,
        pan_failure : typing___Optional[builtin___bool] = None,
        tilt_failure : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"crosshair_state",b"crosshair_state",u"laser_state",b"laser_state",u"scanner_descriptor",b"scanner_descriptor"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crosshair_state",b"crosshair_state",u"error_code",b"error_code",u"error_message",b"error_message",u"laser_state",b"laser_state",u"pan_failure",b"pan_failure",u"scanner_descriptor",b"scanner_descriptor",u"scanner_error",b"scanner_error",u"target_trajectory_id",b"target_trajectory_id",u"tilt_failure",b"tilt_failure"]) -> None: ...
type___ScannerState = ScannerState

class AutoXHairCalibrationProgress(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    in_progress: builtin___bool = ...
    progress: builtin___float = ...

    def __init__(self,
        *,
        in_progress : typing___Optional[builtin___bool] = None,
        progress : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"in_progress",b"in_progress",u"progress",b"progress"]) -> None: ...
type___AutoXHairCalibrationProgress = AutoXHairCalibrationProgress

class ScannerStatusReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def states(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ScannerState]: ...

    @property
    def x_hair_progress(self) -> type___AutoXHairCalibrationProgress: ...

    def __init__(self,
        *,
        states : typing___Optional[typing___Iterable[type___ScannerState]] = None,
        x_hair_progress : typing___Optional[type___AutoXHairCalibrationProgress] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"x_hair_progress",b"x_hair_progress"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"states",b"states",u"x_hair_progress",b"x_hair_progress"]) -> None: ...
type___ScannerStatusReply = ScannerStatusReply

class BootedReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    booted: builtin___bool = ...

    def __init__(self,
        *,
        booted : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"booted",b"booted"]) -> None: ...
type___BootedReply = BootedReply

class TrackerState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...
    at_weed_limit: builtin___bool = ...
    rotary_timeout: builtin___bool = ...
    deepweed_error: builtin___bool = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        at_weed_limit : typing___Optional[builtin___bool] = None,
        rotary_timeout : typing___Optional[builtin___bool] = None,
        deepweed_error : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"at_weed_limit",b"at_weed_limit",u"deepweed_error",b"deepweed_error",u"id",b"id",u"rotary_timeout",b"rotary_timeout"]) -> None: ...
type___TrackerState = TrackerState

class SchedulerState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    over_capacity: builtin___bool = ...

    def __init__(self,
        *,
        over_capacity : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"over_capacity",b"over_capacity"]) -> None: ...
type___SchedulerState = SchedulerState

class TrackingState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def states(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TrackerState]: ...

    @property
    def scheduler_state(self) -> type___SchedulerState: ...

    def __init__(self,
        *,
        states : typing___Optional[typing___Iterable[type___TrackerState]] = None,
        scheduler_state : typing___Optional[type___SchedulerState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"scheduler_state",b"scheduler_state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scheduler_state",b"scheduler_state",u"states",b"states"]) -> None: ...
type___TrackingState = TrackingState

class BedtopHeightProfile(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weed_height_columns: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    crop_height_columns: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    pcam_id: typing___Text = ...

    def __init__(self,
        *,
        weed_height_columns : typing___Optional[typing___Iterable[builtin___float]] = None,
        crop_height_columns : typing___Optional[typing___Iterable[builtin___float]] = None,
        pcam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_height_columns",b"crop_height_columns",u"pcam_id",b"pcam_id",u"weed_height_columns",b"weed_height_columns"]) -> None: ...
type___BedtopHeightProfile = BedtopHeightProfile

class TrackerBedtopHeightProfile(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    bbh_offset_mm: builtin___float = ...

    @property
    def profiles(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___BedtopHeightProfile]: ...

    def __init__(self,
        *,
        profiles : typing___Optional[typing___Iterable[type___BedtopHeightProfile]] = None,
        bbh_offset_mm : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bbh_offset_mm",b"bbh_offset_mm",u"profiles",b"profiles"]) -> None: ...
type___TrackerBedtopHeightProfile = TrackerBedtopHeightProfile

class GetDimensionsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    min_x_mm: builtin___float = ...
    max_x_mm: builtin___float = ...
    min_y_mm: builtin___float = ...
    max_y_mm: builtin___float = ...
    center_x_mm: builtin___float = ...

    def __init__(self,
        *,
        min_x_mm : typing___Optional[builtin___float] = None,
        max_x_mm : typing___Optional[builtin___float] = None,
        min_y_mm : typing___Optional[builtin___float] = None,
        max_y_mm : typing___Optional[builtin___float] = None,
        center_x_mm : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_x_mm",b"center_x_mm",u"max_x_mm",b"max_x_mm",u"max_y_mm",b"max_y_mm",u"min_x_mm",b"min_x_mm",u"min_y_mm",b"min_y_mm"]) -> None: ...
type___GetDimensionsResponse = GetDimensionsResponse

class GetTargetCamSNRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    camera_id: typing___Text = ...

    def __init__(self,
        *,
        camera_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"camera_id",b"camera_id"]) -> None: ...
type___GetTargetCamSNRequest = GetTargetCamSNRequest

class GetTargetCamSNResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    serial_number: typing___Text = ...

    def __init__(self,
        *,
        serial_number : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"serial_number",b"serial_number"]) -> None: ...
type___GetTargetCamSNResponse = GetTargetCamSNResponse

class ReloadThinningConfRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ReloadThinningConfRequest = ReloadThinningConfRequest

class ReloadAlmanacConfRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ReloadAlmanacConfRequest = ReloadAlmanacConfRequest

class ReloadDiscriminatorConfRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ReloadDiscriminatorConfRequest = ReloadDiscriminatorConfRequest

class ReloadModelinatorConfRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ReloadModelinatorConfRequest = ReloadModelinatorConfRequest

class ReloadTVEProfileRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ReloadTVEProfileRequest = ReloadTVEProfileRequest

class FixLaserMetricsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    total_fire_count: builtin___int = ...
    total_fire_time_ms: builtin___int = ...

    @property
    def scanner(self) -> type___ScannerDescriptor: ...

    def __init__(self,
        *,
        scanner : typing___Optional[type___ScannerDescriptor] = None,
        total_fire_count : typing___Optional[builtin___int] = None,
        total_fire_time_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"scanner",b"scanner"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"scanner",b"scanner",u"total_fire_count",b"total_fire_count",u"total_fire_time_ms",b"total_fire_time_ms"]) -> None: ...
type___FixLaserMetricsRequest = FixLaserMetricsRequest

class TrackedItemsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id"]) -> None: ...
type___TrackedItemsRequest = TrackedItemsRequest

class TrackedItemHistory(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    @property
    def detection(self) -> weed_tracking___proto___weed_tracking_pb2___Detection: ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        detection : typing___Optional[weed_tracking___proto___weed_tracking_pb2___Detection] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"detection",b"detection"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"detection",b"detection",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___TrackedItemHistory = TrackedItemHistory

class TrackedItem(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...

    @property
    def history(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TrackedItemHistory]: ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        history : typing___Optional[typing___Iterable[type___TrackedItemHistory]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"history",b"history",u"id",b"id"]) -> None: ...
type___TrackedItem = TrackedItem

class TrackedItemsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def tracked_items(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TrackedItem]: ...

    def __init__(self,
        *,
        tracked_items : typing___Optional[typing___Iterable[type___TrackedItem]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"tracked_items",b"tracked_items"]) -> None: ...
type___TrackedItemsResponse = TrackedItemsResponse

class ParticipationResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    running_as_leader: builtin___bool = ...

    def __init__(self,
        *,
        running_as_leader : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"running_as_leader",b"running_as_leader"]) -> None: ...
type___ParticipationResponse = ParticipationResponse

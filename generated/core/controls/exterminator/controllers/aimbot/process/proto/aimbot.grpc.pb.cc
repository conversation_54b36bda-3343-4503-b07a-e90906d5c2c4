// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: core/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto

#include "core/controls/exterminator/controllers/aimbot/process/proto/aimbot.pb.h"
#include "core/controls/exterminator/controllers/aimbot/process/proto/aimbot.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace aimbot {

static const char* AimbotService_method_names[] = {
  "/aimbot.AimbotService/Ping",
  "/aimbot.AimbotService/GetBooted",
  "/aimbot.AimbotService/ArmLasers",
  "/aimbot.AimbotService/DisarmLasers",
  "/aimbot.AimbotService/GetAimbotState",
  "/aimbot.AimbotService/SetTargetingState",
  "/aimbot.AimbotService/StartActuationTask",
  "/aimbot.AimbotService/CancelActuationTask",
  "/aimbot.AimbotService/LensSet",
  "/aimbot.AimbotService/LensGet",
  "/aimbot.AimbotService/LensGetAll",
  "/aimbot.AimbotService/LensAutoFocus",
  "/aimbot.AimbotService/StopLensAutoFocus",
  "/aimbot.AimbotService/LaserArm",
  "/aimbot.AimbotService/LaserSet",
  "/aimbot.AimbotService/LaserEnable",
  "/aimbot.AimbotService/LaserFire",
  "/aimbot.AimbotService/ResetLaserMetrics",
  "/aimbot.AimbotService/FixLaserMetrics",
  "/aimbot.AimbotService/BurnIdividualImage",
  "/aimbot.AimbotService/ServoGoTo",
  "/aimbot.AimbotService/ServoGetPosVel",
  "/aimbot.AimbotService/ServoGetLimits",
  "/aimbot.AimbotService/TuningParamsUpdate",
  "/aimbot.AimbotService/TuningParamsGet",
  "/aimbot.AimbotService/GetLoadEstimate",
  "/aimbot.AimbotService/GetDiagnostic",
  "/aimbot.AimbotService/ResetDevices",
  "/aimbot.AimbotService/ResetScanner",
  "/aimbot.AimbotService/StartAutoCalibrateCrosshair",
  "/aimbot.AimbotService/StartAutoCalibrateAllCrosshairs",
  "/aimbot.AimbotService/StopAutoCalibrate",
  "/aimbot.AimbotService/SetCrosshairPosition",
  "/aimbot.AimbotService/MoveScanner",
  "/aimbot.AimbotService/GetAutoCalibrationProgress",
  "/aimbot.AimbotService/GetScannerStatus",
  "/aimbot.AimbotService/GetTargetVelocity",
  "/aimbot.AimbotService/GetTrackingState",
  "/aimbot.AimbotService/GetBedtopHeightProfile",
  "/aimbot.AimbotService/GetDimensions",
  "/aimbot.AimbotService/GetTargetCamSN",
  "/aimbot.AimbotService/ReloadThinningConf",
  "/aimbot.AimbotService/ReloadAlmanacConf",
  "/aimbot.AimbotService/ReloadDiscriminatorConf",
  "/aimbot.AimbotService/ReloadModelinatorConf",
  "/aimbot.AimbotService/ReloadTVEProfile",
  "/aimbot.AimbotService/GetDistanceTrackedItems",
  "/aimbot.AimbotService/GetParticipation",
};

std::unique_ptr< AimbotService::Stub> AimbotService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< AimbotService::Stub> stub(new AimbotService::Stub(channel, options));
  return stub;
}

AimbotService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Ping_(AimbotService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetBooted_(AimbotService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ArmLasers_(AimbotService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DisarmLasers_(AimbotService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetAimbotState_(AimbotService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetTargetingState_(AimbotService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartActuationTask_(AimbotService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CancelActuationTask_(AimbotService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LensSet_(AimbotService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LensGet_(AimbotService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LensGetAll_(AimbotService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LensAutoFocus_(AimbotService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopLensAutoFocus_(AimbotService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LaserArm_(AimbotService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LaserSet_(AimbotService_method_names[14], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LaserEnable_(AimbotService_method_names[15], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LaserFire_(AimbotService_method_names[16], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResetLaserMetrics_(AimbotService_method_names[17], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_FixLaserMetrics_(AimbotService_method_names[18], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_BurnIdividualImage_(AimbotService_method_names[19], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ServoGoTo_(AimbotService_method_names[20], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ServoGetPosVel_(AimbotService_method_names[21], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ServoGetLimits_(AimbotService_method_names[22], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_TuningParamsUpdate_(AimbotService_method_names[23], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_TuningParamsGet_(AimbotService_method_names[24], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetLoadEstimate_(AimbotService_method_names[25], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDiagnostic_(AimbotService_method_names[26], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResetDevices_(AimbotService_method_names[27], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResetScanner_(AimbotService_method_names[28], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartAutoCalibrateCrosshair_(AimbotService_method_names[29], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartAutoCalibrateAllCrosshairs_(AimbotService_method_names[30], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopAutoCalibrate_(AimbotService_method_names[31], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetCrosshairPosition_(AimbotService_method_names[32], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_MoveScanner_(AimbotService_method_names[33], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetAutoCalibrationProgress_(AimbotService_method_names[34], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetScannerStatus_(AimbotService_method_names[35], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTargetVelocity_(AimbotService_method_names[36], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTrackingState_(AimbotService_method_names[37], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetBedtopHeightProfile_(AimbotService_method_names[38], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDimensions_(AimbotService_method_names[39], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTargetCamSN_(AimbotService_method_names[40], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReloadThinningConf_(AimbotService_method_names[41], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReloadAlmanacConf_(AimbotService_method_names[42], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReloadDiscriminatorConf_(AimbotService_method_names[43], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReloadModelinatorConf_(AimbotService_method_names[44], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReloadTVEProfile_(AimbotService_method_names[45], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDistanceTrackedItems_(AimbotService_method_names[46], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetParticipation_(AimbotService_method_names[47], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status AimbotService::Stub::Ping(::grpc::ClientContext* context, const ::aimbot::PingRequest& request, ::aimbot::PongReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::PingRequest, ::aimbot::PongReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Ping_, context, request, response);
}

void AimbotService::Stub::async::Ping(::grpc::ClientContext* context, const ::aimbot::PingRequest* request, ::aimbot::PongReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::PingRequest, ::aimbot::PongReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::Ping(::grpc::ClientContext* context, const ::aimbot::PingRequest* request, ::aimbot::PongReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::PongReply>* AimbotService::Stub::PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::aimbot::PingRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::PongReply, ::aimbot::PingRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Ping_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::PongReply>* AimbotService::Stub::AsyncPingRaw(::grpc::ClientContext* context, const ::aimbot::PingRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetBooted(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::BootedReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::BootedReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetBooted_, context, request, response);
}

void AimbotService::Stub::async::GetBooted(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::BootedReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::BootedReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBooted_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetBooted(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::BootedReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBooted_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::BootedReply>* AimbotService::Stub::PrepareAsyncGetBootedRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::BootedReply, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetBooted_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::BootedReply>* AimbotService::Stub::AsyncGetBootedRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetBootedRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ArmLasers(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ArmLasers_, context, request, response);
}

void AimbotService::Stub::async::ArmLasers(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ArmLasers_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ArmLasers(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ArmLasers_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncArmLasersRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ArmLasers_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncArmLasersRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncArmLasersRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::DisarmLasers(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DisarmLasers_, context, request, response);
}

void AimbotService::Stub::async::DisarmLasers(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DisarmLasers_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::DisarmLasers(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DisarmLasers_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncDisarmLasersRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DisarmLasers_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncDisarmLasersRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDisarmLasersRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetAimbotState(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::AimbotState* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::AimbotState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetAimbotState_, context, request, response);
}

void AimbotService::Stub::async::GetAimbotState(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::AimbotState* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::AimbotState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetAimbotState_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetAimbotState(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::AimbotState* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetAimbotState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::AimbotState>* AimbotService::Stub::PrepareAsyncGetAimbotStateRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::AimbotState, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetAimbotState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::AimbotState>* AimbotService::Stub::AsyncGetAimbotStateRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetAimbotStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::SetTargetingState(::grpc::ClientContext* context, const ::aimbot::TargetingState& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::TargetingState, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetTargetingState_, context, request, response);
}

void AimbotService::Stub::async::SetTargetingState(::grpc::ClientContext* context, const ::aimbot::TargetingState* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::TargetingState, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTargetingState_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::SetTargetingState(::grpc::ClientContext* context, const ::aimbot::TargetingState* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTargetingState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::aimbot::TargetingState& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::TargetingState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetTargetingState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::aimbot::TargetingState& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetTargetingStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::StartActuationTask(::grpc::ClientContext* context, const ::aimbot::ActuationTaskRequest& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ActuationTaskRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartActuationTask_, context, request, response);
}

void AimbotService::Stub::async::StartActuationTask(::grpc::ClientContext* context, const ::aimbot::ActuationTaskRequest* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ActuationTaskRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartActuationTask_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::StartActuationTask(::grpc::ClientContext* context, const ::aimbot::ActuationTaskRequest* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartActuationTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncStartActuationTaskRaw(::grpc::ClientContext* context, const ::aimbot::ActuationTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ActuationTaskRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartActuationTask_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncStartActuationTaskRaw(::grpc::ClientContext* context, const ::aimbot::ActuationTaskRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartActuationTaskRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::CancelActuationTask(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CancelActuationTask_, context, request, response);
}

void AimbotService::Stub::async::CancelActuationTask(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelActuationTask_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::CancelActuationTask(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelActuationTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncCancelActuationTaskRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CancelActuationTask_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncCancelActuationTaskRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCancelActuationTaskRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::LensSet(::grpc::ClientContext* context, const ::aimbot::LensSetRequest& request, ::aimbot::LensSetReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::LensSetRequest, ::aimbot::LensSetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LensSet_, context, request, response);
}

void AimbotService::Stub::async::LensSet(::grpc::ClientContext* context, const ::aimbot::LensSetRequest* request, ::aimbot::LensSetReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::LensSetRequest, ::aimbot::LensSetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LensSet_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::LensSet(::grpc::ClientContext* context, const ::aimbot::LensSetRequest* request, ::aimbot::LensSetReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LensSet_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LensSetReply>* AimbotService::Stub::PrepareAsyncLensSetRaw(::grpc::ClientContext* context, const ::aimbot::LensSetRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::LensSetReply, ::aimbot::LensSetRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LensSet_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LensSetReply>* AimbotService::Stub::AsyncLensSetRaw(::grpc::ClientContext* context, const ::aimbot::LensSetRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLensSetRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::LensGet(::grpc::ClientContext* context, const ::aimbot::LensGetRequest& request, ::aimbot::LensGetReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::LensGetRequest, ::aimbot::LensGetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LensGet_, context, request, response);
}

void AimbotService::Stub::async::LensGet(::grpc::ClientContext* context, const ::aimbot::LensGetRequest* request, ::aimbot::LensGetReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::LensGetRequest, ::aimbot::LensGetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LensGet_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::LensGet(::grpc::ClientContext* context, const ::aimbot::LensGetRequest* request, ::aimbot::LensGetReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LensGet_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LensGetReply>* AimbotService::Stub::PrepareAsyncLensGetRaw(::grpc::ClientContext* context, const ::aimbot::LensGetRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::LensGetReply, ::aimbot::LensGetRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LensGet_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LensGetReply>* AimbotService::Stub::AsyncLensGetRaw(::grpc::ClientContext* context, const ::aimbot::LensGetRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLensGetRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::LensGetAll(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::LensGetAllReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::LensGetAllReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LensGetAll_, context, request, response);
}

void AimbotService::Stub::async::LensGetAll(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::LensGetAllReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::LensGetAllReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LensGetAll_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::LensGetAll(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::LensGetAllReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LensGetAll_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LensGetAllReply>* AimbotService::Stub::PrepareAsyncLensGetAllRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::LensGetAllReply, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LensGetAll_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LensGetAllReply>* AimbotService::Stub::AsyncLensGetAllRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLensGetAllRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::LensAutoFocus(::grpc::ClientContext* context, const ::aimbot::LensAutoFocusRequest& request, ::aimbot::LensAutoFocusReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::LensAutoFocusRequest, ::aimbot::LensAutoFocusReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LensAutoFocus_, context, request, response);
}

void AimbotService::Stub::async::LensAutoFocus(::grpc::ClientContext* context, const ::aimbot::LensAutoFocusRequest* request, ::aimbot::LensAutoFocusReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::LensAutoFocusRequest, ::aimbot::LensAutoFocusReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LensAutoFocus_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::LensAutoFocus(::grpc::ClientContext* context, const ::aimbot::LensAutoFocusRequest* request, ::aimbot::LensAutoFocusReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LensAutoFocus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LensAutoFocusReply>* AimbotService::Stub::PrepareAsyncLensAutoFocusRaw(::grpc::ClientContext* context, const ::aimbot::LensAutoFocusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::LensAutoFocusReply, ::aimbot::LensAutoFocusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LensAutoFocus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LensAutoFocusReply>* AimbotService::Stub::AsyncLensAutoFocusRaw(::grpc::ClientContext* context, const ::aimbot::LensAutoFocusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLensAutoFocusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::StopLensAutoFocus(::grpc::ClientContext* context, const ::aimbot::StopLensAutoFocusRequest& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::StopLensAutoFocusRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopLensAutoFocus_, context, request, response);
}

void AimbotService::Stub::async::StopLensAutoFocus(::grpc::ClientContext* context, const ::aimbot::StopLensAutoFocusRequest* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::StopLensAutoFocusRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopLensAutoFocus_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::StopLensAutoFocus(::grpc::ClientContext* context, const ::aimbot::StopLensAutoFocusRequest* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopLensAutoFocus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncStopLensAutoFocusRaw(::grpc::ClientContext* context, const ::aimbot::StopLensAutoFocusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::StopLensAutoFocusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopLensAutoFocus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncStopLensAutoFocusRaw(::grpc::ClientContext* context, const ::aimbot::StopLensAutoFocusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopLensAutoFocusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::LaserArm(::grpc::ClientContext* context, const ::aimbot::LaserArmRequest& request, ::aimbot::LaserArmReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::LaserArmRequest, ::aimbot::LaserArmReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LaserArm_, context, request, response);
}

void AimbotService::Stub::async::LaserArm(::grpc::ClientContext* context, const ::aimbot::LaserArmRequest* request, ::aimbot::LaserArmReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::LaserArmRequest, ::aimbot::LaserArmReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LaserArm_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::LaserArm(::grpc::ClientContext* context, const ::aimbot::LaserArmRequest* request, ::aimbot::LaserArmReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LaserArm_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LaserArmReply>* AimbotService::Stub::PrepareAsyncLaserArmRaw(::grpc::ClientContext* context, const ::aimbot::LaserArmRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::LaserArmReply, ::aimbot::LaserArmRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LaserArm_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LaserArmReply>* AimbotService::Stub::AsyncLaserArmRaw(::grpc::ClientContext* context, const ::aimbot::LaserArmRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLaserArmRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::LaserSet(::grpc::ClientContext* context, const ::aimbot::LaserSetRequest& request, ::aimbot::LaserSetReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::LaserSetRequest, ::aimbot::LaserSetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LaserSet_, context, request, response);
}

void AimbotService::Stub::async::LaserSet(::grpc::ClientContext* context, const ::aimbot::LaserSetRequest* request, ::aimbot::LaserSetReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::LaserSetRequest, ::aimbot::LaserSetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LaserSet_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::LaserSet(::grpc::ClientContext* context, const ::aimbot::LaserSetRequest* request, ::aimbot::LaserSetReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LaserSet_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LaserSetReply>* AimbotService::Stub::PrepareAsyncLaserSetRaw(::grpc::ClientContext* context, const ::aimbot::LaserSetRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::LaserSetReply, ::aimbot::LaserSetRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LaserSet_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LaserSetReply>* AimbotService::Stub::AsyncLaserSetRaw(::grpc::ClientContext* context, const ::aimbot::LaserSetRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLaserSetRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::LaserEnable(::grpc::ClientContext* context, const ::aimbot::LaserEnableRequest& request, ::aimbot::LaserEnableReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::LaserEnableRequest, ::aimbot::LaserEnableReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LaserEnable_, context, request, response);
}

void AimbotService::Stub::async::LaserEnable(::grpc::ClientContext* context, const ::aimbot::LaserEnableRequest* request, ::aimbot::LaserEnableReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::LaserEnableRequest, ::aimbot::LaserEnableReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LaserEnable_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::LaserEnable(::grpc::ClientContext* context, const ::aimbot::LaserEnableRequest* request, ::aimbot::LaserEnableReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LaserEnable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LaserEnableReply>* AimbotService::Stub::PrepareAsyncLaserEnableRaw(::grpc::ClientContext* context, const ::aimbot::LaserEnableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::LaserEnableReply, ::aimbot::LaserEnableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LaserEnable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LaserEnableReply>* AimbotService::Stub::AsyncLaserEnableRaw(::grpc::ClientContext* context, const ::aimbot::LaserEnableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLaserEnableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::LaserFire(::grpc::ClientContext* context, const ::aimbot::LaserFireRequest& request, ::aimbot::LaserFireReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::LaserFireRequest, ::aimbot::LaserFireReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LaserFire_, context, request, response);
}

void AimbotService::Stub::async::LaserFire(::grpc::ClientContext* context, const ::aimbot::LaserFireRequest* request, ::aimbot::LaserFireReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::LaserFireRequest, ::aimbot::LaserFireReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LaserFire_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::LaserFire(::grpc::ClientContext* context, const ::aimbot::LaserFireRequest* request, ::aimbot::LaserFireReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LaserFire_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LaserFireReply>* AimbotService::Stub::PrepareAsyncLaserFireRaw(::grpc::ClientContext* context, const ::aimbot::LaserFireRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::LaserFireReply, ::aimbot::LaserFireRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LaserFire_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::LaserFireReply>* AimbotService::Stub::AsyncLaserFireRaw(::grpc::ClientContext* context, const ::aimbot::LaserFireRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLaserFireRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ResetLaserMetrics(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ScannerDescriptor, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ResetLaserMetrics_, context, request, response);
}

void AimbotService::Stub::async::ResetLaserMetrics(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ScannerDescriptor, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetLaserMetrics_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ResetLaserMetrics(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetLaserMetrics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncResetLaserMetricsRaw(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ScannerDescriptor, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ResetLaserMetrics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncResetLaserMetricsRaw(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResetLaserMetricsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::FixLaserMetrics(::grpc::ClientContext* context, const ::aimbot::FixLaserMetricsRequest& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::FixLaserMetricsRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_FixLaserMetrics_, context, request, response);
}

void AimbotService::Stub::async::FixLaserMetrics(::grpc::ClientContext* context, const ::aimbot::FixLaserMetricsRequest* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::FixLaserMetricsRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FixLaserMetrics_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::FixLaserMetrics(::grpc::ClientContext* context, const ::aimbot::FixLaserMetricsRequest* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FixLaserMetrics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncFixLaserMetricsRaw(::grpc::ClientContext* context, const ::aimbot::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::FixLaserMetricsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_FixLaserMetrics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncFixLaserMetricsRaw(::grpc::ClientContext* context, const ::aimbot::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncFixLaserMetricsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::BurnIdividualImage(::grpc::ClientContext* context, const ::aimbot::BurnIdividualImagesRequest& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::BurnIdividualImagesRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_BurnIdividualImage_, context, request, response);
}

void AimbotService::Stub::async::BurnIdividualImage(::grpc::ClientContext* context, const ::aimbot::BurnIdividualImagesRequest* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::BurnIdividualImagesRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_BurnIdividualImage_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::BurnIdividualImage(::grpc::ClientContext* context, const ::aimbot::BurnIdividualImagesRequest* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_BurnIdividualImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncBurnIdividualImageRaw(::grpc::ClientContext* context, const ::aimbot::BurnIdividualImagesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::BurnIdividualImagesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_BurnIdividualImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncBurnIdividualImageRaw(::grpc::ClientContext* context, const ::aimbot::BurnIdividualImagesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncBurnIdividualImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ServoGoTo(::grpc::ClientContext* context, const ::aimbot::ServoGoToRequest& request, ::aimbot::ServoGoToReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ServoGoToRequest, ::aimbot::ServoGoToReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ServoGoTo_, context, request, response);
}

void AimbotService::Stub::async::ServoGoTo(::grpc::ClientContext* context, const ::aimbot::ServoGoToRequest* request, ::aimbot::ServoGoToReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ServoGoToRequest, ::aimbot::ServoGoToReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ServoGoTo_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ServoGoTo(::grpc::ClientContext* context, const ::aimbot::ServoGoToRequest* request, ::aimbot::ServoGoToReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ServoGoTo_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ServoGoToReply>* AimbotService::Stub::PrepareAsyncServoGoToRaw(::grpc::ClientContext* context, const ::aimbot::ServoGoToRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::ServoGoToReply, ::aimbot::ServoGoToRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ServoGoTo_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ServoGoToReply>* AimbotService::Stub::AsyncServoGoToRaw(::grpc::ClientContext* context, const ::aimbot::ServoGoToRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncServoGoToRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ServoGetPosVel(::grpc::ClientContext* context, const ::aimbot::ServoGetPosVelRequest& request, ::aimbot::ServoGetPosVelReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ServoGetPosVelRequest, ::aimbot::ServoGetPosVelReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ServoGetPosVel_, context, request, response);
}

void AimbotService::Stub::async::ServoGetPosVel(::grpc::ClientContext* context, const ::aimbot::ServoGetPosVelRequest* request, ::aimbot::ServoGetPosVelReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ServoGetPosVelRequest, ::aimbot::ServoGetPosVelReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ServoGetPosVel_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ServoGetPosVel(::grpc::ClientContext* context, const ::aimbot::ServoGetPosVelRequest* request, ::aimbot::ServoGetPosVelReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ServoGetPosVel_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ServoGetPosVelReply>* AimbotService::Stub::PrepareAsyncServoGetPosVelRaw(::grpc::ClientContext* context, const ::aimbot::ServoGetPosVelRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::ServoGetPosVelReply, ::aimbot::ServoGetPosVelRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ServoGetPosVel_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ServoGetPosVelReply>* AimbotService::Stub::AsyncServoGetPosVelRaw(::grpc::ClientContext* context, const ::aimbot::ServoGetPosVelRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncServoGetPosVelRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ServoGetLimits(::grpc::ClientContext* context, const ::aimbot::ServoGetLimitsRequest& request, ::aimbot::ServoGetLimitsReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ServoGetLimitsRequest, ::aimbot::ServoGetLimitsReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ServoGetLimits_, context, request, response);
}

void AimbotService::Stub::async::ServoGetLimits(::grpc::ClientContext* context, const ::aimbot::ServoGetLimitsRequest* request, ::aimbot::ServoGetLimitsReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ServoGetLimitsRequest, ::aimbot::ServoGetLimitsReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ServoGetLimits_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ServoGetLimits(::grpc::ClientContext* context, const ::aimbot::ServoGetLimitsRequest* request, ::aimbot::ServoGetLimitsReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ServoGetLimits_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ServoGetLimitsReply>* AimbotService::Stub::PrepareAsyncServoGetLimitsRaw(::grpc::ClientContext* context, const ::aimbot::ServoGetLimitsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::ServoGetLimitsReply, ::aimbot::ServoGetLimitsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ServoGetLimits_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ServoGetLimitsReply>* AimbotService::Stub::AsyncServoGetLimitsRaw(::grpc::ClientContext* context, const ::aimbot::ServoGetLimitsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncServoGetLimitsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::TuningParamsUpdate(::grpc::ClientContext* context, const ::aimbot::TuningParamsUpdateRequest& request, ::aimbot::TuningParamsUpdateReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::TuningParamsUpdateRequest, ::aimbot::TuningParamsUpdateReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_TuningParamsUpdate_, context, request, response);
}

void AimbotService::Stub::async::TuningParamsUpdate(::grpc::ClientContext* context, const ::aimbot::TuningParamsUpdateRequest* request, ::aimbot::TuningParamsUpdateReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::TuningParamsUpdateRequest, ::aimbot::TuningParamsUpdateReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TuningParamsUpdate_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::TuningParamsUpdate(::grpc::ClientContext* context, const ::aimbot::TuningParamsUpdateRequest* request, ::aimbot::TuningParamsUpdateReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TuningParamsUpdate_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TuningParamsUpdateReply>* AimbotService::Stub::PrepareAsyncTuningParamsUpdateRaw(::grpc::ClientContext* context, const ::aimbot::TuningParamsUpdateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::TuningParamsUpdateReply, ::aimbot::TuningParamsUpdateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_TuningParamsUpdate_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TuningParamsUpdateReply>* AimbotService::Stub::AsyncTuningParamsUpdateRaw(::grpc::ClientContext* context, const ::aimbot::TuningParamsUpdateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncTuningParamsUpdateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::TuningParamsGet(::grpc::ClientContext* context, const ::aimbot::TuningParamsGetRequest& request, ::aimbot::TuningParamsGetReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::TuningParamsGetRequest, ::aimbot::TuningParamsGetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_TuningParamsGet_, context, request, response);
}

void AimbotService::Stub::async::TuningParamsGet(::grpc::ClientContext* context, const ::aimbot::TuningParamsGetRequest* request, ::aimbot::TuningParamsGetReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::TuningParamsGetRequest, ::aimbot::TuningParamsGetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TuningParamsGet_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::TuningParamsGet(::grpc::ClientContext* context, const ::aimbot::TuningParamsGetRequest* request, ::aimbot::TuningParamsGetReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TuningParamsGet_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TuningParamsGetReply>* AimbotService::Stub::PrepareAsyncTuningParamsGetRaw(::grpc::ClientContext* context, const ::aimbot::TuningParamsGetRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::TuningParamsGetReply, ::aimbot::TuningParamsGetRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_TuningParamsGet_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TuningParamsGetReply>* AimbotService::Stub::AsyncTuningParamsGetRaw(::grpc::ClientContext* context, const ::aimbot::TuningParamsGetRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncTuningParamsGetRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetLoadEstimate(::grpc::ClientContext* context, const ::aimbot::GetLoadEstimateRequest& request, ::aimbot::GetLoadEstimateReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::GetLoadEstimateRequest, ::aimbot::GetLoadEstimateReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetLoadEstimate_, context, request, response);
}

void AimbotService::Stub::async::GetLoadEstimate(::grpc::ClientContext* context, const ::aimbot::GetLoadEstimateRequest* request, ::aimbot::GetLoadEstimateReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::GetLoadEstimateRequest, ::aimbot::GetLoadEstimateReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLoadEstimate_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetLoadEstimate(::grpc::ClientContext* context, const ::aimbot::GetLoadEstimateRequest* request, ::aimbot::GetLoadEstimateReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLoadEstimate_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetLoadEstimateReply>* AimbotService::Stub::PrepareAsyncGetLoadEstimateRaw(::grpc::ClientContext* context, const ::aimbot::GetLoadEstimateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::GetLoadEstimateReply, ::aimbot::GetLoadEstimateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetLoadEstimate_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetLoadEstimateReply>* AimbotService::Stub::AsyncGetLoadEstimateRaw(::grpc::ClientContext* context, const ::aimbot::GetLoadEstimateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetLoadEstimateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetDiagnostic(::grpc::ClientContext* context, const ::aimbot::GetDiagnosticRequest& request, ::aimbot::GetDiagnosticReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::GetDiagnosticRequest, ::aimbot::GetDiagnosticReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDiagnostic_, context, request, response);
}

void AimbotService::Stub::async::GetDiagnostic(::grpc::ClientContext* context, const ::aimbot::GetDiagnosticRequest* request, ::aimbot::GetDiagnosticReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::GetDiagnosticRequest, ::aimbot::GetDiagnosticReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDiagnostic_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetDiagnostic(::grpc::ClientContext* context, const ::aimbot::GetDiagnosticRequest* request, ::aimbot::GetDiagnosticReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDiagnostic_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetDiagnosticReply>* AimbotService::Stub::PrepareAsyncGetDiagnosticRaw(::grpc::ClientContext* context, const ::aimbot::GetDiagnosticRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::GetDiagnosticReply, ::aimbot::GetDiagnosticRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDiagnostic_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetDiagnosticReply>* AimbotService::Stub::AsyncGetDiagnosticRaw(::grpc::ClientContext* context, const ::aimbot::GetDiagnosticRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDiagnosticRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ResetDevices(::grpc::ClientContext* context, const ::aimbot::ResetDevicesRequest& request, ::aimbot::ResetDevicesReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ResetDevicesRequest, ::aimbot::ResetDevicesReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ResetDevices_, context, request, response);
}

void AimbotService::Stub::async::ResetDevices(::grpc::ClientContext* context, const ::aimbot::ResetDevicesRequest* request, ::aimbot::ResetDevicesReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ResetDevicesRequest, ::aimbot::ResetDevicesReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetDevices_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ResetDevices(::grpc::ClientContext* context, const ::aimbot::ResetDevicesRequest* request, ::aimbot::ResetDevicesReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetDevices_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ResetDevicesReply>* AimbotService::Stub::PrepareAsyncResetDevicesRaw(::grpc::ClientContext* context, const ::aimbot::ResetDevicesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::ResetDevicesReply, ::aimbot::ResetDevicesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ResetDevices_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ResetDevicesReply>* AimbotService::Stub::AsyncResetDevicesRaw(::grpc::ClientContext* context, const ::aimbot::ResetDevicesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResetDevicesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ResetScanner(::grpc::ClientContext* context, const ::aimbot::ResetScannerRequest& request, ::aimbot::ResetScannerReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ResetScannerRequest, ::aimbot::ResetScannerReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ResetScanner_, context, request, response);
}

void AimbotService::Stub::async::ResetScanner(::grpc::ClientContext* context, const ::aimbot::ResetScannerRequest* request, ::aimbot::ResetScannerReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ResetScannerRequest, ::aimbot::ResetScannerReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetScanner_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ResetScanner(::grpc::ClientContext* context, const ::aimbot::ResetScannerRequest* request, ::aimbot::ResetScannerReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetScanner_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ResetScannerReply>* AimbotService::Stub::PrepareAsyncResetScannerRaw(::grpc::ClientContext* context, const ::aimbot::ResetScannerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::ResetScannerReply, ::aimbot::ResetScannerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ResetScanner_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ResetScannerReply>* AimbotService::Stub::AsyncResetScannerRaw(::grpc::ClientContext* context, const ::aimbot::ResetScannerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResetScannerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ScannerDescriptor, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartAutoCalibrateCrosshair_, context, request, response);
}

void AimbotService::Stub::async::StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ScannerDescriptor, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoCalibrateCrosshair_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoCalibrateCrosshair_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncStartAutoCalibrateCrosshairRaw(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ScannerDescriptor, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartAutoCalibrateCrosshair_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncStartAutoCalibrateCrosshairRaw(::grpc::ClientContext* context, const ::aimbot::ScannerDescriptor& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartAutoCalibrateCrosshairRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartAutoCalibrateAllCrosshairs_, context, request, response);
}

void AimbotService::Stub::async::StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoCalibrateAllCrosshairs_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoCalibrateAllCrosshairs_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncStartAutoCalibrateAllCrosshairsRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartAutoCalibrateAllCrosshairs_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncStartAutoCalibrateAllCrosshairsRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartAutoCalibrateAllCrosshairsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::StopAutoCalibrate(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopAutoCalibrate_, context, request, response);
}

void AimbotService::Stub::async::StopAutoCalibrate(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopAutoCalibrate_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::StopAutoCalibrate(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopAutoCalibrate_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncStopAutoCalibrateRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopAutoCalibrate_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncStopAutoCalibrateRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopAutoCalibrateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::SetCrosshairPosition(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ScannerTargetPosition, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetCrosshairPosition_, context, request, response);
}

void AimbotService::Stub::async::SetCrosshairPosition(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ScannerTargetPosition, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCrosshairPosition_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::SetCrosshairPosition(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCrosshairPosition_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncSetCrosshairPositionRaw(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ScannerTargetPosition, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetCrosshairPosition_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncSetCrosshairPositionRaw(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetCrosshairPositionRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::MoveScanner(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ScannerTargetPosition, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_MoveScanner_, context, request, response);
}

void AimbotService::Stub::async::MoveScanner(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ScannerTargetPosition, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MoveScanner_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::MoveScanner(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MoveScanner_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncMoveScannerRaw(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ScannerTargetPosition, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_MoveScanner_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncMoveScannerRaw(::grpc::ClientContext* context, const ::aimbot::ScannerTargetPosition& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncMoveScannerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetAutoCalibrationProgress(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::AutoXHairCalibrationProgress* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::AutoXHairCalibrationProgress, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetAutoCalibrationProgress_, context, request, response);
}

void AimbotService::Stub::async::GetAutoCalibrationProgress(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::AutoXHairCalibrationProgress* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::AutoXHairCalibrationProgress, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetAutoCalibrationProgress_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetAutoCalibrationProgress(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::AutoXHairCalibrationProgress* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetAutoCalibrationProgress_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::AutoXHairCalibrationProgress>* AimbotService::Stub::PrepareAsyncGetAutoCalibrationProgressRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::AutoXHairCalibrationProgress, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetAutoCalibrationProgress_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::AutoXHairCalibrationProgress>* AimbotService::Stub::AsyncGetAutoCalibrationProgressRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetAutoCalibrationProgressRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetScannerStatus(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::ScannerStatusReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::ScannerStatusReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetScannerStatus_, context, request, response);
}

void AimbotService::Stub::async::GetScannerStatus(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::ScannerStatusReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::ScannerStatusReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetScannerStatus_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetScannerStatus(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::ScannerStatusReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetScannerStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ScannerStatusReply>* AimbotService::Stub::PrepareAsyncGetScannerStatusRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::ScannerStatusReply, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetScannerStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ScannerStatusReply>* AimbotService::Stub::AsyncGetScannerStatusRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetScannerStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetTargetVelocity(::grpc::ClientContext* context, const ::aimbot::TargetVelocityRequest& request, ::aimbot::TargetVelocityReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::TargetVelocityRequest, ::aimbot::TargetVelocityReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTargetVelocity_, context, request, response);
}

void AimbotService::Stub::async::GetTargetVelocity(::grpc::ClientContext* context, const ::aimbot::TargetVelocityRequest* request, ::aimbot::TargetVelocityReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::TargetVelocityRequest, ::aimbot::TargetVelocityReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTargetVelocity_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetTargetVelocity(::grpc::ClientContext* context, const ::aimbot::TargetVelocityRequest* request, ::aimbot::TargetVelocityReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTargetVelocity_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TargetVelocityReply>* AimbotService::Stub::PrepareAsyncGetTargetVelocityRaw(::grpc::ClientContext* context, const ::aimbot::TargetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::TargetVelocityReply, ::aimbot::TargetVelocityRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTargetVelocity_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TargetVelocityReply>* AimbotService::Stub::AsyncGetTargetVelocityRaw(::grpc::ClientContext* context, const ::aimbot::TargetVelocityRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTargetVelocityRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetTrackingState(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::TrackingState* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::TrackingState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTrackingState_, context, request, response);
}

void AimbotService::Stub::async::GetTrackingState(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::TrackingState* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::TrackingState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTrackingState_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetTrackingState(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::TrackingState* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTrackingState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TrackingState>* AimbotService::Stub::PrepareAsyncGetTrackingStateRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::TrackingState, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTrackingState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TrackingState>* AimbotService::Stub::AsyncGetTrackingStateRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTrackingStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetBedtopHeightProfile(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::TrackerBedtopHeightProfile* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::TrackerBedtopHeightProfile, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetBedtopHeightProfile_, context, request, response);
}

void AimbotService::Stub::async::GetBedtopHeightProfile(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::TrackerBedtopHeightProfile* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::TrackerBedtopHeightProfile, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBedtopHeightProfile_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetBedtopHeightProfile(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::TrackerBedtopHeightProfile* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBedtopHeightProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TrackerBedtopHeightProfile>* AimbotService::Stub::PrepareAsyncGetBedtopHeightProfileRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::TrackerBedtopHeightProfile, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetBedtopHeightProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TrackerBedtopHeightProfile>* AimbotService::Stub::AsyncGetBedtopHeightProfileRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetBedtopHeightProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetDimensions(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::GetDimensionsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::GetDimensionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDimensions_, context, request, response);
}

void AimbotService::Stub::async::GetDimensions(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::GetDimensionsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::GetDimensionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDimensions_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetDimensions(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::GetDimensionsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDimensions_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>* AimbotService::Stub::PrepareAsyncGetDimensionsRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::GetDimensionsResponse, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDimensions_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>* AimbotService::Stub::AsyncGetDimensionsRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDimensionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetTargetCamSN(::grpc::ClientContext* context, const ::aimbot::GetTargetCamSNRequest& request, ::aimbot::GetTargetCamSNResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::GetTargetCamSNRequest, ::aimbot::GetTargetCamSNResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTargetCamSN_, context, request, response);
}

void AimbotService::Stub::async::GetTargetCamSN(::grpc::ClientContext* context, const ::aimbot::GetTargetCamSNRequest* request, ::aimbot::GetTargetCamSNResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::GetTargetCamSNRequest, ::aimbot::GetTargetCamSNResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTargetCamSN_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetTargetCamSN(::grpc::ClientContext* context, const ::aimbot::GetTargetCamSNRequest* request, ::aimbot::GetTargetCamSNResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTargetCamSN_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetTargetCamSNResponse>* AimbotService::Stub::PrepareAsyncGetTargetCamSNRaw(::grpc::ClientContext* context, const ::aimbot::GetTargetCamSNRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::GetTargetCamSNResponse, ::aimbot::GetTargetCamSNRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTargetCamSN_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::GetTargetCamSNResponse>* AimbotService::Stub::AsyncGetTargetCamSNRaw(::grpc::ClientContext* context, const ::aimbot::GetTargetCamSNRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTargetCamSNRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ReloadThinningConf(::grpc::ClientContext* context, const ::aimbot::ReloadThinningConfRequest& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ReloadThinningConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReloadThinningConf_, context, request, response);
}

void AimbotService::Stub::async::ReloadThinningConf(::grpc::ClientContext* context, const ::aimbot::ReloadThinningConfRequest* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ReloadThinningConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadThinningConf_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ReloadThinningConf(::grpc::ClientContext* context, const ::aimbot::ReloadThinningConfRequest* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadThinningConf_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncReloadThinningConfRaw(::grpc::ClientContext* context, const ::aimbot::ReloadThinningConfRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ReloadThinningConfRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReloadThinningConf_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncReloadThinningConfRaw(::grpc::ClientContext* context, const ::aimbot::ReloadThinningConfRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReloadThinningConfRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ReloadAlmanacConf(::grpc::ClientContext* context, const ::aimbot::ReloadAlmanacConfRequest& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ReloadAlmanacConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReloadAlmanacConf_, context, request, response);
}

void AimbotService::Stub::async::ReloadAlmanacConf(::grpc::ClientContext* context, const ::aimbot::ReloadAlmanacConfRequest* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ReloadAlmanacConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadAlmanacConf_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ReloadAlmanacConf(::grpc::ClientContext* context, const ::aimbot::ReloadAlmanacConfRequest* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadAlmanacConf_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncReloadAlmanacConfRaw(::grpc::ClientContext* context, const ::aimbot::ReloadAlmanacConfRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ReloadAlmanacConfRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReloadAlmanacConf_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncReloadAlmanacConfRaw(::grpc::ClientContext* context, const ::aimbot::ReloadAlmanacConfRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReloadAlmanacConfRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ReloadDiscriminatorConf(::grpc::ClientContext* context, const ::aimbot::ReloadDiscriminatorConfRequest& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ReloadDiscriminatorConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReloadDiscriminatorConf_, context, request, response);
}

void AimbotService::Stub::async::ReloadDiscriminatorConf(::grpc::ClientContext* context, const ::aimbot::ReloadDiscriminatorConfRequest* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ReloadDiscriminatorConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadDiscriminatorConf_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ReloadDiscriminatorConf(::grpc::ClientContext* context, const ::aimbot::ReloadDiscriminatorConfRequest* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadDiscriminatorConf_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncReloadDiscriminatorConfRaw(::grpc::ClientContext* context, const ::aimbot::ReloadDiscriminatorConfRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ReloadDiscriminatorConfRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReloadDiscriminatorConf_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncReloadDiscriminatorConfRaw(::grpc::ClientContext* context, const ::aimbot::ReloadDiscriminatorConfRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReloadDiscriminatorConfRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ReloadModelinatorConf(::grpc::ClientContext* context, const ::aimbot::ReloadModelinatorConfRequest& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ReloadModelinatorConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReloadModelinatorConf_, context, request, response);
}

void AimbotService::Stub::async::ReloadModelinatorConf(::grpc::ClientContext* context, const ::aimbot::ReloadModelinatorConfRequest* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ReloadModelinatorConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadModelinatorConf_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ReloadModelinatorConf(::grpc::ClientContext* context, const ::aimbot::ReloadModelinatorConfRequest* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadModelinatorConf_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncReloadModelinatorConfRaw(::grpc::ClientContext* context, const ::aimbot::ReloadModelinatorConfRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ReloadModelinatorConfRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReloadModelinatorConf_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncReloadModelinatorConfRaw(::grpc::ClientContext* context, const ::aimbot::ReloadModelinatorConfRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReloadModelinatorConfRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::ReloadTVEProfile(::grpc::ClientContext* context, const ::aimbot::ReloadTVEProfileRequest& request, ::aimbot::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::ReloadTVEProfileRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReloadTVEProfile_, context, request, response);
}

void AimbotService::Stub::async::ReloadTVEProfile(::grpc::ClientContext* context, const ::aimbot::ReloadTVEProfileRequest* request, ::aimbot::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::ReloadTVEProfileRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadTVEProfile_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::ReloadTVEProfile(::grpc::ClientContext* context, const ::aimbot::ReloadTVEProfileRequest* request, ::aimbot::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadTVEProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::PrepareAsyncReloadTVEProfileRaw(::grpc::ClientContext* context, const ::aimbot::ReloadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::Empty, ::aimbot::ReloadTVEProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReloadTVEProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::Empty>* AimbotService::Stub::AsyncReloadTVEProfileRaw(::grpc::ClientContext* context, const ::aimbot::ReloadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReloadTVEProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetDistanceTrackedItems(::grpc::ClientContext* context, const ::aimbot::TrackedItemsRequest& request, ::aimbot::TrackedItemsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::TrackedItemsRequest, ::aimbot::TrackedItemsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDistanceTrackedItems_, context, request, response);
}

void AimbotService::Stub::async::GetDistanceTrackedItems(::grpc::ClientContext* context, const ::aimbot::TrackedItemsRequest* request, ::aimbot::TrackedItemsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::TrackedItemsRequest, ::aimbot::TrackedItemsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDistanceTrackedItems_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetDistanceTrackedItems(::grpc::ClientContext* context, const ::aimbot::TrackedItemsRequest* request, ::aimbot::TrackedItemsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDistanceTrackedItems_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TrackedItemsResponse>* AimbotService::Stub::PrepareAsyncGetDistanceTrackedItemsRaw(::grpc::ClientContext* context, const ::aimbot::TrackedItemsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::TrackedItemsResponse, ::aimbot::TrackedItemsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDistanceTrackedItems_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::TrackedItemsResponse>* AimbotService::Stub::AsyncGetDistanceTrackedItemsRaw(::grpc::ClientContext* context, const ::aimbot::TrackedItemsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDistanceTrackedItemsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AimbotService::Stub::GetParticipation(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::aimbot::ParticipationResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::aimbot::Empty, ::aimbot::ParticipationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetParticipation_, context, request, response);
}

void AimbotService::Stub::async::GetParticipation(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::ParticipationResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::aimbot::Empty, ::aimbot::ParticipationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetParticipation_, context, request, response, std::move(f));
}

void AimbotService::Stub::async::GetParticipation(::grpc::ClientContext* context, const ::aimbot::Empty* request, ::aimbot::ParticipationResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetParticipation_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ParticipationResponse>* AimbotService::Stub::PrepareAsyncGetParticipationRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::aimbot::ParticipationResponse, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetParticipation_, context, request);
}

::grpc::ClientAsyncResponseReader< ::aimbot::ParticipationResponse>* AimbotService::Stub::AsyncGetParticipationRaw(::grpc::ClientContext* context, const ::aimbot::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetParticipationRaw(context, request, cq);
  result->StartCall();
  return result;
}

AimbotService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::PingRequest, ::aimbot::PongReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::PingRequest* req,
             ::aimbot::PongReply* resp) {
               return service->Ping(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::BootedReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::BootedReply* resp) {
               return service->GetBooted(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::Empty* resp) {
               return service->ArmLasers(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::Empty* resp) {
               return service->DisarmLasers(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::AimbotState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::AimbotState* resp) {
               return service->GetAimbotState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::TargetingState, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::TargetingState* req,
             ::aimbot::Empty* resp) {
               return service->SetTargetingState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ActuationTaskRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ActuationTaskRequest* req,
             ::aimbot::Empty* resp) {
               return service->StartActuationTask(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::Empty* resp) {
               return service->CancelActuationTask(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::LensSetRequest, ::aimbot::LensSetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::LensSetRequest* req,
             ::aimbot::LensSetReply* resp) {
               return service->LensSet(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::LensGetRequest, ::aimbot::LensGetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::LensGetRequest* req,
             ::aimbot::LensGetReply* resp) {
               return service->LensGet(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::LensGetAllReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::LensGetAllReply* resp) {
               return service->LensGetAll(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::LensAutoFocusRequest, ::aimbot::LensAutoFocusReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::LensAutoFocusRequest* req,
             ::aimbot::LensAutoFocusReply* resp) {
               return service->LensAutoFocus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::StopLensAutoFocusRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::StopLensAutoFocusRequest* req,
             ::aimbot::Empty* resp) {
               return service->StopLensAutoFocus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::LaserArmRequest, ::aimbot::LaserArmReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::LaserArmRequest* req,
             ::aimbot::LaserArmReply* resp) {
               return service->LaserArm(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::LaserSetRequest, ::aimbot::LaserSetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::LaserSetRequest* req,
             ::aimbot::LaserSetReply* resp) {
               return service->LaserSet(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[15],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::LaserEnableRequest, ::aimbot::LaserEnableReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::LaserEnableRequest* req,
             ::aimbot::LaserEnableReply* resp) {
               return service->LaserEnable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[16],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::LaserFireRequest, ::aimbot::LaserFireReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::LaserFireRequest* req,
             ::aimbot::LaserFireReply* resp) {
               return service->LaserFire(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[17],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ScannerDescriptor, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ScannerDescriptor* req,
             ::aimbot::Empty* resp) {
               return service->ResetLaserMetrics(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[18],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::FixLaserMetricsRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::FixLaserMetricsRequest* req,
             ::aimbot::Empty* resp) {
               return service->FixLaserMetrics(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[19],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::BurnIdividualImagesRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::BurnIdividualImagesRequest* req,
             ::aimbot::Empty* resp) {
               return service->BurnIdividualImage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[20],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ServoGoToRequest, ::aimbot::ServoGoToReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ServoGoToRequest* req,
             ::aimbot::ServoGoToReply* resp) {
               return service->ServoGoTo(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[21],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ServoGetPosVelRequest, ::aimbot::ServoGetPosVelReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ServoGetPosVelRequest* req,
             ::aimbot::ServoGetPosVelReply* resp) {
               return service->ServoGetPosVel(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[22],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ServoGetLimitsRequest, ::aimbot::ServoGetLimitsReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ServoGetLimitsRequest* req,
             ::aimbot::ServoGetLimitsReply* resp) {
               return service->ServoGetLimits(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[23],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::TuningParamsUpdateRequest, ::aimbot::TuningParamsUpdateReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::TuningParamsUpdateRequest* req,
             ::aimbot::TuningParamsUpdateReply* resp) {
               return service->TuningParamsUpdate(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[24],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::TuningParamsGetRequest, ::aimbot::TuningParamsGetReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::TuningParamsGetRequest* req,
             ::aimbot::TuningParamsGetReply* resp) {
               return service->TuningParamsGet(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[25],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::GetLoadEstimateRequest, ::aimbot::GetLoadEstimateReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::GetLoadEstimateRequest* req,
             ::aimbot::GetLoadEstimateReply* resp) {
               return service->GetLoadEstimate(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[26],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::GetDiagnosticRequest, ::aimbot::GetDiagnosticReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::GetDiagnosticRequest* req,
             ::aimbot::GetDiagnosticReply* resp) {
               return service->GetDiagnostic(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[27],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ResetDevicesRequest, ::aimbot::ResetDevicesReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ResetDevicesRequest* req,
             ::aimbot::ResetDevicesReply* resp) {
               return service->ResetDevices(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[28],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ResetScannerRequest, ::aimbot::ResetScannerReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ResetScannerRequest* req,
             ::aimbot::ResetScannerReply* resp) {
               return service->ResetScanner(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[29],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ScannerDescriptor, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ScannerDescriptor* req,
             ::aimbot::Empty* resp) {
               return service->StartAutoCalibrateCrosshair(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[30],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::Empty* resp) {
               return service->StartAutoCalibrateAllCrosshairs(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[31],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::Empty* resp) {
               return service->StopAutoCalibrate(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[32],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ScannerTargetPosition, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ScannerTargetPosition* req,
             ::aimbot::Empty* resp) {
               return service->SetCrosshairPosition(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[33],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ScannerTargetPosition, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ScannerTargetPosition* req,
             ::aimbot::Empty* resp) {
               return service->MoveScanner(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[34],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::AutoXHairCalibrationProgress, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::AutoXHairCalibrationProgress* resp) {
               return service->GetAutoCalibrationProgress(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[35],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::ScannerStatusReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::ScannerStatusReply* resp) {
               return service->GetScannerStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[36],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::TargetVelocityRequest, ::aimbot::TargetVelocityReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::TargetVelocityRequest* req,
             ::aimbot::TargetVelocityReply* resp) {
               return service->GetTargetVelocity(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[37],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::TrackingState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::TrackingState* resp) {
               return service->GetTrackingState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[38],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::TrackerBedtopHeightProfile, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::TrackerBedtopHeightProfile* resp) {
               return service->GetBedtopHeightProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[39],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::GetDimensionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::GetDimensionsResponse* resp) {
               return service->GetDimensions(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[40],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::GetTargetCamSNRequest, ::aimbot::GetTargetCamSNResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::GetTargetCamSNRequest* req,
             ::aimbot::GetTargetCamSNResponse* resp) {
               return service->GetTargetCamSN(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[41],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ReloadThinningConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ReloadThinningConfRequest* req,
             ::aimbot::Empty* resp) {
               return service->ReloadThinningConf(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[42],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ReloadAlmanacConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ReloadAlmanacConfRequest* req,
             ::aimbot::Empty* resp) {
               return service->ReloadAlmanacConf(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[43],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ReloadDiscriminatorConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ReloadDiscriminatorConfRequest* req,
             ::aimbot::Empty* resp) {
               return service->ReloadDiscriminatorConf(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[44],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ReloadModelinatorConfRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ReloadModelinatorConfRequest* req,
             ::aimbot::Empty* resp) {
               return service->ReloadModelinatorConf(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[45],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::ReloadTVEProfileRequest, ::aimbot::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::ReloadTVEProfileRequest* req,
             ::aimbot::Empty* resp) {
               return service->ReloadTVEProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[46],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::TrackedItemsRequest, ::aimbot::TrackedItemsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::TrackedItemsRequest* req,
             ::aimbot::TrackedItemsResponse* resp) {
               return service->GetDistanceTrackedItems(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AimbotService_method_names[47],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AimbotService::Service, ::aimbot::Empty, ::aimbot::ParticipationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AimbotService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::aimbot::Empty* req,
             ::aimbot::ParticipationResponse* resp) {
               return service->GetParticipation(ctx, req, resp);
             }, this)));
}

AimbotService::Service::~Service() {
}

::grpc::Status AimbotService::Service::Ping(::grpc::ServerContext* context, const ::aimbot::PingRequest* request, ::aimbot::PongReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetBooted(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::BootedReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ArmLasers(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::DisarmLasers(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetAimbotState(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::AimbotState* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::SetTargetingState(::grpc::ServerContext* context, const ::aimbot::TargetingState* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::StartActuationTask(::grpc::ServerContext* context, const ::aimbot::ActuationTaskRequest* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::CancelActuationTask(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::LensSet(::grpc::ServerContext* context, const ::aimbot::LensSetRequest* request, ::aimbot::LensSetReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::LensGet(::grpc::ServerContext* context, const ::aimbot::LensGetRequest* request, ::aimbot::LensGetReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::LensGetAll(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::LensGetAllReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::LensAutoFocus(::grpc::ServerContext* context, const ::aimbot::LensAutoFocusRequest* request, ::aimbot::LensAutoFocusReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::StopLensAutoFocus(::grpc::ServerContext* context, const ::aimbot::StopLensAutoFocusRequest* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::LaserArm(::grpc::ServerContext* context, const ::aimbot::LaserArmRequest* request, ::aimbot::LaserArmReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::LaserSet(::grpc::ServerContext* context, const ::aimbot::LaserSetRequest* request, ::aimbot::LaserSetReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::LaserEnable(::grpc::ServerContext* context, const ::aimbot::LaserEnableRequest* request, ::aimbot::LaserEnableReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::LaserFire(::grpc::ServerContext* context, const ::aimbot::LaserFireRequest* request, ::aimbot::LaserFireReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ResetLaserMetrics(::grpc::ServerContext* context, const ::aimbot::ScannerDescriptor* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::FixLaserMetrics(::grpc::ServerContext* context, const ::aimbot::FixLaserMetricsRequest* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::BurnIdividualImage(::grpc::ServerContext* context, const ::aimbot::BurnIdividualImagesRequest* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ServoGoTo(::grpc::ServerContext* context, const ::aimbot::ServoGoToRequest* request, ::aimbot::ServoGoToReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ServoGetPosVel(::grpc::ServerContext* context, const ::aimbot::ServoGetPosVelRequest* request, ::aimbot::ServoGetPosVelReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ServoGetLimits(::grpc::ServerContext* context, const ::aimbot::ServoGetLimitsRequest* request, ::aimbot::ServoGetLimitsReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::TuningParamsUpdate(::grpc::ServerContext* context, const ::aimbot::TuningParamsUpdateRequest* request, ::aimbot::TuningParamsUpdateReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::TuningParamsGet(::grpc::ServerContext* context, const ::aimbot::TuningParamsGetRequest* request, ::aimbot::TuningParamsGetReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetLoadEstimate(::grpc::ServerContext* context, const ::aimbot::GetLoadEstimateRequest* request, ::aimbot::GetLoadEstimateReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetDiagnostic(::grpc::ServerContext* context, const ::aimbot::GetDiagnosticRequest* request, ::aimbot::GetDiagnosticReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ResetDevices(::grpc::ServerContext* context, const ::aimbot::ResetDevicesRequest* request, ::aimbot::ResetDevicesReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ResetScanner(::grpc::ServerContext* context, const ::aimbot::ResetScannerRequest* request, ::aimbot::ResetScannerReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::StartAutoCalibrateCrosshair(::grpc::ServerContext* context, const ::aimbot::ScannerDescriptor* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::StartAutoCalibrateAllCrosshairs(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::StopAutoCalibrate(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::SetCrosshairPosition(::grpc::ServerContext* context, const ::aimbot::ScannerTargetPosition* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::MoveScanner(::grpc::ServerContext* context, const ::aimbot::ScannerTargetPosition* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetAutoCalibrationProgress(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::AutoXHairCalibrationProgress* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetScannerStatus(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::ScannerStatusReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetTargetVelocity(::grpc::ServerContext* context, const ::aimbot::TargetVelocityRequest* request, ::aimbot::TargetVelocityReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetTrackingState(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::TrackingState* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetBedtopHeightProfile(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::TrackerBedtopHeightProfile* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetDimensions(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::GetDimensionsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetTargetCamSN(::grpc::ServerContext* context, const ::aimbot::GetTargetCamSNRequest* request, ::aimbot::GetTargetCamSNResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ReloadThinningConf(::grpc::ServerContext* context, const ::aimbot::ReloadThinningConfRequest* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ReloadAlmanacConf(::grpc::ServerContext* context, const ::aimbot::ReloadAlmanacConfRequest* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ReloadDiscriminatorConf(::grpc::ServerContext* context, const ::aimbot::ReloadDiscriminatorConfRequest* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ReloadModelinatorConf(::grpc::ServerContext* context, const ::aimbot::ReloadModelinatorConfRequest* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::ReloadTVEProfile(::grpc::ServerContext* context, const ::aimbot::ReloadTVEProfileRequest* request, ::aimbot::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetDistanceTrackedItems(::grpc::ServerContext* context, const ::aimbot::TrackedItemsRequest* request, ::aimbot::TrackedItemsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AimbotService::Service::GetParticipation(::grpc::ServerContext* context, const ::aimbot::Empty* request, ::aimbot::ParticipationResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace aimbot


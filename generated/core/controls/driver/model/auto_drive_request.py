# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Optional, Type

from generated.core.controls.driver.model.protobuf.requests_pb2 import AutoDriveRequestProto
from lib.common.protocol.codegen.compile.python.validation import required
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin
from lib.common.time.time import TimestampedObject


class AutoDriveRequest(TimestampedObject, ProtobufSerializationMixin[AutoDriveRequestProto]):
    """
    Auto-generated AutoDriveRequest
    """

    def __init__(self, *, timestamp_ms: int, source: str, plan: str, datum: Optional[float] = None):

        # timestamp_ms
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

        # source
        required(source)
        self._source: str = source

        # plan
        required(plan)
        self._plan: str = plan

        # datum
        self._datum: Optional[float] = datum

    @classmethod
    def proto_type(cls) -> Type[AutoDriveRequestProto]:
        return AutoDriveRequestProto

    @property
    def source(self) -> str:
        return self._source

    @property
    def plan(self) -> str:
        return self._plan

    @property
    def datum(self) -> Optional[float]:
        return self._datum

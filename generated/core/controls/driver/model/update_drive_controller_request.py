# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Type

from generated.core.controls.driver.model.protobuf.requests_pb2 import UpdateDriveControllerRequestProto
from lib.common.protocol.codegen.compile.python.validation import required
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin
from lib.common.time.time import TimestampedObject


class UpdateDriveControllerRequest(TimestampedObject, ProtobufSerializationMixin[UpdateDriveControllerRequestProto]):
    """
    Auto-generated UpdateDriveControllerRequest
    """

    def __init__(self, *, timestamp_ms: int, controller: str):

        # timestamp_ms
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

        # controller
        required(controller)
        self._controller: str = controller

    @classmethod
    def proto_type(cls) -> Type[UpdateDriveControllerRequestProto]:
        return UpdateDriveControllerRequestProto

    @property
    def controller(self) -> str:
        return self._controller

# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Optional, Type

from generated.core.controls.driver.model.protobuf.requests_pb2 import ManualDriveRequestProto
from lib.common.protocol.codegen.compile.python.validation import required, unit_interval_inclusive
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin
from lib.common.time.time import TimestampedObject


class ManualDriveRequest(TimestampedObject, ProtobufSerializationMixin[ManualDriveRequestProto]):
    """
    Auto-generated ManualDriveRequest
    """

    def __init__(
        self, *, timestamp_ms: int, source: str, forward: Optional[float] = None, steer: Optional[float] = None
    ):

        # timestamp_ms
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

        # source
        required(source)
        self._source: str = source

        # forward
        if forward is not None:
            unit_interval_inclusive(forward)
        self._forward: Optional[float] = forward

        # steer
        if steer is not None:
            unit_interval_inclusive(steer)
        self._steer: Optional[float] = steer

    @classmethod
    def proto_type(cls) -> Type[ManualDriveRequestProto]:
        return ManualDriveRequestProto

    @property
    def source(self) -> str:
        return self._source

    @property
    def forward(self) -> Optional[float]:
        return self._forward

    @property
    def steer(self) -> Optional[float]:
        return self._steer

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: responses.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='responses.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x0fresponses.proto\".\n\x16\x41utoDriveResponseProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\"4\n\x18ManualDriveResponseProto\x12\x18\n\x10positive_control\x18\x01 \x01(\x08\":\n\"UpdateDriveControllerResponseProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\"8\n UpdateSpeedLimitPctResponseProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x62\x06proto3'
)




_AUTODRIVERESPONSEPROTO = _descriptor.Descriptor(
  name='AutoDriveResponseProto',
  full_name='AutoDriveResponseProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='AutoDriveResponseProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19,
  serialized_end=65,
)


_MANUALDRIVERESPONSEPROTO = _descriptor.Descriptor(
  name='ManualDriveResponseProto',
  full_name='ManualDriveResponseProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='positive_control', full_name='ManualDriveResponseProto.positive_control', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=67,
  serialized_end=119,
)


_UPDATEDRIVECONTROLLERRESPONSEPROTO = _descriptor.Descriptor(
  name='UpdateDriveControllerResponseProto',
  full_name='UpdateDriveControllerResponseProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='UpdateDriveControllerResponseProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=121,
  serialized_end=179,
)


_UPDATESPEEDLIMITPCTRESPONSEPROTO = _descriptor.Descriptor(
  name='UpdateSpeedLimitPctResponseProto',
  full_name='UpdateSpeedLimitPctResponseProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='UpdateSpeedLimitPctResponseProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=181,
  serialized_end=237,
)

DESCRIPTOR.message_types_by_name['AutoDriveResponseProto'] = _AUTODRIVERESPONSEPROTO
DESCRIPTOR.message_types_by_name['ManualDriveResponseProto'] = _MANUALDRIVERESPONSEPROTO
DESCRIPTOR.message_types_by_name['UpdateDriveControllerResponseProto'] = _UPDATEDRIVECONTROLLERRESPONSEPROTO
DESCRIPTOR.message_types_by_name['UpdateSpeedLimitPctResponseProto'] = _UPDATESPEEDLIMITPCTRESPONSEPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AutoDriveResponseProto = _reflection.GeneratedProtocolMessageType('AutoDriveResponseProto', (_message.Message,), {
  'DESCRIPTOR' : _AUTODRIVERESPONSEPROTO,
  '__module__' : 'responses_pb2'
  # @@protoc_insertion_point(class_scope:AutoDriveResponseProto)
  })
_sym_db.RegisterMessage(AutoDriveResponseProto)

ManualDriveResponseProto = _reflection.GeneratedProtocolMessageType('ManualDriveResponseProto', (_message.Message,), {
  'DESCRIPTOR' : _MANUALDRIVERESPONSEPROTO,
  '__module__' : 'responses_pb2'
  # @@protoc_insertion_point(class_scope:ManualDriveResponseProto)
  })
_sym_db.RegisterMessage(ManualDriveResponseProto)

UpdateDriveControllerResponseProto = _reflection.GeneratedProtocolMessageType('UpdateDriveControllerResponseProto', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEDRIVECONTROLLERRESPONSEPROTO,
  '__module__' : 'responses_pb2'
  # @@protoc_insertion_point(class_scope:UpdateDriveControllerResponseProto)
  })
_sym_db.RegisterMessage(UpdateDriveControllerResponseProto)

UpdateSpeedLimitPctResponseProto = _reflection.GeneratedProtocolMessageType('UpdateSpeedLimitPctResponseProto', (_message.Message,), {
  'DESCRIPTOR' : _UPDATESPEEDLIMITPCTRESPONSEPROTO,
  '__module__' : 'responses_pb2'
  # @@protoc_insertion_point(class_scope:UpdateSpeedLimitPctResponseProto)
  })
_sym_db.RegisterMessage(UpdateSpeedLimitPctResponseProto)


# @@protoc_insertion_point(module_scope)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class AutoDriveResponseProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___AutoDriveResponseProto = AutoDriveResponseProto

class ManualDriveResponseProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    positive_control: builtin___bool = ...

    def __init__(self,
        *,
        positive_control : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"positive_control",b"positive_control"]) -> None: ...
type___ManualDriveResponseProto = ManualDriveResponseProto

class UpdateDriveControllerResponseProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___UpdateDriveControllerResponseProto = UpdateDriveControllerResponseProto

class UpdateSpeedLimitPctResponseProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___UpdateSpeedLimitPctResponseProto = UpdateSpeedLimitPctResponseProto

// version: 1
// Generated by the compose compiler.  DO NOT EDIT!
syntax = "proto3";


message AutoDriveRequestProto {
    uint64 timestamp_ms = 1;
    string source = 2;
    string plan = 3;
    float datum = 4;
}

message StopRequestProto {
}

message ManualDriveRequestProto {
    uint64 timestamp_ms = 1;
    string source = 2;
    float forward = 3;
    float steer = 4;
}

message UpdateDriveControllerRequestProto {
    uint64 timestamp_ms = 1;
    string controller = 2;
}

message UpdateSpeedLimitPctRequestProto {
    uint64 timestamp_ms = 1;
    string source = 2;
    float speed_limit_pct = 3;
}

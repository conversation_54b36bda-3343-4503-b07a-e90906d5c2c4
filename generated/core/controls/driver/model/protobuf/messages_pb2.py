# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: messages.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='messages.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x0emessages.proto\"A\n\x19PixelDistanceMessageProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x0e\n\x06pixels\x18\x02 \x01(\x05\x62\x06proto3'
)




_PIXELDISTANCEMESSAGEPROTO = _descriptor.Descriptor(
  name='PixelDistanceMessageProto',
  full_name='PixelDistanceMessageProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='PixelDistanceMessageProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pixels', full_name='PixelDistanceMessageProto.pixels', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18,
  serialized_end=83,
)

DESCRIPTOR.message_types_by_name['PixelDistanceMessageProto'] = _PIXELDISTANCEMESSAGEPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PixelDistanceMessageProto = _reflection.GeneratedProtocolMessageType('PixelDistanceMessageProto', (_message.Message,), {
  'DESCRIPTOR' : _PIXELDISTANCEMESSAGEPROTO,
  '__module__' : 'messages_pb2'
  # @@protoc_insertion_point(class_scope:PixelDistanceMessageProto)
  })
_sym_db.RegisterMessage(PixelDistanceMessageProto)


# @@protoc_insertion_point(module_scope)

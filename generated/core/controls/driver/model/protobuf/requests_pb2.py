# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: requests.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='requests.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x0erequests.proto\"Z\n\x15\x41utoDriveRequestProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0c\n\x04plan\x18\x03 \x01(\t\x12\r\n\x05\x64\x61tum\x18\x04 \x01(\x02\"\x12\n\x10StopRequestProto\"_\n\x17ManualDriveRequestProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0f\n\x07\x66orward\x18\x03 \x01(\x02\x12\r\n\x05steer\x18\x04 \x01(\x02\"M\n!UpdateDriveControllerRequestProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x12\n\ncontroller\x18\x02 \x01(\t\"`\n\x1fUpdateSpeedLimitPctRequestProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x17\n\x0fspeed_limit_pct\x18\x03 \x01(\x02\x62\x06proto3'
)




_AUTODRIVEREQUESTPROTO = _descriptor.Descriptor(
  name='AutoDriveRequestProto',
  full_name='AutoDriveRequestProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='AutoDriveRequestProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='source', full_name='AutoDriveRequestProto.source', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plan', full_name='AutoDriveRequestProto.plan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='datum', full_name='AutoDriveRequestProto.datum', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18,
  serialized_end=108,
)


_STOPREQUESTPROTO = _descriptor.Descriptor(
  name='StopRequestProto',
  full_name='StopRequestProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=110,
  serialized_end=128,
)


_MANUALDRIVEREQUESTPROTO = _descriptor.Descriptor(
  name='ManualDriveRequestProto',
  full_name='ManualDriveRequestProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='ManualDriveRequestProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='source', full_name='ManualDriveRequestProto.source', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='forward', full_name='ManualDriveRequestProto.forward', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steer', full_name='ManualDriveRequestProto.steer', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=130,
  serialized_end=225,
)


_UPDATEDRIVECONTROLLERREQUESTPROTO = _descriptor.Descriptor(
  name='UpdateDriveControllerRequestProto',
  full_name='UpdateDriveControllerRequestProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='UpdateDriveControllerRequestProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='controller', full_name='UpdateDriveControllerRequestProto.controller', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=227,
  serialized_end=304,
)


_UPDATESPEEDLIMITPCTREQUESTPROTO = _descriptor.Descriptor(
  name='UpdateSpeedLimitPctRequestProto',
  full_name='UpdateSpeedLimitPctRequestProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='UpdateSpeedLimitPctRequestProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='source', full_name='UpdateSpeedLimitPctRequestProto.source', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_limit_pct', full_name='UpdateSpeedLimitPctRequestProto.speed_limit_pct', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=306,
  serialized_end=402,
)

DESCRIPTOR.message_types_by_name['AutoDriveRequestProto'] = _AUTODRIVEREQUESTPROTO
DESCRIPTOR.message_types_by_name['StopRequestProto'] = _STOPREQUESTPROTO
DESCRIPTOR.message_types_by_name['ManualDriveRequestProto'] = _MANUALDRIVEREQUESTPROTO
DESCRIPTOR.message_types_by_name['UpdateDriveControllerRequestProto'] = _UPDATEDRIVECONTROLLERREQUESTPROTO
DESCRIPTOR.message_types_by_name['UpdateSpeedLimitPctRequestProto'] = _UPDATESPEEDLIMITPCTREQUESTPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AutoDriveRequestProto = _reflection.GeneratedProtocolMessageType('AutoDriveRequestProto', (_message.Message,), {
  'DESCRIPTOR' : _AUTODRIVEREQUESTPROTO,
  '__module__' : 'requests_pb2'
  # @@protoc_insertion_point(class_scope:AutoDriveRequestProto)
  })
_sym_db.RegisterMessage(AutoDriveRequestProto)

StopRequestProto = _reflection.GeneratedProtocolMessageType('StopRequestProto', (_message.Message,), {
  'DESCRIPTOR' : _STOPREQUESTPROTO,
  '__module__' : 'requests_pb2'
  # @@protoc_insertion_point(class_scope:StopRequestProto)
  })
_sym_db.RegisterMessage(StopRequestProto)

ManualDriveRequestProto = _reflection.GeneratedProtocolMessageType('ManualDriveRequestProto', (_message.Message,), {
  'DESCRIPTOR' : _MANUALDRIVEREQUESTPROTO,
  '__module__' : 'requests_pb2'
  # @@protoc_insertion_point(class_scope:ManualDriveRequestProto)
  })
_sym_db.RegisterMessage(ManualDriveRequestProto)

UpdateDriveControllerRequestProto = _reflection.GeneratedProtocolMessageType('UpdateDriveControllerRequestProto', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEDRIVECONTROLLERREQUESTPROTO,
  '__module__' : 'requests_pb2'
  # @@protoc_insertion_point(class_scope:UpdateDriveControllerRequestProto)
  })
_sym_db.RegisterMessage(UpdateDriveControllerRequestProto)

UpdateSpeedLimitPctRequestProto = _reflection.GeneratedProtocolMessageType('UpdateSpeedLimitPctRequestProto', (_message.Message,), {
  'DESCRIPTOR' : _UPDATESPEEDLIMITPCTREQUESTPROTO,
  '__module__' : 'requests_pb2'
  # @@protoc_insertion_point(class_scope:UpdateSpeedLimitPctRequestProto)
  })
_sym_db.RegisterMessage(UpdateSpeedLimitPctRequestProto)


# @@protoc_insertion_point(module_scope)

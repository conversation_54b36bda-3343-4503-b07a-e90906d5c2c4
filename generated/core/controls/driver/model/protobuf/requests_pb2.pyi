"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class AutoDriveRequestProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    source: typing___Text = ...
    plan: typing___Text = ...
    datum: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        source : typing___Optional[typing___Text] = None,
        plan : typing___Optional[typing___Text] = None,
        datum : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"datum",b"datum",u"plan",b"plan",u"source",b"source",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___AutoDriveRequestProto = AutoDriveRequestProto

class StopRequestProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StopRequestProto = StopRequestProto

class ManualDriveRequestProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    source: typing___Text = ...
    forward: builtin___float = ...
    steer: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        source : typing___Optional[typing___Text] = None,
        forward : typing___Optional[builtin___float] = None,
        steer : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"forward",b"forward",u"source",b"source",u"steer",b"steer",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___ManualDriveRequestProto = ManualDriveRequestProto

class UpdateDriveControllerRequestProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    controller: typing___Text = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        controller : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"controller",b"controller",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___UpdateDriveControllerRequestProto = UpdateDriveControllerRequestProto

class UpdateSpeedLimitPctRequestProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    source: typing___Text = ...
    speed_limit_pct: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        source : typing___Optional[typing___Text] = None,
        speed_limit_pct : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"source",b"source",u"speed_limit_pct",b"speed_limit_pct",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___UpdateSpeedLimitPctRequestProto = UpdateSpeedLimitPctRequestProto

# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Type

from generated.core.controls.driver.model.protobuf.messages_pb2 import PixelDistanceMessageProto
from lib.common.protocol.codegen.compile.python.validation import required
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin
from lib.common.time.time import TimestampedObject


class PixelDistanceMessage(TimestampedObject, ProtobufSerializationMixin[PixelDistanceMessageProto]):
    """
    Auto-generated PixelDistanceMessage
    """

    def __init__(self, *, timestamp_ms: int, pixels: int):

        # timestamp_ms
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

        # pixels
        required(pixels)
        self._pixels: int = pixels

    @classmethod
    def proto_type(cls) -> Type[PixelDistanceMessageProto]:
        return PixelDistanceMessageProto

    @property
    def pixels(self) -> int:
        return self._pixels

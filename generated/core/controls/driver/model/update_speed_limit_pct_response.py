# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Type

from generated.core.controls.driver.model.protobuf.responses_pb2 import UpdateSpeedLimitPctResponseProto
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin
from lib.common.time.time import TimestampedObject


class UpdateSpeedLimitPctResponse(TimestampedObject, ProtobufSerializationMixin[UpdateSpeedLimitPctResponseProto]):
    """
    Auto-generated UpdateSpeedLimitPctResponse
    """

    def __init__(self, *, timestamp_ms: int):

        # timestamp_ms
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

    @classmethod
    def proto_type(cls) -> Type[UpdateSpeedLimitPctResponseProto]:
        return UpdateSpeedLimitPctResponseProto

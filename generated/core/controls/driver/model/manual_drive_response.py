# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Type

from generated.core.controls.driver.model.protobuf.responses_pb2 import ManualDriveResponseProto
from lib.common.protocol.codegen.compile.python.validation import required
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin


class ManualDriveResponse(ProtobufSerializationMixin[ManualDriveResponseProto]):
    """
    Auto-generated ManualDriveResponse
    """

    def __init__(self, *, positive_control: bool):

        # positive_control
        required(positive_control)
        self._positive_control: bool = positive_control

    @classmethod
    def proto_type(cls) -> Type[ManualDriveResponseProto]:
        return ManualDriveResponseProto

    @property
    def positive_control(self) -> bool:
        return self._positive_control

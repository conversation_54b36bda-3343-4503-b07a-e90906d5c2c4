# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Type

from generated.core.controls.driver.model.protobuf.requests_pb2 import UpdateSpeedLimitPctRequestProto
from lib.common.protocol.codegen.compile.python.validation import positive, required
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin
from lib.common.time.time import TimestampedObject


class UpdateSpeedLimitPctRequest(TimestampedObject, ProtobufSerializationMixin[UpdateSpeedLimitPctRequestProto]):
    """
    Auto-generated UpdateSpeedLimitPctRequest
    """

    def __init__(self, *, timestamp_ms: int, source: str, speed_limit_pct: float):

        # timestamp_ms
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

        # source
        required(source)
        self._source: str = source

        # speed_limit_pct
        required(speed_limit_pct)
        positive(speed_limit_pct)
        self._speed_limit_pct: float = speed_limit_pct

    @classmethod
    def proto_type(cls) -> Type[UpdateSpeedLimitPctRequestProto]:
        return UpdateSpeedLimitPctRequestProto

    @property
    def source(self) -> str:
        return self._source

    @property
    def speed_limit_pct(self) -> float:
        return self._speed_limit_pct

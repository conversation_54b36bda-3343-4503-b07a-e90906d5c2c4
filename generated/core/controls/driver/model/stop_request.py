# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Type

from generated.core.controls.driver.model.protobuf.requests_pb2 import StopRequestProto
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin


class StopRequest(ProtobufSerializationMixin[StopRequestProto]):
    """
    Auto-generated StopRequest
    """

    @classmethod
    def proto_type(cls) -> Type[StopRequestProto]:
        return StopRequestProto

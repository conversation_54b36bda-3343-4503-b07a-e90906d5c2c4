###############################################################################
# Variables
###############################################################################

BOLD=01;
END=\033[0m
#RED=\033[$(BOLD)31m
#GREEN=\033[$(BOLD)32m
YELLOW=\033[$(BOLD)33m
#BLUE=\033[$(BOLD)34m
#MAGENTA=\033[${BOLD}35m
#CYAN=\033[$(BOLD)36m

SERVICE=driver
REQUESTS=requests
RESPONSES=responses

FILE_OWNER=$(firstword ${USER} ${MAKA_USER})

# directories
ROOT_DIR=$(shell realpath ../../../../..)
GENERATED_ROOT=$(ROOT_DIR)/generated/core/controls/$(SERVICE)/model
YAML_SRC_DIR=$(ROOT_DIR)/core/controls/$(SERVICE)/model/yaml

# PYTHON
PYTHONPATH=$(ROOT_DIR)
PYTHON=PYTHONPATH=$(PYTHONPATH) python

# COMPILERS
# note: * syntax works because they are source files and won't be deleted
COMPOSEC_FILES=$(wildcard $(ROOT_DIR)/lib/common/protocol/**/*.py)

# Source files
# note: * syntax works because they are source files and won't be deleted
YAML_SRC_FILES=$(wildcard $(YAML_SRC_DIR)/*.yaml)
PYTHON_DEPS=$(PROTO_SRC_FILES) $(YAML_SRC_FILES) $(COMPOSEC_FILES)

# Generated python files
GENERATED_PYTHON_DIR=$(GENERATED_ROOT)

# generated python files (cannot use * syntax because they might not be generated yet
GENERATED_PYTHON_COMPOSE_FILES_PY=$(shell sed 's/yaml/py/g'<$(YAML_SRC_FILES))
GENERATED_PYTHON_FILES=$(GENERATED_PYTHON_FILES_PY)

# Generate javascript
GENERATED_JAVASCRIPT_OUT=$(GENERATED_ROOT)/javascript
PROTOC_JAVASCRIPT_ARGS=--js_out=$(GENERATED_JAVASCRIPT_OUT)

###############################################################################
# Targets
###############################################################################

# debug variables
print-%  : ; @echo $* = $($*)

all: python clean_up_permissions

clean_up_permissions:
	-sudo chown -R $(FILE_OWNER):$(FILE_OWNER) $(GENERATED_PYTHON_DIR)

# python
# TODO dockerize
python: $(PYTHON_DEPS)
	# create dir
	mkdir -p $(GENERATED_PYTHON_DIR)
	# codegen
	$(PYTHON) -m lib.common.protocol.codegen.compile.main \
	    --input         $(YAML_SRC_FILES) \
	    --output        $(GENERATED_PYTHON_DIR) \
	    --python-out    \
	    --colorize


# TODO JavaScript
# Purposefully committing this but not the generated output
#
# Scaffolding seen below but still need to figure out how to actually link it into web code
#
javascript: $(PROTOBUF_SRC_FILE)
	# javascript
	mkdir -p $(GENERATED_JAVASCRIPT_OUT)
	protoc $(PROTOC_JAVASCRIPT_ARGS) $(PROTO_PATH_ARGS)

clean:
	rm -rf $(GENERATED_ROOT)/*

.PHONY: all clean python

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: core/controls/driver/drivebot/proto/drivebot.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='core/controls/driver/drivebot/proto/drivebot.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n2core/controls/driver/drivebot/proto/drivebot.proto\"\xc7\x01\n\x0c\x44riveRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\t\x12\x19\n\x0cvelocity_mph\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12!\n\x14left_wheel_angle_deg\x18\x03 \x01(\x02H\x01\x88\x01\x01\x12\"\n\x15right_wheel_angle_deg\x18\x04 \x01(\x02H\x02\x88\x01\x01\x42\x0f\n\r_velocity_mphB\x17\n\x15_left_wheel_angle_degB\x18\n\x16_right_wheel_angle_deg\"\x0f\n\rDriveResponse\"\x12\n\x10GetStatusRequest\"\xb9\x02\n\x11GetStatusResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x15\n\x05state\x18\x02 \x01(\x0e\x32\x06.State\x12\x12\n\npc_control\x18\x03 \x01(\x08\x12#\n\x1btarget_left_wheel_angle_deg\x18\x04 \x01(\x02\x12#\n\x1b\x61\x63tual_left_wheel_angle_deg\x18\x05 \x01(\x02\x12$\n\x1ctarget_right_wheel_angle_deg\x18\x06 \x01(\x02\x12$\n\x1c\x61\x63tual_right_wheel_angle_deg\x18\x07 \x01(\x02\x12\x1b\n\x13target_velocity_mph\x18\x08 \x01(\x02\x12\x1b\n\x13\x61\x63tual_velocity_mph\x18\t \x01(\x02\x12\x13\n\x0bodometer_in\x18\n \x01(\x02\"3\n\x0b\x42uttonPress\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x0e\n\x06\x62utton\x18\x02 \x01(\t\"\x19\n\x17GetButtonPressesRequest\"@\n\x18GetButtonPressesResponse\x12$\n\x0e\x62utton_presses\x18\x01 \x03(\x0b\x32\x0c.ButtonPress\"\x92\x01\n\x15SetVelocityPidRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\t\x12\n\n\x02kp\x18\x02 \x01(\x02\x12\n\n\x02ki\x18\x03 \x01(\x02\x12\n\n\x02kd\x18\x04 \x01(\x02\x12\x16\n\x0estart_drive_ma\x18\x05 \x01(\x02\x12\x14\n\x0cmin_drive_ma\x18\x06 \x01(\x02\x12\x14\n\x0cmax_drive_ma\x18\x07 \x01(\x02\"\x18\n\x16SetVelocityPidResponse\"p\n\x12SetSteerPidRequest\x12\x11\n\tcaller_id\x18\x01 \x01(\t\x12\n\n\x02kp\x18\x02 \x01(\x02\x12\n\n\x02ki\x18\x03 \x01(\x02\x12\n\n\x02kd\x18\x04 \x01(\x02\x12#\n\x1bsteering_deadband_angle_deg\x18\x05 \x01(\x02\"\x15\n\x13SetSteerPidResponse*/\n\x05State\x12\x0b\n\x07STOPPED\x10\x00\x12\x0c\n\x08STOPPING\x10\x01\x12\x0b\n\x07\x44RIVING\x10\x02\x32\xba\x02\n\x0c\x44riveService\x12(\n\x05\x44rive\x12\r.DriveRequest\x1a\x0e.DriveResponse\"\x00\x12\x34\n\tGetStatus\x12\x11.GetStatusRequest\x1a\x12.GetStatusResponse\"\x00\x12\x43\n\x0eSetVelocityPid\x12\x16.SetVelocityPidRequest\x1a\x17.SetVelocityPidResponse\"\x00\x12:\n\x0bSetSteerPid\x12\x13.SetSteerPidRequest\x1a\x14.SetSteerPidResponse\"\x00\x12I\n\x10GetButtonPresses\x12\x18.GetButtonPressesRequest\x1a\x19.GetButtonPressesResponse\"\x00\x62\x06proto3'
)

_STATE = _descriptor.EnumDescriptor(
  name='State',
  full_name='State',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STOPPED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STOPPING', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DRIVING', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1067,
  serialized_end=1114,
)
_sym_db.RegisterEnumDescriptor(_STATE)

State = enum_type_wrapper.EnumTypeWrapper(_STATE)
STOPPED = 0
STOPPING = 1
DRIVING = 2



_DRIVEREQUEST = _descriptor.Descriptor(
  name='DriveRequest',
  full_name='DriveRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='caller_id', full_name='DriveRequest.caller_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity_mph', full_name='DriveRequest.velocity_mph', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='left_wheel_angle_deg', full_name='DriveRequest.left_wheel_angle_deg', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='right_wheel_angle_deg', full_name='DriveRequest.right_wheel_angle_deg', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_velocity_mph', full_name='DriveRequest._velocity_mph',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_left_wheel_angle_deg', full_name='DriveRequest._left_wheel_angle_deg',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_right_wheel_angle_deg', full_name='DriveRequest._right_wheel_angle_deg',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=55,
  serialized_end=254,
)


_DRIVERESPONSE = _descriptor.Descriptor(
  name='DriveResponse',
  full_name='DriveResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=256,
  serialized_end=271,
)


_GETSTATUSREQUEST = _descriptor.Descriptor(
  name='GetStatusRequest',
  full_name='GetStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=273,
  serialized_end=291,
)


_GETSTATUSRESPONSE = _descriptor.Descriptor(
  name='GetStatusResponse',
  full_name='GetStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='GetStatusResponse.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='GetStatusResponse.state', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pc_control', full_name='GetStatusResponse.pc_control', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_left_wheel_angle_deg', full_name='GetStatusResponse.target_left_wheel_angle_deg', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='actual_left_wheel_angle_deg', full_name='GetStatusResponse.actual_left_wheel_angle_deg', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_right_wheel_angle_deg', full_name='GetStatusResponse.target_right_wheel_angle_deg', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='actual_right_wheel_angle_deg', full_name='GetStatusResponse.actual_right_wheel_angle_deg', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_velocity_mph', full_name='GetStatusResponse.target_velocity_mph', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='actual_velocity_mph', full_name='GetStatusResponse.actual_velocity_mph', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='odometer_in', full_name='GetStatusResponse.odometer_in', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=294,
  serialized_end=607,
)


_BUTTONPRESS = _descriptor.Descriptor(
  name='ButtonPress',
  full_name='ButtonPress',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='ButtonPress.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='button', full_name='ButtonPress.button', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=609,
  serialized_end=660,
)


_GETBUTTONPRESSESREQUEST = _descriptor.Descriptor(
  name='GetButtonPressesRequest',
  full_name='GetButtonPressesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=662,
  serialized_end=687,
)


_GETBUTTONPRESSESRESPONSE = _descriptor.Descriptor(
  name='GetButtonPressesResponse',
  full_name='GetButtonPressesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='button_presses', full_name='GetButtonPressesResponse.button_presses', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=689,
  serialized_end=753,
)


_SETVELOCITYPIDREQUEST = _descriptor.Descriptor(
  name='SetVelocityPidRequest',
  full_name='SetVelocityPidRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='caller_id', full_name='SetVelocityPidRequest.caller_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='kp', full_name='SetVelocityPidRequest.kp', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ki', full_name='SetVelocityPidRequest.ki', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='kd', full_name='SetVelocityPidRequest.kd', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_drive_ma', full_name='SetVelocityPidRequest.start_drive_ma', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_drive_ma', full_name='SetVelocityPidRequest.min_drive_ma', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_drive_ma', full_name='SetVelocityPidRequest.max_drive_ma', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=756,
  serialized_end=902,
)


_SETVELOCITYPIDRESPONSE = _descriptor.Descriptor(
  name='SetVelocityPidResponse',
  full_name='SetVelocityPidResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=904,
  serialized_end=928,
)


_SETSTEERPIDREQUEST = _descriptor.Descriptor(
  name='SetSteerPidRequest',
  full_name='SetSteerPidRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='caller_id', full_name='SetSteerPidRequest.caller_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='kp', full_name='SetSteerPidRequest.kp', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ki', full_name='SetSteerPidRequest.ki', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='kd', full_name='SetSteerPidRequest.kd', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steering_deadband_angle_deg', full_name='SetSteerPidRequest.steering_deadband_angle_deg', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=930,
  serialized_end=1042,
)


_SETSTEERPIDRESPONSE = _descriptor.Descriptor(
  name='SetSteerPidResponse',
  full_name='SetSteerPidResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1044,
  serialized_end=1065,
)

_DRIVEREQUEST.oneofs_by_name['_velocity_mph'].fields.append(
  _DRIVEREQUEST.fields_by_name['velocity_mph'])
_DRIVEREQUEST.fields_by_name['velocity_mph'].containing_oneof = _DRIVEREQUEST.oneofs_by_name['_velocity_mph']
_DRIVEREQUEST.oneofs_by_name['_left_wheel_angle_deg'].fields.append(
  _DRIVEREQUEST.fields_by_name['left_wheel_angle_deg'])
_DRIVEREQUEST.fields_by_name['left_wheel_angle_deg'].containing_oneof = _DRIVEREQUEST.oneofs_by_name['_left_wheel_angle_deg']
_DRIVEREQUEST.oneofs_by_name['_right_wheel_angle_deg'].fields.append(
  _DRIVEREQUEST.fields_by_name['right_wheel_angle_deg'])
_DRIVEREQUEST.fields_by_name['right_wheel_angle_deg'].containing_oneof = _DRIVEREQUEST.oneofs_by_name['_right_wheel_angle_deg']
_GETSTATUSRESPONSE.fields_by_name['state'].enum_type = _STATE
_GETBUTTONPRESSESRESPONSE.fields_by_name['button_presses'].message_type = _BUTTONPRESS
DESCRIPTOR.message_types_by_name['DriveRequest'] = _DRIVEREQUEST
DESCRIPTOR.message_types_by_name['DriveResponse'] = _DRIVERESPONSE
DESCRIPTOR.message_types_by_name['GetStatusRequest'] = _GETSTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetStatusResponse'] = _GETSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['ButtonPress'] = _BUTTONPRESS
DESCRIPTOR.message_types_by_name['GetButtonPressesRequest'] = _GETBUTTONPRESSESREQUEST
DESCRIPTOR.message_types_by_name['GetButtonPressesResponse'] = _GETBUTTONPRESSESRESPONSE
DESCRIPTOR.message_types_by_name['SetVelocityPidRequest'] = _SETVELOCITYPIDREQUEST
DESCRIPTOR.message_types_by_name['SetVelocityPidResponse'] = _SETVELOCITYPIDRESPONSE
DESCRIPTOR.message_types_by_name['SetSteerPidRequest'] = _SETSTEERPIDREQUEST
DESCRIPTOR.message_types_by_name['SetSteerPidResponse'] = _SETSTEERPIDRESPONSE
DESCRIPTOR.enum_types_by_name['State'] = _STATE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DriveRequest = _reflection.GeneratedProtocolMessageType('DriveRequest', (_message.Message,), {
  'DESCRIPTOR' : _DRIVEREQUEST,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:DriveRequest)
  })
_sym_db.RegisterMessage(DriveRequest)

DriveResponse = _reflection.GeneratedProtocolMessageType('DriveResponse', (_message.Message,), {
  'DESCRIPTOR' : _DRIVERESPONSE,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:DriveResponse)
  })
_sym_db.RegisterMessage(DriveResponse)

GetStatusRequest = _reflection.GeneratedProtocolMessageType('GetStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSTATUSREQUEST,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:GetStatusRequest)
  })
_sym_db.RegisterMessage(GetStatusRequest)

GetStatusResponse = _reflection.GeneratedProtocolMessageType('GetStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSTATUSRESPONSE,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:GetStatusResponse)
  })
_sym_db.RegisterMessage(GetStatusResponse)

ButtonPress = _reflection.GeneratedProtocolMessageType('ButtonPress', (_message.Message,), {
  'DESCRIPTOR' : _BUTTONPRESS,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:ButtonPress)
  })
_sym_db.RegisterMessage(ButtonPress)

GetButtonPressesRequest = _reflection.GeneratedProtocolMessageType('GetButtonPressesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETBUTTONPRESSESREQUEST,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:GetButtonPressesRequest)
  })
_sym_db.RegisterMessage(GetButtonPressesRequest)

GetButtonPressesResponse = _reflection.GeneratedProtocolMessageType('GetButtonPressesResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETBUTTONPRESSESRESPONSE,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:GetButtonPressesResponse)
  })
_sym_db.RegisterMessage(GetButtonPressesResponse)

SetVelocityPidRequest = _reflection.GeneratedProtocolMessageType('SetVelocityPidRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETVELOCITYPIDREQUEST,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:SetVelocityPidRequest)
  })
_sym_db.RegisterMessage(SetVelocityPidRequest)

SetVelocityPidResponse = _reflection.GeneratedProtocolMessageType('SetVelocityPidResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETVELOCITYPIDRESPONSE,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:SetVelocityPidResponse)
  })
_sym_db.RegisterMessage(SetVelocityPidResponse)

SetSteerPidRequest = _reflection.GeneratedProtocolMessageType('SetSteerPidRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETSTEERPIDREQUEST,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:SetSteerPidRequest)
  })
_sym_db.RegisterMessage(SetSteerPidRequest)

SetSteerPidResponse = _reflection.GeneratedProtocolMessageType('SetSteerPidResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETSTEERPIDRESPONSE,
  '__module__' : 'core.controls.driver.drivebot.proto.drivebot_pb2'
  # @@protoc_insertion_point(class_scope:SetSteerPidResponse)
  })
_sym_db.RegisterMessage(SetSteerPidResponse)



_DRIVESERVICE = _descriptor.ServiceDescriptor(
  name='DriveService',
  full_name='DriveService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1117,
  serialized_end=1431,
  methods=[
  _descriptor.MethodDescriptor(
    name='Drive',
    full_name='DriveService.Drive',
    index=0,
    containing_service=None,
    input_type=_DRIVEREQUEST,
    output_type=_DRIVERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetStatus',
    full_name='DriveService.GetStatus',
    index=1,
    containing_service=None,
    input_type=_GETSTATUSREQUEST,
    output_type=_GETSTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetVelocityPid',
    full_name='DriveService.SetVelocityPid',
    index=2,
    containing_service=None,
    input_type=_SETVELOCITYPIDREQUEST,
    output_type=_SETVELOCITYPIDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetSteerPid',
    full_name='DriveService.SetSteerPid',
    index=3,
    containing_service=None,
    input_type=_SETSTEERPIDREQUEST,
    output_type=_SETSTEERPIDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetButtonPresses',
    full_name='DriveService.GetButtonPresses',
    index=4,
    containing_service=None,
    input_type=_GETBUTTONPRESSESREQUEST,
    output_type=_GETBUTTONPRESSESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_DRIVESERVICE)

DESCRIPTOR.services_by_name['DriveService'] = _DRIVESERVICE

# @@protoc_insertion_point(module_scope)

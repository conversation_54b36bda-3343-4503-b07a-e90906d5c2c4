"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

StateValue = typing___NewType('StateValue', builtin___int)
type___StateValue = StateValue
State: _State
class _State(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[StateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    STOPPED = typing___cast(StateValue, 0)
    STOPPING = typing___cast(StateValue, 1)
    DRIVING = typing___cast(StateValue, 2)
STOPPED = typing___cast(StateValue, 0)
STOPPING = typing___cast(StateValue, 1)
DRIVING = typing___cast(StateValue, 2)

class DriveRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    caller_id: typing___Text = ...
    velocity_mph: builtin___float = ...
    left_wheel_angle_deg: builtin___float = ...
    right_wheel_angle_deg: builtin___float = ...

    def __init__(self,
        *,
        caller_id : typing___Optional[typing___Text] = None,
        velocity_mph : typing___Optional[builtin___float] = None,
        left_wheel_angle_deg : typing___Optional[builtin___float] = None,
        right_wheel_angle_deg : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_left_wheel_angle_deg",b"_left_wheel_angle_deg",u"_right_wheel_angle_deg",b"_right_wheel_angle_deg",u"_velocity_mph",b"_velocity_mph",u"left_wheel_angle_deg",b"left_wheel_angle_deg",u"right_wheel_angle_deg",b"right_wheel_angle_deg",u"velocity_mph",b"velocity_mph"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_left_wheel_angle_deg",b"_left_wheel_angle_deg",u"_right_wheel_angle_deg",b"_right_wheel_angle_deg",u"_velocity_mph",b"_velocity_mph",u"caller_id",b"caller_id",u"left_wheel_angle_deg",b"left_wheel_angle_deg",u"right_wheel_angle_deg",b"right_wheel_angle_deg",u"velocity_mph",b"velocity_mph"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_left_wheel_angle_deg",b"_left_wheel_angle_deg"]) -> typing_extensions___Literal["left_wheel_angle_deg"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_right_wheel_angle_deg",b"_right_wheel_angle_deg"]) -> typing_extensions___Literal["right_wheel_angle_deg"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_velocity_mph",b"_velocity_mph"]) -> typing_extensions___Literal["velocity_mph"]: ...
type___DriveRequest = DriveRequest

class DriveResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___DriveResponse = DriveResponse

class GetStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetStatusRequest = GetStatusRequest

class GetStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    state: type___StateValue = ...
    pc_control: builtin___bool = ...
    target_left_wheel_angle_deg: builtin___float = ...
    actual_left_wheel_angle_deg: builtin___float = ...
    target_right_wheel_angle_deg: builtin___float = ...
    actual_right_wheel_angle_deg: builtin___float = ...
    target_velocity_mph: builtin___float = ...
    actual_velocity_mph: builtin___float = ...
    odometer_in: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        state : typing___Optional[type___StateValue] = None,
        pc_control : typing___Optional[builtin___bool] = None,
        target_left_wheel_angle_deg : typing___Optional[builtin___float] = None,
        actual_left_wheel_angle_deg : typing___Optional[builtin___float] = None,
        target_right_wheel_angle_deg : typing___Optional[builtin___float] = None,
        actual_right_wheel_angle_deg : typing___Optional[builtin___float] = None,
        target_velocity_mph : typing___Optional[builtin___float] = None,
        actual_velocity_mph : typing___Optional[builtin___float] = None,
        odometer_in : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"actual_left_wheel_angle_deg",b"actual_left_wheel_angle_deg",u"actual_right_wheel_angle_deg",b"actual_right_wheel_angle_deg",u"actual_velocity_mph",b"actual_velocity_mph",u"odometer_in",b"odometer_in",u"pc_control",b"pc_control",u"state",b"state",u"target_left_wheel_angle_deg",b"target_left_wheel_angle_deg",u"target_right_wheel_angle_deg",b"target_right_wheel_angle_deg",u"target_velocity_mph",b"target_velocity_mph",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetStatusResponse = GetStatusResponse

class ButtonPress(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    button: typing___Text = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        button : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"button",b"button",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___ButtonPress = ButtonPress

class GetButtonPressesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetButtonPressesRequest = GetButtonPressesRequest

class GetButtonPressesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def button_presses(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ButtonPress]: ...

    def __init__(self,
        *,
        button_presses : typing___Optional[typing___Iterable[type___ButtonPress]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"button_presses",b"button_presses"]) -> None: ...
type___GetButtonPressesResponse = GetButtonPressesResponse

class SetVelocityPidRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    caller_id: typing___Text = ...
    kp: builtin___float = ...
    ki: builtin___float = ...
    kd: builtin___float = ...
    start_drive_ma: builtin___float = ...
    min_drive_ma: builtin___float = ...
    max_drive_ma: builtin___float = ...

    def __init__(self,
        *,
        caller_id : typing___Optional[typing___Text] = None,
        kp : typing___Optional[builtin___float] = None,
        ki : typing___Optional[builtin___float] = None,
        kd : typing___Optional[builtin___float] = None,
        start_drive_ma : typing___Optional[builtin___float] = None,
        min_drive_ma : typing___Optional[builtin___float] = None,
        max_drive_ma : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"caller_id",b"caller_id",u"kd",b"kd",u"ki",b"ki",u"kp",b"kp",u"max_drive_ma",b"max_drive_ma",u"min_drive_ma",b"min_drive_ma",u"start_drive_ma",b"start_drive_ma"]) -> None: ...
type___SetVelocityPidRequest = SetVelocityPidRequest

class SetVelocityPidResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetVelocityPidResponse = SetVelocityPidResponse

class SetSteerPidRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    caller_id: typing___Text = ...
    kp: builtin___float = ...
    ki: builtin___float = ...
    kd: builtin___float = ...
    steering_deadband_angle_deg: builtin___float = ...

    def __init__(self,
        *,
        caller_id : typing___Optional[typing___Text] = None,
        kp : typing___Optional[builtin___float] = None,
        ki : typing___Optional[builtin___float] = None,
        kd : typing___Optional[builtin___float] = None,
        steering_deadband_angle_deg : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"caller_id",b"caller_id",u"kd",b"kd",u"ki",b"ki",u"kp",b"kp",u"steering_deadband_angle_deg",b"steering_deadband_angle_deg"]) -> None: ...
type___SetSteerPidRequest = SetSteerPidRequest

class SetSteerPidResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetSteerPidResponse = SetSteerPidResponse

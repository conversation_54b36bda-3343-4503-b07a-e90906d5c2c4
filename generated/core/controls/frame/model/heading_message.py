# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Type

from generated.core.controls.frame.model.protobuf.frame_messages_pb2 import HeadingMessageProto
from lib.common.protocol.codegen.compile.python.validation import required, zero_to_360
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin
from lib.common.time.time import TimestampedObject


class HeadingMessage(TimestampedObject, ProtobufSerializationMixin[HeadingMessageProto]):
    """
    Auto-generated HeadingMessage
    """

    def __init__(self, *, timestamp_ms: int, heading: float):

        # timestamp_ms
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

        # heading
        required(heading)
        zero_to_360(heading)
        self._heading: float = heading

    @classmethod
    def proto_type(cls) -> Type[HeadingMessageProto]:
        return HeadingMessageProto

    @property
    def heading(self) -> float:
        return self._heading

# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Type

from generated.core.controls.frame.model.protobuf.frame_messages_pb2 import VelocityMessageProto
from lib.common.protocol.codegen.compile.python.validation import required
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin
from lib.common.time.time import TimestampedObject


class VelocityMessage(TimestampedObject, ProtobufSerializationMixin[VelocityMessageProto]):
    """
    Auto-generated VelocityMessage
    """

    def __init__(self, *, timestamp_ms: int, x: float, y: float, z: float):

        # timestamp_ms
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

        # x
        required(x)
        self._x: float = x

        # y
        required(y)
        self._y: float = y

        # z
        required(z)
        self._z: float = z

    @classmethod
    def proto_type(cls) -> Type[VelocityMessageProto]:
        return VelocityMessageProto

    @property
    def x(self) -> float:
        return self._x

    @property
    def y(self) -> float:
        return self._y

    @property
    def z(self) -> float:
        return self._z

# version: 1
# Generated by the compose compiler.  DO NOT EDIT!
from typing import Type

from generated.core.controls.frame.model.protobuf.frame_messages_pb2 import GeopositionLatLonAltMessageProto
from lib.common.protocol.codegen.compile.python.validation import required
from lib.common.protocol.protobuf.proto import ProtobufSerializationMixin
from lib.common.time.time import TimestampedObject


class GeopositionLatLonAltMessage(TimestampedObject, ProtobufSerializationMixin[GeopositionLatLonAltMessageProto]):
    """
    Auto-generated GeopositionLatLonAltMessage
    """

    def __init__(self, *, timestamp_ms: int, lat: float, lon: float, alt: float):

        # timestamp_ms
        TimestampedObject.__init__(self, timestamp_ms=timestamp_ms)

        # lat
        required(lat)
        self._lat: float = lat

        # lon
        required(lon)
        self._lon: float = lon

        # alt
        required(alt)
        self._alt: float = alt

    @classmethod
    def proto_type(cls) -> Type[GeopositionLatLonAltMessageProto]:
        return GeopositionLatLonAltMessageProto

    @property
    def lat(self) -> float:
        return self._lat

    @property
    def lon(self) -> float:
        return self._lon

    @property
    def alt(self) -> float:
        return self._alt

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frame_messages.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='frame_messages.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x14\x66rame_messages.proto\"Q\n\x18\x41\x63\x63\x65lerationMessageProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\x12\t\n\x01z\x18\x04 \x01(\x02\"T\n\x1b\x41ngularVelocityMessageProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\x12\t\n\x01z\x18\x04 \x01(\x02\"T\n\x1bGeopositionEcefMessageProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\x12\t\n\x01z\x18\x04 \x01(\x02\"_\n GeopositionLatLonAltMessageProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x0b\n\x03lat\x18\x02 \x01(\x02\x12\x0b\n\x03lon\x18\x03 \x01(\x02\x12\x0b\n\x03\x61lt\x18\x04 \x01(\x02\"<\n\x13HeadingMessageProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x0f\n\x07heading\x18\x02 \x01(\x02\"H\n\x0fMagMessageProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\x12\t\n\x01z\x18\x04 \x01(\x02\"M\n\x14VelocityMessageProto\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\x12\t\n\x01z\x18\x04 \x01(\x02\x62\x06proto3'
)




_ACCELERATIONMESSAGEPROTO = _descriptor.Descriptor(
  name='AccelerationMessageProto',
  full_name='AccelerationMessageProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='AccelerationMessageProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x', full_name='AccelerationMessageProto.x', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='AccelerationMessageProto.y', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='AccelerationMessageProto.z', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=24,
  serialized_end=105,
)


_ANGULARVELOCITYMESSAGEPROTO = _descriptor.Descriptor(
  name='AngularVelocityMessageProto',
  full_name='AngularVelocityMessageProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='AngularVelocityMessageProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x', full_name='AngularVelocityMessageProto.x', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='AngularVelocityMessageProto.y', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='AngularVelocityMessageProto.z', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=107,
  serialized_end=191,
)


_GEOPOSITIONECEFMESSAGEPROTO = _descriptor.Descriptor(
  name='GeopositionEcefMessageProto',
  full_name='GeopositionEcefMessageProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='GeopositionEcefMessageProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x', full_name='GeopositionEcefMessageProto.x', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='GeopositionEcefMessageProto.y', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='GeopositionEcefMessageProto.z', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=193,
  serialized_end=277,
)


_GEOPOSITIONLATLONALTMESSAGEPROTO = _descriptor.Descriptor(
  name='GeopositionLatLonAltMessageProto',
  full_name='GeopositionLatLonAltMessageProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='GeopositionLatLonAltMessageProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lat', full_name='GeopositionLatLonAltMessageProto.lat', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lon', full_name='GeopositionLatLonAltMessageProto.lon', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alt', full_name='GeopositionLatLonAltMessageProto.alt', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=279,
  serialized_end=374,
)


_HEADINGMESSAGEPROTO = _descriptor.Descriptor(
  name='HeadingMessageProto',
  full_name='HeadingMessageProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='HeadingMessageProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading', full_name='HeadingMessageProto.heading', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=376,
  serialized_end=436,
)


_MAGMESSAGEPROTO = _descriptor.Descriptor(
  name='MagMessageProto',
  full_name='MagMessageProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='MagMessageProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x', full_name='MagMessageProto.x', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='MagMessageProto.y', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='MagMessageProto.z', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=438,
  serialized_end=510,
)


_VELOCITYMESSAGEPROTO = _descriptor.Descriptor(
  name='VelocityMessageProto',
  full_name='VelocityMessageProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='VelocityMessageProto.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x', full_name='VelocityMessageProto.x', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='VelocityMessageProto.y', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='VelocityMessageProto.z', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=512,
  serialized_end=589,
)

DESCRIPTOR.message_types_by_name['AccelerationMessageProto'] = _ACCELERATIONMESSAGEPROTO
DESCRIPTOR.message_types_by_name['AngularVelocityMessageProto'] = _ANGULARVELOCITYMESSAGEPROTO
DESCRIPTOR.message_types_by_name['GeopositionEcefMessageProto'] = _GEOPOSITIONECEFMESSAGEPROTO
DESCRIPTOR.message_types_by_name['GeopositionLatLonAltMessageProto'] = _GEOPOSITIONLATLONALTMESSAGEPROTO
DESCRIPTOR.message_types_by_name['HeadingMessageProto'] = _HEADINGMESSAGEPROTO
DESCRIPTOR.message_types_by_name['MagMessageProto'] = _MAGMESSAGEPROTO
DESCRIPTOR.message_types_by_name['VelocityMessageProto'] = _VELOCITYMESSAGEPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AccelerationMessageProto = _reflection.GeneratedProtocolMessageType('AccelerationMessageProto', (_message.Message,), {
  'DESCRIPTOR' : _ACCELERATIONMESSAGEPROTO,
  '__module__' : 'frame_messages_pb2'
  # @@protoc_insertion_point(class_scope:AccelerationMessageProto)
  })
_sym_db.RegisterMessage(AccelerationMessageProto)

AngularVelocityMessageProto = _reflection.GeneratedProtocolMessageType('AngularVelocityMessageProto', (_message.Message,), {
  'DESCRIPTOR' : _ANGULARVELOCITYMESSAGEPROTO,
  '__module__' : 'frame_messages_pb2'
  # @@protoc_insertion_point(class_scope:AngularVelocityMessageProto)
  })
_sym_db.RegisterMessage(AngularVelocityMessageProto)

GeopositionEcefMessageProto = _reflection.GeneratedProtocolMessageType('GeopositionEcefMessageProto', (_message.Message,), {
  'DESCRIPTOR' : _GEOPOSITIONECEFMESSAGEPROTO,
  '__module__' : 'frame_messages_pb2'
  # @@protoc_insertion_point(class_scope:GeopositionEcefMessageProto)
  })
_sym_db.RegisterMessage(GeopositionEcefMessageProto)

GeopositionLatLonAltMessageProto = _reflection.GeneratedProtocolMessageType('GeopositionLatLonAltMessageProto', (_message.Message,), {
  'DESCRIPTOR' : _GEOPOSITIONLATLONALTMESSAGEPROTO,
  '__module__' : 'frame_messages_pb2'
  # @@protoc_insertion_point(class_scope:GeopositionLatLonAltMessageProto)
  })
_sym_db.RegisterMessage(GeopositionLatLonAltMessageProto)

HeadingMessageProto = _reflection.GeneratedProtocolMessageType('HeadingMessageProto', (_message.Message,), {
  'DESCRIPTOR' : _HEADINGMESSAGEPROTO,
  '__module__' : 'frame_messages_pb2'
  # @@protoc_insertion_point(class_scope:HeadingMessageProto)
  })
_sym_db.RegisterMessage(HeadingMessageProto)

MagMessageProto = _reflection.GeneratedProtocolMessageType('MagMessageProto', (_message.Message,), {
  'DESCRIPTOR' : _MAGMESSAGEPROTO,
  '__module__' : 'frame_messages_pb2'
  # @@protoc_insertion_point(class_scope:MagMessageProto)
  })
_sym_db.RegisterMessage(MagMessageProto)

VelocityMessageProto = _reflection.GeneratedProtocolMessageType('VelocityMessageProto', (_message.Message,), {
  'DESCRIPTOR' : _VELOCITYMESSAGEPROTO,
  '__module__' : 'frame_messages_pb2'
  # @@protoc_insertion_point(class_scope:VelocityMessageProto)
  })
_sym_db.RegisterMessage(VelocityMessageProto)


# @@protoc_insertion_point(module_scope)

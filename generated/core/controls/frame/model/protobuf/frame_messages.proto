// version: 1
// Generated by the compose compiler.  DO NOT EDIT!
syntax = "proto3";


message AccelerationMessageProto {
    uint64 timestamp_ms = 1;
    float x = 2;
    float y = 3;
    float z = 4;
}

message AngularVelocityMessageProto {
    uint64 timestamp_ms = 1;
    float x = 2;
    float y = 3;
    float z = 4;
}

message GeopositionEcefMessageProto {
    uint64 timestamp_ms = 1;
    float x = 2;
    float y = 3;
    float z = 4;
}

message GeopositionLatLonAltMessageProto {
    uint64 timestamp_ms = 1;
    float lat = 2;
    float lon = 3;
    float alt = 4;
}

message HeadingMessageProto {
    uint64 timestamp_ms = 1;
    float heading = 2;
}

message MagMessageProto {
    uint64 timestamp_ms = 1;
    float x = 2;
    float y = 3;
    float z = 4;
}

message VelocityMessageProto {
    uint64 timestamp_ms = 1;
    float x = 2;
    float y = 3;
    float z = 4;
}

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class AccelerationMessageProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    x: builtin___float = ...
    y: builtin___float = ...
    z: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        z : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms",u"x",b"x",u"y",b"y",u"z",b"z"]) -> None: ...
type___AccelerationMessageProto = AccelerationMessageProto

class AngularVelocityMessageProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    x: builtin___float = ...
    y: builtin___float = ...
    z: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        z : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms",u"x",b"x",u"y",b"y",u"z",b"z"]) -> None: ...
type___AngularVelocityMessageProto = AngularVelocityMessageProto

class GeopositionEcefMessageProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    x: builtin___float = ...
    y: builtin___float = ...
    z: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        z : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms",u"x",b"x",u"y",b"y",u"z",b"z"]) -> None: ...
type___GeopositionEcefMessageProto = GeopositionEcefMessageProto

class GeopositionLatLonAltMessageProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    lat: builtin___float = ...
    lon: builtin___float = ...
    alt: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        lat : typing___Optional[builtin___float] = None,
        lon : typing___Optional[builtin___float] = None,
        alt : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"alt",b"alt",u"lat",b"lat",u"lon",b"lon",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GeopositionLatLonAltMessageProto = GeopositionLatLonAltMessageProto

class HeadingMessageProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    heading: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        heading : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"heading",b"heading",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___HeadingMessageProto = HeadingMessageProto

class MagMessageProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    x: builtin___float = ...
    y: builtin___float = ...
    z: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        z : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms",u"x",b"x",u"y",b"y",u"z",b"z"]) -> None: ...
type___MagMessageProto = MagMessageProto

class VelocityMessageProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    x: builtin___float = ...
    y: builtin___float = ...
    z: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        z : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms",u"x",b"x",u"y",b"y",u"z",b"z"]) -> None: ...
type___VelocityMessageProto = VelocityMessageProto

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: weed_tracking/proto/weed_tracking.proto
#ifndef GRPC_weed_5ftracking_2fproto_2fweed_5ftracking_2eproto__INCLUDED
#define GRPC_weed_5ftracking_2fproto_2fweed_5ftracking_2eproto__INCLUDED

#include "weed_tracking/proto/weed_tracking.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace weed_tracking {

class WeedTrackingService final {
 public:
  static constexpr char const* service_full_name() {
    return "weed_tracking.WeedTrackingService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Ping(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::weed_tracking::PongReply* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PongReply>> AsyncPing(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PongReply>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PongReply>> PrepareAsyncPing(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PongReply>>(PrepareAsyncPingRaw(context, request, cq));
    }
    virtual ::grpc::Status GetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::weed_tracking::GetDetectionsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetDetectionsResponse>> AsyncGetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetDetectionsResponse>>(AsyncGetDetectionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetDetectionsResponse>> PrepareAsyncGetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetDetectionsResponse>>(PrepareAsyncGetDetectionsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::weed_tracking::GetTrajectoryMetadataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTrajectoryMetadataResponse>> AsyncGetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTrajectoryMetadataResponse>>(AsyncGetTrajectoryMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTrajectoryMetadataResponse>> PrepareAsyncGetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTrajectoryMetadataResponse>>(PrepareAsyncGetTrajectoryMetadataRaw(context, request, cq));
    }
    virtual ::grpc::Status UpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncUpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncUpdateBandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncUpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncUpdateBandsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::weed_tracking::GetBootedResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetBootedResponse>> AsyncGetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetBootedResponse>>(AsyncGetBootedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetBootedResponse>> PrepareAsyncGetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetBootedResponse>>(PrepareAsyncGetBootedRaw(context, request, cq));
    }
    // weeding diagnostics
    virtual ::grpc::Status GetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::DiagnosticsSnapshot* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>> AsyncGetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>>(AsyncGetCurrentTrajectoriesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>> PrepareAsyncGetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>>(PrepareAsyncGetCurrentTrajectoriesRaw(context, request, cq));
    }
    virtual ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncStartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncStartSavingCropLineDetectionReplayRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncStartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncStartSavingCropLineDetectionReplayRaw(context, request, cq));
    }
    virtual ::grpc::Status StartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncStartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncStartRecordingDiagnosticsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncStartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncStartRecordingDiagnosticsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::GetRecordingStatusResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetRecordingStatusResponse>> AsyncGetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetRecordingStatusResponse>>(AsyncGetDiagnosticsRecordingStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetRecordingStatusResponse>> PrepareAsyncGetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetRecordingStatusResponse>>(PrepareAsyncGetDiagnosticsRecordingStatusRaw(context, request, cq));
    }
    virtual ::grpc::Status RemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncRemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncRemoveRecordingsDirectoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncRemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncRemoveRecordingsDirectoryRaw(context, request, cq));
    }
    virtual ::grpc::Status StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncStartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncStartRecordingAimbotInputsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncStartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncStartRecordingAimbotInputsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::ConclusionCounter* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::ConclusionCounter>> AsyncGetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::ConclusionCounter>>(AsyncGetConclusionCounterRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::ConclusionCounter>> PrepareAsyncGetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::ConclusionCounter>>(PrepareAsyncGetConclusionCounterRaw(context, request, cq));
    }
    virtual ::grpc::Status GetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::BandDefinitions* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::BandDefinitions>> AsyncGetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::BandDefinitions>>(AsyncGetBandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::BandDefinitions>> PrepareAsyncGetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::BandDefinitions>>(PrepareAsyncGetBandsRaw(context, request, cq));
    }
    virtual ::grpc::Status StartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncStartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncStartPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncStartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncStartPlantCaptchaRaw(context, request, cq));
    }
    virtual ::grpc::Status GetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::PlantCaptchaStatusResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PlantCaptchaStatusResponse>> AsyncGetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PlantCaptchaStatusResponse>>(AsyncGetPlantCaptchaStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PlantCaptchaStatusResponse>> PrepareAsyncGetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PlantCaptchaStatusResponse>>(PrepareAsyncGetPlantCaptchaStatusRaw(context, request, cq));
    }
    virtual ::grpc::Status RemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncRemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncRemovePlantCaptchaDirectoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncRemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncRemovePlantCaptchaDirectoryRaw(context, request, cq));
    }
    virtual ::grpc::Status CancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncCancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncCancelPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncCancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncCancelPlantCaptchaRaw(context, request, cq));
    }
    virtual ::grpc::Status GetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::weed_tracking::GetTargetingEnabledResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTargetingEnabledResponse>> AsyncGetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTargetingEnabledResponse>>(AsyncGetTargetingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTargetingEnabledResponse>> PrepareAsyncGetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTargetingEnabledResponse>>(PrepareAsyncGetTargetingEnabledRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Ping(::grpc::ClientContext* context, const ::weed_tracking::PingRequest* request, ::weed_tracking::PongReply* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Ping(::grpc::ClientContext* context, const ::weed_tracking::PingRequest* request, ::weed_tracking::PongReply* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest* request, ::weed_tracking::GetDetectionsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest* request, ::weed_tracking::GetDetectionsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest* request, ::weed_tracking::GetTrajectoryMetadataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest* request, ::weed_tracking::GetTrajectoryMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void UpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest* request, ::weed_tracking::GetBootedResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest* request, ::weed_tracking::GetBootedResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // weeding diagnostics
      virtual void GetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::DiagnosticsSnapshot* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::DiagnosticsSnapshot* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::GetRecordingStatusResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::GetRecordingStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void RemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void RemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::ConclusionCounter* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::ConclusionCounter* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::BandDefinitions* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::BandDefinitions* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::PlantCaptchaStatusResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::PlantCaptchaStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void RemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void RemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void CancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest* request, ::weed_tracking::GetTargetingEnabledResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest* request, ::weed_tracking::GetTargetingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PongReply>* AsyncPingRaw(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PongReply>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetDetectionsResponse>* AsyncGetDetectionsRaw(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetDetectionsResponse>* PrepareAsyncGetDetectionsRaw(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTrajectoryMetadataResponse>* AsyncGetTrajectoryMetadataRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTrajectoryMetadataResponse>* PrepareAsyncGetTrajectoryMetadataRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncUpdateBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncUpdateBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetBootedResponse>* AsyncGetBootedRaw(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetBootedResponse>* PrepareAsyncGetBootedRaw(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>* AsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::DiagnosticsSnapshot>* PrepareAsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncStartRecordingDiagnosticsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncStartRecordingDiagnosticsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetRecordingStatusResponse>* AsyncGetDiagnosticsRecordingStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetRecordingStatusResponse>* PrepareAsyncGetDiagnosticsRecordingStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncRemoveRecordingsDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncRemoveRecordingsDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::ConclusionCounter>* AsyncGetConclusionCounterRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::ConclusionCounter>* PrepareAsyncGetConclusionCounterRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::BandDefinitions>* AsyncGetBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::BandDefinitions>* PrepareAsyncGetBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PlantCaptchaStatusResponse>* AsyncGetPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::PlantCaptchaStatusResponse>* PrepareAsyncGetPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncRemovePlantCaptchaDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncRemovePlantCaptchaDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTargetingEnabledResponse>* AsyncGetTargetingEnabledRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::GetTargetingEnabledResponse>* PrepareAsyncGetTargetingEnabledRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Ping(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::weed_tracking::PongReply* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::PongReply>> AsyncPing(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::PongReply>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::PongReply>> PrepareAsyncPing(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::PongReply>>(PrepareAsyncPingRaw(context, request, cq));
    }
    ::grpc::Status GetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::weed_tracking::GetDetectionsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetDetectionsResponse>> AsyncGetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetDetectionsResponse>>(AsyncGetDetectionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetDetectionsResponse>> PrepareAsyncGetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetDetectionsResponse>>(PrepareAsyncGetDetectionsRaw(context, request, cq));
    }
    ::grpc::Status GetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::weed_tracking::GetTrajectoryMetadataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTrajectoryMetadataResponse>> AsyncGetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTrajectoryMetadataResponse>>(AsyncGetTrajectoryMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTrajectoryMetadataResponse>> PrepareAsyncGetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTrajectoryMetadataResponse>>(PrepareAsyncGetTrajectoryMetadataRaw(context, request, cq));
    }
    ::grpc::Status UpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncUpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncUpdateBandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncUpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncUpdateBandsRaw(context, request, cq));
    }
    ::grpc::Status GetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::weed_tracking::GetBootedResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetBootedResponse>> AsyncGetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetBootedResponse>>(AsyncGetBootedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetBootedResponse>> PrepareAsyncGetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetBootedResponse>>(PrepareAsyncGetBootedRaw(context, request, cq));
    }
    ::grpc::Status GetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::DiagnosticsSnapshot* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>> AsyncGetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>>(AsyncGetCurrentTrajectoriesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>> PrepareAsyncGetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>>(PrepareAsyncGetCurrentTrajectoriesRaw(context, request, cq));
    }
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncStartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncStartSavingCropLineDetectionReplayRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncStartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncStartSavingCropLineDetectionReplayRaw(context, request, cq));
    }
    ::grpc::Status StartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncStartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncStartRecordingDiagnosticsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncStartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncStartRecordingDiagnosticsRaw(context, request, cq));
    }
    ::grpc::Status GetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::GetRecordingStatusResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetRecordingStatusResponse>> AsyncGetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetRecordingStatusResponse>>(AsyncGetDiagnosticsRecordingStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetRecordingStatusResponse>> PrepareAsyncGetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetRecordingStatusResponse>>(PrepareAsyncGetDiagnosticsRecordingStatusRaw(context, request, cq));
    }
    ::grpc::Status RemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncRemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncRemoveRecordingsDirectoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncRemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncRemoveRecordingsDirectoryRaw(context, request, cq));
    }
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncStartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncStartRecordingAimbotInputsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncStartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncStartRecordingAimbotInputsRaw(context, request, cq));
    }
    ::grpc::Status GetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::ConclusionCounter* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::ConclusionCounter>> AsyncGetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::ConclusionCounter>>(AsyncGetConclusionCounterRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::ConclusionCounter>> PrepareAsyncGetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::ConclusionCounter>>(PrepareAsyncGetConclusionCounterRaw(context, request, cq));
    }
    ::grpc::Status GetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::BandDefinitions* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::BandDefinitions>> AsyncGetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::BandDefinitions>>(AsyncGetBandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::BandDefinitions>> PrepareAsyncGetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::BandDefinitions>>(PrepareAsyncGetBandsRaw(context, request, cq));
    }
    ::grpc::Status StartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncStartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncStartPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncStartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncStartPlantCaptchaRaw(context, request, cq));
    }
    ::grpc::Status GetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::PlantCaptchaStatusResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::PlantCaptchaStatusResponse>> AsyncGetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::PlantCaptchaStatusResponse>>(AsyncGetPlantCaptchaStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::PlantCaptchaStatusResponse>> PrepareAsyncGetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::PlantCaptchaStatusResponse>>(PrepareAsyncGetPlantCaptchaStatusRaw(context, request, cq));
    }
    ::grpc::Status RemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncRemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncRemovePlantCaptchaDirectoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncRemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncRemovePlantCaptchaDirectoryRaw(context, request, cq));
    }
    ::grpc::Status CancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncCancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncCancelPlantCaptchaRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncCancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncCancelPlantCaptchaRaw(context, request, cq));
    }
    ::grpc::Status GetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::weed_tracking::GetTargetingEnabledResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTargetingEnabledResponse>> AsyncGetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTargetingEnabledResponse>>(AsyncGetTargetingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTargetingEnabledResponse>> PrepareAsyncGetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTargetingEnabledResponse>>(PrepareAsyncGetTargetingEnabledRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Ping(::grpc::ClientContext* context, const ::weed_tracking::PingRequest* request, ::weed_tracking::PongReply* response, std::function<void(::grpc::Status)>) override;
      void Ping(::grpc::ClientContext* context, const ::weed_tracking::PingRequest* request, ::weed_tracking::PongReply* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest* request, ::weed_tracking::GetDetectionsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest* request, ::weed_tracking::GetDetectionsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest* request, ::weed_tracking::GetTrajectoryMetadataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest* request, ::weed_tracking::GetTrajectoryMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void UpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest* request, ::weed_tracking::GetBootedResponse* response, std::function<void(::grpc::Status)>) override;
      void GetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest* request, ::weed_tracking::GetBootedResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::DiagnosticsSnapshot* response, std::function<void(::grpc::Status)>) override;
      void GetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::DiagnosticsSnapshot* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::GetRecordingStatusResponse* response, std::function<void(::grpc::Status)>) override;
      void GetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::GetRecordingStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void RemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void RemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::ConclusionCounter* response, std::function<void(::grpc::Status)>) override;
      void GetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::ConclusionCounter* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::BandDefinitions* response, std::function<void(::grpc::Status)>) override;
      void GetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::BandDefinitions* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::PlantCaptchaStatusResponse* response, std::function<void(::grpc::Status)>) override;
      void GetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::PlantCaptchaStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void RemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void RemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void CancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void CancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest* request, ::weed_tracking::GetTargetingEnabledResponse* response, std::function<void(::grpc::Status)>) override;
      void GetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest* request, ::weed_tracking::GetTargetingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::PongReply>* AsyncPingRaw(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::PongReply>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetDetectionsResponse>* AsyncGetDetectionsRaw(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetDetectionsResponse>* PrepareAsyncGetDetectionsRaw(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTrajectoryMetadataResponse>* AsyncGetTrajectoryMetadataRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTrajectoryMetadataResponse>* PrepareAsyncGetTrajectoryMetadataRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncUpdateBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncUpdateBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetBootedResponse>* AsyncGetBootedRaw(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetBootedResponse>* PrepareAsyncGetBootedRaw(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>* AsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>* PrepareAsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncStartRecordingDiagnosticsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncStartRecordingDiagnosticsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetRecordingStatusResponse>* AsyncGetDiagnosticsRecordingStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetRecordingStatusResponse>* PrepareAsyncGetDiagnosticsRecordingStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncRemoveRecordingsDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncRemoveRecordingsDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::ConclusionCounter>* AsyncGetConclusionCounterRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::ConclusionCounter>* PrepareAsyncGetConclusionCounterRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::BandDefinitions>* AsyncGetBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::BandDefinitions>* PrepareAsyncGetBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::PlantCaptchaStatusResponse>* AsyncGetPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::PlantCaptchaStatusResponse>* PrepareAsyncGetPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncRemovePlantCaptchaDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncRemovePlantCaptchaDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTargetingEnabledResponse>* AsyncGetTargetingEnabledRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTargetingEnabledResponse>* PrepareAsyncGetTargetingEnabledRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Ping_;
    const ::grpc::internal::RpcMethod rpcmethod_GetDetections_;
    const ::grpc::internal::RpcMethod rpcmethod_GetTrajectoryMetadata_;
    const ::grpc::internal::RpcMethod rpcmethod_UpdateBands_;
    const ::grpc::internal::RpcMethod rpcmethod_GetBooted_;
    const ::grpc::internal::RpcMethod rpcmethod_GetCurrentTrajectories_;
    const ::grpc::internal::RpcMethod rpcmethod_StartSavingCropLineDetectionReplay_;
    const ::grpc::internal::RpcMethod rpcmethod_StartRecordingDiagnostics_;
    const ::grpc::internal::RpcMethod rpcmethod_GetDiagnosticsRecordingStatus_;
    const ::grpc::internal::RpcMethod rpcmethod_RemoveRecordingsDirectory_;
    const ::grpc::internal::RpcMethod rpcmethod_StartRecordingAimbotInputs_;
    const ::grpc::internal::RpcMethod rpcmethod_GetConclusionCounter_;
    const ::grpc::internal::RpcMethod rpcmethod_GetBands_;
    const ::grpc::internal::RpcMethod rpcmethod_StartPlantCaptcha_;
    const ::grpc::internal::RpcMethod rpcmethod_GetPlantCaptchaStatus_;
    const ::grpc::internal::RpcMethod rpcmethod_RemovePlantCaptchaDirectory_;
    const ::grpc::internal::RpcMethod rpcmethod_CancelPlantCaptcha_;
    const ::grpc::internal::RpcMethod rpcmethod_GetTargetingEnabled_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Ping(::grpc::ServerContext* context, const ::weed_tracking::PingRequest* request, ::weed_tracking::PongReply* response);
    virtual ::grpc::Status GetDetections(::grpc::ServerContext* context, const ::weed_tracking::GetDetectionsRequest* request, ::weed_tracking::GetDetectionsResponse* response);
    virtual ::grpc::Status GetTrajectoryMetadata(::grpc::ServerContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest* request, ::weed_tracking::GetTrajectoryMetadataResponse* response);
    virtual ::grpc::Status UpdateBands(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status GetBooted(::grpc::ServerContext* context, const ::weed_tracking::GetBootedRequest* request, ::weed_tracking::GetBootedResponse* response);
    // weeding diagnostics
    virtual ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::DiagnosticsSnapshot* response);
    virtual ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status StartRecordingDiagnostics(::grpc::ServerContext* context, const ::weed_tracking::RecordDiagnosticsRequest* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status GetDiagnosticsRecordingStatus(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::GetRecordingStatusResponse* response);
    virtual ::grpc::Status RemoveRecordingsDirectory(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status GetConclusionCounter(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::ConclusionCounter* response);
    virtual ::grpc::Status GetBands(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::BandDefinitions* response);
    virtual ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status GetPlantCaptchaStatus(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::PlantCaptchaStatusResponse* response);
    virtual ::grpc::Status RemovePlantCaptchaDirectory(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status GetTargetingEnabled(::grpc::ServerContext* context, const ::weed_tracking::GetTargetingEnabledRequest* request, ::weed_tracking::GetTargetingEnabledResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Ping() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::weed_tracking::PingRequest* /*request*/, ::weed_tracking::PongReply* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::weed_tracking::PingRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::PongReply>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetDetections() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDetections(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetDetectionsRequest* /*request*/, ::weed_tracking::GetDetectionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDetections(::grpc::ServerContext* context, ::weed_tracking::GetDetectionsRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::GetDetectionsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTrajectoryMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetTrajectoryMetadata() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetTrajectoryMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryMetadata(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTrajectoryMetadataRequest* /*request*/, ::weed_tracking::GetTrajectoryMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTrajectoryMetadata(::grpc::ServerContext* context, ::weed_tracking::GetTrajectoryMetadataRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::GetTrajectoryMetadataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UpdateBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UpdateBands() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_UpdateBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateBands(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetBooted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetBooted() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_GetBooted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBooted(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetBootedRequest* /*request*/, ::weed_tracking::GetBootedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetBooted(::grpc::ServerContext* context, ::weed_tracking::GetBootedRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::GetBootedResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCurrentTrajectories(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::DiagnosticsSnapshot>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartSavingCropLineDetectionReplay(::grpc::ServerContext* context, ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartRecordingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartRecordingDiagnostics() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_StartRecordingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingDiagnostics(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordDiagnosticsRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartRecordingDiagnostics(::grpc::ServerContext* context, ::weed_tracking::RecordDiagnosticsRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetDiagnosticsRecordingStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetDiagnosticsRecordingStatus() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_GetDiagnosticsRecordingStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticsRecordingStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::GetRecordingStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDiagnosticsRecordingStatus(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::GetRecordingStatusResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_RemoveRecordingsDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_RemoveRecordingsDirectory() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_RemoveRecordingsDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveRecordingsDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRemoveRecordingsDirectory(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartRecordingAimbotInputs(::grpc::ServerContext* context, ::weed_tracking::RecordAimbotInputRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetConclusionCounter : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetConclusionCounter() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_GetConclusionCounter() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConclusionCounter(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::ConclusionCounter* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConclusionCounter(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::ConclusionCounter>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetBands() {
      ::grpc::Service::MarkMethodAsync(12);
    }
    ~WithAsyncMethod_GetBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::BandDefinitions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetBands(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::BandDefinitions>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodAsync(13);
    }
    ~WithAsyncMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartPlantCaptcha(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodAsync(14);
    }
    ~WithAsyncMethod_GetPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::PlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPlantCaptchaStatus(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::PlantCaptchaStatusResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_RemovePlantCaptchaDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_RemovePlantCaptchaDirectory() {
      ::grpc::Service::MarkMethodAsync(15);
    }
    ~WithAsyncMethod_RemovePlantCaptchaDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemovePlantCaptchaDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRemovePlantCaptchaDirectory(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodAsync(16);
    }
    ~WithAsyncMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelPlantCaptcha(::grpc::ServerContext* context, ::weed_tracking::Empty* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(16, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTargetingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetTargetingEnabled() {
      ::grpc::Service::MarkMethodAsync(17);
    }
    ~WithAsyncMethod_GetTargetingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTargetingEnabled(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTargetingEnabledRequest* /*request*/, ::weed_tracking::GetTargetingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTargetingEnabled(::grpc::ServerContext* context, ::weed_tracking::GetTargetingEnabledRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::GetTargetingEnabledResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(17, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Ping<WithAsyncMethod_GetDetections<WithAsyncMethod_GetTrajectoryMetadata<WithAsyncMethod_UpdateBands<WithAsyncMethod_GetBooted<WithAsyncMethod_GetCurrentTrajectories<WithAsyncMethod_StartSavingCropLineDetectionReplay<WithAsyncMethod_StartRecordingDiagnostics<WithAsyncMethod_GetDiagnosticsRecordingStatus<WithAsyncMethod_RemoveRecordingsDirectory<WithAsyncMethod_StartRecordingAimbotInputs<WithAsyncMethod_GetConclusionCounter<WithAsyncMethod_GetBands<WithAsyncMethod_StartPlantCaptcha<WithAsyncMethod_GetPlantCaptchaStatus<WithAsyncMethod_RemovePlantCaptchaDirectory<WithAsyncMethod_CancelPlantCaptcha<WithAsyncMethod_GetTargetingEnabled<Service > > > > > > > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::PingRequest, ::weed_tracking::PongReply>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::PingRequest* request, ::weed_tracking::PongReply* response) { return this->Ping(context, request, response); }));}
    void SetMessageAllocatorFor_Ping(
        ::grpc::MessageAllocator< ::weed_tracking::PingRequest, ::weed_tracking::PongReply>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::PingRequest, ::weed_tracking::PongReply>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::weed_tracking::PingRequest* /*request*/, ::weed_tracking::PongReply* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::PingRequest* /*request*/, ::weed_tracking::PongReply* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetDetections() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::GetDetectionsRequest, ::weed_tracking::GetDetectionsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::GetDetectionsRequest* request, ::weed_tracking::GetDetectionsResponse* response) { return this->GetDetections(context, request, response); }));}
    void SetMessageAllocatorFor_GetDetections(
        ::grpc::MessageAllocator< ::weed_tracking::GetDetectionsRequest, ::weed_tracking::GetDetectionsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::GetDetectionsRequest, ::weed_tracking::GetDetectionsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDetections(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetDetectionsRequest* /*request*/, ::weed_tracking::GetDetectionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDetections(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::GetDetectionsRequest* /*request*/, ::weed_tracking::GetDetectionsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetTrajectoryMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetTrajectoryMetadata() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::GetTrajectoryMetadataRequest, ::weed_tracking::GetTrajectoryMetadataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest* request, ::weed_tracking::GetTrajectoryMetadataResponse* response) { return this->GetTrajectoryMetadata(context, request, response); }));}
    void SetMessageAllocatorFor_GetTrajectoryMetadata(
        ::grpc::MessageAllocator< ::weed_tracking::GetTrajectoryMetadataRequest, ::weed_tracking::GetTrajectoryMetadataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::GetTrajectoryMetadataRequest, ::weed_tracking::GetTrajectoryMetadataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetTrajectoryMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryMetadata(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTrajectoryMetadataRequest* /*request*/, ::weed_tracking::GetTrajectoryMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTrajectoryMetadata(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::GetTrajectoryMetadataRequest* /*request*/, ::weed_tracking::GetTrajectoryMetadataResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UpdateBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UpdateBands() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) { return this->UpdateBands(context, request, response); }));}
    void SetMessageAllocatorFor_UpdateBands(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UpdateBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateBands(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetBooted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetBooted() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::GetBootedRequest, ::weed_tracking::GetBootedResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::GetBootedRequest* request, ::weed_tracking::GetBootedResponse* response) { return this->GetBooted(context, request, response); }));}
    void SetMessageAllocatorFor_GetBooted(
        ::grpc::MessageAllocator< ::weed_tracking::GetBootedRequest, ::weed_tracking::GetBootedResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::GetBootedRequest, ::weed_tracking::GetBootedResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetBooted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBooted(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetBootedRequest* /*request*/, ::weed_tracking::GetBootedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetBooted(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::GetBootedRequest* /*request*/, ::weed_tracking::GetBootedResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::DiagnosticsSnapshot>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::DiagnosticsSnapshot* response) { return this->GetCurrentTrajectories(context, request, response); }));}
    void SetMessageAllocatorFor_GetCurrentTrajectories(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::DiagnosticsSnapshot>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::DiagnosticsSnapshot>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCurrentTrajectories(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response) { return this->StartSavingCropLineDetectionReplay(context, request, response); }));}
    void SetMessageAllocatorFor_StartSavingCropLineDetectionReplay(
        ::grpc::MessageAllocator< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartSavingCropLineDetectionReplay(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartRecordingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartRecordingDiagnostics() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::RecordDiagnosticsRequest, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::RecordDiagnosticsRequest* request, ::weed_tracking::Empty* response) { return this->StartRecordingDiagnostics(context, request, response); }));}
    void SetMessageAllocatorFor_StartRecordingDiagnostics(
        ::grpc::MessageAllocator< ::weed_tracking::RecordDiagnosticsRequest, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::RecordDiagnosticsRequest, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartRecordingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingDiagnostics(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordDiagnosticsRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartRecordingDiagnostics(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::RecordDiagnosticsRequest* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetDiagnosticsRecordingStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetDiagnosticsRecordingStatus() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::GetRecordingStatusResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::GetRecordingStatusResponse* response) { return this->GetDiagnosticsRecordingStatus(context, request, response); }));}
    void SetMessageAllocatorFor_GetDiagnosticsRecordingStatus(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::GetRecordingStatusResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::GetRecordingStatusResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetDiagnosticsRecordingStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticsRecordingStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::GetRecordingStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDiagnosticsRecordingStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::GetRecordingStatusResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_RemoveRecordingsDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_RemoveRecordingsDirectory() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) { return this->RemoveRecordingsDirectory(context, request, response); }));}
    void SetMessageAllocatorFor_RemoveRecordingsDirectory(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_RemoveRecordingsDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveRecordingsDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RemoveRecordingsDirectory(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response) { return this->StartRecordingAimbotInputs(context, request, response); }));}
    void SetMessageAllocatorFor_StartRecordingAimbotInputs(
        ::grpc::MessageAllocator< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartRecordingAimbotInputs(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetConclusionCounter : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetConclusionCounter() {
      ::grpc::Service::MarkMethodCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::ConclusionCounter>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::ConclusionCounter* response) { return this->GetConclusionCounter(context, request, response); }));}
    void SetMessageAllocatorFor_GetConclusionCounter(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::ConclusionCounter>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::ConclusionCounter>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetConclusionCounter() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConclusionCounter(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::ConclusionCounter* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConclusionCounter(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::ConclusionCounter* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetBands() {
      ::grpc::Service::MarkMethodCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::BandDefinitions>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::BandDefinitions* response) { return this->GetBands(context, request, response); }));}
    void SetMessageAllocatorFor_GetBands(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::BandDefinitions>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(12);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::BandDefinitions>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::BandDefinitions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetBands(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::BandDefinitions* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) { return this->StartPlantCaptcha(context, request, response); }));}
    void SetMessageAllocatorFor_StartPlantCaptcha(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(13);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::PlantCaptchaStatusResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::PlantCaptchaStatusResponse* response) { return this->GetPlantCaptchaStatus(context, request, response); }));}
    void SetMessageAllocatorFor_GetPlantCaptchaStatus(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::PlantCaptchaStatusResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(14);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::PlantCaptchaStatusResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::PlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPlantCaptchaStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::PlantCaptchaStatusResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_RemovePlantCaptchaDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_RemovePlantCaptchaDirectory() {
      ::grpc::Service::MarkMethodCallback(15,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) { return this->RemovePlantCaptchaDirectory(context, request, response); }));}
    void SetMessageAllocatorFor_RemovePlantCaptchaDirectory(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(15);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_RemovePlantCaptchaDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemovePlantCaptchaDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RemovePlantCaptchaDirectory(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodCallback(16,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) { return this->CancelPlantCaptcha(context, request, response); }));}
    void SetMessageAllocatorFor_CancelPlantCaptcha(
        ::grpc::MessageAllocator< ::weed_tracking::Empty, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(16);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::Empty, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CancelPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetTargetingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetTargetingEnabled() {
      ::grpc::Service::MarkMethodCallback(17,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::GetTargetingEnabledRequest, ::weed_tracking::GetTargetingEnabledResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::GetTargetingEnabledRequest* request, ::weed_tracking::GetTargetingEnabledResponse* response) { return this->GetTargetingEnabled(context, request, response); }));}
    void SetMessageAllocatorFor_GetTargetingEnabled(
        ::grpc::MessageAllocator< ::weed_tracking::GetTargetingEnabledRequest, ::weed_tracking::GetTargetingEnabledResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(17);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::GetTargetingEnabledRequest, ::weed_tracking::GetTargetingEnabledResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetTargetingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTargetingEnabled(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTargetingEnabledRequest* /*request*/, ::weed_tracking::GetTargetingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTargetingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::GetTargetingEnabledRequest* /*request*/, ::weed_tracking::GetTargetingEnabledResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Ping<WithCallbackMethod_GetDetections<WithCallbackMethod_GetTrajectoryMetadata<WithCallbackMethod_UpdateBands<WithCallbackMethod_GetBooted<WithCallbackMethod_GetCurrentTrajectories<WithCallbackMethod_StartSavingCropLineDetectionReplay<WithCallbackMethod_StartRecordingDiagnostics<WithCallbackMethod_GetDiagnosticsRecordingStatus<WithCallbackMethod_RemoveRecordingsDirectory<WithCallbackMethod_StartRecordingAimbotInputs<WithCallbackMethod_GetConclusionCounter<WithCallbackMethod_GetBands<WithCallbackMethod_StartPlantCaptcha<WithCallbackMethod_GetPlantCaptchaStatus<WithCallbackMethod_RemovePlantCaptchaDirectory<WithCallbackMethod_CancelPlantCaptcha<WithCallbackMethod_GetTargetingEnabled<Service > > > > > > > > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Ping() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::weed_tracking::PingRequest* /*request*/, ::weed_tracking::PongReply* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetDetections() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDetections(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetDetectionsRequest* /*request*/, ::weed_tracking::GetDetectionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetTrajectoryMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetTrajectoryMetadata() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetTrajectoryMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryMetadata(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTrajectoryMetadataRequest* /*request*/, ::weed_tracking::GetTrajectoryMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UpdateBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UpdateBands() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_UpdateBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetBooted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetBooted() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_GetBooted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBooted(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetBootedRequest* /*request*/, ::weed_tracking::GetBootedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartRecordingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartRecordingDiagnostics() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_StartRecordingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingDiagnostics(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordDiagnosticsRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetDiagnosticsRecordingStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetDiagnosticsRecordingStatus() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_GetDiagnosticsRecordingStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticsRecordingStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::GetRecordingStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_RemoveRecordingsDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_RemoveRecordingsDirectory() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_RemoveRecordingsDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveRecordingsDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetConclusionCounter : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetConclusionCounter() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_GetConclusionCounter() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConclusionCounter(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::ConclusionCounter* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetBands() {
      ::grpc::Service::MarkMethodGeneric(12);
    }
    ~WithGenericMethod_GetBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::BandDefinitions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodGeneric(13);
    }
    ~WithGenericMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodGeneric(14);
    }
    ~WithGenericMethod_GetPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::PlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_RemovePlantCaptchaDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_RemovePlantCaptchaDirectory() {
      ::grpc::Service::MarkMethodGeneric(15);
    }
    ~WithGenericMethod_RemovePlantCaptchaDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemovePlantCaptchaDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodGeneric(16);
    }
    ~WithGenericMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetTargetingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetTargetingEnabled() {
      ::grpc::Service::MarkMethodGeneric(17);
    }
    ~WithGenericMethod_GetTargetingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTargetingEnabled(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTargetingEnabledRequest* /*request*/, ::weed_tracking::GetTargetingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Ping() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::weed_tracking::PingRequest* /*request*/, ::weed_tracking::PongReply* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetDetections() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDetections(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetDetectionsRequest* /*request*/, ::weed_tracking::GetDetectionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDetections(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTrajectoryMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetTrajectoryMetadata() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetTrajectoryMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryMetadata(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTrajectoryMetadataRequest* /*request*/, ::weed_tracking::GetTrajectoryMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTrajectoryMetadata(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UpdateBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UpdateBands() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_UpdateBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateBands(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetBooted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetBooted() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_GetBooted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBooted(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetBootedRequest* /*request*/, ::weed_tracking::GetBootedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetBooted(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCurrentTrajectories(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartSavingCropLineDetectionReplay(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartRecordingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartRecordingDiagnostics() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_StartRecordingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingDiagnostics(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordDiagnosticsRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartRecordingDiagnostics(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetDiagnosticsRecordingStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetDiagnosticsRecordingStatus() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_GetDiagnosticsRecordingStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticsRecordingStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::GetRecordingStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDiagnosticsRecordingStatus(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_RemoveRecordingsDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_RemoveRecordingsDirectory() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_RemoveRecordingsDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveRecordingsDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRemoveRecordingsDirectory(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartRecordingAimbotInputs(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetConclusionCounter : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetConclusionCounter() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_GetConclusionCounter() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConclusionCounter(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::ConclusionCounter* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConclusionCounter(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetBands() {
      ::grpc::Service::MarkMethodRaw(12);
    }
    ~WithRawMethod_GetBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::BandDefinitions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetBands(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodRaw(13);
    }
    ~WithRawMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartPlantCaptcha(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodRaw(14);
    }
    ~WithRawMethod_GetPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::PlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPlantCaptchaStatus(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_RemovePlantCaptchaDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_RemovePlantCaptchaDirectory() {
      ::grpc::Service::MarkMethodRaw(15);
    }
    ~WithRawMethod_RemovePlantCaptchaDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemovePlantCaptchaDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRemovePlantCaptchaDirectory(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodRaw(16);
    }
    ~WithRawMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelPlantCaptcha(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(16, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTargetingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetTargetingEnabled() {
      ::grpc::Service::MarkMethodRaw(17);
    }
    ~WithRawMethod_GetTargetingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTargetingEnabled(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTargetingEnabledRequest* /*request*/, ::weed_tracking::GetTargetingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTargetingEnabled(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(17, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Ping(context, request, response); }));
    }
    ~WithRawCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::weed_tracking::PingRequest* /*request*/, ::weed_tracking::PongReply* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetDetections() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetDetections(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDetections(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetDetectionsRequest* /*request*/, ::weed_tracking::GetDetectionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDetections(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetTrajectoryMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetTrajectoryMetadata() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetTrajectoryMetadata(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetTrajectoryMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTrajectoryMetadata(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTrajectoryMetadataRequest* /*request*/, ::weed_tracking::GetTrajectoryMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTrajectoryMetadata(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UpdateBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UpdateBands() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UpdateBands(context, request, response); }));
    }
    ~WithRawCallbackMethod_UpdateBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateBands(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetBooted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetBooted() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetBooted(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetBooted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBooted(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetBootedRequest* /*request*/, ::weed_tracking::GetBootedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetBooted(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetCurrentTrajectories(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCurrentTrajectories(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartSavingCropLineDetectionReplay(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartSavingCropLineDetectionReplay(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartRecordingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartRecordingDiagnostics() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartRecordingDiagnostics(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartRecordingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingDiagnostics(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordDiagnosticsRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartRecordingDiagnostics(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetDiagnosticsRecordingStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetDiagnosticsRecordingStatus() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetDiagnosticsRecordingStatus(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetDiagnosticsRecordingStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDiagnosticsRecordingStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::GetRecordingStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDiagnosticsRecordingStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_RemoveRecordingsDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_RemoveRecordingsDirectory() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->RemoveRecordingsDirectory(context, request, response); }));
    }
    ~WithRawCallbackMethod_RemoveRecordingsDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveRecordingsDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RemoveRecordingsDirectory(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartRecordingAimbotInputs(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartRecordingAimbotInputs(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetConclusionCounter : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetConclusionCounter() {
      ::grpc::Service::MarkMethodRawCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetConclusionCounter(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetConclusionCounter() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConclusionCounter(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::ConclusionCounter* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConclusionCounter(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetBands() {
      ::grpc::Service::MarkMethodRawCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetBands(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::BandDefinitions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetBands(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodRawCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartPlantCaptcha(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodRawCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetPlantCaptchaStatus(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::PlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPlantCaptchaStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_RemovePlantCaptchaDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_RemovePlantCaptchaDirectory() {
      ::grpc::Service::MarkMethodRawCallback(15,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->RemovePlantCaptchaDirectory(context, request, response); }));
    }
    ~WithRawCallbackMethod_RemovePlantCaptchaDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemovePlantCaptchaDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RemovePlantCaptchaDirectory(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodRawCallback(16,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CancelPlantCaptcha(context, request, response); }));
    }
    ~WithRawCallbackMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CancelPlantCaptcha(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetTargetingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetTargetingEnabled() {
      ::grpc::Service::MarkMethodRawCallback(17,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetTargetingEnabled(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetTargetingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTargetingEnabled(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTargetingEnabledRequest* /*request*/, ::weed_tracking::GetTargetingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTargetingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Ping() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::PingRequest, ::weed_tracking::PongReply>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::PingRequest, ::weed_tracking::PongReply>* streamer) {
                       return this->StreamedPing(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::weed_tracking::PingRequest* /*request*/, ::weed_tracking::PongReply* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPing(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::PingRequest,::weed_tracking::PongReply>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetDetections() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::GetDetectionsRequest, ::weed_tracking::GetDetectionsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::GetDetectionsRequest, ::weed_tracking::GetDetectionsResponse>* streamer) {
                       return this->StreamedGetDetections(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetDetections(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetDetectionsRequest* /*request*/, ::weed_tracking::GetDetectionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetDetections(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::GetDetectionsRequest,::weed_tracking::GetDetectionsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetTrajectoryMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetTrajectoryMetadata() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::GetTrajectoryMetadataRequest, ::weed_tracking::GetTrajectoryMetadataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::GetTrajectoryMetadataRequest, ::weed_tracking::GetTrajectoryMetadataResponse>* streamer) {
                       return this->StreamedGetTrajectoryMetadata(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetTrajectoryMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetTrajectoryMetadata(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTrajectoryMetadataRequest* /*request*/, ::weed_tracking::GetTrajectoryMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetTrajectoryMetadata(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::GetTrajectoryMetadataRequest,::weed_tracking::GetTrajectoryMetadataResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UpdateBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UpdateBands() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedUpdateBands(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UpdateBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UpdateBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUpdateBands(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetBooted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetBooted() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::GetBootedRequest, ::weed_tracking::GetBootedResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::GetBootedRequest, ::weed_tracking::GetBootedResponse>* streamer) {
                       return this->StreamedGetBooted(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetBooted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetBooted(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetBootedRequest* /*request*/, ::weed_tracking::GetBootedResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetBooted(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::GetBootedRequest,::weed_tracking::GetBootedResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetCurrentTrajectories : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetCurrentTrajectories() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::DiagnosticsSnapshot>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::DiagnosticsSnapshot>* streamer) {
                       return this->StreamedGetCurrentTrajectories(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetCurrentTrajectories() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetCurrentTrajectories(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::DiagnosticsSnapshot* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetCurrentTrajectories(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::DiagnosticsSnapshot>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedStartSavingCropLineDetectionReplay(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartSavingCropLineDetectionReplay(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::StartSavingCropLineDetectionReplayRequest,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartRecordingDiagnostics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartRecordingDiagnostics() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::RecordDiagnosticsRequest, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::RecordDiagnosticsRequest, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedStartRecordingDiagnostics(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartRecordingDiagnostics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartRecordingDiagnostics(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordDiagnosticsRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartRecordingDiagnostics(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::RecordDiagnosticsRequest,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetDiagnosticsRecordingStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetDiagnosticsRecordingStatus() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::GetRecordingStatusResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::GetRecordingStatusResponse>* streamer) {
                       return this->StreamedGetDiagnosticsRecordingStatus(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetDiagnosticsRecordingStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetDiagnosticsRecordingStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::GetRecordingStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetDiagnosticsRecordingStatus(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::GetRecordingStatusResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_RemoveRecordingsDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_RemoveRecordingsDirectory() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedRemoveRecordingsDirectory(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_RemoveRecordingsDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status RemoveRecordingsDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedRemoveRecordingsDirectory(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedStartRecordingAimbotInputs(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartRecordingAimbotInputs(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::RecordAimbotInputRequest,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetConclusionCounter : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetConclusionCounter() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::ConclusionCounter>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::ConclusionCounter>* streamer) {
                       return this->StreamedGetConclusionCounter(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetConclusionCounter() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetConclusionCounter(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::ConclusionCounter* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetConclusionCounter(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::ConclusionCounter>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetBands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetBands() {
      ::grpc::Service::MarkMethodStreamed(12,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::BandDefinitions>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::BandDefinitions>* streamer) {
                       return this->StreamedGetBands(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetBands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetBands(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::BandDefinitions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetBands(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::BandDefinitions>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartPlantCaptcha() {
      ::grpc::Service::MarkMethodStreamed(13,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedStartPlantCaptcha(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartPlantCaptcha(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetPlantCaptchaStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetPlantCaptchaStatus() {
      ::grpc::Service::MarkMethodStreamed(14,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::PlantCaptchaStatusResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::PlantCaptchaStatusResponse>* streamer) {
                       return this->StreamedGetPlantCaptchaStatus(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetPlantCaptchaStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetPlantCaptchaStatus(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::PlantCaptchaStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetPlantCaptchaStatus(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::PlantCaptchaStatusResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_RemovePlantCaptchaDirectory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_RemovePlantCaptchaDirectory() {
      ::grpc::Service::MarkMethodStreamed(15,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedRemovePlantCaptchaDirectory(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_RemovePlantCaptchaDirectory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status RemovePlantCaptchaDirectory(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedRemovePlantCaptchaDirectory(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CancelPlantCaptcha : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CancelPlantCaptcha() {
      ::grpc::Service::MarkMethodStreamed(16,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::Empty, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::Empty, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedCancelPlantCaptcha(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CancelPlantCaptcha() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CancelPlantCaptcha(::grpc::ServerContext* /*context*/, const ::weed_tracking::Empty* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCancelPlantCaptcha(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::Empty,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetTargetingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetTargetingEnabled() {
      ::grpc::Service::MarkMethodStreamed(17,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::GetTargetingEnabledRequest, ::weed_tracking::GetTargetingEnabledResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::GetTargetingEnabledRequest, ::weed_tracking::GetTargetingEnabledResponse>* streamer) {
                       return this->StreamedGetTargetingEnabled(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetTargetingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetTargetingEnabled(::grpc::ServerContext* /*context*/, const ::weed_tracking::GetTargetingEnabledRequest* /*request*/, ::weed_tracking::GetTargetingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetTargetingEnabled(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::GetTargetingEnabledRequest,::weed_tracking::GetTargetingEnabledResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_GetDetections<WithStreamedUnaryMethod_GetTrajectoryMetadata<WithStreamedUnaryMethod_UpdateBands<WithStreamedUnaryMethod_GetBooted<WithStreamedUnaryMethod_GetCurrentTrajectories<WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay<WithStreamedUnaryMethod_StartRecordingDiagnostics<WithStreamedUnaryMethod_GetDiagnosticsRecordingStatus<WithStreamedUnaryMethod_RemoveRecordingsDirectory<WithStreamedUnaryMethod_StartRecordingAimbotInputs<WithStreamedUnaryMethod_GetConclusionCounter<WithStreamedUnaryMethod_GetBands<WithStreamedUnaryMethod_StartPlantCaptcha<WithStreamedUnaryMethod_GetPlantCaptchaStatus<WithStreamedUnaryMethod_RemovePlantCaptchaDirectory<WithStreamedUnaryMethod_CancelPlantCaptcha<WithStreamedUnaryMethod_GetTargetingEnabled<Service > > > > > > > > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_GetDetections<WithStreamedUnaryMethod_GetTrajectoryMetadata<WithStreamedUnaryMethod_UpdateBands<WithStreamedUnaryMethod_GetBooted<WithStreamedUnaryMethod_GetCurrentTrajectories<WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay<WithStreamedUnaryMethod_StartRecordingDiagnostics<WithStreamedUnaryMethod_GetDiagnosticsRecordingStatus<WithStreamedUnaryMethod_RemoveRecordingsDirectory<WithStreamedUnaryMethod_StartRecordingAimbotInputs<WithStreamedUnaryMethod_GetConclusionCounter<WithStreamedUnaryMethod_GetBands<WithStreamedUnaryMethod_StartPlantCaptcha<WithStreamedUnaryMethod_GetPlantCaptchaStatus<WithStreamedUnaryMethod_RemovePlantCaptchaDirectory<WithStreamedUnaryMethod_CancelPlantCaptcha<WithStreamedUnaryMethod_GetTargetingEnabled<Service > > > > > > > > > > > > > > > > > > StreamedService;
};

}  // namespace weed_tracking


#endif  // GRPC_weed_5ftracking_2fproto_2fweed_5ftracking_2eproto__INCLUDED

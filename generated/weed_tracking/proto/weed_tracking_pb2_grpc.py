# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.weed_tracking.proto import weed_tracking_pb2 as weed__tracking_dot_proto_dot_weed__tracking__pb2


class WeedTrackingServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/Ping',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.PingRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.PongReply.FromString,
                )
        self.GetDetections = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/GetDetections',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetDetectionsRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetDetectionsResponse.FromString,
                )
        self.GetTrajectoryMetadata = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/GetTrajectoryMetadata',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTrajectoryMetadataRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTrajectoryMetadataResponse.FromString,
                )
        self.UpdateBands = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/UpdateBands',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.GetBooted = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/GetBooted',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetBootedRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetBootedResponse.FromString,
                )
        self.GetCurrentTrajectories = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/GetCurrentTrajectories',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.DiagnosticsSnapshot.FromString,
                )
        self.StartSavingCropLineDetectionReplay = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/StartSavingCropLineDetectionReplay',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.StartSavingCropLineDetectionReplayRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.StartRecordingDiagnostics = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/StartRecordingDiagnostics',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.RecordDiagnosticsRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.GetDiagnosticsRecordingStatus = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/GetDiagnosticsRecordingStatus',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetRecordingStatusResponse.FromString,
                )
        self.RemoveRecordingsDirectory = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/RemoveRecordingsDirectory',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.StartRecordingAimbotInputs = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/StartRecordingAimbotInputs',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.RecordAimbotInputRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.GetConclusionCounter = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/GetConclusionCounter',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.ConclusionCounter.FromString,
                )
        self.GetBands = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/GetBands',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.BandDefinitions.FromString,
                )
        self.StartPlantCaptcha = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/StartPlantCaptcha',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.GetPlantCaptchaStatus = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/GetPlantCaptchaStatus',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.PlantCaptchaStatusResponse.FromString,
                )
        self.RemovePlantCaptchaDirectory = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/RemovePlantCaptchaDirectory',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.CancelPlantCaptcha = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/CancelPlantCaptcha',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.GetTargetingEnabled = channel.unary_unary(
                '/weed_tracking.WeedTrackingService/GetTargetingEnabled',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTargetingEnabledRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTargetingEnabledResponse.FromString,
                )


class WeedTrackingServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDetections(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTrajectoryMetadata(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateBands(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBooted(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCurrentTrajectories(self, request, context):
        """weeding diagnostics
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartSavingCropLineDetectionReplay(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartRecordingDiagnostics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDiagnosticsRecordingStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RemoveRecordingsDirectory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartRecordingAimbotInputs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetConclusionCounter(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBands(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartPlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPlantCaptchaStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RemovePlantCaptchaDirectory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelPlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTargetingEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_WeedTrackingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.PingRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.PongReply.SerializeToString,
            ),
            'GetDetections': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDetections,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetDetectionsRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetDetectionsResponse.SerializeToString,
            ),
            'GetTrajectoryMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTrajectoryMetadata,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTrajectoryMetadataRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTrajectoryMetadataResponse.SerializeToString,
            ),
            'UpdateBands': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateBands,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'GetBooted': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBooted,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetBootedRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetBootedResponse.SerializeToString,
            ),
            'GetCurrentTrajectories': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCurrentTrajectories,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.DiagnosticsSnapshot.SerializeToString,
            ),
            'StartSavingCropLineDetectionReplay': grpc.unary_unary_rpc_method_handler(
                    servicer.StartSavingCropLineDetectionReplay,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.StartSavingCropLineDetectionReplayRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'StartRecordingDiagnostics': grpc.unary_unary_rpc_method_handler(
                    servicer.StartRecordingDiagnostics,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.RecordDiagnosticsRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'GetDiagnosticsRecordingStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDiagnosticsRecordingStatus,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetRecordingStatusResponse.SerializeToString,
            ),
            'RemoveRecordingsDirectory': grpc.unary_unary_rpc_method_handler(
                    servicer.RemoveRecordingsDirectory,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'StartRecordingAimbotInputs': grpc.unary_unary_rpc_method_handler(
                    servicer.StartRecordingAimbotInputs,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.RecordAimbotInputRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'GetConclusionCounter': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConclusionCounter,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.ConclusionCounter.SerializeToString,
            ),
            'GetBands': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBands,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.BandDefinitions.SerializeToString,
            ),
            'StartPlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.StartPlantCaptcha,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'GetPlantCaptchaStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPlantCaptchaStatus,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.PlantCaptchaStatusResponse.SerializeToString,
            ),
            'RemovePlantCaptchaDirectory': grpc.unary_unary_rpc_method_handler(
                    servicer.RemovePlantCaptchaDirectory,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'CancelPlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelPlantCaptcha,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'GetTargetingEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTargetingEnabled,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTargetingEnabledRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTargetingEnabledResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'weed_tracking.WeedTrackingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class WeedTrackingService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/Ping',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.PingRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.PongReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDetections(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/GetDetections',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.GetDetectionsRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.GetDetectionsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTrajectoryMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/GetTrajectoryMetadata',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTrajectoryMetadataRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTrajectoryMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateBands(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/UpdateBands',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetBooted(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/GetBooted',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.GetBootedRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.GetBootedResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCurrentTrajectories(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/GetCurrentTrajectories',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.DiagnosticsSnapshot.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartSavingCropLineDetectionReplay(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/StartSavingCropLineDetectionReplay',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.StartSavingCropLineDetectionReplayRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartRecordingDiagnostics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/StartRecordingDiagnostics',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.RecordDiagnosticsRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDiagnosticsRecordingStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/GetDiagnosticsRecordingStatus',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.GetRecordingStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RemoveRecordingsDirectory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/RemoveRecordingsDirectory',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartRecordingAimbotInputs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/StartRecordingAimbotInputs',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.RecordAimbotInputRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetConclusionCounter(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/GetConclusionCounter',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.ConclusionCounter.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetBands(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/GetBands',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.BandDefinitions.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartPlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/StartPlantCaptcha',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPlantCaptchaStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/GetPlantCaptchaStatus',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.PlantCaptchaStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RemovePlantCaptchaDirectory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/RemovePlantCaptchaDirectory',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CancelPlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/CancelPlantCaptcha',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTargetingEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/weed_tracking.WeedTrackingService/GetTargetingEnabled',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTargetingEnabledRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.GetTargetingEnabledResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

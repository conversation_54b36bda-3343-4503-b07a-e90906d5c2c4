// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: weed_tracking/proto/weed_tracking.proto

#include "weed_tracking/proto/weed_tracking.pb.h"
#include "weed_tracking/proto/weed_tracking.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace weed_tracking {

static const char* WeedTrackingService_method_names[] = {
  "/weed_tracking.WeedTrackingService/Ping",
  "/weed_tracking.WeedTrackingService/GetDetections",
  "/weed_tracking.WeedTrackingService/GetTrajectoryMetadata",
  "/weed_tracking.WeedTrackingService/UpdateBands",
  "/weed_tracking.WeedTrackingService/GetBooted",
  "/weed_tracking.WeedTrackingService/GetCurrentTrajectories",
  "/weed_tracking.WeedTrackingService/StartSavingCropLineDetectionReplay",
  "/weed_tracking.WeedTrackingService/StartRecordingDiagnostics",
  "/weed_tracking.WeedTrackingService/GetDiagnosticsRecordingStatus",
  "/weed_tracking.WeedTrackingService/RemoveRecordingsDirectory",
  "/weed_tracking.WeedTrackingService/StartRecordingAimbotInputs",
  "/weed_tracking.WeedTrackingService/GetConclusionCounter",
  "/weed_tracking.WeedTrackingService/GetBands",
  "/weed_tracking.WeedTrackingService/StartPlantCaptcha",
  "/weed_tracking.WeedTrackingService/GetPlantCaptchaStatus",
  "/weed_tracking.WeedTrackingService/RemovePlantCaptchaDirectory",
  "/weed_tracking.WeedTrackingService/CancelPlantCaptcha",
  "/weed_tracking.WeedTrackingService/GetTargetingEnabled",
};

std::unique_ptr< WeedTrackingService::Stub> WeedTrackingService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< WeedTrackingService::Stub> stub(new WeedTrackingService::Stub(channel, options));
  return stub;
}

WeedTrackingService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Ping_(WeedTrackingService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDetections_(WeedTrackingService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTrajectoryMetadata_(WeedTrackingService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UpdateBands_(WeedTrackingService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetBooted_(WeedTrackingService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCurrentTrajectories_(WeedTrackingService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartSavingCropLineDetectionReplay_(WeedTrackingService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartRecordingDiagnostics_(WeedTrackingService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDiagnosticsRecordingStatus_(WeedTrackingService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_RemoveRecordingsDirectory_(WeedTrackingService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartRecordingAimbotInputs_(WeedTrackingService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetConclusionCounter_(WeedTrackingService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetBands_(WeedTrackingService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartPlantCaptcha_(WeedTrackingService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetPlantCaptchaStatus_(WeedTrackingService_method_names[14], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_RemovePlantCaptchaDirectory_(WeedTrackingService_method_names[15], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CancelPlantCaptcha_(WeedTrackingService_method_names[16], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTargetingEnabled_(WeedTrackingService_method_names[17], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status WeedTrackingService::Stub::Ping(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::weed_tracking::PongReply* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::PingRequest, ::weed_tracking::PongReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Ping_, context, request, response);
}

void WeedTrackingService::Stub::async::Ping(::grpc::ClientContext* context, const ::weed_tracking::PingRequest* request, ::weed_tracking::PongReply* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::PingRequest, ::weed_tracking::PongReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::Ping(::grpc::ClientContext* context, const ::weed_tracking::PingRequest* request, ::weed_tracking::PongReply* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::PongReply>* WeedTrackingService::Stub::PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::PongReply, ::weed_tracking::PingRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Ping_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::PongReply>* WeedTrackingService::Stub::AsyncPingRaw(::grpc::ClientContext* context, const ::weed_tracking::PingRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::GetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::weed_tracking::GetDetectionsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::GetDetectionsRequest, ::weed_tracking::GetDetectionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDetections_, context, request, response);
}

void WeedTrackingService::Stub::async::GetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest* request, ::weed_tracking::GetDetectionsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::GetDetectionsRequest, ::weed_tracking::GetDetectionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDetections_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::GetDetections(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest* request, ::weed_tracking::GetDetectionsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDetections_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetDetectionsResponse>* WeedTrackingService::Stub::PrepareAsyncGetDetectionsRaw(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::GetDetectionsResponse, ::weed_tracking::GetDetectionsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDetections_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetDetectionsResponse>* WeedTrackingService::Stub::AsyncGetDetectionsRaw(::grpc::ClientContext* context, const ::weed_tracking::GetDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDetectionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::GetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::weed_tracking::GetTrajectoryMetadataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::GetTrajectoryMetadataRequest, ::weed_tracking::GetTrajectoryMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTrajectoryMetadata_, context, request, response);
}

void WeedTrackingService::Stub::async::GetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest* request, ::weed_tracking::GetTrajectoryMetadataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::GetTrajectoryMetadataRequest, ::weed_tracking::GetTrajectoryMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTrajectoryMetadata_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::GetTrajectoryMetadata(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest* request, ::weed_tracking::GetTrajectoryMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTrajectoryMetadata_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTrajectoryMetadataResponse>* WeedTrackingService::Stub::PrepareAsyncGetTrajectoryMetadataRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::GetTrajectoryMetadataResponse, ::weed_tracking::GetTrajectoryMetadataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTrajectoryMetadata_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTrajectoryMetadataResponse>* WeedTrackingService::Stub::AsyncGetTrajectoryMetadataRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTrajectoryMetadataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::UpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UpdateBands_, context, request, response);
}

void WeedTrackingService::Stub::async::UpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateBands_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::UpdateBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateBands_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::PrepareAsyncUpdateBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UpdateBands_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::AsyncUpdateBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUpdateBandsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::GetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::weed_tracking::GetBootedResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::GetBootedRequest, ::weed_tracking::GetBootedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetBooted_, context, request, response);
}

void WeedTrackingService::Stub::async::GetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest* request, ::weed_tracking::GetBootedResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::GetBootedRequest, ::weed_tracking::GetBootedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBooted_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::GetBooted(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest* request, ::weed_tracking::GetBootedResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBooted_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetBootedResponse>* WeedTrackingService::Stub::PrepareAsyncGetBootedRaw(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::GetBootedResponse, ::weed_tracking::GetBootedRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetBooted_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetBootedResponse>* WeedTrackingService::Stub::AsyncGetBootedRaw(::grpc::ClientContext* context, const ::weed_tracking::GetBootedRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetBootedRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::GetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::DiagnosticsSnapshot* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::DiagnosticsSnapshot, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCurrentTrajectories_, context, request, response);
}

void WeedTrackingService::Stub::async::GetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::DiagnosticsSnapshot* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::DiagnosticsSnapshot, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCurrentTrajectories_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::GetCurrentTrajectories(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::DiagnosticsSnapshot* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCurrentTrajectories_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>* WeedTrackingService::Stub::PrepareAsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::DiagnosticsSnapshot, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCurrentTrajectories_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::DiagnosticsSnapshot>* WeedTrackingService::Stub::AsyncGetCurrentTrajectoriesRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCurrentTrajectoriesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartSavingCropLineDetectionReplay_, context, request, response);
}

void WeedTrackingService::Stub::async::StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartSavingCropLineDetectionReplay_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartSavingCropLineDetectionReplay_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::PrepareAsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartSavingCropLineDetectionReplay_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::AsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartSavingCropLineDetectionReplayRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::StartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::RecordDiagnosticsRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartRecordingDiagnostics_, context, request, response);
}

void WeedTrackingService::Stub::async::StartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::RecordDiagnosticsRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartRecordingDiagnostics_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::StartRecordingDiagnostics(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartRecordingDiagnostics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::PrepareAsyncStartRecordingDiagnosticsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::RecordDiagnosticsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartRecordingDiagnostics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::AsyncStartRecordingDiagnosticsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordDiagnosticsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartRecordingDiagnosticsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::GetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::GetRecordingStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::GetRecordingStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDiagnosticsRecordingStatus_, context, request, response);
}

void WeedTrackingService::Stub::async::GetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::GetRecordingStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::GetRecordingStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDiagnosticsRecordingStatus_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::GetDiagnosticsRecordingStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::GetRecordingStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDiagnosticsRecordingStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetRecordingStatusResponse>* WeedTrackingService::Stub::PrepareAsyncGetDiagnosticsRecordingStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::GetRecordingStatusResponse, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDiagnosticsRecordingStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetRecordingStatusResponse>* WeedTrackingService::Stub::AsyncGetDiagnosticsRecordingStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDiagnosticsRecordingStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::RemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_RemoveRecordingsDirectory_, context, request, response);
}

void WeedTrackingService::Stub::async::RemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RemoveRecordingsDirectory_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::RemoveRecordingsDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RemoveRecordingsDirectory_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::PrepareAsyncRemoveRecordingsDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_RemoveRecordingsDirectory_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::AsyncRemoveRecordingsDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncRemoveRecordingsDirectoryRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartRecordingAimbotInputs_, context, request, response);
}

void WeedTrackingService::Stub::async::StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartRecordingAimbotInputs_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartRecordingAimbotInputs_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::PrepareAsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::RecordAimbotInputRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartRecordingAimbotInputs_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::AsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartRecordingAimbotInputsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::GetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::ConclusionCounter* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::ConclusionCounter, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetConclusionCounter_, context, request, response);
}

void WeedTrackingService::Stub::async::GetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::ConclusionCounter* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::ConclusionCounter, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConclusionCounter_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::GetConclusionCounter(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::ConclusionCounter* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConclusionCounter_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::ConclusionCounter>* WeedTrackingService::Stub::PrepareAsyncGetConclusionCounterRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::ConclusionCounter, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetConclusionCounter_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::ConclusionCounter>* WeedTrackingService::Stub::AsyncGetConclusionCounterRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetConclusionCounterRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::GetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::BandDefinitions* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::BandDefinitions, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetBands_, context, request, response);
}

void WeedTrackingService::Stub::async::GetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::BandDefinitions* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::BandDefinitions, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBands_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::GetBands(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::BandDefinitions* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetBands_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::BandDefinitions>* WeedTrackingService::Stub::PrepareAsyncGetBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::BandDefinitions, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetBands_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::BandDefinitions>* WeedTrackingService::Stub::AsyncGetBandsRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetBandsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::StartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartPlantCaptcha_, context, request, response);
}

void WeedTrackingService::Stub::async::StartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartPlantCaptcha_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::StartPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartPlantCaptcha_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::PrepareAsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartPlantCaptcha_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::AsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartPlantCaptchaRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::GetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::PlantCaptchaStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::PlantCaptchaStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetPlantCaptchaStatus_, context, request, response);
}

void WeedTrackingService::Stub::async::GetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::PlantCaptchaStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::PlantCaptchaStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPlantCaptchaStatus_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::GetPlantCaptchaStatus(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::PlantCaptchaStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPlantCaptchaStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::PlantCaptchaStatusResponse>* WeedTrackingService::Stub::PrepareAsyncGetPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::PlantCaptchaStatusResponse, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetPlantCaptchaStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::PlantCaptchaStatusResponse>* WeedTrackingService::Stub::AsyncGetPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetPlantCaptchaStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::RemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_RemovePlantCaptchaDirectory_, context, request, response);
}

void WeedTrackingService::Stub::async::RemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RemovePlantCaptchaDirectory_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::RemovePlantCaptchaDirectory(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RemovePlantCaptchaDirectory_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::PrepareAsyncRemovePlantCaptchaDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_RemovePlantCaptchaDirectory_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::AsyncRemovePlantCaptchaDirectoryRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncRemovePlantCaptchaDirectoryRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::CancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CancelPlantCaptcha_, context, request, response);
}

void WeedTrackingService::Stub::async::CancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelPlantCaptcha_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::CancelPlantCaptcha(::grpc::ClientContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelPlantCaptcha_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::PrepareAsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CancelPlantCaptcha_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* WeedTrackingService::Stub::AsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::weed_tracking::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCancelPlantCaptchaRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status WeedTrackingService::Stub::GetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::weed_tracking::GetTargetingEnabledResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::GetTargetingEnabledRequest, ::weed_tracking::GetTargetingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTargetingEnabled_, context, request, response);
}

void WeedTrackingService::Stub::async::GetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest* request, ::weed_tracking::GetTargetingEnabledResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::GetTargetingEnabledRequest, ::weed_tracking::GetTargetingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTargetingEnabled_, context, request, response, std::move(f));
}

void WeedTrackingService::Stub::async::GetTargetingEnabled(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest* request, ::weed_tracking::GetTargetingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTargetingEnabled_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTargetingEnabledResponse>* WeedTrackingService::Stub::PrepareAsyncGetTargetingEnabledRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::GetTargetingEnabledResponse, ::weed_tracking::GetTargetingEnabledRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTargetingEnabled_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::GetTargetingEnabledResponse>* WeedTrackingService::Stub::AsyncGetTargetingEnabledRaw(::grpc::ClientContext* context, const ::weed_tracking::GetTargetingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTargetingEnabledRaw(context, request, cq);
  result->StartCall();
  return result;
}

WeedTrackingService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::PingRequest, ::weed_tracking::PongReply, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::PingRequest* req,
             ::weed_tracking::PongReply* resp) {
               return service->Ping(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::GetDetectionsRequest, ::weed_tracking::GetDetectionsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::GetDetectionsRequest* req,
             ::weed_tracking::GetDetectionsResponse* resp) {
               return service->GetDetections(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::GetTrajectoryMetadataRequest, ::weed_tracking::GetTrajectoryMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::GetTrajectoryMetadataRequest* req,
             ::weed_tracking::GetTrajectoryMetadataResponse* resp) {
               return service->GetTrajectoryMetadata(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::Empty* resp) {
               return service->UpdateBands(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::GetBootedRequest, ::weed_tracking::GetBootedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::GetBootedRequest* req,
             ::weed_tracking::GetBootedResponse* resp) {
               return service->GetBooted(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::DiagnosticsSnapshot, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::DiagnosticsSnapshot* resp) {
               return service->GetCurrentTrajectories(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* req,
             ::weed_tracking::Empty* resp) {
               return service->StartSavingCropLineDetectionReplay(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::RecordDiagnosticsRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::RecordDiagnosticsRequest* req,
             ::weed_tracking::Empty* resp) {
               return service->StartRecordingDiagnostics(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::GetRecordingStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::GetRecordingStatusResponse* resp) {
               return service->GetDiagnosticsRecordingStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::Empty* resp) {
               return service->RemoveRecordingsDirectory(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::RecordAimbotInputRequest* req,
             ::weed_tracking::Empty* resp) {
               return service->StartRecordingAimbotInputs(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::ConclusionCounter, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::ConclusionCounter* resp) {
               return service->GetConclusionCounter(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::BandDefinitions, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::BandDefinitions* resp) {
               return service->GetBands(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::Empty* resp) {
               return service->StartPlantCaptcha(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::PlantCaptchaStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::PlantCaptchaStatusResponse* resp) {
               return service->GetPlantCaptchaStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[15],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::Empty* resp) {
               return service->RemovePlantCaptchaDirectory(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[16],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::Empty, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::Empty* req,
             ::weed_tracking::Empty* resp) {
               return service->CancelPlantCaptcha(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WeedTrackingService_method_names[17],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WeedTrackingService::Service, ::weed_tracking::GetTargetingEnabledRequest, ::weed_tracking::GetTargetingEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](WeedTrackingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::GetTargetingEnabledRequest* req,
             ::weed_tracking::GetTargetingEnabledResponse* resp) {
               return service->GetTargetingEnabled(ctx, req, resp);
             }, this)));
}

WeedTrackingService::Service::~Service() {
}

::grpc::Status WeedTrackingService::Service::Ping(::grpc::ServerContext* context, const ::weed_tracking::PingRequest* request, ::weed_tracking::PongReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::GetDetections(::grpc::ServerContext* context, const ::weed_tracking::GetDetectionsRequest* request, ::weed_tracking::GetDetectionsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::GetTrajectoryMetadata(::grpc::ServerContext* context, const ::weed_tracking::GetTrajectoryMetadataRequest* request, ::weed_tracking::GetTrajectoryMetadataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::UpdateBands(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::GetBooted(::grpc::ServerContext* context, const ::weed_tracking::GetBootedRequest* request, ::weed_tracking::GetBootedResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::GetCurrentTrajectories(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::DiagnosticsSnapshot* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::StartSavingCropLineDetectionReplay(::grpc::ServerContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::StartRecordingDiagnostics(::grpc::ServerContext* context, const ::weed_tracking::RecordDiagnosticsRequest* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::GetDiagnosticsRecordingStatus(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::GetRecordingStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::RemoveRecordingsDirectory(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::StartRecordingAimbotInputs(::grpc::ServerContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::GetConclusionCounter(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::ConclusionCounter* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::GetBands(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::BandDefinitions* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::StartPlantCaptcha(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::GetPlantCaptchaStatus(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::PlantCaptchaStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::RemovePlantCaptchaDirectory(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::CancelPlantCaptcha(::grpc::ServerContext* context, const ::weed_tracking::Empty* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WeedTrackingService::Service::GetTargetingEnabled(::grpc::ServerContext* context, const ::weed_tracking::GetTargetingEnabledRequest* request, ::weed_tracking::GetTargetingEnabledResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace weed_tracking


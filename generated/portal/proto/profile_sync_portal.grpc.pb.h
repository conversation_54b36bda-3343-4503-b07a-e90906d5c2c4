// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/profile_sync_portal.proto
#ifndef GRPC_portal_2fproto_2fprofile_5fsync_5fportal_2eproto__INCLUDED
#define GRPC_portal_2fproto_2fprofile_5fsync_5fportal_2eproto__INCLUDED

#include "portal/proto/profile_sync_portal.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace portal {
namespace profile_sync {

class PortalProfileSyncService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.portal.profile_sync.PortalProfileSyncService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfilesDataResponse>> AsyncGetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfilesDataResponse>>(AsyncGetProfilesDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfilesDataResponse>> PrepareAsyncGetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfilesDataResponse>>(PrepareAsyncGetProfilesDataRaw(context, request, cq));
    }
    virtual ::grpc::Status UploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncUploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncUploadProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncUploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncUploadProfileRaw(context, request, cq));
    }
    virtual ::grpc::Status GetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::carbon::portal::profile_sync::GetProfileResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfileResponse>> AsyncGetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfileResponse>>(AsyncGetProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfileResponse>> PrepareAsyncGetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfileResponse>>(PrepareAsyncGetProfileRaw(context, request, cq));
    }
    virtual ::grpc::Status DeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncDeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncDeleteProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncDeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncDeleteProfileRaw(context, request, cq));
    }
    virtual ::grpc::Status PurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncPurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncPurgeProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncPurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncPurgeProfileRaw(context, request, cq));
    }
    virtual ::grpc::Status GetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>> AsyncGetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>>(AsyncGetSetActiveProfileCommandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>> PrepareAsyncGetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>>(PrepareAsyncGetSetActiveProfileCommandsRaw(context, request, cq));
    }
    virtual ::grpc::Status PurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncPurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncPurgeSetActiveProfileCommandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncPurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncPurgeSetActiveProfileCommandsRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void UploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest* request, ::carbon::portal::profile_sync::GetProfileResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest* request, ::carbon::portal::profile_sync::GetProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void PurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void PurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void PurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void PurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfilesDataResponse>* AsyncGetProfilesDataRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfilesDataResponse>* PrepareAsyncGetProfilesDataRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncUploadProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncUploadProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfileResponse>* AsyncGetProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetProfileResponse>* PrepareAsyncGetProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncPurgeProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncPurgeProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* AsyncGetSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* PrepareAsyncGetSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncPurgeSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncPurgeSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfilesDataResponse>> AsyncGetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfilesDataResponse>>(AsyncGetProfilesDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfilesDataResponse>> PrepareAsyncGetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfilesDataResponse>>(PrepareAsyncGetProfilesDataRaw(context, request, cq));
    }
    ::grpc::Status UploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncUploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncUploadProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncUploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncUploadProfileRaw(context, request, cq));
    }
    ::grpc::Status GetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::carbon::portal::profile_sync::GetProfileResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfileResponse>> AsyncGetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfileResponse>>(AsyncGetProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfileResponse>> PrepareAsyncGetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfileResponse>>(PrepareAsyncGetProfileRaw(context, request, cq));
    }
    ::grpc::Status DeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncDeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncDeleteProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncDeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncDeleteProfileRaw(context, request, cq));
    }
    ::grpc::Status PurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncPurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncPurgeProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncPurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncPurgeProfileRaw(context, request, cq));
    }
    ::grpc::Status GetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>> AsyncGetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>>(AsyncGetSetActiveProfileCommandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>> PrepareAsyncGetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>>(PrepareAsyncGetSetActiveProfileCommandsRaw(context, request, cq));
    }
    ::grpc::Status PurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncPurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncPurgeSetActiveProfileCommandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncPurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncPurgeSetActiveProfileCommandsRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void UploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest* request, ::carbon::portal::profile_sync::GetProfileResponse* response, std::function<void(::grpc::Status)>) override;
      void GetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest* request, ::carbon::portal::profile_sync::GetProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void PurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void PurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void PurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void PurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfilesDataResponse>* AsyncGetProfilesDataRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfilesDataResponse>* PrepareAsyncGetProfilesDataRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncUploadProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncUploadProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfileResponse>* AsyncGetProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfileResponse>* PrepareAsyncGetProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncPurgeProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncPurgeProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* AsyncGetSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* PrepareAsyncGetSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncPurgeSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncPurgeSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetProfilesData_;
    const ::grpc::internal::RpcMethod rpcmethod_UploadProfile_;
    const ::grpc::internal::RpcMethod rpcmethod_GetProfile_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteProfile_;
    const ::grpc::internal::RpcMethod rpcmethod_PurgeProfile_;
    const ::grpc::internal::RpcMethod rpcmethod_GetSetActiveProfileCommands_;
    const ::grpc::internal::RpcMethod rpcmethod_PurgeSetActiveProfileCommands_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetProfilesData(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response);
    virtual ::grpc::Status UploadProfile(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response);
    virtual ::grpc::Status GetProfile(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::GetProfileRequest* request, ::carbon::portal::profile_sync::GetProfileResponse* response);
    virtual ::grpc::Status DeleteProfile(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response);
    virtual ::grpc::Status PurgeProfile(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response);
    virtual ::grpc::Status GetSetActiveProfileCommands(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response);
    virtual ::grpc::Status PurgeSetActiveProfileCommands(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::carbon::portal::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetProfilesData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetProfilesData() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetProfilesData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfilesData(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfilesDataRequest* /*request*/, ::carbon::portal::profile_sync::GetProfilesDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetProfilesData(::grpc::ServerContext* context, ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::profile_sync::GetProfilesDataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UploadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UploadProfile() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_UploadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::UploadProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadProfile(::grpc::ServerContext* context, ::carbon::portal::profile_sync::UploadProfileRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetProfile() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfileRequest* /*request*/, ::carbon::portal::profile_sync::GetProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetProfile(::grpc::ServerContext* context, ::carbon::portal::profile_sync::GetProfileRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::profile_sync::GetProfileResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::DeleteProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteProfile(::grpc::ServerContext* context, ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_PurgeProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_PurgeProfile() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_PurgeProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPurgeProfile(::grpc::ServerContext* context, ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_GetSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSetActiveProfileCommands(::grpc::ServerContext* context, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_PurgeSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_PurgeSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_PurgeSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPurgeSetActiveProfileCommands(::grpc::ServerContext* context, ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetProfilesData<WithAsyncMethod_UploadProfile<WithAsyncMethod_GetProfile<WithAsyncMethod_DeleteProfile<WithAsyncMethod_PurgeProfile<WithAsyncMethod_GetSetActiveProfileCommands<WithAsyncMethod_PurgeSetActiveProfileCommands<Service > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetProfilesData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetProfilesData() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::GetProfilesDataRequest, ::carbon::portal::profile_sync::GetProfilesDataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response) { return this->GetProfilesData(context, request, response); }));}
    void SetMessageAllocatorFor_GetProfilesData(
        ::grpc::MessageAllocator< ::carbon::portal::profile_sync::GetProfilesDataRequest, ::carbon::portal::profile_sync::GetProfilesDataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::GetProfilesDataRequest, ::carbon::portal::profile_sync::GetProfilesDataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetProfilesData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfilesData(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfilesDataRequest* /*request*/, ::carbon::portal::profile_sync::GetProfilesDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetProfilesData(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfilesDataRequest* /*request*/, ::carbon::portal::profile_sync::GetProfilesDataResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UploadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UploadProfile() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response) { return this->UploadProfile(context, request, response); }));}
    void SetMessageAllocatorFor_UploadProfile(
        ::grpc::MessageAllocator< ::carbon::portal::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UploadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::UploadProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::profile_sync::UploadProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetProfile() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::GetProfileRequest, ::carbon::portal::profile_sync::GetProfileResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::profile_sync::GetProfileRequest* request, ::carbon::portal::profile_sync::GetProfileResponse* response) { return this->GetProfile(context, request, response); }));}
    void SetMessageAllocatorFor_GetProfile(
        ::grpc::MessageAllocator< ::carbon::portal::profile_sync::GetProfileRequest, ::carbon::portal::profile_sync::GetProfileResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::GetProfileRequest, ::carbon::portal::profile_sync::GetProfileResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfileRequest* /*request*/, ::carbon::portal::profile_sync::GetProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfileRequest* /*request*/, ::carbon::portal::profile_sync::GetProfileResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response) { return this->DeleteProfile(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteProfile(
        ::grpc::MessageAllocator< ::carbon::portal::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::DeleteProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::profile_sync::DeleteProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_PurgeProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_PurgeProfile() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response) { return this->PurgeProfile(context, request, response); }));}
    void SetMessageAllocatorFor_PurgeProfile(
        ::grpc::MessageAllocator< ::carbon::portal::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_PurgeProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* PurgeProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response) { return this->GetSetActiveProfileCommands(context, request, response); }));}
    void SetMessageAllocatorFor_GetSetActiveProfileCommands(
        ::grpc::MessageAllocator< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSetActiveProfileCommands(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_PurgeSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_PurgeSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::carbon::portal::util::Empty* response) { return this->PurgeSetActiveProfileCommands(context, request, response); }));}
    void SetMessageAllocatorFor_PurgeSetActiveProfileCommands(
        ::grpc::MessageAllocator< ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_PurgeSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* PurgeSetActiveProfileCommands(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetProfilesData<WithCallbackMethod_UploadProfile<WithCallbackMethod_GetProfile<WithCallbackMethod_DeleteProfile<WithCallbackMethod_PurgeProfile<WithCallbackMethod_GetSetActiveProfileCommands<WithCallbackMethod_PurgeSetActiveProfileCommands<Service > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetProfilesData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetProfilesData() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetProfilesData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfilesData(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfilesDataRequest* /*request*/, ::carbon::portal::profile_sync::GetProfilesDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UploadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UploadProfile() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_UploadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::UploadProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetProfile() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfileRequest* /*request*/, ::carbon::portal::profile_sync::GetProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::DeleteProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_PurgeProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_PurgeProfile() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_PurgeProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_GetSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_PurgeSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_PurgeSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_PurgeSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetProfilesData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetProfilesData() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetProfilesData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfilesData(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfilesDataRequest* /*request*/, ::carbon::portal::profile_sync::GetProfilesDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetProfilesData(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UploadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UploadProfile() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_UploadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::UploadProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadProfile(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetProfile() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfileRequest* /*request*/, ::carbon::portal::profile_sync::GetProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetProfile(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::DeleteProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteProfile(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_PurgeProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_PurgeProfile() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_PurgeProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPurgeProfile(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_GetSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSetActiveProfileCommands(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_PurgeSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_PurgeSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_PurgeSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPurgeSetActiveProfileCommands(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetProfilesData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetProfilesData() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetProfilesData(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetProfilesData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfilesData(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfilesDataRequest* /*request*/, ::carbon::portal::profile_sync::GetProfilesDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetProfilesData(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UploadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UploadProfile() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UploadProfile(context, request, response); }));
    }
    ~WithRawCallbackMethod_UploadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::UploadProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetProfile() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetProfile(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfileRequest* /*request*/, ::carbon::portal::profile_sync::GetProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteProfile(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::DeleteProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_PurgeProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_PurgeProfile() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->PurgeProfile(context, request, response); }));
    }
    ~WithRawCallbackMethod_PurgeProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* PurgeProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetSetActiveProfileCommands(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSetActiveProfileCommands(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_PurgeSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_PurgeSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->PurgeSetActiveProfileCommands(context, request, response); }));
    }
    ~WithRawCallbackMethod_PurgeSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* PurgeSetActiveProfileCommands(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetProfilesData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetProfilesData() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::profile_sync::GetProfilesDataRequest, ::carbon::portal::profile_sync::GetProfilesDataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::profile_sync::GetProfilesDataRequest, ::carbon::portal::profile_sync::GetProfilesDataResponse>* streamer) {
                       return this->StreamedGetProfilesData(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetProfilesData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetProfilesData(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfilesDataRequest* /*request*/, ::carbon::portal::profile_sync::GetProfilesDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetProfilesData(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::profile_sync::GetProfilesDataRequest,::carbon::portal::profile_sync::GetProfilesDataResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UploadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UploadProfile() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedUploadProfile(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UploadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UploadProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::UploadProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUploadProfile(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::profile_sync::UploadProfileRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetProfile() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::profile_sync::GetProfileRequest, ::carbon::portal::profile_sync::GetProfileResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::profile_sync::GetProfileRequest, ::carbon::portal::profile_sync::GetProfileResponse>* streamer) {
                       return this->StreamedGetProfile(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetProfileRequest* /*request*/, ::carbon::portal::profile_sync::GetProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetProfile(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::profile_sync::GetProfileRequest,::carbon::portal::profile_sync::GetProfileResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedDeleteProfile(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::DeleteProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteProfile(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::profile_sync::DeleteProfileRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_PurgeProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_PurgeProfile() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedPurgeProfile(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_PurgeProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status PurgeProfile(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeProfileRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPurgeProfile(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::profile_sync::PurgeProfileRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* streamer) {
                       return this->StreamedGetSetActiveProfileCommands(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetSetActiveProfileCommands(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest,::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_PurgeSetActiveProfileCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_PurgeSetActiveProfileCommands() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedPurgeSetActiveProfileCommands(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_PurgeSetActiveProfileCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status PurgeSetActiveProfileCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPurgeSetActiveProfileCommands(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetProfilesData<WithStreamedUnaryMethod_UploadProfile<WithStreamedUnaryMethod_GetProfile<WithStreamedUnaryMethod_DeleteProfile<WithStreamedUnaryMethod_PurgeProfile<WithStreamedUnaryMethod_GetSetActiveProfileCommands<WithStreamedUnaryMethod_PurgeSetActiveProfileCommands<Service > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetProfilesData<WithStreamedUnaryMethod_UploadProfile<WithStreamedUnaryMethod_GetProfile<WithStreamedUnaryMethod_DeleteProfile<WithStreamedUnaryMethod_PurgeProfile<WithStreamedUnaryMethod_GetSetActiveProfileCommands<WithStreamedUnaryMethod_PurgeSetActiveProfileCommands<Service > > > > > > > StreamedService;
};

}  // namespace profile_sync
}  // namespace portal
}  // namespace carbon


#endif  // GRPC_portal_2fproto_2fprofile_5fsync_5fportal_2eproto__INCLUDED

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.portal.proto import remoteit_pb2 as portal_dot_proto_dot_remoteit__pb2


class RemoteItManagerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Configure = channel.unary_unary(
                '/carbon.portal.remoteit.RemoteItManager/Configure',
                request_serializer=portal_dot_proto_dot_remoteit__pb2.ConfigureRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_remoteit__pb2.ConfigureResult.FromString,
                )


class RemoteItManagerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Configure(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RemoteItManagerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Configure': grpc.unary_unary_rpc_method_handler(
                    servicer.Configure,
                    request_deserializer=portal_dot_proto_dot_remoteit__pb2.ConfigureRequest.FromString,
                    response_serializer=portal_dot_proto_dot_remoteit__pb2.ConfigureResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.portal.remoteit.RemoteItManager', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class RemoteItManager(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Configure(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.remoteit.RemoteItManager/Configure',
            portal_dot_proto_dot_remoteit__pb2.ConfigureRequest.SerializeToString,
            portal_dot_proto_dot_remoteit__pb2.ConfigureResult.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

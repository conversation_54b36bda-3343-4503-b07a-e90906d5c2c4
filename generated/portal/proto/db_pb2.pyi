"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class DB(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...
    created_at: builtin___int = ...
    updated_at: builtin___int = ...
    deleted_at: builtin___int = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        created_at : typing___Optional[builtin___int] = None,
        updated_at : typing___Optional[builtin___int] = None,
        deleted_at : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"created_at",b"created_at",u"deleted_at",b"deleted_at",u"id",b"id",u"updated_at",b"updated_at"]) -> None: ...
type___DB = DB

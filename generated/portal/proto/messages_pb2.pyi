"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.portal.proto.db_pb2 import (
    DB as portal___proto___db_pb2___DB,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Message(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    message: typing___Text = ...
    author_user_id: typing___Text = ...
    author_robot_id: builtin___int = ...
    recipient_user_id: typing___Text = ...
    recipient_customer_id: builtin___int = ...
    recipient_robot_id: builtin___int = ...

    @property
    def db(self) -> portal___proto___db_pb2___DB: ...

    def __init__(self,
        *,
        db : typing___Optional[portal___proto___db_pb2___DB] = None,
        message : typing___Optional[typing___Text] = None,
        author_user_id : typing___Optional[typing___Text] = None,
        author_robot_id : typing___Optional[builtin___int] = None,
        recipient_user_id : typing___Optional[typing___Text] = None,
        recipient_customer_id : typing___Optional[builtin___int] = None,
        recipient_robot_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"db",b"db"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"author_robot_id",b"author_robot_id",u"author_user_id",b"author_user_id",u"db",b"db",u"message",b"message",u"recipient_customer_id",b"recipient_customer_id",u"recipient_robot_id",b"recipient_robot_id",u"recipient_user_id",b"recipient_user_id"]) -> None: ...
type___Message = Message

class MessagesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page: builtin___int = ...
    limit: builtin___int = ...

    @property
    def messages(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Message]: ...

    def __init__(self,
        *,
        page : typing___Optional[builtin___int] = None,
        limit : typing___Optional[builtin___int] = None,
        messages : typing___Optional[typing___Iterable[type___Message]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"limit",b"limit",u"messages",b"messages",u"page",b"page"]) -> None: ...
type___MessagesResponse = MessagesResponse

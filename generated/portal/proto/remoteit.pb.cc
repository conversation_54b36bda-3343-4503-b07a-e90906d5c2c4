// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/remoteit.proto

#include "portal/proto/remoteit.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace remoteit {
constexpr ConfigureRequest::ConfigureRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : serviceid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , devicename_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ConfigureRequestDefaultTypeInternal {
  constexpr ConfigureRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigureRequestDefaultTypeInternal() {}
  union {
    ConfigureRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigureRequestDefaultTypeInternal _ConfigureRequest_default_instance_;
constexpr ConfigureResult::ConfigureResult(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : status_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ConfigureResultDefaultTypeInternal {
  constexpr ConfigureResultDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigureResultDefaultTypeInternal() {}
  union {
    ConfigureResult _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigureResultDefaultTypeInternal _ConfigureResult_default_instance_;
}  // namespace remoteit
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2fremoteit_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2fremoteit_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2fremoteit_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2fremoteit_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::remoteit::ConfigureRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::remoteit::ConfigureRequest, serviceid_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::remoteit::ConfigureRequest, devicename_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::remoteit::ConfigureResult, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::remoteit::ConfigureResult, status_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::remoteit::ConfigureRequest)},
  { 8, -1, -1, sizeof(::carbon::portal::remoteit::ConfigureResult)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::remoteit::_ConfigureRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::remoteit::_ConfigureResult_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2fremoteit_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033portal/proto/remoteit.proto\022\026carbon.po"
  "rtal.remoteit\"9\n\020ConfigureRequest\022\021\n\tser"
  "viceId\030\001 \001(\t\022\022\n\ndeviceName\030\002 \001(\t\"!\n\017Conf"
  "igureResult\022\016\n\006status\030\001 \001(\t2s\n\017RemoteItM"
  "anager\022`\n\tConfigure\022(.carbon.portal.remo"
  "teit.ConfigureRequest\032\'.carbon.portal.re"
  "moteit.ConfigureResult\"\000B\016Z\014proto/portal"
  "b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2fremoteit_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fremoteit_2eproto = {
  false, false, 288, descriptor_table_protodef_portal_2fproto_2fremoteit_2eproto, "portal/proto/remoteit.proto", 
  &descriptor_table_portal_2fproto_2fremoteit_2eproto_once, nullptr, 0, 2,
  schemas, file_default_instances, TableStruct_portal_2fproto_2fremoteit_2eproto::offsets,
  file_level_metadata_portal_2fproto_2fremoteit_2eproto, file_level_enum_descriptors_portal_2fproto_2fremoteit_2eproto, file_level_service_descriptors_portal_2fproto_2fremoteit_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2fremoteit_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2fremoteit_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2fremoteit_2eproto(&descriptor_table_portal_2fproto_2fremoteit_2eproto);
namespace carbon {
namespace portal {
namespace remoteit {

// ===================================================================

class ConfigureRequest::_Internal {
 public:
};

ConfigureRequest::ConfigureRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.remoteit.ConfigureRequest)
}
ConfigureRequest::ConfigureRequest(const ConfigureRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  serviceid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    serviceid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_serviceid().empty()) {
    serviceid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serviceid(), 
      GetArenaForAllocation());
  }
  devicename_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    devicename_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_devicename().empty()) {
    devicename_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_devicename(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.remoteit.ConfigureRequest)
}

inline void ConfigureRequest::SharedCtor() {
serviceid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  serviceid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
devicename_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  devicename_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ConfigureRequest::~ConfigureRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.remoteit.ConfigureRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ConfigureRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  serviceid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  devicename_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ConfigureRequest::ArenaDtor(void* object) {
  ConfigureRequest* _this = reinterpret_cast< ConfigureRequest* >(object);
  (void)_this;
}
void ConfigureRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ConfigureRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ConfigureRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.remoteit.ConfigureRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  serviceid_.ClearToEmpty();
  devicename_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigureRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string serviceId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_serviceid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.remoteit.ConfigureRequest.serviceId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string deviceName = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_devicename();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.remoteit.ConfigureRequest.deviceName"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigureRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.remoteit.ConfigureRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string serviceId = 1;
  if (!this->_internal_serviceid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_serviceid().data(), static_cast<int>(this->_internal_serviceid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.remoteit.ConfigureRequest.serviceId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_serviceid(), target);
  }

  // string deviceName = 2;
  if (!this->_internal_devicename().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_devicename().data(), static_cast<int>(this->_internal_devicename().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.remoteit.ConfigureRequest.deviceName");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_devicename(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.remoteit.ConfigureRequest)
  return target;
}

size_t ConfigureRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.remoteit.ConfigureRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string serviceId = 1;
  if (!this->_internal_serviceid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_serviceid());
  }

  // string deviceName = 2;
  if (!this->_internal_devicename().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_devicename());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigureRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ConfigureRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigureRequest::GetClassData() const { return &_class_data_; }

void ConfigureRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ConfigureRequest *>(to)->MergeFrom(
      static_cast<const ConfigureRequest &>(from));
}


void ConfigureRequest::MergeFrom(const ConfigureRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.remoteit.ConfigureRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_serviceid().empty()) {
    _internal_set_serviceid(from._internal_serviceid());
  }
  if (!from._internal_devicename().empty()) {
    _internal_set_devicename(from._internal_devicename());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigureRequest::CopyFrom(const ConfigureRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.remoteit.ConfigureRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigureRequest::IsInitialized() const {
  return true;
}

void ConfigureRequest::InternalSwap(ConfigureRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serviceid_, lhs_arena,
      &other->serviceid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &devicename_, lhs_arena,
      &other->devicename_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigureRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fremoteit_2eproto_getter, &descriptor_table_portal_2fproto_2fremoteit_2eproto_once,
      file_level_metadata_portal_2fproto_2fremoteit_2eproto[0]);
}

// ===================================================================

class ConfigureResult::_Internal {
 public:
};

ConfigureResult::ConfigureResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.remoteit.ConfigureResult)
}
ConfigureResult::ConfigureResult(const ConfigureResult& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  status_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_status().empty()) {
    status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_status(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.remoteit.ConfigureResult)
}

inline void ConfigureResult::SharedCtor() {
status_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ConfigureResult::~ConfigureResult() {
  // @@protoc_insertion_point(destructor:carbon.portal.remoteit.ConfigureResult)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ConfigureResult::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  status_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ConfigureResult::ArenaDtor(void* object) {
  ConfigureResult* _this = reinterpret_cast< ConfigureResult* >(object);
  (void)_this;
}
void ConfigureResult::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ConfigureResult::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ConfigureResult::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.remoteit.ConfigureResult)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  status_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigureResult::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string status = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_status();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.remoteit.ConfigureResult.status"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigureResult::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.remoteit.ConfigureResult)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string status = 1;
  if (!this->_internal_status().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_status().data(), static_cast<int>(this->_internal_status().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.remoteit.ConfigureResult.status");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_status(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.remoteit.ConfigureResult)
  return target;
}

size_t ConfigureResult::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.remoteit.ConfigureResult)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string status = 1;
  if (!this->_internal_status().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_status());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigureResult::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ConfigureResult::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigureResult::GetClassData() const { return &_class_data_; }

void ConfigureResult::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ConfigureResult *>(to)->MergeFrom(
      static_cast<const ConfigureResult &>(from));
}


void ConfigureResult::MergeFrom(const ConfigureResult& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.remoteit.ConfigureResult)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_status().empty()) {
    _internal_set_status(from._internal_status());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigureResult::CopyFrom(const ConfigureResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.remoteit.ConfigureResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigureResult::IsInitialized() const {
  return true;
}

void ConfigureResult::InternalSwap(ConfigureResult* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &status_, lhs_arena,
      &other->status_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigureResult::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fremoteit_2eproto_getter, &descriptor_table_portal_2fproto_2fremoteit_2eproto_once,
      file_level_metadata_portal_2fproto_2fremoteit_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace remoteit
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::remoteit::ConfigureRequest* Arena::CreateMaybeMessage< ::carbon::portal::remoteit::ConfigureRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::remoteit::ConfigureRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::remoteit::ConfigureResult* Arena::CreateMaybeMessage< ::carbon::portal::remoteit::ConfigureResult >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::remoteit::ConfigureResult >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

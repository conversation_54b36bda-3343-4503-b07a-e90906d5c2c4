# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.portal.proto import reaper_pb2 as portal_dot_proto_dot_reaper__pb2


class ReaperConfigurationServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.UploadReaperConfiguration = channel.unary_unary(
                '/carbon.portal.reaper.ReaperConfigurationService/UploadReaperConfiguration',
                request_serializer=portal_dot_proto_dot_reaper__pb2.UploadReaperConfigurationRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_reaper__pb2.UploadReaperConfigurationResponse.FromString,
                )


class ReaperConfigurationServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def UploadReaperConfiguration(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ReaperConfigurationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'UploadReaperConfiguration': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadReaperConfiguration,
                    request_deserializer=portal_dot_proto_dot_reaper__pb2.UploadReaperConfigurationRequest.FromString,
                    response_serializer=portal_dot_proto_dot_reaper__pb2.UploadReaperConfigurationResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.portal.reaper.ReaperConfigurationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ReaperConfigurationService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def UploadReaperConfiguration(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.reaper.ReaperConfigurationService/UploadReaperConfiguration',
            portal_dot_proto_dot_reaper__pb2.UploadReaperConfigurationRequest.SerializeToString,
            portal_dot_proto_dot_reaper__pb2.UploadReaperConfigurationResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class ModelInfo(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model_id: typing___Text = ...
    crop_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    nickname: typing___Text = ...

    def __init__(self,
        *,
        model_id : typing___Optional[typing___Text] = None,
        crop_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        nickname : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_ids",b"crop_ids",u"model_id",b"model_id",u"nickname",b"nickname"]) -> None: ...
type___ModelInfo = ModelInfo

class UploadModelInfosRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    robot: typing___Text = ...

    @property
    def model_infos(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModelInfo]: ...

    def __init__(self,
        *,
        robot : typing___Optional[typing___Text] = None,
        model_infos : typing___Optional[typing___Iterable[type___ModelInfo]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"model_infos",b"model_infos",u"robot",b"robot"]) -> None: ...
type___UploadModelInfosRequest = UploadModelInfosRequest

class RenameModelCommand(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model_id: typing___Text = ...
    new_nickname: typing___Text = ...

    def __init__(self,
        *,
        model_id : typing___Optional[typing___Text] = None,
        new_nickname : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"model_id",b"model_id",u"new_nickname",b"new_nickname"]) -> None: ...
type___RenameModelCommand = RenameModelCommand

class GetRenameModelCommandsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    robot: typing___Text = ...

    def __init__(self,
        *,
        robot : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"robot",b"robot"]) -> None: ...
type___GetRenameModelCommandsRequest = GetRenameModelCommandsRequest

class GetRenameModelCommandsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def commands(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___RenameModelCommand]: ...

    def __init__(self,
        *,
        commands : typing___Optional[typing___Iterable[type___RenameModelCommand]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"commands",b"commands"]) -> None: ...
type___GetRenameModelCommandsResponse = GetRenameModelCommandsResponse

class PurgeRenameModelCommandsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    robot: typing___Text = ...

    @property
    def commands(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___RenameModelCommand]: ...

    def __init__(self,
        *,
        robot : typing___Optional[typing___Text] = None,
        commands : typing___Optional[typing___Iterable[type___RenameModelCommand]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"commands",b"commands",u"robot",b"robot"]) -> None: ...
type___PurgeRenameModelCommandsRequest = PurgeRenameModelCommandsRequest

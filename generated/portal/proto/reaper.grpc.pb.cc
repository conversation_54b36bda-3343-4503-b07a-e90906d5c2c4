// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/reaper.proto

#include "portal/proto/reaper.pb.h"
#include "portal/proto/reaper.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace reaper {

static const char* ReaperConfigurationService_method_names[] = {
  "/carbon.portal.reaper.ReaperConfigurationService/UploadReaperConfiguration",
};

std::unique_ptr< ReaperConfigurationService::Stub> ReaperConfigurationService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ReaperConfigurationService::Stub> stub(new ReaperConfigurationService::Stub(channel, options));
  return stub;
}

ReaperConfigurationService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_UploadReaperConfiguration_(ReaperConfigurationService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ReaperConfigurationService::Stub::UploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::reaper::UploadReaperConfigurationRequest, ::carbon::portal::reaper::UploadReaperConfigurationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UploadReaperConfiguration_, context, request, response);
}

void ReaperConfigurationService::Stub::async::UploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::reaper::UploadReaperConfigurationRequest, ::carbon::portal::reaper::UploadReaperConfigurationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadReaperConfiguration_, context, request, response, std::move(f));
}

void ReaperConfigurationService::Stub::async::UploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadReaperConfiguration_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::reaper::UploadReaperConfigurationResponse>* ReaperConfigurationService::Stub::PrepareAsyncUploadReaperConfigurationRaw(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::reaper::UploadReaperConfigurationResponse, ::carbon::portal::reaper::UploadReaperConfigurationRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UploadReaperConfiguration_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::reaper::UploadReaperConfigurationResponse>* ReaperConfigurationService::Stub::AsyncUploadReaperConfigurationRaw(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUploadReaperConfigurationRaw(context, request, cq);
  result->StartCall();
  return result;
}

ReaperConfigurationService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ReaperConfigurationService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ReaperConfigurationService::Service, ::carbon::portal::reaper::UploadReaperConfigurationRequest, ::carbon::portal::reaper::UploadReaperConfigurationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ReaperConfigurationService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::reaper::UploadReaperConfigurationRequest* req,
             ::carbon::portal::reaper::UploadReaperConfigurationResponse* resp) {
               return service->UploadReaperConfiguration(ctx, req, resp);
             }, this)));
}

ReaperConfigurationService::Service::~Service() {
}

::grpc::Status ReaperConfigurationService::Service::UploadReaperConfiguration(::grpc::ServerContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace reaper


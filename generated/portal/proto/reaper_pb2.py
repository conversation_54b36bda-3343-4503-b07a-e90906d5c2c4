# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/reaper.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import module_pb2 as frontend_dot_proto_dot_module__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/reaper.proto',
  package='carbon.portal.reaper',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x19portal/proto/reaper.proto\x12\x14\x63\x61rbon.portal.reaper\x1a\x1b\x66rontend/proto/module.proto\"\xa2\x01\n\x13ReaperConfiguration\x12@\n\x10\x61ssigned_modules\x18\x01 \x03(\x0b\x32&.carbon.frontend.module.ModuleIdentity\x12I\n\x18\x63urrent_robot_definition\x18\x02 \x01(\x0b\x32\'.carbon.frontend.module.RobotDefinition\"d\n UploadReaperConfigurationRequest\x12@\n\rconfiguration\x18\x01 \x01(\x0b\x32).carbon.portal.reaper.ReaperConfiguration\"#\n!UploadReaperConfigurationResponse2\xab\x01\n\x1aReaperConfigurationService\x12\x8c\x01\n\x19UploadReaperConfiguration\x12\x36.carbon.portal.reaper.UploadReaperConfigurationRequest\x1a\x37.carbon.portal.reaper.UploadReaperConfigurationResponseB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_module__pb2.DESCRIPTOR,])




_REAPERCONFIGURATION = _descriptor.Descriptor(
  name='ReaperConfiguration',
  full_name='carbon.portal.reaper.ReaperConfiguration',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='assigned_modules', full_name='carbon.portal.reaper.ReaperConfiguration.assigned_modules', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_robot_definition', full_name='carbon.portal.reaper.ReaperConfiguration.current_robot_definition', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=81,
  serialized_end=243,
)


_UPLOADREAPERCONFIGURATIONREQUEST = _descriptor.Descriptor(
  name='UploadReaperConfigurationRequest',
  full_name='carbon.portal.reaper.UploadReaperConfigurationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='configuration', full_name='carbon.portal.reaper.UploadReaperConfigurationRequest.configuration', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=245,
  serialized_end=345,
)


_UPLOADREAPERCONFIGURATIONRESPONSE = _descriptor.Descriptor(
  name='UploadReaperConfigurationResponse',
  full_name='carbon.portal.reaper.UploadReaperConfigurationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=347,
  serialized_end=382,
)

_REAPERCONFIGURATION.fields_by_name['assigned_modules'].message_type = frontend_dot_proto_dot_module__pb2._MODULEIDENTITY
_REAPERCONFIGURATION.fields_by_name['current_robot_definition'].message_type = frontend_dot_proto_dot_module__pb2._ROBOTDEFINITION
_UPLOADREAPERCONFIGURATIONREQUEST.fields_by_name['configuration'].message_type = _REAPERCONFIGURATION
DESCRIPTOR.message_types_by_name['ReaperConfiguration'] = _REAPERCONFIGURATION
DESCRIPTOR.message_types_by_name['UploadReaperConfigurationRequest'] = _UPLOADREAPERCONFIGURATIONREQUEST
DESCRIPTOR.message_types_by_name['UploadReaperConfigurationResponse'] = _UPLOADREAPERCONFIGURATIONRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ReaperConfiguration = _reflection.GeneratedProtocolMessageType('ReaperConfiguration', (_message.Message,), {
  'DESCRIPTOR' : _REAPERCONFIGURATION,
  '__module__' : 'portal.proto.reaper_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.reaper.ReaperConfiguration)
  })
_sym_db.RegisterMessage(ReaperConfiguration)

UploadReaperConfigurationRequest = _reflection.GeneratedProtocolMessageType('UploadReaperConfigurationRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADREAPERCONFIGURATIONREQUEST,
  '__module__' : 'portal.proto.reaper_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.reaper.UploadReaperConfigurationRequest)
  })
_sym_db.RegisterMessage(UploadReaperConfigurationRequest)

UploadReaperConfigurationResponse = _reflection.GeneratedProtocolMessageType('UploadReaperConfigurationResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADREAPERCONFIGURATIONRESPONSE,
  '__module__' : 'portal.proto.reaper_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.reaper.UploadReaperConfigurationResponse)
  })
_sym_db.RegisterMessage(UploadReaperConfigurationResponse)


DESCRIPTOR._options = None

_REAPERCONFIGURATIONSERVICE = _descriptor.ServiceDescriptor(
  name='ReaperConfigurationService',
  full_name='carbon.portal.reaper.ReaperConfigurationService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=385,
  serialized_end=556,
  methods=[
  _descriptor.MethodDescriptor(
    name='UploadReaperConfiguration',
    full_name='carbon.portal.reaper.ReaperConfigurationService.UploadReaperConfiguration',
    index=0,
    containing_service=None,
    input_type=_UPLOADREAPERCONFIGURATIONREQUEST,
    output_type=_UPLOADREAPERCONFIGURATIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_REAPERCONFIGURATIONSERVICE)

DESCRIPTOR.services_by_name['ReaperConfigurationService'] = _REAPERCONFIGURATIONSERVICE

# @@protoc_insertion_point(module_scope)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

ModelEventTypeValue = typing___NewType('ModelEventTypeValue', builtin___int)
type___ModelEventTypeValue = ModelEventTypeValue
ModelEventType: _ModelEventType
class _ModelEventType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ModelEventTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    UNKNOWN = typing___cast(ModelEventTypeValue, 0)
    ROBOT_START = typing___cast(ModelEventTypeValue, 1)
    PINNED = typing___cast(ModelEventTypeValue, 2)
    UNPINNED = typing___cast(ModelEventTypeValue, 3)
    RECOMMENDED = typing___cast(ModelEventTypeValue, 4)
    ACTIVATED = typing___cast(ModelEventTypeValue, 5)
    NICKNAME_CHANGE = typing___cast(ModelEventTypeValue, 6)
    NICKNAME_DELETE = typing___cast(ModelEventTypeValue, 7)
    DEFAULT_PARAMETER_CHANGE = typing___cast(ModelEventTypeValue, 8)
    PARAMETER_CHANGE = typing___cast(ModelEventTypeValue, 9)
UNKNOWN = typing___cast(ModelEventTypeValue, 0)
ROBOT_START = typing___cast(ModelEventTypeValue, 1)
PINNED = typing___cast(ModelEventTypeValue, 2)
UNPINNED = typing___cast(ModelEventTypeValue, 3)
RECOMMENDED = typing___cast(ModelEventTypeValue, 4)
ACTIVATED = typing___cast(ModelEventTypeValue, 5)
NICKNAME_CHANGE = typing___cast(ModelEventTypeValue, 6)
NICKNAME_DELETE = typing___cast(ModelEventTypeValue, 7)
DEFAULT_PARAMETER_CHANGE = typing___cast(ModelEventTypeValue, 8)
PARAMETER_CHANGE = typing___cast(ModelEventTypeValue, 9)

class ModelEvent(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type: type___ModelEventTypeValue = ...
    model_id: typing___Text = ...
    model_nickname: typing___Text = ...
    model_parameters: typing___Text = ...
    model_type: typing___Text = ...
    crop_id: typing___Text = ...
    job_id: typing___Text = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        type : typing___Optional[type___ModelEventTypeValue] = None,
        model_id : typing___Optional[typing___Text] = None,
        model_nickname : typing___Optional[typing___Text] = None,
        model_parameters : typing___Optional[typing___Text] = None,
        model_type : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        job_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_id",b"crop_id",u"job_id",b"job_id",u"model_id",b"model_id",u"model_nickname",b"model_nickname",u"model_parameters",b"model_parameters",u"model_type",b"model_type",u"timestamp_ms",b"timestamp_ms",u"type",b"type"]) -> None: ...
type___ModelEvent = ModelEvent

class UploadModelEventsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    robot: typing___Text = ...

    @property
    def events(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModelEvent]: ...

    def __init__(self,
        *,
        robot : typing___Optional[typing___Text] = None,
        events : typing___Optional[typing___Iterable[type___ModelEvent]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"events",b"events",u"robot",b"robot"]) -> None: ...
type___UploadModelEventsRequest = UploadModelEventsRequest

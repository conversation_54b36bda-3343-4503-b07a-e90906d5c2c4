# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/util.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/util.proto',
  package='carbon.portal.util',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x17portal/proto/util.proto\x12\x12\x63\x61rbon.portal.util\"\x07\n\x05\x45mpty\"!\n\tTimestamp\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x42\x0eZ\x0cproto/portalb\x06proto3'
)




_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='carbon.portal.util.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=47,
  serialized_end=54,
)


_TIMESTAMP = _descriptor.Descriptor(
  name='Timestamp',
  full_name='carbon.portal.util.Timestamp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.portal.util.Timestamp.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=56,
  serialized_end=89,
)

DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['Timestamp'] = _TIMESTAMP
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'portal.proto.util_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.util.Empty)
  })
_sym_db.RegisterMessage(Empty)

Timestamp = _reflection.GeneratedProtocolMessageType('Timestamp', (_message.Message,), {
  'DESCRIPTOR' : _TIMESTAMP,
  '__module__' : 'portal.proto.util_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.util.Timestamp)
  })
_sym_db.RegisterMessage(Timestamp)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.portal.proto import model_info_sync_pb2 as portal_dot_proto_dot_model__info__sync__pb2
from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2


class ModelInfoSyncServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.UploadModelInfos = channel.unary_unary(
                '/carbon.portal.model_info.ModelInfoSyncService/UploadModelInfos',
                request_serializer=portal_dot_proto_dot_model__info__sync__pb2.UploadModelInfosRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetRenameModelCommands = channel.unary_unary(
                '/carbon.portal.model_info.ModelInfoSyncService/GetRenameModelCommands',
                request_serializer=portal_dot_proto_dot_model__info__sync__pb2.GetRenameModelCommandsRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_model__info__sync__pb2.GetRenameModelCommandsResponse.FromString,
                )
        self.PurgeRenameModelCommands = channel.unary_unary(
                '/carbon.portal.model_info.ModelInfoSyncService/PurgeRenameModelCommands',
                request_serializer=portal_dot_proto_dot_model__info__sync__pb2.PurgeRenameModelCommandsRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )


class ModelInfoSyncServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def UploadModelInfos(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRenameModelCommands(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PurgeRenameModelCommands(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModelInfoSyncServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'UploadModelInfos': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadModelInfos,
                    request_deserializer=portal_dot_proto_dot_model__info__sync__pb2.UploadModelInfosRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetRenameModelCommands': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRenameModelCommands,
                    request_deserializer=portal_dot_proto_dot_model__info__sync__pb2.GetRenameModelCommandsRequest.FromString,
                    response_serializer=portal_dot_proto_dot_model__info__sync__pb2.GetRenameModelCommandsResponse.SerializeToString,
            ),
            'PurgeRenameModelCommands': grpc.unary_unary_rpc_method_handler(
                    servicer.PurgeRenameModelCommands,
                    request_deserializer=portal_dot_proto_dot_model__info__sync__pb2.PurgeRenameModelCommandsRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.portal.model_info.ModelInfoSyncService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ModelInfoSyncService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def UploadModelInfos(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.model_info.ModelInfoSyncService/UploadModelInfos',
            portal_dot_proto_dot_model__info__sync__pb2.UploadModelInfosRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetRenameModelCommands(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.model_info.ModelInfoSyncService/GetRenameModelCommands',
            portal_dot_proto_dot_model__info__sync__pb2.GetRenameModelCommandsRequest.SerializeToString,
            portal_dot_proto_dot_model__info__sync__pb2.GetRenameModelCommandsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def PurgeRenameModelCommands(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.model_info.ModelInfoSyncService/PurgeRenameModelCommands',
            portal_dot_proto_dot_model__info__sync__pb2.PurgeRenameModelCommandsRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

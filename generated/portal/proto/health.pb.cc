// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/health.proto

#include "portal/proto/health.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace health {
constexpr AlarmRow::AlarmRow(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : alarm_code_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , identifier_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0})
  , level_(0)

  , acknowledged_(false)
  , impact_(0)
{}
struct AlarmRowDefaultTypeInternal {
  constexpr AlarmRowDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AlarmRowDefaultTypeInternal() {}
  union {
    AlarmRow _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AlarmRowDefaultTypeInternal _AlarmRow_default_instance_;
constexpr Location::Location(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0)
  , y_(0)
  , z_(0){}
struct LocationDefaultTypeInternal {
  constexpr LocationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LocationDefaultTypeInternal() {}
  union {
    Location _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LocationDefaultTypeInternal _Location_default_instance_;
constexpr FieldConfig::FieldConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : active_band_config_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_thinning_config_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_job_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_almanac_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_discriminator_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_band_config_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_thinning_config_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_job_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_almanac_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_discriminator_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_modelinator_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_velocity_estimator_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_velocity_estimator_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_category_collection_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , banding_enabled_(false)
  , banding_dynamic_(false)
  , is_weeding_(false)
  , is_thinning_(false){}
struct FieldConfigDefaultTypeInternal {
  constexpr FieldConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FieldConfigDefaultTypeInternal() {}
  union {
    FieldConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FieldConfigDefaultTypeInternal _FieldConfig_default_instance_;
constexpr Versions::Versions(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : current_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , latest_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct VersionsDefaultTypeInternal {
  constexpr VersionsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VersionsDefaultTypeInternal() {}
  union {
    Versions _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VersionsDefaultTypeInternal _Versions_default_instance_;
constexpr WeedingPerformance::WeedingPerformance(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : area_weeded_total_(0)
  , area_weeded_today_(0)
  , time_weeded_today_(int64_t{0}){}
struct WeedingPerformanceDefaultTypeInternal {
  constexpr WeedingPerformanceDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~WeedingPerformanceDefaultTypeInternal() {}
  union {
    WeedingPerformance _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT WeedingPerformanceDefaultTypeInternal _WeedingPerformance_default_instance_;
constexpr Performance::Performance(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : weeding_(nullptr){}
struct PerformanceDefaultTypeInternal {
  constexpr PerformanceDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PerformanceDefaultTypeInternal() {}
  union {
    Performance _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PerformanceDefaultTypeInternal _Performance_default_instance_;
constexpr DailyMetrics_MetricsEntry_DoNotUse::DailyMetrics_MetricsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct DailyMetrics_MetricsEntry_DoNotUseDefaultTypeInternal {
  constexpr DailyMetrics_MetricsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DailyMetrics_MetricsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    DailyMetrics_MetricsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DailyMetrics_MetricsEntry_DoNotUseDefaultTypeInternal _DailyMetrics_MetricsEntry_DoNotUse_default_instance_;
constexpr DailyMetrics::DailyMetrics(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : metrics_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct DailyMetricsDefaultTypeInternal {
  constexpr DailyMetricsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DailyMetricsDefaultTypeInternal() {}
  union {
    DailyMetrics _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DailyMetricsDefaultTypeInternal _DailyMetrics_default_instance_;
constexpr Metrics_DailyMetricsEntry_DoNotUse::Metrics_DailyMetricsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct Metrics_DailyMetricsEntry_DoNotUseDefaultTypeInternal {
  constexpr Metrics_DailyMetricsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~Metrics_DailyMetricsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    Metrics_DailyMetricsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT Metrics_DailyMetricsEntry_DoNotUseDefaultTypeInternal _Metrics_DailyMetricsEntry_DoNotUse_default_instance_;
constexpr Metrics::Metrics(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : daily_metrics_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct MetricsDefaultTypeInternal {
  constexpr MetricsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MetricsDefaultTypeInternal() {}
  union {
    Metrics _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MetricsDefaultTypeInternal _Metrics_default_instance_;
constexpr HealthLog_MetricTotalsEntry_DoNotUse::HealthLog_MetricTotalsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct HealthLog_MetricTotalsEntry_DoNotUseDefaultTypeInternal {
  constexpr HealthLog_MetricTotalsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HealthLog_MetricTotalsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    HealthLog_MetricTotalsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HealthLog_MetricTotalsEntry_DoNotUseDefaultTypeInternal _HealthLog_MetricTotalsEntry_DoNotUse_default_instance_;
constexpr HealthLog_HostSerialsEntry_DoNotUse::HealthLog_HostSerialsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct HealthLog_HostSerialsEntry_DoNotUseDefaultTypeInternal {
  constexpr HealthLog_HostSerialsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HealthLog_HostSerialsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    HealthLog_HostSerialsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HealthLog_HostSerialsEntry_DoNotUseDefaultTypeInternal _HealthLog_HostSerialsEntry_DoNotUse_default_instance_;
constexpr HealthLog_FeatureFlagsEntry_DoNotUse::HealthLog_FeatureFlagsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct HealthLog_FeatureFlagsEntry_DoNotUseDefaultTypeInternal {
  constexpr HealthLog_FeatureFlagsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HealthLog_FeatureFlagsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    HealthLog_FeatureFlagsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HealthLog_FeatureFlagsEntry_DoNotUseDefaultTypeInternal _HealthLog_FeatureFlagsEntry_DoNotUse_default_instance_;
constexpr HealthLog::HealthLog(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : alarms_()
  , models_()
  , systems_()
  , metric_totals_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , alarm_list_()
  , host_serials_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , feature_flags_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , model_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , robot_serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , p2p_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , software_version_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , target_version_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , status_message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , location_(nullptr)
  , performance_(nullptr)
  , field_config_(nullptr)
  , metrics_(nullptr)
  , laser_state_(nullptr)
  , laser_change_times_(nullptr)
  , translated_status_message_(nullptr)
  , reported_at_(int64_t{0})
  , status_changed_at_(int64_t{0})
  , status_(0)

  , target_version_ready_(false)
  , robot_runtime_240v_(0u){}
struct HealthLogDefaultTypeInternal {
  constexpr HealthLogDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HealthLogDefaultTypeInternal() {}
  union {
    HealthLog _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HealthLogDefaultTypeInternal _HealthLog_default_instance_;
constexpr IssueReport::IssueReport(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , phone_number_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , robot_serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , software_version_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , reported_at_(int64_t{0}){}
struct IssueReportDefaultTypeInternal {
  constexpr IssueReportDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~IssueReportDefaultTypeInternal() {}
  union {
    IssueReport _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT IssueReportDefaultTypeInternal _IssueReport_default_instance_;
}  // namespace health
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2fhealth_2eproto[15];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_portal_2fproto_2fhealth_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2fhealth_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2fhealth_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::AlarmRow, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::AlarmRow, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::AlarmRow, alarm_code_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::AlarmRow, description_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::AlarmRow, level_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::AlarmRow, identifier_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::AlarmRow, acknowledged_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::AlarmRow, impact_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Location, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Location, x_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Location, y_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Location, z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, banding_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, banding_dynamic_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_band_config_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_thinning_config_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_job_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_almanac_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_discriminator_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, is_weeding_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, is_thinning_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_band_config_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_thinning_config_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_job_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_almanac_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_discriminator_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_modelinator_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_velocity_estimator_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_velocity_estimator_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::FieldConfig, active_category_collection_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Versions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Versions, current_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Versions, latest_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::WeedingPerformance, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::WeedingPerformance, area_weeded_total_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::WeedingPerformance, area_weeded_today_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::WeedingPerformance, time_weeded_today_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Performance, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Performance, weeding_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::DailyMetrics, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::DailyMetrics, metrics_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Metrics, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::Metrics, daily_metrics_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse, value_),
  0,
  1,
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, alarms_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, location_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, model_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, models_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, performance_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, reported_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, robot_serial_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, systems_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, status_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, status_changed_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, p2p_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, software_version_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, target_version_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, target_version_ready_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, status_message_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, metric_totals_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, alarm_list_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, field_config_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, metrics_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, robot_runtime_240v_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, laser_state_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, laser_change_times_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, host_serials_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, feature_flags_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::HealthLog, translated_status_message_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::IssueReport, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::IssueReport, description_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::IssueReport, phone_number_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::IssueReport, robot_serial_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::IssueReport, reported_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::IssueReport, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::IssueReport, model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::IssueReport, software_version_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::health::IssueReport, crop_id_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::health::AlarmRow)},
  { 13, -1, -1, sizeof(::carbon::portal::health::Location)},
  { 22, -1, -1, sizeof(::carbon::portal::health::FieldConfig)},
  { 46, -1, -1, sizeof(::carbon::portal::health::Versions)},
  { 54, -1, -1, sizeof(::carbon::portal::health::WeedingPerformance)},
  { 63, -1, -1, sizeof(::carbon::portal::health::Performance)},
  { 70, 78, -1, sizeof(::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse)},
  { 80, -1, -1, sizeof(::carbon::portal::health::DailyMetrics)},
  { 87, 95, -1, sizeof(::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse)},
  { 97, -1, -1, sizeof(::carbon::portal::health::Metrics)},
  { 104, 112, -1, sizeof(::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse)},
  { 114, 122, -1, sizeof(::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse)},
  { 124, 132, -1, sizeof(::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse)},
  { 134, -1, -1, sizeof(::carbon::portal::health::HealthLog)},
  { 167, -1, -1, sizeof(::carbon::portal::health::IssueReport)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_AlarmRow_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_Location_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_FieldConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_Versions_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_WeedingPerformance_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_Performance_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_DailyMetrics_MetricsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_DailyMetrics_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_Metrics_DailyMetricsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_Metrics_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_HealthLog_MetricTotalsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_HealthLog_HostSerialsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_HealthLog_FeatureFlagsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_HealthLog_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::health::_IssueReport_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2fhealth_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031portal/proto/health.proto\022\024carbon.port"
  "al.health\032\034google/protobuf/struct.proto\032"
  "\027portal/proto/util.proto\032\032frontend/proto"
  "/alarm.proto\032\032frontend/proto/laser.proto"
  "\032\037frontend/proto/status_bar.proto\032\033proto"
  "/metrics/metrics.proto\"\333\001\n\010AlarmRow\022\024\n\014t"
  "imestamp_ms\030\001 \001(\003\022\022\n\nalarm_code\030\002 \001(\t\022\023\n"
  "\013description\030\003 \001(\t\022/\n\005level\030\004 \001(\0162 .carb"
  "on.portal.health.AlarmLevel\022\022\n\nidentifie"
  "r\030\005 \001(\t\022\024\n\014acknowledged\030\006 \001(\010\0221\n\006impact\030"
  "\007 \001(\0162!.carbon.portal.health.AlarmImpact"
  ":\002\030\001\"+\n\010Location\022\t\n\001x\030\001 \001(\001\022\t\n\001y\030\002 \001(\001\022\t"
  "\n\001z\030\003 \001(\001\"\255\004\n\013FieldConfig\022\027\n\017banding_ena"
  "bled\030\001 \001(\010\022\027\n\017banding_dynamic\030\002 \001(\010\022\032\n\022a"
  "ctive_band_config\030\003 \001(\t\022!\n\031active_thinni"
  "ng_config_id\030\004 \001(\t\022\025\n\ractive_job_id\030\005 \001("
  "\t\022\031\n\021active_almanac_id\030\006 \001(\t\022\037\n\027active_d"
  "iscriminator_id\030\007 \001(\t\022\022\n\nis_weeding\030\010 \001("
  "\010\022\023\n\013is_thinning\030\t \001(\010\022\037\n\027active_band_co"
  "nfig_name\030\n \001(\t\022#\n\033active_thinning_confi"
  "g_name\030\013 \001(\t\022\027\n\017active_job_name\030\014 \001(\t\022\033\n"
  "\023active_almanac_name\030\r \001(\t\022!\n\031active_dis"
  "criminator_name\030\016 \001(\t\022\035\n\025active_modelina"
  "tor_id\030\017 \001(\t\022$\n\034active_velocity_estimato"
  "r_id\030\020 \001(\t\022&\n\036active_velocity_estimator_"
  "name\030\021 \001(\t\022%\n\035active_category_collection"
  "_id\030\022 \001(\t\"+\n\010Versions\022\017\n\007current\030\001 \001(\t\022\016"
  "\n\006latest\030\002 \001(\t\"e\n\022WeedingPerformance\022\031\n\021"
  "area_weeded_total\030\001 \001(\001\022\031\n\021area_weeded_t"
  "oday\030\002 \001(\001\022\031\n\021time_weeded_today\030\003 \001(\003\"H\n"
  "\013Performance\0229\n\007weeding\030\001 \001(\0132(.carbon.p"
  "ortal.health.WeedingPerformance\"\200\001\n\014Dail"
  "yMetrics\022@\n\007metrics\030\001 \003(\0132/.carbon.porta"
  "l.health.DailyMetrics.MetricsEntry\032.\n\014Me"
  "tricsEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\002"
  "8\001\"\252\001\n\007Metrics\022F\n\rdaily_metrics\030\001 \003(\0132/."
  "carbon.portal.health.Metrics.DailyMetric"
  "sEntry\032W\n\021DailyMetricsEntry\022\013\n\003key\030\001 \001(\t"
  "\0221\n\005value\030\002 \001(\0132\".carbon.portal.health.D"
  "ailyMetrics:\0028\001\"\212\n\n\tHealthLog\0222\n\006alarms\030"
  "\001 \003(\0132\036.carbon.portal.health.AlarmRowB\002\030"
  "\001\0220\n\010location\030\002 \001(\0132\036.carbon.portal.heal"
  "th.Location\022\r\n\005model\030\003 \001(\t\022\016\n\006models\030\004 \003"
  "(\t\0226\n\013performance\030\005 \001(\0132!.carbon.portal."
  "health.Performance\022\023\n\013reported_at\030\006 \001(\003\022"
  "\024\n\014robot_serial\030\007 \001(\t\022(\n\007systems\030\010 \003(\0132\027"
  ".google.protobuf.Struct\0222\n\006status\030\t \001(\0162"
  "\".carbon.frontend.status_bar.Status\022\031\n\021s"
  "tatus_changed_at\030\n \001(\003\022\020\n\004crop\030\013 \001(\tB\002\030\001"
  "\022\013\n\003p2p\030\014 \001(\t\022\030\n\020software_version\030\r \001(\t\022"
  "\026\n\016target_version\030\016 \001(\t\022\034\n\024target_versio"
  "n_ready\030\017 \001(\010\022\026\n\016status_message\030\020 \001(\t\022H\n"
  "\rmetric_totals\030\021 \003(\01321.carbon.portal.hea"
  "lth.HealthLog.MetricTotalsEntry\0223\n\nalarm"
  "_list\030\022 \003(\0132\037.carbon.frontend.alarm.Alar"
  "mRow\0227\n\014field_config\030\023 \001(\0132!.carbon.port"
  "al.health.FieldConfig\022.\n\007metrics\030\024 \001(\0132\035"
  ".carbon.portal.health.Metrics\022\017\n\007crop_id"
  "\030\025 \001(\t\022\032\n\022robot_runtime_240v\030\026 \001(\r\022:\n\013la"
  "ser_state\030\027 \001(\0132%.carbon.frontend.laser."
  "LaserStateList\022<\n\022laser_change_times\030\030 \001"
  "(\0132 .carbon.metrics.LaserChangeTimes\022F\n\014"
  "host_serials\030\031 \003(\01320.carbon.portal.healt"
  "h.HealthLog.HostSerialsEntry\022H\n\rfeature_"
  "flags\030\032 \003(\01321.carbon.portal.health.Healt"
  "hLog.FeatureFlagsEntry\022V\n\031translated_sta"
  "tus_message\030\033 \001(\01323.carbon.frontend.stat"
  "us_bar.TranslatedStatusMessage\0323\n\021Metric"
  "TotalsEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\004:"
  "\0028\001\0322\n\020HostSerialsEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005"
  "value\030\002 \001(\t:\0028\001\0323\n\021FeatureFlagsEntry\022\013\n\003"
  "key\030\001 \001(\t\022\r\n\005value\030\002 \001(\010:\0028\001\"\262\001\n\013IssueRe"
  "port\022\023\n\013description\030\001 \001(\t\022\024\n\014phone_numbe"
  "r\030\002 \001(\t\022\024\n\014robot_serial\030\003 \001(\t\022\023\n\013reporte"
  "d_at\030\004 \001(\003\022\020\n\004crop\030\005 \001(\tB\002\030\001\022\020\n\010model_id"
  "\030\006 \001(\t\022\030\n\020software_version\030\007 \001(\t\022\017\n\007crop"
  "_id\030\010 \001(\t*\216\001\n\nAlarmLevel\022\025\n\rALARM_UNKNOW"
  "N\020\000\032\002\010\001\022\026\n\016ALARM_CRITICAL\020\001\032\002\010\001\022\022\n\nALARM"
  "_HIGH\020\002\032\002\010\001\022\024\n\014ALARM_MEDIUM\020\003\032\002\010\001\022\021\n\tALA"
  "RM_LOW\020\004\032\002\010\001\022\024\n\014ALARM_HIDDEN\020\005\032\002\010\001*\206\001\n\013A"
  "larmImpact\022\030\n\020IMPACT_UNDEFINED\020\000\032\002\010\001\022\027\n\017"
  "IMPACT_CRITICAL\020\001\032\002\010\001\022\026\n\016IMPACT_OFFLINE\020"
  "\002\032\002\010\001\022\027\n\017IMPACT_DEGRADED\020\003\032\002\010\001\022\023\n\013IMPACT"
  "_NONE\020\004\032\002\010\0012\245\001\n\rHealthService\022G\n\tLogHeal"
  "th\022\037.carbon.portal.health.HealthLog\032\031.ca"
  "rbon.portal.util.Empty\022K\n\013ReportIssue\022!."
  "carbon.portal.health.IssueReport\032\031.carbo"
  "n.portal.util.EmptyB\016Z\014proto/portalb\006pro"
  "to3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2fhealth_2eproto_deps[6] = {
  &::descriptor_table_frontend_2fproto_2falarm_2eproto,
  &::descriptor_table_frontend_2fproto_2flaser_2eproto,
  &::descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto,
  &::descriptor_table_google_2fprotobuf_2fstruct_2eproto,
  &::descriptor_table_portal_2fproto_2futil_2eproto,
  &::descriptor_table_proto_2fmetrics_2fmetrics_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2fhealth_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fhealth_2eproto = {
  false, false, 3523, descriptor_table_protodef_portal_2fproto_2fhealth_2eproto, "portal/proto/health.proto", 
  &descriptor_table_portal_2fproto_2fhealth_2eproto_once, descriptor_table_portal_2fproto_2fhealth_2eproto_deps, 6, 15,
  schemas, file_default_instances, TableStruct_portal_2fproto_2fhealth_2eproto::offsets,
  file_level_metadata_portal_2fproto_2fhealth_2eproto, file_level_enum_descriptors_portal_2fproto_2fhealth_2eproto, file_level_service_descriptors_portal_2fproto_2fhealth_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2fhealth_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2fhealth_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2fhealth_2eproto(&descriptor_table_portal_2fproto_2fhealth_2eproto);
namespace carbon {
namespace portal {
namespace health {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmLevel_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_portal_2fproto_2fhealth_2eproto);
  return file_level_enum_descriptors_portal_2fproto_2fhealth_2eproto[0];
}
bool AlarmLevel_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmImpact_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_portal_2fproto_2fhealth_2eproto);
  return file_level_enum_descriptors_portal_2fproto_2fhealth_2eproto[1];
}
bool AlarmImpact_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class AlarmRow::_Internal {
 public:
};

AlarmRow::AlarmRow(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.AlarmRow)
}
AlarmRow::AlarmRow(const AlarmRow& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  alarm_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    alarm_code_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_alarm_code().empty()) {
    alarm_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_alarm_code(), 
      GetArenaForAllocation());
  }
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  identifier_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_identifier().empty()) {
    identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_identifier(), 
      GetArenaForAllocation());
  }
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&impact_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(impact_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.AlarmRow)
}

inline void AlarmRow::SharedCtor() {
alarm_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  alarm_code_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
identifier_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&impact_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(impact_));
}

AlarmRow::~AlarmRow() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.AlarmRow)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AlarmRow::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  alarm_code_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  identifier_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void AlarmRow::ArenaDtor(void* object) {
  AlarmRow* _this = reinterpret_cast< AlarmRow* >(object);
  (void)_this;
}
void AlarmRow::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AlarmRow::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AlarmRow::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.AlarmRow)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  alarm_code_.ClearToEmpty();
  description_.ClearToEmpty();
  identifier_.ClearToEmpty();
  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&impact_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(impact_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AlarmRow::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string alarm_code = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_alarm_code();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.AlarmRow.alarm_code"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.AlarmRow.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.health.AlarmLevel level = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_level(static_cast<::carbon::portal::health::AlarmLevel>(val));
        } else
          goto handle_unusual;
        continue;
      // string identifier = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_identifier();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.AlarmRow.identifier"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool acknowledged = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          acknowledged_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.health.AlarmImpact impact = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_impact(static_cast<::carbon::portal::health::AlarmImpact>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AlarmRow::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.AlarmRow)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // string alarm_code = 2;
  if (!this->_internal_alarm_code().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_alarm_code().data(), static_cast<int>(this->_internal_alarm_code().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.AlarmRow.alarm_code");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_alarm_code(), target);
  }

  // string description = 3;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.AlarmRow.description");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_description(), target);
  }

  // .carbon.portal.health.AlarmLevel level = 4;
  if (this->_internal_level() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->_internal_level(), target);
  }

  // string identifier = 5;
  if (!this->_internal_identifier().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_identifier().data(), static_cast<int>(this->_internal_identifier().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.AlarmRow.identifier");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_identifier(), target);
  }

  // bool acknowledged = 6;
  if (this->_internal_acknowledged() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_acknowledged(), target);
  }

  // .carbon.portal.health.AlarmImpact impact = 7;
  if (this->_internal_impact() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      7, this->_internal_impact(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.AlarmRow)
  return target;
}

size_t AlarmRow::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.AlarmRow)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string alarm_code = 2;
  if (!this->_internal_alarm_code().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_alarm_code());
  }

  // string description = 3;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // string identifier = 5;
  if (!this->_internal_identifier().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_identifier());
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // .carbon.portal.health.AlarmLevel level = 4;
  if (this->_internal_level() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_level());
  }

  // bool acknowledged = 6;
  if (this->_internal_acknowledged() != 0) {
    total_size += 1 + 1;
  }

  // .carbon.portal.health.AlarmImpact impact = 7;
  if (this->_internal_impact() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_impact());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AlarmRow::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AlarmRow::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AlarmRow::GetClassData() const { return &_class_data_; }

void AlarmRow::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AlarmRow *>(to)->MergeFrom(
      static_cast<const AlarmRow &>(from));
}


void AlarmRow::MergeFrom(const AlarmRow& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.AlarmRow)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_alarm_code().empty()) {
    _internal_set_alarm_code(from._internal_alarm_code());
  }
  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (!from._internal_identifier().empty()) {
    _internal_set_identifier(from._internal_identifier());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_level() != 0) {
    _internal_set_level(from._internal_level());
  }
  if (from._internal_acknowledged() != 0) {
    _internal_set_acknowledged(from._internal_acknowledged());
  }
  if (from._internal_impact() != 0) {
    _internal_set_impact(from._internal_impact());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AlarmRow::CopyFrom(const AlarmRow& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.AlarmRow)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AlarmRow::IsInitialized() const {
  return true;
}

void AlarmRow::InternalSwap(AlarmRow* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &alarm_code_, lhs_arena,
      &other->alarm_code_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &identifier_, lhs_arena,
      &other->identifier_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AlarmRow, impact_)
      + sizeof(AlarmRow::impact_)
      - PROTOBUF_FIELD_OFFSET(AlarmRow, timestamp_ms_)>(
          reinterpret_cast<char*>(&timestamp_ms_),
          reinterpret_cast<char*>(&other->timestamp_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AlarmRow::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[0]);
}

// ===================================================================

class Location::_Internal {
 public:
};

Location::Location(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.Location)
}
Location::Location(const Location& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.Location)
}

inline void Location::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

Location::~Location() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.Location)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Location::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Location::ArenaDtor(void* object) {
  Location* _this = reinterpret_cast< Location* >(object);
  (void)_this;
}
void Location::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Location::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Location::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.Location)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Location::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Location::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.Location)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_x(), target);
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_y(), target);
  }

  // double z = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.Location)
  return target;
}

size_t Location::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.Location)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 8;
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 8;
  }

  // double z = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Location::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Location::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Location::GetClassData() const { return &_class_data_; }

void Location::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Location *>(to)->MergeFrom(
      static_cast<const Location &>(from));
}


void Location::MergeFrom(const Location& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.Location)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = from._internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = from._internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = from._internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    _internal_set_z(from._internal_z());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Location::CopyFrom(const Location& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.Location)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Location::IsInitialized() const {
  return true;
}

void Location::InternalSwap(Location* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Location, z_)
      + sizeof(Location::z_)
      - PROTOBUF_FIELD_OFFSET(Location, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Location::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[1]);
}

// ===================================================================

class FieldConfig::_Internal {
 public:
};

FieldConfig::FieldConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.FieldConfig)
}
FieldConfig::FieldConfig(const FieldConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  active_band_config_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_band_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_band_config().empty()) {
    active_band_config_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_band_config(), 
      GetArenaForAllocation());
  }
  active_thinning_config_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_thinning_config_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_thinning_config_id().empty()) {
    active_thinning_config_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_thinning_config_id(), 
      GetArenaForAllocation());
  }
  active_job_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_job_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_job_id().empty()) {
    active_job_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_job_id(), 
      GetArenaForAllocation());
  }
  active_almanac_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_almanac_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_almanac_id().empty()) {
    active_almanac_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_almanac_id(), 
      GetArenaForAllocation());
  }
  active_discriminator_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_discriminator_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_discriminator_id().empty()) {
    active_discriminator_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_discriminator_id(), 
      GetArenaForAllocation());
  }
  active_band_config_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_band_config_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_band_config_name().empty()) {
    active_band_config_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_band_config_name(), 
      GetArenaForAllocation());
  }
  active_thinning_config_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_thinning_config_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_thinning_config_name().empty()) {
    active_thinning_config_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_thinning_config_name(), 
      GetArenaForAllocation());
  }
  active_job_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_job_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_job_name().empty()) {
    active_job_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_job_name(), 
      GetArenaForAllocation());
  }
  active_almanac_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_almanac_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_almanac_name().empty()) {
    active_almanac_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_almanac_name(), 
      GetArenaForAllocation());
  }
  active_discriminator_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_discriminator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_discriminator_name().empty()) {
    active_discriminator_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_discriminator_name(), 
      GetArenaForAllocation());
  }
  active_modelinator_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_modelinator_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_modelinator_id().empty()) {
    active_modelinator_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_modelinator_id(), 
      GetArenaForAllocation());
  }
  active_velocity_estimator_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_velocity_estimator_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_velocity_estimator_id().empty()) {
    active_velocity_estimator_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_velocity_estimator_id(), 
      GetArenaForAllocation());
  }
  active_velocity_estimator_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_velocity_estimator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_velocity_estimator_name().empty()) {
    active_velocity_estimator_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_velocity_estimator_name(), 
      GetArenaForAllocation());
  }
  active_category_collection_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_category_collection_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_category_collection_id().empty()) {
    active_category_collection_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_category_collection_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&banding_enabled_, &from.banding_enabled_,
    static_cast<size_t>(reinterpret_cast<char*>(&is_thinning_) -
    reinterpret_cast<char*>(&banding_enabled_)) + sizeof(is_thinning_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.FieldConfig)
}

inline void FieldConfig::SharedCtor() {
active_band_config_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_band_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_thinning_config_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_thinning_config_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_job_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_job_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_almanac_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_almanac_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_discriminator_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_discriminator_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_band_config_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_band_config_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_thinning_config_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_thinning_config_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_job_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_job_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_almanac_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_almanac_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_discriminator_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_discriminator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_modelinator_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_modelinator_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_velocity_estimator_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_velocity_estimator_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_velocity_estimator_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_velocity_estimator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
active_category_collection_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_category_collection_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&banding_enabled_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&is_thinning_) -
    reinterpret_cast<char*>(&banding_enabled_)) + sizeof(is_thinning_));
}

FieldConfig::~FieldConfig() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.FieldConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FieldConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  active_band_config_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_thinning_config_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_job_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_almanac_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_discriminator_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_band_config_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_thinning_config_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_job_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_almanac_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_discriminator_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_modelinator_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_velocity_estimator_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_velocity_estimator_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  active_category_collection_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void FieldConfig::ArenaDtor(void* object) {
  FieldConfig* _this = reinterpret_cast< FieldConfig* >(object);
  (void)_this;
}
void FieldConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FieldConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FieldConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.FieldConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  active_band_config_.ClearToEmpty();
  active_thinning_config_id_.ClearToEmpty();
  active_job_id_.ClearToEmpty();
  active_almanac_id_.ClearToEmpty();
  active_discriminator_id_.ClearToEmpty();
  active_band_config_name_.ClearToEmpty();
  active_thinning_config_name_.ClearToEmpty();
  active_job_name_.ClearToEmpty();
  active_almanac_name_.ClearToEmpty();
  active_discriminator_name_.ClearToEmpty();
  active_modelinator_id_.ClearToEmpty();
  active_velocity_estimator_id_.ClearToEmpty();
  active_velocity_estimator_name_.ClearToEmpty();
  active_category_collection_id_.ClearToEmpty();
  ::memset(&banding_enabled_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_thinning_) -
      reinterpret_cast<char*>(&banding_enabled_)) + sizeof(is_thinning_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FieldConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool banding_enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          banding_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool banding_dynamic = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          banding_dynamic_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_band_config = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_active_band_config();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_band_config"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_thinning_config_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_active_thinning_config_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_thinning_config_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_job_id = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_active_job_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_job_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_almanac_id = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_active_almanac_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_almanac_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_discriminator_id = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_active_discriminator_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_discriminator_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_weeding = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          is_weeding_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_thinning = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          is_thinning_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_band_config_name = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_active_band_config_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_band_config_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_thinning_config_name = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          auto str = _internal_mutable_active_thinning_config_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_thinning_config_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_job_name = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          auto str = _internal_mutable_active_job_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_job_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_almanac_name = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          auto str = _internal_mutable_active_almanac_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_almanac_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_discriminator_name = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          auto str = _internal_mutable_active_discriminator_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_discriminator_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_modelinator_id = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          auto str = _internal_mutable_active_modelinator_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_modelinator_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_velocity_estimator_id = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 130)) {
          auto str = _internal_mutable_active_velocity_estimator_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_velocity_estimator_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_velocity_estimator_name = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          auto str = _internal_mutable_active_velocity_estimator_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_velocity_estimator_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string active_category_collection_id = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          auto str = _internal_mutable_active_category_collection_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.FieldConfig.active_category_collection_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FieldConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.FieldConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool banding_enabled = 1;
  if (this->_internal_banding_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_banding_enabled(), target);
  }

  // bool banding_dynamic = 2;
  if (this->_internal_banding_dynamic() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_banding_dynamic(), target);
  }

  // string active_band_config = 3;
  if (!this->_internal_active_band_config().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_band_config().data(), static_cast<int>(this->_internal_active_band_config().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_band_config");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_active_band_config(), target);
  }

  // string active_thinning_config_id = 4;
  if (!this->_internal_active_thinning_config_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_thinning_config_id().data(), static_cast<int>(this->_internal_active_thinning_config_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_thinning_config_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_active_thinning_config_id(), target);
  }

  // string active_job_id = 5;
  if (!this->_internal_active_job_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_job_id().data(), static_cast<int>(this->_internal_active_job_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_job_id");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_active_job_id(), target);
  }

  // string active_almanac_id = 6;
  if (!this->_internal_active_almanac_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_almanac_id().data(), static_cast<int>(this->_internal_active_almanac_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_almanac_id");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_active_almanac_id(), target);
  }

  // string active_discriminator_id = 7;
  if (!this->_internal_active_discriminator_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_discriminator_id().data(), static_cast<int>(this->_internal_active_discriminator_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_discriminator_id");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_active_discriminator_id(), target);
  }

  // bool is_weeding = 8;
  if (this->_internal_is_weeding() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_is_weeding(), target);
  }

  // bool is_thinning = 9;
  if (this->_internal_is_thinning() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(9, this->_internal_is_thinning(), target);
  }

  // string active_band_config_name = 10;
  if (!this->_internal_active_band_config_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_band_config_name().data(), static_cast<int>(this->_internal_active_band_config_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_band_config_name");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_active_band_config_name(), target);
  }

  // string active_thinning_config_name = 11;
  if (!this->_internal_active_thinning_config_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_thinning_config_name().data(), static_cast<int>(this->_internal_active_thinning_config_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_thinning_config_name");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_active_thinning_config_name(), target);
  }

  // string active_job_name = 12;
  if (!this->_internal_active_job_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_job_name().data(), static_cast<int>(this->_internal_active_job_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_job_name");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_active_job_name(), target);
  }

  // string active_almanac_name = 13;
  if (!this->_internal_active_almanac_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_almanac_name().data(), static_cast<int>(this->_internal_active_almanac_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_almanac_name");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_active_almanac_name(), target);
  }

  // string active_discriminator_name = 14;
  if (!this->_internal_active_discriminator_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_discriminator_name().data(), static_cast<int>(this->_internal_active_discriminator_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_discriminator_name");
    target = stream->WriteStringMaybeAliased(
        14, this->_internal_active_discriminator_name(), target);
  }

  // string active_modelinator_id = 15;
  if (!this->_internal_active_modelinator_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_modelinator_id().data(), static_cast<int>(this->_internal_active_modelinator_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_modelinator_id");
    target = stream->WriteStringMaybeAliased(
        15, this->_internal_active_modelinator_id(), target);
  }

  // string active_velocity_estimator_id = 16;
  if (!this->_internal_active_velocity_estimator_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_velocity_estimator_id().data(), static_cast<int>(this->_internal_active_velocity_estimator_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_velocity_estimator_id");
    target = stream->WriteStringMaybeAliased(
        16, this->_internal_active_velocity_estimator_id(), target);
  }

  // string active_velocity_estimator_name = 17;
  if (!this->_internal_active_velocity_estimator_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_velocity_estimator_name().data(), static_cast<int>(this->_internal_active_velocity_estimator_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_velocity_estimator_name");
    target = stream->WriteStringMaybeAliased(
        17, this->_internal_active_velocity_estimator_name(), target);
  }

  // string active_category_collection_id = 18;
  if (!this->_internal_active_category_collection_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_category_collection_id().data(), static_cast<int>(this->_internal_active_category_collection_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.FieldConfig.active_category_collection_id");
    target = stream->WriteStringMaybeAliased(
        18, this->_internal_active_category_collection_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.FieldConfig)
  return target;
}

size_t FieldConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.FieldConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string active_band_config = 3;
  if (!this->_internal_active_band_config().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_band_config());
  }

  // string active_thinning_config_id = 4;
  if (!this->_internal_active_thinning_config_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_thinning_config_id());
  }

  // string active_job_id = 5;
  if (!this->_internal_active_job_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_job_id());
  }

  // string active_almanac_id = 6;
  if (!this->_internal_active_almanac_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_almanac_id());
  }

  // string active_discriminator_id = 7;
  if (!this->_internal_active_discriminator_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_discriminator_id());
  }

  // string active_band_config_name = 10;
  if (!this->_internal_active_band_config_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_band_config_name());
  }

  // string active_thinning_config_name = 11;
  if (!this->_internal_active_thinning_config_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_thinning_config_name());
  }

  // string active_job_name = 12;
  if (!this->_internal_active_job_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_job_name());
  }

  // string active_almanac_name = 13;
  if (!this->_internal_active_almanac_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_almanac_name());
  }

  // string active_discriminator_name = 14;
  if (!this->_internal_active_discriminator_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_discriminator_name());
  }

  // string active_modelinator_id = 15;
  if (!this->_internal_active_modelinator_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_modelinator_id());
  }

  // string active_velocity_estimator_id = 16;
  if (!this->_internal_active_velocity_estimator_id().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_velocity_estimator_id());
  }

  // string active_velocity_estimator_name = 17;
  if (!this->_internal_active_velocity_estimator_name().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_velocity_estimator_name());
  }

  // string active_category_collection_id = 18;
  if (!this->_internal_active_category_collection_id().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_category_collection_id());
  }

  // bool banding_enabled = 1;
  if (this->_internal_banding_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool banding_dynamic = 2;
  if (this->_internal_banding_dynamic() != 0) {
    total_size += 1 + 1;
  }

  // bool is_weeding = 8;
  if (this->_internal_is_weeding() != 0) {
    total_size += 1 + 1;
  }

  // bool is_thinning = 9;
  if (this->_internal_is_thinning() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FieldConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FieldConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FieldConfig::GetClassData() const { return &_class_data_; }

void FieldConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FieldConfig *>(to)->MergeFrom(
      static_cast<const FieldConfig &>(from));
}


void FieldConfig::MergeFrom(const FieldConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.FieldConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_active_band_config().empty()) {
    _internal_set_active_band_config(from._internal_active_band_config());
  }
  if (!from._internal_active_thinning_config_id().empty()) {
    _internal_set_active_thinning_config_id(from._internal_active_thinning_config_id());
  }
  if (!from._internal_active_job_id().empty()) {
    _internal_set_active_job_id(from._internal_active_job_id());
  }
  if (!from._internal_active_almanac_id().empty()) {
    _internal_set_active_almanac_id(from._internal_active_almanac_id());
  }
  if (!from._internal_active_discriminator_id().empty()) {
    _internal_set_active_discriminator_id(from._internal_active_discriminator_id());
  }
  if (!from._internal_active_band_config_name().empty()) {
    _internal_set_active_band_config_name(from._internal_active_band_config_name());
  }
  if (!from._internal_active_thinning_config_name().empty()) {
    _internal_set_active_thinning_config_name(from._internal_active_thinning_config_name());
  }
  if (!from._internal_active_job_name().empty()) {
    _internal_set_active_job_name(from._internal_active_job_name());
  }
  if (!from._internal_active_almanac_name().empty()) {
    _internal_set_active_almanac_name(from._internal_active_almanac_name());
  }
  if (!from._internal_active_discriminator_name().empty()) {
    _internal_set_active_discriminator_name(from._internal_active_discriminator_name());
  }
  if (!from._internal_active_modelinator_id().empty()) {
    _internal_set_active_modelinator_id(from._internal_active_modelinator_id());
  }
  if (!from._internal_active_velocity_estimator_id().empty()) {
    _internal_set_active_velocity_estimator_id(from._internal_active_velocity_estimator_id());
  }
  if (!from._internal_active_velocity_estimator_name().empty()) {
    _internal_set_active_velocity_estimator_name(from._internal_active_velocity_estimator_name());
  }
  if (!from._internal_active_category_collection_id().empty()) {
    _internal_set_active_category_collection_id(from._internal_active_category_collection_id());
  }
  if (from._internal_banding_enabled() != 0) {
    _internal_set_banding_enabled(from._internal_banding_enabled());
  }
  if (from._internal_banding_dynamic() != 0) {
    _internal_set_banding_dynamic(from._internal_banding_dynamic());
  }
  if (from._internal_is_weeding() != 0) {
    _internal_set_is_weeding(from._internal_is_weeding());
  }
  if (from._internal_is_thinning() != 0) {
    _internal_set_is_thinning(from._internal_is_thinning());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FieldConfig::CopyFrom(const FieldConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.FieldConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FieldConfig::IsInitialized() const {
  return true;
}

void FieldConfig::InternalSwap(FieldConfig* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_band_config_, lhs_arena,
      &other->active_band_config_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_thinning_config_id_, lhs_arena,
      &other->active_thinning_config_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_job_id_, lhs_arena,
      &other->active_job_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_almanac_id_, lhs_arena,
      &other->active_almanac_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_discriminator_id_, lhs_arena,
      &other->active_discriminator_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_band_config_name_, lhs_arena,
      &other->active_band_config_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_thinning_config_name_, lhs_arena,
      &other->active_thinning_config_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_job_name_, lhs_arena,
      &other->active_job_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_almanac_name_, lhs_arena,
      &other->active_almanac_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_discriminator_name_, lhs_arena,
      &other->active_discriminator_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_modelinator_id_, lhs_arena,
      &other->active_modelinator_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_velocity_estimator_id_, lhs_arena,
      &other->active_velocity_estimator_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_velocity_estimator_name_, lhs_arena,
      &other->active_velocity_estimator_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_category_collection_id_, lhs_arena,
      &other->active_category_collection_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FieldConfig, is_thinning_)
      + sizeof(FieldConfig::is_thinning_)
      - PROTOBUF_FIELD_OFFSET(FieldConfig, banding_enabled_)>(
          reinterpret_cast<char*>(&banding_enabled_),
          reinterpret_cast<char*>(&other->banding_enabled_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FieldConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[2]);
}

// ===================================================================

class Versions::_Internal {
 public:
};

Versions::Versions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.Versions)
}
Versions::Versions(const Versions& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  current_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    current_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_current().empty()) {
    current_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_current(), 
      GetArenaForAllocation());
  }
  latest_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    latest_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_latest().empty()) {
    latest_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_latest(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.Versions)
}

inline void Versions::SharedCtor() {
current_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  current_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
latest_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  latest_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Versions::~Versions() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.Versions)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Versions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  current_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  latest_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void Versions::ArenaDtor(void* object) {
  Versions* _this = reinterpret_cast< Versions* >(object);
  (void)_this;
}
void Versions::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Versions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Versions::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.Versions)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  current_.ClearToEmpty();
  latest_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Versions::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string current = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_current();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.Versions.current"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string latest = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_latest();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.Versions.latest"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Versions::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.Versions)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string current = 1;
  if (!this->_internal_current().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_current().data(), static_cast<int>(this->_internal_current().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.Versions.current");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_current(), target);
  }

  // string latest = 2;
  if (!this->_internal_latest().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_latest().data(), static_cast<int>(this->_internal_latest().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.Versions.latest");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_latest(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.Versions)
  return target;
}

size_t Versions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.Versions)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string current = 1;
  if (!this->_internal_current().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_current());
  }

  // string latest = 2;
  if (!this->_internal_latest().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_latest());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Versions::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Versions::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Versions::GetClassData() const { return &_class_data_; }

void Versions::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Versions *>(to)->MergeFrom(
      static_cast<const Versions &>(from));
}


void Versions::MergeFrom(const Versions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.Versions)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_current().empty()) {
    _internal_set_current(from._internal_current());
  }
  if (!from._internal_latest().empty()) {
    _internal_set_latest(from._internal_latest());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Versions::CopyFrom(const Versions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.Versions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Versions::IsInitialized() const {
  return true;
}

void Versions::InternalSwap(Versions* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &current_, lhs_arena,
      &other->current_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &latest_, lhs_arena,
      &other->latest_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata Versions::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[3]);
}

// ===================================================================

class WeedingPerformance::_Internal {
 public:
};

WeedingPerformance::WeedingPerformance(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.WeedingPerformance)
}
WeedingPerformance::WeedingPerformance(const WeedingPerformance& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&area_weeded_total_, &from.area_weeded_total_,
    static_cast<size_t>(reinterpret_cast<char*>(&time_weeded_today_) -
    reinterpret_cast<char*>(&area_weeded_total_)) + sizeof(time_weeded_today_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.WeedingPerformance)
}

inline void WeedingPerformance::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&area_weeded_total_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&time_weeded_today_) -
    reinterpret_cast<char*>(&area_weeded_total_)) + sizeof(time_weeded_today_));
}

WeedingPerformance::~WeedingPerformance() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.WeedingPerformance)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void WeedingPerformance::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void WeedingPerformance::ArenaDtor(void* object) {
  WeedingPerformance* _this = reinterpret_cast< WeedingPerformance* >(object);
  (void)_this;
}
void WeedingPerformance::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WeedingPerformance::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void WeedingPerformance::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.WeedingPerformance)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&area_weeded_total_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&time_weeded_today_) -
      reinterpret_cast<char*>(&area_weeded_total_)) + sizeof(time_weeded_today_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WeedingPerformance::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double area_weeded_total = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          area_weeded_total_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double area_weeded_today = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          area_weeded_today_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // int64 time_weeded_today = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          time_weeded_today_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WeedingPerformance::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.WeedingPerformance)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double area_weeded_total = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_area_weeded_total = this->_internal_area_weeded_total();
  uint64_t raw_area_weeded_total;
  memcpy(&raw_area_weeded_total, &tmp_area_weeded_total, sizeof(tmp_area_weeded_total));
  if (raw_area_weeded_total != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_area_weeded_total(), target);
  }

  // double area_weeded_today = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_area_weeded_today = this->_internal_area_weeded_today();
  uint64_t raw_area_weeded_today;
  memcpy(&raw_area_weeded_today, &tmp_area_weeded_today, sizeof(tmp_area_weeded_today));
  if (raw_area_weeded_today != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_area_weeded_today(), target);
  }

  // int64 time_weeded_today = 3;
  if (this->_internal_time_weeded_today() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_time_weeded_today(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.WeedingPerformance)
  return target;
}

size_t WeedingPerformance::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.WeedingPerformance)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double area_weeded_total = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_area_weeded_total = this->_internal_area_weeded_total();
  uint64_t raw_area_weeded_total;
  memcpy(&raw_area_weeded_total, &tmp_area_weeded_total, sizeof(tmp_area_weeded_total));
  if (raw_area_weeded_total != 0) {
    total_size += 1 + 8;
  }

  // double area_weeded_today = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_area_weeded_today = this->_internal_area_weeded_today();
  uint64_t raw_area_weeded_today;
  memcpy(&raw_area_weeded_today, &tmp_area_weeded_today, sizeof(tmp_area_weeded_today));
  if (raw_area_weeded_today != 0) {
    total_size += 1 + 8;
  }

  // int64 time_weeded_today = 3;
  if (this->_internal_time_weeded_today() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_time_weeded_today());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WeedingPerformance::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    WeedingPerformance::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WeedingPerformance::GetClassData() const { return &_class_data_; }

void WeedingPerformance::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<WeedingPerformance *>(to)->MergeFrom(
      static_cast<const WeedingPerformance &>(from));
}


void WeedingPerformance::MergeFrom(const WeedingPerformance& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.WeedingPerformance)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_area_weeded_total = from._internal_area_weeded_total();
  uint64_t raw_area_weeded_total;
  memcpy(&raw_area_weeded_total, &tmp_area_weeded_total, sizeof(tmp_area_weeded_total));
  if (raw_area_weeded_total != 0) {
    _internal_set_area_weeded_total(from._internal_area_weeded_total());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_area_weeded_today = from._internal_area_weeded_today();
  uint64_t raw_area_weeded_today;
  memcpy(&raw_area_weeded_today, &tmp_area_weeded_today, sizeof(tmp_area_weeded_today));
  if (raw_area_weeded_today != 0) {
    _internal_set_area_weeded_today(from._internal_area_weeded_today());
  }
  if (from._internal_time_weeded_today() != 0) {
    _internal_set_time_weeded_today(from._internal_time_weeded_today());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WeedingPerformance::CopyFrom(const WeedingPerformance& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.WeedingPerformance)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WeedingPerformance::IsInitialized() const {
  return true;
}

void WeedingPerformance::InternalSwap(WeedingPerformance* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(WeedingPerformance, time_weeded_today_)
      + sizeof(WeedingPerformance::time_weeded_today_)
      - PROTOBUF_FIELD_OFFSET(WeedingPerformance, area_weeded_total_)>(
          reinterpret_cast<char*>(&area_weeded_total_),
          reinterpret_cast<char*>(&other->area_weeded_total_));
}

::PROTOBUF_NAMESPACE_ID::Metadata WeedingPerformance::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[4]);
}

// ===================================================================

class Performance::_Internal {
 public:
  static const ::carbon::portal::health::WeedingPerformance& weeding(const Performance* msg);
};

const ::carbon::portal::health::WeedingPerformance&
Performance::_Internal::weeding(const Performance* msg) {
  return *msg->weeding_;
}
Performance::Performance(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.Performance)
}
Performance::Performance(const Performance& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_weeding()) {
    weeding_ = new ::carbon::portal::health::WeedingPerformance(*from.weeding_);
  } else {
    weeding_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.Performance)
}

inline void Performance::SharedCtor() {
weeding_ = nullptr;
}

Performance::~Performance() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.Performance)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Performance::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete weeding_;
}

void Performance::ArenaDtor(void* object) {
  Performance* _this = reinterpret_cast< Performance* >(object);
  (void)_this;
}
void Performance::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Performance::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Performance::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.Performance)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && weeding_ != nullptr) {
    delete weeding_;
  }
  weeding_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Performance::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.portal.health.WeedingPerformance weeding = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_weeding(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Performance::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.Performance)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.portal.health.WeedingPerformance weeding = 1;
  if (this->_internal_has_weeding()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::weeding(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.Performance)
  return target;
}

size_t Performance::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.Performance)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.portal.health.WeedingPerformance weeding = 1;
  if (this->_internal_has_weeding()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *weeding_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Performance::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Performance::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Performance::GetClassData() const { return &_class_data_; }

void Performance::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Performance *>(to)->MergeFrom(
      static_cast<const Performance &>(from));
}


void Performance::MergeFrom(const Performance& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.Performance)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_weeding()) {
    _internal_mutable_weeding()->::carbon::portal::health::WeedingPerformance::MergeFrom(from._internal_weeding());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Performance::CopyFrom(const Performance& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.Performance)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Performance::IsInitialized() const {
  return true;
}

void Performance::InternalSwap(Performance* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(weeding_, other->weeding_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Performance::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[5]);
}

// ===================================================================

DailyMetrics_MetricsEntry_DoNotUse::DailyMetrics_MetricsEntry_DoNotUse() {}
DailyMetrics_MetricsEntry_DoNotUse::DailyMetrics_MetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void DailyMetrics_MetricsEntry_DoNotUse::MergeFrom(const DailyMetrics_MetricsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata DailyMetrics_MetricsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[6]);
}

// ===================================================================

class DailyMetrics::_Internal {
 public:
};

DailyMetrics::DailyMetrics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  metrics_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.DailyMetrics)
}
DailyMetrics::DailyMetrics(const DailyMetrics& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  metrics_.MergeFrom(from.metrics_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.DailyMetrics)
}

inline void DailyMetrics::SharedCtor() {
}

DailyMetrics::~DailyMetrics() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.DailyMetrics)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DailyMetrics::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DailyMetrics::ArenaDtor(void* object) {
  DailyMetrics* _this = reinterpret_cast< DailyMetrics* >(object);
  (void)_this;
  _this->metrics_. ~MapField();
}
inline void DailyMetrics::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &DailyMetrics::ArenaDtor);
  }
}
void DailyMetrics::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DailyMetrics::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.DailyMetrics)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  metrics_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DailyMetrics::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, string> metrics = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&metrics_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DailyMetrics::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.DailyMetrics)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, string> metrics = 1;
  if (!this->_internal_metrics().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.portal.health.DailyMetrics.MetricsEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.portal.health.DailyMetrics.MetricsEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_metrics().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_metrics().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_metrics().begin();
          it != this->_internal_metrics().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = DailyMetrics_MetricsEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_metrics().begin();
          it != this->_internal_metrics().end(); ++it) {
        target = DailyMetrics_MetricsEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.DailyMetrics)
  return target;
}

size_t DailyMetrics::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.DailyMetrics)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> metrics = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_metrics_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_metrics().begin();
      it != this->_internal_metrics().end(); ++it) {
    total_size += DailyMetrics_MetricsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DailyMetrics::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DailyMetrics::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DailyMetrics::GetClassData() const { return &_class_data_; }

void DailyMetrics::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DailyMetrics *>(to)->MergeFrom(
      static_cast<const DailyMetrics &>(from));
}


void DailyMetrics::MergeFrom(const DailyMetrics& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.DailyMetrics)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  metrics_.MergeFrom(from.metrics_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DailyMetrics::CopyFrom(const DailyMetrics& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.DailyMetrics)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DailyMetrics::IsInitialized() const {
  return true;
}

void DailyMetrics::InternalSwap(DailyMetrics* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  metrics_.InternalSwap(&other->metrics_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DailyMetrics::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[7]);
}

// ===================================================================

Metrics_DailyMetricsEntry_DoNotUse::Metrics_DailyMetricsEntry_DoNotUse() {}
Metrics_DailyMetricsEntry_DoNotUse::Metrics_DailyMetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void Metrics_DailyMetricsEntry_DoNotUse::MergeFrom(const Metrics_DailyMetricsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata Metrics_DailyMetricsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[8]);
}

// ===================================================================

class Metrics::_Internal {
 public:
};

Metrics::Metrics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  daily_metrics_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.Metrics)
}
Metrics::Metrics(const Metrics& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  daily_metrics_.MergeFrom(from.daily_metrics_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.Metrics)
}

inline void Metrics::SharedCtor() {
}

Metrics::~Metrics() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.Metrics)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Metrics::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Metrics::ArenaDtor(void* object) {
  Metrics* _this = reinterpret_cast< Metrics* >(object);
  (void)_this;
  _this->daily_metrics_. ~MapField();
}
inline void Metrics::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &Metrics::ArenaDtor);
  }
}
void Metrics::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Metrics::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.Metrics)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  daily_metrics_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Metrics::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, .carbon.portal.health.DailyMetrics> daily_metrics = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&daily_metrics_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Metrics::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.Metrics)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .carbon.portal.health.DailyMetrics> daily_metrics = 1;
  if (!this->_internal_daily_metrics().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.portal.health.Metrics.DailyMetricsEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_daily_metrics().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_daily_metrics().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >::const_iterator
          it = this->_internal_daily_metrics().begin();
          it != this->_internal_daily_metrics().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = Metrics_DailyMetricsEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >::const_iterator
          it = this->_internal_daily_metrics().begin();
          it != this->_internal_daily_metrics().end(); ++it) {
        target = Metrics_DailyMetricsEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.Metrics)
  return target;
}

size_t Metrics::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.Metrics)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, .carbon.portal.health.DailyMetrics> daily_metrics = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_daily_metrics_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >::const_iterator
      it = this->_internal_daily_metrics().begin();
      it != this->_internal_daily_metrics().end(); ++it) {
    total_size += Metrics_DailyMetricsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Metrics::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Metrics::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Metrics::GetClassData() const { return &_class_data_; }

void Metrics::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Metrics *>(to)->MergeFrom(
      static_cast<const Metrics &>(from));
}


void Metrics::MergeFrom(const Metrics& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.Metrics)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  daily_metrics_.MergeFrom(from.daily_metrics_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Metrics::CopyFrom(const Metrics& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.Metrics)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Metrics::IsInitialized() const {
  return true;
}

void Metrics::InternalSwap(Metrics* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  daily_metrics_.InternalSwap(&other->daily_metrics_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Metrics::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[9]);
}

// ===================================================================

HealthLog_MetricTotalsEntry_DoNotUse::HealthLog_MetricTotalsEntry_DoNotUse() {}
HealthLog_MetricTotalsEntry_DoNotUse::HealthLog_MetricTotalsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void HealthLog_MetricTotalsEntry_DoNotUse::MergeFrom(const HealthLog_MetricTotalsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata HealthLog_MetricTotalsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[10]);
}

// ===================================================================

HealthLog_HostSerialsEntry_DoNotUse::HealthLog_HostSerialsEntry_DoNotUse() {}
HealthLog_HostSerialsEntry_DoNotUse::HealthLog_HostSerialsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void HealthLog_HostSerialsEntry_DoNotUse::MergeFrom(const HealthLog_HostSerialsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata HealthLog_HostSerialsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[11]);
}

// ===================================================================

HealthLog_FeatureFlagsEntry_DoNotUse::HealthLog_FeatureFlagsEntry_DoNotUse() {}
HealthLog_FeatureFlagsEntry_DoNotUse::HealthLog_FeatureFlagsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void HealthLog_FeatureFlagsEntry_DoNotUse::MergeFrom(const HealthLog_FeatureFlagsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata HealthLog_FeatureFlagsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[12]);
}

// ===================================================================

class HealthLog::_Internal {
 public:
  static const ::carbon::portal::health::Location& location(const HealthLog* msg);
  static const ::carbon::portal::health::Performance& performance(const HealthLog* msg);
  static const ::carbon::portal::health::FieldConfig& field_config(const HealthLog* msg);
  static const ::carbon::portal::health::Metrics& metrics(const HealthLog* msg);
  static const ::carbon::frontend::laser::LaserStateList& laser_state(const HealthLog* msg);
  static const ::carbon::metrics::LaserChangeTimes& laser_change_times(const HealthLog* msg);
  static const ::carbon::frontend::status_bar::TranslatedStatusMessage& translated_status_message(const HealthLog* msg);
};

const ::carbon::portal::health::Location&
HealthLog::_Internal::location(const HealthLog* msg) {
  return *msg->location_;
}
const ::carbon::portal::health::Performance&
HealthLog::_Internal::performance(const HealthLog* msg) {
  return *msg->performance_;
}
const ::carbon::portal::health::FieldConfig&
HealthLog::_Internal::field_config(const HealthLog* msg) {
  return *msg->field_config_;
}
const ::carbon::portal::health::Metrics&
HealthLog::_Internal::metrics(const HealthLog* msg) {
  return *msg->metrics_;
}
const ::carbon::frontend::laser::LaserStateList&
HealthLog::_Internal::laser_state(const HealthLog* msg) {
  return *msg->laser_state_;
}
const ::carbon::metrics::LaserChangeTimes&
HealthLog::_Internal::laser_change_times(const HealthLog* msg) {
  return *msg->laser_change_times_;
}
const ::carbon::frontend::status_bar::TranslatedStatusMessage&
HealthLog::_Internal::translated_status_message(const HealthLog* msg) {
  return *msg->translated_status_message_;
}
void HealthLog::clear_systems() {
  systems_.Clear();
}
void HealthLog::clear_alarm_list() {
  alarm_list_.Clear();
}
void HealthLog::clear_laser_state() {
  if (GetArenaForAllocation() == nullptr && laser_state_ != nullptr) {
    delete laser_state_;
  }
  laser_state_ = nullptr;
}
void HealthLog::clear_laser_change_times() {
  if (GetArenaForAllocation() == nullptr && laser_change_times_ != nullptr) {
    delete laser_change_times_;
  }
  laser_change_times_ = nullptr;
}
void HealthLog::clear_translated_status_message() {
  if (GetArenaForAllocation() == nullptr && translated_status_message_ != nullptr) {
    delete translated_status_message_;
  }
  translated_status_message_ = nullptr;
}
HealthLog::HealthLog(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  alarms_(arena),
  models_(arena),
  systems_(arena),
  metric_totals_(arena),
  alarm_list_(arena),
  host_serials_(arena),
  feature_flags_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.HealthLog)
}
HealthLog::HealthLog(const HealthLog& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      alarms_(from.alarms_),
      models_(from.models_),
      systems_(from.systems_),
      alarm_list_(from.alarm_list_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  metric_totals_.MergeFrom(from.metric_totals_);
  host_serials_.MergeFrom(from.host_serials_);
  feature_flags_.MergeFrom(from.feature_flags_);
  model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model().empty()) {
    model_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model(), 
      GetArenaForAllocation());
  }
  robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_serial().empty()) {
    robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_serial(), 
      GetArenaForAllocation());
  }
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  p2p_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    p2p_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_p2p().empty()) {
    p2p_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_p2p(), 
      GetArenaForAllocation());
  }
  software_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    software_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_software_version().empty()) {
    software_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_software_version(), 
      GetArenaForAllocation());
  }
  target_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    target_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_target_version().empty()) {
    target_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_target_version(), 
      GetArenaForAllocation());
  }
  status_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    status_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_status_message().empty()) {
    status_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_status_message(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_location()) {
    location_ = new ::carbon::portal::health::Location(*from.location_);
  } else {
    location_ = nullptr;
  }
  if (from._internal_has_performance()) {
    performance_ = new ::carbon::portal::health::Performance(*from.performance_);
  } else {
    performance_ = nullptr;
  }
  if (from._internal_has_field_config()) {
    field_config_ = new ::carbon::portal::health::FieldConfig(*from.field_config_);
  } else {
    field_config_ = nullptr;
  }
  if (from._internal_has_metrics()) {
    metrics_ = new ::carbon::portal::health::Metrics(*from.metrics_);
  } else {
    metrics_ = nullptr;
  }
  if (from._internal_has_laser_state()) {
    laser_state_ = new ::carbon::frontend::laser::LaserStateList(*from.laser_state_);
  } else {
    laser_state_ = nullptr;
  }
  if (from._internal_has_laser_change_times()) {
    laser_change_times_ = new ::carbon::metrics::LaserChangeTimes(*from.laser_change_times_);
  } else {
    laser_change_times_ = nullptr;
  }
  if (from._internal_has_translated_status_message()) {
    translated_status_message_ = new ::carbon::frontend::status_bar::TranslatedStatusMessage(*from.translated_status_message_);
  } else {
    translated_status_message_ = nullptr;
  }
  ::memcpy(&reported_at_, &from.reported_at_,
    static_cast<size_t>(reinterpret_cast<char*>(&robot_runtime_240v_) -
    reinterpret_cast<char*>(&reported_at_)) + sizeof(robot_runtime_240v_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.HealthLog)
}

inline void HealthLog::SharedCtor() {
model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
p2p_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  p2p_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
software_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  software_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
target_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  target_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
status_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  status_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&location_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&robot_runtime_240v_) -
    reinterpret_cast<char*>(&location_)) + sizeof(robot_runtime_240v_));
}

HealthLog::~HealthLog() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.HealthLog)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HealthLog::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  model_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  robot_serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  p2p_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  software_version_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  target_version_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  status_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete location_;
  if (this != internal_default_instance()) delete performance_;
  if (this != internal_default_instance()) delete field_config_;
  if (this != internal_default_instance()) delete metrics_;
  if (this != internal_default_instance()) delete laser_state_;
  if (this != internal_default_instance()) delete laser_change_times_;
  if (this != internal_default_instance()) delete translated_status_message_;
}

void HealthLog::ArenaDtor(void* object) {
  HealthLog* _this = reinterpret_cast< HealthLog* >(object);
  (void)_this;
  _this->metric_totals_. ~MapField();
  _this->host_serials_. ~MapField();
  _this->feature_flags_. ~MapField();
}
inline void HealthLog::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &HealthLog::ArenaDtor);
  }
}
void HealthLog::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HealthLog::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.HealthLog)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  alarms_.Clear();
  models_.Clear();
  systems_.Clear();
  metric_totals_.Clear();
  alarm_list_.Clear();
  host_serials_.Clear();
  feature_flags_.Clear();
  model_.ClearToEmpty();
  robot_serial_.ClearToEmpty();
  crop_.ClearToEmpty();
  p2p_.ClearToEmpty();
  software_version_.ClearToEmpty();
  target_version_.ClearToEmpty();
  status_message_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && location_ != nullptr) {
    delete location_;
  }
  location_ = nullptr;
  if (GetArenaForAllocation() == nullptr && performance_ != nullptr) {
    delete performance_;
  }
  performance_ = nullptr;
  if (GetArenaForAllocation() == nullptr && field_config_ != nullptr) {
    delete field_config_;
  }
  field_config_ = nullptr;
  if (GetArenaForAllocation() == nullptr && metrics_ != nullptr) {
    delete metrics_;
  }
  metrics_ = nullptr;
  if (GetArenaForAllocation() == nullptr && laser_state_ != nullptr) {
    delete laser_state_;
  }
  laser_state_ = nullptr;
  if (GetArenaForAllocation() == nullptr && laser_change_times_ != nullptr) {
    delete laser_change_times_;
  }
  laser_change_times_ = nullptr;
  if (GetArenaForAllocation() == nullptr && translated_status_message_ != nullptr) {
    delete translated_status_message_;
  }
  translated_status_message_ = nullptr;
  ::memset(&reported_at_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&robot_runtime_240v_) -
      reinterpret_cast<char*>(&reported_at_)) + sizeof(robot_runtime_240v_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HealthLog::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.portal.health.AlarmRow alarms = 1 [deprecated = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_alarms(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.health.Location location = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_location(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_model();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.HealthLog.model"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string models = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_models();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.HealthLog.models"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.health.Performance performance = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_performance(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 reported_at = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          reported_at_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string robot_serial = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_robot_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.HealthLog.robot_serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .google.protobuf.Struct systems = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_systems(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.status_bar.Status status = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_status(static_cast<::carbon::frontend::status_bar::Status>(val));
        } else
          goto handle_unusual;
        continue;
      // int64 status_changed_at = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          status_changed_at_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop = 11 [deprecated = true];
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.HealthLog.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string p2p = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          auto str = _internal_mutable_p2p();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.HealthLog.p2p"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string software_version = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          auto str = _internal_mutable_software_version();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.HealthLog.software_version"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string target_version = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          auto str = _internal_mutable_target_version();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.HealthLog.target_version"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool target_version_ready = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 120)) {
          target_version_ready_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string status_message = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 130)) {
          auto str = _internal_mutable_status_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.HealthLog.status_message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, uint64> metric_totals = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(&metric_totals_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<138>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.alarm.AlarmRow alarm_list = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_alarm_list(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<146>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.health.FieldConfig field_config = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 154)) {
          ptr = ctx->ParseMessage(_internal_mutable_field_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.health.Metrics metrics = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 162)) {
          ptr = ctx->ParseMessage(_internal_mutable_metrics(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 170)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.HealthLog.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 robot_runtime_240v = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 176)) {
          robot_runtime_240v_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.laser.LaserStateList laser_state = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 186)) {
          ptr = ctx->ParseMessage(_internal_mutable_laser_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.LaserChangeTimes laser_change_times = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 194)) {
          ptr = ctx->ParseMessage(_internal_mutable_laser_change_times(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, string> host_serials = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 202)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(&host_serials_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<202>(ptr));
        } else
          goto handle_unusual;
        continue;
      // map<string, bool> feature_flags = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 210)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(&feature_flags_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<210>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 218)) {
          ptr = ctx->ParseMessage(_internal_mutable_translated_status_message(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HealthLog::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.HealthLog)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.portal.health.AlarmRow alarms = 1 [deprecated = true];
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_alarms_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_alarms(i), target, stream);
  }

  // .carbon.portal.health.Location location = 2;
  if (this->_internal_has_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::location(this), target, stream);
  }

  // string model = 3;
  if (!this->_internal_model().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model().data(), static_cast<int>(this->_internal_model().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.HealthLog.model");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_model(), target);
  }

  // repeated string models = 4;
  for (int i = 0, n = this->_internal_models_size(); i < n; i++) {
    const auto& s = this->_internal_models(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.HealthLog.models");
    target = stream->WriteString(4, s, target);
  }

  // .carbon.portal.health.Performance performance = 5;
  if (this->_internal_has_performance()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::performance(this), target, stream);
  }

  // int64 reported_at = 6;
  if (this->_internal_reported_at() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(6, this->_internal_reported_at(), target);
  }

  // string robot_serial = 7;
  if (!this->_internal_robot_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_serial().data(), static_cast<int>(this->_internal_robot_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.HealthLog.robot_serial");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_robot_serial(), target);
  }

  // repeated .google.protobuf.Struct systems = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_systems_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(8, this->_internal_systems(i), target, stream);
  }

  // .carbon.frontend.status_bar.Status status = 9;
  if (this->_internal_status() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      9, this->_internal_status(), target);
  }

  // int64 status_changed_at = 10;
  if (this->_internal_status_changed_at() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(10, this->_internal_status_changed_at(), target);
  }

  // string crop = 11 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.HealthLog.crop");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_crop(), target);
  }

  // string p2p = 12;
  if (!this->_internal_p2p().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_p2p().data(), static_cast<int>(this->_internal_p2p().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.HealthLog.p2p");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_p2p(), target);
  }

  // string software_version = 13;
  if (!this->_internal_software_version().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_software_version().data(), static_cast<int>(this->_internal_software_version().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.HealthLog.software_version");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_software_version(), target);
  }

  // string target_version = 14;
  if (!this->_internal_target_version().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_target_version().data(), static_cast<int>(this->_internal_target_version().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.HealthLog.target_version");
    target = stream->WriteStringMaybeAliased(
        14, this->_internal_target_version(), target);
  }

  // bool target_version_ready = 15;
  if (this->_internal_target_version_ready() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(15, this->_internal_target_version_ready(), target);
  }

  // string status_message = 16;
  if (!this->_internal_status_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_status_message().data(), static_cast<int>(this->_internal_status_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.HealthLog.status_message");
    target = stream->WriteStringMaybeAliased(
        16, this->_internal_status_message(), target);
  }

  // map<string, uint64> metric_totals = 17;
  if (!this->_internal_metric_totals().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.portal.health.HealthLog.MetricTotalsEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_metric_totals().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_metric_totals().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >::const_iterator
          it = this->_internal_metric_totals().begin();
          it != this->_internal_metric_totals().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = HealthLog_MetricTotalsEntry_DoNotUse::Funcs::InternalSerialize(17, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >::const_iterator
          it = this->_internal_metric_totals().begin();
          it != this->_internal_metric_totals().end(); ++it) {
        target = HealthLog_MetricTotalsEntry_DoNotUse::Funcs::InternalSerialize(17, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // repeated .carbon.frontend.alarm.AlarmRow alarm_list = 18;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_alarm_list_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(18, this->_internal_alarm_list(i), target, stream);
  }

  // .carbon.portal.health.FieldConfig field_config = 19;
  if (this->_internal_has_field_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        19, _Internal::field_config(this), target, stream);
  }

  // .carbon.portal.health.Metrics metrics = 20;
  if (this->_internal_has_metrics()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        20, _Internal::metrics(this), target, stream);
  }

  // string crop_id = 21;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.HealthLog.crop_id");
    target = stream->WriteStringMaybeAliased(
        21, this->_internal_crop_id(), target);
  }

  // uint32 robot_runtime_240v = 22;
  if (this->_internal_robot_runtime_240v() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(22, this->_internal_robot_runtime_240v(), target);
  }

  // .carbon.frontend.laser.LaserStateList laser_state = 23;
  if (this->_internal_has_laser_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        23, _Internal::laser_state(this), target, stream);
  }

  // .carbon.metrics.LaserChangeTimes laser_change_times = 24;
  if (this->_internal_has_laser_change_times()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        24, _Internal::laser_change_times(this), target, stream);
  }

  // map<string, string> host_serials = 25;
  if (!this->_internal_host_serials().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.portal.health.HealthLog.HostSerialsEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.portal.health.HealthLog.HostSerialsEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_host_serials().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_host_serials().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_host_serials().begin();
          it != this->_internal_host_serials().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = HealthLog_HostSerialsEntry_DoNotUse::Funcs::InternalSerialize(25, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_host_serials().begin();
          it != this->_internal_host_serials().end(); ++it) {
        target = HealthLog_HostSerialsEntry_DoNotUse::Funcs::InternalSerialize(25, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // map<string, bool> feature_flags = 26;
  if (!this->_internal_feature_flags().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.portal.health.HealthLog.FeatureFlagsEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_feature_flags().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_feature_flags().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::const_iterator
          it = this->_internal_feature_flags().begin();
          it != this->_internal_feature_flags().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = HealthLog_FeatureFlagsEntry_DoNotUse::Funcs::InternalSerialize(26, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::const_iterator
          it = this->_internal_feature_flags().begin();
          it != this->_internal_feature_flags().end(); ++it) {
        target = HealthLog_FeatureFlagsEntry_DoNotUse::Funcs::InternalSerialize(26, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 27;
  if (this->_internal_has_translated_status_message()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        27, _Internal::translated_status_message(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.HealthLog)
  return target;
}

size_t HealthLog::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.HealthLog)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.health.AlarmRow alarms = 1 [deprecated = true];
  total_size += 1UL * this->_internal_alarms_size();
  for (const auto& msg : this->alarms_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated string models = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(models_.size());
  for (int i = 0, n = models_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      models_.Get(i));
  }

  // repeated .google.protobuf.Struct systems = 8;
  total_size += 1UL * this->_internal_systems_size();
  for (const auto& msg : this->systems_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // map<string, uint64> metric_totals = 17;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_metric_totals_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >::const_iterator
      it = this->_internal_metric_totals().begin();
      it != this->_internal_metric_totals().end(); ++it) {
    total_size += HealthLog_MetricTotalsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // repeated .carbon.frontend.alarm.AlarmRow alarm_list = 18;
  total_size += 2UL * this->_internal_alarm_list_size();
  for (const auto& msg : this->alarm_list_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // map<string, string> host_serials = 25;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_host_serials_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_host_serials().begin();
      it != this->_internal_host_serials().end(); ++it) {
    total_size += HealthLog_HostSerialsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // map<string, bool> feature_flags = 26;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_feature_flags_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::const_iterator
      it = this->_internal_feature_flags().begin();
      it != this->_internal_feature_flags().end(); ++it) {
    total_size += HealthLog_FeatureFlagsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // string model = 3;
  if (!this->_internal_model().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model());
  }

  // string robot_serial = 7;
  if (!this->_internal_robot_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_serial());
  }

  // string crop = 11 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string p2p = 12;
  if (!this->_internal_p2p().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_p2p());
  }

  // string software_version = 13;
  if (!this->_internal_software_version().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_software_version());
  }

  // string target_version = 14;
  if (!this->_internal_target_version().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_target_version());
  }

  // string status_message = 16;
  if (!this->_internal_status_message().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_status_message());
  }

  // string crop_id = 21;
  if (!this->_internal_crop_id().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // .carbon.portal.health.Location location = 2;
  if (this->_internal_has_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *location_);
  }

  // .carbon.portal.health.Performance performance = 5;
  if (this->_internal_has_performance()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *performance_);
  }

  // .carbon.portal.health.FieldConfig field_config = 19;
  if (this->_internal_has_field_config()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *field_config_);
  }

  // .carbon.portal.health.Metrics metrics = 20;
  if (this->_internal_has_metrics()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *metrics_);
  }

  // .carbon.frontend.laser.LaserStateList laser_state = 23;
  if (this->_internal_has_laser_state()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *laser_state_);
  }

  // .carbon.metrics.LaserChangeTimes laser_change_times = 24;
  if (this->_internal_has_laser_change_times()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *laser_change_times_);
  }

  // .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 27;
  if (this->_internal_has_translated_status_message()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *translated_status_message_);
  }

  // int64 reported_at = 6;
  if (this->_internal_reported_at() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_reported_at());
  }

  // int64 status_changed_at = 10;
  if (this->_internal_status_changed_at() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_status_changed_at());
  }

  // .carbon.frontend.status_bar.Status status = 9;
  if (this->_internal_status() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status());
  }

  // bool target_version_ready = 15;
  if (this->_internal_target_version_ready() != 0) {
    total_size += 1 + 1;
  }

  // uint32 robot_runtime_240v = 22;
  if (this->_internal_robot_runtime_240v() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_robot_runtime_240v());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HealthLog::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HealthLog::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HealthLog::GetClassData() const { return &_class_data_; }

void HealthLog::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HealthLog *>(to)->MergeFrom(
      static_cast<const HealthLog &>(from));
}


void HealthLog::MergeFrom(const HealthLog& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.HealthLog)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  alarms_.MergeFrom(from.alarms_);
  models_.MergeFrom(from.models_);
  systems_.MergeFrom(from.systems_);
  metric_totals_.MergeFrom(from.metric_totals_);
  alarm_list_.MergeFrom(from.alarm_list_);
  host_serials_.MergeFrom(from.host_serials_);
  feature_flags_.MergeFrom(from.feature_flags_);
  if (!from._internal_model().empty()) {
    _internal_set_model(from._internal_model());
  }
  if (!from._internal_robot_serial().empty()) {
    _internal_set_robot_serial(from._internal_robot_serial());
  }
  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_p2p().empty()) {
    _internal_set_p2p(from._internal_p2p());
  }
  if (!from._internal_software_version().empty()) {
    _internal_set_software_version(from._internal_software_version());
  }
  if (!from._internal_target_version().empty()) {
    _internal_set_target_version(from._internal_target_version());
  }
  if (!from._internal_status_message().empty()) {
    _internal_set_status_message(from._internal_status_message());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_has_location()) {
    _internal_mutable_location()->::carbon::portal::health::Location::MergeFrom(from._internal_location());
  }
  if (from._internal_has_performance()) {
    _internal_mutable_performance()->::carbon::portal::health::Performance::MergeFrom(from._internal_performance());
  }
  if (from._internal_has_field_config()) {
    _internal_mutable_field_config()->::carbon::portal::health::FieldConfig::MergeFrom(from._internal_field_config());
  }
  if (from._internal_has_metrics()) {
    _internal_mutable_metrics()->::carbon::portal::health::Metrics::MergeFrom(from._internal_metrics());
  }
  if (from._internal_has_laser_state()) {
    _internal_mutable_laser_state()->::carbon::frontend::laser::LaserStateList::MergeFrom(from._internal_laser_state());
  }
  if (from._internal_has_laser_change_times()) {
    _internal_mutable_laser_change_times()->::carbon::metrics::LaserChangeTimes::MergeFrom(from._internal_laser_change_times());
  }
  if (from._internal_has_translated_status_message()) {
    _internal_mutable_translated_status_message()->::carbon::frontend::status_bar::TranslatedStatusMessage::MergeFrom(from._internal_translated_status_message());
  }
  if (from._internal_reported_at() != 0) {
    _internal_set_reported_at(from._internal_reported_at());
  }
  if (from._internal_status_changed_at() != 0) {
    _internal_set_status_changed_at(from._internal_status_changed_at());
  }
  if (from._internal_status() != 0) {
    _internal_set_status(from._internal_status());
  }
  if (from._internal_target_version_ready() != 0) {
    _internal_set_target_version_ready(from._internal_target_version_ready());
  }
  if (from._internal_robot_runtime_240v() != 0) {
    _internal_set_robot_runtime_240v(from._internal_robot_runtime_240v());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HealthLog::CopyFrom(const HealthLog& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.HealthLog)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HealthLog::IsInitialized() const {
  return true;
}

void HealthLog::InternalSwap(HealthLog* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  alarms_.InternalSwap(&other->alarms_);
  models_.InternalSwap(&other->models_);
  systems_.InternalSwap(&other->systems_);
  metric_totals_.InternalSwap(&other->metric_totals_);
  alarm_list_.InternalSwap(&other->alarm_list_);
  host_serials_.InternalSwap(&other->host_serials_);
  feature_flags_.InternalSwap(&other->feature_flags_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_, lhs_arena,
      &other->model_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_serial_, lhs_arena,
      &other->robot_serial_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &p2p_, lhs_arena,
      &other->p2p_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &software_version_, lhs_arena,
      &other->software_version_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &target_version_, lhs_arena,
      &other->target_version_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &status_message_, lhs_arena,
      &other->status_message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HealthLog, robot_runtime_240v_)
      + sizeof(HealthLog::robot_runtime_240v_)
      - PROTOBUF_FIELD_OFFSET(HealthLog, location_)>(
          reinterpret_cast<char*>(&location_),
          reinterpret_cast<char*>(&other->location_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HealthLog::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[13]);
}

// ===================================================================

class IssueReport::_Internal {
 public:
};

IssueReport::IssueReport(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.health.IssueReport)
}
IssueReport::IssueReport(const IssueReport& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  phone_number_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    phone_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_phone_number().empty()) {
    phone_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_phone_number(), 
      GetArenaForAllocation());
  }
  robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_serial().empty()) {
    robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_serial(), 
      GetArenaForAllocation());
  }
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  software_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    software_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_software_version().empty()) {
    software_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_software_version(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  reported_at_ = from.reported_at_;
  // @@protoc_insertion_point(copy_constructor:carbon.portal.health.IssueReport)
}

inline void IssueReport::SharedCtor() {
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
phone_number_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  phone_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
software_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  software_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
reported_at_ = int64_t{0};
}

IssueReport::~IssueReport() {
  // @@protoc_insertion_point(destructor:carbon.portal.health.IssueReport)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void IssueReport::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  phone_number_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  robot_serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  software_version_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void IssueReport::ArenaDtor(void* object) {
  IssueReport* _this = reinterpret_cast< IssueReport* >(object);
  (void)_this;
}
void IssueReport::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void IssueReport::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void IssueReport::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.health.IssueReport)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  description_.ClearToEmpty();
  phone_number_.ClearToEmpty();
  robot_serial_.ClearToEmpty();
  crop_.ClearToEmpty();
  model_id_.ClearToEmpty();
  software_version_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  reported_at_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* IssueReport::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string description = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.IssueReport.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string phone_number = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_phone_number();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.IssueReport.phone_number"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string robot_serial = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_robot_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.IssueReport.robot_serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 reported_at = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          reported_at_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop = 5 [deprecated = true];
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.IssueReport.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_id = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.IssueReport.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string software_version = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_software_version();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.IssueReport.software_version"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.health.IssueReport.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* IssueReport::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.health.IssueReport)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string description = 1;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.IssueReport.description");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_description(), target);
  }

  // string phone_number = 2;
  if (!this->_internal_phone_number().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_phone_number().data(), static_cast<int>(this->_internal_phone_number().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.IssueReport.phone_number");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_phone_number(), target);
  }

  // string robot_serial = 3;
  if (!this->_internal_robot_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_serial().data(), static_cast<int>(this->_internal_robot_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.IssueReport.robot_serial");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_robot_serial(), target);
  }

  // int64 reported_at = 4;
  if (this->_internal_reported_at() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(4, this->_internal_reported_at(), target);
  }

  // string crop = 5 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.IssueReport.crop");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_crop(), target);
  }

  // string model_id = 6;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.IssueReport.model_id");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_model_id(), target);
  }

  // string software_version = 7;
  if (!this->_internal_software_version().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_software_version().data(), static_cast<int>(this->_internal_software_version().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.IssueReport.software_version");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_software_version(), target);
  }

  // string crop_id = 8;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.health.IssueReport.crop_id");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_crop_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.health.IssueReport)
  return target;
}

size_t IssueReport::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.health.IssueReport)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string description = 1;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // string phone_number = 2;
  if (!this->_internal_phone_number().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_phone_number());
  }

  // string robot_serial = 3;
  if (!this->_internal_robot_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_serial());
  }

  // string crop = 5 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string model_id = 6;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  // string software_version = 7;
  if (!this->_internal_software_version().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_software_version());
  }

  // string crop_id = 8;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // int64 reported_at = 4;
  if (this->_internal_reported_at() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_reported_at());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData IssueReport::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    IssueReport::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*IssueReport::GetClassData() const { return &_class_data_; }

void IssueReport::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<IssueReport *>(to)->MergeFrom(
      static_cast<const IssueReport &>(from));
}


void IssueReport::MergeFrom(const IssueReport& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.health.IssueReport)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (!from._internal_phone_number().empty()) {
    _internal_set_phone_number(from._internal_phone_number());
  }
  if (!from._internal_robot_serial().empty()) {
    _internal_set_robot_serial(from._internal_robot_serial());
  }
  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  if (!from._internal_software_version().empty()) {
    _internal_set_software_version(from._internal_software_version());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_reported_at() != 0) {
    _internal_set_reported_at(from._internal_reported_at());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void IssueReport::CopyFrom(const IssueReport& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.health.IssueReport)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IssueReport::IsInitialized() const {
  return true;
}

void IssueReport::InternalSwap(IssueReport* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &phone_number_, lhs_arena,
      &other->phone_number_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_serial_, lhs_arena,
      &other->robot_serial_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &software_version_, lhs_arena,
      &other->software_version_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  swap(reported_at_, other->reported_at_);
}

::PROTOBUF_NAMESPACE_ID::Metadata IssueReport::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fhealth_2eproto_getter, &descriptor_table_portal_2fproto_2fhealth_2eproto_once,
      file_level_metadata_portal_2fproto_2fhealth_2eproto[14]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace health
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::health::AlarmRow* Arena::CreateMaybeMessage< ::carbon::portal::health::AlarmRow >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::AlarmRow >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::Location* Arena::CreateMaybeMessage< ::carbon::portal::health::Location >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::Location >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::FieldConfig* Arena::CreateMaybeMessage< ::carbon::portal::health::FieldConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::FieldConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::Versions* Arena::CreateMaybeMessage< ::carbon::portal::health::Versions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::Versions >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::WeedingPerformance* Arena::CreateMaybeMessage< ::carbon::portal::health::WeedingPerformance >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::WeedingPerformance >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::Performance* Arena::CreateMaybeMessage< ::carbon::portal::health::Performance >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::Performance >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::DailyMetrics* Arena::CreateMaybeMessage< ::carbon::portal::health::DailyMetrics >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::DailyMetrics >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::Metrics* Arena::CreateMaybeMessage< ::carbon::portal::health::Metrics >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::Metrics >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::HealthLog* Arena::CreateMaybeMessage< ::carbon::portal::health::HealthLog >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::HealthLog >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::health::IssueReport* Arena::CreateMaybeMessage< ::carbon::portal::health::IssueReport >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::health::IssueReport >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

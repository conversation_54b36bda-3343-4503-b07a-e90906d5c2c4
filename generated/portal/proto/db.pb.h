// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/db.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fdb_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fdb_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2fdb_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2fdb_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fdb_2eproto;
namespace carbon {
namespace portal {
namespace db {
class DB;
struct DBDefaultTypeInternal;
extern DBDefaultTypeInternal _DB_default_instance_;
}  // namespace db
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::db::DB* Arena::CreateMaybeMessage<::carbon::portal::db::DB>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace db {

// ===================================================================

class DB final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.db.DB) */ {
 public:
  inline DB() : DB(nullptr) {}
  ~DB() override;
  explicit constexpr DB(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DB(const DB& from);
  DB(DB&& from) noexcept
    : DB() {
    *this = ::std::move(from);
  }

  inline DB& operator=(const DB& from) {
    CopyFrom(from);
    return *this;
  }
  inline DB& operator=(DB&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DB& default_instance() {
    return *internal_default_instance();
  }
  static inline const DB* internal_default_instance() {
    return reinterpret_cast<const DB*>(
               &_DB_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DB& a, DB& b) {
    a.Swap(&b);
  }
  inline void Swap(DB* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DB* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DB* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DB>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DB& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DB& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DB* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.db.DB";
  }
  protected:
  explicit DB(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kCreatedAtFieldNumber = 2,
    kUpdatedAtFieldNumber = 3,
    kDeletedAtFieldNumber = 4,
  };
  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // int64 created_at = 2;
  void clear_created_at();
  int64_t created_at() const;
  void set_created_at(int64_t value);
  private:
  int64_t _internal_created_at() const;
  void _internal_set_created_at(int64_t value);
  public:

  // int64 updated_at = 3;
  void clear_updated_at();
  int64_t updated_at() const;
  void set_updated_at(int64_t value);
  private:
  int64_t _internal_updated_at() const;
  void _internal_set_updated_at(int64_t value);
  public:

  // int64 deleted_at = 4;
  void clear_deleted_at();
  int64_t deleted_at() const;
  void set_deleted_at(int64_t value);
  private:
  int64_t _internal_deleted_at() const;
  void _internal_set_deleted_at(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.db.DB)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int64_t id_;
  int64_t created_at_;
  int64_t updated_at_;
  int64_t deleted_at_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fdb_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DB

// int64 id = 1;
inline void DB::clear_id() {
  id_ = int64_t{0};
}
inline int64_t DB::_internal_id() const {
  return id_;
}
inline int64_t DB::id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.db.DB.id)
  return _internal_id();
}
inline void DB::_internal_set_id(int64_t value) {
  
  id_ = value;
}
inline void DB::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:carbon.portal.db.DB.id)
}

// int64 created_at = 2;
inline void DB::clear_created_at() {
  created_at_ = int64_t{0};
}
inline int64_t DB::_internal_created_at() const {
  return created_at_;
}
inline int64_t DB::created_at() const {
  // @@protoc_insertion_point(field_get:carbon.portal.db.DB.created_at)
  return _internal_created_at();
}
inline void DB::_internal_set_created_at(int64_t value) {
  
  created_at_ = value;
}
inline void DB::set_created_at(int64_t value) {
  _internal_set_created_at(value);
  // @@protoc_insertion_point(field_set:carbon.portal.db.DB.created_at)
}

// int64 updated_at = 3;
inline void DB::clear_updated_at() {
  updated_at_ = int64_t{0};
}
inline int64_t DB::_internal_updated_at() const {
  return updated_at_;
}
inline int64_t DB::updated_at() const {
  // @@protoc_insertion_point(field_get:carbon.portal.db.DB.updated_at)
  return _internal_updated_at();
}
inline void DB::_internal_set_updated_at(int64_t value) {
  
  updated_at_ = value;
}
inline void DB::set_updated_at(int64_t value) {
  _internal_set_updated_at(value);
  // @@protoc_insertion_point(field_set:carbon.portal.db.DB.updated_at)
}

// int64 deleted_at = 4;
inline void DB::clear_deleted_at() {
  deleted_at_ = int64_t{0};
}
inline int64_t DB::_internal_deleted_at() const {
  return deleted_at_;
}
inline int64_t DB::deleted_at() const {
  // @@protoc_insertion_point(field_get:carbon.portal.db.DB.deleted_at)
  return _internal_deleted_at();
}
inline void DB::_internal_set_deleted_at(int64_t value) {
  
  deleted_at_ = value;
}
inline void DB::set_deleted_at(int64_t value) {
  _internal_set_deleted_at(value);
  // @@protoc_insertion_point(field_set:carbon.portal.db.DB.deleted_at)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace db
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fdb_2eproto

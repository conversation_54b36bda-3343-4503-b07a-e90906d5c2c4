// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/health.proto

#include "portal/proto/health.pb.h"
#include "portal/proto/health.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace health {

static const char* HealthService_method_names[] = {
  "/carbon.portal.health.HealthService/LogHealth",
  "/carbon.portal.health.HealthService/ReportIssue",
};

std::unique_ptr< HealthService::Stub> HealthService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< HealthService::Stub> stub(new HealthService::Stub(channel, options));
  return stub;
}

HealthService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_LogHealth_(HealthService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReportIssue_(HealthService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status HealthService::Stub::LogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::health::HealthLog, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LogHealth_, context, request, response);
}

void HealthService::Stub::async::LogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::health::HealthLog, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LogHealth_, context, request, response, std::move(f));
}

void HealthService::Stub::async::LogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LogHealth_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* HealthService::Stub::PrepareAsyncLogHealthRaw(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::health::HealthLog, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LogHealth_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* HealthService::Stub::AsyncLogHealthRaw(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLogHealthRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HealthService::Stub::ReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::health::IssueReport, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReportIssue_, context, request, response);
}

void HealthService::Stub::async::ReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::health::IssueReport, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReportIssue_, context, request, response, std::move(f));
}

void HealthService::Stub::async::ReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReportIssue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* HealthService::Stub::PrepareAsyncReportIssueRaw(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::health::IssueReport, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReportIssue_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* HealthService::Stub::AsyncReportIssueRaw(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReportIssueRaw(context, request, cq);
  result->StartCall();
  return result;
}

HealthService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HealthService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HealthService::Service, ::carbon::portal::health::HealthLog, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HealthService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::health::HealthLog* req,
             ::carbon::portal::util::Empty* resp) {
               return service->LogHealth(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HealthService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HealthService::Service, ::carbon::portal::health::IssueReport, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HealthService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::health::IssueReport* req,
             ::carbon::portal::util::Empty* resp) {
               return service->ReportIssue(ctx, req, resp);
             }, this)));
}

HealthService::Service::~Service() {
}

::grpc::Status HealthService::Service::LogHealth(::grpc::ServerContext* context, const ::carbon::portal::health::HealthLog* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HealthService::Service::ReportIssue(::grpc::ServerContext* context, const ::carbon::portal::health::IssueReport* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace health


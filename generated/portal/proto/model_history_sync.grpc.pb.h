// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/model_history_sync.proto
#ifndef GRPC_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto__INCLUDED
#define GRPC_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto__INCLUDED

#include "portal/proto/model_history_sync.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace portal {
namespace model_history {

class ModelHistorySyncService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.portal.model_history.ModelHistorySyncService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status UploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncUploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncUploadModelEventsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncUploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncUploadModelEventsRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void UploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncUploadModelEventsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncUploadModelEventsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status UploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncUploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncUploadModelEventsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncUploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncUploadModelEventsRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void UploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void UploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncUploadModelEventsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncUploadModelEventsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_UploadModelEvents_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status UploadModelEvents(::grpc::ServerContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest* request, ::carbon::portal::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_UploadModelEvents : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UploadModelEvents() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_UploadModelEvents() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelEvents(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_history::UploadModelEventsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadModelEvents(::grpc::ServerContext* context, ::carbon::portal::model_history::UploadModelEventsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_UploadModelEvents<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_UploadModelEvents : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UploadModelEvents() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::model_history::UploadModelEventsRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest* request, ::carbon::portal::util::Empty* response) { return this->UploadModelEvents(context, request, response); }));}
    void SetMessageAllocatorFor_UploadModelEvents(
        ::grpc::MessageAllocator< ::carbon::portal::model_history::UploadModelEventsRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::model_history::UploadModelEventsRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UploadModelEvents() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelEvents(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_history::UploadModelEventsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadModelEvents(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::model_history::UploadModelEventsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_UploadModelEvents<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_UploadModelEvents : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UploadModelEvents() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_UploadModelEvents() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelEvents(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_history::UploadModelEventsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_UploadModelEvents : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UploadModelEvents() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_UploadModelEvents() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelEvents(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_history::UploadModelEventsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadModelEvents(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UploadModelEvents : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UploadModelEvents() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UploadModelEvents(context, request, response); }));
    }
    ~WithRawCallbackMethod_UploadModelEvents() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelEvents(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_history::UploadModelEventsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadModelEvents(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UploadModelEvents : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UploadModelEvents() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::model_history::UploadModelEventsRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::model_history::UploadModelEventsRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedUploadModelEvents(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UploadModelEvents() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UploadModelEvents(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_history::UploadModelEventsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUploadModelEvents(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::model_history::UploadModelEventsRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_UploadModelEvents<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_UploadModelEvents<Service > StreamedService;
};

}  // namespace model_history
}  // namespace portal
}  // namespace carbon


#endif  // GRPC_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto__INCLUDED

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/health.proto
#ifndef GRPC_portal_2fproto_2fhealth_2eproto__INCLUDED
#define GRPC_portal_2fproto_2fhealth_2eproto__INCLUDED

#include "portal/proto/health.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace portal {
namespace health {

class HealthService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.portal.health.HealthService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status LogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncLogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncLogHealthRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncLogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncLogHealthRaw(context, request, cq));
    }
    virtual ::grpc::Status ReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncReportIssueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncReportIssueRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void LogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void LogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncLogHealthRaw(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncLogHealthRaw(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncReportIssueRaw(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncReportIssueRaw(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status LogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncLogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncLogHealthRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncLogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncLogHealthRaw(context, request, cq));
    }
    ::grpc::Status ReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncReportIssueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncReportIssueRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void LogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void LogHealth(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void ReportIssue(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncLogHealthRaw(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncLogHealthRaw(::grpc::ClientContext* context, const ::carbon::portal::health::HealthLog& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncReportIssueRaw(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncReportIssueRaw(::grpc::ClientContext* context, const ::carbon::portal::health::IssueReport& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_LogHealth_;
    const ::grpc::internal::RpcMethod rpcmethod_ReportIssue_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status LogHealth(::grpc::ServerContext* context, const ::carbon::portal::health::HealthLog* request, ::carbon::portal::util::Empty* response);
    virtual ::grpc::Status ReportIssue(::grpc::ServerContext* context, const ::carbon::portal::health::IssueReport* request, ::carbon::portal::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_LogHealth : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_LogHealth() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_LogHealth() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogHealth(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::HealthLog* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLogHealth(::grpc::ServerContext* context, ::carbon::portal::health::HealthLog* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ReportIssue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ReportIssue() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_ReportIssue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportIssue(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::IssueReport* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReportIssue(::grpc::ServerContext* context, ::carbon::portal::health::IssueReport* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_LogHealth<WithAsyncMethod_ReportIssue<Service > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_LogHealth : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_LogHealth() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::health::HealthLog, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::health::HealthLog* request, ::carbon::portal::util::Empty* response) { return this->LogHealth(context, request, response); }));}
    void SetMessageAllocatorFor_LogHealth(
        ::grpc::MessageAllocator< ::carbon::portal::health::HealthLog, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::health::HealthLog, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_LogHealth() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogHealth(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::HealthLog* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LogHealth(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::health::HealthLog* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ReportIssue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ReportIssue() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::health::IssueReport, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::health::IssueReport* request, ::carbon::portal::util::Empty* response) { return this->ReportIssue(context, request, response); }));}
    void SetMessageAllocatorFor_ReportIssue(
        ::grpc::MessageAllocator< ::carbon::portal::health::IssueReport, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::health::IssueReport, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ReportIssue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportIssue(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::IssueReport* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ReportIssue(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::health::IssueReport* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_LogHealth<WithCallbackMethod_ReportIssue<Service > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_LogHealth : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_LogHealth() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_LogHealth() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogHealth(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::HealthLog* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ReportIssue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ReportIssue() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_ReportIssue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportIssue(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::IssueReport* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_LogHealth : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_LogHealth() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_LogHealth() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogHealth(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::HealthLog* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLogHealth(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ReportIssue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ReportIssue() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_ReportIssue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportIssue(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::IssueReport* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReportIssue(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_LogHealth : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_LogHealth() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->LogHealth(context, request, response); }));
    }
    ~WithRawCallbackMethod_LogHealth() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogHealth(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::HealthLog* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LogHealth(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ReportIssue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ReportIssue() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ReportIssue(context, request, response); }));
    }
    ~WithRawCallbackMethod_ReportIssue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportIssue(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::IssueReport* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ReportIssue(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_LogHealth : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_LogHealth() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::health::HealthLog, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::health::HealthLog, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedLogHealth(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_LogHealth() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status LogHealth(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::HealthLog* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedLogHealth(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::health::HealthLog,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ReportIssue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ReportIssue() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::health::IssueReport, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::health::IssueReport, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedReportIssue(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ReportIssue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ReportIssue(::grpc::ServerContext* /*context*/, const ::carbon::portal::health::IssueReport* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedReportIssue(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::health::IssueReport,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_LogHealth<WithStreamedUnaryMethod_ReportIssue<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_LogHealth<WithStreamedUnaryMethod_ReportIssue<Service > > StreamedService;
};

}  // namespace health
}  // namespace portal
}  // namespace carbon


#endif  // GRPC_portal_2fproto_2fhealth_2eproto__INCLUDED

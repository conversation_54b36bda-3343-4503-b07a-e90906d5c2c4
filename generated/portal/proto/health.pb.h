// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/health.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fhealth_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fhealth_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/struct.pb.h>
#include "portal/proto/util.pb.h"
#include "frontend/proto/alarm.pb.h"
#include "frontend/proto/laser.pb.h"
#include "frontend/proto/status_bar.pb.h"
#include "proto/metrics/metrics.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2fhealth_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2fhealth_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[15]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fhealth_2eproto;
namespace carbon {
namespace portal {
namespace health {
class AlarmRow;
struct AlarmRowDefaultTypeInternal;
extern AlarmRowDefaultTypeInternal _AlarmRow_default_instance_;
class DailyMetrics;
struct DailyMetricsDefaultTypeInternal;
extern DailyMetricsDefaultTypeInternal _DailyMetrics_default_instance_;
class DailyMetrics_MetricsEntry_DoNotUse;
struct DailyMetrics_MetricsEntry_DoNotUseDefaultTypeInternal;
extern DailyMetrics_MetricsEntry_DoNotUseDefaultTypeInternal _DailyMetrics_MetricsEntry_DoNotUse_default_instance_;
class FieldConfig;
struct FieldConfigDefaultTypeInternal;
extern FieldConfigDefaultTypeInternal _FieldConfig_default_instance_;
class HealthLog;
struct HealthLogDefaultTypeInternal;
extern HealthLogDefaultTypeInternal _HealthLog_default_instance_;
class HealthLog_FeatureFlagsEntry_DoNotUse;
struct HealthLog_FeatureFlagsEntry_DoNotUseDefaultTypeInternal;
extern HealthLog_FeatureFlagsEntry_DoNotUseDefaultTypeInternal _HealthLog_FeatureFlagsEntry_DoNotUse_default_instance_;
class HealthLog_HostSerialsEntry_DoNotUse;
struct HealthLog_HostSerialsEntry_DoNotUseDefaultTypeInternal;
extern HealthLog_HostSerialsEntry_DoNotUseDefaultTypeInternal _HealthLog_HostSerialsEntry_DoNotUse_default_instance_;
class HealthLog_MetricTotalsEntry_DoNotUse;
struct HealthLog_MetricTotalsEntry_DoNotUseDefaultTypeInternal;
extern HealthLog_MetricTotalsEntry_DoNotUseDefaultTypeInternal _HealthLog_MetricTotalsEntry_DoNotUse_default_instance_;
class IssueReport;
struct IssueReportDefaultTypeInternal;
extern IssueReportDefaultTypeInternal _IssueReport_default_instance_;
class Location;
struct LocationDefaultTypeInternal;
extern LocationDefaultTypeInternal _Location_default_instance_;
class Metrics;
struct MetricsDefaultTypeInternal;
extern MetricsDefaultTypeInternal _Metrics_default_instance_;
class Metrics_DailyMetricsEntry_DoNotUse;
struct Metrics_DailyMetricsEntry_DoNotUseDefaultTypeInternal;
extern Metrics_DailyMetricsEntry_DoNotUseDefaultTypeInternal _Metrics_DailyMetricsEntry_DoNotUse_default_instance_;
class Performance;
struct PerformanceDefaultTypeInternal;
extern PerformanceDefaultTypeInternal _Performance_default_instance_;
class Versions;
struct VersionsDefaultTypeInternal;
extern VersionsDefaultTypeInternal _Versions_default_instance_;
class WeedingPerformance;
struct WeedingPerformanceDefaultTypeInternal;
extern WeedingPerformanceDefaultTypeInternal _WeedingPerformance_default_instance_;
}  // namespace health
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::health::AlarmRow* Arena::CreateMaybeMessage<::carbon::portal::health::AlarmRow>(Arena*);
template<> ::carbon::portal::health::DailyMetrics* Arena::CreateMaybeMessage<::carbon::portal::health::DailyMetrics>(Arena*);
template<> ::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::portal::health::DailyMetrics_MetricsEntry_DoNotUse>(Arena*);
template<> ::carbon::portal::health::FieldConfig* Arena::CreateMaybeMessage<::carbon::portal::health::FieldConfig>(Arena*);
template<> ::carbon::portal::health::HealthLog* Arena::CreateMaybeMessage<::carbon::portal::health::HealthLog>(Arena*);
template<> ::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::portal::health::HealthLog_FeatureFlagsEntry_DoNotUse>(Arena*);
template<> ::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::portal::health::HealthLog_HostSerialsEntry_DoNotUse>(Arena*);
template<> ::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::portal::health::HealthLog_MetricTotalsEntry_DoNotUse>(Arena*);
template<> ::carbon::portal::health::IssueReport* Arena::CreateMaybeMessage<::carbon::portal::health::IssueReport>(Arena*);
template<> ::carbon::portal::health::Location* Arena::CreateMaybeMessage<::carbon::portal::health::Location>(Arena*);
template<> ::carbon::portal::health::Metrics* Arena::CreateMaybeMessage<::carbon::portal::health::Metrics>(Arena*);
template<> ::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::portal::health::Metrics_DailyMetricsEntry_DoNotUse>(Arena*);
template<> ::carbon::portal::health::Performance* Arena::CreateMaybeMessage<::carbon::portal::health::Performance>(Arena*);
template<> ::carbon::portal::health::Versions* Arena::CreateMaybeMessage<::carbon::portal::health::Versions>(Arena*);
template<> ::carbon::portal::health::WeedingPerformance* Arena::CreateMaybeMessage<::carbon::portal::health::WeedingPerformance>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace health {

enum AlarmLevel : int {
  ALARM_UNKNOWN PROTOBUF_DEPRECATED_ENUM = 0,
  ALARM_CRITICAL PROTOBUF_DEPRECATED_ENUM = 1,
  ALARM_HIGH PROTOBUF_DEPRECATED_ENUM = 2,
  ALARM_MEDIUM PROTOBUF_DEPRECATED_ENUM = 3,
  ALARM_LOW PROTOBUF_DEPRECATED_ENUM = 4,
  ALARM_HIDDEN PROTOBUF_DEPRECATED_ENUM = 5,
  AlarmLevel_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  AlarmLevel_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool AlarmLevel_IsValid(int value);
constexpr AlarmLevel AlarmLevel_MIN = ALARM_UNKNOWN;
constexpr AlarmLevel AlarmLevel_MAX = ALARM_HIDDEN;
constexpr int AlarmLevel_ARRAYSIZE = AlarmLevel_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmLevel_descriptor();
template<typename T>
inline const std::string& AlarmLevel_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AlarmLevel>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AlarmLevel_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AlarmLevel_descriptor(), enum_t_value);
}
inline bool AlarmLevel_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AlarmLevel* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AlarmLevel>(
    AlarmLevel_descriptor(), name, value);
}
enum AlarmImpact : int {
  IMPACT_UNDEFINED PROTOBUF_DEPRECATED_ENUM = 0,
  IMPACT_CRITICAL PROTOBUF_DEPRECATED_ENUM = 1,
  IMPACT_OFFLINE PROTOBUF_DEPRECATED_ENUM = 2,
  IMPACT_DEGRADED PROTOBUF_DEPRECATED_ENUM = 3,
  IMPACT_NONE PROTOBUF_DEPRECATED_ENUM = 4,
  AlarmImpact_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  AlarmImpact_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool AlarmImpact_IsValid(int value);
constexpr AlarmImpact AlarmImpact_MIN = IMPACT_UNDEFINED;
constexpr AlarmImpact AlarmImpact_MAX = IMPACT_NONE;
constexpr int AlarmImpact_ARRAYSIZE = AlarmImpact_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmImpact_descriptor();
template<typename T>
inline const std::string& AlarmImpact_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AlarmImpact>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AlarmImpact_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AlarmImpact_descriptor(), enum_t_value);
}
inline bool AlarmImpact_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AlarmImpact* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AlarmImpact>(
    AlarmImpact_descriptor(), name, value);
}
// ===================================================================

class AlarmRow final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.AlarmRow) */ {
 public:
  inline AlarmRow() : AlarmRow(nullptr) {}
  ~AlarmRow() override;
  explicit constexpr AlarmRow(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AlarmRow(const AlarmRow& from);
  AlarmRow(AlarmRow&& from) noexcept
    : AlarmRow() {
    *this = ::std::move(from);
  }

  inline AlarmRow& operator=(const AlarmRow& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlarmRow& operator=(AlarmRow&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AlarmRow& default_instance() {
    return *internal_default_instance();
  }
  static inline const AlarmRow* internal_default_instance() {
    return reinterpret_cast<const AlarmRow*>(
               &_AlarmRow_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AlarmRow& a, AlarmRow& b) {
    a.Swap(&b);
  }
  inline void Swap(AlarmRow* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AlarmRow* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AlarmRow* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AlarmRow>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AlarmRow& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AlarmRow& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlarmRow* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.AlarmRow";
  }
  protected:
  explicit AlarmRow(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlarmCodeFieldNumber = 2,
    kDescriptionFieldNumber = 3,
    kIdentifierFieldNumber = 5,
    kTimestampMsFieldNumber = 1,
    kLevelFieldNumber = 4,
    kAcknowledgedFieldNumber = 6,
    kImpactFieldNumber = 7,
  };
  // string alarm_code = 2;
  void clear_alarm_code();
  const std::string& alarm_code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_alarm_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_alarm_code();
  PROTOBUF_NODISCARD std::string* release_alarm_code();
  void set_allocated_alarm_code(std::string* alarm_code);
  private:
  const std::string& _internal_alarm_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_alarm_code(const std::string& value);
  std::string* _internal_mutable_alarm_code();
  public:

  // string description = 3;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // string identifier = 5;
  void clear_identifier();
  const std::string& identifier() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_identifier(ArgT0&& arg0, ArgT... args);
  std::string* mutable_identifier();
  PROTOBUF_NODISCARD std::string* release_identifier();
  void set_allocated_identifier(std::string* identifier);
  private:
  const std::string& _internal_identifier() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_identifier(const std::string& value);
  std::string* _internal_mutable_identifier();
  public:

  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // .carbon.portal.health.AlarmLevel level = 4;
  void clear_level();
  ::carbon::portal::health::AlarmLevel level() const;
  void set_level(::carbon::portal::health::AlarmLevel value);
  private:
  ::carbon::portal::health::AlarmLevel _internal_level() const;
  void _internal_set_level(::carbon::portal::health::AlarmLevel value);
  public:

  // bool acknowledged = 6;
  void clear_acknowledged();
  bool acknowledged() const;
  void set_acknowledged(bool value);
  private:
  bool _internal_acknowledged() const;
  void _internal_set_acknowledged(bool value);
  public:

  // .carbon.portal.health.AlarmImpact impact = 7;
  void clear_impact();
  ::carbon::portal::health::AlarmImpact impact() const;
  void set_impact(::carbon::portal::health::AlarmImpact value);
  private:
  ::carbon::portal::health::AlarmImpact _internal_impact() const;
  void _internal_set_impact(::carbon::portal::health::AlarmImpact value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.health.AlarmRow)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr alarm_code_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr identifier_;
  int64_t timestamp_ms_;
  int level_;
  bool acknowledged_;
  int impact_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// -------------------------------------------------------------------

class Location final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.Location) */ {
 public:
  inline Location() : Location(nullptr) {}
  ~Location() override;
  explicit constexpr Location(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Location(const Location& from);
  Location(Location&& from) noexcept
    : Location() {
    *this = ::std::move(from);
  }

  inline Location& operator=(const Location& from) {
    CopyFrom(from);
    return *this;
  }
  inline Location& operator=(Location&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Location& default_instance() {
    return *internal_default_instance();
  }
  static inline const Location* internal_default_instance() {
    return reinterpret_cast<const Location*>(
               &_Location_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Location& a, Location& b) {
    a.Swap(&b);
  }
  inline void Swap(Location* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Location* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Location* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Location>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Location& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Location& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Location* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.Location";
  }
  protected:
  explicit Location(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // double x = 1;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 2;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // double z = 3;
  void clear_z();
  double z() const;
  void set_z(double value);
  private:
  double _internal_z() const;
  void _internal_set_z(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.health.Location)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double x_;
  double y_;
  double z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// -------------------------------------------------------------------

class FieldConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.FieldConfig) */ {
 public:
  inline FieldConfig() : FieldConfig(nullptr) {}
  ~FieldConfig() override;
  explicit constexpr FieldConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FieldConfig(const FieldConfig& from);
  FieldConfig(FieldConfig&& from) noexcept
    : FieldConfig() {
    *this = ::std::move(from);
  }

  inline FieldConfig& operator=(const FieldConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline FieldConfig& operator=(FieldConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FieldConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const FieldConfig* internal_default_instance() {
    return reinterpret_cast<const FieldConfig*>(
               &_FieldConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(FieldConfig& a, FieldConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(FieldConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FieldConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FieldConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FieldConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FieldConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FieldConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FieldConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.FieldConfig";
  }
  protected:
  explicit FieldConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kActiveBandConfigFieldNumber = 3,
    kActiveThinningConfigIdFieldNumber = 4,
    kActiveJobIdFieldNumber = 5,
    kActiveAlmanacIdFieldNumber = 6,
    kActiveDiscriminatorIdFieldNumber = 7,
    kActiveBandConfigNameFieldNumber = 10,
    kActiveThinningConfigNameFieldNumber = 11,
    kActiveJobNameFieldNumber = 12,
    kActiveAlmanacNameFieldNumber = 13,
    kActiveDiscriminatorNameFieldNumber = 14,
    kActiveModelinatorIdFieldNumber = 15,
    kActiveVelocityEstimatorIdFieldNumber = 16,
    kActiveVelocityEstimatorNameFieldNumber = 17,
    kActiveCategoryCollectionIdFieldNumber = 18,
    kBandingEnabledFieldNumber = 1,
    kBandingDynamicFieldNumber = 2,
    kIsWeedingFieldNumber = 8,
    kIsThinningFieldNumber = 9,
  };
  // string active_band_config = 3;
  void clear_active_band_config();
  const std::string& active_band_config() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_band_config(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_band_config();
  PROTOBUF_NODISCARD std::string* release_active_band_config();
  void set_allocated_active_band_config(std::string* active_band_config);
  private:
  const std::string& _internal_active_band_config() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_band_config(const std::string& value);
  std::string* _internal_mutable_active_band_config();
  public:

  // string active_thinning_config_id = 4;
  void clear_active_thinning_config_id();
  const std::string& active_thinning_config_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_thinning_config_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_thinning_config_id();
  PROTOBUF_NODISCARD std::string* release_active_thinning_config_id();
  void set_allocated_active_thinning_config_id(std::string* active_thinning_config_id);
  private:
  const std::string& _internal_active_thinning_config_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_thinning_config_id(const std::string& value);
  std::string* _internal_mutable_active_thinning_config_id();
  public:

  // string active_job_id = 5;
  void clear_active_job_id();
  const std::string& active_job_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_job_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_job_id();
  PROTOBUF_NODISCARD std::string* release_active_job_id();
  void set_allocated_active_job_id(std::string* active_job_id);
  private:
  const std::string& _internal_active_job_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_job_id(const std::string& value);
  std::string* _internal_mutable_active_job_id();
  public:

  // string active_almanac_id = 6;
  void clear_active_almanac_id();
  const std::string& active_almanac_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_almanac_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_almanac_id();
  PROTOBUF_NODISCARD std::string* release_active_almanac_id();
  void set_allocated_active_almanac_id(std::string* active_almanac_id);
  private:
  const std::string& _internal_active_almanac_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_almanac_id(const std::string& value);
  std::string* _internal_mutable_active_almanac_id();
  public:

  // string active_discriminator_id = 7;
  void clear_active_discriminator_id();
  const std::string& active_discriminator_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_discriminator_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_discriminator_id();
  PROTOBUF_NODISCARD std::string* release_active_discriminator_id();
  void set_allocated_active_discriminator_id(std::string* active_discriminator_id);
  private:
  const std::string& _internal_active_discriminator_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_discriminator_id(const std::string& value);
  std::string* _internal_mutable_active_discriminator_id();
  public:

  // string active_band_config_name = 10;
  void clear_active_band_config_name();
  const std::string& active_band_config_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_band_config_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_band_config_name();
  PROTOBUF_NODISCARD std::string* release_active_band_config_name();
  void set_allocated_active_band_config_name(std::string* active_band_config_name);
  private:
  const std::string& _internal_active_band_config_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_band_config_name(const std::string& value);
  std::string* _internal_mutable_active_band_config_name();
  public:

  // string active_thinning_config_name = 11;
  void clear_active_thinning_config_name();
  const std::string& active_thinning_config_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_thinning_config_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_thinning_config_name();
  PROTOBUF_NODISCARD std::string* release_active_thinning_config_name();
  void set_allocated_active_thinning_config_name(std::string* active_thinning_config_name);
  private:
  const std::string& _internal_active_thinning_config_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_thinning_config_name(const std::string& value);
  std::string* _internal_mutable_active_thinning_config_name();
  public:

  // string active_job_name = 12;
  void clear_active_job_name();
  const std::string& active_job_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_job_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_job_name();
  PROTOBUF_NODISCARD std::string* release_active_job_name();
  void set_allocated_active_job_name(std::string* active_job_name);
  private:
  const std::string& _internal_active_job_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_job_name(const std::string& value);
  std::string* _internal_mutable_active_job_name();
  public:

  // string active_almanac_name = 13;
  void clear_active_almanac_name();
  const std::string& active_almanac_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_almanac_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_almanac_name();
  PROTOBUF_NODISCARD std::string* release_active_almanac_name();
  void set_allocated_active_almanac_name(std::string* active_almanac_name);
  private:
  const std::string& _internal_active_almanac_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_almanac_name(const std::string& value);
  std::string* _internal_mutable_active_almanac_name();
  public:

  // string active_discriminator_name = 14;
  void clear_active_discriminator_name();
  const std::string& active_discriminator_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_discriminator_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_discriminator_name();
  PROTOBUF_NODISCARD std::string* release_active_discriminator_name();
  void set_allocated_active_discriminator_name(std::string* active_discriminator_name);
  private:
  const std::string& _internal_active_discriminator_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_discriminator_name(const std::string& value);
  std::string* _internal_mutable_active_discriminator_name();
  public:

  // string active_modelinator_id = 15;
  void clear_active_modelinator_id();
  const std::string& active_modelinator_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_modelinator_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_modelinator_id();
  PROTOBUF_NODISCARD std::string* release_active_modelinator_id();
  void set_allocated_active_modelinator_id(std::string* active_modelinator_id);
  private:
  const std::string& _internal_active_modelinator_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_modelinator_id(const std::string& value);
  std::string* _internal_mutable_active_modelinator_id();
  public:

  // string active_velocity_estimator_id = 16;
  void clear_active_velocity_estimator_id();
  const std::string& active_velocity_estimator_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_velocity_estimator_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_velocity_estimator_id();
  PROTOBUF_NODISCARD std::string* release_active_velocity_estimator_id();
  void set_allocated_active_velocity_estimator_id(std::string* active_velocity_estimator_id);
  private:
  const std::string& _internal_active_velocity_estimator_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_velocity_estimator_id(const std::string& value);
  std::string* _internal_mutable_active_velocity_estimator_id();
  public:

  // string active_velocity_estimator_name = 17;
  void clear_active_velocity_estimator_name();
  const std::string& active_velocity_estimator_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_velocity_estimator_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_velocity_estimator_name();
  PROTOBUF_NODISCARD std::string* release_active_velocity_estimator_name();
  void set_allocated_active_velocity_estimator_name(std::string* active_velocity_estimator_name);
  private:
  const std::string& _internal_active_velocity_estimator_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_velocity_estimator_name(const std::string& value);
  std::string* _internal_mutable_active_velocity_estimator_name();
  public:

  // string active_category_collection_id = 18;
  void clear_active_category_collection_id();
  const std::string& active_category_collection_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_category_collection_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_category_collection_id();
  PROTOBUF_NODISCARD std::string* release_active_category_collection_id();
  void set_allocated_active_category_collection_id(std::string* active_category_collection_id);
  private:
  const std::string& _internal_active_category_collection_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_category_collection_id(const std::string& value);
  std::string* _internal_mutable_active_category_collection_id();
  public:

  // bool banding_enabled = 1;
  void clear_banding_enabled();
  bool banding_enabled() const;
  void set_banding_enabled(bool value);
  private:
  bool _internal_banding_enabled() const;
  void _internal_set_banding_enabled(bool value);
  public:

  // bool banding_dynamic = 2;
  void clear_banding_dynamic();
  bool banding_dynamic() const;
  void set_banding_dynamic(bool value);
  private:
  bool _internal_banding_dynamic() const;
  void _internal_set_banding_dynamic(bool value);
  public:

  // bool is_weeding = 8;
  void clear_is_weeding();
  bool is_weeding() const;
  void set_is_weeding(bool value);
  private:
  bool _internal_is_weeding() const;
  void _internal_set_is_weeding(bool value);
  public:

  // bool is_thinning = 9;
  void clear_is_thinning();
  bool is_thinning() const;
  void set_is_thinning(bool value);
  private:
  bool _internal_is_thinning() const;
  void _internal_set_is_thinning(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.health.FieldConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_band_config_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_thinning_config_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_job_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_almanac_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_discriminator_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_band_config_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_thinning_config_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_job_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_almanac_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_discriminator_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_modelinator_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_velocity_estimator_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_velocity_estimator_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_category_collection_id_;
  bool banding_enabled_;
  bool banding_dynamic_;
  bool is_weeding_;
  bool is_thinning_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// -------------------------------------------------------------------

class Versions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.Versions) */ {
 public:
  inline Versions() : Versions(nullptr) {}
  ~Versions() override;
  explicit constexpr Versions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Versions(const Versions& from);
  Versions(Versions&& from) noexcept
    : Versions() {
    *this = ::std::move(from);
  }

  inline Versions& operator=(const Versions& from) {
    CopyFrom(from);
    return *this;
  }
  inline Versions& operator=(Versions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Versions& default_instance() {
    return *internal_default_instance();
  }
  static inline const Versions* internal_default_instance() {
    return reinterpret_cast<const Versions*>(
               &_Versions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Versions& a, Versions& b) {
    a.Swap(&b);
  }
  inline void Swap(Versions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Versions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Versions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Versions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Versions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Versions& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Versions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.Versions";
  }
  protected:
  explicit Versions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCurrentFieldNumber = 1,
    kLatestFieldNumber = 2,
  };
  // string current = 1;
  void clear_current();
  const std::string& current() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_current(ArgT0&& arg0, ArgT... args);
  std::string* mutable_current();
  PROTOBUF_NODISCARD std::string* release_current();
  void set_allocated_current(std::string* current);
  private:
  const std::string& _internal_current() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_current(const std::string& value);
  std::string* _internal_mutable_current();
  public:

  // string latest = 2;
  void clear_latest();
  const std::string& latest() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_latest(ArgT0&& arg0, ArgT... args);
  std::string* mutable_latest();
  PROTOBUF_NODISCARD std::string* release_latest();
  void set_allocated_latest(std::string* latest);
  private:
  const std::string& _internal_latest() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_latest(const std::string& value);
  std::string* _internal_mutable_latest();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.health.Versions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr current_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr latest_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// -------------------------------------------------------------------

class WeedingPerformance final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.WeedingPerformance) */ {
 public:
  inline WeedingPerformance() : WeedingPerformance(nullptr) {}
  ~WeedingPerformance() override;
  explicit constexpr WeedingPerformance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WeedingPerformance(const WeedingPerformance& from);
  WeedingPerformance(WeedingPerformance&& from) noexcept
    : WeedingPerformance() {
    *this = ::std::move(from);
  }

  inline WeedingPerformance& operator=(const WeedingPerformance& from) {
    CopyFrom(from);
    return *this;
  }
  inline WeedingPerformance& operator=(WeedingPerformance&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WeedingPerformance& default_instance() {
    return *internal_default_instance();
  }
  static inline const WeedingPerformance* internal_default_instance() {
    return reinterpret_cast<const WeedingPerformance*>(
               &_WeedingPerformance_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(WeedingPerformance& a, WeedingPerformance& b) {
    a.Swap(&b);
  }
  inline void Swap(WeedingPerformance* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WeedingPerformance* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WeedingPerformance* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WeedingPerformance>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WeedingPerformance& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const WeedingPerformance& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WeedingPerformance* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.WeedingPerformance";
  }
  protected:
  explicit WeedingPerformance(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAreaWeededTotalFieldNumber = 1,
    kAreaWeededTodayFieldNumber = 2,
    kTimeWeededTodayFieldNumber = 3,
  };
  // double area_weeded_total = 1;
  void clear_area_weeded_total();
  double area_weeded_total() const;
  void set_area_weeded_total(double value);
  private:
  double _internal_area_weeded_total() const;
  void _internal_set_area_weeded_total(double value);
  public:

  // double area_weeded_today = 2;
  void clear_area_weeded_today();
  double area_weeded_today() const;
  void set_area_weeded_today(double value);
  private:
  double _internal_area_weeded_today() const;
  void _internal_set_area_weeded_today(double value);
  public:

  // int64 time_weeded_today = 3;
  void clear_time_weeded_today();
  int64_t time_weeded_today() const;
  void set_time_weeded_today(int64_t value);
  private:
  int64_t _internal_time_weeded_today() const;
  void _internal_set_time_weeded_today(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.health.WeedingPerformance)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double area_weeded_total_;
  double area_weeded_today_;
  int64_t time_weeded_today_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// -------------------------------------------------------------------

class Performance final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.Performance) */ {
 public:
  inline Performance() : Performance(nullptr) {}
  ~Performance() override;
  explicit constexpr Performance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Performance(const Performance& from);
  Performance(Performance&& from) noexcept
    : Performance() {
    *this = ::std::move(from);
  }

  inline Performance& operator=(const Performance& from) {
    CopyFrom(from);
    return *this;
  }
  inline Performance& operator=(Performance&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Performance& default_instance() {
    return *internal_default_instance();
  }
  static inline const Performance* internal_default_instance() {
    return reinterpret_cast<const Performance*>(
               &_Performance_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Performance& a, Performance& b) {
    a.Swap(&b);
  }
  inline void Swap(Performance* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Performance* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Performance* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Performance>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Performance& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Performance& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Performance* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.Performance";
  }
  protected:
  explicit Performance(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWeedingFieldNumber = 1,
  };
  // .carbon.portal.health.WeedingPerformance weeding = 1;
  bool has_weeding() const;
  private:
  bool _internal_has_weeding() const;
  public:
  void clear_weeding();
  const ::carbon::portal::health::WeedingPerformance& weeding() const;
  PROTOBUF_NODISCARD ::carbon::portal::health::WeedingPerformance* release_weeding();
  ::carbon::portal::health::WeedingPerformance* mutable_weeding();
  void set_allocated_weeding(::carbon::portal::health::WeedingPerformance* weeding);
  private:
  const ::carbon::portal::health::WeedingPerformance& _internal_weeding() const;
  ::carbon::portal::health::WeedingPerformance* _internal_mutable_weeding();
  public:
  void unsafe_arena_set_allocated_weeding(
      ::carbon::portal::health::WeedingPerformance* weeding);
  ::carbon::portal::health::WeedingPerformance* unsafe_arena_release_weeding();

  // @@protoc_insertion_point(class_scope:carbon.portal.health.Performance)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::portal::health::WeedingPerformance* weeding_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// -------------------------------------------------------------------

class DailyMetrics_MetricsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DailyMetrics_MetricsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DailyMetrics_MetricsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  DailyMetrics_MetricsEntry_DoNotUse();
  explicit constexpr DailyMetrics_MetricsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit DailyMetrics_MetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DailyMetrics_MetricsEntry_DoNotUse& other);
  static const DailyMetrics_MetricsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DailyMetrics_MetricsEntry_DoNotUse*>(&_DailyMetrics_MetricsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.portal.health.DailyMetrics.MetricsEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.portal.health.DailyMetrics.MetricsEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class DailyMetrics final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.DailyMetrics) */ {
 public:
  inline DailyMetrics() : DailyMetrics(nullptr) {}
  ~DailyMetrics() override;
  explicit constexpr DailyMetrics(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DailyMetrics(const DailyMetrics& from);
  DailyMetrics(DailyMetrics&& from) noexcept
    : DailyMetrics() {
    *this = ::std::move(from);
  }

  inline DailyMetrics& operator=(const DailyMetrics& from) {
    CopyFrom(from);
    return *this;
  }
  inline DailyMetrics& operator=(DailyMetrics&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DailyMetrics& default_instance() {
    return *internal_default_instance();
  }
  static inline const DailyMetrics* internal_default_instance() {
    return reinterpret_cast<const DailyMetrics*>(
               &_DailyMetrics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(DailyMetrics& a, DailyMetrics& b) {
    a.Swap(&b);
  }
  inline void Swap(DailyMetrics* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DailyMetrics* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DailyMetrics* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DailyMetrics>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DailyMetrics& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DailyMetrics& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DailyMetrics* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.DailyMetrics";
  }
  protected:
  explicit DailyMetrics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kMetricsFieldNumber = 1,
  };
  // map<string, string> metrics = 1;
  int metrics_size() const;
  private:
  int _internal_metrics_size() const;
  public:
  void clear_metrics();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_metrics();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_metrics();

  // @@protoc_insertion_point(class_scope:carbon.portal.health.DailyMetrics)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      DailyMetrics_MetricsEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> metrics_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// -------------------------------------------------------------------

class Metrics_DailyMetricsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Metrics_DailyMetricsEntry_DoNotUse, 
    std::string, ::carbon::portal::health::DailyMetrics,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Metrics_DailyMetricsEntry_DoNotUse, 
    std::string, ::carbon::portal::health::DailyMetrics,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  Metrics_DailyMetricsEntry_DoNotUse();
  explicit constexpr Metrics_DailyMetricsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit Metrics_DailyMetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const Metrics_DailyMetricsEntry_DoNotUse& other);
  static const Metrics_DailyMetricsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Metrics_DailyMetricsEntry_DoNotUse*>(&_Metrics_DailyMetricsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.portal.health.Metrics.DailyMetricsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class Metrics final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.Metrics) */ {
 public:
  inline Metrics() : Metrics(nullptr) {}
  ~Metrics() override;
  explicit constexpr Metrics(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Metrics(const Metrics& from);
  Metrics(Metrics&& from) noexcept
    : Metrics() {
    *this = ::std::move(from);
  }

  inline Metrics& operator=(const Metrics& from) {
    CopyFrom(from);
    return *this;
  }
  inline Metrics& operator=(Metrics&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Metrics& default_instance() {
    return *internal_default_instance();
  }
  static inline const Metrics* internal_default_instance() {
    return reinterpret_cast<const Metrics*>(
               &_Metrics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(Metrics& a, Metrics& b) {
    a.Swap(&b);
  }
  inline void Swap(Metrics* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Metrics* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Metrics* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Metrics>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Metrics& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Metrics& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Metrics* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.Metrics";
  }
  protected:
  explicit Metrics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kDailyMetricsFieldNumber = 1,
  };
  // map<string, .carbon.portal.health.DailyMetrics> daily_metrics = 1;
  int daily_metrics_size() const;
  private:
  int _internal_daily_metrics_size() const;
  public:
  void clear_daily_metrics();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >&
      _internal_daily_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >*
      _internal_mutable_daily_metrics();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >&
      daily_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >*
      mutable_daily_metrics();

  // @@protoc_insertion_point(class_scope:carbon.portal.health.Metrics)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      Metrics_DailyMetricsEntry_DoNotUse,
      std::string, ::carbon::portal::health::DailyMetrics,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> daily_metrics_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// -------------------------------------------------------------------

class HealthLog_MetricTotalsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HealthLog_MetricTotalsEntry_DoNotUse, 
    std::string, uint64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT64> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HealthLog_MetricTotalsEntry_DoNotUse, 
    std::string, uint64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT64> SuperType;
  HealthLog_MetricTotalsEntry_DoNotUse();
  explicit constexpr HealthLog_MetricTotalsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit HealthLog_MetricTotalsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const HealthLog_MetricTotalsEntry_DoNotUse& other);
  static const HealthLog_MetricTotalsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const HealthLog_MetricTotalsEntry_DoNotUse*>(&_HealthLog_MetricTotalsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.portal.health.HealthLog.MetricTotalsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class HealthLog_HostSerialsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HealthLog_HostSerialsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HealthLog_HostSerialsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  HealthLog_HostSerialsEntry_DoNotUse();
  explicit constexpr HealthLog_HostSerialsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit HealthLog_HostSerialsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const HealthLog_HostSerialsEntry_DoNotUse& other);
  static const HealthLog_HostSerialsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const HealthLog_HostSerialsEntry_DoNotUse*>(&_HealthLog_HostSerialsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.portal.health.HealthLog.HostSerialsEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.portal.health.HealthLog.HostSerialsEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class HealthLog_FeatureFlagsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HealthLog_FeatureFlagsEntry_DoNotUse, 
    std::string, bool,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<HealthLog_FeatureFlagsEntry_DoNotUse, 
    std::string, bool,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL> SuperType;
  HealthLog_FeatureFlagsEntry_DoNotUse();
  explicit constexpr HealthLog_FeatureFlagsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit HealthLog_FeatureFlagsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const HealthLog_FeatureFlagsEntry_DoNotUse& other);
  static const HealthLog_FeatureFlagsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const HealthLog_FeatureFlagsEntry_DoNotUse*>(&_HealthLog_FeatureFlagsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.portal.health.HealthLog.FeatureFlagsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class HealthLog final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.HealthLog) */ {
 public:
  inline HealthLog() : HealthLog(nullptr) {}
  ~HealthLog() override;
  explicit constexpr HealthLog(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HealthLog(const HealthLog& from);
  HealthLog(HealthLog&& from) noexcept
    : HealthLog() {
    *this = ::std::move(from);
  }

  inline HealthLog& operator=(const HealthLog& from) {
    CopyFrom(from);
    return *this;
  }
  inline HealthLog& operator=(HealthLog&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HealthLog& default_instance() {
    return *internal_default_instance();
  }
  static inline const HealthLog* internal_default_instance() {
    return reinterpret_cast<const HealthLog*>(
               &_HealthLog_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(HealthLog& a, HealthLog& b) {
    a.Swap(&b);
  }
  inline void Swap(HealthLog* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HealthLog* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HealthLog* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HealthLog>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HealthLog& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HealthLog& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HealthLog* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.HealthLog";
  }
  protected:
  explicit HealthLog(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAlarmsFieldNumber = 1,
    kModelsFieldNumber = 4,
    kSystemsFieldNumber = 8,
    kMetricTotalsFieldNumber = 17,
    kAlarmListFieldNumber = 18,
    kHostSerialsFieldNumber = 25,
    kFeatureFlagsFieldNumber = 26,
    kModelFieldNumber = 3,
    kRobotSerialFieldNumber = 7,
    kCropFieldNumber = 11,
    kP2PFieldNumber = 12,
    kSoftwareVersionFieldNumber = 13,
    kTargetVersionFieldNumber = 14,
    kStatusMessageFieldNumber = 16,
    kCropIdFieldNumber = 21,
    kLocationFieldNumber = 2,
    kPerformanceFieldNumber = 5,
    kFieldConfigFieldNumber = 19,
    kMetricsFieldNumber = 20,
    kLaserStateFieldNumber = 23,
    kLaserChangeTimesFieldNumber = 24,
    kTranslatedStatusMessageFieldNumber = 27,
    kReportedAtFieldNumber = 6,
    kStatusChangedAtFieldNumber = 10,
    kStatusFieldNumber = 9,
    kTargetVersionReadyFieldNumber = 15,
    kRobotRuntime240VFieldNumber = 22,
  };
  // repeated .carbon.portal.health.AlarmRow alarms = 1 [deprecated = true];
  PROTOBUF_DEPRECATED int alarms_size() const;
  private:
  int _internal_alarms_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_alarms();
  PROTOBUF_DEPRECATED ::carbon::portal::health::AlarmRow* mutable_alarms(int index);
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::health::AlarmRow >*
      mutable_alarms();
  private:
  const ::carbon::portal::health::AlarmRow& _internal_alarms(int index) const;
  ::carbon::portal::health::AlarmRow* _internal_add_alarms();
  public:
  PROTOBUF_DEPRECATED const ::carbon::portal::health::AlarmRow& alarms(int index) const;
  PROTOBUF_DEPRECATED ::carbon::portal::health::AlarmRow* add_alarms();
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::health::AlarmRow >&
      alarms() const;

  // repeated string models = 4;
  int models_size() const;
  private:
  int _internal_models_size() const;
  public:
  void clear_models();
  const std::string& models(int index) const;
  std::string* mutable_models(int index);
  void set_models(int index, const std::string& value);
  void set_models(int index, std::string&& value);
  void set_models(int index, const char* value);
  void set_models(int index, const char* value, size_t size);
  std::string* add_models();
  void add_models(const std::string& value);
  void add_models(std::string&& value);
  void add_models(const char* value);
  void add_models(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& models() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_models();
  private:
  const std::string& _internal_models(int index) const;
  std::string* _internal_add_models();
  public:

  // repeated .google.protobuf.Struct systems = 8;
  int systems_size() const;
  private:
  int _internal_systems_size() const;
  public:
  void clear_systems();
  ::PROTOBUF_NAMESPACE_ID::Struct* mutable_systems(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct >*
      mutable_systems();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Struct& _internal_systems(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Struct* _internal_add_systems();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Struct& systems(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Struct* add_systems();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct >&
      systems() const;

  // map<string, uint64> metric_totals = 17;
  int metric_totals_size() const;
  private:
  int _internal_metric_totals_size() const;
  public:
  void clear_metric_totals();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >&
      _internal_metric_totals() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >*
      _internal_mutable_metric_totals();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >&
      metric_totals() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >*
      mutable_metric_totals();

  // repeated .carbon.frontend.alarm.AlarmRow alarm_list = 18;
  int alarm_list_size() const;
  private:
  int _internal_alarm_list_size() const;
  public:
  void clear_alarm_list();
  ::carbon::frontend::alarm::AlarmRow* mutable_alarm_list(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
      mutable_alarm_list();
  private:
  const ::carbon::frontend::alarm::AlarmRow& _internal_alarm_list(int index) const;
  ::carbon::frontend::alarm::AlarmRow* _internal_add_alarm_list();
  public:
  const ::carbon::frontend::alarm::AlarmRow& alarm_list(int index) const;
  ::carbon::frontend::alarm::AlarmRow* add_alarm_list();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
      alarm_list() const;

  // map<string, string> host_serials = 25;
  int host_serials_size() const;
  private:
  int _internal_host_serials_size() const;
  public:
  void clear_host_serials();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_host_serials() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_host_serials();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      host_serials() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_host_serials();

  // map<string, bool> feature_flags = 26;
  int feature_flags_size() const;
  private:
  int _internal_feature_flags_size() const;
  public:
  void clear_feature_flags();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >&
      _internal_feature_flags() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >*
      _internal_mutable_feature_flags();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >&
      feature_flags() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >*
      mutable_feature_flags();

  // string model = 3;
  void clear_model();
  const std::string& model() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model();
  PROTOBUF_NODISCARD std::string* release_model();
  void set_allocated_model(std::string* model);
  private:
  const std::string& _internal_model() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model(const std::string& value);
  std::string* _internal_mutable_model();
  public:

  // string robot_serial = 7;
  void clear_robot_serial();
  const std::string& robot_serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_serial();
  PROTOBUF_NODISCARD std::string* release_robot_serial();
  void set_allocated_robot_serial(std::string* robot_serial);
  private:
  const std::string& _internal_robot_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_serial(const std::string& value);
  std::string* _internal_mutable_robot_serial();
  public:

  // string crop = 11 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_crop();
  PROTOBUF_DEPRECATED const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_crop(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_crop();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_crop();
  PROTOBUF_DEPRECATED void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string p2p = 12;
  void clear_p2p();
  const std::string& p2p() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_p2p(ArgT0&& arg0, ArgT... args);
  std::string* mutable_p2p();
  PROTOBUF_NODISCARD std::string* release_p2p();
  void set_allocated_p2p(std::string* p2p);
  private:
  const std::string& _internal_p2p() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_p2p(const std::string& value);
  std::string* _internal_mutable_p2p();
  public:

  // string software_version = 13;
  void clear_software_version();
  const std::string& software_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_software_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_software_version();
  PROTOBUF_NODISCARD std::string* release_software_version();
  void set_allocated_software_version(std::string* software_version);
  private:
  const std::string& _internal_software_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_software_version(const std::string& value);
  std::string* _internal_mutable_software_version();
  public:

  // string target_version = 14;
  void clear_target_version();
  const std::string& target_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_target_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_target_version();
  PROTOBUF_NODISCARD std::string* release_target_version();
  void set_allocated_target_version(std::string* target_version);
  private:
  const std::string& _internal_target_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_target_version(const std::string& value);
  std::string* _internal_mutable_target_version();
  public:

  // string status_message = 16;
  void clear_status_message();
  const std::string& status_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status_message();
  PROTOBUF_NODISCARD std::string* release_status_message();
  void set_allocated_status_message(std::string* status_message);
  private:
  const std::string& _internal_status_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status_message(const std::string& value);
  std::string* _internal_mutable_status_message();
  public:

  // string crop_id = 21;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // .carbon.portal.health.Location location = 2;
  bool has_location() const;
  private:
  bool _internal_has_location() const;
  public:
  void clear_location();
  const ::carbon::portal::health::Location& location() const;
  PROTOBUF_NODISCARD ::carbon::portal::health::Location* release_location();
  ::carbon::portal::health::Location* mutable_location();
  void set_allocated_location(::carbon::portal::health::Location* location);
  private:
  const ::carbon::portal::health::Location& _internal_location() const;
  ::carbon::portal::health::Location* _internal_mutable_location();
  public:
  void unsafe_arena_set_allocated_location(
      ::carbon::portal::health::Location* location);
  ::carbon::portal::health::Location* unsafe_arena_release_location();

  // .carbon.portal.health.Performance performance = 5;
  bool has_performance() const;
  private:
  bool _internal_has_performance() const;
  public:
  void clear_performance();
  const ::carbon::portal::health::Performance& performance() const;
  PROTOBUF_NODISCARD ::carbon::portal::health::Performance* release_performance();
  ::carbon::portal::health::Performance* mutable_performance();
  void set_allocated_performance(::carbon::portal::health::Performance* performance);
  private:
  const ::carbon::portal::health::Performance& _internal_performance() const;
  ::carbon::portal::health::Performance* _internal_mutable_performance();
  public:
  void unsafe_arena_set_allocated_performance(
      ::carbon::portal::health::Performance* performance);
  ::carbon::portal::health::Performance* unsafe_arena_release_performance();

  // .carbon.portal.health.FieldConfig field_config = 19;
  bool has_field_config() const;
  private:
  bool _internal_has_field_config() const;
  public:
  void clear_field_config();
  const ::carbon::portal::health::FieldConfig& field_config() const;
  PROTOBUF_NODISCARD ::carbon::portal::health::FieldConfig* release_field_config();
  ::carbon::portal::health::FieldConfig* mutable_field_config();
  void set_allocated_field_config(::carbon::portal::health::FieldConfig* field_config);
  private:
  const ::carbon::portal::health::FieldConfig& _internal_field_config() const;
  ::carbon::portal::health::FieldConfig* _internal_mutable_field_config();
  public:
  void unsafe_arena_set_allocated_field_config(
      ::carbon::portal::health::FieldConfig* field_config);
  ::carbon::portal::health::FieldConfig* unsafe_arena_release_field_config();

  // .carbon.portal.health.Metrics metrics = 20;
  bool has_metrics() const;
  private:
  bool _internal_has_metrics() const;
  public:
  void clear_metrics();
  const ::carbon::portal::health::Metrics& metrics() const;
  PROTOBUF_NODISCARD ::carbon::portal::health::Metrics* release_metrics();
  ::carbon::portal::health::Metrics* mutable_metrics();
  void set_allocated_metrics(::carbon::portal::health::Metrics* metrics);
  private:
  const ::carbon::portal::health::Metrics& _internal_metrics() const;
  ::carbon::portal::health::Metrics* _internal_mutable_metrics();
  public:
  void unsafe_arena_set_allocated_metrics(
      ::carbon::portal::health::Metrics* metrics);
  ::carbon::portal::health::Metrics* unsafe_arena_release_metrics();

  // .carbon.frontend.laser.LaserStateList laser_state = 23;
  bool has_laser_state() const;
  private:
  bool _internal_has_laser_state() const;
  public:
  void clear_laser_state();
  const ::carbon::frontend::laser::LaserStateList& laser_state() const;
  PROTOBUF_NODISCARD ::carbon::frontend::laser::LaserStateList* release_laser_state();
  ::carbon::frontend::laser::LaserStateList* mutable_laser_state();
  void set_allocated_laser_state(::carbon::frontend::laser::LaserStateList* laser_state);
  private:
  const ::carbon::frontend::laser::LaserStateList& _internal_laser_state() const;
  ::carbon::frontend::laser::LaserStateList* _internal_mutable_laser_state();
  public:
  void unsafe_arena_set_allocated_laser_state(
      ::carbon::frontend::laser::LaserStateList* laser_state);
  ::carbon::frontend::laser::LaserStateList* unsafe_arena_release_laser_state();

  // .carbon.metrics.LaserChangeTimes laser_change_times = 24;
  bool has_laser_change_times() const;
  private:
  bool _internal_has_laser_change_times() const;
  public:
  void clear_laser_change_times();
  const ::carbon::metrics::LaserChangeTimes& laser_change_times() const;
  PROTOBUF_NODISCARD ::carbon::metrics::LaserChangeTimes* release_laser_change_times();
  ::carbon::metrics::LaserChangeTimes* mutable_laser_change_times();
  void set_allocated_laser_change_times(::carbon::metrics::LaserChangeTimes* laser_change_times);
  private:
  const ::carbon::metrics::LaserChangeTimes& _internal_laser_change_times() const;
  ::carbon::metrics::LaserChangeTimes* _internal_mutable_laser_change_times();
  public:
  void unsafe_arena_set_allocated_laser_change_times(
      ::carbon::metrics::LaserChangeTimes* laser_change_times);
  ::carbon::metrics::LaserChangeTimes* unsafe_arena_release_laser_change_times();

  // .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 27;
  bool has_translated_status_message() const;
  private:
  bool _internal_has_translated_status_message() const;
  public:
  void clear_translated_status_message();
  const ::carbon::frontend::status_bar::TranslatedStatusMessage& translated_status_message() const;
  PROTOBUF_NODISCARD ::carbon::frontend::status_bar::TranslatedStatusMessage* release_translated_status_message();
  ::carbon::frontend::status_bar::TranslatedStatusMessage* mutable_translated_status_message();
  void set_allocated_translated_status_message(::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message);
  private:
  const ::carbon::frontend::status_bar::TranslatedStatusMessage& _internal_translated_status_message() const;
  ::carbon::frontend::status_bar::TranslatedStatusMessage* _internal_mutable_translated_status_message();
  public:
  void unsafe_arena_set_allocated_translated_status_message(
      ::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message);
  ::carbon::frontend::status_bar::TranslatedStatusMessage* unsafe_arena_release_translated_status_message();

  // int64 reported_at = 6;
  void clear_reported_at();
  int64_t reported_at() const;
  void set_reported_at(int64_t value);
  private:
  int64_t _internal_reported_at() const;
  void _internal_set_reported_at(int64_t value);
  public:

  // int64 status_changed_at = 10;
  void clear_status_changed_at();
  int64_t status_changed_at() const;
  void set_status_changed_at(int64_t value);
  private:
  int64_t _internal_status_changed_at() const;
  void _internal_set_status_changed_at(int64_t value);
  public:

  // .carbon.frontend.status_bar.Status status = 9;
  void clear_status();
  ::carbon::frontend::status_bar::Status status() const;
  void set_status(::carbon::frontend::status_bar::Status value);
  private:
  ::carbon::frontend::status_bar::Status _internal_status() const;
  void _internal_set_status(::carbon::frontend::status_bar::Status value);
  public:

  // bool target_version_ready = 15;
  void clear_target_version_ready();
  bool target_version_ready() const;
  void set_target_version_ready(bool value);
  private:
  bool _internal_target_version_ready() const;
  void _internal_set_target_version_ready(bool value);
  public:

  // uint32 robot_runtime_240v = 22;
  void clear_robot_runtime_240v();
  uint32_t robot_runtime_240v() const;
  void set_robot_runtime_240v(uint32_t value);
  private:
  uint32_t _internal_robot_runtime_240v() const;
  void _internal_set_robot_runtime_240v(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.health.HealthLog)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::health::AlarmRow > alarms_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> models_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct > systems_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      HealthLog_MetricTotalsEntry_DoNotUse,
      std::string, uint64_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT64> metric_totals_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow > alarm_list_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      HealthLog_HostSerialsEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> host_serials_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      HealthLog_FeatureFlagsEntry_DoNotUse,
      std::string, bool,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL> feature_flags_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_serial_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr p2p_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr software_version_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr target_version_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_message_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::carbon::portal::health::Location* location_;
  ::carbon::portal::health::Performance* performance_;
  ::carbon::portal::health::FieldConfig* field_config_;
  ::carbon::portal::health::Metrics* metrics_;
  ::carbon::frontend::laser::LaserStateList* laser_state_;
  ::carbon::metrics::LaserChangeTimes* laser_change_times_;
  ::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message_;
  int64_t reported_at_;
  int64_t status_changed_at_;
  int status_;
  bool target_version_ready_;
  uint32_t robot_runtime_240v_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// -------------------------------------------------------------------

class IssueReport final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.health.IssueReport) */ {
 public:
  inline IssueReport() : IssueReport(nullptr) {}
  ~IssueReport() override;
  explicit constexpr IssueReport(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IssueReport(const IssueReport& from);
  IssueReport(IssueReport&& from) noexcept
    : IssueReport() {
    *this = ::std::move(from);
  }

  inline IssueReport& operator=(const IssueReport& from) {
    CopyFrom(from);
    return *this;
  }
  inline IssueReport& operator=(IssueReport&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IssueReport& default_instance() {
    return *internal_default_instance();
  }
  static inline const IssueReport* internal_default_instance() {
    return reinterpret_cast<const IssueReport*>(
               &_IssueReport_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(IssueReport& a, IssueReport& b) {
    a.Swap(&b);
  }
  inline void Swap(IssueReport* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IssueReport* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IssueReport* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IssueReport>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IssueReport& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const IssueReport& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IssueReport* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.health.IssueReport";
  }
  protected:
  explicit IssueReport(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDescriptionFieldNumber = 1,
    kPhoneNumberFieldNumber = 2,
    kRobotSerialFieldNumber = 3,
    kCropFieldNumber = 5,
    kModelIdFieldNumber = 6,
    kSoftwareVersionFieldNumber = 7,
    kCropIdFieldNumber = 8,
    kReportedAtFieldNumber = 4,
  };
  // string description = 1;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // string phone_number = 2;
  void clear_phone_number();
  const std::string& phone_number() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_phone_number(ArgT0&& arg0, ArgT... args);
  std::string* mutable_phone_number();
  PROTOBUF_NODISCARD std::string* release_phone_number();
  void set_allocated_phone_number(std::string* phone_number);
  private:
  const std::string& _internal_phone_number() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_phone_number(const std::string& value);
  std::string* _internal_mutable_phone_number();
  public:

  // string robot_serial = 3;
  void clear_robot_serial();
  const std::string& robot_serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_serial();
  PROTOBUF_NODISCARD std::string* release_robot_serial();
  void set_allocated_robot_serial(std::string* robot_serial);
  private:
  const std::string& _internal_robot_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_serial(const std::string& value);
  std::string* _internal_mutable_robot_serial();
  public:

  // string crop = 5 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_crop();
  PROTOBUF_DEPRECATED const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_crop(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_crop();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_crop();
  PROTOBUF_DEPRECATED void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string model_id = 6;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // string software_version = 7;
  void clear_software_version();
  const std::string& software_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_software_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_software_version();
  PROTOBUF_NODISCARD std::string* release_software_version();
  void set_allocated_software_version(std::string* software_version);
  private:
  const std::string& _internal_software_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_software_version(const std::string& value);
  std::string* _internal_mutable_software_version();
  public:

  // string crop_id = 8;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // int64 reported_at = 4;
  void clear_reported_at();
  int64_t reported_at() const;
  void set_reported_at(int64_t value);
  private:
  int64_t _internal_reported_at() const;
  void _internal_set_reported_at(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.health.IssueReport)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr phone_number_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_serial_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr software_version_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  int64_t reported_at_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fhealth_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AlarmRow

// int64 timestamp_ms = 1;
inline void AlarmRow::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t AlarmRow::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t AlarmRow::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.AlarmRow.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void AlarmRow::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void AlarmRow::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.AlarmRow.timestamp_ms)
}

// string alarm_code = 2;
inline void AlarmRow::clear_alarm_code() {
  alarm_code_.ClearToEmpty();
}
inline const std::string& AlarmRow::alarm_code() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.AlarmRow.alarm_code)
  return _internal_alarm_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlarmRow::set_alarm_code(ArgT0&& arg0, ArgT... args) {
 
 alarm_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.AlarmRow.alarm_code)
}
inline std::string* AlarmRow::mutable_alarm_code() {
  std::string* _s = _internal_mutable_alarm_code();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.AlarmRow.alarm_code)
  return _s;
}
inline const std::string& AlarmRow::_internal_alarm_code() const {
  return alarm_code_.Get();
}
inline void AlarmRow::_internal_set_alarm_code(const std::string& value) {
  
  alarm_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlarmRow::_internal_mutable_alarm_code() {
  
  return alarm_code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlarmRow::release_alarm_code() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.AlarmRow.alarm_code)
  return alarm_code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlarmRow::set_allocated_alarm_code(std::string* alarm_code) {
  if (alarm_code != nullptr) {
    
  } else {
    
  }
  alarm_code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), alarm_code,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (alarm_code_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    alarm_code_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.AlarmRow.alarm_code)
}

// string description = 3;
inline void AlarmRow::clear_description() {
  description_.ClearToEmpty();
}
inline const std::string& AlarmRow::description() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.AlarmRow.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlarmRow::set_description(ArgT0&& arg0, ArgT... args) {
 
 description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.AlarmRow.description)
}
inline std::string* AlarmRow::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.AlarmRow.description)
  return _s;
}
inline const std::string& AlarmRow::_internal_description() const {
  return description_.Get();
}
inline void AlarmRow::_internal_set_description(const std::string& value) {
  
  description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlarmRow::_internal_mutable_description() {
  
  return description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlarmRow::release_description() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.AlarmRow.description)
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlarmRow::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (description_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.AlarmRow.description)
}

// .carbon.portal.health.AlarmLevel level = 4;
inline void AlarmRow::clear_level() {
  level_ = 0;
}
inline ::carbon::portal::health::AlarmLevel AlarmRow::_internal_level() const {
  return static_cast< ::carbon::portal::health::AlarmLevel >(level_);
}
inline ::carbon::portal::health::AlarmLevel AlarmRow::level() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.AlarmRow.level)
  return _internal_level();
}
inline void AlarmRow::_internal_set_level(::carbon::portal::health::AlarmLevel value) {
  
  level_ = value;
}
inline void AlarmRow::set_level(::carbon::portal::health::AlarmLevel value) {
  _internal_set_level(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.AlarmRow.level)
}

// string identifier = 5;
inline void AlarmRow::clear_identifier() {
  identifier_.ClearToEmpty();
}
inline const std::string& AlarmRow::identifier() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.AlarmRow.identifier)
  return _internal_identifier();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlarmRow::set_identifier(ArgT0&& arg0, ArgT... args) {
 
 identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.AlarmRow.identifier)
}
inline std::string* AlarmRow::mutable_identifier() {
  std::string* _s = _internal_mutable_identifier();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.AlarmRow.identifier)
  return _s;
}
inline const std::string& AlarmRow::_internal_identifier() const {
  return identifier_.Get();
}
inline void AlarmRow::_internal_set_identifier(const std::string& value) {
  
  identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlarmRow::_internal_mutable_identifier() {
  
  return identifier_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlarmRow::release_identifier() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.AlarmRow.identifier)
  return identifier_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlarmRow::set_allocated_identifier(std::string* identifier) {
  if (identifier != nullptr) {
    
  } else {
    
  }
  identifier_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), identifier,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (identifier_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.AlarmRow.identifier)
}

// bool acknowledged = 6;
inline void AlarmRow::clear_acknowledged() {
  acknowledged_ = false;
}
inline bool AlarmRow::_internal_acknowledged() const {
  return acknowledged_;
}
inline bool AlarmRow::acknowledged() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.AlarmRow.acknowledged)
  return _internal_acknowledged();
}
inline void AlarmRow::_internal_set_acknowledged(bool value) {
  
  acknowledged_ = value;
}
inline void AlarmRow::set_acknowledged(bool value) {
  _internal_set_acknowledged(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.AlarmRow.acknowledged)
}

// .carbon.portal.health.AlarmImpact impact = 7;
inline void AlarmRow::clear_impact() {
  impact_ = 0;
}
inline ::carbon::portal::health::AlarmImpact AlarmRow::_internal_impact() const {
  return static_cast< ::carbon::portal::health::AlarmImpact >(impact_);
}
inline ::carbon::portal::health::AlarmImpact AlarmRow::impact() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.AlarmRow.impact)
  return _internal_impact();
}
inline void AlarmRow::_internal_set_impact(::carbon::portal::health::AlarmImpact value) {
  
  impact_ = value;
}
inline void AlarmRow::set_impact(::carbon::portal::health::AlarmImpact value) {
  _internal_set_impact(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.AlarmRow.impact)
}

// -------------------------------------------------------------------

// Location

// double x = 1;
inline void Location::clear_x() {
  x_ = 0;
}
inline double Location::_internal_x() const {
  return x_;
}
inline double Location::x() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.Location.x)
  return _internal_x();
}
inline void Location::_internal_set_x(double value) {
  
  x_ = value;
}
inline void Location::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.Location.x)
}

// double y = 2;
inline void Location::clear_y() {
  y_ = 0;
}
inline double Location::_internal_y() const {
  return y_;
}
inline double Location::y() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.Location.y)
  return _internal_y();
}
inline void Location::_internal_set_y(double value) {
  
  y_ = value;
}
inline void Location::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.Location.y)
}

// double z = 3;
inline void Location::clear_z() {
  z_ = 0;
}
inline double Location::_internal_z() const {
  return z_;
}
inline double Location::z() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.Location.z)
  return _internal_z();
}
inline void Location::_internal_set_z(double value) {
  
  z_ = value;
}
inline void Location::set_z(double value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.Location.z)
}

// -------------------------------------------------------------------

// FieldConfig

// bool banding_enabled = 1;
inline void FieldConfig::clear_banding_enabled() {
  banding_enabled_ = false;
}
inline bool FieldConfig::_internal_banding_enabled() const {
  return banding_enabled_;
}
inline bool FieldConfig::banding_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.banding_enabled)
  return _internal_banding_enabled();
}
inline void FieldConfig::_internal_set_banding_enabled(bool value) {
  
  banding_enabled_ = value;
}
inline void FieldConfig::set_banding_enabled(bool value) {
  _internal_set_banding_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.banding_enabled)
}

// bool banding_dynamic = 2;
inline void FieldConfig::clear_banding_dynamic() {
  banding_dynamic_ = false;
}
inline bool FieldConfig::_internal_banding_dynamic() const {
  return banding_dynamic_;
}
inline bool FieldConfig::banding_dynamic() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.banding_dynamic)
  return _internal_banding_dynamic();
}
inline void FieldConfig::_internal_set_banding_dynamic(bool value) {
  
  banding_dynamic_ = value;
}
inline void FieldConfig::set_banding_dynamic(bool value) {
  _internal_set_banding_dynamic(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.banding_dynamic)
}

// string active_band_config = 3;
inline void FieldConfig::clear_active_band_config() {
  active_band_config_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_band_config() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_band_config)
  return _internal_active_band_config();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_band_config(ArgT0&& arg0, ArgT... args) {
 
 active_band_config_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_band_config)
}
inline std::string* FieldConfig::mutable_active_band_config() {
  std::string* _s = _internal_mutable_active_band_config();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_band_config)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_band_config() const {
  return active_band_config_.Get();
}
inline void FieldConfig::_internal_set_active_band_config(const std::string& value) {
  
  active_band_config_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_band_config() {
  
  return active_band_config_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_band_config() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_band_config)
  return active_band_config_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_band_config(std::string* active_band_config) {
  if (active_band_config != nullptr) {
    
  } else {
    
  }
  active_band_config_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_band_config,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_band_config_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_band_config_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_band_config)
}

// string active_thinning_config_id = 4;
inline void FieldConfig::clear_active_thinning_config_id() {
  active_thinning_config_id_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_thinning_config_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_thinning_config_id)
  return _internal_active_thinning_config_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_thinning_config_id(ArgT0&& arg0, ArgT... args) {
 
 active_thinning_config_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_thinning_config_id)
}
inline std::string* FieldConfig::mutable_active_thinning_config_id() {
  std::string* _s = _internal_mutable_active_thinning_config_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_thinning_config_id)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_thinning_config_id() const {
  return active_thinning_config_id_.Get();
}
inline void FieldConfig::_internal_set_active_thinning_config_id(const std::string& value) {
  
  active_thinning_config_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_thinning_config_id() {
  
  return active_thinning_config_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_thinning_config_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_thinning_config_id)
  return active_thinning_config_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_thinning_config_id(std::string* active_thinning_config_id) {
  if (active_thinning_config_id != nullptr) {
    
  } else {
    
  }
  active_thinning_config_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_thinning_config_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_thinning_config_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_thinning_config_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_thinning_config_id)
}

// string active_job_id = 5;
inline void FieldConfig::clear_active_job_id() {
  active_job_id_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_job_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_job_id)
  return _internal_active_job_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_job_id(ArgT0&& arg0, ArgT... args) {
 
 active_job_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_job_id)
}
inline std::string* FieldConfig::mutable_active_job_id() {
  std::string* _s = _internal_mutable_active_job_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_job_id)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_job_id() const {
  return active_job_id_.Get();
}
inline void FieldConfig::_internal_set_active_job_id(const std::string& value) {
  
  active_job_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_job_id() {
  
  return active_job_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_job_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_job_id)
  return active_job_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_job_id(std::string* active_job_id) {
  if (active_job_id != nullptr) {
    
  } else {
    
  }
  active_job_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_job_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_job_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_job_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_job_id)
}

// string active_almanac_id = 6;
inline void FieldConfig::clear_active_almanac_id() {
  active_almanac_id_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_almanac_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_almanac_id)
  return _internal_active_almanac_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_almanac_id(ArgT0&& arg0, ArgT... args) {
 
 active_almanac_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_almanac_id)
}
inline std::string* FieldConfig::mutable_active_almanac_id() {
  std::string* _s = _internal_mutable_active_almanac_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_almanac_id)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_almanac_id() const {
  return active_almanac_id_.Get();
}
inline void FieldConfig::_internal_set_active_almanac_id(const std::string& value) {
  
  active_almanac_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_almanac_id() {
  
  return active_almanac_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_almanac_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_almanac_id)
  return active_almanac_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_almanac_id(std::string* active_almanac_id) {
  if (active_almanac_id != nullptr) {
    
  } else {
    
  }
  active_almanac_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_almanac_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_almanac_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_almanac_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_almanac_id)
}

// string active_discriminator_id = 7;
inline void FieldConfig::clear_active_discriminator_id() {
  active_discriminator_id_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_discriminator_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_discriminator_id)
  return _internal_active_discriminator_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_discriminator_id(ArgT0&& arg0, ArgT... args) {
 
 active_discriminator_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_discriminator_id)
}
inline std::string* FieldConfig::mutable_active_discriminator_id() {
  std::string* _s = _internal_mutable_active_discriminator_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_discriminator_id)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_discriminator_id() const {
  return active_discriminator_id_.Get();
}
inline void FieldConfig::_internal_set_active_discriminator_id(const std::string& value) {
  
  active_discriminator_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_discriminator_id() {
  
  return active_discriminator_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_discriminator_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_discriminator_id)
  return active_discriminator_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_discriminator_id(std::string* active_discriminator_id) {
  if (active_discriminator_id != nullptr) {
    
  } else {
    
  }
  active_discriminator_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_discriminator_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_discriminator_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_discriminator_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_discriminator_id)
}

// bool is_weeding = 8;
inline void FieldConfig::clear_is_weeding() {
  is_weeding_ = false;
}
inline bool FieldConfig::_internal_is_weeding() const {
  return is_weeding_;
}
inline bool FieldConfig::is_weeding() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.is_weeding)
  return _internal_is_weeding();
}
inline void FieldConfig::_internal_set_is_weeding(bool value) {
  
  is_weeding_ = value;
}
inline void FieldConfig::set_is_weeding(bool value) {
  _internal_set_is_weeding(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.is_weeding)
}

// bool is_thinning = 9;
inline void FieldConfig::clear_is_thinning() {
  is_thinning_ = false;
}
inline bool FieldConfig::_internal_is_thinning() const {
  return is_thinning_;
}
inline bool FieldConfig::is_thinning() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.is_thinning)
  return _internal_is_thinning();
}
inline void FieldConfig::_internal_set_is_thinning(bool value) {
  
  is_thinning_ = value;
}
inline void FieldConfig::set_is_thinning(bool value) {
  _internal_set_is_thinning(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.is_thinning)
}

// string active_band_config_name = 10;
inline void FieldConfig::clear_active_band_config_name() {
  active_band_config_name_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_band_config_name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_band_config_name)
  return _internal_active_band_config_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_band_config_name(ArgT0&& arg0, ArgT... args) {
 
 active_band_config_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_band_config_name)
}
inline std::string* FieldConfig::mutable_active_band_config_name() {
  std::string* _s = _internal_mutable_active_band_config_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_band_config_name)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_band_config_name() const {
  return active_band_config_name_.Get();
}
inline void FieldConfig::_internal_set_active_band_config_name(const std::string& value) {
  
  active_band_config_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_band_config_name() {
  
  return active_band_config_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_band_config_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_band_config_name)
  return active_band_config_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_band_config_name(std::string* active_band_config_name) {
  if (active_band_config_name != nullptr) {
    
  } else {
    
  }
  active_band_config_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_band_config_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_band_config_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_band_config_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_band_config_name)
}

// string active_thinning_config_name = 11;
inline void FieldConfig::clear_active_thinning_config_name() {
  active_thinning_config_name_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_thinning_config_name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_thinning_config_name)
  return _internal_active_thinning_config_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_thinning_config_name(ArgT0&& arg0, ArgT... args) {
 
 active_thinning_config_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_thinning_config_name)
}
inline std::string* FieldConfig::mutable_active_thinning_config_name() {
  std::string* _s = _internal_mutable_active_thinning_config_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_thinning_config_name)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_thinning_config_name() const {
  return active_thinning_config_name_.Get();
}
inline void FieldConfig::_internal_set_active_thinning_config_name(const std::string& value) {
  
  active_thinning_config_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_thinning_config_name() {
  
  return active_thinning_config_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_thinning_config_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_thinning_config_name)
  return active_thinning_config_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_thinning_config_name(std::string* active_thinning_config_name) {
  if (active_thinning_config_name != nullptr) {
    
  } else {
    
  }
  active_thinning_config_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_thinning_config_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_thinning_config_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_thinning_config_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_thinning_config_name)
}

// string active_job_name = 12;
inline void FieldConfig::clear_active_job_name() {
  active_job_name_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_job_name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_job_name)
  return _internal_active_job_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_job_name(ArgT0&& arg0, ArgT... args) {
 
 active_job_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_job_name)
}
inline std::string* FieldConfig::mutable_active_job_name() {
  std::string* _s = _internal_mutable_active_job_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_job_name)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_job_name() const {
  return active_job_name_.Get();
}
inline void FieldConfig::_internal_set_active_job_name(const std::string& value) {
  
  active_job_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_job_name() {
  
  return active_job_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_job_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_job_name)
  return active_job_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_job_name(std::string* active_job_name) {
  if (active_job_name != nullptr) {
    
  } else {
    
  }
  active_job_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_job_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_job_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_job_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_job_name)
}

// string active_almanac_name = 13;
inline void FieldConfig::clear_active_almanac_name() {
  active_almanac_name_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_almanac_name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_almanac_name)
  return _internal_active_almanac_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_almanac_name(ArgT0&& arg0, ArgT... args) {
 
 active_almanac_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_almanac_name)
}
inline std::string* FieldConfig::mutable_active_almanac_name() {
  std::string* _s = _internal_mutable_active_almanac_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_almanac_name)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_almanac_name() const {
  return active_almanac_name_.Get();
}
inline void FieldConfig::_internal_set_active_almanac_name(const std::string& value) {
  
  active_almanac_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_almanac_name() {
  
  return active_almanac_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_almanac_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_almanac_name)
  return active_almanac_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_almanac_name(std::string* active_almanac_name) {
  if (active_almanac_name != nullptr) {
    
  } else {
    
  }
  active_almanac_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_almanac_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_almanac_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_almanac_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_almanac_name)
}

// string active_discriminator_name = 14;
inline void FieldConfig::clear_active_discriminator_name() {
  active_discriminator_name_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_discriminator_name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_discriminator_name)
  return _internal_active_discriminator_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_discriminator_name(ArgT0&& arg0, ArgT... args) {
 
 active_discriminator_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_discriminator_name)
}
inline std::string* FieldConfig::mutable_active_discriminator_name() {
  std::string* _s = _internal_mutable_active_discriminator_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_discriminator_name)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_discriminator_name() const {
  return active_discriminator_name_.Get();
}
inline void FieldConfig::_internal_set_active_discriminator_name(const std::string& value) {
  
  active_discriminator_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_discriminator_name() {
  
  return active_discriminator_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_discriminator_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_discriminator_name)
  return active_discriminator_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_discriminator_name(std::string* active_discriminator_name) {
  if (active_discriminator_name != nullptr) {
    
  } else {
    
  }
  active_discriminator_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_discriminator_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_discriminator_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_discriminator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_discriminator_name)
}

// string active_modelinator_id = 15;
inline void FieldConfig::clear_active_modelinator_id() {
  active_modelinator_id_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_modelinator_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_modelinator_id)
  return _internal_active_modelinator_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_modelinator_id(ArgT0&& arg0, ArgT... args) {
 
 active_modelinator_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_modelinator_id)
}
inline std::string* FieldConfig::mutable_active_modelinator_id() {
  std::string* _s = _internal_mutable_active_modelinator_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_modelinator_id)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_modelinator_id() const {
  return active_modelinator_id_.Get();
}
inline void FieldConfig::_internal_set_active_modelinator_id(const std::string& value) {
  
  active_modelinator_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_modelinator_id() {
  
  return active_modelinator_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_modelinator_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_modelinator_id)
  return active_modelinator_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_modelinator_id(std::string* active_modelinator_id) {
  if (active_modelinator_id != nullptr) {
    
  } else {
    
  }
  active_modelinator_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_modelinator_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_modelinator_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_modelinator_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_modelinator_id)
}

// string active_velocity_estimator_id = 16;
inline void FieldConfig::clear_active_velocity_estimator_id() {
  active_velocity_estimator_id_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_velocity_estimator_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_velocity_estimator_id)
  return _internal_active_velocity_estimator_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_velocity_estimator_id(ArgT0&& arg0, ArgT... args) {
 
 active_velocity_estimator_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_velocity_estimator_id)
}
inline std::string* FieldConfig::mutable_active_velocity_estimator_id() {
  std::string* _s = _internal_mutable_active_velocity_estimator_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_velocity_estimator_id)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_velocity_estimator_id() const {
  return active_velocity_estimator_id_.Get();
}
inline void FieldConfig::_internal_set_active_velocity_estimator_id(const std::string& value) {
  
  active_velocity_estimator_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_velocity_estimator_id() {
  
  return active_velocity_estimator_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_velocity_estimator_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_velocity_estimator_id)
  return active_velocity_estimator_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_velocity_estimator_id(std::string* active_velocity_estimator_id) {
  if (active_velocity_estimator_id != nullptr) {
    
  } else {
    
  }
  active_velocity_estimator_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_velocity_estimator_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_velocity_estimator_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_velocity_estimator_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_velocity_estimator_id)
}

// string active_velocity_estimator_name = 17;
inline void FieldConfig::clear_active_velocity_estimator_name() {
  active_velocity_estimator_name_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_velocity_estimator_name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_velocity_estimator_name)
  return _internal_active_velocity_estimator_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_velocity_estimator_name(ArgT0&& arg0, ArgT... args) {
 
 active_velocity_estimator_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_velocity_estimator_name)
}
inline std::string* FieldConfig::mutable_active_velocity_estimator_name() {
  std::string* _s = _internal_mutable_active_velocity_estimator_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_velocity_estimator_name)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_velocity_estimator_name() const {
  return active_velocity_estimator_name_.Get();
}
inline void FieldConfig::_internal_set_active_velocity_estimator_name(const std::string& value) {
  
  active_velocity_estimator_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_velocity_estimator_name() {
  
  return active_velocity_estimator_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_velocity_estimator_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_velocity_estimator_name)
  return active_velocity_estimator_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_velocity_estimator_name(std::string* active_velocity_estimator_name) {
  if (active_velocity_estimator_name != nullptr) {
    
  } else {
    
  }
  active_velocity_estimator_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_velocity_estimator_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_velocity_estimator_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_velocity_estimator_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_velocity_estimator_name)
}

// string active_category_collection_id = 18;
inline void FieldConfig::clear_active_category_collection_id() {
  active_category_collection_id_.ClearToEmpty();
}
inline const std::string& FieldConfig::active_category_collection_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.FieldConfig.active_category_collection_id)
  return _internal_active_category_collection_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FieldConfig::set_active_category_collection_id(ArgT0&& arg0, ArgT... args) {
 
 active_category_collection_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.FieldConfig.active_category_collection_id)
}
inline std::string* FieldConfig::mutable_active_category_collection_id() {
  std::string* _s = _internal_mutable_active_category_collection_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.FieldConfig.active_category_collection_id)
  return _s;
}
inline const std::string& FieldConfig::_internal_active_category_collection_id() const {
  return active_category_collection_id_.Get();
}
inline void FieldConfig::_internal_set_active_category_collection_id(const std::string& value) {
  
  active_category_collection_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FieldConfig::_internal_mutable_active_category_collection_id() {
  
  return active_category_collection_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FieldConfig::release_active_category_collection_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.FieldConfig.active_category_collection_id)
  return active_category_collection_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FieldConfig::set_allocated_active_category_collection_id(std::string* active_category_collection_id) {
  if (active_category_collection_id != nullptr) {
    
  } else {
    
  }
  active_category_collection_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_category_collection_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_category_collection_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_category_collection_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.FieldConfig.active_category_collection_id)
}

// -------------------------------------------------------------------

// Versions

// string current = 1;
inline void Versions::clear_current() {
  current_.ClearToEmpty();
}
inline const std::string& Versions::current() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.Versions.current)
  return _internal_current();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Versions::set_current(ArgT0&& arg0, ArgT... args) {
 
 current_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.Versions.current)
}
inline std::string* Versions::mutable_current() {
  std::string* _s = _internal_mutable_current();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.Versions.current)
  return _s;
}
inline const std::string& Versions::_internal_current() const {
  return current_.Get();
}
inline void Versions::_internal_set_current(const std::string& value) {
  
  current_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Versions::_internal_mutable_current() {
  
  return current_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Versions::release_current() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.Versions.current)
  return current_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Versions::set_allocated_current(std::string* current) {
  if (current != nullptr) {
    
  } else {
    
  }
  current_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), current,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (current_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    current_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.Versions.current)
}

// string latest = 2;
inline void Versions::clear_latest() {
  latest_.ClearToEmpty();
}
inline const std::string& Versions::latest() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.Versions.latest)
  return _internal_latest();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Versions::set_latest(ArgT0&& arg0, ArgT... args) {
 
 latest_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.Versions.latest)
}
inline std::string* Versions::mutable_latest() {
  std::string* _s = _internal_mutable_latest();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.Versions.latest)
  return _s;
}
inline const std::string& Versions::_internal_latest() const {
  return latest_.Get();
}
inline void Versions::_internal_set_latest(const std::string& value) {
  
  latest_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Versions::_internal_mutable_latest() {
  
  return latest_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Versions::release_latest() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.Versions.latest)
  return latest_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Versions::set_allocated_latest(std::string* latest) {
  if (latest != nullptr) {
    
  } else {
    
  }
  latest_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), latest,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (latest_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    latest_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.Versions.latest)
}

// -------------------------------------------------------------------

// WeedingPerformance

// double area_weeded_total = 1;
inline void WeedingPerformance::clear_area_weeded_total() {
  area_weeded_total_ = 0;
}
inline double WeedingPerformance::_internal_area_weeded_total() const {
  return area_weeded_total_;
}
inline double WeedingPerformance::area_weeded_total() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.WeedingPerformance.area_weeded_total)
  return _internal_area_weeded_total();
}
inline void WeedingPerformance::_internal_set_area_weeded_total(double value) {
  
  area_weeded_total_ = value;
}
inline void WeedingPerformance::set_area_weeded_total(double value) {
  _internal_set_area_weeded_total(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.WeedingPerformance.area_weeded_total)
}

// double area_weeded_today = 2;
inline void WeedingPerformance::clear_area_weeded_today() {
  area_weeded_today_ = 0;
}
inline double WeedingPerformance::_internal_area_weeded_today() const {
  return area_weeded_today_;
}
inline double WeedingPerformance::area_weeded_today() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.WeedingPerformance.area_weeded_today)
  return _internal_area_weeded_today();
}
inline void WeedingPerformance::_internal_set_area_weeded_today(double value) {
  
  area_weeded_today_ = value;
}
inline void WeedingPerformance::set_area_weeded_today(double value) {
  _internal_set_area_weeded_today(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.WeedingPerformance.area_weeded_today)
}

// int64 time_weeded_today = 3;
inline void WeedingPerformance::clear_time_weeded_today() {
  time_weeded_today_ = int64_t{0};
}
inline int64_t WeedingPerformance::_internal_time_weeded_today() const {
  return time_weeded_today_;
}
inline int64_t WeedingPerformance::time_weeded_today() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.WeedingPerformance.time_weeded_today)
  return _internal_time_weeded_today();
}
inline void WeedingPerformance::_internal_set_time_weeded_today(int64_t value) {
  
  time_weeded_today_ = value;
}
inline void WeedingPerformance::set_time_weeded_today(int64_t value) {
  _internal_set_time_weeded_today(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.WeedingPerformance.time_weeded_today)
}

// -------------------------------------------------------------------

// Performance

// .carbon.portal.health.WeedingPerformance weeding = 1;
inline bool Performance::_internal_has_weeding() const {
  return this != internal_default_instance() && weeding_ != nullptr;
}
inline bool Performance::has_weeding() const {
  return _internal_has_weeding();
}
inline void Performance::clear_weeding() {
  if (GetArenaForAllocation() == nullptr && weeding_ != nullptr) {
    delete weeding_;
  }
  weeding_ = nullptr;
}
inline const ::carbon::portal::health::WeedingPerformance& Performance::_internal_weeding() const {
  const ::carbon::portal::health::WeedingPerformance* p = weeding_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::health::WeedingPerformance&>(
      ::carbon::portal::health::_WeedingPerformance_default_instance_);
}
inline const ::carbon::portal::health::WeedingPerformance& Performance::weeding() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.Performance.weeding)
  return _internal_weeding();
}
inline void Performance::unsafe_arena_set_allocated_weeding(
    ::carbon::portal::health::WeedingPerformance* weeding) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(weeding_);
  }
  weeding_ = weeding;
  if (weeding) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.health.Performance.weeding)
}
inline ::carbon::portal::health::WeedingPerformance* Performance::release_weeding() {
  
  ::carbon::portal::health::WeedingPerformance* temp = weeding_;
  weeding_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::health::WeedingPerformance* Performance::unsafe_arena_release_weeding() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.Performance.weeding)
  
  ::carbon::portal::health::WeedingPerformance* temp = weeding_;
  weeding_ = nullptr;
  return temp;
}
inline ::carbon::portal::health::WeedingPerformance* Performance::_internal_mutable_weeding() {
  
  if (weeding_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::health::WeedingPerformance>(GetArenaForAllocation());
    weeding_ = p;
  }
  return weeding_;
}
inline ::carbon::portal::health::WeedingPerformance* Performance::mutable_weeding() {
  ::carbon::portal::health::WeedingPerformance* _msg = _internal_mutable_weeding();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.Performance.weeding)
  return _msg;
}
inline void Performance::set_allocated_weeding(::carbon::portal::health::WeedingPerformance* weeding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete weeding_;
  }
  if (weeding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::health::WeedingPerformance>::GetOwningArena(weeding);
    if (message_arena != submessage_arena) {
      weeding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, weeding, submessage_arena);
    }
    
  } else {
    
  }
  weeding_ = weeding;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.Performance.weeding)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DailyMetrics

// map<string, string> metrics = 1;
inline int DailyMetrics::_internal_metrics_size() const {
  return metrics_.size();
}
inline int DailyMetrics::metrics_size() const {
  return _internal_metrics_size();
}
inline void DailyMetrics::clear_metrics() {
  metrics_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
DailyMetrics::_internal_metrics() const {
  return metrics_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
DailyMetrics::metrics() const {
  // @@protoc_insertion_point(field_map:carbon.portal.health.DailyMetrics.metrics)
  return _internal_metrics();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
DailyMetrics::_internal_mutable_metrics() {
  return metrics_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
DailyMetrics::mutable_metrics() {
  // @@protoc_insertion_point(field_mutable_map:carbon.portal.health.DailyMetrics.metrics)
  return _internal_mutable_metrics();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Metrics

// map<string, .carbon.portal.health.DailyMetrics> daily_metrics = 1;
inline int Metrics::_internal_daily_metrics_size() const {
  return daily_metrics_.size();
}
inline int Metrics::daily_metrics_size() const {
  return _internal_daily_metrics_size();
}
inline void Metrics::clear_daily_metrics() {
  daily_metrics_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >&
Metrics::_internal_daily_metrics() const {
  return daily_metrics_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >&
Metrics::daily_metrics() const {
  // @@protoc_insertion_point(field_map:carbon.portal.health.Metrics.daily_metrics)
  return _internal_daily_metrics();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >*
Metrics::_internal_mutable_daily_metrics() {
  return daily_metrics_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::portal::health::DailyMetrics >*
Metrics::mutable_daily_metrics() {
  // @@protoc_insertion_point(field_mutable_map:carbon.portal.health.Metrics.daily_metrics)
  return _internal_mutable_daily_metrics();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// HealthLog

// repeated .carbon.portal.health.AlarmRow alarms = 1 [deprecated = true];
inline int HealthLog::_internal_alarms_size() const {
  return alarms_.size();
}
inline int HealthLog::alarms_size() const {
  return _internal_alarms_size();
}
inline void HealthLog::clear_alarms() {
  alarms_.Clear();
}
inline ::carbon::portal::health::AlarmRow* HealthLog::mutable_alarms(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.alarms)
  return alarms_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::health::AlarmRow >*
HealthLog::mutable_alarms() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.health.HealthLog.alarms)
  return &alarms_;
}
inline const ::carbon::portal::health::AlarmRow& HealthLog::_internal_alarms(int index) const {
  return alarms_.Get(index);
}
inline const ::carbon::portal::health::AlarmRow& HealthLog::alarms(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.alarms)
  return _internal_alarms(index);
}
inline ::carbon::portal::health::AlarmRow* HealthLog::_internal_add_alarms() {
  return alarms_.Add();
}
inline ::carbon::portal::health::AlarmRow* HealthLog::add_alarms() {
  ::carbon::portal::health::AlarmRow* _add = _internal_add_alarms();
  // @@protoc_insertion_point(field_add:carbon.portal.health.HealthLog.alarms)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::health::AlarmRow >&
HealthLog::alarms() const {
  // @@protoc_insertion_point(field_list:carbon.portal.health.HealthLog.alarms)
  return alarms_;
}

// .carbon.portal.health.Location location = 2;
inline bool HealthLog::_internal_has_location() const {
  return this != internal_default_instance() && location_ != nullptr;
}
inline bool HealthLog::has_location() const {
  return _internal_has_location();
}
inline void HealthLog::clear_location() {
  if (GetArenaForAllocation() == nullptr && location_ != nullptr) {
    delete location_;
  }
  location_ = nullptr;
}
inline const ::carbon::portal::health::Location& HealthLog::_internal_location() const {
  const ::carbon::portal::health::Location* p = location_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::health::Location&>(
      ::carbon::portal::health::_Location_default_instance_);
}
inline const ::carbon::portal::health::Location& HealthLog::location() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.location)
  return _internal_location();
}
inline void HealthLog::unsafe_arena_set_allocated_location(
    ::carbon::portal::health::Location* location) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_);
  }
  location_ = location;
  if (location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.health.HealthLog.location)
}
inline ::carbon::portal::health::Location* HealthLog::release_location() {
  
  ::carbon::portal::health::Location* temp = location_;
  location_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::health::Location* HealthLog::unsafe_arena_release_location() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.location)
  
  ::carbon::portal::health::Location* temp = location_;
  location_ = nullptr;
  return temp;
}
inline ::carbon::portal::health::Location* HealthLog::_internal_mutable_location() {
  
  if (location_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::health::Location>(GetArenaForAllocation());
    location_ = p;
  }
  return location_;
}
inline ::carbon::portal::health::Location* HealthLog::mutable_location() {
  ::carbon::portal::health::Location* _msg = _internal_mutable_location();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.location)
  return _msg;
}
inline void HealthLog::set_allocated_location(::carbon::portal::health::Location* location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete location_;
  }
  if (location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::health::Location>::GetOwningArena(location);
    if (message_arena != submessage_arena) {
      location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, location, submessage_arena);
    }
    
  } else {
    
  }
  location_ = location;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.location)
}

// string model = 3;
inline void HealthLog::clear_model() {
  model_.ClearToEmpty();
}
inline const std::string& HealthLog::model() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.model)
  return _internal_model();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HealthLog::set_model(ArgT0&& arg0, ArgT... args) {
 
 model_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.model)
}
inline std::string* HealthLog::mutable_model() {
  std::string* _s = _internal_mutable_model();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.model)
  return _s;
}
inline const std::string& HealthLog::_internal_model() const {
  return model_.Get();
}
inline void HealthLog::_internal_set_model(const std::string& value) {
  
  model_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HealthLog::_internal_mutable_model() {
  
  return model_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HealthLog::release_model() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.model)
  return model_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HealthLog::set_allocated_model(std::string* model) {
  if (model != nullptr) {
    
  } else {
    
  }
  model_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.model)
}

// repeated string models = 4;
inline int HealthLog::_internal_models_size() const {
  return models_.size();
}
inline int HealthLog::models_size() const {
  return _internal_models_size();
}
inline void HealthLog::clear_models() {
  models_.Clear();
}
inline std::string* HealthLog::add_models() {
  std::string* _s = _internal_add_models();
  // @@protoc_insertion_point(field_add_mutable:carbon.portal.health.HealthLog.models)
  return _s;
}
inline const std::string& HealthLog::_internal_models(int index) const {
  return models_.Get(index);
}
inline const std::string& HealthLog::models(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.models)
  return _internal_models(index);
}
inline std::string* HealthLog::mutable_models(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.models)
  return models_.Mutable(index);
}
inline void HealthLog::set_models(int index, const std::string& value) {
  models_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.models)
}
inline void HealthLog::set_models(int index, std::string&& value) {
  models_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.models)
}
inline void HealthLog::set_models(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  models_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.portal.health.HealthLog.models)
}
inline void HealthLog::set_models(int index, const char* value, size_t size) {
  models_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.portal.health.HealthLog.models)
}
inline std::string* HealthLog::_internal_add_models() {
  return models_.Add();
}
inline void HealthLog::add_models(const std::string& value) {
  models_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.portal.health.HealthLog.models)
}
inline void HealthLog::add_models(std::string&& value) {
  models_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.portal.health.HealthLog.models)
}
inline void HealthLog::add_models(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  models_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.portal.health.HealthLog.models)
}
inline void HealthLog::add_models(const char* value, size_t size) {
  models_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.portal.health.HealthLog.models)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
HealthLog::models() const {
  // @@protoc_insertion_point(field_list:carbon.portal.health.HealthLog.models)
  return models_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
HealthLog::mutable_models() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.health.HealthLog.models)
  return &models_;
}

// .carbon.portal.health.Performance performance = 5;
inline bool HealthLog::_internal_has_performance() const {
  return this != internal_default_instance() && performance_ != nullptr;
}
inline bool HealthLog::has_performance() const {
  return _internal_has_performance();
}
inline void HealthLog::clear_performance() {
  if (GetArenaForAllocation() == nullptr && performance_ != nullptr) {
    delete performance_;
  }
  performance_ = nullptr;
}
inline const ::carbon::portal::health::Performance& HealthLog::_internal_performance() const {
  const ::carbon::portal::health::Performance* p = performance_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::health::Performance&>(
      ::carbon::portal::health::_Performance_default_instance_);
}
inline const ::carbon::portal::health::Performance& HealthLog::performance() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.performance)
  return _internal_performance();
}
inline void HealthLog::unsafe_arena_set_allocated_performance(
    ::carbon::portal::health::Performance* performance) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(performance_);
  }
  performance_ = performance;
  if (performance) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.health.HealthLog.performance)
}
inline ::carbon::portal::health::Performance* HealthLog::release_performance() {
  
  ::carbon::portal::health::Performance* temp = performance_;
  performance_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::health::Performance* HealthLog::unsafe_arena_release_performance() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.performance)
  
  ::carbon::portal::health::Performance* temp = performance_;
  performance_ = nullptr;
  return temp;
}
inline ::carbon::portal::health::Performance* HealthLog::_internal_mutable_performance() {
  
  if (performance_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::health::Performance>(GetArenaForAllocation());
    performance_ = p;
  }
  return performance_;
}
inline ::carbon::portal::health::Performance* HealthLog::mutable_performance() {
  ::carbon::portal::health::Performance* _msg = _internal_mutable_performance();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.performance)
  return _msg;
}
inline void HealthLog::set_allocated_performance(::carbon::portal::health::Performance* performance) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete performance_;
  }
  if (performance) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::health::Performance>::GetOwningArena(performance);
    if (message_arena != submessage_arena) {
      performance = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, performance, submessage_arena);
    }
    
  } else {
    
  }
  performance_ = performance;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.performance)
}

// int64 reported_at = 6;
inline void HealthLog::clear_reported_at() {
  reported_at_ = int64_t{0};
}
inline int64_t HealthLog::_internal_reported_at() const {
  return reported_at_;
}
inline int64_t HealthLog::reported_at() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.reported_at)
  return _internal_reported_at();
}
inline void HealthLog::_internal_set_reported_at(int64_t value) {
  
  reported_at_ = value;
}
inline void HealthLog::set_reported_at(int64_t value) {
  _internal_set_reported_at(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.reported_at)
}

// string robot_serial = 7;
inline void HealthLog::clear_robot_serial() {
  robot_serial_.ClearToEmpty();
}
inline const std::string& HealthLog::robot_serial() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.robot_serial)
  return _internal_robot_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HealthLog::set_robot_serial(ArgT0&& arg0, ArgT... args) {
 
 robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.robot_serial)
}
inline std::string* HealthLog::mutable_robot_serial() {
  std::string* _s = _internal_mutable_robot_serial();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.robot_serial)
  return _s;
}
inline const std::string& HealthLog::_internal_robot_serial() const {
  return robot_serial_.Get();
}
inline void HealthLog::_internal_set_robot_serial(const std::string& value) {
  
  robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HealthLog::_internal_mutable_robot_serial() {
  
  return robot_serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HealthLog::release_robot_serial() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.robot_serial)
  return robot_serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HealthLog::set_allocated_robot_serial(std::string* robot_serial) {
  if (robot_serial != nullptr) {
    
  } else {
    
  }
  robot_serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.robot_serial)
}

// repeated .google.protobuf.Struct systems = 8;
inline int HealthLog::_internal_systems_size() const {
  return systems_.size();
}
inline int HealthLog::systems_size() const {
  return _internal_systems_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* HealthLog::mutable_systems(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.systems)
  return systems_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct >*
HealthLog::mutable_systems() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.health.HealthLog.systems)
  return &systems_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Struct& HealthLog::_internal_systems(int index) const {
  return systems_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Struct& HealthLog::systems(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.systems)
  return _internal_systems(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* HealthLog::_internal_add_systems() {
  return systems_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Struct* HealthLog::add_systems() {
  ::PROTOBUF_NAMESPACE_ID::Struct* _add = _internal_add_systems();
  // @@protoc_insertion_point(field_add:carbon.portal.health.HealthLog.systems)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Struct >&
HealthLog::systems() const {
  // @@protoc_insertion_point(field_list:carbon.portal.health.HealthLog.systems)
  return systems_;
}

// .carbon.frontend.status_bar.Status status = 9;
inline void HealthLog::clear_status() {
  status_ = 0;
}
inline ::carbon::frontend::status_bar::Status HealthLog::_internal_status() const {
  return static_cast< ::carbon::frontend::status_bar::Status >(status_);
}
inline ::carbon::frontend::status_bar::Status HealthLog::status() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.status)
  return _internal_status();
}
inline void HealthLog::_internal_set_status(::carbon::frontend::status_bar::Status value) {
  
  status_ = value;
}
inline void HealthLog::set_status(::carbon::frontend::status_bar::Status value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.status)
}

// int64 status_changed_at = 10;
inline void HealthLog::clear_status_changed_at() {
  status_changed_at_ = int64_t{0};
}
inline int64_t HealthLog::_internal_status_changed_at() const {
  return status_changed_at_;
}
inline int64_t HealthLog::status_changed_at() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.status_changed_at)
  return _internal_status_changed_at();
}
inline void HealthLog::_internal_set_status_changed_at(int64_t value) {
  
  status_changed_at_ = value;
}
inline void HealthLog::set_status_changed_at(int64_t value) {
  _internal_set_status_changed_at(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.status_changed_at)
}

// string crop = 11 [deprecated = true];
inline void HealthLog::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& HealthLog::crop() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HealthLog::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.crop)
}
inline std::string* HealthLog::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.crop)
  return _s;
}
inline const std::string& HealthLog::_internal_crop() const {
  return crop_.Get();
}
inline void HealthLog::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HealthLog::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HealthLog::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HealthLog::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.crop)
}

// string p2p = 12;
inline void HealthLog::clear_p2p() {
  p2p_.ClearToEmpty();
}
inline const std::string& HealthLog::p2p() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.p2p)
  return _internal_p2p();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HealthLog::set_p2p(ArgT0&& arg0, ArgT... args) {
 
 p2p_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.p2p)
}
inline std::string* HealthLog::mutable_p2p() {
  std::string* _s = _internal_mutable_p2p();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.p2p)
  return _s;
}
inline const std::string& HealthLog::_internal_p2p() const {
  return p2p_.Get();
}
inline void HealthLog::_internal_set_p2p(const std::string& value) {
  
  p2p_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HealthLog::_internal_mutable_p2p() {
  
  return p2p_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HealthLog::release_p2p() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.p2p)
  return p2p_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HealthLog::set_allocated_p2p(std::string* p2p) {
  if (p2p != nullptr) {
    
  } else {
    
  }
  p2p_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), p2p,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (p2p_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    p2p_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.p2p)
}

// string software_version = 13;
inline void HealthLog::clear_software_version() {
  software_version_.ClearToEmpty();
}
inline const std::string& HealthLog::software_version() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.software_version)
  return _internal_software_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HealthLog::set_software_version(ArgT0&& arg0, ArgT... args) {
 
 software_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.software_version)
}
inline std::string* HealthLog::mutable_software_version() {
  std::string* _s = _internal_mutable_software_version();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.software_version)
  return _s;
}
inline const std::string& HealthLog::_internal_software_version() const {
  return software_version_.Get();
}
inline void HealthLog::_internal_set_software_version(const std::string& value) {
  
  software_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HealthLog::_internal_mutable_software_version() {
  
  return software_version_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HealthLog::release_software_version() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.software_version)
  return software_version_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HealthLog::set_allocated_software_version(std::string* software_version) {
  if (software_version != nullptr) {
    
  } else {
    
  }
  software_version_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), software_version,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (software_version_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    software_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.software_version)
}

// string target_version = 14;
inline void HealthLog::clear_target_version() {
  target_version_.ClearToEmpty();
}
inline const std::string& HealthLog::target_version() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.target_version)
  return _internal_target_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HealthLog::set_target_version(ArgT0&& arg0, ArgT... args) {
 
 target_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.target_version)
}
inline std::string* HealthLog::mutable_target_version() {
  std::string* _s = _internal_mutable_target_version();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.target_version)
  return _s;
}
inline const std::string& HealthLog::_internal_target_version() const {
  return target_version_.Get();
}
inline void HealthLog::_internal_set_target_version(const std::string& value) {
  
  target_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HealthLog::_internal_mutable_target_version() {
  
  return target_version_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HealthLog::release_target_version() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.target_version)
  return target_version_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HealthLog::set_allocated_target_version(std::string* target_version) {
  if (target_version != nullptr) {
    
  } else {
    
  }
  target_version_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), target_version,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (target_version_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    target_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.target_version)
}

// bool target_version_ready = 15;
inline void HealthLog::clear_target_version_ready() {
  target_version_ready_ = false;
}
inline bool HealthLog::_internal_target_version_ready() const {
  return target_version_ready_;
}
inline bool HealthLog::target_version_ready() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.target_version_ready)
  return _internal_target_version_ready();
}
inline void HealthLog::_internal_set_target_version_ready(bool value) {
  
  target_version_ready_ = value;
}
inline void HealthLog::set_target_version_ready(bool value) {
  _internal_set_target_version_ready(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.target_version_ready)
}

// string status_message = 16;
inline void HealthLog::clear_status_message() {
  status_message_.ClearToEmpty();
}
inline const std::string& HealthLog::status_message() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.status_message)
  return _internal_status_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HealthLog::set_status_message(ArgT0&& arg0, ArgT... args) {
 
 status_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.status_message)
}
inline std::string* HealthLog::mutable_status_message() {
  std::string* _s = _internal_mutable_status_message();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.status_message)
  return _s;
}
inline const std::string& HealthLog::_internal_status_message() const {
  return status_message_.Get();
}
inline void HealthLog::_internal_set_status_message(const std::string& value) {
  
  status_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HealthLog::_internal_mutable_status_message() {
  
  return status_message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HealthLog::release_status_message() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.status_message)
  return status_message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HealthLog::set_allocated_status_message(std::string* status_message) {
  if (status_message != nullptr) {
    
  } else {
    
  }
  status_message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), status_message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (status_message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    status_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.status_message)
}

// map<string, uint64> metric_totals = 17;
inline int HealthLog::_internal_metric_totals_size() const {
  return metric_totals_.size();
}
inline int HealthLog::metric_totals_size() const {
  return _internal_metric_totals_size();
}
inline void HealthLog::clear_metric_totals() {
  metric_totals_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >&
HealthLog::_internal_metric_totals() const {
  return metric_totals_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >&
HealthLog::metric_totals() const {
  // @@protoc_insertion_point(field_map:carbon.portal.health.HealthLog.metric_totals)
  return _internal_metric_totals();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >*
HealthLog::_internal_mutable_metric_totals() {
  return metric_totals_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >*
HealthLog::mutable_metric_totals() {
  // @@protoc_insertion_point(field_mutable_map:carbon.portal.health.HealthLog.metric_totals)
  return _internal_mutable_metric_totals();
}

// repeated .carbon.frontend.alarm.AlarmRow alarm_list = 18;
inline int HealthLog::_internal_alarm_list_size() const {
  return alarm_list_.size();
}
inline int HealthLog::alarm_list_size() const {
  return _internal_alarm_list_size();
}
inline ::carbon::frontend::alarm::AlarmRow* HealthLog::mutable_alarm_list(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.alarm_list)
  return alarm_list_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
HealthLog::mutable_alarm_list() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.health.HealthLog.alarm_list)
  return &alarm_list_;
}
inline const ::carbon::frontend::alarm::AlarmRow& HealthLog::_internal_alarm_list(int index) const {
  return alarm_list_.Get(index);
}
inline const ::carbon::frontend::alarm::AlarmRow& HealthLog::alarm_list(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.alarm_list)
  return _internal_alarm_list(index);
}
inline ::carbon::frontend::alarm::AlarmRow* HealthLog::_internal_add_alarm_list() {
  return alarm_list_.Add();
}
inline ::carbon::frontend::alarm::AlarmRow* HealthLog::add_alarm_list() {
  ::carbon::frontend::alarm::AlarmRow* _add = _internal_add_alarm_list();
  // @@protoc_insertion_point(field_add:carbon.portal.health.HealthLog.alarm_list)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
HealthLog::alarm_list() const {
  // @@protoc_insertion_point(field_list:carbon.portal.health.HealthLog.alarm_list)
  return alarm_list_;
}

// .carbon.portal.health.FieldConfig field_config = 19;
inline bool HealthLog::_internal_has_field_config() const {
  return this != internal_default_instance() && field_config_ != nullptr;
}
inline bool HealthLog::has_field_config() const {
  return _internal_has_field_config();
}
inline void HealthLog::clear_field_config() {
  if (GetArenaForAllocation() == nullptr && field_config_ != nullptr) {
    delete field_config_;
  }
  field_config_ = nullptr;
}
inline const ::carbon::portal::health::FieldConfig& HealthLog::_internal_field_config() const {
  const ::carbon::portal::health::FieldConfig* p = field_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::health::FieldConfig&>(
      ::carbon::portal::health::_FieldConfig_default_instance_);
}
inline const ::carbon::portal::health::FieldConfig& HealthLog::field_config() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.field_config)
  return _internal_field_config();
}
inline void HealthLog::unsafe_arena_set_allocated_field_config(
    ::carbon::portal::health::FieldConfig* field_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(field_config_);
  }
  field_config_ = field_config;
  if (field_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.health.HealthLog.field_config)
}
inline ::carbon::portal::health::FieldConfig* HealthLog::release_field_config() {
  
  ::carbon::portal::health::FieldConfig* temp = field_config_;
  field_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::health::FieldConfig* HealthLog::unsafe_arena_release_field_config() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.field_config)
  
  ::carbon::portal::health::FieldConfig* temp = field_config_;
  field_config_ = nullptr;
  return temp;
}
inline ::carbon::portal::health::FieldConfig* HealthLog::_internal_mutable_field_config() {
  
  if (field_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::health::FieldConfig>(GetArenaForAllocation());
    field_config_ = p;
  }
  return field_config_;
}
inline ::carbon::portal::health::FieldConfig* HealthLog::mutable_field_config() {
  ::carbon::portal::health::FieldConfig* _msg = _internal_mutable_field_config();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.field_config)
  return _msg;
}
inline void HealthLog::set_allocated_field_config(::carbon::portal::health::FieldConfig* field_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete field_config_;
  }
  if (field_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::health::FieldConfig>::GetOwningArena(field_config);
    if (message_arena != submessage_arena) {
      field_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, field_config, submessage_arena);
    }
    
  } else {
    
  }
  field_config_ = field_config;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.field_config)
}

// .carbon.portal.health.Metrics metrics = 20;
inline bool HealthLog::_internal_has_metrics() const {
  return this != internal_default_instance() && metrics_ != nullptr;
}
inline bool HealthLog::has_metrics() const {
  return _internal_has_metrics();
}
inline void HealthLog::clear_metrics() {
  if (GetArenaForAllocation() == nullptr && metrics_ != nullptr) {
    delete metrics_;
  }
  metrics_ = nullptr;
}
inline const ::carbon::portal::health::Metrics& HealthLog::_internal_metrics() const {
  const ::carbon::portal::health::Metrics* p = metrics_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::health::Metrics&>(
      ::carbon::portal::health::_Metrics_default_instance_);
}
inline const ::carbon::portal::health::Metrics& HealthLog::metrics() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.metrics)
  return _internal_metrics();
}
inline void HealthLog::unsafe_arena_set_allocated_metrics(
    ::carbon::portal::health::Metrics* metrics) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metrics_);
  }
  metrics_ = metrics;
  if (metrics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.health.HealthLog.metrics)
}
inline ::carbon::portal::health::Metrics* HealthLog::release_metrics() {
  
  ::carbon::portal::health::Metrics* temp = metrics_;
  metrics_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::health::Metrics* HealthLog::unsafe_arena_release_metrics() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.metrics)
  
  ::carbon::portal::health::Metrics* temp = metrics_;
  metrics_ = nullptr;
  return temp;
}
inline ::carbon::portal::health::Metrics* HealthLog::_internal_mutable_metrics() {
  
  if (metrics_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::health::Metrics>(GetArenaForAllocation());
    metrics_ = p;
  }
  return metrics_;
}
inline ::carbon::portal::health::Metrics* HealthLog::mutable_metrics() {
  ::carbon::portal::health::Metrics* _msg = _internal_mutable_metrics();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.metrics)
  return _msg;
}
inline void HealthLog::set_allocated_metrics(::carbon::portal::health::Metrics* metrics) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete metrics_;
  }
  if (metrics) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::health::Metrics>::GetOwningArena(metrics);
    if (message_arena != submessage_arena) {
      metrics = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metrics, submessage_arena);
    }
    
  } else {
    
  }
  metrics_ = metrics;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.metrics)
}

// string crop_id = 21;
inline void HealthLog::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& HealthLog::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HealthLog::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.crop_id)
}
inline std::string* HealthLog::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.crop_id)
  return _s;
}
inline const std::string& HealthLog::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void HealthLog::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HealthLog::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HealthLog::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HealthLog::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.crop_id)
}

// uint32 robot_runtime_240v = 22;
inline void HealthLog::clear_robot_runtime_240v() {
  robot_runtime_240v_ = 0u;
}
inline uint32_t HealthLog::_internal_robot_runtime_240v() const {
  return robot_runtime_240v_;
}
inline uint32_t HealthLog::robot_runtime_240v() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.robot_runtime_240v)
  return _internal_robot_runtime_240v();
}
inline void HealthLog::_internal_set_robot_runtime_240v(uint32_t value) {
  
  robot_runtime_240v_ = value;
}
inline void HealthLog::set_robot_runtime_240v(uint32_t value) {
  _internal_set_robot_runtime_240v(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.HealthLog.robot_runtime_240v)
}

// .carbon.frontend.laser.LaserStateList laser_state = 23;
inline bool HealthLog::_internal_has_laser_state() const {
  return this != internal_default_instance() && laser_state_ != nullptr;
}
inline bool HealthLog::has_laser_state() const {
  return _internal_has_laser_state();
}
inline const ::carbon::frontend::laser::LaserStateList& HealthLog::_internal_laser_state() const {
  const ::carbon::frontend::laser::LaserStateList* p = laser_state_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::laser::LaserStateList&>(
      ::carbon::frontend::laser::_LaserStateList_default_instance_);
}
inline const ::carbon::frontend::laser::LaserStateList& HealthLog::laser_state() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.laser_state)
  return _internal_laser_state();
}
inline void HealthLog::unsafe_arena_set_allocated_laser_state(
    ::carbon::frontend::laser::LaserStateList* laser_state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_state_);
  }
  laser_state_ = laser_state;
  if (laser_state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.health.HealthLog.laser_state)
}
inline ::carbon::frontend::laser::LaserStateList* HealthLog::release_laser_state() {
  
  ::carbon::frontend::laser::LaserStateList* temp = laser_state_;
  laser_state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::laser::LaserStateList* HealthLog::unsafe_arena_release_laser_state() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.laser_state)
  
  ::carbon::frontend::laser::LaserStateList* temp = laser_state_;
  laser_state_ = nullptr;
  return temp;
}
inline ::carbon::frontend::laser::LaserStateList* HealthLog::_internal_mutable_laser_state() {
  
  if (laser_state_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::laser::LaserStateList>(GetArenaForAllocation());
    laser_state_ = p;
  }
  return laser_state_;
}
inline ::carbon::frontend::laser::LaserStateList* HealthLog::mutable_laser_state() {
  ::carbon::frontend::laser::LaserStateList* _msg = _internal_mutable_laser_state();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.laser_state)
  return _msg;
}
inline void HealthLog::set_allocated_laser_state(::carbon::frontend::laser::LaserStateList* laser_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_state_);
  }
  if (laser_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_state));
    if (message_arena != submessage_arena) {
      laser_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, laser_state, submessage_arena);
    }
    
  } else {
    
  }
  laser_state_ = laser_state;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.laser_state)
}

// .carbon.metrics.LaserChangeTimes laser_change_times = 24;
inline bool HealthLog::_internal_has_laser_change_times() const {
  return this != internal_default_instance() && laser_change_times_ != nullptr;
}
inline bool HealthLog::has_laser_change_times() const {
  return _internal_has_laser_change_times();
}
inline const ::carbon::metrics::LaserChangeTimes& HealthLog::_internal_laser_change_times() const {
  const ::carbon::metrics::LaserChangeTimes* p = laser_change_times_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::LaserChangeTimes&>(
      ::carbon::metrics::_LaserChangeTimes_default_instance_);
}
inline const ::carbon::metrics::LaserChangeTimes& HealthLog::laser_change_times() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.laser_change_times)
  return _internal_laser_change_times();
}
inline void HealthLog::unsafe_arena_set_allocated_laser_change_times(
    ::carbon::metrics::LaserChangeTimes* laser_change_times) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_change_times_);
  }
  laser_change_times_ = laser_change_times;
  if (laser_change_times) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.health.HealthLog.laser_change_times)
}
inline ::carbon::metrics::LaserChangeTimes* HealthLog::release_laser_change_times() {
  
  ::carbon::metrics::LaserChangeTimes* temp = laser_change_times_;
  laser_change_times_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::LaserChangeTimes* HealthLog::unsafe_arena_release_laser_change_times() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.laser_change_times)
  
  ::carbon::metrics::LaserChangeTimes* temp = laser_change_times_;
  laser_change_times_ = nullptr;
  return temp;
}
inline ::carbon::metrics::LaserChangeTimes* HealthLog::_internal_mutable_laser_change_times() {
  
  if (laser_change_times_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::LaserChangeTimes>(GetArenaForAllocation());
    laser_change_times_ = p;
  }
  return laser_change_times_;
}
inline ::carbon::metrics::LaserChangeTimes* HealthLog::mutable_laser_change_times() {
  ::carbon::metrics::LaserChangeTimes* _msg = _internal_mutable_laser_change_times();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.laser_change_times)
  return _msg;
}
inline void HealthLog::set_allocated_laser_change_times(::carbon::metrics::LaserChangeTimes* laser_change_times) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_change_times_);
  }
  if (laser_change_times) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_change_times));
    if (message_arena != submessage_arena) {
      laser_change_times = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, laser_change_times, submessage_arena);
    }
    
  } else {
    
  }
  laser_change_times_ = laser_change_times;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.laser_change_times)
}

// map<string, string> host_serials = 25;
inline int HealthLog::_internal_host_serials_size() const {
  return host_serials_.size();
}
inline int HealthLog::host_serials_size() const {
  return _internal_host_serials_size();
}
inline void HealthLog::clear_host_serials() {
  host_serials_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
HealthLog::_internal_host_serials() const {
  return host_serials_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
HealthLog::host_serials() const {
  // @@protoc_insertion_point(field_map:carbon.portal.health.HealthLog.host_serials)
  return _internal_host_serials();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
HealthLog::_internal_mutable_host_serials() {
  return host_serials_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
HealthLog::mutable_host_serials() {
  // @@protoc_insertion_point(field_mutable_map:carbon.portal.health.HealthLog.host_serials)
  return _internal_mutable_host_serials();
}

// map<string, bool> feature_flags = 26;
inline int HealthLog::_internal_feature_flags_size() const {
  return feature_flags_.size();
}
inline int HealthLog::feature_flags_size() const {
  return _internal_feature_flags_size();
}
inline void HealthLog::clear_feature_flags() {
  feature_flags_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >&
HealthLog::_internal_feature_flags() const {
  return feature_flags_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >&
HealthLog::feature_flags() const {
  // @@protoc_insertion_point(field_map:carbon.portal.health.HealthLog.feature_flags)
  return _internal_feature_flags();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >*
HealthLog::_internal_mutable_feature_flags() {
  return feature_flags_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >*
HealthLog::mutable_feature_flags() {
  // @@protoc_insertion_point(field_mutable_map:carbon.portal.health.HealthLog.feature_flags)
  return _internal_mutable_feature_flags();
}

// .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 27;
inline bool HealthLog::_internal_has_translated_status_message() const {
  return this != internal_default_instance() && translated_status_message_ != nullptr;
}
inline bool HealthLog::has_translated_status_message() const {
  return _internal_has_translated_status_message();
}
inline const ::carbon::frontend::status_bar::TranslatedStatusMessage& HealthLog::_internal_translated_status_message() const {
  const ::carbon::frontend::status_bar::TranslatedStatusMessage* p = translated_status_message_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::status_bar::TranslatedStatusMessage&>(
      ::carbon::frontend::status_bar::_TranslatedStatusMessage_default_instance_);
}
inline const ::carbon::frontend::status_bar::TranslatedStatusMessage& HealthLog::translated_status_message() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.HealthLog.translated_status_message)
  return _internal_translated_status_message();
}
inline void HealthLog::unsafe_arena_set_allocated_translated_status_message(
    ::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(translated_status_message_);
  }
  translated_status_message_ = translated_status_message;
  if (translated_status_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.health.HealthLog.translated_status_message)
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessage* HealthLog::release_translated_status_message() {
  
  ::carbon::frontend::status_bar::TranslatedStatusMessage* temp = translated_status_message_;
  translated_status_message_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessage* HealthLog::unsafe_arena_release_translated_status_message() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.HealthLog.translated_status_message)
  
  ::carbon::frontend::status_bar::TranslatedStatusMessage* temp = translated_status_message_;
  translated_status_message_ = nullptr;
  return temp;
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessage* HealthLog::_internal_mutable_translated_status_message() {
  
  if (translated_status_message_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::status_bar::TranslatedStatusMessage>(GetArenaForAllocation());
    translated_status_message_ = p;
  }
  return translated_status_message_;
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessage* HealthLog::mutable_translated_status_message() {
  ::carbon::frontend::status_bar::TranslatedStatusMessage* _msg = _internal_mutable_translated_status_message();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.HealthLog.translated_status_message)
  return _msg;
}
inline void HealthLog::set_allocated_translated_status_message(::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(translated_status_message_);
  }
  if (translated_status_message) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(translated_status_message));
    if (message_arena != submessage_arena) {
      translated_status_message = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, translated_status_message, submessage_arena);
    }
    
  } else {
    
  }
  translated_status_message_ = translated_status_message;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.HealthLog.translated_status_message)
}

// -------------------------------------------------------------------

// IssueReport

// string description = 1;
inline void IssueReport::clear_description() {
  description_.ClearToEmpty();
}
inline const std::string& IssueReport::description() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.IssueReport.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void IssueReport::set_description(ArgT0&& arg0, ArgT... args) {
 
 description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.IssueReport.description)
}
inline std::string* IssueReport::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.IssueReport.description)
  return _s;
}
inline const std::string& IssueReport::_internal_description() const {
  return description_.Get();
}
inline void IssueReport::_internal_set_description(const std::string& value) {
  
  description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* IssueReport::_internal_mutable_description() {
  
  return description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* IssueReport::release_description() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.IssueReport.description)
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void IssueReport::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (description_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.IssueReport.description)
}

// string phone_number = 2;
inline void IssueReport::clear_phone_number() {
  phone_number_.ClearToEmpty();
}
inline const std::string& IssueReport::phone_number() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.IssueReport.phone_number)
  return _internal_phone_number();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void IssueReport::set_phone_number(ArgT0&& arg0, ArgT... args) {
 
 phone_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.IssueReport.phone_number)
}
inline std::string* IssueReport::mutable_phone_number() {
  std::string* _s = _internal_mutable_phone_number();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.IssueReport.phone_number)
  return _s;
}
inline const std::string& IssueReport::_internal_phone_number() const {
  return phone_number_.Get();
}
inline void IssueReport::_internal_set_phone_number(const std::string& value) {
  
  phone_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* IssueReport::_internal_mutable_phone_number() {
  
  return phone_number_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* IssueReport::release_phone_number() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.IssueReport.phone_number)
  return phone_number_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void IssueReport::set_allocated_phone_number(std::string* phone_number) {
  if (phone_number != nullptr) {
    
  } else {
    
  }
  phone_number_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), phone_number,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (phone_number_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    phone_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.IssueReport.phone_number)
}

// string robot_serial = 3;
inline void IssueReport::clear_robot_serial() {
  robot_serial_.ClearToEmpty();
}
inline const std::string& IssueReport::robot_serial() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.IssueReport.robot_serial)
  return _internal_robot_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void IssueReport::set_robot_serial(ArgT0&& arg0, ArgT... args) {
 
 robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.IssueReport.robot_serial)
}
inline std::string* IssueReport::mutable_robot_serial() {
  std::string* _s = _internal_mutable_robot_serial();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.IssueReport.robot_serial)
  return _s;
}
inline const std::string& IssueReport::_internal_robot_serial() const {
  return robot_serial_.Get();
}
inline void IssueReport::_internal_set_robot_serial(const std::string& value) {
  
  robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* IssueReport::_internal_mutable_robot_serial() {
  
  return robot_serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* IssueReport::release_robot_serial() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.IssueReport.robot_serial)
  return robot_serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void IssueReport::set_allocated_robot_serial(std::string* robot_serial) {
  if (robot_serial != nullptr) {
    
  } else {
    
  }
  robot_serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.IssueReport.robot_serial)
}

// int64 reported_at = 4;
inline void IssueReport::clear_reported_at() {
  reported_at_ = int64_t{0};
}
inline int64_t IssueReport::_internal_reported_at() const {
  return reported_at_;
}
inline int64_t IssueReport::reported_at() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.IssueReport.reported_at)
  return _internal_reported_at();
}
inline void IssueReport::_internal_set_reported_at(int64_t value) {
  
  reported_at_ = value;
}
inline void IssueReport::set_reported_at(int64_t value) {
  _internal_set_reported_at(value);
  // @@protoc_insertion_point(field_set:carbon.portal.health.IssueReport.reported_at)
}

// string crop = 5 [deprecated = true];
inline void IssueReport::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& IssueReport::crop() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.IssueReport.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void IssueReport::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.IssueReport.crop)
}
inline std::string* IssueReport::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.IssueReport.crop)
  return _s;
}
inline const std::string& IssueReport::_internal_crop() const {
  return crop_.Get();
}
inline void IssueReport::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* IssueReport::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* IssueReport::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.IssueReport.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void IssueReport::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.IssueReport.crop)
}

// string model_id = 6;
inline void IssueReport::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& IssueReport::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.IssueReport.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void IssueReport::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.IssueReport.model_id)
}
inline std::string* IssueReport::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.IssueReport.model_id)
  return _s;
}
inline const std::string& IssueReport::_internal_model_id() const {
  return model_id_.Get();
}
inline void IssueReport::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* IssueReport::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* IssueReport::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.IssueReport.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void IssueReport::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.IssueReport.model_id)
}

// string software_version = 7;
inline void IssueReport::clear_software_version() {
  software_version_.ClearToEmpty();
}
inline const std::string& IssueReport::software_version() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.IssueReport.software_version)
  return _internal_software_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void IssueReport::set_software_version(ArgT0&& arg0, ArgT... args) {
 
 software_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.IssueReport.software_version)
}
inline std::string* IssueReport::mutable_software_version() {
  std::string* _s = _internal_mutable_software_version();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.IssueReport.software_version)
  return _s;
}
inline const std::string& IssueReport::_internal_software_version() const {
  return software_version_.Get();
}
inline void IssueReport::_internal_set_software_version(const std::string& value) {
  
  software_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* IssueReport::_internal_mutable_software_version() {
  
  return software_version_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* IssueReport::release_software_version() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.IssueReport.software_version)
  return software_version_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void IssueReport::set_allocated_software_version(std::string* software_version) {
  if (software_version != nullptr) {
    
  } else {
    
  }
  software_version_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), software_version,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (software_version_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    software_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.IssueReport.software_version)
}

// string crop_id = 8;
inline void IssueReport::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& IssueReport::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.health.IssueReport.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void IssueReport::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.health.IssueReport.crop_id)
}
inline std::string* IssueReport::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.health.IssueReport.crop_id)
  return _s;
}
inline const std::string& IssueReport::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void IssueReport::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* IssueReport::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* IssueReport::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.health.IssueReport.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void IssueReport::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.health.IssueReport.crop_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace health
}  // namespace portal
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::portal::health::AlarmLevel> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::portal::health::AlarmLevel>() {
  return ::carbon::portal::health::AlarmLevel_descriptor();
}
template <> struct is_proto_enum< ::carbon::portal::health::AlarmImpact> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::portal::health::AlarmImpact>() {
  return ::carbon::portal::health::AlarmImpact_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fhealth_2eproto

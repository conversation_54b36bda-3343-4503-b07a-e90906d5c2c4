# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/messages.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal.proto import db_pb2 as portal_dot_proto_dot_db__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/messages.proto',
  package='carbon.portal.messages',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1bportal/proto/messages.proto\x12\x16\x63\x61rbon.portal.messages\x1a\x15portal/proto/db.proto\"\xc3\x01\n\x07Message\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x16\n\x0e\x61uthor_user_id\x18\x03 \x01(\t\x12\x17\n\x0f\x61uthor_robot_id\x18\x04 \x01(\x03\x12\x19\n\x11recipient_user_id\x18\x05 \x01(\t\x12\x1d\n\x15recipient_customer_id\x18\x06 \x01(\x03\x12\x1a\n\x12recipient_robot_id\x18\x07 \x01(\x03\"b\n\x10MessagesResponse\x12\x0c\n\x04page\x18\x02 \x01(\x03\x12\r\n\x05limit\x18\x03 \x01(\x03\x12\x31\n\x08messages\x18\x04 \x03(\x0b\x32\x1f.carbon.portal.messages.MessageB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[portal_dot_proto_dot_db__pb2.DESCRIPTOR,])




_MESSAGE = _descriptor.Descriptor(
  name='Message',
  full_name='carbon.portal.messages.Message',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='db', full_name='carbon.portal.messages.Message.db', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='message', full_name='carbon.portal.messages.Message.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='author_user_id', full_name='carbon.portal.messages.Message.author_user_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='author_robot_id', full_name='carbon.portal.messages.Message.author_robot_id', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recipient_user_id', full_name='carbon.portal.messages.Message.recipient_user_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recipient_customer_id', full_name='carbon.portal.messages.Message.recipient_customer_id', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recipient_robot_id', full_name='carbon.portal.messages.Message.recipient_robot_id', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=79,
  serialized_end=274,
)


_MESSAGESRESPONSE = _descriptor.Descriptor(
  name='MessagesResponse',
  full_name='carbon.portal.messages.MessagesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page', full_name='carbon.portal.messages.MessagesResponse.page', index=0,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='limit', full_name='carbon.portal.messages.MessagesResponse.limit', index=1,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='messages', full_name='carbon.portal.messages.MessagesResponse.messages', index=2,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=276,
  serialized_end=374,
)

_MESSAGE.fields_by_name['db'].message_type = portal_dot_proto_dot_db__pb2._DB
_MESSAGESRESPONSE.fields_by_name['messages'].message_type = _MESSAGE
DESCRIPTOR.message_types_by_name['Message'] = _MESSAGE
DESCRIPTOR.message_types_by_name['MessagesResponse'] = _MESSAGESRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Message = _reflection.GeneratedProtocolMessageType('Message', (_message.Message,), {
  'DESCRIPTOR' : _MESSAGE,
  '__module__' : 'portal.proto.messages_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.messages.Message)
  })
_sym_db.RegisterMessage(Message)

MessagesResponse = _reflection.GeneratedProtocolMessageType('MessagesResponse', (_message.Message,), {
  'DESCRIPTOR' : _MESSAGESRESPONSE,
  '__module__' : 'portal.proto.messages_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.messages.MessagesResponse)
  })
_sym_db.RegisterMessage(MessagesResponse)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

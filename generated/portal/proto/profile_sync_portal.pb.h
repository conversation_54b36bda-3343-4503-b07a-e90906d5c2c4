// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/profile_sync_portal.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fprofile_5fsync_5fportal_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fprofile_5fsync_5fportal_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "portal/proto/util.pb.h"
#include "frontend/proto/profile_sync.pb.h"
#include "proto/almanac/almanac.pb.h"
#include "proto/thinning/thinning.pb.h"
#include "frontend/proto/banding.pb.h"
#include "proto/target_velocity_estimator/target_velocity_estimator.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2fprofile_5fsync_5fportal_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[12]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
namespace carbon {
namespace portal {
namespace profile_sync {
class DeleteProfileRequest;
struct DeleteProfileRequestDefaultTypeInternal;
extern DeleteProfileRequestDefaultTypeInternal _DeleteProfileRequest_default_instance_;
class GetProfileRequest;
struct GetProfileRequestDefaultTypeInternal;
extern GetProfileRequestDefaultTypeInternal _GetProfileRequest_default_instance_;
class GetProfileResponse;
struct GetProfileResponseDefaultTypeInternal;
extern GetProfileResponseDefaultTypeInternal _GetProfileResponse_default_instance_;
class GetProfilesDataRequest;
struct GetProfilesDataRequestDefaultTypeInternal;
extern GetProfilesDataRequestDefaultTypeInternal _GetProfilesDataRequest_default_instance_;
class GetProfilesDataResponse;
struct GetProfilesDataResponseDefaultTypeInternal;
extern GetProfilesDataResponseDefaultTypeInternal _GetProfilesDataResponse_default_instance_;
class GetProfilesDataResponse_ProfilesEntry_DoNotUse;
struct GetProfilesDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal;
extern GetProfilesDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal _GetProfilesDataResponse_ProfilesEntry_DoNotUse_default_instance_;
class GetSetActiveProfileCommandsRequest;
struct GetSetActiveProfileCommandsRequestDefaultTypeInternal;
extern GetSetActiveProfileCommandsRequestDefaultTypeInternal _GetSetActiveProfileCommandsRequest_default_instance_;
class GetSetActiveProfileCommandsResponse;
struct GetSetActiveProfileCommandsResponseDefaultTypeInternal;
extern GetSetActiveProfileCommandsResponseDefaultTypeInternal _GetSetActiveProfileCommandsResponse_default_instance_;
class PurgeProfileRequest;
struct PurgeProfileRequestDefaultTypeInternal;
extern PurgeProfileRequestDefaultTypeInternal _PurgeProfileRequest_default_instance_;
class PurgeSetActiveProfileCommandsRequest;
struct PurgeSetActiveProfileCommandsRequestDefaultTypeInternal;
extern PurgeSetActiveProfileCommandsRequestDefaultTypeInternal _PurgeSetActiveProfileCommandsRequest_default_instance_;
class SetActiveProfileCommand;
struct SetActiveProfileCommandDefaultTypeInternal;
extern SetActiveProfileCommandDefaultTypeInternal _SetActiveProfileCommand_default_instance_;
class UploadProfileRequest;
struct UploadProfileRequestDefaultTypeInternal;
extern UploadProfileRequestDefaultTypeInternal _UploadProfileRequest_default_instance_;
}  // namespace profile_sync
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::profile_sync::DeleteProfileRequest* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::DeleteProfileRequest>(Arena*);
template<> ::carbon::portal::profile_sync::GetProfileRequest* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::GetProfileRequest>(Arena*);
template<> ::carbon::portal::profile_sync::GetProfileResponse* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::GetProfileResponse>(Arena*);
template<> ::carbon::portal::profile_sync::GetProfilesDataRequest* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::GetProfilesDataRequest>(Arena*);
template<> ::carbon::portal::profile_sync::GetProfilesDataResponse* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::GetProfilesDataResponse>(Arena*);
template<> ::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse>(Arena*);
template<> ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest>(Arena*);
template<> ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>(Arena*);
template<> ::carbon::portal::profile_sync::PurgeProfileRequest* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::PurgeProfileRequest>(Arena*);
template<> ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest>(Arena*);
template<> ::carbon::portal::profile_sync::SetActiveProfileCommand* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::SetActiveProfileCommand>(Arena*);
template<> ::carbon::portal::profile_sync::UploadProfileRequest* Arena::CreateMaybeMessage<::carbon::portal::profile_sync::UploadProfileRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace profile_sync {

// ===================================================================

class GetProfilesDataRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.GetProfilesDataRequest) */ {
 public:
  inline GetProfilesDataRequest() : GetProfilesDataRequest(nullptr) {}
  ~GetProfilesDataRequest() override;
  explicit constexpr GetProfilesDataRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetProfilesDataRequest(const GetProfilesDataRequest& from);
  GetProfilesDataRequest(GetProfilesDataRequest&& from) noexcept
    : GetProfilesDataRequest() {
    *this = ::std::move(from);
  }

  inline GetProfilesDataRequest& operator=(const GetProfilesDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetProfilesDataRequest& operator=(GetProfilesDataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetProfilesDataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetProfilesDataRequest* internal_default_instance() {
    return reinterpret_cast<const GetProfilesDataRequest*>(
               &_GetProfilesDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetProfilesDataRequest& a, GetProfilesDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetProfilesDataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetProfilesDataRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetProfilesDataRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetProfilesDataRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetProfilesDataRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetProfilesDataRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetProfilesDataRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.GetProfilesDataRequest";
  }
  protected:
  explicit GetProfilesDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotNameFieldNumber = 1,
  };
  // string robot_name = 1;
  void clear_robot_name();
  const std::string& robot_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_name();
  PROTOBUF_NODISCARD std::string* release_robot_name();
  void set_allocated_robot_name(std::string* robot_name);
  private:
  const std::string& _internal_robot_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_name(const std::string& value);
  std::string* _internal_mutable_robot_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetProfilesDataRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class GetProfilesDataResponse_ProfilesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetProfilesDataResponse_ProfilesEntry_DoNotUse, 
    std::string, ::carbon::frontend::profile_sync::ProfileSyncData,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetProfilesDataResponse_ProfilesEntry_DoNotUse, 
    std::string, ::carbon::frontend::profile_sync::ProfileSyncData,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  GetProfilesDataResponse_ProfilesEntry_DoNotUse();
  explicit constexpr GetProfilesDataResponse_ProfilesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetProfilesDataResponse_ProfilesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetProfilesDataResponse_ProfilesEntry_DoNotUse& other);
  static const GetProfilesDataResponse_ProfilesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetProfilesDataResponse_ProfilesEntry_DoNotUse*>(&_GetProfilesDataResponse_ProfilesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetProfilesDataResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.GetProfilesDataResponse) */ {
 public:
  inline GetProfilesDataResponse() : GetProfilesDataResponse(nullptr) {}
  ~GetProfilesDataResponse() override;
  explicit constexpr GetProfilesDataResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetProfilesDataResponse(const GetProfilesDataResponse& from);
  GetProfilesDataResponse(GetProfilesDataResponse&& from) noexcept
    : GetProfilesDataResponse() {
    *this = ::std::move(from);
  }

  inline GetProfilesDataResponse& operator=(const GetProfilesDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetProfilesDataResponse& operator=(GetProfilesDataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetProfilesDataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetProfilesDataResponse* internal_default_instance() {
    return reinterpret_cast<const GetProfilesDataResponse*>(
               &_GetProfilesDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GetProfilesDataResponse& a, GetProfilesDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetProfilesDataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetProfilesDataResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetProfilesDataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetProfilesDataResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetProfilesDataResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetProfilesDataResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetProfilesDataResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.GetProfilesDataResponse";
  }
  protected:
  explicit GetProfilesDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kProfilesFieldNumber = 1,
  };
  // map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
  int profiles_size() const;
  private:
  int _internal_profiles_size() const;
  public:
  void clear_profiles();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >&
      _internal_profiles() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >*
      _internal_mutable_profiles();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >&
      profiles() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >*
      mutable_profiles();

  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetProfilesDataResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetProfilesDataResponse_ProfilesEntry_DoNotUse,
      std::string, ::carbon::frontend::profile_sync::ProfileSyncData,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> profiles_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class UploadProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.UploadProfileRequest) */ {
 public:
  inline UploadProfileRequest() : UploadProfileRequest(nullptr) {}
  ~UploadProfileRequest() override;
  explicit constexpr UploadProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UploadProfileRequest(const UploadProfileRequest& from);
  UploadProfileRequest(UploadProfileRequest&& from) noexcept
    : UploadProfileRequest() {
    *this = ::std::move(from);
  }

  inline UploadProfileRequest& operator=(const UploadProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UploadProfileRequest& operator=(UploadProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UploadProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  enum ProfileCase {
    kAlmanac = 3,
    kDiscriminator = 4,
    kModelinator = 5,
    kBanding = 6,
    kThinning = 7,
    kTargetVel = 8,
    PROFILE_NOT_SET = 0,
  };

  static inline const UploadProfileRequest* internal_default_instance() {
    return reinterpret_cast<const UploadProfileRequest*>(
               &_UploadProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(UploadProfileRequest& a, UploadProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UploadProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UploadProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UploadProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UploadProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UploadProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UploadProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UploadProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.UploadProfileRequest";
  }
  protected:
  explicit UploadProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotNameFieldNumber = 2,
    kLastUpdateTimeMsFieldNumber = 1,
    kAlmanacFieldNumber = 3,
    kDiscriminatorFieldNumber = 4,
    kModelinatorFieldNumber = 5,
    kBandingFieldNumber = 6,
    kThinningFieldNumber = 7,
    kTargetVelFieldNumber = 8,
  };
  // string robot_name = 2;
  void clear_robot_name();
  const std::string& robot_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_name();
  PROTOBUF_NODISCARD std::string* release_robot_name();
  void set_allocated_robot_name(std::string* robot_name);
  private:
  const std::string& _internal_robot_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_name(const std::string& value);
  std::string* _internal_mutable_robot_name();
  public:

  // int64 last_update_time_ms = 1;
  void clear_last_update_time_ms();
  int64_t last_update_time_ms() const;
  void set_last_update_time_ms(int64_t value);
  private:
  int64_t _internal_last_update_time_ms() const;
  void _internal_set_last_update_time_ms(int64_t value);
  public:

  // .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
  bool has_almanac() const;
  private:
  bool _internal_has_almanac() const;
  public:
  void clear_almanac();
  const ::carbon::aimbot::almanac::AlmanacConfig& almanac() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::AlmanacConfig* release_almanac();
  ::carbon::aimbot::almanac::AlmanacConfig* mutable_almanac();
  void set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac);
  private:
  const ::carbon::aimbot::almanac::AlmanacConfig& _internal_almanac() const;
  ::carbon::aimbot::almanac::AlmanacConfig* _internal_mutable_almanac();
  public:
  void unsafe_arena_set_allocated_almanac(
      ::carbon::aimbot::almanac::AlmanacConfig* almanac);
  ::carbon::aimbot::almanac::AlmanacConfig* unsafe_arena_release_almanac();

  // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
  bool has_discriminator() const;
  private:
  bool _internal_has_discriminator() const;
  public:
  void clear_discriminator();
  const ::carbon::aimbot::almanac::DiscriminatorConfig& discriminator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::DiscriminatorConfig* release_discriminator();
  ::carbon::aimbot::almanac::DiscriminatorConfig* mutable_discriminator();
  void set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator);
  private:
  const ::carbon::aimbot::almanac::DiscriminatorConfig& _internal_discriminator() const;
  ::carbon::aimbot::almanac::DiscriminatorConfig* _internal_mutable_discriminator();
  public:
  void unsafe_arena_set_allocated_discriminator(
      ::carbon::aimbot::almanac::DiscriminatorConfig* discriminator);
  ::carbon::aimbot::almanac::DiscriminatorConfig* unsafe_arena_release_discriminator();

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
  bool has_modelinator() const;
  private:
  bool _internal_has_modelinator() const;
  public:
  void clear_modelinator();
  const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_modelinator();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_modelinator();
  void set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_modelinator() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_modelinator();
  public:
  void unsafe_arena_set_allocated_modelinator(
      ::carbon::aimbot::almanac::ModelinatorConfig* modelinator);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_modelinator();

  // .carbon.frontend.banding.BandingDef banding = 6;
  bool has_banding() const;
  private:
  bool _internal_has_banding() const;
  public:
  void clear_banding();
  const ::carbon::frontend::banding::BandingDef& banding() const;
  PROTOBUF_NODISCARD ::carbon::frontend::banding::BandingDef* release_banding();
  ::carbon::frontend::banding::BandingDef* mutable_banding();
  void set_allocated_banding(::carbon::frontend::banding::BandingDef* banding);
  private:
  const ::carbon::frontend::banding::BandingDef& _internal_banding() const;
  ::carbon::frontend::banding::BandingDef* _internal_mutable_banding();
  public:
  void unsafe_arena_set_allocated_banding(
      ::carbon::frontend::banding::BandingDef* banding);
  ::carbon::frontend::banding::BandingDef* unsafe_arena_release_banding();

  // .carbon.thinning.ConfigDefinition thinning = 7;
  bool has_thinning() const;
  private:
  bool _internal_has_thinning() const;
  public:
  void clear_thinning();
  const ::carbon::thinning::ConfigDefinition& thinning() const;
  PROTOBUF_NODISCARD ::carbon::thinning::ConfigDefinition* release_thinning();
  ::carbon::thinning::ConfigDefinition* mutable_thinning();
  void set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning);
  private:
  const ::carbon::thinning::ConfigDefinition& _internal_thinning() const;
  ::carbon::thinning::ConfigDefinition* _internal_mutable_thinning();
  public:
  void unsafe_arena_set_allocated_thinning(
      ::carbon::thinning::ConfigDefinition* thinning);
  ::carbon::thinning::ConfigDefinition* unsafe_arena_release_thinning();

  // .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 8;
  bool has_target_vel() const;
  private:
  bool _internal_has_target_vel() const;
  public:
  void clear_target_vel();
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& target_vel() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVEProfile* release_target_vel();
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* mutable_target_vel();
  void set_allocated_target_vel(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& _internal_target_vel() const;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _internal_mutable_target_vel();
  public:
  void unsafe_arena_set_allocated_target_vel(
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel);
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* unsafe_arena_release_target_vel();

  void clear_profile();
  ProfileCase profile_case() const;
  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.UploadProfileRequest)
 private:
  class _Internal;
  void set_has_almanac();
  void set_has_discriminator();
  void set_has_modelinator();
  void set_has_banding();
  void set_has_thinning();
  void set_has_target_vel();

  inline bool has_profile() const;
  inline void clear_has_profile();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_name_;
  int64_t last_update_time_ms_;
  union ProfileUnion {
    constexpr ProfileUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::aimbot::almanac::AlmanacConfig* almanac_;
    ::carbon::aimbot::almanac::DiscriminatorConfig* discriminator_;
    ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_;
    ::carbon::frontend::banding::BandingDef* banding_;
    ::carbon::thinning::ConfigDefinition* thinning_;
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel_;
  } profile_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class GetProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.GetProfileRequest) */ {
 public:
  inline GetProfileRequest() : GetProfileRequest(nullptr) {}
  ~GetProfileRequest() override;
  explicit constexpr GetProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetProfileRequest(const GetProfileRequest& from);
  GetProfileRequest(GetProfileRequest&& from) noexcept
    : GetProfileRequest() {
    *this = ::std::move(from);
  }

  inline GetProfileRequest& operator=(const GetProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetProfileRequest& operator=(GetProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetProfileRequest* internal_default_instance() {
    return reinterpret_cast<const GetProfileRequest*>(
               &_GetProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetProfileRequest& a, GetProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.GetProfileRequest";
  }
  protected:
  explicit GetProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
  };
  // string uuid = 1;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class GetProfileResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.GetProfileResponse) */ {
 public:
  inline GetProfileResponse() : GetProfileResponse(nullptr) {}
  ~GetProfileResponse() override;
  explicit constexpr GetProfileResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetProfileResponse(const GetProfileResponse& from);
  GetProfileResponse(GetProfileResponse&& from) noexcept
    : GetProfileResponse() {
    *this = ::std::move(from);
  }

  inline GetProfileResponse& operator=(const GetProfileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetProfileResponse& operator=(GetProfileResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetProfileResponse& default_instance() {
    return *internal_default_instance();
  }
  enum ProfileCase {
    kAlmanac = 1,
    kDiscriminator = 2,
    kModelinator = 3,
    kBanding = 4,
    kThinning = 5,
    kTargetVel = 6,
    PROFILE_NOT_SET = 0,
  };

  static inline const GetProfileResponse* internal_default_instance() {
    return reinterpret_cast<const GetProfileResponse*>(
               &_GetProfileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GetProfileResponse& a, GetProfileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetProfileResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetProfileResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetProfileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetProfileResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetProfileResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetProfileResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetProfileResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.GetProfileResponse";
  }
  protected:
  explicit GetProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlmanacFieldNumber = 1,
    kDiscriminatorFieldNumber = 2,
    kModelinatorFieldNumber = 3,
    kBandingFieldNumber = 4,
    kThinningFieldNumber = 5,
    kTargetVelFieldNumber = 6,
  };
  // .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
  bool has_almanac() const;
  private:
  bool _internal_has_almanac() const;
  public:
  void clear_almanac();
  const ::carbon::aimbot::almanac::AlmanacConfig& almanac() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::AlmanacConfig* release_almanac();
  ::carbon::aimbot::almanac::AlmanacConfig* mutable_almanac();
  void set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac);
  private:
  const ::carbon::aimbot::almanac::AlmanacConfig& _internal_almanac() const;
  ::carbon::aimbot::almanac::AlmanacConfig* _internal_mutable_almanac();
  public:
  void unsafe_arena_set_allocated_almanac(
      ::carbon::aimbot::almanac::AlmanacConfig* almanac);
  ::carbon::aimbot::almanac::AlmanacConfig* unsafe_arena_release_almanac();

  // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
  bool has_discriminator() const;
  private:
  bool _internal_has_discriminator() const;
  public:
  void clear_discriminator();
  const ::carbon::aimbot::almanac::DiscriminatorConfig& discriminator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::DiscriminatorConfig* release_discriminator();
  ::carbon::aimbot::almanac::DiscriminatorConfig* mutable_discriminator();
  void set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator);
  private:
  const ::carbon::aimbot::almanac::DiscriminatorConfig& _internal_discriminator() const;
  ::carbon::aimbot::almanac::DiscriminatorConfig* _internal_mutable_discriminator();
  public:
  void unsafe_arena_set_allocated_discriminator(
      ::carbon::aimbot::almanac::DiscriminatorConfig* discriminator);
  ::carbon::aimbot::almanac::DiscriminatorConfig* unsafe_arena_release_discriminator();

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
  bool has_modelinator() const;
  private:
  bool _internal_has_modelinator() const;
  public:
  void clear_modelinator();
  const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_modelinator();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_modelinator();
  void set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_modelinator() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_modelinator();
  public:
  void unsafe_arena_set_allocated_modelinator(
      ::carbon::aimbot::almanac::ModelinatorConfig* modelinator);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_modelinator();

  // .carbon.frontend.banding.BandingDef banding = 4;
  bool has_banding() const;
  private:
  bool _internal_has_banding() const;
  public:
  void clear_banding();
  const ::carbon::frontend::banding::BandingDef& banding() const;
  PROTOBUF_NODISCARD ::carbon::frontend::banding::BandingDef* release_banding();
  ::carbon::frontend::banding::BandingDef* mutable_banding();
  void set_allocated_banding(::carbon::frontend::banding::BandingDef* banding);
  private:
  const ::carbon::frontend::banding::BandingDef& _internal_banding() const;
  ::carbon::frontend::banding::BandingDef* _internal_mutable_banding();
  public:
  void unsafe_arena_set_allocated_banding(
      ::carbon::frontend::banding::BandingDef* banding);
  ::carbon::frontend::banding::BandingDef* unsafe_arena_release_banding();

  // .carbon.thinning.ConfigDefinition thinning = 5;
  bool has_thinning() const;
  private:
  bool _internal_has_thinning() const;
  public:
  void clear_thinning();
  const ::carbon::thinning::ConfigDefinition& thinning() const;
  PROTOBUF_NODISCARD ::carbon::thinning::ConfigDefinition* release_thinning();
  ::carbon::thinning::ConfigDefinition* mutable_thinning();
  void set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning);
  private:
  const ::carbon::thinning::ConfigDefinition& _internal_thinning() const;
  ::carbon::thinning::ConfigDefinition* _internal_mutable_thinning();
  public:
  void unsafe_arena_set_allocated_thinning(
      ::carbon::thinning::ConfigDefinition* thinning);
  ::carbon::thinning::ConfigDefinition* unsafe_arena_release_thinning();

  // .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 6;
  bool has_target_vel() const;
  private:
  bool _internal_has_target_vel() const;
  public:
  void clear_target_vel();
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& target_vel() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVEProfile* release_target_vel();
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* mutable_target_vel();
  void set_allocated_target_vel(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& _internal_target_vel() const;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _internal_mutable_target_vel();
  public:
  void unsafe_arena_set_allocated_target_vel(
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel);
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* unsafe_arena_release_target_vel();

  void clear_profile();
  ProfileCase profile_case() const;
  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetProfileResponse)
 private:
  class _Internal;
  void set_has_almanac();
  void set_has_discriminator();
  void set_has_modelinator();
  void set_has_banding();
  void set_has_thinning();
  void set_has_target_vel();

  inline bool has_profile() const;
  inline void clear_has_profile();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ProfileUnion {
    constexpr ProfileUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::aimbot::almanac::AlmanacConfig* almanac_;
    ::carbon::aimbot::almanac::DiscriminatorConfig* discriminator_;
    ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_;
    ::carbon::frontend::banding::BandingDef* banding_;
    ::carbon::thinning::ConfigDefinition* thinning_;
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel_;
  } profile_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class DeleteProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.DeleteProfileRequest) */ {
 public:
  inline DeleteProfileRequest() : DeleteProfileRequest(nullptr) {}
  ~DeleteProfileRequest() override;
  explicit constexpr DeleteProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteProfileRequest(const DeleteProfileRequest& from);
  DeleteProfileRequest(DeleteProfileRequest&& from) noexcept
    : DeleteProfileRequest() {
    *this = ::std::move(from);
  }

  inline DeleteProfileRequest& operator=(const DeleteProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteProfileRequest& operator=(DeleteProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteProfileRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteProfileRequest*>(
               &_DeleteProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DeleteProfileRequest& a, DeleteProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeleteProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.DeleteProfileRequest";
  }
  protected:
  explicit DeleteProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
  };
  // string uuid = 1;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.DeleteProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class PurgeProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.PurgeProfileRequest) */ {
 public:
  inline PurgeProfileRequest() : PurgeProfileRequest(nullptr) {}
  ~PurgeProfileRequest() override;
  explicit constexpr PurgeProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PurgeProfileRequest(const PurgeProfileRequest& from);
  PurgeProfileRequest(PurgeProfileRequest&& from) noexcept
    : PurgeProfileRequest() {
    *this = ::std::move(from);
  }

  inline PurgeProfileRequest& operator=(const PurgeProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PurgeProfileRequest& operator=(PurgeProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PurgeProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PurgeProfileRequest* internal_default_instance() {
    return reinterpret_cast<const PurgeProfileRequest*>(
               &_PurgeProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(PurgeProfileRequest& a, PurgeProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PurgeProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PurgeProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PurgeProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PurgeProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PurgeProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PurgeProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PurgeProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.PurgeProfileRequest";
  }
  protected:
  explicit PurgeProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
  };
  // string uuid = 1;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.PurgeProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class SetActiveProfileCommand final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.SetActiveProfileCommand) */ {
 public:
  inline SetActiveProfileCommand() : SetActiveProfileCommand(nullptr) {}
  ~SetActiveProfileCommand() override;
  explicit constexpr SetActiveProfileCommand(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetActiveProfileCommand(const SetActiveProfileCommand& from);
  SetActiveProfileCommand(SetActiveProfileCommand&& from) noexcept
    : SetActiveProfileCommand() {
    *this = ::std::move(from);
  }

  inline SetActiveProfileCommand& operator=(const SetActiveProfileCommand& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetActiveProfileCommand& operator=(SetActiveProfileCommand&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetActiveProfileCommand& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetActiveProfileCommand* internal_default_instance() {
    return reinterpret_cast<const SetActiveProfileCommand*>(
               &_SetActiveProfileCommand_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(SetActiveProfileCommand& a, SetActiveProfileCommand& b) {
    a.Swap(&b);
  }
  inline void Swap(SetActiveProfileCommand* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetActiveProfileCommand* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetActiveProfileCommand* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetActiveProfileCommand>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetActiveProfileCommand& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetActiveProfileCommand& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetActiveProfileCommand* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.SetActiveProfileCommand";
  }
  protected:
  explicit SetActiveProfileCommand(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 2,
    kProfileTypeFieldNumber = 1,
  };
  // string uuid = 2;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
  void clear_profile_type();
  ::carbon::frontend::profile_sync::ProfileType profile_type() const;
  void set_profile_type(::carbon::frontend::profile_sync::ProfileType value);
  private:
  ::carbon::frontend::profile_sync::ProfileType _internal_profile_type() const;
  void _internal_set_profile_type(::carbon::frontend::profile_sync::ProfileType value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.SetActiveProfileCommand)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  int profile_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class GetSetActiveProfileCommandsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest) */ {
 public:
  inline GetSetActiveProfileCommandsRequest() : GetSetActiveProfileCommandsRequest(nullptr) {}
  ~GetSetActiveProfileCommandsRequest() override;
  explicit constexpr GetSetActiveProfileCommandsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetSetActiveProfileCommandsRequest(const GetSetActiveProfileCommandsRequest& from);
  GetSetActiveProfileCommandsRequest(GetSetActiveProfileCommandsRequest&& from) noexcept
    : GetSetActiveProfileCommandsRequest() {
    *this = ::std::move(from);
  }

  inline GetSetActiveProfileCommandsRequest& operator=(const GetSetActiveProfileCommandsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetSetActiveProfileCommandsRequest& operator=(GetSetActiveProfileCommandsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetSetActiveProfileCommandsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetSetActiveProfileCommandsRequest* internal_default_instance() {
    return reinterpret_cast<const GetSetActiveProfileCommandsRequest*>(
               &_GetSetActiveProfileCommandsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GetSetActiveProfileCommandsRequest& a, GetSetActiveProfileCommandsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetSetActiveProfileCommandsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetSetActiveProfileCommandsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetSetActiveProfileCommandsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetSetActiveProfileCommandsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetSetActiveProfileCommandsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetSetActiveProfileCommandsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetSetActiveProfileCommandsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest";
  }
  protected:
  explicit GetSetActiveProfileCommandsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotFieldNumber = 1,
  };
  // string robot = 1;
  void clear_robot();
  const std::string& robot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot();
  PROTOBUF_NODISCARD std::string* release_robot();
  void set_allocated_robot(std::string* robot);
  private:
  const std::string& _internal_robot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot(const std::string& value);
  std::string* _internal_mutable_robot();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class GetSetActiveProfileCommandsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse) */ {
 public:
  inline GetSetActiveProfileCommandsResponse() : GetSetActiveProfileCommandsResponse(nullptr) {}
  ~GetSetActiveProfileCommandsResponse() override;
  explicit constexpr GetSetActiveProfileCommandsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetSetActiveProfileCommandsResponse(const GetSetActiveProfileCommandsResponse& from);
  GetSetActiveProfileCommandsResponse(GetSetActiveProfileCommandsResponse&& from) noexcept
    : GetSetActiveProfileCommandsResponse() {
    *this = ::std::move(from);
  }

  inline GetSetActiveProfileCommandsResponse& operator=(const GetSetActiveProfileCommandsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetSetActiveProfileCommandsResponse& operator=(GetSetActiveProfileCommandsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetSetActiveProfileCommandsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetSetActiveProfileCommandsResponse* internal_default_instance() {
    return reinterpret_cast<const GetSetActiveProfileCommandsResponse*>(
               &_GetSetActiveProfileCommandsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GetSetActiveProfileCommandsResponse& a, GetSetActiveProfileCommandsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetSetActiveProfileCommandsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetSetActiveProfileCommandsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetSetActiveProfileCommandsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetSetActiveProfileCommandsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetSetActiveProfileCommandsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetSetActiveProfileCommandsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetSetActiveProfileCommandsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse";
  }
  protected:
  explicit GetSetActiveProfileCommandsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCommandsFieldNumber = 1,
  };
  // repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 1;
  int commands_size() const;
  private:
  int _internal_commands_size() const;
  public:
  void clear_commands();
  ::carbon::portal::profile_sync::SetActiveProfileCommand* mutable_commands(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand >*
      mutable_commands();
  private:
  const ::carbon::portal::profile_sync::SetActiveProfileCommand& _internal_commands(int index) const;
  ::carbon::portal::profile_sync::SetActiveProfileCommand* _internal_add_commands();
  public:
  const ::carbon::portal::profile_sync::SetActiveProfileCommand& commands(int index) const;
  ::carbon::portal::profile_sync::SetActiveProfileCommand* add_commands();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand >&
      commands() const;

  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand > commands_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// -------------------------------------------------------------------

class PurgeSetActiveProfileCommandsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest) */ {
 public:
  inline PurgeSetActiveProfileCommandsRequest() : PurgeSetActiveProfileCommandsRequest(nullptr) {}
  ~PurgeSetActiveProfileCommandsRequest() override;
  explicit constexpr PurgeSetActiveProfileCommandsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PurgeSetActiveProfileCommandsRequest(const PurgeSetActiveProfileCommandsRequest& from);
  PurgeSetActiveProfileCommandsRequest(PurgeSetActiveProfileCommandsRequest&& from) noexcept
    : PurgeSetActiveProfileCommandsRequest() {
    *this = ::std::move(from);
  }

  inline PurgeSetActiveProfileCommandsRequest& operator=(const PurgeSetActiveProfileCommandsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PurgeSetActiveProfileCommandsRequest& operator=(PurgeSetActiveProfileCommandsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PurgeSetActiveProfileCommandsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PurgeSetActiveProfileCommandsRequest* internal_default_instance() {
    return reinterpret_cast<const PurgeSetActiveProfileCommandsRequest*>(
               &_PurgeSetActiveProfileCommandsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(PurgeSetActiveProfileCommandsRequest& a, PurgeSetActiveProfileCommandsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PurgeSetActiveProfileCommandsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PurgeSetActiveProfileCommandsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PurgeSetActiveProfileCommandsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PurgeSetActiveProfileCommandsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PurgeSetActiveProfileCommandsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PurgeSetActiveProfileCommandsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PurgeSetActiveProfileCommandsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest";
  }
  protected:
  explicit PurgeSetActiveProfileCommandsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCommandsFieldNumber = 2,
    kRobotFieldNumber = 1,
  };
  // repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 2;
  int commands_size() const;
  private:
  int _internal_commands_size() const;
  public:
  void clear_commands();
  ::carbon::portal::profile_sync::SetActiveProfileCommand* mutable_commands(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand >*
      mutable_commands();
  private:
  const ::carbon::portal::profile_sync::SetActiveProfileCommand& _internal_commands(int index) const;
  ::carbon::portal::profile_sync::SetActiveProfileCommand* _internal_add_commands();
  public:
  const ::carbon::portal::profile_sync::SetActiveProfileCommand& commands(int index) const;
  ::carbon::portal::profile_sync::SetActiveProfileCommand* add_commands();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand >&
      commands() const;

  // string robot = 1;
  void clear_robot();
  const std::string& robot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot();
  PROTOBUF_NODISCARD std::string* release_robot();
  void set_allocated_robot(std::string* robot);
  private:
  const std::string& _internal_robot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot(const std::string& value);
  std::string* _internal_mutable_robot();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand > commands_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetProfilesDataRequest

// string robot_name = 1;
inline void GetProfilesDataRequest::clear_robot_name() {
  robot_name_.ClearToEmpty();
}
inline const std::string& GetProfilesDataRequest::robot_name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetProfilesDataRequest.robot_name)
  return _internal_robot_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetProfilesDataRequest::set_robot_name(ArgT0&& arg0, ArgT... args) {
 
 robot_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.GetProfilesDataRequest.robot_name)
}
inline std::string* GetProfilesDataRequest::mutable_robot_name() {
  std::string* _s = _internal_mutable_robot_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetProfilesDataRequest.robot_name)
  return _s;
}
inline const std::string& GetProfilesDataRequest::_internal_robot_name() const {
  return robot_name_.Get();
}
inline void GetProfilesDataRequest::_internal_set_robot_name(const std::string& value) {
  
  robot_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetProfilesDataRequest::_internal_mutable_robot_name() {
  
  return robot_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetProfilesDataRequest::release_robot_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.GetProfilesDataRequest.robot_name)
  return robot_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetProfilesDataRequest::set_allocated_robot_name(std::string* robot_name) {
  if (robot_name != nullptr) {
    
  } else {
    
  }
  robot_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.GetProfilesDataRequest.robot_name)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetProfilesDataResponse

// map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
inline int GetProfilesDataResponse::_internal_profiles_size() const {
  return profiles_.size();
}
inline int GetProfilesDataResponse::profiles_size() const {
  return _internal_profiles_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >&
GetProfilesDataResponse::_internal_profiles() const {
  return profiles_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >&
GetProfilesDataResponse::profiles() const {
  // @@protoc_insertion_point(field_map:carbon.portal.profile_sync.GetProfilesDataResponse.profiles)
  return _internal_profiles();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >*
GetProfilesDataResponse::_internal_mutable_profiles() {
  return profiles_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >*
GetProfilesDataResponse::mutable_profiles() {
  // @@protoc_insertion_point(field_mutable_map:carbon.portal.profile_sync.GetProfilesDataResponse.profiles)
  return _internal_mutable_profiles();
}

// -------------------------------------------------------------------

// UploadProfileRequest

// int64 last_update_time_ms = 1;
inline void UploadProfileRequest::clear_last_update_time_ms() {
  last_update_time_ms_ = int64_t{0};
}
inline int64_t UploadProfileRequest::_internal_last_update_time_ms() const {
  return last_update_time_ms_;
}
inline int64_t UploadProfileRequest::last_update_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.UploadProfileRequest.last_update_time_ms)
  return _internal_last_update_time_ms();
}
inline void UploadProfileRequest::_internal_set_last_update_time_ms(int64_t value) {
  
  last_update_time_ms_ = value;
}
inline void UploadProfileRequest::set_last_update_time_ms(int64_t value) {
  _internal_set_last_update_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.UploadProfileRequest.last_update_time_ms)
}

// string robot_name = 2;
inline void UploadProfileRequest::clear_robot_name() {
  robot_name_.ClearToEmpty();
}
inline const std::string& UploadProfileRequest::robot_name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.UploadProfileRequest.robot_name)
  return _internal_robot_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UploadProfileRequest::set_robot_name(ArgT0&& arg0, ArgT... args) {
 
 robot_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.UploadProfileRequest.robot_name)
}
inline std::string* UploadProfileRequest::mutable_robot_name() {
  std::string* _s = _internal_mutable_robot_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.UploadProfileRequest.robot_name)
  return _s;
}
inline const std::string& UploadProfileRequest::_internal_robot_name() const {
  return robot_name_.Get();
}
inline void UploadProfileRequest::_internal_set_robot_name(const std::string& value) {
  
  robot_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UploadProfileRequest::_internal_mutable_robot_name() {
  
  return robot_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UploadProfileRequest::release_robot_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.UploadProfileRequest.robot_name)
  return robot_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UploadProfileRequest::set_allocated_robot_name(std::string* robot_name) {
  if (robot_name != nullptr) {
    
  } else {
    
  }
  robot_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.robot_name)
}

// .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
inline bool UploadProfileRequest::_internal_has_almanac() const {
  return profile_case() == kAlmanac;
}
inline bool UploadProfileRequest::has_almanac() const {
  return _internal_has_almanac();
}
inline void UploadProfileRequest::set_has_almanac() {
  _oneof_case_[0] = kAlmanac;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* UploadProfileRequest::release_almanac() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.UploadProfileRequest.almanac)
  if (_internal_has_almanac()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::AlmanacConfig* temp = profile_.almanac_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.almanac_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& UploadProfileRequest::_internal_almanac() const {
  return _internal_has_almanac()
      ? *profile_.almanac_
      : reinterpret_cast< ::carbon::aimbot::almanac::AlmanacConfig&>(::carbon::aimbot::almanac::_AlmanacConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& UploadProfileRequest::almanac() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.UploadProfileRequest.almanac)
  return _internal_almanac();
}
inline ::carbon::aimbot::almanac::AlmanacConfig* UploadProfileRequest::unsafe_arena_release_almanac() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.UploadProfileRequest.almanac)
  if (_internal_has_almanac()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::AlmanacConfig* temp = profile_.almanac_;
    profile_.almanac_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  clear_profile();
  if (almanac) {
    set_has_almanac();
    profile_.almanac_ = almanac;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.almanac)
}
inline ::carbon::aimbot::almanac::AlmanacConfig* UploadProfileRequest::_internal_mutable_almanac() {
  if (!_internal_has_almanac()) {
    clear_profile();
    set_has_almanac();
    profile_.almanac_ = CreateMaybeMessage< ::carbon::aimbot::almanac::AlmanacConfig >(GetArenaForAllocation());
  }
  return profile_.almanac_;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* UploadProfileRequest::mutable_almanac() {
  ::carbon::aimbot::almanac::AlmanacConfig* _msg = _internal_mutable_almanac();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.UploadProfileRequest.almanac)
  return _msg;
}

// .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
inline bool UploadProfileRequest::_internal_has_discriminator() const {
  return profile_case() == kDiscriminator;
}
inline bool UploadProfileRequest::has_discriminator() const {
  return _internal_has_discriminator();
}
inline void UploadProfileRequest::set_has_discriminator() {
  _oneof_case_[0] = kDiscriminator;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* UploadProfileRequest::release_discriminator() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.UploadProfileRequest.discriminator)
  if (_internal_has_discriminator()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::DiscriminatorConfig* temp = profile_.discriminator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.discriminator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& UploadProfileRequest::_internal_discriminator() const {
  return _internal_has_discriminator()
      ? *profile_.discriminator_
      : reinterpret_cast< ::carbon::aimbot::almanac::DiscriminatorConfig&>(::carbon::aimbot::almanac::_DiscriminatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& UploadProfileRequest::discriminator() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.UploadProfileRequest.discriminator)
  return _internal_discriminator();
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* UploadProfileRequest::unsafe_arena_release_discriminator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.UploadProfileRequest.discriminator)
  if (_internal_has_discriminator()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::DiscriminatorConfig* temp = profile_.discriminator_;
    profile_.discriminator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator) {
  clear_profile();
  if (discriminator) {
    set_has_discriminator();
    profile_.discriminator_ = discriminator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.discriminator)
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* UploadProfileRequest::_internal_mutable_discriminator() {
  if (!_internal_has_discriminator()) {
    clear_profile();
    set_has_discriminator();
    profile_.discriminator_ = CreateMaybeMessage< ::carbon::aimbot::almanac::DiscriminatorConfig >(GetArenaForAllocation());
  }
  return profile_.discriminator_;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* UploadProfileRequest::mutable_discriminator() {
  ::carbon::aimbot::almanac::DiscriminatorConfig* _msg = _internal_mutable_discriminator();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.UploadProfileRequest.discriminator)
  return _msg;
}

// .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
inline bool UploadProfileRequest::_internal_has_modelinator() const {
  return profile_case() == kModelinator;
}
inline bool UploadProfileRequest::has_modelinator() const {
  return _internal_has_modelinator();
}
inline void UploadProfileRequest::set_has_modelinator() {
  _oneof_case_[0] = kModelinator;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* UploadProfileRequest::release_modelinator() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.UploadProfileRequest.modelinator)
  if (_internal_has_modelinator()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::ModelinatorConfig* temp = profile_.modelinator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.modelinator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& UploadProfileRequest::_internal_modelinator() const {
  return _internal_has_modelinator()
      ? *profile_.modelinator_
      : reinterpret_cast< ::carbon::aimbot::almanac::ModelinatorConfig&>(::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& UploadProfileRequest::modelinator() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.UploadProfileRequest.modelinator)
  return _internal_modelinator();
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* UploadProfileRequest::unsafe_arena_release_modelinator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.UploadProfileRequest.modelinator)
  if (_internal_has_modelinator()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::ModelinatorConfig* temp = profile_.modelinator_;
    profile_.modelinator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator) {
  clear_profile();
  if (modelinator) {
    set_has_modelinator();
    profile_.modelinator_ = modelinator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.modelinator)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* UploadProfileRequest::_internal_mutable_modelinator() {
  if (!_internal_has_modelinator()) {
    clear_profile();
    set_has_modelinator();
    profile_.modelinator_ = CreateMaybeMessage< ::carbon::aimbot::almanac::ModelinatorConfig >(GetArenaForAllocation());
  }
  return profile_.modelinator_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* UploadProfileRequest::mutable_modelinator() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_modelinator();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.UploadProfileRequest.modelinator)
  return _msg;
}

// .carbon.frontend.banding.BandingDef banding = 6;
inline bool UploadProfileRequest::_internal_has_banding() const {
  return profile_case() == kBanding;
}
inline bool UploadProfileRequest::has_banding() const {
  return _internal_has_banding();
}
inline void UploadProfileRequest::set_has_banding() {
  _oneof_case_[0] = kBanding;
}
inline ::carbon::frontend::banding::BandingDef* UploadProfileRequest::release_banding() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.UploadProfileRequest.banding)
  if (_internal_has_banding()) {
    clear_has_profile();
      ::carbon::frontend::banding::BandingDef* temp = profile_.banding_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.banding_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::banding::BandingDef& UploadProfileRequest::_internal_banding() const {
  return _internal_has_banding()
      ? *profile_.banding_
      : reinterpret_cast< ::carbon::frontend::banding::BandingDef&>(::carbon::frontend::banding::_BandingDef_default_instance_);
}
inline const ::carbon::frontend::banding::BandingDef& UploadProfileRequest::banding() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.UploadProfileRequest.banding)
  return _internal_banding();
}
inline ::carbon::frontend::banding::BandingDef* UploadProfileRequest::unsafe_arena_release_banding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.UploadProfileRequest.banding)
  if (_internal_has_banding()) {
    clear_has_profile();
    ::carbon::frontend::banding::BandingDef* temp = profile_.banding_;
    profile_.banding_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_banding(::carbon::frontend::banding::BandingDef* banding) {
  clear_profile();
  if (banding) {
    set_has_banding();
    profile_.banding_ = banding;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.banding)
}
inline ::carbon::frontend::banding::BandingDef* UploadProfileRequest::_internal_mutable_banding() {
  if (!_internal_has_banding()) {
    clear_profile();
    set_has_banding();
    profile_.banding_ = CreateMaybeMessage< ::carbon::frontend::banding::BandingDef >(GetArenaForAllocation());
  }
  return profile_.banding_;
}
inline ::carbon::frontend::banding::BandingDef* UploadProfileRequest::mutable_banding() {
  ::carbon::frontend::banding::BandingDef* _msg = _internal_mutable_banding();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.UploadProfileRequest.banding)
  return _msg;
}

// .carbon.thinning.ConfigDefinition thinning = 7;
inline bool UploadProfileRequest::_internal_has_thinning() const {
  return profile_case() == kThinning;
}
inline bool UploadProfileRequest::has_thinning() const {
  return _internal_has_thinning();
}
inline void UploadProfileRequest::set_has_thinning() {
  _oneof_case_[0] = kThinning;
}
inline ::carbon::thinning::ConfigDefinition* UploadProfileRequest::release_thinning() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.UploadProfileRequest.thinning)
  if (_internal_has_thinning()) {
    clear_has_profile();
      ::carbon::thinning::ConfigDefinition* temp = profile_.thinning_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.thinning_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::thinning::ConfigDefinition& UploadProfileRequest::_internal_thinning() const {
  return _internal_has_thinning()
      ? *profile_.thinning_
      : reinterpret_cast< ::carbon::thinning::ConfigDefinition&>(::carbon::thinning::_ConfigDefinition_default_instance_);
}
inline const ::carbon::thinning::ConfigDefinition& UploadProfileRequest::thinning() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.UploadProfileRequest.thinning)
  return _internal_thinning();
}
inline ::carbon::thinning::ConfigDefinition* UploadProfileRequest::unsafe_arena_release_thinning() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.UploadProfileRequest.thinning)
  if (_internal_has_thinning()) {
    clear_has_profile();
    ::carbon::thinning::ConfigDefinition* temp = profile_.thinning_;
    profile_.thinning_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning) {
  clear_profile();
  if (thinning) {
    set_has_thinning();
    profile_.thinning_ = thinning;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.thinning)
}
inline ::carbon::thinning::ConfigDefinition* UploadProfileRequest::_internal_mutable_thinning() {
  if (!_internal_has_thinning()) {
    clear_profile();
    set_has_thinning();
    profile_.thinning_ = CreateMaybeMessage< ::carbon::thinning::ConfigDefinition >(GetArenaForAllocation());
  }
  return profile_.thinning_;
}
inline ::carbon::thinning::ConfigDefinition* UploadProfileRequest::mutable_thinning() {
  ::carbon::thinning::ConfigDefinition* _msg = _internal_mutable_thinning();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.UploadProfileRequest.thinning)
  return _msg;
}

// .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 8;
inline bool UploadProfileRequest::_internal_has_target_vel() const {
  return profile_case() == kTargetVel;
}
inline bool UploadProfileRequest::has_target_vel() const {
  return _internal_has_target_vel();
}
inline void UploadProfileRequest::set_has_target_vel() {
  _oneof_case_[0] = kTargetVel;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* UploadProfileRequest::release_target_vel() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.UploadProfileRequest.target_vel)
  if (_internal_has_target_vel()) {
    clear_has_profile();
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_.target_vel_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.target_vel_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& UploadProfileRequest::_internal_target_vel() const {
  return _internal_has_target_vel()
      ? *profile_.target_vel_
      : reinterpret_cast< ::carbon::aimbot::target_velocity_estimator::TVEProfile&>(::carbon::aimbot::target_velocity_estimator::_TVEProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& UploadProfileRequest::target_vel() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.UploadProfileRequest.target_vel)
  return _internal_target_vel();
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* UploadProfileRequest::unsafe_arena_release_target_vel() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.UploadProfileRequest.target_vel)
  if (_internal_has_target_vel()) {
    clear_has_profile();
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_.target_vel_;
    profile_.target_vel_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_target_vel(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel) {
  clear_profile();
  if (target_vel) {
    set_has_target_vel();
    profile_.target_vel_ = target_vel;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.target_vel)
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* UploadProfileRequest::_internal_mutable_target_vel() {
  if (!_internal_has_target_vel()) {
    clear_profile();
    set_has_target_vel();
    profile_.target_vel_ = CreateMaybeMessage< ::carbon::aimbot::target_velocity_estimator::TVEProfile >(GetArenaForAllocation());
  }
  return profile_.target_vel_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* UploadProfileRequest::mutable_target_vel() {
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _msg = _internal_mutable_target_vel();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.UploadProfileRequest.target_vel)
  return _msg;
}

inline bool UploadProfileRequest::has_profile() const {
  return profile_case() != PROFILE_NOT_SET;
}
inline void UploadProfileRequest::clear_has_profile() {
  _oneof_case_[0] = PROFILE_NOT_SET;
}
inline UploadProfileRequest::ProfileCase UploadProfileRequest::profile_case() const {
  return UploadProfileRequest::ProfileCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// GetProfileRequest

// string uuid = 1;
inline void GetProfileRequest::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& GetProfileRequest::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetProfileRequest.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetProfileRequest::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.GetProfileRequest.uuid)
}
inline std::string* GetProfileRequest::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetProfileRequest.uuid)
  return _s;
}
inline const std::string& GetProfileRequest::_internal_uuid() const {
  return uuid_.Get();
}
inline void GetProfileRequest::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetProfileRequest::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetProfileRequest::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.GetProfileRequest.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetProfileRequest::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.GetProfileRequest.uuid)
}

// -------------------------------------------------------------------

// GetProfileResponse

// .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
inline bool GetProfileResponse::_internal_has_almanac() const {
  return profile_case() == kAlmanac;
}
inline bool GetProfileResponse::has_almanac() const {
  return _internal_has_almanac();
}
inline void GetProfileResponse::set_has_almanac() {
  _oneof_case_[0] = kAlmanac;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* GetProfileResponse::release_almanac() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.GetProfileResponse.almanac)
  if (_internal_has_almanac()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::AlmanacConfig* temp = profile_.almanac_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.almanac_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& GetProfileResponse::_internal_almanac() const {
  return _internal_has_almanac()
      ? *profile_.almanac_
      : reinterpret_cast< ::carbon::aimbot::almanac::AlmanacConfig&>(::carbon::aimbot::almanac::_AlmanacConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& GetProfileResponse::almanac() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetProfileResponse.almanac)
  return _internal_almanac();
}
inline ::carbon::aimbot::almanac::AlmanacConfig* GetProfileResponse::unsafe_arena_release_almanac() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.GetProfileResponse.almanac)
  if (_internal_has_almanac()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::AlmanacConfig* temp = profile_.almanac_;
    profile_.almanac_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  clear_profile();
  if (almanac) {
    set_has_almanac();
    profile_.almanac_ = almanac;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.GetProfileResponse.almanac)
}
inline ::carbon::aimbot::almanac::AlmanacConfig* GetProfileResponse::_internal_mutable_almanac() {
  if (!_internal_has_almanac()) {
    clear_profile();
    set_has_almanac();
    profile_.almanac_ = CreateMaybeMessage< ::carbon::aimbot::almanac::AlmanacConfig >(GetArenaForAllocation());
  }
  return profile_.almanac_;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* GetProfileResponse::mutable_almanac() {
  ::carbon::aimbot::almanac::AlmanacConfig* _msg = _internal_mutable_almanac();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetProfileResponse.almanac)
  return _msg;
}

// .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
inline bool GetProfileResponse::_internal_has_discriminator() const {
  return profile_case() == kDiscriminator;
}
inline bool GetProfileResponse::has_discriminator() const {
  return _internal_has_discriminator();
}
inline void GetProfileResponse::set_has_discriminator() {
  _oneof_case_[0] = kDiscriminator;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* GetProfileResponse::release_discriminator() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.GetProfileResponse.discriminator)
  if (_internal_has_discriminator()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::DiscriminatorConfig* temp = profile_.discriminator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.discriminator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& GetProfileResponse::_internal_discriminator() const {
  return _internal_has_discriminator()
      ? *profile_.discriminator_
      : reinterpret_cast< ::carbon::aimbot::almanac::DiscriminatorConfig&>(::carbon::aimbot::almanac::_DiscriminatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& GetProfileResponse::discriminator() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetProfileResponse.discriminator)
  return _internal_discriminator();
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* GetProfileResponse::unsafe_arena_release_discriminator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.GetProfileResponse.discriminator)
  if (_internal_has_discriminator()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::DiscriminatorConfig* temp = profile_.discriminator_;
    profile_.discriminator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator) {
  clear_profile();
  if (discriminator) {
    set_has_discriminator();
    profile_.discriminator_ = discriminator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.GetProfileResponse.discriminator)
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* GetProfileResponse::_internal_mutable_discriminator() {
  if (!_internal_has_discriminator()) {
    clear_profile();
    set_has_discriminator();
    profile_.discriminator_ = CreateMaybeMessage< ::carbon::aimbot::almanac::DiscriminatorConfig >(GetArenaForAllocation());
  }
  return profile_.discriminator_;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* GetProfileResponse::mutable_discriminator() {
  ::carbon::aimbot::almanac::DiscriminatorConfig* _msg = _internal_mutable_discriminator();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetProfileResponse.discriminator)
  return _msg;
}

// .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
inline bool GetProfileResponse::_internal_has_modelinator() const {
  return profile_case() == kModelinator;
}
inline bool GetProfileResponse::has_modelinator() const {
  return _internal_has_modelinator();
}
inline void GetProfileResponse::set_has_modelinator() {
  _oneof_case_[0] = kModelinator;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetProfileResponse::release_modelinator() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.GetProfileResponse.modelinator)
  if (_internal_has_modelinator()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::ModelinatorConfig* temp = profile_.modelinator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.modelinator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& GetProfileResponse::_internal_modelinator() const {
  return _internal_has_modelinator()
      ? *profile_.modelinator_
      : reinterpret_cast< ::carbon::aimbot::almanac::ModelinatorConfig&>(::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& GetProfileResponse::modelinator() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetProfileResponse.modelinator)
  return _internal_modelinator();
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetProfileResponse::unsafe_arena_release_modelinator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.GetProfileResponse.modelinator)
  if (_internal_has_modelinator()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::ModelinatorConfig* temp = profile_.modelinator_;
    profile_.modelinator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator) {
  clear_profile();
  if (modelinator) {
    set_has_modelinator();
    profile_.modelinator_ = modelinator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.GetProfileResponse.modelinator)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetProfileResponse::_internal_mutable_modelinator() {
  if (!_internal_has_modelinator()) {
    clear_profile();
    set_has_modelinator();
    profile_.modelinator_ = CreateMaybeMessage< ::carbon::aimbot::almanac::ModelinatorConfig >(GetArenaForAllocation());
  }
  return profile_.modelinator_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetProfileResponse::mutable_modelinator() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_modelinator();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetProfileResponse.modelinator)
  return _msg;
}

// .carbon.frontend.banding.BandingDef banding = 4;
inline bool GetProfileResponse::_internal_has_banding() const {
  return profile_case() == kBanding;
}
inline bool GetProfileResponse::has_banding() const {
  return _internal_has_banding();
}
inline void GetProfileResponse::set_has_banding() {
  _oneof_case_[0] = kBanding;
}
inline ::carbon::frontend::banding::BandingDef* GetProfileResponse::release_banding() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.GetProfileResponse.banding)
  if (_internal_has_banding()) {
    clear_has_profile();
      ::carbon::frontend::banding::BandingDef* temp = profile_.banding_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.banding_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::banding::BandingDef& GetProfileResponse::_internal_banding() const {
  return _internal_has_banding()
      ? *profile_.banding_
      : reinterpret_cast< ::carbon::frontend::banding::BandingDef&>(::carbon::frontend::banding::_BandingDef_default_instance_);
}
inline const ::carbon::frontend::banding::BandingDef& GetProfileResponse::banding() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetProfileResponse.banding)
  return _internal_banding();
}
inline ::carbon::frontend::banding::BandingDef* GetProfileResponse::unsafe_arena_release_banding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.GetProfileResponse.banding)
  if (_internal_has_banding()) {
    clear_has_profile();
    ::carbon::frontend::banding::BandingDef* temp = profile_.banding_;
    profile_.banding_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_banding(::carbon::frontend::banding::BandingDef* banding) {
  clear_profile();
  if (banding) {
    set_has_banding();
    profile_.banding_ = banding;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.GetProfileResponse.banding)
}
inline ::carbon::frontend::banding::BandingDef* GetProfileResponse::_internal_mutable_banding() {
  if (!_internal_has_banding()) {
    clear_profile();
    set_has_banding();
    profile_.banding_ = CreateMaybeMessage< ::carbon::frontend::banding::BandingDef >(GetArenaForAllocation());
  }
  return profile_.banding_;
}
inline ::carbon::frontend::banding::BandingDef* GetProfileResponse::mutable_banding() {
  ::carbon::frontend::banding::BandingDef* _msg = _internal_mutable_banding();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetProfileResponse.banding)
  return _msg;
}

// .carbon.thinning.ConfigDefinition thinning = 5;
inline bool GetProfileResponse::_internal_has_thinning() const {
  return profile_case() == kThinning;
}
inline bool GetProfileResponse::has_thinning() const {
  return _internal_has_thinning();
}
inline void GetProfileResponse::set_has_thinning() {
  _oneof_case_[0] = kThinning;
}
inline ::carbon::thinning::ConfigDefinition* GetProfileResponse::release_thinning() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.GetProfileResponse.thinning)
  if (_internal_has_thinning()) {
    clear_has_profile();
      ::carbon::thinning::ConfigDefinition* temp = profile_.thinning_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.thinning_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::thinning::ConfigDefinition& GetProfileResponse::_internal_thinning() const {
  return _internal_has_thinning()
      ? *profile_.thinning_
      : reinterpret_cast< ::carbon::thinning::ConfigDefinition&>(::carbon::thinning::_ConfigDefinition_default_instance_);
}
inline const ::carbon::thinning::ConfigDefinition& GetProfileResponse::thinning() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetProfileResponse.thinning)
  return _internal_thinning();
}
inline ::carbon::thinning::ConfigDefinition* GetProfileResponse::unsafe_arena_release_thinning() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.GetProfileResponse.thinning)
  if (_internal_has_thinning()) {
    clear_has_profile();
    ::carbon::thinning::ConfigDefinition* temp = profile_.thinning_;
    profile_.thinning_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning) {
  clear_profile();
  if (thinning) {
    set_has_thinning();
    profile_.thinning_ = thinning;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.GetProfileResponse.thinning)
}
inline ::carbon::thinning::ConfigDefinition* GetProfileResponse::_internal_mutable_thinning() {
  if (!_internal_has_thinning()) {
    clear_profile();
    set_has_thinning();
    profile_.thinning_ = CreateMaybeMessage< ::carbon::thinning::ConfigDefinition >(GetArenaForAllocation());
  }
  return profile_.thinning_;
}
inline ::carbon::thinning::ConfigDefinition* GetProfileResponse::mutable_thinning() {
  ::carbon::thinning::ConfigDefinition* _msg = _internal_mutable_thinning();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetProfileResponse.thinning)
  return _msg;
}

// .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 6;
inline bool GetProfileResponse::_internal_has_target_vel() const {
  return profile_case() == kTargetVel;
}
inline bool GetProfileResponse::has_target_vel() const {
  return _internal_has_target_vel();
}
inline void GetProfileResponse::set_has_target_vel() {
  _oneof_case_[0] = kTargetVel;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetProfileResponse::release_target_vel() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.GetProfileResponse.target_vel)
  if (_internal_has_target_vel()) {
    clear_has_profile();
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_.target_vel_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.target_vel_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& GetProfileResponse::_internal_target_vel() const {
  return _internal_has_target_vel()
      ? *profile_.target_vel_
      : reinterpret_cast< ::carbon::aimbot::target_velocity_estimator::TVEProfile&>(::carbon::aimbot::target_velocity_estimator::_TVEProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& GetProfileResponse::target_vel() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetProfileResponse.target_vel)
  return _internal_target_vel();
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetProfileResponse::unsafe_arena_release_target_vel() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.profile_sync.GetProfileResponse.target_vel)
  if (_internal_has_target_vel()) {
    clear_has_profile();
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_.target_vel_;
    profile_.target_vel_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_target_vel(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel) {
  clear_profile();
  if (target_vel) {
    set_has_target_vel();
    profile_.target_vel_ = target_vel;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.profile_sync.GetProfileResponse.target_vel)
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetProfileResponse::_internal_mutable_target_vel() {
  if (!_internal_has_target_vel()) {
    clear_profile();
    set_has_target_vel();
    profile_.target_vel_ = CreateMaybeMessage< ::carbon::aimbot::target_velocity_estimator::TVEProfile >(GetArenaForAllocation());
  }
  return profile_.target_vel_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetProfileResponse::mutable_target_vel() {
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _msg = _internal_mutable_target_vel();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetProfileResponse.target_vel)
  return _msg;
}

inline bool GetProfileResponse::has_profile() const {
  return profile_case() != PROFILE_NOT_SET;
}
inline void GetProfileResponse::clear_has_profile() {
  _oneof_case_[0] = PROFILE_NOT_SET;
}
inline GetProfileResponse::ProfileCase GetProfileResponse::profile_case() const {
  return GetProfileResponse::ProfileCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// DeleteProfileRequest

// string uuid = 1;
inline void DeleteProfileRequest::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& DeleteProfileRequest::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.DeleteProfileRequest.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteProfileRequest::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.DeleteProfileRequest.uuid)
}
inline std::string* DeleteProfileRequest::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.DeleteProfileRequest.uuid)
  return _s;
}
inline const std::string& DeleteProfileRequest::_internal_uuid() const {
  return uuid_.Get();
}
inline void DeleteProfileRequest::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteProfileRequest::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteProfileRequest::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.DeleteProfileRequest.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteProfileRequest::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.DeleteProfileRequest.uuid)
}

// -------------------------------------------------------------------

// PurgeProfileRequest

// string uuid = 1;
inline void PurgeProfileRequest::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& PurgeProfileRequest::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.PurgeProfileRequest.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PurgeProfileRequest::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.PurgeProfileRequest.uuid)
}
inline std::string* PurgeProfileRequest::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.PurgeProfileRequest.uuid)
  return _s;
}
inline const std::string& PurgeProfileRequest::_internal_uuid() const {
  return uuid_.Get();
}
inline void PurgeProfileRequest::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PurgeProfileRequest::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PurgeProfileRequest::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.PurgeProfileRequest.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PurgeProfileRequest::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.PurgeProfileRequest.uuid)
}

// -------------------------------------------------------------------

// SetActiveProfileCommand

// .carbon.frontend.profile_sync.ProfileType profile_type = 1;
inline void SetActiveProfileCommand::clear_profile_type() {
  profile_type_ = 0;
}
inline ::carbon::frontend::profile_sync::ProfileType SetActiveProfileCommand::_internal_profile_type() const {
  return static_cast< ::carbon::frontend::profile_sync::ProfileType >(profile_type_);
}
inline ::carbon::frontend::profile_sync::ProfileType SetActiveProfileCommand::profile_type() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.SetActiveProfileCommand.profile_type)
  return _internal_profile_type();
}
inline void SetActiveProfileCommand::_internal_set_profile_type(::carbon::frontend::profile_sync::ProfileType value) {
  
  profile_type_ = value;
}
inline void SetActiveProfileCommand::set_profile_type(::carbon::frontend::profile_sync::ProfileType value) {
  _internal_set_profile_type(value);
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.SetActiveProfileCommand.profile_type)
}

// string uuid = 2;
inline void SetActiveProfileCommand::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& SetActiveProfileCommand::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.SetActiveProfileCommand.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveProfileCommand::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.SetActiveProfileCommand.uuid)
}
inline std::string* SetActiveProfileCommand::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.SetActiveProfileCommand.uuid)
  return _s;
}
inline const std::string& SetActiveProfileCommand::_internal_uuid() const {
  return uuid_.Get();
}
inline void SetActiveProfileCommand::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveProfileCommand::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveProfileCommand::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.SetActiveProfileCommand.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetActiveProfileCommand::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.SetActiveProfileCommand.uuid)
}

// -------------------------------------------------------------------

// GetSetActiveProfileCommandsRequest

// string robot = 1;
inline void GetSetActiveProfileCommandsRequest::clear_robot() {
  robot_.ClearToEmpty();
}
inline const std::string& GetSetActiveProfileCommandsRequest::robot() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest.robot)
  return _internal_robot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetSetActiveProfileCommandsRequest::set_robot(ArgT0&& arg0, ArgT... args) {
 
 robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest.robot)
}
inline std::string* GetSetActiveProfileCommandsRequest::mutable_robot() {
  std::string* _s = _internal_mutable_robot();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest.robot)
  return _s;
}
inline const std::string& GetSetActiveProfileCommandsRequest::_internal_robot() const {
  return robot_.Get();
}
inline void GetSetActiveProfileCommandsRequest::_internal_set_robot(const std::string& value) {
  
  robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetSetActiveProfileCommandsRequest::_internal_mutable_robot() {
  
  return robot_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetSetActiveProfileCommandsRequest::release_robot() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest.robot)
  return robot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetSetActiveProfileCommandsRequest::set_allocated_robot(std::string* robot) {
  if (robot != nullptr) {
    
  } else {
    
  }
  robot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest.robot)
}

// -------------------------------------------------------------------

// GetSetActiveProfileCommandsResponse

// repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 1;
inline int GetSetActiveProfileCommandsResponse::_internal_commands_size() const {
  return commands_.size();
}
inline int GetSetActiveProfileCommandsResponse::commands_size() const {
  return _internal_commands_size();
}
inline void GetSetActiveProfileCommandsResponse::clear_commands() {
  commands_.Clear();
}
inline ::carbon::portal::profile_sync::SetActiveProfileCommand* GetSetActiveProfileCommandsResponse::mutable_commands(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse.commands)
  return commands_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand >*
GetSetActiveProfileCommandsResponse::mutable_commands() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse.commands)
  return &commands_;
}
inline const ::carbon::portal::profile_sync::SetActiveProfileCommand& GetSetActiveProfileCommandsResponse::_internal_commands(int index) const {
  return commands_.Get(index);
}
inline const ::carbon::portal::profile_sync::SetActiveProfileCommand& GetSetActiveProfileCommandsResponse::commands(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse.commands)
  return _internal_commands(index);
}
inline ::carbon::portal::profile_sync::SetActiveProfileCommand* GetSetActiveProfileCommandsResponse::_internal_add_commands() {
  return commands_.Add();
}
inline ::carbon::portal::profile_sync::SetActiveProfileCommand* GetSetActiveProfileCommandsResponse::add_commands() {
  ::carbon::portal::profile_sync::SetActiveProfileCommand* _add = _internal_add_commands();
  // @@protoc_insertion_point(field_add:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse.commands)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand >&
GetSetActiveProfileCommandsResponse::commands() const {
  // @@protoc_insertion_point(field_list:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse.commands)
  return commands_;
}

// -------------------------------------------------------------------

// PurgeSetActiveProfileCommandsRequest

// string robot = 1;
inline void PurgeSetActiveProfileCommandsRequest::clear_robot() {
  robot_.ClearToEmpty();
}
inline const std::string& PurgeSetActiveProfileCommandsRequest::robot() const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.robot)
  return _internal_robot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PurgeSetActiveProfileCommandsRequest::set_robot(ArgT0&& arg0, ArgT... args) {
 
 robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.robot)
}
inline std::string* PurgeSetActiveProfileCommandsRequest::mutable_robot() {
  std::string* _s = _internal_mutable_robot();
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.robot)
  return _s;
}
inline const std::string& PurgeSetActiveProfileCommandsRequest::_internal_robot() const {
  return robot_.Get();
}
inline void PurgeSetActiveProfileCommandsRequest::_internal_set_robot(const std::string& value) {
  
  robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PurgeSetActiveProfileCommandsRequest::_internal_mutable_robot() {
  
  return robot_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PurgeSetActiveProfileCommandsRequest::release_robot() {
  // @@protoc_insertion_point(field_release:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.robot)
  return robot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PurgeSetActiveProfileCommandsRequest::set_allocated_robot(std::string* robot) {
  if (robot != nullptr) {
    
  } else {
    
  }
  robot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.robot)
}

// repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 2;
inline int PurgeSetActiveProfileCommandsRequest::_internal_commands_size() const {
  return commands_.size();
}
inline int PurgeSetActiveProfileCommandsRequest::commands_size() const {
  return _internal_commands_size();
}
inline void PurgeSetActiveProfileCommandsRequest::clear_commands() {
  commands_.Clear();
}
inline ::carbon::portal::profile_sync::SetActiveProfileCommand* PurgeSetActiveProfileCommandsRequest::mutable_commands(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.commands)
  return commands_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand >*
PurgeSetActiveProfileCommandsRequest::mutable_commands() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.commands)
  return &commands_;
}
inline const ::carbon::portal::profile_sync::SetActiveProfileCommand& PurgeSetActiveProfileCommandsRequest::_internal_commands(int index) const {
  return commands_.Get(index);
}
inline const ::carbon::portal::profile_sync::SetActiveProfileCommand& PurgeSetActiveProfileCommandsRequest::commands(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.commands)
  return _internal_commands(index);
}
inline ::carbon::portal::profile_sync::SetActiveProfileCommand* PurgeSetActiveProfileCommandsRequest::_internal_add_commands() {
  return commands_.Add();
}
inline ::carbon::portal::profile_sync::SetActiveProfileCommand* PurgeSetActiveProfileCommandsRequest::add_commands() {
  ::carbon::portal::profile_sync::SetActiveProfileCommand* _add = _internal_add_commands();
  // @@protoc_insertion_point(field_add:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.commands)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::profile_sync::SetActiveProfileCommand >&
PurgeSetActiveProfileCommandsRequest::commands() const {
  // @@protoc_insertion_point(field_list:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.commands)
  return commands_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace profile_sync
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fprofile_5fsync_5fportal_2eproto

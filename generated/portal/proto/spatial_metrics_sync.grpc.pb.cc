// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/spatial_metrics_sync.proto

#include "portal/proto/spatial_metrics_sync.pb.h"
#include "portal/proto/spatial_metrics_sync.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace spatial_metrics {

static const char* SpatialMetricsSyncService_method_names[] = {
  "/carbon.portal.spatial_metrics.SpatialMetricsSyncService/SyncSpatialMetricBlocks",
};

std::unique_ptr< SpatialMetricsSyncService::Stub> SpatialMetricsSyncService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< SpatialMetricsSyncService::Stub> stub(new SpatialMetricsSyncService::Stub(channel, options));
  return stub;
}

SpatialMetricsSyncService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_SyncSpatialMetricBlocks_(SpatialMetricsSyncService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status SpatialMetricsSyncService::Stub::SyncSpatialMetricBlocks(::grpc::ClientContext* context, const ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SyncSpatialMetricBlocks_, context, request, response);
}

void SpatialMetricsSyncService::Stub::async::SyncSpatialMetricBlocks(::grpc::ClientContext* context, const ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SyncSpatialMetricBlocks_, context, request, response, std::move(f));
}

void SpatialMetricsSyncService::Stub::async::SyncSpatialMetricBlocks(::grpc::ClientContext* context, const ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SyncSpatialMetricBlocks_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* SpatialMetricsSyncService::Stub::PrepareAsyncSyncSpatialMetricBlocksRaw(::grpc::ClientContext* context, const ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SyncSpatialMetricBlocks_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* SpatialMetricsSyncService::Stub::AsyncSyncSpatialMetricBlocksRaw(::grpc::ClientContext* context, const ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSyncSpatialMetricBlocksRaw(context, request, cq);
  result->StartCall();
  return result;
}

SpatialMetricsSyncService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      SpatialMetricsSyncService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< SpatialMetricsSyncService::Service, ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](SpatialMetricsSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->SyncSpatialMetricBlocks(ctx, req, resp);
             }, this)));
}

SpatialMetricsSyncService::Service::~Service() {
}

::grpc::Status SpatialMetricsSyncService::Service::SyncSpatialMetricBlocks(::grpc::ServerContext* context, const ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace spatial_metrics


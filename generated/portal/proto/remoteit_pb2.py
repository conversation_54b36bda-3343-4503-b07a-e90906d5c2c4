# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/remoteit.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/remoteit.proto',
  package='carbon.portal.remoteit',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1bportal/proto/remoteit.proto\x12\x16\x63\x61rbon.portal.remoteit\"9\n\x10\x43onfigureRequest\x12\x11\n\tserviceId\x18\x01 \x01(\t\x12\x12\n\ndeviceName\x18\x02 \x01(\t\"!\n\x0f\x43onfigureResult\x12\x0e\n\x06status\x18\x01 \x01(\t2s\n\x0fRemoteItManager\x12`\n\tConfigure\x12(.carbon.portal.remoteit.ConfigureRequest\x1a\'.carbon.portal.remoteit.ConfigureResult\"\x00\x42\x0eZ\x0cproto/portalb\x06proto3'
)




_CONFIGUREREQUEST = _descriptor.Descriptor(
  name='ConfigureRequest',
  full_name='carbon.portal.remoteit.ConfigureRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='serviceId', full_name='carbon.portal.remoteit.ConfigureRequest.serviceId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deviceName', full_name='carbon.portal.remoteit.ConfigureRequest.deviceName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=55,
  serialized_end=112,
)


_CONFIGURERESULT = _descriptor.Descriptor(
  name='ConfigureResult',
  full_name='carbon.portal.remoteit.ConfigureResult',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='carbon.portal.remoteit.ConfigureResult.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=114,
  serialized_end=147,
)

DESCRIPTOR.message_types_by_name['ConfigureRequest'] = _CONFIGUREREQUEST
DESCRIPTOR.message_types_by_name['ConfigureResult'] = _CONFIGURERESULT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ConfigureRequest = _reflection.GeneratedProtocolMessageType('ConfigureRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONFIGUREREQUEST,
  '__module__' : 'portal.proto.remoteit_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.remoteit.ConfigureRequest)
  })
_sym_db.RegisterMessage(ConfigureRequest)

ConfigureResult = _reflection.GeneratedProtocolMessageType('ConfigureResult', (_message.Message,), {
  'DESCRIPTOR' : _CONFIGURERESULT,
  '__module__' : 'portal.proto.remoteit_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.remoteit.ConfigureResult)
  })
_sym_db.RegisterMessage(ConfigureResult)


DESCRIPTOR._options = None

_REMOTEITMANAGER = _descriptor.ServiceDescriptor(
  name='RemoteItManager',
  full_name='carbon.portal.remoteit.RemoteItManager',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=149,
  serialized_end=264,
  methods=[
  _descriptor.MethodDescriptor(
    name='Configure',
    full_name='carbon.portal.remoteit.RemoteItManager.Configure',
    index=0,
    containing_service=None,
    input_type=_CONFIGUREREQUEST,
    output_type=_CONFIGURERESULT,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_REMOTEITMANAGER)

DESCRIPTOR.services_by_name['RemoteItManager'] = _REMOTEITMANAGER

# @@protoc_insertion_point(module_scope)

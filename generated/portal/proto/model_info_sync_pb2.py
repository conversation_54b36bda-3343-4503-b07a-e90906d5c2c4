# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/model_info_sync.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/model_info_sync.proto',
  package='carbon.portal.model_info',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\"portal/proto/model_info_sync.proto\x12\x18\x63\x61rbon.portal.model_info\x1a\x17portal/proto/util.proto\"A\n\tModelInfo\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x10\n\x08\x63rop_ids\x18\x02 \x03(\t\x12\x10\n\x08nickname\x18\x03 \x01(\t\"b\n\x17UploadModelInfosRequest\x12\r\n\x05robot\x18\x01 \x01(\t\x12\x38\n\x0bmodel_infos\x18\x02 \x03(\x0b\x32#.carbon.portal.model_info.ModelInfo\"<\n\x12RenameModelCommand\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x14\n\x0cnew_nickname\x18\x02 \x01(\t\".\n\x1dGetRenameModelCommandsRequest\x12\r\n\x05robot\x18\x01 \x01(\t\"`\n\x1eGetRenameModelCommandsResponse\x12>\n\x08\x63ommands\x18\x01 \x03(\x0b\x32,.carbon.portal.model_info.RenameModelCommand\"p\n\x1fPurgeRenameModelCommandsRequest\x12\r\n\x05robot\x18\x01 \x01(\t\x12>\n\x08\x63ommands\x18\x02 \x03(\x0b\x32,.carbon.portal.model_info.RenameModelCommand2\xf8\x02\n\x14ModelInfoSyncService\x12`\n\x10UploadModelInfos\x12\x31.carbon.portal.model_info.UploadModelInfosRequest\x1a\x19.carbon.portal.util.Empty\x12\x8b\x01\n\x16GetRenameModelCommands\x12\x37.carbon.portal.model_info.GetRenameModelCommandsRequest\x1a\x38.carbon.portal.model_info.GetRenameModelCommandsResponse\x12p\n\x18PurgeRenameModelCommands\x12\x39.carbon.portal.model_info.PurgeRenameModelCommandsRequest\x1a\x19.carbon.portal.util.EmptyB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[portal_dot_proto_dot_util__pb2.DESCRIPTOR,])




_MODELINFO = _descriptor.Descriptor(
  name='ModelInfo',
  full_name='carbon.portal.model_info.ModelInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.portal.model_info.ModelInfo.model_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_ids', full_name='carbon.portal.model_info.ModelInfo.crop_ids', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nickname', full_name='carbon.portal.model_info.ModelInfo.nickname', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=89,
  serialized_end=154,
)


_UPLOADMODELINFOSREQUEST = _descriptor.Descriptor(
  name='UploadModelInfosRequest',
  full_name='carbon.portal.model_info.UploadModelInfosRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot', full_name='carbon.portal.model_info.UploadModelInfosRequest.robot', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_infos', full_name='carbon.portal.model_info.UploadModelInfosRequest.model_infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=156,
  serialized_end=254,
)


_RENAMEMODELCOMMAND = _descriptor.Descriptor(
  name='RenameModelCommand',
  full_name='carbon.portal.model_info.RenameModelCommand',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.portal.model_info.RenameModelCommand.model_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='new_nickname', full_name='carbon.portal.model_info.RenameModelCommand.new_nickname', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=256,
  serialized_end=316,
)


_GETRENAMEMODELCOMMANDSREQUEST = _descriptor.Descriptor(
  name='GetRenameModelCommandsRequest',
  full_name='carbon.portal.model_info.GetRenameModelCommandsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot', full_name='carbon.portal.model_info.GetRenameModelCommandsRequest.robot', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=318,
  serialized_end=364,
)


_GETRENAMEMODELCOMMANDSRESPONSE = _descriptor.Descriptor(
  name='GetRenameModelCommandsResponse',
  full_name='carbon.portal.model_info.GetRenameModelCommandsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='commands', full_name='carbon.portal.model_info.GetRenameModelCommandsResponse.commands', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=366,
  serialized_end=462,
)


_PURGERENAMEMODELCOMMANDSREQUEST = _descriptor.Descriptor(
  name='PurgeRenameModelCommandsRequest',
  full_name='carbon.portal.model_info.PurgeRenameModelCommandsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot', full_name='carbon.portal.model_info.PurgeRenameModelCommandsRequest.robot', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='commands', full_name='carbon.portal.model_info.PurgeRenameModelCommandsRequest.commands', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=464,
  serialized_end=576,
)

_UPLOADMODELINFOSREQUEST.fields_by_name['model_infos'].message_type = _MODELINFO
_GETRENAMEMODELCOMMANDSRESPONSE.fields_by_name['commands'].message_type = _RENAMEMODELCOMMAND
_PURGERENAMEMODELCOMMANDSREQUEST.fields_by_name['commands'].message_type = _RENAMEMODELCOMMAND
DESCRIPTOR.message_types_by_name['ModelInfo'] = _MODELINFO
DESCRIPTOR.message_types_by_name['UploadModelInfosRequest'] = _UPLOADMODELINFOSREQUEST
DESCRIPTOR.message_types_by_name['RenameModelCommand'] = _RENAMEMODELCOMMAND
DESCRIPTOR.message_types_by_name['GetRenameModelCommandsRequest'] = _GETRENAMEMODELCOMMANDSREQUEST
DESCRIPTOR.message_types_by_name['GetRenameModelCommandsResponse'] = _GETRENAMEMODELCOMMANDSRESPONSE
DESCRIPTOR.message_types_by_name['PurgeRenameModelCommandsRequest'] = _PURGERENAMEMODELCOMMANDSREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ModelInfo = _reflection.GeneratedProtocolMessageType('ModelInfo', (_message.Message,), {
  'DESCRIPTOR' : _MODELINFO,
  '__module__' : 'portal.proto.model_info_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.model_info.ModelInfo)
  })
_sym_db.RegisterMessage(ModelInfo)

UploadModelInfosRequest = _reflection.GeneratedProtocolMessageType('UploadModelInfosRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADMODELINFOSREQUEST,
  '__module__' : 'portal.proto.model_info_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.model_info.UploadModelInfosRequest)
  })
_sym_db.RegisterMessage(UploadModelInfosRequest)

RenameModelCommand = _reflection.GeneratedProtocolMessageType('RenameModelCommand', (_message.Message,), {
  'DESCRIPTOR' : _RENAMEMODELCOMMAND,
  '__module__' : 'portal.proto.model_info_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.model_info.RenameModelCommand)
  })
_sym_db.RegisterMessage(RenameModelCommand)

GetRenameModelCommandsRequest = _reflection.GeneratedProtocolMessageType('GetRenameModelCommandsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETRENAMEMODELCOMMANDSREQUEST,
  '__module__' : 'portal.proto.model_info_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.model_info.GetRenameModelCommandsRequest)
  })
_sym_db.RegisterMessage(GetRenameModelCommandsRequest)

GetRenameModelCommandsResponse = _reflection.GeneratedProtocolMessageType('GetRenameModelCommandsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETRENAMEMODELCOMMANDSRESPONSE,
  '__module__' : 'portal.proto.model_info_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.model_info.GetRenameModelCommandsResponse)
  })
_sym_db.RegisterMessage(GetRenameModelCommandsResponse)

PurgeRenameModelCommandsRequest = _reflection.GeneratedProtocolMessageType('PurgeRenameModelCommandsRequest', (_message.Message,), {
  'DESCRIPTOR' : _PURGERENAMEMODELCOMMANDSREQUEST,
  '__module__' : 'portal.proto.model_info_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
  })
_sym_db.RegisterMessage(PurgeRenameModelCommandsRequest)


DESCRIPTOR._options = None

_MODELINFOSYNCSERVICE = _descriptor.ServiceDescriptor(
  name='ModelInfoSyncService',
  full_name='carbon.portal.model_info.ModelInfoSyncService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=579,
  serialized_end=955,
  methods=[
  _descriptor.MethodDescriptor(
    name='UploadModelInfos',
    full_name='carbon.portal.model_info.ModelInfoSyncService.UploadModelInfos',
    index=0,
    containing_service=None,
    input_type=_UPLOADMODELINFOSREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRenameModelCommands',
    full_name='carbon.portal.model_info.ModelInfoSyncService.GetRenameModelCommands',
    index=1,
    containing_service=None,
    input_type=_GETRENAMEMODELCOMMANDSREQUEST,
    output_type=_GETRENAMEMODELCOMMANDSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PurgeRenameModelCommands',
    full_name='carbon.portal.model_info.ModelInfoSyncService.PurgeRenameModelCommands',
    index=2,
    containing_service=None,
    input_type=_PURGERENAMEMODELCOMMANDSREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_MODELINFOSYNCSERVICE)

DESCRIPTOR.services_by_name['ModelInfoSyncService'] = _MODELINFOSYNCSERVICE

# @@protoc_insertion_point(module_scope)

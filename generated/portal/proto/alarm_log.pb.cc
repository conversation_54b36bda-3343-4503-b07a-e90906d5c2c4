// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/alarm_log.proto

#include "portal/proto/alarm_log.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace alarm_log {
constexpr SyncAlarmsRequest::SyncAlarmsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : alarms_()
  , robot_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SyncAlarmsRequestDefaultTypeInternal {
  constexpr SyncAlarmsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SyncAlarmsRequestDefaultTypeInternal() {}
  union {
    SyncAlarmsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SyncAlarmsRequestDefaultTypeInternal _SyncAlarmsRequest_default_instance_;
}  // namespace alarm_log
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2falarm_5flog_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2falarm_5flog_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2falarm_5flog_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2falarm_5flog_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::alarm_log::SyncAlarmsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::alarm_log::SyncAlarmsRequest, alarms_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::alarm_log::SyncAlarmsRequest, robot_name_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::alarm_log::SyncAlarmsRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::alarm_log::_SyncAlarmsRequest_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2falarm_5flog_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034portal/proto/alarm_log.proto\022\027carbon.p"
  "ortal.alarm_log\032\027portal/proto/util.proto"
  "\032\032frontend/proto/alarm.proto\"X\n\021SyncAlar"
  "msRequest\022/\n\006alarms\030\001 \003(\0132\037.carbon.front"
  "end.alarm.AlarmRow\022\022\n\nrobot_name\030\002 \001(\t2l"
  "\n\025PortalAlarmLogService\022S\n\nSyncAlarms\022*."
  "carbon.portal.alarm_log.SyncAlarmsReques"
  "t\032\031.carbon.portal.util.EmptyB\016Z\014proto/po"
  "rtalb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2falarm_5flog_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2falarm_2eproto,
  &::descriptor_table_portal_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2falarm_5flog_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2falarm_5flog_2eproto = {
  false, false, 332, descriptor_table_protodef_portal_2fproto_2falarm_5flog_2eproto, "portal/proto/alarm_log.proto", 
  &descriptor_table_portal_2fproto_2falarm_5flog_2eproto_once, descriptor_table_portal_2fproto_2falarm_5flog_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_portal_2fproto_2falarm_5flog_2eproto::offsets,
  file_level_metadata_portal_2fproto_2falarm_5flog_2eproto, file_level_enum_descriptors_portal_2fproto_2falarm_5flog_2eproto, file_level_service_descriptors_portal_2fproto_2falarm_5flog_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2falarm_5flog_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2falarm_5flog_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2falarm_5flog_2eproto(&descriptor_table_portal_2fproto_2falarm_5flog_2eproto);
namespace carbon {
namespace portal {
namespace alarm_log {

// ===================================================================

class SyncAlarmsRequest::_Internal {
 public:
};

void SyncAlarmsRequest::clear_alarms() {
  alarms_.Clear();
}
SyncAlarmsRequest::SyncAlarmsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  alarms_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.alarm_log.SyncAlarmsRequest)
}
SyncAlarmsRequest::SyncAlarmsRequest(const SyncAlarmsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      alarms_(from.alarms_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_name().empty()) {
    robot_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.alarm_log.SyncAlarmsRequest)
}

inline void SyncAlarmsRequest::SharedCtor() {
robot_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SyncAlarmsRequest::~SyncAlarmsRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.alarm_log.SyncAlarmsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SyncAlarmsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SyncAlarmsRequest::ArenaDtor(void* object) {
  SyncAlarmsRequest* _this = reinterpret_cast< SyncAlarmsRequest* >(object);
  (void)_this;
}
void SyncAlarmsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SyncAlarmsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SyncAlarmsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.alarm_log.SyncAlarmsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  alarms_.Clear();
  robot_name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SyncAlarmsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_alarms(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string robot_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_robot_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.alarm_log.SyncAlarmsRequest.robot_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SyncAlarmsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.alarm_log.SyncAlarmsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_alarms_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_alarms(i), target, stream);
  }

  // string robot_name = 2;
  if (!this->_internal_robot_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_name().data(), static_cast<int>(this->_internal_robot_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.alarm_log.SyncAlarmsRequest.robot_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_robot_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.alarm_log.SyncAlarmsRequest)
  return target;
}

size_t SyncAlarmsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.alarm_log.SyncAlarmsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
  total_size += 1UL * this->_internal_alarms_size();
  for (const auto& msg : this->alarms_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string robot_name = 2;
  if (!this->_internal_robot_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SyncAlarmsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SyncAlarmsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SyncAlarmsRequest::GetClassData() const { return &_class_data_; }

void SyncAlarmsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SyncAlarmsRequest *>(to)->MergeFrom(
      static_cast<const SyncAlarmsRequest &>(from));
}


void SyncAlarmsRequest::MergeFrom(const SyncAlarmsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.alarm_log.SyncAlarmsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  alarms_.MergeFrom(from.alarms_);
  if (!from._internal_robot_name().empty()) {
    _internal_set_robot_name(from._internal_robot_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SyncAlarmsRequest::CopyFrom(const SyncAlarmsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.alarm_log.SyncAlarmsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SyncAlarmsRequest::IsInitialized() const {
  return true;
}

void SyncAlarmsRequest::InternalSwap(SyncAlarmsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  alarms_.InternalSwap(&other->alarms_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_name_, lhs_arena,
      &other->robot_name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SyncAlarmsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2falarm_5flog_2eproto_getter, &descriptor_table_portal_2fproto_2falarm_5flog_2eproto_once,
      file_level_metadata_portal_2fproto_2falarm_5flog_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace alarm_log
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::alarm_log::SyncAlarmsRequest* Arena::CreateMaybeMessage< ::carbon::portal::alarm_log::SyncAlarmsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::alarm_log::SyncAlarmsRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

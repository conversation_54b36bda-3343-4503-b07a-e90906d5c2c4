"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from google.protobuf.timestamp_pb2 import (
    Timestamp as google___protobuf___timestamp_pb2___Timestamp,
)

from generated.proto.geo.geo_pb2 import (
    AbLine as proto___geo___geo_pb2___AbLine,
    Id as proto___geo___geo_pb2___Id,
    LineString as proto___geo___geo_pb2___LineString,
    Point as proto___geo___geo_pb2___Point,
    Polygon as proto___geo___geo_pb2___Polygon,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Farm(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    customer_id: builtin___int = ...

    @property
    def id(self) -> proto___geo___geo_pb2___Id: ...

    @property
    def version(self) -> type___VersionInfo: ...

    @property
    def point_defs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PointDefinition]: ...

    @property
    def zones(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Zone]: ...

    def __init__(self,
        *,
        id : typing___Optional[proto___geo___geo_pb2___Id] = None,
        version : typing___Optional[type___VersionInfo] = None,
        name : typing___Optional[typing___Text] = None,
        customer_id : typing___Optional[builtin___int] = None,
        point_defs : typing___Optional[typing___Iterable[type___PointDefinition]] = None,
        zones : typing___Optional[typing___Iterable[type___Zone]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"id",b"id",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"customer_id",b"customer_id",u"id",b"id",u"name",b"name",u"point_defs",b"point_defs",u"version",b"version",u"zones",b"zones"]) -> None: ...
type___Farm = Farm

class VersionInfo(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ordinal: builtin___int = ...
    deleted: builtin___bool = ...
    changed: builtin___bool = ...

    @property
    def update_time(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    def __init__(self,
        *,
        ordinal : typing___Optional[builtin___int] = None,
        update_time : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        deleted : typing___Optional[builtin___bool] = None,
        changed : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"update_time",b"update_time"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"changed",b"changed",u"deleted",b"deleted",u"ordinal",b"ordinal",u"update_time",b"update_time"]) -> None: ...
type___VersionInfo = VersionInfo

class PointDefinition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def point(self) -> proto___geo___geo_pb2___Point: ...

    @property
    def version(self) -> type___VersionInfo: ...

    def __init__(self,
        *,
        point : typing___Optional[proto___geo___geo_pb2___Point] = None,
        version : typing___Optional[type___VersionInfo] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"point",b"point",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"point",b"point",u"version",b"version"]) -> None: ...
type___PointDefinition = PointDefinition

class Zone(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    @property
    def id(self) -> proto___geo___geo_pb2___Id: ...

    @property
    def version(self) -> type___VersionInfo: ...

    @property
    def areas(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Area]: ...

    @property
    def contents(self) -> type___ZoneContents: ...

    def __init__(self,
        *,
        id : typing___Optional[proto___geo___geo_pb2___Id] = None,
        version : typing___Optional[type___VersionInfo] = None,
        name : typing___Optional[typing___Text] = None,
        areas : typing___Optional[typing___Iterable[type___Area]] = None,
        contents : typing___Optional[type___ZoneContents] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"contents",b"contents",u"id",b"id",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"areas",b"areas",u"contents",b"contents",u"id",b"id",u"name",b"name",u"version",b"version"]) -> None: ...
type___Zone = Zone

class ZoneContents(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def farm_boundary(self) -> type___FarmBoundaryData: ...

    @property
    def field(self) -> type___FieldData: ...

    @property
    def headland(self) -> type___HeadlandData: ...

    @property
    def private_road(self) -> type___PrivateRoadData: ...

    @property
    def obstacle(self) -> type___ObstacleData: ...

    def __init__(self,
        *,
        farm_boundary : typing___Optional[type___FarmBoundaryData] = None,
        field : typing___Optional[type___FieldData] = None,
        headland : typing___Optional[type___HeadlandData] = None,
        private_road : typing___Optional[type___PrivateRoadData] = None,
        obstacle : typing___Optional[type___ObstacleData] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"data",b"data",u"farm_boundary",b"farm_boundary",u"field",b"field",u"headland",b"headland",u"obstacle",b"obstacle",u"private_road",b"private_road"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data",u"farm_boundary",b"farm_boundary",u"field",b"field",u"headland",b"headland",u"obstacle",b"obstacle",u"private_road",b"private_road"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"data",b"data"]) -> typing_extensions___Literal["farm_boundary","field","headland","private_road","obstacle"]: ...
type___ZoneContents = ZoneContents

class Area(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    buffer_meters: builtin___float = ...

    @property
    def point(self) -> proto___geo___geo_pb2___Point: ...

    @property
    def line_string(self) -> proto___geo___geo_pb2___LineString: ...

    @property
    def polygon(self) -> proto___geo___geo_pb2___Polygon: ...

    def __init__(self,
        *,
        buffer_meters : typing___Optional[builtin___float] = None,
        point : typing___Optional[proto___geo___geo_pb2___Point] = None,
        line_string : typing___Optional[proto___geo___geo_pb2___LineString] = None,
        polygon : typing___Optional[proto___geo___geo_pb2___Polygon] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"geometry",b"geometry",u"line_string",b"line_string",u"point",b"point",u"polygon",b"polygon"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"buffer_meters",b"buffer_meters",u"geometry",b"geometry",u"line_string",b"line_string",u"point",b"point",u"polygon",b"polygon"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"geometry",b"geometry"]) -> typing_extensions___Literal["point","line_string","polygon"]: ...
type___Area = Area

class FarmBoundaryData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___FarmBoundaryData = FarmBoundaryData

class FieldData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def planting_heading(self) -> type___PlantingHeading: ...

    @property
    def center_pivot(self) -> type___CenterPivot: ...

    def __init__(self,
        *,
        planting_heading : typing___Optional[type___PlantingHeading] = None,
        center_pivot : typing___Optional[type___CenterPivot] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"center_pivot",b"center_pivot",u"planting_heading",b"planting_heading"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_pivot",b"center_pivot",u"planting_heading",b"planting_heading"]) -> None: ...
type___FieldData = FieldData

class HeadlandData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___HeadlandData = HeadlandData

class PrivateRoadData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___PrivateRoadData = PrivateRoadData

class ObstacleData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ObstacleData = ObstacleData

class PlantingHeading(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    azimuth_degrees: builtin___float = ...

    @property
    def ab_line(self) -> proto___geo___geo_pb2___AbLine: ...

    def __init__(self,
        *,
        azimuth_degrees : typing___Optional[builtin___float] = None,
        ab_line : typing___Optional[proto___geo___geo_pb2___AbLine] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ab_line",b"ab_line",u"azimuth_degrees",b"azimuth_degrees",u"heading",b"heading"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ab_line",b"ab_line",u"azimuth_degrees",b"azimuth_degrees",u"heading",b"heading"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"heading",b"heading"]) -> typing_extensions___Literal["azimuth_degrees","ab_line"]: ...
type___PlantingHeading = PlantingHeading

class CenterPivot(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    width_meters: builtin___float = ...
    length_meters: builtin___float = ...

    @property
    def center(self) -> proto___geo___geo_pb2___Point: ...

    def __init__(self,
        *,
        center : typing___Optional[proto___geo___geo_pb2___Point] = None,
        width_meters : typing___Optional[builtin___float] = None,
        length_meters : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"center",b"center"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center",b"center",u"length_meters",b"length_meters",u"width_meters",b"width_meters"]) -> None: ...
type___CenterPivot = CenterPivot

class CreateFarmRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def farm(self) -> type___Farm: ...

    def __init__(self,
        *,
        farm : typing___Optional[type___Farm] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"farm",b"farm"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"farm",b"farm"]) -> None: ...
type___CreateFarmRequest = CreateFarmRequest

class UpdateFarmRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def farm(self) -> type___Farm: ...

    def __init__(self,
        *,
        farm : typing___Optional[type___Farm] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"farm",b"farm"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"farm",b"farm"]) -> None: ...
type___UpdateFarmRequest = UpdateFarmRequest

class ListFarmsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page_token: typing___Text = ...

    def __init__(self,
        *,
        page_token : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"page_token",b"page_token"]) -> None: ...
type___ListFarmsRequest = ListFarmsRequest

class ListFarmsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    next_page_token: typing___Text = ...

    @property
    def farms(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Farm]: ...

    def __init__(self,
        *,
        farms : typing___Optional[typing___Iterable[type___Farm]] = None,
        next_page_token : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"farms",b"farms",u"next_page_token",b"next_page_token"]) -> None: ...
type___ListFarmsResponse = ListFarmsResponse

class GetFarmRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def id(self) -> proto___geo___geo_pb2___Id: ...

    @property
    def if_modified_since(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    def __init__(self,
        *,
        id : typing___Optional[proto___geo___geo_pb2___Id] = None,
        if_modified_since : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"id",b"id",u"if_modified_since",b"if_modified_since"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"if_modified_since",b"if_modified_since"]) -> None: ...
type___GetFarmRequest = GetFarmRequest

class GetFarmResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def farm(self) -> type___Farm: ...

    def __init__(self,
        *,
        farm : typing___Optional[type___Farm] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"farm",b"farm"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"farm",b"farm"]) -> None: ...
type___GetFarmResponse = GetFarmResponse

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.metrics.metrics_pb2 import (
    SpatialMetricBlock as proto___metrics___metrics_pb2___SpatialMetricBlock,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class SyncSpatialMetricBlocksRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    robot: typing___Text = ...

    @property
    def blocks(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[proto___metrics___metrics_pb2___SpatialMetricBlock]: ...

    def __init__(self,
        *,
        blocks : typing___Optional[typing___Iterable[proto___metrics___metrics_pb2___SpatialMetricBlock]] = None,
        robot : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"blocks",b"blocks",u"robot",b"robot"]) -> None: ...
type___SyncSpatialMetricBlocksRequest = SyncSpatialMetricBlocksRequest

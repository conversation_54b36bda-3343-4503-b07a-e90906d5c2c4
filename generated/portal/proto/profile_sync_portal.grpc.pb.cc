// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/profile_sync_portal.proto

#include "portal/proto/profile_sync_portal.pb.h"
#include "portal/proto/profile_sync_portal.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace profile_sync {

static const char* PortalProfileSyncService_method_names[] = {
  "/carbon.portal.profile_sync.PortalProfileSyncService/GetProfilesData",
  "/carbon.portal.profile_sync.PortalProfileSyncService/UploadProfile",
  "/carbon.portal.profile_sync.PortalProfileSyncService/GetProfile",
  "/carbon.portal.profile_sync.PortalProfileSyncService/DeleteProfile",
  "/carbon.portal.profile_sync.PortalProfileSyncService/PurgeProfile",
  "/carbon.portal.profile_sync.PortalProfileSyncService/GetSetActiveProfileCommands",
  "/carbon.portal.profile_sync.PortalProfileSyncService/PurgeSetActiveProfileCommands",
};

std::unique_ptr< PortalProfileSyncService::Stub> PortalProfileSyncService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< PortalProfileSyncService::Stub> stub(new PortalProfileSyncService::Stub(channel, options));
  return stub;
}

PortalProfileSyncService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetProfilesData_(PortalProfileSyncService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UploadProfile_(PortalProfileSyncService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetProfile_(PortalProfileSyncService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteProfile_(PortalProfileSyncService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_PurgeProfile_(PortalProfileSyncService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSetActiveProfileCommands_(PortalProfileSyncService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_PurgeSetActiveProfileCommands_(PortalProfileSyncService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status PortalProfileSyncService::Stub::GetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::profile_sync::GetProfilesDataRequest, ::carbon::portal::profile_sync::GetProfilesDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetProfilesData_, context, request, response);
}

void PortalProfileSyncService::Stub::async::GetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::profile_sync::GetProfilesDataRequest, ::carbon::portal::profile_sync::GetProfilesDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetProfilesData_, context, request, response, std::move(f));
}

void PortalProfileSyncService::Stub::async::GetProfilesData(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetProfilesData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfilesDataResponse>* PortalProfileSyncService::Stub::PrepareAsyncGetProfilesDataRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::profile_sync::GetProfilesDataResponse, ::carbon::portal::profile_sync::GetProfilesDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetProfilesData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfilesDataResponse>* PortalProfileSyncService::Stub::AsyncGetProfilesDataRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetProfilesDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PortalProfileSyncService::Stub::UploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UploadProfile_, context, request, response);
}

void PortalProfileSyncService::Stub::async::UploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadProfile_, context, request, response, std::move(f));
}

void PortalProfileSyncService::Stub::async::UploadProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalProfileSyncService::Stub::PrepareAsyncUploadProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::profile_sync::UploadProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UploadProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalProfileSyncService::Stub::AsyncUploadProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUploadProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PortalProfileSyncService::Stub::GetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::carbon::portal::profile_sync::GetProfileResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::profile_sync::GetProfileRequest, ::carbon::portal::profile_sync::GetProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetProfile_, context, request, response);
}

void PortalProfileSyncService::Stub::async::GetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest* request, ::carbon::portal::profile_sync::GetProfileResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::profile_sync::GetProfileRequest, ::carbon::portal::profile_sync::GetProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetProfile_, context, request, response, std::move(f));
}

void PortalProfileSyncService::Stub::async::GetProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest* request, ::carbon::portal::profile_sync::GetProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfileResponse>* PortalProfileSyncService::Stub::PrepareAsyncGetProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::profile_sync::GetProfileResponse, ::carbon::portal::profile_sync::GetProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetProfileResponse>* PortalProfileSyncService::Stub::AsyncGetProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PortalProfileSyncService::Stub::DeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteProfile_, context, request, response);
}

void PortalProfileSyncService::Stub::async::DeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteProfile_, context, request, response, std::move(f));
}

void PortalProfileSyncService::Stub::async::DeleteProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalProfileSyncService::Stub::PrepareAsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::profile_sync::DeleteProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalProfileSyncService::Stub::AsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PortalProfileSyncService::Stub::PurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_PurgeProfile_, context, request, response);
}

void PortalProfileSyncService::Stub::async::PurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PurgeProfile_, context, request, response, std::move(f));
}

void PortalProfileSyncService::Stub::async::PurgeProfile(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PurgeProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalProfileSyncService::Stub::PrepareAsyncPurgeProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::profile_sync::PurgeProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_PurgeProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalProfileSyncService::Stub::AsyncPurgeProfileRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPurgeProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PortalProfileSyncService::Stub::GetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSetActiveProfileCommands_, context, request, response);
}

void PortalProfileSyncService::Stub::async::GetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSetActiveProfileCommands_, context, request, response, std::move(f));
}

void PortalProfileSyncService::Stub::async::GetSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSetActiveProfileCommands_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* PortalProfileSyncService::Stub::PrepareAsyncGetSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSetActiveProfileCommands_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse>* PortalProfileSyncService::Stub::AsyncGetSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSetActiveProfileCommandsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PortalProfileSyncService::Stub::PurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_PurgeSetActiveProfileCommands_, context, request, response);
}

void PortalProfileSyncService::Stub::async::PurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PurgeSetActiveProfileCommands_, context, request, response, std::move(f));
}

void PortalProfileSyncService::Stub::async::PurgeSetActiveProfileCommands(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PurgeSetActiveProfileCommands_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalProfileSyncService::Stub::PrepareAsyncPurgeSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_PurgeSetActiveProfileCommands_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalProfileSyncService::Stub::AsyncPurgeSetActiveProfileCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPurgeSetActiveProfileCommandsRaw(context, request, cq);
  result->StartCall();
  return result;
}

PortalProfileSyncService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalProfileSyncService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalProfileSyncService::Service, ::carbon::portal::profile_sync::GetProfilesDataRequest, ::carbon::portal::profile_sync::GetProfilesDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::profile_sync::GetProfilesDataRequest* req,
             ::carbon::portal::profile_sync::GetProfilesDataResponse* resp) {
               return service->GetProfilesData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalProfileSyncService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalProfileSyncService::Service, ::carbon::portal::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::profile_sync::UploadProfileRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->UploadProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalProfileSyncService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalProfileSyncService::Service, ::carbon::portal::profile_sync::GetProfileRequest, ::carbon::portal::profile_sync::GetProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::profile_sync::GetProfileRequest* req,
             ::carbon::portal::profile_sync::GetProfileResponse* resp) {
               return service->GetProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalProfileSyncService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalProfileSyncService::Service, ::carbon::portal::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::profile_sync::DeleteProfileRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->DeleteProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalProfileSyncService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalProfileSyncService::Service, ::carbon::portal::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::profile_sync::PurgeProfileRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->PurgeProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalProfileSyncService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalProfileSyncService::Service, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* req,
             ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* resp) {
               return service->GetSetActiveProfileCommands(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalProfileSyncService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalProfileSyncService::Service, ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->PurgeSetActiveProfileCommands(ctx, req, resp);
             }, this)));
}

PortalProfileSyncService::Service::~Service() {
}

::grpc::Status PortalProfileSyncService::Service::GetProfilesData(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::GetProfilesDataRequest* request, ::carbon::portal::profile_sync::GetProfilesDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PortalProfileSyncService::Service::UploadProfile(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PortalProfileSyncService::Service::GetProfile(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::GetProfileRequest* request, ::carbon::portal::profile_sync::GetProfileResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PortalProfileSyncService::Service::DeleteProfile(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PortalProfileSyncService::Service::PurgeProfile(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PortalProfileSyncService::Service::GetSetActiveProfileCommands(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* request, ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PortalProfileSyncService::Service::PurgeSetActiveProfileCommands(::grpc::ServerContext* context, const ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace profile_sync


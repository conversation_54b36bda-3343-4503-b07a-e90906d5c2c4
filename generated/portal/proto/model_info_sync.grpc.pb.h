// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/model_info_sync.proto
#ifndef GRPC_portal_2fproto_2fmodel_5finfo_5fsync_2eproto__INCLUDED
#define GRPC_portal_2fproto_2fmodel_5finfo_5fsync_2eproto__INCLUDED

#include "portal/proto/model_info_sync.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace portal {
namespace model_info {

class ModelInfoSyncService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.portal.model_info.ModelInfoSyncService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status UploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncUploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncUploadModelInfosRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncUploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncUploadModelInfosRaw(context, request, cq));
    }
    virtual ::grpc::Status GetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::model_info::GetRenameModelCommandsResponse>> AsyncGetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::model_info::GetRenameModelCommandsResponse>>(AsyncGetRenameModelCommandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::model_info::GetRenameModelCommandsResponse>> PrepareAsyncGetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::model_info::GetRenameModelCommandsResponse>>(PrepareAsyncGetRenameModelCommandsRaw(context, request, cq));
    }
    virtual ::grpc::Status PurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncPurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncPurgeRenameModelCommandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncPurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncPurgeRenameModelCommandsRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void UploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void PurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void PurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncUploadModelInfosRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncUploadModelInfosRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::model_info::GetRenameModelCommandsResponse>* AsyncGetRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::model_info::GetRenameModelCommandsResponse>* PrepareAsyncGetRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncPurgeRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncPurgeRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status UploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncUploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncUploadModelInfosRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncUploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncUploadModelInfosRaw(context, request, cq));
    }
    ::grpc::Status GetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::model_info::GetRenameModelCommandsResponse>> AsyncGetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::model_info::GetRenameModelCommandsResponse>>(AsyncGetRenameModelCommandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::model_info::GetRenameModelCommandsResponse>> PrepareAsyncGetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::model_info::GetRenameModelCommandsResponse>>(PrepareAsyncGetRenameModelCommandsRaw(context, request, cq));
    }
    ::grpc::Status PurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncPurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncPurgeRenameModelCommandsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncPurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncPurgeRenameModelCommandsRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void UploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void UploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void PurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void PurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncUploadModelInfosRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncUploadModelInfosRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::model_info::GetRenameModelCommandsResponse>* AsyncGetRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::model_info::GetRenameModelCommandsResponse>* PrepareAsyncGetRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncPurgeRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncPurgeRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_UploadModelInfos_;
    const ::grpc::internal::RpcMethod rpcmethod_GetRenameModelCommands_;
    const ::grpc::internal::RpcMethod rpcmethod_PurgeRenameModelCommands_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status UploadModelInfos(::grpc::ServerContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest* request, ::carbon::portal::util::Empty* response);
    virtual ::grpc::Status GetRenameModelCommands(::grpc::ServerContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response);
    virtual ::grpc::Status PurgeRenameModelCommands(::grpc::ServerContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::carbon::portal::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_UploadModelInfos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UploadModelInfos() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_UploadModelInfos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelInfos(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::UploadModelInfosRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadModelInfos(::grpc::ServerContext* context, ::carbon::portal::model_info::UploadModelInfosRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetRenameModelCommands() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* /*request*/, ::carbon::portal::model_info::GetRenameModelCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRenameModelCommands(::grpc::ServerContext* context, ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::model_info::GetRenameModelCommandsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_PurgeRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_PurgeRenameModelCommands() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_PurgeRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPurgeRenameModelCommands(::grpc::ServerContext* context, ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_UploadModelInfos<WithAsyncMethod_GetRenameModelCommands<WithAsyncMethod_PurgeRenameModelCommands<Service > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_UploadModelInfos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UploadModelInfos() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::model_info::UploadModelInfosRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest* request, ::carbon::portal::util::Empty* response) { return this->UploadModelInfos(context, request, response); }));}
    void SetMessageAllocatorFor_UploadModelInfos(
        ::grpc::MessageAllocator< ::carbon::portal::model_info::UploadModelInfosRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::model_info::UploadModelInfosRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UploadModelInfos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelInfos(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::UploadModelInfosRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadModelInfos(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::model_info::UploadModelInfosRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetRenameModelCommands() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::model_info::GetRenameModelCommandsRequest, ::carbon::portal::model_info::GetRenameModelCommandsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response) { return this->GetRenameModelCommands(context, request, response); }));}
    void SetMessageAllocatorFor_GetRenameModelCommands(
        ::grpc::MessageAllocator< ::carbon::portal::model_info::GetRenameModelCommandsRequest, ::carbon::portal::model_info::GetRenameModelCommandsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::model_info::GetRenameModelCommandsRequest, ::carbon::portal::model_info::GetRenameModelCommandsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* /*request*/, ::carbon::portal::model_info::GetRenameModelCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRenameModelCommands(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* /*request*/, ::carbon::portal::model_info::GetRenameModelCommandsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_PurgeRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_PurgeRenameModelCommands() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::model_info::PurgeRenameModelCommandsRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::carbon::portal::util::Empty* response) { return this->PurgeRenameModelCommands(context, request, response); }));}
    void SetMessageAllocatorFor_PurgeRenameModelCommands(
        ::grpc::MessageAllocator< ::carbon::portal::model_info::PurgeRenameModelCommandsRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::model_info::PurgeRenameModelCommandsRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_PurgeRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* PurgeRenameModelCommands(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_UploadModelInfos<WithCallbackMethod_GetRenameModelCommands<WithCallbackMethod_PurgeRenameModelCommands<Service > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_UploadModelInfos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UploadModelInfos() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_UploadModelInfos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelInfos(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::UploadModelInfosRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetRenameModelCommands() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* /*request*/, ::carbon::portal::model_info::GetRenameModelCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_PurgeRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_PurgeRenameModelCommands() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_PurgeRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_UploadModelInfos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UploadModelInfos() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_UploadModelInfos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelInfos(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::UploadModelInfosRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadModelInfos(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetRenameModelCommands() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* /*request*/, ::carbon::portal::model_info::GetRenameModelCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRenameModelCommands(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_PurgeRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_PurgeRenameModelCommands() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_PurgeRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPurgeRenameModelCommands(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UploadModelInfos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UploadModelInfos() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UploadModelInfos(context, request, response); }));
    }
    ~WithRawCallbackMethod_UploadModelInfos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadModelInfos(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::UploadModelInfosRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadModelInfos(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetRenameModelCommands() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetRenameModelCommands(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* /*request*/, ::carbon::portal::model_info::GetRenameModelCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRenameModelCommands(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_PurgeRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_PurgeRenameModelCommands() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->PurgeRenameModelCommands(context, request, response); }));
    }
    ~WithRawCallbackMethod_PurgeRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PurgeRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* PurgeRenameModelCommands(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UploadModelInfos : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UploadModelInfos() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::model_info::UploadModelInfosRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::model_info::UploadModelInfosRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedUploadModelInfos(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UploadModelInfos() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UploadModelInfos(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::UploadModelInfosRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUploadModelInfos(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::model_info::UploadModelInfosRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetRenameModelCommands() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::model_info::GetRenameModelCommandsRequest, ::carbon::portal::model_info::GetRenameModelCommandsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::model_info::GetRenameModelCommandsRequest, ::carbon::portal::model_info::GetRenameModelCommandsResponse>* streamer) {
                       return this->StreamedGetRenameModelCommands(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* /*request*/, ::carbon::portal::model_info::GetRenameModelCommandsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetRenameModelCommands(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::model_info::GetRenameModelCommandsRequest,::carbon::portal::model_info::GetRenameModelCommandsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_PurgeRenameModelCommands : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_PurgeRenameModelCommands() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::model_info::PurgeRenameModelCommandsRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::model_info::PurgeRenameModelCommandsRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedPurgeRenameModelCommands(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_PurgeRenameModelCommands() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status PurgeRenameModelCommands(::grpc::ServerContext* /*context*/, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPurgeRenameModelCommands(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::model_info::PurgeRenameModelCommandsRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_UploadModelInfos<WithStreamedUnaryMethod_GetRenameModelCommands<WithStreamedUnaryMethod_PurgeRenameModelCommands<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_UploadModelInfos<WithStreamedUnaryMethod_GetRenameModelCommands<WithStreamedUnaryMethod_PurgeRenameModelCommands<Service > > > StreamedService;
};

}  // namespace model_info
}  // namespace portal
}  // namespace carbon


#endif  // GRPC_portal_2fproto_2fmodel_5finfo_5fsync_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/farm.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2ffarm_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2ffarm_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "proto/geo/geo.pb.h"
#include <google/protobuf/timestamp.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2ffarm_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2ffarm_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[19]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2ffarm_2eproto;
namespace carbon {
namespace portal {
namespace farm {
class Area;
struct AreaDefaultTypeInternal;
extern AreaDefaultTypeInternal _Area_default_instance_;
class CenterPivot;
struct CenterPivotDefaultTypeInternal;
extern CenterPivotDefaultTypeInternal _CenterPivot_default_instance_;
class CreateFarmRequest;
struct CreateFarmRequestDefaultTypeInternal;
extern CreateFarmRequestDefaultTypeInternal _CreateFarmRequest_default_instance_;
class Farm;
struct FarmDefaultTypeInternal;
extern FarmDefaultTypeInternal _Farm_default_instance_;
class FarmBoundaryData;
struct FarmBoundaryDataDefaultTypeInternal;
extern FarmBoundaryDataDefaultTypeInternal _FarmBoundaryData_default_instance_;
class FieldData;
struct FieldDataDefaultTypeInternal;
extern FieldDataDefaultTypeInternal _FieldData_default_instance_;
class GetFarmRequest;
struct GetFarmRequestDefaultTypeInternal;
extern GetFarmRequestDefaultTypeInternal _GetFarmRequest_default_instance_;
class GetFarmResponse;
struct GetFarmResponseDefaultTypeInternal;
extern GetFarmResponseDefaultTypeInternal _GetFarmResponse_default_instance_;
class HeadlandData;
struct HeadlandDataDefaultTypeInternal;
extern HeadlandDataDefaultTypeInternal _HeadlandData_default_instance_;
class ListFarmsRequest;
struct ListFarmsRequestDefaultTypeInternal;
extern ListFarmsRequestDefaultTypeInternal _ListFarmsRequest_default_instance_;
class ListFarmsResponse;
struct ListFarmsResponseDefaultTypeInternal;
extern ListFarmsResponseDefaultTypeInternal _ListFarmsResponse_default_instance_;
class ObstacleData;
struct ObstacleDataDefaultTypeInternal;
extern ObstacleDataDefaultTypeInternal _ObstacleData_default_instance_;
class PlantingHeading;
struct PlantingHeadingDefaultTypeInternal;
extern PlantingHeadingDefaultTypeInternal _PlantingHeading_default_instance_;
class PointDefinition;
struct PointDefinitionDefaultTypeInternal;
extern PointDefinitionDefaultTypeInternal _PointDefinition_default_instance_;
class PrivateRoadData;
struct PrivateRoadDataDefaultTypeInternal;
extern PrivateRoadDataDefaultTypeInternal _PrivateRoadData_default_instance_;
class UpdateFarmRequest;
struct UpdateFarmRequestDefaultTypeInternal;
extern UpdateFarmRequestDefaultTypeInternal _UpdateFarmRequest_default_instance_;
class VersionInfo;
struct VersionInfoDefaultTypeInternal;
extern VersionInfoDefaultTypeInternal _VersionInfo_default_instance_;
class Zone;
struct ZoneDefaultTypeInternal;
extern ZoneDefaultTypeInternal _Zone_default_instance_;
class ZoneContents;
struct ZoneContentsDefaultTypeInternal;
extern ZoneContentsDefaultTypeInternal _ZoneContents_default_instance_;
}  // namespace farm
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::farm::Area* Arena::CreateMaybeMessage<::carbon::portal::farm::Area>(Arena*);
template<> ::carbon::portal::farm::CenterPivot* Arena::CreateMaybeMessage<::carbon::portal::farm::CenterPivot>(Arena*);
template<> ::carbon::portal::farm::CreateFarmRequest* Arena::CreateMaybeMessage<::carbon::portal::farm::CreateFarmRequest>(Arena*);
template<> ::carbon::portal::farm::Farm* Arena::CreateMaybeMessage<::carbon::portal::farm::Farm>(Arena*);
template<> ::carbon::portal::farm::FarmBoundaryData* Arena::CreateMaybeMessage<::carbon::portal::farm::FarmBoundaryData>(Arena*);
template<> ::carbon::portal::farm::FieldData* Arena::CreateMaybeMessage<::carbon::portal::farm::FieldData>(Arena*);
template<> ::carbon::portal::farm::GetFarmRequest* Arena::CreateMaybeMessage<::carbon::portal::farm::GetFarmRequest>(Arena*);
template<> ::carbon::portal::farm::GetFarmResponse* Arena::CreateMaybeMessage<::carbon::portal::farm::GetFarmResponse>(Arena*);
template<> ::carbon::portal::farm::HeadlandData* Arena::CreateMaybeMessage<::carbon::portal::farm::HeadlandData>(Arena*);
template<> ::carbon::portal::farm::ListFarmsRequest* Arena::CreateMaybeMessage<::carbon::portal::farm::ListFarmsRequest>(Arena*);
template<> ::carbon::portal::farm::ListFarmsResponse* Arena::CreateMaybeMessage<::carbon::portal::farm::ListFarmsResponse>(Arena*);
template<> ::carbon::portal::farm::ObstacleData* Arena::CreateMaybeMessage<::carbon::portal::farm::ObstacleData>(Arena*);
template<> ::carbon::portal::farm::PlantingHeading* Arena::CreateMaybeMessage<::carbon::portal::farm::PlantingHeading>(Arena*);
template<> ::carbon::portal::farm::PointDefinition* Arena::CreateMaybeMessage<::carbon::portal::farm::PointDefinition>(Arena*);
template<> ::carbon::portal::farm::PrivateRoadData* Arena::CreateMaybeMessage<::carbon::portal::farm::PrivateRoadData>(Arena*);
template<> ::carbon::portal::farm::UpdateFarmRequest* Arena::CreateMaybeMessage<::carbon::portal::farm::UpdateFarmRequest>(Arena*);
template<> ::carbon::portal::farm::VersionInfo* Arena::CreateMaybeMessage<::carbon::portal::farm::VersionInfo>(Arena*);
template<> ::carbon::portal::farm::Zone* Arena::CreateMaybeMessage<::carbon::portal::farm::Zone>(Arena*);
template<> ::carbon::portal::farm::ZoneContents* Arena::CreateMaybeMessage<::carbon::portal::farm::ZoneContents>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace farm {

// ===================================================================

class Farm final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.Farm) */ {
 public:
  inline Farm() : Farm(nullptr) {}
  ~Farm() override;
  explicit constexpr Farm(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Farm(const Farm& from);
  Farm(Farm&& from) noexcept
    : Farm() {
    *this = ::std::move(from);
  }

  inline Farm& operator=(const Farm& from) {
    CopyFrom(from);
    return *this;
  }
  inline Farm& operator=(Farm&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Farm& default_instance() {
    return *internal_default_instance();
  }
  static inline const Farm* internal_default_instance() {
    return reinterpret_cast<const Farm*>(
               &_Farm_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Farm& a, Farm& b) {
    a.Swap(&b);
  }
  inline void Swap(Farm* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Farm* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Farm* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Farm>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Farm& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Farm& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Farm* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.Farm";
  }
  protected:
  explicit Farm(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointDefsFieldNumber = 5,
    kZonesFieldNumber = 6,
    kNameFieldNumber = 3,
    kIdFieldNumber = 1,
    kVersionFieldNumber = 2,
    kCustomerIdFieldNumber = 4,
  };
  // repeated .carbon.portal.farm.PointDefinition point_defs = 5;
  int point_defs_size() const;
  private:
  int _internal_point_defs_size() const;
  public:
  void clear_point_defs();
  ::carbon::portal::farm::PointDefinition* mutable_point_defs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::PointDefinition >*
      mutable_point_defs();
  private:
  const ::carbon::portal::farm::PointDefinition& _internal_point_defs(int index) const;
  ::carbon::portal::farm::PointDefinition* _internal_add_point_defs();
  public:
  const ::carbon::portal::farm::PointDefinition& point_defs(int index) const;
  ::carbon::portal::farm::PointDefinition* add_point_defs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::PointDefinition >&
      point_defs() const;

  // repeated .carbon.portal.farm.Zone zones = 6;
  int zones_size() const;
  private:
  int _internal_zones_size() const;
  public:
  void clear_zones();
  ::carbon::portal::farm::Zone* mutable_zones(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Zone >*
      mutable_zones();
  private:
  const ::carbon::portal::farm::Zone& _internal_zones(int index) const;
  ::carbon::portal::farm::Zone* _internal_add_zones();
  public:
  const ::carbon::portal::farm::Zone& zones(int index) const;
  ::carbon::portal::farm::Zone* add_zones();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Zone >&
      zones() const;

  // string name = 3;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .carbon.geo.Id id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  const ::carbon::geo::Id& id() const;
  PROTOBUF_NODISCARD ::carbon::geo::Id* release_id();
  ::carbon::geo::Id* mutable_id();
  void set_allocated_id(::carbon::geo::Id* id);
  private:
  const ::carbon::geo::Id& _internal_id() const;
  ::carbon::geo::Id* _internal_mutable_id();
  public:
  void unsafe_arena_set_allocated_id(
      ::carbon::geo::Id* id);
  ::carbon::geo::Id* unsafe_arena_release_id();

  // .carbon.portal.farm.VersionInfo version = 2;
  bool has_version() const;
  private:
  bool _internal_has_version() const;
  public:
  void clear_version();
  const ::carbon::portal::farm::VersionInfo& version() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::VersionInfo* release_version();
  ::carbon::portal::farm::VersionInfo* mutable_version();
  void set_allocated_version(::carbon::portal::farm::VersionInfo* version);
  private:
  const ::carbon::portal::farm::VersionInfo& _internal_version() const;
  ::carbon::portal::farm::VersionInfo* _internal_mutable_version();
  public:
  void unsafe_arena_set_allocated_version(
      ::carbon::portal::farm::VersionInfo* version);
  ::carbon::portal::farm::VersionInfo* unsafe_arena_release_version();

  // int64 customer_id = 4;
  void clear_customer_id();
  int64_t customer_id() const;
  void set_customer_id(int64_t value);
  private:
  int64_t _internal_customer_id() const;
  void _internal_set_customer_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.Farm)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::PointDefinition > point_defs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Zone > zones_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::carbon::geo::Id* id_;
  ::carbon::portal::farm::VersionInfo* version_;
  int64_t customer_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class VersionInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.VersionInfo) */ {
 public:
  inline VersionInfo() : VersionInfo(nullptr) {}
  ~VersionInfo() override;
  explicit constexpr VersionInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VersionInfo(const VersionInfo& from);
  VersionInfo(VersionInfo&& from) noexcept
    : VersionInfo() {
    *this = ::std::move(from);
  }

  inline VersionInfo& operator=(const VersionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline VersionInfo& operator=(VersionInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VersionInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const VersionInfo* internal_default_instance() {
    return reinterpret_cast<const VersionInfo*>(
               &_VersionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(VersionInfo& a, VersionInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(VersionInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VersionInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VersionInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VersionInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VersionInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VersionInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VersionInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.VersionInfo";
  }
  protected:
  explicit VersionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUpdateTimeFieldNumber = 2,
    kOrdinalFieldNumber = 1,
    kDeletedFieldNumber = 3,
    kChangedFieldNumber = 4,
  };
  // .google.protobuf.Timestamp update_time = 2;
  bool has_update_time() const;
  private:
  bool _internal_has_update_time() const;
  public:
  void clear_update_time();
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& update_time() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Timestamp* release_update_time();
  ::PROTOBUF_NAMESPACE_ID::Timestamp* mutable_update_time();
  void set_allocated_update_time(::PROTOBUF_NAMESPACE_ID::Timestamp* update_time);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& _internal_update_time() const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _internal_mutable_update_time();
  public:
  void unsafe_arena_set_allocated_update_time(
      ::PROTOBUF_NAMESPACE_ID::Timestamp* update_time);
  ::PROTOBUF_NAMESPACE_ID::Timestamp* unsafe_arena_release_update_time();

  // int64 ordinal = 1;
  void clear_ordinal();
  int64_t ordinal() const;
  void set_ordinal(int64_t value);
  private:
  int64_t _internal_ordinal() const;
  void _internal_set_ordinal(int64_t value);
  public:

  // bool deleted = 3;
  void clear_deleted();
  bool deleted() const;
  void set_deleted(bool value);
  private:
  bool _internal_deleted() const;
  void _internal_set_deleted(bool value);
  public:

  // bool changed = 4;
  void clear_changed();
  bool changed() const;
  void set_changed(bool value);
  private:
  bool _internal_changed() const;
  void _internal_set_changed(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.VersionInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* update_time_;
  int64_t ordinal_;
  bool deleted_;
  bool changed_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class PointDefinition final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.PointDefinition) */ {
 public:
  inline PointDefinition() : PointDefinition(nullptr) {}
  ~PointDefinition() override;
  explicit constexpr PointDefinition(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PointDefinition(const PointDefinition& from);
  PointDefinition(PointDefinition&& from) noexcept
    : PointDefinition() {
    *this = ::std::move(from);
  }

  inline PointDefinition& operator=(const PointDefinition& from) {
    CopyFrom(from);
    return *this;
  }
  inline PointDefinition& operator=(PointDefinition&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PointDefinition& default_instance() {
    return *internal_default_instance();
  }
  static inline const PointDefinition* internal_default_instance() {
    return reinterpret_cast<const PointDefinition*>(
               &_PointDefinition_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(PointDefinition& a, PointDefinition& b) {
    a.Swap(&b);
  }
  inline void Swap(PointDefinition* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PointDefinition* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PointDefinition* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PointDefinition>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PointDefinition& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PointDefinition& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PointDefinition* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.PointDefinition";
  }
  protected:
  explicit PointDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointFieldNumber = 1,
    kVersionFieldNumber = 2,
  };
  // .carbon.geo.Point point = 1;
  bool has_point() const;
  private:
  bool _internal_has_point() const;
  public:
  void clear_point();
  const ::carbon::geo::Point& point() const;
  PROTOBUF_NODISCARD ::carbon::geo::Point* release_point();
  ::carbon::geo::Point* mutable_point();
  void set_allocated_point(::carbon::geo::Point* point);
  private:
  const ::carbon::geo::Point& _internal_point() const;
  ::carbon::geo::Point* _internal_mutable_point();
  public:
  void unsafe_arena_set_allocated_point(
      ::carbon::geo::Point* point);
  ::carbon::geo::Point* unsafe_arena_release_point();

  // .carbon.portal.farm.VersionInfo version = 2;
  bool has_version() const;
  private:
  bool _internal_has_version() const;
  public:
  void clear_version();
  const ::carbon::portal::farm::VersionInfo& version() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::VersionInfo* release_version();
  ::carbon::portal::farm::VersionInfo* mutable_version();
  void set_allocated_version(::carbon::portal::farm::VersionInfo* version);
  private:
  const ::carbon::portal::farm::VersionInfo& _internal_version() const;
  ::carbon::portal::farm::VersionInfo* _internal_mutable_version();
  public:
  void unsafe_arena_set_allocated_version(
      ::carbon::portal::farm::VersionInfo* version);
  ::carbon::portal::farm::VersionInfo* unsafe_arena_release_version();

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.PointDefinition)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::geo::Point* point_;
  ::carbon::portal::farm::VersionInfo* version_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class Zone final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.Zone) */ {
 public:
  inline Zone() : Zone(nullptr) {}
  ~Zone() override;
  explicit constexpr Zone(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Zone(const Zone& from);
  Zone(Zone&& from) noexcept
    : Zone() {
    *this = ::std::move(from);
  }

  inline Zone& operator=(const Zone& from) {
    CopyFrom(from);
    return *this;
  }
  inline Zone& operator=(Zone&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Zone& default_instance() {
    return *internal_default_instance();
  }
  static inline const Zone* internal_default_instance() {
    return reinterpret_cast<const Zone*>(
               &_Zone_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Zone& a, Zone& b) {
    a.Swap(&b);
  }
  inline void Swap(Zone* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Zone* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Zone* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Zone>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Zone& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Zone& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Zone* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.Zone";
  }
  protected:
  explicit Zone(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAreasFieldNumber = 4,
    kNameFieldNumber = 3,
    kIdFieldNumber = 1,
    kVersionFieldNumber = 2,
    kContentsFieldNumber = 5,
  };
  // repeated .carbon.portal.farm.Area areas = 4;
  int areas_size() const;
  private:
  int _internal_areas_size() const;
  public:
  void clear_areas();
  ::carbon::portal::farm::Area* mutable_areas(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Area >*
      mutable_areas();
  private:
  const ::carbon::portal::farm::Area& _internal_areas(int index) const;
  ::carbon::portal::farm::Area* _internal_add_areas();
  public:
  const ::carbon::portal::farm::Area& areas(int index) const;
  ::carbon::portal::farm::Area* add_areas();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Area >&
      areas() const;

  // string name = 3;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .carbon.geo.Id id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  const ::carbon::geo::Id& id() const;
  PROTOBUF_NODISCARD ::carbon::geo::Id* release_id();
  ::carbon::geo::Id* mutable_id();
  void set_allocated_id(::carbon::geo::Id* id);
  private:
  const ::carbon::geo::Id& _internal_id() const;
  ::carbon::geo::Id* _internal_mutable_id();
  public:
  void unsafe_arena_set_allocated_id(
      ::carbon::geo::Id* id);
  ::carbon::geo::Id* unsafe_arena_release_id();

  // .carbon.portal.farm.VersionInfo version = 2;
  bool has_version() const;
  private:
  bool _internal_has_version() const;
  public:
  void clear_version();
  const ::carbon::portal::farm::VersionInfo& version() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::VersionInfo* release_version();
  ::carbon::portal::farm::VersionInfo* mutable_version();
  void set_allocated_version(::carbon::portal::farm::VersionInfo* version);
  private:
  const ::carbon::portal::farm::VersionInfo& _internal_version() const;
  ::carbon::portal::farm::VersionInfo* _internal_mutable_version();
  public:
  void unsafe_arena_set_allocated_version(
      ::carbon::portal::farm::VersionInfo* version);
  ::carbon::portal::farm::VersionInfo* unsafe_arena_release_version();

  // .carbon.portal.farm.ZoneContents contents = 5;
  bool has_contents() const;
  private:
  bool _internal_has_contents() const;
  public:
  void clear_contents();
  const ::carbon::portal::farm::ZoneContents& contents() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::ZoneContents* release_contents();
  ::carbon::portal::farm::ZoneContents* mutable_contents();
  void set_allocated_contents(::carbon::portal::farm::ZoneContents* contents);
  private:
  const ::carbon::portal::farm::ZoneContents& _internal_contents() const;
  ::carbon::portal::farm::ZoneContents* _internal_mutable_contents();
  public:
  void unsafe_arena_set_allocated_contents(
      ::carbon::portal::farm::ZoneContents* contents);
  ::carbon::portal::farm::ZoneContents* unsafe_arena_release_contents();

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.Zone)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Area > areas_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::carbon::geo::Id* id_;
  ::carbon::portal::farm::VersionInfo* version_;
  ::carbon::portal::farm::ZoneContents* contents_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class ZoneContents final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.ZoneContents) */ {
 public:
  inline ZoneContents() : ZoneContents(nullptr) {}
  ~ZoneContents() override;
  explicit constexpr ZoneContents(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ZoneContents(const ZoneContents& from);
  ZoneContents(ZoneContents&& from) noexcept
    : ZoneContents() {
    *this = ::std::move(from);
  }

  inline ZoneContents& operator=(const ZoneContents& from) {
    CopyFrom(from);
    return *this;
  }
  inline ZoneContents& operator=(ZoneContents&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ZoneContents& default_instance() {
    return *internal_default_instance();
  }
  enum DataCase {
    kFarmBoundary = 9,
    kField = 5,
    kHeadland = 6,
    kPrivateRoad = 7,
    kObstacle = 8,
    DATA_NOT_SET = 0,
  };

  static inline const ZoneContents* internal_default_instance() {
    return reinterpret_cast<const ZoneContents*>(
               &_ZoneContents_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ZoneContents& a, ZoneContents& b) {
    a.Swap(&b);
  }
  inline void Swap(ZoneContents* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ZoneContents* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ZoneContents* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ZoneContents>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ZoneContents& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ZoneContents& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ZoneContents* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.ZoneContents";
  }
  protected:
  explicit ZoneContents(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFarmBoundaryFieldNumber = 9,
    kFieldFieldNumber = 5,
    kHeadlandFieldNumber = 6,
    kPrivateRoadFieldNumber = 7,
    kObstacleFieldNumber = 8,
  };
  // .carbon.portal.farm.FarmBoundaryData farm_boundary = 9;
  bool has_farm_boundary() const;
  private:
  bool _internal_has_farm_boundary() const;
  public:
  void clear_farm_boundary();
  const ::carbon::portal::farm::FarmBoundaryData& farm_boundary() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::FarmBoundaryData* release_farm_boundary();
  ::carbon::portal::farm::FarmBoundaryData* mutable_farm_boundary();
  void set_allocated_farm_boundary(::carbon::portal::farm::FarmBoundaryData* farm_boundary);
  private:
  const ::carbon::portal::farm::FarmBoundaryData& _internal_farm_boundary() const;
  ::carbon::portal::farm::FarmBoundaryData* _internal_mutable_farm_boundary();
  public:
  void unsafe_arena_set_allocated_farm_boundary(
      ::carbon::portal::farm::FarmBoundaryData* farm_boundary);
  ::carbon::portal::farm::FarmBoundaryData* unsafe_arena_release_farm_boundary();

  // .carbon.portal.farm.FieldData field = 5;
  bool has_field() const;
  private:
  bool _internal_has_field() const;
  public:
  void clear_field();
  const ::carbon::portal::farm::FieldData& field() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::FieldData* release_field();
  ::carbon::portal::farm::FieldData* mutable_field();
  void set_allocated_field(::carbon::portal::farm::FieldData* field);
  private:
  const ::carbon::portal::farm::FieldData& _internal_field() const;
  ::carbon::portal::farm::FieldData* _internal_mutable_field();
  public:
  void unsafe_arena_set_allocated_field(
      ::carbon::portal::farm::FieldData* field);
  ::carbon::portal::farm::FieldData* unsafe_arena_release_field();

  // .carbon.portal.farm.HeadlandData headland = 6;
  bool has_headland() const;
  private:
  bool _internal_has_headland() const;
  public:
  void clear_headland();
  const ::carbon::portal::farm::HeadlandData& headland() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::HeadlandData* release_headland();
  ::carbon::portal::farm::HeadlandData* mutable_headland();
  void set_allocated_headland(::carbon::portal::farm::HeadlandData* headland);
  private:
  const ::carbon::portal::farm::HeadlandData& _internal_headland() const;
  ::carbon::portal::farm::HeadlandData* _internal_mutable_headland();
  public:
  void unsafe_arena_set_allocated_headland(
      ::carbon::portal::farm::HeadlandData* headland);
  ::carbon::portal::farm::HeadlandData* unsafe_arena_release_headland();

  // .carbon.portal.farm.PrivateRoadData private_road = 7;
  bool has_private_road() const;
  private:
  bool _internal_has_private_road() const;
  public:
  void clear_private_road();
  const ::carbon::portal::farm::PrivateRoadData& private_road() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::PrivateRoadData* release_private_road();
  ::carbon::portal::farm::PrivateRoadData* mutable_private_road();
  void set_allocated_private_road(::carbon::portal::farm::PrivateRoadData* private_road);
  private:
  const ::carbon::portal::farm::PrivateRoadData& _internal_private_road() const;
  ::carbon::portal::farm::PrivateRoadData* _internal_mutable_private_road();
  public:
  void unsafe_arena_set_allocated_private_road(
      ::carbon::portal::farm::PrivateRoadData* private_road);
  ::carbon::portal::farm::PrivateRoadData* unsafe_arena_release_private_road();

  // .carbon.portal.farm.ObstacleData obstacle = 8;
  bool has_obstacle() const;
  private:
  bool _internal_has_obstacle() const;
  public:
  void clear_obstacle();
  const ::carbon::portal::farm::ObstacleData& obstacle() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::ObstacleData* release_obstacle();
  ::carbon::portal::farm::ObstacleData* mutable_obstacle();
  void set_allocated_obstacle(::carbon::portal::farm::ObstacleData* obstacle);
  private:
  const ::carbon::portal::farm::ObstacleData& _internal_obstacle() const;
  ::carbon::portal::farm::ObstacleData* _internal_mutable_obstacle();
  public:
  void unsafe_arena_set_allocated_obstacle(
      ::carbon::portal::farm::ObstacleData* obstacle);
  ::carbon::portal::farm::ObstacleData* unsafe_arena_release_obstacle();

  void clear_data();
  DataCase data_case() const;
  // @@protoc_insertion_point(class_scope:carbon.portal.farm.ZoneContents)
 private:
  class _Internal;
  void set_has_farm_boundary();
  void set_has_field();
  void set_has_headland();
  void set_has_private_road();
  void set_has_obstacle();

  inline bool has_data() const;
  inline void clear_has_data();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union DataUnion {
    constexpr DataUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::portal::farm::FarmBoundaryData* farm_boundary_;
    ::carbon::portal::farm::FieldData* field_;
    ::carbon::portal::farm::HeadlandData* headland_;
    ::carbon::portal::farm::PrivateRoadData* private_road_;
    ::carbon::portal::farm::ObstacleData* obstacle_;
  } data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class Area final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.Area) */ {
 public:
  inline Area() : Area(nullptr) {}
  ~Area() override;
  explicit constexpr Area(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Area(const Area& from);
  Area(Area&& from) noexcept
    : Area() {
    *this = ::std::move(from);
  }

  inline Area& operator=(const Area& from) {
    CopyFrom(from);
    return *this;
  }
  inline Area& operator=(Area&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Area& default_instance() {
    return *internal_default_instance();
  }
  enum GeometryCase {
    kPoint = 2,
    kLineString = 3,
    kPolygon = 4,
    GEOMETRY_NOT_SET = 0,
  };

  static inline const Area* internal_default_instance() {
    return reinterpret_cast<const Area*>(
               &_Area_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Area& a, Area& b) {
    a.Swap(&b);
  }
  inline void Swap(Area* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Area* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Area* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Area>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Area& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Area& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Area* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.Area";
  }
  protected:
  explicit Area(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBufferMetersFieldNumber = 1,
    kPointFieldNumber = 2,
    kLineStringFieldNumber = 3,
    kPolygonFieldNumber = 4,
  };
  // double buffer_meters = 1;
  void clear_buffer_meters();
  double buffer_meters() const;
  void set_buffer_meters(double value);
  private:
  double _internal_buffer_meters() const;
  void _internal_set_buffer_meters(double value);
  public:

  // .carbon.geo.Point point = 2;
  bool has_point() const;
  private:
  bool _internal_has_point() const;
  public:
  void clear_point();
  const ::carbon::geo::Point& point() const;
  PROTOBUF_NODISCARD ::carbon::geo::Point* release_point();
  ::carbon::geo::Point* mutable_point();
  void set_allocated_point(::carbon::geo::Point* point);
  private:
  const ::carbon::geo::Point& _internal_point() const;
  ::carbon::geo::Point* _internal_mutable_point();
  public:
  void unsafe_arena_set_allocated_point(
      ::carbon::geo::Point* point);
  ::carbon::geo::Point* unsafe_arena_release_point();

  // .carbon.geo.LineString line_string = 3;
  bool has_line_string() const;
  private:
  bool _internal_has_line_string() const;
  public:
  void clear_line_string();
  const ::carbon::geo::LineString& line_string() const;
  PROTOBUF_NODISCARD ::carbon::geo::LineString* release_line_string();
  ::carbon::geo::LineString* mutable_line_string();
  void set_allocated_line_string(::carbon::geo::LineString* line_string);
  private:
  const ::carbon::geo::LineString& _internal_line_string() const;
  ::carbon::geo::LineString* _internal_mutable_line_string();
  public:
  void unsafe_arena_set_allocated_line_string(
      ::carbon::geo::LineString* line_string);
  ::carbon::geo::LineString* unsafe_arena_release_line_string();

  // .carbon.geo.Polygon polygon = 4;
  bool has_polygon() const;
  private:
  bool _internal_has_polygon() const;
  public:
  void clear_polygon();
  const ::carbon::geo::Polygon& polygon() const;
  PROTOBUF_NODISCARD ::carbon::geo::Polygon* release_polygon();
  ::carbon::geo::Polygon* mutable_polygon();
  void set_allocated_polygon(::carbon::geo::Polygon* polygon);
  private:
  const ::carbon::geo::Polygon& _internal_polygon() const;
  ::carbon::geo::Polygon* _internal_mutable_polygon();
  public:
  void unsafe_arena_set_allocated_polygon(
      ::carbon::geo::Polygon* polygon);
  ::carbon::geo::Polygon* unsafe_arena_release_polygon();

  void clear_geometry();
  GeometryCase geometry_case() const;
  // @@protoc_insertion_point(class_scope:carbon.portal.farm.Area)
 private:
  class _Internal;
  void set_has_point();
  void set_has_line_string();
  void set_has_polygon();

  inline bool has_geometry() const;
  inline void clear_has_geometry();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double buffer_meters_;
  union GeometryUnion {
    constexpr GeometryUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::geo::Point* point_;
    ::carbon::geo::LineString* line_string_;
    ::carbon::geo::Polygon* polygon_;
  } geometry_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class FarmBoundaryData final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.portal.farm.FarmBoundaryData) */ {
 public:
  inline FarmBoundaryData() : FarmBoundaryData(nullptr) {}
  explicit constexpr FarmBoundaryData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FarmBoundaryData(const FarmBoundaryData& from);
  FarmBoundaryData(FarmBoundaryData&& from) noexcept
    : FarmBoundaryData() {
    *this = ::std::move(from);
  }

  inline FarmBoundaryData& operator=(const FarmBoundaryData& from) {
    CopyFrom(from);
    return *this;
  }
  inline FarmBoundaryData& operator=(FarmBoundaryData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FarmBoundaryData& default_instance() {
    return *internal_default_instance();
  }
  static inline const FarmBoundaryData* internal_default_instance() {
    return reinterpret_cast<const FarmBoundaryData*>(
               &_FarmBoundaryData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(FarmBoundaryData& a, FarmBoundaryData& b) {
    a.Swap(&b);
  }
  inline void Swap(FarmBoundaryData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FarmBoundaryData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FarmBoundaryData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FarmBoundaryData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const FarmBoundaryData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const FarmBoundaryData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.FarmBoundaryData";
  }
  protected:
  explicit FarmBoundaryData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.FarmBoundaryData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class FieldData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.FieldData) */ {
 public:
  inline FieldData() : FieldData(nullptr) {}
  ~FieldData() override;
  explicit constexpr FieldData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FieldData(const FieldData& from);
  FieldData(FieldData&& from) noexcept
    : FieldData() {
    *this = ::std::move(from);
  }

  inline FieldData& operator=(const FieldData& from) {
    CopyFrom(from);
    return *this;
  }
  inline FieldData& operator=(FieldData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FieldData& default_instance() {
    return *internal_default_instance();
  }
  static inline const FieldData* internal_default_instance() {
    return reinterpret_cast<const FieldData*>(
               &_FieldData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(FieldData& a, FieldData& b) {
    a.Swap(&b);
  }
  inline void Swap(FieldData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FieldData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FieldData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FieldData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FieldData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FieldData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FieldData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.FieldData";
  }
  protected:
  explicit FieldData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlantingHeadingFieldNumber = 1,
    kCenterPivotFieldNumber = 2,
  };
  // .carbon.portal.farm.PlantingHeading planting_heading = 1;
  bool has_planting_heading() const;
  private:
  bool _internal_has_planting_heading() const;
  public:
  void clear_planting_heading();
  const ::carbon::portal::farm::PlantingHeading& planting_heading() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::PlantingHeading* release_planting_heading();
  ::carbon::portal::farm::PlantingHeading* mutable_planting_heading();
  void set_allocated_planting_heading(::carbon::portal::farm::PlantingHeading* planting_heading);
  private:
  const ::carbon::portal::farm::PlantingHeading& _internal_planting_heading() const;
  ::carbon::portal::farm::PlantingHeading* _internal_mutable_planting_heading();
  public:
  void unsafe_arena_set_allocated_planting_heading(
      ::carbon::portal::farm::PlantingHeading* planting_heading);
  ::carbon::portal::farm::PlantingHeading* unsafe_arena_release_planting_heading();

  // .carbon.portal.farm.CenterPivot center_pivot = 2;
  bool has_center_pivot() const;
  private:
  bool _internal_has_center_pivot() const;
  public:
  void clear_center_pivot();
  const ::carbon::portal::farm::CenterPivot& center_pivot() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::CenterPivot* release_center_pivot();
  ::carbon::portal::farm::CenterPivot* mutable_center_pivot();
  void set_allocated_center_pivot(::carbon::portal::farm::CenterPivot* center_pivot);
  private:
  const ::carbon::portal::farm::CenterPivot& _internal_center_pivot() const;
  ::carbon::portal::farm::CenterPivot* _internal_mutable_center_pivot();
  public:
  void unsafe_arena_set_allocated_center_pivot(
      ::carbon::portal::farm::CenterPivot* center_pivot);
  ::carbon::portal::farm::CenterPivot* unsafe_arena_release_center_pivot();

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.FieldData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::portal::farm::PlantingHeading* planting_heading_;
  ::carbon::portal::farm::CenterPivot* center_pivot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class HeadlandData final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.portal.farm.HeadlandData) */ {
 public:
  inline HeadlandData() : HeadlandData(nullptr) {}
  explicit constexpr HeadlandData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HeadlandData(const HeadlandData& from);
  HeadlandData(HeadlandData&& from) noexcept
    : HeadlandData() {
    *this = ::std::move(from);
  }

  inline HeadlandData& operator=(const HeadlandData& from) {
    CopyFrom(from);
    return *this;
  }
  inline HeadlandData& operator=(HeadlandData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HeadlandData& default_instance() {
    return *internal_default_instance();
  }
  static inline const HeadlandData* internal_default_instance() {
    return reinterpret_cast<const HeadlandData*>(
               &_HeadlandData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(HeadlandData& a, HeadlandData& b) {
    a.Swap(&b);
  }
  inline void Swap(HeadlandData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HeadlandData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HeadlandData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HeadlandData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const HeadlandData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const HeadlandData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.HeadlandData";
  }
  protected:
  explicit HeadlandData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.HeadlandData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class PrivateRoadData final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.portal.farm.PrivateRoadData) */ {
 public:
  inline PrivateRoadData() : PrivateRoadData(nullptr) {}
  explicit constexpr PrivateRoadData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PrivateRoadData(const PrivateRoadData& from);
  PrivateRoadData(PrivateRoadData&& from) noexcept
    : PrivateRoadData() {
    *this = ::std::move(from);
  }

  inline PrivateRoadData& operator=(const PrivateRoadData& from) {
    CopyFrom(from);
    return *this;
  }
  inline PrivateRoadData& operator=(PrivateRoadData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PrivateRoadData& default_instance() {
    return *internal_default_instance();
  }
  static inline const PrivateRoadData* internal_default_instance() {
    return reinterpret_cast<const PrivateRoadData*>(
               &_PrivateRoadData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(PrivateRoadData& a, PrivateRoadData& b) {
    a.Swap(&b);
  }
  inline void Swap(PrivateRoadData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PrivateRoadData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PrivateRoadData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PrivateRoadData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const PrivateRoadData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const PrivateRoadData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.PrivateRoadData";
  }
  protected:
  explicit PrivateRoadData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.PrivateRoadData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class ObstacleData final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.portal.farm.ObstacleData) */ {
 public:
  inline ObstacleData() : ObstacleData(nullptr) {}
  explicit constexpr ObstacleData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ObstacleData(const ObstacleData& from);
  ObstacleData(ObstacleData&& from) noexcept
    : ObstacleData() {
    *this = ::std::move(from);
  }

  inline ObstacleData& operator=(const ObstacleData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ObstacleData& operator=(ObstacleData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ObstacleData& default_instance() {
    return *internal_default_instance();
  }
  static inline const ObstacleData* internal_default_instance() {
    return reinterpret_cast<const ObstacleData*>(
               &_ObstacleData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(ObstacleData& a, ObstacleData& b) {
    a.Swap(&b);
  }
  inline void Swap(ObstacleData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ObstacleData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ObstacleData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ObstacleData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const ObstacleData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const ObstacleData& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.ObstacleData";
  }
  protected:
  explicit ObstacleData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.ObstacleData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class PlantingHeading final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.PlantingHeading) */ {
 public:
  inline PlantingHeading() : PlantingHeading(nullptr) {}
  ~PlantingHeading() override;
  explicit constexpr PlantingHeading(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PlantingHeading(const PlantingHeading& from);
  PlantingHeading(PlantingHeading&& from) noexcept
    : PlantingHeading() {
    *this = ::std::move(from);
  }

  inline PlantingHeading& operator=(const PlantingHeading& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlantingHeading& operator=(PlantingHeading&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PlantingHeading& default_instance() {
    return *internal_default_instance();
  }
  enum HeadingCase {
    kAzimuthDegrees = 1,
    kAbLine = 2,
    HEADING_NOT_SET = 0,
  };

  static inline const PlantingHeading* internal_default_instance() {
    return reinterpret_cast<const PlantingHeading*>(
               &_PlantingHeading_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(PlantingHeading& a, PlantingHeading& b) {
    a.Swap(&b);
  }
  inline void Swap(PlantingHeading* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlantingHeading* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PlantingHeading* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PlantingHeading>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PlantingHeading& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PlantingHeading& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlantingHeading* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.PlantingHeading";
  }
  protected:
  explicit PlantingHeading(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAzimuthDegreesFieldNumber = 1,
    kAbLineFieldNumber = 2,
  };
  // double azimuth_degrees = 1;
  bool has_azimuth_degrees() const;
  private:
  bool _internal_has_azimuth_degrees() const;
  public:
  void clear_azimuth_degrees();
  double azimuth_degrees() const;
  void set_azimuth_degrees(double value);
  private:
  double _internal_azimuth_degrees() const;
  void _internal_set_azimuth_degrees(double value);
  public:

  // .carbon.geo.AbLine ab_line = 2;
  bool has_ab_line() const;
  private:
  bool _internal_has_ab_line() const;
  public:
  void clear_ab_line();
  const ::carbon::geo::AbLine& ab_line() const;
  PROTOBUF_NODISCARD ::carbon::geo::AbLine* release_ab_line();
  ::carbon::geo::AbLine* mutable_ab_line();
  void set_allocated_ab_line(::carbon::geo::AbLine* ab_line);
  private:
  const ::carbon::geo::AbLine& _internal_ab_line() const;
  ::carbon::geo::AbLine* _internal_mutable_ab_line();
  public:
  void unsafe_arena_set_allocated_ab_line(
      ::carbon::geo::AbLine* ab_line);
  ::carbon::geo::AbLine* unsafe_arena_release_ab_line();

  void clear_heading();
  HeadingCase heading_case() const;
  // @@protoc_insertion_point(class_scope:carbon.portal.farm.PlantingHeading)
 private:
  class _Internal;
  void set_has_azimuth_degrees();
  void set_has_ab_line();

  inline bool has_heading() const;
  inline void clear_has_heading();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union HeadingUnion {
    constexpr HeadingUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    double azimuth_degrees_;
    ::carbon::geo::AbLine* ab_line_;
  } heading_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class CenterPivot final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.CenterPivot) */ {
 public:
  inline CenterPivot() : CenterPivot(nullptr) {}
  ~CenterPivot() override;
  explicit constexpr CenterPivot(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CenterPivot(const CenterPivot& from);
  CenterPivot(CenterPivot&& from) noexcept
    : CenterPivot() {
    *this = ::std::move(from);
  }

  inline CenterPivot& operator=(const CenterPivot& from) {
    CopyFrom(from);
    return *this;
  }
  inline CenterPivot& operator=(CenterPivot&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CenterPivot& default_instance() {
    return *internal_default_instance();
  }
  static inline const CenterPivot* internal_default_instance() {
    return reinterpret_cast<const CenterPivot*>(
               &_CenterPivot_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(CenterPivot& a, CenterPivot& b) {
    a.Swap(&b);
  }
  inline void Swap(CenterPivot* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CenterPivot* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CenterPivot* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CenterPivot>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CenterPivot& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CenterPivot& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CenterPivot* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.CenterPivot";
  }
  protected:
  explicit CenterPivot(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCenterFieldNumber = 1,
    kWidthMetersFieldNumber = 2,
    kLengthMetersFieldNumber = 3,
  };
  // .carbon.geo.Point center = 1;
  bool has_center() const;
  private:
  bool _internal_has_center() const;
  public:
  void clear_center();
  const ::carbon::geo::Point& center() const;
  PROTOBUF_NODISCARD ::carbon::geo::Point* release_center();
  ::carbon::geo::Point* mutable_center();
  void set_allocated_center(::carbon::geo::Point* center);
  private:
  const ::carbon::geo::Point& _internal_center() const;
  ::carbon::geo::Point* _internal_mutable_center();
  public:
  void unsafe_arena_set_allocated_center(
      ::carbon::geo::Point* center);
  ::carbon::geo::Point* unsafe_arena_release_center();

  // double width_meters = 2;
  void clear_width_meters();
  double width_meters() const;
  void set_width_meters(double value);
  private:
  double _internal_width_meters() const;
  void _internal_set_width_meters(double value);
  public:

  // double length_meters = 3;
  void clear_length_meters();
  double length_meters() const;
  void set_length_meters(double value);
  private:
  double _internal_length_meters() const;
  void _internal_set_length_meters(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.CenterPivot)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::geo::Point* center_;
  double width_meters_;
  double length_meters_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class CreateFarmRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.CreateFarmRequest) */ {
 public:
  inline CreateFarmRequest() : CreateFarmRequest(nullptr) {}
  ~CreateFarmRequest() override;
  explicit constexpr CreateFarmRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateFarmRequest(const CreateFarmRequest& from);
  CreateFarmRequest(CreateFarmRequest&& from) noexcept
    : CreateFarmRequest() {
    *this = ::std::move(from);
  }

  inline CreateFarmRequest& operator=(const CreateFarmRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateFarmRequest& operator=(CreateFarmRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CreateFarmRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateFarmRequest* internal_default_instance() {
    return reinterpret_cast<const CreateFarmRequest*>(
               &_CreateFarmRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(CreateFarmRequest& a, CreateFarmRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateFarmRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateFarmRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateFarmRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateFarmRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CreateFarmRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CreateFarmRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateFarmRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.CreateFarmRequest";
  }
  protected:
  explicit CreateFarmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFarmFieldNumber = 1,
  };
  // .carbon.portal.farm.Farm farm = 1;
  bool has_farm() const;
  private:
  bool _internal_has_farm() const;
  public:
  void clear_farm();
  const ::carbon::portal::farm::Farm& farm() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::Farm* release_farm();
  ::carbon::portal::farm::Farm* mutable_farm();
  void set_allocated_farm(::carbon::portal::farm::Farm* farm);
  private:
  const ::carbon::portal::farm::Farm& _internal_farm() const;
  ::carbon::portal::farm::Farm* _internal_mutable_farm();
  public:
  void unsafe_arena_set_allocated_farm(
      ::carbon::portal::farm::Farm* farm);
  ::carbon::portal::farm::Farm* unsafe_arena_release_farm();

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.CreateFarmRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::portal::farm::Farm* farm_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class UpdateFarmRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.UpdateFarmRequest) */ {
 public:
  inline UpdateFarmRequest() : UpdateFarmRequest(nullptr) {}
  ~UpdateFarmRequest() override;
  explicit constexpr UpdateFarmRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UpdateFarmRequest(const UpdateFarmRequest& from);
  UpdateFarmRequest(UpdateFarmRequest&& from) noexcept
    : UpdateFarmRequest() {
    *this = ::std::move(from);
  }

  inline UpdateFarmRequest& operator=(const UpdateFarmRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpdateFarmRequest& operator=(UpdateFarmRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UpdateFarmRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UpdateFarmRequest* internal_default_instance() {
    return reinterpret_cast<const UpdateFarmRequest*>(
               &_UpdateFarmRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(UpdateFarmRequest& a, UpdateFarmRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UpdateFarmRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UpdateFarmRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UpdateFarmRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UpdateFarmRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UpdateFarmRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UpdateFarmRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpdateFarmRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.UpdateFarmRequest";
  }
  protected:
  explicit UpdateFarmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFarmFieldNumber = 1,
  };
  // .carbon.portal.farm.Farm farm = 1;
  bool has_farm() const;
  private:
  bool _internal_has_farm() const;
  public:
  void clear_farm();
  const ::carbon::portal::farm::Farm& farm() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::Farm* release_farm();
  ::carbon::portal::farm::Farm* mutable_farm();
  void set_allocated_farm(::carbon::portal::farm::Farm* farm);
  private:
  const ::carbon::portal::farm::Farm& _internal_farm() const;
  ::carbon::portal::farm::Farm* _internal_mutable_farm();
  public:
  void unsafe_arena_set_allocated_farm(
      ::carbon::portal::farm::Farm* farm);
  ::carbon::portal::farm::Farm* unsafe_arena_release_farm();

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.UpdateFarmRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::portal::farm::Farm* farm_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class ListFarmsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.ListFarmsRequest) */ {
 public:
  inline ListFarmsRequest() : ListFarmsRequest(nullptr) {}
  ~ListFarmsRequest() override;
  explicit constexpr ListFarmsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListFarmsRequest(const ListFarmsRequest& from);
  ListFarmsRequest(ListFarmsRequest&& from) noexcept
    : ListFarmsRequest() {
    *this = ::std::move(from);
  }

  inline ListFarmsRequest& operator=(const ListFarmsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListFarmsRequest& operator=(ListFarmsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListFarmsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListFarmsRequest* internal_default_instance() {
    return reinterpret_cast<const ListFarmsRequest*>(
               &_ListFarmsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(ListFarmsRequest& a, ListFarmsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ListFarmsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListFarmsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListFarmsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListFarmsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListFarmsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ListFarmsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListFarmsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.ListFarmsRequest";
  }
  protected:
  explicit ListFarmsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPageTokenFieldNumber = 1,
  };
  // string page_token = 1;
  void clear_page_token();
  const std::string& page_token() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_page_token(ArgT0&& arg0, ArgT... args);
  std::string* mutable_page_token();
  PROTOBUF_NODISCARD std::string* release_page_token();
  void set_allocated_page_token(std::string* page_token);
  private:
  const std::string& _internal_page_token() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_page_token(const std::string& value);
  std::string* _internal_mutable_page_token();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.ListFarmsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr page_token_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class ListFarmsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.ListFarmsResponse) */ {
 public:
  inline ListFarmsResponse() : ListFarmsResponse(nullptr) {}
  ~ListFarmsResponse() override;
  explicit constexpr ListFarmsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListFarmsResponse(const ListFarmsResponse& from);
  ListFarmsResponse(ListFarmsResponse&& from) noexcept
    : ListFarmsResponse() {
    *this = ::std::move(from);
  }

  inline ListFarmsResponse& operator=(const ListFarmsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListFarmsResponse& operator=(ListFarmsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListFarmsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListFarmsResponse* internal_default_instance() {
    return reinterpret_cast<const ListFarmsResponse*>(
               &_ListFarmsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(ListFarmsResponse& a, ListFarmsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ListFarmsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListFarmsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListFarmsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListFarmsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListFarmsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ListFarmsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListFarmsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.ListFarmsResponse";
  }
  protected:
  explicit ListFarmsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFarmsFieldNumber = 1,
    kNextPageTokenFieldNumber = 2,
  };
  // repeated .carbon.portal.farm.Farm farms = 1;
  int farms_size() const;
  private:
  int _internal_farms_size() const;
  public:
  void clear_farms();
  ::carbon::portal::farm::Farm* mutable_farms(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Farm >*
      mutable_farms();
  private:
  const ::carbon::portal::farm::Farm& _internal_farms(int index) const;
  ::carbon::portal::farm::Farm* _internal_add_farms();
  public:
  const ::carbon::portal::farm::Farm& farms(int index) const;
  ::carbon::portal::farm::Farm* add_farms();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Farm >&
      farms() const;

  // string next_page_token = 2;
  void clear_next_page_token();
  const std::string& next_page_token() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_next_page_token(ArgT0&& arg0, ArgT... args);
  std::string* mutable_next_page_token();
  PROTOBUF_NODISCARD std::string* release_next_page_token();
  void set_allocated_next_page_token(std::string* next_page_token);
  private:
  const std::string& _internal_next_page_token() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_next_page_token(const std::string& value);
  std::string* _internal_mutable_next_page_token();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.ListFarmsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Farm > farms_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr next_page_token_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class GetFarmRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.GetFarmRequest) */ {
 public:
  inline GetFarmRequest() : GetFarmRequest(nullptr) {}
  ~GetFarmRequest() override;
  explicit constexpr GetFarmRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetFarmRequest(const GetFarmRequest& from);
  GetFarmRequest(GetFarmRequest&& from) noexcept
    : GetFarmRequest() {
    *this = ::std::move(from);
  }

  inline GetFarmRequest& operator=(const GetFarmRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetFarmRequest& operator=(GetFarmRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetFarmRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetFarmRequest* internal_default_instance() {
    return reinterpret_cast<const GetFarmRequest*>(
               &_GetFarmRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(GetFarmRequest& a, GetFarmRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetFarmRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetFarmRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetFarmRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetFarmRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetFarmRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetFarmRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetFarmRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.GetFarmRequest";
  }
  protected:
  explicit GetFarmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kIfModifiedSinceFieldNumber = 2,
  };
  // .carbon.geo.Id id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  const ::carbon::geo::Id& id() const;
  PROTOBUF_NODISCARD ::carbon::geo::Id* release_id();
  ::carbon::geo::Id* mutable_id();
  void set_allocated_id(::carbon::geo::Id* id);
  private:
  const ::carbon::geo::Id& _internal_id() const;
  ::carbon::geo::Id* _internal_mutable_id();
  public:
  void unsafe_arena_set_allocated_id(
      ::carbon::geo::Id* id);
  ::carbon::geo::Id* unsafe_arena_release_id();

  // .google.protobuf.Timestamp if_modified_since = 2;
  bool has_if_modified_since() const;
  private:
  bool _internal_has_if_modified_since() const;
  public:
  void clear_if_modified_since();
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& if_modified_since() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Timestamp* release_if_modified_since();
  ::PROTOBUF_NAMESPACE_ID::Timestamp* mutable_if_modified_since();
  void set_allocated_if_modified_since(::PROTOBUF_NAMESPACE_ID::Timestamp* if_modified_since);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& _internal_if_modified_since() const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _internal_mutable_if_modified_since();
  public:
  void unsafe_arena_set_allocated_if_modified_since(
      ::PROTOBUF_NAMESPACE_ID::Timestamp* if_modified_since);
  ::PROTOBUF_NAMESPACE_ID::Timestamp* unsafe_arena_release_if_modified_since();

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.GetFarmRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::geo::Id* id_;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* if_modified_since_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// -------------------------------------------------------------------

class GetFarmResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.farm.GetFarmResponse) */ {
 public:
  inline GetFarmResponse() : GetFarmResponse(nullptr) {}
  ~GetFarmResponse() override;
  explicit constexpr GetFarmResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetFarmResponse(const GetFarmResponse& from);
  GetFarmResponse(GetFarmResponse&& from) noexcept
    : GetFarmResponse() {
    *this = ::std::move(from);
  }

  inline GetFarmResponse& operator=(const GetFarmResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetFarmResponse& operator=(GetFarmResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetFarmResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetFarmResponse* internal_default_instance() {
    return reinterpret_cast<const GetFarmResponse*>(
               &_GetFarmResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(GetFarmResponse& a, GetFarmResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetFarmResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetFarmResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetFarmResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetFarmResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetFarmResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetFarmResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetFarmResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.farm.GetFarmResponse";
  }
  protected:
  explicit GetFarmResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFarmFieldNumber = 1,
  };
  // .carbon.portal.farm.Farm farm = 1;
  bool has_farm() const;
  private:
  bool _internal_has_farm() const;
  public:
  void clear_farm();
  const ::carbon::portal::farm::Farm& farm() const;
  PROTOBUF_NODISCARD ::carbon::portal::farm::Farm* release_farm();
  ::carbon::portal::farm::Farm* mutable_farm();
  void set_allocated_farm(::carbon::portal::farm::Farm* farm);
  private:
  const ::carbon::portal::farm::Farm& _internal_farm() const;
  ::carbon::portal::farm::Farm* _internal_mutable_farm();
  public:
  void unsafe_arena_set_allocated_farm(
      ::carbon::portal::farm::Farm* farm);
  ::carbon::portal::farm::Farm* unsafe_arena_release_farm();

  // @@protoc_insertion_point(class_scope:carbon.portal.farm.GetFarmResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::portal::farm::Farm* farm_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2ffarm_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Farm

// .carbon.geo.Id id = 1;
inline bool Farm::_internal_has_id() const {
  return this != internal_default_instance() && id_ != nullptr;
}
inline bool Farm::has_id() const {
  return _internal_has_id();
}
inline const ::carbon::geo::Id& Farm::_internal_id() const {
  const ::carbon::geo::Id* p = id_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::Id&>(
      ::carbon::geo::_Id_default_instance_);
}
inline const ::carbon::geo::Id& Farm::id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Farm.id)
  return _internal_id();
}
inline void Farm::unsafe_arena_set_allocated_id(
    ::carbon::geo::Id* id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(id_);
  }
  id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.Farm.id)
}
inline ::carbon::geo::Id* Farm::release_id() {
  
  ::carbon::geo::Id* temp = id_;
  id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::Id* Farm::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Farm.id)
  
  ::carbon::geo::Id* temp = id_;
  id_ = nullptr;
  return temp;
}
inline ::carbon::geo::Id* Farm::_internal_mutable_id() {
  
  if (id_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::Id>(GetArenaForAllocation());
    id_ = p;
  }
  return id_;
}
inline ::carbon::geo::Id* Farm::mutable_id() {
  ::carbon::geo::Id* _msg = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Farm.id)
  return _msg;
}
inline void Farm::set_allocated_id(::carbon::geo::Id* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(id_);
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(id));
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Farm.id)
}

// .carbon.portal.farm.VersionInfo version = 2;
inline bool Farm::_internal_has_version() const {
  return this != internal_default_instance() && version_ != nullptr;
}
inline bool Farm::has_version() const {
  return _internal_has_version();
}
inline void Farm::clear_version() {
  if (GetArenaForAllocation() == nullptr && version_ != nullptr) {
    delete version_;
  }
  version_ = nullptr;
}
inline const ::carbon::portal::farm::VersionInfo& Farm::_internal_version() const {
  const ::carbon::portal::farm::VersionInfo* p = version_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::farm::VersionInfo&>(
      ::carbon::portal::farm::_VersionInfo_default_instance_);
}
inline const ::carbon::portal::farm::VersionInfo& Farm::version() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Farm.version)
  return _internal_version();
}
inline void Farm::unsafe_arena_set_allocated_version(
    ::carbon::portal::farm::VersionInfo* version) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(version_);
  }
  version_ = version;
  if (version) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.Farm.version)
}
inline ::carbon::portal::farm::VersionInfo* Farm::release_version() {
  
  ::carbon::portal::farm::VersionInfo* temp = version_;
  version_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::farm::VersionInfo* Farm::unsafe_arena_release_version() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Farm.version)
  
  ::carbon::portal::farm::VersionInfo* temp = version_;
  version_ = nullptr;
  return temp;
}
inline ::carbon::portal::farm::VersionInfo* Farm::_internal_mutable_version() {
  
  if (version_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::farm::VersionInfo>(GetArenaForAllocation());
    version_ = p;
  }
  return version_;
}
inline ::carbon::portal::farm::VersionInfo* Farm::mutable_version() {
  ::carbon::portal::farm::VersionInfo* _msg = _internal_mutable_version();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Farm.version)
  return _msg;
}
inline void Farm::set_allocated_version(::carbon::portal::farm::VersionInfo* version) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete version_;
  }
  if (version) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::VersionInfo>::GetOwningArena(version);
    if (message_arena != submessage_arena) {
      version = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, version, submessage_arena);
    }
    
  } else {
    
  }
  version_ = version;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Farm.version)
}

// string name = 3;
inline void Farm::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& Farm::name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Farm.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Farm::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.farm.Farm.name)
}
inline std::string* Farm::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Farm.name)
  return _s;
}
inline const std::string& Farm::_internal_name() const {
  return name_.Get();
}
inline void Farm::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Farm::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Farm::release_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Farm.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Farm::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Farm.name)
}

// int64 customer_id = 4;
inline void Farm::clear_customer_id() {
  customer_id_ = int64_t{0};
}
inline int64_t Farm::_internal_customer_id() const {
  return customer_id_;
}
inline int64_t Farm::customer_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Farm.customer_id)
  return _internal_customer_id();
}
inline void Farm::_internal_set_customer_id(int64_t value) {
  
  customer_id_ = value;
}
inline void Farm::set_customer_id(int64_t value) {
  _internal_set_customer_id(value);
  // @@protoc_insertion_point(field_set:carbon.portal.farm.Farm.customer_id)
}

// repeated .carbon.portal.farm.PointDefinition point_defs = 5;
inline int Farm::_internal_point_defs_size() const {
  return point_defs_.size();
}
inline int Farm::point_defs_size() const {
  return _internal_point_defs_size();
}
inline void Farm::clear_point_defs() {
  point_defs_.Clear();
}
inline ::carbon::portal::farm::PointDefinition* Farm::mutable_point_defs(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Farm.point_defs)
  return point_defs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::PointDefinition >*
Farm::mutable_point_defs() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.farm.Farm.point_defs)
  return &point_defs_;
}
inline const ::carbon::portal::farm::PointDefinition& Farm::_internal_point_defs(int index) const {
  return point_defs_.Get(index);
}
inline const ::carbon::portal::farm::PointDefinition& Farm::point_defs(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Farm.point_defs)
  return _internal_point_defs(index);
}
inline ::carbon::portal::farm::PointDefinition* Farm::_internal_add_point_defs() {
  return point_defs_.Add();
}
inline ::carbon::portal::farm::PointDefinition* Farm::add_point_defs() {
  ::carbon::portal::farm::PointDefinition* _add = _internal_add_point_defs();
  // @@protoc_insertion_point(field_add:carbon.portal.farm.Farm.point_defs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::PointDefinition >&
Farm::point_defs() const {
  // @@protoc_insertion_point(field_list:carbon.portal.farm.Farm.point_defs)
  return point_defs_;
}

// repeated .carbon.portal.farm.Zone zones = 6;
inline int Farm::_internal_zones_size() const {
  return zones_.size();
}
inline int Farm::zones_size() const {
  return _internal_zones_size();
}
inline void Farm::clear_zones() {
  zones_.Clear();
}
inline ::carbon::portal::farm::Zone* Farm::mutable_zones(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Farm.zones)
  return zones_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Zone >*
Farm::mutable_zones() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.farm.Farm.zones)
  return &zones_;
}
inline const ::carbon::portal::farm::Zone& Farm::_internal_zones(int index) const {
  return zones_.Get(index);
}
inline const ::carbon::portal::farm::Zone& Farm::zones(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Farm.zones)
  return _internal_zones(index);
}
inline ::carbon::portal::farm::Zone* Farm::_internal_add_zones() {
  return zones_.Add();
}
inline ::carbon::portal::farm::Zone* Farm::add_zones() {
  ::carbon::portal::farm::Zone* _add = _internal_add_zones();
  // @@protoc_insertion_point(field_add:carbon.portal.farm.Farm.zones)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Zone >&
Farm::zones() const {
  // @@protoc_insertion_point(field_list:carbon.portal.farm.Farm.zones)
  return zones_;
}

// -------------------------------------------------------------------

// VersionInfo

// int64 ordinal = 1;
inline void VersionInfo::clear_ordinal() {
  ordinal_ = int64_t{0};
}
inline int64_t VersionInfo::_internal_ordinal() const {
  return ordinal_;
}
inline int64_t VersionInfo::ordinal() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.VersionInfo.ordinal)
  return _internal_ordinal();
}
inline void VersionInfo::_internal_set_ordinal(int64_t value) {
  
  ordinal_ = value;
}
inline void VersionInfo::set_ordinal(int64_t value) {
  _internal_set_ordinal(value);
  // @@protoc_insertion_point(field_set:carbon.portal.farm.VersionInfo.ordinal)
}

// .google.protobuf.Timestamp update_time = 2;
inline bool VersionInfo::_internal_has_update_time() const {
  return this != internal_default_instance() && update_time_ != nullptr;
}
inline bool VersionInfo::has_update_time() const {
  return _internal_has_update_time();
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& VersionInfo::_internal_update_time() const {
  const ::PROTOBUF_NAMESPACE_ID::Timestamp* p = update_time_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Timestamp&>(
      ::PROTOBUF_NAMESPACE_ID::_Timestamp_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& VersionInfo::update_time() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.VersionInfo.update_time)
  return _internal_update_time();
}
inline void VersionInfo::unsafe_arena_set_allocated_update_time(
    ::PROTOBUF_NAMESPACE_ID::Timestamp* update_time) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(update_time_);
  }
  update_time_ = update_time;
  if (update_time) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.VersionInfo.update_time)
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* VersionInfo::release_update_time() {
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = update_time_;
  update_time_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* VersionInfo::unsafe_arena_release_update_time() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.VersionInfo.update_time)
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = update_time_;
  update_time_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* VersionInfo::_internal_mutable_update_time() {
  
  if (update_time_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Timestamp>(GetArenaForAllocation());
    update_time_ = p;
  }
  return update_time_;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* VersionInfo::mutable_update_time() {
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _msg = _internal_mutable_update_time();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.VersionInfo.update_time)
  return _msg;
}
inline void VersionInfo::set_allocated_update_time(::PROTOBUF_NAMESPACE_ID::Timestamp* update_time) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(update_time_);
  }
  if (update_time) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(update_time));
    if (message_arena != submessage_arena) {
      update_time = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, update_time, submessage_arena);
    }
    
  } else {
    
  }
  update_time_ = update_time;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.VersionInfo.update_time)
}

// bool deleted = 3;
inline void VersionInfo::clear_deleted() {
  deleted_ = false;
}
inline bool VersionInfo::_internal_deleted() const {
  return deleted_;
}
inline bool VersionInfo::deleted() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.VersionInfo.deleted)
  return _internal_deleted();
}
inline void VersionInfo::_internal_set_deleted(bool value) {
  
  deleted_ = value;
}
inline void VersionInfo::set_deleted(bool value) {
  _internal_set_deleted(value);
  // @@protoc_insertion_point(field_set:carbon.portal.farm.VersionInfo.deleted)
}

// bool changed = 4;
inline void VersionInfo::clear_changed() {
  changed_ = false;
}
inline bool VersionInfo::_internal_changed() const {
  return changed_;
}
inline bool VersionInfo::changed() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.VersionInfo.changed)
  return _internal_changed();
}
inline void VersionInfo::_internal_set_changed(bool value) {
  
  changed_ = value;
}
inline void VersionInfo::set_changed(bool value) {
  _internal_set_changed(value);
  // @@protoc_insertion_point(field_set:carbon.portal.farm.VersionInfo.changed)
}

// -------------------------------------------------------------------

// PointDefinition

// .carbon.geo.Point point = 1;
inline bool PointDefinition::_internal_has_point() const {
  return this != internal_default_instance() && point_ != nullptr;
}
inline bool PointDefinition::has_point() const {
  return _internal_has_point();
}
inline const ::carbon::geo::Point& PointDefinition::_internal_point() const {
  const ::carbon::geo::Point* p = point_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::Point&>(
      ::carbon::geo::_Point_default_instance_);
}
inline const ::carbon::geo::Point& PointDefinition::point() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.PointDefinition.point)
  return _internal_point();
}
inline void PointDefinition::unsafe_arena_set_allocated_point(
    ::carbon::geo::Point* point) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(point_);
  }
  point_ = point;
  if (point) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.PointDefinition.point)
}
inline ::carbon::geo::Point* PointDefinition::release_point() {
  
  ::carbon::geo::Point* temp = point_;
  point_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::Point* PointDefinition::unsafe_arena_release_point() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.PointDefinition.point)
  
  ::carbon::geo::Point* temp = point_;
  point_ = nullptr;
  return temp;
}
inline ::carbon::geo::Point* PointDefinition::_internal_mutable_point() {
  
  if (point_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::Point>(GetArenaForAllocation());
    point_ = p;
  }
  return point_;
}
inline ::carbon::geo::Point* PointDefinition::mutable_point() {
  ::carbon::geo::Point* _msg = _internal_mutable_point();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.PointDefinition.point)
  return _msg;
}
inline void PointDefinition::set_allocated_point(::carbon::geo::Point* point) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(point_);
  }
  if (point) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(point));
    if (message_arena != submessage_arena) {
      point = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, point, submessage_arena);
    }
    
  } else {
    
  }
  point_ = point;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.PointDefinition.point)
}

// .carbon.portal.farm.VersionInfo version = 2;
inline bool PointDefinition::_internal_has_version() const {
  return this != internal_default_instance() && version_ != nullptr;
}
inline bool PointDefinition::has_version() const {
  return _internal_has_version();
}
inline void PointDefinition::clear_version() {
  if (GetArenaForAllocation() == nullptr && version_ != nullptr) {
    delete version_;
  }
  version_ = nullptr;
}
inline const ::carbon::portal::farm::VersionInfo& PointDefinition::_internal_version() const {
  const ::carbon::portal::farm::VersionInfo* p = version_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::farm::VersionInfo&>(
      ::carbon::portal::farm::_VersionInfo_default_instance_);
}
inline const ::carbon::portal::farm::VersionInfo& PointDefinition::version() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.PointDefinition.version)
  return _internal_version();
}
inline void PointDefinition::unsafe_arena_set_allocated_version(
    ::carbon::portal::farm::VersionInfo* version) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(version_);
  }
  version_ = version;
  if (version) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.PointDefinition.version)
}
inline ::carbon::portal::farm::VersionInfo* PointDefinition::release_version() {
  
  ::carbon::portal::farm::VersionInfo* temp = version_;
  version_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::farm::VersionInfo* PointDefinition::unsafe_arena_release_version() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.PointDefinition.version)
  
  ::carbon::portal::farm::VersionInfo* temp = version_;
  version_ = nullptr;
  return temp;
}
inline ::carbon::portal::farm::VersionInfo* PointDefinition::_internal_mutable_version() {
  
  if (version_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::farm::VersionInfo>(GetArenaForAllocation());
    version_ = p;
  }
  return version_;
}
inline ::carbon::portal::farm::VersionInfo* PointDefinition::mutable_version() {
  ::carbon::portal::farm::VersionInfo* _msg = _internal_mutable_version();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.PointDefinition.version)
  return _msg;
}
inline void PointDefinition::set_allocated_version(::carbon::portal::farm::VersionInfo* version) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete version_;
  }
  if (version) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::VersionInfo>::GetOwningArena(version);
    if (message_arena != submessage_arena) {
      version = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, version, submessage_arena);
    }
    
  } else {
    
  }
  version_ = version;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.PointDefinition.version)
}

// -------------------------------------------------------------------

// Zone

// .carbon.geo.Id id = 1;
inline bool Zone::_internal_has_id() const {
  return this != internal_default_instance() && id_ != nullptr;
}
inline bool Zone::has_id() const {
  return _internal_has_id();
}
inline const ::carbon::geo::Id& Zone::_internal_id() const {
  const ::carbon::geo::Id* p = id_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::Id&>(
      ::carbon::geo::_Id_default_instance_);
}
inline const ::carbon::geo::Id& Zone::id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Zone.id)
  return _internal_id();
}
inline void Zone::unsafe_arena_set_allocated_id(
    ::carbon::geo::Id* id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(id_);
  }
  id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.Zone.id)
}
inline ::carbon::geo::Id* Zone::release_id() {
  
  ::carbon::geo::Id* temp = id_;
  id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::Id* Zone::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Zone.id)
  
  ::carbon::geo::Id* temp = id_;
  id_ = nullptr;
  return temp;
}
inline ::carbon::geo::Id* Zone::_internal_mutable_id() {
  
  if (id_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::Id>(GetArenaForAllocation());
    id_ = p;
  }
  return id_;
}
inline ::carbon::geo::Id* Zone::mutable_id() {
  ::carbon::geo::Id* _msg = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Zone.id)
  return _msg;
}
inline void Zone::set_allocated_id(::carbon::geo::Id* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(id_);
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(id));
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Zone.id)
}

// .carbon.portal.farm.VersionInfo version = 2;
inline bool Zone::_internal_has_version() const {
  return this != internal_default_instance() && version_ != nullptr;
}
inline bool Zone::has_version() const {
  return _internal_has_version();
}
inline void Zone::clear_version() {
  if (GetArenaForAllocation() == nullptr && version_ != nullptr) {
    delete version_;
  }
  version_ = nullptr;
}
inline const ::carbon::portal::farm::VersionInfo& Zone::_internal_version() const {
  const ::carbon::portal::farm::VersionInfo* p = version_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::farm::VersionInfo&>(
      ::carbon::portal::farm::_VersionInfo_default_instance_);
}
inline const ::carbon::portal::farm::VersionInfo& Zone::version() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Zone.version)
  return _internal_version();
}
inline void Zone::unsafe_arena_set_allocated_version(
    ::carbon::portal::farm::VersionInfo* version) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(version_);
  }
  version_ = version;
  if (version) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.Zone.version)
}
inline ::carbon::portal::farm::VersionInfo* Zone::release_version() {
  
  ::carbon::portal::farm::VersionInfo* temp = version_;
  version_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::farm::VersionInfo* Zone::unsafe_arena_release_version() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Zone.version)
  
  ::carbon::portal::farm::VersionInfo* temp = version_;
  version_ = nullptr;
  return temp;
}
inline ::carbon::portal::farm::VersionInfo* Zone::_internal_mutable_version() {
  
  if (version_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::farm::VersionInfo>(GetArenaForAllocation());
    version_ = p;
  }
  return version_;
}
inline ::carbon::portal::farm::VersionInfo* Zone::mutable_version() {
  ::carbon::portal::farm::VersionInfo* _msg = _internal_mutable_version();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Zone.version)
  return _msg;
}
inline void Zone::set_allocated_version(::carbon::portal::farm::VersionInfo* version) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete version_;
  }
  if (version) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::VersionInfo>::GetOwningArena(version);
    if (message_arena != submessage_arena) {
      version = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, version, submessage_arena);
    }
    
  } else {
    
  }
  version_ = version;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Zone.version)
}

// string name = 3;
inline void Zone::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& Zone::name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Zone.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Zone::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.farm.Zone.name)
}
inline std::string* Zone::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Zone.name)
  return _s;
}
inline const std::string& Zone::_internal_name() const {
  return name_.Get();
}
inline void Zone::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Zone::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Zone::release_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Zone.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Zone::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Zone.name)
}

// repeated .carbon.portal.farm.Area areas = 4;
inline int Zone::_internal_areas_size() const {
  return areas_.size();
}
inline int Zone::areas_size() const {
  return _internal_areas_size();
}
inline void Zone::clear_areas() {
  areas_.Clear();
}
inline ::carbon::portal::farm::Area* Zone::mutable_areas(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Zone.areas)
  return areas_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Area >*
Zone::mutable_areas() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.farm.Zone.areas)
  return &areas_;
}
inline const ::carbon::portal::farm::Area& Zone::_internal_areas(int index) const {
  return areas_.Get(index);
}
inline const ::carbon::portal::farm::Area& Zone::areas(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Zone.areas)
  return _internal_areas(index);
}
inline ::carbon::portal::farm::Area* Zone::_internal_add_areas() {
  return areas_.Add();
}
inline ::carbon::portal::farm::Area* Zone::add_areas() {
  ::carbon::portal::farm::Area* _add = _internal_add_areas();
  // @@protoc_insertion_point(field_add:carbon.portal.farm.Zone.areas)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Area >&
Zone::areas() const {
  // @@protoc_insertion_point(field_list:carbon.portal.farm.Zone.areas)
  return areas_;
}

// .carbon.portal.farm.ZoneContents contents = 5;
inline bool Zone::_internal_has_contents() const {
  return this != internal_default_instance() && contents_ != nullptr;
}
inline bool Zone::has_contents() const {
  return _internal_has_contents();
}
inline void Zone::clear_contents() {
  if (GetArenaForAllocation() == nullptr && contents_ != nullptr) {
    delete contents_;
  }
  contents_ = nullptr;
}
inline const ::carbon::portal::farm::ZoneContents& Zone::_internal_contents() const {
  const ::carbon::portal::farm::ZoneContents* p = contents_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::farm::ZoneContents&>(
      ::carbon::portal::farm::_ZoneContents_default_instance_);
}
inline const ::carbon::portal::farm::ZoneContents& Zone::contents() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Zone.contents)
  return _internal_contents();
}
inline void Zone::unsafe_arena_set_allocated_contents(
    ::carbon::portal::farm::ZoneContents* contents) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(contents_);
  }
  contents_ = contents;
  if (contents) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.Zone.contents)
}
inline ::carbon::portal::farm::ZoneContents* Zone::release_contents() {
  
  ::carbon::portal::farm::ZoneContents* temp = contents_;
  contents_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::farm::ZoneContents* Zone::unsafe_arena_release_contents() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Zone.contents)
  
  ::carbon::portal::farm::ZoneContents* temp = contents_;
  contents_ = nullptr;
  return temp;
}
inline ::carbon::portal::farm::ZoneContents* Zone::_internal_mutable_contents() {
  
  if (contents_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::farm::ZoneContents>(GetArenaForAllocation());
    contents_ = p;
  }
  return contents_;
}
inline ::carbon::portal::farm::ZoneContents* Zone::mutable_contents() {
  ::carbon::portal::farm::ZoneContents* _msg = _internal_mutable_contents();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Zone.contents)
  return _msg;
}
inline void Zone::set_allocated_contents(::carbon::portal::farm::ZoneContents* contents) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete contents_;
  }
  if (contents) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::ZoneContents>::GetOwningArena(contents);
    if (message_arena != submessage_arena) {
      contents = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, contents, submessage_arena);
    }
    
  } else {
    
  }
  contents_ = contents;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Zone.contents)
}

// -------------------------------------------------------------------

// ZoneContents

// .carbon.portal.farm.FarmBoundaryData farm_boundary = 9;
inline bool ZoneContents::_internal_has_farm_boundary() const {
  return data_case() == kFarmBoundary;
}
inline bool ZoneContents::has_farm_boundary() const {
  return _internal_has_farm_boundary();
}
inline void ZoneContents::set_has_farm_boundary() {
  _oneof_case_[0] = kFarmBoundary;
}
inline void ZoneContents::clear_farm_boundary() {
  if (_internal_has_farm_boundary()) {
    if (GetArenaForAllocation() == nullptr) {
      delete data_.farm_boundary_;
    }
    clear_has_data();
  }
}
inline ::carbon::portal::farm::FarmBoundaryData* ZoneContents::release_farm_boundary() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.ZoneContents.farm_boundary)
  if (_internal_has_farm_boundary()) {
    clear_has_data();
      ::carbon::portal::farm::FarmBoundaryData* temp = data_.farm_boundary_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    data_.farm_boundary_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::portal::farm::FarmBoundaryData& ZoneContents::_internal_farm_boundary() const {
  return _internal_has_farm_boundary()
      ? *data_.farm_boundary_
      : reinterpret_cast< ::carbon::portal::farm::FarmBoundaryData&>(::carbon::portal::farm::_FarmBoundaryData_default_instance_);
}
inline const ::carbon::portal::farm::FarmBoundaryData& ZoneContents::farm_boundary() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.ZoneContents.farm_boundary)
  return _internal_farm_boundary();
}
inline ::carbon::portal::farm::FarmBoundaryData* ZoneContents::unsafe_arena_release_farm_boundary() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.farm.ZoneContents.farm_boundary)
  if (_internal_has_farm_boundary()) {
    clear_has_data();
    ::carbon::portal::farm::FarmBoundaryData* temp = data_.farm_boundary_;
    data_.farm_boundary_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ZoneContents::unsafe_arena_set_allocated_farm_boundary(::carbon::portal::farm::FarmBoundaryData* farm_boundary) {
  clear_data();
  if (farm_boundary) {
    set_has_farm_boundary();
    data_.farm_boundary_ = farm_boundary;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.ZoneContents.farm_boundary)
}
inline ::carbon::portal::farm::FarmBoundaryData* ZoneContents::_internal_mutable_farm_boundary() {
  if (!_internal_has_farm_boundary()) {
    clear_data();
    set_has_farm_boundary();
    data_.farm_boundary_ = CreateMaybeMessage< ::carbon::portal::farm::FarmBoundaryData >(GetArenaForAllocation());
  }
  return data_.farm_boundary_;
}
inline ::carbon::portal::farm::FarmBoundaryData* ZoneContents::mutable_farm_boundary() {
  ::carbon::portal::farm::FarmBoundaryData* _msg = _internal_mutable_farm_boundary();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.ZoneContents.farm_boundary)
  return _msg;
}

// .carbon.portal.farm.FieldData field = 5;
inline bool ZoneContents::_internal_has_field() const {
  return data_case() == kField;
}
inline bool ZoneContents::has_field() const {
  return _internal_has_field();
}
inline void ZoneContents::set_has_field() {
  _oneof_case_[0] = kField;
}
inline void ZoneContents::clear_field() {
  if (_internal_has_field()) {
    if (GetArenaForAllocation() == nullptr) {
      delete data_.field_;
    }
    clear_has_data();
  }
}
inline ::carbon::portal::farm::FieldData* ZoneContents::release_field() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.ZoneContents.field)
  if (_internal_has_field()) {
    clear_has_data();
      ::carbon::portal::farm::FieldData* temp = data_.field_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    data_.field_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::portal::farm::FieldData& ZoneContents::_internal_field() const {
  return _internal_has_field()
      ? *data_.field_
      : reinterpret_cast< ::carbon::portal::farm::FieldData&>(::carbon::portal::farm::_FieldData_default_instance_);
}
inline const ::carbon::portal::farm::FieldData& ZoneContents::field() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.ZoneContents.field)
  return _internal_field();
}
inline ::carbon::portal::farm::FieldData* ZoneContents::unsafe_arena_release_field() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.farm.ZoneContents.field)
  if (_internal_has_field()) {
    clear_has_data();
    ::carbon::portal::farm::FieldData* temp = data_.field_;
    data_.field_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ZoneContents::unsafe_arena_set_allocated_field(::carbon::portal::farm::FieldData* field) {
  clear_data();
  if (field) {
    set_has_field();
    data_.field_ = field;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.ZoneContents.field)
}
inline ::carbon::portal::farm::FieldData* ZoneContents::_internal_mutable_field() {
  if (!_internal_has_field()) {
    clear_data();
    set_has_field();
    data_.field_ = CreateMaybeMessage< ::carbon::portal::farm::FieldData >(GetArenaForAllocation());
  }
  return data_.field_;
}
inline ::carbon::portal::farm::FieldData* ZoneContents::mutable_field() {
  ::carbon::portal::farm::FieldData* _msg = _internal_mutable_field();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.ZoneContents.field)
  return _msg;
}

// .carbon.portal.farm.HeadlandData headland = 6;
inline bool ZoneContents::_internal_has_headland() const {
  return data_case() == kHeadland;
}
inline bool ZoneContents::has_headland() const {
  return _internal_has_headland();
}
inline void ZoneContents::set_has_headland() {
  _oneof_case_[0] = kHeadland;
}
inline void ZoneContents::clear_headland() {
  if (_internal_has_headland()) {
    if (GetArenaForAllocation() == nullptr) {
      delete data_.headland_;
    }
    clear_has_data();
  }
}
inline ::carbon::portal::farm::HeadlandData* ZoneContents::release_headland() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.ZoneContents.headland)
  if (_internal_has_headland()) {
    clear_has_data();
      ::carbon::portal::farm::HeadlandData* temp = data_.headland_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    data_.headland_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::portal::farm::HeadlandData& ZoneContents::_internal_headland() const {
  return _internal_has_headland()
      ? *data_.headland_
      : reinterpret_cast< ::carbon::portal::farm::HeadlandData&>(::carbon::portal::farm::_HeadlandData_default_instance_);
}
inline const ::carbon::portal::farm::HeadlandData& ZoneContents::headland() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.ZoneContents.headland)
  return _internal_headland();
}
inline ::carbon::portal::farm::HeadlandData* ZoneContents::unsafe_arena_release_headland() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.farm.ZoneContents.headland)
  if (_internal_has_headland()) {
    clear_has_data();
    ::carbon::portal::farm::HeadlandData* temp = data_.headland_;
    data_.headland_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ZoneContents::unsafe_arena_set_allocated_headland(::carbon::portal::farm::HeadlandData* headland) {
  clear_data();
  if (headland) {
    set_has_headland();
    data_.headland_ = headland;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.ZoneContents.headland)
}
inline ::carbon::portal::farm::HeadlandData* ZoneContents::_internal_mutable_headland() {
  if (!_internal_has_headland()) {
    clear_data();
    set_has_headland();
    data_.headland_ = CreateMaybeMessage< ::carbon::portal::farm::HeadlandData >(GetArenaForAllocation());
  }
  return data_.headland_;
}
inline ::carbon::portal::farm::HeadlandData* ZoneContents::mutable_headland() {
  ::carbon::portal::farm::HeadlandData* _msg = _internal_mutable_headland();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.ZoneContents.headland)
  return _msg;
}

// .carbon.portal.farm.PrivateRoadData private_road = 7;
inline bool ZoneContents::_internal_has_private_road() const {
  return data_case() == kPrivateRoad;
}
inline bool ZoneContents::has_private_road() const {
  return _internal_has_private_road();
}
inline void ZoneContents::set_has_private_road() {
  _oneof_case_[0] = kPrivateRoad;
}
inline void ZoneContents::clear_private_road() {
  if (_internal_has_private_road()) {
    if (GetArenaForAllocation() == nullptr) {
      delete data_.private_road_;
    }
    clear_has_data();
  }
}
inline ::carbon::portal::farm::PrivateRoadData* ZoneContents::release_private_road() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.ZoneContents.private_road)
  if (_internal_has_private_road()) {
    clear_has_data();
      ::carbon::portal::farm::PrivateRoadData* temp = data_.private_road_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    data_.private_road_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::portal::farm::PrivateRoadData& ZoneContents::_internal_private_road() const {
  return _internal_has_private_road()
      ? *data_.private_road_
      : reinterpret_cast< ::carbon::portal::farm::PrivateRoadData&>(::carbon::portal::farm::_PrivateRoadData_default_instance_);
}
inline const ::carbon::portal::farm::PrivateRoadData& ZoneContents::private_road() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.ZoneContents.private_road)
  return _internal_private_road();
}
inline ::carbon::portal::farm::PrivateRoadData* ZoneContents::unsafe_arena_release_private_road() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.farm.ZoneContents.private_road)
  if (_internal_has_private_road()) {
    clear_has_data();
    ::carbon::portal::farm::PrivateRoadData* temp = data_.private_road_;
    data_.private_road_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ZoneContents::unsafe_arena_set_allocated_private_road(::carbon::portal::farm::PrivateRoadData* private_road) {
  clear_data();
  if (private_road) {
    set_has_private_road();
    data_.private_road_ = private_road;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.ZoneContents.private_road)
}
inline ::carbon::portal::farm::PrivateRoadData* ZoneContents::_internal_mutable_private_road() {
  if (!_internal_has_private_road()) {
    clear_data();
    set_has_private_road();
    data_.private_road_ = CreateMaybeMessage< ::carbon::portal::farm::PrivateRoadData >(GetArenaForAllocation());
  }
  return data_.private_road_;
}
inline ::carbon::portal::farm::PrivateRoadData* ZoneContents::mutable_private_road() {
  ::carbon::portal::farm::PrivateRoadData* _msg = _internal_mutable_private_road();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.ZoneContents.private_road)
  return _msg;
}

// .carbon.portal.farm.ObstacleData obstacle = 8;
inline bool ZoneContents::_internal_has_obstacle() const {
  return data_case() == kObstacle;
}
inline bool ZoneContents::has_obstacle() const {
  return _internal_has_obstacle();
}
inline void ZoneContents::set_has_obstacle() {
  _oneof_case_[0] = kObstacle;
}
inline void ZoneContents::clear_obstacle() {
  if (_internal_has_obstacle()) {
    if (GetArenaForAllocation() == nullptr) {
      delete data_.obstacle_;
    }
    clear_has_data();
  }
}
inline ::carbon::portal::farm::ObstacleData* ZoneContents::release_obstacle() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.ZoneContents.obstacle)
  if (_internal_has_obstacle()) {
    clear_has_data();
      ::carbon::portal::farm::ObstacleData* temp = data_.obstacle_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    data_.obstacle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::portal::farm::ObstacleData& ZoneContents::_internal_obstacle() const {
  return _internal_has_obstacle()
      ? *data_.obstacle_
      : reinterpret_cast< ::carbon::portal::farm::ObstacleData&>(::carbon::portal::farm::_ObstacleData_default_instance_);
}
inline const ::carbon::portal::farm::ObstacleData& ZoneContents::obstacle() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.ZoneContents.obstacle)
  return _internal_obstacle();
}
inline ::carbon::portal::farm::ObstacleData* ZoneContents::unsafe_arena_release_obstacle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.farm.ZoneContents.obstacle)
  if (_internal_has_obstacle()) {
    clear_has_data();
    ::carbon::portal::farm::ObstacleData* temp = data_.obstacle_;
    data_.obstacle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ZoneContents::unsafe_arena_set_allocated_obstacle(::carbon::portal::farm::ObstacleData* obstacle) {
  clear_data();
  if (obstacle) {
    set_has_obstacle();
    data_.obstacle_ = obstacle;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.ZoneContents.obstacle)
}
inline ::carbon::portal::farm::ObstacleData* ZoneContents::_internal_mutable_obstacle() {
  if (!_internal_has_obstacle()) {
    clear_data();
    set_has_obstacle();
    data_.obstacle_ = CreateMaybeMessage< ::carbon::portal::farm::ObstacleData >(GetArenaForAllocation());
  }
  return data_.obstacle_;
}
inline ::carbon::portal::farm::ObstacleData* ZoneContents::mutable_obstacle() {
  ::carbon::portal::farm::ObstacleData* _msg = _internal_mutable_obstacle();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.ZoneContents.obstacle)
  return _msg;
}

inline bool ZoneContents::has_data() const {
  return data_case() != DATA_NOT_SET;
}
inline void ZoneContents::clear_has_data() {
  _oneof_case_[0] = DATA_NOT_SET;
}
inline ZoneContents::DataCase ZoneContents::data_case() const {
  return ZoneContents::DataCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// Area

// double buffer_meters = 1;
inline void Area::clear_buffer_meters() {
  buffer_meters_ = 0;
}
inline double Area::_internal_buffer_meters() const {
  return buffer_meters_;
}
inline double Area::buffer_meters() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Area.buffer_meters)
  return _internal_buffer_meters();
}
inline void Area::_internal_set_buffer_meters(double value) {
  
  buffer_meters_ = value;
}
inline void Area::set_buffer_meters(double value) {
  _internal_set_buffer_meters(value);
  // @@protoc_insertion_point(field_set:carbon.portal.farm.Area.buffer_meters)
}

// .carbon.geo.Point point = 2;
inline bool Area::_internal_has_point() const {
  return geometry_case() == kPoint;
}
inline bool Area::has_point() const {
  return _internal_has_point();
}
inline void Area::set_has_point() {
  _oneof_case_[0] = kPoint;
}
inline ::carbon::geo::Point* Area::release_point() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Area.point)
  if (_internal_has_point()) {
    clear_has_geometry();
      ::carbon::geo::Point* temp = geometry_.point_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    geometry_.point_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::geo::Point& Area::_internal_point() const {
  return _internal_has_point()
      ? *geometry_.point_
      : reinterpret_cast< ::carbon::geo::Point&>(::carbon::geo::_Point_default_instance_);
}
inline const ::carbon::geo::Point& Area::point() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Area.point)
  return _internal_point();
}
inline ::carbon::geo::Point* Area::unsafe_arena_release_point() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.farm.Area.point)
  if (_internal_has_point()) {
    clear_has_geometry();
    ::carbon::geo::Point* temp = geometry_.point_;
    geometry_.point_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Area::unsafe_arena_set_allocated_point(::carbon::geo::Point* point) {
  clear_geometry();
  if (point) {
    set_has_point();
    geometry_.point_ = point;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.Area.point)
}
inline ::carbon::geo::Point* Area::_internal_mutable_point() {
  if (!_internal_has_point()) {
    clear_geometry();
    set_has_point();
    geometry_.point_ = CreateMaybeMessage< ::carbon::geo::Point >(GetArenaForAllocation());
  }
  return geometry_.point_;
}
inline ::carbon::geo::Point* Area::mutable_point() {
  ::carbon::geo::Point* _msg = _internal_mutable_point();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Area.point)
  return _msg;
}

// .carbon.geo.LineString line_string = 3;
inline bool Area::_internal_has_line_string() const {
  return geometry_case() == kLineString;
}
inline bool Area::has_line_string() const {
  return _internal_has_line_string();
}
inline void Area::set_has_line_string() {
  _oneof_case_[0] = kLineString;
}
inline ::carbon::geo::LineString* Area::release_line_string() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Area.line_string)
  if (_internal_has_line_string()) {
    clear_has_geometry();
      ::carbon::geo::LineString* temp = geometry_.line_string_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    geometry_.line_string_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::geo::LineString& Area::_internal_line_string() const {
  return _internal_has_line_string()
      ? *geometry_.line_string_
      : reinterpret_cast< ::carbon::geo::LineString&>(::carbon::geo::_LineString_default_instance_);
}
inline const ::carbon::geo::LineString& Area::line_string() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Area.line_string)
  return _internal_line_string();
}
inline ::carbon::geo::LineString* Area::unsafe_arena_release_line_string() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.farm.Area.line_string)
  if (_internal_has_line_string()) {
    clear_has_geometry();
    ::carbon::geo::LineString* temp = geometry_.line_string_;
    geometry_.line_string_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Area::unsafe_arena_set_allocated_line_string(::carbon::geo::LineString* line_string) {
  clear_geometry();
  if (line_string) {
    set_has_line_string();
    geometry_.line_string_ = line_string;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.Area.line_string)
}
inline ::carbon::geo::LineString* Area::_internal_mutable_line_string() {
  if (!_internal_has_line_string()) {
    clear_geometry();
    set_has_line_string();
    geometry_.line_string_ = CreateMaybeMessage< ::carbon::geo::LineString >(GetArenaForAllocation());
  }
  return geometry_.line_string_;
}
inline ::carbon::geo::LineString* Area::mutable_line_string() {
  ::carbon::geo::LineString* _msg = _internal_mutable_line_string();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Area.line_string)
  return _msg;
}

// .carbon.geo.Polygon polygon = 4;
inline bool Area::_internal_has_polygon() const {
  return geometry_case() == kPolygon;
}
inline bool Area::has_polygon() const {
  return _internal_has_polygon();
}
inline void Area::set_has_polygon() {
  _oneof_case_[0] = kPolygon;
}
inline ::carbon::geo::Polygon* Area::release_polygon() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.Area.polygon)
  if (_internal_has_polygon()) {
    clear_has_geometry();
      ::carbon::geo::Polygon* temp = geometry_.polygon_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    geometry_.polygon_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::geo::Polygon& Area::_internal_polygon() const {
  return _internal_has_polygon()
      ? *geometry_.polygon_
      : reinterpret_cast< ::carbon::geo::Polygon&>(::carbon::geo::_Polygon_default_instance_);
}
inline const ::carbon::geo::Polygon& Area::polygon() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.Area.polygon)
  return _internal_polygon();
}
inline ::carbon::geo::Polygon* Area::unsafe_arena_release_polygon() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.farm.Area.polygon)
  if (_internal_has_polygon()) {
    clear_has_geometry();
    ::carbon::geo::Polygon* temp = geometry_.polygon_;
    geometry_.polygon_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Area::unsafe_arena_set_allocated_polygon(::carbon::geo::Polygon* polygon) {
  clear_geometry();
  if (polygon) {
    set_has_polygon();
    geometry_.polygon_ = polygon;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.Area.polygon)
}
inline ::carbon::geo::Polygon* Area::_internal_mutable_polygon() {
  if (!_internal_has_polygon()) {
    clear_geometry();
    set_has_polygon();
    geometry_.polygon_ = CreateMaybeMessage< ::carbon::geo::Polygon >(GetArenaForAllocation());
  }
  return geometry_.polygon_;
}
inline ::carbon::geo::Polygon* Area::mutable_polygon() {
  ::carbon::geo::Polygon* _msg = _internal_mutable_polygon();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.Area.polygon)
  return _msg;
}

inline bool Area::has_geometry() const {
  return geometry_case() != GEOMETRY_NOT_SET;
}
inline void Area::clear_has_geometry() {
  _oneof_case_[0] = GEOMETRY_NOT_SET;
}
inline Area::GeometryCase Area::geometry_case() const {
  return Area::GeometryCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// FarmBoundaryData

// -------------------------------------------------------------------

// FieldData

// .carbon.portal.farm.PlantingHeading planting_heading = 1;
inline bool FieldData::_internal_has_planting_heading() const {
  return this != internal_default_instance() && planting_heading_ != nullptr;
}
inline bool FieldData::has_planting_heading() const {
  return _internal_has_planting_heading();
}
inline void FieldData::clear_planting_heading() {
  if (GetArenaForAllocation() == nullptr && planting_heading_ != nullptr) {
    delete planting_heading_;
  }
  planting_heading_ = nullptr;
}
inline const ::carbon::portal::farm::PlantingHeading& FieldData::_internal_planting_heading() const {
  const ::carbon::portal::farm::PlantingHeading* p = planting_heading_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::farm::PlantingHeading&>(
      ::carbon::portal::farm::_PlantingHeading_default_instance_);
}
inline const ::carbon::portal::farm::PlantingHeading& FieldData::planting_heading() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.FieldData.planting_heading)
  return _internal_planting_heading();
}
inline void FieldData::unsafe_arena_set_allocated_planting_heading(
    ::carbon::portal::farm::PlantingHeading* planting_heading) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(planting_heading_);
  }
  planting_heading_ = planting_heading;
  if (planting_heading) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.FieldData.planting_heading)
}
inline ::carbon::portal::farm::PlantingHeading* FieldData::release_planting_heading() {
  
  ::carbon::portal::farm::PlantingHeading* temp = planting_heading_;
  planting_heading_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::farm::PlantingHeading* FieldData::unsafe_arena_release_planting_heading() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.FieldData.planting_heading)
  
  ::carbon::portal::farm::PlantingHeading* temp = planting_heading_;
  planting_heading_ = nullptr;
  return temp;
}
inline ::carbon::portal::farm::PlantingHeading* FieldData::_internal_mutable_planting_heading() {
  
  if (planting_heading_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::farm::PlantingHeading>(GetArenaForAllocation());
    planting_heading_ = p;
  }
  return planting_heading_;
}
inline ::carbon::portal::farm::PlantingHeading* FieldData::mutable_planting_heading() {
  ::carbon::portal::farm::PlantingHeading* _msg = _internal_mutable_planting_heading();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.FieldData.planting_heading)
  return _msg;
}
inline void FieldData::set_allocated_planting_heading(::carbon::portal::farm::PlantingHeading* planting_heading) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete planting_heading_;
  }
  if (planting_heading) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::PlantingHeading>::GetOwningArena(planting_heading);
    if (message_arena != submessage_arena) {
      planting_heading = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, planting_heading, submessage_arena);
    }
    
  } else {
    
  }
  planting_heading_ = planting_heading;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.FieldData.planting_heading)
}

// .carbon.portal.farm.CenterPivot center_pivot = 2;
inline bool FieldData::_internal_has_center_pivot() const {
  return this != internal_default_instance() && center_pivot_ != nullptr;
}
inline bool FieldData::has_center_pivot() const {
  return _internal_has_center_pivot();
}
inline void FieldData::clear_center_pivot() {
  if (GetArenaForAllocation() == nullptr && center_pivot_ != nullptr) {
    delete center_pivot_;
  }
  center_pivot_ = nullptr;
}
inline const ::carbon::portal::farm::CenterPivot& FieldData::_internal_center_pivot() const {
  const ::carbon::portal::farm::CenterPivot* p = center_pivot_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::farm::CenterPivot&>(
      ::carbon::portal::farm::_CenterPivot_default_instance_);
}
inline const ::carbon::portal::farm::CenterPivot& FieldData::center_pivot() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.FieldData.center_pivot)
  return _internal_center_pivot();
}
inline void FieldData::unsafe_arena_set_allocated_center_pivot(
    ::carbon::portal::farm::CenterPivot* center_pivot) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_pivot_);
  }
  center_pivot_ = center_pivot;
  if (center_pivot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.FieldData.center_pivot)
}
inline ::carbon::portal::farm::CenterPivot* FieldData::release_center_pivot() {
  
  ::carbon::portal::farm::CenterPivot* temp = center_pivot_;
  center_pivot_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::farm::CenterPivot* FieldData::unsafe_arena_release_center_pivot() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.FieldData.center_pivot)
  
  ::carbon::portal::farm::CenterPivot* temp = center_pivot_;
  center_pivot_ = nullptr;
  return temp;
}
inline ::carbon::portal::farm::CenterPivot* FieldData::_internal_mutable_center_pivot() {
  
  if (center_pivot_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::farm::CenterPivot>(GetArenaForAllocation());
    center_pivot_ = p;
  }
  return center_pivot_;
}
inline ::carbon::portal::farm::CenterPivot* FieldData::mutable_center_pivot() {
  ::carbon::portal::farm::CenterPivot* _msg = _internal_mutable_center_pivot();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.FieldData.center_pivot)
  return _msg;
}
inline void FieldData::set_allocated_center_pivot(::carbon::portal::farm::CenterPivot* center_pivot) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete center_pivot_;
  }
  if (center_pivot) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::CenterPivot>::GetOwningArena(center_pivot);
    if (message_arena != submessage_arena) {
      center_pivot = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, center_pivot, submessage_arena);
    }
    
  } else {
    
  }
  center_pivot_ = center_pivot;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.FieldData.center_pivot)
}

// -------------------------------------------------------------------

// HeadlandData

// -------------------------------------------------------------------

// PrivateRoadData

// -------------------------------------------------------------------

// ObstacleData

// -------------------------------------------------------------------

// PlantingHeading

// double azimuth_degrees = 1;
inline bool PlantingHeading::_internal_has_azimuth_degrees() const {
  return heading_case() == kAzimuthDegrees;
}
inline bool PlantingHeading::has_azimuth_degrees() const {
  return _internal_has_azimuth_degrees();
}
inline void PlantingHeading::set_has_azimuth_degrees() {
  _oneof_case_[0] = kAzimuthDegrees;
}
inline void PlantingHeading::clear_azimuth_degrees() {
  if (_internal_has_azimuth_degrees()) {
    heading_.azimuth_degrees_ = 0;
    clear_has_heading();
  }
}
inline double PlantingHeading::_internal_azimuth_degrees() const {
  if (_internal_has_azimuth_degrees()) {
    return heading_.azimuth_degrees_;
  }
  return 0;
}
inline void PlantingHeading::_internal_set_azimuth_degrees(double value) {
  if (!_internal_has_azimuth_degrees()) {
    clear_heading();
    set_has_azimuth_degrees();
  }
  heading_.azimuth_degrees_ = value;
}
inline double PlantingHeading::azimuth_degrees() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.PlantingHeading.azimuth_degrees)
  return _internal_azimuth_degrees();
}
inline void PlantingHeading::set_azimuth_degrees(double value) {
  _internal_set_azimuth_degrees(value);
  // @@protoc_insertion_point(field_set:carbon.portal.farm.PlantingHeading.azimuth_degrees)
}

// .carbon.geo.AbLine ab_line = 2;
inline bool PlantingHeading::_internal_has_ab_line() const {
  return heading_case() == kAbLine;
}
inline bool PlantingHeading::has_ab_line() const {
  return _internal_has_ab_line();
}
inline void PlantingHeading::set_has_ab_line() {
  _oneof_case_[0] = kAbLine;
}
inline ::carbon::geo::AbLine* PlantingHeading::release_ab_line() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.PlantingHeading.ab_line)
  if (_internal_has_ab_line()) {
    clear_has_heading();
      ::carbon::geo::AbLine* temp = heading_.ab_line_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    heading_.ab_line_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::geo::AbLine& PlantingHeading::_internal_ab_line() const {
  return _internal_has_ab_line()
      ? *heading_.ab_line_
      : reinterpret_cast< ::carbon::geo::AbLine&>(::carbon::geo::_AbLine_default_instance_);
}
inline const ::carbon::geo::AbLine& PlantingHeading::ab_line() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.PlantingHeading.ab_line)
  return _internal_ab_line();
}
inline ::carbon::geo::AbLine* PlantingHeading::unsafe_arena_release_ab_line() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.portal.farm.PlantingHeading.ab_line)
  if (_internal_has_ab_line()) {
    clear_has_heading();
    ::carbon::geo::AbLine* temp = heading_.ab_line_;
    heading_.ab_line_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void PlantingHeading::unsafe_arena_set_allocated_ab_line(::carbon::geo::AbLine* ab_line) {
  clear_heading();
  if (ab_line) {
    set_has_ab_line();
    heading_.ab_line_ = ab_line;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.PlantingHeading.ab_line)
}
inline ::carbon::geo::AbLine* PlantingHeading::_internal_mutable_ab_line() {
  if (!_internal_has_ab_line()) {
    clear_heading();
    set_has_ab_line();
    heading_.ab_line_ = CreateMaybeMessage< ::carbon::geo::AbLine >(GetArenaForAllocation());
  }
  return heading_.ab_line_;
}
inline ::carbon::geo::AbLine* PlantingHeading::mutable_ab_line() {
  ::carbon::geo::AbLine* _msg = _internal_mutable_ab_line();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.PlantingHeading.ab_line)
  return _msg;
}

inline bool PlantingHeading::has_heading() const {
  return heading_case() != HEADING_NOT_SET;
}
inline void PlantingHeading::clear_has_heading() {
  _oneof_case_[0] = HEADING_NOT_SET;
}
inline PlantingHeading::HeadingCase PlantingHeading::heading_case() const {
  return PlantingHeading::HeadingCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// CenterPivot

// .carbon.geo.Point center = 1;
inline bool CenterPivot::_internal_has_center() const {
  return this != internal_default_instance() && center_ != nullptr;
}
inline bool CenterPivot::has_center() const {
  return _internal_has_center();
}
inline const ::carbon::geo::Point& CenterPivot::_internal_center() const {
  const ::carbon::geo::Point* p = center_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::Point&>(
      ::carbon::geo::_Point_default_instance_);
}
inline const ::carbon::geo::Point& CenterPivot::center() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.CenterPivot.center)
  return _internal_center();
}
inline void CenterPivot::unsafe_arena_set_allocated_center(
    ::carbon::geo::Point* center) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_);
  }
  center_ = center;
  if (center) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.CenterPivot.center)
}
inline ::carbon::geo::Point* CenterPivot::release_center() {
  
  ::carbon::geo::Point* temp = center_;
  center_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::Point* CenterPivot::unsafe_arena_release_center() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.CenterPivot.center)
  
  ::carbon::geo::Point* temp = center_;
  center_ = nullptr;
  return temp;
}
inline ::carbon::geo::Point* CenterPivot::_internal_mutable_center() {
  
  if (center_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::Point>(GetArenaForAllocation());
    center_ = p;
  }
  return center_;
}
inline ::carbon::geo::Point* CenterPivot::mutable_center() {
  ::carbon::geo::Point* _msg = _internal_mutable_center();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.CenterPivot.center)
  return _msg;
}
inline void CenterPivot::set_allocated_center(::carbon::geo::Point* center) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_);
  }
  if (center) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center));
    if (message_arena != submessage_arena) {
      center = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, center, submessage_arena);
    }
    
  } else {
    
  }
  center_ = center;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.CenterPivot.center)
}

// double width_meters = 2;
inline void CenterPivot::clear_width_meters() {
  width_meters_ = 0;
}
inline double CenterPivot::_internal_width_meters() const {
  return width_meters_;
}
inline double CenterPivot::width_meters() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.CenterPivot.width_meters)
  return _internal_width_meters();
}
inline void CenterPivot::_internal_set_width_meters(double value) {
  
  width_meters_ = value;
}
inline void CenterPivot::set_width_meters(double value) {
  _internal_set_width_meters(value);
  // @@protoc_insertion_point(field_set:carbon.portal.farm.CenterPivot.width_meters)
}

// double length_meters = 3;
inline void CenterPivot::clear_length_meters() {
  length_meters_ = 0;
}
inline double CenterPivot::_internal_length_meters() const {
  return length_meters_;
}
inline double CenterPivot::length_meters() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.CenterPivot.length_meters)
  return _internal_length_meters();
}
inline void CenterPivot::_internal_set_length_meters(double value) {
  
  length_meters_ = value;
}
inline void CenterPivot::set_length_meters(double value) {
  _internal_set_length_meters(value);
  // @@protoc_insertion_point(field_set:carbon.portal.farm.CenterPivot.length_meters)
}

// -------------------------------------------------------------------

// CreateFarmRequest

// .carbon.portal.farm.Farm farm = 1;
inline bool CreateFarmRequest::_internal_has_farm() const {
  return this != internal_default_instance() && farm_ != nullptr;
}
inline bool CreateFarmRequest::has_farm() const {
  return _internal_has_farm();
}
inline void CreateFarmRequest::clear_farm() {
  if (GetArenaForAllocation() == nullptr && farm_ != nullptr) {
    delete farm_;
  }
  farm_ = nullptr;
}
inline const ::carbon::portal::farm::Farm& CreateFarmRequest::_internal_farm() const {
  const ::carbon::portal::farm::Farm* p = farm_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::farm::Farm&>(
      ::carbon::portal::farm::_Farm_default_instance_);
}
inline const ::carbon::portal::farm::Farm& CreateFarmRequest::farm() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.CreateFarmRequest.farm)
  return _internal_farm();
}
inline void CreateFarmRequest::unsafe_arena_set_allocated_farm(
    ::carbon::portal::farm::Farm* farm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(farm_);
  }
  farm_ = farm;
  if (farm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.CreateFarmRequest.farm)
}
inline ::carbon::portal::farm::Farm* CreateFarmRequest::release_farm() {
  
  ::carbon::portal::farm::Farm* temp = farm_;
  farm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::farm::Farm* CreateFarmRequest::unsafe_arena_release_farm() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.CreateFarmRequest.farm)
  
  ::carbon::portal::farm::Farm* temp = farm_;
  farm_ = nullptr;
  return temp;
}
inline ::carbon::portal::farm::Farm* CreateFarmRequest::_internal_mutable_farm() {
  
  if (farm_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::farm::Farm>(GetArenaForAllocation());
    farm_ = p;
  }
  return farm_;
}
inline ::carbon::portal::farm::Farm* CreateFarmRequest::mutable_farm() {
  ::carbon::portal::farm::Farm* _msg = _internal_mutable_farm();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.CreateFarmRequest.farm)
  return _msg;
}
inline void CreateFarmRequest::set_allocated_farm(::carbon::portal::farm::Farm* farm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete farm_;
  }
  if (farm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::Farm>::GetOwningArena(farm);
    if (message_arena != submessage_arena) {
      farm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, farm, submessage_arena);
    }
    
  } else {
    
  }
  farm_ = farm;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.CreateFarmRequest.farm)
}

// -------------------------------------------------------------------

// UpdateFarmRequest

// .carbon.portal.farm.Farm farm = 1;
inline bool UpdateFarmRequest::_internal_has_farm() const {
  return this != internal_default_instance() && farm_ != nullptr;
}
inline bool UpdateFarmRequest::has_farm() const {
  return _internal_has_farm();
}
inline void UpdateFarmRequest::clear_farm() {
  if (GetArenaForAllocation() == nullptr && farm_ != nullptr) {
    delete farm_;
  }
  farm_ = nullptr;
}
inline const ::carbon::portal::farm::Farm& UpdateFarmRequest::_internal_farm() const {
  const ::carbon::portal::farm::Farm* p = farm_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::farm::Farm&>(
      ::carbon::portal::farm::_Farm_default_instance_);
}
inline const ::carbon::portal::farm::Farm& UpdateFarmRequest::farm() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.UpdateFarmRequest.farm)
  return _internal_farm();
}
inline void UpdateFarmRequest::unsafe_arena_set_allocated_farm(
    ::carbon::portal::farm::Farm* farm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(farm_);
  }
  farm_ = farm;
  if (farm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.UpdateFarmRequest.farm)
}
inline ::carbon::portal::farm::Farm* UpdateFarmRequest::release_farm() {
  
  ::carbon::portal::farm::Farm* temp = farm_;
  farm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::farm::Farm* UpdateFarmRequest::unsafe_arena_release_farm() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.UpdateFarmRequest.farm)
  
  ::carbon::portal::farm::Farm* temp = farm_;
  farm_ = nullptr;
  return temp;
}
inline ::carbon::portal::farm::Farm* UpdateFarmRequest::_internal_mutable_farm() {
  
  if (farm_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::farm::Farm>(GetArenaForAllocation());
    farm_ = p;
  }
  return farm_;
}
inline ::carbon::portal::farm::Farm* UpdateFarmRequest::mutable_farm() {
  ::carbon::portal::farm::Farm* _msg = _internal_mutable_farm();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.UpdateFarmRequest.farm)
  return _msg;
}
inline void UpdateFarmRequest::set_allocated_farm(::carbon::portal::farm::Farm* farm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete farm_;
  }
  if (farm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::Farm>::GetOwningArena(farm);
    if (message_arena != submessage_arena) {
      farm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, farm, submessage_arena);
    }
    
  } else {
    
  }
  farm_ = farm;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.UpdateFarmRequest.farm)
}

// -------------------------------------------------------------------

// ListFarmsRequest

// string page_token = 1;
inline void ListFarmsRequest::clear_page_token() {
  page_token_.ClearToEmpty();
}
inline const std::string& ListFarmsRequest::page_token() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.ListFarmsRequest.page_token)
  return _internal_page_token();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ListFarmsRequest::set_page_token(ArgT0&& arg0, ArgT... args) {
 
 page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.farm.ListFarmsRequest.page_token)
}
inline std::string* ListFarmsRequest::mutable_page_token() {
  std::string* _s = _internal_mutable_page_token();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.ListFarmsRequest.page_token)
  return _s;
}
inline const std::string& ListFarmsRequest::_internal_page_token() const {
  return page_token_.Get();
}
inline void ListFarmsRequest::_internal_set_page_token(const std::string& value) {
  
  page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ListFarmsRequest::_internal_mutable_page_token() {
  
  return page_token_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ListFarmsRequest::release_page_token() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.ListFarmsRequest.page_token)
  return page_token_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ListFarmsRequest::set_allocated_page_token(std::string* page_token) {
  if (page_token != nullptr) {
    
  } else {
    
  }
  page_token_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), page_token,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (page_token_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.ListFarmsRequest.page_token)
}

// -------------------------------------------------------------------

// ListFarmsResponse

// repeated .carbon.portal.farm.Farm farms = 1;
inline int ListFarmsResponse::_internal_farms_size() const {
  return farms_.size();
}
inline int ListFarmsResponse::farms_size() const {
  return _internal_farms_size();
}
inline void ListFarmsResponse::clear_farms() {
  farms_.Clear();
}
inline ::carbon::portal::farm::Farm* ListFarmsResponse::mutable_farms(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.ListFarmsResponse.farms)
  return farms_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Farm >*
ListFarmsResponse::mutable_farms() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.farm.ListFarmsResponse.farms)
  return &farms_;
}
inline const ::carbon::portal::farm::Farm& ListFarmsResponse::_internal_farms(int index) const {
  return farms_.Get(index);
}
inline const ::carbon::portal::farm::Farm& ListFarmsResponse::farms(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.ListFarmsResponse.farms)
  return _internal_farms(index);
}
inline ::carbon::portal::farm::Farm* ListFarmsResponse::_internal_add_farms() {
  return farms_.Add();
}
inline ::carbon::portal::farm::Farm* ListFarmsResponse::add_farms() {
  ::carbon::portal::farm::Farm* _add = _internal_add_farms();
  // @@protoc_insertion_point(field_add:carbon.portal.farm.ListFarmsResponse.farms)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::farm::Farm >&
ListFarmsResponse::farms() const {
  // @@protoc_insertion_point(field_list:carbon.portal.farm.ListFarmsResponse.farms)
  return farms_;
}

// string next_page_token = 2;
inline void ListFarmsResponse::clear_next_page_token() {
  next_page_token_.ClearToEmpty();
}
inline const std::string& ListFarmsResponse::next_page_token() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.ListFarmsResponse.next_page_token)
  return _internal_next_page_token();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ListFarmsResponse::set_next_page_token(ArgT0&& arg0, ArgT... args) {
 
 next_page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.farm.ListFarmsResponse.next_page_token)
}
inline std::string* ListFarmsResponse::mutable_next_page_token() {
  std::string* _s = _internal_mutable_next_page_token();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.ListFarmsResponse.next_page_token)
  return _s;
}
inline const std::string& ListFarmsResponse::_internal_next_page_token() const {
  return next_page_token_.Get();
}
inline void ListFarmsResponse::_internal_set_next_page_token(const std::string& value) {
  
  next_page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ListFarmsResponse::_internal_mutable_next_page_token() {
  
  return next_page_token_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ListFarmsResponse::release_next_page_token() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.ListFarmsResponse.next_page_token)
  return next_page_token_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ListFarmsResponse::set_allocated_next_page_token(std::string* next_page_token) {
  if (next_page_token != nullptr) {
    
  } else {
    
  }
  next_page_token_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), next_page_token,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (next_page_token_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    next_page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.ListFarmsResponse.next_page_token)
}

// -------------------------------------------------------------------

// GetFarmRequest

// .carbon.geo.Id id = 1;
inline bool GetFarmRequest::_internal_has_id() const {
  return this != internal_default_instance() && id_ != nullptr;
}
inline bool GetFarmRequest::has_id() const {
  return _internal_has_id();
}
inline const ::carbon::geo::Id& GetFarmRequest::_internal_id() const {
  const ::carbon::geo::Id* p = id_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::Id&>(
      ::carbon::geo::_Id_default_instance_);
}
inline const ::carbon::geo::Id& GetFarmRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.GetFarmRequest.id)
  return _internal_id();
}
inline void GetFarmRequest::unsafe_arena_set_allocated_id(
    ::carbon::geo::Id* id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(id_);
  }
  id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.GetFarmRequest.id)
}
inline ::carbon::geo::Id* GetFarmRequest::release_id() {
  
  ::carbon::geo::Id* temp = id_;
  id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::Id* GetFarmRequest::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.GetFarmRequest.id)
  
  ::carbon::geo::Id* temp = id_;
  id_ = nullptr;
  return temp;
}
inline ::carbon::geo::Id* GetFarmRequest::_internal_mutable_id() {
  
  if (id_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::Id>(GetArenaForAllocation());
    id_ = p;
  }
  return id_;
}
inline ::carbon::geo::Id* GetFarmRequest::mutable_id() {
  ::carbon::geo::Id* _msg = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.GetFarmRequest.id)
  return _msg;
}
inline void GetFarmRequest::set_allocated_id(::carbon::geo::Id* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(id_);
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(id));
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.GetFarmRequest.id)
}

// .google.protobuf.Timestamp if_modified_since = 2;
inline bool GetFarmRequest::_internal_has_if_modified_since() const {
  return this != internal_default_instance() && if_modified_since_ != nullptr;
}
inline bool GetFarmRequest::has_if_modified_since() const {
  return _internal_has_if_modified_since();
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& GetFarmRequest::_internal_if_modified_since() const {
  const ::PROTOBUF_NAMESPACE_ID::Timestamp* p = if_modified_since_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Timestamp&>(
      ::PROTOBUF_NAMESPACE_ID::_Timestamp_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& GetFarmRequest::if_modified_since() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.GetFarmRequest.if_modified_since)
  return _internal_if_modified_since();
}
inline void GetFarmRequest::unsafe_arena_set_allocated_if_modified_since(
    ::PROTOBUF_NAMESPACE_ID::Timestamp* if_modified_since) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(if_modified_since_);
  }
  if_modified_since_ = if_modified_since;
  if (if_modified_since) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.GetFarmRequest.if_modified_since)
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* GetFarmRequest::release_if_modified_since() {
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = if_modified_since_;
  if_modified_since_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* GetFarmRequest::unsafe_arena_release_if_modified_since() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.GetFarmRequest.if_modified_since)
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = if_modified_since_;
  if_modified_since_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* GetFarmRequest::_internal_mutable_if_modified_since() {
  
  if (if_modified_since_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Timestamp>(GetArenaForAllocation());
    if_modified_since_ = p;
  }
  return if_modified_since_;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* GetFarmRequest::mutable_if_modified_since() {
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _msg = _internal_mutable_if_modified_since();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.GetFarmRequest.if_modified_since)
  return _msg;
}
inline void GetFarmRequest::set_allocated_if_modified_since(::PROTOBUF_NAMESPACE_ID::Timestamp* if_modified_since) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(if_modified_since_);
  }
  if (if_modified_since) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(if_modified_since));
    if (message_arena != submessage_arena) {
      if_modified_since = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, if_modified_since, submessage_arena);
    }
    
  } else {
    
  }
  if_modified_since_ = if_modified_since;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.GetFarmRequest.if_modified_since)
}

// -------------------------------------------------------------------

// GetFarmResponse

// .carbon.portal.farm.Farm farm = 1;
inline bool GetFarmResponse::_internal_has_farm() const {
  return this != internal_default_instance() && farm_ != nullptr;
}
inline bool GetFarmResponse::has_farm() const {
  return _internal_has_farm();
}
inline void GetFarmResponse::clear_farm() {
  if (GetArenaForAllocation() == nullptr && farm_ != nullptr) {
    delete farm_;
  }
  farm_ = nullptr;
}
inline const ::carbon::portal::farm::Farm& GetFarmResponse::_internal_farm() const {
  const ::carbon::portal::farm::Farm* p = farm_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::farm::Farm&>(
      ::carbon::portal::farm::_Farm_default_instance_);
}
inline const ::carbon::portal::farm::Farm& GetFarmResponse::farm() const {
  // @@protoc_insertion_point(field_get:carbon.portal.farm.GetFarmResponse.farm)
  return _internal_farm();
}
inline void GetFarmResponse::unsafe_arena_set_allocated_farm(
    ::carbon::portal::farm::Farm* farm) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(farm_);
  }
  farm_ = farm;
  if (farm) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.farm.GetFarmResponse.farm)
}
inline ::carbon::portal::farm::Farm* GetFarmResponse::release_farm() {
  
  ::carbon::portal::farm::Farm* temp = farm_;
  farm_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::farm::Farm* GetFarmResponse::unsafe_arena_release_farm() {
  // @@protoc_insertion_point(field_release:carbon.portal.farm.GetFarmResponse.farm)
  
  ::carbon::portal::farm::Farm* temp = farm_;
  farm_ = nullptr;
  return temp;
}
inline ::carbon::portal::farm::Farm* GetFarmResponse::_internal_mutable_farm() {
  
  if (farm_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::farm::Farm>(GetArenaForAllocation());
    farm_ = p;
  }
  return farm_;
}
inline ::carbon::portal::farm::Farm* GetFarmResponse::mutable_farm() {
  ::carbon::portal::farm::Farm* _msg = _internal_mutable_farm();
  // @@protoc_insertion_point(field_mutable:carbon.portal.farm.GetFarmResponse.farm)
  return _msg;
}
inline void GetFarmResponse::set_allocated_farm(::carbon::portal::farm::Farm* farm) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete farm_;
  }
  if (farm) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::Farm>::GetOwningArena(farm);
    if (message_arena != submessage_arena) {
      farm = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, farm, submessage_arena);
    }
    
  } else {
    
  }
  farm_ = farm;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.GetFarmResponse.farm)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace farm
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2ffarm_2eproto

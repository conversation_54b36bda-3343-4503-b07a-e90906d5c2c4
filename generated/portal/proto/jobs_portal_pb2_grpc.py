# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.portal.proto import jobs_portal_pb2 as portal_dot_proto_dot_jobs__portal__pb2
from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2


class PortalJobsServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.UploadJob = channel.unary_unary(
                '/carbon.portal.jobs.PortalJobsService/UploadJob',
                request_serializer=portal_dot_proto_dot_jobs__portal__pb2.UploadJobRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.UploadJobConfigDump = channel.unary_unary(
                '/carbon.portal.jobs.PortalJobsService/UploadJobConfigDump',
                request_serializer=portal_dot_proto_dot_jobs__portal__pb2.UploadJobConfigDumpRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.UploadJobMetrics = channel.unary_unary(
                '/carbon.portal.jobs.PortalJobsService/UploadJobMetrics',
                request_serializer=portal_dot_proto_dot_jobs__portal__pb2.UploadJobMetricsRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )


class PortalJobsServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def UploadJob(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UploadJobConfigDump(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UploadJobMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PortalJobsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'UploadJob': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadJob,
                    request_deserializer=portal_dot_proto_dot_jobs__portal__pb2.UploadJobRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'UploadJobConfigDump': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadJobConfigDump,
                    request_deserializer=portal_dot_proto_dot_jobs__portal__pb2.UploadJobConfigDumpRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'UploadJobMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadJobMetrics,
                    request_deserializer=portal_dot_proto_dot_jobs__portal__pb2.UploadJobMetricsRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.portal.jobs.PortalJobsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class PortalJobsService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def UploadJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.jobs.PortalJobsService/UploadJob',
            portal_dot_proto_dot_jobs__portal__pb2.UploadJobRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UploadJobConfigDump(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.jobs.PortalJobsService/UploadJobConfigDump',
            portal_dot_proto_dot_jobs__portal__pb2.UploadJobConfigDumpRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UploadJobMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.jobs.PortalJobsService/UploadJobMetrics',
            portal_dot_proto_dot_jobs__portal__pb2.UploadJobMetricsRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/db.proto

#include "portal/proto/db.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace db {
constexpr DB::DB(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(int64_t{0})
  , created_at_(int64_t{0})
  , updated_at_(int64_t{0})
  , deleted_at_(int64_t{0}){}
struct DBDefaultTypeInternal {
  constexpr DBDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DBDefaultTypeInternal() {}
  union {
    DB _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DBDefaultTypeInternal _DB_default_instance_;
}  // namespace db
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2fdb_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2fdb_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2fdb_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2fdb_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::db::DB, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::db::DB, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::db::DB, created_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::db::DB, updated_at_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::db::DB, deleted_at_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::db::DB)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::db::_DB_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2fdb_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\025portal/proto/db.proto\022\020carbon.portal.d"
  "b\"L\n\002DB\022\n\n\002id\030\001 \001(\003\022\022\n\ncreated_at\030\002 \001(\003\022"
  "\022\n\nupdated_at\030\003 \001(\003\022\022\n\ndeleted_at\030\004 \001(\003B"
  "\016Z\014proto/portalb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2fdb_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fdb_2eproto = {
  false, false, 143, descriptor_table_protodef_portal_2fproto_2fdb_2eproto, "portal/proto/db.proto", 
  &descriptor_table_portal_2fproto_2fdb_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_portal_2fproto_2fdb_2eproto::offsets,
  file_level_metadata_portal_2fproto_2fdb_2eproto, file_level_enum_descriptors_portal_2fproto_2fdb_2eproto, file_level_service_descriptors_portal_2fproto_2fdb_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2fdb_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2fdb_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2fdb_2eproto(&descriptor_table_portal_2fproto_2fdb_2eproto);
namespace carbon {
namespace portal {
namespace db {

// ===================================================================

class DB::_Internal {
 public:
};

DB::DB(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.db.DB)
}
DB::DB(const DB& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&deleted_at_) -
    reinterpret_cast<char*>(&id_)) + sizeof(deleted_at_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.db.DB)
}

inline void DB::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&deleted_at_) -
    reinterpret_cast<char*>(&id_)) + sizeof(deleted_at_));
}

DB::~DB() {
  // @@protoc_insertion_point(destructor:carbon.portal.db.DB)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DB::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DB::ArenaDtor(void* object) {
  DB* _this = reinterpret_cast< DB* >(object);
  (void)_this;
}
void DB::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DB::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DB::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.db.DB)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&deleted_at_) -
      reinterpret_cast<char*>(&id_)) + sizeof(deleted_at_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DB::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 created_at = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          created_at_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 updated_at = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          updated_at_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 deleted_at = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          deleted_at_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DB::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.db.DB)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_id(), target);
  }

  // int64 created_at = 2;
  if (this->_internal_created_at() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_created_at(), target);
  }

  // int64 updated_at = 3;
  if (this->_internal_updated_at() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_updated_at(), target);
  }

  // int64 deleted_at = 4;
  if (this->_internal_deleted_at() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(4, this->_internal_deleted_at(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.db.DB)
  return target;
}

size_t DB::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.db.DB)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_id());
  }

  // int64 created_at = 2;
  if (this->_internal_created_at() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_created_at());
  }

  // int64 updated_at = 3;
  if (this->_internal_updated_at() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_updated_at());
  }

  // int64 deleted_at = 4;
  if (this->_internal_deleted_at() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_deleted_at());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DB::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DB::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DB::GetClassData() const { return &_class_data_; }

void DB::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DB *>(to)->MergeFrom(
      static_cast<const DB &>(from));
}


void DB::MergeFrom(const DB& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.db.DB)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_created_at() != 0) {
    _internal_set_created_at(from._internal_created_at());
  }
  if (from._internal_updated_at() != 0) {
    _internal_set_updated_at(from._internal_updated_at());
  }
  if (from._internal_deleted_at() != 0) {
    _internal_set_deleted_at(from._internal_deleted_at());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DB::CopyFrom(const DB& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.db.DB)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DB::IsInitialized() const {
  return true;
}

void DB::InternalSwap(DB* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DB, deleted_at_)
      + sizeof(DB::deleted_at_)
      - PROTOBUF_FIELD_OFFSET(DB, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DB::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fdb_2eproto_getter, &descriptor_table_portal_2fproto_2fdb_2eproto_once,
      file_level_metadata_portal_2fproto_2fdb_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace db
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::db::DB* Arena::CreateMaybeMessage< ::carbon::portal::db::DB >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::db::DB >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

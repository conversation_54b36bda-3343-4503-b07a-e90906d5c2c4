// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/spatial_metrics_sync.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "proto/metrics/metrics.pb.h"
#include "portal/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto;
namespace carbon {
namespace portal {
namespace spatial_metrics {
class SyncSpatialMetricBlocksRequest;
struct SyncSpatialMetricBlocksRequestDefaultTypeInternal;
extern SyncSpatialMetricBlocksRequestDefaultTypeInternal _SyncSpatialMetricBlocksRequest_default_instance_;
}  // namespace spatial_metrics
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest* Arena::CreateMaybeMessage<::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace spatial_metrics {

// ===================================================================

class SyncSpatialMetricBlocksRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest) */ {
 public:
  inline SyncSpatialMetricBlocksRequest() : SyncSpatialMetricBlocksRequest(nullptr) {}
  ~SyncSpatialMetricBlocksRequest() override;
  explicit constexpr SyncSpatialMetricBlocksRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SyncSpatialMetricBlocksRequest(const SyncSpatialMetricBlocksRequest& from);
  SyncSpatialMetricBlocksRequest(SyncSpatialMetricBlocksRequest&& from) noexcept
    : SyncSpatialMetricBlocksRequest() {
    *this = ::std::move(from);
  }

  inline SyncSpatialMetricBlocksRequest& operator=(const SyncSpatialMetricBlocksRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SyncSpatialMetricBlocksRequest& operator=(SyncSpatialMetricBlocksRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SyncSpatialMetricBlocksRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SyncSpatialMetricBlocksRequest* internal_default_instance() {
    return reinterpret_cast<const SyncSpatialMetricBlocksRequest*>(
               &_SyncSpatialMetricBlocksRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SyncSpatialMetricBlocksRequest& a, SyncSpatialMetricBlocksRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SyncSpatialMetricBlocksRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SyncSpatialMetricBlocksRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SyncSpatialMetricBlocksRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SyncSpatialMetricBlocksRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SyncSpatialMetricBlocksRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SyncSpatialMetricBlocksRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SyncSpatialMetricBlocksRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest";
  }
  protected:
  explicit SyncSpatialMetricBlocksRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBlocksFieldNumber = 1,
    kRobotFieldNumber = 2,
  };
  // repeated .carbon.metrics.SpatialMetricBlock blocks = 1;
  int blocks_size() const;
  private:
  int _internal_blocks_size() const;
  public:
  void clear_blocks();
  ::carbon::metrics::SpatialMetricBlock* mutable_blocks(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::SpatialMetricBlock >*
      mutable_blocks();
  private:
  const ::carbon::metrics::SpatialMetricBlock& _internal_blocks(int index) const;
  ::carbon::metrics::SpatialMetricBlock* _internal_add_blocks();
  public:
  const ::carbon::metrics::SpatialMetricBlock& blocks(int index) const;
  ::carbon::metrics::SpatialMetricBlock* add_blocks();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::SpatialMetricBlock >&
      blocks() const;

  // string robot = 2;
  void clear_robot();
  const std::string& robot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot();
  PROTOBUF_NODISCARD std::string* release_robot();
  void set_allocated_robot(std::string* robot);
  private:
  const std::string& _internal_robot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot(const std::string& value);
  std::string* _internal_mutable_robot();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::SpatialMetricBlock > blocks_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SyncSpatialMetricBlocksRequest

// repeated .carbon.metrics.SpatialMetricBlock blocks = 1;
inline int SyncSpatialMetricBlocksRequest::_internal_blocks_size() const {
  return blocks_.size();
}
inline int SyncSpatialMetricBlocksRequest::blocks_size() const {
  return _internal_blocks_size();
}
inline ::carbon::metrics::SpatialMetricBlock* SyncSpatialMetricBlocksRequest::mutable_blocks(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.blocks)
  return blocks_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::SpatialMetricBlock >*
SyncSpatialMetricBlocksRequest::mutable_blocks() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.blocks)
  return &blocks_;
}
inline const ::carbon::metrics::SpatialMetricBlock& SyncSpatialMetricBlocksRequest::_internal_blocks(int index) const {
  return blocks_.Get(index);
}
inline const ::carbon::metrics::SpatialMetricBlock& SyncSpatialMetricBlocksRequest::blocks(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.blocks)
  return _internal_blocks(index);
}
inline ::carbon::metrics::SpatialMetricBlock* SyncSpatialMetricBlocksRequest::_internal_add_blocks() {
  return blocks_.Add();
}
inline ::carbon::metrics::SpatialMetricBlock* SyncSpatialMetricBlocksRequest::add_blocks() {
  ::carbon::metrics::SpatialMetricBlock* _add = _internal_add_blocks();
  // @@protoc_insertion_point(field_add:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.blocks)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::SpatialMetricBlock >&
SyncSpatialMetricBlocksRequest::blocks() const {
  // @@protoc_insertion_point(field_list:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.blocks)
  return blocks_;
}

// string robot = 2;
inline void SyncSpatialMetricBlocksRequest::clear_robot() {
  robot_.ClearToEmpty();
}
inline const std::string& SyncSpatialMetricBlocksRequest::robot() const {
  // @@protoc_insertion_point(field_get:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.robot)
  return _internal_robot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SyncSpatialMetricBlocksRequest::set_robot(ArgT0&& arg0, ArgT... args) {
 
 robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.robot)
}
inline std::string* SyncSpatialMetricBlocksRequest::mutable_robot() {
  std::string* _s = _internal_mutable_robot();
  // @@protoc_insertion_point(field_mutable:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.robot)
  return _s;
}
inline const std::string& SyncSpatialMetricBlocksRequest::_internal_robot() const {
  return robot_.Get();
}
inline void SyncSpatialMetricBlocksRequest::_internal_set_robot(const std::string& value) {
  
  robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SyncSpatialMetricBlocksRequest::_internal_mutable_robot() {
  
  return robot_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SyncSpatialMetricBlocksRequest::release_robot() {
  // @@protoc_insertion_point(field_release:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.robot)
  return robot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SyncSpatialMetricBlocksRequest::set_allocated_robot(std::string* robot) {
  if (robot != nullptr) {
    
  } else {
    
  }
  robot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.robot)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace spatial_metrics
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto

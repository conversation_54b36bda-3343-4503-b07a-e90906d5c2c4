"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.alarm_pb2 import (
    AlarmRow as frontend___proto___alarm_pb2___AlarmRow,
)

from generated.frontend.proto.laser_pb2 import (
    Laser<PERSON>tateList as frontend___proto___laser_pb2___LaserStateList,
)

from generated.frontend.proto.status_bar_pb2 import (
    StatusValue as frontend___proto___status_bar_pb2___StatusValue,
    TranslatedStatusMessage as frontend___proto___status_bar_pb2___TranslatedStatusMessage,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
    RepeatedComposite<PERSON>ield<PERSON>ontainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from google.protobuf.struct_pb2 import (
    Struct as google___protobuf___struct_pb2___Struct,
)

from generated.proto.metrics.metrics_pb2 import (
    LaserChangeTimes as proto___metrics___metrics_pb2___LaserChangeTimes,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

AlarmLevelValue = typing___NewType('AlarmLevelValue', builtin___int)
type___AlarmLevelValue = AlarmLevelValue
AlarmLevel: _AlarmLevel
class _AlarmLevel(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[AlarmLevelValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    ALARM_UNKNOWN = typing___cast(AlarmLevelValue, 0)
    ALARM_CRITICAL = typing___cast(AlarmLevelValue, 1)
    ALARM_HIGH = typing___cast(AlarmLevelValue, 2)
    ALARM_MEDIUM = typing___cast(AlarmLevelValue, 3)
    ALARM_LOW = typing___cast(AlarmLevelValue, 4)
    ALARM_HIDDEN = typing___cast(AlarmLevelValue, 5)
ALARM_UNKNOWN = typing___cast(AlarmLevelValue, 0)
ALARM_CRITICAL = typing___cast(AlarmLevelValue, 1)
ALARM_HIGH = typing___cast(AlarmLevelValue, 2)
ALARM_MEDIUM = typing___cast(AlarmLevelValue, 3)
ALARM_LOW = typing___cast(AlarmLevelValue, 4)
ALARM_HIDDEN = typing___cast(AlarmLevelValue, 5)

AlarmImpactValue = typing___NewType('AlarmImpactValue', builtin___int)
type___AlarmImpactValue = AlarmImpactValue
AlarmImpact: _AlarmImpact
class _AlarmImpact(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[AlarmImpactValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    IMPACT_UNDEFINED = typing___cast(AlarmImpactValue, 0)
    IMPACT_CRITICAL = typing___cast(AlarmImpactValue, 1)
    IMPACT_OFFLINE = typing___cast(AlarmImpactValue, 2)
    IMPACT_DEGRADED = typing___cast(AlarmImpactValue, 3)
    IMPACT_NONE = typing___cast(AlarmImpactValue, 4)
IMPACT_UNDEFINED = typing___cast(AlarmImpactValue, 0)
IMPACT_CRITICAL = typing___cast(AlarmImpactValue, 1)
IMPACT_OFFLINE = typing___cast(AlarmImpactValue, 2)
IMPACT_DEGRADED = typing___cast(AlarmImpactValue, 3)
IMPACT_NONE = typing___cast(AlarmImpactValue, 4)

class AlarmRow(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    alarm_code: typing___Text = ...
    description: typing___Text = ...
    level: type___AlarmLevelValue = ...
    identifier: typing___Text = ...
    acknowledged: builtin___bool = ...
    impact: type___AlarmImpactValue = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        alarm_code : typing___Optional[typing___Text] = None,
        description : typing___Optional[typing___Text] = None,
        level : typing___Optional[type___AlarmLevelValue] = None,
        identifier : typing___Optional[typing___Text] = None,
        acknowledged : typing___Optional[builtin___bool] = None,
        impact : typing___Optional[type___AlarmImpactValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"acknowledged",b"acknowledged",u"alarm_code",b"alarm_code",u"description",b"description",u"identifier",b"identifier",u"impact",b"impact",u"level",b"level",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___AlarmRow = AlarmRow

class Location(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___float = ...
    y: builtin___float = ...
    z: builtin___float = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        z : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x",u"y",b"y",u"z",b"z"]) -> None: ...
type___Location = Location

class FieldConfig(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    banding_enabled: builtin___bool = ...
    banding_dynamic: builtin___bool = ...
    active_band_config: typing___Text = ...
    active_thinning_config_id: typing___Text = ...
    active_job_id: typing___Text = ...
    active_almanac_id: typing___Text = ...
    active_discriminator_id: typing___Text = ...
    is_weeding: builtin___bool = ...
    is_thinning: builtin___bool = ...
    active_band_config_name: typing___Text = ...
    active_thinning_config_name: typing___Text = ...
    active_job_name: typing___Text = ...
    active_almanac_name: typing___Text = ...
    active_discriminator_name: typing___Text = ...
    active_modelinator_id: typing___Text = ...
    active_velocity_estimator_id: typing___Text = ...
    active_velocity_estimator_name: typing___Text = ...
    active_category_collection_id: typing___Text = ...

    def __init__(self,
        *,
        banding_enabled : typing___Optional[builtin___bool] = None,
        banding_dynamic : typing___Optional[builtin___bool] = None,
        active_band_config : typing___Optional[typing___Text] = None,
        active_thinning_config_id : typing___Optional[typing___Text] = None,
        active_job_id : typing___Optional[typing___Text] = None,
        active_almanac_id : typing___Optional[typing___Text] = None,
        active_discriminator_id : typing___Optional[typing___Text] = None,
        is_weeding : typing___Optional[builtin___bool] = None,
        is_thinning : typing___Optional[builtin___bool] = None,
        active_band_config_name : typing___Optional[typing___Text] = None,
        active_thinning_config_name : typing___Optional[typing___Text] = None,
        active_job_name : typing___Optional[typing___Text] = None,
        active_almanac_name : typing___Optional[typing___Text] = None,
        active_discriminator_name : typing___Optional[typing___Text] = None,
        active_modelinator_id : typing___Optional[typing___Text] = None,
        active_velocity_estimator_id : typing___Optional[typing___Text] = None,
        active_velocity_estimator_name : typing___Optional[typing___Text] = None,
        active_category_collection_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active_almanac_id",b"active_almanac_id",u"active_almanac_name",b"active_almanac_name",u"active_band_config",b"active_band_config",u"active_band_config_name",b"active_band_config_name",u"active_category_collection_id",b"active_category_collection_id",u"active_discriminator_id",b"active_discriminator_id",u"active_discriminator_name",b"active_discriminator_name",u"active_job_id",b"active_job_id",u"active_job_name",b"active_job_name",u"active_modelinator_id",b"active_modelinator_id",u"active_thinning_config_id",b"active_thinning_config_id",u"active_thinning_config_name",b"active_thinning_config_name",u"active_velocity_estimator_id",b"active_velocity_estimator_id",u"active_velocity_estimator_name",b"active_velocity_estimator_name",u"banding_dynamic",b"banding_dynamic",u"banding_enabled",b"banding_enabled",u"is_thinning",b"is_thinning",u"is_weeding",b"is_weeding"]) -> None: ...
type___FieldConfig = FieldConfig

class Versions(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current: typing___Text = ...
    latest: typing___Text = ...

    def __init__(self,
        *,
        current : typing___Optional[typing___Text] = None,
        latest : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current",b"current",u"latest",b"latest"]) -> None: ...
type___Versions = Versions

class WeedingPerformance(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    area_weeded_total: builtin___float = ...
    area_weeded_today: builtin___float = ...
    time_weeded_today: builtin___int = ...

    def __init__(self,
        *,
        area_weeded_total : typing___Optional[builtin___float] = None,
        area_weeded_today : typing___Optional[builtin___float] = None,
        time_weeded_today : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"area_weeded_today",b"area_weeded_today",u"area_weeded_total",b"area_weeded_total",u"time_weeded_today",b"time_weeded_today"]) -> None: ...
type___WeedingPerformance = WeedingPerformance

class Performance(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def weeding(self) -> type___WeedingPerformance: ...

    def __init__(self,
        *,
        weeding : typing___Optional[type___WeedingPerformance] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"weeding",b"weeding"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"weeding",b"weeding"]) -> None: ...
type___Performance = Performance

class DailyMetrics(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class MetricsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___MetricsEntry = MetricsEntry


    @property
    def metrics(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    def __init__(self,
        *,
        metrics : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"metrics",b"metrics"]) -> None: ...
type___DailyMetrics = DailyMetrics

class Metrics(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class DailyMetricsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...

        @property
        def value(self) -> type___DailyMetrics: ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[type___DailyMetrics] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___DailyMetricsEntry = DailyMetricsEntry


    @property
    def daily_metrics(self) -> google___protobuf___internal___containers___MessageMap[typing___Text, type___DailyMetrics]: ...

    def __init__(self,
        *,
        daily_metrics : typing___Optional[typing___Mapping[typing___Text, type___DailyMetrics]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"daily_metrics",b"daily_metrics"]) -> None: ...
type___Metrics = Metrics

class HealthLog(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class MetricTotalsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___int = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___int] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___MetricTotalsEntry = MetricTotalsEntry

    class HostSerialsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___HostSerialsEntry = HostSerialsEntry

    class FeatureFlagsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___bool = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___bool] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___FeatureFlagsEntry = FeatureFlagsEntry

    model: typing___Text = ...
    models: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    reported_at: builtin___int = ...
    robot_serial: typing___Text = ...
    status: frontend___proto___status_bar_pb2___StatusValue = ...
    status_changed_at: builtin___int = ...
    crop: typing___Text = ...
    p2p: typing___Text = ...
    software_version: typing___Text = ...
    target_version: typing___Text = ...
    target_version_ready: builtin___bool = ...
    status_message: typing___Text = ...
    crop_id: typing___Text = ...
    robot_runtime_240v: builtin___int = ...

    @property
    def alarms(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___AlarmRow]: ...

    @property
    def location(self) -> type___Location: ...

    @property
    def performance(self) -> type___Performance: ...

    @property
    def systems(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[google___protobuf___struct_pb2___Struct]: ...

    @property
    def metric_totals(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___int]: ...

    @property
    def alarm_list(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[frontend___proto___alarm_pb2___AlarmRow]: ...

    @property
    def field_config(self) -> type___FieldConfig: ...

    @property
    def metrics(self) -> type___Metrics: ...

    @property
    def laser_state(self) -> frontend___proto___laser_pb2___LaserStateList: ...

    @property
    def laser_change_times(self) -> proto___metrics___metrics_pb2___LaserChangeTimes: ...

    @property
    def host_serials(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    @property
    def feature_flags(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___bool]: ...

    @property
    def translated_status_message(self) -> frontend___proto___status_bar_pb2___TranslatedStatusMessage: ...

    def __init__(self,
        *,
        alarms : typing___Optional[typing___Iterable[type___AlarmRow]] = None,
        location : typing___Optional[type___Location] = None,
        model : typing___Optional[typing___Text] = None,
        models : typing___Optional[typing___Iterable[typing___Text]] = None,
        performance : typing___Optional[type___Performance] = None,
        reported_at : typing___Optional[builtin___int] = None,
        robot_serial : typing___Optional[typing___Text] = None,
        systems : typing___Optional[typing___Iterable[google___protobuf___struct_pb2___Struct]] = None,
        status : typing___Optional[frontend___proto___status_bar_pb2___StatusValue] = None,
        status_changed_at : typing___Optional[builtin___int] = None,
        crop : typing___Optional[typing___Text] = None,
        p2p : typing___Optional[typing___Text] = None,
        software_version : typing___Optional[typing___Text] = None,
        target_version : typing___Optional[typing___Text] = None,
        target_version_ready : typing___Optional[builtin___bool] = None,
        status_message : typing___Optional[typing___Text] = None,
        metric_totals : typing___Optional[typing___Mapping[typing___Text, builtin___int]] = None,
        alarm_list : typing___Optional[typing___Iterable[frontend___proto___alarm_pb2___AlarmRow]] = None,
        field_config : typing___Optional[type___FieldConfig] = None,
        metrics : typing___Optional[type___Metrics] = None,
        crop_id : typing___Optional[typing___Text] = None,
        robot_runtime_240v : typing___Optional[builtin___int] = None,
        laser_state : typing___Optional[frontend___proto___laser_pb2___LaserStateList] = None,
        laser_change_times : typing___Optional[proto___metrics___metrics_pb2___LaserChangeTimes] = None,
        host_serials : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        feature_flags : typing___Optional[typing___Mapping[typing___Text, builtin___bool]] = None,
        translated_status_message : typing___Optional[frontend___proto___status_bar_pb2___TranslatedStatusMessage] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"field_config",b"field_config",u"laser_change_times",b"laser_change_times",u"laser_state",b"laser_state",u"location",b"location",u"metrics",b"metrics",u"performance",b"performance",u"translated_status_message",b"translated_status_message"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"alarm_list",b"alarm_list",u"alarms",b"alarms",u"crop",b"crop",u"crop_id",b"crop_id",u"feature_flags",b"feature_flags",u"field_config",b"field_config",u"host_serials",b"host_serials",u"laser_change_times",b"laser_change_times",u"laser_state",b"laser_state",u"location",b"location",u"metric_totals",b"metric_totals",u"metrics",b"metrics",u"model",b"model",u"models",b"models",u"p2p",b"p2p",u"performance",b"performance",u"reported_at",b"reported_at",u"robot_runtime_240v",b"robot_runtime_240v",u"robot_serial",b"robot_serial",u"software_version",b"software_version",u"status",b"status",u"status_changed_at",b"status_changed_at",u"status_message",b"status_message",u"systems",b"systems",u"target_version",b"target_version",u"target_version_ready",b"target_version_ready",u"translated_status_message",b"translated_status_message"]) -> None: ...
type___HealthLog = HealthLog

class IssueReport(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    description: typing___Text = ...
    phone_number: typing___Text = ...
    robot_serial: typing___Text = ...
    reported_at: builtin___int = ...
    crop: typing___Text = ...
    model_id: typing___Text = ...
    software_version: typing___Text = ...
    crop_id: typing___Text = ...

    def __init__(self,
        *,
        description : typing___Optional[typing___Text] = None,
        phone_number : typing___Optional[typing___Text] = None,
        robot_serial : typing___Optional[typing___Text] = None,
        reported_at : typing___Optional[builtin___int] = None,
        crop : typing___Optional[typing___Text] = None,
        model_id : typing___Optional[typing___Text] = None,
        software_version : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop",b"crop",u"crop_id",b"crop_id",u"description",b"description",u"model_id",b"model_id",u"phone_number",b"phone_number",u"reported_at",b"reported_at",u"robot_serial",b"robot_serial",u"software_version",b"software_version"]) -> None: ...
type___IssueReport = IssueReport

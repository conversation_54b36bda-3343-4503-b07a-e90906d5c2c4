# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/model_history_sync.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/model_history_sync.proto',
  package='carbon.portal.model_history',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n%portal/proto/model_history_sync.proto\x12\x1b\x63\x61rbon.portal.model_history\x1a\x17portal/proto/util.proto\"\xd6\x01\n\nModelEvent\x12\x39\n\x04type\x18\x01 \x01(\x0e\x32+.carbon.portal.model_history.ModelEventType\x12\x10\n\x08model_id\x18\x02 \x01(\t\x12\x16\n\x0emodel_nickname\x18\x03 \x01(\t\x12\x18\n\x10model_parameters\x18\x04 \x01(\t\x12\x12\n\nmodel_type\x18\x05 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x06 \x01(\t\x12\x0e\n\x06job_id\x18\x07 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x08 \x01(\x03\"b\n\x18UploadModelEventsRequest\x12\r\n\x05robot\x18\x01 \x01(\t\x12\x37\n\x06\x65vents\x18\x02 \x03(\x0b\x32\'.carbon.portal.model_history.ModelEvent*\xc6\x01\n\x0eModelEventType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0f\n\x0bROBOT_START\x10\x01\x12\n\n\x06PINNED\x10\x02\x12\x0c\n\x08UNPINNED\x10\x03\x12\x0f\n\x0bRECOMMENDED\x10\x04\x12\r\n\tACTIVATED\x10\x05\x12\x13\n\x0fNICKNAME_CHANGE\x10\x06\x12\x13\n\x0fNICKNAME_DELETE\x10\x07\x12\x1c\n\x18\x44\x45\x46\x41ULT_PARAMETER_CHANGE\x10\x08\x12\x14\n\x10PARAMETER_CHANGE\x10\t2\x80\x01\n\x17ModelHistorySyncService\x12\x65\n\x11UploadModelEvents\x12\x35.carbon.portal.model_history.UploadModelEventsRequest\x1a\x19.carbon.portal.util.EmptyB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[portal_dot_proto_dot_util__pb2.DESCRIPTOR,])

_MODELEVENTTYPE = _descriptor.EnumDescriptor(
  name='ModelEventType',
  full_name='carbon.portal.model_history.ModelEventType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ROBOT_START', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PINNED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UNPINNED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RECOMMENDED', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTIVATED', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NICKNAME_CHANGE', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NICKNAME_DELETE', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DEFAULT_PARAMETER_CHANGE', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PARAMETER_CHANGE', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=413,
  serialized_end=611,
)
_sym_db.RegisterEnumDescriptor(_MODELEVENTTYPE)

ModelEventType = enum_type_wrapper.EnumTypeWrapper(_MODELEVENTTYPE)
UNKNOWN = 0
ROBOT_START = 1
PINNED = 2
UNPINNED = 3
RECOMMENDED = 4
ACTIVATED = 5
NICKNAME_CHANGE = 6
NICKNAME_DELETE = 7
DEFAULT_PARAMETER_CHANGE = 8
PARAMETER_CHANGE = 9



_MODELEVENT = _descriptor.Descriptor(
  name='ModelEvent',
  full_name='carbon.portal.model_history.ModelEvent',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.portal.model_history.ModelEvent.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.portal.model_history.ModelEvent.model_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_nickname', full_name='carbon.portal.model_history.ModelEvent.model_nickname', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_parameters', full_name='carbon.portal.model_history.ModelEvent.model_parameters', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_type', full_name='carbon.portal.model_history.ModelEvent.model_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.portal.model_history.ModelEvent.crop_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='job_id', full_name='carbon.portal.model_history.ModelEvent.job_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.portal.model_history.ModelEvent.timestamp_ms', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=96,
  serialized_end=310,
)


_UPLOADMODELEVENTSREQUEST = _descriptor.Descriptor(
  name='UploadModelEventsRequest',
  full_name='carbon.portal.model_history.UploadModelEventsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot', full_name='carbon.portal.model_history.UploadModelEventsRequest.robot', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='events', full_name='carbon.portal.model_history.UploadModelEventsRequest.events', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=312,
  serialized_end=410,
)

_MODELEVENT.fields_by_name['type'].enum_type = _MODELEVENTTYPE
_UPLOADMODELEVENTSREQUEST.fields_by_name['events'].message_type = _MODELEVENT
DESCRIPTOR.message_types_by_name['ModelEvent'] = _MODELEVENT
DESCRIPTOR.message_types_by_name['UploadModelEventsRequest'] = _UPLOADMODELEVENTSREQUEST
DESCRIPTOR.enum_types_by_name['ModelEventType'] = _MODELEVENTTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ModelEvent = _reflection.GeneratedProtocolMessageType('ModelEvent', (_message.Message,), {
  'DESCRIPTOR' : _MODELEVENT,
  '__module__' : 'portal.proto.model_history_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.model_history.ModelEvent)
  })
_sym_db.RegisterMessage(ModelEvent)

UploadModelEventsRequest = _reflection.GeneratedProtocolMessageType('UploadModelEventsRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADMODELEVENTSREQUEST,
  '__module__' : 'portal.proto.model_history_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.model_history.UploadModelEventsRequest)
  })
_sym_db.RegisterMessage(UploadModelEventsRequest)


DESCRIPTOR._options = None

_MODELHISTORYSYNCSERVICE = _descriptor.ServiceDescriptor(
  name='ModelHistorySyncService',
  full_name='carbon.portal.model_history.ModelHistorySyncService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=614,
  serialized_end=742,
  methods=[
  _descriptor.MethodDescriptor(
    name='UploadModelEvents',
    full_name='carbon.portal.model_history.ModelHistorySyncService.UploadModelEvents',
    index=0,
    containing_service=None,
    input_type=_UPLOADMODELEVENTSREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_MODELHISTORYSYNCSERVICE)

DESCRIPTOR.services_by_name['ModelHistorySyncService'] = _MODELHISTORYSYNCSERVICE

# @@protoc_insertion_point(module_scope)

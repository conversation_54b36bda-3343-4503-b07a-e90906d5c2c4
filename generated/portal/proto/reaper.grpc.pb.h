// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/reaper.proto
#ifndef GRPC_portal_2fproto_2freaper_2eproto__INCLUDED
#define GRPC_portal_2fproto_2freaper_2eproto__INCLUDED

#include "portal/proto/reaper.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace portal {
namespace reaper {

class ReaperConfigurationService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.portal.reaper.ReaperConfigurationService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status UploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::reaper::UploadReaperConfigurationResponse>> AsyncUploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::reaper::UploadReaperConfigurationResponse>>(AsyncUploadReaperConfigurationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::reaper::UploadReaperConfigurationResponse>> PrepareAsyncUploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::reaper::UploadReaperConfigurationResponse>>(PrepareAsyncUploadReaperConfigurationRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void UploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::reaper::UploadReaperConfigurationResponse>* AsyncUploadReaperConfigurationRaw(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::reaper::UploadReaperConfigurationResponse>* PrepareAsyncUploadReaperConfigurationRaw(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status UploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::reaper::UploadReaperConfigurationResponse>> AsyncUploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::reaper::UploadReaperConfigurationResponse>>(AsyncUploadReaperConfigurationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::reaper::UploadReaperConfigurationResponse>> PrepareAsyncUploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::reaper::UploadReaperConfigurationResponse>>(PrepareAsyncUploadReaperConfigurationRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void UploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response, std::function<void(::grpc::Status)>) override;
      void UploadReaperConfiguration(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::reaper::UploadReaperConfigurationResponse>* AsyncUploadReaperConfigurationRaw(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::reaper::UploadReaperConfigurationResponse>* PrepareAsyncUploadReaperConfigurationRaw(::grpc::ClientContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_UploadReaperConfiguration_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status UploadReaperConfiguration(::grpc::ServerContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_UploadReaperConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UploadReaperConfiguration() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_UploadReaperConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadReaperConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* /*request*/, ::carbon::portal::reaper::UploadReaperConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadReaperConfiguration(::grpc::ServerContext* context, ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::reaper::UploadReaperConfigurationResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_UploadReaperConfiguration<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_UploadReaperConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UploadReaperConfiguration() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::reaper::UploadReaperConfigurationRequest, ::carbon::portal::reaper::UploadReaperConfigurationResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* request, ::carbon::portal::reaper::UploadReaperConfigurationResponse* response) { return this->UploadReaperConfiguration(context, request, response); }));}
    void SetMessageAllocatorFor_UploadReaperConfiguration(
        ::grpc::MessageAllocator< ::carbon::portal::reaper::UploadReaperConfigurationRequest, ::carbon::portal::reaper::UploadReaperConfigurationResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::reaper::UploadReaperConfigurationRequest, ::carbon::portal::reaper::UploadReaperConfigurationResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UploadReaperConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadReaperConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* /*request*/, ::carbon::portal::reaper::UploadReaperConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadReaperConfiguration(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* /*request*/, ::carbon::portal::reaper::UploadReaperConfigurationResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_UploadReaperConfiguration<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_UploadReaperConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UploadReaperConfiguration() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_UploadReaperConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadReaperConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* /*request*/, ::carbon::portal::reaper::UploadReaperConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_UploadReaperConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UploadReaperConfiguration() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_UploadReaperConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadReaperConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* /*request*/, ::carbon::portal::reaper::UploadReaperConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadReaperConfiguration(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UploadReaperConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UploadReaperConfiguration() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UploadReaperConfiguration(context, request, response); }));
    }
    ~WithRawCallbackMethod_UploadReaperConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadReaperConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* /*request*/, ::carbon::portal::reaper::UploadReaperConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadReaperConfiguration(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UploadReaperConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UploadReaperConfiguration() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::reaper::UploadReaperConfigurationRequest, ::carbon::portal::reaper::UploadReaperConfigurationResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::reaper::UploadReaperConfigurationRequest, ::carbon::portal::reaper::UploadReaperConfigurationResponse>* streamer) {
                       return this->StreamedUploadReaperConfiguration(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UploadReaperConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UploadReaperConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::portal::reaper::UploadReaperConfigurationRequest* /*request*/, ::carbon::portal::reaper::UploadReaperConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUploadReaperConfiguration(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::reaper::UploadReaperConfigurationRequest,::carbon::portal::reaper::UploadReaperConfigurationResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_UploadReaperConfiguration<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_UploadReaperConfiguration<Service > StreamedService;
};

}  // namespace reaper
}  // namespace portal
}  // namespace carbon


#endif  // GRPC_portal_2fproto_2freaper_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/reaper.proto

#include "portal/proto/reaper.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace reaper {
constexpr ReaperConfiguration::ReaperConfiguration(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : assigned_modules_()
  , current_robot_definition_(nullptr){}
struct ReaperConfigurationDefaultTypeInternal {
  constexpr ReaperConfigurationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ReaperConfigurationDefaultTypeInternal() {}
  union {
    ReaperConfiguration _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ReaperConfigurationDefaultTypeInternal _ReaperConfiguration_default_instance_;
constexpr UploadReaperConfigurationRequest::UploadReaperConfigurationRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : configuration_(nullptr){}
struct UploadReaperConfigurationRequestDefaultTypeInternal {
  constexpr UploadReaperConfigurationRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UploadReaperConfigurationRequestDefaultTypeInternal() {}
  union {
    UploadReaperConfigurationRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UploadReaperConfigurationRequestDefaultTypeInternal _UploadReaperConfigurationRequest_default_instance_;
constexpr UploadReaperConfigurationResponse::UploadReaperConfigurationResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct UploadReaperConfigurationResponseDefaultTypeInternal {
  constexpr UploadReaperConfigurationResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UploadReaperConfigurationResponseDefaultTypeInternal() {}
  union {
    UploadReaperConfigurationResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UploadReaperConfigurationResponseDefaultTypeInternal _UploadReaperConfigurationResponse_default_instance_;
}  // namespace reaper
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2freaper_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2freaper_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2freaper_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2freaper_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::reaper::ReaperConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::reaper::ReaperConfiguration, assigned_modules_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::reaper::ReaperConfiguration, current_robot_definition_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::reaper::UploadReaperConfigurationRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::reaper::UploadReaperConfigurationRequest, configuration_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::reaper::UploadReaperConfigurationResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::reaper::ReaperConfiguration)},
  { 8, -1, -1, sizeof(::carbon::portal::reaper::UploadReaperConfigurationRequest)},
  { 15, -1, -1, sizeof(::carbon::portal::reaper::UploadReaperConfigurationResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::reaper::_ReaperConfiguration_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::reaper::_UploadReaperConfigurationRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::reaper::_UploadReaperConfigurationResponse_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2freaper_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031portal/proto/reaper.proto\022\024carbon.port"
  "al.reaper\032\033frontend/proto/module.proto\"\242"
  "\001\n\023ReaperConfiguration\022@\n\020assigned_modul"
  "es\030\001 \003(\0132&.carbon.frontend.module.Module"
  "Identity\022I\n\030current_robot_definition\030\002 \001"
  "(\0132\'.carbon.frontend.module.RobotDefinit"
  "ion\"d\n UploadReaperConfigurationRequest\022"
  "@\n\rconfiguration\030\001 \001(\0132).carbon.portal.r"
  "eaper.ReaperConfiguration\"#\n!UploadReape"
  "rConfigurationResponse2\253\001\n\032ReaperConfigu"
  "rationService\022\214\001\n\031UploadReaperConfigurat"
  "ion\0226.carbon.portal.reaper.UploadReaperC"
  "onfigurationRequest\0327.carbon.portal.reap"
  "er.UploadReaperConfigurationResponseB\016Z\014"
  "proto/portalb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2freaper_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2fmodule_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2freaper_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2freaper_2eproto = {
  false, false, 580, descriptor_table_protodef_portal_2fproto_2freaper_2eproto, "portal/proto/reaper.proto", 
  &descriptor_table_portal_2fproto_2freaper_2eproto_once, descriptor_table_portal_2fproto_2freaper_2eproto_deps, 1, 3,
  schemas, file_default_instances, TableStruct_portal_2fproto_2freaper_2eproto::offsets,
  file_level_metadata_portal_2fproto_2freaper_2eproto, file_level_enum_descriptors_portal_2fproto_2freaper_2eproto, file_level_service_descriptors_portal_2fproto_2freaper_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2freaper_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2freaper_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2freaper_2eproto(&descriptor_table_portal_2fproto_2freaper_2eproto);
namespace carbon {
namespace portal {
namespace reaper {

// ===================================================================

class ReaperConfiguration::_Internal {
 public:
  static const ::carbon::frontend::module::RobotDefinition& current_robot_definition(const ReaperConfiguration* msg);
};

const ::carbon::frontend::module::RobotDefinition&
ReaperConfiguration::_Internal::current_robot_definition(const ReaperConfiguration* msg) {
  return *msg->current_robot_definition_;
}
void ReaperConfiguration::clear_assigned_modules() {
  assigned_modules_.Clear();
}
void ReaperConfiguration::clear_current_robot_definition() {
  if (GetArenaForAllocation() == nullptr && current_robot_definition_ != nullptr) {
    delete current_robot_definition_;
  }
  current_robot_definition_ = nullptr;
}
ReaperConfiguration::ReaperConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  assigned_modules_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.reaper.ReaperConfiguration)
}
ReaperConfiguration::ReaperConfiguration(const ReaperConfiguration& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      assigned_modules_(from.assigned_modules_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_current_robot_definition()) {
    current_robot_definition_ = new ::carbon::frontend::module::RobotDefinition(*from.current_robot_definition_);
  } else {
    current_robot_definition_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.reaper.ReaperConfiguration)
}

inline void ReaperConfiguration::SharedCtor() {
current_robot_definition_ = nullptr;
}

ReaperConfiguration::~ReaperConfiguration() {
  // @@protoc_insertion_point(destructor:carbon.portal.reaper.ReaperConfiguration)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ReaperConfiguration::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete current_robot_definition_;
}

void ReaperConfiguration::ArenaDtor(void* object) {
  ReaperConfiguration* _this = reinterpret_cast< ReaperConfiguration* >(object);
  (void)_this;
}
void ReaperConfiguration::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ReaperConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ReaperConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.reaper.ReaperConfiguration)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  assigned_modules_.Clear();
  if (GetArenaForAllocation() == nullptr && current_robot_definition_ != nullptr) {
    delete current_robot_definition_;
  }
  current_robot_definition_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ReaperConfiguration::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_assigned_modules(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.module.RobotDefinition current_robot_definition = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_current_robot_definition(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ReaperConfiguration::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.reaper.ReaperConfiguration)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_assigned_modules_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_assigned_modules(i), target, stream);
  }

  // .carbon.frontend.module.RobotDefinition current_robot_definition = 2;
  if (this->_internal_has_current_robot_definition()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::current_robot_definition(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.reaper.ReaperConfiguration)
  return target;
}

size_t ReaperConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.reaper.ReaperConfiguration)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 1;
  total_size += 1UL * this->_internal_assigned_modules_size();
  for (const auto& msg : this->assigned_modules_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.module.RobotDefinition current_robot_definition = 2;
  if (this->_internal_has_current_robot_definition()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *current_robot_definition_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ReaperConfiguration::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ReaperConfiguration::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ReaperConfiguration::GetClassData() const { return &_class_data_; }

void ReaperConfiguration::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ReaperConfiguration *>(to)->MergeFrom(
      static_cast<const ReaperConfiguration &>(from));
}


void ReaperConfiguration::MergeFrom(const ReaperConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.reaper.ReaperConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  assigned_modules_.MergeFrom(from.assigned_modules_);
  if (from._internal_has_current_robot_definition()) {
    _internal_mutable_current_robot_definition()->::carbon::frontend::module::RobotDefinition::MergeFrom(from._internal_current_robot_definition());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ReaperConfiguration::CopyFrom(const ReaperConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.reaper.ReaperConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReaperConfiguration::IsInitialized() const {
  return true;
}

void ReaperConfiguration::InternalSwap(ReaperConfiguration* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  assigned_modules_.InternalSwap(&other->assigned_modules_);
  swap(current_robot_definition_, other->current_robot_definition_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ReaperConfiguration::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2freaper_2eproto_getter, &descriptor_table_portal_2fproto_2freaper_2eproto_once,
      file_level_metadata_portal_2fproto_2freaper_2eproto[0]);
}

// ===================================================================

class UploadReaperConfigurationRequest::_Internal {
 public:
  static const ::carbon::portal::reaper::ReaperConfiguration& configuration(const UploadReaperConfigurationRequest* msg);
};

const ::carbon::portal::reaper::ReaperConfiguration&
UploadReaperConfigurationRequest::_Internal::configuration(const UploadReaperConfigurationRequest* msg) {
  return *msg->configuration_;
}
UploadReaperConfigurationRequest::UploadReaperConfigurationRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.reaper.UploadReaperConfigurationRequest)
}
UploadReaperConfigurationRequest::UploadReaperConfigurationRequest(const UploadReaperConfigurationRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_configuration()) {
    configuration_ = new ::carbon::portal::reaper::ReaperConfiguration(*from.configuration_);
  } else {
    configuration_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.reaper.UploadReaperConfigurationRequest)
}

inline void UploadReaperConfigurationRequest::SharedCtor() {
configuration_ = nullptr;
}

UploadReaperConfigurationRequest::~UploadReaperConfigurationRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.reaper.UploadReaperConfigurationRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UploadReaperConfigurationRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete configuration_;
}

void UploadReaperConfigurationRequest::ArenaDtor(void* object) {
  UploadReaperConfigurationRequest* _this = reinterpret_cast< UploadReaperConfigurationRequest* >(object);
  (void)_this;
}
void UploadReaperConfigurationRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UploadReaperConfigurationRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UploadReaperConfigurationRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.reaper.UploadReaperConfigurationRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && configuration_ != nullptr) {
    delete configuration_;
  }
  configuration_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UploadReaperConfigurationRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.portal.reaper.ReaperConfiguration configuration = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_configuration(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UploadReaperConfigurationRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.reaper.UploadReaperConfigurationRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.portal.reaper.ReaperConfiguration configuration = 1;
  if (this->_internal_has_configuration()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::configuration(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.reaper.UploadReaperConfigurationRequest)
  return target;
}

size_t UploadReaperConfigurationRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.reaper.UploadReaperConfigurationRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.portal.reaper.ReaperConfiguration configuration = 1;
  if (this->_internal_has_configuration()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *configuration_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UploadReaperConfigurationRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UploadReaperConfigurationRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UploadReaperConfigurationRequest::GetClassData() const { return &_class_data_; }

void UploadReaperConfigurationRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UploadReaperConfigurationRequest *>(to)->MergeFrom(
      static_cast<const UploadReaperConfigurationRequest &>(from));
}


void UploadReaperConfigurationRequest::MergeFrom(const UploadReaperConfigurationRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.reaper.UploadReaperConfigurationRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_configuration()) {
    _internal_mutable_configuration()->::carbon::portal::reaper::ReaperConfiguration::MergeFrom(from._internal_configuration());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UploadReaperConfigurationRequest::CopyFrom(const UploadReaperConfigurationRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.reaper.UploadReaperConfigurationRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UploadReaperConfigurationRequest::IsInitialized() const {
  return true;
}

void UploadReaperConfigurationRequest::InternalSwap(UploadReaperConfigurationRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(configuration_, other->configuration_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UploadReaperConfigurationRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2freaper_2eproto_getter, &descriptor_table_portal_2fproto_2freaper_2eproto_once,
      file_level_metadata_portal_2fproto_2freaper_2eproto[1]);
}

// ===================================================================

class UploadReaperConfigurationResponse::_Internal {
 public:
};

UploadReaperConfigurationResponse::UploadReaperConfigurationResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.portal.reaper.UploadReaperConfigurationResponse)
}
UploadReaperConfigurationResponse::UploadReaperConfigurationResponse(const UploadReaperConfigurationResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.reaper.UploadReaperConfigurationResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UploadReaperConfigurationResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UploadReaperConfigurationResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata UploadReaperConfigurationResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2freaper_2eproto_getter, &descriptor_table_portal_2fproto_2freaper_2eproto_once,
      file_level_metadata_portal_2fproto_2freaper_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace reaper
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::reaper::ReaperConfiguration* Arena::CreateMaybeMessage< ::carbon::portal::reaper::ReaperConfiguration >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::reaper::ReaperConfiguration >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::reaper::UploadReaperConfigurationRequest* Arena::CreateMaybeMessage< ::carbon::portal::reaper::UploadReaperConfigurationRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::reaper::UploadReaperConfigurationRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::reaper::UploadReaperConfigurationResponse* Arena::CreateMaybeMessage< ::carbon::portal::reaper::UploadReaperConfigurationResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::reaper::UploadReaperConfigurationResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

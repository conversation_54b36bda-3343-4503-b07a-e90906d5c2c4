"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.jobs_pb2 import (
    Job as frontend___proto___jobs_pb2___Job,
)

from generated.frontend.proto.weeding_diagnostics_pb2 import (
    ConfigNode<PERSON>napshot as frontend___proto___weeding_diagnostics_pb2___ConfigNodeSnapshot,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.metrics.proto.metrics_aggregator_service_pb2 import (
    Metrics as metrics___proto___metrics_aggregator_service_pb2___Metrics,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class UploadJobRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    robot: typing___Text = ...

    @property
    def job(self) -> frontend___proto___jobs_pb2___Job: ...

    def __init__(self,
        *,
        job : typing___Optional[frontend___proto___jobs_pb2___Job] = None,
        robot : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"job",b"job"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"job",b"job",u"robot",b"robot"]) -> None: ...
type___UploadJobRequest = UploadJobRequest

class UploadJobConfigDumpRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    @property
    def rootConfig(self) -> frontend___proto___weeding_diagnostics_pb2___ConfigNodeSnapshot: ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        rootConfig : typing___Optional[frontend___proto___weeding_diagnostics_pb2___ConfigNodeSnapshot] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"rootConfig",b"rootConfig"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId",u"rootConfig",b"rootConfig"]) -> None: ...
type___UploadJobConfigDumpRequest = UploadJobConfigDumpRequest

class UploadJobMetricsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    @property
    def jobMetrics(self) -> metrics___proto___metrics_aggregator_service_pb2___Metrics: ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        jobMetrics : typing___Optional[metrics___proto___metrics_aggregator_service_pb2___Metrics] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"jobMetrics",b"jobMetrics"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId",u"jobMetrics",b"jobMetrics"]) -> None: ...
type___UploadJobMetricsRequest = UploadJobMetricsRequest

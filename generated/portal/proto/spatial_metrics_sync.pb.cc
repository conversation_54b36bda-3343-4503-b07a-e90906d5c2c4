// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/spatial_metrics_sync.proto

#include "portal/proto/spatial_metrics_sync.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace spatial_metrics {
constexpr SyncSpatialMetricBlocksRequest::SyncSpatialMetricBlocksRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : blocks_()
  , robot_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SyncSpatialMetricBlocksRequestDefaultTypeInternal {
  constexpr SyncSpatialMetricBlocksRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SyncSpatialMetricBlocksRequestDefaultTypeInternal() {}
  union {
    SyncSpatialMetricBlocksRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SyncSpatialMetricBlocksRequestDefaultTypeInternal _SyncSpatialMetricBlocksRequest_default_instance_;
}  // namespace spatial_metrics
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest, blocks_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest, robot_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::spatial_metrics::_SyncSpatialMetricBlocksRequest_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\'portal/proto/spatial_metrics_sync.prot"
  "o\022\035carbon.portal.spatial_metrics\032\033proto/"
  "metrics/metrics.proto\032\027portal/proto/util"
  ".proto\"c\n\036SyncSpatialMetricBlocksRequest"
  "\0222\n\006blocks\030\001 \003(\0132\".carbon.metrics.Spatia"
  "lMetricBlock\022\r\n\005robot\030\002 \001(\t2\220\001\n\031SpatialM"
  "etricsSyncService\022s\n\027SyncSpatialMetricBl"
  "ocks\022=.carbon.portal.spatial_metrics.Syn"
  "cSpatialMetricBlocksRequest\032\031.carbon.por"
  "tal.util.EmptyB\016Z\014proto/portalb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto_deps[2] = {
  &::descriptor_table_portal_2fproto_2futil_2eproto,
  &::descriptor_table_proto_2fmetrics_2fmetrics_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto = {
  false, false, 398, descriptor_table_protodef_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto, "portal/proto/spatial_metrics_sync.proto", 
  &descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto_once, descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto::offsets,
  file_level_metadata_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto, file_level_enum_descriptors_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto, file_level_service_descriptors_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto(&descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto);
namespace carbon {
namespace portal {
namespace spatial_metrics {

// ===================================================================

class SyncSpatialMetricBlocksRequest::_Internal {
 public:
};

void SyncSpatialMetricBlocksRequest::clear_blocks() {
  blocks_.Clear();
}
SyncSpatialMetricBlocksRequest::SyncSpatialMetricBlocksRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  blocks_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
}
SyncSpatialMetricBlocksRequest::SyncSpatialMetricBlocksRequest(const SyncSpatialMetricBlocksRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      blocks_(from.blocks_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot().empty()) {
    robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
}

inline void SyncSpatialMetricBlocksRequest::SharedCtor() {
robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SyncSpatialMetricBlocksRequest::~SyncSpatialMetricBlocksRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SyncSpatialMetricBlocksRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SyncSpatialMetricBlocksRequest::ArenaDtor(void* object) {
  SyncSpatialMetricBlocksRequest* _this = reinterpret_cast< SyncSpatialMetricBlocksRequest* >(object);
  (void)_this;
}
void SyncSpatialMetricBlocksRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SyncSpatialMetricBlocksRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SyncSpatialMetricBlocksRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  blocks_.Clear();
  robot_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SyncSpatialMetricBlocksRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.metrics.SpatialMetricBlock blocks = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_blocks(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string robot = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_robot();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.robot"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SyncSpatialMetricBlocksRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.metrics.SpatialMetricBlock blocks = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_blocks_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_blocks(i), target, stream);
  }

  // string robot = 2;
  if (!this->_internal_robot().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot().data(), static_cast<int>(this->_internal_robot().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.robot");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_robot(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
  return target;
}

size_t SyncSpatialMetricBlocksRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.metrics.SpatialMetricBlock blocks = 1;
  total_size += 1UL * this->_internal_blocks_size();
  for (const auto& msg : this->blocks_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string robot = 2;
  if (!this->_internal_robot().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SyncSpatialMetricBlocksRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SyncSpatialMetricBlocksRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SyncSpatialMetricBlocksRequest::GetClassData() const { return &_class_data_; }

void SyncSpatialMetricBlocksRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SyncSpatialMetricBlocksRequest *>(to)->MergeFrom(
      static_cast<const SyncSpatialMetricBlocksRequest &>(from));
}


void SyncSpatialMetricBlocksRequest::MergeFrom(const SyncSpatialMetricBlocksRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  blocks_.MergeFrom(from.blocks_);
  if (!from._internal_robot().empty()) {
    _internal_set_robot(from._internal_robot());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SyncSpatialMetricBlocksRequest::CopyFrom(const SyncSpatialMetricBlocksRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SyncSpatialMetricBlocksRequest::IsInitialized() const {
  return true;
}

void SyncSpatialMetricBlocksRequest::InternalSwap(SyncSpatialMetricBlocksRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  blocks_.InternalSwap(&other->blocks_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_, lhs_arena,
      &other->robot_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SyncSpatialMetricBlocksRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto_getter, &descriptor_table_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto_once,
      file_level_metadata_portal_2fproto_2fspatial_5fmetrics_5fsync_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace spatial_metrics
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest* Arena::CreateMaybeMessage< ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::spatial_metrics::SyncSpatialMetricBlocksRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

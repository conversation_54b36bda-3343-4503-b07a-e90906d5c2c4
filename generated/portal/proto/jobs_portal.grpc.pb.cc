// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/jobs_portal.proto

#include "portal/proto/jobs_portal.pb.h"
#include "portal/proto/jobs_portal.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace jobs {

static const char* PortalJobsService_method_names[] = {
  "/carbon.portal.jobs.PortalJobsService/UploadJob",
  "/carbon.portal.jobs.PortalJobsService/UploadJobConfigDump",
  "/carbon.portal.jobs.PortalJobsService/UploadJobMetrics",
};

std::unique_ptr< PortalJobsService::Stub> PortalJobsService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< PortalJobsService::Stub> stub(new PortalJobsService::Stub(channel, options));
  return stub;
}

PortalJobsService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_UploadJob_(PortalJobsService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UploadJobConfigDump_(PortalJobsService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UploadJobMetrics_(PortalJobsService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status PortalJobsService::Stub::UploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::jobs::UploadJobRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UploadJob_, context, request, response);
}

void PortalJobsService::Stub::async::UploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::jobs::UploadJobRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadJob_, context, request, response, std::move(f));
}

void PortalJobsService::Stub::async::UploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadJob_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalJobsService::Stub::PrepareAsyncUploadJobRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::jobs::UploadJobRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UploadJob_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalJobsService::Stub::AsyncUploadJobRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUploadJobRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PortalJobsService::Stub::UploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::jobs::UploadJobConfigDumpRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UploadJobConfigDump_, context, request, response);
}

void PortalJobsService::Stub::async::UploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::jobs::UploadJobConfigDumpRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadJobConfigDump_, context, request, response, std::move(f));
}

void PortalJobsService::Stub::async::UploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadJobConfigDump_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalJobsService::Stub::PrepareAsyncUploadJobConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::jobs::UploadJobConfigDumpRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UploadJobConfigDump_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalJobsService::Stub::AsyncUploadJobConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUploadJobConfigDumpRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PortalJobsService::Stub::UploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::jobs::UploadJobMetricsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UploadJobMetrics_, context, request, response);
}

void PortalJobsService::Stub::async::UploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::jobs::UploadJobMetricsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadJobMetrics_, context, request, response, std::move(f));
}

void PortalJobsService::Stub::async::UploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadJobMetrics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalJobsService::Stub::PrepareAsyncUploadJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::jobs::UploadJobMetricsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UploadJobMetrics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalJobsService::Stub::AsyncUploadJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUploadJobMetricsRaw(context, request, cq);
  result->StartCall();
  return result;
}

PortalJobsService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalJobsService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalJobsService::Service, ::carbon::portal::jobs::UploadJobRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalJobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::jobs::UploadJobRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->UploadJob(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalJobsService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalJobsService::Service, ::carbon::portal::jobs::UploadJobConfigDumpRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalJobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::jobs::UploadJobConfigDumpRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->UploadJobConfigDump(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalJobsService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalJobsService::Service, ::carbon::portal::jobs::UploadJobMetricsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalJobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::jobs::UploadJobMetricsRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->UploadJobMetrics(ctx, req, resp);
             }, this)));
}

PortalJobsService::Service::~Service() {
}

::grpc::Status PortalJobsService::Service::UploadJob(::grpc::ServerContext* context, const ::carbon::portal::jobs::UploadJobRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PortalJobsService::Service::UploadJobConfigDump(::grpc::ServerContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PortalJobsService::Service::UploadJobMetrics(::grpc::ServerContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace jobs


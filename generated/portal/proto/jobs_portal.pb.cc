// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/jobs_portal.proto

#include "portal/proto/jobs_portal.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace jobs {
constexpr UploadJobRequest::UploadJobRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , job_(nullptr){}
struct UploadJobRequestDefaultTypeInternal {
  constexpr UploadJobRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UploadJobRequestDefaultTypeInternal() {}
  union {
    UploadJobRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UploadJobRequestDefaultTypeInternal _UploadJobRequest_default_instance_;
constexpr UploadJobConfigDumpRequest::UploadJobConfigDumpRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , rootconfig_(nullptr){}
struct UploadJobConfigDumpRequestDefaultTypeInternal {
  constexpr UploadJobConfigDumpRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UploadJobConfigDumpRequestDefaultTypeInternal() {}
  union {
    UploadJobConfigDumpRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UploadJobConfigDumpRequestDefaultTypeInternal _UploadJobConfigDumpRequest_default_instance_;
constexpr UploadJobMetricsRequest::UploadJobMetricsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , jobmetrics_(nullptr){}
struct UploadJobMetricsRequestDefaultTypeInternal {
  constexpr UploadJobMetricsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UploadJobMetricsRequestDefaultTypeInternal() {}
  union {
    UploadJobMetricsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UploadJobMetricsRequestDefaultTypeInternal _UploadJobMetricsRequest_default_instance_;
}  // namespace jobs
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2fjobs_5fportal_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2fjobs_5fportal_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2fjobs_5fportal_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2fjobs_5fportal_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::jobs::UploadJobRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::jobs::UploadJobRequest, job_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::jobs::UploadJobRequest, robot_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::jobs::UploadJobConfigDumpRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::jobs::UploadJobConfigDumpRequest, jobid_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::jobs::UploadJobConfigDumpRequest, rootconfig_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::jobs::UploadJobMetricsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::jobs::UploadJobMetricsRequest, jobid_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::jobs::UploadJobMetricsRequest, jobmetrics_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::jobs::UploadJobRequest)},
  { 8, -1, -1, sizeof(::carbon::portal::jobs::UploadJobConfigDumpRequest)},
  { 16, -1, -1, sizeof(::carbon::portal::jobs::UploadJobMetricsRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::jobs::_UploadJobRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::jobs::_UploadJobConfigDumpRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::jobs::_UploadJobMetricsRequest_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2fjobs_5fportal_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\036portal/proto/jobs_portal.proto\022\022carbon"
  ".portal.jobs\032\027portal/proto/util.proto\032\031f"
  "rontend/proto/jobs.proto\032(frontend/proto"
  "/weeding_diagnostics.proto\032.metrics/prot"
  "o/metrics_aggregator_service.proto\"I\n\020Up"
  "loadJobRequest\022&\n\003job\030\001 \001(\0132\031.carbon.fro"
  "ntend.jobs.Job\022\r\n\005robot\030\002 \001(\t\"x\n\032UploadJ"
  "obConfigDumpRequest\022\r\n\005jobId\030\001 \001(\t\022K\n\nro"
  "otConfig\030\002 \001(\01327.carbon.frontend.weeding"
  "_diagnostics.ConfigNodeSnapshot\"Y\n\027Uploa"
  "dJobMetricsRequest\022\r\n\005jobId\030\001 \001(\t\022/\n\njob"
  "Metrics\030\002 \001(\0132\033.metrics_aggregator.Metri"
  "cs2\237\002\n\021PortalJobsService\022L\n\tUploadJob\022$."
  "carbon.portal.jobs.UploadJobRequest\032\031.ca"
  "rbon.portal.util.Empty\022`\n\023UploadJobConfi"
  "gDump\022..carbon.portal.jobs.UploadJobConf"
  "igDumpRequest\032\031.carbon.portal.util.Empty"
  "\022Z\n\020UploadJobMetrics\022+.carbon.portal.job"
  "s.UploadJobMetricsRequest\032\031.carbon.porta"
  "l.util.EmptyB\016Z\014proto/portalb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_deps[4] = {
  &::descriptor_table_frontend_2fproto_2fjobs_2eproto,
  &::descriptor_table_frontend_2fproto_2fweeding_5fdiagnostics_2eproto,
  &::descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto,
  &::descriptor_table_portal_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto = {
  false, false, 796, descriptor_table_protodef_portal_2fproto_2fjobs_5fportal_2eproto, "portal/proto/jobs_portal.proto", 
  &descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_once, descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_deps, 4, 3,
  schemas, file_default_instances, TableStruct_portal_2fproto_2fjobs_5fportal_2eproto::offsets,
  file_level_metadata_portal_2fproto_2fjobs_5fportal_2eproto, file_level_enum_descriptors_portal_2fproto_2fjobs_5fportal_2eproto, file_level_service_descriptors_portal_2fproto_2fjobs_5fportal_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2fjobs_5fportal_2eproto(&descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto);
namespace carbon {
namespace portal {
namespace jobs {

// ===================================================================

class UploadJobRequest::_Internal {
 public:
  static const ::carbon::frontend::jobs::Job& job(const UploadJobRequest* msg);
};

const ::carbon::frontend::jobs::Job&
UploadJobRequest::_Internal::job(const UploadJobRequest* msg) {
  return *msg->job_;
}
void UploadJobRequest::clear_job() {
  if (GetArenaForAllocation() == nullptr && job_ != nullptr) {
    delete job_;
  }
  job_ = nullptr;
}
UploadJobRequest::UploadJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.jobs.UploadJobRequest)
}
UploadJobRequest::UploadJobRequest(const UploadJobRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot().empty()) {
    robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_job()) {
    job_ = new ::carbon::frontend::jobs::Job(*from.job_);
  } else {
    job_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.jobs.UploadJobRequest)
}

inline void UploadJobRequest::SharedCtor() {
robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
job_ = nullptr;
}

UploadJobRequest::~UploadJobRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.jobs.UploadJobRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UploadJobRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete job_;
}

void UploadJobRequest::ArenaDtor(void* object) {
  UploadJobRequest* _this = reinterpret_cast< UploadJobRequest* >(object);
  (void)_this;
}
void UploadJobRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UploadJobRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UploadJobRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.jobs.UploadJobRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && job_ != nullptr) {
    delete job_;
  }
  job_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UploadJobRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.jobs.Job job = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_job(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string robot = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_robot();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.jobs.UploadJobRequest.robot"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UploadJobRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.jobs.UploadJobRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.jobs.Job job = 1;
  if (this->_internal_has_job()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::job(this), target, stream);
  }

  // string robot = 2;
  if (!this->_internal_robot().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot().data(), static_cast<int>(this->_internal_robot().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.jobs.UploadJobRequest.robot");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_robot(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.jobs.UploadJobRequest)
  return target;
}

size_t UploadJobRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.jobs.UploadJobRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot = 2;
  if (!this->_internal_robot().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot());
  }

  // .carbon.frontend.jobs.Job job = 1;
  if (this->_internal_has_job()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *job_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UploadJobRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UploadJobRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UploadJobRequest::GetClassData() const { return &_class_data_; }

void UploadJobRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UploadJobRequest *>(to)->MergeFrom(
      static_cast<const UploadJobRequest &>(from));
}


void UploadJobRequest::MergeFrom(const UploadJobRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.jobs.UploadJobRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot().empty()) {
    _internal_set_robot(from._internal_robot());
  }
  if (from._internal_has_job()) {
    _internal_mutable_job()->::carbon::frontend::jobs::Job::MergeFrom(from._internal_job());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UploadJobRequest::CopyFrom(const UploadJobRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.jobs.UploadJobRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UploadJobRequest::IsInitialized() const {
  return true;
}

void UploadJobRequest::InternalSwap(UploadJobRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_, lhs_arena,
      &other->robot_, rhs_arena
  );
  swap(job_, other->job_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UploadJobRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fjobs_5fportal_2eproto[0]);
}

// ===================================================================

class UploadJobConfigDumpRequest::_Internal {
 public:
  static const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& rootconfig(const UploadJobConfigDumpRequest* msg);
};

const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot&
UploadJobConfigDumpRequest::_Internal::rootconfig(const UploadJobConfigDumpRequest* msg) {
  return *msg->rootconfig_;
}
void UploadJobConfigDumpRequest::clear_rootconfig() {
  if (GetArenaForAllocation() == nullptr && rootconfig_ != nullptr) {
    delete rootconfig_;
  }
  rootconfig_ = nullptr;
}
UploadJobConfigDumpRequest::UploadJobConfigDumpRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.jobs.UploadJobConfigDumpRequest)
}
UploadJobConfigDumpRequest::UploadJobConfigDumpRequest(const UploadJobConfigDumpRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_rootconfig()) {
    rootconfig_ = new ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot(*from.rootconfig_);
  } else {
    rootconfig_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.jobs.UploadJobConfigDumpRequest)
}

inline void UploadJobConfigDumpRequest::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
rootconfig_ = nullptr;
}

UploadJobConfigDumpRequest::~UploadJobConfigDumpRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.jobs.UploadJobConfigDumpRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UploadJobConfigDumpRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete rootconfig_;
}

void UploadJobConfigDumpRequest::ArenaDtor(void* object) {
  UploadJobConfigDumpRequest* _this = reinterpret_cast< UploadJobConfigDumpRequest* >(object);
  (void)_this;
}
void UploadJobConfigDumpRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UploadJobConfigDumpRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UploadJobConfigDumpRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.jobs.UploadJobConfigDumpRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && rootconfig_ != nullptr) {
    delete rootconfig_;
  }
  rootconfig_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UploadJobConfigDumpRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.jobs.UploadJobConfigDumpRequest.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_rootconfig(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UploadJobConfigDumpRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.jobs.UploadJobConfigDumpRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.jobs.UploadJobConfigDumpRequest.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  // .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 2;
  if (this->_internal_has_rootconfig()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::rootconfig(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.jobs.UploadJobConfigDumpRequest)
  return target;
}

size_t UploadJobConfigDumpRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.jobs.UploadJobConfigDumpRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  // .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 2;
  if (this->_internal_has_rootconfig()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *rootconfig_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UploadJobConfigDumpRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UploadJobConfigDumpRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UploadJobConfigDumpRequest::GetClassData() const { return &_class_data_; }

void UploadJobConfigDumpRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UploadJobConfigDumpRequest *>(to)->MergeFrom(
      static_cast<const UploadJobConfigDumpRequest &>(from));
}


void UploadJobConfigDumpRequest::MergeFrom(const UploadJobConfigDumpRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.jobs.UploadJobConfigDumpRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  if (from._internal_has_rootconfig()) {
    _internal_mutable_rootconfig()->::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot::MergeFrom(from._internal_rootconfig());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UploadJobConfigDumpRequest::CopyFrom(const UploadJobConfigDumpRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.jobs.UploadJobConfigDumpRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UploadJobConfigDumpRequest::IsInitialized() const {
  return true;
}

void UploadJobConfigDumpRequest::InternalSwap(UploadJobConfigDumpRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
  swap(rootconfig_, other->rootconfig_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UploadJobConfigDumpRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fjobs_5fportal_2eproto[1]);
}

// ===================================================================

class UploadJobMetricsRequest::_Internal {
 public:
  static const ::metrics_aggregator::Metrics& jobmetrics(const UploadJobMetricsRequest* msg);
};

const ::metrics_aggregator::Metrics&
UploadJobMetricsRequest::_Internal::jobmetrics(const UploadJobMetricsRequest* msg) {
  return *msg->jobmetrics_;
}
void UploadJobMetricsRequest::clear_jobmetrics() {
  if (GetArenaForAllocation() == nullptr && jobmetrics_ != nullptr) {
    delete jobmetrics_;
  }
  jobmetrics_ = nullptr;
}
UploadJobMetricsRequest::UploadJobMetricsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.jobs.UploadJobMetricsRequest)
}
UploadJobMetricsRequest::UploadJobMetricsRequest(const UploadJobMetricsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_jobmetrics()) {
    jobmetrics_ = new ::metrics_aggregator::Metrics(*from.jobmetrics_);
  } else {
    jobmetrics_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.jobs.UploadJobMetricsRequest)
}

inline void UploadJobMetricsRequest::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
jobmetrics_ = nullptr;
}

UploadJobMetricsRequest::~UploadJobMetricsRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.jobs.UploadJobMetricsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UploadJobMetricsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete jobmetrics_;
}

void UploadJobMetricsRequest::ArenaDtor(void* object) {
  UploadJobMetricsRequest* _this = reinterpret_cast< UploadJobMetricsRequest* >(object);
  (void)_this;
}
void UploadJobMetricsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UploadJobMetricsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UploadJobMetricsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.jobs.UploadJobMetricsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && jobmetrics_ != nullptr) {
    delete jobmetrics_;
  }
  jobmetrics_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UploadJobMetricsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.jobs.UploadJobMetricsRequest.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .metrics_aggregator.Metrics jobMetrics = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_jobmetrics(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UploadJobMetricsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.jobs.UploadJobMetricsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.jobs.UploadJobMetricsRequest.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  // .metrics_aggregator.Metrics jobMetrics = 2;
  if (this->_internal_has_jobmetrics()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::jobmetrics(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.jobs.UploadJobMetricsRequest)
  return target;
}

size_t UploadJobMetricsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.jobs.UploadJobMetricsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  // .metrics_aggregator.Metrics jobMetrics = 2;
  if (this->_internal_has_jobmetrics()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *jobmetrics_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UploadJobMetricsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UploadJobMetricsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UploadJobMetricsRequest::GetClassData() const { return &_class_data_; }

void UploadJobMetricsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UploadJobMetricsRequest *>(to)->MergeFrom(
      static_cast<const UploadJobMetricsRequest &>(from));
}


void UploadJobMetricsRequest::MergeFrom(const UploadJobMetricsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.jobs.UploadJobMetricsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  if (from._internal_has_jobmetrics()) {
    _internal_mutable_jobmetrics()->::metrics_aggregator::Metrics::MergeFrom(from._internal_jobmetrics());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UploadJobMetricsRequest::CopyFrom(const UploadJobMetricsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.jobs.UploadJobMetricsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UploadJobMetricsRequest::IsInitialized() const {
  return true;
}

void UploadJobMetricsRequest::InternalSwap(UploadJobMetricsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
  swap(jobmetrics_, other->jobmetrics_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UploadJobMetricsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fjobs_5fportal_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace jobs
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::jobs::UploadJobRequest* Arena::CreateMaybeMessage< ::carbon::portal::jobs::UploadJobRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::jobs::UploadJobRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::jobs::UploadJobConfigDumpRequest* Arena::CreateMaybeMessage< ::carbon::portal::jobs::UploadJobConfigDumpRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::jobs::UploadJobConfigDumpRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::jobs::UploadJobMetricsRequest* Arena::CreateMaybeMessage< ::carbon::portal::jobs::UploadJobMetricsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::jobs::UploadJobMetricsRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

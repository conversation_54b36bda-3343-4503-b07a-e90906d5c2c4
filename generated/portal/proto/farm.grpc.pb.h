// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/farm.proto
#ifndef GRPC_portal_2fproto_2ffarm_2eproto__INCLUDED
#define GRPC_portal_2fproto_2ffarm_2eproto__INCLUDED

#include "portal/proto/farm.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace portal {
namespace farm {

class FarmsService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.portal.farm.FarmsService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Gets a farm by ID. If `if_modified_since` is present and none of the
    // farm's descendant entities has been modified at a strictly later time than
    // that, then the RPC will succeed with code OK and no `farm` on the
    // response.
    virtual ::grpc::Status GetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::carbon::portal::farm::GetFarmResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::farm::GetFarmResponse>> AsyncGetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::farm::GetFarmResponse>>(AsyncGetFarmRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::farm::GetFarmResponse>> PrepareAsyncGetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::farm::GetFarmResponse>>(PrepareAsyncGetFarmRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      // Gets a farm by ID. If `if_modified_since` is present and none of the
      // farm's descendant entities has been modified at a strictly later time than
      // that, then the RPC will succeed with code OK and no `farm` on the
      // response.
      virtual void GetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest* request, ::carbon::portal::farm::GetFarmResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest* request, ::carbon::portal::farm::GetFarmResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::farm::GetFarmResponse>* AsyncGetFarmRaw(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::farm::GetFarmResponse>* PrepareAsyncGetFarmRaw(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::carbon::portal::farm::GetFarmResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::farm::GetFarmResponse>> AsyncGetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::farm::GetFarmResponse>>(AsyncGetFarmRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::farm::GetFarmResponse>> PrepareAsyncGetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::farm::GetFarmResponse>>(PrepareAsyncGetFarmRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest* request, ::carbon::portal::farm::GetFarmResponse* response, std::function<void(::grpc::Status)>) override;
      void GetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest* request, ::carbon::portal::farm::GetFarmResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::farm::GetFarmResponse>* AsyncGetFarmRaw(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::farm::GetFarmResponse>* PrepareAsyncGetFarmRaw(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetFarm_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Gets a farm by ID. If `if_modified_since` is present and none of the
    // farm's descendant entities has been modified at a strictly later time than
    // that, then the RPC will succeed with code OK and no `farm` on the
    // response.
    virtual ::grpc::Status GetFarm(::grpc::ServerContext* context, const ::carbon::portal::farm::GetFarmRequest* request, ::carbon::portal::farm::GetFarmResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetFarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetFarm() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetFarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetFarm(::grpc::ServerContext* /*context*/, const ::carbon::portal::farm::GetFarmRequest* /*request*/, ::carbon::portal::farm::GetFarmResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetFarm(::grpc::ServerContext* context, ::carbon::portal::farm::GetFarmRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::farm::GetFarmResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetFarm<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetFarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetFarm() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::farm::GetFarmRequest, ::carbon::portal::farm::GetFarmResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::farm::GetFarmRequest* request, ::carbon::portal::farm::GetFarmResponse* response) { return this->GetFarm(context, request, response); }));}
    void SetMessageAllocatorFor_GetFarm(
        ::grpc::MessageAllocator< ::carbon::portal::farm::GetFarmRequest, ::carbon::portal::farm::GetFarmResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::farm::GetFarmRequest, ::carbon::portal::farm::GetFarmResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetFarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetFarm(::grpc::ServerContext* /*context*/, const ::carbon::portal::farm::GetFarmRequest* /*request*/, ::carbon::portal::farm::GetFarmResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetFarm(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::farm::GetFarmRequest* /*request*/, ::carbon::portal::farm::GetFarmResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetFarm<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetFarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetFarm() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetFarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetFarm(::grpc::ServerContext* /*context*/, const ::carbon::portal::farm::GetFarmRequest* /*request*/, ::carbon::portal::farm::GetFarmResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetFarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetFarm() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetFarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetFarm(::grpc::ServerContext* /*context*/, const ::carbon::portal::farm::GetFarmRequest* /*request*/, ::carbon::portal::farm::GetFarmResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetFarm(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetFarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetFarm() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetFarm(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetFarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetFarm(::grpc::ServerContext* /*context*/, const ::carbon::portal::farm::GetFarmRequest* /*request*/, ::carbon::portal::farm::GetFarmResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetFarm(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetFarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetFarm() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::farm::GetFarmRequest, ::carbon::portal::farm::GetFarmResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::farm::GetFarmRequest, ::carbon::portal::farm::GetFarmResponse>* streamer) {
                       return this->StreamedGetFarm(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetFarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetFarm(::grpc::ServerContext* /*context*/, const ::carbon::portal::farm::GetFarmRequest* /*request*/, ::carbon::portal::farm::GetFarmResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetFarm(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::farm::GetFarmRequest,::carbon::portal::farm::GetFarmResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetFarm<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetFarm<Service > StreamedService;
};

}  // namespace farm
}  // namespace portal
}  // namespace carbon


#endif  // GRPC_portal_2fproto_2ffarm_2eproto__INCLUDED

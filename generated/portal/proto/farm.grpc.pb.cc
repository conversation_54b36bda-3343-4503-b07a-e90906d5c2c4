// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/farm.proto

#include "portal/proto/farm.pb.h"
#include "portal/proto/farm.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace farm {

static const char* FarmsService_method_names[] = {
  "/carbon.portal.farm.FarmsService/GetFarm",
};

std::unique_ptr< FarmsService::Stub> FarmsService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< FarmsService::Stub> stub(new FarmsService::Stub(channel, options));
  return stub;
}

FarmsService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetFarm_(FarmsService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status FarmsService::Stub::GetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::carbon::portal::farm::GetFarmResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::farm::GetFarmRequest, ::carbon::portal::farm::GetFarmResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetFarm_, context, request, response);
}

void FarmsService::Stub::async::GetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest* request, ::carbon::portal::farm::GetFarmResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::farm::GetFarmRequest, ::carbon::portal::farm::GetFarmResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetFarm_, context, request, response, std::move(f));
}

void FarmsService::Stub::async::GetFarm(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest* request, ::carbon::portal::farm::GetFarmResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetFarm_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::farm::GetFarmResponse>* FarmsService::Stub::PrepareAsyncGetFarmRaw(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::farm::GetFarmResponse, ::carbon::portal::farm::GetFarmRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetFarm_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::farm::GetFarmResponse>* FarmsService::Stub::AsyncGetFarmRaw(::grpc::ClientContext* context, const ::carbon::portal::farm::GetFarmRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetFarmRaw(context, request, cq);
  result->StartCall();
  return result;
}

FarmsService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      FarmsService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< FarmsService::Service, ::carbon::portal::farm::GetFarmRequest, ::carbon::portal::farm::GetFarmResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](FarmsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::farm::GetFarmRequest* req,
             ::carbon::portal::farm::GetFarmResponse* resp) {
               return service->GetFarm(ctx, req, resp);
             }, this)));
}

FarmsService::Service::~Service() {
}

::grpc::Status FarmsService::Service::GetFarm(::grpc::ServerContext* context, const ::carbon::portal::farm::GetFarmRequest* request, ::carbon::portal::farm::GetFarmResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace farm


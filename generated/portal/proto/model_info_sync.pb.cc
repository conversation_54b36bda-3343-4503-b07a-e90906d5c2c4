// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/model_info_sync.proto

#include "portal/proto/model_info_sync.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace model_info {
constexpr ModelInfo::ModelInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_ids_()
  , model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , nickname_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ModelInfoDefaultTypeInternal {
  constexpr ModelInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelInfoDefaultTypeInternal() {}
  union {
    ModelInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelInfoDefaultTypeInternal _ModelInfo_default_instance_;
constexpr UploadModelInfosRequest::UploadModelInfosRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : model_infos_()
  , robot_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct UploadModelInfosRequestDefaultTypeInternal {
  constexpr UploadModelInfosRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UploadModelInfosRequestDefaultTypeInternal() {}
  union {
    UploadModelInfosRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UploadModelInfosRequestDefaultTypeInternal _UploadModelInfosRequest_default_instance_;
constexpr RenameModelCommand::RenameModelCommand(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , new_nickname_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct RenameModelCommandDefaultTypeInternal {
  constexpr RenameModelCommandDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RenameModelCommandDefaultTypeInternal() {}
  union {
    RenameModelCommand _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RenameModelCommandDefaultTypeInternal _RenameModelCommand_default_instance_;
constexpr GetRenameModelCommandsRequest::GetRenameModelCommandsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetRenameModelCommandsRequestDefaultTypeInternal {
  constexpr GetRenameModelCommandsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetRenameModelCommandsRequestDefaultTypeInternal() {}
  union {
    GetRenameModelCommandsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetRenameModelCommandsRequestDefaultTypeInternal _GetRenameModelCommandsRequest_default_instance_;
constexpr GetRenameModelCommandsResponse::GetRenameModelCommandsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : commands_(){}
struct GetRenameModelCommandsResponseDefaultTypeInternal {
  constexpr GetRenameModelCommandsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetRenameModelCommandsResponseDefaultTypeInternal() {}
  union {
    GetRenameModelCommandsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetRenameModelCommandsResponseDefaultTypeInternal _GetRenameModelCommandsResponse_default_instance_;
constexpr PurgeRenameModelCommandsRequest::PurgeRenameModelCommandsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : commands_()
  , robot_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct PurgeRenameModelCommandsRequestDefaultTypeInternal {
  constexpr PurgeRenameModelCommandsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PurgeRenameModelCommandsRequestDefaultTypeInternal() {}
  union {
    PurgeRenameModelCommandsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PurgeRenameModelCommandsRequestDefaultTypeInternal _PurgeRenameModelCommandsRequest_default_instance_;
}  // namespace model_info
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2fmodel_5finfo_5fsync_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2fmodel_5finfo_5fsync_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2fmodel_5finfo_5fsync_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2fmodel_5finfo_5fsync_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::ModelInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::ModelInfo, model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::ModelInfo, crop_ids_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::ModelInfo, nickname_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::UploadModelInfosRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::UploadModelInfosRequest, robot_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::UploadModelInfosRequest, model_infos_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::RenameModelCommand, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::RenameModelCommand, model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::RenameModelCommand, new_nickname_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::GetRenameModelCommandsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::GetRenameModelCommandsRequest, robot_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::GetRenameModelCommandsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::GetRenameModelCommandsResponse, commands_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::PurgeRenameModelCommandsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::PurgeRenameModelCommandsRequest, robot_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_info::PurgeRenameModelCommandsRequest, commands_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::model_info::ModelInfo)},
  { 9, -1, -1, sizeof(::carbon::portal::model_info::UploadModelInfosRequest)},
  { 17, -1, -1, sizeof(::carbon::portal::model_info::RenameModelCommand)},
  { 25, -1, -1, sizeof(::carbon::portal::model_info::GetRenameModelCommandsRequest)},
  { 32, -1, -1, sizeof(::carbon::portal::model_info::GetRenameModelCommandsResponse)},
  { 39, -1, -1, sizeof(::carbon::portal::model_info::PurgeRenameModelCommandsRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::model_info::_ModelInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::model_info::_UploadModelInfosRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::model_info::_RenameModelCommand_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::model_info::_GetRenameModelCommandsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::model_info::_GetRenameModelCommandsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::model_info::_PurgeRenameModelCommandsRequest_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2fmodel_5finfo_5fsync_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\"portal/proto/model_info_sync.proto\022\030ca"
  "rbon.portal.model_info\032\027portal/proto/uti"
  "l.proto\"A\n\tModelInfo\022\020\n\010model_id\030\001 \001(\t\022\020"
  "\n\010crop_ids\030\002 \003(\t\022\020\n\010nickname\030\003 \001(\t\"b\n\027Up"
  "loadModelInfosRequest\022\r\n\005robot\030\001 \001(\t\0228\n\013"
  "model_infos\030\002 \003(\0132#.carbon.portal.model_"
  "info.ModelInfo\"<\n\022RenameModelCommand\022\020\n\010"
  "model_id\030\001 \001(\t\022\024\n\014new_nickname\030\002 \001(\t\".\n\035"
  "GetRenameModelCommandsRequest\022\r\n\005robot\030\001"
  " \001(\t\"`\n\036GetRenameModelCommandsResponse\022>"
  "\n\010commands\030\001 \003(\0132,.carbon.portal.model_i"
  "nfo.RenameModelCommand\"p\n\037PurgeRenameMod"
  "elCommandsRequest\022\r\n\005robot\030\001 \001(\t\022>\n\010comm"
  "ands\030\002 \003(\0132,.carbon.portal.model_info.Re"
  "nameModelCommand2\370\002\n\024ModelInfoSyncServic"
  "e\022`\n\020UploadModelInfos\0221.carbon.portal.mo"
  "del_info.UploadModelInfosRequest\032\031.carbo"
  "n.portal.util.Empty\022\213\001\n\026GetRenameModelCo"
  "mmands\0227.carbon.portal.model_info.GetRen"
  "ameModelCommandsRequest\0328.carbon.portal."
  "model_info.GetRenameModelCommandsRespons"
  "e\022p\n\030PurgeRenameModelCommands\0229.carbon.p"
  "ortal.model_info.PurgeRenameModelCommand"
  "sRequest\032\031.carbon.portal.util.EmptyB\016Z\014p"
  "roto/portalb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_deps[1] = {
  &::descriptor_table_portal_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto = {
  false, false, 979, descriptor_table_protodef_portal_2fproto_2fmodel_5finfo_5fsync_2eproto, "portal/proto/model_info_sync.proto", 
  &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_once, descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_deps, 1, 6,
  schemas, file_default_instances, TableStruct_portal_2fproto_2fmodel_5finfo_5fsync_2eproto::offsets,
  file_level_metadata_portal_2fproto_2fmodel_5finfo_5fsync_2eproto, file_level_enum_descriptors_portal_2fproto_2fmodel_5finfo_5fsync_2eproto, file_level_service_descriptors_portal_2fproto_2fmodel_5finfo_5fsync_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2fmodel_5finfo_5fsync_2eproto(&descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto);
namespace carbon {
namespace portal {
namespace model_info {

// ===================================================================

class ModelInfo::_Internal {
 public:
};

ModelInfo::ModelInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  crop_ids_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.model_info.ModelInfo)
}
ModelInfo::ModelInfo(const ModelInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      crop_ids_(from.crop_ids_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_nickname().empty()) {
    nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_nickname(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.model_info.ModelInfo)
}

inline void ModelInfo::SharedCtor() {
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ModelInfo::~ModelInfo() {
  // @@protoc_insertion_point(destructor:carbon.portal.model_info.ModelInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  nickname_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ModelInfo::ArenaDtor(void* object) {
  ModelInfo* _this = reinterpret_cast< ModelInfo* >(object);
  (void)_this;
}
void ModelInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.model_info.ModelInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_ids_.Clear();
  model_id_.ClearToEmpty();
  nickname_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_info.ModelInfo.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string crop_ids = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_crop_ids();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_info.ModelInfo.crop_ids"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string nickname = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_nickname();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_info.ModelInfo.nickname"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.model_info.ModelInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_info.ModelInfo.model_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_model_id(), target);
  }

  // repeated string crop_ids = 2;
  for (int i = 0, n = this->_internal_crop_ids_size(); i < n; i++) {
    const auto& s = this->_internal_crop_ids(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_info.ModelInfo.crop_ids");
    target = stream->WriteString(2, s, target);
  }

  // string nickname = 3;
  if (!this->_internal_nickname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_nickname().data(), static_cast<int>(this->_internal_nickname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_info.ModelInfo.nickname");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_nickname(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.model_info.ModelInfo)
  return target;
}

size_t ModelInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.model_info.ModelInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string crop_ids = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(crop_ids_.size());
  for (int i = 0, n = crop_ids_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      crop_ids_.Get(i));
  }

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  // string nickname = 3;
  if (!this->_internal_nickname().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_nickname());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelInfo::GetClassData() const { return &_class_data_; }

void ModelInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelInfo *>(to)->MergeFrom(
      static_cast<const ModelInfo &>(from));
}


void ModelInfo::MergeFrom(const ModelInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.model_info.ModelInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  crop_ids_.MergeFrom(from.crop_ids_);
  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  if (!from._internal_nickname().empty()) {
    _internal_set_nickname(from._internal_nickname());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelInfo::CopyFrom(const ModelInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.model_info.ModelInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelInfo::IsInitialized() const {
  return true;
}

void ModelInfo::InternalSwap(ModelInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  crop_ids_.InternalSwap(&other->crop_ids_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &nickname_, lhs_arena,
      &other->nickname_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_getter, &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_once,
      file_level_metadata_portal_2fproto_2fmodel_5finfo_5fsync_2eproto[0]);
}

// ===================================================================

class UploadModelInfosRequest::_Internal {
 public:
};

UploadModelInfosRequest::UploadModelInfosRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  model_infos_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.model_info.UploadModelInfosRequest)
}
UploadModelInfosRequest::UploadModelInfosRequest(const UploadModelInfosRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      model_infos_(from.model_infos_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot().empty()) {
    robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.model_info.UploadModelInfosRequest)
}

inline void UploadModelInfosRequest::SharedCtor() {
robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

UploadModelInfosRequest::~UploadModelInfosRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.model_info.UploadModelInfosRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UploadModelInfosRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void UploadModelInfosRequest::ArenaDtor(void* object) {
  UploadModelInfosRequest* _this = reinterpret_cast< UploadModelInfosRequest* >(object);
  (void)_this;
}
void UploadModelInfosRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UploadModelInfosRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UploadModelInfosRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.model_info.UploadModelInfosRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_infos_.Clear();
  robot_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UploadModelInfosRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string robot = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_robot();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_info.UploadModelInfosRequest.robot"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.portal.model_info.ModelInfo model_infos = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_model_infos(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UploadModelInfosRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.model_info.UploadModelInfosRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot().data(), static_cast<int>(this->_internal_robot().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_info.UploadModelInfosRequest.robot");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_robot(), target);
  }

  // repeated .carbon.portal.model_info.ModelInfo model_infos = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_model_infos_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_model_infos(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.model_info.UploadModelInfosRequest)
  return target;
}

size_t UploadModelInfosRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.model_info.UploadModelInfosRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.model_info.ModelInfo model_infos = 2;
  total_size += 1UL * this->_internal_model_infos_size();
  for (const auto& msg : this->model_infos_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UploadModelInfosRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UploadModelInfosRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UploadModelInfosRequest::GetClassData() const { return &_class_data_; }

void UploadModelInfosRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UploadModelInfosRequest *>(to)->MergeFrom(
      static_cast<const UploadModelInfosRequest &>(from));
}


void UploadModelInfosRequest::MergeFrom(const UploadModelInfosRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.model_info.UploadModelInfosRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  model_infos_.MergeFrom(from.model_infos_);
  if (!from._internal_robot().empty()) {
    _internal_set_robot(from._internal_robot());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UploadModelInfosRequest::CopyFrom(const UploadModelInfosRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.model_info.UploadModelInfosRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UploadModelInfosRequest::IsInitialized() const {
  return true;
}

void UploadModelInfosRequest::InternalSwap(UploadModelInfosRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  model_infos_.InternalSwap(&other->model_infos_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_, lhs_arena,
      &other->robot_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata UploadModelInfosRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_getter, &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_once,
      file_level_metadata_portal_2fproto_2fmodel_5finfo_5fsync_2eproto[1]);
}

// ===================================================================

class RenameModelCommand::_Internal {
 public:
};

RenameModelCommand::RenameModelCommand(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.model_info.RenameModelCommand)
}
RenameModelCommand::RenameModelCommand(const RenameModelCommand& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  new_nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    new_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_new_nickname().empty()) {
    new_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_new_nickname(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.model_info.RenameModelCommand)
}

inline void RenameModelCommand::SharedCtor() {
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
new_nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  new_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RenameModelCommand::~RenameModelCommand() {
  // @@protoc_insertion_point(destructor:carbon.portal.model_info.RenameModelCommand)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RenameModelCommand::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  new_nickname_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RenameModelCommand::ArenaDtor(void* object) {
  RenameModelCommand* _this = reinterpret_cast< RenameModelCommand* >(object);
  (void)_this;
}
void RenameModelCommand::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RenameModelCommand::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RenameModelCommand::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.model_info.RenameModelCommand)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_id_.ClearToEmpty();
  new_nickname_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RenameModelCommand::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_info.RenameModelCommand.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string new_nickname = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_new_nickname();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_info.RenameModelCommand.new_nickname"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RenameModelCommand::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.model_info.RenameModelCommand)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_info.RenameModelCommand.model_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_model_id(), target);
  }

  // string new_nickname = 2;
  if (!this->_internal_new_nickname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_new_nickname().data(), static_cast<int>(this->_internal_new_nickname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_info.RenameModelCommand.new_nickname");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_new_nickname(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.model_info.RenameModelCommand)
  return target;
}

size_t RenameModelCommand::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.model_info.RenameModelCommand)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  // string new_nickname = 2;
  if (!this->_internal_new_nickname().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_new_nickname());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RenameModelCommand::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RenameModelCommand::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RenameModelCommand::GetClassData() const { return &_class_data_; }

void RenameModelCommand::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RenameModelCommand *>(to)->MergeFrom(
      static_cast<const RenameModelCommand &>(from));
}


void RenameModelCommand::MergeFrom(const RenameModelCommand& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.model_info.RenameModelCommand)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  if (!from._internal_new_nickname().empty()) {
    _internal_set_new_nickname(from._internal_new_nickname());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RenameModelCommand::CopyFrom(const RenameModelCommand& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.model_info.RenameModelCommand)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RenameModelCommand::IsInitialized() const {
  return true;
}

void RenameModelCommand::InternalSwap(RenameModelCommand* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &new_nickname_, lhs_arena,
      &other->new_nickname_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata RenameModelCommand::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_getter, &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_once,
      file_level_metadata_portal_2fproto_2fmodel_5finfo_5fsync_2eproto[2]);
}

// ===================================================================

class GetRenameModelCommandsRequest::_Internal {
 public:
};

GetRenameModelCommandsRequest::GetRenameModelCommandsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.model_info.GetRenameModelCommandsRequest)
}
GetRenameModelCommandsRequest::GetRenameModelCommandsRequest(const GetRenameModelCommandsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot().empty()) {
    robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.model_info.GetRenameModelCommandsRequest)
}

inline void GetRenameModelCommandsRequest::SharedCtor() {
robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetRenameModelCommandsRequest::~GetRenameModelCommandsRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.model_info.GetRenameModelCommandsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetRenameModelCommandsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetRenameModelCommandsRequest::ArenaDtor(void* object) {
  GetRenameModelCommandsRequest* _this = reinterpret_cast< GetRenameModelCommandsRequest* >(object);
  (void)_this;
}
void GetRenameModelCommandsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetRenameModelCommandsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetRenameModelCommandsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.model_info.GetRenameModelCommandsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetRenameModelCommandsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string robot = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_robot();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_info.GetRenameModelCommandsRequest.robot"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetRenameModelCommandsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.model_info.GetRenameModelCommandsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot().data(), static_cast<int>(this->_internal_robot().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_info.GetRenameModelCommandsRequest.robot");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_robot(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.model_info.GetRenameModelCommandsRequest)
  return target;
}

size_t GetRenameModelCommandsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.model_info.GetRenameModelCommandsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetRenameModelCommandsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetRenameModelCommandsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetRenameModelCommandsRequest::GetClassData() const { return &_class_data_; }

void GetRenameModelCommandsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetRenameModelCommandsRequest *>(to)->MergeFrom(
      static_cast<const GetRenameModelCommandsRequest &>(from));
}


void GetRenameModelCommandsRequest::MergeFrom(const GetRenameModelCommandsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.model_info.GetRenameModelCommandsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot().empty()) {
    _internal_set_robot(from._internal_robot());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetRenameModelCommandsRequest::CopyFrom(const GetRenameModelCommandsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.model_info.GetRenameModelCommandsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetRenameModelCommandsRequest::IsInitialized() const {
  return true;
}

void GetRenameModelCommandsRequest::InternalSwap(GetRenameModelCommandsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_, lhs_arena,
      &other->robot_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetRenameModelCommandsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_getter, &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_once,
      file_level_metadata_portal_2fproto_2fmodel_5finfo_5fsync_2eproto[3]);
}

// ===================================================================

class GetRenameModelCommandsResponse::_Internal {
 public:
};

GetRenameModelCommandsResponse::GetRenameModelCommandsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  commands_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.model_info.GetRenameModelCommandsResponse)
}
GetRenameModelCommandsResponse::GetRenameModelCommandsResponse(const GetRenameModelCommandsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      commands_(from.commands_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.model_info.GetRenameModelCommandsResponse)
}

inline void GetRenameModelCommandsResponse::SharedCtor() {
}

GetRenameModelCommandsResponse::~GetRenameModelCommandsResponse() {
  // @@protoc_insertion_point(destructor:carbon.portal.model_info.GetRenameModelCommandsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetRenameModelCommandsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetRenameModelCommandsResponse::ArenaDtor(void* object) {
  GetRenameModelCommandsResponse* _this = reinterpret_cast< GetRenameModelCommandsResponse* >(object);
  (void)_this;
}
void GetRenameModelCommandsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetRenameModelCommandsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetRenameModelCommandsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.model_info.GetRenameModelCommandsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  commands_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetRenameModelCommandsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.portal.model_info.RenameModelCommand commands = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_commands(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetRenameModelCommandsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.model_info.GetRenameModelCommandsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.portal.model_info.RenameModelCommand commands = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_commands_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_commands(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.model_info.GetRenameModelCommandsResponse)
  return target;
}

size_t GetRenameModelCommandsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.model_info.GetRenameModelCommandsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.model_info.RenameModelCommand commands = 1;
  total_size += 1UL * this->_internal_commands_size();
  for (const auto& msg : this->commands_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetRenameModelCommandsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetRenameModelCommandsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetRenameModelCommandsResponse::GetClassData() const { return &_class_data_; }

void GetRenameModelCommandsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetRenameModelCommandsResponse *>(to)->MergeFrom(
      static_cast<const GetRenameModelCommandsResponse &>(from));
}


void GetRenameModelCommandsResponse::MergeFrom(const GetRenameModelCommandsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.model_info.GetRenameModelCommandsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  commands_.MergeFrom(from.commands_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetRenameModelCommandsResponse::CopyFrom(const GetRenameModelCommandsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.model_info.GetRenameModelCommandsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetRenameModelCommandsResponse::IsInitialized() const {
  return true;
}

void GetRenameModelCommandsResponse::InternalSwap(GetRenameModelCommandsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  commands_.InternalSwap(&other->commands_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetRenameModelCommandsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_getter, &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_once,
      file_level_metadata_portal_2fproto_2fmodel_5finfo_5fsync_2eproto[4]);
}

// ===================================================================

class PurgeRenameModelCommandsRequest::_Internal {
 public:
};

PurgeRenameModelCommandsRequest::PurgeRenameModelCommandsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  commands_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
}
PurgeRenameModelCommandsRequest::PurgeRenameModelCommandsRequest(const PurgeRenameModelCommandsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      commands_(from.commands_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot().empty()) {
    robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
}

inline void PurgeRenameModelCommandsRequest::SharedCtor() {
robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

PurgeRenameModelCommandsRequest::~PurgeRenameModelCommandsRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PurgeRenameModelCommandsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PurgeRenameModelCommandsRequest::ArenaDtor(void* object) {
  PurgeRenameModelCommandsRequest* _this = reinterpret_cast< PurgeRenameModelCommandsRequest* >(object);
  (void)_this;
}
void PurgeRenameModelCommandsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PurgeRenameModelCommandsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PurgeRenameModelCommandsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  commands_.Clear();
  robot_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PurgeRenameModelCommandsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string robot = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_robot();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_info.PurgeRenameModelCommandsRequest.robot"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.portal.model_info.RenameModelCommand commands = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_commands(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PurgeRenameModelCommandsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot().data(), static_cast<int>(this->_internal_robot().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_info.PurgeRenameModelCommandsRequest.robot");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_robot(), target);
  }

  // repeated .carbon.portal.model_info.RenameModelCommand commands = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_commands_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_commands(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
  return target;
}

size_t PurgeRenameModelCommandsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.model_info.RenameModelCommand commands = 2;
  total_size += 1UL * this->_internal_commands_size();
  for (const auto& msg : this->commands_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PurgeRenameModelCommandsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PurgeRenameModelCommandsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PurgeRenameModelCommandsRequest::GetClassData() const { return &_class_data_; }

void PurgeRenameModelCommandsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PurgeRenameModelCommandsRequest *>(to)->MergeFrom(
      static_cast<const PurgeRenameModelCommandsRequest &>(from));
}


void PurgeRenameModelCommandsRequest::MergeFrom(const PurgeRenameModelCommandsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  commands_.MergeFrom(from.commands_);
  if (!from._internal_robot().empty()) {
    _internal_set_robot(from._internal_robot());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PurgeRenameModelCommandsRequest::CopyFrom(const PurgeRenameModelCommandsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PurgeRenameModelCommandsRequest::IsInitialized() const {
  return true;
}

void PurgeRenameModelCommandsRequest::InternalSwap(PurgeRenameModelCommandsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  commands_.InternalSwap(&other->commands_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_, lhs_arena,
      &other->robot_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata PurgeRenameModelCommandsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_getter, &descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto_once,
      file_level_metadata_portal_2fproto_2fmodel_5finfo_5fsync_2eproto[5]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace model_info
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::model_info::ModelInfo* Arena::CreateMaybeMessage< ::carbon::portal::model_info::ModelInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::model_info::ModelInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::model_info::UploadModelInfosRequest* Arena::CreateMaybeMessage< ::carbon::portal::model_info::UploadModelInfosRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::model_info::UploadModelInfosRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::model_info::RenameModelCommand* Arena::CreateMaybeMessage< ::carbon::portal::model_info::RenameModelCommand >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::model_info::RenameModelCommand >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::model_info::GetRenameModelCommandsRequest* Arena::CreateMaybeMessage< ::carbon::portal::model_info::GetRenameModelCommandsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::model_info::GetRenameModelCommandsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::model_info::GetRenameModelCommandsResponse* Arena::CreateMaybeMessage< ::carbon::portal::model_info::GetRenameModelCommandsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::model_info::GetRenameModelCommandsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* Arena::CreateMaybeMessage< ::carbon::portal::model_info::PurgeRenameModelCommandsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::model_info::PurgeRenameModelCommandsRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

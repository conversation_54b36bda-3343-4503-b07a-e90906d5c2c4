// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/jobs_portal.proto
#ifndef GRPC_portal_2fproto_2fjobs_5fportal_2eproto__INCLUDED
#define GRPC_portal_2fproto_2fjobs_5fportal_2eproto__INCLUDED

#include "portal/proto/jobs_portal.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace portal {
namespace jobs {

class PortalJobsService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.portal.jobs.PortalJobsService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status UploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncUploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncUploadJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncUploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncUploadJobRaw(context, request, cq));
    }
    virtual ::grpc::Status UploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncUploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncUploadJobConfigDumpRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncUploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncUploadJobConfigDumpRaw(context, request, cq));
    }
    virtual ::grpc::Status UploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::carbon::portal::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> AsyncUploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(AsyncUploadJobMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>> PrepareAsyncUploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>>(PrepareAsyncUploadJobMetricsRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void UploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void UploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void UploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncUploadJobRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncUploadJobRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncUploadJobConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncUploadJobConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* AsyncUploadJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::util::Empty>* PrepareAsyncUploadJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status UploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncUploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncUploadJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncUploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncUploadJobRaw(context, request, cq));
    }
    ::grpc::Status UploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncUploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncUploadJobConfigDumpRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncUploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncUploadJobConfigDumpRaw(context, request, cq));
    }
    ::grpc::Status UploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::carbon::portal::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> AsyncUploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(AsyncUploadJobMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>> PrepareAsyncUploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>>(PrepareAsyncUploadJobMetricsRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void UploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void UploadJob(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void UploadJobConfigDump(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void UploadJobMetrics(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncUploadJobRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncUploadJobRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncUploadJobConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncUploadJobConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* AsyncUploadJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PrepareAsyncUploadJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_UploadJob_;
    const ::grpc::internal::RpcMethod rpcmethod_UploadJobConfigDump_;
    const ::grpc::internal::RpcMethod rpcmethod_UploadJobMetrics_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status UploadJob(::grpc::ServerContext* context, const ::carbon::portal::jobs::UploadJobRequest* request, ::carbon::portal::util::Empty* response);
    virtual ::grpc::Status UploadJobConfigDump(::grpc::ServerContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::carbon::portal::util::Empty* response);
    virtual ::grpc::Status UploadJobMetrics(::grpc::ServerContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::carbon::portal::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_UploadJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UploadJob() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_UploadJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJob(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadJob(::grpc::ServerContext* context, ::carbon::portal::jobs::UploadJobRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UploadJobConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UploadJobConfigDump() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_UploadJobConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadJobConfigDump(::grpc::ServerContext* context, ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UploadJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UploadJobMetrics() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_UploadJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobMetricsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadJobMetrics(::grpc::ServerContext* context, ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_UploadJob<WithAsyncMethod_UploadJobConfigDump<WithAsyncMethod_UploadJobMetrics<Service > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_UploadJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UploadJob() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::jobs::UploadJobRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::jobs::UploadJobRequest* request, ::carbon::portal::util::Empty* response) { return this->UploadJob(context, request, response); }));}
    void SetMessageAllocatorFor_UploadJob(
        ::grpc::MessageAllocator< ::carbon::portal::jobs::UploadJobRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::jobs::UploadJobRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UploadJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJob(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadJob(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UploadJobConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UploadJobConfigDump() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::jobs::UploadJobConfigDumpRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* request, ::carbon::portal::util::Empty* response) { return this->UploadJobConfigDump(context, request, response); }));}
    void SetMessageAllocatorFor_UploadJobConfigDump(
        ::grpc::MessageAllocator< ::carbon::portal::jobs::UploadJobConfigDumpRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::jobs::UploadJobConfigDumpRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UploadJobConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadJobConfigDump(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UploadJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UploadJobMetrics() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::jobs::UploadJobMetricsRequest, ::carbon::portal::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::jobs::UploadJobMetricsRequest* request, ::carbon::portal::util::Empty* response) { return this->UploadJobMetrics(context, request, response); }));}
    void SetMessageAllocatorFor_UploadJobMetrics(
        ::grpc::MessageAllocator< ::carbon::portal::jobs::UploadJobMetricsRequest, ::carbon::portal::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::jobs::UploadJobMetricsRequest, ::carbon::portal::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UploadJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobMetricsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadJobMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobMetricsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_UploadJob<WithCallbackMethod_UploadJobConfigDump<WithCallbackMethod_UploadJobMetrics<Service > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_UploadJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UploadJob() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_UploadJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJob(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UploadJobConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UploadJobConfigDump() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_UploadJobConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UploadJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UploadJobMetrics() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_UploadJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobMetricsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_UploadJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UploadJob() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_UploadJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJob(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadJob(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UploadJobConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UploadJobConfigDump() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_UploadJobConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadJobConfigDump(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UploadJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UploadJobMetrics() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_UploadJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobMetricsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUploadJobMetrics(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UploadJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UploadJob() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UploadJob(context, request, response); }));
    }
    ~WithRawCallbackMethod_UploadJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJob(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadJob(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UploadJobConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UploadJobConfigDump() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UploadJobConfigDump(context, request, response); }));
    }
    ~WithRawCallbackMethod_UploadJobConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadJobConfigDump(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UploadJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UploadJobMetrics() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UploadJobMetrics(context, request, response); }));
    }
    ~WithRawCallbackMethod_UploadJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UploadJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobMetricsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UploadJobMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UploadJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UploadJob() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::jobs::UploadJobRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::jobs::UploadJobRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedUploadJob(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UploadJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UploadJob(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUploadJob(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::jobs::UploadJobRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UploadJobConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UploadJobConfigDump() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::jobs::UploadJobConfigDumpRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::jobs::UploadJobConfigDumpRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedUploadJobConfigDump(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UploadJobConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UploadJobConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobConfigDumpRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUploadJobConfigDump(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::jobs::UploadJobConfigDumpRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UploadJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UploadJobMetrics() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::jobs::UploadJobMetricsRequest, ::carbon::portal::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::jobs::UploadJobMetricsRequest, ::carbon::portal::util::Empty>* streamer) {
                       return this->StreamedUploadJobMetrics(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UploadJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UploadJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::portal::jobs::UploadJobMetricsRequest* /*request*/, ::carbon::portal::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUploadJobMetrics(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::jobs::UploadJobMetricsRequest,::carbon::portal::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_UploadJob<WithStreamedUnaryMethod_UploadJobConfigDump<WithStreamedUnaryMethod_UploadJobMetrics<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_UploadJob<WithStreamedUnaryMethod_UploadJobConfigDump<WithStreamedUnaryMethod_UploadJobMetrics<Service > > > StreamedService;
};

}  // namespace jobs
}  // namespace portal
}  // namespace carbon


#endif  // GRPC_portal_2fproto_2fjobs_5fportal_2eproto__INCLUDED

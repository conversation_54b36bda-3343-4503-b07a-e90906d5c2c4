# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/spatial_metrics_sync.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.proto.metrics import metrics_pb2 as proto_dot_metrics_dot_metrics__pb2
from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/spatial_metrics_sync.proto',
  package='carbon.portal.spatial_metrics',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\'portal/proto/spatial_metrics_sync.proto\x12\x1d\x63\x61rbon.portal.spatial_metrics\x1a\x1bproto/metrics/metrics.proto\x1a\x17portal/proto/util.proto\"c\n\x1eSyncSpatialMetricBlocksRequest\x12\x32\n\x06\x62locks\x18\x01 \x03(\x0b\x32\".carbon.metrics.SpatialMetricBlock\x12\r\n\x05robot\x18\x02 \x01(\t2\x90\x01\n\x19SpatialMetricsSyncService\x12s\n\x17SyncSpatialMetricBlocks\x12=.carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest\x1a\x19.carbon.portal.util.EmptyB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[proto_dot_metrics_dot_metrics__pb2.DESCRIPTOR,portal_dot_proto_dot_util__pb2.DESCRIPTOR,])




_SYNCSPATIALMETRICBLOCKSREQUEST = _descriptor.Descriptor(
  name='SyncSpatialMetricBlocksRequest',
  full_name='carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='blocks', full_name='carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.blocks', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot', full_name='carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest.robot', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=128,
  serialized_end=227,
)

_SYNCSPATIALMETRICBLOCKSREQUEST.fields_by_name['blocks'].message_type = proto_dot_metrics_dot_metrics__pb2._SPATIALMETRICBLOCK
DESCRIPTOR.message_types_by_name['SyncSpatialMetricBlocksRequest'] = _SYNCSPATIALMETRICBLOCKSREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SyncSpatialMetricBlocksRequest = _reflection.GeneratedProtocolMessageType('SyncSpatialMetricBlocksRequest', (_message.Message,), {
  'DESCRIPTOR' : _SYNCSPATIALMETRICBLOCKSREQUEST,
  '__module__' : 'portal.proto.spatial_metrics_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest)
  })
_sym_db.RegisterMessage(SyncSpatialMetricBlocksRequest)


DESCRIPTOR._options = None

_SPATIALMETRICSSYNCSERVICE = _descriptor.ServiceDescriptor(
  name='SpatialMetricsSyncService',
  full_name='carbon.portal.spatial_metrics.SpatialMetricsSyncService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=230,
  serialized_end=374,
  methods=[
  _descriptor.MethodDescriptor(
    name='SyncSpatialMetricBlocks',
    full_name='carbon.portal.spatial_metrics.SpatialMetricsSyncService.SyncSpatialMetricBlocks',
    index=0,
    containing_service=None,
    input_type=_SYNCSPATIALMETRICBLOCKSREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_SPATIALMETRICSSYNCSERVICE)

DESCRIPTOR.services_by_name['SpatialMetricsSyncService'] = _SPATIALMETRICSSYNCSERVICE

# @@protoc_insertion_point(module_scope)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/alarm_log.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2
from generated.frontend.proto import alarm_pb2 as frontend_dot_proto_dot_alarm__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/alarm_log.proto',
  package='carbon.portal.alarm_log',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1cportal/proto/alarm_log.proto\x12\x17\x63\x61rbon.portal.alarm_log\x1a\x17portal/proto/util.proto\x1a\x1a\x66rontend/proto/alarm.proto\"X\n\x11SyncAlarmsRequest\x12/\n\x06\x61larms\x18\x01 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\x12\x12\n\nrobot_name\x18\x02 \x01(\t2l\n\x15PortalAlarmLogService\x12S\n\nSyncAlarms\x12*.carbon.portal.alarm_log.SyncAlarmsRequest\x1a\x19.carbon.portal.util.EmptyB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[portal_dot_proto_dot_util__pb2.DESCRIPTOR,frontend_dot_proto_dot_alarm__pb2.DESCRIPTOR,])




_SYNCALARMSREQUEST = _descriptor.Descriptor(
  name='SyncAlarmsRequest',
  full_name='carbon.portal.alarm_log.SyncAlarmsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='alarms', full_name='carbon.portal.alarm_log.SyncAlarmsRequest.alarms', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_name', full_name='carbon.portal.alarm_log.SyncAlarmsRequest.robot_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=110,
  serialized_end=198,
)

_SYNCALARMSREQUEST.fields_by_name['alarms'].message_type = frontend_dot_proto_dot_alarm__pb2._ALARMROW
DESCRIPTOR.message_types_by_name['SyncAlarmsRequest'] = _SYNCALARMSREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SyncAlarmsRequest = _reflection.GeneratedProtocolMessageType('SyncAlarmsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SYNCALARMSREQUEST,
  '__module__' : 'portal.proto.alarm_log_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.alarm_log.SyncAlarmsRequest)
  })
_sym_db.RegisterMessage(SyncAlarmsRequest)


DESCRIPTOR._options = None

_PORTALALARMLOGSERVICE = _descriptor.ServiceDescriptor(
  name='PortalAlarmLogService',
  full_name='carbon.portal.alarm_log.PortalAlarmLogService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=200,
  serialized_end=308,
  methods=[
  _descriptor.MethodDescriptor(
    name='SyncAlarms',
    full_name='carbon.portal.alarm_log.PortalAlarmLogService.SyncAlarms',
    index=0,
    containing_service=None,
    input_type=_SYNCALARMSREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_PORTALALARMLOGSERVICE)

DESCRIPTOR.services_by_name['PortalAlarmLogService'] = _PORTALALARMLOGSERVICE

# @@protoc_insertion_point(module_scope)

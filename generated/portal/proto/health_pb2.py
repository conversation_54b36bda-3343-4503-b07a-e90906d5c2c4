# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/health.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2
from generated.frontend.proto import alarm_pb2 as frontend_dot_proto_dot_alarm__pb2
from generated.frontend.proto import laser_pb2 as frontend_dot_proto_dot_laser__pb2
from generated.frontend.proto import status_bar_pb2 as frontend_dot_proto_dot_status__bar__pb2
from generated.proto.metrics import metrics_pb2 as proto_dot_metrics_dot_metrics__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/health.proto',
  package='carbon.portal.health',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x19portal/proto/health.proto\x12\x14\x63\x61rbon.portal.health\x1a\x1cgoogle/protobuf/struct.proto\x1a\x17portal/proto/util.proto\x1a\x1a\x66rontend/proto/alarm.proto\x1a\x1a\x66rontend/proto/laser.proto\x1a\x1f\x66rontend/proto/status_bar.proto\x1a\x1bproto/metrics/metrics.proto\"\xdb\x01\n\x08\x41larmRow\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x12\n\nalarm_code\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12/\n\x05level\x18\x04 \x01(\x0e\x32 .carbon.portal.health.AlarmLevel\x12\x12\n\nidentifier\x18\x05 \x01(\t\x12\x14\n\x0c\x61\x63knowledged\x18\x06 \x01(\x08\x12\x31\n\x06impact\x18\x07 \x01(\x0e\x32!.carbon.portal.health.AlarmImpact:\x02\x18\x01\"+\n\x08Location\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\"\xad\x04\n\x0b\x46ieldConfig\x12\x17\n\x0f\x62\x61nding_enabled\x18\x01 \x01(\x08\x12\x17\n\x0f\x62\x61nding_dynamic\x18\x02 \x01(\x08\x12\x1a\n\x12\x61\x63tive_band_config\x18\x03 \x01(\t\x12!\n\x19\x61\x63tive_thinning_config_id\x18\x04 \x01(\t\x12\x15\n\ractive_job_id\x18\x05 \x01(\t\x12\x19\n\x11\x61\x63tive_almanac_id\x18\x06 \x01(\t\x12\x1f\n\x17\x61\x63tive_discriminator_id\x18\x07 \x01(\t\x12\x12\n\nis_weeding\x18\x08 \x01(\x08\x12\x13\n\x0bis_thinning\x18\t \x01(\x08\x12\x1f\n\x17\x61\x63tive_band_config_name\x18\n \x01(\t\x12#\n\x1b\x61\x63tive_thinning_config_name\x18\x0b \x01(\t\x12\x17\n\x0f\x61\x63tive_job_name\x18\x0c \x01(\t\x12\x1b\n\x13\x61\x63tive_almanac_name\x18\r \x01(\t\x12!\n\x19\x61\x63tive_discriminator_name\x18\x0e \x01(\t\x12\x1d\n\x15\x61\x63tive_modelinator_id\x18\x0f \x01(\t\x12$\n\x1c\x61\x63tive_velocity_estimator_id\x18\x10 \x01(\t\x12&\n\x1e\x61\x63tive_velocity_estimator_name\x18\x11 \x01(\t\x12%\n\x1d\x61\x63tive_category_collection_id\x18\x12 \x01(\t\"+\n\x08Versions\x12\x0f\n\x07\x63urrent\x18\x01 \x01(\t\x12\x0e\n\x06latest\x18\x02 \x01(\t\"e\n\x12WeedingPerformance\x12\x19\n\x11\x61rea_weeded_total\x18\x01 \x01(\x01\x12\x19\n\x11\x61rea_weeded_today\x18\x02 \x01(\x01\x12\x19\n\x11time_weeded_today\x18\x03 \x01(\x03\"H\n\x0bPerformance\x12\x39\n\x07weeding\x18\x01 \x01(\x0b\x32(.carbon.portal.health.WeedingPerformance\"\x80\x01\n\x0c\x44\x61ilyMetrics\x12@\n\x07metrics\x18\x01 \x03(\x0b\x32/.carbon.portal.health.DailyMetrics.MetricsEntry\x1a.\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xaa\x01\n\x07Metrics\x12\x46\n\rdaily_metrics\x18\x01 \x03(\x0b\x32/.carbon.portal.health.Metrics.DailyMetricsEntry\x1aW\n\x11\x44\x61ilyMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32\".carbon.portal.health.DailyMetrics:\x02\x38\x01\"\x8a\n\n\tHealthLog\x12\x32\n\x06\x61larms\x18\x01 \x03(\x0b\x32\x1e.carbon.portal.health.AlarmRowB\x02\x18\x01\x12\x30\n\x08location\x18\x02 \x01(\x0b\x32\x1e.carbon.portal.health.Location\x12\r\n\x05model\x18\x03 \x01(\t\x12\x0e\n\x06models\x18\x04 \x03(\t\x12\x36\n\x0bperformance\x18\x05 \x01(\x0b\x32!.carbon.portal.health.Performance\x12\x13\n\x0breported_at\x18\x06 \x01(\x03\x12\x14\n\x0crobot_serial\x18\x07 \x01(\t\x12(\n\x07systems\x18\x08 \x03(\x0b\x32\x17.google.protobuf.Struct\x12\x32\n\x06status\x18\t \x01(\x0e\x32\".carbon.frontend.status_bar.Status\x12\x19\n\x11status_changed_at\x18\n \x01(\x03\x12\x10\n\x04\x63rop\x18\x0b \x01(\tB\x02\x18\x01\x12\x0b\n\x03p2p\x18\x0c \x01(\t\x12\x18\n\x10software_version\x18\r \x01(\t\x12\x16\n\x0etarget_version\x18\x0e \x01(\t\x12\x1c\n\x14target_version_ready\x18\x0f \x01(\x08\x12\x16\n\x0estatus_message\x18\x10 \x01(\t\x12H\n\rmetric_totals\x18\x11 \x03(\x0b\x32\x31.carbon.portal.health.HealthLog.MetricTotalsEntry\x12\x33\n\nalarm_list\x18\x12 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\x12\x37\n\x0c\x66ield_config\x18\x13 \x01(\x0b\x32!.carbon.portal.health.FieldConfig\x12.\n\x07metrics\x18\x14 \x01(\x0b\x32\x1d.carbon.portal.health.Metrics\x12\x0f\n\x07\x63rop_id\x18\x15 \x01(\t\x12\x1a\n\x12robot_runtime_240v\x18\x16 \x01(\r\x12:\n\x0blaser_state\x18\x17 \x01(\x0b\x32%.carbon.frontend.laser.LaserStateList\x12<\n\x12laser_change_times\x18\x18 \x01(\x0b\x32 .carbon.metrics.LaserChangeTimes\x12\x46\n\x0chost_serials\x18\x19 \x03(\x0b\x32\x30.carbon.portal.health.HealthLog.HostSerialsEntry\x12H\n\rfeature_flags\x18\x1a \x03(\x0b\x32\x31.carbon.portal.health.HealthLog.FeatureFlagsEntry\x12V\n\x19translated_status_message\x18\x1b \x01(\x0b\x32\x33.carbon.frontend.status_bar.TranslatedStatusMessage\x1a\x33\n\x11MetricTotalsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x04:\x02\x38\x01\x1a\x32\n\x10HostSerialsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x33\n\x11\x46\x65\x61tureFlagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"\xb2\x01\n\x0bIssueReport\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12\x14\n\x0cphone_number\x18\x02 \x01(\t\x12\x14\n\x0crobot_serial\x18\x03 \x01(\t\x12\x13\n\x0breported_at\x18\x04 \x01(\x03\x12\x10\n\x04\x63rop\x18\x05 \x01(\tB\x02\x18\x01\x12\x10\n\x08model_id\x18\x06 \x01(\t\x12\x18\n\x10software_version\x18\x07 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x08 \x01(\t*\x8e\x01\n\nAlarmLevel\x12\x15\n\rALARM_UNKNOWN\x10\x00\x1a\x02\x08\x01\x12\x16\n\x0e\x41LARM_CRITICAL\x10\x01\x1a\x02\x08\x01\x12\x12\n\nALARM_HIGH\x10\x02\x1a\x02\x08\x01\x12\x14\n\x0c\x41LARM_MEDIUM\x10\x03\x1a\x02\x08\x01\x12\x11\n\tALARM_LOW\x10\x04\x1a\x02\x08\x01\x12\x14\n\x0c\x41LARM_HIDDEN\x10\x05\x1a\x02\x08\x01*\x86\x01\n\x0b\x41larmImpact\x12\x18\n\x10IMPACT_UNDEFINED\x10\x00\x1a\x02\x08\x01\x12\x17\n\x0fIMPACT_CRITICAL\x10\x01\x1a\x02\x08\x01\x12\x16\n\x0eIMPACT_OFFLINE\x10\x02\x1a\x02\x08\x01\x12\x17\n\x0fIMPACT_DEGRADED\x10\x03\x1a\x02\x08\x01\x12\x13\n\x0bIMPACT_NONE\x10\x04\x1a\x02\x08\x01\x32\xa5\x01\n\rHealthService\x12G\n\tLogHealth\x12\x1f.carbon.portal.health.HealthLog\x1a\x19.carbon.portal.util.Empty\x12K\n\x0bReportIssue\x12!.carbon.portal.health.IssueReport\x1a\x19.carbon.portal.util.EmptyB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,portal_dot_proto_dot_util__pb2.DESCRIPTOR,frontend_dot_proto_dot_alarm__pb2.DESCRIPTOR,frontend_dot_proto_dot_laser__pb2.DESCRIPTOR,frontend_dot_proto_dot_status__bar__pb2.DESCRIPTOR,proto_dot_metrics_dot_metrics__pb2.DESCRIPTOR,])

_ALARMLEVEL = _descriptor.EnumDescriptor(
  name='AlarmLevel',
  full_name='carbon.portal.health.AlarmLevel',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ALARM_UNKNOWN', index=0, number=0,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ALARM_CRITICAL', index=1, number=1,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ALARM_HIGH', index=2, number=2,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ALARM_MEDIUM', index=3, number=3,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ALARM_LOW', index=4, number=4,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ALARM_HIDDEN', index=5, number=5,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3052,
  serialized_end=3194,
)
_sym_db.RegisterEnumDescriptor(_ALARMLEVEL)

AlarmLevel = enum_type_wrapper.EnumTypeWrapper(_ALARMLEVEL)
_ALARMIMPACT = _descriptor.EnumDescriptor(
  name='AlarmImpact',
  full_name='carbon.portal.health.AlarmImpact',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='IMPACT_UNDEFINED', index=0, number=0,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IMPACT_CRITICAL', index=1, number=1,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IMPACT_OFFLINE', index=2, number=2,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IMPACT_DEGRADED', index=3, number=3,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IMPACT_NONE', index=4, number=4,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3197,
  serialized_end=3331,
)
_sym_db.RegisterEnumDescriptor(_ALARMIMPACT)

AlarmImpact = enum_type_wrapper.EnumTypeWrapper(_ALARMIMPACT)
ALARM_UNKNOWN = 0
ALARM_CRITICAL = 1
ALARM_HIGH = 2
ALARM_MEDIUM = 3
ALARM_LOW = 4
ALARM_HIDDEN = 5
IMPACT_UNDEFINED = 0
IMPACT_CRITICAL = 1
IMPACT_OFFLINE = 2
IMPACT_DEGRADED = 3
IMPACT_NONE = 4



_ALARMROW = _descriptor.Descriptor(
  name='AlarmRow',
  full_name='carbon.portal.health.AlarmRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.portal.health.AlarmRow.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alarm_code', full_name='carbon.portal.health.AlarmRow.alarm_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='description', full_name='carbon.portal.health.AlarmRow.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='level', full_name='carbon.portal.health.AlarmRow.level', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='identifier', full_name='carbon.portal.health.AlarmRow.identifier', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='acknowledged', full_name='carbon.portal.health.AlarmRow.acknowledged', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='impact', full_name='carbon.portal.health.AlarmRow.impact', index=6,
      number=7, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=225,
  serialized_end=444,
)


_LOCATION = _descriptor.Descriptor(
  name='Location',
  full_name='carbon.portal.health.Location',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='carbon.portal.health.Location.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='carbon.portal.health.Location.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='carbon.portal.health.Location.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=446,
  serialized_end=489,
)


_FIELDCONFIG = _descriptor.Descriptor(
  name='FieldConfig',
  full_name='carbon.portal.health.FieldConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='banding_enabled', full_name='carbon.portal.health.FieldConfig.banding_enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding_dynamic', full_name='carbon.portal.health.FieldConfig.banding_dynamic', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_band_config', full_name='carbon.portal.health.FieldConfig.active_band_config', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_thinning_config_id', full_name='carbon.portal.health.FieldConfig.active_thinning_config_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_job_id', full_name='carbon.portal.health.FieldConfig.active_job_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_almanac_id', full_name='carbon.portal.health.FieldConfig.active_almanac_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_discriminator_id', full_name='carbon.portal.health.FieldConfig.active_discriminator_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_weeding', full_name='carbon.portal.health.FieldConfig.is_weeding', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_thinning', full_name='carbon.portal.health.FieldConfig.is_thinning', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_band_config_name', full_name='carbon.portal.health.FieldConfig.active_band_config_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_thinning_config_name', full_name='carbon.portal.health.FieldConfig.active_thinning_config_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_job_name', full_name='carbon.portal.health.FieldConfig.active_job_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_almanac_name', full_name='carbon.portal.health.FieldConfig.active_almanac_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_discriminator_name', full_name='carbon.portal.health.FieldConfig.active_discriminator_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_modelinator_id', full_name='carbon.portal.health.FieldConfig.active_modelinator_id', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_velocity_estimator_id', full_name='carbon.portal.health.FieldConfig.active_velocity_estimator_id', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_velocity_estimator_name', full_name='carbon.portal.health.FieldConfig.active_velocity_estimator_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_category_collection_id', full_name='carbon.portal.health.FieldConfig.active_category_collection_id', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=492,
  serialized_end=1049,
)


_VERSIONS = _descriptor.Descriptor(
  name='Versions',
  full_name='carbon.portal.health.Versions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current', full_name='carbon.portal.health.Versions.current', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latest', full_name='carbon.portal.health.Versions.latest', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1051,
  serialized_end=1094,
)


_WEEDINGPERFORMANCE = _descriptor.Descriptor(
  name='WeedingPerformance',
  full_name='carbon.portal.health.WeedingPerformance',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='area_weeded_total', full_name='carbon.portal.health.WeedingPerformance.area_weeded_total', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='area_weeded_today', full_name='carbon.portal.health.WeedingPerformance.area_weeded_today', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time_weeded_today', full_name='carbon.portal.health.WeedingPerformance.time_weeded_today', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1096,
  serialized_end=1197,
)


_PERFORMANCE = _descriptor.Descriptor(
  name='Performance',
  full_name='carbon.portal.health.Performance',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weeding', full_name='carbon.portal.health.Performance.weeding', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1199,
  serialized_end=1271,
)


_DAILYMETRICS_METRICSENTRY = _descriptor.Descriptor(
  name='MetricsEntry',
  full_name='carbon.portal.health.DailyMetrics.MetricsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.portal.health.DailyMetrics.MetricsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.portal.health.DailyMetrics.MetricsEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1356,
  serialized_end=1402,
)

_DAILYMETRICS = _descriptor.Descriptor(
  name='DailyMetrics',
  full_name='carbon.portal.health.DailyMetrics',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='metrics', full_name='carbon.portal.health.DailyMetrics.metrics', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_DAILYMETRICS_METRICSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1274,
  serialized_end=1402,
)


_METRICS_DAILYMETRICSENTRY = _descriptor.Descriptor(
  name='DailyMetricsEntry',
  full_name='carbon.portal.health.Metrics.DailyMetricsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.portal.health.Metrics.DailyMetricsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.portal.health.Metrics.DailyMetricsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1488,
  serialized_end=1575,
)

_METRICS = _descriptor.Descriptor(
  name='Metrics',
  full_name='carbon.portal.health.Metrics',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='daily_metrics', full_name='carbon.portal.health.Metrics.daily_metrics', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_METRICS_DAILYMETRICSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1405,
  serialized_end=1575,
)


_HEALTHLOG_METRICTOTALSENTRY = _descriptor.Descriptor(
  name='MetricTotalsEntry',
  full_name='carbon.portal.health.HealthLog.MetricTotalsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.portal.health.HealthLog.MetricTotalsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.portal.health.HealthLog.MetricTotalsEntry.value', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2712,
  serialized_end=2763,
)

_HEALTHLOG_HOSTSERIALSENTRY = _descriptor.Descriptor(
  name='HostSerialsEntry',
  full_name='carbon.portal.health.HealthLog.HostSerialsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.portal.health.HealthLog.HostSerialsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.portal.health.HealthLog.HostSerialsEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2765,
  serialized_end=2815,
)

_HEALTHLOG_FEATUREFLAGSENTRY = _descriptor.Descriptor(
  name='FeatureFlagsEntry',
  full_name='carbon.portal.health.HealthLog.FeatureFlagsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.portal.health.HealthLog.FeatureFlagsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.portal.health.HealthLog.FeatureFlagsEntry.value', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2817,
  serialized_end=2868,
)

_HEALTHLOG = _descriptor.Descriptor(
  name='HealthLog',
  full_name='carbon.portal.health.HealthLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='alarms', full_name='carbon.portal.health.HealthLog.alarms', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='location', full_name='carbon.portal.health.HealthLog.location', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model', full_name='carbon.portal.health.HealthLog.model', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='models', full_name='carbon.portal.health.HealthLog.models', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='performance', full_name='carbon.portal.health.HealthLog.performance', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reported_at', full_name='carbon.portal.health.HealthLog.reported_at', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_serial', full_name='carbon.portal.health.HealthLog.robot_serial', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='systems', full_name='carbon.portal.health.HealthLog.systems', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='carbon.portal.health.HealthLog.status', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status_changed_at', full_name='carbon.portal.health.HealthLog.status_changed_at', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.portal.health.HealthLog.crop', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p', full_name='carbon.portal.health.HealthLog.p2p', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='software_version', full_name='carbon.portal.health.HealthLog.software_version', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_version', full_name='carbon.portal.health.HealthLog.target_version', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_version_ready', full_name='carbon.portal.health.HealthLog.target_version_ready', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status_message', full_name='carbon.portal.health.HealthLog.status_message', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metric_totals', full_name='carbon.portal.health.HealthLog.metric_totals', index=16,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alarm_list', full_name='carbon.portal.health.HealthLog.alarm_list', index=17,
      number=18, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='field_config', full_name='carbon.portal.health.HealthLog.field_config', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metrics', full_name='carbon.portal.health.HealthLog.metrics', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.portal.health.HealthLog.crop_id', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_runtime_240v', full_name='carbon.portal.health.HealthLog.robot_runtime_240v', index=21,
      number=22, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_state', full_name='carbon.portal.health.HealthLog.laser_state', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_change_times', full_name='carbon.portal.health.HealthLog.laser_change_times', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='host_serials', full_name='carbon.portal.health.HealthLog.host_serials', index=24,
      number=25, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='feature_flags', full_name='carbon.portal.health.HealthLog.feature_flags', index=25,
      number=26, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='translated_status_message', full_name='carbon.portal.health.HealthLog.translated_status_message', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_HEALTHLOG_METRICTOTALSENTRY, _HEALTHLOG_HOSTSERIALSENTRY, _HEALTHLOG_FEATUREFLAGSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1578,
  serialized_end=2868,
)


_ISSUEREPORT = _descriptor.Descriptor(
  name='IssueReport',
  full_name='carbon.portal.health.IssueReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='carbon.portal.health.IssueReport.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phone_number', full_name='carbon.portal.health.IssueReport.phone_number', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_serial', full_name='carbon.portal.health.IssueReport.robot_serial', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reported_at', full_name='carbon.portal.health.IssueReport.reported_at', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.portal.health.IssueReport.crop', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.portal.health.IssueReport.model_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='software_version', full_name='carbon.portal.health.IssueReport.software_version', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.portal.health.IssueReport.crop_id', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2871,
  serialized_end=3049,
)

_ALARMROW.fields_by_name['level'].enum_type = _ALARMLEVEL
_ALARMROW.fields_by_name['impact'].enum_type = _ALARMIMPACT
_PERFORMANCE.fields_by_name['weeding'].message_type = _WEEDINGPERFORMANCE
_DAILYMETRICS_METRICSENTRY.containing_type = _DAILYMETRICS
_DAILYMETRICS.fields_by_name['metrics'].message_type = _DAILYMETRICS_METRICSENTRY
_METRICS_DAILYMETRICSENTRY.fields_by_name['value'].message_type = _DAILYMETRICS
_METRICS_DAILYMETRICSENTRY.containing_type = _METRICS
_METRICS.fields_by_name['daily_metrics'].message_type = _METRICS_DAILYMETRICSENTRY
_HEALTHLOG_METRICTOTALSENTRY.containing_type = _HEALTHLOG
_HEALTHLOG_HOSTSERIALSENTRY.containing_type = _HEALTHLOG
_HEALTHLOG_FEATUREFLAGSENTRY.containing_type = _HEALTHLOG
_HEALTHLOG.fields_by_name['alarms'].message_type = _ALARMROW
_HEALTHLOG.fields_by_name['location'].message_type = _LOCATION
_HEALTHLOG.fields_by_name['performance'].message_type = _PERFORMANCE
_HEALTHLOG.fields_by_name['systems'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_HEALTHLOG.fields_by_name['status'].enum_type = frontend_dot_proto_dot_status__bar__pb2._STATUS
_HEALTHLOG.fields_by_name['metric_totals'].message_type = _HEALTHLOG_METRICTOTALSENTRY
_HEALTHLOG.fields_by_name['alarm_list'].message_type = frontend_dot_proto_dot_alarm__pb2._ALARMROW
_HEALTHLOG.fields_by_name['field_config'].message_type = _FIELDCONFIG
_HEALTHLOG.fields_by_name['metrics'].message_type = _METRICS
_HEALTHLOG.fields_by_name['laser_state'].message_type = frontend_dot_proto_dot_laser__pb2._LASERSTATELIST
_HEALTHLOG.fields_by_name['laser_change_times'].message_type = proto_dot_metrics_dot_metrics__pb2._LASERCHANGETIMES
_HEALTHLOG.fields_by_name['host_serials'].message_type = _HEALTHLOG_HOSTSERIALSENTRY
_HEALTHLOG.fields_by_name['feature_flags'].message_type = _HEALTHLOG_FEATUREFLAGSENTRY
_HEALTHLOG.fields_by_name['translated_status_message'].message_type = frontend_dot_proto_dot_status__bar__pb2._TRANSLATEDSTATUSMESSAGE
DESCRIPTOR.message_types_by_name['AlarmRow'] = _ALARMROW
DESCRIPTOR.message_types_by_name['Location'] = _LOCATION
DESCRIPTOR.message_types_by_name['FieldConfig'] = _FIELDCONFIG
DESCRIPTOR.message_types_by_name['Versions'] = _VERSIONS
DESCRIPTOR.message_types_by_name['WeedingPerformance'] = _WEEDINGPERFORMANCE
DESCRIPTOR.message_types_by_name['Performance'] = _PERFORMANCE
DESCRIPTOR.message_types_by_name['DailyMetrics'] = _DAILYMETRICS
DESCRIPTOR.message_types_by_name['Metrics'] = _METRICS
DESCRIPTOR.message_types_by_name['HealthLog'] = _HEALTHLOG
DESCRIPTOR.message_types_by_name['IssueReport'] = _ISSUEREPORT
DESCRIPTOR.enum_types_by_name['AlarmLevel'] = _ALARMLEVEL
DESCRIPTOR.enum_types_by_name['AlarmImpact'] = _ALARMIMPACT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AlarmRow = _reflection.GeneratedProtocolMessageType('AlarmRow', (_message.Message,), {
  'DESCRIPTOR' : _ALARMROW,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.AlarmRow)
  })
_sym_db.RegisterMessage(AlarmRow)

Location = _reflection.GeneratedProtocolMessageType('Location', (_message.Message,), {
  'DESCRIPTOR' : _LOCATION,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.Location)
  })
_sym_db.RegisterMessage(Location)

FieldConfig = _reflection.GeneratedProtocolMessageType('FieldConfig', (_message.Message,), {
  'DESCRIPTOR' : _FIELDCONFIG,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.FieldConfig)
  })
_sym_db.RegisterMessage(FieldConfig)

Versions = _reflection.GeneratedProtocolMessageType('Versions', (_message.Message,), {
  'DESCRIPTOR' : _VERSIONS,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.Versions)
  })
_sym_db.RegisterMessage(Versions)

WeedingPerformance = _reflection.GeneratedProtocolMessageType('WeedingPerformance', (_message.Message,), {
  'DESCRIPTOR' : _WEEDINGPERFORMANCE,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.WeedingPerformance)
  })
_sym_db.RegisterMessage(WeedingPerformance)

Performance = _reflection.GeneratedProtocolMessageType('Performance', (_message.Message,), {
  'DESCRIPTOR' : _PERFORMANCE,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.Performance)
  })
_sym_db.RegisterMessage(Performance)

DailyMetrics = _reflection.GeneratedProtocolMessageType('DailyMetrics', (_message.Message,), {

  'MetricsEntry' : _reflection.GeneratedProtocolMessageType('MetricsEntry', (_message.Message,), {
    'DESCRIPTOR' : _DAILYMETRICS_METRICSENTRY,
    '__module__' : 'portal.proto.health_pb2'
    # @@protoc_insertion_point(class_scope:carbon.portal.health.DailyMetrics.MetricsEntry)
    })
  ,
  'DESCRIPTOR' : _DAILYMETRICS,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.DailyMetrics)
  })
_sym_db.RegisterMessage(DailyMetrics)
_sym_db.RegisterMessage(DailyMetrics.MetricsEntry)

Metrics = _reflection.GeneratedProtocolMessageType('Metrics', (_message.Message,), {

  'DailyMetricsEntry' : _reflection.GeneratedProtocolMessageType('DailyMetricsEntry', (_message.Message,), {
    'DESCRIPTOR' : _METRICS_DAILYMETRICSENTRY,
    '__module__' : 'portal.proto.health_pb2'
    # @@protoc_insertion_point(class_scope:carbon.portal.health.Metrics.DailyMetricsEntry)
    })
  ,
  'DESCRIPTOR' : _METRICS,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.Metrics)
  })
_sym_db.RegisterMessage(Metrics)
_sym_db.RegisterMessage(Metrics.DailyMetricsEntry)

HealthLog = _reflection.GeneratedProtocolMessageType('HealthLog', (_message.Message,), {

  'MetricTotalsEntry' : _reflection.GeneratedProtocolMessageType('MetricTotalsEntry', (_message.Message,), {
    'DESCRIPTOR' : _HEALTHLOG_METRICTOTALSENTRY,
    '__module__' : 'portal.proto.health_pb2'
    # @@protoc_insertion_point(class_scope:carbon.portal.health.HealthLog.MetricTotalsEntry)
    })
  ,

  'HostSerialsEntry' : _reflection.GeneratedProtocolMessageType('HostSerialsEntry', (_message.Message,), {
    'DESCRIPTOR' : _HEALTHLOG_HOSTSERIALSENTRY,
    '__module__' : 'portal.proto.health_pb2'
    # @@protoc_insertion_point(class_scope:carbon.portal.health.HealthLog.HostSerialsEntry)
    })
  ,

  'FeatureFlagsEntry' : _reflection.GeneratedProtocolMessageType('FeatureFlagsEntry', (_message.Message,), {
    'DESCRIPTOR' : _HEALTHLOG_FEATUREFLAGSENTRY,
    '__module__' : 'portal.proto.health_pb2'
    # @@protoc_insertion_point(class_scope:carbon.portal.health.HealthLog.FeatureFlagsEntry)
    })
  ,
  'DESCRIPTOR' : _HEALTHLOG,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.HealthLog)
  })
_sym_db.RegisterMessage(HealthLog)
_sym_db.RegisterMessage(HealthLog.MetricTotalsEntry)
_sym_db.RegisterMessage(HealthLog.HostSerialsEntry)
_sym_db.RegisterMessage(HealthLog.FeatureFlagsEntry)

IssueReport = _reflection.GeneratedProtocolMessageType('IssueReport', (_message.Message,), {
  'DESCRIPTOR' : _ISSUEREPORT,
  '__module__' : 'portal.proto.health_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.health.IssueReport)
  })
_sym_db.RegisterMessage(IssueReport)


DESCRIPTOR._options = None
_ALARMLEVEL.values_by_name["ALARM_UNKNOWN"]._options = None
_ALARMLEVEL.values_by_name["ALARM_CRITICAL"]._options = None
_ALARMLEVEL.values_by_name["ALARM_HIGH"]._options = None
_ALARMLEVEL.values_by_name["ALARM_MEDIUM"]._options = None
_ALARMLEVEL.values_by_name["ALARM_LOW"]._options = None
_ALARMLEVEL.values_by_name["ALARM_HIDDEN"]._options = None
_ALARMIMPACT.values_by_name["IMPACT_UNDEFINED"]._options = None
_ALARMIMPACT.values_by_name["IMPACT_CRITICAL"]._options = None
_ALARMIMPACT.values_by_name["IMPACT_OFFLINE"]._options = None
_ALARMIMPACT.values_by_name["IMPACT_DEGRADED"]._options = None
_ALARMIMPACT.values_by_name["IMPACT_NONE"]._options = None
_ALARMROW._options = None
_DAILYMETRICS_METRICSENTRY._options = None
_METRICS_DAILYMETRICSENTRY._options = None
_HEALTHLOG_METRICTOTALSENTRY._options = None
_HEALTHLOG_HOSTSERIALSENTRY._options = None
_HEALTHLOG_FEATUREFLAGSENTRY._options = None
_HEALTHLOG.fields_by_name['alarms']._options = None
_HEALTHLOG.fields_by_name['crop']._options = None
_ISSUEREPORT.fields_by_name['crop']._options = None

_HEALTHSERVICE = _descriptor.ServiceDescriptor(
  name='HealthService',
  full_name='carbon.portal.health.HealthService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=3334,
  serialized_end=3499,
  methods=[
  _descriptor.MethodDescriptor(
    name='LogHealth',
    full_name='carbon.portal.health.HealthService.LogHealth',
    index=0,
    containing_service=None,
    input_type=_HEALTHLOG,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReportIssue',
    full_name='carbon.portal.health.HealthService.ReportIssue',
    index=1,
    containing_service=None,
    input_type=_ISSUEREPORT,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_HEALTHSERVICE)

DESCRIPTOR.services_by_name['HealthService'] = _HEALTHSERVICE

# @@protoc_insertion_point(module_scope)

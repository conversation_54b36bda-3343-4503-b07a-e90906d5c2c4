// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/jobs_portal.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fjobs_5fportal_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fjobs_5fportal_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "portal/proto/util.pb.h"
#include "frontend/proto/jobs.pb.h"
#include "frontend/proto/weeding_diagnostics.pb.h"
#include "metrics/proto/metrics_aggregator_service.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2fjobs_5fportal_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2fjobs_5fportal_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fjobs_5fportal_2eproto;
namespace carbon {
namespace portal {
namespace jobs {
class UploadJobConfigDumpRequest;
struct UploadJobConfigDumpRequestDefaultTypeInternal;
extern UploadJobConfigDumpRequestDefaultTypeInternal _UploadJobConfigDumpRequest_default_instance_;
class UploadJobMetricsRequest;
struct UploadJobMetricsRequestDefaultTypeInternal;
extern UploadJobMetricsRequestDefaultTypeInternal _UploadJobMetricsRequest_default_instance_;
class UploadJobRequest;
struct UploadJobRequestDefaultTypeInternal;
extern UploadJobRequestDefaultTypeInternal _UploadJobRequest_default_instance_;
}  // namespace jobs
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::jobs::UploadJobConfigDumpRequest* Arena::CreateMaybeMessage<::carbon::portal::jobs::UploadJobConfigDumpRequest>(Arena*);
template<> ::carbon::portal::jobs::UploadJobMetricsRequest* Arena::CreateMaybeMessage<::carbon::portal::jobs::UploadJobMetricsRequest>(Arena*);
template<> ::carbon::portal::jobs::UploadJobRequest* Arena::CreateMaybeMessage<::carbon::portal::jobs::UploadJobRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace jobs {

// ===================================================================

class UploadJobRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.jobs.UploadJobRequest) */ {
 public:
  inline UploadJobRequest() : UploadJobRequest(nullptr) {}
  ~UploadJobRequest() override;
  explicit constexpr UploadJobRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UploadJobRequest(const UploadJobRequest& from);
  UploadJobRequest(UploadJobRequest&& from) noexcept
    : UploadJobRequest() {
    *this = ::std::move(from);
  }

  inline UploadJobRequest& operator=(const UploadJobRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UploadJobRequest& operator=(UploadJobRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UploadJobRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UploadJobRequest* internal_default_instance() {
    return reinterpret_cast<const UploadJobRequest*>(
               &_UploadJobRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(UploadJobRequest& a, UploadJobRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UploadJobRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UploadJobRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UploadJobRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UploadJobRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UploadJobRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UploadJobRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UploadJobRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.jobs.UploadJobRequest";
  }
  protected:
  explicit UploadJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotFieldNumber = 2,
    kJobFieldNumber = 1,
  };
  // string robot = 2;
  void clear_robot();
  const std::string& robot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot();
  PROTOBUF_NODISCARD std::string* release_robot();
  void set_allocated_robot(std::string* robot);
  private:
  const std::string& _internal_robot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot(const std::string& value);
  std::string* _internal_mutable_robot();
  public:

  // .carbon.frontend.jobs.Job job = 1;
  bool has_job() const;
  private:
  bool _internal_has_job() const;
  public:
  void clear_job();
  const ::carbon::frontend::jobs::Job& job() const;
  PROTOBUF_NODISCARD ::carbon::frontend::jobs::Job* release_job();
  ::carbon::frontend::jobs::Job* mutable_job();
  void set_allocated_job(::carbon::frontend::jobs::Job* job);
  private:
  const ::carbon::frontend::jobs::Job& _internal_job() const;
  ::carbon::frontend::jobs::Job* _internal_mutable_job();
  public:
  void unsafe_arena_set_allocated_job(
      ::carbon::frontend::jobs::Job* job);
  ::carbon::frontend::jobs::Job* unsafe_arena_release_job();

  // @@protoc_insertion_point(class_scope:carbon.portal.jobs.UploadJobRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_;
  ::carbon::frontend::jobs::Job* job_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fjobs_5fportal_2eproto;
};
// -------------------------------------------------------------------

class UploadJobConfigDumpRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.jobs.UploadJobConfigDumpRequest) */ {
 public:
  inline UploadJobConfigDumpRequest() : UploadJobConfigDumpRequest(nullptr) {}
  ~UploadJobConfigDumpRequest() override;
  explicit constexpr UploadJobConfigDumpRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UploadJobConfigDumpRequest(const UploadJobConfigDumpRequest& from);
  UploadJobConfigDumpRequest(UploadJobConfigDumpRequest&& from) noexcept
    : UploadJobConfigDumpRequest() {
    *this = ::std::move(from);
  }

  inline UploadJobConfigDumpRequest& operator=(const UploadJobConfigDumpRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UploadJobConfigDumpRequest& operator=(UploadJobConfigDumpRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UploadJobConfigDumpRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UploadJobConfigDumpRequest* internal_default_instance() {
    return reinterpret_cast<const UploadJobConfigDumpRequest*>(
               &_UploadJobConfigDumpRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(UploadJobConfigDumpRequest& a, UploadJobConfigDumpRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UploadJobConfigDumpRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UploadJobConfigDumpRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UploadJobConfigDumpRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UploadJobConfigDumpRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UploadJobConfigDumpRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UploadJobConfigDumpRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UploadJobConfigDumpRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.jobs.UploadJobConfigDumpRequest";
  }
  protected:
  explicit UploadJobConfigDumpRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
    kRootConfigFieldNumber = 2,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 2;
  bool has_rootconfig() const;
  private:
  bool _internal_has_rootconfig() const;
  public:
  void clear_rootconfig();
  const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& rootconfig() const;
  PROTOBUF_NODISCARD ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* release_rootconfig();
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* mutable_rootconfig();
  void set_allocated_rootconfig(::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig);
  private:
  const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& _internal_rootconfig() const;
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* _internal_mutable_rootconfig();
  public:
  void unsafe_arena_set_allocated_rootconfig(
      ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig);
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* unsafe_arena_release_rootconfig();

  // @@protoc_insertion_point(class_scope:carbon.portal.jobs.UploadJobConfigDumpRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fjobs_5fportal_2eproto;
};
// -------------------------------------------------------------------

class UploadJobMetricsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.jobs.UploadJobMetricsRequest) */ {
 public:
  inline UploadJobMetricsRequest() : UploadJobMetricsRequest(nullptr) {}
  ~UploadJobMetricsRequest() override;
  explicit constexpr UploadJobMetricsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UploadJobMetricsRequest(const UploadJobMetricsRequest& from);
  UploadJobMetricsRequest(UploadJobMetricsRequest&& from) noexcept
    : UploadJobMetricsRequest() {
    *this = ::std::move(from);
  }

  inline UploadJobMetricsRequest& operator=(const UploadJobMetricsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UploadJobMetricsRequest& operator=(UploadJobMetricsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UploadJobMetricsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UploadJobMetricsRequest* internal_default_instance() {
    return reinterpret_cast<const UploadJobMetricsRequest*>(
               &_UploadJobMetricsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(UploadJobMetricsRequest& a, UploadJobMetricsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UploadJobMetricsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UploadJobMetricsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UploadJobMetricsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UploadJobMetricsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UploadJobMetricsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UploadJobMetricsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UploadJobMetricsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.jobs.UploadJobMetricsRequest";
  }
  protected:
  explicit UploadJobMetricsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
    kJobMetricsFieldNumber = 2,
  };
  // string jobId = 1;
  void clear_jobid();
  const std::string& jobid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_jobid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_jobid();
  PROTOBUF_NODISCARD std::string* release_jobid();
  void set_allocated_jobid(std::string* jobid);
  private:
  const std::string& _internal_jobid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_jobid(const std::string& value);
  std::string* _internal_mutable_jobid();
  public:

  // .metrics_aggregator.Metrics jobMetrics = 2;
  bool has_jobmetrics() const;
  private:
  bool _internal_has_jobmetrics() const;
  public:
  void clear_jobmetrics();
  const ::metrics_aggregator::Metrics& jobmetrics() const;
  PROTOBUF_NODISCARD ::metrics_aggregator::Metrics* release_jobmetrics();
  ::metrics_aggregator::Metrics* mutable_jobmetrics();
  void set_allocated_jobmetrics(::metrics_aggregator::Metrics* jobmetrics);
  private:
  const ::metrics_aggregator::Metrics& _internal_jobmetrics() const;
  ::metrics_aggregator::Metrics* _internal_mutable_jobmetrics();
  public:
  void unsafe_arena_set_allocated_jobmetrics(
      ::metrics_aggregator::Metrics* jobmetrics);
  ::metrics_aggregator::Metrics* unsafe_arena_release_jobmetrics();

  // @@protoc_insertion_point(class_scope:carbon.portal.jobs.UploadJobMetricsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr jobid_;
  ::metrics_aggregator::Metrics* jobmetrics_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fjobs_5fportal_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// UploadJobRequest

// .carbon.frontend.jobs.Job job = 1;
inline bool UploadJobRequest::_internal_has_job() const {
  return this != internal_default_instance() && job_ != nullptr;
}
inline bool UploadJobRequest::has_job() const {
  return _internal_has_job();
}
inline const ::carbon::frontend::jobs::Job& UploadJobRequest::_internal_job() const {
  const ::carbon::frontend::jobs::Job* p = job_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::jobs::Job&>(
      ::carbon::frontend::jobs::_Job_default_instance_);
}
inline const ::carbon::frontend::jobs::Job& UploadJobRequest::job() const {
  // @@protoc_insertion_point(field_get:carbon.portal.jobs.UploadJobRequest.job)
  return _internal_job();
}
inline void UploadJobRequest::unsafe_arena_set_allocated_job(
    ::carbon::frontend::jobs::Job* job) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(job_);
  }
  job_ = job;
  if (job) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.jobs.UploadJobRequest.job)
}
inline ::carbon::frontend::jobs::Job* UploadJobRequest::release_job() {
  
  ::carbon::frontend::jobs::Job* temp = job_;
  job_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::jobs::Job* UploadJobRequest::unsafe_arena_release_job() {
  // @@protoc_insertion_point(field_release:carbon.portal.jobs.UploadJobRequest.job)
  
  ::carbon::frontend::jobs::Job* temp = job_;
  job_ = nullptr;
  return temp;
}
inline ::carbon::frontend::jobs::Job* UploadJobRequest::_internal_mutable_job() {
  
  if (job_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::jobs::Job>(GetArenaForAllocation());
    job_ = p;
  }
  return job_;
}
inline ::carbon::frontend::jobs::Job* UploadJobRequest::mutable_job() {
  ::carbon::frontend::jobs::Job* _msg = _internal_mutable_job();
  // @@protoc_insertion_point(field_mutable:carbon.portal.jobs.UploadJobRequest.job)
  return _msg;
}
inline void UploadJobRequest::set_allocated_job(::carbon::frontend::jobs::Job* job) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(job_);
  }
  if (job) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(job));
    if (message_arena != submessage_arena) {
      job = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, job, submessage_arena);
    }
    
  } else {
    
  }
  job_ = job;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.jobs.UploadJobRequest.job)
}

// string robot = 2;
inline void UploadJobRequest::clear_robot() {
  robot_.ClearToEmpty();
}
inline const std::string& UploadJobRequest::robot() const {
  // @@protoc_insertion_point(field_get:carbon.portal.jobs.UploadJobRequest.robot)
  return _internal_robot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UploadJobRequest::set_robot(ArgT0&& arg0, ArgT... args) {
 
 robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.jobs.UploadJobRequest.robot)
}
inline std::string* UploadJobRequest::mutable_robot() {
  std::string* _s = _internal_mutable_robot();
  // @@protoc_insertion_point(field_mutable:carbon.portal.jobs.UploadJobRequest.robot)
  return _s;
}
inline const std::string& UploadJobRequest::_internal_robot() const {
  return robot_.Get();
}
inline void UploadJobRequest::_internal_set_robot(const std::string& value) {
  
  robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UploadJobRequest::_internal_mutable_robot() {
  
  return robot_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UploadJobRequest::release_robot() {
  // @@protoc_insertion_point(field_release:carbon.portal.jobs.UploadJobRequest.robot)
  return robot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UploadJobRequest::set_allocated_robot(std::string* robot) {
  if (robot != nullptr) {
    
  } else {
    
  }
  robot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.jobs.UploadJobRequest.robot)
}

// -------------------------------------------------------------------

// UploadJobConfigDumpRequest

// string jobId = 1;
inline void UploadJobConfigDumpRequest::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& UploadJobConfigDumpRequest::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.portal.jobs.UploadJobConfigDumpRequest.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UploadJobConfigDumpRequest::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.jobs.UploadJobConfigDumpRequest.jobId)
}
inline std::string* UploadJobConfigDumpRequest::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.portal.jobs.UploadJobConfigDumpRequest.jobId)
  return _s;
}
inline const std::string& UploadJobConfigDumpRequest::_internal_jobid() const {
  return jobid_.Get();
}
inline void UploadJobConfigDumpRequest::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UploadJobConfigDumpRequest::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UploadJobConfigDumpRequest::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.portal.jobs.UploadJobConfigDumpRequest.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UploadJobConfigDumpRequest::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.jobs.UploadJobConfigDumpRequest.jobId)
}

// .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 2;
inline bool UploadJobConfigDumpRequest::_internal_has_rootconfig() const {
  return this != internal_default_instance() && rootconfig_ != nullptr;
}
inline bool UploadJobConfigDumpRequest::has_rootconfig() const {
  return _internal_has_rootconfig();
}
inline const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& UploadJobConfigDumpRequest::_internal_rootconfig() const {
  const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* p = rootconfig_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot&>(
      ::carbon::frontend::weeding_diagnostics::_ConfigNodeSnapshot_default_instance_);
}
inline const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& UploadJobConfigDumpRequest::rootconfig() const {
  // @@protoc_insertion_point(field_get:carbon.portal.jobs.UploadJobConfigDumpRequest.rootConfig)
  return _internal_rootconfig();
}
inline void UploadJobConfigDumpRequest::unsafe_arena_set_allocated_rootconfig(
    ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rootconfig_);
  }
  rootconfig_ = rootconfig;
  if (rootconfig) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.jobs.UploadJobConfigDumpRequest.rootConfig)
}
inline ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* UploadJobConfigDumpRequest::release_rootconfig() {
  
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* temp = rootconfig_;
  rootconfig_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* UploadJobConfigDumpRequest::unsafe_arena_release_rootconfig() {
  // @@protoc_insertion_point(field_release:carbon.portal.jobs.UploadJobConfigDumpRequest.rootConfig)
  
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* temp = rootconfig_;
  rootconfig_ = nullptr;
  return temp;
}
inline ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* UploadJobConfigDumpRequest::_internal_mutable_rootconfig() {
  
  if (rootconfig_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot>(GetArenaForAllocation());
    rootconfig_ = p;
  }
  return rootconfig_;
}
inline ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* UploadJobConfigDumpRequest::mutable_rootconfig() {
  ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* _msg = _internal_mutable_rootconfig();
  // @@protoc_insertion_point(field_mutable:carbon.portal.jobs.UploadJobConfigDumpRequest.rootConfig)
  return _msg;
}
inline void UploadJobConfigDumpRequest::set_allocated_rootconfig(::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot* rootconfig) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(rootconfig_);
  }
  if (rootconfig) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rootconfig));
    if (message_arena != submessage_arena) {
      rootconfig = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rootconfig, submessage_arena);
    }
    
  } else {
    
  }
  rootconfig_ = rootconfig;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.jobs.UploadJobConfigDumpRequest.rootConfig)
}

// -------------------------------------------------------------------

// UploadJobMetricsRequest

// string jobId = 1;
inline void UploadJobMetricsRequest::clear_jobid() {
  jobid_.ClearToEmpty();
}
inline const std::string& UploadJobMetricsRequest::jobid() const {
  // @@protoc_insertion_point(field_get:carbon.portal.jobs.UploadJobMetricsRequest.jobId)
  return _internal_jobid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UploadJobMetricsRequest::set_jobid(ArgT0&& arg0, ArgT... args) {
 
 jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.jobs.UploadJobMetricsRequest.jobId)
}
inline std::string* UploadJobMetricsRequest::mutable_jobid() {
  std::string* _s = _internal_mutable_jobid();
  // @@protoc_insertion_point(field_mutable:carbon.portal.jobs.UploadJobMetricsRequest.jobId)
  return _s;
}
inline const std::string& UploadJobMetricsRequest::_internal_jobid() const {
  return jobid_.Get();
}
inline void UploadJobMetricsRequest::_internal_set_jobid(const std::string& value) {
  
  jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UploadJobMetricsRequest::_internal_mutable_jobid() {
  
  return jobid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UploadJobMetricsRequest::release_jobid() {
  // @@protoc_insertion_point(field_release:carbon.portal.jobs.UploadJobMetricsRequest.jobId)
  return jobid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UploadJobMetricsRequest::set_allocated_jobid(std::string* jobid) {
  if (jobid != nullptr) {
    
  } else {
    
  }
  jobid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), jobid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (jobid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.jobs.UploadJobMetricsRequest.jobId)
}

// .metrics_aggregator.Metrics jobMetrics = 2;
inline bool UploadJobMetricsRequest::_internal_has_jobmetrics() const {
  return this != internal_default_instance() && jobmetrics_ != nullptr;
}
inline bool UploadJobMetricsRequest::has_jobmetrics() const {
  return _internal_has_jobmetrics();
}
inline const ::metrics_aggregator::Metrics& UploadJobMetricsRequest::_internal_jobmetrics() const {
  const ::metrics_aggregator::Metrics* p = jobmetrics_;
  return p != nullptr ? *p : reinterpret_cast<const ::metrics_aggregator::Metrics&>(
      ::metrics_aggregator::_Metrics_default_instance_);
}
inline const ::metrics_aggregator::Metrics& UploadJobMetricsRequest::jobmetrics() const {
  // @@protoc_insertion_point(field_get:carbon.portal.jobs.UploadJobMetricsRequest.jobMetrics)
  return _internal_jobmetrics();
}
inline void UploadJobMetricsRequest::unsafe_arena_set_allocated_jobmetrics(
    ::metrics_aggregator::Metrics* jobmetrics) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(jobmetrics_);
  }
  jobmetrics_ = jobmetrics;
  if (jobmetrics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.jobs.UploadJobMetricsRequest.jobMetrics)
}
inline ::metrics_aggregator::Metrics* UploadJobMetricsRequest::release_jobmetrics() {
  
  ::metrics_aggregator::Metrics* temp = jobmetrics_;
  jobmetrics_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metrics_aggregator::Metrics* UploadJobMetricsRequest::unsafe_arena_release_jobmetrics() {
  // @@protoc_insertion_point(field_release:carbon.portal.jobs.UploadJobMetricsRequest.jobMetrics)
  
  ::metrics_aggregator::Metrics* temp = jobmetrics_;
  jobmetrics_ = nullptr;
  return temp;
}
inline ::metrics_aggregator::Metrics* UploadJobMetricsRequest::_internal_mutable_jobmetrics() {
  
  if (jobmetrics_ == nullptr) {
    auto* p = CreateMaybeMessage<::metrics_aggregator::Metrics>(GetArenaForAllocation());
    jobmetrics_ = p;
  }
  return jobmetrics_;
}
inline ::metrics_aggregator::Metrics* UploadJobMetricsRequest::mutable_jobmetrics() {
  ::metrics_aggregator::Metrics* _msg = _internal_mutable_jobmetrics();
  // @@protoc_insertion_point(field_mutable:carbon.portal.jobs.UploadJobMetricsRequest.jobMetrics)
  return _msg;
}
inline void UploadJobMetricsRequest::set_allocated_jobmetrics(::metrics_aggregator::Metrics* jobmetrics) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(jobmetrics_);
  }
  if (jobmetrics) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(jobmetrics));
    if (message_arena != submessage_arena) {
      jobmetrics = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, jobmetrics, submessage_arena);
    }
    
  } else {
    
  }
  jobmetrics_ = jobmetrics;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.jobs.UploadJobMetricsRequest.jobMetrics)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace jobs
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fjobs_5fportal_2eproto

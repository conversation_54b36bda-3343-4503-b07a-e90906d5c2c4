// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/model_history_sync.proto

#include "portal/proto/model_history_sync.pb.h"
#include "portal/proto/model_history_sync.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace model_history {

static const char* ModelHistorySyncService_method_names[] = {
  "/carbon.portal.model_history.ModelHistorySyncService/UploadModelEvents",
};

std::unique_ptr< ModelHistorySyncService::Stub> ModelHistorySyncService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ModelHistorySyncService::Stub> stub(new ModelHistorySyncService::Stub(channel, options));
  return stub;
}

ModelHistorySyncService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_UploadModelEvents_(ModelHistorySyncService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ModelHistorySyncService::Stub::UploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::model_history::UploadModelEventsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UploadModelEvents_, context, request, response);
}

void ModelHistorySyncService::Stub::async::UploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::model_history::UploadModelEventsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadModelEvents_, context, request, response, std::move(f));
}

void ModelHistorySyncService::Stub::async::UploadModelEvents(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadModelEvents_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* ModelHistorySyncService::Stub::PrepareAsyncUploadModelEventsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::model_history::UploadModelEventsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UploadModelEvents_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* ModelHistorySyncService::Stub::AsyncUploadModelEventsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUploadModelEventsRaw(context, request, cq);
  result->StartCall();
  return result;
}

ModelHistorySyncService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelHistorySyncService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelHistorySyncService::Service, ::carbon::portal::model_history::UploadModelEventsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelHistorySyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::model_history::UploadModelEventsRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->UploadModelEvents(ctx, req, resp);
             }, this)));
}

ModelHistorySyncService::Service::~Service() {
}

::grpc::Status ModelHistorySyncService::Service::UploadModelEvents(::grpc::ServerContext* context, const ::carbon::portal::model_history::UploadModelEventsRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace model_history


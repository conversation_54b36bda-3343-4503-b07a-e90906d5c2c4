// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/alarm_log.proto

#include "portal/proto/alarm_log.pb.h"
#include "portal/proto/alarm_log.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace alarm_log {

static const char* PortalAlarmLogService_method_names[] = {
  "/carbon.portal.alarm_log.PortalAlarmLogService/SyncAlarms",
};

std::unique_ptr< PortalAlarmLogService::Stub> PortalAlarmLogService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< PortalAlarmLogService::Stub> stub(new PortalAlarmLogService::Stub(channel, options));
  return stub;
}

PortalAlarmLogService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_SyncAlarms_(PortalAlarmLogService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status PortalAlarmLogService::Stub::SyncAlarms(::grpc::ClientContext* context, const ::carbon::portal::alarm_log::SyncAlarmsRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::alarm_log::SyncAlarmsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SyncAlarms_, context, request, response);
}

void PortalAlarmLogService::Stub::async::SyncAlarms(::grpc::ClientContext* context, const ::carbon::portal::alarm_log::SyncAlarmsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::alarm_log::SyncAlarmsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SyncAlarms_, context, request, response, std::move(f));
}

void PortalAlarmLogService::Stub::async::SyncAlarms(::grpc::ClientContext* context, const ::carbon::portal::alarm_log::SyncAlarmsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SyncAlarms_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalAlarmLogService::Stub::PrepareAsyncSyncAlarmsRaw(::grpc::ClientContext* context, const ::carbon::portal::alarm_log::SyncAlarmsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::alarm_log::SyncAlarmsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SyncAlarms_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* PortalAlarmLogService::Stub::AsyncSyncAlarmsRaw(::grpc::ClientContext* context, const ::carbon::portal::alarm_log::SyncAlarmsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSyncAlarmsRaw(context, request, cq);
  result->StartCall();
  return result;
}

PortalAlarmLogService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PortalAlarmLogService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PortalAlarmLogService::Service, ::carbon::portal::alarm_log::SyncAlarmsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PortalAlarmLogService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::alarm_log::SyncAlarmsRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->SyncAlarms(ctx, req, resp);
             }, this)));
}

PortalAlarmLogService::Service::~Service() {
}

::grpc::Status PortalAlarmLogService::Service::SyncAlarms(::grpc::ServerContext* context, const ::carbon::portal::alarm_log::SyncAlarmsRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace alarm_log


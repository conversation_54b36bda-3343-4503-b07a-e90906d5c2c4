// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/model_history_sync.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "portal/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto;
namespace carbon {
namespace portal {
namespace model_history {
class ModelEvent;
struct ModelEventDefaultTypeInternal;
extern ModelEventDefaultTypeInternal _ModelEvent_default_instance_;
class UploadModelEventsRequest;
struct UploadModelEventsRequestDefaultTypeInternal;
extern UploadModelEventsRequestDefaultTypeInternal _UploadModelEventsRequest_default_instance_;
}  // namespace model_history
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::model_history::ModelEvent* Arena::CreateMaybeMessage<::carbon::portal::model_history::ModelEvent>(Arena*);
template<> ::carbon::portal::model_history::UploadModelEventsRequest* Arena::CreateMaybeMessage<::carbon::portal::model_history::UploadModelEventsRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace model_history {

enum ModelEventType : int {
  UNKNOWN = 0,
  ROBOT_START = 1,
  PINNED = 2,
  UNPINNED = 3,
  RECOMMENDED = 4,
  ACTIVATED = 5,
  NICKNAME_CHANGE = 6,
  NICKNAME_DELETE = 7,
  DEFAULT_PARAMETER_CHANGE = 8,
  PARAMETER_CHANGE = 9,
  ModelEventType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ModelEventType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ModelEventType_IsValid(int value);
constexpr ModelEventType ModelEventType_MIN = UNKNOWN;
constexpr ModelEventType ModelEventType_MAX = PARAMETER_CHANGE;
constexpr int ModelEventType_ARRAYSIZE = ModelEventType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ModelEventType_descriptor();
template<typename T>
inline const std::string& ModelEventType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ModelEventType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ModelEventType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ModelEventType_descriptor(), enum_t_value);
}
inline bool ModelEventType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ModelEventType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ModelEventType>(
    ModelEventType_descriptor(), name, value);
}
// ===================================================================

class ModelEvent final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.model_history.ModelEvent) */ {
 public:
  inline ModelEvent() : ModelEvent(nullptr) {}
  ~ModelEvent() override;
  explicit constexpr ModelEvent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelEvent(const ModelEvent& from);
  ModelEvent(ModelEvent&& from) noexcept
    : ModelEvent() {
    *this = ::std::move(from);
  }

  inline ModelEvent& operator=(const ModelEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelEvent& operator=(ModelEvent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelEvent& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelEvent* internal_default_instance() {
    return reinterpret_cast<const ModelEvent*>(
               &_ModelEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ModelEvent& a, ModelEvent& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelEvent* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelEvent>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelEvent& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelEvent& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelEvent* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.model_history.ModelEvent";
  }
  protected:
  explicit ModelEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelIdFieldNumber = 2,
    kModelNicknameFieldNumber = 3,
    kModelParametersFieldNumber = 4,
    kModelTypeFieldNumber = 5,
    kCropIdFieldNumber = 6,
    kJobIdFieldNumber = 7,
    kTimestampMsFieldNumber = 8,
    kTypeFieldNumber = 1,
  };
  // string model_id = 2;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // string model_nickname = 3;
  void clear_model_nickname();
  const std::string& model_nickname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_nickname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_nickname();
  PROTOBUF_NODISCARD std::string* release_model_nickname();
  void set_allocated_model_nickname(std::string* model_nickname);
  private:
  const std::string& _internal_model_nickname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_nickname(const std::string& value);
  std::string* _internal_mutable_model_nickname();
  public:

  // string model_parameters = 4;
  void clear_model_parameters();
  const std::string& model_parameters() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_parameters(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_parameters();
  PROTOBUF_NODISCARD std::string* release_model_parameters();
  void set_allocated_model_parameters(std::string* model_parameters);
  private:
  const std::string& _internal_model_parameters() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_parameters(const std::string& value);
  std::string* _internal_mutable_model_parameters();
  public:

  // string model_type = 5;
  void clear_model_type();
  const std::string& model_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_type();
  PROTOBUF_NODISCARD std::string* release_model_type();
  void set_allocated_model_type(std::string* model_type);
  private:
  const std::string& _internal_model_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_type(const std::string& value);
  std::string* _internal_mutable_model_type();
  public:

  // string crop_id = 6;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // string job_id = 7;
  void clear_job_id();
  const std::string& job_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_job_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_job_id();
  PROTOBUF_NODISCARD std::string* release_job_id();
  void set_allocated_job_id(std::string* job_id);
  private:
  const std::string& _internal_job_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_job_id(const std::string& value);
  std::string* _internal_mutable_job_id();
  public:

  // int64 timestamp_ms = 8;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // .carbon.portal.model_history.ModelEventType type = 1;
  void clear_type();
  ::carbon::portal::model_history::ModelEventType type() const;
  void set_type(::carbon::portal::model_history::ModelEventType value);
  private:
  ::carbon::portal::model_history::ModelEventType _internal_type() const;
  void _internal_set_type(::carbon::portal::model_history::ModelEventType value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.model_history.ModelEvent)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_nickname_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_parameters_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr job_id_;
  int64_t timestamp_ms_;
  int type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto;
};
// -------------------------------------------------------------------

class UploadModelEventsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.model_history.UploadModelEventsRequest) */ {
 public:
  inline UploadModelEventsRequest() : UploadModelEventsRequest(nullptr) {}
  ~UploadModelEventsRequest() override;
  explicit constexpr UploadModelEventsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UploadModelEventsRequest(const UploadModelEventsRequest& from);
  UploadModelEventsRequest(UploadModelEventsRequest&& from) noexcept
    : UploadModelEventsRequest() {
    *this = ::std::move(from);
  }

  inline UploadModelEventsRequest& operator=(const UploadModelEventsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UploadModelEventsRequest& operator=(UploadModelEventsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UploadModelEventsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UploadModelEventsRequest* internal_default_instance() {
    return reinterpret_cast<const UploadModelEventsRequest*>(
               &_UploadModelEventsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(UploadModelEventsRequest& a, UploadModelEventsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UploadModelEventsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UploadModelEventsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UploadModelEventsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UploadModelEventsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UploadModelEventsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UploadModelEventsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UploadModelEventsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.model_history.UploadModelEventsRequest";
  }
  protected:
  explicit UploadModelEventsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEventsFieldNumber = 2,
    kRobotFieldNumber = 1,
  };
  // repeated .carbon.portal.model_history.ModelEvent events = 2;
  int events_size() const;
  private:
  int _internal_events_size() const;
  public:
  void clear_events();
  ::carbon::portal::model_history::ModelEvent* mutable_events(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_history::ModelEvent >*
      mutable_events();
  private:
  const ::carbon::portal::model_history::ModelEvent& _internal_events(int index) const;
  ::carbon::portal::model_history::ModelEvent* _internal_add_events();
  public:
  const ::carbon::portal::model_history::ModelEvent& events(int index) const;
  ::carbon::portal::model_history::ModelEvent* add_events();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_history::ModelEvent >&
      events() const;

  // string robot = 1;
  void clear_robot();
  const std::string& robot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot();
  PROTOBUF_NODISCARD std::string* release_robot();
  void set_allocated_robot(std::string* robot);
  private:
  const std::string& _internal_robot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot(const std::string& value);
  std::string* _internal_mutable_robot();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.model_history.UploadModelEventsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_history::ModelEvent > events_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ModelEvent

// .carbon.portal.model_history.ModelEventType type = 1;
inline void ModelEvent::clear_type() {
  type_ = 0;
}
inline ::carbon::portal::model_history::ModelEventType ModelEvent::_internal_type() const {
  return static_cast< ::carbon::portal::model_history::ModelEventType >(type_);
}
inline ::carbon::portal::model_history::ModelEventType ModelEvent::type() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.ModelEvent.type)
  return _internal_type();
}
inline void ModelEvent::_internal_set_type(::carbon::portal::model_history::ModelEventType value) {
  
  type_ = value;
}
inline void ModelEvent::set_type(::carbon::portal::model_history::ModelEventType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:carbon.portal.model_history.ModelEvent.type)
}

// string model_id = 2;
inline void ModelEvent::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& ModelEvent::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.ModelEvent.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_history.ModelEvent.model_id)
}
inline std::string* ModelEvent::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_history.ModelEvent.model_id)
  return _s;
}
inline const std::string& ModelEvent::_internal_model_id() const {
  return model_id_.Get();
}
inline void ModelEvent::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_history.ModelEvent.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_history.ModelEvent.model_id)
}

// string model_nickname = 3;
inline void ModelEvent::clear_model_nickname() {
  model_nickname_.ClearToEmpty();
}
inline const std::string& ModelEvent::model_nickname() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.ModelEvent.model_nickname)
  return _internal_model_nickname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_model_nickname(ArgT0&& arg0, ArgT... args) {
 
 model_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_history.ModelEvent.model_nickname)
}
inline std::string* ModelEvent::mutable_model_nickname() {
  std::string* _s = _internal_mutable_model_nickname();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_history.ModelEvent.model_nickname)
  return _s;
}
inline const std::string& ModelEvent::_internal_model_nickname() const {
  return model_nickname_.Get();
}
inline void ModelEvent::_internal_set_model_nickname(const std::string& value) {
  
  model_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_model_nickname() {
  
  return model_nickname_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_model_nickname() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_history.ModelEvent.model_nickname)
  return model_nickname_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_model_nickname(std::string* model_nickname) {
  if (model_nickname != nullptr) {
    
  } else {
    
  }
  model_nickname_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_nickname,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_nickname_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_history.ModelEvent.model_nickname)
}

// string model_parameters = 4;
inline void ModelEvent::clear_model_parameters() {
  model_parameters_.ClearToEmpty();
}
inline const std::string& ModelEvent::model_parameters() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.ModelEvent.model_parameters)
  return _internal_model_parameters();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_model_parameters(ArgT0&& arg0, ArgT... args) {
 
 model_parameters_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_history.ModelEvent.model_parameters)
}
inline std::string* ModelEvent::mutable_model_parameters() {
  std::string* _s = _internal_mutable_model_parameters();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_history.ModelEvent.model_parameters)
  return _s;
}
inline const std::string& ModelEvent::_internal_model_parameters() const {
  return model_parameters_.Get();
}
inline void ModelEvent::_internal_set_model_parameters(const std::string& value) {
  
  model_parameters_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_model_parameters() {
  
  return model_parameters_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_model_parameters() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_history.ModelEvent.model_parameters)
  return model_parameters_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_model_parameters(std::string* model_parameters) {
  if (model_parameters != nullptr) {
    
  } else {
    
  }
  model_parameters_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_parameters,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_parameters_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_parameters_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_history.ModelEvent.model_parameters)
}

// string model_type = 5;
inline void ModelEvent::clear_model_type() {
  model_type_.ClearToEmpty();
}
inline const std::string& ModelEvent::model_type() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.ModelEvent.model_type)
  return _internal_model_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_model_type(ArgT0&& arg0, ArgT... args) {
 
 model_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_history.ModelEvent.model_type)
}
inline std::string* ModelEvent::mutable_model_type() {
  std::string* _s = _internal_mutable_model_type();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_history.ModelEvent.model_type)
  return _s;
}
inline const std::string& ModelEvent::_internal_model_type() const {
  return model_type_.Get();
}
inline void ModelEvent::_internal_set_model_type(const std::string& value) {
  
  model_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_model_type() {
  
  return model_type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_model_type() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_history.ModelEvent.model_type)
  return model_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_model_type(std::string* model_type) {
  if (model_type != nullptr) {
    
  } else {
    
  }
  model_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_type,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_history.ModelEvent.model_type)
}

// string crop_id = 6;
inline void ModelEvent::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& ModelEvent::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.ModelEvent.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_history.ModelEvent.crop_id)
}
inline std::string* ModelEvent::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_history.ModelEvent.crop_id)
  return _s;
}
inline const std::string& ModelEvent::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void ModelEvent::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_history.ModelEvent.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_history.ModelEvent.crop_id)
}

// string job_id = 7;
inline void ModelEvent::clear_job_id() {
  job_id_.ClearToEmpty();
}
inline const std::string& ModelEvent::job_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.ModelEvent.job_id)
  return _internal_job_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelEvent::set_job_id(ArgT0&& arg0, ArgT... args) {
 
 job_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_history.ModelEvent.job_id)
}
inline std::string* ModelEvent::mutable_job_id() {
  std::string* _s = _internal_mutable_job_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_history.ModelEvent.job_id)
  return _s;
}
inline const std::string& ModelEvent::_internal_job_id() const {
  return job_id_.Get();
}
inline void ModelEvent::_internal_set_job_id(const std::string& value) {
  
  job_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelEvent::_internal_mutable_job_id() {
  
  return job_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelEvent::release_job_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_history.ModelEvent.job_id)
  return job_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelEvent::set_allocated_job_id(std::string* job_id) {
  if (job_id != nullptr) {
    
  } else {
    
  }
  job_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), job_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (job_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    job_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_history.ModelEvent.job_id)
}

// int64 timestamp_ms = 8;
inline void ModelEvent::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t ModelEvent::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t ModelEvent::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.ModelEvent.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void ModelEvent::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void ModelEvent::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.portal.model_history.ModelEvent.timestamp_ms)
}

// -------------------------------------------------------------------

// UploadModelEventsRequest

// string robot = 1;
inline void UploadModelEventsRequest::clear_robot() {
  robot_.ClearToEmpty();
}
inline const std::string& UploadModelEventsRequest::robot() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.UploadModelEventsRequest.robot)
  return _internal_robot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UploadModelEventsRequest::set_robot(ArgT0&& arg0, ArgT... args) {
 
 robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_history.UploadModelEventsRequest.robot)
}
inline std::string* UploadModelEventsRequest::mutable_robot() {
  std::string* _s = _internal_mutable_robot();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_history.UploadModelEventsRequest.robot)
  return _s;
}
inline const std::string& UploadModelEventsRequest::_internal_robot() const {
  return robot_.Get();
}
inline void UploadModelEventsRequest::_internal_set_robot(const std::string& value) {
  
  robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UploadModelEventsRequest::_internal_mutable_robot() {
  
  return robot_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UploadModelEventsRequest::release_robot() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_history.UploadModelEventsRequest.robot)
  return robot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UploadModelEventsRequest::set_allocated_robot(std::string* robot) {
  if (robot != nullptr) {
    
  } else {
    
  }
  robot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_history.UploadModelEventsRequest.robot)
}

// repeated .carbon.portal.model_history.ModelEvent events = 2;
inline int UploadModelEventsRequest::_internal_events_size() const {
  return events_.size();
}
inline int UploadModelEventsRequest::events_size() const {
  return _internal_events_size();
}
inline void UploadModelEventsRequest::clear_events() {
  events_.Clear();
}
inline ::carbon::portal::model_history::ModelEvent* UploadModelEventsRequest::mutable_events(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_history.UploadModelEventsRequest.events)
  return events_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_history::ModelEvent >*
UploadModelEventsRequest::mutable_events() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.model_history.UploadModelEventsRequest.events)
  return &events_;
}
inline const ::carbon::portal::model_history::ModelEvent& UploadModelEventsRequest::_internal_events(int index) const {
  return events_.Get(index);
}
inline const ::carbon::portal::model_history::ModelEvent& UploadModelEventsRequest::events(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_history.UploadModelEventsRequest.events)
  return _internal_events(index);
}
inline ::carbon::portal::model_history::ModelEvent* UploadModelEventsRequest::_internal_add_events() {
  return events_.Add();
}
inline ::carbon::portal::model_history::ModelEvent* UploadModelEventsRequest::add_events() {
  ::carbon::portal::model_history::ModelEvent* _add = _internal_add_events();
  // @@protoc_insertion_point(field_add:carbon.portal.model_history.UploadModelEventsRequest.events)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_history::ModelEvent >&
UploadModelEventsRequest::events() const {
  // @@protoc_insertion_point(field_list:carbon.portal.model_history.UploadModelEventsRequest.events)
  return events_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model_history
}  // namespace portal
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::portal::model_history::ModelEventType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::portal::model_history::ModelEventType>() {
  return ::carbon::portal::model_history::ModelEventType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/remoteit.proto

#include "portal/proto/remoteit.pb.h"
#include "portal/proto/remoteit.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace remoteit {

static const char* RemoteItManager_method_names[] = {
  "/carbon.portal.remoteit.RemoteItManager/Configure",
};

std::unique_ptr< RemoteItManager::Stub> RemoteItManager::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< RemoteItManager::Stub> stub(new RemoteItManager::Stub(channel, options));
  return stub;
}

RemoteItManager::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Configure_(RemoteItManager_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status RemoteItManager::Stub::Configure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::carbon::portal::remoteit::ConfigureResult* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::remoteit::ConfigureRequest, ::carbon::portal::remoteit::ConfigureResult, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Configure_, context, request, response);
}

void RemoteItManager::Stub::async::Configure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest* request, ::carbon::portal::remoteit::ConfigureResult* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::remoteit::ConfigureRequest, ::carbon::portal::remoteit::ConfigureResult, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Configure_, context, request, response, std::move(f));
}

void RemoteItManager::Stub::async::Configure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest* request, ::carbon::portal::remoteit::ConfigureResult* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Configure_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::remoteit::ConfigureResult>* RemoteItManager::Stub::PrepareAsyncConfigureRaw(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::remoteit::ConfigureResult, ::carbon::portal::remoteit::ConfigureRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Configure_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::remoteit::ConfigureResult>* RemoteItManager::Stub::AsyncConfigureRaw(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncConfigureRaw(context, request, cq);
  result->StartCall();
  return result;
}

RemoteItManager::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RemoteItManager_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RemoteItManager::Service, ::carbon::portal::remoteit::ConfigureRequest, ::carbon::portal::remoteit::ConfigureResult, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RemoteItManager::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::remoteit::ConfigureRequest* req,
             ::carbon::portal::remoteit::ConfigureResult* resp) {
               return service->Configure(ctx, req, resp);
             }, this)));
}

RemoteItManager::Service::~Service() {
}

::grpc::Status RemoteItManager::Service::Configure(::grpc::ServerContext* context, const ::carbon::portal::remoteit::ConfigureRequest* request, ::carbon::portal::remoteit::ConfigureResult* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace remoteit


// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/alarm_log.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2falarm_5flog_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2falarm_5flog_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "portal/proto/util.pb.h"
#include "frontend/proto/alarm.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2falarm_5flog_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2falarm_5flog_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2falarm_5flog_2eproto;
namespace carbon {
namespace portal {
namespace alarm_log {
class SyncAlarmsRequest;
struct SyncAlarmsRequestDefaultTypeInternal;
extern SyncAlarmsRequestDefaultTypeInternal _SyncAlarmsRequest_default_instance_;
}  // namespace alarm_log
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::alarm_log::SyncAlarmsRequest* Arena::CreateMaybeMessage<::carbon::portal::alarm_log::SyncAlarmsRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace alarm_log {

// ===================================================================

class SyncAlarmsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.alarm_log.SyncAlarmsRequest) */ {
 public:
  inline SyncAlarmsRequest() : SyncAlarmsRequest(nullptr) {}
  ~SyncAlarmsRequest() override;
  explicit constexpr SyncAlarmsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SyncAlarmsRequest(const SyncAlarmsRequest& from);
  SyncAlarmsRequest(SyncAlarmsRequest&& from) noexcept
    : SyncAlarmsRequest() {
    *this = ::std::move(from);
  }

  inline SyncAlarmsRequest& operator=(const SyncAlarmsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SyncAlarmsRequest& operator=(SyncAlarmsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SyncAlarmsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SyncAlarmsRequest* internal_default_instance() {
    return reinterpret_cast<const SyncAlarmsRequest*>(
               &_SyncAlarmsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SyncAlarmsRequest& a, SyncAlarmsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SyncAlarmsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SyncAlarmsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SyncAlarmsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SyncAlarmsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SyncAlarmsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SyncAlarmsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SyncAlarmsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.alarm_log.SyncAlarmsRequest";
  }
  protected:
  explicit SyncAlarmsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlarmsFieldNumber = 1,
    kRobotNameFieldNumber = 2,
  };
  // repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
  int alarms_size() const;
  private:
  int _internal_alarms_size() const;
  public:
  void clear_alarms();
  ::carbon::frontend::alarm::AlarmRow* mutable_alarms(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
      mutable_alarms();
  private:
  const ::carbon::frontend::alarm::AlarmRow& _internal_alarms(int index) const;
  ::carbon::frontend::alarm::AlarmRow* _internal_add_alarms();
  public:
  const ::carbon::frontend::alarm::AlarmRow& alarms(int index) const;
  ::carbon::frontend::alarm::AlarmRow* add_alarms();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
      alarms() const;

  // string robot_name = 2;
  void clear_robot_name();
  const std::string& robot_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_name();
  PROTOBUF_NODISCARD std::string* release_robot_name();
  void set_allocated_robot_name(std::string* robot_name);
  private:
  const std::string& _internal_robot_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_name(const std::string& value);
  std::string* _internal_mutable_robot_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.alarm_log.SyncAlarmsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow > alarms_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2falarm_5flog_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SyncAlarmsRequest

// repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
inline int SyncAlarmsRequest::_internal_alarms_size() const {
  return alarms_.size();
}
inline int SyncAlarmsRequest::alarms_size() const {
  return _internal_alarms_size();
}
inline ::carbon::frontend::alarm::AlarmRow* SyncAlarmsRequest::mutable_alarms(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.alarm_log.SyncAlarmsRequest.alarms)
  return alarms_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
SyncAlarmsRequest::mutable_alarms() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.alarm_log.SyncAlarmsRequest.alarms)
  return &alarms_;
}
inline const ::carbon::frontend::alarm::AlarmRow& SyncAlarmsRequest::_internal_alarms(int index) const {
  return alarms_.Get(index);
}
inline const ::carbon::frontend::alarm::AlarmRow& SyncAlarmsRequest::alarms(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.alarm_log.SyncAlarmsRequest.alarms)
  return _internal_alarms(index);
}
inline ::carbon::frontend::alarm::AlarmRow* SyncAlarmsRequest::_internal_add_alarms() {
  return alarms_.Add();
}
inline ::carbon::frontend::alarm::AlarmRow* SyncAlarmsRequest::add_alarms() {
  ::carbon::frontend::alarm::AlarmRow* _add = _internal_add_alarms();
  // @@protoc_insertion_point(field_add:carbon.portal.alarm_log.SyncAlarmsRequest.alarms)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
SyncAlarmsRequest::alarms() const {
  // @@protoc_insertion_point(field_list:carbon.portal.alarm_log.SyncAlarmsRequest.alarms)
  return alarms_;
}

// string robot_name = 2;
inline void SyncAlarmsRequest::clear_robot_name() {
  robot_name_.ClearToEmpty();
}
inline const std::string& SyncAlarmsRequest::robot_name() const {
  // @@protoc_insertion_point(field_get:carbon.portal.alarm_log.SyncAlarmsRequest.robot_name)
  return _internal_robot_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SyncAlarmsRequest::set_robot_name(ArgT0&& arg0, ArgT... args) {
 
 robot_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.alarm_log.SyncAlarmsRequest.robot_name)
}
inline std::string* SyncAlarmsRequest::mutable_robot_name() {
  std::string* _s = _internal_mutable_robot_name();
  // @@protoc_insertion_point(field_mutable:carbon.portal.alarm_log.SyncAlarmsRequest.robot_name)
  return _s;
}
inline const std::string& SyncAlarmsRequest::_internal_robot_name() const {
  return robot_name_.Get();
}
inline void SyncAlarmsRequest::_internal_set_robot_name(const std::string& value) {
  
  robot_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SyncAlarmsRequest::_internal_mutable_robot_name() {
  
  return robot_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SyncAlarmsRequest::release_robot_name() {
  // @@protoc_insertion_point(field_release:carbon.portal.alarm_log.SyncAlarmsRequest.robot_name)
  return robot_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SyncAlarmsRequest::set_allocated_robot_name(std::string* robot_name) {
  if (robot_name != nullptr) {
    
  } else {
    
  }
  robot_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.alarm_log.SyncAlarmsRequest.robot_name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace alarm_log
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2falarm_5flog_2eproto

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.portal.proto import profile_sync_portal_pb2 as portal_dot_proto_dot_profile__sync__portal__pb2
from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2


class PortalProfileSyncServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetProfilesData = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfilesData',
                request_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetProfilesDataRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetProfilesDataResponse.FromString,
                )
        self.UploadProfile = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/UploadProfile',
                request_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.UploadProfileRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetProfile = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfile',
                request_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetProfileRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetProfileResponse.FromString,
                )
        self.DeleteProfile = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/DeleteProfile',
                request_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.DeleteProfileRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.PurgeProfile = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeProfile',
                request_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.PurgeProfileRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetSetActiveProfileCommands = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/GetSetActiveProfileCommands',
                request_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetSetActiveProfileCommandsRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetSetActiveProfileCommandsResponse.FromString,
                )
        self.PurgeSetActiveProfileCommands = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeSetActiveProfileCommands',
                request_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.PurgeSetActiveProfileCommandsRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )


class PortalProfileSyncServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetProfilesData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UploadProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PurgeProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSetActiveProfileCommands(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PurgeSetActiveProfileCommands(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PortalProfileSyncServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetProfilesData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProfilesData,
                    request_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetProfilesDataRequest.FromString,
                    response_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetProfilesDataResponse.SerializeToString,
            ),
            'UploadProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadProfile,
                    request_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.UploadProfileRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProfile,
                    request_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetProfileRequest.FromString,
                    response_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetProfileResponse.SerializeToString,
            ),
            'DeleteProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteProfile,
                    request_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.DeleteProfileRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'PurgeProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.PurgeProfile,
                    request_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.PurgeProfileRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetSetActiveProfileCommands': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSetActiveProfileCommands,
                    request_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetSetActiveProfileCommandsRequest.FromString,
                    response_serializer=portal_dot_proto_dot_profile__sync__portal__pb2.GetSetActiveProfileCommandsResponse.SerializeToString,
            ),
            'PurgeSetActiveProfileCommands': grpc.unary_unary_rpc_method_handler(
                    servicer.PurgeSetActiveProfileCommands,
                    request_deserializer=portal_dot_proto_dot_profile__sync__portal__pb2.PurgeSetActiveProfileCommandsRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.portal.profile_sync.PortalProfileSyncService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class PortalProfileSyncService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetProfilesData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfilesData',
            portal_dot_proto_dot_profile__sync__portal__pb2.GetProfilesDataRequest.SerializeToString,
            portal_dot_proto_dot_profile__sync__portal__pb2.GetProfilesDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UploadProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.profile_sync.PortalProfileSyncService/UploadProfile',
            portal_dot_proto_dot_profile__sync__portal__pb2.UploadProfileRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfile',
            portal_dot_proto_dot_profile__sync__portal__pb2.GetProfileRequest.SerializeToString,
            portal_dot_proto_dot_profile__sync__portal__pb2.GetProfileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.profile_sync.PortalProfileSyncService/DeleteProfile',
            portal_dot_proto_dot_profile__sync__portal__pb2.DeleteProfileRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def PurgeProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeProfile',
            portal_dot_proto_dot_profile__sync__portal__pb2.PurgeProfileRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSetActiveProfileCommands(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.profile_sync.PortalProfileSyncService/GetSetActiveProfileCommands',
            portal_dot_proto_dot_profile__sync__portal__pb2.GetSetActiveProfileCommandsRequest.SerializeToString,
            portal_dot_proto_dot_profile__sync__portal__pb2.GetSetActiveProfileCommandsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def PurgeSetActiveProfileCommands(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeSetActiveProfileCommands',
            portal_dot_proto_dot_profile__sync__portal__pb2.PurgeSetActiveProfileCommandsRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/remoteit.proto
#ifndef GRPC_portal_2fproto_2fremoteit_2eproto__INCLUDED
#define GRPC_portal_2fproto_2fremoteit_2eproto__INCLUDED

#include "portal/proto/remoteit.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace portal {
namespace remoteit {

class RemoteItManager final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.portal.remoteit.RemoteItManager";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Configure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::carbon::portal::remoteit::ConfigureResult* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::remoteit::ConfigureResult>> AsyncConfigure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::remoteit::ConfigureResult>>(AsyncConfigureRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::remoteit::ConfigureResult>> PrepareAsyncConfigure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::remoteit::ConfigureResult>>(PrepareAsyncConfigureRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Configure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest* request, ::carbon::portal::remoteit::ConfigureResult* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Configure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest* request, ::carbon::portal::remoteit::ConfigureResult* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::remoteit::ConfigureResult>* AsyncConfigureRaw(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::portal::remoteit::ConfigureResult>* PrepareAsyncConfigureRaw(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Configure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::carbon::portal::remoteit::ConfigureResult* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::remoteit::ConfigureResult>> AsyncConfigure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::remoteit::ConfigureResult>>(AsyncConfigureRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::remoteit::ConfigureResult>> PrepareAsyncConfigure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::portal::remoteit::ConfigureResult>>(PrepareAsyncConfigureRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Configure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest* request, ::carbon::portal::remoteit::ConfigureResult* response, std::function<void(::grpc::Status)>) override;
      void Configure(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest* request, ::carbon::portal::remoteit::ConfigureResult* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::remoteit::ConfigureResult>* AsyncConfigureRaw(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::portal::remoteit::ConfigureResult>* PrepareAsyncConfigureRaw(::grpc::ClientContext* context, const ::carbon::portal::remoteit::ConfigureRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Configure_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Configure(::grpc::ServerContext* context, const ::carbon::portal::remoteit::ConfigureRequest* request, ::carbon::portal::remoteit::ConfigureResult* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Configure : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Configure() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Configure() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Configure(::grpc::ServerContext* /*context*/, const ::carbon::portal::remoteit::ConfigureRequest* /*request*/, ::carbon::portal::remoteit::ConfigureResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestConfigure(::grpc::ServerContext* context, ::carbon::portal::remoteit::ConfigureRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::portal::remoteit::ConfigureResult>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Configure<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Configure : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Configure() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::portal::remoteit::ConfigureRequest, ::carbon::portal::remoteit::ConfigureResult>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::portal::remoteit::ConfigureRequest* request, ::carbon::portal::remoteit::ConfigureResult* response) { return this->Configure(context, request, response); }));}
    void SetMessageAllocatorFor_Configure(
        ::grpc::MessageAllocator< ::carbon::portal::remoteit::ConfigureRequest, ::carbon::portal::remoteit::ConfigureResult>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::portal::remoteit::ConfigureRequest, ::carbon::portal::remoteit::ConfigureResult>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Configure() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Configure(::grpc::ServerContext* /*context*/, const ::carbon::portal::remoteit::ConfigureRequest* /*request*/, ::carbon::portal::remoteit::ConfigureResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Configure(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::portal::remoteit::ConfigureRequest* /*request*/, ::carbon::portal::remoteit::ConfigureResult* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Configure<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Configure : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Configure() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Configure() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Configure(::grpc::ServerContext* /*context*/, const ::carbon::portal::remoteit::ConfigureRequest* /*request*/, ::carbon::portal::remoteit::ConfigureResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Configure : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Configure() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Configure() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Configure(::grpc::ServerContext* /*context*/, const ::carbon::portal::remoteit::ConfigureRequest* /*request*/, ::carbon::portal::remoteit::ConfigureResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestConfigure(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Configure : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Configure() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Configure(context, request, response); }));
    }
    ~WithRawCallbackMethod_Configure() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Configure(::grpc::ServerContext* /*context*/, const ::carbon::portal::remoteit::ConfigureRequest* /*request*/, ::carbon::portal::remoteit::ConfigureResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Configure(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Configure : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Configure() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::portal::remoteit::ConfigureRequest, ::carbon::portal::remoteit::ConfigureResult>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::portal::remoteit::ConfigureRequest, ::carbon::portal::remoteit::ConfigureResult>* streamer) {
                       return this->StreamedConfigure(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Configure() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Configure(::grpc::ServerContext* /*context*/, const ::carbon::portal::remoteit::ConfigureRequest* /*request*/, ::carbon::portal::remoteit::ConfigureResult* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedConfigure(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::portal::remoteit::ConfigureRequest,::carbon::portal::remoteit::ConfigureResult>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Configure<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Configure<Service > StreamedService;
};

}  // namespace remoteit
}  // namespace portal
}  // namespace carbon


#endif  // GRPC_portal_2fproto_2fremoteit_2eproto__INCLUDED

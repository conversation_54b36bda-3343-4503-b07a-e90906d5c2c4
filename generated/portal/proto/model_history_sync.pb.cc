// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/model_history_sync.proto

#include "portal/proto/model_history_sync.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace model_history {
constexpr ModelEvent::ModelEvent(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_nickname_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_parameters_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_type_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , job_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0})
  , type_(0)
{}
struct ModelEventDefaultTypeInternal {
  constexpr ModelEventDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelEventDefaultTypeInternal() {}
  union {
    ModelEvent _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelEventDefaultTypeInternal _ModelEvent_default_instance_;
constexpr UploadModelEventsRequest::UploadModelEventsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : events_()
  , robot_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct UploadModelEventsRequestDefaultTypeInternal {
  constexpr UploadModelEventsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UploadModelEventsRequestDefaultTypeInternal() {}
  union {
    UploadModelEventsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UploadModelEventsRequestDefaultTypeInternal _UploadModelEventsRequest_default_instance_;
}  // namespace model_history
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto[2];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::ModelEvent, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::ModelEvent, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::ModelEvent, model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::ModelEvent, model_nickname_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::ModelEvent, model_parameters_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::ModelEvent, model_type_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::ModelEvent, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::ModelEvent, job_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::ModelEvent, timestamp_ms_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::UploadModelEventsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::UploadModelEventsRequest, robot_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::model_history::UploadModelEventsRequest, events_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::model_history::ModelEvent)},
  { 14, -1, -1, sizeof(::carbon::portal::model_history::UploadModelEventsRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::model_history::_ModelEvent_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::model_history::_UploadModelEventsRequest_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n%portal/proto/model_history_sync.proto\022"
  "\033carbon.portal.model_history\032\027portal/pro"
  "to/util.proto\"\326\001\n\nModelEvent\0229\n\004type\030\001 \001"
  "(\0162+.carbon.portal.model_history.ModelEv"
  "entType\022\020\n\010model_id\030\002 \001(\t\022\026\n\016model_nickn"
  "ame\030\003 \001(\t\022\030\n\020model_parameters\030\004 \001(\t\022\022\n\nm"
  "odel_type\030\005 \001(\t\022\017\n\007crop_id\030\006 \001(\t\022\016\n\006job_"
  "id\030\007 \001(\t\022\024\n\014timestamp_ms\030\010 \001(\003\"b\n\030Upload"
  "ModelEventsRequest\022\r\n\005robot\030\001 \001(\t\0227\n\006eve"
  "nts\030\002 \003(\0132\'.carbon.portal.model_history."
  "ModelEvent*\306\001\n\016ModelEventType\022\013\n\007UNKNOWN"
  "\020\000\022\017\n\013ROBOT_START\020\001\022\n\n\006PINNED\020\002\022\014\n\010UNPIN"
  "NED\020\003\022\017\n\013RECOMMENDED\020\004\022\r\n\tACTIVATED\020\005\022\023\n"
  "\017NICKNAME_CHANGE\020\006\022\023\n\017NICKNAME_DELETE\020\007\022"
  "\034\n\030DEFAULT_PARAMETER_CHANGE\020\010\022\024\n\020PARAMET"
  "ER_CHANGE\020\t2\200\001\n\027ModelHistorySyncService\022"
  "e\n\021UploadModelEvents\0225.carbon.portal.mod"
  "el_history.UploadModelEventsRequest\032\031.ca"
  "rbon.portal.util.EmptyB\016Z\014proto/portalb\006"
  "proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto_deps[1] = {
  &::descriptor_table_portal_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto = {
  false, false, 766, descriptor_table_protodef_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto, "portal/proto/model_history_sync.proto", 
  &descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto_once, descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto_deps, 1, 2,
  schemas, file_default_instances, TableStruct_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto::offsets,
  file_level_metadata_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto, file_level_enum_descriptors_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto, file_level_service_descriptors_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto(&descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto);
namespace carbon {
namespace portal {
namespace model_history {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ModelEventType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto);
  return file_level_enum_descriptors_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto[0];
}
bool ModelEventType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class ModelEvent::_Internal {
 public:
};

ModelEvent::ModelEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.model_history.ModelEvent)
}
ModelEvent::ModelEvent(const ModelEvent& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  model_nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_nickname().empty()) {
    model_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_nickname(), 
      GetArenaForAllocation());
  }
  model_parameters_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_parameters_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_parameters().empty()) {
    model_parameters_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_parameters(), 
      GetArenaForAllocation());
  }
  model_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_type().empty()) {
    model_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_type(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  job_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    job_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_job_id().empty()) {
    job_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_job_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(type_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.model_history.ModelEvent)
}

inline void ModelEvent::SharedCtor() {
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_parameters_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_parameters_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
job_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  job_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(type_));
}

ModelEvent::~ModelEvent() {
  // @@protoc_insertion_point(destructor:carbon.portal.model_history.ModelEvent)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelEvent::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_nickname_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_parameters_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  job_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ModelEvent::ArenaDtor(void* object) {
  ModelEvent* _this = reinterpret_cast< ModelEvent* >(object);
  (void)_this;
}
void ModelEvent::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelEvent::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelEvent::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.model_history.ModelEvent)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_id_.ClearToEmpty();
  model_nickname_.ClearToEmpty();
  model_parameters_.ClearToEmpty();
  model_type_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  job_id_.ClearToEmpty();
  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelEvent::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.portal.model_history.ModelEventType type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::carbon::portal::model_history::ModelEventType>(val));
        } else
          goto handle_unusual;
        continue;
      // string model_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_history.ModelEvent.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_nickname = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_model_nickname();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_history.ModelEvent.model_nickname"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_parameters = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_model_parameters();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_history.ModelEvent.model_parameters"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_type = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_model_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_history.ModelEvent.model_type"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_history.ModelEvent.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string job_id = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_job_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_history.ModelEvent.job_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 timestamp_ms = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelEvent::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.model_history.ModelEvent)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.portal.model_history.ModelEventType type = 1;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // string model_id = 2;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_history.ModelEvent.model_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_model_id(), target);
  }

  // string model_nickname = 3;
  if (!this->_internal_model_nickname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_nickname().data(), static_cast<int>(this->_internal_model_nickname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_history.ModelEvent.model_nickname");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_model_nickname(), target);
  }

  // string model_parameters = 4;
  if (!this->_internal_model_parameters().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_parameters().data(), static_cast<int>(this->_internal_model_parameters().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_history.ModelEvent.model_parameters");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_model_parameters(), target);
  }

  // string model_type = 5;
  if (!this->_internal_model_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_type().data(), static_cast<int>(this->_internal_model_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_history.ModelEvent.model_type");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_model_type(), target);
  }

  // string crop_id = 6;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_history.ModelEvent.crop_id");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_crop_id(), target);
  }

  // string job_id = 7;
  if (!this->_internal_job_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_job_id().data(), static_cast<int>(this->_internal_job_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_history.ModelEvent.job_id");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_job_id(), target);
  }

  // int64 timestamp_ms = 8;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(8, this->_internal_timestamp_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.model_history.ModelEvent)
  return target;
}

size_t ModelEvent::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.model_history.ModelEvent)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string model_id = 2;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  // string model_nickname = 3;
  if (!this->_internal_model_nickname().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_nickname());
  }

  // string model_parameters = 4;
  if (!this->_internal_model_parameters().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_parameters());
  }

  // string model_type = 5;
  if (!this->_internal_model_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_type());
  }

  // string crop_id = 6;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // string job_id = 7;
  if (!this->_internal_job_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_job_id());
  }

  // int64 timestamp_ms = 8;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // .carbon.portal.model_history.ModelEventType type = 1;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelEvent::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelEvent::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelEvent::GetClassData() const { return &_class_data_; }

void ModelEvent::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelEvent *>(to)->MergeFrom(
      static_cast<const ModelEvent &>(from));
}


void ModelEvent::MergeFrom(const ModelEvent& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.model_history.ModelEvent)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  if (!from._internal_model_nickname().empty()) {
    _internal_set_model_nickname(from._internal_model_nickname());
  }
  if (!from._internal_model_parameters().empty()) {
    _internal_set_model_parameters(from._internal_model_parameters());
  }
  if (!from._internal_model_type().empty()) {
    _internal_set_model_type(from._internal_model_type());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (!from._internal_job_id().empty()) {
    _internal_set_job_id(from._internal_job_id());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_type() != 0) {
    _internal_set_type(from._internal_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelEvent::CopyFrom(const ModelEvent& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.model_history.ModelEvent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelEvent::IsInitialized() const {
  return true;
}

void ModelEvent::InternalSwap(ModelEvent* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_nickname_, lhs_arena,
      &other->model_nickname_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_parameters_, lhs_arena,
      &other->model_parameters_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_type_, lhs_arena,
      &other->model_type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &job_id_, lhs_arena,
      &other->job_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ModelEvent, type_)
      + sizeof(ModelEvent::type_)
      - PROTOBUF_FIELD_OFFSET(ModelEvent, timestamp_ms_)>(
          reinterpret_cast<char*>(&timestamp_ms_),
          reinterpret_cast<char*>(&other->timestamp_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelEvent::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto_getter, &descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto_once,
      file_level_metadata_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto[0]);
}

// ===================================================================

class UploadModelEventsRequest::_Internal {
 public:
};

UploadModelEventsRequest::UploadModelEventsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  events_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.model_history.UploadModelEventsRequest)
}
UploadModelEventsRequest::UploadModelEventsRequest(const UploadModelEventsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      events_(from.events_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot().empty()) {
    robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.model_history.UploadModelEventsRequest)
}

inline void UploadModelEventsRequest::SharedCtor() {
robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

UploadModelEventsRequest::~UploadModelEventsRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.model_history.UploadModelEventsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UploadModelEventsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void UploadModelEventsRequest::ArenaDtor(void* object) {
  UploadModelEventsRequest* _this = reinterpret_cast< UploadModelEventsRequest* >(object);
  (void)_this;
}
void UploadModelEventsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UploadModelEventsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UploadModelEventsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.model_history.UploadModelEventsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  events_.Clear();
  robot_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UploadModelEventsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string robot = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_robot();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.model_history.UploadModelEventsRequest.robot"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.portal.model_history.ModelEvent events = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_events(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UploadModelEventsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.model_history.UploadModelEventsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot().data(), static_cast<int>(this->_internal_robot().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.model_history.UploadModelEventsRequest.robot");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_robot(), target);
  }

  // repeated .carbon.portal.model_history.ModelEvent events = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_events_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_events(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.model_history.UploadModelEventsRequest)
  return target;
}

size_t UploadModelEventsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.model_history.UploadModelEventsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.model_history.ModelEvent events = 2;
  total_size += 1UL * this->_internal_events_size();
  for (const auto& msg : this->events_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UploadModelEventsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UploadModelEventsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UploadModelEventsRequest::GetClassData() const { return &_class_data_; }

void UploadModelEventsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UploadModelEventsRequest *>(to)->MergeFrom(
      static_cast<const UploadModelEventsRequest &>(from));
}


void UploadModelEventsRequest::MergeFrom(const UploadModelEventsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.model_history.UploadModelEventsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  events_.MergeFrom(from.events_);
  if (!from._internal_robot().empty()) {
    _internal_set_robot(from._internal_robot());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UploadModelEventsRequest::CopyFrom(const UploadModelEventsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.model_history.UploadModelEventsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UploadModelEventsRequest::IsInitialized() const {
  return true;
}

void UploadModelEventsRequest::InternalSwap(UploadModelEventsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  events_.InternalSwap(&other->events_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_, lhs_arena,
      &other->robot_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata UploadModelEventsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto_getter, &descriptor_table_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto_once,
      file_level_metadata_portal_2fproto_2fmodel_5fhistory_5fsync_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace model_history
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::model_history::ModelEvent* Arena::CreateMaybeMessage< ::carbon::portal::model_history::ModelEvent >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::model_history::ModelEvent >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::model_history::UploadModelEventsRequest* Arena::CreateMaybeMessage< ::carbon::portal::model_history::UploadModelEventsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::model_history::UploadModelEventsRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

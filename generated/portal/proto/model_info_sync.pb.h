// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/model_info_sync.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fmodel_5finfo_5fsync_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fmodel_5finfo_5fsync_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "portal/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2fmodel_5finfo_5fsync_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2fmodel_5finfo_5fsync_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fmodel_5finfo_5fsync_2eproto;
namespace carbon {
namespace portal {
namespace model_info {
class GetRenameModelCommandsRequest;
struct GetRenameModelCommandsRequestDefaultTypeInternal;
extern GetRenameModelCommandsRequestDefaultTypeInternal _GetRenameModelCommandsRequest_default_instance_;
class GetRenameModelCommandsResponse;
struct GetRenameModelCommandsResponseDefaultTypeInternal;
extern GetRenameModelCommandsResponseDefaultTypeInternal _GetRenameModelCommandsResponse_default_instance_;
class ModelInfo;
struct ModelInfoDefaultTypeInternal;
extern ModelInfoDefaultTypeInternal _ModelInfo_default_instance_;
class PurgeRenameModelCommandsRequest;
struct PurgeRenameModelCommandsRequestDefaultTypeInternal;
extern PurgeRenameModelCommandsRequestDefaultTypeInternal _PurgeRenameModelCommandsRequest_default_instance_;
class RenameModelCommand;
struct RenameModelCommandDefaultTypeInternal;
extern RenameModelCommandDefaultTypeInternal _RenameModelCommand_default_instance_;
class UploadModelInfosRequest;
struct UploadModelInfosRequestDefaultTypeInternal;
extern UploadModelInfosRequestDefaultTypeInternal _UploadModelInfosRequest_default_instance_;
}  // namespace model_info
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::model_info::GetRenameModelCommandsRequest* Arena::CreateMaybeMessage<::carbon::portal::model_info::GetRenameModelCommandsRequest>(Arena*);
template<> ::carbon::portal::model_info::GetRenameModelCommandsResponse* Arena::CreateMaybeMessage<::carbon::portal::model_info::GetRenameModelCommandsResponse>(Arena*);
template<> ::carbon::portal::model_info::ModelInfo* Arena::CreateMaybeMessage<::carbon::portal::model_info::ModelInfo>(Arena*);
template<> ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* Arena::CreateMaybeMessage<::carbon::portal::model_info::PurgeRenameModelCommandsRequest>(Arena*);
template<> ::carbon::portal::model_info::RenameModelCommand* Arena::CreateMaybeMessage<::carbon::portal::model_info::RenameModelCommand>(Arena*);
template<> ::carbon::portal::model_info::UploadModelInfosRequest* Arena::CreateMaybeMessage<::carbon::portal::model_info::UploadModelInfosRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace model_info {

// ===================================================================

class ModelInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.model_info.ModelInfo) */ {
 public:
  inline ModelInfo() : ModelInfo(nullptr) {}
  ~ModelInfo() override;
  explicit constexpr ModelInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelInfo(const ModelInfo& from);
  ModelInfo(ModelInfo&& from) noexcept
    : ModelInfo() {
    *this = ::std::move(from);
  }

  inline ModelInfo& operator=(const ModelInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelInfo& operator=(ModelInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelInfo* internal_default_instance() {
    return reinterpret_cast<const ModelInfo*>(
               &_ModelInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ModelInfo& a, ModelInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.model_info.ModelInfo";
  }
  protected:
  explicit ModelInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropIdsFieldNumber = 2,
    kModelIdFieldNumber = 1,
    kNicknameFieldNumber = 3,
  };
  // repeated string crop_ids = 2;
  int crop_ids_size() const;
  private:
  int _internal_crop_ids_size() const;
  public:
  void clear_crop_ids();
  const std::string& crop_ids(int index) const;
  std::string* mutable_crop_ids(int index);
  void set_crop_ids(int index, const std::string& value);
  void set_crop_ids(int index, std::string&& value);
  void set_crop_ids(int index, const char* value);
  void set_crop_ids(int index, const char* value, size_t size);
  std::string* add_crop_ids();
  void add_crop_ids(const std::string& value);
  void add_crop_ids(std::string&& value);
  void add_crop_ids(const char* value);
  void add_crop_ids(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& crop_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_crop_ids();
  private:
  const std::string& _internal_crop_ids(int index) const;
  std::string* _internal_add_crop_ids();
  public:

  // string model_id = 1;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // string nickname = 3;
  void clear_nickname();
  const std::string& nickname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_nickname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_nickname();
  PROTOBUF_NODISCARD std::string* release_nickname();
  void set_allocated_nickname(std::string* nickname);
  private:
  const std::string& _internal_nickname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_nickname(const std::string& value);
  std::string* _internal_mutable_nickname();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.model_info.ModelInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> crop_ids_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr nickname_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmodel_5finfo_5fsync_2eproto;
};
// -------------------------------------------------------------------

class UploadModelInfosRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.model_info.UploadModelInfosRequest) */ {
 public:
  inline UploadModelInfosRequest() : UploadModelInfosRequest(nullptr) {}
  ~UploadModelInfosRequest() override;
  explicit constexpr UploadModelInfosRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UploadModelInfosRequest(const UploadModelInfosRequest& from);
  UploadModelInfosRequest(UploadModelInfosRequest&& from) noexcept
    : UploadModelInfosRequest() {
    *this = ::std::move(from);
  }

  inline UploadModelInfosRequest& operator=(const UploadModelInfosRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UploadModelInfosRequest& operator=(UploadModelInfosRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UploadModelInfosRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UploadModelInfosRequest* internal_default_instance() {
    return reinterpret_cast<const UploadModelInfosRequest*>(
               &_UploadModelInfosRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(UploadModelInfosRequest& a, UploadModelInfosRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UploadModelInfosRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UploadModelInfosRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UploadModelInfosRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UploadModelInfosRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UploadModelInfosRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UploadModelInfosRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UploadModelInfosRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.model_info.UploadModelInfosRequest";
  }
  protected:
  explicit UploadModelInfosRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelInfosFieldNumber = 2,
    kRobotFieldNumber = 1,
  };
  // repeated .carbon.portal.model_info.ModelInfo model_infos = 2;
  int model_infos_size() const;
  private:
  int _internal_model_infos_size() const;
  public:
  void clear_model_infos();
  ::carbon::portal::model_info::ModelInfo* mutable_model_infos(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::ModelInfo >*
      mutable_model_infos();
  private:
  const ::carbon::portal::model_info::ModelInfo& _internal_model_infos(int index) const;
  ::carbon::portal::model_info::ModelInfo* _internal_add_model_infos();
  public:
  const ::carbon::portal::model_info::ModelInfo& model_infos(int index) const;
  ::carbon::portal::model_info::ModelInfo* add_model_infos();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::ModelInfo >&
      model_infos() const;

  // string robot = 1;
  void clear_robot();
  const std::string& robot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot();
  PROTOBUF_NODISCARD std::string* release_robot();
  void set_allocated_robot(std::string* robot);
  private:
  const std::string& _internal_robot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot(const std::string& value);
  std::string* _internal_mutable_robot();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.model_info.UploadModelInfosRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::ModelInfo > model_infos_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmodel_5finfo_5fsync_2eproto;
};
// -------------------------------------------------------------------

class RenameModelCommand final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.model_info.RenameModelCommand) */ {
 public:
  inline RenameModelCommand() : RenameModelCommand(nullptr) {}
  ~RenameModelCommand() override;
  explicit constexpr RenameModelCommand(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RenameModelCommand(const RenameModelCommand& from);
  RenameModelCommand(RenameModelCommand&& from) noexcept
    : RenameModelCommand() {
    *this = ::std::move(from);
  }

  inline RenameModelCommand& operator=(const RenameModelCommand& from) {
    CopyFrom(from);
    return *this;
  }
  inline RenameModelCommand& operator=(RenameModelCommand&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RenameModelCommand& default_instance() {
    return *internal_default_instance();
  }
  static inline const RenameModelCommand* internal_default_instance() {
    return reinterpret_cast<const RenameModelCommand*>(
               &_RenameModelCommand_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(RenameModelCommand& a, RenameModelCommand& b) {
    a.Swap(&b);
  }
  inline void Swap(RenameModelCommand* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RenameModelCommand* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RenameModelCommand* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RenameModelCommand>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RenameModelCommand& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RenameModelCommand& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RenameModelCommand* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.model_info.RenameModelCommand";
  }
  protected:
  explicit RenameModelCommand(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelIdFieldNumber = 1,
    kNewNicknameFieldNumber = 2,
  };
  // string model_id = 1;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // string new_nickname = 2;
  void clear_new_nickname();
  const std::string& new_nickname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_new_nickname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_new_nickname();
  PROTOBUF_NODISCARD std::string* release_new_nickname();
  void set_allocated_new_nickname(std::string* new_nickname);
  private:
  const std::string& _internal_new_nickname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_new_nickname(const std::string& value);
  std::string* _internal_mutable_new_nickname();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.model_info.RenameModelCommand)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr new_nickname_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmodel_5finfo_5fsync_2eproto;
};
// -------------------------------------------------------------------

class GetRenameModelCommandsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.model_info.GetRenameModelCommandsRequest) */ {
 public:
  inline GetRenameModelCommandsRequest() : GetRenameModelCommandsRequest(nullptr) {}
  ~GetRenameModelCommandsRequest() override;
  explicit constexpr GetRenameModelCommandsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetRenameModelCommandsRequest(const GetRenameModelCommandsRequest& from);
  GetRenameModelCommandsRequest(GetRenameModelCommandsRequest&& from) noexcept
    : GetRenameModelCommandsRequest() {
    *this = ::std::move(from);
  }

  inline GetRenameModelCommandsRequest& operator=(const GetRenameModelCommandsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetRenameModelCommandsRequest& operator=(GetRenameModelCommandsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetRenameModelCommandsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetRenameModelCommandsRequest* internal_default_instance() {
    return reinterpret_cast<const GetRenameModelCommandsRequest*>(
               &_GetRenameModelCommandsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetRenameModelCommandsRequest& a, GetRenameModelCommandsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetRenameModelCommandsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetRenameModelCommandsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetRenameModelCommandsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetRenameModelCommandsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetRenameModelCommandsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetRenameModelCommandsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetRenameModelCommandsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.model_info.GetRenameModelCommandsRequest";
  }
  protected:
  explicit GetRenameModelCommandsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotFieldNumber = 1,
  };
  // string robot = 1;
  void clear_robot();
  const std::string& robot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot();
  PROTOBUF_NODISCARD std::string* release_robot();
  void set_allocated_robot(std::string* robot);
  private:
  const std::string& _internal_robot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot(const std::string& value);
  std::string* _internal_mutable_robot();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.model_info.GetRenameModelCommandsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmodel_5finfo_5fsync_2eproto;
};
// -------------------------------------------------------------------

class GetRenameModelCommandsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.model_info.GetRenameModelCommandsResponse) */ {
 public:
  inline GetRenameModelCommandsResponse() : GetRenameModelCommandsResponse(nullptr) {}
  ~GetRenameModelCommandsResponse() override;
  explicit constexpr GetRenameModelCommandsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetRenameModelCommandsResponse(const GetRenameModelCommandsResponse& from);
  GetRenameModelCommandsResponse(GetRenameModelCommandsResponse&& from) noexcept
    : GetRenameModelCommandsResponse() {
    *this = ::std::move(from);
  }

  inline GetRenameModelCommandsResponse& operator=(const GetRenameModelCommandsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetRenameModelCommandsResponse& operator=(GetRenameModelCommandsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetRenameModelCommandsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetRenameModelCommandsResponse* internal_default_instance() {
    return reinterpret_cast<const GetRenameModelCommandsResponse*>(
               &_GetRenameModelCommandsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetRenameModelCommandsResponse& a, GetRenameModelCommandsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetRenameModelCommandsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetRenameModelCommandsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetRenameModelCommandsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetRenameModelCommandsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetRenameModelCommandsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetRenameModelCommandsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetRenameModelCommandsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.model_info.GetRenameModelCommandsResponse";
  }
  protected:
  explicit GetRenameModelCommandsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCommandsFieldNumber = 1,
  };
  // repeated .carbon.portal.model_info.RenameModelCommand commands = 1;
  int commands_size() const;
  private:
  int _internal_commands_size() const;
  public:
  void clear_commands();
  ::carbon::portal::model_info::RenameModelCommand* mutable_commands(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand >*
      mutable_commands();
  private:
  const ::carbon::portal::model_info::RenameModelCommand& _internal_commands(int index) const;
  ::carbon::portal::model_info::RenameModelCommand* _internal_add_commands();
  public:
  const ::carbon::portal::model_info::RenameModelCommand& commands(int index) const;
  ::carbon::portal::model_info::RenameModelCommand* add_commands();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand >&
      commands() const;

  // @@protoc_insertion_point(class_scope:carbon.portal.model_info.GetRenameModelCommandsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand > commands_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmodel_5finfo_5fsync_2eproto;
};
// -------------------------------------------------------------------

class PurgeRenameModelCommandsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.model_info.PurgeRenameModelCommandsRequest) */ {
 public:
  inline PurgeRenameModelCommandsRequest() : PurgeRenameModelCommandsRequest(nullptr) {}
  ~PurgeRenameModelCommandsRequest() override;
  explicit constexpr PurgeRenameModelCommandsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PurgeRenameModelCommandsRequest(const PurgeRenameModelCommandsRequest& from);
  PurgeRenameModelCommandsRequest(PurgeRenameModelCommandsRequest&& from) noexcept
    : PurgeRenameModelCommandsRequest() {
    *this = ::std::move(from);
  }

  inline PurgeRenameModelCommandsRequest& operator=(const PurgeRenameModelCommandsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PurgeRenameModelCommandsRequest& operator=(PurgeRenameModelCommandsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PurgeRenameModelCommandsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PurgeRenameModelCommandsRequest* internal_default_instance() {
    return reinterpret_cast<const PurgeRenameModelCommandsRequest*>(
               &_PurgeRenameModelCommandsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(PurgeRenameModelCommandsRequest& a, PurgeRenameModelCommandsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PurgeRenameModelCommandsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PurgeRenameModelCommandsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PurgeRenameModelCommandsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PurgeRenameModelCommandsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PurgeRenameModelCommandsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PurgeRenameModelCommandsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PurgeRenameModelCommandsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.model_info.PurgeRenameModelCommandsRequest";
  }
  protected:
  explicit PurgeRenameModelCommandsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCommandsFieldNumber = 2,
    kRobotFieldNumber = 1,
  };
  // repeated .carbon.portal.model_info.RenameModelCommand commands = 2;
  int commands_size() const;
  private:
  int _internal_commands_size() const;
  public:
  void clear_commands();
  ::carbon::portal::model_info::RenameModelCommand* mutable_commands(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand >*
      mutable_commands();
  private:
  const ::carbon::portal::model_info::RenameModelCommand& _internal_commands(int index) const;
  ::carbon::portal::model_info::RenameModelCommand* _internal_add_commands();
  public:
  const ::carbon::portal::model_info::RenameModelCommand& commands(int index) const;
  ::carbon::portal::model_info::RenameModelCommand* add_commands();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand >&
      commands() const;

  // string robot = 1;
  void clear_robot();
  const std::string& robot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot();
  PROTOBUF_NODISCARD std::string* release_robot();
  void set_allocated_robot(std::string* robot);
  private:
  const std::string& _internal_robot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot(const std::string& value);
  std::string* _internal_mutable_robot();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.model_info.PurgeRenameModelCommandsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand > commands_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmodel_5finfo_5fsync_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ModelInfo

// string model_id = 1;
inline void ModelInfo::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& ModelInfo::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.ModelInfo.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelInfo::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_info.ModelInfo.model_id)
}
inline std::string* ModelInfo::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.ModelInfo.model_id)
  return _s;
}
inline const std::string& ModelInfo::_internal_model_id() const {
  return model_id_.Get();
}
inline void ModelInfo::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelInfo::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelInfo::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_info.ModelInfo.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelInfo::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_info.ModelInfo.model_id)
}

// repeated string crop_ids = 2;
inline int ModelInfo::_internal_crop_ids_size() const {
  return crop_ids_.size();
}
inline int ModelInfo::crop_ids_size() const {
  return _internal_crop_ids_size();
}
inline void ModelInfo::clear_crop_ids() {
  crop_ids_.Clear();
}
inline std::string* ModelInfo::add_crop_ids() {
  std::string* _s = _internal_add_crop_ids();
  // @@protoc_insertion_point(field_add_mutable:carbon.portal.model_info.ModelInfo.crop_ids)
  return _s;
}
inline const std::string& ModelInfo::_internal_crop_ids(int index) const {
  return crop_ids_.Get(index);
}
inline const std::string& ModelInfo::crop_ids(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.ModelInfo.crop_ids)
  return _internal_crop_ids(index);
}
inline std::string* ModelInfo::mutable_crop_ids(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.ModelInfo.crop_ids)
  return crop_ids_.Mutable(index);
}
inline void ModelInfo::set_crop_ids(int index, const std::string& value) {
  crop_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.portal.model_info.ModelInfo.crop_ids)
}
inline void ModelInfo::set_crop_ids(int index, std::string&& value) {
  crop_ids_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.portal.model_info.ModelInfo.crop_ids)
}
inline void ModelInfo::set_crop_ids(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  crop_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.portal.model_info.ModelInfo.crop_ids)
}
inline void ModelInfo::set_crop_ids(int index, const char* value, size_t size) {
  crop_ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.portal.model_info.ModelInfo.crop_ids)
}
inline std::string* ModelInfo::_internal_add_crop_ids() {
  return crop_ids_.Add();
}
inline void ModelInfo::add_crop_ids(const std::string& value) {
  crop_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.portal.model_info.ModelInfo.crop_ids)
}
inline void ModelInfo::add_crop_ids(std::string&& value) {
  crop_ids_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.portal.model_info.ModelInfo.crop_ids)
}
inline void ModelInfo::add_crop_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  crop_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.portal.model_info.ModelInfo.crop_ids)
}
inline void ModelInfo::add_crop_ids(const char* value, size_t size) {
  crop_ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.portal.model_info.ModelInfo.crop_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelInfo::crop_ids() const {
  // @@protoc_insertion_point(field_list:carbon.portal.model_info.ModelInfo.crop_ids)
  return crop_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelInfo::mutable_crop_ids() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.model_info.ModelInfo.crop_ids)
  return &crop_ids_;
}

// string nickname = 3;
inline void ModelInfo::clear_nickname() {
  nickname_.ClearToEmpty();
}
inline const std::string& ModelInfo::nickname() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.ModelInfo.nickname)
  return _internal_nickname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelInfo::set_nickname(ArgT0&& arg0, ArgT... args) {
 
 nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_info.ModelInfo.nickname)
}
inline std::string* ModelInfo::mutable_nickname() {
  std::string* _s = _internal_mutable_nickname();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.ModelInfo.nickname)
  return _s;
}
inline const std::string& ModelInfo::_internal_nickname() const {
  return nickname_.Get();
}
inline void ModelInfo::_internal_set_nickname(const std::string& value) {
  
  nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelInfo::_internal_mutable_nickname() {
  
  return nickname_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelInfo::release_nickname() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_info.ModelInfo.nickname)
  return nickname_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelInfo::set_allocated_nickname(std::string* nickname) {
  if (nickname != nullptr) {
    
  } else {
    
  }
  nickname_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), nickname,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (nickname_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_info.ModelInfo.nickname)
}

// -------------------------------------------------------------------

// UploadModelInfosRequest

// string robot = 1;
inline void UploadModelInfosRequest::clear_robot() {
  robot_.ClearToEmpty();
}
inline const std::string& UploadModelInfosRequest::robot() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.UploadModelInfosRequest.robot)
  return _internal_robot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UploadModelInfosRequest::set_robot(ArgT0&& arg0, ArgT... args) {
 
 robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_info.UploadModelInfosRequest.robot)
}
inline std::string* UploadModelInfosRequest::mutable_robot() {
  std::string* _s = _internal_mutable_robot();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.UploadModelInfosRequest.robot)
  return _s;
}
inline const std::string& UploadModelInfosRequest::_internal_robot() const {
  return robot_.Get();
}
inline void UploadModelInfosRequest::_internal_set_robot(const std::string& value) {
  
  robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UploadModelInfosRequest::_internal_mutable_robot() {
  
  return robot_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UploadModelInfosRequest::release_robot() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_info.UploadModelInfosRequest.robot)
  return robot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UploadModelInfosRequest::set_allocated_robot(std::string* robot) {
  if (robot != nullptr) {
    
  } else {
    
  }
  robot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_info.UploadModelInfosRequest.robot)
}

// repeated .carbon.portal.model_info.ModelInfo model_infos = 2;
inline int UploadModelInfosRequest::_internal_model_infos_size() const {
  return model_infos_.size();
}
inline int UploadModelInfosRequest::model_infos_size() const {
  return _internal_model_infos_size();
}
inline void UploadModelInfosRequest::clear_model_infos() {
  model_infos_.Clear();
}
inline ::carbon::portal::model_info::ModelInfo* UploadModelInfosRequest::mutable_model_infos(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.UploadModelInfosRequest.model_infos)
  return model_infos_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::ModelInfo >*
UploadModelInfosRequest::mutable_model_infos() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.model_info.UploadModelInfosRequest.model_infos)
  return &model_infos_;
}
inline const ::carbon::portal::model_info::ModelInfo& UploadModelInfosRequest::_internal_model_infos(int index) const {
  return model_infos_.Get(index);
}
inline const ::carbon::portal::model_info::ModelInfo& UploadModelInfosRequest::model_infos(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.UploadModelInfosRequest.model_infos)
  return _internal_model_infos(index);
}
inline ::carbon::portal::model_info::ModelInfo* UploadModelInfosRequest::_internal_add_model_infos() {
  return model_infos_.Add();
}
inline ::carbon::portal::model_info::ModelInfo* UploadModelInfosRequest::add_model_infos() {
  ::carbon::portal::model_info::ModelInfo* _add = _internal_add_model_infos();
  // @@protoc_insertion_point(field_add:carbon.portal.model_info.UploadModelInfosRequest.model_infos)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::ModelInfo >&
UploadModelInfosRequest::model_infos() const {
  // @@protoc_insertion_point(field_list:carbon.portal.model_info.UploadModelInfosRequest.model_infos)
  return model_infos_;
}

// -------------------------------------------------------------------

// RenameModelCommand

// string model_id = 1;
inline void RenameModelCommand::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& RenameModelCommand::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.RenameModelCommand.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RenameModelCommand::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_info.RenameModelCommand.model_id)
}
inline std::string* RenameModelCommand::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.RenameModelCommand.model_id)
  return _s;
}
inline const std::string& RenameModelCommand::_internal_model_id() const {
  return model_id_.Get();
}
inline void RenameModelCommand::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RenameModelCommand::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RenameModelCommand::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_info.RenameModelCommand.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RenameModelCommand::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_info.RenameModelCommand.model_id)
}

// string new_nickname = 2;
inline void RenameModelCommand::clear_new_nickname() {
  new_nickname_.ClearToEmpty();
}
inline const std::string& RenameModelCommand::new_nickname() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.RenameModelCommand.new_nickname)
  return _internal_new_nickname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RenameModelCommand::set_new_nickname(ArgT0&& arg0, ArgT... args) {
 
 new_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_info.RenameModelCommand.new_nickname)
}
inline std::string* RenameModelCommand::mutable_new_nickname() {
  std::string* _s = _internal_mutable_new_nickname();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.RenameModelCommand.new_nickname)
  return _s;
}
inline const std::string& RenameModelCommand::_internal_new_nickname() const {
  return new_nickname_.Get();
}
inline void RenameModelCommand::_internal_set_new_nickname(const std::string& value) {
  
  new_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RenameModelCommand::_internal_mutable_new_nickname() {
  
  return new_nickname_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RenameModelCommand::release_new_nickname() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_info.RenameModelCommand.new_nickname)
  return new_nickname_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RenameModelCommand::set_allocated_new_nickname(std::string* new_nickname) {
  if (new_nickname != nullptr) {
    
  } else {
    
  }
  new_nickname_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), new_nickname,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (new_nickname_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    new_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_info.RenameModelCommand.new_nickname)
}

// -------------------------------------------------------------------

// GetRenameModelCommandsRequest

// string robot = 1;
inline void GetRenameModelCommandsRequest::clear_robot() {
  robot_.ClearToEmpty();
}
inline const std::string& GetRenameModelCommandsRequest::robot() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.GetRenameModelCommandsRequest.robot)
  return _internal_robot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetRenameModelCommandsRequest::set_robot(ArgT0&& arg0, ArgT... args) {
 
 robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_info.GetRenameModelCommandsRequest.robot)
}
inline std::string* GetRenameModelCommandsRequest::mutable_robot() {
  std::string* _s = _internal_mutable_robot();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.GetRenameModelCommandsRequest.robot)
  return _s;
}
inline const std::string& GetRenameModelCommandsRequest::_internal_robot() const {
  return robot_.Get();
}
inline void GetRenameModelCommandsRequest::_internal_set_robot(const std::string& value) {
  
  robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetRenameModelCommandsRequest::_internal_mutable_robot() {
  
  return robot_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetRenameModelCommandsRequest::release_robot() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_info.GetRenameModelCommandsRequest.robot)
  return robot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetRenameModelCommandsRequest::set_allocated_robot(std::string* robot) {
  if (robot != nullptr) {
    
  } else {
    
  }
  robot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_info.GetRenameModelCommandsRequest.robot)
}

// -------------------------------------------------------------------

// GetRenameModelCommandsResponse

// repeated .carbon.portal.model_info.RenameModelCommand commands = 1;
inline int GetRenameModelCommandsResponse::_internal_commands_size() const {
  return commands_.size();
}
inline int GetRenameModelCommandsResponse::commands_size() const {
  return _internal_commands_size();
}
inline void GetRenameModelCommandsResponse::clear_commands() {
  commands_.Clear();
}
inline ::carbon::portal::model_info::RenameModelCommand* GetRenameModelCommandsResponse::mutable_commands(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.GetRenameModelCommandsResponse.commands)
  return commands_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand >*
GetRenameModelCommandsResponse::mutable_commands() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.model_info.GetRenameModelCommandsResponse.commands)
  return &commands_;
}
inline const ::carbon::portal::model_info::RenameModelCommand& GetRenameModelCommandsResponse::_internal_commands(int index) const {
  return commands_.Get(index);
}
inline const ::carbon::portal::model_info::RenameModelCommand& GetRenameModelCommandsResponse::commands(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.GetRenameModelCommandsResponse.commands)
  return _internal_commands(index);
}
inline ::carbon::portal::model_info::RenameModelCommand* GetRenameModelCommandsResponse::_internal_add_commands() {
  return commands_.Add();
}
inline ::carbon::portal::model_info::RenameModelCommand* GetRenameModelCommandsResponse::add_commands() {
  ::carbon::portal::model_info::RenameModelCommand* _add = _internal_add_commands();
  // @@protoc_insertion_point(field_add:carbon.portal.model_info.GetRenameModelCommandsResponse.commands)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand >&
GetRenameModelCommandsResponse::commands() const {
  // @@protoc_insertion_point(field_list:carbon.portal.model_info.GetRenameModelCommandsResponse.commands)
  return commands_;
}

// -------------------------------------------------------------------

// PurgeRenameModelCommandsRequest

// string robot = 1;
inline void PurgeRenameModelCommandsRequest::clear_robot() {
  robot_.ClearToEmpty();
}
inline const std::string& PurgeRenameModelCommandsRequest::robot() const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.PurgeRenameModelCommandsRequest.robot)
  return _internal_robot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PurgeRenameModelCommandsRequest::set_robot(ArgT0&& arg0, ArgT... args) {
 
 robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.model_info.PurgeRenameModelCommandsRequest.robot)
}
inline std::string* PurgeRenameModelCommandsRequest::mutable_robot() {
  std::string* _s = _internal_mutable_robot();
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.PurgeRenameModelCommandsRequest.robot)
  return _s;
}
inline const std::string& PurgeRenameModelCommandsRequest::_internal_robot() const {
  return robot_.Get();
}
inline void PurgeRenameModelCommandsRequest::_internal_set_robot(const std::string& value) {
  
  robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PurgeRenameModelCommandsRequest::_internal_mutable_robot() {
  
  return robot_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PurgeRenameModelCommandsRequest::release_robot() {
  // @@protoc_insertion_point(field_release:carbon.portal.model_info.PurgeRenameModelCommandsRequest.robot)
  return robot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PurgeRenameModelCommandsRequest::set_allocated_robot(std::string* robot) {
  if (robot != nullptr) {
    
  } else {
    
  }
  robot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.model_info.PurgeRenameModelCommandsRequest.robot)
}

// repeated .carbon.portal.model_info.RenameModelCommand commands = 2;
inline int PurgeRenameModelCommandsRequest::_internal_commands_size() const {
  return commands_.size();
}
inline int PurgeRenameModelCommandsRequest::commands_size() const {
  return _internal_commands_size();
}
inline void PurgeRenameModelCommandsRequest::clear_commands() {
  commands_.Clear();
}
inline ::carbon::portal::model_info::RenameModelCommand* PurgeRenameModelCommandsRequest::mutable_commands(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.model_info.PurgeRenameModelCommandsRequest.commands)
  return commands_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand >*
PurgeRenameModelCommandsRequest::mutable_commands() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.model_info.PurgeRenameModelCommandsRequest.commands)
  return &commands_;
}
inline const ::carbon::portal::model_info::RenameModelCommand& PurgeRenameModelCommandsRequest::_internal_commands(int index) const {
  return commands_.Get(index);
}
inline const ::carbon::portal::model_info::RenameModelCommand& PurgeRenameModelCommandsRequest::commands(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.model_info.PurgeRenameModelCommandsRequest.commands)
  return _internal_commands(index);
}
inline ::carbon::portal::model_info::RenameModelCommand* PurgeRenameModelCommandsRequest::_internal_add_commands() {
  return commands_.Add();
}
inline ::carbon::portal::model_info::RenameModelCommand* PurgeRenameModelCommandsRequest::add_commands() {
  ::carbon::portal::model_info::RenameModelCommand* _add = _internal_add_commands();
  // @@protoc_insertion_point(field_add:carbon.portal.model_info.PurgeRenameModelCommandsRequest.commands)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::model_info::RenameModelCommand >&
PurgeRenameModelCommandsRequest::commands() const {
  // @@protoc_insertion_point(field_list:carbon.portal.model_info.PurgeRenameModelCommandsRequest.commands)
  return commands_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model_info
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fmodel_5finfo_5fsync_2eproto

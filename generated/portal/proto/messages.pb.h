// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/messages.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fmessages_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fmessages_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "portal/proto/db.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2fmessages_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2fmessages_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fmessages_2eproto;
namespace carbon {
namespace portal {
namespace messages {
class Message;
struct MessageDefaultTypeInternal;
extern MessageDefaultTypeInternal _Message_default_instance_;
class MessagesResponse;
struct MessagesResponseDefaultTypeInternal;
extern MessagesResponseDefaultTypeInternal _MessagesResponse_default_instance_;
}  // namespace messages
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::messages::Message* Arena::CreateMaybeMessage<::carbon::portal::messages::Message>(Arena*);
template<> ::carbon::portal::messages::MessagesResponse* Arena::CreateMaybeMessage<::carbon::portal::messages::MessagesResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace messages {

// ===================================================================

class Message final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.messages.Message) */ {
 public:
  inline Message() : Message(nullptr) {}
  ~Message() override;
  explicit constexpr Message(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Message(const Message& from);
  Message(Message&& from) noexcept
    : Message() {
    *this = ::std::move(from);
  }

  inline Message& operator=(const Message& from) {
    CopyFrom(from);
    return *this;
  }
  inline Message& operator=(Message&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Message& default_instance() {
    return *internal_default_instance();
  }
  static inline const Message* internal_default_instance() {
    return reinterpret_cast<const Message*>(
               &_Message_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Message& a, Message& b) {
    a.Swap(&b);
  }
  inline void Swap(Message* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Message* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Message* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Message>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Message& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Message& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Message* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.messages.Message";
  }
  protected:
  explicit Message(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kAuthorUserIdFieldNumber = 3,
    kRecipientUserIdFieldNumber = 5,
    kDbFieldNumber = 1,
    kAuthorRobotIdFieldNumber = 4,
    kRecipientCustomerIdFieldNumber = 6,
    kRecipientRobotIdFieldNumber = 7,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // string author_user_id = 3;
  void clear_author_user_id();
  const std::string& author_user_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_author_user_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_author_user_id();
  PROTOBUF_NODISCARD std::string* release_author_user_id();
  void set_allocated_author_user_id(std::string* author_user_id);
  private:
  const std::string& _internal_author_user_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_author_user_id(const std::string& value);
  std::string* _internal_mutable_author_user_id();
  public:

  // string recipient_user_id = 5;
  void clear_recipient_user_id();
  const std::string& recipient_user_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_recipient_user_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_recipient_user_id();
  PROTOBUF_NODISCARD std::string* release_recipient_user_id();
  void set_allocated_recipient_user_id(std::string* recipient_user_id);
  private:
  const std::string& _internal_recipient_user_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_recipient_user_id(const std::string& value);
  std::string* _internal_mutable_recipient_user_id();
  public:

  // .carbon.portal.db.DB db = 1;
  bool has_db() const;
  private:
  bool _internal_has_db() const;
  public:
  void clear_db();
  const ::carbon::portal::db::DB& db() const;
  PROTOBUF_NODISCARD ::carbon::portal::db::DB* release_db();
  ::carbon::portal::db::DB* mutable_db();
  void set_allocated_db(::carbon::portal::db::DB* db);
  private:
  const ::carbon::portal::db::DB& _internal_db() const;
  ::carbon::portal::db::DB* _internal_mutable_db();
  public:
  void unsafe_arena_set_allocated_db(
      ::carbon::portal::db::DB* db);
  ::carbon::portal::db::DB* unsafe_arena_release_db();

  // int64 author_robot_id = 4;
  void clear_author_robot_id();
  int64_t author_robot_id() const;
  void set_author_robot_id(int64_t value);
  private:
  int64_t _internal_author_robot_id() const;
  void _internal_set_author_robot_id(int64_t value);
  public:

  // int64 recipient_customer_id = 6;
  void clear_recipient_customer_id();
  int64_t recipient_customer_id() const;
  void set_recipient_customer_id(int64_t value);
  private:
  int64_t _internal_recipient_customer_id() const;
  void _internal_set_recipient_customer_id(int64_t value);
  public:

  // int64 recipient_robot_id = 7;
  void clear_recipient_robot_id();
  int64_t recipient_robot_id() const;
  void set_recipient_robot_id(int64_t value);
  private:
  int64_t _internal_recipient_robot_id() const;
  void _internal_set_recipient_robot_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.messages.Message)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr author_user_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr recipient_user_id_;
  ::carbon::portal::db::DB* db_;
  int64_t author_robot_id_;
  int64_t recipient_customer_id_;
  int64_t recipient_robot_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmessages_2eproto;
};
// -------------------------------------------------------------------

class MessagesResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.messages.MessagesResponse) */ {
 public:
  inline MessagesResponse() : MessagesResponse(nullptr) {}
  ~MessagesResponse() override;
  explicit constexpr MessagesResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MessagesResponse(const MessagesResponse& from);
  MessagesResponse(MessagesResponse&& from) noexcept
    : MessagesResponse() {
    *this = ::std::move(from);
  }

  inline MessagesResponse& operator=(const MessagesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MessagesResponse& operator=(MessagesResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MessagesResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const MessagesResponse* internal_default_instance() {
    return reinterpret_cast<const MessagesResponse*>(
               &_MessagesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MessagesResponse& a, MessagesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MessagesResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MessagesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MessagesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MessagesResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MessagesResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MessagesResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MessagesResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.messages.MessagesResponse";
  }
  protected:
  explicit MessagesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessagesFieldNumber = 4,
    kPageFieldNumber = 2,
    kLimitFieldNumber = 3,
  };
  // repeated .carbon.portal.messages.Message messages = 4;
  int messages_size() const;
  private:
  int _internal_messages_size() const;
  public:
  void clear_messages();
  ::carbon::portal::messages::Message* mutable_messages(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::messages::Message >*
      mutable_messages();
  private:
  const ::carbon::portal::messages::Message& _internal_messages(int index) const;
  ::carbon::portal::messages::Message* _internal_add_messages();
  public:
  const ::carbon::portal::messages::Message& messages(int index) const;
  ::carbon::portal::messages::Message* add_messages();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::messages::Message >&
      messages() const;

  // int64 page = 2;
  void clear_page();
  int64_t page() const;
  void set_page(int64_t value);
  private:
  int64_t _internal_page() const;
  void _internal_set_page(int64_t value);
  public:

  // int64 limit = 3;
  void clear_limit();
  int64_t limit() const;
  void set_limit(int64_t value);
  private:
  int64_t _internal_limit() const;
  void _internal_set_limit(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.messages.MessagesResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::messages::Message > messages_;
  int64_t page_;
  int64_t limit_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fmessages_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Message

// .carbon.portal.db.DB db = 1;
inline bool Message::_internal_has_db() const {
  return this != internal_default_instance() && db_ != nullptr;
}
inline bool Message::has_db() const {
  return _internal_has_db();
}
inline const ::carbon::portal::db::DB& Message::_internal_db() const {
  const ::carbon::portal::db::DB* p = db_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::db::DB&>(
      ::carbon::portal::db::_DB_default_instance_);
}
inline const ::carbon::portal::db::DB& Message::db() const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.Message.db)
  return _internal_db();
}
inline void Message::unsafe_arena_set_allocated_db(
    ::carbon::portal::db::DB* db) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(db_);
  }
  db_ = db;
  if (db) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.messages.Message.db)
}
inline ::carbon::portal::db::DB* Message::release_db() {
  
  ::carbon::portal::db::DB* temp = db_;
  db_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::db::DB* Message::unsafe_arena_release_db() {
  // @@protoc_insertion_point(field_release:carbon.portal.messages.Message.db)
  
  ::carbon::portal::db::DB* temp = db_;
  db_ = nullptr;
  return temp;
}
inline ::carbon::portal::db::DB* Message::_internal_mutable_db() {
  
  if (db_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::db::DB>(GetArenaForAllocation());
    db_ = p;
  }
  return db_;
}
inline ::carbon::portal::db::DB* Message::mutable_db() {
  ::carbon::portal::db::DB* _msg = _internal_mutable_db();
  // @@protoc_insertion_point(field_mutable:carbon.portal.messages.Message.db)
  return _msg;
}
inline void Message::set_allocated_db(::carbon::portal::db::DB* db) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(db_);
  }
  if (db) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(db));
    if (message_arena != submessage_arena) {
      db = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, db, submessage_arena);
    }
    
  } else {
    
  }
  db_ = db;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.messages.Message.db)
}

// string message = 2;
inline void Message::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& Message::message() const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.Message.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Message::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.messages.Message.message)
}
inline std::string* Message::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:carbon.portal.messages.Message.message)
  return _s;
}
inline const std::string& Message::_internal_message() const {
  return message_.Get();
}
inline void Message::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Message::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Message::release_message() {
  // @@protoc_insertion_point(field_release:carbon.portal.messages.Message.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Message::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.messages.Message.message)
}

// string author_user_id = 3;
inline void Message::clear_author_user_id() {
  author_user_id_.ClearToEmpty();
}
inline const std::string& Message::author_user_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.Message.author_user_id)
  return _internal_author_user_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Message::set_author_user_id(ArgT0&& arg0, ArgT... args) {
 
 author_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.messages.Message.author_user_id)
}
inline std::string* Message::mutable_author_user_id() {
  std::string* _s = _internal_mutable_author_user_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.messages.Message.author_user_id)
  return _s;
}
inline const std::string& Message::_internal_author_user_id() const {
  return author_user_id_.Get();
}
inline void Message::_internal_set_author_user_id(const std::string& value) {
  
  author_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Message::_internal_mutable_author_user_id() {
  
  return author_user_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Message::release_author_user_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.messages.Message.author_user_id)
  return author_user_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Message::set_allocated_author_user_id(std::string* author_user_id) {
  if (author_user_id != nullptr) {
    
  } else {
    
  }
  author_user_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), author_user_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (author_user_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    author_user_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.messages.Message.author_user_id)
}

// int64 author_robot_id = 4;
inline void Message::clear_author_robot_id() {
  author_robot_id_ = int64_t{0};
}
inline int64_t Message::_internal_author_robot_id() const {
  return author_robot_id_;
}
inline int64_t Message::author_robot_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.Message.author_robot_id)
  return _internal_author_robot_id();
}
inline void Message::_internal_set_author_robot_id(int64_t value) {
  
  author_robot_id_ = value;
}
inline void Message::set_author_robot_id(int64_t value) {
  _internal_set_author_robot_id(value);
  // @@protoc_insertion_point(field_set:carbon.portal.messages.Message.author_robot_id)
}

// string recipient_user_id = 5;
inline void Message::clear_recipient_user_id() {
  recipient_user_id_.ClearToEmpty();
}
inline const std::string& Message::recipient_user_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.Message.recipient_user_id)
  return _internal_recipient_user_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Message::set_recipient_user_id(ArgT0&& arg0, ArgT... args) {
 
 recipient_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.messages.Message.recipient_user_id)
}
inline std::string* Message::mutable_recipient_user_id() {
  std::string* _s = _internal_mutable_recipient_user_id();
  // @@protoc_insertion_point(field_mutable:carbon.portal.messages.Message.recipient_user_id)
  return _s;
}
inline const std::string& Message::_internal_recipient_user_id() const {
  return recipient_user_id_.Get();
}
inline void Message::_internal_set_recipient_user_id(const std::string& value) {
  
  recipient_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Message::_internal_mutable_recipient_user_id() {
  
  return recipient_user_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Message::release_recipient_user_id() {
  // @@protoc_insertion_point(field_release:carbon.portal.messages.Message.recipient_user_id)
  return recipient_user_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Message::set_allocated_recipient_user_id(std::string* recipient_user_id) {
  if (recipient_user_id != nullptr) {
    
  } else {
    
  }
  recipient_user_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), recipient_user_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (recipient_user_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    recipient_user_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.messages.Message.recipient_user_id)
}

// int64 recipient_customer_id = 6;
inline void Message::clear_recipient_customer_id() {
  recipient_customer_id_ = int64_t{0};
}
inline int64_t Message::_internal_recipient_customer_id() const {
  return recipient_customer_id_;
}
inline int64_t Message::recipient_customer_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.Message.recipient_customer_id)
  return _internal_recipient_customer_id();
}
inline void Message::_internal_set_recipient_customer_id(int64_t value) {
  
  recipient_customer_id_ = value;
}
inline void Message::set_recipient_customer_id(int64_t value) {
  _internal_set_recipient_customer_id(value);
  // @@protoc_insertion_point(field_set:carbon.portal.messages.Message.recipient_customer_id)
}

// int64 recipient_robot_id = 7;
inline void Message::clear_recipient_robot_id() {
  recipient_robot_id_ = int64_t{0};
}
inline int64_t Message::_internal_recipient_robot_id() const {
  return recipient_robot_id_;
}
inline int64_t Message::recipient_robot_id() const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.Message.recipient_robot_id)
  return _internal_recipient_robot_id();
}
inline void Message::_internal_set_recipient_robot_id(int64_t value) {
  
  recipient_robot_id_ = value;
}
inline void Message::set_recipient_robot_id(int64_t value) {
  _internal_set_recipient_robot_id(value);
  // @@protoc_insertion_point(field_set:carbon.portal.messages.Message.recipient_robot_id)
}

// -------------------------------------------------------------------

// MessagesResponse

// int64 page = 2;
inline void MessagesResponse::clear_page() {
  page_ = int64_t{0};
}
inline int64_t MessagesResponse::_internal_page() const {
  return page_;
}
inline int64_t MessagesResponse::page() const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.MessagesResponse.page)
  return _internal_page();
}
inline void MessagesResponse::_internal_set_page(int64_t value) {
  
  page_ = value;
}
inline void MessagesResponse::set_page(int64_t value) {
  _internal_set_page(value);
  // @@protoc_insertion_point(field_set:carbon.portal.messages.MessagesResponse.page)
}

// int64 limit = 3;
inline void MessagesResponse::clear_limit() {
  limit_ = int64_t{0};
}
inline int64_t MessagesResponse::_internal_limit() const {
  return limit_;
}
inline int64_t MessagesResponse::limit() const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.MessagesResponse.limit)
  return _internal_limit();
}
inline void MessagesResponse::_internal_set_limit(int64_t value) {
  
  limit_ = value;
}
inline void MessagesResponse::set_limit(int64_t value) {
  _internal_set_limit(value);
  // @@protoc_insertion_point(field_set:carbon.portal.messages.MessagesResponse.limit)
}

// repeated .carbon.portal.messages.Message messages = 4;
inline int MessagesResponse::_internal_messages_size() const {
  return messages_.size();
}
inline int MessagesResponse::messages_size() const {
  return _internal_messages_size();
}
inline void MessagesResponse::clear_messages() {
  messages_.Clear();
}
inline ::carbon::portal::messages::Message* MessagesResponse::mutable_messages(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.messages.MessagesResponse.messages)
  return messages_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::messages::Message >*
MessagesResponse::mutable_messages() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.messages.MessagesResponse.messages)
  return &messages_;
}
inline const ::carbon::portal::messages::Message& MessagesResponse::_internal_messages(int index) const {
  return messages_.Get(index);
}
inline const ::carbon::portal::messages::Message& MessagesResponse::messages(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.messages.MessagesResponse.messages)
  return _internal_messages(index);
}
inline ::carbon::portal::messages::Message* MessagesResponse::_internal_add_messages() {
  return messages_.Add();
}
inline ::carbon::portal::messages::Message* MessagesResponse::add_messages() {
  ::carbon::portal::messages::Message* _add = _internal_add_messages();
  // @@protoc_insertion_point(field_add:carbon.portal.messages.MessagesResponse.messages)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::portal::messages::Message >&
MessagesResponse::messages() const {
  // @@protoc_insertion_point(field_list:carbon.portal.messages.MessagesResponse.messages)
  return messages_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace messages
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fmessages_2eproto

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/db.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/db.proto',
  package='carbon.portal.db',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x15portal/proto/db.proto\x12\x10\x63\x61rbon.portal.db\"L\n\x02\x44\x42\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x12\n\ncreated_at\x18\x02 \x01(\x03\x12\x12\n\nupdated_at\x18\x03 \x01(\x03\x12\x12\n\ndeleted_at\x18\x04 \x01(\x03\x42\x0eZ\x0cproto/portalb\x06proto3'
)




_DB = _descriptor.Descriptor(
  name='DB',
  full_name='carbon.portal.db.DB',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.portal.db.DB.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='carbon.portal.db.DB.created_at', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='carbon.portal.db.DB.updated_at', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deleted_at', full_name='carbon.portal.db.DB.deleted_at', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=43,
  serialized_end=119,
)

DESCRIPTOR.message_types_by_name['DB'] = _DB
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DB = _reflection.GeneratedProtocolMessageType('DB', (_message.Message,), {
  'DESCRIPTOR' : _DB,
  '__module__' : 'portal.proto.db_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.db.DB)
  })
_sym_db.RegisterMessage(DB)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.portal.proto import farm_pb2 as portal_dot_proto_dot_farm__pb2


class FarmsServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetFarm = channel.unary_unary(
                '/carbon.portal.farm.FarmsService/GetFarm',
                request_serializer=portal_dot_proto_dot_farm__pb2.GetFarmRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_farm__pb2.GetFarmResponse.FromString,
                )


class FarmsServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetFarm(self, request, context):
        """Gets a farm by ID. If `if_modified_since` is present and none of the
        farm's descendant entities has been modified at a strictly later time than
        that, then the RPC will succeed with code OK and no `farm` on the
        response.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FarmsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetFarm': grpc.unary_unary_rpc_method_handler(
                    servicer.GetFarm,
                    request_deserializer=portal_dot_proto_dot_farm__pb2.GetFarmRequest.FromString,
                    response_serializer=portal_dot_proto_dot_farm__pb2.GetFarmResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.portal.farm.FarmsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class FarmsService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetFarm(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.farm.FarmsService/GetFarm',
            portal_dot_proto_dot_farm__pb2.GetFarmRequest.SerializeToString,
            portal_dot_proto_dot_farm__pb2.GetFarmResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.portal.proto import spatial_metrics_sync_pb2 as portal_dot_proto_dot_spatial__metrics__sync__pb2
from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2


class SpatialMetricsSyncServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SyncSpatialMetricBlocks = channel.unary_unary(
                '/carbon.portal.spatial_metrics.SpatialMetricsSyncService/SyncSpatialMetricBlocks',
                request_serializer=portal_dot_proto_dot_spatial__metrics__sync__pb2.SyncSpatialMetricBlocksRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )


class SpatialMetricsSyncServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SyncSpatialMetricBlocks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SpatialMetricsSyncServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SyncSpatialMetricBlocks': grpc.unary_unary_rpc_method_handler(
                    servicer.SyncSpatialMetricBlocks,
                    request_deserializer=portal_dot_proto_dot_spatial__metrics__sync__pb2.SyncSpatialMetricBlocksRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.portal.spatial_metrics.SpatialMetricsSyncService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class SpatialMetricsSyncService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SyncSpatialMetricBlocks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.portal.spatial_metrics.SpatialMetricsSyncService/SyncSpatialMetricBlocks',
            portal_dot_proto_dot_spatial__metrics__sync__pb2.SyncSpatialMetricBlocksRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.module_pb2 import (
    ModuleIdentity as frontend___proto___module_pb2___ModuleIdentity,
    RobotDefinition as frontend___proto___module_pb2___RobotDefinition,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class ReaperConfiguration(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def assigned_modules(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[frontend___proto___module_pb2___ModuleIdentity]: ...

    @property
    def current_robot_definition(self) -> frontend___proto___module_pb2___RobotDefinition: ...

    def __init__(self,
        *,
        assigned_modules : typing___Optional[typing___Iterable[frontend___proto___module_pb2___ModuleIdentity]] = None,
        current_robot_definition : typing___Optional[frontend___proto___module_pb2___RobotDefinition] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"current_robot_definition",b"current_robot_definition"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"assigned_modules",b"assigned_modules",u"current_robot_definition",b"current_robot_definition"]) -> None: ...
type___ReaperConfiguration = ReaperConfiguration

class UploadReaperConfigurationRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def configuration(self) -> type___ReaperConfiguration: ...

    def __init__(self,
        *,
        configuration : typing___Optional[type___ReaperConfiguration] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"configuration",b"configuration"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"configuration",b"configuration"]) -> None: ...
type___UploadReaperConfigurationRequest = UploadReaperConfigurationRequest

class UploadReaperConfigurationResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___UploadReaperConfigurationResponse = UploadReaperConfigurationResponse

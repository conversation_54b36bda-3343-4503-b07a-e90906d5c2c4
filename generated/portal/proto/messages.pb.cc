// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/messages.proto

#include "portal/proto/messages.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace messages {
constexpr Message::Message(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , author_user_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , recipient_user_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , db_(nullptr)
  , author_robot_id_(int64_t{0})
  , recipient_customer_id_(int64_t{0})
  , recipient_robot_id_(int64_t{0}){}
struct MessageDefaultTypeInternal {
  constexpr MessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MessageDefaultTypeInternal() {}
  union {
    Message _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MessageDefaultTypeInternal _Message_default_instance_;
constexpr MessagesResponse::MessagesResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : messages_()
  , page_(int64_t{0})
  , limit_(int64_t{0}){}
struct MessagesResponseDefaultTypeInternal {
  constexpr MessagesResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MessagesResponseDefaultTypeInternal() {}
  union {
    MessagesResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MessagesResponseDefaultTypeInternal _MessagesResponse_default_instance_;
}  // namespace messages
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2fmessages_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2fmessages_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2fmessages_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2fmessages_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::Message, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::Message, db_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::Message, message_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::Message, author_user_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::Message, author_robot_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::Message, recipient_user_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::Message, recipient_customer_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::Message, recipient_robot_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::MessagesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::MessagesResponse, page_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::MessagesResponse, limit_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::messages::MessagesResponse, messages_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::messages::Message)},
  { 13, -1, -1, sizeof(::carbon::portal::messages::MessagesResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::messages::_Message_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::messages::_MessagesResponse_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2fmessages_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033portal/proto/messages.proto\022\026carbon.po"
  "rtal.messages\032\025portal/proto/db.proto\"\303\001\n"
  "\007Message\022 \n\002db\030\001 \001(\0132\024.carbon.portal.db."
  "DB\022\017\n\007message\030\002 \001(\t\022\026\n\016author_user_id\030\003 "
  "\001(\t\022\027\n\017author_robot_id\030\004 \001(\003\022\031\n\021recipien"
  "t_user_id\030\005 \001(\t\022\035\n\025recipient_customer_id"
  "\030\006 \001(\003\022\032\n\022recipient_robot_id\030\007 \001(\003\"b\n\020Me"
  "ssagesResponse\022\014\n\004page\030\002 \001(\003\022\r\n\005limit\030\003 "
  "\001(\003\0221\n\010messages\030\004 \003(\0132\037.carbon.portal.me"
  "ssages.MessageB\016Z\014proto/portalb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2fmessages_2eproto_deps[1] = {
  &::descriptor_table_portal_2fproto_2fdb_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2fmessages_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fmessages_2eproto = {
  false, false, 398, descriptor_table_protodef_portal_2fproto_2fmessages_2eproto, "portal/proto/messages.proto", 
  &descriptor_table_portal_2fproto_2fmessages_2eproto_once, descriptor_table_portal_2fproto_2fmessages_2eproto_deps, 1, 2,
  schemas, file_default_instances, TableStruct_portal_2fproto_2fmessages_2eproto::offsets,
  file_level_metadata_portal_2fproto_2fmessages_2eproto, file_level_enum_descriptors_portal_2fproto_2fmessages_2eproto, file_level_service_descriptors_portal_2fproto_2fmessages_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2fmessages_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2fmessages_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2fmessages_2eproto(&descriptor_table_portal_2fproto_2fmessages_2eproto);
namespace carbon {
namespace portal {
namespace messages {

// ===================================================================

class Message::_Internal {
 public:
  static const ::carbon::portal::db::DB& db(const Message* msg);
};

const ::carbon::portal::db::DB&
Message::_Internal::db(const Message* msg) {
  return *msg->db_;
}
void Message::clear_db() {
  if (GetArenaForAllocation() == nullptr && db_ != nullptr) {
    delete db_;
  }
  db_ = nullptr;
}
Message::Message(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.messages.Message)
}
Message::Message(const Message& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_message().empty()) {
    message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_message(), 
      GetArenaForAllocation());
  }
  author_user_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    author_user_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_author_user_id().empty()) {
    author_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_author_user_id(), 
      GetArenaForAllocation());
  }
  recipient_user_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    recipient_user_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_recipient_user_id().empty()) {
    recipient_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_recipient_user_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_db()) {
    db_ = new ::carbon::portal::db::DB(*from.db_);
  } else {
    db_ = nullptr;
  }
  ::memcpy(&author_robot_id_, &from.author_robot_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&recipient_robot_id_) -
    reinterpret_cast<char*>(&author_robot_id_)) + sizeof(recipient_robot_id_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.messages.Message)
}

inline void Message::SharedCtor() {
message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
author_user_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  author_user_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
recipient_user_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  recipient_user_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&db_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&recipient_robot_id_) -
    reinterpret_cast<char*>(&db_)) + sizeof(recipient_robot_id_));
}

Message::~Message() {
  // @@protoc_insertion_point(destructor:carbon.portal.messages.Message)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Message::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  author_user_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  recipient_user_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete db_;
}

void Message::ArenaDtor(void* object) {
  Message* _this = reinterpret_cast< Message* >(object);
  (void)_this;
}
void Message::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Message::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Message::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.messages.Message)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty();
  author_user_id_.ClearToEmpty();
  recipient_user_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && db_ != nullptr) {
    delete db_;
  }
  db_ = nullptr;
  ::memset(&author_robot_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&recipient_robot_id_) -
      reinterpret_cast<char*>(&author_robot_id_)) + sizeof(recipient_robot_id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Message::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.portal.db.DB db = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_db(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.messages.Message.message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string author_user_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_author_user_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.messages.Message.author_user_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 author_robot_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          author_robot_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string recipient_user_id = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_recipient_user_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.messages.Message.recipient_user_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 recipient_customer_id = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          recipient_customer_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 recipient_robot_id = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          recipient_robot_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Message::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.messages.Message)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.portal.db.DB db = 1;
  if (this->_internal_has_db()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::db(this), target, stream);
  }

  // string message = 2;
  if (!this->_internal_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.messages.Message.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  // string author_user_id = 3;
  if (!this->_internal_author_user_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_author_user_id().data(), static_cast<int>(this->_internal_author_user_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.messages.Message.author_user_id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_author_user_id(), target);
  }

  // int64 author_robot_id = 4;
  if (this->_internal_author_robot_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(4, this->_internal_author_robot_id(), target);
  }

  // string recipient_user_id = 5;
  if (!this->_internal_recipient_user_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_recipient_user_id().data(), static_cast<int>(this->_internal_recipient_user_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.messages.Message.recipient_user_id");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_recipient_user_id(), target);
  }

  // int64 recipient_customer_id = 6;
  if (this->_internal_recipient_customer_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(6, this->_internal_recipient_customer_id(), target);
  }

  // int64 recipient_robot_id = 7;
  if (this->_internal_recipient_robot_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(7, this->_internal_recipient_robot_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.messages.Message)
  return target;
}

size_t Message::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.messages.Message)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (!this->_internal_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // string author_user_id = 3;
  if (!this->_internal_author_user_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_author_user_id());
  }

  // string recipient_user_id = 5;
  if (!this->_internal_recipient_user_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_recipient_user_id());
  }

  // .carbon.portal.db.DB db = 1;
  if (this->_internal_has_db()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *db_);
  }

  // int64 author_robot_id = 4;
  if (this->_internal_author_robot_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_author_robot_id());
  }

  // int64 recipient_customer_id = 6;
  if (this->_internal_recipient_customer_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_recipient_customer_id());
  }

  // int64 recipient_robot_id = 7;
  if (this->_internal_recipient_robot_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_recipient_robot_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Message::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Message::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Message::GetClassData() const { return &_class_data_; }

void Message::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Message *>(to)->MergeFrom(
      static_cast<const Message &>(from));
}


void Message::MergeFrom(const Message& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.messages.Message)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_message().empty()) {
    _internal_set_message(from._internal_message());
  }
  if (!from._internal_author_user_id().empty()) {
    _internal_set_author_user_id(from._internal_author_user_id());
  }
  if (!from._internal_recipient_user_id().empty()) {
    _internal_set_recipient_user_id(from._internal_recipient_user_id());
  }
  if (from._internal_has_db()) {
    _internal_mutable_db()->::carbon::portal::db::DB::MergeFrom(from._internal_db());
  }
  if (from._internal_author_robot_id() != 0) {
    _internal_set_author_robot_id(from._internal_author_robot_id());
  }
  if (from._internal_recipient_customer_id() != 0) {
    _internal_set_recipient_customer_id(from._internal_recipient_customer_id());
  }
  if (from._internal_recipient_robot_id() != 0) {
    _internal_set_recipient_robot_id(from._internal_recipient_robot_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Message::CopyFrom(const Message& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.messages.Message)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Message::IsInitialized() const {
  return true;
}

void Message::InternalSwap(Message* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &message_, lhs_arena,
      &other->message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &author_user_id_, lhs_arena,
      &other->author_user_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &recipient_user_id_, lhs_arena,
      &other->recipient_user_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Message, recipient_robot_id_)
      + sizeof(Message::recipient_robot_id_)
      - PROTOBUF_FIELD_OFFSET(Message, db_)>(
          reinterpret_cast<char*>(&db_),
          reinterpret_cast<char*>(&other->db_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Message::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmessages_2eproto_getter, &descriptor_table_portal_2fproto_2fmessages_2eproto_once,
      file_level_metadata_portal_2fproto_2fmessages_2eproto[0]);
}

// ===================================================================

class MessagesResponse::_Internal {
 public:
};

MessagesResponse::MessagesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  messages_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.messages.MessagesResponse)
}
MessagesResponse::MessagesResponse(const MessagesResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      messages_(from.messages_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&page_, &from.page_,
    static_cast<size_t>(reinterpret_cast<char*>(&limit_) -
    reinterpret_cast<char*>(&page_)) + sizeof(limit_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.messages.MessagesResponse)
}

inline void MessagesResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&page_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&limit_) -
    reinterpret_cast<char*>(&page_)) + sizeof(limit_));
}

MessagesResponse::~MessagesResponse() {
  // @@protoc_insertion_point(destructor:carbon.portal.messages.MessagesResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MessagesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MessagesResponse::ArenaDtor(void* object) {
  MessagesResponse* _this = reinterpret_cast< MessagesResponse* >(object);
  (void)_this;
}
void MessagesResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MessagesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MessagesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.messages.MessagesResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  messages_.Clear();
  ::memset(&page_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&limit_) -
      reinterpret_cast<char*>(&page_)) + sizeof(limit_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MessagesResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 page = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          page_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 limit = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          limit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.portal.messages.Message messages = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_messages(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MessagesResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.messages.MessagesResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 page = 2;
  if (this->_internal_page() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_page(), target);
  }

  // int64 limit = 3;
  if (this->_internal_limit() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_limit(), target);
  }

  // repeated .carbon.portal.messages.Message messages = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_messages_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_messages(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.messages.MessagesResponse)
  return target;
}

size_t MessagesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.messages.MessagesResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.messages.Message messages = 4;
  total_size += 1UL * this->_internal_messages_size();
  for (const auto& msg : this->messages_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int64 page = 2;
  if (this->_internal_page() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_page());
  }

  // int64 limit = 3;
  if (this->_internal_limit() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_limit());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MessagesResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MessagesResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MessagesResponse::GetClassData() const { return &_class_data_; }

void MessagesResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MessagesResponse *>(to)->MergeFrom(
      static_cast<const MessagesResponse &>(from));
}


void MessagesResponse::MergeFrom(const MessagesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.messages.MessagesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  messages_.MergeFrom(from.messages_);
  if (from._internal_page() != 0) {
    _internal_set_page(from._internal_page());
  }
  if (from._internal_limit() != 0) {
    _internal_set_limit(from._internal_limit());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MessagesResponse::CopyFrom(const MessagesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.messages.MessagesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MessagesResponse::IsInitialized() const {
  return true;
}

void MessagesResponse::InternalSwap(MessagesResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  messages_.InternalSwap(&other->messages_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MessagesResponse, limit_)
      + sizeof(MessagesResponse::limit_)
      - PROTOBUF_FIELD_OFFSET(MessagesResponse, page_)>(
          reinterpret_cast<char*>(&page_),
          reinterpret_cast<char*>(&other->page_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MessagesResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fmessages_2eproto_getter, &descriptor_table_portal_2fproto_2fmessages_2eproto_once,
      file_level_metadata_portal_2fproto_2fmessages_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace messages
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::messages::Message* Arena::CreateMaybeMessage< ::carbon::portal::messages::Message >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::messages::Message >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::messages::MessagesResponse* Arena::CreateMaybeMessage< ::carbon::portal::messages::MessagesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::messages::MessagesResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/remoteit.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fremoteit_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fremoteit_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2fremoteit_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2fremoteit_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fremoteit_2eproto;
namespace carbon {
namespace portal {
namespace remoteit {
class ConfigureRequest;
struct ConfigureRequestDefaultTypeInternal;
extern ConfigureRequestDefaultTypeInternal _ConfigureRequest_default_instance_;
class ConfigureResult;
struct ConfigureResultDefaultTypeInternal;
extern ConfigureResultDefaultTypeInternal _ConfigureResult_default_instance_;
}  // namespace remoteit
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::remoteit::ConfigureRequest* Arena::CreateMaybeMessage<::carbon::portal::remoteit::ConfigureRequest>(Arena*);
template<> ::carbon::portal::remoteit::ConfigureResult* Arena::CreateMaybeMessage<::carbon::portal::remoteit::ConfigureResult>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace remoteit {

// ===================================================================

class ConfigureRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.remoteit.ConfigureRequest) */ {
 public:
  inline ConfigureRequest() : ConfigureRequest(nullptr) {}
  ~ConfigureRequest() override;
  explicit constexpr ConfigureRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigureRequest(const ConfigureRequest& from);
  ConfigureRequest(ConfigureRequest&& from) noexcept
    : ConfigureRequest() {
    *this = ::std::move(from);
  }

  inline ConfigureRequest& operator=(const ConfigureRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigureRequest& operator=(ConfigureRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigureRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigureRequest* internal_default_instance() {
    return reinterpret_cast<const ConfigureRequest*>(
               &_ConfigureRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ConfigureRequest& a, ConfigureRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigureRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigureRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigureRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigureRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigureRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ConfigureRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigureRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.remoteit.ConfigureRequest";
  }
  protected:
  explicit ConfigureRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kServiceIdFieldNumber = 1,
    kDeviceNameFieldNumber = 2,
  };
  // string serviceId = 1;
  void clear_serviceid();
  const std::string& serviceid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serviceid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serviceid();
  PROTOBUF_NODISCARD std::string* release_serviceid();
  void set_allocated_serviceid(std::string* serviceid);
  private:
  const std::string& _internal_serviceid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serviceid(const std::string& value);
  std::string* _internal_mutable_serviceid();
  public:

  // string deviceName = 2;
  void clear_devicename();
  const std::string& devicename() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_devicename(ArgT0&& arg0, ArgT... args);
  std::string* mutable_devicename();
  PROTOBUF_NODISCARD std::string* release_devicename();
  void set_allocated_devicename(std::string* devicename);
  private:
  const std::string& _internal_devicename() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_devicename(const std::string& value);
  std::string* _internal_mutable_devicename();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.remoteit.ConfigureRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serviceid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr devicename_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fremoteit_2eproto;
};
// -------------------------------------------------------------------

class ConfigureResult final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.remoteit.ConfigureResult) */ {
 public:
  inline ConfigureResult() : ConfigureResult(nullptr) {}
  ~ConfigureResult() override;
  explicit constexpr ConfigureResult(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigureResult(const ConfigureResult& from);
  ConfigureResult(ConfigureResult&& from) noexcept
    : ConfigureResult() {
    *this = ::std::move(from);
  }

  inline ConfigureResult& operator=(const ConfigureResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigureResult& operator=(ConfigureResult&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigureResult& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigureResult* internal_default_instance() {
    return reinterpret_cast<const ConfigureResult*>(
               &_ConfigureResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ConfigureResult& a, ConfigureResult& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigureResult* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigureResult* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigureResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigureResult>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigureResult& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ConfigureResult& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigureResult* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.remoteit.ConfigureResult";
  }
  protected:
  explicit ConfigureResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStatusFieldNumber = 1,
  };
  // string status = 1;
  void clear_status();
  const std::string& status() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status();
  PROTOBUF_NODISCARD std::string* release_status();
  void set_allocated_status(std::string* status);
  private:
  const std::string& _internal_status() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status(const std::string& value);
  std::string* _internal_mutable_status();
  public:

  // @@protoc_insertion_point(class_scope:carbon.portal.remoteit.ConfigureResult)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2fremoteit_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ConfigureRequest

// string serviceId = 1;
inline void ConfigureRequest::clear_serviceid() {
  serviceid_.ClearToEmpty();
}
inline const std::string& ConfigureRequest::serviceid() const {
  // @@protoc_insertion_point(field_get:carbon.portal.remoteit.ConfigureRequest.serviceId)
  return _internal_serviceid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigureRequest::set_serviceid(ArgT0&& arg0, ArgT... args) {
 
 serviceid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.remoteit.ConfigureRequest.serviceId)
}
inline std::string* ConfigureRequest::mutable_serviceid() {
  std::string* _s = _internal_mutable_serviceid();
  // @@protoc_insertion_point(field_mutable:carbon.portal.remoteit.ConfigureRequest.serviceId)
  return _s;
}
inline const std::string& ConfigureRequest::_internal_serviceid() const {
  return serviceid_.Get();
}
inline void ConfigureRequest::_internal_set_serviceid(const std::string& value) {
  
  serviceid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigureRequest::_internal_mutable_serviceid() {
  
  return serviceid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigureRequest::release_serviceid() {
  // @@protoc_insertion_point(field_release:carbon.portal.remoteit.ConfigureRequest.serviceId)
  return serviceid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigureRequest::set_allocated_serviceid(std::string* serviceid) {
  if (serviceid != nullptr) {
    
  } else {
    
  }
  serviceid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serviceid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (serviceid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    serviceid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.remoteit.ConfigureRequest.serviceId)
}

// string deviceName = 2;
inline void ConfigureRequest::clear_devicename() {
  devicename_.ClearToEmpty();
}
inline const std::string& ConfigureRequest::devicename() const {
  // @@protoc_insertion_point(field_get:carbon.portal.remoteit.ConfigureRequest.deviceName)
  return _internal_devicename();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigureRequest::set_devicename(ArgT0&& arg0, ArgT... args) {
 
 devicename_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.remoteit.ConfigureRequest.deviceName)
}
inline std::string* ConfigureRequest::mutable_devicename() {
  std::string* _s = _internal_mutable_devicename();
  // @@protoc_insertion_point(field_mutable:carbon.portal.remoteit.ConfigureRequest.deviceName)
  return _s;
}
inline const std::string& ConfigureRequest::_internal_devicename() const {
  return devicename_.Get();
}
inline void ConfigureRequest::_internal_set_devicename(const std::string& value) {
  
  devicename_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigureRequest::_internal_mutable_devicename() {
  
  return devicename_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigureRequest::release_devicename() {
  // @@protoc_insertion_point(field_release:carbon.portal.remoteit.ConfigureRequest.deviceName)
  return devicename_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigureRequest::set_allocated_devicename(std::string* devicename) {
  if (devicename != nullptr) {
    
  } else {
    
  }
  devicename_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), devicename,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (devicename_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    devicename_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.remoteit.ConfigureRequest.deviceName)
}

// -------------------------------------------------------------------

// ConfigureResult

// string status = 1;
inline void ConfigureResult::clear_status() {
  status_.ClearToEmpty();
}
inline const std::string& ConfigureResult::status() const {
  // @@protoc_insertion_point(field_get:carbon.portal.remoteit.ConfigureResult.status)
  return _internal_status();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigureResult::set_status(ArgT0&& arg0, ArgT... args) {
 
 status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.portal.remoteit.ConfigureResult.status)
}
inline std::string* ConfigureResult::mutable_status() {
  std::string* _s = _internal_mutable_status();
  // @@protoc_insertion_point(field_mutable:carbon.portal.remoteit.ConfigureResult.status)
  return _s;
}
inline const std::string& ConfigureResult::_internal_status() const {
  return status_.Get();
}
inline void ConfigureResult::_internal_set_status(const std::string& value) {
  
  status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigureResult::_internal_mutable_status() {
  
  return status_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigureResult::release_status() {
  // @@protoc_insertion_point(field_release:carbon.portal.remoteit.ConfigureResult.status)
  return status_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigureResult::set_allocated_status(std::string* status) {
  if (status != nullptr) {
    
  } else {
    
  }
  status_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), status,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (status_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.remoteit.ConfigureResult.status)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace remoteit
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2fremoteit_2eproto

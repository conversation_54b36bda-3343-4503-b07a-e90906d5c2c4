// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/profile_sync_portal.proto

#include "portal/proto/profile_sync_portal.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace profile_sync {
constexpr GetProfilesDataRequest::GetProfilesDataRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetProfilesDataRequestDefaultTypeInternal {
  constexpr GetProfilesDataRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfilesDataRequestDefaultTypeInternal() {}
  union {
    GetProfilesDataRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfilesDataRequestDefaultTypeInternal _GetProfilesDataRequest_default_instance_;
constexpr GetProfilesDataResponse_ProfilesEntry_DoNotUse::GetProfilesDataResponse_ProfilesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetProfilesDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal {
  constexpr GetProfilesDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfilesDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetProfilesDataResponse_ProfilesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfilesDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal _GetProfilesDataResponse_ProfilesEntry_DoNotUse_default_instance_;
constexpr GetProfilesDataResponse::GetProfilesDataResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : profiles_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct GetProfilesDataResponseDefaultTypeInternal {
  constexpr GetProfilesDataResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfilesDataResponseDefaultTypeInternal() {}
  union {
    GetProfilesDataResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfilesDataResponseDefaultTypeInternal _GetProfilesDataResponse_default_instance_;
constexpr UploadProfileRequest::UploadProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , last_update_time_ms_(int64_t{0})
  , _oneof_case_{}{}
struct UploadProfileRequestDefaultTypeInternal {
  constexpr UploadProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UploadProfileRequestDefaultTypeInternal() {}
  union {
    UploadProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UploadProfileRequestDefaultTypeInternal _UploadProfileRequest_default_instance_;
constexpr GetProfileRequest::GetProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetProfileRequestDefaultTypeInternal {
  constexpr GetProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfileRequestDefaultTypeInternal() {}
  union {
    GetProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfileRequestDefaultTypeInternal _GetProfileRequest_default_instance_;
constexpr GetProfileResponse::GetProfileResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct GetProfileResponseDefaultTypeInternal {
  constexpr GetProfileResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetProfileResponseDefaultTypeInternal() {}
  union {
    GetProfileResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetProfileResponseDefaultTypeInternal _GetProfileResponse_default_instance_;
constexpr DeleteProfileRequest::DeleteProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DeleteProfileRequestDefaultTypeInternal {
  constexpr DeleteProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteProfileRequestDefaultTypeInternal() {}
  union {
    DeleteProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteProfileRequestDefaultTypeInternal _DeleteProfileRequest_default_instance_;
constexpr PurgeProfileRequest::PurgeProfileRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct PurgeProfileRequestDefaultTypeInternal {
  constexpr PurgeProfileRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PurgeProfileRequestDefaultTypeInternal() {}
  union {
    PurgeProfileRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PurgeProfileRequestDefaultTypeInternal _PurgeProfileRequest_default_instance_;
constexpr SetActiveProfileCommand::SetActiveProfileCommand(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , profile_type_(0)
{}
struct SetActiveProfileCommandDefaultTypeInternal {
  constexpr SetActiveProfileCommandDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetActiveProfileCommandDefaultTypeInternal() {}
  union {
    SetActiveProfileCommand _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetActiveProfileCommandDefaultTypeInternal _SetActiveProfileCommand_default_instance_;
constexpr GetSetActiveProfileCommandsRequest::GetSetActiveProfileCommandsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetSetActiveProfileCommandsRequestDefaultTypeInternal {
  constexpr GetSetActiveProfileCommandsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetSetActiveProfileCommandsRequestDefaultTypeInternal() {}
  union {
    GetSetActiveProfileCommandsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetSetActiveProfileCommandsRequestDefaultTypeInternal _GetSetActiveProfileCommandsRequest_default_instance_;
constexpr GetSetActiveProfileCommandsResponse::GetSetActiveProfileCommandsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : commands_(){}
struct GetSetActiveProfileCommandsResponseDefaultTypeInternal {
  constexpr GetSetActiveProfileCommandsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetSetActiveProfileCommandsResponseDefaultTypeInternal() {}
  union {
    GetSetActiveProfileCommandsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetSetActiveProfileCommandsResponseDefaultTypeInternal _GetSetActiveProfileCommandsResponse_default_instance_;
constexpr PurgeSetActiveProfileCommandsRequest::PurgeSetActiveProfileCommandsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : commands_()
  , robot_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct PurgeSetActiveProfileCommandsRequestDefaultTypeInternal {
  constexpr PurgeSetActiveProfileCommandsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PurgeSetActiveProfileCommandsRequestDefaultTypeInternal() {}
  union {
    PurgeSetActiveProfileCommandsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PurgeSetActiveProfileCommandsRequestDefaultTypeInternal _PurgeSetActiveProfileCommandsRequest_default_instance_;
}  // namespace profile_sync
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[12];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2fprofile_5fsync_5fportal_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2fprofile_5fsync_5fportal_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfilesDataRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfilesDataRequest, robot_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfilesDataResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfilesDataResponse, profiles_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::UploadProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::UploadProfileRequest, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::UploadProfileRequest, last_update_time_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::UploadProfileRequest, robot_name_),
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::UploadProfileRequest, profile_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfileRequest, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfileResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfileResponse, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetProfileResponse, profile_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::DeleteProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::DeleteProfileRequest, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::PurgeProfileRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::PurgeProfileRequest, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::SetActiveProfileCommand, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::SetActiveProfileCommand, profile_type_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::SetActiveProfileCommand, uuid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest, robot_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse, commands_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, robot_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest, commands_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::profile_sync::GetProfilesDataRequest)},
  { 7, 15, -1, sizeof(::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse)},
  { 17, -1, -1, sizeof(::carbon::portal::profile_sync::GetProfilesDataResponse)},
  { 24, -1, -1, sizeof(::carbon::portal::profile_sync::UploadProfileRequest)},
  { 39, -1, -1, sizeof(::carbon::portal::profile_sync::GetProfileRequest)},
  { 46, -1, -1, sizeof(::carbon::portal::profile_sync::GetProfileResponse)},
  { 59, -1, -1, sizeof(::carbon::portal::profile_sync::DeleteProfileRequest)},
  { 66, -1, -1, sizeof(::carbon::portal::profile_sync::PurgeProfileRequest)},
  { 73, -1, -1, sizeof(::carbon::portal::profile_sync::SetActiveProfileCommand)},
  { 81, -1, -1, sizeof(::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest)},
  { 88, -1, -1, sizeof(::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse)},
  { 95, -1, -1, sizeof(::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_GetProfilesDataRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_GetProfilesDataResponse_ProfilesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_GetProfilesDataResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_UploadProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_GetProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_GetProfileResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_DeleteProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_PurgeProfileRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_SetActiveProfileCommand_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_GetSetActiveProfileCommandsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_GetSetActiveProfileCommandsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::profile_sync::_PurgeSetActiveProfileCommandsRequest_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n&portal/proto/profile_sync_portal.proto"
  "\022\032carbon.portal.profile_sync\032\027portal/pro"
  "to/util.proto\032!frontend/proto/profile_sy"
  "nc.proto\032\033proto/almanac/almanac.proto\032\035p"
  "roto/thinning/thinning.proto\032\034frontend/p"
  "roto/banding.proto\032\?proto/target_velocit"
  "y_estimator/target_velocity_estimator.pr"
  "oto\",\n\026GetProfilesDataRequest\022\022\n\nrobot_n"
  "ame\030\001 \001(\t\"\316\001\n\027GetProfilesDataResponse\022S\n"
  "\010profiles\030\001 \003(\0132A.carbon.portal.profile_"
  "sync.GetProfilesDataResponse.ProfilesEnt"
  "ry\032^\n\rProfilesEntry\022\013\n\003key\030\001 \001(\t\022<\n\005valu"
  "e\030\002 \001(\0132-.carbon.frontend.profile_sync.P"
  "rofileSyncData:\0028\001\"\313\003\n\024UploadProfileRequ"
  "est\022\033\n\023last_update_time_ms\030\001 \001(\003\022\022\n\nrobo"
  "t_name\030\002 \001(\t\0227\n\007almanac\030\003 \001(\0132$.carbon.a"
  "imbot.almanac.AlmanacConfigH\000\022C\n\rdiscrim"
  "inator\030\004 \001(\0132*.carbon.aimbot.almanac.Dis"
  "criminatorConfigH\000\022\?\n\013modelinator\030\005 \001(\0132"
  "(.carbon.aimbot.almanac.ModelinatorConfi"
  "gH\000\0226\n\007banding\030\006 \001(\0132#.carbon.frontend.b"
  "anding.BandingDefH\000\0225\n\010thinning\030\007 \001(\0132!."
  "carbon.thinning.ConfigDefinitionH\000\022I\n\nta"
  "rget_vel\030\010 \001(\01323.carbon.aimbot.target_ve"
  "locity_estimator.TVEProfileH\000B\t\n\007profile"
  "\"!\n\021GetProfileRequest\022\014\n\004uuid\030\001 \001(\t\"\230\003\n\022"
  "GetProfileResponse\0227\n\007almanac\030\001 \001(\0132$.ca"
  "rbon.aimbot.almanac.AlmanacConfigH\000\022C\n\rd"
  "iscriminator\030\002 \001(\0132*.carbon.aimbot.alman"
  "ac.DiscriminatorConfigH\000\022\?\n\013modelinator\030"
  "\003 \001(\0132(.carbon.aimbot.almanac.Modelinato"
  "rConfigH\000\0226\n\007banding\030\004 \001(\0132#.carbon.fron"
  "tend.banding.BandingDefH\000\0225\n\010thinning\030\005 "
  "\001(\0132!.carbon.thinning.ConfigDefinitionH\000"
  "\022I\n\ntarget_vel\030\006 \001(\01323.carbon.aimbot.tar"
  "get_velocity_estimator.TVEProfileH\000B\t\n\007p"
  "rofile\"$\n\024DeleteProfileRequest\022\014\n\004uuid\030\001"
  " \001(\t\"#\n\023PurgeProfileRequest\022\014\n\004uuid\030\001 \001("
  "\t\"h\n\027SetActiveProfileCommand\022\?\n\014profile_"
  "type\030\001 \001(\0162).carbon.frontend.profile_syn"
  "c.ProfileType\022\014\n\004uuid\030\002 \001(\t\"3\n\"GetSetAct"
  "iveProfileCommandsRequest\022\r\n\005robot\030\001 \001(\t"
  "\"l\n#GetSetActiveProfileCommandsResponse\022"
  "E\n\010commands\030\001 \003(\01323.carbon.portal.profil"
  "e_sync.SetActiveProfileCommand\"|\n$PurgeS"
  "etActiveProfileCommandsRequest\022\r\n\005robot\030"
  "\001 \001(\t\022E\n\010commands\030\002 \003(\01323.carbon.portal."
  "profile_sync.SetActiveProfileCommand2\272\006\n"
  "\030PortalProfileSyncService\022z\n\017GetProfiles"
  "Data\0222.carbon.portal.profile_sync.GetPro"
  "filesDataRequest\0323.carbon.portal.profile"
  "_sync.GetProfilesDataResponse\022\\\n\rUploadP"
  "rofile\0220.carbon.portal.profile_sync.Uplo"
  "adProfileRequest\032\031.carbon.portal.util.Em"
  "pty\022k\n\nGetProfile\022-.carbon.portal.profil"
  "e_sync.GetProfileRequest\032..carbon.portal"
  ".profile_sync.GetProfileResponse\022\\\n\rDele"
  "teProfile\0220.carbon.portal.profile_sync.D"
  "eleteProfileRequest\032\031.carbon.portal.util"
  ".Empty\022Z\n\014PurgeProfile\022/.carbon.portal.p"
  "rofile_sync.PurgeProfileRequest\032\031.carbon"
  ".portal.util.Empty\022\236\001\n\033GetSetActiveProfi"
  "leCommands\022>.carbon.portal.profile_sync."
  "GetSetActiveProfileCommandsRequest\032\?.car"
  "bon.portal.profile_sync.GetSetActiveProf"
  "ileCommandsResponse\022|\n\035PurgeSetActivePro"
  "fileCommands\<EMAIL>.profile_syn"
  "c.PurgeSetActiveProfileCommandsRequest\032\031"
  ".carbon.portal.util.EmptyB\016Z\014proto/porta"
  "lb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_deps[6] = {
  &::descriptor_table_frontend_2fproto_2fbanding_2eproto,
  &::descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto,
  &::descriptor_table_portal_2fproto_2futil_2eproto,
  &::descriptor_table_proto_2falmanac_2falmanac_2eproto,
  &::descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto,
  &::descriptor_table_proto_2fthinning_2fthinning_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto = {
  false, false, 2769, descriptor_table_protodef_portal_2fproto_2fprofile_5fsync_5fportal_2eproto, "portal/proto/profile_sync_portal.proto", 
  &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once, descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_deps, 6, 12,
  schemas, file_default_instances, TableStruct_portal_2fproto_2fprofile_5fsync_5fportal_2eproto::offsets,
  file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto, file_level_enum_descriptors_portal_2fproto_2fprofile_5fsync_5fportal_2eproto, file_level_service_descriptors_portal_2fproto_2fprofile_5fsync_5fportal_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2fprofile_5fsync_5fportal_2eproto(&descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto);
namespace carbon {
namespace portal {
namespace profile_sync {

// ===================================================================

class GetProfilesDataRequest::_Internal {
 public:
};

GetProfilesDataRequest::GetProfilesDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.GetProfilesDataRequest)
}
GetProfilesDataRequest::GetProfilesDataRequest(const GetProfilesDataRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_name().empty()) {
    robot_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.GetProfilesDataRequest)
}

inline void GetProfilesDataRequest::SharedCtor() {
robot_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetProfilesDataRequest::~GetProfilesDataRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.GetProfilesDataRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetProfilesDataRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetProfilesDataRequest::ArenaDtor(void* object) {
  GetProfilesDataRequest* _this = reinterpret_cast< GetProfilesDataRequest* >(object);
  (void)_this;
}
void GetProfilesDataRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetProfilesDataRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetProfilesDataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.GetProfilesDataRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetProfilesDataRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string robot_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_robot_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.profile_sync.GetProfilesDataRequest.robot_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetProfilesDataRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.GetProfilesDataRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string robot_name = 1;
  if (!this->_internal_robot_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_name().data(), static_cast<int>(this->_internal_robot_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.profile_sync.GetProfilesDataRequest.robot_name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_robot_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.GetProfilesDataRequest)
  return target;
}

size_t GetProfilesDataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.GetProfilesDataRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot_name = 1;
  if (!this->_internal_robot_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetProfilesDataRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetProfilesDataRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetProfilesDataRequest::GetClassData() const { return &_class_data_; }

void GetProfilesDataRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetProfilesDataRequest *>(to)->MergeFrom(
      static_cast<const GetProfilesDataRequest &>(from));
}


void GetProfilesDataRequest::MergeFrom(const GetProfilesDataRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.GetProfilesDataRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot_name().empty()) {
    _internal_set_robot_name(from._internal_robot_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetProfilesDataRequest::CopyFrom(const GetProfilesDataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.GetProfilesDataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProfilesDataRequest::IsInitialized() const {
  return true;
}

void GetProfilesDataRequest::InternalSwap(GetProfilesDataRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_name_, lhs_arena,
      &other->robot_name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetProfilesDataRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[0]);
}

// ===================================================================

GetProfilesDataResponse_ProfilesEntry_DoNotUse::GetProfilesDataResponse_ProfilesEntry_DoNotUse() {}
GetProfilesDataResponse_ProfilesEntry_DoNotUse::GetProfilesDataResponse_ProfilesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetProfilesDataResponse_ProfilesEntry_DoNotUse::MergeFrom(const GetProfilesDataResponse_ProfilesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetProfilesDataResponse_ProfilesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[1]);
}

// ===================================================================

class GetProfilesDataResponse::_Internal {
 public:
};

void GetProfilesDataResponse::clear_profiles() {
  profiles_.Clear();
}
GetProfilesDataResponse::GetProfilesDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  profiles_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.GetProfilesDataResponse)
}
GetProfilesDataResponse::GetProfilesDataResponse(const GetProfilesDataResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  profiles_.MergeFrom(from.profiles_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.GetProfilesDataResponse)
}

inline void GetProfilesDataResponse::SharedCtor() {
}

GetProfilesDataResponse::~GetProfilesDataResponse() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.GetProfilesDataResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetProfilesDataResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetProfilesDataResponse::ArenaDtor(void* object) {
  GetProfilesDataResponse* _this = reinterpret_cast< GetProfilesDataResponse* >(object);
  (void)_this;
  _this->profiles_. ~MapField();
}
inline void GetProfilesDataResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetProfilesDataResponse::ArenaDtor);
  }
}
void GetProfilesDataResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetProfilesDataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.GetProfilesDataResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  profiles_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetProfilesDataResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&profiles_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetProfilesDataResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.GetProfilesDataResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
  if (!this->_internal_profiles().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_profiles().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_profiles().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::const_iterator
          it = this->_internal_profiles().begin();
          it != this->_internal_profiles().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetProfilesDataResponse_ProfilesEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::const_iterator
          it = this->_internal_profiles().begin();
          it != this->_internal_profiles().end(); ++it) {
        target = GetProfilesDataResponse_ProfilesEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.GetProfilesDataResponse)
  return target;
}

size_t GetProfilesDataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.GetProfilesDataResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_profiles_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >::const_iterator
      it = this->_internal_profiles().begin();
      it != this->_internal_profiles().end(); ++it) {
    total_size += GetProfilesDataResponse_ProfilesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetProfilesDataResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetProfilesDataResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetProfilesDataResponse::GetClassData() const { return &_class_data_; }

void GetProfilesDataResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetProfilesDataResponse *>(to)->MergeFrom(
      static_cast<const GetProfilesDataResponse &>(from));
}


void GetProfilesDataResponse::MergeFrom(const GetProfilesDataResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.GetProfilesDataResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  profiles_.MergeFrom(from.profiles_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetProfilesDataResponse::CopyFrom(const GetProfilesDataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.GetProfilesDataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProfilesDataResponse::IsInitialized() const {
  return true;
}

void GetProfilesDataResponse::InternalSwap(GetProfilesDataResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  profiles_.InternalSwap(&other->profiles_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetProfilesDataResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[2]);
}

// ===================================================================

class UploadProfileRequest::_Internal {
 public:
  static const ::carbon::aimbot::almanac::AlmanacConfig& almanac(const UploadProfileRequest* msg);
  static const ::carbon::aimbot::almanac::DiscriminatorConfig& discriminator(const UploadProfileRequest* msg);
  static const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator(const UploadProfileRequest* msg);
  static const ::carbon::frontend::banding::BandingDef& banding(const UploadProfileRequest* msg);
  static const ::carbon::thinning::ConfigDefinition& thinning(const UploadProfileRequest* msg);
  static const ::carbon::aimbot::target_velocity_estimator::TVEProfile& target_vel(const UploadProfileRequest* msg);
};

const ::carbon::aimbot::almanac::AlmanacConfig&
UploadProfileRequest::_Internal::almanac(const UploadProfileRequest* msg) {
  return *msg->profile_.almanac_;
}
const ::carbon::aimbot::almanac::DiscriminatorConfig&
UploadProfileRequest::_Internal::discriminator(const UploadProfileRequest* msg) {
  return *msg->profile_.discriminator_;
}
const ::carbon::aimbot::almanac::ModelinatorConfig&
UploadProfileRequest::_Internal::modelinator(const UploadProfileRequest* msg) {
  return *msg->profile_.modelinator_;
}
const ::carbon::frontend::banding::BandingDef&
UploadProfileRequest::_Internal::banding(const UploadProfileRequest* msg) {
  return *msg->profile_.banding_;
}
const ::carbon::thinning::ConfigDefinition&
UploadProfileRequest::_Internal::thinning(const UploadProfileRequest* msg) {
  return *msg->profile_.thinning_;
}
const ::carbon::aimbot::target_velocity_estimator::TVEProfile&
UploadProfileRequest::_Internal::target_vel(const UploadProfileRequest* msg) {
  return *msg->profile_.target_vel_;
}
void UploadProfileRequest::set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (almanac) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(almanac));
    if (message_arena != submessage_arena) {
      almanac = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, almanac, submessage_arena);
    }
    set_has_almanac();
    profile_.almanac_ = almanac;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.almanac)
}
void UploadProfileRequest::clear_almanac() {
  if (_internal_has_almanac()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.almanac_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (discriminator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(discriminator));
    if (message_arena != submessage_arena) {
      discriminator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, discriminator, submessage_arena);
    }
    set_has_discriminator();
    profile_.discriminator_ = discriminator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.discriminator)
}
void UploadProfileRequest::clear_discriminator() {
  if (_internal_has_discriminator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.discriminator_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (modelinator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator));
    if (message_arena != submessage_arena) {
      modelinator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, modelinator, submessage_arena);
    }
    set_has_modelinator();
    profile_.modelinator_ = modelinator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.modelinator)
}
void UploadProfileRequest::clear_modelinator() {
  if (_internal_has_modelinator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.modelinator_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_banding(::carbon::frontend::banding::BandingDef* banding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (banding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(banding));
    if (message_arena != submessage_arena) {
      banding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, banding, submessage_arena);
    }
    set_has_banding();
    profile_.banding_ = banding;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.banding)
}
void UploadProfileRequest::clear_banding() {
  if (_internal_has_banding()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.banding_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (thinning) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(thinning));
    if (message_arena != submessage_arena) {
      thinning = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, thinning, submessage_arena);
    }
    set_has_thinning();
    profile_.thinning_ = thinning;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.thinning)
}
void UploadProfileRequest::clear_thinning() {
  if (_internal_has_thinning()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.thinning_;
    }
    clear_has_profile();
  }
}
void UploadProfileRequest::set_allocated_target_vel(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (target_vel) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(target_vel));
    if (message_arena != submessage_arena) {
      target_vel = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, target_vel, submessage_arena);
    }
    set_has_target_vel();
    profile_.target_vel_ = target_vel;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.UploadProfileRequest.target_vel)
}
void UploadProfileRequest::clear_target_vel() {
  if (_internal_has_target_vel()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.target_vel_;
    }
    clear_has_profile();
  }
}
UploadProfileRequest::UploadProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.UploadProfileRequest)
}
UploadProfileRequest::UploadProfileRequest(const UploadProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_name().empty()) {
    robot_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_name(), 
      GetArenaForAllocation());
  }
  last_update_time_ms_ = from.last_update_time_ms_;
  clear_has_profile();
  switch (from.profile_case()) {
    case kAlmanac: {
      _internal_mutable_almanac()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_almanac());
      break;
    }
    case kDiscriminator: {
      _internal_mutable_discriminator()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_discriminator());
      break;
    }
    case kModelinator: {
      _internal_mutable_modelinator()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator());
      break;
    }
    case kBanding: {
      _internal_mutable_banding()->::carbon::frontend::banding::BandingDef::MergeFrom(from._internal_banding());
      break;
    }
    case kThinning: {
      _internal_mutable_thinning()->::carbon::thinning::ConfigDefinition::MergeFrom(from._internal_thinning());
      break;
    }
    case kTargetVel: {
      _internal_mutable_target_vel()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_target_vel());
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.UploadProfileRequest)
}

inline void UploadProfileRequest::SharedCtor() {
robot_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
last_update_time_ms_ = int64_t{0};
clear_has_profile();
}

UploadProfileRequest::~UploadProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.UploadProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UploadProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (has_profile()) {
    clear_profile();
  }
}

void UploadProfileRequest::ArenaDtor(void* object) {
  UploadProfileRequest* _this = reinterpret_cast< UploadProfileRequest* >(object);
  (void)_this;
}
void UploadProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UploadProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UploadProfileRequest::clear_profile() {
// @@protoc_insertion_point(one_of_clear_start:carbon.portal.profile_sync.UploadProfileRequest)
  switch (profile_case()) {
    case kAlmanac: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.almanac_;
      }
      break;
    }
    case kDiscriminator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.discriminator_;
      }
      break;
    }
    case kModelinator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.modelinator_;
      }
      break;
    }
    case kBanding: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.banding_;
      }
      break;
    }
    case kThinning: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.thinning_;
      }
      break;
    }
    case kTargetVel: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.target_vel_;
      }
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = PROFILE_NOT_SET;
}


void UploadProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.UploadProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_name_.ClearToEmpty();
  last_update_time_ms_ = int64_t{0};
  clear_profile();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UploadProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 last_update_time_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          last_update_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string robot_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_robot_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.profile_sync.UploadProfileRequest.robot_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_almanac(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_discriminator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_modelinator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.banding.BandingDef banding = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_banding(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.ConfigDefinition thinning = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_thinning(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_target_vel(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UploadProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.UploadProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 last_update_time_ms = 1;
  if (this->_internal_last_update_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_last_update_time_ms(), target);
  }

  // string robot_name = 2;
  if (!this->_internal_robot_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_name().data(), static_cast<int>(this->_internal_robot_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.profile_sync.UploadProfileRequest.robot_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_robot_name(), target);
  }

  // .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
  if (_internal_has_almanac()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::almanac(this), target, stream);
  }

  // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
  if (_internal_has_discriminator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::discriminator(this), target, stream);
  }

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
  if (_internal_has_modelinator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::modelinator(this), target, stream);
  }

  // .carbon.frontend.banding.BandingDef banding = 6;
  if (_internal_has_banding()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::banding(this), target, stream);
  }

  // .carbon.thinning.ConfigDefinition thinning = 7;
  if (_internal_has_thinning()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::thinning(this), target, stream);
  }

  // .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 8;
  if (_internal_has_target_vel()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::target_vel(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.UploadProfileRequest)
  return target;
}

size_t UploadProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.UploadProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot_name = 2;
  if (!this->_internal_robot_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_name());
  }

  // int64 last_update_time_ms = 1;
  if (this->_internal_last_update_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_last_update_time_ms());
  }

  switch (profile_case()) {
    // .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
    case kAlmanac: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.almanac_);
      break;
    }
    // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
    case kDiscriminator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.discriminator_);
      break;
    }
    // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
    case kModelinator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.modelinator_);
      break;
    }
    // .carbon.frontend.banding.BandingDef banding = 6;
    case kBanding: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.banding_);
      break;
    }
    // .carbon.thinning.ConfigDefinition thinning = 7;
    case kThinning: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.thinning_);
      break;
    }
    // .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 8;
    case kTargetVel: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.target_vel_);
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UploadProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UploadProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UploadProfileRequest::GetClassData() const { return &_class_data_; }

void UploadProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UploadProfileRequest *>(to)->MergeFrom(
      static_cast<const UploadProfileRequest &>(from));
}


void UploadProfileRequest::MergeFrom(const UploadProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.UploadProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot_name().empty()) {
    _internal_set_robot_name(from._internal_robot_name());
  }
  if (from._internal_last_update_time_ms() != 0) {
    _internal_set_last_update_time_ms(from._internal_last_update_time_ms());
  }
  switch (from.profile_case()) {
    case kAlmanac: {
      _internal_mutable_almanac()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_almanac());
      break;
    }
    case kDiscriminator: {
      _internal_mutable_discriminator()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_discriminator());
      break;
    }
    case kModelinator: {
      _internal_mutable_modelinator()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator());
      break;
    }
    case kBanding: {
      _internal_mutable_banding()->::carbon::frontend::banding::BandingDef::MergeFrom(from._internal_banding());
      break;
    }
    case kThinning: {
      _internal_mutable_thinning()->::carbon::thinning::ConfigDefinition::MergeFrom(from._internal_thinning());
      break;
    }
    case kTargetVel: {
      _internal_mutable_target_vel()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_target_vel());
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UploadProfileRequest::CopyFrom(const UploadProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.UploadProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UploadProfileRequest::IsInitialized() const {
  return true;
}

void UploadProfileRequest::InternalSwap(UploadProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_name_, lhs_arena,
      &other->robot_name_, rhs_arena
  );
  swap(last_update_time_ms_, other->last_update_time_ms_);
  swap(profile_, other->profile_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata UploadProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[3]);
}

// ===================================================================

class GetProfileRequest::_Internal {
 public:
};

GetProfileRequest::GetProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.GetProfileRequest)
}
GetProfileRequest::GetProfileRequest(const GetProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.GetProfileRequest)
}

inline void GetProfileRequest::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetProfileRequest::~GetProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.GetProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetProfileRequest::ArenaDtor(void* object) {
  GetProfileRequest* _this = reinterpret_cast< GetProfileRequest* >(object);
  (void)_this;
}
void GetProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.GetProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.profile_sync.GetProfileRequest.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.GetProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.profile_sync.GetProfileRequest.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.GetProfileRequest)
  return target;
}

size_t GetProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.GetProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetProfileRequest::GetClassData() const { return &_class_data_; }

void GetProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetProfileRequest *>(to)->MergeFrom(
      static_cast<const GetProfileRequest &>(from));
}


void GetProfileRequest::MergeFrom(const GetProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.GetProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetProfileRequest::CopyFrom(const GetProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.GetProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProfileRequest::IsInitialized() const {
  return true;
}

void GetProfileRequest::InternalSwap(GetProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[4]);
}

// ===================================================================

class GetProfileResponse::_Internal {
 public:
  static const ::carbon::aimbot::almanac::AlmanacConfig& almanac(const GetProfileResponse* msg);
  static const ::carbon::aimbot::almanac::DiscriminatorConfig& discriminator(const GetProfileResponse* msg);
  static const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator(const GetProfileResponse* msg);
  static const ::carbon::frontend::banding::BandingDef& banding(const GetProfileResponse* msg);
  static const ::carbon::thinning::ConfigDefinition& thinning(const GetProfileResponse* msg);
  static const ::carbon::aimbot::target_velocity_estimator::TVEProfile& target_vel(const GetProfileResponse* msg);
};

const ::carbon::aimbot::almanac::AlmanacConfig&
GetProfileResponse::_Internal::almanac(const GetProfileResponse* msg) {
  return *msg->profile_.almanac_;
}
const ::carbon::aimbot::almanac::DiscriminatorConfig&
GetProfileResponse::_Internal::discriminator(const GetProfileResponse* msg) {
  return *msg->profile_.discriminator_;
}
const ::carbon::aimbot::almanac::ModelinatorConfig&
GetProfileResponse::_Internal::modelinator(const GetProfileResponse* msg) {
  return *msg->profile_.modelinator_;
}
const ::carbon::frontend::banding::BandingDef&
GetProfileResponse::_Internal::banding(const GetProfileResponse* msg) {
  return *msg->profile_.banding_;
}
const ::carbon::thinning::ConfigDefinition&
GetProfileResponse::_Internal::thinning(const GetProfileResponse* msg) {
  return *msg->profile_.thinning_;
}
const ::carbon::aimbot::target_velocity_estimator::TVEProfile&
GetProfileResponse::_Internal::target_vel(const GetProfileResponse* msg) {
  return *msg->profile_.target_vel_;
}
void GetProfileResponse::set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (almanac) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(almanac));
    if (message_arena != submessage_arena) {
      almanac = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, almanac, submessage_arena);
    }
    set_has_almanac();
    profile_.almanac_ = almanac;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.GetProfileResponse.almanac)
}
void GetProfileResponse::clear_almanac() {
  if (_internal_has_almanac()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.almanac_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (discriminator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(discriminator));
    if (message_arena != submessage_arena) {
      discriminator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, discriminator, submessage_arena);
    }
    set_has_discriminator();
    profile_.discriminator_ = discriminator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.GetProfileResponse.discriminator)
}
void GetProfileResponse::clear_discriminator() {
  if (_internal_has_discriminator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.discriminator_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (modelinator) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator));
    if (message_arena != submessage_arena) {
      modelinator = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, modelinator, submessage_arena);
    }
    set_has_modelinator();
    profile_.modelinator_ = modelinator;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.GetProfileResponse.modelinator)
}
void GetProfileResponse::clear_modelinator() {
  if (_internal_has_modelinator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.modelinator_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_banding(::carbon::frontend::banding::BandingDef* banding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (banding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(banding));
    if (message_arena != submessage_arena) {
      banding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, banding, submessage_arena);
    }
    set_has_banding();
    profile_.banding_ = banding;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.GetProfileResponse.banding)
}
void GetProfileResponse::clear_banding() {
  if (_internal_has_banding()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.banding_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (thinning) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(thinning));
    if (message_arena != submessage_arena) {
      thinning = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, thinning, submessage_arena);
    }
    set_has_thinning();
    profile_.thinning_ = thinning;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.GetProfileResponse.thinning)
}
void GetProfileResponse::clear_thinning() {
  if (_internal_has_thinning()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.thinning_;
    }
    clear_has_profile();
  }
}
void GetProfileResponse::set_allocated_target_vel(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_vel) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_profile();
  if (target_vel) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(target_vel));
    if (message_arena != submessage_arena) {
      target_vel = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, target_vel, submessage_arena);
    }
    set_has_target_vel();
    profile_.target_vel_ = target_vel;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.profile_sync.GetProfileResponse.target_vel)
}
void GetProfileResponse::clear_target_vel() {
  if (_internal_has_target_vel()) {
    if (GetArenaForAllocation() == nullptr) {
      delete profile_.target_vel_;
    }
    clear_has_profile();
  }
}
GetProfileResponse::GetProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.GetProfileResponse)
}
GetProfileResponse::GetProfileResponse(const GetProfileResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_profile();
  switch (from.profile_case()) {
    case kAlmanac: {
      _internal_mutable_almanac()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_almanac());
      break;
    }
    case kDiscriminator: {
      _internal_mutable_discriminator()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_discriminator());
      break;
    }
    case kModelinator: {
      _internal_mutable_modelinator()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator());
      break;
    }
    case kBanding: {
      _internal_mutable_banding()->::carbon::frontend::banding::BandingDef::MergeFrom(from._internal_banding());
      break;
    }
    case kThinning: {
      _internal_mutable_thinning()->::carbon::thinning::ConfigDefinition::MergeFrom(from._internal_thinning());
      break;
    }
    case kTargetVel: {
      _internal_mutable_target_vel()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_target_vel());
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.GetProfileResponse)
}

inline void GetProfileResponse::SharedCtor() {
clear_has_profile();
}

GetProfileResponse::~GetProfileResponse() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.GetProfileResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetProfileResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_profile()) {
    clear_profile();
  }
}

void GetProfileResponse::ArenaDtor(void* object) {
  GetProfileResponse* _this = reinterpret_cast< GetProfileResponse* >(object);
  (void)_this;
}
void GetProfileResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetProfileResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetProfileResponse::clear_profile() {
// @@protoc_insertion_point(one_of_clear_start:carbon.portal.profile_sync.GetProfileResponse)
  switch (profile_case()) {
    case kAlmanac: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.almanac_;
      }
      break;
    }
    case kDiscriminator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.discriminator_;
      }
      break;
    }
    case kModelinator: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.modelinator_;
      }
      break;
    }
    case kBanding: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.banding_;
      }
      break;
    }
    case kThinning: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.thinning_;
      }
      break;
    }
    case kTargetVel: {
      if (GetArenaForAllocation() == nullptr) {
        delete profile_.target_vel_;
      }
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = PROFILE_NOT_SET;
}


void GetProfileResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.GetProfileResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_profile();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetProfileResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_almanac(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_discriminator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_modelinator(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.banding.BandingDef banding = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_banding(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.ConfigDefinition thinning = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_thinning(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_target_vel(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetProfileResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.GetProfileResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
  if (_internal_has_almanac()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::almanac(this), target, stream);
  }

  // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
  if (_internal_has_discriminator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::discriminator(this), target, stream);
  }

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
  if (_internal_has_modelinator()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::modelinator(this), target, stream);
  }

  // .carbon.frontend.banding.BandingDef banding = 4;
  if (_internal_has_banding()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::banding(this), target, stream);
  }

  // .carbon.thinning.ConfigDefinition thinning = 5;
  if (_internal_has_thinning()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::thinning(this), target, stream);
  }

  // .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 6;
  if (_internal_has_target_vel()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::target_vel(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.GetProfileResponse)
  return target;
}

size_t GetProfileResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.GetProfileResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (profile_case()) {
    // .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
    case kAlmanac: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.almanac_);
      break;
    }
    // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
    case kDiscriminator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.discriminator_);
      break;
    }
    // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
    case kModelinator: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.modelinator_);
      break;
    }
    // .carbon.frontend.banding.BandingDef banding = 4;
    case kBanding: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.banding_);
      break;
    }
    // .carbon.thinning.ConfigDefinition thinning = 5;
    case kThinning: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.thinning_);
      break;
    }
    // .carbon.aimbot.target_velocity_estimator.TVEProfile target_vel = 6;
    case kTargetVel: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *profile_.target_vel_);
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetProfileResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetProfileResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetProfileResponse::GetClassData() const { return &_class_data_; }

void GetProfileResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetProfileResponse *>(to)->MergeFrom(
      static_cast<const GetProfileResponse &>(from));
}


void GetProfileResponse::MergeFrom(const GetProfileResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.GetProfileResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.profile_case()) {
    case kAlmanac: {
      _internal_mutable_almanac()->::carbon::aimbot::almanac::AlmanacConfig::MergeFrom(from._internal_almanac());
      break;
    }
    case kDiscriminator: {
      _internal_mutable_discriminator()->::carbon::aimbot::almanac::DiscriminatorConfig::MergeFrom(from._internal_discriminator());
      break;
    }
    case kModelinator: {
      _internal_mutable_modelinator()->::carbon::aimbot::almanac::ModelinatorConfig::MergeFrom(from._internal_modelinator());
      break;
    }
    case kBanding: {
      _internal_mutable_banding()->::carbon::frontend::banding::BandingDef::MergeFrom(from._internal_banding());
      break;
    }
    case kThinning: {
      _internal_mutable_thinning()->::carbon::thinning::ConfigDefinition::MergeFrom(from._internal_thinning());
      break;
    }
    case kTargetVel: {
      _internal_mutable_target_vel()->::carbon::aimbot::target_velocity_estimator::TVEProfile::MergeFrom(from._internal_target_vel());
      break;
    }
    case PROFILE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetProfileResponse::CopyFrom(const GetProfileResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.GetProfileResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetProfileResponse::IsInitialized() const {
  return true;
}

void GetProfileResponse::InternalSwap(GetProfileResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(profile_, other->profile_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetProfileResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[5]);
}

// ===================================================================

class DeleteProfileRequest::_Internal {
 public:
};

DeleteProfileRequest::DeleteProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.DeleteProfileRequest)
}
DeleteProfileRequest::DeleteProfileRequest(const DeleteProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.DeleteProfileRequest)
}

inline void DeleteProfileRequest::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeleteProfileRequest::~DeleteProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.DeleteProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeleteProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeleteProfileRequest::ArenaDtor(void* object) {
  DeleteProfileRequest* _this = reinterpret_cast< DeleteProfileRequest* >(object);
  (void)_this;
}
void DeleteProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeleteProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeleteProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.DeleteProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeleteProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.profile_sync.DeleteProfileRequest.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeleteProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.DeleteProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.profile_sync.DeleteProfileRequest.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.DeleteProfileRequest)
  return target;
}

size_t DeleteProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.DeleteProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeleteProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteProfileRequest::GetClassData() const { return &_class_data_; }

void DeleteProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeleteProfileRequest *>(to)->MergeFrom(
      static_cast<const DeleteProfileRequest &>(from));
}


void DeleteProfileRequest::MergeFrom(const DeleteProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.DeleteProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeleteProfileRequest::CopyFrom(const DeleteProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.DeleteProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteProfileRequest::IsInitialized() const {
  return true;
}

void DeleteProfileRequest::InternalSwap(DeleteProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DeleteProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[6]);
}

// ===================================================================

class PurgeProfileRequest::_Internal {
 public:
};

PurgeProfileRequest::PurgeProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.PurgeProfileRequest)
}
PurgeProfileRequest::PurgeProfileRequest(const PurgeProfileRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.PurgeProfileRequest)
}

inline void PurgeProfileRequest::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

PurgeProfileRequest::~PurgeProfileRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.PurgeProfileRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PurgeProfileRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PurgeProfileRequest::ArenaDtor(void* object) {
  PurgeProfileRequest* _this = reinterpret_cast< PurgeProfileRequest* >(object);
  (void)_this;
}
void PurgeProfileRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PurgeProfileRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PurgeProfileRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.PurgeProfileRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PurgeProfileRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.profile_sync.PurgeProfileRequest.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PurgeProfileRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.PurgeProfileRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.profile_sync.PurgeProfileRequest.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.PurgeProfileRequest)
  return target;
}

size_t PurgeProfileRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.PurgeProfileRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PurgeProfileRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PurgeProfileRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PurgeProfileRequest::GetClassData() const { return &_class_data_; }

void PurgeProfileRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PurgeProfileRequest *>(to)->MergeFrom(
      static_cast<const PurgeProfileRequest &>(from));
}


void PurgeProfileRequest::MergeFrom(const PurgeProfileRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.PurgeProfileRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PurgeProfileRequest::CopyFrom(const PurgeProfileRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.PurgeProfileRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PurgeProfileRequest::IsInitialized() const {
  return true;
}

void PurgeProfileRequest::InternalSwap(PurgeProfileRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata PurgeProfileRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[7]);
}

// ===================================================================

class SetActiveProfileCommand::_Internal {
 public:
};

SetActiveProfileCommand::SetActiveProfileCommand(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.SetActiveProfileCommand)
}
SetActiveProfileCommand::SetActiveProfileCommand(const SetActiveProfileCommand& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  profile_type_ = from.profile_type_;
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.SetActiveProfileCommand)
}

inline void SetActiveProfileCommand::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
profile_type_ = 0;
}

SetActiveProfileCommand::~SetActiveProfileCommand() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.SetActiveProfileCommand)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetActiveProfileCommand::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetActiveProfileCommand::ArenaDtor(void* object) {
  SetActiveProfileCommand* _this = reinterpret_cast< SetActiveProfileCommand* >(object);
  (void)_this;
}
void SetActiveProfileCommand::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetActiveProfileCommand::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetActiveProfileCommand::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.SetActiveProfileCommand)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  profile_type_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetActiveProfileCommand::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_profile_type(static_cast<::carbon::frontend::profile_sync::ProfileType>(val));
        } else
          goto handle_unusual;
        continue;
      // string uuid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.profile_sync.SetActiveProfileCommand.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetActiveProfileCommand::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.SetActiveProfileCommand)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
  if (this->_internal_profile_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_profile_type(), target);
  }

  // string uuid = 2;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.profile_sync.SetActiveProfileCommand.uuid");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_uuid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.SetActiveProfileCommand)
  return target;
}

size_t SetActiveProfileCommand::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.SetActiveProfileCommand)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 2;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
  if (this->_internal_profile_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_profile_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetActiveProfileCommand::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetActiveProfileCommand::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetActiveProfileCommand::GetClassData() const { return &_class_data_; }

void SetActiveProfileCommand::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetActiveProfileCommand *>(to)->MergeFrom(
      static_cast<const SetActiveProfileCommand &>(from));
}


void SetActiveProfileCommand::MergeFrom(const SetActiveProfileCommand& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.SetActiveProfileCommand)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  if (from._internal_profile_type() != 0) {
    _internal_set_profile_type(from._internal_profile_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetActiveProfileCommand::CopyFrom(const SetActiveProfileCommand& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.SetActiveProfileCommand)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetActiveProfileCommand::IsInitialized() const {
  return true;
}

void SetActiveProfileCommand::InternalSwap(SetActiveProfileCommand* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
  swap(profile_type_, other->profile_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetActiveProfileCommand::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[8]);
}

// ===================================================================

class GetSetActiveProfileCommandsRequest::_Internal {
 public:
};

GetSetActiveProfileCommandsRequest::GetSetActiveProfileCommandsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
}
GetSetActiveProfileCommandsRequest::GetSetActiveProfileCommandsRequest(const GetSetActiveProfileCommandsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot().empty()) {
    robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
}

inline void GetSetActiveProfileCommandsRequest::SharedCtor() {
robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetSetActiveProfileCommandsRequest::~GetSetActiveProfileCommandsRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetSetActiveProfileCommandsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetSetActiveProfileCommandsRequest::ArenaDtor(void* object) {
  GetSetActiveProfileCommandsRequest* _this = reinterpret_cast< GetSetActiveProfileCommandsRequest* >(object);
  (void)_this;
}
void GetSetActiveProfileCommandsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetSetActiveProfileCommandsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetSetActiveProfileCommandsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetSetActiveProfileCommandsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string robot = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_robot();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest.robot"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetSetActiveProfileCommandsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot().data(), static_cast<int>(this->_internal_robot().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest.robot");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_robot(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
  return target;
}

size_t GetSetActiveProfileCommandsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetSetActiveProfileCommandsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetSetActiveProfileCommandsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetSetActiveProfileCommandsRequest::GetClassData() const { return &_class_data_; }

void GetSetActiveProfileCommandsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetSetActiveProfileCommandsRequest *>(to)->MergeFrom(
      static_cast<const GetSetActiveProfileCommandsRequest &>(from));
}


void GetSetActiveProfileCommandsRequest::MergeFrom(const GetSetActiveProfileCommandsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot().empty()) {
    _internal_set_robot(from._internal_robot());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetSetActiveProfileCommandsRequest::CopyFrom(const GetSetActiveProfileCommandsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetSetActiveProfileCommandsRequest::IsInitialized() const {
  return true;
}

void GetSetActiveProfileCommandsRequest::InternalSwap(GetSetActiveProfileCommandsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_, lhs_arena,
      &other->robot_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetSetActiveProfileCommandsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[9]);
}

// ===================================================================

class GetSetActiveProfileCommandsResponse::_Internal {
 public:
};

GetSetActiveProfileCommandsResponse::GetSetActiveProfileCommandsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  commands_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
}
GetSetActiveProfileCommandsResponse::GetSetActiveProfileCommandsResponse(const GetSetActiveProfileCommandsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      commands_(from.commands_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
}

inline void GetSetActiveProfileCommandsResponse::SharedCtor() {
}

GetSetActiveProfileCommandsResponse::~GetSetActiveProfileCommandsResponse() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetSetActiveProfileCommandsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetSetActiveProfileCommandsResponse::ArenaDtor(void* object) {
  GetSetActiveProfileCommandsResponse* _this = reinterpret_cast< GetSetActiveProfileCommandsResponse* >(object);
  (void)_this;
}
void GetSetActiveProfileCommandsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetSetActiveProfileCommandsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetSetActiveProfileCommandsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  commands_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetSetActiveProfileCommandsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_commands(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetSetActiveProfileCommandsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_commands_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_commands(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
  return target;
}

size_t GetSetActiveProfileCommandsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 1;
  total_size += 1UL * this->_internal_commands_size();
  for (const auto& msg : this->commands_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetSetActiveProfileCommandsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetSetActiveProfileCommandsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetSetActiveProfileCommandsResponse::GetClassData() const { return &_class_data_; }

void GetSetActiveProfileCommandsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetSetActiveProfileCommandsResponse *>(to)->MergeFrom(
      static_cast<const GetSetActiveProfileCommandsResponse &>(from));
}


void GetSetActiveProfileCommandsResponse::MergeFrom(const GetSetActiveProfileCommandsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  commands_.MergeFrom(from.commands_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetSetActiveProfileCommandsResponse::CopyFrom(const GetSetActiveProfileCommandsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetSetActiveProfileCommandsResponse::IsInitialized() const {
  return true;
}

void GetSetActiveProfileCommandsResponse::InternalSwap(GetSetActiveProfileCommandsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  commands_.InternalSwap(&other->commands_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetSetActiveProfileCommandsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[10]);
}

// ===================================================================

class PurgeSetActiveProfileCommandsRequest::_Internal {
 public:
};

PurgeSetActiveProfileCommandsRequest::PurgeSetActiveProfileCommandsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  commands_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
}
PurgeSetActiveProfileCommandsRequest::PurgeSetActiveProfileCommandsRequest(const PurgeSetActiveProfileCommandsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      commands_(from.commands_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot().empty()) {
    robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
}

inline void PurgeSetActiveProfileCommandsRequest::SharedCtor() {
robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

PurgeSetActiveProfileCommandsRequest::~PurgeSetActiveProfileCommandsRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PurgeSetActiveProfileCommandsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PurgeSetActiveProfileCommandsRequest::ArenaDtor(void* object) {
  PurgeSetActiveProfileCommandsRequest* _this = reinterpret_cast< PurgeSetActiveProfileCommandsRequest* >(object);
  (void)_this;
}
void PurgeSetActiveProfileCommandsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PurgeSetActiveProfileCommandsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PurgeSetActiveProfileCommandsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  commands_.Clear();
  robot_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PurgeSetActiveProfileCommandsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string robot = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_robot();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.robot"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_commands(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PurgeSetActiveProfileCommandsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot().data(), static_cast<int>(this->_internal_robot().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.robot");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_robot(), target);
  }

  // repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_commands_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_commands(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
  return target;
}

size_t PurgeSetActiveProfileCommandsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.profile_sync.SetActiveProfileCommand commands = 2;
  total_size += 1UL * this->_internal_commands_size();
  for (const auto& msg : this->commands_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PurgeSetActiveProfileCommandsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PurgeSetActiveProfileCommandsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PurgeSetActiveProfileCommandsRequest::GetClassData() const { return &_class_data_; }

void PurgeSetActiveProfileCommandsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PurgeSetActiveProfileCommandsRequest *>(to)->MergeFrom(
      static_cast<const PurgeSetActiveProfileCommandsRequest &>(from));
}


void PurgeSetActiveProfileCommandsRequest::MergeFrom(const PurgeSetActiveProfileCommandsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  commands_.MergeFrom(from.commands_);
  if (!from._internal_robot().empty()) {
    _internal_set_robot(from._internal_robot());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PurgeSetActiveProfileCommandsRequest::CopyFrom(const PurgeSetActiveProfileCommandsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PurgeSetActiveProfileCommandsRequest::IsInitialized() const {
  return true;
}

void PurgeSetActiveProfileCommandsRequest::InternalSwap(PurgeSetActiveProfileCommandsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  commands_.InternalSwap(&other->commands_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_, lhs_arena,
      &other->robot_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata PurgeSetActiveProfileCommandsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_getter, &descriptor_table_portal_2fproto_2fprofile_5fsync_5fportal_2eproto_once,
      file_level_metadata_portal_2fproto_2fprofile_5fsync_5fportal_2eproto[11]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace profile_sync
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::GetProfilesDataRequest* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::GetProfilesDataRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::GetProfilesDataRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::GetProfilesDataResponse_ProfilesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::GetProfilesDataResponse* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::GetProfilesDataResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::GetProfilesDataResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::UploadProfileRequest* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::UploadProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::UploadProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::GetProfileRequest* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::GetProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::GetProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::GetProfileResponse* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::GetProfileResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::GetProfileResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::DeleteProfileRequest* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::DeleteProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::DeleteProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::PurgeProfileRequest* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::PurgeProfileRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::PurgeProfileRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::SetActiveProfileCommand* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::SetActiveProfileCommand >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::SetActiveProfileCommand >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::GetSetActiveProfileCommandsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest* Arena::CreateMaybeMessage< ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::profile_sync::PurgeSetActiveProfileCommandsRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

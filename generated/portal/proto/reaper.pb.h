// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/reaper.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2freaper_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2freaper_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/module.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_portal_2fproto_2freaper_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_portal_2fproto_2freaper_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2freaper_2eproto;
namespace carbon {
namespace portal {
namespace reaper {
class ReaperConfiguration;
struct ReaperConfigurationDefaultTypeInternal;
extern ReaperConfigurationDefaultTypeInternal _ReaperConfiguration_default_instance_;
class UploadReaperConfigurationRequest;
struct UploadReaperConfigurationRequestDefaultTypeInternal;
extern UploadReaperConfigurationRequestDefaultTypeInternal _UploadReaperConfigurationRequest_default_instance_;
class UploadReaperConfigurationResponse;
struct UploadReaperConfigurationResponseDefaultTypeInternal;
extern UploadReaperConfigurationResponseDefaultTypeInternal _UploadReaperConfigurationResponse_default_instance_;
}  // namespace reaper
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::portal::reaper::ReaperConfiguration* Arena::CreateMaybeMessage<::carbon::portal::reaper::ReaperConfiguration>(Arena*);
template<> ::carbon::portal::reaper::UploadReaperConfigurationRequest* Arena::CreateMaybeMessage<::carbon::portal::reaper::UploadReaperConfigurationRequest>(Arena*);
template<> ::carbon::portal::reaper::UploadReaperConfigurationResponse* Arena::CreateMaybeMessage<::carbon::portal::reaper::UploadReaperConfigurationResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace portal {
namespace reaper {

// ===================================================================

class ReaperConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.reaper.ReaperConfiguration) */ {
 public:
  inline ReaperConfiguration() : ReaperConfiguration(nullptr) {}
  ~ReaperConfiguration() override;
  explicit constexpr ReaperConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReaperConfiguration(const ReaperConfiguration& from);
  ReaperConfiguration(ReaperConfiguration&& from) noexcept
    : ReaperConfiguration() {
    *this = ::std::move(from);
  }

  inline ReaperConfiguration& operator=(const ReaperConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReaperConfiguration& operator=(ReaperConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReaperConfiguration& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReaperConfiguration* internal_default_instance() {
    return reinterpret_cast<const ReaperConfiguration*>(
               &_ReaperConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ReaperConfiguration& a, ReaperConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(ReaperConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReaperConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReaperConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReaperConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ReaperConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ReaperConfiguration& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReaperConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.reaper.ReaperConfiguration";
  }
  protected:
  explicit ReaperConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAssignedModulesFieldNumber = 1,
    kCurrentRobotDefinitionFieldNumber = 2,
  };
  // repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 1;
  int assigned_modules_size() const;
  private:
  int _internal_assigned_modules_size() const;
  public:
  void clear_assigned_modules();
  ::carbon::frontend::module::ModuleIdentity* mutable_assigned_modules(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
      mutable_assigned_modules();
  private:
  const ::carbon::frontend::module::ModuleIdentity& _internal_assigned_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* _internal_add_assigned_modules();
  public:
  const ::carbon::frontend::module::ModuleIdentity& assigned_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* add_assigned_modules();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
      assigned_modules() const;

  // .carbon.frontend.module.RobotDefinition current_robot_definition = 2;
  bool has_current_robot_definition() const;
  private:
  bool _internal_has_current_robot_definition() const;
  public:
  void clear_current_robot_definition();
  const ::carbon::frontend::module::RobotDefinition& current_robot_definition() const;
  PROTOBUF_NODISCARD ::carbon::frontend::module::RobotDefinition* release_current_robot_definition();
  ::carbon::frontend::module::RobotDefinition* mutable_current_robot_definition();
  void set_allocated_current_robot_definition(::carbon::frontend::module::RobotDefinition* current_robot_definition);
  private:
  const ::carbon::frontend::module::RobotDefinition& _internal_current_robot_definition() const;
  ::carbon::frontend::module::RobotDefinition* _internal_mutable_current_robot_definition();
  public:
  void unsafe_arena_set_allocated_current_robot_definition(
      ::carbon::frontend::module::RobotDefinition* current_robot_definition);
  ::carbon::frontend::module::RobotDefinition* unsafe_arena_release_current_robot_definition();

  // @@protoc_insertion_point(class_scope:carbon.portal.reaper.ReaperConfiguration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity > assigned_modules_;
  ::carbon::frontend::module::RobotDefinition* current_robot_definition_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2freaper_2eproto;
};
// -------------------------------------------------------------------

class UploadReaperConfigurationRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.portal.reaper.UploadReaperConfigurationRequest) */ {
 public:
  inline UploadReaperConfigurationRequest() : UploadReaperConfigurationRequest(nullptr) {}
  ~UploadReaperConfigurationRequest() override;
  explicit constexpr UploadReaperConfigurationRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UploadReaperConfigurationRequest(const UploadReaperConfigurationRequest& from);
  UploadReaperConfigurationRequest(UploadReaperConfigurationRequest&& from) noexcept
    : UploadReaperConfigurationRequest() {
    *this = ::std::move(from);
  }

  inline UploadReaperConfigurationRequest& operator=(const UploadReaperConfigurationRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UploadReaperConfigurationRequest& operator=(UploadReaperConfigurationRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UploadReaperConfigurationRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UploadReaperConfigurationRequest* internal_default_instance() {
    return reinterpret_cast<const UploadReaperConfigurationRequest*>(
               &_UploadReaperConfigurationRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(UploadReaperConfigurationRequest& a, UploadReaperConfigurationRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UploadReaperConfigurationRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UploadReaperConfigurationRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UploadReaperConfigurationRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UploadReaperConfigurationRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UploadReaperConfigurationRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UploadReaperConfigurationRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UploadReaperConfigurationRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.reaper.UploadReaperConfigurationRequest";
  }
  protected:
  explicit UploadReaperConfigurationRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigurationFieldNumber = 1,
  };
  // .carbon.portal.reaper.ReaperConfiguration configuration = 1;
  bool has_configuration() const;
  private:
  bool _internal_has_configuration() const;
  public:
  void clear_configuration();
  const ::carbon::portal::reaper::ReaperConfiguration& configuration() const;
  PROTOBUF_NODISCARD ::carbon::portal::reaper::ReaperConfiguration* release_configuration();
  ::carbon::portal::reaper::ReaperConfiguration* mutable_configuration();
  void set_allocated_configuration(::carbon::portal::reaper::ReaperConfiguration* configuration);
  private:
  const ::carbon::portal::reaper::ReaperConfiguration& _internal_configuration() const;
  ::carbon::portal::reaper::ReaperConfiguration* _internal_mutable_configuration();
  public:
  void unsafe_arena_set_allocated_configuration(
      ::carbon::portal::reaper::ReaperConfiguration* configuration);
  ::carbon::portal::reaper::ReaperConfiguration* unsafe_arena_release_configuration();

  // @@protoc_insertion_point(class_scope:carbon.portal.reaper.UploadReaperConfigurationRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::portal::reaper::ReaperConfiguration* configuration_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2freaper_2eproto;
};
// -------------------------------------------------------------------

class UploadReaperConfigurationResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.portal.reaper.UploadReaperConfigurationResponse) */ {
 public:
  inline UploadReaperConfigurationResponse() : UploadReaperConfigurationResponse(nullptr) {}
  explicit constexpr UploadReaperConfigurationResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UploadReaperConfigurationResponse(const UploadReaperConfigurationResponse& from);
  UploadReaperConfigurationResponse(UploadReaperConfigurationResponse&& from) noexcept
    : UploadReaperConfigurationResponse() {
    *this = ::std::move(from);
  }

  inline UploadReaperConfigurationResponse& operator=(const UploadReaperConfigurationResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline UploadReaperConfigurationResponse& operator=(UploadReaperConfigurationResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UploadReaperConfigurationResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const UploadReaperConfigurationResponse* internal_default_instance() {
    return reinterpret_cast<const UploadReaperConfigurationResponse*>(
               &_UploadReaperConfigurationResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(UploadReaperConfigurationResponse& a, UploadReaperConfigurationResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(UploadReaperConfigurationResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UploadReaperConfigurationResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UploadReaperConfigurationResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UploadReaperConfigurationResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const UploadReaperConfigurationResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const UploadReaperConfigurationResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.portal.reaper.UploadReaperConfigurationResponse";
  }
  protected:
  explicit UploadReaperConfigurationResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.portal.reaper.UploadReaperConfigurationResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_portal_2fproto_2freaper_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ReaperConfiguration

// repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 1;
inline int ReaperConfiguration::_internal_assigned_modules_size() const {
  return assigned_modules_.size();
}
inline int ReaperConfiguration::assigned_modules_size() const {
  return _internal_assigned_modules_size();
}
inline ::carbon::frontend::module::ModuleIdentity* ReaperConfiguration::mutable_assigned_modules(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.portal.reaper.ReaperConfiguration.assigned_modules)
  return assigned_modules_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
ReaperConfiguration::mutable_assigned_modules() {
  // @@protoc_insertion_point(field_mutable_list:carbon.portal.reaper.ReaperConfiguration.assigned_modules)
  return &assigned_modules_;
}
inline const ::carbon::frontend::module::ModuleIdentity& ReaperConfiguration::_internal_assigned_modules(int index) const {
  return assigned_modules_.Get(index);
}
inline const ::carbon::frontend::module::ModuleIdentity& ReaperConfiguration::assigned_modules(int index) const {
  // @@protoc_insertion_point(field_get:carbon.portal.reaper.ReaperConfiguration.assigned_modules)
  return _internal_assigned_modules(index);
}
inline ::carbon::frontend::module::ModuleIdentity* ReaperConfiguration::_internal_add_assigned_modules() {
  return assigned_modules_.Add();
}
inline ::carbon::frontend::module::ModuleIdentity* ReaperConfiguration::add_assigned_modules() {
  ::carbon::frontend::module::ModuleIdentity* _add = _internal_add_assigned_modules();
  // @@protoc_insertion_point(field_add:carbon.portal.reaper.ReaperConfiguration.assigned_modules)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
ReaperConfiguration::assigned_modules() const {
  // @@protoc_insertion_point(field_list:carbon.portal.reaper.ReaperConfiguration.assigned_modules)
  return assigned_modules_;
}

// .carbon.frontend.module.RobotDefinition current_robot_definition = 2;
inline bool ReaperConfiguration::_internal_has_current_robot_definition() const {
  return this != internal_default_instance() && current_robot_definition_ != nullptr;
}
inline bool ReaperConfiguration::has_current_robot_definition() const {
  return _internal_has_current_robot_definition();
}
inline const ::carbon::frontend::module::RobotDefinition& ReaperConfiguration::_internal_current_robot_definition() const {
  const ::carbon::frontend::module::RobotDefinition* p = current_robot_definition_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::module::RobotDefinition&>(
      ::carbon::frontend::module::_RobotDefinition_default_instance_);
}
inline const ::carbon::frontend::module::RobotDefinition& ReaperConfiguration::current_robot_definition() const {
  // @@protoc_insertion_point(field_get:carbon.portal.reaper.ReaperConfiguration.current_robot_definition)
  return _internal_current_robot_definition();
}
inline void ReaperConfiguration::unsafe_arena_set_allocated_current_robot_definition(
    ::carbon::frontend::module::RobotDefinition* current_robot_definition) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_robot_definition_);
  }
  current_robot_definition_ = current_robot_definition;
  if (current_robot_definition) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.reaper.ReaperConfiguration.current_robot_definition)
}
inline ::carbon::frontend::module::RobotDefinition* ReaperConfiguration::release_current_robot_definition() {
  
  ::carbon::frontend::module::RobotDefinition* temp = current_robot_definition_;
  current_robot_definition_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::module::RobotDefinition* ReaperConfiguration::unsafe_arena_release_current_robot_definition() {
  // @@protoc_insertion_point(field_release:carbon.portal.reaper.ReaperConfiguration.current_robot_definition)
  
  ::carbon::frontend::module::RobotDefinition* temp = current_robot_definition_;
  current_robot_definition_ = nullptr;
  return temp;
}
inline ::carbon::frontend::module::RobotDefinition* ReaperConfiguration::_internal_mutable_current_robot_definition() {
  
  if (current_robot_definition_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::module::RobotDefinition>(GetArenaForAllocation());
    current_robot_definition_ = p;
  }
  return current_robot_definition_;
}
inline ::carbon::frontend::module::RobotDefinition* ReaperConfiguration::mutable_current_robot_definition() {
  ::carbon::frontend::module::RobotDefinition* _msg = _internal_mutable_current_robot_definition();
  // @@protoc_insertion_point(field_mutable:carbon.portal.reaper.ReaperConfiguration.current_robot_definition)
  return _msg;
}
inline void ReaperConfiguration::set_allocated_current_robot_definition(::carbon::frontend::module::RobotDefinition* current_robot_definition) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_robot_definition_);
  }
  if (current_robot_definition) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_robot_definition));
    if (message_arena != submessage_arena) {
      current_robot_definition = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, current_robot_definition, submessage_arena);
    }
    
  } else {
    
  }
  current_robot_definition_ = current_robot_definition;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.reaper.ReaperConfiguration.current_robot_definition)
}

// -------------------------------------------------------------------

// UploadReaperConfigurationRequest

// .carbon.portal.reaper.ReaperConfiguration configuration = 1;
inline bool UploadReaperConfigurationRequest::_internal_has_configuration() const {
  return this != internal_default_instance() && configuration_ != nullptr;
}
inline bool UploadReaperConfigurationRequest::has_configuration() const {
  return _internal_has_configuration();
}
inline void UploadReaperConfigurationRequest::clear_configuration() {
  if (GetArenaForAllocation() == nullptr && configuration_ != nullptr) {
    delete configuration_;
  }
  configuration_ = nullptr;
}
inline const ::carbon::portal::reaper::ReaperConfiguration& UploadReaperConfigurationRequest::_internal_configuration() const {
  const ::carbon::portal::reaper::ReaperConfiguration* p = configuration_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::portal::reaper::ReaperConfiguration&>(
      ::carbon::portal::reaper::_ReaperConfiguration_default_instance_);
}
inline const ::carbon::portal::reaper::ReaperConfiguration& UploadReaperConfigurationRequest::configuration() const {
  // @@protoc_insertion_point(field_get:carbon.portal.reaper.UploadReaperConfigurationRequest.configuration)
  return _internal_configuration();
}
inline void UploadReaperConfigurationRequest::unsafe_arena_set_allocated_configuration(
    ::carbon::portal::reaper::ReaperConfiguration* configuration) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(configuration_);
  }
  configuration_ = configuration;
  if (configuration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.portal.reaper.UploadReaperConfigurationRequest.configuration)
}
inline ::carbon::portal::reaper::ReaperConfiguration* UploadReaperConfigurationRequest::release_configuration() {
  
  ::carbon::portal::reaper::ReaperConfiguration* temp = configuration_;
  configuration_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::portal::reaper::ReaperConfiguration* UploadReaperConfigurationRequest::unsafe_arena_release_configuration() {
  // @@protoc_insertion_point(field_release:carbon.portal.reaper.UploadReaperConfigurationRequest.configuration)
  
  ::carbon::portal::reaper::ReaperConfiguration* temp = configuration_;
  configuration_ = nullptr;
  return temp;
}
inline ::carbon::portal::reaper::ReaperConfiguration* UploadReaperConfigurationRequest::_internal_mutable_configuration() {
  
  if (configuration_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::portal::reaper::ReaperConfiguration>(GetArenaForAllocation());
    configuration_ = p;
  }
  return configuration_;
}
inline ::carbon::portal::reaper::ReaperConfiguration* UploadReaperConfigurationRequest::mutable_configuration() {
  ::carbon::portal::reaper::ReaperConfiguration* _msg = _internal_mutable_configuration();
  // @@protoc_insertion_point(field_mutable:carbon.portal.reaper.UploadReaperConfigurationRequest.configuration)
  return _msg;
}
inline void UploadReaperConfigurationRequest::set_allocated_configuration(::carbon::portal::reaper::ReaperConfiguration* configuration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete configuration_;
  }
  if (configuration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::reaper::ReaperConfiguration>::GetOwningArena(configuration);
    if (message_arena != submessage_arena) {
      configuration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, configuration, submessage_arena);
    }
    
  } else {
    
  }
  configuration_ = configuration;
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.reaper.UploadReaperConfigurationRequest.configuration)
}

// -------------------------------------------------------------------

// UploadReaperConfigurationResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace reaper
}  // namespace portal
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_portal_2fproto_2freaper_2eproto

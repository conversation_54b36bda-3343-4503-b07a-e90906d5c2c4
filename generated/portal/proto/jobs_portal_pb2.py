# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/jobs_portal.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2
from generated.frontend.proto import jobs_pb2 as frontend_dot_proto_dot_jobs__pb2
from generated.frontend.proto import weeding_diagnostics_pb2 as frontend_dot_proto_dot_weeding__diagnostics__pb2
from generated.metrics.proto import metrics_aggregator_service_pb2 as metrics_dot_proto_dot_metrics__aggregator__service__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/jobs_portal.proto',
  package='carbon.portal.jobs',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1eportal/proto/jobs_portal.proto\x12\x12\x63\x61rbon.portal.jobs\x1a\x17portal/proto/util.proto\x1a\x19\x66rontend/proto/jobs.proto\x1a(frontend/proto/weeding_diagnostics.proto\x1a.metrics/proto/metrics_aggregator_service.proto\"I\n\x10UploadJobRequest\x12&\n\x03job\x18\x01 \x01(\x0b\x32\x19.carbon.frontend.jobs.Job\x12\r\n\x05robot\x18\x02 \x01(\t\"x\n\x1aUploadJobConfigDumpRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\x12K\n\nrootConfig\x18\x02 \x01(\x0b\x32\x37.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot\"Y\n\x17UploadJobMetricsRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\x12/\n\njobMetrics\x18\x02 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics2\x9f\x02\n\x11PortalJobsService\x12L\n\tUploadJob\x12$.carbon.portal.jobs.UploadJobRequest\x1a\x19.carbon.portal.util.Empty\x12`\n\x13UploadJobConfigDump\x12..carbon.portal.jobs.UploadJobConfigDumpRequest\x1a\x19.carbon.portal.util.Empty\x12Z\n\x10UploadJobMetrics\x12+.carbon.portal.jobs.UploadJobMetricsRequest\x1a\x19.carbon.portal.util.EmptyB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[portal_dot_proto_dot_util__pb2.DESCRIPTOR,frontend_dot_proto_dot_jobs__pb2.DESCRIPTOR,frontend_dot_proto_dot_weeding__diagnostics__pb2.DESCRIPTOR,metrics_dot_proto_dot_metrics__aggregator__service__pb2.DESCRIPTOR,])




_UPLOADJOBREQUEST = _descriptor.Descriptor(
  name='UploadJobRequest',
  full_name='carbon.portal.jobs.UploadJobRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='job', full_name='carbon.portal.jobs.UploadJobRequest.job', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot', full_name='carbon.portal.jobs.UploadJobRequest.robot', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=196,
  serialized_end=269,
)


_UPLOADJOBCONFIGDUMPREQUEST = _descriptor.Descriptor(
  name='UploadJobConfigDumpRequest',
  full_name='carbon.portal.jobs.UploadJobConfigDumpRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.portal.jobs.UploadJobConfigDumpRequest.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rootConfig', full_name='carbon.portal.jobs.UploadJobConfigDumpRequest.rootConfig', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=271,
  serialized_end=391,
)


_UPLOADJOBMETRICSREQUEST = _descriptor.Descriptor(
  name='UploadJobMetricsRequest',
  full_name='carbon.portal.jobs.UploadJobMetricsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.portal.jobs.UploadJobMetricsRequest.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='jobMetrics', full_name='carbon.portal.jobs.UploadJobMetricsRequest.jobMetrics', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=393,
  serialized_end=482,
)

_UPLOADJOBREQUEST.fields_by_name['job'].message_type = frontend_dot_proto_dot_jobs__pb2._JOB
_UPLOADJOBCONFIGDUMPREQUEST.fields_by_name['rootConfig'].message_type = frontend_dot_proto_dot_weeding__diagnostics__pb2._CONFIGNODESNAPSHOT
_UPLOADJOBMETRICSREQUEST.fields_by_name['jobMetrics'].message_type = metrics_dot_proto_dot_metrics__aggregator__service__pb2._METRICS
DESCRIPTOR.message_types_by_name['UploadJobRequest'] = _UPLOADJOBREQUEST
DESCRIPTOR.message_types_by_name['UploadJobConfigDumpRequest'] = _UPLOADJOBCONFIGDUMPREQUEST
DESCRIPTOR.message_types_by_name['UploadJobMetricsRequest'] = _UPLOADJOBMETRICSREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

UploadJobRequest = _reflection.GeneratedProtocolMessageType('UploadJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADJOBREQUEST,
  '__module__' : 'portal.proto.jobs_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.jobs.UploadJobRequest)
  })
_sym_db.RegisterMessage(UploadJobRequest)

UploadJobConfigDumpRequest = _reflection.GeneratedProtocolMessageType('UploadJobConfigDumpRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADJOBCONFIGDUMPREQUEST,
  '__module__' : 'portal.proto.jobs_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.jobs.UploadJobConfigDumpRequest)
  })
_sym_db.RegisterMessage(UploadJobConfigDumpRequest)

UploadJobMetricsRequest = _reflection.GeneratedProtocolMessageType('UploadJobMetricsRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADJOBMETRICSREQUEST,
  '__module__' : 'portal.proto.jobs_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.jobs.UploadJobMetricsRequest)
  })
_sym_db.RegisterMessage(UploadJobMetricsRequest)


DESCRIPTOR._options = None

_PORTALJOBSSERVICE = _descriptor.ServiceDescriptor(
  name='PortalJobsService',
  full_name='carbon.portal.jobs.PortalJobsService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=485,
  serialized_end=772,
  methods=[
  _descriptor.MethodDescriptor(
    name='UploadJob',
    full_name='carbon.portal.jobs.PortalJobsService.UploadJob',
    index=0,
    containing_service=None,
    input_type=_UPLOADJOBREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UploadJobConfigDump',
    full_name='carbon.portal.jobs.PortalJobsService.UploadJobConfigDump',
    index=1,
    containing_service=None,
    input_type=_UPLOADJOBCONFIGDUMPREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UploadJobMetrics',
    full_name='carbon.portal.jobs.PortalJobsService.UploadJobMetrics',
    index=2,
    containing_service=None,
    input_type=_UPLOADJOBMETRICSREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_PORTALJOBSSERVICE)

DESCRIPTOR.services_by_name['PortalJobsService'] = _PORTALJOBSSERVICE

# @@protoc_insertion_point(module_scope)

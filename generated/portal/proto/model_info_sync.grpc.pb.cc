// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: portal/proto/model_info_sync.proto

#include "portal/proto/model_info_sync.pb.h"
#include "portal/proto/model_info_sync.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace portal {
namespace model_info {

static const char* ModelInfoSyncService_method_names[] = {
  "/carbon.portal.model_info.ModelInfoSyncService/UploadModelInfos",
  "/carbon.portal.model_info.ModelInfoSyncService/GetRenameModelCommands",
  "/carbon.portal.model_info.ModelInfoSyncService/PurgeRenameModelCommands",
};

std::unique_ptr< ModelInfoSyncService::Stub> ModelInfoSyncService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ModelInfoSyncService::Stub> stub(new ModelInfoSyncService::Stub(channel, options));
  return stub;
}

ModelInfoSyncService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_UploadModelInfos_(ModelInfoSyncService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetRenameModelCommands_(ModelInfoSyncService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_PurgeRenameModelCommands_(ModelInfoSyncService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ModelInfoSyncService::Stub::UploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::model_info::UploadModelInfosRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UploadModelInfos_, context, request, response);
}

void ModelInfoSyncService::Stub::async::UploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::model_info::UploadModelInfosRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadModelInfos_, context, request, response, std::move(f));
}

void ModelInfoSyncService::Stub::async::UploadModelInfos(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadModelInfos_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* ModelInfoSyncService::Stub::PrepareAsyncUploadModelInfosRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::model_info::UploadModelInfosRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UploadModelInfos_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* ModelInfoSyncService::Stub::AsyncUploadModelInfosRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUploadModelInfosRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelInfoSyncService::Stub::GetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::model_info::GetRenameModelCommandsRequest, ::carbon::portal::model_info::GetRenameModelCommandsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetRenameModelCommands_, context, request, response);
}

void ModelInfoSyncService::Stub::async::GetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::model_info::GetRenameModelCommandsRequest, ::carbon::portal::model_info::GetRenameModelCommandsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRenameModelCommands_, context, request, response, std::move(f));
}

void ModelInfoSyncService::Stub::async::GetRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRenameModelCommands_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::model_info::GetRenameModelCommandsResponse>* ModelInfoSyncService::Stub::PrepareAsyncGetRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::model_info::GetRenameModelCommandsResponse, ::carbon::portal::model_info::GetRenameModelCommandsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetRenameModelCommands_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::model_info::GetRenameModelCommandsResponse>* ModelInfoSyncService::Stub::AsyncGetRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetRenameModelCommandsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelInfoSyncService::Stub::PurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::portal::model_info::PurgeRenameModelCommandsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_PurgeRenameModelCommands_, context, request, response);
}

void ModelInfoSyncService::Stub::async::PurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::portal::model_info::PurgeRenameModelCommandsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PurgeRenameModelCommands_, context, request, response, std::move(f));
}

void ModelInfoSyncService::Stub::async::PurgeRenameModelCommands(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PurgeRenameModelCommands_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* ModelInfoSyncService::Stub::PrepareAsyncPurgeRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::portal::model_info::PurgeRenameModelCommandsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_PurgeRenameModelCommands_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* ModelInfoSyncService::Stub::AsyncPurgeRenameModelCommandsRaw(::grpc::ClientContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPurgeRenameModelCommandsRaw(context, request, cq);
  result->StartCall();
  return result;
}

ModelInfoSyncService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelInfoSyncService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelInfoSyncService::Service, ::carbon::portal::model_info::UploadModelInfosRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelInfoSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::model_info::UploadModelInfosRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->UploadModelInfos(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelInfoSyncService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelInfoSyncService::Service, ::carbon::portal::model_info::GetRenameModelCommandsRequest, ::carbon::portal::model_info::GetRenameModelCommandsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelInfoSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::model_info::GetRenameModelCommandsRequest* req,
             ::carbon::portal::model_info::GetRenameModelCommandsResponse* resp) {
               return service->GetRenameModelCommands(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelInfoSyncService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelInfoSyncService::Service, ::carbon::portal::model_info::PurgeRenameModelCommandsRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelInfoSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->PurgeRenameModelCommands(ctx, req, resp);
             }, this)));
}

ModelInfoSyncService::Service::~Service() {
}

::grpc::Status ModelInfoSyncService::Service::UploadModelInfos(::grpc::ServerContext* context, const ::carbon::portal::model_info::UploadModelInfosRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelInfoSyncService::Service::GetRenameModelCommands(::grpc::ServerContext* context, const ::carbon::portal::model_info::GetRenameModelCommandsRequest* request, ::carbon::portal::model_info::GetRenameModelCommandsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelInfoSyncService::Service::PurgeRenameModelCommands(::grpc::ServerContext* context, const ::carbon::portal::model_info::PurgeRenameModelCommandsRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace portal
}  // namespace model_info


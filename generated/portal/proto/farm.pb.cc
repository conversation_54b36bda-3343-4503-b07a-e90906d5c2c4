// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: portal/proto/farm.proto

#include "portal/proto/farm.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace portal {
namespace farm {
constexpr Farm::Farm(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : point_defs_()
  , zones_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(nullptr)
  , version_(nullptr)
  , customer_id_(int64_t{0}){}
struct FarmDefaultTypeInternal {
  constexpr FarmDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FarmDefaultTypeInternal() {}
  union {
    Farm _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FarmDefaultTypeInternal _Farm_default_instance_;
constexpr VersionInfo::VersionInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : update_time_(nullptr)
  , ordinal_(int64_t{0})
  , deleted_(false)
  , changed_(false){}
struct VersionInfoDefaultTypeInternal {
  constexpr VersionInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VersionInfoDefaultTypeInternal() {}
  union {
    VersionInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VersionInfoDefaultTypeInternal _VersionInfo_default_instance_;
constexpr PointDefinition::PointDefinition(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : point_(nullptr)
  , version_(nullptr){}
struct PointDefinitionDefaultTypeInternal {
  constexpr PointDefinitionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PointDefinitionDefaultTypeInternal() {}
  union {
    PointDefinition _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PointDefinitionDefaultTypeInternal _PointDefinition_default_instance_;
constexpr Zone::Zone(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : areas_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(nullptr)
  , version_(nullptr)
  , contents_(nullptr){}
struct ZoneDefaultTypeInternal {
  constexpr ZoneDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ZoneDefaultTypeInternal() {}
  union {
    Zone _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ZoneDefaultTypeInternal _Zone_default_instance_;
constexpr ZoneContents::ZoneContents(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct ZoneContentsDefaultTypeInternal {
  constexpr ZoneContentsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ZoneContentsDefaultTypeInternal() {}
  union {
    ZoneContents _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ZoneContentsDefaultTypeInternal _ZoneContents_default_instance_;
constexpr Area::Area(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : buffer_meters_(0)
  , _oneof_case_{}{}
struct AreaDefaultTypeInternal {
  constexpr AreaDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AreaDefaultTypeInternal() {}
  union {
    Area _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AreaDefaultTypeInternal _Area_default_instance_;
constexpr FarmBoundaryData::FarmBoundaryData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct FarmBoundaryDataDefaultTypeInternal {
  constexpr FarmBoundaryDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FarmBoundaryDataDefaultTypeInternal() {}
  union {
    FarmBoundaryData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FarmBoundaryDataDefaultTypeInternal _FarmBoundaryData_default_instance_;
constexpr FieldData::FieldData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : planting_heading_(nullptr)
  , center_pivot_(nullptr){}
struct FieldDataDefaultTypeInternal {
  constexpr FieldDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FieldDataDefaultTypeInternal() {}
  union {
    FieldData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FieldDataDefaultTypeInternal _FieldData_default_instance_;
constexpr HeadlandData::HeadlandData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct HeadlandDataDefaultTypeInternal {
  constexpr HeadlandDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HeadlandDataDefaultTypeInternal() {}
  union {
    HeadlandData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HeadlandDataDefaultTypeInternal _HeadlandData_default_instance_;
constexpr PrivateRoadData::PrivateRoadData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct PrivateRoadDataDefaultTypeInternal {
  constexpr PrivateRoadDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PrivateRoadDataDefaultTypeInternal() {}
  union {
    PrivateRoadData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PrivateRoadDataDefaultTypeInternal _PrivateRoadData_default_instance_;
constexpr ObstacleData::ObstacleData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ObstacleDataDefaultTypeInternal {
  constexpr ObstacleDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ObstacleDataDefaultTypeInternal() {}
  union {
    ObstacleData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ObstacleDataDefaultTypeInternal _ObstacleData_default_instance_;
constexpr PlantingHeading::PlantingHeading(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct PlantingHeadingDefaultTypeInternal {
  constexpr PlantingHeadingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PlantingHeadingDefaultTypeInternal() {}
  union {
    PlantingHeading _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PlantingHeadingDefaultTypeInternal _PlantingHeading_default_instance_;
constexpr CenterPivot::CenterPivot(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : center_(nullptr)
  , width_meters_(0)
  , length_meters_(0){}
struct CenterPivotDefaultTypeInternal {
  constexpr CenterPivotDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CenterPivotDefaultTypeInternal() {}
  union {
    CenterPivot _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CenterPivotDefaultTypeInternal _CenterPivot_default_instance_;
constexpr CreateFarmRequest::CreateFarmRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : farm_(nullptr){}
struct CreateFarmRequestDefaultTypeInternal {
  constexpr CreateFarmRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CreateFarmRequestDefaultTypeInternal() {}
  union {
    CreateFarmRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CreateFarmRequestDefaultTypeInternal _CreateFarmRequest_default_instance_;
constexpr UpdateFarmRequest::UpdateFarmRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : farm_(nullptr){}
struct UpdateFarmRequestDefaultTypeInternal {
  constexpr UpdateFarmRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UpdateFarmRequestDefaultTypeInternal() {}
  union {
    UpdateFarmRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UpdateFarmRequestDefaultTypeInternal _UpdateFarmRequest_default_instance_;
constexpr ListFarmsRequest::ListFarmsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ListFarmsRequestDefaultTypeInternal {
  constexpr ListFarmsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListFarmsRequestDefaultTypeInternal() {}
  union {
    ListFarmsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListFarmsRequestDefaultTypeInternal _ListFarmsRequest_default_instance_;
constexpr ListFarmsResponse::ListFarmsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : farms_()
  , next_page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ListFarmsResponseDefaultTypeInternal {
  constexpr ListFarmsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListFarmsResponseDefaultTypeInternal() {}
  union {
    ListFarmsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListFarmsResponseDefaultTypeInternal _ListFarmsResponse_default_instance_;
constexpr GetFarmRequest::GetFarmRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(nullptr)
  , if_modified_since_(nullptr){}
struct GetFarmRequestDefaultTypeInternal {
  constexpr GetFarmRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetFarmRequestDefaultTypeInternal() {}
  union {
    GetFarmRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetFarmRequestDefaultTypeInternal _GetFarmRequest_default_instance_;
constexpr GetFarmResponse::GetFarmResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : farm_(nullptr){}
struct GetFarmResponseDefaultTypeInternal {
  constexpr GetFarmResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetFarmResponseDefaultTypeInternal() {}
  union {
    GetFarmResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetFarmResponseDefaultTypeInternal _GetFarmResponse_default_instance_;
}  // namespace farm
}  // namespace portal
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_portal_2fproto_2ffarm_2eproto[19];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_portal_2fproto_2ffarm_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_portal_2fproto_2ffarm_2eproto = nullptr;

const uint32_t TableStruct_portal_2fproto_2ffarm_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Farm, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Farm, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Farm, version_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Farm, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Farm, customer_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Farm, point_defs_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Farm, zones_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::VersionInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::VersionInfo, ordinal_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::VersionInfo, update_time_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::VersionInfo, deleted_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::VersionInfo, changed_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::PointDefinition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::PointDefinition, point_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::PointDefinition, version_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Zone, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Zone, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Zone, version_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Zone, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Zone, areas_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Zone, contents_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::ZoneContents, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::ZoneContents, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::ZoneContents, data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Area, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Area, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Area, buffer_meters_),
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::Area, geometry_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::FarmBoundaryData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::FieldData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::FieldData, planting_heading_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::FieldData, center_pivot_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::HeadlandData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::PrivateRoadData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::ObstacleData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::PlantingHeading, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::PlantingHeading, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::PlantingHeading, heading_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::CenterPivot, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::CenterPivot, center_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::CenterPivot, width_meters_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::CenterPivot, length_meters_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::CreateFarmRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::CreateFarmRequest, farm_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::UpdateFarmRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::UpdateFarmRequest, farm_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::ListFarmsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::ListFarmsRequest, page_token_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::ListFarmsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::ListFarmsResponse, farms_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::ListFarmsResponse, next_page_token_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::GetFarmRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::GetFarmRequest, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::GetFarmRequest, if_modified_since_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::GetFarmResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::portal::farm::GetFarmResponse, farm_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::portal::farm::Farm)},
  { 12, -1, -1, sizeof(::carbon::portal::farm::VersionInfo)},
  { 22, -1, -1, sizeof(::carbon::portal::farm::PointDefinition)},
  { 30, -1, -1, sizeof(::carbon::portal::farm::Zone)},
  { 41, -1, -1, sizeof(::carbon::portal::farm::ZoneContents)},
  { 53, -1, -1, sizeof(::carbon::portal::farm::Area)},
  { 64, -1, -1, sizeof(::carbon::portal::farm::FarmBoundaryData)},
  { 70, -1, -1, sizeof(::carbon::portal::farm::FieldData)},
  { 78, -1, -1, sizeof(::carbon::portal::farm::HeadlandData)},
  { 84, -1, -1, sizeof(::carbon::portal::farm::PrivateRoadData)},
  { 90, -1, -1, sizeof(::carbon::portal::farm::ObstacleData)},
  { 96, -1, -1, sizeof(::carbon::portal::farm::PlantingHeading)},
  { 105, -1, -1, sizeof(::carbon::portal::farm::CenterPivot)},
  { 114, -1, -1, sizeof(::carbon::portal::farm::CreateFarmRequest)},
  { 121, -1, -1, sizeof(::carbon::portal::farm::UpdateFarmRequest)},
  { 128, -1, -1, sizeof(::carbon::portal::farm::ListFarmsRequest)},
  { 135, -1, -1, sizeof(::carbon::portal::farm::ListFarmsResponse)},
  { 143, -1, -1, sizeof(::carbon::portal::farm::GetFarmRequest)},
  { 151, -1, -1, sizeof(::carbon::portal::farm::GetFarmResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_Farm_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_VersionInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_PointDefinition_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_Zone_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_ZoneContents_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_Area_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_FarmBoundaryData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_FieldData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_HeadlandData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_PrivateRoadData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_ObstacleData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_PlantingHeading_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_CenterPivot_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_CreateFarmRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_UpdateFarmRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_ListFarmsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_ListFarmsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_GetFarmRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::portal::farm::_GetFarmResponse_default_instance_),
};

const char descriptor_table_protodef_portal_2fproto_2ffarm_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\027portal/proto/farm.proto\022\022carbon.portal"
  ".farm\032\023proto/geo/geo.proto\032\037google/proto"
  "buf/timestamp.proto\"\331\001\n\004Farm\022\032\n\002id\030\001 \001(\013"
  "2\016.carbon.geo.Id\0220\n\007version\030\002 \001(\0132\037.carb"
  "on.portal.farm.VersionInfo\022\014\n\004name\030\003 \001(\t"
  "\022\023\n\013customer_id\030\004 \001(\003\0227\n\npoint_defs\030\005 \003("
  "\0132#.carbon.portal.farm.PointDefinition\022\'"
  "\n\005zones\030\006 \003(\0132\030.carbon.portal.farm.Zone\""
  "q\n\013VersionInfo\022\017\n\007ordinal\030\001 \001(\003\022/\n\013updat"
  "e_time\030\002 \001(\0132\032.google.protobuf.Timestamp"
  "\022\017\n\007deleted\030\003 \001(\010\022\017\n\007changed\030\004 \001(\010\"e\n\017Po"
  "intDefinition\022 \n\005point\030\001 \001(\0132\021.carbon.ge"
  "o.Point\0220\n\007version\030\002 \001(\0132\037.carbon.portal"
  ".farm.VersionInfo\"\277\001\n\004Zone\022\032\n\002id\030\001 \001(\0132\016"
  ".carbon.geo.Id\0220\n\007version\030\002 \001(\0132\037.carbon"
  ".portal.farm.VersionInfo\022\014\n\004name\030\003 \001(\t\022\'"
  "\n\005areas\030\004 \003(\0132\030.carbon.portal.farm.Area\022"
  "2\n\010contents\030\005 \001(\0132 .carbon.portal.farm.Z"
  "oneContents\"\256\002\n\014ZoneContents\022=\n\rfarm_bou"
  "ndary\030\t \001(\0132$.carbon.portal.farm.FarmBou"
  "ndaryDataH\000\022.\n\005field\030\005 \001(\0132\035.carbon.port"
  "al.farm.FieldDataH\000\0224\n\010headland\030\006 \001(\0132 ."
  "carbon.portal.farm.HeadlandDataH\000\022;\n\014pri"
  "vate_road\030\007 \001(\0132#.carbon.portal.farm.Pri"
  "vateRoadDataH\000\0224\n\010obstacle\030\010 \001(\0132 .carbo"
  "n.portal.farm.ObstacleDataH\000B\006\n\004data\"\244\001\n"
  "\004Area\022\025\n\rbuffer_meters\030\001 \001(\001\022\"\n\005point\030\002 "
  "\001(\0132\021.carbon.geo.PointH\000\022-\n\013line_string\030"
  "\003 \001(\0132\026.carbon.geo.LineStringH\000\022&\n\007polyg"
  "on\030\004 \001(\0132\023.carbon.geo.PolygonH\000B\n\n\010geome"
  "try\"\022\n\020FarmBoundaryData\"\201\001\n\tFieldData\022=\n"
  "\020planting_heading\030\001 \001(\0132#.carbon.portal."
  "farm.PlantingHeading\0225\n\014center_pivot\030\002 \001"
  "(\0132\037.carbon.portal.farm.CenterPivot\"\016\n\014H"
  "eadlandData\"\021\n\017PrivateRoadData\"\016\n\014Obstac"
  "leData\"^\n\017PlantingHeading\022\031\n\017azimuth_deg"
  "rees\030\001 \001(\001H\000\022%\n\007ab_line\030\002 \001(\0132\022.carbon.g"
  "eo.AbLineH\000B\t\n\007heading\"]\n\013CenterPivot\022!\n"
  "\006center\030\001 \001(\0132\021.carbon.geo.Point\022\024\n\014widt"
  "h_meters\030\002 \001(\001\022\025\n\rlength_meters\030\003 \001(\001\";\n"
  "\021CreateFarmRequest\022&\n\004farm\030\001 \001(\0132\030.carbo"
  "n.portal.farm.Farm\";\n\021UpdateFarmRequest\022"
  "&\n\004farm\030\001 \001(\0132\030.carbon.portal.farm.Farm\""
  "&\n\020ListFarmsRequest\022\022\n\npage_token\030\001 \001(\t\""
  "U\n\021ListFarmsResponse\022\'\n\005farms\030\001 \003(\0132\030.ca"
  "rbon.portal.farm.Farm\022\027\n\017next_page_token"
  "\030\002 \001(\t\"c\n\016GetFarmRequest\022\032\n\002id\030\001 \001(\0132\016.c"
  "arbon.geo.Id\0225\n\021if_modified_since\030\002 \001(\0132"
  "\032.google.protobuf.Timestamp\"9\n\017GetFarmRe"
  "sponse\022&\n\004farm\030\001 \001(\0132\030.carbon.portal.far"
  "m.Farm2b\n\014FarmsService\022R\n\007GetFarm\022\".carb"
  "on.portal.farm.GetFarmRequest\032#.carbon.p"
  "ortal.farm.GetFarmResponseB\016Z\014proto/port"
  "alb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_portal_2fproto_2ffarm_2eproto_deps[2] = {
  &::descriptor_table_google_2fprotobuf_2ftimestamp_2eproto,
  &::descriptor_table_proto_2fgeo_2fgeo_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_portal_2fproto_2ffarm_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_portal_2fproto_2ffarm_2eproto = {
  false, false, 2130, descriptor_table_protodef_portal_2fproto_2ffarm_2eproto, "portal/proto/farm.proto", 
  &descriptor_table_portal_2fproto_2ffarm_2eproto_once, descriptor_table_portal_2fproto_2ffarm_2eproto_deps, 2, 19,
  schemas, file_default_instances, TableStruct_portal_2fproto_2ffarm_2eproto::offsets,
  file_level_metadata_portal_2fproto_2ffarm_2eproto, file_level_enum_descriptors_portal_2fproto_2ffarm_2eproto, file_level_service_descriptors_portal_2fproto_2ffarm_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_portal_2fproto_2ffarm_2eproto_getter() {
  return &descriptor_table_portal_2fproto_2ffarm_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_portal_2fproto_2ffarm_2eproto(&descriptor_table_portal_2fproto_2ffarm_2eproto);
namespace carbon {
namespace portal {
namespace farm {

// ===================================================================

class Farm::_Internal {
 public:
  static const ::carbon::geo::Id& id(const Farm* msg);
  static const ::carbon::portal::farm::VersionInfo& version(const Farm* msg);
};

const ::carbon::geo::Id&
Farm::_Internal::id(const Farm* msg) {
  return *msg->id_;
}
const ::carbon::portal::farm::VersionInfo&
Farm::_Internal::version(const Farm* msg) {
  return *msg->version_;
}
void Farm::clear_id() {
  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
}
Farm::Farm(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  point_defs_(arena),
  zones_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.Farm)
}
Farm::Farm(const Farm& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      point_defs_(from.point_defs_),
      zones_(from.zones_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_id()) {
    id_ = new ::carbon::geo::Id(*from.id_);
  } else {
    id_ = nullptr;
  }
  if (from._internal_has_version()) {
    version_ = new ::carbon::portal::farm::VersionInfo(*from.version_);
  } else {
    version_ = nullptr;
  }
  customer_id_ = from.customer_id_;
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.Farm)
}

inline void Farm::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&customer_id_) -
    reinterpret_cast<char*>(&id_)) + sizeof(customer_id_));
}

Farm::~Farm() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.Farm)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Farm::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete id_;
  if (this != internal_default_instance()) delete version_;
}

void Farm::ArenaDtor(void* object) {
  Farm* _this = reinterpret_cast< Farm* >(object);
  (void)_this;
}
void Farm::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Farm::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Farm::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.Farm)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  point_defs_.Clear();
  zones_.Clear();
  name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
  if (GetArenaForAllocation() == nullptr && version_ != nullptr) {
    delete version_;
  }
  version_ = nullptr;
  customer_id_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Farm::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.Id id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_id(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.farm.VersionInfo version = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_version(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.farm.Farm.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 customer_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          customer_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.portal.farm.PointDefinition point_defs = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_point_defs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.portal.farm.Zone zones = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_zones(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Farm::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.Farm)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.Id id = 1;
  if (this->_internal_has_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::id(this), target, stream);
  }

  // .carbon.portal.farm.VersionInfo version = 2;
  if (this->_internal_has_version()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::version(this), target, stream);
  }

  // string name = 3;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.farm.Farm.name");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_name(), target);
  }

  // int64 customer_id = 4;
  if (this->_internal_customer_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(4, this->_internal_customer_id(), target);
  }

  // repeated .carbon.portal.farm.PointDefinition point_defs = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_point_defs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_point_defs(i), target, stream);
  }

  // repeated .carbon.portal.farm.Zone zones = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_zones_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, this->_internal_zones(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.Farm)
  return target;
}

size_t Farm::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.Farm)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.farm.PointDefinition point_defs = 5;
  total_size += 1UL * this->_internal_point_defs_size();
  for (const auto& msg : this->point_defs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .carbon.portal.farm.Zone zones = 6;
  total_size += 1UL * this->_internal_zones_size();
  for (const auto& msg : this->zones_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 3;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .carbon.geo.Id id = 1;
  if (this->_internal_has_id()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *id_);
  }

  // .carbon.portal.farm.VersionInfo version = 2;
  if (this->_internal_has_version()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *version_);
  }

  // int64 customer_id = 4;
  if (this->_internal_customer_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_customer_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Farm::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Farm::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Farm::GetClassData() const { return &_class_data_; }

void Farm::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Farm *>(to)->MergeFrom(
      static_cast<const Farm &>(from));
}


void Farm::MergeFrom(const Farm& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.Farm)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  point_defs_.MergeFrom(from.point_defs_);
  zones_.MergeFrom(from.zones_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_has_id()) {
    _internal_mutable_id()->::carbon::geo::Id::MergeFrom(from._internal_id());
  }
  if (from._internal_has_version()) {
    _internal_mutable_version()->::carbon::portal::farm::VersionInfo::MergeFrom(from._internal_version());
  }
  if (from._internal_customer_id() != 0) {
    _internal_set_customer_id(from._internal_customer_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Farm::CopyFrom(const Farm& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.Farm)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Farm::IsInitialized() const {
  return true;
}

void Farm::InternalSwap(Farm* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  point_defs_.InternalSwap(&other->point_defs_);
  zones_.InternalSwap(&other->zones_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Farm, customer_id_)
      + sizeof(Farm::customer_id_)
      - PROTOBUF_FIELD_OFFSET(Farm, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Farm::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[0]);
}

// ===================================================================

class VersionInfo::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& update_time(const VersionInfo* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Timestamp&
VersionInfo::_Internal::update_time(const VersionInfo* msg) {
  return *msg->update_time_;
}
void VersionInfo::clear_update_time() {
  if (GetArenaForAllocation() == nullptr && update_time_ != nullptr) {
    delete update_time_;
  }
  update_time_ = nullptr;
}
VersionInfo::VersionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.VersionInfo)
}
VersionInfo::VersionInfo(const VersionInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_update_time()) {
    update_time_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.update_time_);
  } else {
    update_time_ = nullptr;
  }
  ::memcpy(&ordinal_, &from.ordinal_,
    static_cast<size_t>(reinterpret_cast<char*>(&changed_) -
    reinterpret_cast<char*>(&ordinal_)) + sizeof(changed_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.VersionInfo)
}

inline void VersionInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&update_time_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&changed_) -
    reinterpret_cast<char*>(&update_time_)) + sizeof(changed_));
}

VersionInfo::~VersionInfo() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.VersionInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VersionInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete update_time_;
}

void VersionInfo::ArenaDtor(void* object) {
  VersionInfo* _this = reinterpret_cast< VersionInfo* >(object);
  (void)_this;
}
void VersionInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VersionInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VersionInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.VersionInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && update_time_ != nullptr) {
    delete update_time_;
  }
  update_time_ = nullptr;
  ::memset(&ordinal_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&changed_) -
      reinterpret_cast<char*>(&ordinal_)) + sizeof(changed_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VersionInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 ordinal = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          ordinal_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp update_time = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_update_time(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool deleted = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          deleted_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool changed = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          changed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VersionInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.VersionInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 ordinal = 1;
  if (this->_internal_ordinal() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_ordinal(), target);
  }

  // .google.protobuf.Timestamp update_time = 2;
  if (this->_internal_has_update_time()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::update_time(this), target, stream);
  }

  // bool deleted = 3;
  if (this->_internal_deleted() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_deleted(), target);
  }

  // bool changed = 4;
  if (this->_internal_changed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_changed(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.VersionInfo)
  return target;
}

size_t VersionInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.VersionInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .google.protobuf.Timestamp update_time = 2;
  if (this->_internal_has_update_time()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *update_time_);
  }

  // int64 ordinal = 1;
  if (this->_internal_ordinal() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_ordinal());
  }

  // bool deleted = 3;
  if (this->_internal_deleted() != 0) {
    total_size += 1 + 1;
  }

  // bool changed = 4;
  if (this->_internal_changed() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VersionInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VersionInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VersionInfo::GetClassData() const { return &_class_data_; }

void VersionInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<VersionInfo *>(to)->MergeFrom(
      static_cast<const VersionInfo &>(from));
}


void VersionInfo::MergeFrom(const VersionInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.VersionInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_update_time()) {
    _internal_mutable_update_time()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_update_time());
  }
  if (from._internal_ordinal() != 0) {
    _internal_set_ordinal(from._internal_ordinal());
  }
  if (from._internal_deleted() != 0) {
    _internal_set_deleted(from._internal_deleted());
  }
  if (from._internal_changed() != 0) {
    _internal_set_changed(from._internal_changed());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VersionInfo::CopyFrom(const VersionInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.VersionInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VersionInfo::IsInitialized() const {
  return true;
}

void VersionInfo::InternalSwap(VersionInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VersionInfo, changed_)
      + sizeof(VersionInfo::changed_)
      - PROTOBUF_FIELD_OFFSET(VersionInfo, update_time_)>(
          reinterpret_cast<char*>(&update_time_),
          reinterpret_cast<char*>(&other->update_time_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VersionInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[1]);
}

// ===================================================================

class PointDefinition::_Internal {
 public:
  static const ::carbon::geo::Point& point(const PointDefinition* msg);
  static const ::carbon::portal::farm::VersionInfo& version(const PointDefinition* msg);
};

const ::carbon::geo::Point&
PointDefinition::_Internal::point(const PointDefinition* msg) {
  return *msg->point_;
}
const ::carbon::portal::farm::VersionInfo&
PointDefinition::_Internal::version(const PointDefinition* msg) {
  return *msg->version_;
}
void PointDefinition::clear_point() {
  if (GetArenaForAllocation() == nullptr && point_ != nullptr) {
    delete point_;
  }
  point_ = nullptr;
}
PointDefinition::PointDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.PointDefinition)
}
PointDefinition::PointDefinition(const PointDefinition& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_point()) {
    point_ = new ::carbon::geo::Point(*from.point_);
  } else {
    point_ = nullptr;
  }
  if (from._internal_has_version()) {
    version_ = new ::carbon::portal::farm::VersionInfo(*from.version_);
  } else {
    version_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.PointDefinition)
}

inline void PointDefinition::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&point_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&version_) -
    reinterpret_cast<char*>(&point_)) + sizeof(version_));
}

PointDefinition::~PointDefinition() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.PointDefinition)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PointDefinition::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete point_;
  if (this != internal_default_instance()) delete version_;
}

void PointDefinition::ArenaDtor(void* object) {
  PointDefinition* _this = reinterpret_cast< PointDefinition* >(object);
  (void)_this;
}
void PointDefinition::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PointDefinition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PointDefinition::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.PointDefinition)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && point_ != nullptr) {
    delete point_;
  }
  point_ = nullptr;
  if (GetArenaForAllocation() == nullptr && version_ != nullptr) {
    delete version_;
  }
  version_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PointDefinition::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.Point point = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_point(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.farm.VersionInfo version = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_version(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PointDefinition::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.PointDefinition)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.Point point = 1;
  if (this->_internal_has_point()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::point(this), target, stream);
  }

  // .carbon.portal.farm.VersionInfo version = 2;
  if (this->_internal_has_version()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::version(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.PointDefinition)
  return target;
}

size_t PointDefinition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.PointDefinition)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.Point point = 1;
  if (this->_internal_has_point()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *point_);
  }

  // .carbon.portal.farm.VersionInfo version = 2;
  if (this->_internal_has_version()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *version_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PointDefinition::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PointDefinition::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PointDefinition::GetClassData() const { return &_class_data_; }

void PointDefinition::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PointDefinition *>(to)->MergeFrom(
      static_cast<const PointDefinition &>(from));
}


void PointDefinition::MergeFrom(const PointDefinition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.PointDefinition)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_point()) {
    _internal_mutable_point()->::carbon::geo::Point::MergeFrom(from._internal_point());
  }
  if (from._internal_has_version()) {
    _internal_mutable_version()->::carbon::portal::farm::VersionInfo::MergeFrom(from._internal_version());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PointDefinition::CopyFrom(const PointDefinition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.PointDefinition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PointDefinition::IsInitialized() const {
  return true;
}

void PointDefinition::InternalSwap(PointDefinition* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PointDefinition, version_)
      + sizeof(PointDefinition::version_)
      - PROTOBUF_FIELD_OFFSET(PointDefinition, point_)>(
          reinterpret_cast<char*>(&point_),
          reinterpret_cast<char*>(&other->point_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PointDefinition::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[2]);
}

// ===================================================================

class Zone::_Internal {
 public:
  static const ::carbon::geo::Id& id(const Zone* msg);
  static const ::carbon::portal::farm::VersionInfo& version(const Zone* msg);
  static const ::carbon::portal::farm::ZoneContents& contents(const Zone* msg);
};

const ::carbon::geo::Id&
Zone::_Internal::id(const Zone* msg) {
  return *msg->id_;
}
const ::carbon::portal::farm::VersionInfo&
Zone::_Internal::version(const Zone* msg) {
  return *msg->version_;
}
const ::carbon::portal::farm::ZoneContents&
Zone::_Internal::contents(const Zone* msg) {
  return *msg->contents_;
}
void Zone::clear_id() {
  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
}
Zone::Zone(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  areas_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.Zone)
}
Zone::Zone(const Zone& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      areas_(from.areas_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_id()) {
    id_ = new ::carbon::geo::Id(*from.id_);
  } else {
    id_ = nullptr;
  }
  if (from._internal_has_version()) {
    version_ = new ::carbon::portal::farm::VersionInfo(*from.version_);
  } else {
    version_ = nullptr;
  }
  if (from._internal_has_contents()) {
    contents_ = new ::carbon::portal::farm::ZoneContents(*from.contents_);
  } else {
    contents_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.Zone)
}

inline void Zone::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&contents_) -
    reinterpret_cast<char*>(&id_)) + sizeof(contents_));
}

Zone::~Zone() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.Zone)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Zone::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete id_;
  if (this != internal_default_instance()) delete version_;
  if (this != internal_default_instance()) delete contents_;
}

void Zone::ArenaDtor(void* object) {
  Zone* _this = reinterpret_cast< Zone* >(object);
  (void)_this;
}
void Zone::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Zone::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Zone::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.Zone)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  areas_.Clear();
  name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
  if (GetArenaForAllocation() == nullptr && version_ != nullptr) {
    delete version_;
  }
  version_ = nullptr;
  if (GetArenaForAllocation() == nullptr && contents_ != nullptr) {
    delete contents_;
  }
  contents_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Zone::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.Id id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_id(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.farm.VersionInfo version = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_version(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.farm.Zone.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.portal.farm.Area areas = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_areas(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.farm.ZoneContents contents = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_contents(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Zone::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.Zone)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.Id id = 1;
  if (this->_internal_has_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::id(this), target, stream);
  }

  // .carbon.portal.farm.VersionInfo version = 2;
  if (this->_internal_has_version()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::version(this), target, stream);
  }

  // string name = 3;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.farm.Zone.name");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_name(), target);
  }

  // repeated .carbon.portal.farm.Area areas = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_areas_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_areas(i), target, stream);
  }

  // .carbon.portal.farm.ZoneContents contents = 5;
  if (this->_internal_has_contents()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::contents(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.Zone)
  return target;
}

size_t Zone::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.Zone)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.farm.Area areas = 4;
  total_size += 1UL * this->_internal_areas_size();
  for (const auto& msg : this->areas_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 3;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .carbon.geo.Id id = 1;
  if (this->_internal_has_id()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *id_);
  }

  // .carbon.portal.farm.VersionInfo version = 2;
  if (this->_internal_has_version()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *version_);
  }

  // .carbon.portal.farm.ZoneContents contents = 5;
  if (this->_internal_has_contents()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *contents_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Zone::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Zone::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Zone::GetClassData() const { return &_class_data_; }

void Zone::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Zone *>(to)->MergeFrom(
      static_cast<const Zone &>(from));
}


void Zone::MergeFrom(const Zone& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.Zone)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  areas_.MergeFrom(from.areas_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_has_id()) {
    _internal_mutable_id()->::carbon::geo::Id::MergeFrom(from._internal_id());
  }
  if (from._internal_has_version()) {
    _internal_mutable_version()->::carbon::portal::farm::VersionInfo::MergeFrom(from._internal_version());
  }
  if (from._internal_has_contents()) {
    _internal_mutable_contents()->::carbon::portal::farm::ZoneContents::MergeFrom(from._internal_contents());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Zone::CopyFrom(const Zone& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.Zone)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Zone::IsInitialized() const {
  return true;
}

void Zone::InternalSwap(Zone* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  areas_.InternalSwap(&other->areas_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Zone, contents_)
      + sizeof(Zone::contents_)
      - PROTOBUF_FIELD_OFFSET(Zone, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Zone::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[3]);
}

// ===================================================================

class ZoneContents::_Internal {
 public:
  static const ::carbon::portal::farm::FarmBoundaryData& farm_boundary(const ZoneContents* msg);
  static const ::carbon::portal::farm::FieldData& field(const ZoneContents* msg);
  static const ::carbon::portal::farm::HeadlandData& headland(const ZoneContents* msg);
  static const ::carbon::portal::farm::PrivateRoadData& private_road(const ZoneContents* msg);
  static const ::carbon::portal::farm::ObstacleData& obstacle(const ZoneContents* msg);
};

const ::carbon::portal::farm::FarmBoundaryData&
ZoneContents::_Internal::farm_boundary(const ZoneContents* msg) {
  return *msg->data_.farm_boundary_;
}
const ::carbon::portal::farm::FieldData&
ZoneContents::_Internal::field(const ZoneContents* msg) {
  return *msg->data_.field_;
}
const ::carbon::portal::farm::HeadlandData&
ZoneContents::_Internal::headland(const ZoneContents* msg) {
  return *msg->data_.headland_;
}
const ::carbon::portal::farm::PrivateRoadData&
ZoneContents::_Internal::private_road(const ZoneContents* msg) {
  return *msg->data_.private_road_;
}
const ::carbon::portal::farm::ObstacleData&
ZoneContents::_Internal::obstacle(const ZoneContents* msg) {
  return *msg->data_.obstacle_;
}
void ZoneContents::set_allocated_farm_boundary(::carbon::portal::farm::FarmBoundaryData* farm_boundary) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (farm_boundary) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::FarmBoundaryData>::GetOwningArena(farm_boundary);
    if (message_arena != submessage_arena) {
      farm_boundary = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, farm_boundary, submessage_arena);
    }
    set_has_farm_boundary();
    data_.farm_boundary_ = farm_boundary;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.ZoneContents.farm_boundary)
}
void ZoneContents::set_allocated_field(::carbon::portal::farm::FieldData* field) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (field) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::FieldData>::GetOwningArena(field);
    if (message_arena != submessage_arena) {
      field = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, field, submessage_arena);
    }
    set_has_field();
    data_.field_ = field;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.ZoneContents.field)
}
void ZoneContents::set_allocated_headland(::carbon::portal::farm::HeadlandData* headland) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (headland) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::HeadlandData>::GetOwningArena(headland);
    if (message_arena != submessage_arena) {
      headland = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, headland, submessage_arena);
    }
    set_has_headland();
    data_.headland_ = headland;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.ZoneContents.headland)
}
void ZoneContents::set_allocated_private_road(::carbon::portal::farm::PrivateRoadData* private_road) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (private_road) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::PrivateRoadData>::GetOwningArena(private_road);
    if (message_arena != submessage_arena) {
      private_road = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, private_road, submessage_arena);
    }
    set_has_private_road();
    data_.private_road_ = private_road;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.ZoneContents.private_road)
}
void ZoneContents::set_allocated_obstacle(::carbon::portal::farm::ObstacleData* obstacle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_data();
  if (obstacle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::portal::farm::ObstacleData>::GetOwningArena(obstacle);
    if (message_arena != submessage_arena) {
      obstacle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, obstacle, submessage_arena);
    }
    set_has_obstacle();
    data_.obstacle_ = obstacle;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.ZoneContents.obstacle)
}
ZoneContents::ZoneContents(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.ZoneContents)
}
ZoneContents::ZoneContents(const ZoneContents& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_data();
  switch (from.data_case()) {
    case kFarmBoundary: {
      _internal_mutable_farm_boundary()->::carbon::portal::farm::FarmBoundaryData::MergeFrom(from._internal_farm_boundary());
      break;
    }
    case kField: {
      _internal_mutable_field()->::carbon::portal::farm::FieldData::MergeFrom(from._internal_field());
      break;
    }
    case kHeadland: {
      _internal_mutable_headland()->::carbon::portal::farm::HeadlandData::MergeFrom(from._internal_headland());
      break;
    }
    case kPrivateRoad: {
      _internal_mutable_private_road()->::carbon::portal::farm::PrivateRoadData::MergeFrom(from._internal_private_road());
      break;
    }
    case kObstacle: {
      _internal_mutable_obstacle()->::carbon::portal::farm::ObstacleData::MergeFrom(from._internal_obstacle());
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.ZoneContents)
}

inline void ZoneContents::SharedCtor() {
clear_has_data();
}

ZoneContents::~ZoneContents() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.ZoneContents)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ZoneContents::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_data()) {
    clear_data();
  }
}

void ZoneContents::ArenaDtor(void* object) {
  ZoneContents* _this = reinterpret_cast< ZoneContents* >(object);
  (void)_this;
}
void ZoneContents::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ZoneContents::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ZoneContents::clear_data() {
// @@protoc_insertion_point(one_of_clear_start:carbon.portal.farm.ZoneContents)
  switch (data_case()) {
    case kFarmBoundary: {
      if (GetArenaForAllocation() == nullptr) {
        delete data_.farm_boundary_;
      }
      break;
    }
    case kField: {
      if (GetArenaForAllocation() == nullptr) {
        delete data_.field_;
      }
      break;
    }
    case kHeadland: {
      if (GetArenaForAllocation() == nullptr) {
        delete data_.headland_;
      }
      break;
    }
    case kPrivateRoad: {
      if (GetArenaForAllocation() == nullptr) {
        delete data_.private_road_;
      }
      break;
    }
    case kObstacle: {
      if (GetArenaForAllocation() == nullptr) {
        delete data_.obstacle_;
      }
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = DATA_NOT_SET;
}


void ZoneContents::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.ZoneContents)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_data();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ZoneContents::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.portal.farm.FieldData field = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_field(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.farm.HeadlandData headland = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_headland(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.farm.PrivateRoadData private_road = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_private_road(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.farm.ObstacleData obstacle = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_obstacle(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.farm.FarmBoundaryData farm_boundary = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_farm_boundary(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ZoneContents::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.ZoneContents)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.portal.farm.FieldData field = 5;
  if (_internal_has_field()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::field(this), target, stream);
  }

  // .carbon.portal.farm.HeadlandData headland = 6;
  if (_internal_has_headland()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::headland(this), target, stream);
  }

  // .carbon.portal.farm.PrivateRoadData private_road = 7;
  if (_internal_has_private_road()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::private_road(this), target, stream);
  }

  // .carbon.portal.farm.ObstacleData obstacle = 8;
  if (_internal_has_obstacle()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::obstacle(this), target, stream);
  }

  // .carbon.portal.farm.FarmBoundaryData farm_boundary = 9;
  if (_internal_has_farm_boundary()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::farm_boundary(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.ZoneContents)
  return target;
}

size_t ZoneContents::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.ZoneContents)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (data_case()) {
    // .carbon.portal.farm.FarmBoundaryData farm_boundary = 9;
    case kFarmBoundary: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *data_.farm_boundary_);
      break;
    }
    // .carbon.portal.farm.FieldData field = 5;
    case kField: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *data_.field_);
      break;
    }
    // .carbon.portal.farm.HeadlandData headland = 6;
    case kHeadland: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *data_.headland_);
      break;
    }
    // .carbon.portal.farm.PrivateRoadData private_road = 7;
    case kPrivateRoad: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *data_.private_road_);
      break;
    }
    // .carbon.portal.farm.ObstacleData obstacle = 8;
    case kObstacle: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *data_.obstacle_);
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ZoneContents::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ZoneContents::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ZoneContents::GetClassData() const { return &_class_data_; }

void ZoneContents::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ZoneContents *>(to)->MergeFrom(
      static_cast<const ZoneContents &>(from));
}


void ZoneContents::MergeFrom(const ZoneContents& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.ZoneContents)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.data_case()) {
    case kFarmBoundary: {
      _internal_mutable_farm_boundary()->::carbon::portal::farm::FarmBoundaryData::MergeFrom(from._internal_farm_boundary());
      break;
    }
    case kField: {
      _internal_mutable_field()->::carbon::portal::farm::FieldData::MergeFrom(from._internal_field());
      break;
    }
    case kHeadland: {
      _internal_mutable_headland()->::carbon::portal::farm::HeadlandData::MergeFrom(from._internal_headland());
      break;
    }
    case kPrivateRoad: {
      _internal_mutable_private_road()->::carbon::portal::farm::PrivateRoadData::MergeFrom(from._internal_private_road());
      break;
    }
    case kObstacle: {
      _internal_mutable_obstacle()->::carbon::portal::farm::ObstacleData::MergeFrom(from._internal_obstacle());
      break;
    }
    case DATA_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ZoneContents::CopyFrom(const ZoneContents& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.ZoneContents)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ZoneContents::IsInitialized() const {
  return true;
}

void ZoneContents::InternalSwap(ZoneContents* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(data_, other->data_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata ZoneContents::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[4]);
}

// ===================================================================

class Area::_Internal {
 public:
  static const ::carbon::geo::Point& point(const Area* msg);
  static const ::carbon::geo::LineString& line_string(const Area* msg);
  static const ::carbon::geo::Polygon& polygon(const Area* msg);
};

const ::carbon::geo::Point&
Area::_Internal::point(const Area* msg) {
  return *msg->geometry_.point_;
}
const ::carbon::geo::LineString&
Area::_Internal::line_string(const Area* msg) {
  return *msg->geometry_.line_string_;
}
const ::carbon::geo::Polygon&
Area::_Internal::polygon(const Area* msg) {
  return *msg->geometry_.polygon_;
}
void Area::set_allocated_point(::carbon::geo::Point* point) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_geometry();
  if (point) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(point));
    if (message_arena != submessage_arena) {
      point = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, point, submessage_arena);
    }
    set_has_point();
    geometry_.point_ = point;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Area.point)
}
void Area::clear_point() {
  if (_internal_has_point()) {
    if (GetArenaForAllocation() == nullptr) {
      delete geometry_.point_;
    }
    clear_has_geometry();
  }
}
void Area::set_allocated_line_string(::carbon::geo::LineString* line_string) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_geometry();
  if (line_string) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(line_string));
    if (message_arena != submessage_arena) {
      line_string = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, line_string, submessage_arena);
    }
    set_has_line_string();
    geometry_.line_string_ = line_string;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Area.line_string)
}
void Area::clear_line_string() {
  if (_internal_has_line_string()) {
    if (GetArenaForAllocation() == nullptr) {
      delete geometry_.line_string_;
    }
    clear_has_geometry();
  }
}
void Area::set_allocated_polygon(::carbon::geo::Polygon* polygon) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_geometry();
  if (polygon) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(polygon));
    if (message_arena != submessage_arena) {
      polygon = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, polygon, submessage_arena);
    }
    set_has_polygon();
    geometry_.polygon_ = polygon;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.Area.polygon)
}
void Area::clear_polygon() {
  if (_internal_has_polygon()) {
    if (GetArenaForAllocation() == nullptr) {
      delete geometry_.polygon_;
    }
    clear_has_geometry();
  }
}
Area::Area(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.Area)
}
Area::Area(const Area& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  buffer_meters_ = from.buffer_meters_;
  clear_has_geometry();
  switch (from.geometry_case()) {
    case kPoint: {
      _internal_mutable_point()->::carbon::geo::Point::MergeFrom(from._internal_point());
      break;
    }
    case kLineString: {
      _internal_mutable_line_string()->::carbon::geo::LineString::MergeFrom(from._internal_line_string());
      break;
    }
    case kPolygon: {
      _internal_mutable_polygon()->::carbon::geo::Polygon::MergeFrom(from._internal_polygon());
      break;
    }
    case GEOMETRY_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.Area)
}

inline void Area::SharedCtor() {
buffer_meters_ = 0;
clear_has_geometry();
}

Area::~Area() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.Area)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Area::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_geometry()) {
    clear_geometry();
  }
}

void Area::ArenaDtor(void* object) {
  Area* _this = reinterpret_cast< Area* >(object);
  (void)_this;
}
void Area::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Area::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Area::clear_geometry() {
// @@protoc_insertion_point(one_of_clear_start:carbon.portal.farm.Area)
  switch (geometry_case()) {
    case kPoint: {
      if (GetArenaForAllocation() == nullptr) {
        delete geometry_.point_;
      }
      break;
    }
    case kLineString: {
      if (GetArenaForAllocation() == nullptr) {
        delete geometry_.line_string_;
      }
      break;
    }
    case kPolygon: {
      if (GetArenaForAllocation() == nullptr) {
        delete geometry_.polygon_;
      }
      break;
    }
    case GEOMETRY_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = GEOMETRY_NOT_SET;
}


void Area::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.Area)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  buffer_meters_ = 0;
  clear_geometry();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Area::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double buffer_meters = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          buffer_meters_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // .carbon.geo.Point point = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_point(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.geo.LineString line_string = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_line_string(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.geo.Polygon polygon = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_polygon(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Area::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.Area)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double buffer_meters = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_buffer_meters = this->_internal_buffer_meters();
  uint64_t raw_buffer_meters;
  memcpy(&raw_buffer_meters, &tmp_buffer_meters, sizeof(tmp_buffer_meters));
  if (raw_buffer_meters != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_buffer_meters(), target);
  }

  // .carbon.geo.Point point = 2;
  if (_internal_has_point()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::point(this), target, stream);
  }

  // .carbon.geo.LineString line_string = 3;
  if (_internal_has_line_string()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::line_string(this), target, stream);
  }

  // .carbon.geo.Polygon polygon = 4;
  if (_internal_has_polygon()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::polygon(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.Area)
  return target;
}

size_t Area::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.Area)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double buffer_meters = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_buffer_meters = this->_internal_buffer_meters();
  uint64_t raw_buffer_meters;
  memcpy(&raw_buffer_meters, &tmp_buffer_meters, sizeof(tmp_buffer_meters));
  if (raw_buffer_meters != 0) {
    total_size += 1 + 8;
  }

  switch (geometry_case()) {
    // .carbon.geo.Point point = 2;
    case kPoint: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *geometry_.point_);
      break;
    }
    // .carbon.geo.LineString line_string = 3;
    case kLineString: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *geometry_.line_string_);
      break;
    }
    // .carbon.geo.Polygon polygon = 4;
    case kPolygon: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *geometry_.polygon_);
      break;
    }
    case GEOMETRY_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Area::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Area::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Area::GetClassData() const { return &_class_data_; }

void Area::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Area *>(to)->MergeFrom(
      static_cast<const Area &>(from));
}


void Area::MergeFrom(const Area& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.Area)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_buffer_meters = from._internal_buffer_meters();
  uint64_t raw_buffer_meters;
  memcpy(&raw_buffer_meters, &tmp_buffer_meters, sizeof(tmp_buffer_meters));
  if (raw_buffer_meters != 0) {
    _internal_set_buffer_meters(from._internal_buffer_meters());
  }
  switch (from.geometry_case()) {
    case kPoint: {
      _internal_mutable_point()->::carbon::geo::Point::MergeFrom(from._internal_point());
      break;
    }
    case kLineString: {
      _internal_mutable_line_string()->::carbon::geo::LineString::MergeFrom(from._internal_line_string());
      break;
    }
    case kPolygon: {
      _internal_mutable_polygon()->::carbon::geo::Polygon::MergeFrom(from._internal_polygon());
      break;
    }
    case GEOMETRY_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Area::CopyFrom(const Area& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.Area)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Area::IsInitialized() const {
  return true;
}

void Area::InternalSwap(Area* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(buffer_meters_, other->buffer_meters_);
  swap(geometry_, other->geometry_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata Area::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[5]);
}

// ===================================================================

class FarmBoundaryData::_Internal {
 public:
};

FarmBoundaryData::FarmBoundaryData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.FarmBoundaryData)
}
FarmBoundaryData::FarmBoundaryData(const FarmBoundaryData& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.FarmBoundaryData)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FarmBoundaryData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FarmBoundaryData::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata FarmBoundaryData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[6]);
}

// ===================================================================

class FieldData::_Internal {
 public:
  static const ::carbon::portal::farm::PlantingHeading& planting_heading(const FieldData* msg);
  static const ::carbon::portal::farm::CenterPivot& center_pivot(const FieldData* msg);
};

const ::carbon::portal::farm::PlantingHeading&
FieldData::_Internal::planting_heading(const FieldData* msg) {
  return *msg->planting_heading_;
}
const ::carbon::portal::farm::CenterPivot&
FieldData::_Internal::center_pivot(const FieldData* msg) {
  return *msg->center_pivot_;
}
FieldData::FieldData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.FieldData)
}
FieldData::FieldData(const FieldData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_planting_heading()) {
    planting_heading_ = new ::carbon::portal::farm::PlantingHeading(*from.planting_heading_);
  } else {
    planting_heading_ = nullptr;
  }
  if (from._internal_has_center_pivot()) {
    center_pivot_ = new ::carbon::portal::farm::CenterPivot(*from.center_pivot_);
  } else {
    center_pivot_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.FieldData)
}

inline void FieldData::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&planting_heading_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&center_pivot_) -
    reinterpret_cast<char*>(&planting_heading_)) + sizeof(center_pivot_));
}

FieldData::~FieldData() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.FieldData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FieldData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete planting_heading_;
  if (this != internal_default_instance()) delete center_pivot_;
}

void FieldData::ArenaDtor(void* object) {
  FieldData* _this = reinterpret_cast< FieldData* >(object);
  (void)_this;
}
void FieldData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FieldData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FieldData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.FieldData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && planting_heading_ != nullptr) {
    delete planting_heading_;
  }
  planting_heading_ = nullptr;
  if (GetArenaForAllocation() == nullptr && center_pivot_ != nullptr) {
    delete center_pivot_;
  }
  center_pivot_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FieldData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.portal.farm.PlantingHeading planting_heading = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_planting_heading(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.portal.farm.CenterPivot center_pivot = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_center_pivot(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FieldData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.FieldData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.portal.farm.PlantingHeading planting_heading = 1;
  if (this->_internal_has_planting_heading()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::planting_heading(this), target, stream);
  }

  // .carbon.portal.farm.CenterPivot center_pivot = 2;
  if (this->_internal_has_center_pivot()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::center_pivot(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.FieldData)
  return target;
}

size_t FieldData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.FieldData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.portal.farm.PlantingHeading planting_heading = 1;
  if (this->_internal_has_planting_heading()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *planting_heading_);
  }

  // .carbon.portal.farm.CenterPivot center_pivot = 2;
  if (this->_internal_has_center_pivot()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *center_pivot_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FieldData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FieldData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FieldData::GetClassData() const { return &_class_data_; }

void FieldData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FieldData *>(to)->MergeFrom(
      static_cast<const FieldData &>(from));
}


void FieldData::MergeFrom(const FieldData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.FieldData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_planting_heading()) {
    _internal_mutable_planting_heading()->::carbon::portal::farm::PlantingHeading::MergeFrom(from._internal_planting_heading());
  }
  if (from._internal_has_center_pivot()) {
    _internal_mutable_center_pivot()->::carbon::portal::farm::CenterPivot::MergeFrom(from._internal_center_pivot());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FieldData::CopyFrom(const FieldData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.FieldData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FieldData::IsInitialized() const {
  return true;
}

void FieldData::InternalSwap(FieldData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FieldData, center_pivot_)
      + sizeof(FieldData::center_pivot_)
      - PROTOBUF_FIELD_OFFSET(FieldData, planting_heading_)>(
          reinterpret_cast<char*>(&planting_heading_),
          reinterpret_cast<char*>(&other->planting_heading_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FieldData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[7]);
}

// ===================================================================

class HeadlandData::_Internal {
 public:
};

HeadlandData::HeadlandData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.HeadlandData)
}
HeadlandData::HeadlandData(const HeadlandData& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.HeadlandData)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HeadlandData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HeadlandData::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata HeadlandData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[8]);
}

// ===================================================================

class PrivateRoadData::_Internal {
 public:
};

PrivateRoadData::PrivateRoadData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.PrivateRoadData)
}
PrivateRoadData::PrivateRoadData(const PrivateRoadData& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.PrivateRoadData)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PrivateRoadData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PrivateRoadData::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata PrivateRoadData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[9]);
}

// ===================================================================

class ObstacleData::_Internal {
 public:
};

ObstacleData::ObstacleData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.ObstacleData)
}
ObstacleData::ObstacleData(const ObstacleData& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.ObstacleData)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ObstacleData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ObstacleData::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata ObstacleData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[10]);
}

// ===================================================================

class PlantingHeading::_Internal {
 public:
  static const ::carbon::geo::AbLine& ab_line(const PlantingHeading* msg);
};

const ::carbon::geo::AbLine&
PlantingHeading::_Internal::ab_line(const PlantingHeading* msg) {
  return *msg->heading_.ab_line_;
}
void PlantingHeading::set_allocated_ab_line(::carbon::geo::AbLine* ab_line) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_heading();
  if (ab_line) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ab_line));
    if (message_arena != submessage_arena) {
      ab_line = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ab_line, submessage_arena);
    }
    set_has_ab_line();
    heading_.ab_line_ = ab_line;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.portal.farm.PlantingHeading.ab_line)
}
void PlantingHeading::clear_ab_line() {
  if (_internal_has_ab_line()) {
    if (GetArenaForAllocation() == nullptr) {
      delete heading_.ab_line_;
    }
    clear_has_heading();
  }
}
PlantingHeading::PlantingHeading(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.PlantingHeading)
}
PlantingHeading::PlantingHeading(const PlantingHeading& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_heading();
  switch (from.heading_case()) {
    case kAzimuthDegrees: {
      _internal_set_azimuth_degrees(from._internal_azimuth_degrees());
      break;
    }
    case kAbLine: {
      _internal_mutable_ab_line()->::carbon::geo::AbLine::MergeFrom(from._internal_ab_line());
      break;
    }
    case HEADING_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.PlantingHeading)
}

inline void PlantingHeading::SharedCtor() {
clear_has_heading();
}

PlantingHeading::~PlantingHeading() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.PlantingHeading)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PlantingHeading::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_heading()) {
    clear_heading();
  }
}

void PlantingHeading::ArenaDtor(void* object) {
  PlantingHeading* _this = reinterpret_cast< PlantingHeading* >(object);
  (void)_this;
}
void PlantingHeading::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PlantingHeading::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PlantingHeading::clear_heading() {
// @@protoc_insertion_point(one_of_clear_start:carbon.portal.farm.PlantingHeading)
  switch (heading_case()) {
    case kAzimuthDegrees: {
      // No need to clear
      break;
    }
    case kAbLine: {
      if (GetArenaForAllocation() == nullptr) {
        delete heading_.ab_line_;
      }
      break;
    }
    case HEADING_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = HEADING_NOT_SET;
}


void PlantingHeading::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.PlantingHeading)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_heading();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PlantingHeading::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double azimuth_degrees = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _internal_set_azimuth_degrees(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // .carbon.geo.AbLine ab_line = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ab_line(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PlantingHeading::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.PlantingHeading)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double azimuth_degrees = 1;
  if (_internal_has_azimuth_degrees()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_azimuth_degrees(), target);
  }

  // .carbon.geo.AbLine ab_line = 2;
  if (_internal_has_ab_line()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ab_line(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.PlantingHeading)
  return target;
}

size_t PlantingHeading::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.PlantingHeading)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (heading_case()) {
    // double azimuth_degrees = 1;
    case kAzimuthDegrees: {
      total_size += 1 + 8;
      break;
    }
    // .carbon.geo.AbLine ab_line = 2;
    case kAbLine: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *heading_.ab_line_);
      break;
    }
    case HEADING_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PlantingHeading::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PlantingHeading::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PlantingHeading::GetClassData() const { return &_class_data_; }

void PlantingHeading::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PlantingHeading *>(to)->MergeFrom(
      static_cast<const PlantingHeading &>(from));
}


void PlantingHeading::MergeFrom(const PlantingHeading& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.PlantingHeading)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.heading_case()) {
    case kAzimuthDegrees: {
      _internal_set_azimuth_degrees(from._internal_azimuth_degrees());
      break;
    }
    case kAbLine: {
      _internal_mutable_ab_line()->::carbon::geo::AbLine::MergeFrom(from._internal_ab_line());
      break;
    }
    case HEADING_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PlantingHeading::CopyFrom(const PlantingHeading& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.PlantingHeading)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlantingHeading::IsInitialized() const {
  return true;
}

void PlantingHeading::InternalSwap(PlantingHeading* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(heading_, other->heading_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata PlantingHeading::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[11]);
}

// ===================================================================

class CenterPivot::_Internal {
 public:
  static const ::carbon::geo::Point& center(const CenterPivot* msg);
};

const ::carbon::geo::Point&
CenterPivot::_Internal::center(const CenterPivot* msg) {
  return *msg->center_;
}
void CenterPivot::clear_center() {
  if (GetArenaForAllocation() == nullptr && center_ != nullptr) {
    delete center_;
  }
  center_ = nullptr;
}
CenterPivot::CenterPivot(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.CenterPivot)
}
CenterPivot::CenterPivot(const CenterPivot& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_center()) {
    center_ = new ::carbon::geo::Point(*from.center_);
  } else {
    center_ = nullptr;
  }
  ::memcpy(&width_meters_, &from.width_meters_,
    static_cast<size_t>(reinterpret_cast<char*>(&length_meters_) -
    reinterpret_cast<char*>(&width_meters_)) + sizeof(length_meters_));
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.CenterPivot)
}

inline void CenterPivot::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&center_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&length_meters_) -
    reinterpret_cast<char*>(&center_)) + sizeof(length_meters_));
}

CenterPivot::~CenterPivot() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.CenterPivot)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CenterPivot::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete center_;
}

void CenterPivot::ArenaDtor(void* object) {
  CenterPivot* _this = reinterpret_cast< CenterPivot* >(object);
  (void)_this;
}
void CenterPivot::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CenterPivot::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CenterPivot::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.CenterPivot)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && center_ != nullptr) {
    delete center_;
  }
  center_ = nullptr;
  ::memset(&width_meters_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&length_meters_) -
      reinterpret_cast<char*>(&width_meters_)) + sizeof(length_meters_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CenterPivot::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.Point center = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_center(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double width_meters = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          width_meters_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double length_meters = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          length_meters_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CenterPivot::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.CenterPivot)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.Point center = 1;
  if (this->_internal_has_center()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::center(this), target, stream);
  }

  // double width_meters = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_width_meters = this->_internal_width_meters();
  uint64_t raw_width_meters;
  memcpy(&raw_width_meters, &tmp_width_meters, sizeof(tmp_width_meters));
  if (raw_width_meters != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_width_meters(), target);
  }

  // double length_meters = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_length_meters = this->_internal_length_meters();
  uint64_t raw_length_meters;
  memcpy(&raw_length_meters, &tmp_length_meters, sizeof(tmp_length_meters));
  if (raw_length_meters != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_length_meters(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.CenterPivot)
  return target;
}

size_t CenterPivot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.CenterPivot)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.Point center = 1;
  if (this->_internal_has_center()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *center_);
  }

  // double width_meters = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_width_meters = this->_internal_width_meters();
  uint64_t raw_width_meters;
  memcpy(&raw_width_meters, &tmp_width_meters, sizeof(tmp_width_meters));
  if (raw_width_meters != 0) {
    total_size += 1 + 8;
  }

  // double length_meters = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_length_meters = this->_internal_length_meters();
  uint64_t raw_length_meters;
  memcpy(&raw_length_meters, &tmp_length_meters, sizeof(tmp_length_meters));
  if (raw_length_meters != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CenterPivot::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CenterPivot::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CenterPivot::GetClassData() const { return &_class_data_; }

void CenterPivot::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CenterPivot *>(to)->MergeFrom(
      static_cast<const CenterPivot &>(from));
}


void CenterPivot::MergeFrom(const CenterPivot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.CenterPivot)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_center()) {
    _internal_mutable_center()->::carbon::geo::Point::MergeFrom(from._internal_center());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_width_meters = from._internal_width_meters();
  uint64_t raw_width_meters;
  memcpy(&raw_width_meters, &tmp_width_meters, sizeof(tmp_width_meters));
  if (raw_width_meters != 0) {
    _internal_set_width_meters(from._internal_width_meters());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_length_meters = from._internal_length_meters();
  uint64_t raw_length_meters;
  memcpy(&raw_length_meters, &tmp_length_meters, sizeof(tmp_length_meters));
  if (raw_length_meters != 0) {
    _internal_set_length_meters(from._internal_length_meters());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CenterPivot::CopyFrom(const CenterPivot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.CenterPivot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CenterPivot::IsInitialized() const {
  return true;
}

void CenterPivot::InternalSwap(CenterPivot* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CenterPivot, length_meters_)
      + sizeof(CenterPivot::length_meters_)
      - PROTOBUF_FIELD_OFFSET(CenterPivot, center_)>(
          reinterpret_cast<char*>(&center_),
          reinterpret_cast<char*>(&other->center_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CenterPivot::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[12]);
}

// ===================================================================

class CreateFarmRequest::_Internal {
 public:
  static const ::carbon::portal::farm::Farm& farm(const CreateFarmRequest* msg);
};

const ::carbon::portal::farm::Farm&
CreateFarmRequest::_Internal::farm(const CreateFarmRequest* msg) {
  return *msg->farm_;
}
CreateFarmRequest::CreateFarmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.CreateFarmRequest)
}
CreateFarmRequest::CreateFarmRequest(const CreateFarmRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_farm()) {
    farm_ = new ::carbon::portal::farm::Farm(*from.farm_);
  } else {
    farm_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.CreateFarmRequest)
}

inline void CreateFarmRequest::SharedCtor() {
farm_ = nullptr;
}

CreateFarmRequest::~CreateFarmRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.CreateFarmRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CreateFarmRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete farm_;
}

void CreateFarmRequest::ArenaDtor(void* object) {
  CreateFarmRequest* _this = reinterpret_cast< CreateFarmRequest* >(object);
  (void)_this;
}
void CreateFarmRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CreateFarmRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CreateFarmRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.CreateFarmRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && farm_ != nullptr) {
    delete farm_;
  }
  farm_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CreateFarmRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.portal.farm.Farm farm = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_farm(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CreateFarmRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.CreateFarmRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.portal.farm.Farm farm = 1;
  if (this->_internal_has_farm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::farm(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.CreateFarmRequest)
  return target;
}

size_t CreateFarmRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.CreateFarmRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.portal.farm.Farm farm = 1;
  if (this->_internal_has_farm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *farm_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CreateFarmRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CreateFarmRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CreateFarmRequest::GetClassData() const { return &_class_data_; }

void CreateFarmRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CreateFarmRequest *>(to)->MergeFrom(
      static_cast<const CreateFarmRequest &>(from));
}


void CreateFarmRequest::MergeFrom(const CreateFarmRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.CreateFarmRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_farm()) {
    _internal_mutable_farm()->::carbon::portal::farm::Farm::MergeFrom(from._internal_farm());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CreateFarmRequest::CopyFrom(const CreateFarmRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.CreateFarmRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateFarmRequest::IsInitialized() const {
  return true;
}

void CreateFarmRequest::InternalSwap(CreateFarmRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(farm_, other->farm_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CreateFarmRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[13]);
}

// ===================================================================

class UpdateFarmRequest::_Internal {
 public:
  static const ::carbon::portal::farm::Farm& farm(const UpdateFarmRequest* msg);
};

const ::carbon::portal::farm::Farm&
UpdateFarmRequest::_Internal::farm(const UpdateFarmRequest* msg) {
  return *msg->farm_;
}
UpdateFarmRequest::UpdateFarmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.UpdateFarmRequest)
}
UpdateFarmRequest::UpdateFarmRequest(const UpdateFarmRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_farm()) {
    farm_ = new ::carbon::portal::farm::Farm(*from.farm_);
  } else {
    farm_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.UpdateFarmRequest)
}

inline void UpdateFarmRequest::SharedCtor() {
farm_ = nullptr;
}

UpdateFarmRequest::~UpdateFarmRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.UpdateFarmRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UpdateFarmRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete farm_;
}

void UpdateFarmRequest::ArenaDtor(void* object) {
  UpdateFarmRequest* _this = reinterpret_cast< UpdateFarmRequest* >(object);
  (void)_this;
}
void UpdateFarmRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UpdateFarmRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UpdateFarmRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.UpdateFarmRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && farm_ != nullptr) {
    delete farm_;
  }
  farm_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UpdateFarmRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.portal.farm.Farm farm = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_farm(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UpdateFarmRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.UpdateFarmRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.portal.farm.Farm farm = 1;
  if (this->_internal_has_farm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::farm(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.UpdateFarmRequest)
  return target;
}

size_t UpdateFarmRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.UpdateFarmRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.portal.farm.Farm farm = 1;
  if (this->_internal_has_farm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *farm_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UpdateFarmRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UpdateFarmRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UpdateFarmRequest::GetClassData() const { return &_class_data_; }

void UpdateFarmRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UpdateFarmRequest *>(to)->MergeFrom(
      static_cast<const UpdateFarmRequest &>(from));
}


void UpdateFarmRequest::MergeFrom(const UpdateFarmRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.UpdateFarmRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_farm()) {
    _internal_mutable_farm()->::carbon::portal::farm::Farm::MergeFrom(from._internal_farm());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UpdateFarmRequest::CopyFrom(const UpdateFarmRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.UpdateFarmRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpdateFarmRequest::IsInitialized() const {
  return true;
}

void UpdateFarmRequest::InternalSwap(UpdateFarmRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(farm_, other->farm_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UpdateFarmRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[14]);
}

// ===================================================================

class ListFarmsRequest::_Internal {
 public:
};

ListFarmsRequest::ListFarmsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.ListFarmsRequest)
}
ListFarmsRequest::ListFarmsRequest(const ListFarmsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_page_token().empty()) {
    page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_page_token(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.ListFarmsRequest)
}

inline void ListFarmsRequest::SharedCtor() {
page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ListFarmsRequest::~ListFarmsRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.ListFarmsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListFarmsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListFarmsRequest::ArenaDtor(void* object) {
  ListFarmsRequest* _this = reinterpret_cast< ListFarmsRequest* >(object);
  (void)_this;
}
void ListFarmsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListFarmsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListFarmsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.ListFarmsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  page_token_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListFarmsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string page_token = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.farm.ListFarmsRequest.page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListFarmsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.ListFarmsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_page_token().data(), static_cast<int>(this->_internal_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.farm.ListFarmsRequest.page_token");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_page_token(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.ListFarmsRequest)
  return target;
}

size_t ListFarmsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.ListFarmsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string page_token = 1;
  if (!this->_internal_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_page_token());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListFarmsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListFarmsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListFarmsRequest::GetClassData() const { return &_class_data_; }

void ListFarmsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListFarmsRequest *>(to)->MergeFrom(
      static_cast<const ListFarmsRequest &>(from));
}


void ListFarmsRequest::MergeFrom(const ListFarmsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.ListFarmsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_page_token().empty()) {
    _internal_set_page_token(from._internal_page_token());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListFarmsRequest::CopyFrom(const ListFarmsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.ListFarmsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListFarmsRequest::IsInitialized() const {
  return true;
}

void ListFarmsRequest::InternalSwap(ListFarmsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &page_token_, lhs_arena,
      &other->page_token_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ListFarmsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[15]);
}

// ===================================================================

class ListFarmsResponse::_Internal {
 public:
};

ListFarmsResponse::ListFarmsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  farms_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.ListFarmsResponse)
}
ListFarmsResponse::ListFarmsResponse(const ListFarmsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      farms_(from.farms_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  next_page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    next_page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_next_page_token().empty()) {
    next_page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_next_page_token(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.ListFarmsResponse)
}

inline void ListFarmsResponse::SharedCtor() {
next_page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  next_page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ListFarmsResponse::~ListFarmsResponse() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.ListFarmsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListFarmsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  next_page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListFarmsResponse::ArenaDtor(void* object) {
  ListFarmsResponse* _this = reinterpret_cast< ListFarmsResponse* >(object);
  (void)_this;
}
void ListFarmsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListFarmsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListFarmsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.ListFarmsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  farms_.Clear();
  next_page_token_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListFarmsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.portal.farm.Farm farms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_farms(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string next_page_token = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_next_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.portal.farm.ListFarmsResponse.next_page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListFarmsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.ListFarmsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.portal.farm.Farm farms = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_farms_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_farms(i), target, stream);
  }

  // string next_page_token = 2;
  if (!this->_internal_next_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_next_page_token().data(), static_cast<int>(this->_internal_next_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.portal.farm.ListFarmsResponse.next_page_token");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_next_page_token(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.ListFarmsResponse)
  return target;
}

size_t ListFarmsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.ListFarmsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.portal.farm.Farm farms = 1;
  total_size += 1UL * this->_internal_farms_size();
  for (const auto& msg : this->farms_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string next_page_token = 2;
  if (!this->_internal_next_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_next_page_token());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListFarmsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListFarmsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListFarmsResponse::GetClassData() const { return &_class_data_; }

void ListFarmsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListFarmsResponse *>(to)->MergeFrom(
      static_cast<const ListFarmsResponse &>(from));
}


void ListFarmsResponse::MergeFrom(const ListFarmsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.ListFarmsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  farms_.MergeFrom(from.farms_);
  if (!from._internal_next_page_token().empty()) {
    _internal_set_next_page_token(from._internal_next_page_token());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListFarmsResponse::CopyFrom(const ListFarmsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.ListFarmsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListFarmsResponse::IsInitialized() const {
  return true;
}

void ListFarmsResponse::InternalSwap(ListFarmsResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  farms_.InternalSwap(&other->farms_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &next_page_token_, lhs_arena,
      &other->next_page_token_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ListFarmsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[16]);
}

// ===================================================================

class GetFarmRequest::_Internal {
 public:
  static const ::carbon::geo::Id& id(const GetFarmRequest* msg);
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& if_modified_since(const GetFarmRequest* msg);
};

const ::carbon::geo::Id&
GetFarmRequest::_Internal::id(const GetFarmRequest* msg) {
  return *msg->id_;
}
const ::PROTOBUF_NAMESPACE_ID::Timestamp&
GetFarmRequest::_Internal::if_modified_since(const GetFarmRequest* msg) {
  return *msg->if_modified_since_;
}
void GetFarmRequest::clear_id() {
  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
}
void GetFarmRequest::clear_if_modified_since() {
  if (GetArenaForAllocation() == nullptr && if_modified_since_ != nullptr) {
    delete if_modified_since_;
  }
  if_modified_since_ = nullptr;
}
GetFarmRequest::GetFarmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.GetFarmRequest)
}
GetFarmRequest::GetFarmRequest(const GetFarmRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_id()) {
    id_ = new ::carbon::geo::Id(*from.id_);
  } else {
    id_ = nullptr;
  }
  if (from._internal_has_if_modified_since()) {
    if_modified_since_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.if_modified_since_);
  } else {
    if_modified_since_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.GetFarmRequest)
}

inline void GetFarmRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&if_modified_since_) -
    reinterpret_cast<char*>(&id_)) + sizeof(if_modified_since_));
}

GetFarmRequest::~GetFarmRequest() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.GetFarmRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetFarmRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete id_;
  if (this != internal_default_instance()) delete if_modified_since_;
}

void GetFarmRequest::ArenaDtor(void* object) {
  GetFarmRequest* _this = reinterpret_cast< GetFarmRequest* >(object);
  (void)_this;
}
void GetFarmRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetFarmRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetFarmRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.GetFarmRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
  if (GetArenaForAllocation() == nullptr && if_modified_since_ != nullptr) {
    delete if_modified_since_;
  }
  if_modified_since_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetFarmRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.Id id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_id(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp if_modified_since = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_if_modified_since(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetFarmRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.GetFarmRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.Id id = 1;
  if (this->_internal_has_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::id(this), target, stream);
  }

  // .google.protobuf.Timestamp if_modified_since = 2;
  if (this->_internal_has_if_modified_since()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::if_modified_since(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.GetFarmRequest)
  return target;
}

size_t GetFarmRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.GetFarmRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.Id id = 1;
  if (this->_internal_has_id()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *id_);
  }

  // .google.protobuf.Timestamp if_modified_since = 2;
  if (this->_internal_has_if_modified_since()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *if_modified_since_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetFarmRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetFarmRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetFarmRequest::GetClassData() const { return &_class_data_; }

void GetFarmRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetFarmRequest *>(to)->MergeFrom(
      static_cast<const GetFarmRequest &>(from));
}


void GetFarmRequest::MergeFrom(const GetFarmRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.GetFarmRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_id()) {
    _internal_mutable_id()->::carbon::geo::Id::MergeFrom(from._internal_id());
  }
  if (from._internal_has_if_modified_since()) {
    _internal_mutable_if_modified_since()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_if_modified_since());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetFarmRequest::CopyFrom(const GetFarmRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.GetFarmRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetFarmRequest::IsInitialized() const {
  return true;
}

void GetFarmRequest::InternalSwap(GetFarmRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetFarmRequest, if_modified_since_)
      + sizeof(GetFarmRequest::if_modified_since_)
      - PROTOBUF_FIELD_OFFSET(GetFarmRequest, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetFarmRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[17]);
}

// ===================================================================

class GetFarmResponse::_Internal {
 public:
  static const ::carbon::portal::farm::Farm& farm(const GetFarmResponse* msg);
};

const ::carbon::portal::farm::Farm&
GetFarmResponse::_Internal::farm(const GetFarmResponse* msg) {
  return *msg->farm_;
}
GetFarmResponse::GetFarmResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.portal.farm.GetFarmResponse)
}
GetFarmResponse::GetFarmResponse(const GetFarmResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_farm()) {
    farm_ = new ::carbon::portal::farm::Farm(*from.farm_);
  } else {
    farm_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.portal.farm.GetFarmResponse)
}

inline void GetFarmResponse::SharedCtor() {
farm_ = nullptr;
}

GetFarmResponse::~GetFarmResponse() {
  // @@protoc_insertion_point(destructor:carbon.portal.farm.GetFarmResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetFarmResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete farm_;
}

void GetFarmResponse::ArenaDtor(void* object) {
  GetFarmResponse* _this = reinterpret_cast< GetFarmResponse* >(object);
  (void)_this;
}
void GetFarmResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetFarmResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetFarmResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.portal.farm.GetFarmResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && farm_ != nullptr) {
    delete farm_;
  }
  farm_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetFarmResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.portal.farm.Farm farm = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_farm(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetFarmResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.portal.farm.GetFarmResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.portal.farm.Farm farm = 1;
  if (this->_internal_has_farm()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::farm(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.portal.farm.GetFarmResponse)
  return target;
}

size_t GetFarmResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.portal.farm.GetFarmResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.portal.farm.Farm farm = 1;
  if (this->_internal_has_farm()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *farm_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetFarmResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetFarmResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetFarmResponse::GetClassData() const { return &_class_data_; }

void GetFarmResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetFarmResponse *>(to)->MergeFrom(
      static_cast<const GetFarmResponse &>(from));
}


void GetFarmResponse::MergeFrom(const GetFarmResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.portal.farm.GetFarmResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_farm()) {
    _internal_mutable_farm()->::carbon::portal::farm::Farm::MergeFrom(from._internal_farm());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetFarmResponse::CopyFrom(const GetFarmResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.portal.farm.GetFarmResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetFarmResponse::IsInitialized() const {
  return true;
}

void GetFarmResponse::InternalSwap(GetFarmResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(farm_, other->farm_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetFarmResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_portal_2fproto_2ffarm_2eproto_getter, &descriptor_table_portal_2fproto_2ffarm_2eproto_once,
      file_level_metadata_portal_2fproto_2ffarm_2eproto[18]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace farm
}  // namespace portal
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::Farm* Arena::CreateMaybeMessage< ::carbon::portal::farm::Farm >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::Farm >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::VersionInfo* Arena::CreateMaybeMessage< ::carbon::portal::farm::VersionInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::VersionInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::PointDefinition* Arena::CreateMaybeMessage< ::carbon::portal::farm::PointDefinition >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::PointDefinition >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::Zone* Arena::CreateMaybeMessage< ::carbon::portal::farm::Zone >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::Zone >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::ZoneContents* Arena::CreateMaybeMessage< ::carbon::portal::farm::ZoneContents >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::ZoneContents >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::Area* Arena::CreateMaybeMessage< ::carbon::portal::farm::Area >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::Area >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::FarmBoundaryData* Arena::CreateMaybeMessage< ::carbon::portal::farm::FarmBoundaryData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::FarmBoundaryData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::FieldData* Arena::CreateMaybeMessage< ::carbon::portal::farm::FieldData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::FieldData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::HeadlandData* Arena::CreateMaybeMessage< ::carbon::portal::farm::HeadlandData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::HeadlandData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::PrivateRoadData* Arena::CreateMaybeMessage< ::carbon::portal::farm::PrivateRoadData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::PrivateRoadData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::ObstacleData* Arena::CreateMaybeMessage< ::carbon::portal::farm::ObstacleData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::ObstacleData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::PlantingHeading* Arena::CreateMaybeMessage< ::carbon::portal::farm::PlantingHeading >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::PlantingHeading >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::CenterPivot* Arena::CreateMaybeMessage< ::carbon::portal::farm::CenterPivot >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::CenterPivot >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::CreateFarmRequest* Arena::CreateMaybeMessage< ::carbon::portal::farm::CreateFarmRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::CreateFarmRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::UpdateFarmRequest* Arena::CreateMaybeMessage< ::carbon::portal::farm::UpdateFarmRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::UpdateFarmRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::ListFarmsRequest* Arena::CreateMaybeMessage< ::carbon::portal::farm::ListFarmsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::ListFarmsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::ListFarmsResponse* Arena::CreateMaybeMessage< ::carbon::portal::farm::ListFarmsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::ListFarmsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::GetFarmRequest* Arena::CreateMaybeMessage< ::carbon::portal::farm::GetFarmRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::GetFarmRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::portal::farm::GetFarmResponse* Arena::CreateMaybeMessage< ::carbon::portal::farm::GetFarmResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::portal::farm::GetFarmResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

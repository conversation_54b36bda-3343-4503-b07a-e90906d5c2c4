# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/profile_sync_portal.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2
from generated.frontend.proto import profile_sync_pb2 as frontend_dot_proto_dot_profile__sync__pb2
from generated.proto.almanac import almanac_pb2 as proto_dot_almanac_dot_almanac__pb2
from generated.proto.thinning import thinning_pb2 as proto_dot_thinning_dot_thinning__pb2
from generated.frontend.proto import banding_pb2 as frontend_dot_proto_dot_banding__pb2
from generated.proto.target_velocity_estimator import target_velocity_estimator_pb2 as proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/profile_sync_portal.proto',
  package='carbon.portal.profile_sync',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n&portal/proto/profile_sync_portal.proto\x12\x1a\x63\x61rbon.portal.profile_sync\x1a\x17portal/proto/util.proto\x1a!frontend/proto/profile_sync.proto\x1a\x1bproto/almanac/almanac.proto\x1a\x1dproto/thinning/thinning.proto\x1a\x1c\x66rontend/proto/banding.proto\x1a?proto/target_velocity_estimator/target_velocity_estimator.proto\",\n\x16GetProfilesDataRequest\x12\x12\n\nrobot_name\x18\x01 \x01(\t\"\xce\x01\n\x17GetProfilesDataResponse\x12S\n\x08profiles\x18\x01 \x03(\x0b\x32\x41.carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry\x1a^\n\rProfilesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12<\n\x05value\x18\x02 \x01(\x0b\x32-.carbon.frontend.profile_sync.ProfileSyncData:\x02\x38\x01\"\xcb\x03\n\x14UploadProfileRequest\x12\x1b\n\x13last_update_time_ms\x18\x01 \x01(\x03\x12\x12\n\nrobot_name\x18\x02 \x01(\t\x12\x37\n\x07\x61lmanac\x18\x03 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfigH\x00\x12\x43\n\rdiscriminator\x18\x04 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfigH\x00\x12?\n\x0bmodelinator\x18\x05 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfigH\x00\x12\x36\n\x07\x62\x61nding\x18\x06 \x01(\x0b\x32#.carbon.frontend.banding.BandingDefH\x00\x12\x35\n\x08thinning\x18\x07 \x01(\x0b\x32!.carbon.thinning.ConfigDefinitionH\x00\x12I\n\ntarget_vel\x18\x08 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfileH\x00\x42\t\n\x07profile\"!\n\x11GetProfileRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"\x98\x03\n\x12GetProfileResponse\x12\x37\n\x07\x61lmanac\x18\x01 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfigH\x00\x12\x43\n\rdiscriminator\x18\x02 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfigH\x00\x12?\n\x0bmodelinator\x18\x03 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfigH\x00\x12\x36\n\x07\x62\x61nding\x18\x04 \x01(\x0b\x32#.carbon.frontend.banding.BandingDefH\x00\x12\x35\n\x08thinning\x18\x05 \x01(\x0b\x32!.carbon.thinning.ConfigDefinitionH\x00\x12I\n\ntarget_vel\x18\x06 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfileH\x00\x42\t\n\x07profile\"$\n\x14\x44\x65leteProfileRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"#\n\x13PurgeProfileRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"h\n\x17SetActiveProfileCommand\x12?\n\x0cprofile_type\x18\x01 \x01(\x0e\x32).carbon.frontend.profile_sync.ProfileType\x12\x0c\n\x04uuid\x18\x02 \x01(\t\"3\n\"GetSetActiveProfileCommandsRequest\x12\r\n\x05robot\x18\x01 \x01(\t\"l\n#GetSetActiveProfileCommandsResponse\x12\x45\n\x08\x63ommands\x18\x01 \x03(\x0b\x32\x33.carbon.portal.profile_sync.SetActiveProfileCommand\"|\n$PurgeSetActiveProfileCommandsRequest\x12\r\n\x05robot\x18\x01 \x01(\t\x12\x45\n\x08\x63ommands\x18\x02 \x03(\x0b\x32\x33.carbon.portal.profile_sync.SetActiveProfileCommand2\xba\x06\n\x18PortalProfileSyncService\x12z\n\x0fGetProfilesData\x12\x32.carbon.portal.profile_sync.GetProfilesDataRequest\x1a\x33.carbon.portal.profile_sync.GetProfilesDataResponse\x12\\\n\rUploadProfile\x12\x30.carbon.portal.profile_sync.UploadProfileRequest\x1a\x19.carbon.portal.util.Empty\x12k\n\nGetProfile\x12-.carbon.portal.profile_sync.GetProfileRequest\x1a..carbon.portal.profile_sync.GetProfileResponse\x12\\\n\rDeleteProfile\x12\x30.carbon.portal.profile_sync.DeleteProfileRequest\x1a\x19.carbon.portal.util.Empty\x12Z\n\x0cPurgeProfile\x12/.carbon.portal.profile_sync.PurgeProfileRequest\x1a\x19.carbon.portal.util.Empty\x12\x9e\x01\n\x1bGetSetActiveProfileCommands\x12>.carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest\x1a?.carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse\x12|\n\x1dPurgeSetActiveProfileCommands\<EMAIL>.profile_sync.PurgeSetActiveProfileCommandsRequest\x1a\x19.carbon.portal.util.EmptyB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[portal_dot_proto_dot_util__pb2.DESCRIPTOR,frontend_dot_proto_dot_profile__sync__pb2.DESCRIPTOR,proto_dot_almanac_dot_almanac__pb2.DESCRIPTOR,proto_dot_thinning_dot_thinning__pb2.DESCRIPTOR,frontend_dot_proto_dot_banding__pb2.DESCRIPTOR,proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2.DESCRIPTOR,])




_GETPROFILESDATAREQUEST = _descriptor.Descriptor(
  name='GetProfilesDataRequest',
  full_name='carbon.portal.profile_sync.GetProfilesDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot_name', full_name='carbon.portal.profile_sync.GetProfilesDataRequest.robot_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=285,
  serialized_end=329,
)


_GETPROFILESDATARESPONSE_PROFILESENTRY = _descriptor.Descriptor(
  name='ProfilesEntry',
  full_name='carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=444,
  serialized_end=538,
)

_GETPROFILESDATARESPONSE = _descriptor.Descriptor(
  name='GetProfilesDataResponse',
  full_name='carbon.portal.profile_sync.GetProfilesDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='profiles', full_name='carbon.portal.profile_sync.GetProfilesDataResponse.profiles', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETPROFILESDATARESPONSE_PROFILESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=332,
  serialized_end=538,
)


_UPLOADPROFILEREQUEST = _descriptor.Descriptor(
  name='UploadProfileRequest',
  full_name='carbon.portal.profile_sync.UploadProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='last_update_time_ms', full_name='carbon.portal.profile_sync.UploadProfileRequest.last_update_time_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_name', full_name='carbon.portal.profile_sync.UploadProfileRequest.robot_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='almanac', full_name='carbon.portal.profile_sync.UploadProfileRequest.almanac', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='discriminator', full_name='carbon.portal.profile_sync.UploadProfileRequest.discriminator', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='modelinator', full_name='carbon.portal.profile_sync.UploadProfileRequest.modelinator', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding', full_name='carbon.portal.profile_sync.UploadProfileRequest.banding', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning', full_name='carbon.portal.profile_sync.UploadProfileRequest.thinning', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_vel', full_name='carbon.portal.profile_sync.UploadProfileRequest.target_vel', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='profile', full_name='carbon.portal.profile_sync.UploadProfileRequest.profile',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=541,
  serialized_end=1000,
)


_GETPROFILEREQUEST = _descriptor.Descriptor(
  name='GetProfileRequest',
  full_name='carbon.portal.profile_sync.GetProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.portal.profile_sync.GetProfileRequest.uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1002,
  serialized_end=1035,
)


_GETPROFILERESPONSE = _descriptor.Descriptor(
  name='GetProfileResponse',
  full_name='carbon.portal.profile_sync.GetProfileResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='almanac', full_name='carbon.portal.profile_sync.GetProfileResponse.almanac', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='discriminator', full_name='carbon.portal.profile_sync.GetProfileResponse.discriminator', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='modelinator', full_name='carbon.portal.profile_sync.GetProfileResponse.modelinator', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding', full_name='carbon.portal.profile_sync.GetProfileResponse.banding', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning', full_name='carbon.portal.profile_sync.GetProfileResponse.thinning', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_vel', full_name='carbon.portal.profile_sync.GetProfileResponse.target_vel', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='profile', full_name='carbon.portal.profile_sync.GetProfileResponse.profile',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1038,
  serialized_end=1446,
)


_DELETEPROFILEREQUEST = _descriptor.Descriptor(
  name='DeleteProfileRequest',
  full_name='carbon.portal.profile_sync.DeleteProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.portal.profile_sync.DeleteProfileRequest.uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1448,
  serialized_end=1484,
)


_PURGEPROFILEREQUEST = _descriptor.Descriptor(
  name='PurgeProfileRequest',
  full_name='carbon.portal.profile_sync.PurgeProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.portal.profile_sync.PurgeProfileRequest.uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1486,
  serialized_end=1521,
)


_SETACTIVEPROFILECOMMAND = _descriptor.Descriptor(
  name='SetActiveProfileCommand',
  full_name='carbon.portal.profile_sync.SetActiveProfileCommand',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='profile_type', full_name='carbon.portal.profile_sync.SetActiveProfileCommand.profile_type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.portal.profile_sync.SetActiveProfileCommand.uuid', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1523,
  serialized_end=1627,
)


_GETSETACTIVEPROFILECOMMANDSREQUEST = _descriptor.Descriptor(
  name='GetSetActiveProfileCommandsRequest',
  full_name='carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot', full_name='carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest.robot', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1629,
  serialized_end=1680,
)


_GETSETACTIVEPROFILECOMMANDSRESPONSE = _descriptor.Descriptor(
  name='GetSetActiveProfileCommandsResponse',
  full_name='carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='commands', full_name='carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse.commands', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1682,
  serialized_end=1790,
)


_PURGESETACTIVEPROFILECOMMANDSREQUEST = _descriptor.Descriptor(
  name='PurgeSetActiveProfileCommandsRequest',
  full_name='carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot', full_name='carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.robot', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='commands', full_name='carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest.commands', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1792,
  serialized_end=1916,
)

_GETPROFILESDATARESPONSE_PROFILESENTRY.fields_by_name['value'].message_type = frontend_dot_proto_dot_profile__sync__pb2._PROFILESYNCDATA
_GETPROFILESDATARESPONSE_PROFILESENTRY.containing_type = _GETPROFILESDATARESPONSE
_GETPROFILESDATARESPONSE.fields_by_name['profiles'].message_type = _GETPROFILESDATARESPONSE_PROFILESENTRY
_UPLOADPROFILEREQUEST.fields_by_name['almanac'].message_type = proto_dot_almanac_dot_almanac__pb2._ALMANACCONFIG
_UPLOADPROFILEREQUEST.fields_by_name['discriminator'].message_type = proto_dot_almanac_dot_almanac__pb2._DISCRIMINATORCONFIG
_UPLOADPROFILEREQUEST.fields_by_name['modelinator'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_UPLOADPROFILEREQUEST.fields_by_name['banding'].message_type = frontend_dot_proto_dot_banding__pb2._BANDINGDEF
_UPLOADPROFILEREQUEST.fields_by_name['thinning'].message_type = proto_dot_thinning_dot_thinning__pb2._CONFIGDEFINITION
_UPLOADPROFILEREQUEST.fields_by_name['target_vel'].message_type = proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2._TVEPROFILE
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['almanac'])
_UPLOADPROFILEREQUEST.fields_by_name['almanac'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['discriminator'])
_UPLOADPROFILEREQUEST.fields_by_name['discriminator'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['modelinator'])
_UPLOADPROFILEREQUEST.fields_by_name['modelinator'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['banding'])
_UPLOADPROFILEREQUEST.fields_by_name['banding'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['thinning'])
_UPLOADPROFILEREQUEST.fields_by_name['thinning'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['target_vel'])
_UPLOADPROFILEREQUEST.fields_by_name['target_vel'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_GETPROFILERESPONSE.fields_by_name['almanac'].message_type = proto_dot_almanac_dot_almanac__pb2._ALMANACCONFIG
_GETPROFILERESPONSE.fields_by_name['discriminator'].message_type = proto_dot_almanac_dot_almanac__pb2._DISCRIMINATORCONFIG
_GETPROFILERESPONSE.fields_by_name['modelinator'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_GETPROFILERESPONSE.fields_by_name['banding'].message_type = frontend_dot_proto_dot_banding__pb2._BANDINGDEF
_GETPROFILERESPONSE.fields_by_name['thinning'].message_type = proto_dot_thinning_dot_thinning__pb2._CONFIGDEFINITION
_GETPROFILERESPONSE.fields_by_name['target_vel'].message_type = proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2._TVEPROFILE
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['almanac'])
_GETPROFILERESPONSE.fields_by_name['almanac'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['discriminator'])
_GETPROFILERESPONSE.fields_by_name['discriminator'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['modelinator'])
_GETPROFILERESPONSE.fields_by_name['modelinator'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['banding'])
_GETPROFILERESPONSE.fields_by_name['banding'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['thinning'])
_GETPROFILERESPONSE.fields_by_name['thinning'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['target_vel'])
_GETPROFILERESPONSE.fields_by_name['target_vel'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_SETACTIVEPROFILECOMMAND.fields_by_name['profile_type'].enum_type = frontend_dot_proto_dot_profile__sync__pb2._PROFILETYPE
_GETSETACTIVEPROFILECOMMANDSRESPONSE.fields_by_name['commands'].message_type = _SETACTIVEPROFILECOMMAND
_PURGESETACTIVEPROFILECOMMANDSREQUEST.fields_by_name['commands'].message_type = _SETACTIVEPROFILECOMMAND
DESCRIPTOR.message_types_by_name['GetProfilesDataRequest'] = _GETPROFILESDATAREQUEST
DESCRIPTOR.message_types_by_name['GetProfilesDataResponse'] = _GETPROFILESDATARESPONSE
DESCRIPTOR.message_types_by_name['UploadProfileRequest'] = _UPLOADPROFILEREQUEST
DESCRIPTOR.message_types_by_name['GetProfileRequest'] = _GETPROFILEREQUEST
DESCRIPTOR.message_types_by_name['GetProfileResponse'] = _GETPROFILERESPONSE
DESCRIPTOR.message_types_by_name['DeleteProfileRequest'] = _DELETEPROFILEREQUEST
DESCRIPTOR.message_types_by_name['PurgeProfileRequest'] = _PURGEPROFILEREQUEST
DESCRIPTOR.message_types_by_name['SetActiveProfileCommand'] = _SETACTIVEPROFILECOMMAND
DESCRIPTOR.message_types_by_name['GetSetActiveProfileCommandsRequest'] = _GETSETACTIVEPROFILECOMMANDSREQUEST
DESCRIPTOR.message_types_by_name['GetSetActiveProfileCommandsResponse'] = _GETSETACTIVEPROFILECOMMANDSRESPONSE
DESCRIPTOR.message_types_by_name['PurgeSetActiveProfileCommandsRequest'] = _PURGESETACTIVEPROFILECOMMANDSREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetProfilesDataRequest = _reflection.GeneratedProtocolMessageType('GetProfilesDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPROFILESDATAREQUEST,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetProfilesDataRequest)
  })
_sym_db.RegisterMessage(GetProfilesDataRequest)

GetProfilesDataResponse = _reflection.GeneratedProtocolMessageType('GetProfilesDataResponse', (_message.Message,), {

  'ProfilesEntry' : _reflection.GeneratedProtocolMessageType('ProfilesEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETPROFILESDATARESPONSE_PROFILESENTRY,
    '__module__' : 'portal.proto.profile_sync_portal_pb2'
    # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetProfilesDataResponse.ProfilesEntry)
    })
  ,
  'DESCRIPTOR' : _GETPROFILESDATARESPONSE,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetProfilesDataResponse)
  })
_sym_db.RegisterMessage(GetProfilesDataResponse)
_sym_db.RegisterMessage(GetProfilesDataResponse.ProfilesEntry)

UploadProfileRequest = _reflection.GeneratedProtocolMessageType('UploadProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADPROFILEREQUEST,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.UploadProfileRequest)
  })
_sym_db.RegisterMessage(UploadProfileRequest)

GetProfileRequest = _reflection.GeneratedProtocolMessageType('GetProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPROFILEREQUEST,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetProfileRequest)
  })
_sym_db.RegisterMessage(GetProfileRequest)

GetProfileResponse = _reflection.GeneratedProtocolMessageType('GetProfileResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETPROFILERESPONSE,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetProfileResponse)
  })
_sym_db.RegisterMessage(GetProfileResponse)

DeleteProfileRequest = _reflection.GeneratedProtocolMessageType('DeleteProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETEPROFILEREQUEST,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.DeleteProfileRequest)
  })
_sym_db.RegisterMessage(DeleteProfileRequest)

PurgeProfileRequest = _reflection.GeneratedProtocolMessageType('PurgeProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _PURGEPROFILEREQUEST,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.PurgeProfileRequest)
  })
_sym_db.RegisterMessage(PurgeProfileRequest)

SetActiveProfileCommand = _reflection.GeneratedProtocolMessageType('SetActiveProfileCommand', (_message.Message,), {
  'DESCRIPTOR' : _SETACTIVEPROFILECOMMAND,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.SetActiveProfileCommand)
  })
_sym_db.RegisterMessage(SetActiveProfileCommand)

GetSetActiveProfileCommandsRequest = _reflection.GeneratedProtocolMessageType('GetSetActiveProfileCommandsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSETACTIVEPROFILECOMMANDSREQUEST,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetSetActiveProfileCommandsRequest)
  })
_sym_db.RegisterMessage(GetSetActiveProfileCommandsRequest)

GetSetActiveProfileCommandsResponse = _reflection.GeneratedProtocolMessageType('GetSetActiveProfileCommandsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSETACTIVEPROFILECOMMANDSRESPONSE,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.GetSetActiveProfileCommandsResponse)
  })
_sym_db.RegisterMessage(GetSetActiveProfileCommandsResponse)

PurgeSetActiveProfileCommandsRequest = _reflection.GeneratedProtocolMessageType('PurgeSetActiveProfileCommandsRequest', (_message.Message,), {
  'DESCRIPTOR' : _PURGESETACTIVEPROFILECOMMANDSREQUEST,
  '__module__' : 'portal.proto.profile_sync_portal_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.profile_sync.PurgeSetActiveProfileCommandsRequest)
  })
_sym_db.RegisterMessage(PurgeSetActiveProfileCommandsRequest)


DESCRIPTOR._options = None
_GETPROFILESDATARESPONSE_PROFILESENTRY._options = None

_PORTALPROFILESYNCSERVICE = _descriptor.ServiceDescriptor(
  name='PortalProfileSyncService',
  full_name='carbon.portal.profile_sync.PortalProfileSyncService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1919,
  serialized_end=2745,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetProfilesData',
    full_name='carbon.portal.profile_sync.PortalProfileSyncService.GetProfilesData',
    index=0,
    containing_service=None,
    input_type=_GETPROFILESDATAREQUEST,
    output_type=_GETPROFILESDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UploadProfile',
    full_name='carbon.portal.profile_sync.PortalProfileSyncService.UploadProfile',
    index=1,
    containing_service=None,
    input_type=_UPLOADPROFILEREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetProfile',
    full_name='carbon.portal.profile_sync.PortalProfileSyncService.GetProfile',
    index=2,
    containing_service=None,
    input_type=_GETPROFILEREQUEST,
    output_type=_GETPROFILERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteProfile',
    full_name='carbon.portal.profile_sync.PortalProfileSyncService.DeleteProfile',
    index=3,
    containing_service=None,
    input_type=_DELETEPROFILEREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PurgeProfile',
    full_name='carbon.portal.profile_sync.PortalProfileSyncService.PurgeProfile',
    index=4,
    containing_service=None,
    input_type=_PURGEPROFILEREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSetActiveProfileCommands',
    full_name='carbon.portal.profile_sync.PortalProfileSyncService.GetSetActiveProfileCommands',
    index=5,
    containing_service=None,
    input_type=_GETSETACTIVEPROFILECOMMANDSREQUEST,
    output_type=_GETSETACTIVEPROFILECOMMANDSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PurgeSetActiveProfileCommands',
    full_name='carbon.portal.profile_sync.PortalProfileSyncService.PurgeSetActiveProfileCommands',
    index=6,
    containing_service=None,
    input_type=_PURGESETACTIVEPROFILECOMMANDSREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_PORTALPROFILESYNCSERVICE)

DESCRIPTOR.services_by_name['PortalProfileSyncService'] = _PORTALPROFILESYNCSERVICE

# @@protoc_insertion_point(module_scope)

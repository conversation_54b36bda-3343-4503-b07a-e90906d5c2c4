# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: portal/proto/farm.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.proto.geo import geo_pb2 as proto_dot_geo_dot_geo__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='portal/proto/farm.proto',
  package='carbon.portal.farm',
  syntax='proto3',
  serialized_options=b'Z\014proto/portal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x17portal/proto/farm.proto\x12\x12\x63\x61rbon.portal.farm\x1a\x13proto/geo/geo.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd9\x01\n\x04\x46\x61rm\x12\x1a\n\x02id\x18\x01 \x01(\x0b\x32\x0e.carbon.geo.Id\x12\x30\n\x07version\x18\x02 \x01(\x0b\x32\x1f.carbon.portal.farm.VersionInfo\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x13\n\x0b\x63ustomer_id\x18\x04 \x01(\x03\x12\x37\n\npoint_defs\x18\x05 \x03(\x0b\x32#.carbon.portal.farm.PointDefinition\x12\'\n\x05zones\x18\x06 \x03(\x0b\x32\x18.carbon.portal.farm.Zone\"q\n\x0bVersionInfo\x12\x0f\n\x07ordinal\x18\x01 \x01(\x03\x12/\n\x0bupdate_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07\x64\x65leted\x18\x03 \x01(\x08\x12\x0f\n\x07\x63hanged\x18\x04 \x01(\x08\"e\n\x0fPointDefinition\x12 \n\x05point\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x30\n\x07version\x18\x02 \x01(\x0b\x32\x1f.carbon.portal.farm.VersionInfo\"\xbf\x01\n\x04Zone\x12\x1a\n\x02id\x18\x01 \x01(\x0b\x32\x0e.carbon.geo.Id\x12\x30\n\x07version\x18\x02 \x01(\x0b\x32\x1f.carbon.portal.farm.VersionInfo\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\'\n\x05\x61reas\x18\x04 \x03(\x0b\x32\x18.carbon.portal.farm.Area\x12\x32\n\x08\x63ontents\x18\x05 \x01(\x0b\x32 .carbon.portal.farm.ZoneContents\"\xae\x02\n\x0cZoneContents\x12=\n\rfarm_boundary\x18\t \x01(\x0b\x32$.carbon.portal.farm.FarmBoundaryDataH\x00\x12.\n\x05\x66ield\x18\x05 \x01(\x0b\x32\x1d.carbon.portal.farm.FieldDataH\x00\x12\x34\n\x08headland\x18\x06 \x01(\x0b\x32 .carbon.portal.farm.HeadlandDataH\x00\x12;\n\x0cprivate_road\x18\x07 \x01(\x0b\x32#.carbon.portal.farm.PrivateRoadDataH\x00\x12\x34\n\x08obstacle\x18\x08 \x01(\x0b\x32 .carbon.portal.farm.ObstacleDataH\x00\x42\x06\n\x04\x64\x61ta\"\xa4\x01\n\x04\x41rea\x12\x15\n\rbuffer_meters\x18\x01 \x01(\x01\x12\"\n\x05point\x18\x02 \x01(\x0b\x32\x11.carbon.geo.PointH\x00\x12-\n\x0bline_string\x18\x03 \x01(\x0b\x32\x16.carbon.geo.LineStringH\x00\x12&\n\x07polygon\x18\x04 \x01(\x0b\x32\x13.carbon.geo.PolygonH\x00\x42\n\n\x08geometry\"\x12\n\x10\x46\x61rmBoundaryData\"\x81\x01\n\tFieldData\x12=\n\x10planting_heading\x18\x01 \x01(\x0b\x32#.carbon.portal.farm.PlantingHeading\x12\x35\n\x0c\x63\x65nter_pivot\x18\x02 \x01(\x0b\x32\x1f.carbon.portal.farm.CenterPivot\"\x0e\n\x0cHeadlandData\"\x11\n\x0fPrivateRoadData\"\x0e\n\x0cObstacleData\"^\n\x0fPlantingHeading\x12\x19\n\x0f\x61zimuth_degrees\x18\x01 \x01(\x01H\x00\x12%\n\x07\x61\x62_line\x18\x02 \x01(\x0b\x32\x12.carbon.geo.AbLineH\x00\x42\t\n\x07heading\"]\n\x0b\x43\x65nterPivot\x12!\n\x06\x63\x65nter\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x14\n\x0cwidth_meters\x18\x02 \x01(\x01\x12\x15\n\rlength_meters\x18\x03 \x01(\x01\";\n\x11\x43reateFarmRequest\x12&\n\x04\x66\x61rm\x18\x01 \x01(\x0b\x32\x18.carbon.portal.farm.Farm\";\n\x11UpdateFarmRequest\x12&\n\x04\x66\x61rm\x18\x01 \x01(\x0b\x32\x18.carbon.portal.farm.Farm\"&\n\x10ListFarmsRequest\x12\x12\n\npage_token\x18\x01 \x01(\t\"U\n\x11ListFarmsResponse\x12\'\n\x05\x66\x61rms\x18\x01 \x03(\x0b\x32\x18.carbon.portal.farm.Farm\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"c\n\x0eGetFarmRequest\x12\x1a\n\x02id\x18\x01 \x01(\x0b\x32\x0e.carbon.geo.Id\x12\x35\n\x11if_modified_since\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"9\n\x0fGetFarmResponse\x12&\n\x04\x66\x61rm\x18\x01 \x01(\x0b\x32\x18.carbon.portal.farm.Farm2b\n\x0c\x46\x61rmsService\x12R\n\x07GetFarm\x12\".carbon.portal.farm.GetFarmRequest\x1a#.carbon.portal.farm.GetFarmResponseB\x0eZ\x0cproto/portalb\x06proto3'
  ,
  dependencies=[proto_dot_geo_dot_geo__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_FARM = _descriptor.Descriptor(
  name='Farm',
  full_name='carbon.portal.farm.Farm',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.portal.farm.Farm.id', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='carbon.portal.farm.Farm.version', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.portal.farm.Farm.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='customer_id', full_name='carbon.portal.farm.Farm.customer_id', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='point_defs', full_name='carbon.portal.farm.Farm.point_defs', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='zones', full_name='carbon.portal.farm.Farm.zones', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=102,
  serialized_end=319,
)


_VERSIONINFO = _descriptor.Descriptor(
  name='VersionInfo',
  full_name='carbon.portal.farm.VersionInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ordinal', full_name='carbon.portal.farm.VersionInfo.ordinal', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='update_time', full_name='carbon.portal.farm.VersionInfo.update_time', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='carbon.portal.farm.VersionInfo.deleted', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='changed', full_name='carbon.portal.farm.VersionInfo.changed', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=321,
  serialized_end=434,
)


_POINTDEFINITION = _descriptor.Descriptor(
  name='PointDefinition',
  full_name='carbon.portal.farm.PointDefinition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='point', full_name='carbon.portal.farm.PointDefinition.point', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='carbon.portal.farm.PointDefinition.version', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=436,
  serialized_end=537,
)


_ZONE = _descriptor.Descriptor(
  name='Zone',
  full_name='carbon.portal.farm.Zone',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.portal.farm.Zone.id', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='carbon.portal.farm.Zone.version', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.portal.farm.Zone.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='areas', full_name='carbon.portal.farm.Zone.areas', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='contents', full_name='carbon.portal.farm.Zone.contents', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=540,
  serialized_end=731,
)


_ZONECONTENTS = _descriptor.Descriptor(
  name='ZoneContents',
  full_name='carbon.portal.farm.ZoneContents',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='farm_boundary', full_name='carbon.portal.farm.ZoneContents.farm_boundary', index=0,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='field', full_name='carbon.portal.farm.ZoneContents.field', index=1,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='headland', full_name='carbon.portal.farm.ZoneContents.headland', index=2,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='private_road', full_name='carbon.portal.farm.ZoneContents.private_road', index=3,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='obstacle', full_name='carbon.portal.farm.ZoneContents.obstacle', index=4,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='data', full_name='carbon.portal.farm.ZoneContents.data',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=734,
  serialized_end=1036,
)


_AREA = _descriptor.Descriptor(
  name='Area',
  full_name='carbon.portal.farm.Area',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='buffer_meters', full_name='carbon.portal.farm.Area.buffer_meters', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='point', full_name='carbon.portal.farm.Area.point', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='line_string', full_name='carbon.portal.farm.Area.line_string', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='polygon', full_name='carbon.portal.farm.Area.polygon', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='geometry', full_name='carbon.portal.farm.Area.geometry',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1039,
  serialized_end=1203,
)


_FARMBOUNDARYDATA = _descriptor.Descriptor(
  name='FarmBoundaryData',
  full_name='carbon.portal.farm.FarmBoundaryData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1205,
  serialized_end=1223,
)


_FIELDDATA = _descriptor.Descriptor(
  name='FieldData',
  full_name='carbon.portal.farm.FieldData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='planting_heading', full_name='carbon.portal.farm.FieldData.planting_heading', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_pivot', full_name='carbon.portal.farm.FieldData.center_pivot', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1226,
  serialized_end=1355,
)


_HEADLANDDATA = _descriptor.Descriptor(
  name='HeadlandData',
  full_name='carbon.portal.farm.HeadlandData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1357,
  serialized_end=1371,
)


_PRIVATEROADDATA = _descriptor.Descriptor(
  name='PrivateRoadData',
  full_name='carbon.portal.farm.PrivateRoadData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1373,
  serialized_end=1390,
)


_OBSTACLEDATA = _descriptor.Descriptor(
  name='ObstacleData',
  full_name='carbon.portal.farm.ObstacleData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1392,
  serialized_end=1406,
)


_PLANTINGHEADING = _descriptor.Descriptor(
  name='PlantingHeading',
  full_name='carbon.portal.farm.PlantingHeading',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='azimuth_degrees', full_name='carbon.portal.farm.PlantingHeading.azimuth_degrees', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ab_line', full_name='carbon.portal.farm.PlantingHeading.ab_line', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='heading', full_name='carbon.portal.farm.PlantingHeading.heading',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1408,
  serialized_end=1502,
)


_CENTERPIVOT = _descriptor.Descriptor(
  name='CenterPivot',
  full_name='carbon.portal.farm.CenterPivot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='center', full_name='carbon.portal.farm.CenterPivot.center', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width_meters', full_name='carbon.portal.farm.CenterPivot.width_meters', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='length_meters', full_name='carbon.portal.farm.CenterPivot.length_meters', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1504,
  serialized_end=1597,
)


_CREATEFARMREQUEST = _descriptor.Descriptor(
  name='CreateFarmRequest',
  full_name='carbon.portal.farm.CreateFarmRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='farm', full_name='carbon.portal.farm.CreateFarmRequest.farm', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1599,
  serialized_end=1658,
)


_UPDATEFARMREQUEST = _descriptor.Descriptor(
  name='UpdateFarmRequest',
  full_name='carbon.portal.farm.UpdateFarmRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='farm', full_name='carbon.portal.farm.UpdateFarmRequest.farm', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1660,
  serialized_end=1719,
)


_LISTFARMSREQUEST = _descriptor.Descriptor(
  name='ListFarmsRequest',
  full_name='carbon.portal.farm.ListFarmsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page_token', full_name='carbon.portal.farm.ListFarmsRequest.page_token', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1721,
  serialized_end=1759,
)


_LISTFARMSRESPONSE = _descriptor.Descriptor(
  name='ListFarmsResponse',
  full_name='carbon.portal.farm.ListFarmsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='farms', full_name='carbon.portal.farm.ListFarmsResponse.farms', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='next_page_token', full_name='carbon.portal.farm.ListFarmsResponse.next_page_token', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1761,
  serialized_end=1846,
)


_GETFARMREQUEST = _descriptor.Descriptor(
  name='GetFarmRequest',
  full_name='carbon.portal.farm.GetFarmRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.portal.farm.GetFarmRequest.id', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='if_modified_since', full_name='carbon.portal.farm.GetFarmRequest.if_modified_since', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1848,
  serialized_end=1947,
)


_GETFARMRESPONSE = _descriptor.Descriptor(
  name='GetFarmResponse',
  full_name='carbon.portal.farm.GetFarmResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='farm', full_name='carbon.portal.farm.GetFarmResponse.farm', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1949,
  serialized_end=2006,
)

_FARM.fields_by_name['id'].message_type = proto_dot_geo_dot_geo__pb2._ID
_FARM.fields_by_name['version'].message_type = _VERSIONINFO
_FARM.fields_by_name['point_defs'].message_type = _POINTDEFINITION
_FARM.fields_by_name['zones'].message_type = _ZONE
_VERSIONINFO.fields_by_name['update_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_POINTDEFINITION.fields_by_name['point'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_POINTDEFINITION.fields_by_name['version'].message_type = _VERSIONINFO
_ZONE.fields_by_name['id'].message_type = proto_dot_geo_dot_geo__pb2._ID
_ZONE.fields_by_name['version'].message_type = _VERSIONINFO
_ZONE.fields_by_name['areas'].message_type = _AREA
_ZONE.fields_by_name['contents'].message_type = _ZONECONTENTS
_ZONECONTENTS.fields_by_name['farm_boundary'].message_type = _FARMBOUNDARYDATA
_ZONECONTENTS.fields_by_name['field'].message_type = _FIELDDATA
_ZONECONTENTS.fields_by_name['headland'].message_type = _HEADLANDDATA
_ZONECONTENTS.fields_by_name['private_road'].message_type = _PRIVATEROADDATA
_ZONECONTENTS.fields_by_name['obstacle'].message_type = _OBSTACLEDATA
_ZONECONTENTS.oneofs_by_name['data'].fields.append(
  _ZONECONTENTS.fields_by_name['farm_boundary'])
_ZONECONTENTS.fields_by_name['farm_boundary'].containing_oneof = _ZONECONTENTS.oneofs_by_name['data']
_ZONECONTENTS.oneofs_by_name['data'].fields.append(
  _ZONECONTENTS.fields_by_name['field'])
_ZONECONTENTS.fields_by_name['field'].containing_oneof = _ZONECONTENTS.oneofs_by_name['data']
_ZONECONTENTS.oneofs_by_name['data'].fields.append(
  _ZONECONTENTS.fields_by_name['headland'])
_ZONECONTENTS.fields_by_name['headland'].containing_oneof = _ZONECONTENTS.oneofs_by_name['data']
_ZONECONTENTS.oneofs_by_name['data'].fields.append(
  _ZONECONTENTS.fields_by_name['private_road'])
_ZONECONTENTS.fields_by_name['private_road'].containing_oneof = _ZONECONTENTS.oneofs_by_name['data']
_ZONECONTENTS.oneofs_by_name['data'].fields.append(
  _ZONECONTENTS.fields_by_name['obstacle'])
_ZONECONTENTS.fields_by_name['obstacle'].containing_oneof = _ZONECONTENTS.oneofs_by_name['data']
_AREA.fields_by_name['point'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_AREA.fields_by_name['line_string'].message_type = proto_dot_geo_dot_geo__pb2._LINESTRING
_AREA.fields_by_name['polygon'].message_type = proto_dot_geo_dot_geo__pb2._POLYGON
_AREA.oneofs_by_name['geometry'].fields.append(
  _AREA.fields_by_name['point'])
_AREA.fields_by_name['point'].containing_oneof = _AREA.oneofs_by_name['geometry']
_AREA.oneofs_by_name['geometry'].fields.append(
  _AREA.fields_by_name['line_string'])
_AREA.fields_by_name['line_string'].containing_oneof = _AREA.oneofs_by_name['geometry']
_AREA.oneofs_by_name['geometry'].fields.append(
  _AREA.fields_by_name['polygon'])
_AREA.fields_by_name['polygon'].containing_oneof = _AREA.oneofs_by_name['geometry']
_FIELDDATA.fields_by_name['planting_heading'].message_type = _PLANTINGHEADING
_FIELDDATA.fields_by_name['center_pivot'].message_type = _CENTERPIVOT
_PLANTINGHEADING.fields_by_name['ab_line'].message_type = proto_dot_geo_dot_geo__pb2._ABLINE
_PLANTINGHEADING.oneofs_by_name['heading'].fields.append(
  _PLANTINGHEADING.fields_by_name['azimuth_degrees'])
_PLANTINGHEADING.fields_by_name['azimuth_degrees'].containing_oneof = _PLANTINGHEADING.oneofs_by_name['heading']
_PLANTINGHEADING.oneofs_by_name['heading'].fields.append(
  _PLANTINGHEADING.fields_by_name['ab_line'])
_PLANTINGHEADING.fields_by_name['ab_line'].containing_oneof = _PLANTINGHEADING.oneofs_by_name['heading']
_CENTERPIVOT.fields_by_name['center'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_CREATEFARMREQUEST.fields_by_name['farm'].message_type = _FARM
_UPDATEFARMREQUEST.fields_by_name['farm'].message_type = _FARM
_LISTFARMSRESPONSE.fields_by_name['farms'].message_type = _FARM
_GETFARMREQUEST.fields_by_name['id'].message_type = proto_dot_geo_dot_geo__pb2._ID
_GETFARMREQUEST.fields_by_name['if_modified_since'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETFARMRESPONSE.fields_by_name['farm'].message_type = _FARM
DESCRIPTOR.message_types_by_name['Farm'] = _FARM
DESCRIPTOR.message_types_by_name['VersionInfo'] = _VERSIONINFO
DESCRIPTOR.message_types_by_name['PointDefinition'] = _POINTDEFINITION
DESCRIPTOR.message_types_by_name['Zone'] = _ZONE
DESCRIPTOR.message_types_by_name['ZoneContents'] = _ZONECONTENTS
DESCRIPTOR.message_types_by_name['Area'] = _AREA
DESCRIPTOR.message_types_by_name['FarmBoundaryData'] = _FARMBOUNDARYDATA
DESCRIPTOR.message_types_by_name['FieldData'] = _FIELDDATA
DESCRIPTOR.message_types_by_name['HeadlandData'] = _HEADLANDDATA
DESCRIPTOR.message_types_by_name['PrivateRoadData'] = _PRIVATEROADDATA
DESCRIPTOR.message_types_by_name['ObstacleData'] = _OBSTACLEDATA
DESCRIPTOR.message_types_by_name['PlantingHeading'] = _PLANTINGHEADING
DESCRIPTOR.message_types_by_name['CenterPivot'] = _CENTERPIVOT
DESCRIPTOR.message_types_by_name['CreateFarmRequest'] = _CREATEFARMREQUEST
DESCRIPTOR.message_types_by_name['UpdateFarmRequest'] = _UPDATEFARMREQUEST
DESCRIPTOR.message_types_by_name['ListFarmsRequest'] = _LISTFARMSREQUEST
DESCRIPTOR.message_types_by_name['ListFarmsResponse'] = _LISTFARMSRESPONSE
DESCRIPTOR.message_types_by_name['GetFarmRequest'] = _GETFARMREQUEST
DESCRIPTOR.message_types_by_name['GetFarmResponse'] = _GETFARMRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Farm = _reflection.GeneratedProtocolMessageType('Farm', (_message.Message,), {
  'DESCRIPTOR' : _FARM,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.Farm)
  })
_sym_db.RegisterMessage(Farm)

VersionInfo = _reflection.GeneratedProtocolMessageType('VersionInfo', (_message.Message,), {
  'DESCRIPTOR' : _VERSIONINFO,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.VersionInfo)
  })
_sym_db.RegisterMessage(VersionInfo)

PointDefinition = _reflection.GeneratedProtocolMessageType('PointDefinition', (_message.Message,), {
  'DESCRIPTOR' : _POINTDEFINITION,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.PointDefinition)
  })
_sym_db.RegisterMessage(PointDefinition)

Zone = _reflection.GeneratedProtocolMessageType('Zone', (_message.Message,), {
  'DESCRIPTOR' : _ZONE,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.Zone)
  })
_sym_db.RegisterMessage(Zone)

ZoneContents = _reflection.GeneratedProtocolMessageType('ZoneContents', (_message.Message,), {
  'DESCRIPTOR' : _ZONECONTENTS,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.ZoneContents)
  })
_sym_db.RegisterMessage(ZoneContents)

Area = _reflection.GeneratedProtocolMessageType('Area', (_message.Message,), {
  'DESCRIPTOR' : _AREA,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.Area)
  })
_sym_db.RegisterMessage(Area)

FarmBoundaryData = _reflection.GeneratedProtocolMessageType('FarmBoundaryData', (_message.Message,), {
  'DESCRIPTOR' : _FARMBOUNDARYDATA,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.FarmBoundaryData)
  })
_sym_db.RegisterMessage(FarmBoundaryData)

FieldData = _reflection.GeneratedProtocolMessageType('FieldData', (_message.Message,), {
  'DESCRIPTOR' : _FIELDDATA,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.FieldData)
  })
_sym_db.RegisterMessage(FieldData)

HeadlandData = _reflection.GeneratedProtocolMessageType('HeadlandData', (_message.Message,), {
  'DESCRIPTOR' : _HEADLANDDATA,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.HeadlandData)
  })
_sym_db.RegisterMessage(HeadlandData)

PrivateRoadData = _reflection.GeneratedProtocolMessageType('PrivateRoadData', (_message.Message,), {
  'DESCRIPTOR' : _PRIVATEROADDATA,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.PrivateRoadData)
  })
_sym_db.RegisterMessage(PrivateRoadData)

ObstacleData = _reflection.GeneratedProtocolMessageType('ObstacleData', (_message.Message,), {
  'DESCRIPTOR' : _OBSTACLEDATA,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.ObstacleData)
  })
_sym_db.RegisterMessage(ObstacleData)

PlantingHeading = _reflection.GeneratedProtocolMessageType('PlantingHeading', (_message.Message,), {
  'DESCRIPTOR' : _PLANTINGHEADING,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.PlantingHeading)
  })
_sym_db.RegisterMessage(PlantingHeading)

CenterPivot = _reflection.GeneratedProtocolMessageType('CenterPivot', (_message.Message,), {
  'DESCRIPTOR' : _CENTERPIVOT,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.CenterPivot)
  })
_sym_db.RegisterMessage(CenterPivot)

CreateFarmRequest = _reflection.GeneratedProtocolMessageType('CreateFarmRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATEFARMREQUEST,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.CreateFarmRequest)
  })
_sym_db.RegisterMessage(CreateFarmRequest)

UpdateFarmRequest = _reflection.GeneratedProtocolMessageType('UpdateFarmRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEFARMREQUEST,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.UpdateFarmRequest)
  })
_sym_db.RegisterMessage(UpdateFarmRequest)

ListFarmsRequest = _reflection.GeneratedProtocolMessageType('ListFarmsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTFARMSREQUEST,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.ListFarmsRequest)
  })
_sym_db.RegisterMessage(ListFarmsRequest)

ListFarmsResponse = _reflection.GeneratedProtocolMessageType('ListFarmsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTFARMSRESPONSE,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.ListFarmsResponse)
  })
_sym_db.RegisterMessage(ListFarmsResponse)

GetFarmRequest = _reflection.GeneratedProtocolMessageType('GetFarmRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETFARMREQUEST,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.GetFarmRequest)
  })
_sym_db.RegisterMessage(GetFarmRequest)

GetFarmResponse = _reflection.GeneratedProtocolMessageType('GetFarmResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETFARMRESPONSE,
  '__module__' : 'portal.proto.farm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.portal.farm.GetFarmResponse)
  })
_sym_db.RegisterMessage(GetFarmResponse)


DESCRIPTOR._options = None

_FARMSSERVICE = _descriptor.ServiceDescriptor(
  name='FarmsService',
  full_name='carbon.portal.farm.FarmsService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=2008,
  serialized_end=2106,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetFarm',
    full_name='carbon.portal.farm.FarmsService.GetFarm',
    index=0,
    containing_service=None,
    input_type=_GETFARMREQUEST,
    output_type=_GETFARMRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_FARMSSERVICE)

DESCRIPTOR.services_by_name['FarmsService'] = _FARMSSERVICE

# @@protoc_insertion_point(module_scope)

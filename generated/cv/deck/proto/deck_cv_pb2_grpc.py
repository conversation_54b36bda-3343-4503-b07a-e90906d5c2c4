# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.cv.deck.proto import deck_cv_pb2 as cv_dot_deck_dot_proto_dot_deck__cv__pb2


class DeckCVServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextFurrowDetections = channel.unary_unary(
                '/cv.deck.proto.DeckCVService/GetNextFurrowDetections',
                request_serializer=cv_dot_deck_dot_proto_dot_deck__cv__pb2.GetNextFurrowDetectionsRequest.SerializeToString,
                response_deserializer=cv_dot_deck_dot_proto_dot_deck__cv__pb2.FurrowDetections.FromString,
                )
        self.GetNodeTiming = channel.unary_unary(
                '/cv.deck.proto.DeckCVService/GetNodeTiming',
                request_serializer=cv_dot_deck_dot_proto_dot_deck__cv__pb2.Empty.SerializeToString,
                response_deserializer=cv_dot_deck_dot_proto_dot_deck__cv__pb2.NodeTimingResponse.FromString,
                )


class DeckCVServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextFurrowDetections(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNodeTiming(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DeckCVServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextFurrowDetections': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextFurrowDetections,
                    request_deserializer=cv_dot_deck_dot_proto_dot_deck__cv__pb2.GetNextFurrowDetectionsRequest.FromString,
                    response_serializer=cv_dot_deck_dot_proto_dot_deck__cv__pb2.FurrowDetections.SerializeToString,
            ),
            'GetNodeTiming': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNodeTiming,
                    request_deserializer=cv_dot_deck_dot_proto_dot_deck__cv__pb2.Empty.FromString,
                    response_serializer=cv_dot_deck_dot_proto_dot_deck__cv__pb2.NodeTimingResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'cv.deck.proto.DeckCVService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DeckCVService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextFurrowDetections(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.deck.proto.DeckCVService/GetNextFurrowDetections',
            cv_dot_deck_dot_proto_dot_deck__cv__pb2.GetNextFurrowDetectionsRequest.SerializeToString,
            cv_dot_deck_dot_proto_dot_deck__cv__pb2.FurrowDetections.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNodeTiming(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/cv.deck.proto.DeckCVService/GetNodeTiming',
            cv_dot_deck_dot_proto_dot_deck__cv__pb2.Empty.SerializeToString,
            cv_dot_deck_dot_proto_dot_deck__cv__pb2.NodeTimingResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

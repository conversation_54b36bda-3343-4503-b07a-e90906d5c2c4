// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: cv/deck/proto/deck_cv.proto

#include "cv/deck/proto/deck_cv.pb.h"
#include "cv/deck/proto/deck_cv.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace cv {
namespace deck {
namespace proto {

static const char* DeckCVService_method_names[] = {
  "/cv.deck.proto.DeckCVService/GetNextFurrowDetections",
  "/cv.deck.proto.DeckCVService/GetNodeTiming",
};

std::unique_ptr< DeckCVService::Stub> DeckCVService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< DeckCVService::Stub> stub(new DeckCVService::Stub(channel, options));
  return stub;
}

DeckCVService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextFurrowDetections_(DeckCVService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNodeTiming_(DeckCVService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status DeckCVService::Stub::GetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::cv::deck::proto::FurrowDetections* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::deck::proto::GetNextFurrowDetectionsRequest, ::cv::deck::proto::FurrowDetections, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextFurrowDetections_, context, request, response);
}

void DeckCVService::Stub::async::GetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::cv::deck::proto::FurrowDetections* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::deck::proto::GetNextFurrowDetectionsRequest, ::cv::deck::proto::FurrowDetections, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextFurrowDetections_, context, request, response, std::move(f));
}

void DeckCVService::Stub::async::GetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::cv::deck::proto::FurrowDetections* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextFurrowDetections_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::deck::proto::FurrowDetections>* DeckCVService::Stub::PrepareAsyncGetNextFurrowDetectionsRaw(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::deck::proto::FurrowDetections, ::cv::deck::proto::GetNextFurrowDetectionsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextFurrowDetections_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::deck::proto::FurrowDetections>* DeckCVService::Stub::AsyncGetNextFurrowDetectionsRaw(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextFurrowDetectionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DeckCVService::Stub::GetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::cv::deck::proto::NodeTimingResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::cv::deck::proto::Empty, ::cv::deck::proto::NodeTimingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNodeTiming_, context, request, response);
}

void DeckCVService::Stub::async::GetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty* request, ::cv::deck::proto::NodeTimingResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::cv::deck::proto::Empty, ::cv::deck::proto::NodeTimingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNodeTiming_, context, request, response, std::move(f));
}

void DeckCVService::Stub::async::GetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty* request, ::cv::deck::proto::NodeTimingResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNodeTiming_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::cv::deck::proto::NodeTimingResponse>* DeckCVService::Stub::PrepareAsyncGetNodeTimingRaw(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::cv::deck::proto::NodeTimingResponse, ::cv::deck::proto::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNodeTiming_, context, request);
}

::grpc::ClientAsyncResponseReader< ::cv::deck::proto::NodeTimingResponse>* DeckCVService::Stub::AsyncGetNodeTimingRaw(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNodeTimingRaw(context, request, cq);
  result->StartCall();
  return result;
}

DeckCVService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DeckCVService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DeckCVService::Service, ::cv::deck::proto::GetNextFurrowDetectionsRequest, ::cv::deck::proto::FurrowDetections, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DeckCVService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::deck::proto::GetNextFurrowDetectionsRequest* req,
             ::cv::deck::proto::FurrowDetections* resp) {
               return service->GetNextFurrowDetections(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DeckCVService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DeckCVService::Service, ::cv::deck::proto::Empty, ::cv::deck::proto::NodeTimingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DeckCVService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::cv::deck::proto::Empty* req,
             ::cv::deck::proto::NodeTimingResponse* resp) {
               return service->GetNodeTiming(ctx, req, resp);
             }, this)));
}

DeckCVService::Service::~Service() {
}

::grpc::Status DeckCVService::Service::GetNextFurrowDetections(::grpc::ServerContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::cv::deck::proto::FurrowDetections* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DeckCVService::Service::GetNodeTiming(::grpc::ServerContext* context, const ::cv::deck::proto::Empty* request, ::cv::deck::proto::NodeTimingResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace cv
}  // namespace deck
}  // namespace proto


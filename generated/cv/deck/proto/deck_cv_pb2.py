# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: cv/deck/proto/deck_cv.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='cv/deck/proto/deck_cv.proto',
  package='cv.deck.proto',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1b\x63v/deck/proto/deck_cv.proto\x12\rcv.deck.proto\"c\n\x0f\x46urrowDetection\x12\x0f\n\x07start_x\x18\x01 \x01(\x02\x12\x0f\n\x07start_y\x18\x02 \x01(\x02\x12\r\n\x05\x65nd_x\x18\x03 \x01(\x02\x12\r\n\x05\x65nd_y\x18\x04 \x01(\x02\x12\x10\n\x08\x63\x61tegory\x18\x05 \x01(\t\"Y\n\x10\x46urrowDetections\x12/\n\x07\x66urrows\x18\x01 \x03(\x0b\x32\x1e.cv.deck.proto.FurrowDetection\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\"]\n\x1eGetNextFurrowDetectionsRequest\x12\x11\n\tcamera_id\x18\x01 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x02 \x01(\x03\x12\x12\n\ntimeout_ms\x18\x03 \x01(\x03\"\xfa\x01\n\nNodeTiming\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08\x66ps_mean\x18\x02 \x01(\x02\x12\x11\n\tfps_99pct\x18\x03 \x01(\x02\x12\x17\n\x0flatency_ms_mean\x18\x04 \x01(\x02\x12\x18\n\x10latency_ms_99pct\x18\x05 \x01(\x02\x12\r\n\x05state\x18\x06 \x01(\t\x12\x42\n\rstate_timings\x18\x07 \x03(\x0b\x32+.cv.deck.proto.NodeTiming.StateTimingsEntry\x1a\x33\n\x11StateTimingsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"D\n\x12NodeTimingResponse\x12.\n\x0bnode_timing\x18\x01 \x03(\x0b\x32\x19.cv.deck.proto.NodeTiming\"\x07\n\x05\x45mpty2\xc8\x01\n\rDeckCVService\x12k\n\x17GetNextFurrowDetections\x12-.cv.deck.proto.GetNextFurrowDetectionsRequest\x1a\x1f.cv.deck.proto.FurrowDetections\"\x00\x12J\n\rGetNodeTiming\x12\x14.cv.deck.proto.Empty\x1a!.cv.deck.proto.NodeTimingResponse\"\x00\x62\x06proto3'
)




_FURROWDETECTION = _descriptor.Descriptor(
  name='FurrowDetection',
  full_name='cv.deck.proto.FurrowDetection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_x', full_name='cv.deck.proto.FurrowDetection.start_x', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_y', full_name='cv.deck.proto.FurrowDetection.start_y', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_x', full_name='cv.deck.proto.FurrowDetection.end_x', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_y', full_name='cv.deck.proto.FurrowDetection.end_y', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='category', full_name='cv.deck.proto.FurrowDetection.category', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=46,
  serialized_end=145,
)


_FURROWDETECTIONS = _descriptor.Descriptor(
  name='FurrowDetections',
  full_name='cv.deck.proto.FurrowDetections',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='furrows', full_name='cv.deck.proto.FurrowDetections.furrows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.deck.proto.FurrowDetections.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=147,
  serialized_end=236,
)


_GETNEXTFURROWDETECTIONSREQUEST = _descriptor.Descriptor(
  name='GetNextFurrowDetectionsRequest',
  full_name='cv.deck.proto.GetNextFurrowDetectionsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='camera_id', full_name='cv.deck.proto.GetNextFurrowDetectionsRequest.camera_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='cv.deck.proto.GetNextFurrowDetectionsRequest.timestamp_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timeout_ms', full_name='cv.deck.proto.GetNextFurrowDetectionsRequest.timeout_ms', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=238,
  serialized_end=331,
)


_NODETIMING_STATETIMINGSENTRY = _descriptor.Descriptor(
  name='StateTimingsEntry',
  full_name='cv.deck.proto.NodeTiming.StateTimingsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='cv.deck.proto.NodeTiming.StateTimingsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='cv.deck.proto.NodeTiming.StateTimingsEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=533,
  serialized_end=584,
)

_NODETIMING = _descriptor.Descriptor(
  name='NodeTiming',
  full_name='cv.deck.proto.NodeTiming',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='cv.deck.proto.NodeTiming.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fps_mean', full_name='cv.deck.proto.NodeTiming.fps_mean', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fps_99pct', full_name='cv.deck.proto.NodeTiming.fps_99pct', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latency_ms_mean', full_name='cv.deck.proto.NodeTiming.latency_ms_mean', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latency_ms_99pct', full_name='cv.deck.proto.NodeTiming.latency_ms_99pct', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='cv.deck.proto.NodeTiming.state', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state_timings', full_name='cv.deck.proto.NodeTiming.state_timings', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_NODETIMING_STATETIMINGSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=334,
  serialized_end=584,
)


_NODETIMINGRESPONSE = _descriptor.Descriptor(
  name='NodeTimingResponse',
  full_name='cv.deck.proto.NodeTimingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='node_timing', full_name='cv.deck.proto.NodeTimingResponse.node_timing', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=586,
  serialized_end=654,
)


_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='cv.deck.proto.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=656,
  serialized_end=663,
)

_FURROWDETECTIONS.fields_by_name['furrows'].message_type = _FURROWDETECTION
_NODETIMING_STATETIMINGSENTRY.containing_type = _NODETIMING
_NODETIMING.fields_by_name['state_timings'].message_type = _NODETIMING_STATETIMINGSENTRY
_NODETIMINGRESPONSE.fields_by_name['node_timing'].message_type = _NODETIMING
DESCRIPTOR.message_types_by_name['FurrowDetection'] = _FURROWDETECTION
DESCRIPTOR.message_types_by_name['FurrowDetections'] = _FURROWDETECTIONS
DESCRIPTOR.message_types_by_name['GetNextFurrowDetectionsRequest'] = _GETNEXTFURROWDETECTIONSREQUEST
DESCRIPTOR.message_types_by_name['NodeTiming'] = _NODETIMING
DESCRIPTOR.message_types_by_name['NodeTimingResponse'] = _NODETIMINGRESPONSE
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

FurrowDetection = _reflection.GeneratedProtocolMessageType('FurrowDetection', (_message.Message,), {
  'DESCRIPTOR' : _FURROWDETECTION,
  '__module__' : 'cv.deck.proto.deck_cv_pb2'
  # @@protoc_insertion_point(class_scope:cv.deck.proto.FurrowDetection)
  })
_sym_db.RegisterMessage(FurrowDetection)

FurrowDetections = _reflection.GeneratedProtocolMessageType('FurrowDetections', (_message.Message,), {
  'DESCRIPTOR' : _FURROWDETECTIONS,
  '__module__' : 'cv.deck.proto.deck_cv_pb2'
  # @@protoc_insertion_point(class_scope:cv.deck.proto.FurrowDetections)
  })
_sym_db.RegisterMessage(FurrowDetections)

GetNextFurrowDetectionsRequest = _reflection.GeneratedProtocolMessageType('GetNextFurrowDetectionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTFURROWDETECTIONSREQUEST,
  '__module__' : 'cv.deck.proto.deck_cv_pb2'
  # @@protoc_insertion_point(class_scope:cv.deck.proto.GetNextFurrowDetectionsRequest)
  })
_sym_db.RegisterMessage(GetNextFurrowDetectionsRequest)

NodeTiming = _reflection.GeneratedProtocolMessageType('NodeTiming', (_message.Message,), {

  'StateTimingsEntry' : _reflection.GeneratedProtocolMessageType('StateTimingsEntry', (_message.Message,), {
    'DESCRIPTOR' : _NODETIMING_STATETIMINGSENTRY,
    '__module__' : 'cv.deck.proto.deck_cv_pb2'
    # @@protoc_insertion_point(class_scope:cv.deck.proto.NodeTiming.StateTimingsEntry)
    })
  ,
  'DESCRIPTOR' : _NODETIMING,
  '__module__' : 'cv.deck.proto.deck_cv_pb2'
  # @@protoc_insertion_point(class_scope:cv.deck.proto.NodeTiming)
  })
_sym_db.RegisterMessage(NodeTiming)
_sym_db.RegisterMessage(NodeTiming.StateTimingsEntry)

NodeTimingResponse = _reflection.GeneratedProtocolMessageType('NodeTimingResponse', (_message.Message,), {
  'DESCRIPTOR' : _NODETIMINGRESPONSE,
  '__module__' : 'cv.deck.proto.deck_cv_pb2'
  # @@protoc_insertion_point(class_scope:cv.deck.proto.NodeTimingResponse)
  })
_sym_db.RegisterMessage(NodeTimingResponse)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'cv.deck.proto.deck_cv_pb2'
  # @@protoc_insertion_point(class_scope:cv.deck.proto.Empty)
  })
_sym_db.RegisterMessage(Empty)


_NODETIMING_STATETIMINGSENTRY._options = None

_DECKCVSERVICE = _descriptor.ServiceDescriptor(
  name='DeckCVService',
  full_name='cv.deck.proto.DeckCVService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=666,
  serialized_end=866,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextFurrowDetections',
    full_name='cv.deck.proto.DeckCVService.GetNextFurrowDetections',
    index=0,
    containing_service=None,
    input_type=_GETNEXTFURROWDETECTIONSREQUEST,
    output_type=_FURROWDETECTIONS,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNodeTiming',
    full_name='cv.deck.proto.DeckCVService.GetNodeTiming',
    index=1,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_NODETIMINGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_DECKCVSERVICE)

DESCRIPTOR.services_by_name['DeckCVService'] = _DECKCVSERVICE

# @@protoc_insertion_point(module_scope)

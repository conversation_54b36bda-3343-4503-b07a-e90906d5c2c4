// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cv/deck/proto/deck_cv.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[7]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto;
namespace cv {
namespace deck {
namespace proto {
class Empty;
struct EmptyDefaultTypeInternal;
extern EmptyDefaultTypeInternal _Empty_default_instance_;
class FurrowDetection;
struct FurrowDetectionDefaultTypeInternal;
extern FurrowDetectionDefaultTypeInternal _FurrowDetection_default_instance_;
class FurrowDetections;
struct FurrowDetectionsDefaultTypeInternal;
extern FurrowDetectionsDefaultTypeInternal _FurrowDetections_default_instance_;
class GetNextFurrowDetectionsRequest;
struct GetNextFurrowDetectionsRequestDefaultTypeInternal;
extern GetNextFurrowDetectionsRequestDefaultTypeInternal _GetNextFurrowDetectionsRequest_default_instance_;
class NodeTiming;
struct NodeTimingDefaultTypeInternal;
extern NodeTimingDefaultTypeInternal _NodeTiming_default_instance_;
class NodeTimingResponse;
struct NodeTimingResponseDefaultTypeInternal;
extern NodeTimingResponseDefaultTypeInternal _NodeTimingResponse_default_instance_;
class NodeTiming_StateTimingsEntry_DoNotUse;
struct NodeTiming_StateTimingsEntry_DoNotUseDefaultTypeInternal;
extern NodeTiming_StateTimingsEntry_DoNotUseDefaultTypeInternal _NodeTiming_StateTimingsEntry_DoNotUse_default_instance_;
}  // namespace proto
}  // namespace deck
}  // namespace cv
PROTOBUF_NAMESPACE_OPEN
template<> ::cv::deck::proto::Empty* Arena::CreateMaybeMessage<::cv::deck::proto::Empty>(Arena*);
template<> ::cv::deck::proto::FurrowDetection* Arena::CreateMaybeMessage<::cv::deck::proto::FurrowDetection>(Arena*);
template<> ::cv::deck::proto::FurrowDetections* Arena::CreateMaybeMessage<::cv::deck::proto::FurrowDetections>(Arena*);
template<> ::cv::deck::proto::GetNextFurrowDetectionsRequest* Arena::CreateMaybeMessage<::cv::deck::proto::GetNextFurrowDetectionsRequest>(Arena*);
template<> ::cv::deck::proto::NodeTiming* Arena::CreateMaybeMessage<::cv::deck::proto::NodeTiming>(Arena*);
template<> ::cv::deck::proto::NodeTimingResponse* Arena::CreateMaybeMessage<::cv::deck::proto::NodeTimingResponse>(Arena*);
template<> ::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse* Arena::CreateMaybeMessage<::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace cv {
namespace deck {
namespace proto {

// ===================================================================

class FurrowDetection final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cv.deck.proto.FurrowDetection) */ {
 public:
  inline FurrowDetection() : FurrowDetection(nullptr) {}
  ~FurrowDetection() override;
  explicit constexpr FurrowDetection(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FurrowDetection(const FurrowDetection& from);
  FurrowDetection(FurrowDetection&& from) noexcept
    : FurrowDetection() {
    *this = ::std::move(from);
  }

  inline FurrowDetection& operator=(const FurrowDetection& from) {
    CopyFrom(from);
    return *this;
  }
  inline FurrowDetection& operator=(FurrowDetection&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FurrowDetection& default_instance() {
    return *internal_default_instance();
  }
  static inline const FurrowDetection* internal_default_instance() {
    return reinterpret_cast<const FurrowDetection*>(
               &_FurrowDetection_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(FurrowDetection& a, FurrowDetection& b) {
    a.Swap(&b);
  }
  inline void Swap(FurrowDetection* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FurrowDetection* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FurrowDetection* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FurrowDetection>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FurrowDetection& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FurrowDetection& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FurrowDetection* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cv.deck.proto.FurrowDetection";
  }
  protected:
  explicit FurrowDetection(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCategoryFieldNumber = 5,
    kStartXFieldNumber = 1,
    kStartYFieldNumber = 2,
    kEndXFieldNumber = 3,
    kEndYFieldNumber = 4,
  };
  // string category = 5;
  void clear_category();
  const std::string& category() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_category(ArgT0&& arg0, ArgT... args);
  std::string* mutable_category();
  PROTOBUF_NODISCARD std::string* release_category();
  void set_allocated_category(std::string* category);
  private:
  const std::string& _internal_category() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_category(const std::string& value);
  std::string* _internal_mutable_category();
  public:

  // float start_x = 1;
  void clear_start_x();
  float start_x() const;
  void set_start_x(float value);
  private:
  float _internal_start_x() const;
  void _internal_set_start_x(float value);
  public:

  // float start_y = 2;
  void clear_start_y();
  float start_y() const;
  void set_start_y(float value);
  private:
  float _internal_start_y() const;
  void _internal_set_start_y(float value);
  public:

  // float end_x = 3;
  void clear_end_x();
  float end_x() const;
  void set_end_x(float value);
  private:
  float _internal_end_x() const;
  void _internal_set_end_x(float value);
  public:

  // float end_y = 4;
  void clear_end_y();
  float end_y() const;
  void set_end_y(float value);
  private:
  float _internal_end_y() const;
  void _internal_set_end_y(float value);
  public:

  // @@protoc_insertion_point(class_scope:cv.deck.proto.FurrowDetection)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr category_;
  float start_x_;
  float start_y_;
  float end_x_;
  float end_y_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto;
};
// -------------------------------------------------------------------

class FurrowDetections final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cv.deck.proto.FurrowDetections) */ {
 public:
  inline FurrowDetections() : FurrowDetections(nullptr) {}
  ~FurrowDetections() override;
  explicit constexpr FurrowDetections(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FurrowDetections(const FurrowDetections& from);
  FurrowDetections(FurrowDetections&& from) noexcept
    : FurrowDetections() {
    *this = ::std::move(from);
  }

  inline FurrowDetections& operator=(const FurrowDetections& from) {
    CopyFrom(from);
    return *this;
  }
  inline FurrowDetections& operator=(FurrowDetections&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FurrowDetections& default_instance() {
    return *internal_default_instance();
  }
  static inline const FurrowDetections* internal_default_instance() {
    return reinterpret_cast<const FurrowDetections*>(
               &_FurrowDetections_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(FurrowDetections& a, FurrowDetections& b) {
    a.Swap(&b);
  }
  inline void Swap(FurrowDetections* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FurrowDetections* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FurrowDetections* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FurrowDetections>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FurrowDetections& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FurrowDetections& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FurrowDetections* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cv.deck.proto.FurrowDetections";
  }
  protected:
  explicit FurrowDetections(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFurrowsFieldNumber = 1,
    kTimestampMsFieldNumber = 2,
  };
  // repeated .cv.deck.proto.FurrowDetection furrows = 1;
  int furrows_size() const;
  private:
  int _internal_furrows_size() const;
  public:
  void clear_furrows();
  ::cv::deck::proto::FurrowDetection* mutable_furrows(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::FurrowDetection >*
      mutable_furrows();
  private:
  const ::cv::deck::proto::FurrowDetection& _internal_furrows(int index) const;
  ::cv::deck::proto::FurrowDetection* _internal_add_furrows();
  public:
  const ::cv::deck::proto::FurrowDetection& furrows(int index) const;
  ::cv::deck::proto::FurrowDetection* add_furrows();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::FurrowDetection >&
      furrows() const;

  // int64 timestamp_ms = 2;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:cv.deck.proto.FurrowDetections)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::FurrowDetection > furrows_;
  int64_t timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto;
};
// -------------------------------------------------------------------

class GetNextFurrowDetectionsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cv.deck.proto.GetNextFurrowDetectionsRequest) */ {
 public:
  inline GetNextFurrowDetectionsRequest() : GetNextFurrowDetectionsRequest(nullptr) {}
  ~GetNextFurrowDetectionsRequest() override;
  explicit constexpr GetNextFurrowDetectionsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextFurrowDetectionsRequest(const GetNextFurrowDetectionsRequest& from);
  GetNextFurrowDetectionsRequest(GetNextFurrowDetectionsRequest&& from) noexcept
    : GetNextFurrowDetectionsRequest() {
    *this = ::std::move(from);
  }

  inline GetNextFurrowDetectionsRequest& operator=(const GetNextFurrowDetectionsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextFurrowDetectionsRequest& operator=(GetNextFurrowDetectionsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextFurrowDetectionsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextFurrowDetectionsRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextFurrowDetectionsRequest*>(
               &_GetNextFurrowDetectionsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GetNextFurrowDetectionsRequest& a, GetNextFurrowDetectionsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextFurrowDetectionsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextFurrowDetectionsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextFurrowDetectionsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextFurrowDetectionsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextFurrowDetectionsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextFurrowDetectionsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextFurrowDetectionsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cv.deck.proto.GetNextFurrowDetectionsRequest";
  }
  protected:
  explicit GetNextFurrowDetectionsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCameraIdFieldNumber = 1,
    kTimestampMsFieldNumber = 2,
    kTimeoutMsFieldNumber = 3,
  };
  // string camera_id = 1;
  void clear_camera_id();
  const std::string& camera_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_camera_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_camera_id();
  PROTOBUF_NODISCARD std::string* release_camera_id();
  void set_allocated_camera_id(std::string* camera_id);
  private:
  const std::string& _internal_camera_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_camera_id(const std::string& value);
  std::string* _internal_mutable_camera_id();
  public:

  // int64 timestamp_ms = 2;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // int64 timeout_ms = 3;
  void clear_timeout_ms();
  int64_t timeout_ms() const;
  void set_timeout_ms(int64_t value);
  private:
  int64_t _internal_timeout_ms() const;
  void _internal_set_timeout_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:cv.deck.proto.GetNextFurrowDetectionsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr camera_id_;
  int64_t timestamp_ms_;
  int64_t timeout_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto;
};
// -------------------------------------------------------------------

class NodeTiming_StateTimingsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<NodeTiming_StateTimingsEntry_DoNotUse, 
    std::string, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<NodeTiming_StateTimingsEntry_DoNotUse, 
    std::string, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> SuperType;
  NodeTiming_StateTimingsEntry_DoNotUse();
  explicit constexpr NodeTiming_StateTimingsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit NodeTiming_StateTimingsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const NodeTiming_StateTimingsEntry_DoNotUse& other);
  static const NodeTiming_StateTimingsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const NodeTiming_StateTimingsEntry_DoNotUse*>(&_NodeTiming_StateTimingsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "cv.deck.proto.NodeTiming.StateTimingsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class NodeTiming final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cv.deck.proto.NodeTiming) */ {
 public:
  inline NodeTiming() : NodeTiming(nullptr) {}
  ~NodeTiming() override;
  explicit constexpr NodeTiming(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NodeTiming(const NodeTiming& from);
  NodeTiming(NodeTiming&& from) noexcept
    : NodeTiming() {
    *this = ::std::move(from);
  }

  inline NodeTiming& operator=(const NodeTiming& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeTiming& operator=(NodeTiming&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeTiming& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeTiming* internal_default_instance() {
    return reinterpret_cast<const NodeTiming*>(
               &_NodeTiming_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(NodeTiming& a, NodeTiming& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeTiming* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeTiming* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeTiming* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NodeTiming>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NodeTiming& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const NodeTiming& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeTiming* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cv.deck.proto.NodeTiming";
  }
  protected:
  explicit NodeTiming(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kStateTimingsFieldNumber = 7,
    kNameFieldNumber = 1,
    kStateFieldNumber = 6,
    kFpsMeanFieldNumber = 2,
    kFps99PctFieldNumber = 3,
    kLatencyMsMeanFieldNumber = 4,
    kLatencyMs99PctFieldNumber = 5,
  };
  // map<string, float> state_timings = 7;
  int state_timings_size() const;
  private:
  int _internal_state_timings_size() const;
  public:
  void clear_state_timings();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
      _internal_state_timings() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
      _internal_mutable_state_timings();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
      state_timings() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
      mutable_state_timings();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string state = 6;
  void clear_state();
  const std::string& state() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_state(ArgT0&& arg0, ArgT... args);
  std::string* mutable_state();
  PROTOBUF_NODISCARD std::string* release_state();
  void set_allocated_state(std::string* state);
  private:
  const std::string& _internal_state() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_state(const std::string& value);
  std::string* _internal_mutable_state();
  public:

  // float fps_mean = 2;
  void clear_fps_mean();
  float fps_mean() const;
  void set_fps_mean(float value);
  private:
  float _internal_fps_mean() const;
  void _internal_set_fps_mean(float value);
  public:

  // float fps_99pct = 3;
  void clear_fps_99pct();
  float fps_99pct() const;
  void set_fps_99pct(float value);
  private:
  float _internal_fps_99pct() const;
  void _internal_set_fps_99pct(float value);
  public:

  // float latency_ms_mean = 4;
  void clear_latency_ms_mean();
  float latency_ms_mean() const;
  void set_latency_ms_mean(float value);
  private:
  float _internal_latency_ms_mean() const;
  void _internal_set_latency_ms_mean(float value);
  public:

  // float latency_ms_99pct = 5;
  void clear_latency_ms_99pct();
  float latency_ms_99pct() const;
  void set_latency_ms_99pct(float value);
  private:
  float _internal_latency_ms_99pct() const;
  void _internal_set_latency_ms_99pct(float value);
  public:

  // @@protoc_insertion_point(class_scope:cv.deck.proto.NodeTiming)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      NodeTiming_StateTimingsEntry_DoNotUse,
      std::string, float,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> state_timings_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr state_;
  float fps_mean_;
  float fps_99pct_;
  float latency_ms_mean_;
  float latency_ms_99pct_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto;
};
// -------------------------------------------------------------------

class NodeTimingResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:cv.deck.proto.NodeTimingResponse) */ {
 public:
  inline NodeTimingResponse() : NodeTimingResponse(nullptr) {}
  ~NodeTimingResponse() override;
  explicit constexpr NodeTimingResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NodeTimingResponse(const NodeTimingResponse& from);
  NodeTimingResponse(NodeTimingResponse&& from) noexcept
    : NodeTimingResponse() {
    *this = ::std::move(from);
  }

  inline NodeTimingResponse& operator=(const NodeTimingResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeTimingResponse& operator=(NodeTimingResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeTimingResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeTimingResponse* internal_default_instance() {
    return reinterpret_cast<const NodeTimingResponse*>(
               &_NodeTimingResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(NodeTimingResponse& a, NodeTimingResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeTimingResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeTimingResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeTimingResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NodeTimingResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NodeTimingResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const NodeTimingResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeTimingResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cv.deck.proto.NodeTimingResponse";
  }
  protected:
  explicit NodeTimingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeTimingFieldNumber = 1,
  };
  // repeated .cv.deck.proto.NodeTiming node_timing = 1;
  int node_timing_size() const;
  private:
  int _internal_node_timing_size() const;
  public:
  void clear_node_timing();
  ::cv::deck::proto::NodeTiming* mutable_node_timing(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::NodeTiming >*
      mutable_node_timing();
  private:
  const ::cv::deck::proto::NodeTiming& _internal_node_timing(int index) const;
  ::cv::deck::proto::NodeTiming* _internal_add_node_timing();
  public:
  const ::cv::deck::proto::NodeTiming& node_timing(int index) const;
  ::cv::deck::proto::NodeTiming* add_node_timing();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::NodeTiming >&
      node_timing() const;

  // @@protoc_insertion_point(class_scope:cv.deck.proto.NodeTimingResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::NodeTiming > node_timing_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto;
};
// -------------------------------------------------------------------

class Empty final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:cv.deck.proto.Empty) */ {
 public:
  inline Empty() : Empty(nullptr) {}
  explicit constexpr Empty(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Empty(const Empty& from);
  Empty(Empty&& from) noexcept
    : Empty() {
    *this = ::std::move(from);
  }

  inline Empty& operator=(const Empty& from) {
    CopyFrom(from);
    return *this;
  }
  inline Empty& operator=(Empty&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Empty& default_instance() {
    return *internal_default_instance();
  }
  static inline const Empty* internal_default_instance() {
    return reinterpret_cast<const Empty*>(
               &_Empty_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(Empty& a, Empty& b) {
    a.Swap(&b);
  }
  inline void Swap(Empty* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Empty* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Empty* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Empty>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const Empty& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const Empty& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "cv.deck.proto.Empty";
  }
  protected:
  explicit Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:cv.deck.proto.Empty)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FurrowDetection

// float start_x = 1;
inline void FurrowDetection::clear_start_x() {
  start_x_ = 0;
}
inline float FurrowDetection::_internal_start_x() const {
  return start_x_;
}
inline float FurrowDetection::start_x() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.FurrowDetection.start_x)
  return _internal_start_x();
}
inline void FurrowDetection::_internal_set_start_x(float value) {
  
  start_x_ = value;
}
inline void FurrowDetection::set_start_x(float value) {
  _internal_set_start_x(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.FurrowDetection.start_x)
}

// float start_y = 2;
inline void FurrowDetection::clear_start_y() {
  start_y_ = 0;
}
inline float FurrowDetection::_internal_start_y() const {
  return start_y_;
}
inline float FurrowDetection::start_y() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.FurrowDetection.start_y)
  return _internal_start_y();
}
inline void FurrowDetection::_internal_set_start_y(float value) {
  
  start_y_ = value;
}
inline void FurrowDetection::set_start_y(float value) {
  _internal_set_start_y(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.FurrowDetection.start_y)
}

// float end_x = 3;
inline void FurrowDetection::clear_end_x() {
  end_x_ = 0;
}
inline float FurrowDetection::_internal_end_x() const {
  return end_x_;
}
inline float FurrowDetection::end_x() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.FurrowDetection.end_x)
  return _internal_end_x();
}
inline void FurrowDetection::_internal_set_end_x(float value) {
  
  end_x_ = value;
}
inline void FurrowDetection::set_end_x(float value) {
  _internal_set_end_x(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.FurrowDetection.end_x)
}

// float end_y = 4;
inline void FurrowDetection::clear_end_y() {
  end_y_ = 0;
}
inline float FurrowDetection::_internal_end_y() const {
  return end_y_;
}
inline float FurrowDetection::end_y() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.FurrowDetection.end_y)
  return _internal_end_y();
}
inline void FurrowDetection::_internal_set_end_y(float value) {
  
  end_y_ = value;
}
inline void FurrowDetection::set_end_y(float value) {
  _internal_set_end_y(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.FurrowDetection.end_y)
}

// string category = 5;
inline void FurrowDetection::clear_category() {
  category_.ClearToEmpty();
}
inline const std::string& FurrowDetection::category() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.FurrowDetection.category)
  return _internal_category();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FurrowDetection::set_category(ArgT0&& arg0, ArgT... args) {
 
 category_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cv.deck.proto.FurrowDetection.category)
}
inline std::string* FurrowDetection::mutable_category() {
  std::string* _s = _internal_mutable_category();
  // @@protoc_insertion_point(field_mutable:cv.deck.proto.FurrowDetection.category)
  return _s;
}
inline const std::string& FurrowDetection::_internal_category() const {
  return category_.Get();
}
inline void FurrowDetection::_internal_set_category(const std::string& value) {
  
  category_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FurrowDetection::_internal_mutable_category() {
  
  return category_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FurrowDetection::release_category() {
  // @@protoc_insertion_point(field_release:cv.deck.proto.FurrowDetection.category)
  return category_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FurrowDetection::set_allocated_category(std::string* category) {
  if (category != nullptr) {
    
  } else {
    
  }
  category_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), category,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (category_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cv.deck.proto.FurrowDetection.category)
}

// -------------------------------------------------------------------

// FurrowDetections

// repeated .cv.deck.proto.FurrowDetection furrows = 1;
inline int FurrowDetections::_internal_furrows_size() const {
  return furrows_.size();
}
inline int FurrowDetections::furrows_size() const {
  return _internal_furrows_size();
}
inline void FurrowDetections::clear_furrows() {
  furrows_.Clear();
}
inline ::cv::deck::proto::FurrowDetection* FurrowDetections::mutable_furrows(int index) {
  // @@protoc_insertion_point(field_mutable:cv.deck.proto.FurrowDetections.furrows)
  return furrows_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::FurrowDetection >*
FurrowDetections::mutable_furrows() {
  // @@protoc_insertion_point(field_mutable_list:cv.deck.proto.FurrowDetections.furrows)
  return &furrows_;
}
inline const ::cv::deck::proto::FurrowDetection& FurrowDetections::_internal_furrows(int index) const {
  return furrows_.Get(index);
}
inline const ::cv::deck::proto::FurrowDetection& FurrowDetections::furrows(int index) const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.FurrowDetections.furrows)
  return _internal_furrows(index);
}
inline ::cv::deck::proto::FurrowDetection* FurrowDetections::_internal_add_furrows() {
  return furrows_.Add();
}
inline ::cv::deck::proto::FurrowDetection* FurrowDetections::add_furrows() {
  ::cv::deck::proto::FurrowDetection* _add = _internal_add_furrows();
  // @@protoc_insertion_point(field_add:cv.deck.proto.FurrowDetections.furrows)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::FurrowDetection >&
FurrowDetections::furrows() const {
  // @@protoc_insertion_point(field_list:cv.deck.proto.FurrowDetections.furrows)
  return furrows_;
}

// int64 timestamp_ms = 2;
inline void FurrowDetections::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t FurrowDetections::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t FurrowDetections::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.FurrowDetections.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void FurrowDetections::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void FurrowDetections::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.FurrowDetections.timestamp_ms)
}

// -------------------------------------------------------------------

// GetNextFurrowDetectionsRequest

// string camera_id = 1;
inline void GetNextFurrowDetectionsRequest::clear_camera_id() {
  camera_id_.ClearToEmpty();
}
inline const std::string& GetNextFurrowDetectionsRequest::camera_id() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.GetNextFurrowDetectionsRequest.camera_id)
  return _internal_camera_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextFurrowDetectionsRequest::set_camera_id(ArgT0&& arg0, ArgT... args) {
 
 camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cv.deck.proto.GetNextFurrowDetectionsRequest.camera_id)
}
inline std::string* GetNextFurrowDetectionsRequest::mutable_camera_id() {
  std::string* _s = _internal_mutable_camera_id();
  // @@protoc_insertion_point(field_mutable:cv.deck.proto.GetNextFurrowDetectionsRequest.camera_id)
  return _s;
}
inline const std::string& GetNextFurrowDetectionsRequest::_internal_camera_id() const {
  return camera_id_.Get();
}
inline void GetNextFurrowDetectionsRequest::_internal_set_camera_id(const std::string& value) {
  
  camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextFurrowDetectionsRequest::_internal_mutable_camera_id() {
  
  return camera_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextFurrowDetectionsRequest::release_camera_id() {
  // @@protoc_insertion_point(field_release:cv.deck.proto.GetNextFurrowDetectionsRequest.camera_id)
  return camera_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextFurrowDetectionsRequest::set_allocated_camera_id(std::string* camera_id) {
  if (camera_id != nullptr) {
    
  } else {
    
  }
  camera_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), camera_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (camera_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cv.deck.proto.GetNextFurrowDetectionsRequest.camera_id)
}

// int64 timestamp_ms = 2;
inline void GetNextFurrowDetectionsRequest::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t GetNextFurrowDetectionsRequest::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t GetNextFurrowDetectionsRequest::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.GetNextFurrowDetectionsRequest.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void GetNextFurrowDetectionsRequest::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void GetNextFurrowDetectionsRequest::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.GetNextFurrowDetectionsRequest.timestamp_ms)
}

// int64 timeout_ms = 3;
inline void GetNextFurrowDetectionsRequest::clear_timeout_ms() {
  timeout_ms_ = int64_t{0};
}
inline int64_t GetNextFurrowDetectionsRequest::_internal_timeout_ms() const {
  return timeout_ms_;
}
inline int64_t GetNextFurrowDetectionsRequest::timeout_ms() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.GetNextFurrowDetectionsRequest.timeout_ms)
  return _internal_timeout_ms();
}
inline void GetNextFurrowDetectionsRequest::_internal_set_timeout_ms(int64_t value) {
  
  timeout_ms_ = value;
}
inline void GetNextFurrowDetectionsRequest::set_timeout_ms(int64_t value) {
  _internal_set_timeout_ms(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.GetNextFurrowDetectionsRequest.timeout_ms)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// NodeTiming

// string name = 1;
inline void NodeTiming::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& NodeTiming::name() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.NodeTiming.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeTiming::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cv.deck.proto.NodeTiming.name)
}
inline std::string* NodeTiming::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:cv.deck.proto.NodeTiming.name)
  return _s;
}
inline const std::string& NodeTiming::_internal_name() const {
  return name_.Get();
}
inline void NodeTiming::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NodeTiming::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NodeTiming::release_name() {
  // @@protoc_insertion_point(field_release:cv.deck.proto.NodeTiming.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void NodeTiming::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cv.deck.proto.NodeTiming.name)
}

// float fps_mean = 2;
inline void NodeTiming::clear_fps_mean() {
  fps_mean_ = 0;
}
inline float NodeTiming::_internal_fps_mean() const {
  return fps_mean_;
}
inline float NodeTiming::fps_mean() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.NodeTiming.fps_mean)
  return _internal_fps_mean();
}
inline void NodeTiming::_internal_set_fps_mean(float value) {
  
  fps_mean_ = value;
}
inline void NodeTiming::set_fps_mean(float value) {
  _internal_set_fps_mean(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.NodeTiming.fps_mean)
}

// float fps_99pct = 3;
inline void NodeTiming::clear_fps_99pct() {
  fps_99pct_ = 0;
}
inline float NodeTiming::_internal_fps_99pct() const {
  return fps_99pct_;
}
inline float NodeTiming::fps_99pct() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.NodeTiming.fps_99pct)
  return _internal_fps_99pct();
}
inline void NodeTiming::_internal_set_fps_99pct(float value) {
  
  fps_99pct_ = value;
}
inline void NodeTiming::set_fps_99pct(float value) {
  _internal_set_fps_99pct(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.NodeTiming.fps_99pct)
}

// float latency_ms_mean = 4;
inline void NodeTiming::clear_latency_ms_mean() {
  latency_ms_mean_ = 0;
}
inline float NodeTiming::_internal_latency_ms_mean() const {
  return latency_ms_mean_;
}
inline float NodeTiming::latency_ms_mean() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.NodeTiming.latency_ms_mean)
  return _internal_latency_ms_mean();
}
inline void NodeTiming::_internal_set_latency_ms_mean(float value) {
  
  latency_ms_mean_ = value;
}
inline void NodeTiming::set_latency_ms_mean(float value) {
  _internal_set_latency_ms_mean(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.NodeTiming.latency_ms_mean)
}

// float latency_ms_99pct = 5;
inline void NodeTiming::clear_latency_ms_99pct() {
  latency_ms_99pct_ = 0;
}
inline float NodeTiming::_internal_latency_ms_99pct() const {
  return latency_ms_99pct_;
}
inline float NodeTiming::latency_ms_99pct() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.NodeTiming.latency_ms_99pct)
  return _internal_latency_ms_99pct();
}
inline void NodeTiming::_internal_set_latency_ms_99pct(float value) {
  
  latency_ms_99pct_ = value;
}
inline void NodeTiming::set_latency_ms_99pct(float value) {
  _internal_set_latency_ms_99pct(value);
  // @@protoc_insertion_point(field_set:cv.deck.proto.NodeTiming.latency_ms_99pct)
}

// string state = 6;
inline void NodeTiming::clear_state() {
  state_.ClearToEmpty();
}
inline const std::string& NodeTiming::state() const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.NodeTiming.state)
  return _internal_state();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeTiming::set_state(ArgT0&& arg0, ArgT... args) {
 
 state_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:cv.deck.proto.NodeTiming.state)
}
inline std::string* NodeTiming::mutable_state() {
  std::string* _s = _internal_mutable_state();
  // @@protoc_insertion_point(field_mutable:cv.deck.proto.NodeTiming.state)
  return _s;
}
inline const std::string& NodeTiming::_internal_state() const {
  return state_.Get();
}
inline void NodeTiming::_internal_set_state(const std::string& value) {
  
  state_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NodeTiming::_internal_mutable_state() {
  
  return state_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NodeTiming::release_state() {
  // @@protoc_insertion_point(field_release:cv.deck.proto.NodeTiming.state)
  return state_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void NodeTiming::set_allocated_state(std::string* state) {
  if (state != nullptr) {
    
  } else {
    
  }
  state_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), state,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (state_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    state_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:cv.deck.proto.NodeTiming.state)
}

// map<string, float> state_timings = 7;
inline int NodeTiming::_internal_state_timings_size() const {
  return state_timings_.size();
}
inline int NodeTiming::state_timings_size() const {
  return _internal_state_timings_size();
}
inline void NodeTiming::clear_state_timings() {
  state_timings_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
NodeTiming::_internal_state_timings() const {
  return state_timings_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
NodeTiming::state_timings() const {
  // @@protoc_insertion_point(field_map:cv.deck.proto.NodeTiming.state_timings)
  return _internal_state_timings();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
NodeTiming::_internal_mutable_state_timings() {
  return state_timings_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
NodeTiming::mutable_state_timings() {
  // @@protoc_insertion_point(field_mutable_map:cv.deck.proto.NodeTiming.state_timings)
  return _internal_mutable_state_timings();
}

// -------------------------------------------------------------------

// NodeTimingResponse

// repeated .cv.deck.proto.NodeTiming node_timing = 1;
inline int NodeTimingResponse::_internal_node_timing_size() const {
  return node_timing_.size();
}
inline int NodeTimingResponse::node_timing_size() const {
  return _internal_node_timing_size();
}
inline void NodeTimingResponse::clear_node_timing() {
  node_timing_.Clear();
}
inline ::cv::deck::proto::NodeTiming* NodeTimingResponse::mutable_node_timing(int index) {
  // @@protoc_insertion_point(field_mutable:cv.deck.proto.NodeTimingResponse.node_timing)
  return node_timing_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::NodeTiming >*
NodeTimingResponse::mutable_node_timing() {
  // @@protoc_insertion_point(field_mutable_list:cv.deck.proto.NodeTimingResponse.node_timing)
  return &node_timing_;
}
inline const ::cv::deck::proto::NodeTiming& NodeTimingResponse::_internal_node_timing(int index) const {
  return node_timing_.Get(index);
}
inline const ::cv::deck::proto::NodeTiming& NodeTimingResponse::node_timing(int index) const {
  // @@protoc_insertion_point(field_get:cv.deck.proto.NodeTimingResponse.node_timing)
  return _internal_node_timing(index);
}
inline ::cv::deck::proto::NodeTiming* NodeTimingResponse::_internal_add_node_timing() {
  return node_timing_.Add();
}
inline ::cv::deck::proto::NodeTiming* NodeTimingResponse::add_node_timing() {
  ::cv::deck::proto::NodeTiming* _add = _internal_add_node_timing();
  // @@protoc_insertion_point(field_add:cv.deck.proto.NodeTimingResponse.node_timing)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::cv::deck::proto::NodeTiming >&
NodeTimingResponse::node_timing() const {
  // @@protoc_insertion_point(field_list:cv.deck.proto.NodeTimingResponse.node_timing)
  return node_timing_;
}

// -------------------------------------------------------------------

// Empty

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto
}  // namespace deck
}  // namespace cv

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto

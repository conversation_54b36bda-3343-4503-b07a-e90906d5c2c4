"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedComposite<PERSON>ieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class FurrowDetection(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    start_x: builtin___float = ...
    start_y: builtin___float = ...
    end_x: builtin___float = ...
    end_y: builtin___float = ...
    category: typing___Text = ...

    def __init__(self,
        *,
        start_x : typing___Optional[builtin___float] = None,
        start_y : typing___Optional[builtin___float] = None,
        end_x : typing___Optional[builtin___float] = None,
        end_y : typing___Optional[builtin___float] = None,
        category : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"category",b"category",u"end_x",b"end_x",u"end_y",b"end_y",u"start_x",b"start_x",u"start_y",b"start_y"]) -> None: ...
type___FurrowDetection = FurrowDetection

class FurrowDetections(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    @property
    def furrows(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___FurrowDetection]: ...

    def __init__(self,
        *,
        furrows : typing___Optional[typing___Iterable[type___FurrowDetection]] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"furrows",b"furrows",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___FurrowDetections = FurrowDetections

class GetNextFurrowDetectionsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    camera_id: typing___Text = ...
    timestamp_ms: builtin___int = ...
    timeout_ms: builtin___int = ...

    def __init__(self,
        *,
        camera_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        timeout_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"camera_id",b"camera_id",u"timeout_ms",b"timeout_ms",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextFurrowDetectionsRequest = GetNextFurrowDetectionsRequest

class NodeTiming(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class StateTimingsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___StateTimingsEntry = StateTimingsEntry

    name: typing___Text = ...
    fps_mean: builtin___float = ...
    fps_99pct: builtin___float = ...
    latency_ms_mean: builtin___float = ...
    latency_ms_99pct: builtin___float = ...
    state: typing___Text = ...

    @property
    def state_timings(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        fps_mean : typing___Optional[builtin___float] = None,
        fps_99pct : typing___Optional[builtin___float] = None,
        latency_ms_mean : typing___Optional[builtin___float] = None,
        latency_ms_99pct : typing___Optional[builtin___float] = None,
        state : typing___Optional[typing___Text] = None,
        state_timings : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"fps_99pct",b"fps_99pct",u"fps_mean",b"fps_mean",u"latency_ms_99pct",b"latency_ms_99pct",u"latency_ms_mean",b"latency_ms_mean",u"name",b"name",u"state",b"state",u"state_timings",b"state_timings"]) -> None: ...
type___NodeTiming = NodeTiming

class NodeTimingResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def node_timing(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___NodeTiming]: ...

    def __init__(self,
        *,
        node_timing : typing___Optional[typing___Iterable[type___NodeTiming]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"node_timing",b"node_timing"]) -> None: ...
type___NodeTimingResponse = NodeTimingResponse

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

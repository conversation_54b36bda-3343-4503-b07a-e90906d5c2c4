// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cv/deck/proto/deck_cv.proto

#include "cv/deck/proto/deck_cv.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace cv {
namespace deck {
namespace proto {
constexpr FurrowDetection::FurrowDetection(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : category_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , start_x_(0)
  , start_y_(0)
  , end_x_(0)
  , end_y_(0){}
struct FurrowDetectionDefaultTypeInternal {
  constexpr FurrowDetectionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FurrowDetectionDefaultTypeInternal() {}
  union {
    FurrowDetection _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FurrowDetectionDefaultTypeInternal _FurrowDetection_default_instance_;
constexpr FurrowDetections::FurrowDetections(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : furrows_()
  , timestamp_ms_(int64_t{0}){}
struct FurrowDetectionsDefaultTypeInternal {
  constexpr FurrowDetectionsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FurrowDetectionsDefaultTypeInternal() {}
  union {
    FurrowDetections _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FurrowDetectionsDefaultTypeInternal _FurrowDetections_default_instance_;
constexpr GetNextFurrowDetectionsRequest::GetNextFurrowDetectionsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : camera_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0})
  , timeout_ms_(int64_t{0}){}
struct GetNextFurrowDetectionsRequestDefaultTypeInternal {
  constexpr GetNextFurrowDetectionsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextFurrowDetectionsRequestDefaultTypeInternal() {}
  union {
    GetNextFurrowDetectionsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextFurrowDetectionsRequestDefaultTypeInternal _GetNextFurrowDetectionsRequest_default_instance_;
constexpr NodeTiming_StateTimingsEntry_DoNotUse::NodeTiming_StateTimingsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct NodeTiming_StateTimingsEntry_DoNotUseDefaultTypeInternal {
  constexpr NodeTiming_StateTimingsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~NodeTiming_StateTimingsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    NodeTiming_StateTimingsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT NodeTiming_StateTimingsEntry_DoNotUseDefaultTypeInternal _NodeTiming_StateTimingsEntry_DoNotUse_default_instance_;
constexpr NodeTiming::NodeTiming(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : state_timings_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , state_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , fps_mean_(0)
  , fps_99pct_(0)
  , latency_ms_mean_(0)
  , latency_ms_99pct_(0){}
struct NodeTimingDefaultTypeInternal {
  constexpr NodeTimingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~NodeTimingDefaultTypeInternal() {}
  union {
    NodeTiming _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT NodeTimingDefaultTypeInternal _NodeTiming_default_instance_;
constexpr NodeTimingResponse::NodeTimingResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_timing_(){}
struct NodeTimingResponseDefaultTypeInternal {
  constexpr NodeTimingResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~NodeTimingResponseDefaultTypeInternal() {}
  union {
    NodeTimingResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT NodeTimingResponseDefaultTypeInternal _NodeTimingResponse_default_instance_;
constexpr Empty::Empty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct EmptyDefaultTypeInternal {
  constexpr EmptyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EmptyDefaultTypeInternal() {}
  union {
    Empty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EmptyDefaultTypeInternal _Empty_default_instance_;
}  // namespace proto
}  // namespace deck
}  // namespace cv
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto[7];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto = nullptr;

const uint32_t TableStruct_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::FurrowDetection, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::FurrowDetection, start_x_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::FurrowDetection, start_y_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::FurrowDetection, end_x_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::FurrowDetection, end_y_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::FurrowDetection, category_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::FurrowDetections, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::FurrowDetections, furrows_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::FurrowDetections, timestamp_ms_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::GetNextFurrowDetectionsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::GetNextFurrowDetectionsRequest, camera_id_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::GetNextFurrowDetectionsRequest, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::GetNextFurrowDetectionsRequest, timeout_ms_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming, name_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming, fps_mean_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming, fps_99pct_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming, latency_ms_mean_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming, latency_ms_99pct_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming, state_),
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTiming, state_timings_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTimingResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::NodeTimingResponse, node_timing_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::cv::deck::proto::Empty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::cv::deck::proto::FurrowDetection)},
  { 11, -1, -1, sizeof(::cv::deck::proto::FurrowDetections)},
  { 19, -1, -1, sizeof(::cv::deck::proto::GetNextFurrowDetectionsRequest)},
  { 28, 36, -1, sizeof(::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse)},
  { 38, -1, -1, sizeof(::cv::deck::proto::NodeTiming)},
  { 51, -1, -1, sizeof(::cv::deck::proto::NodeTimingResponse)},
  { 58, -1, -1, sizeof(::cv::deck::proto::Empty)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::cv::deck::proto::_FurrowDetection_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::cv::deck::proto::_FurrowDetections_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::cv::deck::proto::_GetNextFurrowDetectionsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::cv::deck::proto::_NodeTiming_StateTimingsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::cv::deck::proto::_NodeTiming_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::cv::deck::proto::_NodeTimingResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::cv::deck::proto::_Empty_default_instance_),
};

const char descriptor_table_protodef_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033cv/deck/proto/deck_cv.proto\022\rcv.deck.p"
  "roto\"c\n\017FurrowDetection\022\017\n\007start_x\030\001 \001(\002"
  "\022\017\n\007start_y\030\002 \001(\002\022\r\n\005end_x\030\003 \001(\002\022\r\n\005end_"
  "y\030\004 \001(\002\022\020\n\010category\030\005 \001(\t\"Y\n\020FurrowDetec"
  "tions\022/\n\007furrows\030\001 \003(\0132\036.cv.deck.proto.F"
  "urrowDetection\022\024\n\014timestamp_ms\030\002 \001(\003\"]\n\036"
  "GetNextFurrowDetectionsRequest\022\021\n\tcamera"
  "_id\030\001 \001(\t\022\024\n\014timestamp_ms\030\002 \001(\003\022\022\n\ntimeo"
  "ut_ms\030\003 \001(\003\"\372\001\n\nNodeTiming\022\014\n\004name\030\001 \001(\t"
  "\022\020\n\010fps_mean\030\002 \001(\002\022\021\n\tfps_99pct\030\003 \001(\002\022\027\n"
  "\017latency_ms_mean\030\004 \001(\002\022\030\n\020latency_ms_99p"
  "ct\030\005 \001(\002\022\r\n\005state\030\006 \001(\t\022B\n\rstate_timings"
  "\030\007 \003(\0132+.cv.deck.proto.NodeTiming.StateT"
  "imingsEntry\0323\n\021StateTimingsEntry\022\013\n\003key\030"
  "\001 \001(\t\022\r\n\005value\030\002 \001(\002:\0028\001\"D\n\022NodeTimingRe"
  "sponse\022.\n\013node_timing\030\001 \003(\0132\031.cv.deck.pr"
  "oto.NodeTiming\"\007\n\005Empty2\310\001\n\rDeckCVServic"
  "e\022k\n\027GetNextFurrowDetections\022-.cv.deck.p"
  "roto.GetNextFurrowDetectionsRequest\032\037.cv"
  ".deck.proto.FurrowDetections\"\000\022J\n\rGetNod"
  "eTiming\022\024.cv.deck.proto.Empty\032!.cv.deck."
  "proto.NodeTimingResponse\"\000b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto = {
  false, false, 874, descriptor_table_protodef_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto, "cv/deck/proto/deck_cv.proto", 
  &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_once, nullptr, 0, 7,
  schemas, file_default_instances, TableStruct_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto::offsets,
  file_level_metadata_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto, file_level_enum_descriptors_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto, file_level_service_descriptors_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_getter() {
  return &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto(&descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto);
namespace cv {
namespace deck {
namespace proto {

// ===================================================================

class FurrowDetection::_Internal {
 public:
};

FurrowDetection::FurrowDetection(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:cv.deck.proto.FurrowDetection)
}
FurrowDetection::FurrowDetection(const FurrowDetection& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  category_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_category().empty()) {
    category_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_category(), 
      GetArenaForAllocation());
  }
  ::memcpy(&start_x_, &from.start_x_,
    static_cast<size_t>(reinterpret_cast<char*>(&end_y_) -
    reinterpret_cast<char*>(&start_x_)) + sizeof(end_y_));
  // @@protoc_insertion_point(copy_constructor:cv.deck.proto.FurrowDetection)
}

inline void FurrowDetection::SharedCtor() {
category_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  category_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&start_x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&end_y_) -
    reinterpret_cast<char*>(&start_x_)) + sizeof(end_y_));
}

FurrowDetection::~FurrowDetection() {
  // @@protoc_insertion_point(destructor:cv.deck.proto.FurrowDetection)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FurrowDetection::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  category_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void FurrowDetection::ArenaDtor(void* object) {
  FurrowDetection* _this = reinterpret_cast< FurrowDetection* >(object);
  (void)_this;
}
void FurrowDetection::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FurrowDetection::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FurrowDetection::Clear() {
// @@protoc_insertion_point(message_clear_start:cv.deck.proto.FurrowDetection)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  category_.ClearToEmpty();
  ::memset(&start_x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&end_y_) -
      reinterpret_cast<char*>(&start_x_)) + sizeof(end_y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FurrowDetection::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float start_x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          start_x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float start_y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          start_y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float end_x = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          end_x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float end_y = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          end_y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // string category = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_category();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "cv.deck.proto.FurrowDetection.category"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FurrowDetection::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cv.deck.proto.FurrowDetection)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float start_x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_start_x = this->_internal_start_x();
  uint32_t raw_start_x;
  memcpy(&raw_start_x, &tmp_start_x, sizeof(tmp_start_x));
  if (raw_start_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_start_x(), target);
  }

  // float start_y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_start_y = this->_internal_start_y();
  uint32_t raw_start_y;
  memcpy(&raw_start_y, &tmp_start_y, sizeof(tmp_start_y));
  if (raw_start_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_start_y(), target);
  }

  // float end_x = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_end_x = this->_internal_end_x();
  uint32_t raw_end_x;
  memcpy(&raw_end_x, &tmp_end_x, sizeof(tmp_end_x));
  if (raw_end_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_end_x(), target);
  }

  // float end_y = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_end_y = this->_internal_end_y();
  uint32_t raw_end_y;
  memcpy(&raw_end_y, &tmp_end_y, sizeof(tmp_end_y));
  if (raw_end_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_end_y(), target);
  }

  // string category = 5;
  if (!this->_internal_category().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_category().data(), static_cast<int>(this->_internal_category().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cv.deck.proto.FurrowDetection.category");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_category(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cv.deck.proto.FurrowDetection)
  return target;
}

size_t FurrowDetection::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cv.deck.proto.FurrowDetection)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string category = 5;
  if (!this->_internal_category().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_category());
  }

  // float start_x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_start_x = this->_internal_start_x();
  uint32_t raw_start_x;
  memcpy(&raw_start_x, &tmp_start_x, sizeof(tmp_start_x));
  if (raw_start_x != 0) {
    total_size += 1 + 4;
  }

  // float start_y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_start_y = this->_internal_start_y();
  uint32_t raw_start_y;
  memcpy(&raw_start_y, &tmp_start_y, sizeof(tmp_start_y));
  if (raw_start_y != 0) {
    total_size += 1 + 4;
  }

  // float end_x = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_end_x = this->_internal_end_x();
  uint32_t raw_end_x;
  memcpy(&raw_end_x, &tmp_end_x, sizeof(tmp_end_x));
  if (raw_end_x != 0) {
    total_size += 1 + 4;
  }

  // float end_y = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_end_y = this->_internal_end_y();
  uint32_t raw_end_y;
  memcpy(&raw_end_y, &tmp_end_y, sizeof(tmp_end_y));
  if (raw_end_y != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FurrowDetection::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FurrowDetection::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FurrowDetection::GetClassData() const { return &_class_data_; }

void FurrowDetection::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FurrowDetection *>(to)->MergeFrom(
      static_cast<const FurrowDetection &>(from));
}


void FurrowDetection::MergeFrom(const FurrowDetection& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:cv.deck.proto.FurrowDetection)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_category().empty()) {
    _internal_set_category(from._internal_category());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_start_x = from._internal_start_x();
  uint32_t raw_start_x;
  memcpy(&raw_start_x, &tmp_start_x, sizeof(tmp_start_x));
  if (raw_start_x != 0) {
    _internal_set_start_x(from._internal_start_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_start_y = from._internal_start_y();
  uint32_t raw_start_y;
  memcpy(&raw_start_y, &tmp_start_y, sizeof(tmp_start_y));
  if (raw_start_y != 0) {
    _internal_set_start_y(from._internal_start_y());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_end_x = from._internal_end_x();
  uint32_t raw_end_x;
  memcpy(&raw_end_x, &tmp_end_x, sizeof(tmp_end_x));
  if (raw_end_x != 0) {
    _internal_set_end_x(from._internal_end_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_end_y = from._internal_end_y();
  uint32_t raw_end_y;
  memcpy(&raw_end_y, &tmp_end_y, sizeof(tmp_end_y));
  if (raw_end_y != 0) {
    _internal_set_end_y(from._internal_end_y());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FurrowDetection::CopyFrom(const FurrowDetection& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cv.deck.proto.FurrowDetection)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FurrowDetection::IsInitialized() const {
  return true;
}

void FurrowDetection::InternalSwap(FurrowDetection* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &category_, lhs_arena,
      &other->category_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FurrowDetection, end_y_)
      + sizeof(FurrowDetection::end_y_)
      - PROTOBUF_FIELD_OFFSET(FurrowDetection, start_x_)>(
          reinterpret_cast<char*>(&start_x_),
          reinterpret_cast<char*>(&other->start_x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FurrowDetection::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_getter, &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_once,
      file_level_metadata_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto[0]);
}

// ===================================================================

class FurrowDetections::_Internal {
 public:
};

FurrowDetections::FurrowDetections(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  furrows_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:cv.deck.proto.FurrowDetections)
}
FurrowDetections::FurrowDetections(const FurrowDetections& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      furrows_(from.furrows_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:cv.deck.proto.FurrowDetections)
}

inline void FurrowDetections::SharedCtor() {
timestamp_ms_ = int64_t{0};
}

FurrowDetections::~FurrowDetections() {
  // @@protoc_insertion_point(destructor:cv.deck.proto.FurrowDetections)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FurrowDetections::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void FurrowDetections::ArenaDtor(void* object) {
  FurrowDetections* _this = reinterpret_cast< FurrowDetections* >(object);
  (void)_this;
}
void FurrowDetections::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FurrowDetections::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FurrowDetections::Clear() {
// @@protoc_insertion_point(message_clear_start:cv.deck.proto.FurrowDetections)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  furrows_.Clear();
  timestamp_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FurrowDetections::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .cv.deck.proto.FurrowDetection furrows = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_furrows(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int64 timestamp_ms = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FurrowDetections::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cv.deck.proto.FurrowDetections)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .cv.deck.proto.FurrowDetection furrows = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_furrows_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_furrows(i), target, stream);
  }

  // int64 timestamp_ms = 2;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_timestamp_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cv.deck.proto.FurrowDetections)
  return target;
}

size_t FurrowDetections::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cv.deck.proto.FurrowDetections)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .cv.deck.proto.FurrowDetection furrows = 1;
  total_size += 1UL * this->_internal_furrows_size();
  for (const auto& msg : this->furrows_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int64 timestamp_ms = 2;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FurrowDetections::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FurrowDetections::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FurrowDetections::GetClassData() const { return &_class_data_; }

void FurrowDetections::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FurrowDetections *>(to)->MergeFrom(
      static_cast<const FurrowDetections &>(from));
}


void FurrowDetections::MergeFrom(const FurrowDetections& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:cv.deck.proto.FurrowDetections)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  furrows_.MergeFrom(from.furrows_);
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FurrowDetections::CopyFrom(const FurrowDetections& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cv.deck.proto.FurrowDetections)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FurrowDetections::IsInitialized() const {
  return true;
}

void FurrowDetections::InternalSwap(FurrowDetections* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  furrows_.InternalSwap(&other->furrows_);
  swap(timestamp_ms_, other->timestamp_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata FurrowDetections::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_getter, &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_once,
      file_level_metadata_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto[1]);
}

// ===================================================================

class GetNextFurrowDetectionsRequest::_Internal {
 public:
};

GetNextFurrowDetectionsRequest::GetNextFurrowDetectionsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:cv.deck.proto.GetNextFurrowDetectionsRequest)
}
GetNextFurrowDetectionsRequest::GetNextFurrowDetectionsRequest(const GetNextFurrowDetectionsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  camera_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_camera_id().empty()) {
    camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_camera_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&timeout_ms_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(timeout_ms_));
  // @@protoc_insertion_point(copy_constructor:cv.deck.proto.GetNextFurrowDetectionsRequest)
}

inline void GetNextFurrowDetectionsRequest::SharedCtor() {
camera_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&timeout_ms_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(timeout_ms_));
}

GetNextFurrowDetectionsRequest::~GetNextFurrowDetectionsRequest() {
  // @@protoc_insertion_point(destructor:cv.deck.proto.GetNextFurrowDetectionsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextFurrowDetectionsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  camera_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetNextFurrowDetectionsRequest::ArenaDtor(void* object) {
  GetNextFurrowDetectionsRequest* _this = reinterpret_cast< GetNextFurrowDetectionsRequest* >(object);
  (void)_this;
}
void GetNextFurrowDetectionsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextFurrowDetectionsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextFurrowDetectionsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:cv.deck.proto.GetNextFurrowDetectionsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  camera_id_.ClearToEmpty();
  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&timeout_ms_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(timeout_ms_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextFurrowDetectionsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string camera_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_camera_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "cv.deck.proto.GetNextFurrowDetectionsRequest.camera_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 timestamp_ms = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 timeout_ms = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          timeout_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextFurrowDetectionsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cv.deck.proto.GetNextFurrowDetectionsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string camera_id = 1;
  if (!this->_internal_camera_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_camera_id().data(), static_cast<int>(this->_internal_camera_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cv.deck.proto.GetNextFurrowDetectionsRequest.camera_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_camera_id(), target);
  }

  // int64 timestamp_ms = 2;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_timestamp_ms(), target);
  }

  // int64 timeout_ms = 3;
  if (this->_internal_timeout_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_timeout_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cv.deck.proto.GetNextFurrowDetectionsRequest)
  return target;
}

size_t GetNextFurrowDetectionsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cv.deck.proto.GetNextFurrowDetectionsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string camera_id = 1;
  if (!this->_internal_camera_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_camera_id());
  }

  // int64 timestamp_ms = 2;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // int64 timeout_ms = 3;
  if (this->_internal_timeout_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timeout_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextFurrowDetectionsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextFurrowDetectionsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextFurrowDetectionsRequest::GetClassData() const { return &_class_data_; }

void GetNextFurrowDetectionsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextFurrowDetectionsRequest *>(to)->MergeFrom(
      static_cast<const GetNextFurrowDetectionsRequest &>(from));
}


void GetNextFurrowDetectionsRequest::MergeFrom(const GetNextFurrowDetectionsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:cv.deck.proto.GetNextFurrowDetectionsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_camera_id().empty()) {
    _internal_set_camera_id(from._internal_camera_id());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_timeout_ms() != 0) {
    _internal_set_timeout_ms(from._internal_timeout_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextFurrowDetectionsRequest::CopyFrom(const GetNextFurrowDetectionsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cv.deck.proto.GetNextFurrowDetectionsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextFurrowDetectionsRequest::IsInitialized() const {
  return true;
}

void GetNextFurrowDetectionsRequest::InternalSwap(GetNextFurrowDetectionsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &camera_id_, lhs_arena,
      &other->camera_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextFurrowDetectionsRequest, timeout_ms_)
      + sizeof(GetNextFurrowDetectionsRequest::timeout_ms_)
      - PROTOBUF_FIELD_OFFSET(GetNextFurrowDetectionsRequest, timestamp_ms_)>(
          reinterpret_cast<char*>(&timestamp_ms_),
          reinterpret_cast<char*>(&other->timestamp_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextFurrowDetectionsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_getter, &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_once,
      file_level_metadata_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto[2]);
}

// ===================================================================

NodeTiming_StateTimingsEntry_DoNotUse::NodeTiming_StateTimingsEntry_DoNotUse() {}
NodeTiming_StateTimingsEntry_DoNotUse::NodeTiming_StateTimingsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void NodeTiming_StateTimingsEntry_DoNotUse::MergeFrom(const NodeTiming_StateTimingsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata NodeTiming_StateTimingsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_getter, &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_once,
      file_level_metadata_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto[3]);
}

// ===================================================================

class NodeTiming::_Internal {
 public:
};

NodeTiming::NodeTiming(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  state_timings_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:cv.deck.proto.NodeTiming)
}
NodeTiming::NodeTiming(const NodeTiming& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  state_timings_.MergeFrom(from.state_timings_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  state_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    state_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_state().empty()) {
    state_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_state(), 
      GetArenaForAllocation());
  }
  ::memcpy(&fps_mean_, &from.fps_mean_,
    static_cast<size_t>(reinterpret_cast<char*>(&latency_ms_99pct_) -
    reinterpret_cast<char*>(&fps_mean_)) + sizeof(latency_ms_99pct_));
  // @@protoc_insertion_point(copy_constructor:cv.deck.proto.NodeTiming)
}

inline void NodeTiming::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
state_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  state_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&fps_mean_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&latency_ms_99pct_) -
    reinterpret_cast<char*>(&fps_mean_)) + sizeof(latency_ms_99pct_));
}

NodeTiming::~NodeTiming() {
  // @@protoc_insertion_point(destructor:cv.deck.proto.NodeTiming)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void NodeTiming::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  state_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void NodeTiming::ArenaDtor(void* object) {
  NodeTiming* _this = reinterpret_cast< NodeTiming* >(object);
  (void)_this;
  _this->state_timings_. ~MapField();
}
inline void NodeTiming::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &NodeTiming::ArenaDtor);
  }
}
void NodeTiming::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void NodeTiming::Clear() {
// @@protoc_insertion_point(message_clear_start:cv.deck.proto.NodeTiming)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  state_timings_.Clear();
  name_.ClearToEmpty();
  state_.ClearToEmpty();
  ::memset(&fps_mean_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&latency_ms_99pct_) -
      reinterpret_cast<char*>(&fps_mean_)) + sizeof(latency_ms_99pct_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* NodeTiming::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "cv.deck.proto.NodeTiming.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float fps_mean = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          fps_mean_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float fps_99pct = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          fps_99pct_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float latency_ms_mean = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          latency_ms_mean_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float latency_ms_99pct = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          latency_ms_99pct_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // string state = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_state();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "cv.deck.proto.NodeTiming.state"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, float> state_timings = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&state_timings_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* NodeTiming::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cv.deck.proto.NodeTiming)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cv.deck.proto.NodeTiming.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // float fps_mean = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fps_mean = this->_internal_fps_mean();
  uint32_t raw_fps_mean;
  memcpy(&raw_fps_mean, &tmp_fps_mean, sizeof(tmp_fps_mean));
  if (raw_fps_mean != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_fps_mean(), target);
  }

  // float fps_99pct = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fps_99pct = this->_internal_fps_99pct();
  uint32_t raw_fps_99pct;
  memcpy(&raw_fps_99pct, &tmp_fps_99pct, sizeof(tmp_fps_99pct));
  if (raw_fps_99pct != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_fps_99pct(), target);
  }

  // float latency_ms_mean = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_latency_ms_mean = this->_internal_latency_ms_mean();
  uint32_t raw_latency_ms_mean;
  memcpy(&raw_latency_ms_mean, &tmp_latency_ms_mean, sizeof(tmp_latency_ms_mean));
  if (raw_latency_ms_mean != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_latency_ms_mean(), target);
  }

  // float latency_ms_99pct = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_latency_ms_99pct = this->_internal_latency_ms_99pct();
  uint32_t raw_latency_ms_99pct;
  memcpy(&raw_latency_ms_99pct, &tmp_latency_ms_99pct, sizeof(tmp_latency_ms_99pct));
  if (raw_latency_ms_99pct != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_latency_ms_99pct(), target);
  }

  // string state = 6;
  if (!this->_internal_state().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_state().data(), static_cast<int>(this->_internal_state().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "cv.deck.proto.NodeTiming.state");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_state(), target);
  }

  // map<string, float> state_timings = 7;
  if (!this->_internal_state_timings().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "cv.deck.proto.NodeTiming.StateTimingsEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_state_timings().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_state_timings().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
          it = this->_internal_state_timings().begin();
          it != this->_internal_state_timings().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = NodeTiming_StateTimingsEntry_DoNotUse::Funcs::InternalSerialize(7, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
          it = this->_internal_state_timings().begin();
          it != this->_internal_state_timings().end(); ++it) {
        target = NodeTiming_StateTimingsEntry_DoNotUse::Funcs::InternalSerialize(7, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cv.deck.proto.NodeTiming)
  return target;
}

size_t NodeTiming::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cv.deck.proto.NodeTiming)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, float> state_timings = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_state_timings_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
      it = this->_internal_state_timings().begin();
      it != this->_internal_state_timings().end(); ++it) {
    total_size += NodeTiming_StateTimingsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string state = 6;
  if (!this->_internal_state().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_state());
  }

  // float fps_mean = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fps_mean = this->_internal_fps_mean();
  uint32_t raw_fps_mean;
  memcpy(&raw_fps_mean, &tmp_fps_mean, sizeof(tmp_fps_mean));
  if (raw_fps_mean != 0) {
    total_size += 1 + 4;
  }

  // float fps_99pct = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fps_99pct = this->_internal_fps_99pct();
  uint32_t raw_fps_99pct;
  memcpy(&raw_fps_99pct, &tmp_fps_99pct, sizeof(tmp_fps_99pct));
  if (raw_fps_99pct != 0) {
    total_size += 1 + 4;
  }

  // float latency_ms_mean = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_latency_ms_mean = this->_internal_latency_ms_mean();
  uint32_t raw_latency_ms_mean;
  memcpy(&raw_latency_ms_mean, &tmp_latency_ms_mean, sizeof(tmp_latency_ms_mean));
  if (raw_latency_ms_mean != 0) {
    total_size += 1 + 4;
  }

  // float latency_ms_99pct = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_latency_ms_99pct = this->_internal_latency_ms_99pct();
  uint32_t raw_latency_ms_99pct;
  memcpy(&raw_latency_ms_99pct, &tmp_latency_ms_99pct, sizeof(tmp_latency_ms_99pct));
  if (raw_latency_ms_99pct != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData NodeTiming::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    NodeTiming::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*NodeTiming::GetClassData() const { return &_class_data_; }

void NodeTiming::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<NodeTiming *>(to)->MergeFrom(
      static_cast<const NodeTiming &>(from));
}


void NodeTiming::MergeFrom(const NodeTiming& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:cv.deck.proto.NodeTiming)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  state_timings_.MergeFrom(from.state_timings_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_state().empty()) {
    _internal_set_state(from._internal_state());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fps_mean = from._internal_fps_mean();
  uint32_t raw_fps_mean;
  memcpy(&raw_fps_mean, &tmp_fps_mean, sizeof(tmp_fps_mean));
  if (raw_fps_mean != 0) {
    _internal_set_fps_mean(from._internal_fps_mean());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_fps_99pct = from._internal_fps_99pct();
  uint32_t raw_fps_99pct;
  memcpy(&raw_fps_99pct, &tmp_fps_99pct, sizeof(tmp_fps_99pct));
  if (raw_fps_99pct != 0) {
    _internal_set_fps_99pct(from._internal_fps_99pct());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_latency_ms_mean = from._internal_latency_ms_mean();
  uint32_t raw_latency_ms_mean;
  memcpy(&raw_latency_ms_mean, &tmp_latency_ms_mean, sizeof(tmp_latency_ms_mean));
  if (raw_latency_ms_mean != 0) {
    _internal_set_latency_ms_mean(from._internal_latency_ms_mean());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_latency_ms_99pct = from._internal_latency_ms_99pct();
  uint32_t raw_latency_ms_99pct;
  memcpy(&raw_latency_ms_99pct, &tmp_latency_ms_99pct, sizeof(tmp_latency_ms_99pct));
  if (raw_latency_ms_99pct != 0) {
    _internal_set_latency_ms_99pct(from._internal_latency_ms_99pct());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void NodeTiming::CopyFrom(const NodeTiming& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cv.deck.proto.NodeTiming)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NodeTiming::IsInitialized() const {
  return true;
}

void NodeTiming::InternalSwap(NodeTiming* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  state_timings_.InternalSwap(&other->state_timings_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &state_, lhs_arena,
      &other->state_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(NodeTiming, latency_ms_99pct_)
      + sizeof(NodeTiming::latency_ms_99pct_)
      - PROTOBUF_FIELD_OFFSET(NodeTiming, fps_mean_)>(
          reinterpret_cast<char*>(&fps_mean_),
          reinterpret_cast<char*>(&other->fps_mean_));
}

::PROTOBUF_NAMESPACE_ID::Metadata NodeTiming::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_getter, &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_once,
      file_level_metadata_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto[4]);
}

// ===================================================================

class NodeTimingResponse::_Internal {
 public:
};

NodeTimingResponse::NodeTimingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  node_timing_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:cv.deck.proto.NodeTimingResponse)
}
NodeTimingResponse::NodeTimingResponse(const NodeTimingResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      node_timing_(from.node_timing_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:cv.deck.proto.NodeTimingResponse)
}

inline void NodeTimingResponse::SharedCtor() {
}

NodeTimingResponse::~NodeTimingResponse() {
  // @@protoc_insertion_point(destructor:cv.deck.proto.NodeTimingResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void NodeTimingResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void NodeTimingResponse::ArenaDtor(void* object) {
  NodeTimingResponse* _this = reinterpret_cast< NodeTimingResponse* >(object);
  (void)_this;
}
void NodeTimingResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void NodeTimingResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void NodeTimingResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:cv.deck.proto.NodeTimingResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  node_timing_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* NodeTimingResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .cv.deck.proto.NodeTiming node_timing = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_node_timing(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* NodeTimingResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:cv.deck.proto.NodeTimingResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .cv.deck.proto.NodeTiming node_timing = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_node_timing_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_node_timing(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:cv.deck.proto.NodeTimingResponse)
  return target;
}

size_t NodeTimingResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:cv.deck.proto.NodeTimingResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .cv.deck.proto.NodeTiming node_timing = 1;
  total_size += 1UL * this->_internal_node_timing_size();
  for (const auto& msg : this->node_timing_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData NodeTimingResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    NodeTimingResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*NodeTimingResponse::GetClassData() const { return &_class_data_; }

void NodeTimingResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<NodeTimingResponse *>(to)->MergeFrom(
      static_cast<const NodeTimingResponse &>(from));
}


void NodeTimingResponse::MergeFrom(const NodeTimingResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:cv.deck.proto.NodeTimingResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  node_timing_.MergeFrom(from.node_timing_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void NodeTimingResponse::CopyFrom(const NodeTimingResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:cv.deck.proto.NodeTimingResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NodeTimingResponse::IsInitialized() const {
  return true;
}

void NodeTimingResponse::InternalSwap(NodeTimingResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  node_timing_.InternalSwap(&other->node_timing_);
}

::PROTOBUF_NAMESPACE_ID::Metadata NodeTimingResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_getter, &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_once,
      file_level_metadata_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto[5]);
}

// ===================================================================

class Empty::_Internal {
 public:
};

Empty::Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:cv.deck.proto.Empty)
}
Empty::Empty(const Empty& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:cv.deck.proto.Empty)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Empty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Empty::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata Empty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_getter, &descriptor_table_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto_once,
      file_level_metadata_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto[6]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace proto
}  // namespace deck
}  // namespace cv
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::cv::deck::proto::FurrowDetection* Arena::CreateMaybeMessage< ::cv::deck::proto::FurrowDetection >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cv::deck::proto::FurrowDetection >(arena);
}
template<> PROTOBUF_NOINLINE ::cv::deck::proto::FurrowDetections* Arena::CreateMaybeMessage< ::cv::deck::proto::FurrowDetections >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cv::deck::proto::FurrowDetections >(arena);
}
template<> PROTOBUF_NOINLINE ::cv::deck::proto::GetNextFurrowDetectionsRequest* Arena::CreateMaybeMessage< ::cv::deck::proto::GetNextFurrowDetectionsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cv::deck::proto::GetNextFurrowDetectionsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse* Arena::CreateMaybeMessage< ::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cv::deck::proto::NodeTiming_StateTimingsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::cv::deck::proto::NodeTiming* Arena::CreateMaybeMessage< ::cv::deck::proto::NodeTiming >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cv::deck::proto::NodeTiming >(arena);
}
template<> PROTOBUF_NOINLINE ::cv::deck::proto::NodeTimingResponse* Arena::CreateMaybeMessage< ::cv::deck::proto::NodeTimingResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cv::deck::proto::NodeTimingResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::cv::deck::proto::Empty* Arena::CreateMaybeMessage< ::cv::deck::proto::Empty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::cv::deck::proto::Empty >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: cv/deck/proto/deck_cv.proto
#ifndef GRPC_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto__INCLUDED
#define GRPC_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto__INCLUDED

#include "cv/deck/proto/deck_cv.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace cv {
namespace deck {
namespace proto {

class DeckCVService final {
 public:
  static constexpr char const* service_full_name() {
    return "cv.deck.proto.DeckCVService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::cv::deck::proto::FurrowDetections* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::FurrowDetections>> AsyncGetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::FurrowDetections>>(AsyncGetNextFurrowDetectionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::FurrowDetections>> PrepareAsyncGetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::FurrowDetections>>(PrepareAsyncGetNextFurrowDetectionsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::cv::deck::proto::NodeTimingResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::NodeTimingResponse>> AsyncGetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::NodeTimingResponse>>(AsyncGetNodeTimingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::NodeTimingResponse>> PrepareAsyncGetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::NodeTimingResponse>>(PrepareAsyncGetNodeTimingRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::cv::deck::proto::FurrowDetections* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::cv::deck::proto::FurrowDetections* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty* request, ::cv::deck::proto::NodeTimingResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty* request, ::cv::deck::proto::NodeTimingResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::FurrowDetections>* AsyncGetNextFurrowDetectionsRaw(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::FurrowDetections>* PrepareAsyncGetNextFurrowDetectionsRaw(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::NodeTimingResponse>* AsyncGetNodeTimingRaw(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::cv::deck::proto::NodeTimingResponse>* PrepareAsyncGetNodeTimingRaw(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::cv::deck::proto::FurrowDetections* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::FurrowDetections>> AsyncGetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::FurrowDetections>>(AsyncGetNextFurrowDetectionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::FurrowDetections>> PrepareAsyncGetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::FurrowDetections>>(PrepareAsyncGetNextFurrowDetectionsRaw(context, request, cq));
    }
    ::grpc::Status GetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::cv::deck::proto::NodeTimingResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::NodeTimingResponse>> AsyncGetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::NodeTimingResponse>>(AsyncGetNodeTimingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::NodeTimingResponse>> PrepareAsyncGetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::NodeTimingResponse>>(PrepareAsyncGetNodeTimingRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::cv::deck::proto::FurrowDetections* response, std::function<void(::grpc::Status)>) override;
      void GetNextFurrowDetections(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::cv::deck::proto::FurrowDetections* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty* request, ::cv::deck::proto::NodeTimingResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNodeTiming(::grpc::ClientContext* context, const ::cv::deck::proto::Empty* request, ::cv::deck::proto::NodeTimingResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::FurrowDetections>* AsyncGetNextFurrowDetectionsRaw(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::FurrowDetections>* PrepareAsyncGetNextFurrowDetectionsRaw(::grpc::ClientContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::NodeTimingResponse>* AsyncGetNodeTimingRaw(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::cv::deck::proto::NodeTimingResponse>* PrepareAsyncGetNodeTimingRaw(::grpc::ClientContext* context, const ::cv::deck::proto::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextFurrowDetections_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNodeTiming_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextFurrowDetections(::grpc::ServerContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::cv::deck::proto::FurrowDetections* response);
    virtual ::grpc::Status GetNodeTiming(::grpc::ServerContext* context, const ::cv::deck::proto::Empty* request, ::cv::deck::proto::NodeTimingResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextFurrowDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextFurrowDetections() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextFurrowDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFurrowDetections(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* /*request*/, ::cv::deck::proto::FurrowDetections* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextFurrowDetections(::grpc::ServerContext* context, ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::grpc::ServerAsyncResponseWriter< ::cv::deck::proto::FurrowDetections>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNodeTiming : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNodeTiming() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNodeTiming() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNodeTiming(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::Empty* /*request*/, ::cv::deck::proto::NodeTimingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNodeTiming(::grpc::ServerContext* context, ::cv::deck::proto::Empty* request, ::grpc::ServerAsyncResponseWriter< ::cv::deck::proto::NodeTimingResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextFurrowDetections<WithAsyncMethod_GetNodeTiming<Service > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextFurrowDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextFurrowDetections() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::cv::deck::proto::GetNextFurrowDetectionsRequest, ::cv::deck::proto::FurrowDetections>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* request, ::cv::deck::proto::FurrowDetections* response) { return this->GetNextFurrowDetections(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextFurrowDetections(
        ::grpc::MessageAllocator< ::cv::deck::proto::GetNextFurrowDetectionsRequest, ::cv::deck::proto::FurrowDetections>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::cv::deck::proto::GetNextFurrowDetectionsRequest, ::cv::deck::proto::FurrowDetections>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextFurrowDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFurrowDetections(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* /*request*/, ::cv::deck::proto::FurrowDetections* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextFurrowDetections(
      ::grpc::CallbackServerContext* /*context*/, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* /*request*/, ::cv::deck::proto::FurrowDetections* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNodeTiming : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNodeTiming() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::cv::deck::proto::Empty, ::cv::deck::proto::NodeTimingResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::cv::deck::proto::Empty* request, ::cv::deck::proto::NodeTimingResponse* response) { return this->GetNodeTiming(context, request, response); }));}
    void SetMessageAllocatorFor_GetNodeTiming(
        ::grpc::MessageAllocator< ::cv::deck::proto::Empty, ::cv::deck::proto::NodeTimingResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::cv::deck::proto::Empty, ::cv::deck::proto::NodeTimingResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNodeTiming() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNodeTiming(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::Empty* /*request*/, ::cv::deck::proto::NodeTimingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNodeTiming(
      ::grpc::CallbackServerContext* /*context*/, const ::cv::deck::proto::Empty* /*request*/, ::cv::deck::proto::NodeTimingResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextFurrowDetections<WithCallbackMethod_GetNodeTiming<Service > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextFurrowDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextFurrowDetections() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextFurrowDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFurrowDetections(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* /*request*/, ::cv::deck::proto::FurrowDetections* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNodeTiming : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNodeTiming() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNodeTiming() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNodeTiming(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::Empty* /*request*/, ::cv::deck::proto::NodeTimingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextFurrowDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextFurrowDetections() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextFurrowDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFurrowDetections(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* /*request*/, ::cv::deck::proto::FurrowDetections* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextFurrowDetections(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNodeTiming : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNodeTiming() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNodeTiming() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNodeTiming(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::Empty* /*request*/, ::cv::deck::proto::NodeTimingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNodeTiming(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextFurrowDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextFurrowDetections() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextFurrowDetections(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextFurrowDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFurrowDetections(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* /*request*/, ::cv::deck::proto::FurrowDetections* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextFurrowDetections(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNodeTiming : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNodeTiming() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNodeTiming(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNodeTiming() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNodeTiming(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::Empty* /*request*/, ::cv::deck::proto::NodeTimingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNodeTiming(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextFurrowDetections : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextFurrowDetections() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::cv::deck::proto::GetNextFurrowDetectionsRequest, ::cv::deck::proto::FurrowDetections>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::cv::deck::proto::GetNextFurrowDetectionsRequest, ::cv::deck::proto::FurrowDetections>* streamer) {
                       return this->StreamedGetNextFurrowDetections(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextFurrowDetections() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextFurrowDetections(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::GetNextFurrowDetectionsRequest* /*request*/, ::cv::deck::proto::FurrowDetections* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextFurrowDetections(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::cv::deck::proto::GetNextFurrowDetectionsRequest,::cv::deck::proto::FurrowDetections>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNodeTiming : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNodeTiming() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::cv::deck::proto::Empty, ::cv::deck::proto::NodeTimingResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::cv::deck::proto::Empty, ::cv::deck::proto::NodeTimingResponse>* streamer) {
                       return this->StreamedGetNodeTiming(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNodeTiming() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNodeTiming(::grpc::ServerContext* /*context*/, const ::cv::deck::proto::Empty* /*request*/, ::cv::deck::proto::NodeTimingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNodeTiming(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::cv::deck::proto::Empty,::cv::deck::proto::NodeTimingResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextFurrowDetections<WithStreamedUnaryMethod_GetNodeTiming<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextFurrowDetections<WithStreamedUnaryMethod_GetNodeTiming<Service > > StreamedService;
};

}  // namespace proto
}  // namespace deck
}  // namespace cv


#endif  // GRPC_cv_2fdeck_2fproto_2fdeck_5fcv_2eproto__INCLUDED

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: metrics/proto/metrics_aggregator_service.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.proto.metrics import metrics_pb2 as proto_dot_metrics_dot_metrics__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='metrics/proto/metrics_aggregator_service.proto',
  package='metrics_aggregator',
  syntax='proto3',
  serialized_options=b'Z\030proto/metrics_aggregator',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n.metrics/proto/metrics_aggregator_service.proto\x12\x12metrics_aggregator\x1a\x1bproto/metrics/metrics.proto\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\r\"\x19\n\x0cPingResponse\x12\t\n\x01x\x18\x01 \x01(\r\"t\n\x07Metrics\x12\x39\n\x07metrics\x18\x01 \x03(\x0b\x32(.metrics_aggregator.Metrics.MetricsEntry\x1a.\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x13\n\x11GetMetricsRequest\"\xb7\x01\n\x12GetMetricsResponse\x12O\n\rdaily_metrics\x18\x01 \x03(\x0b\x32\x38.metrics_aggregator.GetMetricsResponse.DailyMetricsEntry\x1aP\n\x11\x44\x61ilyMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics:\x02\x38\x01\"-\n\x1d\x41\x63knowledgeDailyMetricRequest\x12\x0c\n\x04\x64\x61ys\x18\x01 \x03(\t\" \n\x1e\x41\x63knowledgeDailyMetricResponse\"\x16\n\x14GetJobMetricsRequest\"H\n\x15GetJobMetricsResponse\x12/\n\njobMetrics\x18\x01 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics\"\x1a\n\x18GetLaserLifeTimesRequest\"\x12\n\x10SetLaserResponse\"\x1c\n\x1aGetLaserChangeTimesRequest\"\x1e\n\x1cRegisterSpatialClientRequest\"+\n\x1dRegisterSpatialClientResponse\x12\n\n\x02id\x18\x01 \x01(\r\"&\n\x18SpatialClientBeatRequest\x12\n\n\x02id\x18\x01 \x01(\r\",\n\x19SpatialClientBeatResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\">\n\x17SpatialClientAckRequest\x12\x11\n\tclient_id\x18\x01 \x01(\r\x12\x10\n\x08\x62lock_id\x18\x02 \x01(\x04\"+\n\x18SpatialClientAckResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\".\n\x1aGetNextSpatialBlockRequest\x12\x10\n\x08\x62lock_id\x18\x01 \x01(\x04\"/\n\x1bGetNextSpatialBlockResponse\x12\x10\n\x08\x62lock_id\x18\x01 \x01(\x04\"Z\n\x14OverrideLaserRequest\x12.\n\x05laser\x18\x01 \x01(\x0b\x32\x1f.carbon.metrics.LaserIdentifier\x12\x12\n\nlifetime_s\x18\x02 \x01(\x04\x32\x97\n\n\x18MetricsAggregatorService\x12K\n\x04Ping\x12\x1f.metrics_aggregator.PingRequest\x1a .metrics_aggregator.PingResponse\"\x00\x12]\n\nGetMetrics\x12%.metrics_aggregator.GetMetricsRequest\x1a&.metrics_aggregator.GetMetricsResponse\"\x00\x12\x81\x01\n\x16\x41\x63knowledgeDailyMetric\x12\x31.metrics_aggregator.AcknowledgeDailyMetricRequest\x1a\x32.metrics_aggregator.AcknowledgeDailyMetricResponse\"\x00\x12\x64\n\rGetJobMetrics\x12(.metrics_aggregator.GetJobMetricsRequest\x1a).metrics_aggregator.GetJobMetricsResponse\x12\x63\n\x11GetLaserLifeTimes\x12,.metrics_aggregator.GetLaserLifeTimesRequest\x1a\x1e.carbon.metrics.LaserLifeTimes\"\x00\x12S\n\x08SetLaser\x12\x1f.carbon.metrics.LaserIdentifier\x1a$.metrics_aggregator.SetLaserResponse\"\x00\x12\x61\n\rOverrideLaser\x12(.metrics_aggregator.OverrideLaserRequest\x1a$.metrics_aggregator.SetLaserResponse\"\x00\x12i\n\x13GetLaserChangeTimes\x12..metrics_aggregator.GetLaserChangeTimesRequest\x1a .carbon.metrics.LaserChangeTimes\"\x00\x12~\n\x15RegisterSpatialClient\x12\x30.metrics_aggregator.RegisterSpatialClientRequest\x1a\x31.metrics_aggregator.RegisterSpatialClientResponse\"\x00\x12r\n\x11SpatialClientBeat\x12,.metrics_aggregator.SpatialClientBeatRequest\x1a-.metrics_aggregator.SpatialClientBeatResponse\"\x00\x12o\n\x10SpatialClientAck\x12+.metrics_aggregator.SpatialClientAckRequest\x1a,.metrics_aggregator.SpatialClientAckResponse\"\x00\x12x\n\x13GetNextSpatialBlock\x12..metrics_aggregator.GetNextSpatialBlockRequest\x1a/.metrics_aggregator.GetNextSpatialBlockResponse\"\x00\x42\x1aZ\x18proto/metrics_aggregatorb\x06proto3'
  ,
  dependencies=[proto_dot_metrics_dot_metrics__pb2.DESCRIPTOR,])




_PINGREQUEST = _descriptor.Descriptor(
  name='PingRequest',
  full_name='metrics_aggregator.PingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='metrics_aggregator.PingRequest.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=99,
  serialized_end=123,
)


_PINGRESPONSE = _descriptor.Descriptor(
  name='PingResponse',
  full_name='metrics_aggregator.PingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='metrics_aggregator.PingResponse.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=125,
  serialized_end=150,
)


_METRICS_METRICSENTRY = _descriptor.Descriptor(
  name='MetricsEntry',
  full_name='metrics_aggregator.Metrics.MetricsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='metrics_aggregator.Metrics.MetricsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='metrics_aggregator.Metrics.MetricsEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=222,
  serialized_end=268,
)

_METRICS = _descriptor.Descriptor(
  name='Metrics',
  full_name='metrics_aggregator.Metrics',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='metrics', full_name='metrics_aggregator.Metrics.metrics', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_METRICS_METRICSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=152,
  serialized_end=268,
)


_GETMETRICSREQUEST = _descriptor.Descriptor(
  name='GetMetricsRequest',
  full_name='metrics_aggregator.GetMetricsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=270,
  serialized_end=289,
)


_GETMETRICSRESPONSE_DAILYMETRICSENTRY = _descriptor.Descriptor(
  name='DailyMetricsEntry',
  full_name='metrics_aggregator.GetMetricsResponse.DailyMetricsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='metrics_aggregator.GetMetricsResponse.DailyMetricsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='metrics_aggregator.GetMetricsResponse.DailyMetricsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=395,
  serialized_end=475,
)

_GETMETRICSRESPONSE = _descriptor.Descriptor(
  name='GetMetricsResponse',
  full_name='metrics_aggregator.GetMetricsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='daily_metrics', full_name='metrics_aggregator.GetMetricsResponse.daily_metrics', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETMETRICSRESPONSE_DAILYMETRICSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=292,
  serialized_end=475,
)


_ACKNOWLEDGEDAILYMETRICREQUEST = _descriptor.Descriptor(
  name='AcknowledgeDailyMetricRequest',
  full_name='metrics_aggregator.AcknowledgeDailyMetricRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='days', full_name='metrics_aggregator.AcknowledgeDailyMetricRequest.days', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=477,
  serialized_end=522,
)


_ACKNOWLEDGEDAILYMETRICRESPONSE = _descriptor.Descriptor(
  name='AcknowledgeDailyMetricResponse',
  full_name='metrics_aggregator.AcknowledgeDailyMetricResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=524,
  serialized_end=556,
)


_GETJOBMETRICSREQUEST = _descriptor.Descriptor(
  name='GetJobMetricsRequest',
  full_name='metrics_aggregator.GetJobMetricsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=558,
  serialized_end=580,
)


_GETJOBMETRICSRESPONSE = _descriptor.Descriptor(
  name='GetJobMetricsResponse',
  full_name='metrics_aggregator.GetJobMetricsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobMetrics', full_name='metrics_aggregator.GetJobMetricsResponse.jobMetrics', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=582,
  serialized_end=654,
)


_GETLASERLIFETIMESREQUEST = _descriptor.Descriptor(
  name='GetLaserLifeTimesRequest',
  full_name='metrics_aggregator.GetLaserLifeTimesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=656,
  serialized_end=682,
)


_SETLASERRESPONSE = _descriptor.Descriptor(
  name='SetLaserResponse',
  full_name='metrics_aggregator.SetLaserResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=684,
  serialized_end=702,
)


_GETLASERCHANGETIMESREQUEST = _descriptor.Descriptor(
  name='GetLaserChangeTimesRequest',
  full_name='metrics_aggregator.GetLaserChangeTimesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=704,
  serialized_end=732,
)


_REGISTERSPATIALCLIENTREQUEST = _descriptor.Descriptor(
  name='RegisterSpatialClientRequest',
  full_name='metrics_aggregator.RegisterSpatialClientRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=734,
  serialized_end=764,
)


_REGISTERSPATIALCLIENTRESPONSE = _descriptor.Descriptor(
  name='RegisterSpatialClientResponse',
  full_name='metrics_aggregator.RegisterSpatialClientResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='metrics_aggregator.RegisterSpatialClientResponse.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=766,
  serialized_end=809,
)


_SPATIALCLIENTBEATREQUEST = _descriptor.Descriptor(
  name='SpatialClientBeatRequest',
  full_name='metrics_aggregator.SpatialClientBeatRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='metrics_aggregator.SpatialClientBeatRequest.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=811,
  serialized_end=849,
)


_SPATIALCLIENTBEATRESPONSE = _descriptor.Descriptor(
  name='SpatialClientBeatResponse',
  full_name='metrics_aggregator.SpatialClientBeatResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='metrics_aggregator.SpatialClientBeatResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=851,
  serialized_end=895,
)


_SPATIALCLIENTACKREQUEST = _descriptor.Descriptor(
  name='SpatialClientAckRequest',
  full_name='metrics_aggregator.SpatialClientAckRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='client_id', full_name='metrics_aggregator.SpatialClientAckRequest.client_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='block_id', full_name='metrics_aggregator.SpatialClientAckRequest.block_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=897,
  serialized_end=959,
)


_SPATIALCLIENTACKRESPONSE = _descriptor.Descriptor(
  name='SpatialClientAckResponse',
  full_name='metrics_aggregator.SpatialClientAckResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='metrics_aggregator.SpatialClientAckResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=961,
  serialized_end=1004,
)


_GETNEXTSPATIALBLOCKREQUEST = _descriptor.Descriptor(
  name='GetNextSpatialBlockRequest',
  full_name='metrics_aggregator.GetNextSpatialBlockRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='block_id', full_name='metrics_aggregator.GetNextSpatialBlockRequest.block_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1006,
  serialized_end=1052,
)


_GETNEXTSPATIALBLOCKRESPONSE = _descriptor.Descriptor(
  name='GetNextSpatialBlockResponse',
  full_name='metrics_aggregator.GetNextSpatialBlockResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='block_id', full_name='metrics_aggregator.GetNextSpatialBlockResponse.block_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1054,
  serialized_end=1101,
)


_OVERRIDELASERREQUEST = _descriptor.Descriptor(
  name='OverrideLaserRequest',
  full_name='metrics_aggregator.OverrideLaserRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='laser', full_name='metrics_aggregator.OverrideLaserRequest.laser', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifetime_s', full_name='metrics_aggregator.OverrideLaserRequest.lifetime_s', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1103,
  serialized_end=1193,
)

_METRICS_METRICSENTRY.containing_type = _METRICS
_METRICS.fields_by_name['metrics'].message_type = _METRICS_METRICSENTRY
_GETMETRICSRESPONSE_DAILYMETRICSENTRY.fields_by_name['value'].message_type = _METRICS
_GETMETRICSRESPONSE_DAILYMETRICSENTRY.containing_type = _GETMETRICSRESPONSE
_GETMETRICSRESPONSE.fields_by_name['daily_metrics'].message_type = _GETMETRICSRESPONSE_DAILYMETRICSENTRY
_GETJOBMETRICSRESPONSE.fields_by_name['jobMetrics'].message_type = _METRICS
_OVERRIDELASERREQUEST.fields_by_name['laser'].message_type = proto_dot_metrics_dot_metrics__pb2._LASERIDENTIFIER
DESCRIPTOR.message_types_by_name['PingRequest'] = _PINGREQUEST
DESCRIPTOR.message_types_by_name['PingResponse'] = _PINGRESPONSE
DESCRIPTOR.message_types_by_name['Metrics'] = _METRICS
DESCRIPTOR.message_types_by_name['GetMetricsRequest'] = _GETMETRICSREQUEST
DESCRIPTOR.message_types_by_name['GetMetricsResponse'] = _GETMETRICSRESPONSE
DESCRIPTOR.message_types_by_name['AcknowledgeDailyMetricRequest'] = _ACKNOWLEDGEDAILYMETRICREQUEST
DESCRIPTOR.message_types_by_name['AcknowledgeDailyMetricResponse'] = _ACKNOWLEDGEDAILYMETRICRESPONSE
DESCRIPTOR.message_types_by_name['GetJobMetricsRequest'] = _GETJOBMETRICSREQUEST
DESCRIPTOR.message_types_by_name['GetJobMetricsResponse'] = _GETJOBMETRICSRESPONSE
DESCRIPTOR.message_types_by_name['GetLaserLifeTimesRequest'] = _GETLASERLIFETIMESREQUEST
DESCRIPTOR.message_types_by_name['SetLaserResponse'] = _SETLASERRESPONSE
DESCRIPTOR.message_types_by_name['GetLaserChangeTimesRequest'] = _GETLASERCHANGETIMESREQUEST
DESCRIPTOR.message_types_by_name['RegisterSpatialClientRequest'] = _REGISTERSPATIALCLIENTREQUEST
DESCRIPTOR.message_types_by_name['RegisterSpatialClientResponse'] = _REGISTERSPATIALCLIENTRESPONSE
DESCRIPTOR.message_types_by_name['SpatialClientBeatRequest'] = _SPATIALCLIENTBEATREQUEST
DESCRIPTOR.message_types_by_name['SpatialClientBeatResponse'] = _SPATIALCLIENTBEATRESPONSE
DESCRIPTOR.message_types_by_name['SpatialClientAckRequest'] = _SPATIALCLIENTACKREQUEST
DESCRIPTOR.message_types_by_name['SpatialClientAckResponse'] = _SPATIALCLIENTACKRESPONSE
DESCRIPTOR.message_types_by_name['GetNextSpatialBlockRequest'] = _GETNEXTSPATIALBLOCKREQUEST
DESCRIPTOR.message_types_by_name['GetNextSpatialBlockResponse'] = _GETNEXTSPATIALBLOCKRESPONSE
DESCRIPTOR.message_types_by_name['OverrideLaserRequest'] = _OVERRIDELASERREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PingRequest = _reflection.GeneratedProtocolMessageType('PingRequest', (_message.Message,), {
  'DESCRIPTOR' : _PINGREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.PingRequest)
  })
_sym_db.RegisterMessage(PingRequest)

PingResponse = _reflection.GeneratedProtocolMessageType('PingResponse', (_message.Message,), {
  'DESCRIPTOR' : _PINGRESPONSE,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.PingResponse)
  })
_sym_db.RegisterMessage(PingResponse)

Metrics = _reflection.GeneratedProtocolMessageType('Metrics', (_message.Message,), {

  'MetricsEntry' : _reflection.GeneratedProtocolMessageType('MetricsEntry', (_message.Message,), {
    'DESCRIPTOR' : _METRICS_METRICSENTRY,
    '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
    # @@protoc_insertion_point(class_scope:metrics_aggregator.Metrics.MetricsEntry)
    })
  ,
  'DESCRIPTOR' : _METRICS,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.Metrics)
  })
_sym_db.RegisterMessage(Metrics)
_sym_db.RegisterMessage(Metrics.MetricsEntry)

GetMetricsRequest = _reflection.GeneratedProtocolMessageType('GetMetricsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETMETRICSREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.GetMetricsRequest)
  })
_sym_db.RegisterMessage(GetMetricsRequest)

GetMetricsResponse = _reflection.GeneratedProtocolMessageType('GetMetricsResponse', (_message.Message,), {

  'DailyMetricsEntry' : _reflection.GeneratedProtocolMessageType('DailyMetricsEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETMETRICSRESPONSE_DAILYMETRICSENTRY,
    '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
    # @@protoc_insertion_point(class_scope:metrics_aggregator.GetMetricsResponse.DailyMetricsEntry)
    })
  ,
  'DESCRIPTOR' : _GETMETRICSRESPONSE,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.GetMetricsResponse)
  })
_sym_db.RegisterMessage(GetMetricsResponse)
_sym_db.RegisterMessage(GetMetricsResponse.DailyMetricsEntry)

AcknowledgeDailyMetricRequest = _reflection.GeneratedProtocolMessageType('AcknowledgeDailyMetricRequest', (_message.Message,), {
  'DESCRIPTOR' : _ACKNOWLEDGEDAILYMETRICREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.AcknowledgeDailyMetricRequest)
  })
_sym_db.RegisterMessage(AcknowledgeDailyMetricRequest)

AcknowledgeDailyMetricResponse = _reflection.GeneratedProtocolMessageType('AcknowledgeDailyMetricResponse', (_message.Message,), {
  'DESCRIPTOR' : _ACKNOWLEDGEDAILYMETRICRESPONSE,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.AcknowledgeDailyMetricResponse)
  })
_sym_db.RegisterMessage(AcknowledgeDailyMetricResponse)

GetJobMetricsRequest = _reflection.GeneratedProtocolMessageType('GetJobMetricsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETJOBMETRICSREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.GetJobMetricsRequest)
  })
_sym_db.RegisterMessage(GetJobMetricsRequest)

GetJobMetricsResponse = _reflection.GeneratedProtocolMessageType('GetJobMetricsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETJOBMETRICSRESPONSE,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.GetJobMetricsResponse)
  })
_sym_db.RegisterMessage(GetJobMetricsResponse)

GetLaserLifeTimesRequest = _reflection.GeneratedProtocolMessageType('GetLaserLifeTimesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETLASERLIFETIMESREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.GetLaserLifeTimesRequest)
  })
_sym_db.RegisterMessage(GetLaserLifeTimesRequest)

SetLaserResponse = _reflection.GeneratedProtocolMessageType('SetLaserResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETLASERRESPONSE,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.SetLaserResponse)
  })
_sym_db.RegisterMessage(SetLaserResponse)

GetLaserChangeTimesRequest = _reflection.GeneratedProtocolMessageType('GetLaserChangeTimesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETLASERCHANGETIMESREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.GetLaserChangeTimesRequest)
  })
_sym_db.RegisterMessage(GetLaserChangeTimesRequest)

RegisterSpatialClientRequest = _reflection.GeneratedProtocolMessageType('RegisterSpatialClientRequest', (_message.Message,), {
  'DESCRIPTOR' : _REGISTERSPATIALCLIENTREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.RegisterSpatialClientRequest)
  })
_sym_db.RegisterMessage(RegisterSpatialClientRequest)

RegisterSpatialClientResponse = _reflection.GeneratedProtocolMessageType('RegisterSpatialClientResponse', (_message.Message,), {
  'DESCRIPTOR' : _REGISTERSPATIALCLIENTRESPONSE,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.RegisterSpatialClientResponse)
  })
_sym_db.RegisterMessage(RegisterSpatialClientResponse)

SpatialClientBeatRequest = _reflection.GeneratedProtocolMessageType('SpatialClientBeatRequest', (_message.Message,), {
  'DESCRIPTOR' : _SPATIALCLIENTBEATREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.SpatialClientBeatRequest)
  })
_sym_db.RegisterMessage(SpatialClientBeatRequest)

SpatialClientBeatResponse = _reflection.GeneratedProtocolMessageType('SpatialClientBeatResponse', (_message.Message,), {
  'DESCRIPTOR' : _SPATIALCLIENTBEATRESPONSE,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.SpatialClientBeatResponse)
  })
_sym_db.RegisterMessage(SpatialClientBeatResponse)

SpatialClientAckRequest = _reflection.GeneratedProtocolMessageType('SpatialClientAckRequest', (_message.Message,), {
  'DESCRIPTOR' : _SPATIALCLIENTACKREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.SpatialClientAckRequest)
  })
_sym_db.RegisterMessage(SpatialClientAckRequest)

SpatialClientAckResponse = _reflection.GeneratedProtocolMessageType('SpatialClientAckResponse', (_message.Message,), {
  'DESCRIPTOR' : _SPATIALCLIENTACKRESPONSE,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.SpatialClientAckResponse)
  })
_sym_db.RegisterMessage(SpatialClientAckResponse)

GetNextSpatialBlockRequest = _reflection.GeneratedProtocolMessageType('GetNextSpatialBlockRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTSPATIALBLOCKREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.GetNextSpatialBlockRequest)
  })
_sym_db.RegisterMessage(GetNextSpatialBlockRequest)

GetNextSpatialBlockResponse = _reflection.GeneratedProtocolMessageType('GetNextSpatialBlockResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTSPATIALBLOCKRESPONSE,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.GetNextSpatialBlockResponse)
  })
_sym_db.RegisterMessage(GetNextSpatialBlockResponse)

OverrideLaserRequest = _reflection.GeneratedProtocolMessageType('OverrideLaserRequest', (_message.Message,), {
  'DESCRIPTOR' : _OVERRIDELASERREQUEST,
  '__module__' : 'metrics.proto.metrics_aggregator_service_pb2'
  # @@protoc_insertion_point(class_scope:metrics_aggregator.OverrideLaserRequest)
  })
_sym_db.RegisterMessage(OverrideLaserRequest)


DESCRIPTOR._options = None
_METRICS_METRICSENTRY._options = None
_GETMETRICSRESPONSE_DAILYMETRICSENTRY._options = None

_METRICSAGGREGATORSERVICE = _descriptor.ServiceDescriptor(
  name='MetricsAggregatorService',
  full_name='metrics_aggregator.MetricsAggregatorService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1196,
  serialized_end=2499,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='metrics_aggregator.MetricsAggregatorService.Ping',
    index=0,
    containing_service=None,
    input_type=_PINGREQUEST,
    output_type=_PINGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetMetrics',
    full_name='metrics_aggregator.MetricsAggregatorService.GetMetrics',
    index=1,
    containing_service=None,
    input_type=_GETMETRICSREQUEST,
    output_type=_GETMETRICSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='AcknowledgeDailyMetric',
    full_name='metrics_aggregator.MetricsAggregatorService.AcknowledgeDailyMetric',
    index=2,
    containing_service=None,
    input_type=_ACKNOWLEDGEDAILYMETRICREQUEST,
    output_type=_ACKNOWLEDGEDAILYMETRICRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetJobMetrics',
    full_name='metrics_aggregator.MetricsAggregatorService.GetJobMetrics',
    index=3,
    containing_service=None,
    input_type=_GETJOBMETRICSREQUEST,
    output_type=_GETJOBMETRICSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetLaserLifeTimes',
    full_name='metrics_aggregator.MetricsAggregatorService.GetLaserLifeTimes',
    index=4,
    containing_service=None,
    input_type=_GETLASERLIFETIMESREQUEST,
    output_type=proto_dot_metrics_dot_metrics__pb2._LASERLIFETIMES,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetLaser',
    full_name='metrics_aggregator.MetricsAggregatorService.SetLaser',
    index=5,
    containing_service=None,
    input_type=proto_dot_metrics_dot_metrics__pb2._LASERIDENTIFIER,
    output_type=_SETLASERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='OverrideLaser',
    full_name='metrics_aggregator.MetricsAggregatorService.OverrideLaser',
    index=6,
    containing_service=None,
    input_type=_OVERRIDELASERREQUEST,
    output_type=_SETLASERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetLaserChangeTimes',
    full_name='metrics_aggregator.MetricsAggregatorService.GetLaserChangeTimes',
    index=7,
    containing_service=None,
    input_type=_GETLASERCHANGETIMESREQUEST,
    output_type=proto_dot_metrics_dot_metrics__pb2._LASERCHANGETIMES,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='RegisterSpatialClient',
    full_name='metrics_aggregator.MetricsAggregatorService.RegisterSpatialClient',
    index=8,
    containing_service=None,
    input_type=_REGISTERSPATIALCLIENTREQUEST,
    output_type=_REGISTERSPATIALCLIENTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SpatialClientBeat',
    full_name='metrics_aggregator.MetricsAggregatorService.SpatialClientBeat',
    index=9,
    containing_service=None,
    input_type=_SPATIALCLIENTBEATREQUEST,
    output_type=_SPATIALCLIENTBEATRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SpatialClientAck',
    full_name='metrics_aggregator.MetricsAggregatorService.SpatialClientAck',
    index=10,
    containing_service=None,
    input_type=_SPATIALCLIENTACKREQUEST,
    output_type=_SPATIALCLIENTACKRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextSpatialBlock',
    full_name='metrics_aggregator.MetricsAggregatorService.GetNextSpatialBlock',
    index=11,
    containing_service=None,
    input_type=_GETNEXTSPATIALBLOCKREQUEST,
    output_type=_GETNEXTSPATIALBLOCKRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_METRICSAGGREGATORSERVICE)

DESCRIPTOR.services_by_name['MetricsAggregatorService'] = _METRICSAGGREGATORSERVICE

# @@protoc_insertion_point(module_scope)

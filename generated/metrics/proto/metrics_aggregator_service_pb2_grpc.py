# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.metrics.proto import metrics_aggregator_service_pb2 as metrics_dot_proto_dot_metrics__aggregator__service__pb2
from generated.proto.metrics import metrics_pb2 as proto_dot_metrics_dot_metrics__pb2


class MetricsAggregatorServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/Ping',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.PingRequest.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.PingResponse.FromString,
                )
        self.GetMetrics = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/GetMetrics',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetMetricsRequest.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetMetricsResponse.FromString,
                )
        self.AcknowledgeDailyMetric = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/AcknowledgeDailyMetric',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricRequest.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricResponse.FromString,
                )
        self.GetJobMetrics = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/GetJobMetrics',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetJobMetricsRequest.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetJobMetricsResponse.FromString,
                )
        self.GetLaserLifeTimes = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/GetLaserLifeTimes',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetLaserLifeTimesRequest.SerializeToString,
                response_deserializer=proto_dot_metrics_dot_metrics__pb2.LaserLifeTimes.FromString,
                )
        self.SetLaser = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/SetLaser',
                request_serializer=proto_dot_metrics_dot_metrics__pb2.LaserIdentifier.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SetLaserResponse.FromString,
                )
        self.OverrideLaser = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/OverrideLaser',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.OverrideLaserRequest.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SetLaserResponse.FromString,
                )
        self.GetLaserChangeTimes = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/GetLaserChangeTimes',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetLaserChangeTimesRequest.SerializeToString,
                response_deserializer=proto_dot_metrics_dot_metrics__pb2.LaserChangeTimes.FromString,
                )
        self.RegisterSpatialClient = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/RegisterSpatialClient',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.RegisterSpatialClientRequest.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.RegisterSpatialClientResponse.FromString,
                )
        self.SpatialClientBeat = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/SpatialClientBeat',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientBeatRequest.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientBeatResponse.FromString,
                )
        self.SpatialClientAck = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/SpatialClientAck',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientAckRequest.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientAckResponse.FromString,
                )
        self.GetNextSpatialBlock = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/GetNextSpatialBlock',
                request_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetNextSpatialBlockRequest.SerializeToString,
                response_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetNextSpatialBlockResponse.FromString,
                )


class MetricsAggregatorServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AcknowledgeDailyMetric(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetJobMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLaserLifeTimes(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetLaser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def OverrideLaser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLaserChangeTimes(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RegisterSpatialClient(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SpatialClientBeat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SpatialClientAck(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextSpatialBlock(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_MetricsAggregatorServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.PingRequest.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.PingResponse.SerializeToString,
            ),
            'GetMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMetrics,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetMetricsRequest.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetMetricsResponse.SerializeToString,
            ),
            'AcknowledgeDailyMetric': grpc.unary_unary_rpc_method_handler(
                    servicer.AcknowledgeDailyMetric,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricRequest.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricResponse.SerializeToString,
            ),
            'GetJobMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetJobMetrics,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetJobMetricsRequest.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetJobMetricsResponse.SerializeToString,
            ),
            'GetLaserLifeTimes': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLaserLifeTimes,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetLaserLifeTimesRequest.FromString,
                    response_serializer=proto_dot_metrics_dot_metrics__pb2.LaserLifeTimes.SerializeToString,
            ),
            'SetLaser': grpc.unary_unary_rpc_method_handler(
                    servicer.SetLaser,
                    request_deserializer=proto_dot_metrics_dot_metrics__pb2.LaserIdentifier.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SetLaserResponse.SerializeToString,
            ),
            'OverrideLaser': grpc.unary_unary_rpc_method_handler(
                    servicer.OverrideLaser,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.OverrideLaserRequest.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SetLaserResponse.SerializeToString,
            ),
            'GetLaserChangeTimes': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLaserChangeTimes,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetLaserChangeTimesRequest.FromString,
                    response_serializer=proto_dot_metrics_dot_metrics__pb2.LaserChangeTimes.SerializeToString,
            ),
            'RegisterSpatialClient': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterSpatialClient,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.RegisterSpatialClientRequest.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.RegisterSpatialClientResponse.SerializeToString,
            ),
            'SpatialClientBeat': grpc.unary_unary_rpc_method_handler(
                    servicer.SpatialClientBeat,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientBeatRequest.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientBeatResponse.SerializeToString,
            ),
            'SpatialClientAck': grpc.unary_unary_rpc_method_handler(
                    servicer.SpatialClientAck,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientAckRequest.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientAckResponse.SerializeToString,
            ),
            'GetNextSpatialBlock': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextSpatialBlock,
                    request_deserializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetNextSpatialBlockRequest.FromString,
                    response_serializer=metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetNextSpatialBlockResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'metrics_aggregator.MetricsAggregatorService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class MetricsAggregatorService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/Ping',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.PingRequest.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.PingResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/GetMetrics',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetMetricsRequest.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetMetricsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AcknowledgeDailyMetric(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/AcknowledgeDailyMetric',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricRequest.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetJobMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/GetJobMetrics',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetJobMetricsRequest.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetJobMetricsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetLaserLifeTimes(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/GetLaserLifeTimes',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetLaserLifeTimesRequest.SerializeToString,
            proto_dot_metrics_dot_metrics__pb2.LaserLifeTimes.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetLaser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/SetLaser',
            proto_dot_metrics_dot_metrics__pb2.LaserIdentifier.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.SetLaserResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def OverrideLaser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/OverrideLaser',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.OverrideLaserRequest.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.SetLaserResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetLaserChangeTimes(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/GetLaserChangeTimes',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetLaserChangeTimesRequest.SerializeToString,
            proto_dot_metrics_dot_metrics__pb2.LaserChangeTimes.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RegisterSpatialClient(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/RegisterSpatialClient',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.RegisterSpatialClientRequest.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.RegisterSpatialClientResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SpatialClientBeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/SpatialClientBeat',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientBeatRequest.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientBeatResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SpatialClientAck(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/SpatialClientAck',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientAckRequest.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.SpatialClientAckResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextSpatialBlock(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/metrics_aggregator.MetricsAggregatorService/GetNextSpatialBlock',
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetNextSpatialBlockRequest.SerializeToString,
            metrics_dot_proto_dot_metrics__aggregator__service__pb2.GetNextSpatialBlockResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: metrics/proto/metrics_aggregator_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "proto/metrics/metrics.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[23]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
namespace metrics_aggregator {
class AcknowledgeDailyMetricRequest;
struct AcknowledgeDailyMetricRequestDefaultTypeInternal;
extern AcknowledgeDailyMetricRequestDefaultTypeInternal _AcknowledgeDailyMetricRequest_default_instance_;
class AcknowledgeDailyMetricResponse;
struct AcknowledgeDailyMetricResponseDefaultTypeInternal;
extern AcknowledgeDailyMetricResponseDefaultTypeInternal _AcknowledgeDailyMetricResponse_default_instance_;
class GetJobMetricsRequest;
struct GetJobMetricsRequestDefaultTypeInternal;
extern GetJobMetricsRequestDefaultTypeInternal _GetJobMetricsRequest_default_instance_;
class GetJobMetricsResponse;
struct GetJobMetricsResponseDefaultTypeInternal;
extern GetJobMetricsResponseDefaultTypeInternal _GetJobMetricsResponse_default_instance_;
class GetLaserChangeTimesRequest;
struct GetLaserChangeTimesRequestDefaultTypeInternal;
extern GetLaserChangeTimesRequestDefaultTypeInternal _GetLaserChangeTimesRequest_default_instance_;
class GetLaserLifeTimesRequest;
struct GetLaserLifeTimesRequestDefaultTypeInternal;
extern GetLaserLifeTimesRequestDefaultTypeInternal _GetLaserLifeTimesRequest_default_instance_;
class GetMetricsRequest;
struct GetMetricsRequestDefaultTypeInternal;
extern GetMetricsRequestDefaultTypeInternal _GetMetricsRequest_default_instance_;
class GetMetricsResponse;
struct GetMetricsResponseDefaultTypeInternal;
extern GetMetricsResponseDefaultTypeInternal _GetMetricsResponse_default_instance_;
class GetMetricsResponse_DailyMetricsEntry_DoNotUse;
struct GetMetricsResponse_DailyMetricsEntry_DoNotUseDefaultTypeInternal;
extern GetMetricsResponse_DailyMetricsEntry_DoNotUseDefaultTypeInternal _GetMetricsResponse_DailyMetricsEntry_DoNotUse_default_instance_;
class GetNextSpatialBlockRequest;
struct GetNextSpatialBlockRequestDefaultTypeInternal;
extern GetNextSpatialBlockRequestDefaultTypeInternal _GetNextSpatialBlockRequest_default_instance_;
class GetNextSpatialBlockResponse;
struct GetNextSpatialBlockResponseDefaultTypeInternal;
extern GetNextSpatialBlockResponseDefaultTypeInternal _GetNextSpatialBlockResponse_default_instance_;
class Metrics;
struct MetricsDefaultTypeInternal;
extern MetricsDefaultTypeInternal _Metrics_default_instance_;
class Metrics_MetricsEntry_DoNotUse;
struct Metrics_MetricsEntry_DoNotUseDefaultTypeInternal;
extern Metrics_MetricsEntry_DoNotUseDefaultTypeInternal _Metrics_MetricsEntry_DoNotUse_default_instance_;
class OverrideLaserRequest;
struct OverrideLaserRequestDefaultTypeInternal;
extern OverrideLaserRequestDefaultTypeInternal _OverrideLaserRequest_default_instance_;
class PingRequest;
struct PingRequestDefaultTypeInternal;
extern PingRequestDefaultTypeInternal _PingRequest_default_instance_;
class PingResponse;
struct PingResponseDefaultTypeInternal;
extern PingResponseDefaultTypeInternal _PingResponse_default_instance_;
class RegisterSpatialClientRequest;
struct RegisterSpatialClientRequestDefaultTypeInternal;
extern RegisterSpatialClientRequestDefaultTypeInternal _RegisterSpatialClientRequest_default_instance_;
class RegisterSpatialClientResponse;
struct RegisterSpatialClientResponseDefaultTypeInternal;
extern RegisterSpatialClientResponseDefaultTypeInternal _RegisterSpatialClientResponse_default_instance_;
class SetLaserResponse;
struct SetLaserResponseDefaultTypeInternal;
extern SetLaserResponseDefaultTypeInternal _SetLaserResponse_default_instance_;
class SpatialClientAckRequest;
struct SpatialClientAckRequestDefaultTypeInternal;
extern SpatialClientAckRequestDefaultTypeInternal _SpatialClientAckRequest_default_instance_;
class SpatialClientAckResponse;
struct SpatialClientAckResponseDefaultTypeInternal;
extern SpatialClientAckResponseDefaultTypeInternal _SpatialClientAckResponse_default_instance_;
class SpatialClientBeatRequest;
struct SpatialClientBeatRequestDefaultTypeInternal;
extern SpatialClientBeatRequestDefaultTypeInternal _SpatialClientBeatRequest_default_instance_;
class SpatialClientBeatResponse;
struct SpatialClientBeatResponseDefaultTypeInternal;
extern SpatialClientBeatResponseDefaultTypeInternal _SpatialClientBeatResponse_default_instance_;
}  // namespace metrics_aggregator
PROTOBUF_NAMESPACE_OPEN
template<> ::metrics_aggregator::AcknowledgeDailyMetricRequest* Arena::CreateMaybeMessage<::metrics_aggregator::AcknowledgeDailyMetricRequest>(Arena*);
template<> ::metrics_aggregator::AcknowledgeDailyMetricResponse* Arena::CreateMaybeMessage<::metrics_aggregator::AcknowledgeDailyMetricResponse>(Arena*);
template<> ::metrics_aggregator::GetJobMetricsRequest* Arena::CreateMaybeMessage<::metrics_aggregator::GetJobMetricsRequest>(Arena*);
template<> ::metrics_aggregator::GetJobMetricsResponse* Arena::CreateMaybeMessage<::metrics_aggregator::GetJobMetricsResponse>(Arena*);
template<> ::metrics_aggregator::GetLaserChangeTimesRequest* Arena::CreateMaybeMessage<::metrics_aggregator::GetLaserChangeTimesRequest>(Arena*);
template<> ::metrics_aggregator::GetLaserLifeTimesRequest* Arena::CreateMaybeMessage<::metrics_aggregator::GetLaserLifeTimesRequest>(Arena*);
template<> ::metrics_aggregator::GetMetricsRequest* Arena::CreateMaybeMessage<::metrics_aggregator::GetMetricsRequest>(Arena*);
template<> ::metrics_aggregator::GetMetricsResponse* Arena::CreateMaybeMessage<::metrics_aggregator::GetMetricsResponse>(Arena*);
template<> ::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse* Arena::CreateMaybeMessage<::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse>(Arena*);
template<> ::metrics_aggregator::GetNextSpatialBlockRequest* Arena::CreateMaybeMessage<::metrics_aggregator::GetNextSpatialBlockRequest>(Arena*);
template<> ::metrics_aggregator::GetNextSpatialBlockResponse* Arena::CreateMaybeMessage<::metrics_aggregator::GetNextSpatialBlockResponse>(Arena*);
template<> ::metrics_aggregator::Metrics* Arena::CreateMaybeMessage<::metrics_aggregator::Metrics>(Arena*);
template<> ::metrics_aggregator::Metrics_MetricsEntry_DoNotUse* Arena::CreateMaybeMessage<::metrics_aggregator::Metrics_MetricsEntry_DoNotUse>(Arena*);
template<> ::metrics_aggregator::OverrideLaserRequest* Arena::CreateMaybeMessage<::metrics_aggregator::OverrideLaserRequest>(Arena*);
template<> ::metrics_aggregator::PingRequest* Arena::CreateMaybeMessage<::metrics_aggregator::PingRequest>(Arena*);
template<> ::metrics_aggregator::PingResponse* Arena::CreateMaybeMessage<::metrics_aggregator::PingResponse>(Arena*);
template<> ::metrics_aggregator::RegisterSpatialClientRequest* Arena::CreateMaybeMessage<::metrics_aggregator::RegisterSpatialClientRequest>(Arena*);
template<> ::metrics_aggregator::RegisterSpatialClientResponse* Arena::CreateMaybeMessage<::metrics_aggregator::RegisterSpatialClientResponse>(Arena*);
template<> ::metrics_aggregator::SetLaserResponse* Arena::CreateMaybeMessage<::metrics_aggregator::SetLaserResponse>(Arena*);
template<> ::metrics_aggregator::SpatialClientAckRequest* Arena::CreateMaybeMessage<::metrics_aggregator::SpatialClientAckRequest>(Arena*);
template<> ::metrics_aggregator::SpatialClientAckResponse* Arena::CreateMaybeMessage<::metrics_aggregator::SpatialClientAckResponse>(Arena*);
template<> ::metrics_aggregator::SpatialClientBeatRequest* Arena::CreateMaybeMessage<::metrics_aggregator::SpatialClientBeatRequest>(Arena*);
template<> ::metrics_aggregator::SpatialClientBeatResponse* Arena::CreateMaybeMessage<::metrics_aggregator::SpatialClientBeatResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace metrics_aggregator {

// ===================================================================

class PingRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.PingRequest) */ {
 public:
  inline PingRequest() : PingRequest(nullptr) {}
  ~PingRequest() override;
  explicit constexpr PingRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PingRequest(const PingRequest& from);
  PingRequest(PingRequest&& from) noexcept
    : PingRequest() {
    *this = ::std::move(from);
  }

  inline PingRequest& operator=(const PingRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PingRequest& operator=(PingRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PingRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PingRequest* internal_default_instance() {
    return reinterpret_cast<const PingRequest*>(
               &_PingRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(PingRequest& a, PingRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PingRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PingRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PingRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PingRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PingRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PingRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PingRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.PingRequest";
  }
  protected:
  explicit PingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
  };
  // uint32 x = 1;
  void clear_x();
  uint32_t x() const;
  void set_x(uint32_t value);
  private:
  uint32_t _internal_x() const;
  void _internal_set_x(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.PingRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t x_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class PingResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.PingResponse) */ {
 public:
  inline PingResponse() : PingResponse(nullptr) {}
  ~PingResponse() override;
  explicit constexpr PingResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PingResponse(const PingResponse& from);
  PingResponse(PingResponse&& from) noexcept
    : PingResponse() {
    *this = ::std::move(from);
  }

  inline PingResponse& operator=(const PingResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline PingResponse& operator=(PingResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PingResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const PingResponse* internal_default_instance() {
    return reinterpret_cast<const PingResponse*>(
               &_PingResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(PingResponse& a, PingResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(PingResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PingResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PingResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PingResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PingResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PingResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PingResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.PingResponse";
  }
  protected:
  explicit PingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
  };
  // uint32 x = 1;
  void clear_x();
  uint32_t x() const;
  void set_x(uint32_t value);
  private:
  uint32_t _internal_x() const;
  void _internal_set_x(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.PingResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t x_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class Metrics_MetricsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Metrics_MetricsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Metrics_MetricsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  Metrics_MetricsEntry_DoNotUse();
  explicit constexpr Metrics_MetricsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit Metrics_MetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const Metrics_MetricsEntry_DoNotUse& other);
  static const Metrics_MetricsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Metrics_MetricsEntry_DoNotUse*>(&_Metrics_MetricsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "metrics_aggregator.Metrics.MetricsEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "metrics_aggregator.Metrics.MetricsEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class Metrics final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.Metrics) */ {
 public:
  inline Metrics() : Metrics(nullptr) {}
  ~Metrics() override;
  explicit constexpr Metrics(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Metrics(const Metrics& from);
  Metrics(Metrics&& from) noexcept
    : Metrics() {
    *this = ::std::move(from);
  }

  inline Metrics& operator=(const Metrics& from) {
    CopyFrom(from);
    return *this;
  }
  inline Metrics& operator=(Metrics&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Metrics& default_instance() {
    return *internal_default_instance();
  }
  static inline const Metrics* internal_default_instance() {
    return reinterpret_cast<const Metrics*>(
               &_Metrics_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Metrics& a, Metrics& b) {
    a.Swap(&b);
  }
  inline void Swap(Metrics* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Metrics* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Metrics* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Metrics>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Metrics& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Metrics& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Metrics* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.Metrics";
  }
  protected:
  explicit Metrics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kMetricsFieldNumber = 1,
  };
  // map<string, string> metrics = 1;
  int metrics_size() const;
  private:
  int _internal_metrics_size() const;
  public:
  void clear_metrics();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_metrics();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_metrics();

  // @@protoc_insertion_point(class_scope:metrics_aggregator.Metrics)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      Metrics_MetricsEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> metrics_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetMetricsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:metrics_aggregator.GetMetricsRequest) */ {
 public:
  inline GetMetricsRequest() : GetMetricsRequest(nullptr) {}
  explicit constexpr GetMetricsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetMetricsRequest(const GetMetricsRequest& from);
  GetMetricsRequest(GetMetricsRequest&& from) noexcept
    : GetMetricsRequest() {
    *this = ::std::move(from);
  }

  inline GetMetricsRequest& operator=(const GetMetricsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetMetricsRequest& operator=(GetMetricsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetMetricsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetMetricsRequest* internal_default_instance() {
    return reinterpret_cast<const GetMetricsRequest*>(
               &_GetMetricsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetMetricsRequest& a, GetMetricsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetMetricsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetMetricsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetMetricsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetMetricsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetMetricsRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetMetricsRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.GetMetricsRequest";
  }
  protected:
  explicit GetMetricsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:metrics_aggregator.GetMetricsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetMetricsResponse_DailyMetricsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetMetricsResponse_DailyMetricsEntry_DoNotUse, 
    std::string, ::metrics_aggregator::Metrics,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetMetricsResponse_DailyMetricsEntry_DoNotUse, 
    std::string, ::metrics_aggregator::Metrics,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  GetMetricsResponse_DailyMetricsEntry_DoNotUse();
  explicit constexpr GetMetricsResponse_DailyMetricsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetMetricsResponse_DailyMetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetMetricsResponse_DailyMetricsEntry_DoNotUse& other);
  static const GetMetricsResponse_DailyMetricsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetMetricsResponse_DailyMetricsEntry_DoNotUse*>(&_GetMetricsResponse_DailyMetricsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "metrics_aggregator.GetMetricsResponse.DailyMetricsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetMetricsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.GetMetricsResponse) */ {
 public:
  inline GetMetricsResponse() : GetMetricsResponse(nullptr) {}
  ~GetMetricsResponse() override;
  explicit constexpr GetMetricsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetMetricsResponse(const GetMetricsResponse& from);
  GetMetricsResponse(GetMetricsResponse&& from) noexcept
    : GetMetricsResponse() {
    *this = ::std::move(from);
  }

  inline GetMetricsResponse& operator=(const GetMetricsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetMetricsResponse& operator=(GetMetricsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetMetricsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetMetricsResponse* internal_default_instance() {
    return reinterpret_cast<const GetMetricsResponse*>(
               &_GetMetricsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(GetMetricsResponse& a, GetMetricsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetMetricsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetMetricsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetMetricsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetMetricsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetMetricsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetMetricsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetMetricsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.GetMetricsResponse";
  }
  protected:
  explicit GetMetricsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kDailyMetricsFieldNumber = 1,
  };
  // map<string, .metrics_aggregator.Metrics> daily_metrics = 1;
  int daily_metrics_size() const;
  private:
  int _internal_daily_metrics_size() const;
  public:
  void clear_daily_metrics();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >&
      _internal_daily_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >*
      _internal_mutable_daily_metrics();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >&
      daily_metrics() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >*
      mutable_daily_metrics();

  // @@protoc_insertion_point(class_scope:metrics_aggregator.GetMetricsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetMetricsResponse_DailyMetricsEntry_DoNotUse,
      std::string, ::metrics_aggregator::Metrics,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> daily_metrics_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class AcknowledgeDailyMetricRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.AcknowledgeDailyMetricRequest) */ {
 public:
  inline AcknowledgeDailyMetricRequest() : AcknowledgeDailyMetricRequest(nullptr) {}
  ~AcknowledgeDailyMetricRequest() override;
  explicit constexpr AcknowledgeDailyMetricRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AcknowledgeDailyMetricRequest(const AcknowledgeDailyMetricRequest& from);
  AcknowledgeDailyMetricRequest(AcknowledgeDailyMetricRequest&& from) noexcept
    : AcknowledgeDailyMetricRequest() {
    *this = ::std::move(from);
  }

  inline AcknowledgeDailyMetricRequest& operator=(const AcknowledgeDailyMetricRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline AcknowledgeDailyMetricRequest& operator=(AcknowledgeDailyMetricRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AcknowledgeDailyMetricRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const AcknowledgeDailyMetricRequest* internal_default_instance() {
    return reinterpret_cast<const AcknowledgeDailyMetricRequest*>(
               &_AcknowledgeDailyMetricRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(AcknowledgeDailyMetricRequest& a, AcknowledgeDailyMetricRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(AcknowledgeDailyMetricRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AcknowledgeDailyMetricRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AcknowledgeDailyMetricRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AcknowledgeDailyMetricRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AcknowledgeDailyMetricRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AcknowledgeDailyMetricRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AcknowledgeDailyMetricRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.AcknowledgeDailyMetricRequest";
  }
  protected:
  explicit AcknowledgeDailyMetricRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDaysFieldNumber = 1,
  };
  // repeated string days = 1;
  int days_size() const;
  private:
  int _internal_days_size() const;
  public:
  void clear_days();
  const std::string& days(int index) const;
  std::string* mutable_days(int index);
  void set_days(int index, const std::string& value);
  void set_days(int index, std::string&& value);
  void set_days(int index, const char* value);
  void set_days(int index, const char* value, size_t size);
  std::string* add_days();
  void add_days(const std::string& value);
  void add_days(std::string&& value);
  void add_days(const char* value);
  void add_days(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& days() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_days();
  private:
  const std::string& _internal_days(int index) const;
  std::string* _internal_add_days();
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.AcknowledgeDailyMetricRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> days_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class AcknowledgeDailyMetricResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:metrics_aggregator.AcknowledgeDailyMetricResponse) */ {
 public:
  inline AcknowledgeDailyMetricResponse() : AcknowledgeDailyMetricResponse(nullptr) {}
  explicit constexpr AcknowledgeDailyMetricResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AcknowledgeDailyMetricResponse(const AcknowledgeDailyMetricResponse& from);
  AcknowledgeDailyMetricResponse(AcknowledgeDailyMetricResponse&& from) noexcept
    : AcknowledgeDailyMetricResponse() {
    *this = ::std::move(from);
  }

  inline AcknowledgeDailyMetricResponse& operator=(const AcknowledgeDailyMetricResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline AcknowledgeDailyMetricResponse& operator=(AcknowledgeDailyMetricResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AcknowledgeDailyMetricResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const AcknowledgeDailyMetricResponse* internal_default_instance() {
    return reinterpret_cast<const AcknowledgeDailyMetricResponse*>(
               &_AcknowledgeDailyMetricResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(AcknowledgeDailyMetricResponse& a, AcknowledgeDailyMetricResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(AcknowledgeDailyMetricResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AcknowledgeDailyMetricResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AcknowledgeDailyMetricResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AcknowledgeDailyMetricResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const AcknowledgeDailyMetricResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const AcknowledgeDailyMetricResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.AcknowledgeDailyMetricResponse";
  }
  protected:
  explicit AcknowledgeDailyMetricResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:metrics_aggregator.AcknowledgeDailyMetricResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetJobMetricsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:metrics_aggregator.GetJobMetricsRequest) */ {
 public:
  inline GetJobMetricsRequest() : GetJobMetricsRequest(nullptr) {}
  explicit constexpr GetJobMetricsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetJobMetricsRequest(const GetJobMetricsRequest& from);
  GetJobMetricsRequest(GetJobMetricsRequest&& from) noexcept
    : GetJobMetricsRequest() {
    *this = ::std::move(from);
  }

  inline GetJobMetricsRequest& operator=(const GetJobMetricsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetJobMetricsRequest& operator=(GetJobMetricsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetJobMetricsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetJobMetricsRequest* internal_default_instance() {
    return reinterpret_cast<const GetJobMetricsRequest*>(
               &_GetJobMetricsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GetJobMetricsRequest& a, GetJobMetricsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetJobMetricsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetJobMetricsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetJobMetricsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetJobMetricsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetJobMetricsRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetJobMetricsRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.GetJobMetricsRequest";
  }
  protected:
  explicit GetJobMetricsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:metrics_aggregator.GetJobMetricsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetJobMetricsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.GetJobMetricsResponse) */ {
 public:
  inline GetJobMetricsResponse() : GetJobMetricsResponse(nullptr) {}
  ~GetJobMetricsResponse() override;
  explicit constexpr GetJobMetricsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetJobMetricsResponse(const GetJobMetricsResponse& from);
  GetJobMetricsResponse(GetJobMetricsResponse&& from) noexcept
    : GetJobMetricsResponse() {
    *this = ::std::move(from);
  }

  inline GetJobMetricsResponse& operator=(const GetJobMetricsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetJobMetricsResponse& operator=(GetJobMetricsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetJobMetricsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetJobMetricsResponse* internal_default_instance() {
    return reinterpret_cast<const GetJobMetricsResponse*>(
               &_GetJobMetricsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GetJobMetricsResponse& a, GetJobMetricsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetJobMetricsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetJobMetricsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetJobMetricsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetJobMetricsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetJobMetricsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetJobMetricsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetJobMetricsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.GetJobMetricsResponse";
  }
  protected:
  explicit GetJobMetricsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobMetricsFieldNumber = 1,
  };
  // .metrics_aggregator.Metrics jobMetrics = 1;
  bool has_jobmetrics() const;
  private:
  bool _internal_has_jobmetrics() const;
  public:
  void clear_jobmetrics();
  const ::metrics_aggregator::Metrics& jobmetrics() const;
  PROTOBUF_NODISCARD ::metrics_aggregator::Metrics* release_jobmetrics();
  ::metrics_aggregator::Metrics* mutable_jobmetrics();
  void set_allocated_jobmetrics(::metrics_aggregator::Metrics* jobmetrics);
  private:
  const ::metrics_aggregator::Metrics& _internal_jobmetrics() const;
  ::metrics_aggregator::Metrics* _internal_mutable_jobmetrics();
  public:
  void unsafe_arena_set_allocated_jobmetrics(
      ::metrics_aggregator::Metrics* jobmetrics);
  ::metrics_aggregator::Metrics* unsafe_arena_release_jobmetrics();

  // @@protoc_insertion_point(class_scope:metrics_aggregator.GetJobMetricsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::metrics_aggregator::Metrics* jobmetrics_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetLaserLifeTimesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:metrics_aggregator.GetLaserLifeTimesRequest) */ {
 public:
  inline GetLaserLifeTimesRequest() : GetLaserLifeTimesRequest(nullptr) {}
  explicit constexpr GetLaserLifeTimesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetLaserLifeTimesRequest(const GetLaserLifeTimesRequest& from);
  GetLaserLifeTimesRequest(GetLaserLifeTimesRequest&& from) noexcept
    : GetLaserLifeTimesRequest() {
    *this = ::std::move(from);
  }

  inline GetLaserLifeTimesRequest& operator=(const GetLaserLifeTimesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetLaserLifeTimesRequest& operator=(GetLaserLifeTimesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetLaserLifeTimesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetLaserLifeTimesRequest* internal_default_instance() {
    return reinterpret_cast<const GetLaserLifeTimesRequest*>(
               &_GetLaserLifeTimesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(GetLaserLifeTimesRequest& a, GetLaserLifeTimesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetLaserLifeTimesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetLaserLifeTimesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetLaserLifeTimesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetLaserLifeTimesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetLaserLifeTimesRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetLaserLifeTimesRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.GetLaserLifeTimesRequest";
  }
  protected:
  explicit GetLaserLifeTimesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:metrics_aggregator.GetLaserLifeTimesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SetLaserResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:metrics_aggregator.SetLaserResponse) */ {
 public:
  inline SetLaserResponse() : SetLaserResponse(nullptr) {}
  explicit constexpr SetLaserResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetLaserResponse(const SetLaserResponse& from);
  SetLaserResponse(SetLaserResponse&& from) noexcept
    : SetLaserResponse() {
    *this = ::std::move(from);
  }

  inline SetLaserResponse& operator=(const SetLaserResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetLaserResponse& operator=(SetLaserResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetLaserResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetLaserResponse* internal_default_instance() {
    return reinterpret_cast<const SetLaserResponse*>(
               &_SetLaserResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(SetLaserResponse& a, SetLaserResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetLaserResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetLaserResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetLaserResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetLaserResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetLaserResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetLaserResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.SetLaserResponse";
  }
  protected:
  explicit SetLaserResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:metrics_aggregator.SetLaserResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetLaserChangeTimesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:metrics_aggregator.GetLaserChangeTimesRequest) */ {
 public:
  inline GetLaserChangeTimesRequest() : GetLaserChangeTimesRequest(nullptr) {}
  explicit constexpr GetLaserChangeTimesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetLaserChangeTimesRequest(const GetLaserChangeTimesRequest& from);
  GetLaserChangeTimesRequest(GetLaserChangeTimesRequest&& from) noexcept
    : GetLaserChangeTimesRequest() {
    *this = ::std::move(from);
  }

  inline GetLaserChangeTimesRequest& operator=(const GetLaserChangeTimesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetLaserChangeTimesRequest& operator=(GetLaserChangeTimesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetLaserChangeTimesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetLaserChangeTimesRequest* internal_default_instance() {
    return reinterpret_cast<const GetLaserChangeTimesRequest*>(
               &_GetLaserChangeTimesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(GetLaserChangeTimesRequest& a, GetLaserChangeTimesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetLaserChangeTimesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetLaserChangeTimesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetLaserChangeTimesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetLaserChangeTimesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetLaserChangeTimesRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetLaserChangeTimesRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.GetLaserChangeTimesRequest";
  }
  protected:
  explicit GetLaserChangeTimesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:metrics_aggregator.GetLaserChangeTimesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RegisterSpatialClientRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:metrics_aggregator.RegisterSpatialClientRequest) */ {
 public:
  inline RegisterSpatialClientRequest() : RegisterSpatialClientRequest(nullptr) {}
  explicit constexpr RegisterSpatialClientRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegisterSpatialClientRequest(const RegisterSpatialClientRequest& from);
  RegisterSpatialClientRequest(RegisterSpatialClientRequest&& from) noexcept
    : RegisterSpatialClientRequest() {
    *this = ::std::move(from);
  }

  inline RegisterSpatialClientRequest& operator=(const RegisterSpatialClientRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterSpatialClientRequest& operator=(RegisterSpatialClientRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegisterSpatialClientRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegisterSpatialClientRequest* internal_default_instance() {
    return reinterpret_cast<const RegisterSpatialClientRequest*>(
               &_RegisterSpatialClientRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(RegisterSpatialClientRequest& a, RegisterSpatialClientRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterSpatialClientRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisterSpatialClientRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegisterSpatialClientRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegisterSpatialClientRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const RegisterSpatialClientRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const RegisterSpatialClientRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.RegisterSpatialClientRequest";
  }
  protected:
  explicit RegisterSpatialClientRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:metrics_aggregator.RegisterSpatialClientRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RegisterSpatialClientResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.RegisterSpatialClientResponse) */ {
 public:
  inline RegisterSpatialClientResponse() : RegisterSpatialClientResponse(nullptr) {}
  ~RegisterSpatialClientResponse() override;
  explicit constexpr RegisterSpatialClientResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegisterSpatialClientResponse(const RegisterSpatialClientResponse& from);
  RegisterSpatialClientResponse(RegisterSpatialClientResponse&& from) noexcept
    : RegisterSpatialClientResponse() {
    *this = ::std::move(from);
  }

  inline RegisterSpatialClientResponse& operator=(const RegisterSpatialClientResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterSpatialClientResponse& operator=(RegisterSpatialClientResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegisterSpatialClientResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegisterSpatialClientResponse* internal_default_instance() {
    return reinterpret_cast<const RegisterSpatialClientResponse*>(
               &_RegisterSpatialClientResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(RegisterSpatialClientResponse& a, RegisterSpatialClientResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterSpatialClientResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisterSpatialClientResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegisterSpatialClientResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegisterSpatialClientResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RegisterSpatialClientResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RegisterSpatialClientResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterSpatialClientResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.RegisterSpatialClientResponse";
  }
  protected:
  explicit RegisterSpatialClientResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // uint32 id = 1;
  void clear_id();
  uint32_t id() const;
  void set_id(uint32_t value);
  private:
  uint32_t _internal_id() const;
  void _internal_set_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.RegisterSpatialClientResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SpatialClientBeatRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.SpatialClientBeatRequest) */ {
 public:
  inline SpatialClientBeatRequest() : SpatialClientBeatRequest(nullptr) {}
  ~SpatialClientBeatRequest() override;
  explicit constexpr SpatialClientBeatRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SpatialClientBeatRequest(const SpatialClientBeatRequest& from);
  SpatialClientBeatRequest(SpatialClientBeatRequest&& from) noexcept
    : SpatialClientBeatRequest() {
    *this = ::std::move(from);
  }

  inline SpatialClientBeatRequest& operator=(const SpatialClientBeatRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SpatialClientBeatRequest& operator=(SpatialClientBeatRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SpatialClientBeatRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SpatialClientBeatRequest* internal_default_instance() {
    return reinterpret_cast<const SpatialClientBeatRequest*>(
               &_SpatialClientBeatRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(SpatialClientBeatRequest& a, SpatialClientBeatRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SpatialClientBeatRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SpatialClientBeatRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SpatialClientBeatRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SpatialClientBeatRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SpatialClientBeatRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SpatialClientBeatRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SpatialClientBeatRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.SpatialClientBeatRequest";
  }
  protected:
  explicit SpatialClientBeatRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // uint32 id = 1;
  void clear_id();
  uint32_t id() const;
  void set_id(uint32_t value);
  private:
  uint32_t _internal_id() const;
  void _internal_set_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.SpatialClientBeatRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SpatialClientBeatResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.SpatialClientBeatResponse) */ {
 public:
  inline SpatialClientBeatResponse() : SpatialClientBeatResponse(nullptr) {}
  ~SpatialClientBeatResponse() override;
  explicit constexpr SpatialClientBeatResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SpatialClientBeatResponse(const SpatialClientBeatResponse& from);
  SpatialClientBeatResponse(SpatialClientBeatResponse&& from) noexcept
    : SpatialClientBeatResponse() {
    *this = ::std::move(from);
  }

  inline SpatialClientBeatResponse& operator=(const SpatialClientBeatResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SpatialClientBeatResponse& operator=(SpatialClientBeatResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SpatialClientBeatResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SpatialClientBeatResponse* internal_default_instance() {
    return reinterpret_cast<const SpatialClientBeatResponse*>(
               &_SpatialClientBeatResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(SpatialClientBeatResponse& a, SpatialClientBeatResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SpatialClientBeatResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SpatialClientBeatResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SpatialClientBeatResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SpatialClientBeatResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SpatialClientBeatResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SpatialClientBeatResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SpatialClientBeatResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.SpatialClientBeatResponse";
  }
  protected:
  explicit SpatialClientBeatResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSuccessFieldNumber = 1,
  };
  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.SpatialClientBeatResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool success_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SpatialClientAckRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.SpatialClientAckRequest) */ {
 public:
  inline SpatialClientAckRequest() : SpatialClientAckRequest(nullptr) {}
  ~SpatialClientAckRequest() override;
  explicit constexpr SpatialClientAckRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SpatialClientAckRequest(const SpatialClientAckRequest& from);
  SpatialClientAckRequest(SpatialClientAckRequest&& from) noexcept
    : SpatialClientAckRequest() {
    *this = ::std::move(from);
  }

  inline SpatialClientAckRequest& operator=(const SpatialClientAckRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SpatialClientAckRequest& operator=(SpatialClientAckRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SpatialClientAckRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SpatialClientAckRequest* internal_default_instance() {
    return reinterpret_cast<const SpatialClientAckRequest*>(
               &_SpatialClientAckRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(SpatialClientAckRequest& a, SpatialClientAckRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SpatialClientAckRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SpatialClientAckRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SpatialClientAckRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SpatialClientAckRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SpatialClientAckRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SpatialClientAckRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SpatialClientAckRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.SpatialClientAckRequest";
  }
  protected:
  explicit SpatialClientAckRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBlockIdFieldNumber = 2,
    kClientIdFieldNumber = 1,
  };
  // uint64 block_id = 2;
  void clear_block_id();
  uint64_t block_id() const;
  void set_block_id(uint64_t value);
  private:
  uint64_t _internal_block_id() const;
  void _internal_set_block_id(uint64_t value);
  public:

  // uint32 client_id = 1;
  void clear_client_id();
  uint32_t client_id() const;
  void set_client_id(uint32_t value);
  private:
  uint32_t _internal_client_id() const;
  void _internal_set_client_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.SpatialClientAckRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t block_id_;
  uint32_t client_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SpatialClientAckResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.SpatialClientAckResponse) */ {
 public:
  inline SpatialClientAckResponse() : SpatialClientAckResponse(nullptr) {}
  ~SpatialClientAckResponse() override;
  explicit constexpr SpatialClientAckResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SpatialClientAckResponse(const SpatialClientAckResponse& from);
  SpatialClientAckResponse(SpatialClientAckResponse&& from) noexcept
    : SpatialClientAckResponse() {
    *this = ::std::move(from);
  }

  inline SpatialClientAckResponse& operator=(const SpatialClientAckResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SpatialClientAckResponse& operator=(SpatialClientAckResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SpatialClientAckResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SpatialClientAckResponse* internal_default_instance() {
    return reinterpret_cast<const SpatialClientAckResponse*>(
               &_SpatialClientAckResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(SpatialClientAckResponse& a, SpatialClientAckResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SpatialClientAckResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SpatialClientAckResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SpatialClientAckResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SpatialClientAckResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SpatialClientAckResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SpatialClientAckResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SpatialClientAckResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.SpatialClientAckResponse";
  }
  protected:
  explicit SpatialClientAckResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSuccessFieldNumber = 1,
  };
  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.SpatialClientAckResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool success_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetNextSpatialBlockRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.GetNextSpatialBlockRequest) */ {
 public:
  inline GetNextSpatialBlockRequest() : GetNextSpatialBlockRequest(nullptr) {}
  ~GetNextSpatialBlockRequest() override;
  explicit constexpr GetNextSpatialBlockRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextSpatialBlockRequest(const GetNextSpatialBlockRequest& from);
  GetNextSpatialBlockRequest(GetNextSpatialBlockRequest&& from) noexcept
    : GetNextSpatialBlockRequest() {
    *this = ::std::move(from);
  }

  inline GetNextSpatialBlockRequest& operator=(const GetNextSpatialBlockRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextSpatialBlockRequest& operator=(GetNextSpatialBlockRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextSpatialBlockRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextSpatialBlockRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextSpatialBlockRequest*>(
               &_GetNextSpatialBlockRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(GetNextSpatialBlockRequest& a, GetNextSpatialBlockRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextSpatialBlockRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextSpatialBlockRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextSpatialBlockRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextSpatialBlockRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextSpatialBlockRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextSpatialBlockRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextSpatialBlockRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.GetNextSpatialBlockRequest";
  }
  protected:
  explicit GetNextSpatialBlockRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBlockIdFieldNumber = 1,
  };
  // uint64 block_id = 1;
  void clear_block_id();
  uint64_t block_id() const;
  void set_block_id(uint64_t value);
  private:
  uint64_t _internal_block_id() const;
  void _internal_set_block_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.GetNextSpatialBlockRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t block_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetNextSpatialBlockResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.GetNextSpatialBlockResponse) */ {
 public:
  inline GetNextSpatialBlockResponse() : GetNextSpatialBlockResponse(nullptr) {}
  ~GetNextSpatialBlockResponse() override;
  explicit constexpr GetNextSpatialBlockResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextSpatialBlockResponse(const GetNextSpatialBlockResponse& from);
  GetNextSpatialBlockResponse(GetNextSpatialBlockResponse&& from) noexcept
    : GetNextSpatialBlockResponse() {
    *this = ::std::move(from);
  }

  inline GetNextSpatialBlockResponse& operator=(const GetNextSpatialBlockResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextSpatialBlockResponse& operator=(GetNextSpatialBlockResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextSpatialBlockResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextSpatialBlockResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextSpatialBlockResponse*>(
               &_GetNextSpatialBlockResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(GetNextSpatialBlockResponse& a, GetNextSpatialBlockResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextSpatialBlockResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextSpatialBlockResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextSpatialBlockResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextSpatialBlockResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextSpatialBlockResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextSpatialBlockResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextSpatialBlockResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.GetNextSpatialBlockResponse";
  }
  protected:
  explicit GetNextSpatialBlockResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBlockIdFieldNumber = 1,
  };
  // uint64 block_id = 1;
  void clear_block_id();
  uint64_t block_id() const;
  void set_block_id(uint64_t value);
  private:
  uint64_t _internal_block_id() const;
  void _internal_set_block_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.GetNextSpatialBlockResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t block_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// -------------------------------------------------------------------

class OverrideLaserRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:metrics_aggregator.OverrideLaserRequest) */ {
 public:
  inline OverrideLaserRequest() : OverrideLaserRequest(nullptr) {}
  ~OverrideLaserRequest() override;
  explicit constexpr OverrideLaserRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OverrideLaserRequest(const OverrideLaserRequest& from);
  OverrideLaserRequest(OverrideLaserRequest&& from) noexcept
    : OverrideLaserRequest() {
    *this = ::std::move(from);
  }

  inline OverrideLaserRequest& operator=(const OverrideLaserRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline OverrideLaserRequest& operator=(OverrideLaserRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OverrideLaserRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const OverrideLaserRequest* internal_default_instance() {
    return reinterpret_cast<const OverrideLaserRequest*>(
               &_OverrideLaserRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(OverrideLaserRequest& a, OverrideLaserRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(OverrideLaserRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OverrideLaserRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OverrideLaserRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OverrideLaserRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OverrideLaserRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OverrideLaserRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OverrideLaserRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "metrics_aggregator.OverrideLaserRequest";
  }
  protected:
  explicit OverrideLaserRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLaserFieldNumber = 1,
    kLifetimeSFieldNumber = 2,
  };
  // .carbon.metrics.LaserIdentifier laser = 1;
  bool has_laser() const;
  private:
  bool _internal_has_laser() const;
  public:
  void clear_laser();
  const ::carbon::metrics::LaserIdentifier& laser() const;
  PROTOBUF_NODISCARD ::carbon::metrics::LaserIdentifier* release_laser();
  ::carbon::metrics::LaserIdentifier* mutable_laser();
  void set_allocated_laser(::carbon::metrics::LaserIdentifier* laser);
  private:
  const ::carbon::metrics::LaserIdentifier& _internal_laser() const;
  ::carbon::metrics::LaserIdentifier* _internal_mutable_laser();
  public:
  void unsafe_arena_set_allocated_laser(
      ::carbon::metrics::LaserIdentifier* laser);
  ::carbon::metrics::LaserIdentifier* unsafe_arena_release_laser();

  // uint64 lifetime_s = 2;
  void clear_lifetime_s();
  uint64_t lifetime_s() const;
  void set_lifetime_s(uint64_t value);
  private:
  uint64_t _internal_lifetime_s() const;
  void _internal_set_lifetime_s(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:metrics_aggregator.OverrideLaserRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::metrics::LaserIdentifier* laser_;
  uint64_t lifetime_s_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PingRequest

// uint32 x = 1;
inline void PingRequest::clear_x() {
  x_ = 0u;
}
inline uint32_t PingRequest::_internal_x() const {
  return x_;
}
inline uint32_t PingRequest::x() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.PingRequest.x)
  return _internal_x();
}
inline void PingRequest::_internal_set_x(uint32_t value) {
  
  x_ = value;
}
inline void PingRequest::set_x(uint32_t value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.PingRequest.x)
}

// -------------------------------------------------------------------

// PingResponse

// uint32 x = 1;
inline void PingResponse::clear_x() {
  x_ = 0u;
}
inline uint32_t PingResponse::_internal_x() const {
  return x_;
}
inline uint32_t PingResponse::x() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.PingResponse.x)
  return _internal_x();
}
inline void PingResponse::_internal_set_x(uint32_t value) {
  
  x_ = value;
}
inline void PingResponse::set_x(uint32_t value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.PingResponse.x)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Metrics

// map<string, string> metrics = 1;
inline int Metrics::_internal_metrics_size() const {
  return metrics_.size();
}
inline int Metrics::metrics_size() const {
  return _internal_metrics_size();
}
inline void Metrics::clear_metrics() {
  metrics_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
Metrics::_internal_metrics() const {
  return metrics_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
Metrics::metrics() const {
  // @@protoc_insertion_point(field_map:metrics_aggregator.Metrics.metrics)
  return _internal_metrics();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
Metrics::_internal_mutable_metrics() {
  return metrics_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
Metrics::mutable_metrics() {
  // @@protoc_insertion_point(field_mutable_map:metrics_aggregator.Metrics.metrics)
  return _internal_mutable_metrics();
}

// -------------------------------------------------------------------

// GetMetricsRequest

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetMetricsResponse

// map<string, .metrics_aggregator.Metrics> daily_metrics = 1;
inline int GetMetricsResponse::_internal_daily_metrics_size() const {
  return daily_metrics_.size();
}
inline int GetMetricsResponse::daily_metrics_size() const {
  return _internal_daily_metrics_size();
}
inline void GetMetricsResponse::clear_daily_metrics() {
  daily_metrics_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >&
GetMetricsResponse::_internal_daily_metrics() const {
  return daily_metrics_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >&
GetMetricsResponse::daily_metrics() const {
  // @@protoc_insertion_point(field_map:metrics_aggregator.GetMetricsResponse.daily_metrics)
  return _internal_daily_metrics();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >*
GetMetricsResponse::_internal_mutable_daily_metrics() {
  return daily_metrics_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >*
GetMetricsResponse::mutable_daily_metrics() {
  // @@protoc_insertion_point(field_mutable_map:metrics_aggregator.GetMetricsResponse.daily_metrics)
  return _internal_mutable_daily_metrics();
}

// -------------------------------------------------------------------

// AcknowledgeDailyMetricRequest

// repeated string days = 1;
inline int AcknowledgeDailyMetricRequest::_internal_days_size() const {
  return days_.size();
}
inline int AcknowledgeDailyMetricRequest::days_size() const {
  return _internal_days_size();
}
inline void AcknowledgeDailyMetricRequest::clear_days() {
  days_.Clear();
}
inline std::string* AcknowledgeDailyMetricRequest::add_days() {
  std::string* _s = _internal_add_days();
  // @@protoc_insertion_point(field_add_mutable:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
  return _s;
}
inline const std::string& AcknowledgeDailyMetricRequest::_internal_days(int index) const {
  return days_.Get(index);
}
inline const std::string& AcknowledgeDailyMetricRequest::days(int index) const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
  return _internal_days(index);
}
inline std::string* AcknowledgeDailyMetricRequest::mutable_days(int index) {
  // @@protoc_insertion_point(field_mutable:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
  return days_.Mutable(index);
}
inline void AcknowledgeDailyMetricRequest::set_days(int index, const std::string& value) {
  days_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
}
inline void AcknowledgeDailyMetricRequest::set_days(int index, std::string&& value) {
  days_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
}
inline void AcknowledgeDailyMetricRequest::set_days(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  days_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
}
inline void AcknowledgeDailyMetricRequest::set_days(int index, const char* value, size_t size) {
  days_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
}
inline std::string* AcknowledgeDailyMetricRequest::_internal_add_days() {
  return days_.Add();
}
inline void AcknowledgeDailyMetricRequest::add_days(const std::string& value) {
  days_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
}
inline void AcknowledgeDailyMetricRequest::add_days(std::string&& value) {
  days_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
}
inline void AcknowledgeDailyMetricRequest::add_days(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  days_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
}
inline void AcknowledgeDailyMetricRequest::add_days(const char* value, size_t size) {
  days_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
AcknowledgeDailyMetricRequest::days() const {
  // @@protoc_insertion_point(field_list:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
  return days_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
AcknowledgeDailyMetricRequest::mutable_days() {
  // @@protoc_insertion_point(field_mutable_list:metrics_aggregator.AcknowledgeDailyMetricRequest.days)
  return &days_;
}

// -------------------------------------------------------------------

// AcknowledgeDailyMetricResponse

// -------------------------------------------------------------------

// GetJobMetricsRequest

// -------------------------------------------------------------------

// GetJobMetricsResponse

// .metrics_aggregator.Metrics jobMetrics = 1;
inline bool GetJobMetricsResponse::_internal_has_jobmetrics() const {
  return this != internal_default_instance() && jobmetrics_ != nullptr;
}
inline bool GetJobMetricsResponse::has_jobmetrics() const {
  return _internal_has_jobmetrics();
}
inline void GetJobMetricsResponse::clear_jobmetrics() {
  if (GetArenaForAllocation() == nullptr && jobmetrics_ != nullptr) {
    delete jobmetrics_;
  }
  jobmetrics_ = nullptr;
}
inline const ::metrics_aggregator::Metrics& GetJobMetricsResponse::_internal_jobmetrics() const {
  const ::metrics_aggregator::Metrics* p = jobmetrics_;
  return p != nullptr ? *p : reinterpret_cast<const ::metrics_aggregator::Metrics&>(
      ::metrics_aggregator::_Metrics_default_instance_);
}
inline const ::metrics_aggregator::Metrics& GetJobMetricsResponse::jobmetrics() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.GetJobMetricsResponse.jobMetrics)
  return _internal_jobmetrics();
}
inline void GetJobMetricsResponse::unsafe_arena_set_allocated_jobmetrics(
    ::metrics_aggregator::Metrics* jobmetrics) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(jobmetrics_);
  }
  jobmetrics_ = jobmetrics;
  if (jobmetrics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metrics_aggregator.GetJobMetricsResponse.jobMetrics)
}
inline ::metrics_aggregator::Metrics* GetJobMetricsResponse::release_jobmetrics() {
  
  ::metrics_aggregator::Metrics* temp = jobmetrics_;
  jobmetrics_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::metrics_aggregator::Metrics* GetJobMetricsResponse::unsafe_arena_release_jobmetrics() {
  // @@protoc_insertion_point(field_release:metrics_aggregator.GetJobMetricsResponse.jobMetrics)
  
  ::metrics_aggregator::Metrics* temp = jobmetrics_;
  jobmetrics_ = nullptr;
  return temp;
}
inline ::metrics_aggregator::Metrics* GetJobMetricsResponse::_internal_mutable_jobmetrics() {
  
  if (jobmetrics_ == nullptr) {
    auto* p = CreateMaybeMessage<::metrics_aggregator::Metrics>(GetArenaForAllocation());
    jobmetrics_ = p;
  }
  return jobmetrics_;
}
inline ::metrics_aggregator::Metrics* GetJobMetricsResponse::mutable_jobmetrics() {
  ::metrics_aggregator::Metrics* _msg = _internal_mutable_jobmetrics();
  // @@protoc_insertion_point(field_mutable:metrics_aggregator.GetJobMetricsResponse.jobMetrics)
  return _msg;
}
inline void GetJobMetricsResponse::set_allocated_jobmetrics(::metrics_aggregator::Metrics* jobmetrics) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete jobmetrics_;
  }
  if (jobmetrics) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::metrics_aggregator::Metrics>::GetOwningArena(jobmetrics);
    if (message_arena != submessage_arena) {
      jobmetrics = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, jobmetrics, submessage_arena);
    }
    
  } else {
    
  }
  jobmetrics_ = jobmetrics;
  // @@protoc_insertion_point(field_set_allocated:metrics_aggregator.GetJobMetricsResponse.jobMetrics)
}

// -------------------------------------------------------------------

// GetLaserLifeTimesRequest

// -------------------------------------------------------------------

// SetLaserResponse

// -------------------------------------------------------------------

// GetLaserChangeTimesRequest

// -------------------------------------------------------------------

// RegisterSpatialClientRequest

// -------------------------------------------------------------------

// RegisterSpatialClientResponse

// uint32 id = 1;
inline void RegisterSpatialClientResponse::clear_id() {
  id_ = 0u;
}
inline uint32_t RegisterSpatialClientResponse::_internal_id() const {
  return id_;
}
inline uint32_t RegisterSpatialClientResponse::id() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.RegisterSpatialClientResponse.id)
  return _internal_id();
}
inline void RegisterSpatialClientResponse::_internal_set_id(uint32_t value) {
  
  id_ = value;
}
inline void RegisterSpatialClientResponse::set_id(uint32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.RegisterSpatialClientResponse.id)
}

// -------------------------------------------------------------------

// SpatialClientBeatRequest

// uint32 id = 1;
inline void SpatialClientBeatRequest::clear_id() {
  id_ = 0u;
}
inline uint32_t SpatialClientBeatRequest::_internal_id() const {
  return id_;
}
inline uint32_t SpatialClientBeatRequest::id() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.SpatialClientBeatRequest.id)
  return _internal_id();
}
inline void SpatialClientBeatRequest::_internal_set_id(uint32_t value) {
  
  id_ = value;
}
inline void SpatialClientBeatRequest::set_id(uint32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.SpatialClientBeatRequest.id)
}

// -------------------------------------------------------------------

// SpatialClientBeatResponse

// bool success = 1;
inline void SpatialClientBeatResponse::clear_success() {
  success_ = false;
}
inline bool SpatialClientBeatResponse::_internal_success() const {
  return success_;
}
inline bool SpatialClientBeatResponse::success() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.SpatialClientBeatResponse.success)
  return _internal_success();
}
inline void SpatialClientBeatResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void SpatialClientBeatResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.SpatialClientBeatResponse.success)
}

// -------------------------------------------------------------------

// SpatialClientAckRequest

// uint32 client_id = 1;
inline void SpatialClientAckRequest::clear_client_id() {
  client_id_ = 0u;
}
inline uint32_t SpatialClientAckRequest::_internal_client_id() const {
  return client_id_;
}
inline uint32_t SpatialClientAckRequest::client_id() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.SpatialClientAckRequest.client_id)
  return _internal_client_id();
}
inline void SpatialClientAckRequest::_internal_set_client_id(uint32_t value) {
  
  client_id_ = value;
}
inline void SpatialClientAckRequest::set_client_id(uint32_t value) {
  _internal_set_client_id(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.SpatialClientAckRequest.client_id)
}

// uint64 block_id = 2;
inline void SpatialClientAckRequest::clear_block_id() {
  block_id_ = uint64_t{0u};
}
inline uint64_t SpatialClientAckRequest::_internal_block_id() const {
  return block_id_;
}
inline uint64_t SpatialClientAckRequest::block_id() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.SpatialClientAckRequest.block_id)
  return _internal_block_id();
}
inline void SpatialClientAckRequest::_internal_set_block_id(uint64_t value) {
  
  block_id_ = value;
}
inline void SpatialClientAckRequest::set_block_id(uint64_t value) {
  _internal_set_block_id(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.SpatialClientAckRequest.block_id)
}

// -------------------------------------------------------------------

// SpatialClientAckResponse

// bool success = 1;
inline void SpatialClientAckResponse::clear_success() {
  success_ = false;
}
inline bool SpatialClientAckResponse::_internal_success() const {
  return success_;
}
inline bool SpatialClientAckResponse::success() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.SpatialClientAckResponse.success)
  return _internal_success();
}
inline void SpatialClientAckResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void SpatialClientAckResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.SpatialClientAckResponse.success)
}

// -------------------------------------------------------------------

// GetNextSpatialBlockRequest

// uint64 block_id = 1;
inline void GetNextSpatialBlockRequest::clear_block_id() {
  block_id_ = uint64_t{0u};
}
inline uint64_t GetNextSpatialBlockRequest::_internal_block_id() const {
  return block_id_;
}
inline uint64_t GetNextSpatialBlockRequest::block_id() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.GetNextSpatialBlockRequest.block_id)
  return _internal_block_id();
}
inline void GetNextSpatialBlockRequest::_internal_set_block_id(uint64_t value) {
  
  block_id_ = value;
}
inline void GetNextSpatialBlockRequest::set_block_id(uint64_t value) {
  _internal_set_block_id(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.GetNextSpatialBlockRequest.block_id)
}

// -------------------------------------------------------------------

// GetNextSpatialBlockResponse

// uint64 block_id = 1;
inline void GetNextSpatialBlockResponse::clear_block_id() {
  block_id_ = uint64_t{0u};
}
inline uint64_t GetNextSpatialBlockResponse::_internal_block_id() const {
  return block_id_;
}
inline uint64_t GetNextSpatialBlockResponse::block_id() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.GetNextSpatialBlockResponse.block_id)
  return _internal_block_id();
}
inline void GetNextSpatialBlockResponse::_internal_set_block_id(uint64_t value) {
  
  block_id_ = value;
}
inline void GetNextSpatialBlockResponse::set_block_id(uint64_t value) {
  _internal_set_block_id(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.GetNextSpatialBlockResponse.block_id)
}

// -------------------------------------------------------------------

// OverrideLaserRequest

// .carbon.metrics.LaserIdentifier laser = 1;
inline bool OverrideLaserRequest::_internal_has_laser() const {
  return this != internal_default_instance() && laser_ != nullptr;
}
inline bool OverrideLaserRequest::has_laser() const {
  return _internal_has_laser();
}
inline const ::carbon::metrics::LaserIdentifier& OverrideLaserRequest::_internal_laser() const {
  const ::carbon::metrics::LaserIdentifier* p = laser_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::LaserIdentifier&>(
      ::carbon::metrics::_LaserIdentifier_default_instance_);
}
inline const ::carbon::metrics::LaserIdentifier& OverrideLaserRequest::laser() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.OverrideLaserRequest.laser)
  return _internal_laser();
}
inline void OverrideLaserRequest::unsafe_arena_set_allocated_laser(
    ::carbon::metrics::LaserIdentifier* laser) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_);
  }
  laser_ = laser;
  if (laser) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:metrics_aggregator.OverrideLaserRequest.laser)
}
inline ::carbon::metrics::LaserIdentifier* OverrideLaserRequest::release_laser() {
  
  ::carbon::metrics::LaserIdentifier* temp = laser_;
  laser_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::LaserIdentifier* OverrideLaserRequest::unsafe_arena_release_laser() {
  // @@protoc_insertion_point(field_release:metrics_aggregator.OverrideLaserRequest.laser)
  
  ::carbon::metrics::LaserIdentifier* temp = laser_;
  laser_ = nullptr;
  return temp;
}
inline ::carbon::metrics::LaserIdentifier* OverrideLaserRequest::_internal_mutable_laser() {
  
  if (laser_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::LaserIdentifier>(GetArenaForAllocation());
    laser_ = p;
  }
  return laser_;
}
inline ::carbon::metrics::LaserIdentifier* OverrideLaserRequest::mutable_laser() {
  ::carbon::metrics::LaserIdentifier* _msg = _internal_mutable_laser();
  // @@protoc_insertion_point(field_mutable:metrics_aggregator.OverrideLaserRequest.laser)
  return _msg;
}
inline void OverrideLaserRequest::set_allocated_laser(::carbon::metrics::LaserIdentifier* laser) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_);
  }
  if (laser) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser));
    if (message_arena != submessage_arena) {
      laser = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, laser, submessage_arena);
    }
    
  } else {
    
  }
  laser_ = laser;
  // @@protoc_insertion_point(field_set_allocated:metrics_aggregator.OverrideLaserRequest.laser)
}

// uint64 lifetime_s = 2;
inline void OverrideLaserRequest::clear_lifetime_s() {
  lifetime_s_ = uint64_t{0u};
}
inline uint64_t OverrideLaserRequest::_internal_lifetime_s() const {
  return lifetime_s_;
}
inline uint64_t OverrideLaserRequest::lifetime_s() const {
  // @@protoc_insertion_point(field_get:metrics_aggregator.OverrideLaserRequest.lifetime_s)
  return _internal_lifetime_s();
}
inline void OverrideLaserRequest::_internal_set_lifetime_s(uint64_t value) {
  
  lifetime_s_ = value;
}
inline void OverrideLaserRequest::set_lifetime_s(uint64_t value) {
  _internal_set_lifetime_s(value);
  // @@protoc_insertion_point(field_set:metrics_aggregator.OverrideLaserRequest.lifetime_s)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace metrics_aggregator

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto

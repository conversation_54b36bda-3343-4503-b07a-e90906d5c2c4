// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: metrics/proto/metrics_aggregator_service.proto

#include "metrics/proto/metrics_aggregator_service.pb.h"
#include "metrics/proto/metrics_aggregator_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace metrics_aggregator {

static const char* MetricsAggregatorService_method_names[] = {
  "/metrics_aggregator.MetricsAggregatorService/Ping",
  "/metrics_aggregator.MetricsAggregatorService/GetMetrics",
  "/metrics_aggregator.MetricsAggregatorService/AcknowledgeDailyMetric",
  "/metrics_aggregator.MetricsAggregatorService/GetJobMetrics",
  "/metrics_aggregator.MetricsAggregatorService/GetLaserLifeTimes",
  "/metrics_aggregator.MetricsAggregatorService/SetLaser",
  "/metrics_aggregator.MetricsAggregatorService/OverrideLaser",
  "/metrics_aggregator.MetricsAggregatorService/GetLaserChangeTimes",
  "/metrics_aggregator.MetricsAggregatorService/RegisterSpatialClient",
  "/metrics_aggregator.MetricsAggregatorService/SpatialClientBeat",
  "/metrics_aggregator.MetricsAggregatorService/SpatialClientAck",
  "/metrics_aggregator.MetricsAggregatorService/GetNextSpatialBlock",
};

std::unique_ptr< MetricsAggregatorService::Stub> MetricsAggregatorService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< MetricsAggregatorService::Stub> stub(new MetricsAggregatorService::Stub(channel, options));
  return stub;
}

MetricsAggregatorService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Ping_(MetricsAggregatorService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetMetrics_(MetricsAggregatorService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_AcknowledgeDailyMetric_(MetricsAggregatorService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetJobMetrics_(MetricsAggregatorService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetLaserLifeTimes_(MetricsAggregatorService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetLaser_(MetricsAggregatorService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_OverrideLaser_(MetricsAggregatorService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetLaserChangeTimes_(MetricsAggregatorService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_RegisterSpatialClient_(MetricsAggregatorService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SpatialClientBeat_(MetricsAggregatorService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SpatialClientAck_(MetricsAggregatorService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextSpatialBlock_(MetricsAggregatorService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status MetricsAggregatorService::Stub::Ping(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::metrics_aggregator::PingResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::PingRequest, ::metrics_aggregator::PingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Ping_, context, request, response);
}

void MetricsAggregatorService::Stub::async::Ping(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest* request, ::metrics_aggregator::PingResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::PingRequest, ::metrics_aggregator::PingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::Ping(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest* request, ::metrics_aggregator::PingResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::PingResponse>* MetricsAggregatorService::Stub::PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::PingResponse, ::metrics_aggregator::PingRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Ping_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::PingResponse>* MetricsAggregatorService::Stub::AsyncPingRaw(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::GetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::metrics_aggregator::GetMetricsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::GetMetricsRequest, ::metrics_aggregator::GetMetricsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetMetrics_, context, request, response);
}

void MetricsAggregatorService::Stub::async::GetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest* request, ::metrics_aggregator::GetMetricsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::GetMetricsRequest, ::metrics_aggregator::GetMetricsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetMetrics_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::GetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest* request, ::metrics_aggregator::GetMetricsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetMetrics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetMetricsResponse>* MetricsAggregatorService::Stub::PrepareAsyncGetMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::GetMetricsResponse, ::metrics_aggregator::GetMetricsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetMetrics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetMetricsResponse>* MetricsAggregatorService::Stub::AsyncGetMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetMetricsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::AcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::AcknowledgeDailyMetricRequest, ::metrics_aggregator::AcknowledgeDailyMetricResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_AcknowledgeDailyMetric_, context, request, response);
}

void MetricsAggregatorService::Stub::async::AcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::AcknowledgeDailyMetricRequest, ::metrics_aggregator::AcknowledgeDailyMetricResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AcknowledgeDailyMetric_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::AcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AcknowledgeDailyMetric_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::AcknowledgeDailyMetricResponse>* MetricsAggregatorService::Stub::PrepareAsyncAcknowledgeDailyMetricRaw(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::AcknowledgeDailyMetricResponse, ::metrics_aggregator::AcknowledgeDailyMetricRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_AcknowledgeDailyMetric_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::AcknowledgeDailyMetricResponse>* MetricsAggregatorService::Stub::AsyncAcknowledgeDailyMetricRaw(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncAcknowledgeDailyMetricRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::GetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::metrics_aggregator::GetJobMetricsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::GetJobMetricsRequest, ::metrics_aggregator::GetJobMetricsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetJobMetrics_, context, request, response);
}

void MetricsAggregatorService::Stub::async::GetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest* request, ::metrics_aggregator::GetJobMetricsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::GetJobMetricsRequest, ::metrics_aggregator::GetJobMetricsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetJobMetrics_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::GetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest* request, ::metrics_aggregator::GetJobMetricsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetJobMetrics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetJobMetricsResponse>* MetricsAggregatorService::Stub::PrepareAsyncGetJobMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::GetJobMetricsResponse, ::metrics_aggregator::GetJobMetricsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetJobMetrics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetJobMetricsResponse>* MetricsAggregatorService::Stub::AsyncGetJobMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetJobMetricsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::GetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::carbon::metrics::LaserLifeTimes* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::GetLaserLifeTimesRequest, ::carbon::metrics::LaserLifeTimes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetLaserLifeTimes_, context, request, response);
}

void MetricsAggregatorService::Stub::async::GetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::carbon::metrics::LaserLifeTimes* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::GetLaserLifeTimesRequest, ::carbon::metrics::LaserLifeTimes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLaserLifeTimes_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::GetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::carbon::metrics::LaserLifeTimes* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLaserLifeTimes_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserLifeTimes>* MetricsAggregatorService::Stub::PrepareAsyncGetLaserLifeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::metrics::LaserLifeTimes, ::metrics_aggregator::GetLaserLifeTimesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetLaserLifeTimes_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserLifeTimes>* MetricsAggregatorService::Stub::AsyncGetLaserLifeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetLaserLifeTimesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::SetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::metrics_aggregator::SetLaserResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::metrics::LaserIdentifier, ::metrics_aggregator::SetLaserResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetLaser_, context, request, response);
}

void MetricsAggregatorService::Stub::async::SetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier* request, ::metrics_aggregator::SetLaserResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::metrics::LaserIdentifier, ::metrics_aggregator::SetLaserResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLaser_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::SetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier* request, ::metrics_aggregator::SetLaserResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLaser_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>* MetricsAggregatorService::Stub::PrepareAsyncSetLaserRaw(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::SetLaserResponse, ::carbon::metrics::LaserIdentifier, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetLaser_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>* MetricsAggregatorService::Stub::AsyncSetLaserRaw(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetLaserRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::OverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::metrics_aggregator::SetLaserResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::OverrideLaserRequest, ::metrics_aggregator::SetLaserResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_OverrideLaser_, context, request, response);
}

void MetricsAggregatorService::Stub::async::OverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest* request, ::metrics_aggregator::SetLaserResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::OverrideLaserRequest, ::metrics_aggregator::SetLaserResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_OverrideLaser_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::OverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest* request, ::metrics_aggregator::SetLaserResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_OverrideLaser_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>* MetricsAggregatorService::Stub::PrepareAsyncOverrideLaserRaw(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::SetLaserResponse, ::metrics_aggregator::OverrideLaserRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_OverrideLaser_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>* MetricsAggregatorService::Stub::AsyncOverrideLaserRaw(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncOverrideLaserRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::GetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::carbon::metrics::LaserChangeTimes* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::GetLaserChangeTimesRequest, ::carbon::metrics::LaserChangeTimes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetLaserChangeTimes_, context, request, response);
}

void MetricsAggregatorService::Stub::async::GetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::carbon::metrics::LaserChangeTimes* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::GetLaserChangeTimesRequest, ::carbon::metrics::LaserChangeTimes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLaserChangeTimes_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::GetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::carbon::metrics::LaserChangeTimes* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLaserChangeTimes_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserChangeTimes>* MetricsAggregatorService::Stub::PrepareAsyncGetLaserChangeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::metrics::LaserChangeTimes, ::metrics_aggregator::GetLaserChangeTimesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetLaserChangeTimes_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserChangeTimes>* MetricsAggregatorService::Stub::AsyncGetLaserChangeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetLaserChangeTimesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::RegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::metrics_aggregator::RegisterSpatialClientResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::RegisterSpatialClientRequest, ::metrics_aggregator::RegisterSpatialClientResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_RegisterSpatialClient_, context, request, response);
}

void MetricsAggregatorService::Stub::async::RegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest* request, ::metrics_aggregator::RegisterSpatialClientResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::RegisterSpatialClientRequest, ::metrics_aggregator::RegisterSpatialClientResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RegisterSpatialClient_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::RegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest* request, ::metrics_aggregator::RegisterSpatialClientResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RegisterSpatialClient_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::RegisterSpatialClientResponse>* MetricsAggregatorService::Stub::PrepareAsyncRegisterSpatialClientRaw(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::RegisterSpatialClientResponse, ::metrics_aggregator::RegisterSpatialClientRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_RegisterSpatialClient_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::RegisterSpatialClientResponse>* MetricsAggregatorService::Stub::AsyncRegisterSpatialClientRaw(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncRegisterSpatialClientRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::SpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::metrics_aggregator::SpatialClientBeatResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::SpatialClientBeatRequest, ::metrics_aggregator::SpatialClientBeatResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SpatialClientBeat_, context, request, response);
}

void MetricsAggregatorService::Stub::async::SpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest* request, ::metrics_aggregator::SpatialClientBeatResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::SpatialClientBeatRequest, ::metrics_aggregator::SpatialClientBeatResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SpatialClientBeat_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::SpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest* request, ::metrics_aggregator::SpatialClientBeatResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SpatialClientBeat_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientBeatResponse>* MetricsAggregatorService::Stub::PrepareAsyncSpatialClientBeatRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::SpatialClientBeatResponse, ::metrics_aggregator::SpatialClientBeatRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SpatialClientBeat_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientBeatResponse>* MetricsAggregatorService::Stub::AsyncSpatialClientBeatRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSpatialClientBeatRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::SpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::metrics_aggregator::SpatialClientAckResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::SpatialClientAckRequest, ::metrics_aggregator::SpatialClientAckResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SpatialClientAck_, context, request, response);
}

void MetricsAggregatorService::Stub::async::SpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest* request, ::metrics_aggregator::SpatialClientAckResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::SpatialClientAckRequest, ::metrics_aggregator::SpatialClientAckResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SpatialClientAck_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::SpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest* request, ::metrics_aggregator::SpatialClientAckResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SpatialClientAck_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientAckResponse>* MetricsAggregatorService::Stub::PrepareAsyncSpatialClientAckRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::SpatialClientAckResponse, ::metrics_aggregator::SpatialClientAckRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SpatialClientAck_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientAckResponse>* MetricsAggregatorService::Stub::AsyncSpatialClientAckRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSpatialClientAckRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MetricsAggregatorService::Stub::GetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::metrics_aggregator::GetNextSpatialBlockResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::metrics_aggregator::GetNextSpatialBlockRequest, ::metrics_aggregator::GetNextSpatialBlockResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextSpatialBlock_, context, request, response);
}

void MetricsAggregatorService::Stub::async::GetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::metrics_aggregator::GetNextSpatialBlockResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::metrics_aggregator::GetNextSpatialBlockRequest, ::metrics_aggregator::GetNextSpatialBlockResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextSpatialBlock_, context, request, response, std::move(f));
}

void MetricsAggregatorService::Stub::async::GetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::metrics_aggregator::GetNextSpatialBlockResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextSpatialBlock_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetNextSpatialBlockResponse>* MetricsAggregatorService::Stub::PrepareAsyncGetNextSpatialBlockRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::metrics_aggregator::GetNextSpatialBlockResponse, ::metrics_aggregator::GetNextSpatialBlockRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextSpatialBlock_, context, request);
}

::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetNextSpatialBlockResponse>* MetricsAggregatorService::Stub::AsyncGetNextSpatialBlockRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextSpatialBlockRaw(context, request, cq);
  result->StartCall();
  return result;
}

MetricsAggregatorService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::PingRequest, ::metrics_aggregator::PingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::PingRequest* req,
             ::metrics_aggregator::PingResponse* resp) {
               return service->Ping(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::GetMetricsRequest, ::metrics_aggregator::GetMetricsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::GetMetricsRequest* req,
             ::metrics_aggregator::GetMetricsResponse* resp) {
               return service->GetMetrics(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::AcknowledgeDailyMetricRequest, ::metrics_aggregator::AcknowledgeDailyMetricResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::AcknowledgeDailyMetricRequest* req,
             ::metrics_aggregator::AcknowledgeDailyMetricResponse* resp) {
               return service->AcknowledgeDailyMetric(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::GetJobMetricsRequest, ::metrics_aggregator::GetJobMetricsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::GetJobMetricsRequest* req,
             ::metrics_aggregator::GetJobMetricsResponse* resp) {
               return service->GetJobMetrics(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::GetLaserLifeTimesRequest, ::carbon::metrics::LaserLifeTimes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::GetLaserLifeTimesRequest* req,
             ::carbon::metrics::LaserLifeTimes* resp) {
               return service->GetLaserLifeTimes(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::carbon::metrics::LaserIdentifier, ::metrics_aggregator::SetLaserResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::metrics::LaserIdentifier* req,
             ::metrics_aggregator::SetLaserResponse* resp) {
               return service->SetLaser(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::OverrideLaserRequest, ::metrics_aggregator::SetLaserResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::OverrideLaserRequest* req,
             ::metrics_aggregator::SetLaserResponse* resp) {
               return service->OverrideLaser(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::GetLaserChangeTimesRequest, ::carbon::metrics::LaserChangeTimes, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::GetLaserChangeTimesRequest* req,
             ::carbon::metrics::LaserChangeTimes* resp) {
               return service->GetLaserChangeTimes(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::RegisterSpatialClientRequest, ::metrics_aggregator::RegisterSpatialClientResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::RegisterSpatialClientRequest* req,
             ::metrics_aggregator::RegisterSpatialClientResponse* resp) {
               return service->RegisterSpatialClient(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::SpatialClientBeatRequest, ::metrics_aggregator::SpatialClientBeatResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::SpatialClientBeatRequest* req,
             ::metrics_aggregator::SpatialClientBeatResponse* resp) {
               return service->SpatialClientBeat(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::SpatialClientAckRequest, ::metrics_aggregator::SpatialClientAckResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::SpatialClientAckRequest* req,
             ::metrics_aggregator::SpatialClientAckResponse* resp) {
               return service->SpatialClientAck(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsAggregatorService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsAggregatorService::Service, ::metrics_aggregator::GetNextSpatialBlockRequest, ::metrics_aggregator::GetNextSpatialBlockResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MetricsAggregatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::metrics_aggregator::GetNextSpatialBlockRequest* req,
             ::metrics_aggregator::GetNextSpatialBlockResponse* resp) {
               return service->GetNextSpatialBlock(ctx, req, resp);
             }, this)));
}

MetricsAggregatorService::Service::~Service() {
}

::grpc::Status MetricsAggregatorService::Service::Ping(::grpc::ServerContext* context, const ::metrics_aggregator::PingRequest* request, ::metrics_aggregator::PingResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::GetMetrics(::grpc::ServerContext* context, const ::metrics_aggregator::GetMetricsRequest* request, ::metrics_aggregator::GetMetricsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::AcknowledgeDailyMetric(::grpc::ServerContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::GetJobMetrics(::grpc::ServerContext* context, const ::metrics_aggregator::GetJobMetricsRequest* request, ::metrics_aggregator::GetJobMetricsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::GetLaserLifeTimes(::grpc::ServerContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::carbon::metrics::LaserLifeTimes* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::SetLaser(::grpc::ServerContext* context, const ::carbon::metrics::LaserIdentifier* request, ::metrics_aggregator::SetLaserResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::OverrideLaser(::grpc::ServerContext* context, const ::metrics_aggregator::OverrideLaserRequest* request, ::metrics_aggregator::SetLaserResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::GetLaserChangeTimes(::grpc::ServerContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::carbon::metrics::LaserChangeTimes* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::RegisterSpatialClient(::grpc::ServerContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest* request, ::metrics_aggregator::RegisterSpatialClientResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::SpatialClientBeat(::grpc::ServerContext* context, const ::metrics_aggregator::SpatialClientBeatRequest* request, ::metrics_aggregator::SpatialClientBeatResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::SpatialClientAck(::grpc::ServerContext* context, const ::metrics_aggregator::SpatialClientAckRequest* request, ::metrics_aggregator::SpatialClientAckResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsAggregatorService::Service::GetNextSpatialBlock(::grpc::ServerContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::metrics_aggregator::GetNextSpatialBlockResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace metrics_aggregator


"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.metrics.metrics_pb2 import (
    LaserIdentifier as proto___metrics___metrics_pb2___LaserIdentifier,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class PingRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PingRequest = PingRequest

class PingResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PingResponse = PingResponse

class Metrics(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class MetricsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___MetricsEntry = MetricsEntry


    @property
    def metrics(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    def __init__(self,
        *,
        metrics : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"metrics",b"metrics"]) -> None: ...
type___Metrics = Metrics

class GetMetricsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetMetricsRequest = GetMetricsRequest

class GetMetricsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class DailyMetricsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...

        @property
        def value(self) -> type___Metrics: ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[type___Metrics] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___DailyMetricsEntry = DailyMetricsEntry


    @property
    def daily_metrics(self) -> google___protobuf___internal___containers___MessageMap[typing___Text, type___Metrics]: ...

    def __init__(self,
        *,
        daily_metrics : typing___Optional[typing___Mapping[typing___Text, type___Metrics]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"daily_metrics",b"daily_metrics"]) -> None: ...
type___GetMetricsResponse = GetMetricsResponse

class AcknowledgeDailyMetricRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    days: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        days : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"days",b"days"]) -> None: ...
type___AcknowledgeDailyMetricRequest = AcknowledgeDailyMetricRequest

class AcknowledgeDailyMetricResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___AcknowledgeDailyMetricResponse = AcknowledgeDailyMetricResponse

class GetJobMetricsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetJobMetricsRequest = GetJobMetricsRequest

class GetJobMetricsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def jobMetrics(self) -> type___Metrics: ...

    def __init__(self,
        *,
        jobMetrics : typing___Optional[type___Metrics] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"jobMetrics",b"jobMetrics"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobMetrics",b"jobMetrics"]) -> None: ...
type___GetJobMetricsResponse = GetJobMetricsResponse

class GetLaserLifeTimesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetLaserLifeTimesRequest = GetLaserLifeTimesRequest

class SetLaserResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetLaserResponse = SetLaserResponse

class GetLaserChangeTimesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetLaserChangeTimesRequest = GetLaserChangeTimesRequest

class RegisterSpatialClientRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___RegisterSpatialClientRequest = RegisterSpatialClientRequest

class RegisterSpatialClientResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___RegisterSpatialClientResponse = RegisterSpatialClientResponse

class SpatialClientBeatRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___SpatialClientBeatRequest = SpatialClientBeatRequest

class SpatialClientBeatResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SpatialClientBeatResponse = SpatialClientBeatResponse

class SpatialClientAckRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    client_id: builtin___int = ...
    block_id: builtin___int = ...

    def __init__(self,
        *,
        client_id : typing___Optional[builtin___int] = None,
        block_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"block_id",b"block_id",u"client_id",b"client_id"]) -> None: ...
type___SpatialClientAckRequest = SpatialClientAckRequest

class SpatialClientAckResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SpatialClientAckResponse = SpatialClientAckResponse

class GetNextSpatialBlockRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    block_id: builtin___int = ...

    def __init__(self,
        *,
        block_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"block_id",b"block_id"]) -> None: ...
type___GetNextSpatialBlockRequest = GetNextSpatialBlockRequest

class GetNextSpatialBlockResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    block_id: builtin___int = ...

    def __init__(self,
        *,
        block_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"block_id",b"block_id"]) -> None: ...
type___GetNextSpatialBlockResponse = GetNextSpatialBlockResponse

class OverrideLaserRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lifetime_s: builtin___int = ...

    @property
    def laser(self) -> proto___metrics___metrics_pb2___LaserIdentifier: ...

    def __init__(self,
        *,
        laser : typing___Optional[proto___metrics___metrics_pb2___LaserIdentifier] = None,
        lifetime_s : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"laser",b"laser"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"laser",b"laser",u"lifetime_s",b"lifetime_s"]) -> None: ...
type___OverrideLaserRequest = OverrideLaserRequest

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: metrics/proto/metrics_aggregator_service.proto

#include "metrics/proto/metrics_aggregator_service.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace metrics_aggregator {
constexpr PingRequest::PingRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0u){}
struct PingRequestDefaultTypeInternal {
  constexpr PingRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PingRequestDefaultTypeInternal() {}
  union {
    PingRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PingRequestDefaultTypeInternal _PingRequest_default_instance_;
constexpr PingResponse::PingResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0u){}
struct PingResponseDefaultTypeInternal {
  constexpr PingResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PingResponseDefaultTypeInternal() {}
  union {
    PingResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PingResponseDefaultTypeInternal _PingResponse_default_instance_;
constexpr Metrics_MetricsEntry_DoNotUse::Metrics_MetricsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct Metrics_MetricsEntry_DoNotUseDefaultTypeInternal {
  constexpr Metrics_MetricsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~Metrics_MetricsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    Metrics_MetricsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT Metrics_MetricsEntry_DoNotUseDefaultTypeInternal _Metrics_MetricsEntry_DoNotUse_default_instance_;
constexpr Metrics::Metrics(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : metrics_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct MetricsDefaultTypeInternal {
  constexpr MetricsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MetricsDefaultTypeInternal() {}
  union {
    Metrics _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MetricsDefaultTypeInternal _Metrics_default_instance_;
constexpr GetMetricsRequest::GetMetricsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetMetricsRequestDefaultTypeInternal {
  constexpr GetMetricsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetMetricsRequestDefaultTypeInternal() {}
  union {
    GetMetricsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetMetricsRequestDefaultTypeInternal _GetMetricsRequest_default_instance_;
constexpr GetMetricsResponse_DailyMetricsEntry_DoNotUse::GetMetricsResponse_DailyMetricsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetMetricsResponse_DailyMetricsEntry_DoNotUseDefaultTypeInternal {
  constexpr GetMetricsResponse_DailyMetricsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetMetricsResponse_DailyMetricsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetMetricsResponse_DailyMetricsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetMetricsResponse_DailyMetricsEntry_DoNotUseDefaultTypeInternal _GetMetricsResponse_DailyMetricsEntry_DoNotUse_default_instance_;
constexpr GetMetricsResponse::GetMetricsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : daily_metrics_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct GetMetricsResponseDefaultTypeInternal {
  constexpr GetMetricsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetMetricsResponseDefaultTypeInternal() {}
  union {
    GetMetricsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetMetricsResponseDefaultTypeInternal _GetMetricsResponse_default_instance_;
constexpr AcknowledgeDailyMetricRequest::AcknowledgeDailyMetricRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : days_(){}
struct AcknowledgeDailyMetricRequestDefaultTypeInternal {
  constexpr AcknowledgeDailyMetricRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AcknowledgeDailyMetricRequestDefaultTypeInternal() {}
  union {
    AcknowledgeDailyMetricRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AcknowledgeDailyMetricRequestDefaultTypeInternal _AcknowledgeDailyMetricRequest_default_instance_;
constexpr AcknowledgeDailyMetricResponse::AcknowledgeDailyMetricResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct AcknowledgeDailyMetricResponseDefaultTypeInternal {
  constexpr AcknowledgeDailyMetricResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AcknowledgeDailyMetricResponseDefaultTypeInternal() {}
  union {
    AcknowledgeDailyMetricResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AcknowledgeDailyMetricResponseDefaultTypeInternal _AcknowledgeDailyMetricResponse_default_instance_;
constexpr GetJobMetricsRequest::GetJobMetricsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetJobMetricsRequestDefaultTypeInternal {
  constexpr GetJobMetricsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetJobMetricsRequestDefaultTypeInternal() {}
  union {
    GetJobMetricsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetJobMetricsRequestDefaultTypeInternal _GetJobMetricsRequest_default_instance_;
constexpr GetJobMetricsResponse::GetJobMetricsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobmetrics_(nullptr){}
struct GetJobMetricsResponseDefaultTypeInternal {
  constexpr GetJobMetricsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetJobMetricsResponseDefaultTypeInternal() {}
  union {
    GetJobMetricsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetJobMetricsResponseDefaultTypeInternal _GetJobMetricsResponse_default_instance_;
constexpr GetLaserLifeTimesRequest::GetLaserLifeTimesRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetLaserLifeTimesRequestDefaultTypeInternal {
  constexpr GetLaserLifeTimesRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetLaserLifeTimesRequestDefaultTypeInternal() {}
  union {
    GetLaserLifeTimesRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetLaserLifeTimesRequestDefaultTypeInternal _GetLaserLifeTimesRequest_default_instance_;
constexpr SetLaserResponse::SetLaserResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetLaserResponseDefaultTypeInternal {
  constexpr SetLaserResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetLaserResponseDefaultTypeInternal() {}
  union {
    SetLaserResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetLaserResponseDefaultTypeInternal _SetLaserResponse_default_instance_;
constexpr GetLaserChangeTimesRequest::GetLaserChangeTimesRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetLaserChangeTimesRequestDefaultTypeInternal {
  constexpr GetLaserChangeTimesRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetLaserChangeTimesRequestDefaultTypeInternal() {}
  union {
    GetLaserChangeTimesRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetLaserChangeTimesRequestDefaultTypeInternal _GetLaserChangeTimesRequest_default_instance_;
constexpr RegisterSpatialClientRequest::RegisterSpatialClientRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct RegisterSpatialClientRequestDefaultTypeInternal {
  constexpr RegisterSpatialClientRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RegisterSpatialClientRequestDefaultTypeInternal() {}
  union {
    RegisterSpatialClientRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RegisterSpatialClientRequestDefaultTypeInternal _RegisterSpatialClientRequest_default_instance_;
constexpr RegisterSpatialClientResponse::RegisterSpatialClientResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(0u){}
struct RegisterSpatialClientResponseDefaultTypeInternal {
  constexpr RegisterSpatialClientResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RegisterSpatialClientResponseDefaultTypeInternal() {}
  union {
    RegisterSpatialClientResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RegisterSpatialClientResponseDefaultTypeInternal _RegisterSpatialClientResponse_default_instance_;
constexpr SpatialClientBeatRequest::SpatialClientBeatRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(0u){}
struct SpatialClientBeatRequestDefaultTypeInternal {
  constexpr SpatialClientBeatRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SpatialClientBeatRequestDefaultTypeInternal() {}
  union {
    SpatialClientBeatRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SpatialClientBeatRequestDefaultTypeInternal _SpatialClientBeatRequest_default_instance_;
constexpr SpatialClientBeatResponse::SpatialClientBeatResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : success_(false){}
struct SpatialClientBeatResponseDefaultTypeInternal {
  constexpr SpatialClientBeatResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SpatialClientBeatResponseDefaultTypeInternal() {}
  union {
    SpatialClientBeatResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SpatialClientBeatResponseDefaultTypeInternal _SpatialClientBeatResponse_default_instance_;
constexpr SpatialClientAckRequest::SpatialClientAckRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : block_id_(uint64_t{0u})
  , client_id_(0u){}
struct SpatialClientAckRequestDefaultTypeInternal {
  constexpr SpatialClientAckRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SpatialClientAckRequestDefaultTypeInternal() {}
  union {
    SpatialClientAckRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SpatialClientAckRequestDefaultTypeInternal _SpatialClientAckRequest_default_instance_;
constexpr SpatialClientAckResponse::SpatialClientAckResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : success_(false){}
struct SpatialClientAckResponseDefaultTypeInternal {
  constexpr SpatialClientAckResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SpatialClientAckResponseDefaultTypeInternal() {}
  union {
    SpatialClientAckResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SpatialClientAckResponseDefaultTypeInternal _SpatialClientAckResponse_default_instance_;
constexpr GetNextSpatialBlockRequest::GetNextSpatialBlockRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : block_id_(uint64_t{0u}){}
struct GetNextSpatialBlockRequestDefaultTypeInternal {
  constexpr GetNextSpatialBlockRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextSpatialBlockRequestDefaultTypeInternal() {}
  union {
    GetNextSpatialBlockRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextSpatialBlockRequestDefaultTypeInternal _GetNextSpatialBlockRequest_default_instance_;
constexpr GetNextSpatialBlockResponse::GetNextSpatialBlockResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : block_id_(uint64_t{0u}){}
struct GetNextSpatialBlockResponseDefaultTypeInternal {
  constexpr GetNextSpatialBlockResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextSpatialBlockResponseDefaultTypeInternal() {}
  union {
    GetNextSpatialBlockResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextSpatialBlockResponseDefaultTypeInternal _GetNextSpatialBlockResponse_default_instance_;
constexpr OverrideLaserRequest::OverrideLaserRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : laser_(nullptr)
  , lifetime_s_(uint64_t{0u}){}
struct OverrideLaserRequestDefaultTypeInternal {
  constexpr OverrideLaserRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~OverrideLaserRequestDefaultTypeInternal() {}
  union {
    OverrideLaserRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT OverrideLaserRequestDefaultTypeInternal _OverrideLaserRequest_default_instance_;
}  // namespace metrics_aggregator
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[23];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto = nullptr;

const uint32_t TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::PingRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::PingRequest, x_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::PingResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::PingResponse, x_),
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::Metrics_MetricsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::Metrics_MetricsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::Metrics_MetricsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::Metrics_MetricsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::Metrics, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::Metrics, metrics_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetMetricsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetMetricsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetMetricsResponse, daily_metrics_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::AcknowledgeDailyMetricRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::AcknowledgeDailyMetricRequest, days_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::AcknowledgeDailyMetricResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetJobMetricsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetJobMetricsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetJobMetricsResponse, jobmetrics_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetLaserLifeTimesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SetLaserResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetLaserChangeTimesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::RegisterSpatialClientRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::RegisterSpatialClientResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::RegisterSpatialClientResponse, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SpatialClientBeatRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SpatialClientBeatRequest, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SpatialClientBeatResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SpatialClientBeatResponse, success_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SpatialClientAckRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SpatialClientAckRequest, client_id_),
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SpatialClientAckRequest, block_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SpatialClientAckResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::SpatialClientAckResponse, success_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetNextSpatialBlockRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetNextSpatialBlockRequest, block_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetNextSpatialBlockResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::GetNextSpatialBlockResponse, block_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::OverrideLaserRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::OverrideLaserRequest, laser_),
  PROTOBUF_FIELD_OFFSET(::metrics_aggregator::OverrideLaserRequest, lifetime_s_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::metrics_aggregator::PingRequest)},
  { 7, -1, -1, sizeof(::metrics_aggregator::PingResponse)},
  { 14, 22, -1, sizeof(::metrics_aggregator::Metrics_MetricsEntry_DoNotUse)},
  { 24, -1, -1, sizeof(::metrics_aggregator::Metrics)},
  { 31, -1, -1, sizeof(::metrics_aggregator::GetMetricsRequest)},
  { 37, 45, -1, sizeof(::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse)},
  { 47, -1, -1, sizeof(::metrics_aggregator::GetMetricsResponse)},
  { 54, -1, -1, sizeof(::metrics_aggregator::AcknowledgeDailyMetricRequest)},
  { 61, -1, -1, sizeof(::metrics_aggregator::AcknowledgeDailyMetricResponse)},
  { 67, -1, -1, sizeof(::metrics_aggregator::GetJobMetricsRequest)},
  { 73, -1, -1, sizeof(::metrics_aggregator::GetJobMetricsResponse)},
  { 80, -1, -1, sizeof(::metrics_aggregator::GetLaserLifeTimesRequest)},
  { 86, -1, -1, sizeof(::metrics_aggregator::SetLaserResponse)},
  { 92, -1, -1, sizeof(::metrics_aggregator::GetLaserChangeTimesRequest)},
  { 98, -1, -1, sizeof(::metrics_aggregator::RegisterSpatialClientRequest)},
  { 104, -1, -1, sizeof(::metrics_aggregator::RegisterSpatialClientResponse)},
  { 111, -1, -1, sizeof(::metrics_aggregator::SpatialClientBeatRequest)},
  { 118, -1, -1, sizeof(::metrics_aggregator::SpatialClientBeatResponse)},
  { 125, -1, -1, sizeof(::metrics_aggregator::SpatialClientAckRequest)},
  { 133, -1, -1, sizeof(::metrics_aggregator::SpatialClientAckResponse)},
  { 140, -1, -1, sizeof(::metrics_aggregator::GetNextSpatialBlockRequest)},
  { 147, -1, -1, sizeof(::metrics_aggregator::GetNextSpatialBlockResponse)},
  { 154, -1, -1, sizeof(::metrics_aggregator::OverrideLaserRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_PingRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_PingResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_Metrics_MetricsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_Metrics_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_GetMetricsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_GetMetricsResponse_DailyMetricsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_GetMetricsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_AcknowledgeDailyMetricRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_AcknowledgeDailyMetricResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_GetJobMetricsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_GetJobMetricsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_GetLaserLifeTimesRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_SetLaserResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_GetLaserChangeTimesRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_RegisterSpatialClientRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_RegisterSpatialClientResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_SpatialClientBeatRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_SpatialClientBeatResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_SpatialClientAckRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_SpatialClientAckResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_GetNextSpatialBlockRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_GetNextSpatialBlockResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::metrics_aggregator::_OverrideLaserRequest_default_instance_),
};

const char descriptor_table_protodef_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n.metrics/proto/metrics_aggregator_servi"
  "ce.proto\022\022metrics_aggregator\032\033proto/metr"
  "ics/metrics.proto\"\030\n\013PingRequest\022\t\n\001x\030\001 "
  "\001(\r\"\031\n\014PingResponse\022\t\n\001x\030\001 \001(\r\"t\n\007Metric"
  "s\0229\n\007metrics\030\001 \003(\0132(.metrics_aggregator."
  "Metrics.MetricsEntry\032.\n\014MetricsEntry\022\013\n\003"
  "key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\023\n\021GetMetri"
  "csRequest\"\267\001\n\022GetMetricsResponse\022O\n\rdail"
  "y_metrics\030\001 \003(\01328.metrics_aggregator.Get"
  "MetricsResponse.DailyMetricsEntry\032P\n\021Dai"
  "lyMetricsEntry\022\013\n\003key\030\001 \001(\t\022*\n\005value\030\002 \001"
  "(\0132\033.metrics_aggregator.Metrics:\0028\001\"-\n\035A"
  "cknowledgeDailyMetricRequest\022\014\n\004days\030\001 \003"
  "(\t\" \n\036AcknowledgeDailyMetricResponse\"\026\n\024"
  "GetJobMetricsRequest\"H\n\025GetJobMetricsRes"
  "ponse\022/\n\njobMetrics\030\001 \001(\0132\033.metrics_aggr"
  "egator.Metrics\"\032\n\030GetLaserLifeTimesReque"
  "st\"\022\n\020SetLaserResponse\"\034\n\032GetLaserChange"
  "TimesRequest\"\036\n\034RegisterSpatialClientReq"
  "uest\"+\n\035RegisterSpatialClientResponse\022\n\n"
  "\002id\030\001 \001(\r\"&\n\030SpatialClientBeatRequest\022\n\n"
  "\002id\030\001 \001(\r\",\n\031SpatialClientBeatResponse\022\017"
  "\n\007success\030\001 \001(\010\">\n\027SpatialClientAckReque"
  "st\022\021\n\tclient_id\030\001 \001(\r\022\020\n\010block_id\030\002 \001(\004\""
  "+\n\030SpatialClientAckResponse\022\017\n\007success\030\001"
  " \001(\010\".\n\032GetNextSpatialBlockRequest\022\020\n\010bl"
  "ock_id\030\001 \001(\004\"/\n\033GetNextSpatialBlockRespo"
  "nse\022\020\n\010block_id\030\001 \001(\004\"Z\n\024OverrideLaserRe"
  "quest\022.\n\005laser\030\001 \001(\0132\037.carbon.metrics.La"
  "serIdentifier\022\022\n\nlifetime_s\030\002 \001(\0042\227\n\n\030Me"
  "tricsAggregatorService\022K\n\004Ping\022\037.metrics"
  "_aggregator.PingRequest\032 .metrics_aggreg"
  "ator.PingResponse\"\000\022]\n\nGetMetrics\022%.metr"
  "ics_aggregator.GetMetricsRequest\032&.metri"
  "cs_aggregator.GetMetricsResponse\"\000\022\201\001\n\026A"
  "cknowledgeDailyMetric\0221.metrics_aggregat"
  "or.AcknowledgeDailyMetricRequest\0322.metri"
  "cs_aggregator.AcknowledgeDailyMetricResp"
  "onse\"\000\022d\n\rGetJobMetrics\022(.metrics_aggreg"
  "ator.GetJobMetricsRequest\032).metrics_aggr"
  "egator.GetJobMetricsResponse\022c\n\021GetLaser"
  "LifeTimes\022,.metrics_aggregator.GetLaserL"
  "ifeTimesRequest\032\036.carbon.metrics.LaserLi"
  "feTimes\"\000\022S\n\010SetLaser\022\037.carbon.metrics.L"
  "aserIdentifier\032$.metrics_aggregator.SetL"
  "aserResponse\"\000\022a\n\rOverrideLaser\022(.metric"
  "s_aggregator.OverrideLaserRequest\032$.metr"
  "ics_aggregator.SetLaserResponse\"\000\022i\n\023Get"
  "LaserChangeTimes\022..metrics_aggregator.Ge"
  "tLaserChangeTimesRequest\032 .carbon.metric"
  "s.LaserChangeTimes\"\000\022~\n\025RegisterSpatialC"
  "lient\0220.metrics_aggregator.RegisterSpati"
  "alClientRequest\0321.metrics_aggregator.Reg"
  "isterSpatialClientResponse\"\000\022r\n\021SpatialC"
  "lientBeat\022,.metrics_aggregator.SpatialCl"
  "ientBeatRequest\032-.metrics_aggregator.Spa"
  "tialClientBeatResponse\"\000\022o\n\020SpatialClien"
  "tAck\022+.metrics_aggregator.SpatialClientA"
  "ckRequest\032,.metrics_aggregator.SpatialCl"
  "ientAckResponse\"\000\022x\n\023GetNextSpatialBlock"
  "\022..metrics_aggregator.GetNextSpatialBloc"
  "kRequest\032/.metrics_aggregator.GetNextSpa"
  "tialBlockResponse\"\000B\032Z\030proto/metrics_agg"
  "regatorb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_deps[1] = {
  &::descriptor_table_proto_2fmetrics_2fmetrics_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto = {
  false, false, 2535, descriptor_table_protodef_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto, "metrics/proto/metrics_aggregator_service.proto", 
  &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once, descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_deps, 1, 23,
  schemas, file_default_instances, TableStruct_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto::offsets,
  file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto, file_level_enum_descriptors_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto, file_level_service_descriptors_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter() {
  return &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto(&descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto);
namespace metrics_aggregator {

// ===================================================================

class PingRequest::_Internal {
 public:
};

PingRequest::PingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.PingRequest)
}
PingRequest::PingRequest(const PingRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  x_ = from.x_;
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.PingRequest)
}

inline void PingRequest::SharedCtor() {
x_ = 0u;
}

PingRequest::~PingRequest() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.PingRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PingRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PingRequest::ArenaDtor(void* object) {
  PingRequest* _this = reinterpret_cast< PingRequest* >(object);
  (void)_this;
}
void PingRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PingRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PingRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.PingRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  x_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PingRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PingRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.PingRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_x(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.PingRequest)
  return target;
}

size_t PingRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.PingRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_x());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PingRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PingRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PingRequest::GetClassData() const { return &_class_data_; }

void PingRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PingRequest *>(to)->MergeFrom(
      static_cast<const PingRequest &>(from));
}


void PingRequest::MergeFrom(const PingRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.PingRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x() != 0) {
    _internal_set_x(from._internal_x());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PingRequest::CopyFrom(const PingRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.PingRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PingRequest::IsInitialized() const {
  return true;
}

void PingRequest::InternalSwap(PingRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(x_, other->x_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PingRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[0]);
}

// ===================================================================

class PingResponse::_Internal {
 public:
};

PingResponse::PingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.PingResponse)
}
PingResponse::PingResponse(const PingResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  x_ = from.x_;
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.PingResponse)
}

inline void PingResponse::SharedCtor() {
x_ = 0u;
}

PingResponse::~PingResponse() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.PingResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PingResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PingResponse::ArenaDtor(void* object) {
  PingResponse* _this = reinterpret_cast< PingResponse* >(object);
  (void)_this;
}
void PingResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PingResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PingResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.PingResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  x_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PingResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PingResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.PingResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_x(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.PingResponse)
  return target;
}

size_t PingResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.PingResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 x = 1;
  if (this->_internal_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_x());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PingResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PingResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PingResponse::GetClassData() const { return &_class_data_; }

void PingResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PingResponse *>(to)->MergeFrom(
      static_cast<const PingResponse &>(from));
}


void PingResponse::MergeFrom(const PingResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.PingResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x() != 0) {
    _internal_set_x(from._internal_x());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PingResponse::CopyFrom(const PingResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.PingResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PingResponse::IsInitialized() const {
  return true;
}

void PingResponse::InternalSwap(PingResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(x_, other->x_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PingResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[1]);
}

// ===================================================================

Metrics_MetricsEntry_DoNotUse::Metrics_MetricsEntry_DoNotUse() {}
Metrics_MetricsEntry_DoNotUse::Metrics_MetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void Metrics_MetricsEntry_DoNotUse::MergeFrom(const Metrics_MetricsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata Metrics_MetricsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[2]);
}

// ===================================================================

class Metrics::_Internal {
 public:
};

Metrics::Metrics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  metrics_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.Metrics)
}
Metrics::Metrics(const Metrics& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  metrics_.MergeFrom(from.metrics_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.Metrics)
}

inline void Metrics::SharedCtor() {
}

Metrics::~Metrics() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.Metrics)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Metrics::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Metrics::ArenaDtor(void* object) {
  Metrics* _this = reinterpret_cast< Metrics* >(object);
  (void)_this;
  _this->metrics_. ~MapField();
}
inline void Metrics::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &Metrics::ArenaDtor);
  }
}
void Metrics::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Metrics::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.Metrics)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  metrics_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Metrics::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, string> metrics = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&metrics_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Metrics::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.Metrics)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, string> metrics = 1;
  if (!this->_internal_metrics().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "metrics_aggregator.Metrics.MetricsEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "metrics_aggregator.Metrics.MetricsEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_metrics().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_metrics().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_metrics().begin();
          it != this->_internal_metrics().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = Metrics_MetricsEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_metrics().begin();
          it != this->_internal_metrics().end(); ++it) {
        target = Metrics_MetricsEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.Metrics)
  return target;
}

size_t Metrics::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.Metrics)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> metrics = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_metrics_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_metrics().begin();
      it != this->_internal_metrics().end(); ++it) {
    total_size += Metrics_MetricsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Metrics::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Metrics::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Metrics::GetClassData() const { return &_class_data_; }

void Metrics::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Metrics *>(to)->MergeFrom(
      static_cast<const Metrics &>(from));
}


void Metrics::MergeFrom(const Metrics& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.Metrics)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  metrics_.MergeFrom(from.metrics_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Metrics::CopyFrom(const Metrics& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.Metrics)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Metrics::IsInitialized() const {
  return true;
}

void Metrics::InternalSwap(Metrics* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  metrics_.InternalSwap(&other->metrics_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Metrics::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[3]);
}

// ===================================================================

class GetMetricsRequest::_Internal {
 public:
};

GetMetricsRequest::GetMetricsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.GetMetricsRequest)
}
GetMetricsRequest::GetMetricsRequest(const GetMetricsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.GetMetricsRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetMetricsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetMetricsRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetMetricsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[4]);
}

// ===================================================================

GetMetricsResponse_DailyMetricsEntry_DoNotUse::GetMetricsResponse_DailyMetricsEntry_DoNotUse() {}
GetMetricsResponse_DailyMetricsEntry_DoNotUse::GetMetricsResponse_DailyMetricsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetMetricsResponse_DailyMetricsEntry_DoNotUse::MergeFrom(const GetMetricsResponse_DailyMetricsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetMetricsResponse_DailyMetricsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[5]);
}

// ===================================================================

class GetMetricsResponse::_Internal {
 public:
};

GetMetricsResponse::GetMetricsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  daily_metrics_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.GetMetricsResponse)
}
GetMetricsResponse::GetMetricsResponse(const GetMetricsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  daily_metrics_.MergeFrom(from.daily_metrics_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.GetMetricsResponse)
}

inline void GetMetricsResponse::SharedCtor() {
}

GetMetricsResponse::~GetMetricsResponse() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.GetMetricsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetMetricsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetMetricsResponse::ArenaDtor(void* object) {
  GetMetricsResponse* _this = reinterpret_cast< GetMetricsResponse* >(object);
  (void)_this;
  _this->daily_metrics_. ~MapField();
}
inline void GetMetricsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetMetricsResponse::ArenaDtor);
  }
}
void GetMetricsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetMetricsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.GetMetricsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  daily_metrics_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetMetricsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, .metrics_aggregator.Metrics> daily_metrics = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&daily_metrics_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetMetricsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.GetMetricsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, .metrics_aggregator.Metrics> daily_metrics = 1;
  if (!this->_internal_daily_metrics().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "metrics_aggregator.GetMetricsResponse.DailyMetricsEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_daily_metrics().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_daily_metrics().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >::const_iterator
          it = this->_internal_daily_metrics().begin();
          it != this->_internal_daily_metrics().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetMetricsResponse_DailyMetricsEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >::const_iterator
          it = this->_internal_daily_metrics().begin();
          it != this->_internal_daily_metrics().end(); ++it) {
        target = GetMetricsResponse_DailyMetricsEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.GetMetricsResponse)
  return target;
}

size_t GetMetricsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.GetMetricsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, .metrics_aggregator.Metrics> daily_metrics = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_daily_metrics_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, ::metrics_aggregator::Metrics >::const_iterator
      it = this->_internal_daily_metrics().begin();
      it != this->_internal_daily_metrics().end(); ++it) {
    total_size += GetMetricsResponse_DailyMetricsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetMetricsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetMetricsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetMetricsResponse::GetClassData() const { return &_class_data_; }

void GetMetricsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetMetricsResponse *>(to)->MergeFrom(
      static_cast<const GetMetricsResponse &>(from));
}


void GetMetricsResponse::MergeFrom(const GetMetricsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.GetMetricsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  daily_metrics_.MergeFrom(from.daily_metrics_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetMetricsResponse::CopyFrom(const GetMetricsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.GetMetricsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetMetricsResponse::IsInitialized() const {
  return true;
}

void GetMetricsResponse::InternalSwap(GetMetricsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  daily_metrics_.InternalSwap(&other->daily_metrics_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetMetricsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[6]);
}

// ===================================================================

class AcknowledgeDailyMetricRequest::_Internal {
 public:
};

AcknowledgeDailyMetricRequest::AcknowledgeDailyMetricRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  days_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.AcknowledgeDailyMetricRequest)
}
AcknowledgeDailyMetricRequest::AcknowledgeDailyMetricRequest(const AcknowledgeDailyMetricRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      days_(from.days_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.AcknowledgeDailyMetricRequest)
}

inline void AcknowledgeDailyMetricRequest::SharedCtor() {
}

AcknowledgeDailyMetricRequest::~AcknowledgeDailyMetricRequest() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.AcknowledgeDailyMetricRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AcknowledgeDailyMetricRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void AcknowledgeDailyMetricRequest::ArenaDtor(void* object) {
  AcknowledgeDailyMetricRequest* _this = reinterpret_cast< AcknowledgeDailyMetricRequest* >(object);
  (void)_this;
}
void AcknowledgeDailyMetricRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AcknowledgeDailyMetricRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AcknowledgeDailyMetricRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.AcknowledgeDailyMetricRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  days_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AcknowledgeDailyMetricRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated string days = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_days();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "metrics_aggregator.AcknowledgeDailyMetricRequest.days"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AcknowledgeDailyMetricRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.AcknowledgeDailyMetricRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string days = 1;
  for (int i = 0, n = this->_internal_days_size(); i < n; i++) {
    const auto& s = this->_internal_days(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "metrics_aggregator.AcknowledgeDailyMetricRequest.days");
    target = stream->WriteString(1, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.AcknowledgeDailyMetricRequest)
  return target;
}

size_t AcknowledgeDailyMetricRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.AcknowledgeDailyMetricRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string days = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(days_.size());
  for (int i = 0, n = days_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      days_.Get(i));
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AcknowledgeDailyMetricRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AcknowledgeDailyMetricRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AcknowledgeDailyMetricRequest::GetClassData() const { return &_class_data_; }

void AcknowledgeDailyMetricRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AcknowledgeDailyMetricRequest *>(to)->MergeFrom(
      static_cast<const AcknowledgeDailyMetricRequest &>(from));
}


void AcknowledgeDailyMetricRequest::MergeFrom(const AcknowledgeDailyMetricRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.AcknowledgeDailyMetricRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  days_.MergeFrom(from.days_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AcknowledgeDailyMetricRequest::CopyFrom(const AcknowledgeDailyMetricRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.AcknowledgeDailyMetricRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AcknowledgeDailyMetricRequest::IsInitialized() const {
  return true;
}

void AcknowledgeDailyMetricRequest::InternalSwap(AcknowledgeDailyMetricRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  days_.InternalSwap(&other->days_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AcknowledgeDailyMetricRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[7]);
}

// ===================================================================

class AcknowledgeDailyMetricResponse::_Internal {
 public:
};

AcknowledgeDailyMetricResponse::AcknowledgeDailyMetricResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.AcknowledgeDailyMetricResponse)
}
AcknowledgeDailyMetricResponse::AcknowledgeDailyMetricResponse(const AcknowledgeDailyMetricResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.AcknowledgeDailyMetricResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AcknowledgeDailyMetricResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AcknowledgeDailyMetricResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata AcknowledgeDailyMetricResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[8]);
}

// ===================================================================

class GetJobMetricsRequest::_Internal {
 public:
};

GetJobMetricsRequest::GetJobMetricsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.GetJobMetricsRequest)
}
GetJobMetricsRequest::GetJobMetricsRequest(const GetJobMetricsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.GetJobMetricsRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetJobMetricsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetJobMetricsRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetJobMetricsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[9]);
}

// ===================================================================

class GetJobMetricsResponse::_Internal {
 public:
  static const ::metrics_aggregator::Metrics& jobmetrics(const GetJobMetricsResponse* msg);
};

const ::metrics_aggregator::Metrics&
GetJobMetricsResponse::_Internal::jobmetrics(const GetJobMetricsResponse* msg) {
  return *msg->jobmetrics_;
}
GetJobMetricsResponse::GetJobMetricsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.GetJobMetricsResponse)
}
GetJobMetricsResponse::GetJobMetricsResponse(const GetJobMetricsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_jobmetrics()) {
    jobmetrics_ = new ::metrics_aggregator::Metrics(*from.jobmetrics_);
  } else {
    jobmetrics_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.GetJobMetricsResponse)
}

inline void GetJobMetricsResponse::SharedCtor() {
jobmetrics_ = nullptr;
}

GetJobMetricsResponse::~GetJobMetricsResponse() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.GetJobMetricsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetJobMetricsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete jobmetrics_;
}

void GetJobMetricsResponse::ArenaDtor(void* object) {
  GetJobMetricsResponse* _this = reinterpret_cast< GetJobMetricsResponse* >(object);
  (void)_this;
}
void GetJobMetricsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetJobMetricsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetJobMetricsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.GetJobMetricsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && jobmetrics_ != nullptr) {
    delete jobmetrics_;
  }
  jobmetrics_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetJobMetricsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .metrics_aggregator.Metrics jobMetrics = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_jobmetrics(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetJobMetricsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.GetJobMetricsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .metrics_aggregator.Metrics jobMetrics = 1;
  if (this->_internal_has_jobmetrics()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::jobmetrics(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.GetJobMetricsResponse)
  return target;
}

size_t GetJobMetricsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.GetJobMetricsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .metrics_aggregator.Metrics jobMetrics = 1;
  if (this->_internal_has_jobmetrics()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *jobmetrics_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetJobMetricsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetJobMetricsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetJobMetricsResponse::GetClassData() const { return &_class_data_; }

void GetJobMetricsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetJobMetricsResponse *>(to)->MergeFrom(
      static_cast<const GetJobMetricsResponse &>(from));
}


void GetJobMetricsResponse::MergeFrom(const GetJobMetricsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.GetJobMetricsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_jobmetrics()) {
    _internal_mutable_jobmetrics()->::metrics_aggregator::Metrics::MergeFrom(from._internal_jobmetrics());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetJobMetricsResponse::CopyFrom(const GetJobMetricsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.GetJobMetricsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetJobMetricsResponse::IsInitialized() const {
  return true;
}

void GetJobMetricsResponse::InternalSwap(GetJobMetricsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(jobmetrics_, other->jobmetrics_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetJobMetricsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[10]);
}

// ===================================================================

class GetLaserLifeTimesRequest::_Internal {
 public:
};

GetLaserLifeTimesRequest::GetLaserLifeTimesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.GetLaserLifeTimesRequest)
}
GetLaserLifeTimesRequest::GetLaserLifeTimesRequest(const GetLaserLifeTimesRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.GetLaserLifeTimesRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetLaserLifeTimesRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetLaserLifeTimesRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetLaserLifeTimesRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[11]);
}

// ===================================================================

class SetLaserResponse::_Internal {
 public:
};

SetLaserResponse::SetLaserResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.SetLaserResponse)
}
SetLaserResponse::SetLaserResponse(const SetLaserResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.SetLaserResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetLaserResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetLaserResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetLaserResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[12]);
}

// ===================================================================

class GetLaserChangeTimesRequest::_Internal {
 public:
};

GetLaserChangeTimesRequest::GetLaserChangeTimesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.GetLaserChangeTimesRequest)
}
GetLaserChangeTimesRequest::GetLaserChangeTimesRequest(const GetLaserChangeTimesRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.GetLaserChangeTimesRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetLaserChangeTimesRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetLaserChangeTimesRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetLaserChangeTimesRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[13]);
}

// ===================================================================

class RegisterSpatialClientRequest::_Internal {
 public:
};

RegisterSpatialClientRequest::RegisterSpatialClientRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.RegisterSpatialClientRequest)
}
RegisterSpatialClientRequest::RegisterSpatialClientRequest(const RegisterSpatialClientRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.RegisterSpatialClientRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RegisterSpatialClientRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RegisterSpatialClientRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata RegisterSpatialClientRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[14]);
}

// ===================================================================

class RegisterSpatialClientResponse::_Internal {
 public:
};

RegisterSpatialClientResponse::RegisterSpatialClientResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.RegisterSpatialClientResponse)
}
RegisterSpatialClientResponse::RegisterSpatialClientResponse(const RegisterSpatialClientResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.RegisterSpatialClientResponse)
}

inline void RegisterSpatialClientResponse::SharedCtor() {
id_ = 0u;
}

RegisterSpatialClientResponse::~RegisterSpatialClientResponse() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.RegisterSpatialClientResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RegisterSpatialClientResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RegisterSpatialClientResponse::ArenaDtor(void* object) {
  RegisterSpatialClientResponse* _this = reinterpret_cast< RegisterSpatialClientResponse* >(object);
  (void)_this;
}
void RegisterSpatialClientResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RegisterSpatialClientResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RegisterSpatialClientResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.RegisterSpatialClientResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RegisterSpatialClientResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RegisterSpatialClientResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.RegisterSpatialClientResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.RegisterSpatialClientResponse)
  return target;
}

size_t RegisterSpatialClientResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.RegisterSpatialClientResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RegisterSpatialClientResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RegisterSpatialClientResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RegisterSpatialClientResponse::GetClassData() const { return &_class_data_; }

void RegisterSpatialClientResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RegisterSpatialClientResponse *>(to)->MergeFrom(
      static_cast<const RegisterSpatialClientResponse &>(from));
}


void RegisterSpatialClientResponse::MergeFrom(const RegisterSpatialClientResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.RegisterSpatialClientResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RegisterSpatialClientResponse::CopyFrom(const RegisterSpatialClientResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.RegisterSpatialClientResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RegisterSpatialClientResponse::IsInitialized() const {
  return true;
}

void RegisterSpatialClientResponse::InternalSwap(RegisterSpatialClientResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(id_, other->id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RegisterSpatialClientResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[15]);
}

// ===================================================================

class SpatialClientBeatRequest::_Internal {
 public:
};

SpatialClientBeatRequest::SpatialClientBeatRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.SpatialClientBeatRequest)
}
SpatialClientBeatRequest::SpatialClientBeatRequest(const SpatialClientBeatRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.SpatialClientBeatRequest)
}

inline void SpatialClientBeatRequest::SharedCtor() {
id_ = 0u;
}

SpatialClientBeatRequest::~SpatialClientBeatRequest() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.SpatialClientBeatRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SpatialClientBeatRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SpatialClientBeatRequest::ArenaDtor(void* object) {
  SpatialClientBeatRequest* _this = reinterpret_cast< SpatialClientBeatRequest* >(object);
  (void)_this;
}
void SpatialClientBeatRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SpatialClientBeatRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SpatialClientBeatRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.SpatialClientBeatRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SpatialClientBeatRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SpatialClientBeatRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.SpatialClientBeatRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.SpatialClientBeatRequest)
  return target;
}

size_t SpatialClientBeatRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.SpatialClientBeatRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SpatialClientBeatRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SpatialClientBeatRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SpatialClientBeatRequest::GetClassData() const { return &_class_data_; }

void SpatialClientBeatRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SpatialClientBeatRequest *>(to)->MergeFrom(
      static_cast<const SpatialClientBeatRequest &>(from));
}


void SpatialClientBeatRequest::MergeFrom(const SpatialClientBeatRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.SpatialClientBeatRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SpatialClientBeatRequest::CopyFrom(const SpatialClientBeatRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.SpatialClientBeatRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpatialClientBeatRequest::IsInitialized() const {
  return true;
}

void SpatialClientBeatRequest::InternalSwap(SpatialClientBeatRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(id_, other->id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SpatialClientBeatRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[16]);
}

// ===================================================================

class SpatialClientBeatResponse::_Internal {
 public:
};

SpatialClientBeatResponse::SpatialClientBeatResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.SpatialClientBeatResponse)
}
SpatialClientBeatResponse::SpatialClientBeatResponse(const SpatialClientBeatResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  success_ = from.success_;
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.SpatialClientBeatResponse)
}

inline void SpatialClientBeatResponse::SharedCtor() {
success_ = false;
}

SpatialClientBeatResponse::~SpatialClientBeatResponse() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.SpatialClientBeatResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SpatialClientBeatResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SpatialClientBeatResponse::ArenaDtor(void* object) {
  SpatialClientBeatResponse* _this = reinterpret_cast< SpatialClientBeatResponse* >(object);
  (void)_this;
}
void SpatialClientBeatResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SpatialClientBeatResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SpatialClientBeatResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.SpatialClientBeatResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  success_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SpatialClientBeatResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SpatialClientBeatResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.SpatialClientBeatResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.SpatialClientBeatResponse)
  return target;
}

size_t SpatialClientBeatResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.SpatialClientBeatResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SpatialClientBeatResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SpatialClientBeatResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SpatialClientBeatResponse::GetClassData() const { return &_class_data_; }

void SpatialClientBeatResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SpatialClientBeatResponse *>(to)->MergeFrom(
      static_cast<const SpatialClientBeatResponse &>(from));
}


void SpatialClientBeatResponse::MergeFrom(const SpatialClientBeatResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.SpatialClientBeatResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SpatialClientBeatResponse::CopyFrom(const SpatialClientBeatResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.SpatialClientBeatResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpatialClientBeatResponse::IsInitialized() const {
  return true;
}

void SpatialClientBeatResponse::InternalSwap(SpatialClientBeatResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(success_, other->success_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SpatialClientBeatResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[17]);
}

// ===================================================================

class SpatialClientAckRequest::_Internal {
 public:
};

SpatialClientAckRequest::SpatialClientAckRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.SpatialClientAckRequest)
}
SpatialClientAckRequest::SpatialClientAckRequest(const SpatialClientAckRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&block_id_, &from.block_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&client_id_) -
    reinterpret_cast<char*>(&block_id_)) + sizeof(client_id_));
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.SpatialClientAckRequest)
}

inline void SpatialClientAckRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&block_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&client_id_) -
    reinterpret_cast<char*>(&block_id_)) + sizeof(client_id_));
}

SpatialClientAckRequest::~SpatialClientAckRequest() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.SpatialClientAckRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SpatialClientAckRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SpatialClientAckRequest::ArenaDtor(void* object) {
  SpatialClientAckRequest* _this = reinterpret_cast< SpatialClientAckRequest* >(object);
  (void)_this;
}
void SpatialClientAckRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SpatialClientAckRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SpatialClientAckRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.SpatialClientAckRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&block_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&client_id_) -
      reinterpret_cast<char*>(&block_id_)) + sizeof(client_id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SpatialClientAckRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 client_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          client_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 block_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          block_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SpatialClientAckRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.SpatialClientAckRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 client_id = 1;
  if (this->_internal_client_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_client_id(), target);
  }

  // uint64 block_id = 2;
  if (this->_internal_block_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_block_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.SpatialClientAckRequest)
  return target;
}

size_t SpatialClientAckRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.SpatialClientAckRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 block_id = 2;
  if (this->_internal_block_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_block_id());
  }

  // uint32 client_id = 1;
  if (this->_internal_client_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_client_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SpatialClientAckRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SpatialClientAckRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SpatialClientAckRequest::GetClassData() const { return &_class_data_; }

void SpatialClientAckRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SpatialClientAckRequest *>(to)->MergeFrom(
      static_cast<const SpatialClientAckRequest &>(from));
}


void SpatialClientAckRequest::MergeFrom(const SpatialClientAckRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.SpatialClientAckRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_block_id() != 0) {
    _internal_set_block_id(from._internal_block_id());
  }
  if (from._internal_client_id() != 0) {
    _internal_set_client_id(from._internal_client_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SpatialClientAckRequest::CopyFrom(const SpatialClientAckRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.SpatialClientAckRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpatialClientAckRequest::IsInitialized() const {
  return true;
}

void SpatialClientAckRequest::InternalSwap(SpatialClientAckRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SpatialClientAckRequest, client_id_)
      + sizeof(SpatialClientAckRequest::client_id_)
      - PROTOBUF_FIELD_OFFSET(SpatialClientAckRequest, block_id_)>(
          reinterpret_cast<char*>(&block_id_),
          reinterpret_cast<char*>(&other->block_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SpatialClientAckRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[18]);
}

// ===================================================================

class SpatialClientAckResponse::_Internal {
 public:
};

SpatialClientAckResponse::SpatialClientAckResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.SpatialClientAckResponse)
}
SpatialClientAckResponse::SpatialClientAckResponse(const SpatialClientAckResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  success_ = from.success_;
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.SpatialClientAckResponse)
}

inline void SpatialClientAckResponse::SharedCtor() {
success_ = false;
}

SpatialClientAckResponse::~SpatialClientAckResponse() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.SpatialClientAckResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SpatialClientAckResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SpatialClientAckResponse::ArenaDtor(void* object) {
  SpatialClientAckResponse* _this = reinterpret_cast< SpatialClientAckResponse* >(object);
  (void)_this;
}
void SpatialClientAckResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SpatialClientAckResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SpatialClientAckResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.SpatialClientAckResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  success_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SpatialClientAckResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool success = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          success_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SpatialClientAckResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.SpatialClientAckResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_success(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.SpatialClientAckResponse)
  return target;
}

size_t SpatialClientAckResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.SpatialClientAckResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool success = 1;
  if (this->_internal_success() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SpatialClientAckResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SpatialClientAckResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SpatialClientAckResponse::GetClassData() const { return &_class_data_; }

void SpatialClientAckResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SpatialClientAckResponse *>(to)->MergeFrom(
      static_cast<const SpatialClientAckResponse &>(from));
}


void SpatialClientAckResponse::MergeFrom(const SpatialClientAckResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.SpatialClientAckResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_success() != 0) {
    _internal_set_success(from._internal_success());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SpatialClientAckResponse::CopyFrom(const SpatialClientAckResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.SpatialClientAckResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpatialClientAckResponse::IsInitialized() const {
  return true;
}

void SpatialClientAckResponse::InternalSwap(SpatialClientAckResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(success_, other->success_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SpatialClientAckResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[19]);
}

// ===================================================================

class GetNextSpatialBlockRequest::_Internal {
 public:
};

GetNextSpatialBlockRequest::GetNextSpatialBlockRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.GetNextSpatialBlockRequest)
}
GetNextSpatialBlockRequest::GetNextSpatialBlockRequest(const GetNextSpatialBlockRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  block_id_ = from.block_id_;
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.GetNextSpatialBlockRequest)
}

inline void GetNextSpatialBlockRequest::SharedCtor() {
block_id_ = uint64_t{0u};
}

GetNextSpatialBlockRequest::~GetNextSpatialBlockRequest() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.GetNextSpatialBlockRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextSpatialBlockRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetNextSpatialBlockRequest::ArenaDtor(void* object) {
  GetNextSpatialBlockRequest* _this = reinterpret_cast< GetNextSpatialBlockRequest* >(object);
  (void)_this;
}
void GetNextSpatialBlockRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextSpatialBlockRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextSpatialBlockRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.GetNextSpatialBlockRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  block_id_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextSpatialBlockRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 block_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          block_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextSpatialBlockRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.GetNextSpatialBlockRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 block_id = 1;
  if (this->_internal_block_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_block_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.GetNextSpatialBlockRequest)
  return target;
}

size_t GetNextSpatialBlockRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.GetNextSpatialBlockRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 block_id = 1;
  if (this->_internal_block_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_block_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextSpatialBlockRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextSpatialBlockRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextSpatialBlockRequest::GetClassData() const { return &_class_data_; }

void GetNextSpatialBlockRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextSpatialBlockRequest *>(to)->MergeFrom(
      static_cast<const GetNextSpatialBlockRequest &>(from));
}


void GetNextSpatialBlockRequest::MergeFrom(const GetNextSpatialBlockRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.GetNextSpatialBlockRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_block_id() != 0) {
    _internal_set_block_id(from._internal_block_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextSpatialBlockRequest::CopyFrom(const GetNextSpatialBlockRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.GetNextSpatialBlockRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextSpatialBlockRequest::IsInitialized() const {
  return true;
}

void GetNextSpatialBlockRequest::InternalSwap(GetNextSpatialBlockRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(block_id_, other->block_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextSpatialBlockRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[20]);
}

// ===================================================================

class GetNextSpatialBlockResponse::_Internal {
 public:
};

GetNextSpatialBlockResponse::GetNextSpatialBlockResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.GetNextSpatialBlockResponse)
}
GetNextSpatialBlockResponse::GetNextSpatialBlockResponse(const GetNextSpatialBlockResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  block_id_ = from.block_id_;
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.GetNextSpatialBlockResponse)
}

inline void GetNextSpatialBlockResponse::SharedCtor() {
block_id_ = uint64_t{0u};
}

GetNextSpatialBlockResponse::~GetNextSpatialBlockResponse() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.GetNextSpatialBlockResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextSpatialBlockResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetNextSpatialBlockResponse::ArenaDtor(void* object) {
  GetNextSpatialBlockResponse* _this = reinterpret_cast< GetNextSpatialBlockResponse* >(object);
  (void)_this;
}
void GetNextSpatialBlockResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextSpatialBlockResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextSpatialBlockResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.GetNextSpatialBlockResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  block_id_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextSpatialBlockResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 block_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          block_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextSpatialBlockResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.GetNextSpatialBlockResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 block_id = 1;
  if (this->_internal_block_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_block_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.GetNextSpatialBlockResponse)
  return target;
}

size_t GetNextSpatialBlockResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.GetNextSpatialBlockResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 block_id = 1;
  if (this->_internal_block_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_block_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextSpatialBlockResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextSpatialBlockResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextSpatialBlockResponse::GetClassData() const { return &_class_data_; }

void GetNextSpatialBlockResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextSpatialBlockResponse *>(to)->MergeFrom(
      static_cast<const GetNextSpatialBlockResponse &>(from));
}


void GetNextSpatialBlockResponse::MergeFrom(const GetNextSpatialBlockResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.GetNextSpatialBlockResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_block_id() != 0) {
    _internal_set_block_id(from._internal_block_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextSpatialBlockResponse::CopyFrom(const GetNextSpatialBlockResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.GetNextSpatialBlockResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextSpatialBlockResponse::IsInitialized() const {
  return true;
}

void GetNextSpatialBlockResponse::InternalSwap(GetNextSpatialBlockResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(block_id_, other->block_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextSpatialBlockResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[21]);
}

// ===================================================================

class OverrideLaserRequest::_Internal {
 public:
  static const ::carbon::metrics::LaserIdentifier& laser(const OverrideLaserRequest* msg);
};

const ::carbon::metrics::LaserIdentifier&
OverrideLaserRequest::_Internal::laser(const OverrideLaserRequest* msg) {
  return *msg->laser_;
}
void OverrideLaserRequest::clear_laser() {
  if (GetArenaForAllocation() == nullptr && laser_ != nullptr) {
    delete laser_;
  }
  laser_ = nullptr;
}
OverrideLaserRequest::OverrideLaserRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:metrics_aggregator.OverrideLaserRequest)
}
OverrideLaserRequest::OverrideLaserRequest(const OverrideLaserRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_laser()) {
    laser_ = new ::carbon::metrics::LaserIdentifier(*from.laser_);
  } else {
    laser_ = nullptr;
  }
  lifetime_s_ = from.lifetime_s_;
  // @@protoc_insertion_point(copy_constructor:metrics_aggregator.OverrideLaserRequest)
}

inline void OverrideLaserRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&laser_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&lifetime_s_) -
    reinterpret_cast<char*>(&laser_)) + sizeof(lifetime_s_));
}

OverrideLaserRequest::~OverrideLaserRequest() {
  // @@protoc_insertion_point(destructor:metrics_aggregator.OverrideLaserRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void OverrideLaserRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete laser_;
}

void OverrideLaserRequest::ArenaDtor(void* object) {
  OverrideLaserRequest* _this = reinterpret_cast< OverrideLaserRequest* >(object);
  (void)_this;
}
void OverrideLaserRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void OverrideLaserRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void OverrideLaserRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:metrics_aggregator.OverrideLaserRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && laser_ != nullptr) {
    delete laser_;
  }
  laser_ = nullptr;
  lifetime_s_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* OverrideLaserRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.metrics.LaserIdentifier laser = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_laser(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 lifetime_s = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          lifetime_s_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* OverrideLaserRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:metrics_aggregator.OverrideLaserRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.metrics.LaserIdentifier laser = 1;
  if (this->_internal_has_laser()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::laser(this), target, stream);
  }

  // uint64 lifetime_s = 2;
  if (this->_internal_lifetime_s() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_lifetime_s(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:metrics_aggregator.OverrideLaserRequest)
  return target;
}

size_t OverrideLaserRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:metrics_aggregator.OverrideLaserRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.metrics.LaserIdentifier laser = 1;
  if (this->_internal_has_laser()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *laser_);
  }

  // uint64 lifetime_s = 2;
  if (this->_internal_lifetime_s() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_lifetime_s());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData OverrideLaserRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    OverrideLaserRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*OverrideLaserRequest::GetClassData() const { return &_class_data_; }

void OverrideLaserRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<OverrideLaserRequest *>(to)->MergeFrom(
      static_cast<const OverrideLaserRequest &>(from));
}


void OverrideLaserRequest::MergeFrom(const OverrideLaserRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:metrics_aggregator.OverrideLaserRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_laser()) {
    _internal_mutable_laser()->::carbon::metrics::LaserIdentifier::MergeFrom(from._internal_laser());
  }
  if (from._internal_lifetime_s() != 0) {
    _internal_set_lifetime_s(from._internal_lifetime_s());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void OverrideLaserRequest::CopyFrom(const OverrideLaserRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:metrics_aggregator.OverrideLaserRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool OverrideLaserRequest::IsInitialized() const {
  return true;
}

void OverrideLaserRequest::InternalSwap(OverrideLaserRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(OverrideLaserRequest, lifetime_s_)
      + sizeof(OverrideLaserRequest::lifetime_s_)
      - PROTOBUF_FIELD_OFFSET(OverrideLaserRequest, laser_)>(
          reinterpret_cast<char*>(&laser_),
          reinterpret_cast<char*>(&other->laser_));
}

::PROTOBUF_NAMESPACE_ID::Metadata OverrideLaserRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_getter, &descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto_once,
      file_level_metadata_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto[22]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace metrics_aggregator
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::metrics_aggregator::PingRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::PingRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::PingRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::PingResponse* Arena::CreateMaybeMessage< ::metrics_aggregator::PingResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::PingResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::Metrics_MetricsEntry_DoNotUse* Arena::CreateMaybeMessage< ::metrics_aggregator::Metrics_MetricsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::Metrics_MetricsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::Metrics* Arena::CreateMaybeMessage< ::metrics_aggregator::Metrics >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::Metrics >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::GetMetricsRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::GetMetricsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::GetMetricsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse* Arena::CreateMaybeMessage< ::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::GetMetricsResponse_DailyMetricsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::GetMetricsResponse* Arena::CreateMaybeMessage< ::metrics_aggregator::GetMetricsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::GetMetricsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::AcknowledgeDailyMetricRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::AcknowledgeDailyMetricRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::AcknowledgeDailyMetricRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::AcknowledgeDailyMetricResponse* Arena::CreateMaybeMessage< ::metrics_aggregator::AcknowledgeDailyMetricResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::AcknowledgeDailyMetricResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::GetJobMetricsRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::GetJobMetricsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::GetJobMetricsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::GetJobMetricsResponse* Arena::CreateMaybeMessage< ::metrics_aggregator::GetJobMetricsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::GetJobMetricsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::GetLaserLifeTimesRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::GetLaserLifeTimesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::GetLaserLifeTimesRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::SetLaserResponse* Arena::CreateMaybeMessage< ::metrics_aggregator::SetLaserResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::SetLaserResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::GetLaserChangeTimesRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::GetLaserChangeTimesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::GetLaserChangeTimesRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::RegisterSpatialClientRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::RegisterSpatialClientRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::RegisterSpatialClientRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::RegisterSpatialClientResponse* Arena::CreateMaybeMessage< ::metrics_aggregator::RegisterSpatialClientResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::RegisterSpatialClientResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::SpatialClientBeatRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::SpatialClientBeatRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::SpatialClientBeatRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::SpatialClientBeatResponse* Arena::CreateMaybeMessage< ::metrics_aggregator::SpatialClientBeatResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::SpatialClientBeatResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::SpatialClientAckRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::SpatialClientAckRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::SpatialClientAckRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::SpatialClientAckResponse* Arena::CreateMaybeMessage< ::metrics_aggregator::SpatialClientAckResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::SpatialClientAckResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::GetNextSpatialBlockRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::GetNextSpatialBlockRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::GetNextSpatialBlockRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::GetNextSpatialBlockResponse* Arena::CreateMaybeMessage< ::metrics_aggregator::GetNextSpatialBlockResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::GetNextSpatialBlockResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::metrics_aggregator::OverrideLaserRequest* Arena::CreateMaybeMessage< ::metrics_aggregator::OverrideLaserRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::metrics_aggregator::OverrideLaserRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

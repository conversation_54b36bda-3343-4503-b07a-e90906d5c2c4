// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: metrics/proto/metrics_aggregator_service.proto
#ifndef GRPC_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto__INCLUDED
#define GRPC_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto__INCLUDED

#include "metrics/proto/metrics_aggregator_service.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace metrics_aggregator {

class MetricsAggregatorService final {
 public:
  static constexpr char const* service_full_name() {
    return "metrics_aggregator.MetricsAggregatorService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Ping(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::metrics_aggregator::PingResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::PingResponse>> AsyncPing(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::PingResponse>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::PingResponse>> PrepareAsyncPing(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::PingResponse>>(PrepareAsyncPingRaw(context, request, cq));
    }
    virtual ::grpc::Status GetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::metrics_aggregator::GetMetricsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetMetricsResponse>> AsyncGetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetMetricsResponse>>(AsyncGetMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetMetricsResponse>> PrepareAsyncGetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetMetricsResponse>>(PrepareAsyncGetMetricsRaw(context, request, cq));
    }
    virtual ::grpc::Status AcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::AcknowledgeDailyMetricResponse>> AsyncAcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::AcknowledgeDailyMetricResponse>>(AsyncAcknowledgeDailyMetricRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::AcknowledgeDailyMetricResponse>> PrepareAsyncAcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::AcknowledgeDailyMetricResponse>>(PrepareAsyncAcknowledgeDailyMetricRaw(context, request, cq));
    }
    virtual ::grpc::Status GetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::metrics_aggregator::GetJobMetricsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetJobMetricsResponse>> AsyncGetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetJobMetricsResponse>>(AsyncGetJobMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetJobMetricsResponse>> PrepareAsyncGetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetJobMetricsResponse>>(PrepareAsyncGetJobMetricsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::carbon::metrics::LaserLifeTimes* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserLifeTimes>> AsyncGetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserLifeTimes>>(AsyncGetLaserLifeTimesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserLifeTimes>> PrepareAsyncGetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserLifeTimes>>(PrepareAsyncGetLaserLifeTimesRaw(context, request, cq));
    }
    virtual ::grpc::Status SetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::metrics_aggregator::SetLaserResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>> AsyncSetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>>(AsyncSetLaserRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>> PrepareAsyncSetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>>(PrepareAsyncSetLaserRaw(context, request, cq));
    }
    virtual ::grpc::Status OverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::metrics_aggregator::SetLaserResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>> AsyncOverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>>(AsyncOverrideLaserRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>> PrepareAsyncOverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>>(PrepareAsyncOverrideLaserRaw(context, request, cq));
    }
    virtual ::grpc::Status GetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::carbon::metrics::LaserChangeTimes* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserChangeTimes>> AsyncGetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserChangeTimes>>(AsyncGetLaserChangeTimesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserChangeTimes>> PrepareAsyncGetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserChangeTimes>>(PrepareAsyncGetLaserChangeTimesRaw(context, request, cq));
    }
    virtual ::grpc::Status RegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::metrics_aggregator::RegisterSpatialClientResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::RegisterSpatialClientResponse>> AsyncRegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::RegisterSpatialClientResponse>>(AsyncRegisterSpatialClientRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::RegisterSpatialClientResponse>> PrepareAsyncRegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::RegisterSpatialClientResponse>>(PrepareAsyncRegisterSpatialClientRaw(context, request, cq));
    }
    virtual ::grpc::Status SpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::metrics_aggregator::SpatialClientBeatResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientBeatResponse>> AsyncSpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientBeatResponse>>(AsyncSpatialClientBeatRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientBeatResponse>> PrepareAsyncSpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientBeatResponse>>(PrepareAsyncSpatialClientBeatRaw(context, request, cq));
    }
    virtual ::grpc::Status SpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::metrics_aggregator::SpatialClientAckResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientAckResponse>> AsyncSpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientAckResponse>>(AsyncSpatialClientAckRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientAckResponse>> PrepareAsyncSpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientAckResponse>>(PrepareAsyncSpatialClientAckRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::metrics_aggregator::GetNextSpatialBlockResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetNextSpatialBlockResponse>> AsyncGetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetNextSpatialBlockResponse>>(AsyncGetNextSpatialBlockRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetNextSpatialBlockResponse>> PrepareAsyncGetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetNextSpatialBlockResponse>>(PrepareAsyncGetNextSpatialBlockRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Ping(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest* request, ::metrics_aggregator::PingResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Ping(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest* request, ::metrics_aggregator::PingResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest* request, ::metrics_aggregator::GetMetricsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest* request, ::metrics_aggregator::GetMetricsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void AcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void AcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest* request, ::metrics_aggregator::GetJobMetricsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest* request, ::metrics_aggregator::GetJobMetricsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::carbon::metrics::LaserLifeTimes* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::carbon::metrics::LaserLifeTimes* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier* request, ::metrics_aggregator::SetLaserResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier* request, ::metrics_aggregator::SetLaserResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void OverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest* request, ::metrics_aggregator::SetLaserResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void OverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest* request, ::metrics_aggregator::SetLaserResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::carbon::metrics::LaserChangeTimes* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::carbon::metrics::LaserChangeTimes* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void RegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest* request, ::metrics_aggregator::RegisterSpatialClientResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void RegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest* request, ::metrics_aggregator::RegisterSpatialClientResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest* request, ::metrics_aggregator::SpatialClientBeatResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest* request, ::metrics_aggregator::SpatialClientBeatResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest* request, ::metrics_aggregator::SpatialClientAckResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest* request, ::metrics_aggregator::SpatialClientAckResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::metrics_aggregator::GetNextSpatialBlockResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::metrics_aggregator::GetNextSpatialBlockResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::PingResponse>* AsyncPingRaw(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::PingResponse>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetMetricsResponse>* AsyncGetMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetMetricsResponse>* PrepareAsyncGetMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::AcknowledgeDailyMetricResponse>* AsyncAcknowledgeDailyMetricRaw(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::AcknowledgeDailyMetricResponse>* PrepareAsyncAcknowledgeDailyMetricRaw(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetJobMetricsResponse>* AsyncGetJobMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetJobMetricsResponse>* PrepareAsyncGetJobMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserLifeTimes>* AsyncGetLaserLifeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserLifeTimes>* PrepareAsyncGetLaserLifeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>* AsyncSetLaserRaw(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>* PrepareAsyncSetLaserRaw(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>* AsyncOverrideLaserRaw(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SetLaserResponse>* PrepareAsyncOverrideLaserRaw(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserChangeTimes>* AsyncGetLaserChangeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::metrics::LaserChangeTimes>* PrepareAsyncGetLaserChangeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::RegisterSpatialClientResponse>* AsyncRegisterSpatialClientRaw(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::RegisterSpatialClientResponse>* PrepareAsyncRegisterSpatialClientRaw(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientBeatResponse>* AsyncSpatialClientBeatRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientBeatResponse>* PrepareAsyncSpatialClientBeatRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientAckResponse>* AsyncSpatialClientAckRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::SpatialClientAckResponse>* PrepareAsyncSpatialClientAckRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetNextSpatialBlockResponse>* AsyncGetNextSpatialBlockRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::metrics_aggregator::GetNextSpatialBlockResponse>* PrepareAsyncGetNextSpatialBlockRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Ping(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::metrics_aggregator::PingResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::PingResponse>> AsyncPing(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::PingResponse>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::PingResponse>> PrepareAsyncPing(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::PingResponse>>(PrepareAsyncPingRaw(context, request, cq));
    }
    ::grpc::Status GetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::metrics_aggregator::GetMetricsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetMetricsResponse>> AsyncGetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetMetricsResponse>>(AsyncGetMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetMetricsResponse>> PrepareAsyncGetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetMetricsResponse>>(PrepareAsyncGetMetricsRaw(context, request, cq));
    }
    ::grpc::Status AcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::AcknowledgeDailyMetricResponse>> AsyncAcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::AcknowledgeDailyMetricResponse>>(AsyncAcknowledgeDailyMetricRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::AcknowledgeDailyMetricResponse>> PrepareAsyncAcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::AcknowledgeDailyMetricResponse>>(PrepareAsyncAcknowledgeDailyMetricRaw(context, request, cq));
    }
    ::grpc::Status GetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::metrics_aggregator::GetJobMetricsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetJobMetricsResponse>> AsyncGetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetJobMetricsResponse>>(AsyncGetJobMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetJobMetricsResponse>> PrepareAsyncGetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetJobMetricsResponse>>(PrepareAsyncGetJobMetricsRaw(context, request, cq));
    }
    ::grpc::Status GetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::carbon::metrics::LaserLifeTimes* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserLifeTimes>> AsyncGetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserLifeTimes>>(AsyncGetLaserLifeTimesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserLifeTimes>> PrepareAsyncGetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserLifeTimes>>(PrepareAsyncGetLaserLifeTimesRaw(context, request, cq));
    }
    ::grpc::Status SetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::metrics_aggregator::SetLaserResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>> AsyncSetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>>(AsyncSetLaserRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>> PrepareAsyncSetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>>(PrepareAsyncSetLaserRaw(context, request, cq));
    }
    ::grpc::Status OverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::metrics_aggregator::SetLaserResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>> AsyncOverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>>(AsyncOverrideLaserRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>> PrepareAsyncOverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>>(PrepareAsyncOverrideLaserRaw(context, request, cq));
    }
    ::grpc::Status GetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::carbon::metrics::LaserChangeTimes* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserChangeTimes>> AsyncGetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserChangeTimes>>(AsyncGetLaserChangeTimesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserChangeTimes>> PrepareAsyncGetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserChangeTimes>>(PrepareAsyncGetLaserChangeTimesRaw(context, request, cq));
    }
    ::grpc::Status RegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::metrics_aggregator::RegisterSpatialClientResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::RegisterSpatialClientResponse>> AsyncRegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::RegisterSpatialClientResponse>>(AsyncRegisterSpatialClientRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::RegisterSpatialClientResponse>> PrepareAsyncRegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::RegisterSpatialClientResponse>>(PrepareAsyncRegisterSpatialClientRaw(context, request, cq));
    }
    ::grpc::Status SpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::metrics_aggregator::SpatialClientBeatResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientBeatResponse>> AsyncSpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientBeatResponse>>(AsyncSpatialClientBeatRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientBeatResponse>> PrepareAsyncSpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientBeatResponse>>(PrepareAsyncSpatialClientBeatRaw(context, request, cq));
    }
    ::grpc::Status SpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::metrics_aggregator::SpatialClientAckResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientAckResponse>> AsyncSpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientAckResponse>>(AsyncSpatialClientAckRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientAckResponse>> PrepareAsyncSpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientAckResponse>>(PrepareAsyncSpatialClientAckRaw(context, request, cq));
    }
    ::grpc::Status GetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::metrics_aggregator::GetNextSpatialBlockResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetNextSpatialBlockResponse>> AsyncGetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetNextSpatialBlockResponse>>(AsyncGetNextSpatialBlockRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetNextSpatialBlockResponse>> PrepareAsyncGetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetNextSpatialBlockResponse>>(PrepareAsyncGetNextSpatialBlockRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Ping(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest* request, ::metrics_aggregator::PingResponse* response, std::function<void(::grpc::Status)>) override;
      void Ping(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest* request, ::metrics_aggregator::PingResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest* request, ::metrics_aggregator::GetMetricsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest* request, ::metrics_aggregator::GetMetricsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void AcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response, std::function<void(::grpc::Status)>) override;
      void AcknowledgeDailyMetric(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest* request, ::metrics_aggregator::GetJobMetricsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetJobMetrics(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest* request, ::metrics_aggregator::GetJobMetricsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::carbon::metrics::LaserLifeTimes* response, std::function<void(::grpc::Status)>) override;
      void GetLaserLifeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::carbon::metrics::LaserLifeTimes* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier* request, ::metrics_aggregator::SetLaserResponse* response, std::function<void(::grpc::Status)>) override;
      void SetLaser(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier* request, ::metrics_aggregator::SetLaserResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void OverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest* request, ::metrics_aggregator::SetLaserResponse* response, std::function<void(::grpc::Status)>) override;
      void OverrideLaser(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest* request, ::metrics_aggregator::SetLaserResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::carbon::metrics::LaserChangeTimes* response, std::function<void(::grpc::Status)>) override;
      void GetLaserChangeTimes(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::carbon::metrics::LaserChangeTimes* response, ::grpc::ClientUnaryReactor* reactor) override;
      void RegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest* request, ::metrics_aggregator::RegisterSpatialClientResponse* response, std::function<void(::grpc::Status)>) override;
      void RegisterSpatialClient(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest* request, ::metrics_aggregator::RegisterSpatialClientResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest* request, ::metrics_aggregator::SpatialClientBeatResponse* response, std::function<void(::grpc::Status)>) override;
      void SpatialClientBeat(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest* request, ::metrics_aggregator::SpatialClientBeatResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest* request, ::metrics_aggregator::SpatialClientAckResponse* response, std::function<void(::grpc::Status)>) override;
      void SpatialClientAck(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest* request, ::metrics_aggregator::SpatialClientAckResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::metrics_aggregator::GetNextSpatialBlockResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextSpatialBlock(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::metrics_aggregator::GetNextSpatialBlockResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::PingResponse>* AsyncPingRaw(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::PingResponse>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::metrics_aggregator::PingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetMetricsResponse>* AsyncGetMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetMetricsResponse>* PrepareAsyncGetMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetMetricsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::AcknowledgeDailyMetricResponse>* AsyncAcknowledgeDailyMetricRaw(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::AcknowledgeDailyMetricResponse>* PrepareAsyncAcknowledgeDailyMetricRaw(::grpc::ClientContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetJobMetricsResponse>* AsyncGetJobMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetJobMetricsResponse>* PrepareAsyncGetJobMetricsRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetJobMetricsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserLifeTimes>* AsyncGetLaserLifeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserLifeTimes>* PrepareAsyncGetLaserLifeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>* AsyncSetLaserRaw(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>* PrepareAsyncSetLaserRaw(::grpc::ClientContext* context, const ::carbon::metrics::LaserIdentifier& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>* AsyncOverrideLaserRaw(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SetLaserResponse>* PrepareAsyncOverrideLaserRaw(::grpc::ClientContext* context, const ::metrics_aggregator::OverrideLaserRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserChangeTimes>* AsyncGetLaserChangeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::metrics::LaserChangeTimes>* PrepareAsyncGetLaserChangeTimesRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::RegisterSpatialClientResponse>* AsyncRegisterSpatialClientRaw(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::RegisterSpatialClientResponse>* PrepareAsyncRegisterSpatialClientRaw(::grpc::ClientContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientBeatResponse>* AsyncSpatialClientBeatRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientBeatResponse>* PrepareAsyncSpatialClientBeatRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientBeatRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientAckResponse>* AsyncSpatialClientAckRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::SpatialClientAckResponse>* PrepareAsyncSpatialClientAckRaw(::grpc::ClientContext* context, const ::metrics_aggregator::SpatialClientAckRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetNextSpatialBlockResponse>* AsyncGetNextSpatialBlockRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::metrics_aggregator::GetNextSpatialBlockResponse>* PrepareAsyncGetNextSpatialBlockRaw(::grpc::ClientContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Ping_;
    const ::grpc::internal::RpcMethod rpcmethod_GetMetrics_;
    const ::grpc::internal::RpcMethod rpcmethod_AcknowledgeDailyMetric_;
    const ::grpc::internal::RpcMethod rpcmethod_GetJobMetrics_;
    const ::grpc::internal::RpcMethod rpcmethod_GetLaserLifeTimes_;
    const ::grpc::internal::RpcMethod rpcmethod_SetLaser_;
    const ::grpc::internal::RpcMethod rpcmethod_OverrideLaser_;
    const ::grpc::internal::RpcMethod rpcmethod_GetLaserChangeTimes_;
    const ::grpc::internal::RpcMethod rpcmethod_RegisterSpatialClient_;
    const ::grpc::internal::RpcMethod rpcmethod_SpatialClientBeat_;
    const ::grpc::internal::RpcMethod rpcmethod_SpatialClientAck_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextSpatialBlock_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Ping(::grpc::ServerContext* context, const ::metrics_aggregator::PingRequest* request, ::metrics_aggregator::PingResponse* response);
    virtual ::grpc::Status GetMetrics(::grpc::ServerContext* context, const ::metrics_aggregator::GetMetricsRequest* request, ::metrics_aggregator::GetMetricsResponse* response);
    virtual ::grpc::Status AcknowledgeDailyMetric(::grpc::ServerContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response);
    virtual ::grpc::Status GetJobMetrics(::grpc::ServerContext* context, const ::metrics_aggregator::GetJobMetricsRequest* request, ::metrics_aggregator::GetJobMetricsResponse* response);
    virtual ::grpc::Status GetLaserLifeTimes(::grpc::ServerContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::carbon::metrics::LaserLifeTimes* response);
    virtual ::grpc::Status SetLaser(::grpc::ServerContext* context, const ::carbon::metrics::LaserIdentifier* request, ::metrics_aggregator::SetLaserResponse* response);
    virtual ::grpc::Status OverrideLaser(::grpc::ServerContext* context, const ::metrics_aggregator::OverrideLaserRequest* request, ::metrics_aggregator::SetLaserResponse* response);
    virtual ::grpc::Status GetLaserChangeTimes(::grpc::ServerContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::carbon::metrics::LaserChangeTimes* response);
    virtual ::grpc::Status RegisterSpatialClient(::grpc::ServerContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest* request, ::metrics_aggregator::RegisterSpatialClientResponse* response);
    virtual ::grpc::Status SpatialClientBeat(::grpc::ServerContext* context, const ::metrics_aggregator::SpatialClientBeatRequest* request, ::metrics_aggregator::SpatialClientBeatResponse* response);
    virtual ::grpc::Status SpatialClientAck(::grpc::ServerContext* context, const ::metrics_aggregator::SpatialClientAckRequest* request, ::metrics_aggregator::SpatialClientAckResponse* response);
    virtual ::grpc::Status GetNextSpatialBlock(::grpc::ServerContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::metrics_aggregator::GetNextSpatialBlockResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Ping() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::PingRequest* /*request*/, ::metrics_aggregator::PingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::metrics_aggregator::PingRequest* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::PingResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetMetrics() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetMetricsRequest* /*request*/, ::metrics_aggregator::GetMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetMetrics(::grpc::ServerContext* context, ::metrics_aggregator::GetMetricsRequest* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::GetMetricsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_AcknowledgeDailyMetric : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_AcknowledgeDailyMetric() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_AcknowledgeDailyMetric() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeDailyMetric(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* /*request*/, ::metrics_aggregator::AcknowledgeDailyMetricResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAcknowledgeDailyMetric(::grpc::ServerContext* context, ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::AcknowledgeDailyMetricResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetJobMetrics() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_GetJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJobMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetJobMetricsRequest* /*request*/, ::metrics_aggregator::GetJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetJobMetrics(::grpc::ServerContext* context, ::metrics_aggregator::GetJobMetricsRequest* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::GetJobMetricsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetLaserLifeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetLaserLifeTimes() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_GetLaserLifeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserLifeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserLifeTimesRequest* /*request*/, ::carbon::metrics::LaserLifeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetLaserLifeTimes(::grpc::ServerContext* context, ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::metrics::LaserLifeTimes>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetLaser() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_SetLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaser(::grpc::ServerContext* /*context*/, const ::carbon::metrics::LaserIdentifier* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLaser(::grpc::ServerContext* context, ::carbon::metrics::LaserIdentifier* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::SetLaserResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_OverrideLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_OverrideLaser() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_OverrideLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OverrideLaser(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::OverrideLaserRequest* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestOverrideLaser(::grpc::ServerContext* context, ::metrics_aggregator::OverrideLaserRequest* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::SetLaserResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetLaserChangeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetLaserChangeTimes() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_GetLaserChangeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserChangeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserChangeTimesRequest* /*request*/, ::carbon::metrics::LaserChangeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetLaserChangeTimes(::grpc::ServerContext* context, ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::metrics::LaserChangeTimes>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_RegisterSpatialClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_RegisterSpatialClient() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_RegisterSpatialClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterSpatialClient(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::RegisterSpatialClientRequest* /*request*/, ::metrics_aggregator::RegisterSpatialClientResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRegisterSpatialClient(::grpc::ServerContext* context, ::metrics_aggregator::RegisterSpatialClientRequest* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::RegisterSpatialClientResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SpatialClientBeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SpatialClientBeat() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_SpatialClientBeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientBeat(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientBeatRequest* /*request*/, ::metrics_aggregator::SpatialClientBeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSpatialClientBeat(::grpc::ServerContext* context, ::metrics_aggregator::SpatialClientBeatRequest* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::SpatialClientBeatResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SpatialClientAck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SpatialClientAck() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_SpatialClientAck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientAck(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientAckRequest* /*request*/, ::metrics_aggregator::SpatialClientAckResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSpatialClientAck(::grpc::ServerContext* context, ::metrics_aggregator::SpatialClientAckRequest* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::SpatialClientAckResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextSpatialBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextSpatialBlock() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_GetNextSpatialBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSpatialBlock(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetNextSpatialBlockRequest* /*request*/, ::metrics_aggregator::GetNextSpatialBlockResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextSpatialBlock(::grpc::ServerContext* context, ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::grpc::ServerAsyncResponseWriter< ::metrics_aggregator::GetNextSpatialBlockResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Ping<WithAsyncMethod_GetMetrics<WithAsyncMethod_AcknowledgeDailyMetric<WithAsyncMethod_GetJobMetrics<WithAsyncMethod_GetLaserLifeTimes<WithAsyncMethod_SetLaser<WithAsyncMethod_OverrideLaser<WithAsyncMethod_GetLaserChangeTimes<WithAsyncMethod_RegisterSpatialClient<WithAsyncMethod_SpatialClientBeat<WithAsyncMethod_SpatialClientAck<WithAsyncMethod_GetNextSpatialBlock<Service > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::PingRequest, ::metrics_aggregator::PingResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::PingRequest* request, ::metrics_aggregator::PingResponse* response) { return this->Ping(context, request, response); }));}
    void SetMessageAllocatorFor_Ping(
        ::grpc::MessageAllocator< ::metrics_aggregator::PingRequest, ::metrics_aggregator::PingResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::PingRequest, ::metrics_aggregator::PingResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::PingRequest* /*request*/, ::metrics_aggregator::PingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::PingRequest* /*request*/, ::metrics_aggregator::PingResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetMetrics() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetMetricsRequest, ::metrics_aggregator::GetMetricsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::GetMetricsRequest* request, ::metrics_aggregator::GetMetricsResponse* response) { return this->GetMetrics(context, request, response); }));}
    void SetMessageAllocatorFor_GetMetrics(
        ::grpc::MessageAllocator< ::metrics_aggregator::GetMetricsRequest, ::metrics_aggregator::GetMetricsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetMetricsRequest, ::metrics_aggregator::GetMetricsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetMetricsRequest* /*request*/, ::metrics_aggregator::GetMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::GetMetricsRequest* /*request*/, ::metrics_aggregator::GetMetricsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_AcknowledgeDailyMetric : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_AcknowledgeDailyMetric() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::AcknowledgeDailyMetricRequest, ::metrics_aggregator::AcknowledgeDailyMetricResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* request, ::metrics_aggregator::AcknowledgeDailyMetricResponse* response) { return this->AcknowledgeDailyMetric(context, request, response); }));}
    void SetMessageAllocatorFor_AcknowledgeDailyMetric(
        ::grpc::MessageAllocator< ::metrics_aggregator::AcknowledgeDailyMetricRequest, ::metrics_aggregator::AcknowledgeDailyMetricResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::AcknowledgeDailyMetricRequest, ::metrics_aggregator::AcknowledgeDailyMetricResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_AcknowledgeDailyMetric() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeDailyMetric(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* /*request*/, ::metrics_aggregator::AcknowledgeDailyMetricResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AcknowledgeDailyMetric(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* /*request*/, ::metrics_aggregator::AcknowledgeDailyMetricResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetJobMetrics() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetJobMetricsRequest, ::metrics_aggregator::GetJobMetricsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::GetJobMetricsRequest* request, ::metrics_aggregator::GetJobMetricsResponse* response) { return this->GetJobMetrics(context, request, response); }));}
    void SetMessageAllocatorFor_GetJobMetrics(
        ::grpc::MessageAllocator< ::metrics_aggregator::GetJobMetricsRequest, ::metrics_aggregator::GetJobMetricsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetJobMetricsRequest, ::metrics_aggregator::GetJobMetricsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJobMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetJobMetricsRequest* /*request*/, ::metrics_aggregator::GetJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetJobMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::GetJobMetricsRequest* /*request*/, ::metrics_aggregator::GetJobMetricsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetLaserLifeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetLaserLifeTimes() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetLaserLifeTimesRequest, ::carbon::metrics::LaserLifeTimes>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::GetLaserLifeTimesRequest* request, ::carbon::metrics::LaserLifeTimes* response) { return this->GetLaserLifeTimes(context, request, response); }));}
    void SetMessageAllocatorFor_GetLaserLifeTimes(
        ::grpc::MessageAllocator< ::metrics_aggregator::GetLaserLifeTimesRequest, ::carbon::metrics::LaserLifeTimes>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetLaserLifeTimesRequest, ::carbon::metrics::LaserLifeTimes>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetLaserLifeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserLifeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserLifeTimesRequest* /*request*/, ::carbon::metrics::LaserLifeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetLaserLifeTimes(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::GetLaserLifeTimesRequest* /*request*/, ::carbon::metrics::LaserLifeTimes* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetLaser() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::metrics::LaserIdentifier, ::metrics_aggregator::SetLaserResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::metrics::LaserIdentifier* request, ::metrics_aggregator::SetLaserResponse* response) { return this->SetLaser(context, request, response); }));}
    void SetMessageAllocatorFor_SetLaser(
        ::grpc::MessageAllocator< ::carbon::metrics::LaserIdentifier, ::metrics_aggregator::SetLaserResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::metrics::LaserIdentifier, ::metrics_aggregator::SetLaserResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaser(::grpc::ServerContext* /*context*/, const ::carbon::metrics::LaserIdentifier* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLaser(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::metrics::LaserIdentifier* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_OverrideLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_OverrideLaser() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::OverrideLaserRequest, ::metrics_aggregator::SetLaserResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::OverrideLaserRequest* request, ::metrics_aggregator::SetLaserResponse* response) { return this->OverrideLaser(context, request, response); }));}
    void SetMessageAllocatorFor_OverrideLaser(
        ::grpc::MessageAllocator< ::metrics_aggregator::OverrideLaserRequest, ::metrics_aggregator::SetLaserResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::OverrideLaserRequest, ::metrics_aggregator::SetLaserResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_OverrideLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OverrideLaser(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::OverrideLaserRequest* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* OverrideLaser(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::OverrideLaserRequest* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetLaserChangeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetLaserChangeTimes() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetLaserChangeTimesRequest, ::carbon::metrics::LaserChangeTimes>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::GetLaserChangeTimesRequest* request, ::carbon::metrics::LaserChangeTimes* response) { return this->GetLaserChangeTimes(context, request, response); }));}
    void SetMessageAllocatorFor_GetLaserChangeTimes(
        ::grpc::MessageAllocator< ::metrics_aggregator::GetLaserChangeTimesRequest, ::carbon::metrics::LaserChangeTimes>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetLaserChangeTimesRequest, ::carbon::metrics::LaserChangeTimes>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetLaserChangeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserChangeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserChangeTimesRequest* /*request*/, ::carbon::metrics::LaserChangeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetLaserChangeTimes(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::GetLaserChangeTimesRequest* /*request*/, ::carbon::metrics::LaserChangeTimes* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_RegisterSpatialClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_RegisterSpatialClient() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::RegisterSpatialClientRequest, ::metrics_aggregator::RegisterSpatialClientResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::RegisterSpatialClientRequest* request, ::metrics_aggregator::RegisterSpatialClientResponse* response) { return this->RegisterSpatialClient(context, request, response); }));}
    void SetMessageAllocatorFor_RegisterSpatialClient(
        ::grpc::MessageAllocator< ::metrics_aggregator::RegisterSpatialClientRequest, ::metrics_aggregator::RegisterSpatialClientResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::RegisterSpatialClientRequest, ::metrics_aggregator::RegisterSpatialClientResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_RegisterSpatialClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterSpatialClient(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::RegisterSpatialClientRequest* /*request*/, ::metrics_aggregator::RegisterSpatialClientResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RegisterSpatialClient(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::RegisterSpatialClientRequest* /*request*/, ::metrics_aggregator::RegisterSpatialClientResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SpatialClientBeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SpatialClientBeat() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::SpatialClientBeatRequest, ::metrics_aggregator::SpatialClientBeatResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::SpatialClientBeatRequest* request, ::metrics_aggregator::SpatialClientBeatResponse* response) { return this->SpatialClientBeat(context, request, response); }));}
    void SetMessageAllocatorFor_SpatialClientBeat(
        ::grpc::MessageAllocator< ::metrics_aggregator::SpatialClientBeatRequest, ::metrics_aggregator::SpatialClientBeatResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::SpatialClientBeatRequest, ::metrics_aggregator::SpatialClientBeatResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SpatialClientBeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientBeat(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientBeatRequest* /*request*/, ::metrics_aggregator::SpatialClientBeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SpatialClientBeat(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::SpatialClientBeatRequest* /*request*/, ::metrics_aggregator::SpatialClientBeatResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SpatialClientAck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SpatialClientAck() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::SpatialClientAckRequest, ::metrics_aggregator::SpatialClientAckResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::SpatialClientAckRequest* request, ::metrics_aggregator::SpatialClientAckResponse* response) { return this->SpatialClientAck(context, request, response); }));}
    void SetMessageAllocatorFor_SpatialClientAck(
        ::grpc::MessageAllocator< ::metrics_aggregator::SpatialClientAckRequest, ::metrics_aggregator::SpatialClientAckResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::SpatialClientAckRequest, ::metrics_aggregator::SpatialClientAckResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SpatialClientAck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientAck(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientAckRequest* /*request*/, ::metrics_aggregator::SpatialClientAckResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SpatialClientAck(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::SpatialClientAckRequest* /*request*/, ::metrics_aggregator::SpatialClientAckResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextSpatialBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextSpatialBlock() {
      ::grpc::Service::MarkMethodCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetNextSpatialBlockRequest, ::metrics_aggregator::GetNextSpatialBlockResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::metrics_aggregator::GetNextSpatialBlockRequest* request, ::metrics_aggregator::GetNextSpatialBlockResponse* response) { return this->GetNextSpatialBlock(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextSpatialBlock(
        ::grpc::MessageAllocator< ::metrics_aggregator::GetNextSpatialBlockRequest, ::metrics_aggregator::GetNextSpatialBlockResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::metrics_aggregator::GetNextSpatialBlockRequest, ::metrics_aggregator::GetNextSpatialBlockResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextSpatialBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSpatialBlock(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetNextSpatialBlockRequest* /*request*/, ::metrics_aggregator::GetNextSpatialBlockResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextSpatialBlock(
      ::grpc::CallbackServerContext* /*context*/, const ::metrics_aggregator::GetNextSpatialBlockRequest* /*request*/, ::metrics_aggregator::GetNextSpatialBlockResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Ping<WithCallbackMethod_GetMetrics<WithCallbackMethod_AcknowledgeDailyMetric<WithCallbackMethod_GetJobMetrics<WithCallbackMethod_GetLaserLifeTimes<WithCallbackMethod_SetLaser<WithCallbackMethod_OverrideLaser<WithCallbackMethod_GetLaserChangeTimes<WithCallbackMethod_RegisterSpatialClient<WithCallbackMethod_SpatialClientBeat<WithCallbackMethod_SpatialClientAck<WithCallbackMethod_GetNextSpatialBlock<Service > > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Ping() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::PingRequest* /*request*/, ::metrics_aggregator::PingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetMetrics() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetMetricsRequest* /*request*/, ::metrics_aggregator::GetMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_AcknowledgeDailyMetric : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_AcknowledgeDailyMetric() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_AcknowledgeDailyMetric() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeDailyMetric(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* /*request*/, ::metrics_aggregator::AcknowledgeDailyMetricResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetJobMetrics() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_GetJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJobMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetJobMetricsRequest* /*request*/, ::metrics_aggregator::GetJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetLaserLifeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetLaserLifeTimes() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_GetLaserLifeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserLifeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserLifeTimesRequest* /*request*/, ::carbon::metrics::LaserLifeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetLaser() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_SetLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaser(::grpc::ServerContext* /*context*/, const ::carbon::metrics::LaserIdentifier* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_OverrideLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_OverrideLaser() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_OverrideLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OverrideLaser(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::OverrideLaserRequest* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetLaserChangeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetLaserChangeTimes() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_GetLaserChangeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserChangeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserChangeTimesRequest* /*request*/, ::carbon::metrics::LaserChangeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_RegisterSpatialClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_RegisterSpatialClient() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_RegisterSpatialClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterSpatialClient(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::RegisterSpatialClientRequest* /*request*/, ::metrics_aggregator::RegisterSpatialClientResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SpatialClientBeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SpatialClientBeat() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_SpatialClientBeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientBeat(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientBeatRequest* /*request*/, ::metrics_aggregator::SpatialClientBeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SpatialClientAck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SpatialClientAck() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_SpatialClientAck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientAck(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientAckRequest* /*request*/, ::metrics_aggregator::SpatialClientAckResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextSpatialBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextSpatialBlock() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_GetNextSpatialBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSpatialBlock(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetNextSpatialBlockRequest* /*request*/, ::metrics_aggregator::GetNextSpatialBlockResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Ping() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::PingRequest* /*request*/, ::metrics_aggregator::PingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetMetrics() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetMetricsRequest* /*request*/, ::metrics_aggregator::GetMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetMetrics(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_AcknowledgeDailyMetric : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_AcknowledgeDailyMetric() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_AcknowledgeDailyMetric() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeDailyMetric(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* /*request*/, ::metrics_aggregator::AcknowledgeDailyMetricResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAcknowledgeDailyMetric(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetJobMetrics() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_GetJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJobMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetJobMetricsRequest* /*request*/, ::metrics_aggregator::GetJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetJobMetrics(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetLaserLifeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetLaserLifeTimes() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_GetLaserLifeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserLifeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserLifeTimesRequest* /*request*/, ::carbon::metrics::LaserLifeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetLaserLifeTimes(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetLaser() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_SetLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaser(::grpc::ServerContext* /*context*/, const ::carbon::metrics::LaserIdentifier* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLaser(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_OverrideLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_OverrideLaser() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_OverrideLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OverrideLaser(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::OverrideLaserRequest* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestOverrideLaser(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetLaserChangeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetLaserChangeTimes() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_GetLaserChangeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserChangeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserChangeTimesRequest* /*request*/, ::carbon::metrics::LaserChangeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetLaserChangeTimes(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_RegisterSpatialClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_RegisterSpatialClient() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_RegisterSpatialClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterSpatialClient(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::RegisterSpatialClientRequest* /*request*/, ::metrics_aggregator::RegisterSpatialClientResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRegisterSpatialClient(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SpatialClientBeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SpatialClientBeat() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_SpatialClientBeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientBeat(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientBeatRequest* /*request*/, ::metrics_aggregator::SpatialClientBeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSpatialClientBeat(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SpatialClientAck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SpatialClientAck() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_SpatialClientAck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientAck(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientAckRequest* /*request*/, ::metrics_aggregator::SpatialClientAckResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSpatialClientAck(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextSpatialBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextSpatialBlock() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_GetNextSpatialBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSpatialBlock(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetNextSpatialBlockRequest* /*request*/, ::metrics_aggregator::GetNextSpatialBlockResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextSpatialBlock(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Ping(context, request, response); }));
    }
    ~WithRawCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::PingRequest* /*request*/, ::metrics_aggregator::PingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetMetrics() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetMetrics(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetMetricsRequest* /*request*/, ::metrics_aggregator::GetMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_AcknowledgeDailyMetric : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_AcknowledgeDailyMetric() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->AcknowledgeDailyMetric(context, request, response); }));
    }
    ~WithRawCallbackMethod_AcknowledgeDailyMetric() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeDailyMetric(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* /*request*/, ::metrics_aggregator::AcknowledgeDailyMetricResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AcknowledgeDailyMetric(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetJobMetrics() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetJobMetrics(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJobMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetJobMetricsRequest* /*request*/, ::metrics_aggregator::GetJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetJobMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetLaserLifeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetLaserLifeTimes() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetLaserLifeTimes(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetLaserLifeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserLifeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserLifeTimesRequest* /*request*/, ::carbon::metrics::LaserLifeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetLaserLifeTimes(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetLaser() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetLaser(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaser(::grpc::ServerContext* /*context*/, const ::carbon::metrics::LaserIdentifier* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLaser(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_OverrideLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_OverrideLaser() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->OverrideLaser(context, request, response); }));
    }
    ~WithRawCallbackMethod_OverrideLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status OverrideLaser(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::OverrideLaserRequest* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* OverrideLaser(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetLaserChangeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetLaserChangeTimes() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetLaserChangeTimes(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetLaserChangeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLaserChangeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserChangeTimesRequest* /*request*/, ::carbon::metrics::LaserChangeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetLaserChangeTimes(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_RegisterSpatialClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_RegisterSpatialClient() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->RegisterSpatialClient(context, request, response); }));
    }
    ~WithRawCallbackMethod_RegisterSpatialClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RegisterSpatialClient(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::RegisterSpatialClientRequest* /*request*/, ::metrics_aggregator::RegisterSpatialClientResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RegisterSpatialClient(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SpatialClientBeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SpatialClientBeat() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SpatialClientBeat(context, request, response); }));
    }
    ~WithRawCallbackMethod_SpatialClientBeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientBeat(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientBeatRequest* /*request*/, ::metrics_aggregator::SpatialClientBeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SpatialClientBeat(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SpatialClientAck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SpatialClientAck() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SpatialClientAck(context, request, response); }));
    }
    ~WithRawCallbackMethod_SpatialClientAck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SpatialClientAck(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientAckRequest* /*request*/, ::metrics_aggregator::SpatialClientAckResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SpatialClientAck(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextSpatialBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextSpatialBlock() {
      ::grpc::Service::MarkMethodRawCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextSpatialBlock(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextSpatialBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSpatialBlock(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetNextSpatialBlockRequest* /*request*/, ::metrics_aggregator::GetNextSpatialBlockResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextSpatialBlock(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Ping() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::PingRequest, ::metrics_aggregator::PingResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::PingRequest, ::metrics_aggregator::PingResponse>* streamer) {
                       return this->StreamedPing(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::PingRequest* /*request*/, ::metrics_aggregator::PingResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPing(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::PingRequest,::metrics_aggregator::PingResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetMetrics() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::GetMetricsRequest, ::metrics_aggregator::GetMetricsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::GetMetricsRequest, ::metrics_aggregator::GetMetricsResponse>* streamer) {
                       return this->StreamedGetMetrics(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetMetricsRequest* /*request*/, ::metrics_aggregator::GetMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetMetrics(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::GetMetricsRequest,::metrics_aggregator::GetMetricsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_AcknowledgeDailyMetric : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_AcknowledgeDailyMetric() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::AcknowledgeDailyMetricRequest, ::metrics_aggregator::AcknowledgeDailyMetricResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::AcknowledgeDailyMetricRequest, ::metrics_aggregator::AcknowledgeDailyMetricResponse>* streamer) {
                       return this->StreamedAcknowledgeDailyMetric(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_AcknowledgeDailyMetric() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status AcknowledgeDailyMetric(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::AcknowledgeDailyMetricRequest* /*request*/, ::metrics_aggregator::AcknowledgeDailyMetricResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedAcknowledgeDailyMetric(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::AcknowledgeDailyMetricRequest,::metrics_aggregator::AcknowledgeDailyMetricResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetJobMetrics() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::GetJobMetricsRequest, ::metrics_aggregator::GetJobMetricsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::GetJobMetricsRequest, ::metrics_aggregator::GetJobMetricsResponse>* streamer) {
                       return this->StreamedGetJobMetrics(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetJobMetrics(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetJobMetricsRequest* /*request*/, ::metrics_aggregator::GetJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetJobMetrics(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::GetJobMetricsRequest,::metrics_aggregator::GetJobMetricsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetLaserLifeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetLaserLifeTimes() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::GetLaserLifeTimesRequest, ::carbon::metrics::LaserLifeTimes>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::GetLaserLifeTimesRequest, ::carbon::metrics::LaserLifeTimes>* streamer) {
                       return this->StreamedGetLaserLifeTimes(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetLaserLifeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetLaserLifeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserLifeTimesRequest* /*request*/, ::carbon::metrics::LaserLifeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetLaserLifeTimes(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::GetLaserLifeTimesRequest,::carbon::metrics::LaserLifeTimes>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetLaser() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::metrics::LaserIdentifier, ::metrics_aggregator::SetLaserResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::metrics::LaserIdentifier, ::metrics_aggregator::SetLaserResponse>* streamer) {
                       return this->StreamedSetLaser(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetLaser(::grpc::ServerContext* /*context*/, const ::carbon::metrics::LaserIdentifier* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetLaser(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::metrics::LaserIdentifier,::metrics_aggregator::SetLaserResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_OverrideLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_OverrideLaser() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::OverrideLaserRequest, ::metrics_aggregator::SetLaserResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::OverrideLaserRequest, ::metrics_aggregator::SetLaserResponse>* streamer) {
                       return this->StreamedOverrideLaser(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_OverrideLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status OverrideLaser(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::OverrideLaserRequest* /*request*/, ::metrics_aggregator::SetLaserResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedOverrideLaser(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::OverrideLaserRequest,::metrics_aggregator::SetLaserResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetLaserChangeTimes : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetLaserChangeTimes() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::GetLaserChangeTimesRequest, ::carbon::metrics::LaserChangeTimes>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::GetLaserChangeTimesRequest, ::carbon::metrics::LaserChangeTimes>* streamer) {
                       return this->StreamedGetLaserChangeTimes(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetLaserChangeTimes() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetLaserChangeTimes(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetLaserChangeTimesRequest* /*request*/, ::carbon::metrics::LaserChangeTimes* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetLaserChangeTimes(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::GetLaserChangeTimesRequest,::carbon::metrics::LaserChangeTimes>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_RegisterSpatialClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_RegisterSpatialClient() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::RegisterSpatialClientRequest, ::metrics_aggregator::RegisterSpatialClientResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::RegisterSpatialClientRequest, ::metrics_aggregator::RegisterSpatialClientResponse>* streamer) {
                       return this->StreamedRegisterSpatialClient(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_RegisterSpatialClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status RegisterSpatialClient(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::RegisterSpatialClientRequest* /*request*/, ::metrics_aggregator::RegisterSpatialClientResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedRegisterSpatialClient(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::RegisterSpatialClientRequest,::metrics_aggregator::RegisterSpatialClientResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SpatialClientBeat : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SpatialClientBeat() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::SpatialClientBeatRequest, ::metrics_aggregator::SpatialClientBeatResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::SpatialClientBeatRequest, ::metrics_aggregator::SpatialClientBeatResponse>* streamer) {
                       return this->StreamedSpatialClientBeat(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SpatialClientBeat() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SpatialClientBeat(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientBeatRequest* /*request*/, ::metrics_aggregator::SpatialClientBeatResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSpatialClientBeat(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::SpatialClientBeatRequest,::metrics_aggregator::SpatialClientBeatResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SpatialClientAck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SpatialClientAck() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::SpatialClientAckRequest, ::metrics_aggregator::SpatialClientAckResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::SpatialClientAckRequest, ::metrics_aggregator::SpatialClientAckResponse>* streamer) {
                       return this->StreamedSpatialClientAck(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SpatialClientAck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SpatialClientAck(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::SpatialClientAckRequest* /*request*/, ::metrics_aggregator::SpatialClientAckResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSpatialClientAck(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::SpatialClientAckRequest,::metrics_aggregator::SpatialClientAckResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextSpatialBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextSpatialBlock() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler<
          ::metrics_aggregator::GetNextSpatialBlockRequest, ::metrics_aggregator::GetNextSpatialBlockResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::metrics_aggregator::GetNextSpatialBlockRequest, ::metrics_aggregator::GetNextSpatialBlockResponse>* streamer) {
                       return this->StreamedGetNextSpatialBlock(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextSpatialBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextSpatialBlock(::grpc::ServerContext* /*context*/, const ::metrics_aggregator::GetNextSpatialBlockRequest* /*request*/, ::metrics_aggregator::GetNextSpatialBlockResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextSpatialBlock(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::metrics_aggregator::GetNextSpatialBlockRequest,::metrics_aggregator::GetNextSpatialBlockResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_GetMetrics<WithStreamedUnaryMethod_AcknowledgeDailyMetric<WithStreamedUnaryMethod_GetJobMetrics<WithStreamedUnaryMethod_GetLaserLifeTimes<WithStreamedUnaryMethod_SetLaser<WithStreamedUnaryMethod_OverrideLaser<WithStreamedUnaryMethod_GetLaserChangeTimes<WithStreamedUnaryMethod_RegisterSpatialClient<WithStreamedUnaryMethod_SpatialClientBeat<WithStreamedUnaryMethod_SpatialClientAck<WithStreamedUnaryMethod_GetNextSpatialBlock<Service > > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_GetMetrics<WithStreamedUnaryMethod_AcknowledgeDailyMetric<WithStreamedUnaryMethod_GetJobMetrics<WithStreamedUnaryMethod_GetLaserLifeTimes<WithStreamedUnaryMethod_SetLaser<WithStreamedUnaryMethod_OverrideLaser<WithStreamedUnaryMethod_GetLaserChangeTimes<WithStreamedUnaryMethod_RegisterSpatialClient<WithStreamedUnaryMethod_SpatialClientBeat<WithStreamedUnaryMethod_SpatialClientAck<WithStreamedUnaryMethod_GetNextSpatialBlock<Service > > > > > > > > > > > > StreamedService;
};

}  // namespace metrics_aggregator


#endif  // GRPC_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto__INCLUDED

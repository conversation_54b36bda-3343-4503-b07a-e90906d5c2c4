# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lib/common/buffer/buffer.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.common.buffer.inline import inline_pb2 as lib_dot_common_dot_buffer_dot_inline_dot_inline__pb2
from generated.lib.common.buffer.shm import shm_pb2 as lib_dot_common_dot_buffer_dot_shm_dot_shm__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='lib/common/buffer/buffer.proto',
  package='lib.common.buffer',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1elib/common/buffer/buffer.proto\x12\x11lib.common.buffer\x1a%lib/common/buffer/inline/inline.proto\x1a\x1flib/common/buffer/shm/shm.proto\"\xb7\x01\n\x0b\x42ufferProto\x12\x36\n\x06inline\x18\x01 \x01(\x0b\x32$.lib.common.buffer.InlineBufferProtoH\x00\x12\x32\n\x07memfile\x18\x02 \x01(\x0b\x32\x1f.lib.common.buffer.MemFileProtoH\x00\x12\x34\n\x05shmem\x18\x03 \x01(\x0b\x32#.lib.common.buffer.ShmemBufferProtoH\x00\x42\x06\n\x04typeb\x06proto3'
  ,
  dependencies=[lib_dot_common_dot_buffer_dot_inline_dot_inline__pb2.DESCRIPTOR,lib_dot_common_dot_buffer_dot_shm_dot_shm__pb2.DESCRIPTOR,])




_BUFFERPROTO = _descriptor.Descriptor(
  name='BufferProto',
  full_name='lib.common.buffer.BufferProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='inline', full_name='lib.common.buffer.BufferProto.inline', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='memfile', full_name='lib.common.buffer.BufferProto.memfile', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='shmem', full_name='lib.common.buffer.BufferProto.shmem', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='type', full_name='lib.common.buffer.BufferProto.type',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=126,
  serialized_end=309,
)

_BUFFERPROTO.fields_by_name['inline'].message_type = lib_dot_common_dot_buffer_dot_inline_dot_inline__pb2._INLINEBUFFERPROTO
_BUFFERPROTO.fields_by_name['memfile'].message_type = lib_dot_common_dot_buffer_dot_shm_dot_shm__pb2._MEMFILEPROTO
_BUFFERPROTO.fields_by_name['shmem'].message_type = lib_dot_common_dot_buffer_dot_shm_dot_shm__pb2._SHMEMBUFFERPROTO
_BUFFERPROTO.oneofs_by_name['type'].fields.append(
  _BUFFERPROTO.fields_by_name['inline'])
_BUFFERPROTO.fields_by_name['inline'].containing_oneof = _BUFFERPROTO.oneofs_by_name['type']
_BUFFERPROTO.oneofs_by_name['type'].fields.append(
  _BUFFERPROTO.fields_by_name['memfile'])
_BUFFERPROTO.fields_by_name['memfile'].containing_oneof = _BUFFERPROTO.oneofs_by_name['type']
_BUFFERPROTO.oneofs_by_name['type'].fields.append(
  _BUFFERPROTO.fields_by_name['shmem'])
_BUFFERPROTO.fields_by_name['shmem'].containing_oneof = _BUFFERPROTO.oneofs_by_name['type']
DESCRIPTOR.message_types_by_name['BufferProto'] = _BUFFERPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

BufferProto = _reflection.GeneratedProtocolMessageType('BufferProto', (_message.Message,), {
  'DESCRIPTOR' : _BUFFERPROTO,
  '__module__' : 'lib.common.buffer.buffer_pb2'
  # @@protoc_insertion_point(class_scope:lib.common.buffer.BufferProto)
  })
_sym_db.RegisterMessage(BufferProto)


# @@protoc_insertion_point(module_scope)

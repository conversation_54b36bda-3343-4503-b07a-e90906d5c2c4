"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.lib.common.buffer.inline.inline_pb2 import (
    InlineBufferProto as lib___common___buffer___inline___inline_pb2___InlineBufferProto,
)

from generated.lib.common.buffer.shm.shm_pb2 import (
    MemFileProto as lib___common___buffer___shm___shm_pb2___MemFileProto,
    ShmemBufferProto as lib___common___buffer___shm___shm_pb2___ShmemBufferProto,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class BufferProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def inline(self) -> lib___common___buffer___inline___inline_pb2___InlineBufferProto: ...

    @property
    def memfile(self) -> lib___common___buffer___shm___shm_pb2___MemFileProto: ...

    @property
    def shmem(self) -> lib___common___buffer___shm___shm_pb2___ShmemBufferProto: ...

    def __init__(self,
        *,
        inline : typing___Optional[lib___common___buffer___inline___inline_pb2___InlineBufferProto] = None,
        memfile : typing___Optional[lib___common___buffer___shm___shm_pb2___MemFileProto] = None,
        shmem : typing___Optional[lib___common___buffer___shm___shm_pb2___ShmemBufferProto] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"inline",b"inline",u"memfile",b"memfile",u"shmem",b"shmem",u"type",b"type"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"inline",b"inline",u"memfile",b"memfile",u"shmem",b"shmem",u"type",b"type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"type",b"type"]) -> typing_extensions___Literal["inline","memfile","shmem"]: ...
type___BufferProto = BufferProto

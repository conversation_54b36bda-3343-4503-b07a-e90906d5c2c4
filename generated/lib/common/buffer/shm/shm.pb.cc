// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/common/buffer/shm/shm.proto

#include "lib/common/buffer/shm/shm.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace lib {
namespace common {
namespace buffer {
constexpr MemFileProto::MemFileProto(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : path_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct MemFileProtoDefaultTypeInternal {
  constexpr MemFileProtoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MemFileProtoDefaultTypeInternal() {}
  union {
    MemFileProto _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MemFileProtoDefaultTypeInternal _MemFileProto_default_instance_;
constexpr ShmemBufferProto::ShmemBufferProto(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : path_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ShmemBufferProtoDefaultTypeInternal {
  constexpr ShmemBufferProtoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ShmemBufferProtoDefaultTypeInternal() {}
  union {
    ShmemBufferProto _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ShmemBufferProtoDefaultTypeInternal _ShmemBufferProto_default_instance_;
}  // namespace buffer
}  // namespace common
}  // namespace lib
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto = nullptr;

const uint32_t TableStruct_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::MemFileProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::MemFileProto, path_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::ShmemBufferProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::ShmemBufferProto, path_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::lib::common::buffer::MemFileProto)},
  { 7, -1, -1, sizeof(::lib::common::buffer::ShmemBufferProto)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::lib::common::buffer::_MemFileProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::lib::common::buffer::_ShmemBufferProto_default_instance_),
};

const char descriptor_table_protodef_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\037lib/common/buffer/shm/shm.proto\022\021lib.c"
  "ommon.buffer\"\034\n\014MemFileProto\022\014\n\004path\030\001 \001"
  "(\t\" \n\020ShmemBufferProto\022\014\n\004path\030\001 \001(\tb\006pr"
  "oto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto = {
  false, false, 124, descriptor_table_protodef_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto, "lib/common/buffer/shm/shm.proto", 
  &descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto_once, nullptr, 0, 2,
  schemas, file_default_instances, TableStruct_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto::offsets,
  file_level_metadata_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto, file_level_enum_descriptors_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto, file_level_service_descriptors_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto_getter() {
  return &descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto(&descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto);
namespace lib {
namespace common {
namespace buffer {

// ===================================================================

class MemFileProto::_Internal {
 public:
};

MemFileProto::MemFileProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:lib.common.buffer.MemFileProto)
}
MemFileProto::MemFileProto(const MemFileProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_path().empty()) {
    path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_path(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:lib.common.buffer.MemFileProto)
}

inline void MemFileProto::SharedCtor() {
path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

MemFileProto::~MemFileProto() {
  // @@protoc_insertion_point(destructor:lib.common.buffer.MemFileProto)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MemFileProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MemFileProto::ArenaDtor(void* object) {
  MemFileProto* _this = reinterpret_cast< MemFileProto* >(object);
  (void)_this;
}
void MemFileProto::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MemFileProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MemFileProto::Clear() {
// @@protoc_insertion_point(message_clear_start:lib.common.buffer.MemFileProto)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  path_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MemFileProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "lib.common.buffer.MemFileProto.path"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MemFileProto::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:lib.common.buffer.MemFileProto)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string path = 1;
  if (!this->_internal_path().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_path().data(), static_cast<int>(this->_internal_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "lib.common.buffer.MemFileProto.path");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_path(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:lib.common.buffer.MemFileProto)
  return target;
}

size_t MemFileProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:lib.common.buffer.MemFileProto)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string path = 1;
  if (!this->_internal_path().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_path());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MemFileProto::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MemFileProto::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MemFileProto::GetClassData() const { return &_class_data_; }

void MemFileProto::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MemFileProto *>(to)->MergeFrom(
      static_cast<const MemFileProto &>(from));
}


void MemFileProto::MergeFrom(const MemFileProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:lib.common.buffer.MemFileProto)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_path().empty()) {
    _internal_set_path(from._internal_path());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MemFileProto::CopyFrom(const MemFileProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:lib.common.buffer.MemFileProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MemFileProto::IsInitialized() const {
  return true;
}

void MemFileProto::InternalSwap(MemFileProto* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &path_, lhs_arena,
      &other->path_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata MemFileProto::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto_getter, &descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto_once,
      file_level_metadata_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto[0]);
}

// ===================================================================

class ShmemBufferProto::_Internal {
 public:
};

ShmemBufferProto::ShmemBufferProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:lib.common.buffer.ShmemBufferProto)
}
ShmemBufferProto::ShmemBufferProto(const ShmemBufferProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_path().empty()) {
    path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_path(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:lib.common.buffer.ShmemBufferProto)
}

inline void ShmemBufferProto::SharedCtor() {
path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ShmemBufferProto::~ShmemBufferProto() {
  // @@protoc_insertion_point(destructor:lib.common.buffer.ShmemBufferProto)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ShmemBufferProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ShmemBufferProto::ArenaDtor(void* object) {
  ShmemBufferProto* _this = reinterpret_cast< ShmemBufferProto* >(object);
  (void)_this;
}
void ShmemBufferProto::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ShmemBufferProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ShmemBufferProto::Clear() {
// @@protoc_insertion_point(message_clear_start:lib.common.buffer.ShmemBufferProto)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  path_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ShmemBufferProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "lib.common.buffer.ShmemBufferProto.path"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ShmemBufferProto::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:lib.common.buffer.ShmemBufferProto)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string path = 1;
  if (!this->_internal_path().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_path().data(), static_cast<int>(this->_internal_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "lib.common.buffer.ShmemBufferProto.path");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_path(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:lib.common.buffer.ShmemBufferProto)
  return target;
}

size_t ShmemBufferProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:lib.common.buffer.ShmemBufferProto)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string path = 1;
  if (!this->_internal_path().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_path());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ShmemBufferProto::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ShmemBufferProto::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ShmemBufferProto::GetClassData() const { return &_class_data_; }

void ShmemBufferProto::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ShmemBufferProto *>(to)->MergeFrom(
      static_cast<const ShmemBufferProto &>(from));
}


void ShmemBufferProto::MergeFrom(const ShmemBufferProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:lib.common.buffer.ShmemBufferProto)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_path().empty()) {
    _internal_set_path(from._internal_path());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ShmemBufferProto::CopyFrom(const ShmemBufferProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:lib.common.buffer.ShmemBufferProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ShmemBufferProto::IsInitialized() const {
  return true;
}

void ShmemBufferProto::InternalSwap(ShmemBufferProto* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &path_, lhs_arena,
      &other->path_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ShmemBufferProto::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto_getter, &descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto_once,
      file_level_metadata_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace buffer
}  // namespace common
}  // namespace lib
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::lib::common::buffer::MemFileProto* Arena::CreateMaybeMessage< ::lib::common::buffer::MemFileProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::lib::common::buffer::MemFileProto >(arena);
}
template<> PROTOBUF_NOINLINE ::lib::common::buffer::ShmemBufferProto* Arena::CreateMaybeMessage< ::lib::common::buffer::ShmemBufferProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::lib::common::buffer::ShmemBufferProto >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

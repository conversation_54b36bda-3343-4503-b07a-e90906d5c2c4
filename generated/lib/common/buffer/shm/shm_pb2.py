# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lib/common/buffer/shm/shm.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='lib/common/buffer/shm/shm.proto',
  package='lib.common.buffer',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1flib/common/buffer/shm/shm.proto\x12\x11lib.common.buffer\"\x1c\n\x0cMemFileProto\x12\x0c\n\x04path\x18\x01 \x01(\t\" \n\x10ShmemBufferProto\x12\x0c\n\x04path\x18\x01 \x01(\tb\x06proto3'
)




_MEMFILEPROTO = _descriptor.Descriptor(
  name='MemFileProto',
  full_name='lib.common.buffer.MemFileProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='path', full_name='lib.common.buffer.MemFileProto.path', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=54,
  serialized_end=82,
)


_SHMEMBUFFERPROTO = _descriptor.Descriptor(
  name='ShmemBufferProto',
  full_name='lib.common.buffer.ShmemBufferProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='path', full_name='lib.common.buffer.ShmemBufferProto.path', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=84,
  serialized_end=116,
)

DESCRIPTOR.message_types_by_name['MemFileProto'] = _MEMFILEPROTO
DESCRIPTOR.message_types_by_name['ShmemBufferProto'] = _SHMEMBUFFERPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

MemFileProto = _reflection.GeneratedProtocolMessageType('MemFileProto', (_message.Message,), {
  'DESCRIPTOR' : _MEMFILEPROTO,
  '__module__' : 'lib.common.buffer.shm.shm_pb2'
  # @@protoc_insertion_point(class_scope:lib.common.buffer.MemFileProto)
  })
_sym_db.RegisterMessage(MemFileProto)

ShmemBufferProto = _reflection.GeneratedProtocolMessageType('ShmemBufferProto', (_message.Message,), {
  'DESCRIPTOR' : _SHMEMBUFFERPROTO,
  '__module__' : 'lib.common.buffer.shm.shm_pb2'
  # @@protoc_insertion_point(class_scope:lib.common.buffer.ShmemBufferProto)
  })
_sym_db.RegisterMessage(ShmemBufferProto)


# @@protoc_insertion_point(module_scope)

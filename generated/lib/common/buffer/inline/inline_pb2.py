# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lib/common/buffer/inline/inline.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='lib/common/buffer/inline/inline.proto',
  package='lib.common.buffer',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n%lib/common/buffer/inline/inline.proto\x12\x11lib.common.buffer\"4\n\x11InlineBufferProto\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x12\x11\n\tnot_empty\x18\x02 \x01(\x08\x62\x06proto3'
)




_INLINEBUFFERPROTO = _descriptor.Descriptor(
  name='InlineBufferProto',
  full_name='lib.common.buffer.InlineBufferProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='lib.common.buffer.InlineBufferProto.data', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='not_empty', full_name='lib.common.buffer.InlineBufferProto.not_empty', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=60,
  serialized_end=112,
)

DESCRIPTOR.message_types_by_name['InlineBufferProto'] = _INLINEBUFFERPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

InlineBufferProto = _reflection.GeneratedProtocolMessageType('InlineBufferProto', (_message.Message,), {
  'DESCRIPTOR' : _INLINEBUFFERPROTO,
  '__module__' : 'lib.common.buffer.inline.inline_pb2'
  # @@protoc_insertion_point(class_scope:lib.common.buffer.InlineBufferProto)
  })
_sym_db.RegisterMessage(InlineBufferProto)


# @@protoc_insertion_point(module_scope)

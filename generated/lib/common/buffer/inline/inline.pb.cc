// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/common/buffer/inline/inline.proto

#include "lib/common/buffer/inline/inline.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace lib {
namespace common {
namespace buffer {
constexpr InlineBufferProto::InlineBufferProto(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : data_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , not_empty_(false){}
struct InlineBufferProtoDefaultTypeInternal {
  constexpr InlineBufferProtoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~InlineBufferProtoDefaultTypeInternal() {}
  union {
    InlineBufferProto _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT InlineBufferProtoDefaultTypeInternal _InlineBufferProto_default_instance_;
}  // namespace buffer
}  // namespace common
}  // namespace lib
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_lib_2fcommon_2fbuffer_2finline_2finline_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_lib_2fcommon_2fbuffer_2finline_2finline_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_lib_2fcommon_2fbuffer_2finline_2finline_2eproto = nullptr;

const uint32_t TableStruct_lib_2fcommon_2fbuffer_2finline_2finline_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::InlineBufferProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::InlineBufferProto, data_),
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::InlineBufferProto, not_empty_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::lib::common::buffer::InlineBufferProto)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::lib::common::buffer::_InlineBufferProto_default_instance_),
};

const char descriptor_table_protodef_lib_2fcommon_2fbuffer_2finline_2finline_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n%lib/common/buffer/inline/inline.proto\022"
  "\021lib.common.buffer\"4\n\021InlineBufferProto\022"
  "\014\n\004data\030\001 \001(\014\022\021\n\tnot_empty\030\002 \001(\010b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_lib_2fcommon_2fbuffer_2finline_2finline_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fcommon_2fbuffer_2finline_2finline_2eproto = {
  false, false, 120, descriptor_table_protodef_lib_2fcommon_2fbuffer_2finline_2finline_2eproto, "lib/common/buffer/inline/inline.proto", 
  &descriptor_table_lib_2fcommon_2fbuffer_2finline_2finline_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_lib_2fcommon_2fbuffer_2finline_2finline_2eproto::offsets,
  file_level_metadata_lib_2fcommon_2fbuffer_2finline_2finline_2eproto, file_level_enum_descriptors_lib_2fcommon_2fbuffer_2finline_2finline_2eproto, file_level_service_descriptors_lib_2fcommon_2fbuffer_2finline_2finline_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_lib_2fcommon_2fbuffer_2finline_2finline_2eproto_getter() {
  return &descriptor_table_lib_2fcommon_2fbuffer_2finline_2finline_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_lib_2fcommon_2fbuffer_2finline_2finline_2eproto(&descriptor_table_lib_2fcommon_2fbuffer_2finline_2finline_2eproto);
namespace lib {
namespace common {
namespace buffer {

// ===================================================================

class InlineBufferProto::_Internal {
 public:
};

InlineBufferProto::InlineBufferProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:lib.common.buffer.InlineBufferProto)
}
InlineBufferProto::InlineBufferProto(const InlineBufferProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArenaForAllocation());
  }
  not_empty_ = from.not_empty_;
  // @@protoc_insertion_point(copy_constructor:lib.common.buffer.InlineBufferProto)
}

inline void InlineBufferProto::SharedCtor() {
data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
not_empty_ = false;
}

InlineBufferProto::~InlineBufferProto() {
  // @@protoc_insertion_point(destructor:lib.common.buffer.InlineBufferProto)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void InlineBufferProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void InlineBufferProto::ArenaDtor(void* object) {
  InlineBufferProto* _this = reinterpret_cast< InlineBufferProto* >(object);
  (void)_this;
}
void InlineBufferProto::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void InlineBufferProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void InlineBufferProto::Clear() {
// @@protoc_insertion_point(message_clear_start:lib.common.buffer.InlineBufferProto)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  data_.ClearToEmpty();
  not_empty_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* InlineBufferProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes data = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool not_empty = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          not_empty_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* InlineBufferProto::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:lib.common.buffer.InlineBufferProto)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes data = 1;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_data(), target);
  }

  // bool not_empty = 2;
  if (this->_internal_not_empty() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_not_empty(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:lib.common.buffer.InlineBufferProto)
  return target;
}

size_t InlineBufferProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:lib.common.buffer.InlineBufferProto)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes data = 1;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  // bool not_empty = 2;
  if (this->_internal_not_empty() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData InlineBufferProto::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    InlineBufferProto::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*InlineBufferProto::GetClassData() const { return &_class_data_; }

void InlineBufferProto::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<InlineBufferProto *>(to)->MergeFrom(
      static_cast<const InlineBufferProto &>(from));
}


void InlineBufferProto::MergeFrom(const InlineBufferProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:lib.common.buffer.InlineBufferProto)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _internal_set_data(from._internal_data());
  }
  if (from._internal_not_empty() != 0) {
    _internal_set_not_empty(from._internal_not_empty());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void InlineBufferProto::CopyFrom(const InlineBufferProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:lib.common.buffer.InlineBufferProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool InlineBufferProto::IsInitialized() const {
  return true;
}

void InlineBufferProto::InternalSwap(InlineBufferProto* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &data_, lhs_arena,
      &other->data_, rhs_arena
  );
  swap(not_empty_, other->not_empty_);
}

::PROTOBUF_NAMESPACE_ID::Metadata InlineBufferProto::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_lib_2fcommon_2fbuffer_2finline_2finline_2eproto_getter, &descriptor_table_lib_2fcommon_2fbuffer_2finline_2finline_2eproto_once,
      file_level_metadata_lib_2fcommon_2fbuffer_2finline_2finline_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace buffer
}  // namespace common
}  // namespace lib
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::lib::common::buffer::InlineBufferProto* Arena::CreateMaybeMessage< ::lib::common::buffer::InlineBufferProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::lib::common::buffer::InlineBufferProto >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

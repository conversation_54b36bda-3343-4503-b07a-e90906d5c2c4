// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/common/buffer/buffer.proto

#include "lib/common/buffer/buffer.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace lib {
namespace common {
namespace buffer {
constexpr BufferProto::BufferProto(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct BufferProtoDefaultTypeInternal {
  constexpr BufferProtoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BufferProtoDefaultTypeInternal() {}
  union {
    BufferProto _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BufferProtoDefaultTypeInternal _BufferProto_default_instance_;
}  // namespace buffer
}  // namespace common
}  // namespace lib
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_lib_2fcommon_2fbuffer_2fbuffer_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_lib_2fcommon_2fbuffer_2fbuffer_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_lib_2fcommon_2fbuffer_2fbuffer_2eproto = nullptr;

const uint32_t TableStruct_lib_2fcommon_2fbuffer_2fbuffer_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::BufferProto, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::BufferProto, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::lib::common::buffer::BufferProto, type_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::lib::common::buffer::BufferProto)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::lib::common::buffer::_BufferProto_default_instance_),
};

const char descriptor_table_protodef_lib_2fcommon_2fbuffer_2fbuffer_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\036lib/common/buffer/buffer.proto\022\021lib.co"
  "mmon.buffer\032%lib/common/buffer/inline/in"
  "line.proto\032\037lib/common/buffer/shm/shm.pr"
  "oto\"\267\001\n\013BufferProto\0226\n\006inline\030\001 \001(\0132$.li"
  "b.common.buffer.InlineBufferProtoH\000\0222\n\007m"
  "emfile\030\002 \001(\0132\037.lib.common.buffer.MemFile"
  "ProtoH\000\0224\n\005shmem\030\003 \001(\0132#.lib.common.buff"
  "er.ShmemBufferProtoH\000B\006\n\004typeb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto_deps[2] = {
  &::descriptor_table_lib_2fcommon_2fbuffer_2finline_2finline_2eproto,
  &::descriptor_table_lib_2fcommon_2fbuffer_2fshm_2fshm_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto = {
  false, false, 317, descriptor_table_protodef_lib_2fcommon_2fbuffer_2fbuffer_2eproto, "lib/common/buffer/buffer.proto", 
  &descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto_once, descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_lib_2fcommon_2fbuffer_2fbuffer_2eproto::offsets,
  file_level_metadata_lib_2fcommon_2fbuffer_2fbuffer_2eproto, file_level_enum_descriptors_lib_2fcommon_2fbuffer_2fbuffer_2eproto, file_level_service_descriptors_lib_2fcommon_2fbuffer_2fbuffer_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto_getter() {
  return &descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_lib_2fcommon_2fbuffer_2fbuffer_2eproto(&descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto);
namespace lib {
namespace common {
namespace buffer {

// ===================================================================

class BufferProto::_Internal {
 public:
  static const ::lib::common::buffer::InlineBufferProto& inline_(const BufferProto* msg);
  static const ::lib::common::buffer::MemFileProto& memfile(const BufferProto* msg);
  static const ::lib::common::buffer::ShmemBufferProto& shmem(const BufferProto* msg);
};

const ::lib::common::buffer::InlineBufferProto&
BufferProto::_Internal::inline_(const BufferProto* msg) {
  return *msg->type_.inline__;
}
const ::lib::common::buffer::MemFileProto&
BufferProto::_Internal::memfile(const BufferProto* msg) {
  return *msg->type_.memfile_;
}
const ::lib::common::buffer::ShmemBufferProto&
BufferProto::_Internal::shmem(const BufferProto* msg) {
  return *msg->type_.shmem_;
}
void BufferProto::set_allocated_inline_(::lib::common::buffer::InlineBufferProto* inline_) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_type();
  if (inline_) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(inline_));
    if (message_arena != submessage_arena) {
      inline_ = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, inline_, submessage_arena);
    }
    set_has_inline_();
    type_.inline__ = inline_;
  }
  // @@protoc_insertion_point(field_set_allocated:lib.common.buffer.BufferProto.inline)
}
void BufferProto::clear_inline_() {
  if (_internal_has_inline_()) {
    if (GetArenaForAllocation() == nullptr) {
      delete type_.inline__;
    }
    clear_has_type();
  }
}
void BufferProto::set_allocated_memfile(::lib::common::buffer::MemFileProto* memfile) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_type();
  if (memfile) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(memfile));
    if (message_arena != submessage_arena) {
      memfile = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, memfile, submessage_arena);
    }
    set_has_memfile();
    type_.memfile_ = memfile;
  }
  // @@protoc_insertion_point(field_set_allocated:lib.common.buffer.BufferProto.memfile)
}
void BufferProto::clear_memfile() {
  if (_internal_has_memfile()) {
    if (GetArenaForAllocation() == nullptr) {
      delete type_.memfile_;
    }
    clear_has_type();
  }
}
void BufferProto::set_allocated_shmem(::lib::common::buffer::ShmemBufferProto* shmem) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_type();
  if (shmem) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shmem));
    if (message_arena != submessage_arena) {
      shmem = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shmem, submessage_arena);
    }
    set_has_shmem();
    type_.shmem_ = shmem;
  }
  // @@protoc_insertion_point(field_set_allocated:lib.common.buffer.BufferProto.shmem)
}
void BufferProto::clear_shmem() {
  if (_internal_has_shmem()) {
    if (GetArenaForAllocation() == nullptr) {
      delete type_.shmem_;
    }
    clear_has_type();
  }
}
BufferProto::BufferProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:lib.common.buffer.BufferProto)
}
BufferProto::BufferProto(const BufferProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_type();
  switch (from.type_case()) {
    case kInline: {
      _internal_mutable_inline_()->::lib::common::buffer::InlineBufferProto::MergeFrom(from._internal_inline_());
      break;
    }
    case kMemfile: {
      _internal_mutable_memfile()->::lib::common::buffer::MemFileProto::MergeFrom(from._internal_memfile());
      break;
    }
    case kShmem: {
      _internal_mutable_shmem()->::lib::common::buffer::ShmemBufferProto::MergeFrom(from._internal_shmem());
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:lib.common.buffer.BufferProto)
}

inline void BufferProto::SharedCtor() {
clear_has_type();
}

BufferProto::~BufferProto() {
  // @@protoc_insertion_point(destructor:lib.common.buffer.BufferProto)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BufferProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_type()) {
    clear_type();
  }
}

void BufferProto::ArenaDtor(void* object) {
  BufferProto* _this = reinterpret_cast< BufferProto* >(object);
  (void)_this;
}
void BufferProto::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BufferProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BufferProto::clear_type() {
// @@protoc_insertion_point(one_of_clear_start:lib.common.buffer.BufferProto)
  switch (type_case()) {
    case kInline: {
      if (GetArenaForAllocation() == nullptr) {
        delete type_.inline__;
      }
      break;
    }
    case kMemfile: {
      if (GetArenaForAllocation() == nullptr) {
        delete type_.memfile_;
      }
      break;
    }
    case kShmem: {
      if (GetArenaForAllocation() == nullptr) {
        delete type_.shmem_;
      }
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = TYPE_NOT_SET;
}


void BufferProto::Clear() {
// @@protoc_insertion_point(message_clear_start:lib.common.buffer.BufferProto)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_type();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BufferProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .lib.common.buffer.InlineBufferProto inline = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_inline_(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .lib.common.buffer.MemFileProto memfile = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_memfile(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .lib.common.buffer.ShmemBufferProto shmem = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_shmem(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* BufferProto::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:lib.common.buffer.BufferProto)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .lib.common.buffer.InlineBufferProto inline = 1;
  if (_internal_has_inline_()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::inline_(this), target, stream);
  }

  // .lib.common.buffer.MemFileProto memfile = 2;
  if (_internal_has_memfile()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::memfile(this), target, stream);
  }

  // .lib.common.buffer.ShmemBufferProto shmem = 3;
  if (_internal_has_shmem()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::shmem(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:lib.common.buffer.BufferProto)
  return target;
}

size_t BufferProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:lib.common.buffer.BufferProto)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (type_case()) {
    // .lib.common.buffer.InlineBufferProto inline = 1;
    case kInline: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *type_.inline__);
      break;
    }
    // .lib.common.buffer.MemFileProto memfile = 2;
    case kMemfile: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *type_.memfile_);
      break;
    }
    // .lib.common.buffer.ShmemBufferProto shmem = 3;
    case kShmem: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *type_.shmem_);
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BufferProto::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BufferProto::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BufferProto::GetClassData() const { return &_class_data_; }

void BufferProto::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<BufferProto *>(to)->MergeFrom(
      static_cast<const BufferProto &>(from));
}


void BufferProto::MergeFrom(const BufferProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:lib.common.buffer.BufferProto)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.type_case()) {
    case kInline: {
      _internal_mutable_inline_()->::lib::common::buffer::InlineBufferProto::MergeFrom(from._internal_inline_());
      break;
    }
    case kMemfile: {
      _internal_mutable_memfile()->::lib::common::buffer::MemFileProto::MergeFrom(from._internal_memfile());
      break;
    }
    case kShmem: {
      _internal_mutable_shmem()->::lib::common::buffer::ShmemBufferProto::MergeFrom(from._internal_shmem());
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BufferProto::CopyFrom(const BufferProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:lib.common.buffer.BufferProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BufferProto::IsInitialized() const {
  return true;
}

void BufferProto::InternalSwap(BufferProto* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(type_, other->type_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata BufferProto::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto_getter, &descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto_once,
      file_level_metadata_lib_2fcommon_2fbuffer_2fbuffer_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace buffer
}  // namespace common
}  // namespace lib
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::lib::common::buffer::BufferProto* Arena::CreateMaybeMessage< ::lib::common::buffer::BufferProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::lib::common::buffer::BufferProto >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

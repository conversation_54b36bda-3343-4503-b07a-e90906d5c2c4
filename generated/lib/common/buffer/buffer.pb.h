// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/common/buffer/buffer.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_lib_2fcommon_2fbuffer_2fbuffer_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_lib_2fcommon_2fbuffer_2fbuffer_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "lib/common/buffer/inline/inline.pb.h"
#include "lib/common/buffer/shm/shm.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_lib_2fcommon_2fbuffer_2fbuffer_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_lib_2fcommon_2fbuffer_2fbuffer_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto;
namespace lib {
namespace common {
namespace buffer {
class BufferProto;
struct BufferProtoDefaultTypeInternal;
extern BufferProtoDefaultTypeInternal _BufferProto_default_instance_;
}  // namespace buffer
}  // namespace common
}  // namespace lib
PROTOBUF_NAMESPACE_OPEN
template<> ::lib::common::buffer::BufferProto* Arena::CreateMaybeMessage<::lib::common::buffer::BufferProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace lib {
namespace common {
namespace buffer {

// ===================================================================

class BufferProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:lib.common.buffer.BufferProto) */ {
 public:
  inline BufferProto() : BufferProto(nullptr) {}
  ~BufferProto() override;
  explicit constexpr BufferProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BufferProto(const BufferProto& from);
  BufferProto(BufferProto&& from) noexcept
    : BufferProto() {
    *this = ::std::move(from);
  }

  inline BufferProto& operator=(const BufferProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline BufferProto& operator=(BufferProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BufferProto& default_instance() {
    return *internal_default_instance();
  }
  enum TypeCase {
    kInline = 1,
    kMemfile = 2,
    kShmem = 3,
    TYPE_NOT_SET = 0,
  };

  static inline const BufferProto* internal_default_instance() {
    return reinterpret_cast<const BufferProto*>(
               &_BufferProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(BufferProto& a, BufferProto& b) {
    a.Swap(&b);
  }
  inline void Swap(BufferProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BufferProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BufferProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BufferProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BufferProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BufferProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BufferProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "lib.common.buffer.BufferProto";
  }
  protected:
  explicit BufferProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInlineFieldNumber = 1,
    kMemfileFieldNumber = 2,
    kShmemFieldNumber = 3,
  };
  // .lib.common.buffer.InlineBufferProto inline = 1;
  bool has_inline_() const;
  private:
  bool _internal_has_inline_() const;
  public:
  void clear_inline_();
  const ::lib::common::buffer::InlineBufferProto& inline_() const;
  PROTOBUF_NODISCARD ::lib::common::buffer::InlineBufferProto* release_inline_();
  ::lib::common::buffer::InlineBufferProto* mutable_inline_();
  void set_allocated_inline_(::lib::common::buffer::InlineBufferProto* inline_);
  private:
  const ::lib::common::buffer::InlineBufferProto& _internal_inline_() const;
  ::lib::common::buffer::InlineBufferProto* _internal_mutable_inline_();
  public:
  void unsafe_arena_set_allocated_inline_(
      ::lib::common::buffer::InlineBufferProto* inline_);
  ::lib::common::buffer::InlineBufferProto* unsafe_arena_release_inline_();

  // .lib.common.buffer.MemFileProto memfile = 2;
  bool has_memfile() const;
  private:
  bool _internal_has_memfile() const;
  public:
  void clear_memfile();
  const ::lib::common::buffer::MemFileProto& memfile() const;
  PROTOBUF_NODISCARD ::lib::common::buffer::MemFileProto* release_memfile();
  ::lib::common::buffer::MemFileProto* mutable_memfile();
  void set_allocated_memfile(::lib::common::buffer::MemFileProto* memfile);
  private:
  const ::lib::common::buffer::MemFileProto& _internal_memfile() const;
  ::lib::common::buffer::MemFileProto* _internal_mutable_memfile();
  public:
  void unsafe_arena_set_allocated_memfile(
      ::lib::common::buffer::MemFileProto* memfile);
  ::lib::common::buffer::MemFileProto* unsafe_arena_release_memfile();

  // .lib.common.buffer.ShmemBufferProto shmem = 3;
  bool has_shmem() const;
  private:
  bool _internal_has_shmem() const;
  public:
  void clear_shmem();
  const ::lib::common::buffer::ShmemBufferProto& shmem() const;
  PROTOBUF_NODISCARD ::lib::common::buffer::ShmemBufferProto* release_shmem();
  ::lib::common::buffer::ShmemBufferProto* mutable_shmem();
  void set_allocated_shmem(::lib::common::buffer::ShmemBufferProto* shmem);
  private:
  const ::lib::common::buffer::ShmemBufferProto& _internal_shmem() const;
  ::lib::common::buffer::ShmemBufferProto* _internal_mutable_shmem();
  public:
  void unsafe_arena_set_allocated_shmem(
      ::lib::common::buffer::ShmemBufferProto* shmem);
  ::lib::common::buffer::ShmemBufferProto* unsafe_arena_release_shmem();

  void clear_type();
  TypeCase type_case() const;
  // @@protoc_insertion_point(class_scope:lib.common.buffer.BufferProto)
 private:
  class _Internal;
  void set_has_inline_();
  void set_has_memfile();
  void set_has_shmem();

  inline bool has_type() const;
  inline void clear_has_type();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union TypeUnion {
    constexpr TypeUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::lib::common::buffer::InlineBufferProto* inline__;
    ::lib::common::buffer::MemFileProto* memfile_;
    ::lib::common::buffer::ShmemBufferProto* shmem_;
  } type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_lib_2fcommon_2fbuffer_2fbuffer_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// BufferProto

// .lib.common.buffer.InlineBufferProto inline = 1;
inline bool BufferProto::_internal_has_inline_() const {
  return type_case() == kInline;
}
inline bool BufferProto::has_inline_() const {
  return _internal_has_inline_();
}
inline void BufferProto::set_has_inline_() {
  _oneof_case_[0] = kInline;
}
inline ::lib::common::buffer::InlineBufferProto* BufferProto::release_inline_() {
  // @@protoc_insertion_point(field_release:lib.common.buffer.BufferProto.inline)
  if (_internal_has_inline_()) {
    clear_has_type();
      ::lib::common::buffer::InlineBufferProto* temp = type_.inline__;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    type_.inline__ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::lib::common::buffer::InlineBufferProto& BufferProto::_internal_inline_() const {
  return _internal_has_inline_()
      ? *type_.inline__
      : reinterpret_cast< ::lib::common::buffer::InlineBufferProto&>(::lib::common::buffer::_InlineBufferProto_default_instance_);
}
inline const ::lib::common::buffer::InlineBufferProto& BufferProto::inline_() const {
  // @@protoc_insertion_point(field_get:lib.common.buffer.BufferProto.inline)
  return _internal_inline_();
}
inline ::lib::common::buffer::InlineBufferProto* BufferProto::unsafe_arena_release_inline_() {
  // @@protoc_insertion_point(field_unsafe_arena_release:lib.common.buffer.BufferProto.inline)
  if (_internal_has_inline_()) {
    clear_has_type();
    ::lib::common::buffer::InlineBufferProto* temp = type_.inline__;
    type_.inline__ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void BufferProto::unsafe_arena_set_allocated_inline_(::lib::common::buffer::InlineBufferProto* inline_) {
  clear_type();
  if (inline_) {
    set_has_inline_();
    type_.inline__ = inline_;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:lib.common.buffer.BufferProto.inline)
}
inline ::lib::common::buffer::InlineBufferProto* BufferProto::_internal_mutable_inline_() {
  if (!_internal_has_inline_()) {
    clear_type();
    set_has_inline_();
    type_.inline__ = CreateMaybeMessage< ::lib::common::buffer::InlineBufferProto >(GetArenaForAllocation());
  }
  return type_.inline__;
}
inline ::lib::common::buffer::InlineBufferProto* BufferProto::mutable_inline_() {
  ::lib::common::buffer::InlineBufferProto* _msg = _internal_mutable_inline_();
  // @@protoc_insertion_point(field_mutable:lib.common.buffer.BufferProto.inline)
  return _msg;
}

// .lib.common.buffer.MemFileProto memfile = 2;
inline bool BufferProto::_internal_has_memfile() const {
  return type_case() == kMemfile;
}
inline bool BufferProto::has_memfile() const {
  return _internal_has_memfile();
}
inline void BufferProto::set_has_memfile() {
  _oneof_case_[0] = kMemfile;
}
inline ::lib::common::buffer::MemFileProto* BufferProto::release_memfile() {
  // @@protoc_insertion_point(field_release:lib.common.buffer.BufferProto.memfile)
  if (_internal_has_memfile()) {
    clear_has_type();
      ::lib::common::buffer::MemFileProto* temp = type_.memfile_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    type_.memfile_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::lib::common::buffer::MemFileProto& BufferProto::_internal_memfile() const {
  return _internal_has_memfile()
      ? *type_.memfile_
      : reinterpret_cast< ::lib::common::buffer::MemFileProto&>(::lib::common::buffer::_MemFileProto_default_instance_);
}
inline const ::lib::common::buffer::MemFileProto& BufferProto::memfile() const {
  // @@protoc_insertion_point(field_get:lib.common.buffer.BufferProto.memfile)
  return _internal_memfile();
}
inline ::lib::common::buffer::MemFileProto* BufferProto::unsafe_arena_release_memfile() {
  // @@protoc_insertion_point(field_unsafe_arena_release:lib.common.buffer.BufferProto.memfile)
  if (_internal_has_memfile()) {
    clear_has_type();
    ::lib::common::buffer::MemFileProto* temp = type_.memfile_;
    type_.memfile_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void BufferProto::unsafe_arena_set_allocated_memfile(::lib::common::buffer::MemFileProto* memfile) {
  clear_type();
  if (memfile) {
    set_has_memfile();
    type_.memfile_ = memfile;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:lib.common.buffer.BufferProto.memfile)
}
inline ::lib::common::buffer::MemFileProto* BufferProto::_internal_mutable_memfile() {
  if (!_internal_has_memfile()) {
    clear_type();
    set_has_memfile();
    type_.memfile_ = CreateMaybeMessage< ::lib::common::buffer::MemFileProto >(GetArenaForAllocation());
  }
  return type_.memfile_;
}
inline ::lib::common::buffer::MemFileProto* BufferProto::mutable_memfile() {
  ::lib::common::buffer::MemFileProto* _msg = _internal_mutable_memfile();
  // @@protoc_insertion_point(field_mutable:lib.common.buffer.BufferProto.memfile)
  return _msg;
}

// .lib.common.buffer.ShmemBufferProto shmem = 3;
inline bool BufferProto::_internal_has_shmem() const {
  return type_case() == kShmem;
}
inline bool BufferProto::has_shmem() const {
  return _internal_has_shmem();
}
inline void BufferProto::set_has_shmem() {
  _oneof_case_[0] = kShmem;
}
inline ::lib::common::buffer::ShmemBufferProto* BufferProto::release_shmem() {
  // @@protoc_insertion_point(field_release:lib.common.buffer.BufferProto.shmem)
  if (_internal_has_shmem()) {
    clear_has_type();
      ::lib::common::buffer::ShmemBufferProto* temp = type_.shmem_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    type_.shmem_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::lib::common::buffer::ShmemBufferProto& BufferProto::_internal_shmem() const {
  return _internal_has_shmem()
      ? *type_.shmem_
      : reinterpret_cast< ::lib::common::buffer::ShmemBufferProto&>(::lib::common::buffer::_ShmemBufferProto_default_instance_);
}
inline const ::lib::common::buffer::ShmemBufferProto& BufferProto::shmem() const {
  // @@protoc_insertion_point(field_get:lib.common.buffer.BufferProto.shmem)
  return _internal_shmem();
}
inline ::lib::common::buffer::ShmemBufferProto* BufferProto::unsafe_arena_release_shmem() {
  // @@protoc_insertion_point(field_unsafe_arena_release:lib.common.buffer.BufferProto.shmem)
  if (_internal_has_shmem()) {
    clear_has_type();
    ::lib::common::buffer::ShmemBufferProto* temp = type_.shmem_;
    type_.shmem_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void BufferProto::unsafe_arena_set_allocated_shmem(::lib::common::buffer::ShmemBufferProto* shmem) {
  clear_type();
  if (shmem) {
    set_has_shmem();
    type_.shmem_ = shmem;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:lib.common.buffer.BufferProto.shmem)
}
inline ::lib::common::buffer::ShmemBufferProto* BufferProto::_internal_mutable_shmem() {
  if (!_internal_has_shmem()) {
    clear_type();
    set_has_shmem();
    type_.shmem_ = CreateMaybeMessage< ::lib::common::buffer::ShmemBufferProto >(GetArenaForAllocation());
  }
  return type_.shmem_;
}
inline ::lib::common::buffer::ShmemBufferProto* BufferProto::mutable_shmem() {
  ::lib::common::buffer::ShmemBufferProto* _msg = _internal_mutable_shmem();
  // @@protoc_insertion_point(field_mutable:lib.common.buffer.BufferProto.shmem)
  return _msg;
}

inline bool BufferProto::has_type() const {
  return type_case() != TYPE_NOT_SET;
}
inline void BufferProto::clear_has_type() {
  _oneof_case_[0] = TYPE_NOT_SET;
}
inline BufferProto::TypeCase BufferProto::type_case() const {
  return BufferProto::TypeCase(_oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace buffer
}  // namespace common
}  // namespace lib

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_lib_2fcommon_2fbuffer_2fbuffer_2eproto

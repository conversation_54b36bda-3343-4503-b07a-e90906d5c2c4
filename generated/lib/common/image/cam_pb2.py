# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lib/common/image/cam.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.common.buffer import buffer_pb2 as lib_dot_common_dot_buffer_dot_buffer__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='lib/common/image/cam.proto',
  package='lib.common.image',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1alib/common/image/cam.proto\x12\x10lib.common.image\x1a\x1elib/common/buffer/buffer.proto\"%\n\x04Size\x12\r\n\x05width\x18\x01 \x01(\x05\x12\x0e\n\x06height\x18\x02 \x01(\x05\"\xe4\x01\n\rCamImageProto\x12\x13\n\x0b\x64\x65vice_path\x18\x01 \x01(\t\x12\x11\n\tcamera_id\x18\x02 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x03 \x01(\x04\x12$\n\x04size\x18\x04 \x01(\x0b\x32\x16.lib.common.image.Size\x12-\n\x05\x62ytes\x18\x05 \x01(\x0b\x32\x1e.lib.common.buffer.BufferProto\x12\x33\n\x0b\x64\x65pth_bytes\x18\x06 \x01(\x0b\x32\x1e.lib.common.buffer.BufferProto\x12\x0b\n\x03ppi\x18\x07 \x01(\x02\x62\x06proto3'
  ,
  dependencies=[lib_dot_common_dot_buffer_dot_buffer__pb2.DESCRIPTOR,])




_SIZE = _descriptor.Descriptor(
  name='Size',
  full_name='lib.common.image.Size',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='width', full_name='lib.common.image.Size.width', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='lib.common.image.Size.height', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=80,
  serialized_end=117,
)


_CAMIMAGEPROTO = _descriptor.Descriptor(
  name='CamImageProto',
  full_name='lib.common.image.CamImageProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='device_path', full_name='lib.common.image.CamImageProto.device_path', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='camera_id', full_name='lib.common.image.CamImageProto.camera_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='lib.common.image.CamImageProto.timestamp_ms', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size', full_name='lib.common.image.CamImageProto.size', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bytes', full_name='lib.common.image.CamImageProto.bytes', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='depth_bytes', full_name='lib.common.image.CamImageProto.depth_bytes', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ppi', full_name='lib.common.image.CamImageProto.ppi', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=120,
  serialized_end=348,
)

_CAMIMAGEPROTO.fields_by_name['size'].message_type = _SIZE
_CAMIMAGEPROTO.fields_by_name['bytes'].message_type = lib_dot_common_dot_buffer_dot_buffer__pb2._BUFFERPROTO
_CAMIMAGEPROTO.fields_by_name['depth_bytes'].message_type = lib_dot_common_dot_buffer_dot_buffer__pb2._BUFFERPROTO
DESCRIPTOR.message_types_by_name['Size'] = _SIZE
DESCRIPTOR.message_types_by_name['CamImageProto'] = _CAMIMAGEPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Size = _reflection.GeneratedProtocolMessageType('Size', (_message.Message,), {
  'DESCRIPTOR' : _SIZE,
  '__module__' : 'lib.common.image.cam_pb2'
  # @@protoc_insertion_point(class_scope:lib.common.image.Size)
  })
_sym_db.RegisterMessage(Size)

CamImageProto = _reflection.GeneratedProtocolMessageType('CamImageProto', (_message.Message,), {
  'DESCRIPTOR' : _CAMIMAGEPROTO,
  '__module__' : 'lib.common.image.cam_pb2'
  # @@protoc_insertion_point(class_scope:lib.common.image.CamImageProto)
  })
_sym_db.RegisterMessage(CamImageProto)


# @@protoc_insertion_point(module_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/common/image/cam.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_lib_2fcommon_2fimage_2fcam_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_lib_2fcommon_2fimage_2fcam_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "lib/common/buffer/buffer.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_lib_2fcommon_2fimage_2fcam_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_lib_2fcommon_2fimage_2fcam_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto;
namespace lib {
namespace common {
namespace image {
class CamImageProto;
struct CamImageProtoDefaultTypeInternal;
extern CamImageProtoDefaultTypeInternal _CamImageProto_default_instance_;
class Size;
struct SizeDefaultTypeInternal;
extern SizeDefaultTypeInternal _Size_default_instance_;
}  // namespace image
}  // namespace common
}  // namespace lib
PROTOBUF_NAMESPACE_OPEN
template<> ::lib::common::image::CamImageProto* Arena::CreateMaybeMessage<::lib::common::image::CamImageProto>(Arena*);
template<> ::lib::common::image::Size* Arena::CreateMaybeMessage<::lib::common::image::Size>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace lib {
namespace common {
namespace image {

// ===================================================================

class Size final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:lib.common.image.Size) */ {
 public:
  inline Size() : Size(nullptr) {}
  ~Size() override;
  explicit constexpr Size(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Size(const Size& from);
  Size(Size&& from) noexcept
    : Size() {
    *this = ::std::move(from);
  }

  inline Size& operator=(const Size& from) {
    CopyFrom(from);
    return *this;
  }
  inline Size& operator=(Size&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Size& default_instance() {
    return *internal_default_instance();
  }
  static inline const Size* internal_default_instance() {
    return reinterpret_cast<const Size*>(
               &_Size_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Size& a, Size& b) {
    a.Swap(&b);
  }
  inline void Swap(Size* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Size* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Size* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Size>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Size& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Size& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Size* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "lib.common.image.Size";
  }
  protected:
  explicit Size(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWidthFieldNumber = 1,
    kHeightFieldNumber = 2,
  };
  // int32 width = 1;
  void clear_width();
  int32_t width() const;
  void set_width(int32_t value);
  private:
  int32_t _internal_width() const;
  void _internal_set_width(int32_t value);
  public:

  // int32 height = 2;
  void clear_height();
  int32_t height() const;
  void set_height(int32_t value);
  private:
  int32_t _internal_height() const;
  void _internal_set_height(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:lib.common.image.Size)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t width_;
  int32_t height_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_lib_2fcommon_2fimage_2fcam_2eproto;
};
// -------------------------------------------------------------------

class CamImageProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:lib.common.image.CamImageProto) */ {
 public:
  inline CamImageProto() : CamImageProto(nullptr) {}
  ~CamImageProto() override;
  explicit constexpr CamImageProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CamImageProto(const CamImageProto& from);
  CamImageProto(CamImageProto&& from) noexcept
    : CamImageProto() {
    *this = ::std::move(from);
  }

  inline CamImageProto& operator=(const CamImageProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline CamImageProto& operator=(CamImageProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CamImageProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const CamImageProto* internal_default_instance() {
    return reinterpret_cast<const CamImageProto*>(
               &_CamImageProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CamImageProto& a, CamImageProto& b) {
    a.Swap(&b);
  }
  inline void Swap(CamImageProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CamImageProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CamImageProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CamImageProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CamImageProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CamImageProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CamImageProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "lib.common.image.CamImageProto";
  }
  protected:
  explicit CamImageProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDevicePathFieldNumber = 1,
    kCameraIdFieldNumber = 2,
    kSizeFieldNumber = 4,
    kBytesFieldNumber = 5,
    kDepthBytesFieldNumber = 6,
    kTimestampMsFieldNumber = 3,
    kPpiFieldNumber = 7,
  };
  // string device_path = 1;
  void clear_device_path();
  const std::string& device_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_path();
  PROTOBUF_NODISCARD std::string* release_device_path();
  void set_allocated_device_path(std::string* device_path);
  private:
  const std::string& _internal_device_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_path(const std::string& value);
  std::string* _internal_mutable_device_path();
  public:

  // string camera_id = 2;
  void clear_camera_id();
  const std::string& camera_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_camera_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_camera_id();
  PROTOBUF_NODISCARD std::string* release_camera_id();
  void set_allocated_camera_id(std::string* camera_id);
  private:
  const std::string& _internal_camera_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_camera_id(const std::string& value);
  std::string* _internal_mutable_camera_id();
  public:

  // .lib.common.image.Size size = 4;
  bool has_size() const;
  private:
  bool _internal_has_size() const;
  public:
  void clear_size();
  const ::lib::common::image::Size& size() const;
  PROTOBUF_NODISCARD ::lib::common::image::Size* release_size();
  ::lib::common::image::Size* mutable_size();
  void set_allocated_size(::lib::common::image::Size* size);
  private:
  const ::lib::common::image::Size& _internal_size() const;
  ::lib::common::image::Size* _internal_mutable_size();
  public:
  void unsafe_arena_set_allocated_size(
      ::lib::common::image::Size* size);
  ::lib::common::image::Size* unsafe_arena_release_size();

  // .lib.common.buffer.BufferProto bytes = 5;
  bool has_bytes() const;
  private:
  bool _internal_has_bytes() const;
  public:
  void clear_bytes();
  const ::lib::common::buffer::BufferProto& bytes() const;
  PROTOBUF_NODISCARD ::lib::common::buffer::BufferProto* release_bytes();
  ::lib::common::buffer::BufferProto* mutable_bytes();
  void set_allocated_bytes(::lib::common::buffer::BufferProto* bytes);
  private:
  const ::lib::common::buffer::BufferProto& _internal_bytes() const;
  ::lib::common::buffer::BufferProto* _internal_mutable_bytes();
  public:
  void unsafe_arena_set_allocated_bytes(
      ::lib::common::buffer::BufferProto* bytes);
  ::lib::common::buffer::BufferProto* unsafe_arena_release_bytes();

  // .lib.common.buffer.BufferProto depth_bytes = 6;
  bool has_depth_bytes() const;
  private:
  bool _internal_has_depth_bytes() const;
  public:
  void clear_depth_bytes();
  const ::lib::common::buffer::BufferProto& depth_bytes() const;
  PROTOBUF_NODISCARD ::lib::common::buffer::BufferProto* release_depth_bytes();
  ::lib::common::buffer::BufferProto* mutable_depth_bytes();
  void set_allocated_depth_bytes(::lib::common::buffer::BufferProto* depth_bytes);
  private:
  const ::lib::common::buffer::BufferProto& _internal_depth_bytes() const;
  ::lib::common::buffer::BufferProto* _internal_mutable_depth_bytes();
  public:
  void unsafe_arena_set_allocated_depth_bytes(
      ::lib::common::buffer::BufferProto* depth_bytes);
  ::lib::common::buffer::BufferProto* unsafe_arena_release_depth_bytes();

  // uint64 timestamp_ms = 3;
  void clear_timestamp_ms();
  uint64_t timestamp_ms() const;
  void set_timestamp_ms(uint64_t value);
  private:
  uint64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(uint64_t value);
  public:

  // float ppi = 7;
  void clear_ppi();
  float ppi() const;
  void set_ppi(float value);
  private:
  float _internal_ppi() const;
  void _internal_set_ppi(float value);
  public:

  // @@protoc_insertion_point(class_scope:lib.common.image.CamImageProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr camera_id_;
  ::lib::common::image::Size* size_;
  ::lib::common::buffer::BufferProto* bytes_;
  ::lib::common::buffer::BufferProto* depth_bytes_;
  uint64_t timestamp_ms_;
  float ppi_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_lib_2fcommon_2fimage_2fcam_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Size

// int32 width = 1;
inline void Size::clear_width() {
  width_ = 0;
}
inline int32_t Size::_internal_width() const {
  return width_;
}
inline int32_t Size::width() const {
  // @@protoc_insertion_point(field_get:lib.common.image.Size.width)
  return _internal_width();
}
inline void Size::_internal_set_width(int32_t value) {
  
  width_ = value;
}
inline void Size::set_width(int32_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:lib.common.image.Size.width)
}

// int32 height = 2;
inline void Size::clear_height() {
  height_ = 0;
}
inline int32_t Size::_internal_height() const {
  return height_;
}
inline int32_t Size::height() const {
  // @@protoc_insertion_point(field_get:lib.common.image.Size.height)
  return _internal_height();
}
inline void Size::_internal_set_height(int32_t value) {
  
  height_ = value;
}
inline void Size::set_height(int32_t value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:lib.common.image.Size.height)
}

// -------------------------------------------------------------------

// CamImageProto

// string device_path = 1;
inline void CamImageProto::clear_device_path() {
  device_path_.ClearToEmpty();
}
inline const std::string& CamImageProto::device_path() const {
  // @@protoc_insertion_point(field_get:lib.common.image.CamImageProto.device_path)
  return _internal_device_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CamImageProto::set_device_path(ArgT0&& arg0, ArgT... args) {
 
 device_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:lib.common.image.CamImageProto.device_path)
}
inline std::string* CamImageProto::mutable_device_path() {
  std::string* _s = _internal_mutable_device_path();
  // @@protoc_insertion_point(field_mutable:lib.common.image.CamImageProto.device_path)
  return _s;
}
inline const std::string& CamImageProto::_internal_device_path() const {
  return device_path_.Get();
}
inline void CamImageProto::_internal_set_device_path(const std::string& value) {
  
  device_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CamImageProto::_internal_mutable_device_path() {
  
  return device_path_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CamImageProto::release_device_path() {
  // @@protoc_insertion_point(field_release:lib.common.image.CamImageProto.device_path)
  return device_path_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CamImageProto::set_allocated_device_path(std::string* device_path) {
  if (device_path != nullptr) {
    
  } else {
    
  }
  device_path_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_path,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (device_path_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    device_path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:lib.common.image.CamImageProto.device_path)
}

// string camera_id = 2;
inline void CamImageProto::clear_camera_id() {
  camera_id_.ClearToEmpty();
}
inline const std::string& CamImageProto::camera_id() const {
  // @@protoc_insertion_point(field_get:lib.common.image.CamImageProto.camera_id)
  return _internal_camera_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CamImageProto::set_camera_id(ArgT0&& arg0, ArgT... args) {
 
 camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:lib.common.image.CamImageProto.camera_id)
}
inline std::string* CamImageProto::mutable_camera_id() {
  std::string* _s = _internal_mutable_camera_id();
  // @@protoc_insertion_point(field_mutable:lib.common.image.CamImageProto.camera_id)
  return _s;
}
inline const std::string& CamImageProto::_internal_camera_id() const {
  return camera_id_.Get();
}
inline void CamImageProto::_internal_set_camera_id(const std::string& value) {
  
  camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CamImageProto::_internal_mutable_camera_id() {
  
  return camera_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CamImageProto::release_camera_id() {
  // @@protoc_insertion_point(field_release:lib.common.image.CamImageProto.camera_id)
  return camera_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CamImageProto::set_allocated_camera_id(std::string* camera_id) {
  if (camera_id != nullptr) {
    
  } else {
    
  }
  camera_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), camera_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (camera_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:lib.common.image.CamImageProto.camera_id)
}

// uint64 timestamp_ms = 3;
inline void CamImageProto::clear_timestamp_ms() {
  timestamp_ms_ = uint64_t{0u};
}
inline uint64_t CamImageProto::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline uint64_t CamImageProto::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:lib.common.image.CamImageProto.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void CamImageProto::_internal_set_timestamp_ms(uint64_t value) {
  
  timestamp_ms_ = value;
}
inline void CamImageProto::set_timestamp_ms(uint64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:lib.common.image.CamImageProto.timestamp_ms)
}

// .lib.common.image.Size size = 4;
inline bool CamImageProto::_internal_has_size() const {
  return this != internal_default_instance() && size_ != nullptr;
}
inline bool CamImageProto::has_size() const {
  return _internal_has_size();
}
inline void CamImageProto::clear_size() {
  if (GetArenaForAllocation() == nullptr && size_ != nullptr) {
    delete size_;
  }
  size_ = nullptr;
}
inline const ::lib::common::image::Size& CamImageProto::_internal_size() const {
  const ::lib::common::image::Size* p = size_;
  return p != nullptr ? *p : reinterpret_cast<const ::lib::common::image::Size&>(
      ::lib::common::image::_Size_default_instance_);
}
inline const ::lib::common::image::Size& CamImageProto::size() const {
  // @@protoc_insertion_point(field_get:lib.common.image.CamImageProto.size)
  return _internal_size();
}
inline void CamImageProto::unsafe_arena_set_allocated_size(
    ::lib::common::image::Size* size) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(size_);
  }
  size_ = size;
  if (size) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:lib.common.image.CamImageProto.size)
}
inline ::lib::common::image::Size* CamImageProto::release_size() {
  
  ::lib::common::image::Size* temp = size_;
  size_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::lib::common::image::Size* CamImageProto::unsafe_arena_release_size() {
  // @@protoc_insertion_point(field_release:lib.common.image.CamImageProto.size)
  
  ::lib::common::image::Size* temp = size_;
  size_ = nullptr;
  return temp;
}
inline ::lib::common::image::Size* CamImageProto::_internal_mutable_size() {
  
  if (size_ == nullptr) {
    auto* p = CreateMaybeMessage<::lib::common::image::Size>(GetArenaForAllocation());
    size_ = p;
  }
  return size_;
}
inline ::lib::common::image::Size* CamImageProto::mutable_size() {
  ::lib::common::image::Size* _msg = _internal_mutable_size();
  // @@protoc_insertion_point(field_mutable:lib.common.image.CamImageProto.size)
  return _msg;
}
inline void CamImageProto::set_allocated_size(::lib::common::image::Size* size) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete size_;
  }
  if (size) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::lib::common::image::Size>::GetOwningArena(size);
    if (message_arena != submessage_arena) {
      size = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, size, submessage_arena);
    }
    
  } else {
    
  }
  size_ = size;
  // @@protoc_insertion_point(field_set_allocated:lib.common.image.CamImageProto.size)
}

// .lib.common.buffer.BufferProto bytes = 5;
inline bool CamImageProto::_internal_has_bytes() const {
  return this != internal_default_instance() && bytes_ != nullptr;
}
inline bool CamImageProto::has_bytes() const {
  return _internal_has_bytes();
}
inline const ::lib::common::buffer::BufferProto& CamImageProto::_internal_bytes() const {
  const ::lib::common::buffer::BufferProto* p = bytes_;
  return p != nullptr ? *p : reinterpret_cast<const ::lib::common::buffer::BufferProto&>(
      ::lib::common::buffer::_BufferProto_default_instance_);
}
inline const ::lib::common::buffer::BufferProto& CamImageProto::bytes() const {
  // @@protoc_insertion_point(field_get:lib.common.image.CamImageProto.bytes)
  return _internal_bytes();
}
inline void CamImageProto::unsafe_arena_set_allocated_bytes(
    ::lib::common::buffer::BufferProto* bytes) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bytes_);
  }
  bytes_ = bytes;
  if (bytes) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:lib.common.image.CamImageProto.bytes)
}
inline ::lib::common::buffer::BufferProto* CamImageProto::release_bytes() {
  
  ::lib::common::buffer::BufferProto* temp = bytes_;
  bytes_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::lib::common::buffer::BufferProto* CamImageProto::unsafe_arena_release_bytes() {
  // @@protoc_insertion_point(field_release:lib.common.image.CamImageProto.bytes)
  
  ::lib::common::buffer::BufferProto* temp = bytes_;
  bytes_ = nullptr;
  return temp;
}
inline ::lib::common::buffer::BufferProto* CamImageProto::_internal_mutable_bytes() {
  
  if (bytes_ == nullptr) {
    auto* p = CreateMaybeMessage<::lib::common::buffer::BufferProto>(GetArenaForAllocation());
    bytes_ = p;
  }
  return bytes_;
}
inline ::lib::common::buffer::BufferProto* CamImageProto::mutable_bytes() {
  ::lib::common::buffer::BufferProto* _msg = _internal_mutable_bytes();
  // @@protoc_insertion_point(field_mutable:lib.common.image.CamImageProto.bytes)
  return _msg;
}
inline void CamImageProto::set_allocated_bytes(::lib::common::buffer::BufferProto* bytes) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(bytes_);
  }
  if (bytes) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bytes));
    if (message_arena != submessage_arena) {
      bytes = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bytes, submessage_arena);
    }
    
  } else {
    
  }
  bytes_ = bytes;
  // @@protoc_insertion_point(field_set_allocated:lib.common.image.CamImageProto.bytes)
}

// .lib.common.buffer.BufferProto depth_bytes = 6;
inline bool CamImageProto::_internal_has_depth_bytes() const {
  return this != internal_default_instance() && depth_bytes_ != nullptr;
}
inline bool CamImageProto::has_depth_bytes() const {
  return _internal_has_depth_bytes();
}
inline const ::lib::common::buffer::BufferProto& CamImageProto::_internal_depth_bytes() const {
  const ::lib::common::buffer::BufferProto* p = depth_bytes_;
  return p != nullptr ? *p : reinterpret_cast<const ::lib::common::buffer::BufferProto&>(
      ::lib::common::buffer::_BufferProto_default_instance_);
}
inline const ::lib::common::buffer::BufferProto& CamImageProto::depth_bytes() const {
  // @@protoc_insertion_point(field_get:lib.common.image.CamImageProto.depth_bytes)
  return _internal_depth_bytes();
}
inline void CamImageProto::unsafe_arena_set_allocated_depth_bytes(
    ::lib::common::buffer::BufferProto* depth_bytes) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(depth_bytes_);
  }
  depth_bytes_ = depth_bytes;
  if (depth_bytes) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:lib.common.image.CamImageProto.depth_bytes)
}
inline ::lib::common::buffer::BufferProto* CamImageProto::release_depth_bytes() {
  
  ::lib::common::buffer::BufferProto* temp = depth_bytes_;
  depth_bytes_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::lib::common::buffer::BufferProto* CamImageProto::unsafe_arena_release_depth_bytes() {
  // @@protoc_insertion_point(field_release:lib.common.image.CamImageProto.depth_bytes)
  
  ::lib::common::buffer::BufferProto* temp = depth_bytes_;
  depth_bytes_ = nullptr;
  return temp;
}
inline ::lib::common::buffer::BufferProto* CamImageProto::_internal_mutable_depth_bytes() {
  
  if (depth_bytes_ == nullptr) {
    auto* p = CreateMaybeMessage<::lib::common::buffer::BufferProto>(GetArenaForAllocation());
    depth_bytes_ = p;
  }
  return depth_bytes_;
}
inline ::lib::common::buffer::BufferProto* CamImageProto::mutable_depth_bytes() {
  ::lib::common::buffer::BufferProto* _msg = _internal_mutable_depth_bytes();
  // @@protoc_insertion_point(field_mutable:lib.common.image.CamImageProto.depth_bytes)
  return _msg;
}
inline void CamImageProto::set_allocated_depth_bytes(::lib::common::buffer::BufferProto* depth_bytes) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(depth_bytes_);
  }
  if (depth_bytes) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(depth_bytes));
    if (message_arena != submessage_arena) {
      depth_bytes = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, depth_bytes, submessage_arena);
    }
    
  } else {
    
  }
  depth_bytes_ = depth_bytes;
  // @@protoc_insertion_point(field_set_allocated:lib.common.image.CamImageProto.depth_bytes)
}

// float ppi = 7;
inline void CamImageProto::clear_ppi() {
  ppi_ = 0;
}
inline float CamImageProto::_internal_ppi() const {
  return ppi_;
}
inline float CamImageProto::ppi() const {
  // @@protoc_insertion_point(field_get:lib.common.image.CamImageProto.ppi)
  return _internal_ppi();
}
inline void CamImageProto::_internal_set_ppi(float value) {
  
  ppi_ = value;
}
inline void CamImageProto::set_ppi(float value) {
  _internal_set_ppi(value);
  // @@protoc_insertion_point(field_set:lib.common.image.CamImageProto.ppi)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace image
}  // namespace common
}  // namespace lib

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_lib_2fcommon_2fimage_2fcam_2eproto

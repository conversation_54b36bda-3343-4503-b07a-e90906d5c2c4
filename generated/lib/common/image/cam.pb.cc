// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/common/image/cam.proto

#include "lib/common/image/cam.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace lib {
namespace common {
namespace image {
constexpr Size::Size(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : width_(0)
  , height_(0){}
struct SizeDefaultTypeInternal {
  constexpr SizeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SizeDefaultTypeInternal() {}
  union {
    Size _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SizeDefaultTypeInternal _Size_default_instance_;
constexpr CamImageProto::CamImageProto(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : device_path_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , camera_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , size_(nullptr)
  , bytes_(nullptr)
  , depth_bytes_(nullptr)
  , timestamp_ms_(uint64_t{0u})
  , ppi_(0){}
struct CamImageProtoDefaultTypeInternal {
  constexpr CamImageProtoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CamImageProtoDefaultTypeInternal() {}
  union {
    CamImageProto _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CamImageProtoDefaultTypeInternal _CamImageProto_default_instance_;
}  // namespace image
}  // namespace common
}  // namespace lib
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_lib_2fcommon_2fimage_2fcam_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_lib_2fcommon_2fimage_2fcam_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_lib_2fcommon_2fimage_2fcam_2eproto = nullptr;

const uint32_t TableStruct_lib_2fcommon_2fimage_2fcam_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::lib::common::image::Size, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::lib::common::image::Size, width_),
  PROTOBUF_FIELD_OFFSET(::lib::common::image::Size, height_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::lib::common::image::CamImageProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::lib::common::image::CamImageProto, device_path_),
  PROTOBUF_FIELD_OFFSET(::lib::common::image::CamImageProto, camera_id_),
  PROTOBUF_FIELD_OFFSET(::lib::common::image::CamImageProto, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::lib::common::image::CamImageProto, size_),
  PROTOBUF_FIELD_OFFSET(::lib::common::image::CamImageProto, bytes_),
  PROTOBUF_FIELD_OFFSET(::lib::common::image::CamImageProto, depth_bytes_),
  PROTOBUF_FIELD_OFFSET(::lib::common::image::CamImageProto, ppi_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::lib::common::image::Size)},
  { 8, -1, -1, sizeof(::lib::common::image::CamImageProto)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::lib::common::image::_Size_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::lib::common::image::_CamImageProto_default_instance_),
};

const char descriptor_table_protodef_lib_2fcommon_2fimage_2fcam_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\032lib/common/image/cam.proto\022\020lib.common"
  ".image\032\036lib/common/buffer/buffer.proto\"%"
  "\n\004Size\022\r\n\005width\030\001 \001(\005\022\016\n\006height\030\002 \001(\005\"\344\001"
  "\n\rCamImageProto\022\023\n\013device_path\030\001 \001(\t\022\021\n\t"
  "camera_id\030\002 \001(\t\022\024\n\014timestamp_ms\030\003 \001(\004\022$\n"
  "\004size\030\004 \001(\0132\026.lib.common.image.Size\022-\n\005b"
  "ytes\030\005 \001(\0132\036.lib.common.buffer.BufferPro"
  "to\0223\n\013depth_bytes\030\006 \001(\0132\036.lib.common.buf"
  "fer.BufferProto\022\013\n\003ppi\030\007 \001(\002b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto_deps[1] = {
  &::descriptor_table_lib_2fcommon_2fbuffer_2fbuffer_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto = {
  false, false, 356, descriptor_table_protodef_lib_2fcommon_2fimage_2fcam_2eproto, "lib/common/image/cam.proto", 
  &descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto_once, descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto_deps, 1, 2,
  schemas, file_default_instances, TableStruct_lib_2fcommon_2fimage_2fcam_2eproto::offsets,
  file_level_metadata_lib_2fcommon_2fimage_2fcam_2eproto, file_level_enum_descriptors_lib_2fcommon_2fimage_2fcam_2eproto, file_level_service_descriptors_lib_2fcommon_2fimage_2fcam_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto_getter() {
  return &descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_lib_2fcommon_2fimage_2fcam_2eproto(&descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto);
namespace lib {
namespace common {
namespace image {

// ===================================================================

class Size::_Internal {
 public:
};

Size::Size(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:lib.common.image.Size)
}
Size::Size(const Size& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&width_, &from.width_,
    static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&width_)) + sizeof(height_));
  // @@protoc_insertion_point(copy_constructor:lib.common.image.Size)
}

inline void Size::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&width_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&width_)) + sizeof(height_));
}

Size::~Size() {
  // @@protoc_insertion_point(destructor:lib.common.image.Size)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Size::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Size::ArenaDtor(void* object) {
  Size* _this = reinterpret_cast< Size* >(object);
  (void)_this;
}
void Size::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Size::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Size::Clear() {
// @@protoc_insertion_point(message_clear_start:lib.common.image.Size)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&width_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&height_) -
      reinterpret_cast<char*>(&width_)) + sizeof(height_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Size::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 width = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Size::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:lib.common.image.Size)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 width = 1;
  if (this->_internal_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_width(), target);
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_height(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:lib.common.image.Size)
  return target;
}

size_t Size::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:lib.common.image.Size)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 width = 1;
  if (this->_internal_width() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_width());
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_height());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Size::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Size::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Size::GetClassData() const { return &_class_data_; }

void Size::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Size *>(to)->MergeFrom(
      static_cast<const Size &>(from));
}


void Size::MergeFrom(const Size& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:lib.common.image.Size)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_width() != 0) {
    _internal_set_width(from._internal_width());
  }
  if (from._internal_height() != 0) {
    _internal_set_height(from._internal_height());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Size::CopyFrom(const Size& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:lib.common.image.Size)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Size::IsInitialized() const {
  return true;
}

void Size::InternalSwap(Size* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Size, height_)
      + sizeof(Size::height_)
      - PROTOBUF_FIELD_OFFSET(Size, width_)>(
          reinterpret_cast<char*>(&width_),
          reinterpret_cast<char*>(&other->width_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Size::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto_getter, &descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto_once,
      file_level_metadata_lib_2fcommon_2fimage_2fcam_2eproto[0]);
}

// ===================================================================

class CamImageProto::_Internal {
 public:
  static const ::lib::common::image::Size& size(const CamImageProto* msg);
  static const ::lib::common::buffer::BufferProto& bytes(const CamImageProto* msg);
  static const ::lib::common::buffer::BufferProto& depth_bytes(const CamImageProto* msg);
};

const ::lib::common::image::Size&
CamImageProto::_Internal::size(const CamImageProto* msg) {
  return *msg->size_;
}
const ::lib::common::buffer::BufferProto&
CamImageProto::_Internal::bytes(const CamImageProto* msg) {
  return *msg->bytes_;
}
const ::lib::common::buffer::BufferProto&
CamImageProto::_Internal::depth_bytes(const CamImageProto* msg) {
  return *msg->depth_bytes_;
}
void CamImageProto::clear_bytes() {
  if (GetArenaForAllocation() == nullptr && bytes_ != nullptr) {
    delete bytes_;
  }
  bytes_ = nullptr;
}
void CamImageProto::clear_depth_bytes() {
  if (GetArenaForAllocation() == nullptr && depth_bytes_ != nullptr) {
    delete depth_bytes_;
  }
  depth_bytes_ = nullptr;
}
CamImageProto::CamImageProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:lib.common.image.CamImageProto)
}
CamImageProto::CamImageProto(const CamImageProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  device_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    device_path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_device_path().empty()) {
    device_path_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_device_path(), 
      GetArenaForAllocation());
  }
  camera_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_camera_id().empty()) {
    camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_camera_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_size()) {
    size_ = new ::lib::common::image::Size(*from.size_);
  } else {
    size_ = nullptr;
  }
  if (from._internal_has_bytes()) {
    bytes_ = new ::lib::common::buffer::BufferProto(*from.bytes_);
  } else {
    bytes_ = nullptr;
  }
  if (from._internal_has_depth_bytes()) {
    depth_bytes_ = new ::lib::common::buffer::BufferProto(*from.depth_bytes_);
  } else {
    depth_bytes_ = nullptr;
  }
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&ppi_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(ppi_));
  // @@protoc_insertion_point(copy_constructor:lib.common.image.CamImageProto)
}

inline void CamImageProto::SharedCtor() {
device_path_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  device_path_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
camera_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&size_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&ppi_) -
    reinterpret_cast<char*>(&size_)) + sizeof(ppi_));
}

CamImageProto::~CamImageProto() {
  // @@protoc_insertion_point(destructor:lib.common.image.CamImageProto)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CamImageProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  device_path_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  camera_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete size_;
  if (this != internal_default_instance()) delete bytes_;
  if (this != internal_default_instance()) delete depth_bytes_;
}

void CamImageProto::ArenaDtor(void* object) {
  CamImageProto* _this = reinterpret_cast< CamImageProto* >(object);
  (void)_this;
}
void CamImageProto::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CamImageProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CamImageProto::Clear() {
// @@protoc_insertion_point(message_clear_start:lib.common.image.CamImageProto)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  device_path_.ClearToEmpty();
  camera_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && size_ != nullptr) {
    delete size_;
  }
  size_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bytes_ != nullptr) {
    delete bytes_;
  }
  bytes_ = nullptr;
  if (GetArenaForAllocation() == nullptr && depth_bytes_ != nullptr) {
    delete depth_bytes_;
  }
  depth_bytes_ = nullptr;
  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ppi_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(ppi_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CamImageProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string device_path = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_device_path();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "lib.common.image.CamImageProto.device_path"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string camera_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_camera_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "lib.common.image.CamImageProto.camera_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 timestamp_ms = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .lib.common.image.Size size = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_size(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .lib.common.buffer.BufferProto bytes = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_bytes(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .lib.common.buffer.BufferProto depth_bytes = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_depth_bytes(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float ppi = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          ppi_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CamImageProto::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:lib.common.image.CamImageProto)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string device_path = 1;
  if (!this->_internal_device_path().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_device_path().data(), static_cast<int>(this->_internal_device_path().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "lib.common.image.CamImageProto.device_path");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_device_path(), target);
  }

  // string camera_id = 2;
  if (!this->_internal_camera_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_camera_id().data(), static_cast<int>(this->_internal_camera_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "lib.common.image.CamImageProto.camera_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_camera_id(), target);
  }

  // uint64 timestamp_ms = 3;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_timestamp_ms(), target);
  }

  // .lib.common.image.Size size = 4;
  if (this->_internal_has_size()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::size(this), target, stream);
  }

  // .lib.common.buffer.BufferProto bytes = 5;
  if (this->_internal_has_bytes()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::bytes(this), target, stream);
  }

  // .lib.common.buffer.BufferProto depth_bytes = 6;
  if (this->_internal_has_depth_bytes()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::depth_bytes(this), target, stream);
  }

  // float ppi = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_ppi = this->_internal_ppi();
  uint32_t raw_ppi;
  memcpy(&raw_ppi, &tmp_ppi, sizeof(tmp_ppi));
  if (raw_ppi != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_ppi(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:lib.common.image.CamImageProto)
  return target;
}

size_t CamImageProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:lib.common.image.CamImageProto)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string device_path = 1;
  if (!this->_internal_device_path().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_device_path());
  }

  // string camera_id = 2;
  if (!this->_internal_camera_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_camera_id());
  }

  // .lib.common.image.Size size = 4;
  if (this->_internal_has_size()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *size_);
  }

  // .lib.common.buffer.BufferProto bytes = 5;
  if (this->_internal_has_bytes()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bytes_);
  }

  // .lib.common.buffer.BufferProto depth_bytes = 6;
  if (this->_internal_has_depth_bytes()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *depth_bytes_);
  }

  // uint64 timestamp_ms = 3;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_timestamp_ms());
  }

  // float ppi = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_ppi = this->_internal_ppi();
  uint32_t raw_ppi;
  memcpy(&raw_ppi, &tmp_ppi, sizeof(tmp_ppi));
  if (raw_ppi != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CamImageProto::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CamImageProto::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CamImageProto::GetClassData() const { return &_class_data_; }

void CamImageProto::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CamImageProto *>(to)->MergeFrom(
      static_cast<const CamImageProto &>(from));
}


void CamImageProto::MergeFrom(const CamImageProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:lib.common.image.CamImageProto)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_device_path().empty()) {
    _internal_set_device_path(from._internal_device_path());
  }
  if (!from._internal_camera_id().empty()) {
    _internal_set_camera_id(from._internal_camera_id());
  }
  if (from._internal_has_size()) {
    _internal_mutable_size()->::lib::common::image::Size::MergeFrom(from._internal_size());
  }
  if (from._internal_has_bytes()) {
    _internal_mutable_bytes()->::lib::common::buffer::BufferProto::MergeFrom(from._internal_bytes());
  }
  if (from._internal_has_depth_bytes()) {
    _internal_mutable_depth_bytes()->::lib::common::buffer::BufferProto::MergeFrom(from._internal_depth_bytes());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_ppi = from._internal_ppi();
  uint32_t raw_ppi;
  memcpy(&raw_ppi, &tmp_ppi, sizeof(tmp_ppi));
  if (raw_ppi != 0) {
    _internal_set_ppi(from._internal_ppi());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CamImageProto::CopyFrom(const CamImageProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:lib.common.image.CamImageProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CamImageProto::IsInitialized() const {
  return true;
}

void CamImageProto::InternalSwap(CamImageProto* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &device_path_, lhs_arena,
      &other->device_path_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &camera_id_, lhs_arena,
      &other->camera_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CamImageProto, ppi_)
      + sizeof(CamImageProto::ppi_)
      - PROTOBUF_FIELD_OFFSET(CamImageProto, size_)>(
          reinterpret_cast<char*>(&size_),
          reinterpret_cast<char*>(&other->size_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CamImageProto::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto_getter, &descriptor_table_lib_2fcommon_2fimage_2fcam_2eproto_once,
      file_level_metadata_lib_2fcommon_2fimage_2fcam_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace image
}  // namespace common
}  // namespace lib
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::lib::common::image::Size* Arena::CreateMaybeMessage< ::lib::common::image::Size >(Arena* arena) {
  return Arena::CreateMessageInternal< ::lib::common::image::Size >(arena);
}
template<> PROTOBUF_NOINLINE ::lib::common::image::CamImageProto* Arena::CreateMaybeMessage< ::lib::common::image::CamImageProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::lib::common::image::CamImageProto >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

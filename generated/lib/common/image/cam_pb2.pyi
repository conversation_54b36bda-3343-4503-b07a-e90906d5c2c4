"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.lib.common.buffer.buffer_pb2 import (
    BufferProto as lib___common___buffer___buffer_pb2___BufferProto,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Size(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    width: builtin___int = ...
    height: builtin___int = ...

    def __init__(self,
        *,
        width : typing___Optional[builtin___int] = None,
        height : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"height",b"height",u"width",b"width"]) -> None: ...
type___Size = Size

class CamImageProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    device_path: typing___Text = ...
    camera_id: typing___Text = ...
    timestamp_ms: builtin___int = ...
    ppi: builtin___float = ...

    @property
    def size(self) -> type___Size: ...

    @property
    def bytes(self) -> lib___common___buffer___buffer_pb2___BufferProto: ...

    @property
    def depth_bytes(self) -> lib___common___buffer___buffer_pb2___BufferProto: ...

    def __init__(self,
        *,
        device_path : typing___Optional[typing___Text] = None,
        camera_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        size : typing___Optional[type___Size] = None,
        bytes : typing___Optional[lib___common___buffer___buffer_pb2___BufferProto] = None,
        depth_bytes : typing___Optional[lib___common___buffer___buffer_pb2___BufferProto] = None,
        ppi : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bytes",b"bytes",u"depth_bytes",b"depth_bytes",u"size",b"size"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bytes",b"bytes",u"camera_id",b"camera_id",u"depth_bytes",b"depth_bytes",u"device_path",b"device_path",u"ppi",b"ppi",u"size",b"size",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___CamImageProto = CamImageProto

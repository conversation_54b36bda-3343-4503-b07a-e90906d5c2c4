# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lib/common/camera/proto/camera.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='lib/common/camera/proto/camera.proto',
  package='lib.common.camera',
  syntax='proto3',
  serialized_options=b'Z\010proto/cv',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n$lib/common/camera/proto/camera.proto\x12\x11lib.common.camera*Y\n\x11LightSourcePreset\x12\x08\n\x04kOff\x10\x00\x12\x12\n\x0ekDaylight5000K\x10\x01\x12\x12\n\x0ekDaylight6500K\x10\x02\x12\x12\n\x0ekTungsten2800K\x10\x03\x42\nZ\x08proto/cvb\x06proto3'
)

_LIGHTSOURCEPRESET = _descriptor.EnumDescriptor(
  name='LightSourcePreset',
  full_name='lib.common.camera.LightSourcePreset',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='kOff', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kDaylight5000K', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kDaylight6500K', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kTungsten2800K', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=59,
  serialized_end=148,
)
_sym_db.RegisterEnumDescriptor(_LIGHTSOURCEPRESET)

LightSourcePreset = enum_type_wrapper.EnumTypeWrapper(_LIGHTSOURCEPRESET)
kOff = 0
kDaylight5000K = 1
kDaylight6500K = 2
kTungsten2800K = 3


DESCRIPTOR.enum_types_by_name['LightSourcePreset'] = _LIGHTSOURCEPRESET
_sym_db.RegisterFileDescriptor(DESCRIPTOR)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    cast as typing___cast,
)


builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

LightSourcePresetValue = typing___NewType('LightSourcePresetValue', builtin___int)
type___LightSourcePresetValue = LightSourcePresetValue
LightSourcePreset: _LightSourcePreset
class _LightSourcePreset(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[LightSourcePresetValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    kOff = typing___cast(LightSourcePresetValue, 0)
    kDaylight5000K = typing___cast(LightSourcePresetValue, 1)
    kDaylight6500K = typing___cast(LightSourcePresetValue, 2)
    kTungsten2800K = typing___cast(LightSourcePresetValue, 3)
kOff = typing___cast(LightSourcePresetValue, 0)
kDaylight5000K = typing___cast(LightSourcePresetValue, 1)
kDaylight6500K = typing___cast(LightSourcePresetValue, 2)
kTungsten2800K = typing___cast(LightSourcePresetValue, 3)

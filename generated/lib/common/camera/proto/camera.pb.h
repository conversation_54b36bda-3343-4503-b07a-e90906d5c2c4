// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/common/camera/proto/camera.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto;
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE
namespace lib {
namespace common {
namespace camera {

enum LightSourcePreset : int {
  kOff = 0,
  kDaylight5000K = 1,
  kDaylight6500K = 2,
  kTungsten2800K = 3,
  LightSourcePreset_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  LightSourcePreset_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool LightSourcePreset_IsValid(int value);
constexpr LightSourcePreset LightSourcePreset_MIN = kOff;
constexpr LightSourcePreset LightSourcePreset_MAX = kTungsten2800K;
constexpr int LightSourcePreset_ARRAYSIZE = LightSourcePreset_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LightSourcePreset_descriptor();
template<typename T>
inline const std::string& LightSourcePreset_Name(T enum_t_value) {
  static_assert(::std::is_same<T, LightSourcePreset>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function LightSourcePreset_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    LightSourcePreset_descriptor(), enum_t_value);
}
inline bool LightSourcePreset_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LightSourcePreset* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<LightSourcePreset>(
    LightSourcePreset_descriptor(), name, value);
}
// ===================================================================


// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace camera
}  // namespace common
}  // namespace lib

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::lib::common::camera::LightSourcePreset> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::lib::common::camera::LightSourcePreset>() {
  return ::lib::common::camera::LightSourcePreset_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto

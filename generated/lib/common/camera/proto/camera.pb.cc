// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/common/camera/proto/camera.proto

#include "lib/common/camera/proto/camera.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace lib {
namespace common {
namespace camera {
}  // namespace camera
}  // namespace common
}  // namespace lib
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto = nullptr;
const uint32_t TableStruct_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto::offsets[1] = {};
static constexpr ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema* schemas = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::Message* const* file_default_instances = nullptr;

const char descriptor_table_protodef_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n$lib/common/camera/proto/camera.proto\022\021"
  "lib.common.camera*Y\n\021LightSourcePreset\022\010"
  "\n\004kOff\020\000\022\022\n\016kDaylight5000K\020\001\022\022\n\016kDayligh"
  "t6500K\020\002\022\022\n\016kTungsten2800K\020\003B\nZ\010proto/cv"
  "b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto = {
  false, false, 168, descriptor_table_protodef_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto, "lib/common/camera/proto/camera.proto", 
  &descriptor_table_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto_once, nullptr, 0, 0,
  schemas, file_default_instances, TableStruct_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto::offsets,
  nullptr, file_level_enum_descriptors_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto, file_level_service_descriptors_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto_getter() {
  return &descriptor_table_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto(&descriptor_table_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto);
namespace lib {
namespace common {
namespace camera {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LightSourcePreset_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto);
  return file_level_enum_descriptors_lib_2fcommon_2fcamera_2fproto_2fcamera_2eproto[0];
}
bool LightSourcePreset_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace camera
}  // namespace common
}  // namespace lib
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

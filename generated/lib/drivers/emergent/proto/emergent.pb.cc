// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: generated/lib/drivers/emergent/proto/emergent.proto

#include "generated/lib/drivers/emergent/proto/emergent.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace emergent {
constexpr GrabRequest::GrabRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timestamp_ms_(int64_t{0})
  , pixel_format_(0)

  , color_temp_(0)

  , exposure_us_(0)
  , gain_db_(0)
  , focus_(0)
  , wb_r_gain_db_(0)
  , wb_gr_gain_db_(0)
  , wb_gb_gain_db_(0)
  , wb_b_gain_db_(0)
  , focusing_mode_(false){}
struct GrabRequestDefaultTypeInternal {
  constexpr GrabRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GrabRequestDefaultTypeInternal() {}
  union {
    GrabRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GrabRequestDefaultTypeInternal _GrabRequest_default_instance_;
constexpr GrabResponse::GrabResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : data_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0})
  , height_(0)
  , width_(0)
  , bits_per_pixel_(0){}
struct GrabResponseDefaultTypeInternal {
  constexpr GrabResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GrabResponseDefaultTypeInternal() {}
  union {
    GrabResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GrabResponseDefaultTypeInternal _GrabResponse_default_instance_;
constexpr GetSensorTempRequest::GetSensorTempRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetSensorTempRequestDefaultTypeInternal {
  constexpr GetSensorTempRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetSensorTempRequestDefaultTypeInternal() {}
  union {
    GetSensorTempRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetSensorTempRequestDefaultTypeInternal _GetSensorTempRequest_default_instance_;
constexpr GetSensorTempResponse::GetSensorTempResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : sensor_temp_c_(0){}
struct GetSensorTempResponseDefaultTypeInternal {
  constexpr GetSensorTempResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetSensorTempResponseDefaultTypeInternal() {}
  union {
    GetSensorTempResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetSensorTempResponseDefaultTypeInternal _GetSensorTempResponse_default_instance_;
}  // namespace emergent
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto[4];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto = nullptr;

const uint32_t TableStruct_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, pixel_format_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, color_temp_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, exposure_us_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, gain_db_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, focus_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, wb_r_gain_db_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, wb_gr_gain_db_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, wb_gb_gain_db_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, wb_b_gain_db_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabRequest, focusing_mode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::emergent::GrabResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::emergent::GrabResponse, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabResponse, height_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabResponse, width_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabResponse, bits_per_pixel_),
  PROTOBUF_FIELD_OFFSET(::emergent::GrabResponse, data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::emergent::GetSensorTempRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::emergent::GetSensorTempResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::emergent::GetSensorTempResponse, sensor_temp_c_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::emergent::GrabRequest)},
  { 17, -1, -1, sizeof(::emergent::GrabResponse)},
  { 28, -1, -1, sizeof(::emergent::GetSensorTempRequest)},
  { 34, -1, -1, sizeof(::emergent::GetSensorTempResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::emergent::_GrabRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::emergent::_GrabResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::emergent::_GetSensorTempRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::emergent::_GetSensorTempResponse_default_instance_),
};

const char descriptor_table_protodef_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n3generated/lib/drivers/emergent/proto/e"
  "mergent.proto\022\010emergent\"\237\002\n\013GrabRequest\022"
  "\024\n\014timestamp_ms\030\001 \001(\003\022+\n\014pixel_format\030\002 "
  "\001(\0162\025.emergent.PixelFormat\022\'\n\ncolor_temp"
  "\030\003 \001(\0162\023.emergent.ColorTemp\022\023\n\013exposure_"
  "us\030\004 \001(\005\022\017\n\007gain_db\030\005 \001(\002\022\r\n\005focus\030\006 \001(\002"
  "\022\024\n\014wb_r_gain_db\030\007 \001(\002\022\025\n\rwb_gr_gain_db\030"
  "\010 \001(\002\022\025\n\rwb_gb_gain_db\030\t \001(\002\022\024\n\014wb_b_gai"
  "n_db\030\n \001(\002\022\025\n\rfocusing_mode\030\013 \001(\010\"i\n\014Gra"
  "bResponse\022\024\n\014timestamp_ms\030\001 \001(\003\022\016\n\006heigh"
  "t\030\002 \001(\005\022\r\n\005width\030\003 \001(\005\022\026\n\016bits_per_pixel"
  "\030\004 \001(\005\022\014\n\004data\030\005 \001(\014\"\026\n\024GetSensorTempReq"
  "uest\".\n\025GetSensorTempResponse\022\025\n\rsensor_"
  "temp_c\030\001 \001(\005*o\n\013PixelFormat\022\016\n\nPF_UNKNOW"
  "N\020\000\022\014\n\010BayerGB8\020\001\022\r\n\tBayerGB10\020\002\022\023\n\017Baye"
  "rGB10Packed\020\003\022\016\n\nRGB8Packed\020\004\022\016\n\nBGR8Pac"
  "ked\020\005*m\n\tColorTemp\022\016\n\nCT_UNKNOWN\020\000\022\n\n\006CT"
  "_Off\020\001\022\014\n\010CT_2800K\020\002\022\014\n\010CT_3000K\020\003\022\014\n\010CT"
  "_4000K\020\004\022\014\n\010CT_5000K\020\005\022\014\n\010CT_6500K\020\0062\236\001\n"
  "\017EmergentService\0227\n\004Grab\022\025.emergent.Grab"
  "Request\032\026.emergent.GrabResponse\"\000\022R\n\rGet"
  "SensorTemp\022\036.emergent.GetSensorTempReque"
  "st\032\037.emergent.GetSensorTempResponse\"\000b\006p"
  "roto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto = {
  false, false, 925, descriptor_table_protodef_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto, "generated/lib/drivers/emergent/proto/emergent.proto", 
  &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_once, nullptr, 0, 4,
  schemas, file_default_instances, TableStruct_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto::offsets,
  file_level_metadata_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto, file_level_enum_descriptors_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto, file_level_service_descriptors_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_getter() {
  return &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto(&descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto);
namespace emergent {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PixelFormat_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto);
  return file_level_enum_descriptors_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto[0];
}
bool PixelFormat_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ColorTemp_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto);
  return file_level_enum_descriptors_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto[1];
}
bool ColorTemp_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class GrabRequest::_Internal {
 public:
};

GrabRequest::GrabRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:emergent.GrabRequest)
}
GrabRequest::GrabRequest(const GrabRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&focusing_mode_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(focusing_mode_));
  // @@protoc_insertion_point(copy_constructor:emergent.GrabRequest)
}

inline void GrabRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&focusing_mode_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(focusing_mode_));
}

GrabRequest::~GrabRequest() {
  // @@protoc_insertion_point(destructor:emergent.GrabRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GrabRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GrabRequest::ArenaDtor(void* object) {
  GrabRequest* _this = reinterpret_cast< GrabRequest* >(object);
  (void)_this;
}
void GrabRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GrabRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GrabRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:emergent.GrabRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&focusing_mode_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(focusing_mode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GrabRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .emergent.PixelFormat pixel_format = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_pixel_format(static_cast<::emergent::PixelFormat>(val));
        } else
          goto handle_unusual;
        continue;
      // .emergent.ColorTemp color_temp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_color_temp(static_cast<::emergent::ColorTemp>(val));
        } else
          goto handle_unusual;
        continue;
      // int32 exposure_us = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          exposure_us_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float gain_db = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          gain_db_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float focus = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          focus_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float wb_r_gain_db = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          wb_r_gain_db_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float wb_gr_gain_db = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          wb_gr_gain_db_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float wb_gb_gain_db = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          wb_gb_gain_db_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float wb_b_gain_db = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 85)) {
          wb_b_gain_db_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // bool focusing_mode = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          focusing_mode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GrabRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:emergent.GrabRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // .emergent.PixelFormat pixel_format = 2;
  if (this->_internal_pixel_format() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_pixel_format(), target);
  }

  // .emergent.ColorTemp color_temp = 3;
  if (this->_internal_color_temp() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_color_temp(), target);
  }

  // int32 exposure_us = 4;
  if (this->_internal_exposure_us() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_exposure_us(), target);
  }

  // float gain_db = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gain_db = this->_internal_gain_db();
  uint32_t raw_gain_db;
  memcpy(&raw_gain_db, &tmp_gain_db, sizeof(tmp_gain_db));
  if (raw_gain_db != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_gain_db(), target);
  }

  // float focus = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focus = this->_internal_focus();
  uint32_t raw_focus;
  memcpy(&raw_focus, &tmp_focus, sizeof(tmp_focus));
  if (raw_focus != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_focus(), target);
  }

  // float wb_r_gain_db = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_r_gain_db = this->_internal_wb_r_gain_db();
  uint32_t raw_wb_r_gain_db;
  memcpy(&raw_wb_r_gain_db, &tmp_wb_r_gain_db, sizeof(tmp_wb_r_gain_db));
  if (raw_wb_r_gain_db != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_wb_r_gain_db(), target);
  }

  // float wb_gr_gain_db = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_gr_gain_db = this->_internal_wb_gr_gain_db();
  uint32_t raw_wb_gr_gain_db;
  memcpy(&raw_wb_gr_gain_db, &tmp_wb_gr_gain_db, sizeof(tmp_wb_gr_gain_db));
  if (raw_wb_gr_gain_db != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_wb_gr_gain_db(), target);
  }

  // float wb_gb_gain_db = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_gb_gain_db = this->_internal_wb_gb_gain_db();
  uint32_t raw_wb_gb_gain_db;
  memcpy(&raw_wb_gb_gain_db, &tmp_wb_gb_gain_db, sizeof(tmp_wb_gb_gain_db));
  if (raw_wb_gb_gain_db != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_wb_gb_gain_db(), target);
  }

  // float wb_b_gain_db = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_b_gain_db = this->_internal_wb_b_gain_db();
  uint32_t raw_wb_b_gain_db;
  memcpy(&raw_wb_b_gain_db, &tmp_wb_b_gain_db, sizeof(tmp_wb_b_gain_db));
  if (raw_wb_b_gain_db != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_wb_b_gain_db(), target);
  }

  // bool focusing_mode = 11;
  if (this->_internal_focusing_mode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(11, this->_internal_focusing_mode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:emergent.GrabRequest)
  return target;
}

size_t GrabRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:emergent.GrabRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // .emergent.PixelFormat pixel_format = 2;
  if (this->_internal_pixel_format() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_pixel_format());
  }

  // .emergent.ColorTemp color_temp = 3;
  if (this->_internal_color_temp() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_color_temp());
  }

  // int32 exposure_us = 4;
  if (this->_internal_exposure_us() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_exposure_us());
  }

  // float gain_db = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gain_db = this->_internal_gain_db();
  uint32_t raw_gain_db;
  memcpy(&raw_gain_db, &tmp_gain_db, sizeof(tmp_gain_db));
  if (raw_gain_db != 0) {
    total_size += 1 + 4;
  }

  // float focus = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focus = this->_internal_focus();
  uint32_t raw_focus;
  memcpy(&raw_focus, &tmp_focus, sizeof(tmp_focus));
  if (raw_focus != 0) {
    total_size += 1 + 4;
  }

  // float wb_r_gain_db = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_r_gain_db = this->_internal_wb_r_gain_db();
  uint32_t raw_wb_r_gain_db;
  memcpy(&raw_wb_r_gain_db, &tmp_wb_r_gain_db, sizeof(tmp_wb_r_gain_db));
  if (raw_wb_r_gain_db != 0) {
    total_size += 1 + 4;
  }

  // float wb_gr_gain_db = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_gr_gain_db = this->_internal_wb_gr_gain_db();
  uint32_t raw_wb_gr_gain_db;
  memcpy(&raw_wb_gr_gain_db, &tmp_wb_gr_gain_db, sizeof(tmp_wb_gr_gain_db));
  if (raw_wb_gr_gain_db != 0) {
    total_size += 1 + 4;
  }

  // float wb_gb_gain_db = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_gb_gain_db = this->_internal_wb_gb_gain_db();
  uint32_t raw_wb_gb_gain_db;
  memcpy(&raw_wb_gb_gain_db, &tmp_wb_gb_gain_db, sizeof(tmp_wb_gb_gain_db));
  if (raw_wb_gb_gain_db != 0) {
    total_size += 1 + 4;
  }

  // float wb_b_gain_db = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_b_gain_db = this->_internal_wb_b_gain_db();
  uint32_t raw_wb_b_gain_db;
  memcpy(&raw_wb_b_gain_db, &tmp_wb_b_gain_db, sizeof(tmp_wb_b_gain_db));
  if (raw_wb_b_gain_db != 0) {
    total_size += 1 + 4;
  }

  // bool focusing_mode = 11;
  if (this->_internal_focusing_mode() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GrabRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GrabRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GrabRequest::GetClassData() const { return &_class_data_; }

void GrabRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GrabRequest *>(to)->MergeFrom(
      static_cast<const GrabRequest &>(from));
}


void GrabRequest::MergeFrom(const GrabRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:emergent.GrabRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_pixel_format() != 0) {
    _internal_set_pixel_format(from._internal_pixel_format());
  }
  if (from._internal_color_temp() != 0) {
    _internal_set_color_temp(from._internal_color_temp());
  }
  if (from._internal_exposure_us() != 0) {
    _internal_set_exposure_us(from._internal_exposure_us());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_gain_db = from._internal_gain_db();
  uint32_t raw_gain_db;
  memcpy(&raw_gain_db, &tmp_gain_db, sizeof(tmp_gain_db));
  if (raw_gain_db != 0) {
    _internal_set_gain_db(from._internal_gain_db());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focus = from._internal_focus();
  uint32_t raw_focus;
  memcpy(&raw_focus, &tmp_focus, sizeof(tmp_focus));
  if (raw_focus != 0) {
    _internal_set_focus(from._internal_focus());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_r_gain_db = from._internal_wb_r_gain_db();
  uint32_t raw_wb_r_gain_db;
  memcpy(&raw_wb_r_gain_db, &tmp_wb_r_gain_db, sizeof(tmp_wb_r_gain_db));
  if (raw_wb_r_gain_db != 0) {
    _internal_set_wb_r_gain_db(from._internal_wb_r_gain_db());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_gr_gain_db = from._internal_wb_gr_gain_db();
  uint32_t raw_wb_gr_gain_db;
  memcpy(&raw_wb_gr_gain_db, &tmp_wb_gr_gain_db, sizeof(tmp_wb_gr_gain_db));
  if (raw_wb_gr_gain_db != 0) {
    _internal_set_wb_gr_gain_db(from._internal_wb_gr_gain_db());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_gb_gain_db = from._internal_wb_gb_gain_db();
  uint32_t raw_wb_gb_gain_db;
  memcpy(&raw_wb_gb_gain_db, &tmp_wb_gb_gain_db, sizeof(tmp_wb_gb_gain_db));
  if (raw_wb_gb_gain_db != 0) {
    _internal_set_wb_gb_gain_db(from._internal_wb_gb_gain_db());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_b_gain_db = from._internal_wb_b_gain_db();
  uint32_t raw_wb_b_gain_db;
  memcpy(&raw_wb_b_gain_db, &tmp_wb_b_gain_db, sizeof(tmp_wb_b_gain_db));
  if (raw_wb_b_gain_db != 0) {
    _internal_set_wb_b_gain_db(from._internal_wb_b_gain_db());
  }
  if (from._internal_focusing_mode() != 0) {
    _internal_set_focusing_mode(from._internal_focusing_mode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GrabRequest::CopyFrom(const GrabRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:emergent.GrabRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GrabRequest::IsInitialized() const {
  return true;
}

void GrabRequest::InternalSwap(GrabRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GrabRequest, focusing_mode_)
      + sizeof(GrabRequest::focusing_mode_)
      - PROTOBUF_FIELD_OFFSET(GrabRequest, timestamp_ms_)>(
          reinterpret_cast<char*>(&timestamp_ms_),
          reinterpret_cast<char*>(&other->timestamp_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GrabRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_getter, &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_once,
      file_level_metadata_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto[0]);
}

// ===================================================================

class GrabResponse::_Internal {
 public:
};

GrabResponse::GrabResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:emergent.GrabResponse)
}
GrabResponse::GrabResponse(const GrabResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArenaForAllocation());
  }
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&bits_per_pixel_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(bits_per_pixel_));
  // @@protoc_insertion_point(copy_constructor:emergent.GrabResponse)
}

inline void GrabResponse::SharedCtor() {
data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bits_per_pixel_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(bits_per_pixel_));
}

GrabResponse::~GrabResponse() {
  // @@protoc_insertion_point(destructor:emergent.GrabResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GrabResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GrabResponse::ArenaDtor(void* object) {
  GrabResponse* _this = reinterpret_cast< GrabResponse* >(object);
  (void)_this;
}
void GrabResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GrabResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GrabResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:emergent.GrabResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  data_.ClearToEmpty();
  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bits_per_pixel_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(bits_per_pixel_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GrabResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 width = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 bits_per_pixel = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          bits_per_pixel_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes data = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GrabResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:emergent.GrabResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_height(), target);
  }

  // int32 width = 3;
  if (this->_internal_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_width(), target);
  }

  // int32 bits_per_pixel = 4;
  if (this->_internal_bits_per_pixel() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_bits_per_pixel(), target);
  }

  // bytes data = 5;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        5, this->_internal_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:emergent.GrabResponse)
  return target;
}

size_t GrabResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:emergent.GrabResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes data = 5;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_height());
  }

  // int32 width = 3;
  if (this->_internal_width() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_width());
  }

  // int32 bits_per_pixel = 4;
  if (this->_internal_bits_per_pixel() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_bits_per_pixel());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GrabResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GrabResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GrabResponse::GetClassData() const { return &_class_data_; }

void GrabResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GrabResponse *>(to)->MergeFrom(
      static_cast<const GrabResponse &>(from));
}


void GrabResponse::MergeFrom(const GrabResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:emergent.GrabResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _internal_set_data(from._internal_data());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_height() != 0) {
    _internal_set_height(from._internal_height());
  }
  if (from._internal_width() != 0) {
    _internal_set_width(from._internal_width());
  }
  if (from._internal_bits_per_pixel() != 0) {
    _internal_set_bits_per_pixel(from._internal_bits_per_pixel());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GrabResponse::CopyFrom(const GrabResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:emergent.GrabResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GrabResponse::IsInitialized() const {
  return true;
}

void GrabResponse::InternalSwap(GrabResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &data_, lhs_arena,
      &other->data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GrabResponse, bits_per_pixel_)
      + sizeof(GrabResponse::bits_per_pixel_)
      - PROTOBUF_FIELD_OFFSET(GrabResponse, timestamp_ms_)>(
          reinterpret_cast<char*>(&timestamp_ms_),
          reinterpret_cast<char*>(&other->timestamp_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GrabResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_getter, &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_once,
      file_level_metadata_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto[1]);
}

// ===================================================================

class GetSensorTempRequest::_Internal {
 public:
};

GetSensorTempRequest::GetSensorTempRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:emergent.GetSensorTempRequest)
}
GetSensorTempRequest::GetSensorTempRequest(const GetSensorTempRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:emergent.GetSensorTempRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetSensorTempRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetSensorTempRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetSensorTempRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_getter, &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_once,
      file_level_metadata_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto[2]);
}

// ===================================================================

class GetSensorTempResponse::_Internal {
 public:
};

GetSensorTempResponse::GetSensorTempResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:emergent.GetSensorTempResponse)
}
GetSensorTempResponse::GetSensorTempResponse(const GetSensorTempResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  sensor_temp_c_ = from.sensor_temp_c_;
  // @@protoc_insertion_point(copy_constructor:emergent.GetSensorTempResponse)
}

inline void GetSensorTempResponse::SharedCtor() {
sensor_temp_c_ = 0;
}

GetSensorTempResponse::~GetSensorTempResponse() {
  // @@protoc_insertion_point(destructor:emergent.GetSensorTempResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetSensorTempResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetSensorTempResponse::ArenaDtor(void* object) {
  GetSensorTempResponse* _this = reinterpret_cast< GetSensorTempResponse* >(object);
  (void)_this;
}
void GetSensorTempResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetSensorTempResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetSensorTempResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:emergent.GetSensorTempResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  sensor_temp_c_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetSensorTempResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 sensor_temp_c = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          sensor_temp_c_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetSensorTempResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:emergent.GetSensorTempResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 sensor_temp_c = 1;
  if (this->_internal_sensor_temp_c() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_sensor_temp_c(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:emergent.GetSensorTempResponse)
  return target;
}

size_t GetSensorTempResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:emergent.GetSensorTempResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 sensor_temp_c = 1;
  if (this->_internal_sensor_temp_c() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_sensor_temp_c());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetSensorTempResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetSensorTempResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetSensorTempResponse::GetClassData() const { return &_class_data_; }

void GetSensorTempResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetSensorTempResponse *>(to)->MergeFrom(
      static_cast<const GetSensorTempResponse &>(from));
}


void GetSensorTempResponse::MergeFrom(const GetSensorTempResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:emergent.GetSensorTempResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_sensor_temp_c() != 0) {
    _internal_set_sensor_temp_c(from._internal_sensor_temp_c());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetSensorTempResponse::CopyFrom(const GetSensorTempResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:emergent.GetSensorTempResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetSensorTempResponse::IsInitialized() const {
  return true;
}

void GetSensorTempResponse::InternalSwap(GetSensorTempResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(sensor_temp_c_, other->sensor_temp_c_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetSensorTempResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_getter, &descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto_once,
      file_level_metadata_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto[3]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace emergent
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::emergent::GrabRequest* Arena::CreateMaybeMessage< ::emergent::GrabRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::emergent::GrabRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::emergent::GrabResponse* Arena::CreateMaybeMessage< ::emergent::GrabResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::emergent::GrabResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::emergent::GetSensorTempRequest* Arena::CreateMaybeMessage< ::emergent::GetSensorTempRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::emergent::GetSensorTempRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::emergent::GetSensorTempResponse* Arena::CreateMaybeMessage< ::emergent::GetSensorTempResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::emergent::GetSensorTempResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: generated/lib/drivers/emergent/proto/emergent.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto;
namespace emergent {
class GetSensorTempRequest;
struct GetSensorTempRequestDefaultTypeInternal;
extern GetSensorTempRequestDefaultTypeInternal _GetSensorTempRequest_default_instance_;
class GetSensorTempResponse;
struct GetSensorTempResponseDefaultTypeInternal;
extern GetSensorTempResponseDefaultTypeInternal _GetSensorTempResponse_default_instance_;
class GrabRequest;
struct GrabRequestDefaultTypeInternal;
extern GrabRequestDefaultTypeInternal _GrabRequest_default_instance_;
class GrabResponse;
struct GrabResponseDefaultTypeInternal;
extern GrabResponseDefaultTypeInternal _GrabResponse_default_instance_;
}  // namespace emergent
PROTOBUF_NAMESPACE_OPEN
template<> ::emergent::GetSensorTempRequest* Arena::CreateMaybeMessage<::emergent::GetSensorTempRequest>(Arena*);
template<> ::emergent::GetSensorTempResponse* Arena::CreateMaybeMessage<::emergent::GetSensorTempResponse>(Arena*);
template<> ::emergent::GrabRequest* Arena::CreateMaybeMessage<::emergent::GrabRequest>(Arena*);
template<> ::emergent::GrabResponse* Arena::CreateMaybeMessage<::emergent::GrabResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace emergent {

enum PixelFormat : int {
  PF_UNKNOWN = 0,
  BayerGB8 = 1,
  BayerGB10 = 2,
  BayerGB10Packed = 3,
  RGB8Packed = 4,
  BGR8Packed = 5,
  PixelFormat_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  PixelFormat_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool PixelFormat_IsValid(int value);
constexpr PixelFormat PixelFormat_MIN = PF_UNKNOWN;
constexpr PixelFormat PixelFormat_MAX = BGR8Packed;
constexpr int PixelFormat_ARRAYSIZE = PixelFormat_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PixelFormat_descriptor();
template<typename T>
inline const std::string& PixelFormat_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PixelFormat>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PixelFormat_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PixelFormat_descriptor(), enum_t_value);
}
inline bool PixelFormat_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PixelFormat* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PixelFormat>(
    PixelFormat_descriptor(), name, value);
}
enum ColorTemp : int {
  CT_UNKNOWN = 0,
  CT_Off = 1,
  CT_2800K = 2,
  CT_3000K = 3,
  CT_4000K = 4,
  CT_5000K = 5,
  CT_6500K = 6,
  ColorTemp_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ColorTemp_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ColorTemp_IsValid(int value);
constexpr ColorTemp ColorTemp_MIN = CT_UNKNOWN;
constexpr ColorTemp ColorTemp_MAX = CT_6500K;
constexpr int ColorTemp_ARRAYSIZE = ColorTemp_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ColorTemp_descriptor();
template<typename T>
inline const std::string& ColorTemp_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ColorTemp>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ColorTemp_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ColorTemp_descriptor(), enum_t_value);
}
inline bool ColorTemp_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ColorTemp* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ColorTemp>(
    ColorTemp_descriptor(), name, value);
}
// ===================================================================

class GrabRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:emergent.GrabRequest) */ {
 public:
  inline GrabRequest() : GrabRequest(nullptr) {}
  ~GrabRequest() override;
  explicit constexpr GrabRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GrabRequest(const GrabRequest& from);
  GrabRequest(GrabRequest&& from) noexcept
    : GrabRequest() {
    *this = ::std::move(from);
  }

  inline GrabRequest& operator=(const GrabRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GrabRequest& operator=(GrabRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GrabRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GrabRequest* internal_default_instance() {
    return reinterpret_cast<const GrabRequest*>(
               &_GrabRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GrabRequest& a, GrabRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GrabRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GrabRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GrabRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GrabRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GrabRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GrabRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GrabRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "emergent.GrabRequest";
  }
  protected:
  explicit GrabRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimestampMsFieldNumber = 1,
    kPixelFormatFieldNumber = 2,
    kColorTempFieldNumber = 3,
    kExposureUsFieldNumber = 4,
    kGainDbFieldNumber = 5,
    kFocusFieldNumber = 6,
    kWbRGainDbFieldNumber = 7,
    kWbGrGainDbFieldNumber = 8,
    kWbGbGainDbFieldNumber = 9,
    kWbBGainDbFieldNumber = 10,
    kFocusingModeFieldNumber = 11,
  };
  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // .emergent.PixelFormat pixel_format = 2;
  void clear_pixel_format();
  ::emergent::PixelFormat pixel_format() const;
  void set_pixel_format(::emergent::PixelFormat value);
  private:
  ::emergent::PixelFormat _internal_pixel_format() const;
  void _internal_set_pixel_format(::emergent::PixelFormat value);
  public:

  // .emergent.ColorTemp color_temp = 3;
  void clear_color_temp();
  ::emergent::ColorTemp color_temp() const;
  void set_color_temp(::emergent::ColorTemp value);
  private:
  ::emergent::ColorTemp _internal_color_temp() const;
  void _internal_set_color_temp(::emergent::ColorTemp value);
  public:

  // int32 exposure_us = 4;
  void clear_exposure_us();
  int32_t exposure_us() const;
  void set_exposure_us(int32_t value);
  private:
  int32_t _internal_exposure_us() const;
  void _internal_set_exposure_us(int32_t value);
  public:

  // float gain_db = 5;
  void clear_gain_db();
  float gain_db() const;
  void set_gain_db(float value);
  private:
  float _internal_gain_db() const;
  void _internal_set_gain_db(float value);
  public:

  // float focus = 6;
  void clear_focus();
  float focus() const;
  void set_focus(float value);
  private:
  float _internal_focus() const;
  void _internal_set_focus(float value);
  public:

  // float wb_r_gain_db = 7;
  void clear_wb_r_gain_db();
  float wb_r_gain_db() const;
  void set_wb_r_gain_db(float value);
  private:
  float _internal_wb_r_gain_db() const;
  void _internal_set_wb_r_gain_db(float value);
  public:

  // float wb_gr_gain_db = 8;
  void clear_wb_gr_gain_db();
  float wb_gr_gain_db() const;
  void set_wb_gr_gain_db(float value);
  private:
  float _internal_wb_gr_gain_db() const;
  void _internal_set_wb_gr_gain_db(float value);
  public:

  // float wb_gb_gain_db = 9;
  void clear_wb_gb_gain_db();
  float wb_gb_gain_db() const;
  void set_wb_gb_gain_db(float value);
  private:
  float _internal_wb_gb_gain_db() const;
  void _internal_set_wb_gb_gain_db(float value);
  public:

  // float wb_b_gain_db = 10;
  void clear_wb_b_gain_db();
  float wb_b_gain_db() const;
  void set_wb_b_gain_db(float value);
  private:
  float _internal_wb_b_gain_db() const;
  void _internal_set_wb_b_gain_db(float value);
  public:

  // bool focusing_mode = 11;
  void clear_focusing_mode();
  bool focusing_mode() const;
  void set_focusing_mode(bool value);
  private:
  bool _internal_focusing_mode() const;
  void _internal_set_focusing_mode(bool value);
  public:

  // @@protoc_insertion_point(class_scope:emergent.GrabRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int64_t timestamp_ms_;
  int pixel_format_;
  int color_temp_;
  int32_t exposure_us_;
  float gain_db_;
  float focus_;
  float wb_r_gain_db_;
  float wb_gr_gain_db_;
  float wb_gb_gain_db_;
  float wb_b_gain_db_;
  bool focusing_mode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto;
};
// -------------------------------------------------------------------

class GrabResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:emergent.GrabResponse) */ {
 public:
  inline GrabResponse() : GrabResponse(nullptr) {}
  ~GrabResponse() override;
  explicit constexpr GrabResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GrabResponse(const GrabResponse& from);
  GrabResponse(GrabResponse&& from) noexcept
    : GrabResponse() {
    *this = ::std::move(from);
  }

  inline GrabResponse& operator=(const GrabResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GrabResponse& operator=(GrabResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GrabResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GrabResponse* internal_default_instance() {
    return reinterpret_cast<const GrabResponse*>(
               &_GrabResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GrabResponse& a, GrabResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GrabResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GrabResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GrabResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GrabResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GrabResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GrabResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GrabResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "emergent.GrabResponse";
  }
  protected:
  explicit GrabResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 5,
    kTimestampMsFieldNumber = 1,
    kHeightFieldNumber = 2,
    kWidthFieldNumber = 3,
    kBitsPerPixelFieldNumber = 4,
  };
  // bytes data = 5;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // int32 height = 2;
  void clear_height();
  int32_t height() const;
  void set_height(int32_t value);
  private:
  int32_t _internal_height() const;
  void _internal_set_height(int32_t value);
  public:

  // int32 width = 3;
  void clear_width();
  int32_t width() const;
  void set_width(int32_t value);
  private:
  int32_t _internal_width() const;
  void _internal_set_width(int32_t value);
  public:

  // int32 bits_per_pixel = 4;
  void clear_bits_per_pixel();
  int32_t bits_per_pixel() const;
  void set_bits_per_pixel(int32_t value);
  private:
  int32_t _internal_bits_per_pixel() const;
  void _internal_set_bits_per_pixel(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:emergent.GrabResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  int64_t timestamp_ms_;
  int32_t height_;
  int32_t width_;
  int32_t bits_per_pixel_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto;
};
// -------------------------------------------------------------------

class GetSensorTempRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:emergent.GetSensorTempRequest) */ {
 public:
  inline GetSensorTempRequest() : GetSensorTempRequest(nullptr) {}
  explicit constexpr GetSensorTempRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetSensorTempRequest(const GetSensorTempRequest& from);
  GetSensorTempRequest(GetSensorTempRequest&& from) noexcept
    : GetSensorTempRequest() {
    *this = ::std::move(from);
  }

  inline GetSensorTempRequest& operator=(const GetSensorTempRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetSensorTempRequest& operator=(GetSensorTempRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetSensorTempRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetSensorTempRequest* internal_default_instance() {
    return reinterpret_cast<const GetSensorTempRequest*>(
               &_GetSensorTempRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GetSensorTempRequest& a, GetSensorTempRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetSensorTempRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetSensorTempRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetSensorTempRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetSensorTempRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetSensorTempRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetSensorTempRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "emergent.GetSensorTempRequest";
  }
  protected:
  explicit GetSensorTempRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:emergent.GetSensorTempRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto;
};
// -------------------------------------------------------------------

class GetSensorTempResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:emergent.GetSensorTempResponse) */ {
 public:
  inline GetSensorTempResponse() : GetSensorTempResponse(nullptr) {}
  ~GetSensorTempResponse() override;
  explicit constexpr GetSensorTempResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetSensorTempResponse(const GetSensorTempResponse& from);
  GetSensorTempResponse(GetSensorTempResponse&& from) noexcept
    : GetSensorTempResponse() {
    *this = ::std::move(from);
  }

  inline GetSensorTempResponse& operator=(const GetSensorTempResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetSensorTempResponse& operator=(GetSensorTempResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetSensorTempResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetSensorTempResponse* internal_default_instance() {
    return reinterpret_cast<const GetSensorTempResponse*>(
               &_GetSensorTempResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetSensorTempResponse& a, GetSensorTempResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetSensorTempResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetSensorTempResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetSensorTempResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetSensorTempResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetSensorTempResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetSensorTempResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetSensorTempResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "emergent.GetSensorTempResponse";
  }
  protected:
  explicit GetSensorTempResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSensorTempCFieldNumber = 1,
  };
  // int32 sensor_temp_c = 1;
  void clear_sensor_temp_c();
  int32_t sensor_temp_c() const;
  void set_sensor_temp_c(int32_t value);
  private:
  int32_t _internal_sensor_temp_c() const;
  void _internal_set_sensor_temp_c(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:emergent.GetSensorTempResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t sensor_temp_c_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GrabRequest

// int64 timestamp_ms = 1;
inline void GrabRequest::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t GrabRequest::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t GrabRequest::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void GrabRequest::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void GrabRequest::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.timestamp_ms)
}

// .emergent.PixelFormat pixel_format = 2;
inline void GrabRequest::clear_pixel_format() {
  pixel_format_ = 0;
}
inline ::emergent::PixelFormat GrabRequest::_internal_pixel_format() const {
  return static_cast< ::emergent::PixelFormat >(pixel_format_);
}
inline ::emergent::PixelFormat GrabRequest::pixel_format() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.pixel_format)
  return _internal_pixel_format();
}
inline void GrabRequest::_internal_set_pixel_format(::emergent::PixelFormat value) {
  
  pixel_format_ = value;
}
inline void GrabRequest::set_pixel_format(::emergent::PixelFormat value) {
  _internal_set_pixel_format(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.pixel_format)
}

// .emergent.ColorTemp color_temp = 3;
inline void GrabRequest::clear_color_temp() {
  color_temp_ = 0;
}
inline ::emergent::ColorTemp GrabRequest::_internal_color_temp() const {
  return static_cast< ::emergent::ColorTemp >(color_temp_);
}
inline ::emergent::ColorTemp GrabRequest::color_temp() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.color_temp)
  return _internal_color_temp();
}
inline void GrabRequest::_internal_set_color_temp(::emergent::ColorTemp value) {
  
  color_temp_ = value;
}
inline void GrabRequest::set_color_temp(::emergent::ColorTemp value) {
  _internal_set_color_temp(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.color_temp)
}

// int32 exposure_us = 4;
inline void GrabRequest::clear_exposure_us() {
  exposure_us_ = 0;
}
inline int32_t GrabRequest::_internal_exposure_us() const {
  return exposure_us_;
}
inline int32_t GrabRequest::exposure_us() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.exposure_us)
  return _internal_exposure_us();
}
inline void GrabRequest::_internal_set_exposure_us(int32_t value) {
  
  exposure_us_ = value;
}
inline void GrabRequest::set_exposure_us(int32_t value) {
  _internal_set_exposure_us(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.exposure_us)
}

// float gain_db = 5;
inline void GrabRequest::clear_gain_db() {
  gain_db_ = 0;
}
inline float GrabRequest::_internal_gain_db() const {
  return gain_db_;
}
inline float GrabRequest::gain_db() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.gain_db)
  return _internal_gain_db();
}
inline void GrabRequest::_internal_set_gain_db(float value) {
  
  gain_db_ = value;
}
inline void GrabRequest::set_gain_db(float value) {
  _internal_set_gain_db(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.gain_db)
}

// float focus = 6;
inline void GrabRequest::clear_focus() {
  focus_ = 0;
}
inline float GrabRequest::_internal_focus() const {
  return focus_;
}
inline float GrabRequest::focus() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.focus)
  return _internal_focus();
}
inline void GrabRequest::_internal_set_focus(float value) {
  
  focus_ = value;
}
inline void GrabRequest::set_focus(float value) {
  _internal_set_focus(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.focus)
}

// float wb_r_gain_db = 7;
inline void GrabRequest::clear_wb_r_gain_db() {
  wb_r_gain_db_ = 0;
}
inline float GrabRequest::_internal_wb_r_gain_db() const {
  return wb_r_gain_db_;
}
inline float GrabRequest::wb_r_gain_db() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.wb_r_gain_db)
  return _internal_wb_r_gain_db();
}
inline void GrabRequest::_internal_set_wb_r_gain_db(float value) {
  
  wb_r_gain_db_ = value;
}
inline void GrabRequest::set_wb_r_gain_db(float value) {
  _internal_set_wb_r_gain_db(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.wb_r_gain_db)
}

// float wb_gr_gain_db = 8;
inline void GrabRequest::clear_wb_gr_gain_db() {
  wb_gr_gain_db_ = 0;
}
inline float GrabRequest::_internal_wb_gr_gain_db() const {
  return wb_gr_gain_db_;
}
inline float GrabRequest::wb_gr_gain_db() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.wb_gr_gain_db)
  return _internal_wb_gr_gain_db();
}
inline void GrabRequest::_internal_set_wb_gr_gain_db(float value) {
  
  wb_gr_gain_db_ = value;
}
inline void GrabRequest::set_wb_gr_gain_db(float value) {
  _internal_set_wb_gr_gain_db(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.wb_gr_gain_db)
}

// float wb_gb_gain_db = 9;
inline void GrabRequest::clear_wb_gb_gain_db() {
  wb_gb_gain_db_ = 0;
}
inline float GrabRequest::_internal_wb_gb_gain_db() const {
  return wb_gb_gain_db_;
}
inline float GrabRequest::wb_gb_gain_db() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.wb_gb_gain_db)
  return _internal_wb_gb_gain_db();
}
inline void GrabRequest::_internal_set_wb_gb_gain_db(float value) {
  
  wb_gb_gain_db_ = value;
}
inline void GrabRequest::set_wb_gb_gain_db(float value) {
  _internal_set_wb_gb_gain_db(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.wb_gb_gain_db)
}

// float wb_b_gain_db = 10;
inline void GrabRequest::clear_wb_b_gain_db() {
  wb_b_gain_db_ = 0;
}
inline float GrabRequest::_internal_wb_b_gain_db() const {
  return wb_b_gain_db_;
}
inline float GrabRequest::wb_b_gain_db() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.wb_b_gain_db)
  return _internal_wb_b_gain_db();
}
inline void GrabRequest::_internal_set_wb_b_gain_db(float value) {
  
  wb_b_gain_db_ = value;
}
inline void GrabRequest::set_wb_b_gain_db(float value) {
  _internal_set_wb_b_gain_db(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.wb_b_gain_db)
}

// bool focusing_mode = 11;
inline void GrabRequest::clear_focusing_mode() {
  focusing_mode_ = false;
}
inline bool GrabRequest::_internal_focusing_mode() const {
  return focusing_mode_;
}
inline bool GrabRequest::focusing_mode() const {
  // @@protoc_insertion_point(field_get:emergent.GrabRequest.focusing_mode)
  return _internal_focusing_mode();
}
inline void GrabRequest::_internal_set_focusing_mode(bool value) {
  
  focusing_mode_ = value;
}
inline void GrabRequest::set_focusing_mode(bool value) {
  _internal_set_focusing_mode(value);
  // @@protoc_insertion_point(field_set:emergent.GrabRequest.focusing_mode)
}

// -------------------------------------------------------------------

// GrabResponse

// int64 timestamp_ms = 1;
inline void GrabResponse::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t GrabResponse::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t GrabResponse::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:emergent.GrabResponse.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void GrabResponse::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void GrabResponse::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:emergent.GrabResponse.timestamp_ms)
}

// int32 height = 2;
inline void GrabResponse::clear_height() {
  height_ = 0;
}
inline int32_t GrabResponse::_internal_height() const {
  return height_;
}
inline int32_t GrabResponse::height() const {
  // @@protoc_insertion_point(field_get:emergent.GrabResponse.height)
  return _internal_height();
}
inline void GrabResponse::_internal_set_height(int32_t value) {
  
  height_ = value;
}
inline void GrabResponse::set_height(int32_t value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:emergent.GrabResponse.height)
}

// int32 width = 3;
inline void GrabResponse::clear_width() {
  width_ = 0;
}
inline int32_t GrabResponse::_internal_width() const {
  return width_;
}
inline int32_t GrabResponse::width() const {
  // @@protoc_insertion_point(field_get:emergent.GrabResponse.width)
  return _internal_width();
}
inline void GrabResponse::_internal_set_width(int32_t value) {
  
  width_ = value;
}
inline void GrabResponse::set_width(int32_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:emergent.GrabResponse.width)
}

// int32 bits_per_pixel = 4;
inline void GrabResponse::clear_bits_per_pixel() {
  bits_per_pixel_ = 0;
}
inline int32_t GrabResponse::_internal_bits_per_pixel() const {
  return bits_per_pixel_;
}
inline int32_t GrabResponse::bits_per_pixel() const {
  // @@protoc_insertion_point(field_get:emergent.GrabResponse.bits_per_pixel)
  return _internal_bits_per_pixel();
}
inline void GrabResponse::_internal_set_bits_per_pixel(int32_t value) {
  
  bits_per_pixel_ = value;
}
inline void GrabResponse::set_bits_per_pixel(int32_t value) {
  _internal_set_bits_per_pixel(value);
  // @@protoc_insertion_point(field_set:emergent.GrabResponse.bits_per_pixel)
}

// bytes data = 5;
inline void GrabResponse::clear_data() {
  data_.ClearToEmpty();
}
inline const std::string& GrabResponse::data() const {
  // @@protoc_insertion_point(field_get:emergent.GrabResponse.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GrabResponse::set_data(ArgT0&& arg0, ArgT... args) {
 
 data_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:emergent.GrabResponse.data)
}
inline std::string* GrabResponse::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:emergent.GrabResponse.data)
  return _s;
}
inline const std::string& GrabResponse::_internal_data() const {
  return data_.Get();
}
inline void GrabResponse::_internal_set_data(const std::string& value) {
  
  data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GrabResponse::_internal_mutable_data() {
  
  return data_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GrabResponse::release_data() {
  // @@protoc_insertion_point(field_release:emergent.GrabResponse.data)
  return data_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GrabResponse::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  data_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (data_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:emergent.GrabResponse.data)
}

// -------------------------------------------------------------------

// GetSensorTempRequest

// -------------------------------------------------------------------

// GetSensorTempResponse

// int32 sensor_temp_c = 1;
inline void GetSensorTempResponse::clear_sensor_temp_c() {
  sensor_temp_c_ = 0;
}
inline int32_t GetSensorTempResponse::_internal_sensor_temp_c() const {
  return sensor_temp_c_;
}
inline int32_t GetSensorTempResponse::sensor_temp_c() const {
  // @@protoc_insertion_point(field_get:emergent.GetSensorTempResponse.sensor_temp_c)
  return _internal_sensor_temp_c();
}
inline void GetSensorTempResponse::_internal_set_sensor_temp_c(int32_t value) {
  
  sensor_temp_c_ = value;
}
inline void GetSensorTempResponse::set_sensor_temp_c(int32_t value) {
  _internal_set_sensor_temp_c(value);
  // @@protoc_insertion_point(field_set:emergent.GetSensorTempResponse.sensor_temp_c)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace emergent

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::emergent::PixelFormat> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::emergent::PixelFormat>() {
  return ::emergent::PixelFormat_descriptor();
}
template <> struct is_proto_enum< ::emergent::ColorTemp> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::emergent::ColorTemp>() {
  return ::emergent::ColorTemp_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto

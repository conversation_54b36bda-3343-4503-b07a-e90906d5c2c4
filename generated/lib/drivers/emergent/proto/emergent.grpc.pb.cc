// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: generated/lib/drivers/emergent/proto/emergent.proto

#include "generated/lib/drivers/emergent/proto/emergent.pb.h"
#include "generated/lib/drivers/emergent/proto/emergent.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace emergent {

static const char* EmergentService_method_names[] = {
  "/emergent.EmergentService/Grab",
  "/emergent.EmergentService/GetSensorTemp",
};

std::unique_ptr< EmergentService::Stub> EmergentService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< EmergentService::Stub> stub(new EmergentService::Stub(channel, options));
  return stub;
}

EmergentService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Grab_(EmergentService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSensorTemp_(EmergentService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status EmergentService::Stub::Grab(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::emergent::GrabResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::emergent::GrabRequest, ::emergent::GrabResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Grab_, context, request, response);
}

void EmergentService::Stub::async::Grab(::grpc::ClientContext* context, const ::emergent::GrabRequest* request, ::emergent::GrabResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::emergent::GrabRequest, ::emergent::GrabResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Grab_, context, request, response, std::move(f));
}

void EmergentService::Stub::async::Grab(::grpc::ClientContext* context, const ::emergent::GrabRequest* request, ::emergent::GrabResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Grab_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::emergent::GrabResponse>* EmergentService::Stub::PrepareAsyncGrabRaw(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::emergent::GrabResponse, ::emergent::GrabRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Grab_, context, request);
}

::grpc::ClientAsyncResponseReader< ::emergent::GrabResponse>* EmergentService::Stub::AsyncGrabRaw(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGrabRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status EmergentService::Stub::GetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::emergent::GetSensorTempResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::emergent::GetSensorTempRequest, ::emergent::GetSensorTempResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSensorTemp_, context, request, response);
}

void EmergentService::Stub::async::GetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest* request, ::emergent::GetSensorTempResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::emergent::GetSensorTempRequest, ::emergent::GetSensorTempResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSensorTemp_, context, request, response, std::move(f));
}

void EmergentService::Stub::async::GetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest* request, ::emergent::GetSensorTempResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSensorTemp_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::emergent::GetSensorTempResponse>* EmergentService::Stub::PrepareAsyncGetSensorTempRaw(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::emergent::GetSensorTempResponse, ::emergent::GetSensorTempRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSensorTemp_, context, request);
}

::grpc::ClientAsyncResponseReader< ::emergent::GetSensorTempResponse>* EmergentService::Stub::AsyncGetSensorTempRaw(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSensorTempRaw(context, request, cq);
  result->StartCall();
  return result;
}

EmergentService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      EmergentService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< EmergentService::Service, ::emergent::GrabRequest, ::emergent::GrabResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](EmergentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::emergent::GrabRequest* req,
             ::emergent::GrabResponse* resp) {
               return service->Grab(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      EmergentService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< EmergentService::Service, ::emergent::GetSensorTempRequest, ::emergent::GetSensorTempResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](EmergentService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::emergent::GetSensorTempRequest* req,
             ::emergent::GetSensorTempResponse* resp) {
               return service->GetSensorTemp(ctx, req, resp);
             }, this)));
}

EmergentService::Service::~Service() {
}

::grpc::Status EmergentService::Service::Grab(::grpc::ServerContext* context, const ::emergent::GrabRequest* request, ::emergent::GrabResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status EmergentService::Service::GetSensorTemp(::grpc::ServerContext* context, const ::emergent::GetSensorTempRequest* request, ::emergent::GetSensorTempResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace emergent


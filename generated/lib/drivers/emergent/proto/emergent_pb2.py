# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/emergent/proto/emergent.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/emergent/proto/emergent.proto',
  package='emergent',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n3generated/lib/drivers/emergent/proto/emergent.proto\x12\x08\x65mergent\"\x9f\x02\n\x0bGrabRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12+\n\x0cpixel_format\x18\x02 \x01(\x0e\x32\x15.emergent.PixelFormat\x12\'\n\ncolor_temp\x18\x03 \x01(\x0e\x32\x13.emergent.ColorTemp\x12\x13\n\x0b\x65xposure_us\x18\x04 \x01(\x05\x12\x0f\n\x07gain_db\x18\x05 \x01(\x02\x12\r\n\x05\x66ocus\x18\x06 \x01(\x02\x12\x14\n\x0cwb_r_gain_db\x18\x07 \x01(\x02\x12\x15\n\rwb_gr_gain_db\x18\x08 \x01(\x02\x12\x15\n\rwb_gb_gain_db\x18\t \x01(\x02\x12\x14\n\x0cwb_b_gain_db\x18\n \x01(\x02\x12\x15\n\rfocusing_mode\x18\x0b \x01(\x08\"i\n\x0cGrabResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x0e\n\x06height\x18\x02 \x01(\x05\x12\r\n\x05width\x18\x03 \x01(\x05\x12\x16\n\x0e\x62its_per_pixel\x18\x04 \x01(\x05\x12\x0c\n\x04\x64\x61ta\x18\x05 \x01(\x0c\"\x16\n\x14GetSensorTempRequest\".\n\x15GetSensorTempResponse\x12\x15\n\rsensor_temp_c\x18\x01 \x01(\x05*o\n\x0bPixelFormat\x12\x0e\n\nPF_UNKNOWN\x10\x00\x12\x0c\n\x08\x42\x61yerGB8\x10\x01\x12\r\n\tBayerGB10\x10\x02\x12\x13\n\x0f\x42\x61yerGB10Packed\x10\x03\x12\x0e\n\nRGB8Packed\x10\x04\x12\x0e\n\nBGR8Packed\x10\x05*m\n\tColorTemp\x12\x0e\n\nCT_UNKNOWN\x10\x00\x12\n\n\x06\x43T_Off\x10\x01\x12\x0c\n\x08\x43T_2800K\x10\x02\x12\x0c\n\x08\x43T_3000K\x10\x03\x12\x0c\n\x08\x43T_4000K\x10\x04\x12\x0c\n\x08\x43T_5000K\x10\x05\x12\x0c\n\x08\x43T_6500K\x10\x06\x32\x9e\x01\n\x0f\x45mergentService\x12\x37\n\x04Grab\x12\x15.emergent.GrabRequest\x1a\x16.emergent.GrabResponse\"\x00\x12R\n\rGetSensorTemp\x12\x1e.emergent.GetSensorTempRequest\x1a\x1f.emergent.GetSensorTempResponse\"\x00\x62\x06proto3'
)

_PIXELFORMAT = _descriptor.EnumDescriptor(
  name='PixelFormat',
  full_name='emergent.PixelFormat',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PF_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BayerGB8', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BayerGB10', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BayerGB10Packed', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RGB8Packed', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BGR8Packed', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=534,
  serialized_end=645,
)
_sym_db.RegisterEnumDescriptor(_PIXELFORMAT)

PixelFormat = enum_type_wrapper.EnumTypeWrapper(_PIXELFORMAT)
_COLORTEMP = _descriptor.EnumDescriptor(
  name='ColorTemp',
  full_name='emergent.ColorTemp',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CT_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CT_Off', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CT_2800K', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CT_3000K', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CT_4000K', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CT_5000K', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CT_6500K', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=647,
  serialized_end=756,
)
_sym_db.RegisterEnumDescriptor(_COLORTEMP)

ColorTemp = enum_type_wrapper.EnumTypeWrapper(_COLORTEMP)
PF_UNKNOWN = 0
BayerGB8 = 1
BayerGB10 = 2
BayerGB10Packed = 3
RGB8Packed = 4
BGR8Packed = 5
CT_UNKNOWN = 0
CT_Off = 1
CT_2800K = 2
CT_3000K = 3
CT_4000K = 4
CT_5000K = 5
CT_6500K = 6



_GRABREQUEST = _descriptor.Descriptor(
  name='GrabRequest',
  full_name='emergent.GrabRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='emergent.GrabRequest.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pixel_format', full_name='emergent.GrabRequest.pixel_format', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='color_temp', full_name='emergent.GrabRequest.color_temp', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exposure_us', full_name='emergent.GrabRequest.exposure_us', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_db', full_name='emergent.GrabRequest.gain_db', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focus', full_name='emergent.GrabRequest.focus', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_r_gain_db', full_name='emergent.GrabRequest.wb_r_gain_db', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_gr_gain_db', full_name='emergent.GrabRequest.wb_gr_gain_db', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_gb_gain_db', full_name='emergent.GrabRequest.wb_gb_gain_db', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_b_gain_db', full_name='emergent.GrabRequest.wb_b_gain_db', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focusing_mode', full_name='emergent.GrabRequest.focusing_mode', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=66,
  serialized_end=353,
)


_GRABRESPONSE = _descriptor.Descriptor(
  name='GrabResponse',
  full_name='emergent.GrabResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='emergent.GrabResponse.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='emergent.GrabResponse.height', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width', full_name='emergent.GrabResponse.width', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bits_per_pixel', full_name='emergent.GrabResponse.bits_per_pixel', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='emergent.GrabResponse.data', index=4,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=355,
  serialized_end=460,
)


_GETSENSORTEMPREQUEST = _descriptor.Descriptor(
  name='GetSensorTempRequest',
  full_name='emergent.GetSensorTempRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=462,
  serialized_end=484,
)


_GETSENSORTEMPRESPONSE = _descriptor.Descriptor(
  name='GetSensorTempResponse',
  full_name='emergent.GetSensorTempResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor_temp_c', full_name='emergent.GetSensorTempResponse.sensor_temp_c', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=486,
  serialized_end=532,
)

_GRABREQUEST.fields_by_name['pixel_format'].enum_type = _PIXELFORMAT
_GRABREQUEST.fields_by_name['color_temp'].enum_type = _COLORTEMP
DESCRIPTOR.message_types_by_name['GrabRequest'] = _GRABREQUEST
DESCRIPTOR.message_types_by_name['GrabResponse'] = _GRABRESPONSE
DESCRIPTOR.message_types_by_name['GetSensorTempRequest'] = _GETSENSORTEMPREQUEST
DESCRIPTOR.message_types_by_name['GetSensorTempResponse'] = _GETSENSORTEMPRESPONSE
DESCRIPTOR.enum_types_by_name['PixelFormat'] = _PIXELFORMAT
DESCRIPTOR.enum_types_by_name['ColorTemp'] = _COLORTEMP
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GrabRequest = _reflection.GeneratedProtocolMessageType('GrabRequest', (_message.Message,), {
  'DESCRIPTOR' : _GRABREQUEST,
  '__module__' : 'generated.lib.drivers.emergent.proto.emergent_pb2'
  # @@protoc_insertion_point(class_scope:emergent.GrabRequest)
  })
_sym_db.RegisterMessage(GrabRequest)

GrabResponse = _reflection.GeneratedProtocolMessageType('GrabResponse', (_message.Message,), {
  'DESCRIPTOR' : _GRABRESPONSE,
  '__module__' : 'generated.lib.drivers.emergent.proto.emergent_pb2'
  # @@protoc_insertion_point(class_scope:emergent.GrabResponse)
  })
_sym_db.RegisterMessage(GrabResponse)

GetSensorTempRequest = _reflection.GeneratedProtocolMessageType('GetSensorTempRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSENSORTEMPREQUEST,
  '__module__' : 'generated.lib.drivers.emergent.proto.emergent_pb2'
  # @@protoc_insertion_point(class_scope:emergent.GetSensorTempRequest)
  })
_sym_db.RegisterMessage(GetSensorTempRequest)

GetSensorTempResponse = _reflection.GeneratedProtocolMessageType('GetSensorTempResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSENSORTEMPRESPONSE,
  '__module__' : 'generated.lib.drivers.emergent.proto.emergent_pb2'
  # @@protoc_insertion_point(class_scope:emergent.GetSensorTempResponse)
  })
_sym_db.RegisterMessage(GetSensorTempResponse)



_EMERGENTSERVICE = _descriptor.ServiceDescriptor(
  name='EmergentService',
  full_name='emergent.EmergentService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=759,
  serialized_end=917,
  methods=[
  _descriptor.MethodDescriptor(
    name='Grab',
    full_name='emergent.EmergentService.Grab',
    index=0,
    containing_service=None,
    input_type=_GRABREQUEST,
    output_type=_GRABRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSensorTemp',
    full_name='emergent.EmergentService.GetSensorTemp',
    index=1,
    containing_service=None,
    input_type=_GETSENSORTEMPREQUEST,
    output_type=_GETSENSORTEMPRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_EMERGENTSERVICE)

DESCRIPTOR.services_by_name['EmergentService'] = _EMERGENTSERVICE

# @@protoc_insertion_point(module_scope)

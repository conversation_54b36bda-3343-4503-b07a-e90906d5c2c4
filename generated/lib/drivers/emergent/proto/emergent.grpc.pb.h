// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: generated/lib/drivers/emergent/proto/emergent.proto
#ifndef GRPC_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto__INCLUDED
#define GRPC_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto__INCLUDED

#include "generated/lib/drivers/emergent/proto/emergent.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace emergent {

class EmergentService final {
 public:
  static constexpr char const* service_full_name() {
    return "emergent.EmergentService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Grab(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::emergent::GrabResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GrabResponse>> AsyncGrab(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GrabResponse>>(AsyncGrabRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GrabResponse>> PrepareAsyncGrab(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GrabResponse>>(PrepareAsyncGrabRaw(context, request, cq));
    }
    virtual ::grpc::Status GetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::emergent::GetSensorTempResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GetSensorTempResponse>> AsyncGetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GetSensorTempResponse>>(AsyncGetSensorTempRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GetSensorTempResponse>> PrepareAsyncGetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GetSensorTempResponse>>(PrepareAsyncGetSensorTempRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Grab(::grpc::ClientContext* context, const ::emergent::GrabRequest* request, ::emergent::GrabResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Grab(::grpc::ClientContext* context, const ::emergent::GrabRequest* request, ::emergent::GrabResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest* request, ::emergent::GetSensorTempResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest* request, ::emergent::GetSensorTempResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GrabResponse>* AsyncGrabRaw(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GrabResponse>* PrepareAsyncGrabRaw(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GetSensorTempResponse>* AsyncGetSensorTempRaw(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::emergent::GetSensorTempResponse>* PrepareAsyncGetSensorTempRaw(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Grab(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::emergent::GrabResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::emergent::GrabResponse>> AsyncGrab(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::emergent::GrabResponse>>(AsyncGrabRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::emergent::GrabResponse>> PrepareAsyncGrab(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::emergent::GrabResponse>>(PrepareAsyncGrabRaw(context, request, cq));
    }
    ::grpc::Status GetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::emergent::GetSensorTempResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::emergent::GetSensorTempResponse>> AsyncGetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::emergent::GetSensorTempResponse>>(AsyncGetSensorTempRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::emergent::GetSensorTempResponse>> PrepareAsyncGetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::emergent::GetSensorTempResponse>>(PrepareAsyncGetSensorTempRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Grab(::grpc::ClientContext* context, const ::emergent::GrabRequest* request, ::emergent::GrabResponse* response, std::function<void(::grpc::Status)>) override;
      void Grab(::grpc::ClientContext* context, const ::emergent::GrabRequest* request, ::emergent::GrabResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest* request, ::emergent::GetSensorTempResponse* response, std::function<void(::grpc::Status)>) override;
      void GetSensorTemp(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest* request, ::emergent::GetSensorTempResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::emergent::GrabResponse>* AsyncGrabRaw(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::emergent::GrabResponse>* PrepareAsyncGrabRaw(::grpc::ClientContext* context, const ::emergent::GrabRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::emergent::GetSensorTempResponse>* AsyncGetSensorTempRaw(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::emergent::GetSensorTempResponse>* PrepareAsyncGetSensorTempRaw(::grpc::ClientContext* context, const ::emergent::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Grab_;
    const ::grpc::internal::RpcMethod rpcmethod_GetSensorTemp_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Grab(::grpc::ServerContext* context, const ::emergent::GrabRequest* request, ::emergent::GrabResponse* response);
    virtual ::grpc::Status GetSensorTemp(::grpc::ServerContext* context, const ::emergent::GetSensorTempRequest* request, ::emergent::GetSensorTempResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Grab() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::emergent::GrabRequest* /*request*/, ::emergent::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGrab(::grpc::ServerContext* context, ::emergent::GrabRequest* request, ::grpc::ServerAsyncResponseWriter< ::emergent::GrabResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetSensorTemp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetSensorTemp() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetSensorTemp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSensorTemp(::grpc::ServerContext* /*context*/, const ::emergent::GetSensorTempRequest* /*request*/, ::emergent::GetSensorTempResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSensorTemp(::grpc::ServerContext* context, ::emergent::GetSensorTempRequest* request, ::grpc::ServerAsyncResponseWriter< ::emergent::GetSensorTempResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Grab<WithAsyncMethod_GetSensorTemp<Service > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Grab() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::emergent::GrabRequest, ::emergent::GrabResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::emergent::GrabRequest* request, ::emergent::GrabResponse* response) { return this->Grab(context, request, response); }));}
    void SetMessageAllocatorFor_Grab(
        ::grpc::MessageAllocator< ::emergent::GrabRequest, ::emergent::GrabResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::emergent::GrabRequest, ::emergent::GrabResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::emergent::GrabRequest* /*request*/, ::emergent::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Grab(
      ::grpc::CallbackServerContext* /*context*/, const ::emergent::GrabRequest* /*request*/, ::emergent::GrabResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetSensorTemp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetSensorTemp() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::emergent::GetSensorTempRequest, ::emergent::GetSensorTempResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::emergent::GetSensorTempRequest* request, ::emergent::GetSensorTempResponse* response) { return this->GetSensorTemp(context, request, response); }));}
    void SetMessageAllocatorFor_GetSensorTemp(
        ::grpc::MessageAllocator< ::emergent::GetSensorTempRequest, ::emergent::GetSensorTempResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::emergent::GetSensorTempRequest, ::emergent::GetSensorTempResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetSensorTemp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSensorTemp(::grpc::ServerContext* /*context*/, const ::emergent::GetSensorTempRequest* /*request*/, ::emergent::GetSensorTempResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSensorTemp(
      ::grpc::CallbackServerContext* /*context*/, const ::emergent::GetSensorTempRequest* /*request*/, ::emergent::GetSensorTempResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Grab<WithCallbackMethod_GetSensorTemp<Service > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Grab() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::emergent::GrabRequest* /*request*/, ::emergent::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetSensorTemp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetSensorTemp() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetSensorTemp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSensorTemp(::grpc::ServerContext* /*context*/, const ::emergent::GetSensorTempRequest* /*request*/, ::emergent::GetSensorTempResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Grab() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::emergent::GrabRequest* /*request*/, ::emergent::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGrab(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetSensorTemp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetSensorTemp() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetSensorTemp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSensorTemp(::grpc::ServerContext* /*context*/, const ::emergent::GetSensorTempRequest* /*request*/, ::emergent::GetSensorTempResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSensorTemp(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Grab() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Grab(context, request, response); }));
    }
    ~WithRawCallbackMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::emergent::GrabRequest* /*request*/, ::emergent::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Grab(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetSensorTemp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetSensorTemp() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetSensorTemp(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetSensorTemp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSensorTemp(::grpc::ServerContext* /*context*/, const ::emergent::GetSensorTempRequest* /*request*/, ::emergent::GetSensorTempResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSensorTemp(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Grab() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::emergent::GrabRequest, ::emergent::GrabResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::emergent::GrabRequest, ::emergent::GrabResponse>* streamer) {
                       return this->StreamedGrab(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::emergent::GrabRequest* /*request*/, ::emergent::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGrab(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::emergent::GrabRequest,::emergent::GrabResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetSensorTemp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetSensorTemp() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::emergent::GetSensorTempRequest, ::emergent::GetSensorTempResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::emergent::GetSensorTempRequest, ::emergent::GetSensorTempResponse>* streamer) {
                       return this->StreamedGetSensorTemp(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetSensorTemp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetSensorTemp(::grpc::ServerContext* /*context*/, const ::emergent::GetSensorTempRequest* /*request*/, ::emergent::GetSensorTempResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetSensorTemp(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::emergent::GetSensorTempRequest,::emergent::GetSensorTempResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Grab<WithStreamedUnaryMethod_GetSensorTemp<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Grab<WithStreamedUnaryMethod_GetSensorTemp<Service > > StreamedService;
};

}  // namespace emergent


#endif  // GRPC_generated_2flib_2fdrivers_2femergent_2fproto_2femergent_2eproto__INCLUDED

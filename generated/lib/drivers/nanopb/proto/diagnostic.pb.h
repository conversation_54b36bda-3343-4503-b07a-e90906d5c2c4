/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_DIAGNOSTIC_DIAGNOSTIC_PB_H_INCLUDED
#define PB_DIAGNOSTIC_DIAGNOSTIC_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _diagnostic_Ping {
    int32_t x;
} diagnostic_Ping;

typedef struct _diagnostic_Pong {
    int32_t x;
} diagnostic_Pong;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define diagnostic_Ping_init_default             {0}
#define diagnostic_Pong_init_default             {0}
#define diagnostic_Ping_init_zero                {0}
#define diagnostic_Pong_init_zero                {0}

/* Field tags (for use in manual encoding/decoding) */
#define diagnostic_Ping_x_tag                    1
#define diagnostic_Pong_x_tag                    1

/* Struct field encoding specification for nanopb */
#define diagnostic_Ping_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    x,                 1)
#define diagnostic_Ping_CALLBACK NULL
#define diagnostic_Ping_DEFAULT NULL

#define diagnostic_Pong_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    x,                 1)
#define diagnostic_Pong_CALLBACK NULL
#define diagnostic_Pong_DEFAULT NULL

extern const pb_msgdesc_t diagnostic_Ping_msg;
extern const pb_msgdesc_t diagnostic_Pong_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define diagnostic_Ping_fields &diagnostic_Ping_msg
#define diagnostic_Pong_fields &diagnostic_Pong_msg

/* Maximum encoded size of messages (where known) */
#define diagnostic_Ping_size                     11
#define diagnostic_Pong_size                     11

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

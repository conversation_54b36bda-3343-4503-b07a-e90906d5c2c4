"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

CarrierPhaseSolnValue = typing___NewType('CarrierPhaseSolnValue', builtin___int)
type___CarrierPhaseSolnValue = CarrierPhaseSolnValue
CarrierPhaseSoln: _CarrierPhaseSoln
class _CarrierPhaseSoln(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[CarrierPhaseSolnValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NONE = typing___cast(CarrierPhaseSolnValue, 0)
    FLOATING = typing___cast(CarrierPhaseSolnValue, 1)
    FIXED = typing___cast(CarrierPhaseSolnValue, 2)
NONE = typing___cast(CarrierPhaseSolnValue, 0)
FLOATING = typing___cast(CarrierPhaseSolnValue, 1)
FIXED = typing___cast(CarrierPhaseSolnValue, 2)

class Position_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Position_Request = Position_Request

class Spartn_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    data: builtin___bytes = ...
    end: builtin___bool = ...

    def __init__(self,
        *,
        data : typing___Optional[builtin___bytes] = None,
        end : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data",u"end",b"end"]) -> None: ...
type___Spartn_Request = Spartn_Request

class Spartn_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Spartn_Reply = Spartn_Reply

class Rtcm_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    data: builtin___bytes = ...
    end: builtin___bool = ...

    def __init__(self,
        *,
        data : typing___Optional[builtin___bytes] = None,
        end : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data",u"end",b"end"]) -> None: ...
type___Rtcm_Request = Rtcm_Request

class Rtcm_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Rtcm_Reply = Rtcm_Reply

class ValueWithAccuracy(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    value: builtin___float = ...
    accuracy: builtin___float = ...

    def __init__(self,
        *,
        value : typing___Optional[builtin___float] = None,
        accuracy : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"accuracy",b"accuracy",u"value",b"value"]) -> None: ...
type___ValueWithAccuracy = ValueWithAccuracy

class DualGpsData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    gnss_valid: builtin___bool = ...
    diff_corrections: builtin___bool = ...
    is_moving_base: builtin___bool = ...
    carrier_phase: type___CarrierPhaseSolnValue = ...
    timestamp_ms: builtin___int = ...

    @property
    def north(self) -> type___ValueWithAccuracy: ...

    @property
    def east(self) -> type___ValueWithAccuracy: ...

    @property
    def down(self) -> type___ValueWithAccuracy: ...

    @property
    def length(self) -> type___ValueWithAccuracy: ...

    @property
    def heading(self) -> type___ValueWithAccuracy: ...

    def __init__(self,
        *,
        gnss_valid : typing___Optional[builtin___bool] = None,
        diff_corrections : typing___Optional[builtin___bool] = None,
        is_moving_base : typing___Optional[builtin___bool] = None,
        carrier_phase : typing___Optional[type___CarrierPhaseSolnValue] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        north : typing___Optional[type___ValueWithAccuracy] = None,
        east : typing___Optional[type___ValueWithAccuracy] = None,
        down : typing___Optional[type___ValueWithAccuracy] = None,
        length : typing___Optional[type___ValueWithAccuracy] = None,
        heading : typing___Optional[type___ValueWithAccuracy] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_down",b"_down",u"_east",b"_east",u"_heading",b"_heading",u"_length",b"_length",u"_north",b"_north",u"down",b"down",u"east",b"east",u"heading",b"heading",u"length",b"length",u"north",b"north"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_down",b"_down",u"_east",b"_east",u"_heading",b"_heading",u"_length",b"_length",u"_north",b"_north",u"carrier_phase",b"carrier_phase",u"diff_corrections",b"diff_corrections",u"down",b"down",u"east",b"east",u"gnss_valid",b"gnss_valid",u"heading",b"heading",u"is_moving_base",b"is_moving_base",u"length",b"length",u"north",b"north",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_down",b"_down"]) -> typing_extensions___Literal["down"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_east",b"_east"]) -> typing_extensions___Literal["east"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_heading",b"_heading"]) -> typing_extensions___Literal["heading"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_length",b"_length"]) -> typing_extensions___Literal["length"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_north",b"_north"]) -> typing_extensions___Literal["north"]: ...
type___DualGpsData = DualGpsData

class Position_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    have_fix: builtin___bool = ...
    latitude: builtin___float = ...
    longitude: builtin___float = ...
    num_sats: builtin___int = ...
    hdop: builtin___float = ...
    timestamp_ms: builtin___int = ...
    height_mm: builtin___int = ...
    have_approx_fix: builtin___bool = ...
    fix_type: builtin___int = ...
    fix_flags: builtin___int = ...

    @property
    def dual(self) -> type___DualGpsData: ...

    def __init__(self,
        *,
        have_fix : typing___Optional[builtin___bool] = None,
        latitude : typing___Optional[builtin___float] = None,
        longitude : typing___Optional[builtin___float] = None,
        num_sats : typing___Optional[builtin___int] = None,
        hdop : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        height_mm : typing___Optional[builtin___int] = None,
        have_approx_fix : typing___Optional[builtin___bool] = None,
        fix_type : typing___Optional[builtin___int] = None,
        fix_flags : typing___Optional[builtin___int] = None,
        dual : typing___Optional[type___DualGpsData] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_dual",b"_dual",u"dual",b"dual"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_dual",b"_dual",u"dual",b"dual",u"fix_flags",b"fix_flags",u"fix_type",b"fix_type",u"have_approx_fix",b"have_approx_fix",u"have_fix",b"have_fix",u"hdop",b"hdop",u"height_mm",b"height_mm",u"latitude",b"latitude",u"longitude",b"longitude",u"num_sats",b"num_sats",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_dual",b"_dual"]) -> typing_extensions___Literal["dual"]: ...
type___Position_Reply = Position_Reply

class HeadingCorrection_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    heading_offset: builtin___float = ...

    def __init__(self,
        *,
        heading_offset : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"heading_offset",b"heading_offset"]) -> None: ...
type___HeadingCorrection_Request = HeadingCorrection_Request

class HeadingCorrection_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___HeadingCorrection_Reply = HeadingCorrection_Reply

class GetLastGga_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetLastGga_Request = GetLastGga_Request

class GetLastGga_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    raw_sentence: typing___Text = ...

    def __init__(self,
        *,
        raw_sentence : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"raw_sentence",b"raw_sentence"]) -> None: ...
type___GetLastGga_Reply = GetLastGga_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def position(self) -> type___Position_Request: ...

    @property
    def spartn(self) -> type___Spartn_Request: ...

    @property
    def heading_correction(self) -> type___HeadingCorrection_Request: ...

    @property
    def rtcm(self) -> type___Rtcm_Request: ...

    @property
    def gga(self) -> type___GetLastGga_Request: ...

    def __init__(self,
        *,
        position : typing___Optional[type___Position_Request] = None,
        spartn : typing___Optional[type___Spartn_Request] = None,
        heading_correction : typing___Optional[type___HeadingCorrection_Request] = None,
        rtcm : typing___Optional[type___Rtcm_Request] = None,
        gga : typing___Optional[type___GetLastGga_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"gga",b"gga",u"heading_correction",b"heading_correction",u"position",b"position",u"request",b"request",u"rtcm",b"rtcm",u"spartn",b"spartn"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"gga",b"gga",u"heading_correction",b"heading_correction",u"position",b"position",u"request",b"request",u"rtcm",b"rtcm",u"spartn",b"spartn"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["position","spartn","heading_correction","rtcm","gga"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def position(self) -> type___Position_Reply: ...

    @property
    def spartn(self) -> type___Spartn_Reply: ...

    @property
    def heading_correction(self) -> type___HeadingCorrection_Reply: ...

    @property
    def rtcm(self) -> type___Rtcm_Reply: ...

    @property
    def gga(self) -> type___GetLastGga_Reply: ...

    def __init__(self,
        *,
        position : typing___Optional[type___Position_Reply] = None,
        spartn : typing___Optional[type___Spartn_Reply] = None,
        heading_correction : typing___Optional[type___HeadingCorrection_Reply] = None,
        rtcm : typing___Optional[type___Rtcm_Reply] = None,
        gga : typing___Optional[type___GetLastGga_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"gga",b"gga",u"heading_correction",b"heading_correction",u"position",b"position",u"reply",b"reply",u"rtcm",b"rtcm",u"spartn",b"spartn"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"gga",b"gga",u"heading_correction",b"heading_correction",u"position",b"position",u"reply",b"reply",u"rtcm",b"rtcm",u"spartn",b"spartn"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["position","spartn","heading_correction","rtcm","gga"]: ...
type___Reply = Reply

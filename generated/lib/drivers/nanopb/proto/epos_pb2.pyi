"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.can_open_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___can_open_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___can_open_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

PID_Request_TypeValue = typing___NewType('PID_Request_TypeValue', builtin___int)
type___PID_Request_TypeValue = PID_Request_TypeValue
PID_Request_Type: _PID_Request_Type
class _PID_Request_Type(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[PID_Request_TypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    PID_REQUEST_FALLBACK = typing___cast(PID_Request_TypeValue, 0)
    PID_REQUEST_TEST = typing___cast(PID_Request_TypeValue, 1)
    PID_REQUEST_SAVE = typing___cast(PID_Request_TypeValue, 2)
PID_REQUEST_FALLBACK = typing___cast(PID_Request_TypeValue, 0)
PID_REQUEST_TEST = typing___cast(PID_Request_TypeValue, 1)
PID_REQUEST_SAVE = typing___cast(PID_Request_TypeValue, 2)

class Setup_PDOs_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Setup_PDOs_Request = Setup_PDOs_Request

class Enable_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Enable_Request = Enable_Request

class Disable_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Disable_Request = Disable_Request

class Hard_Home_Params(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    step_size: builtin___int = ...
    offset: builtin___int = ...

    def __init__(self,
        *,
        step_size : typing___Optional[builtin___int] = None,
        offset : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"offset",b"offset",u"step_size",b"step_size"]) -> None: ...
type___Hard_Home_Params = Hard_Home_Params

class Switch_Home_Params(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    step_size: builtin___int = ...
    threshold_step: builtin___int = ...

    def __init__(self,
        *,
        step_size : typing___Optional[builtin___int] = None,
        threshold_step : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"step_size",b"step_size",u"threshold_step",b"threshold_step"]) -> None: ...
type___Switch_Home_Params = Switch_Home_Params

class Actual_Position_Home_Params(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    range: builtin___int = ...

    def __init__(self,
        *,
        range : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"range",b"range"]) -> None: ...
type___Actual_Position_Home_Params = Actual_Position_Home_Params

class Home_Params(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    min_position: builtin___int = ...
    max_position: builtin___int = ...
    profile_velocity: builtin___int = ...
    invert: builtin___bool = ...

    @property
    def hard_stop(self) -> type___Hard_Home_Params: ...

    @property
    def limit_switch(self) -> type___Switch_Home_Params: ...

    @property
    def actual_position(self) -> type___Actual_Position_Home_Params: ...

    def __init__(self,
        *,
        min_position : typing___Optional[builtin___int] = None,
        max_position : typing___Optional[builtin___int] = None,
        profile_velocity : typing___Optional[builtin___int] = None,
        hard_stop : typing___Optional[type___Hard_Home_Params] = None,
        limit_switch : typing___Optional[type___Switch_Home_Params] = None,
        actual_position : typing___Optional[type___Actual_Position_Home_Params] = None,
        invert : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"actual_position",b"actual_position",u"hard_stop",b"hard_stop",u"limit_switch",b"limit_switch",u"params",b"params"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"actual_position",b"actual_position",u"hard_stop",b"hard_stop",u"invert",b"invert",u"limit_switch",b"limit_switch",u"max_position",b"max_position",u"min_position",b"min_position",u"params",b"params",u"profile_velocity",b"profile_velocity"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"params",b"params"]) -> typing_extensions___Literal["hard_stop","limit_switch","actual_position"]: ...
type___Home_Params = Home_Params

class Home_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def params(self) -> type___Home_Params: ...

    def __init__(self,
        *,
        params : typing___Optional[type___Home_Params] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"params",b"params"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"params",b"params"]) -> None: ...
type___Home_Request = Home_Request

class Await_Settling_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    target_position: builtin___int = ...
    window: builtin___int = ...
    timeout_ms: builtin___int = ...

    def __init__(self,
        *,
        target_position : typing___Optional[builtin___int] = None,
        window : typing___Optional[builtin___int] = None,
        timeout_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"target_position",b"target_position",u"timeout_ms",b"timeout_ms",u"window",b"window"]) -> None: ...
type___Await_Settling_Request = Await_Settling_Request

class Get_Pos_Vel_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Pos_Vel_Request = Get_Pos_Vel_Request

class Go_To_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    position: builtin___int = ...
    velocity: builtin___int = ...
    window: builtin___int = ...
    timeout_ms: builtin___int = ...

    def __init__(self,
        *,
        position : typing___Optional[builtin___int] = None,
        velocity : typing___Optional[builtin___int] = None,
        window : typing___Optional[builtin___int] = None,
        timeout_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"position",b"position",u"timeout_ms",b"timeout_ms",u"velocity",b"velocity",u"window",b"window"]) -> None: ...
type___Go_To_Request = Go_To_Request

class Await_Status_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timeout_ms: builtin___int = ...
    expected: builtin___int = ...
    expected_neg: builtin___int = ...

    def __init__(self,
        *,
        timeout_ms : typing___Optional[builtin___int] = None,
        expected : typing___Optional[builtin___int] = None,
        expected_neg : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"expected",b"expected",u"expected_neg",b"expected_neg",u"timeout_ms",b"timeout_ms"]) -> None: ...
type___Await_Status_Request = Await_Status_Request

class Set_Positional_PID_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    gain_p: builtin___int = ...
    gain_i: builtin___int = ...
    gain_d: builtin___int = ...
    gain_ffv: builtin___int = ...
    gain_ffa: builtin___int = ...

    def __init__(self,
        *,
        gain_p : typing___Optional[builtin___int] = None,
        gain_i : typing___Optional[builtin___int] = None,
        gain_d : typing___Optional[builtin___int] = None,
        gain_ffv : typing___Optional[builtin___int] = None,
        gain_ffa : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"gain_d",b"gain_d",u"gain_ffa",b"gain_ffa",u"gain_ffv",b"gain_ffv",u"gain_i",b"gain_i",u"gain_p",b"gain_p"]) -> None: ...
type___Set_Positional_PID_Request = Set_Positional_PID_Request

class Set_PID_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current_p: builtin___int = ...
    current_i: builtin___int = ...

    @property
    def positional_pid(self) -> type___Set_Positional_PID_Request: ...

    def __init__(self,
        *,
        current_p : typing___Optional[builtin___int] = None,
        current_i : typing___Optional[builtin___int] = None,
        positional_pid : typing___Optional[type___Set_Positional_PID_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"positional_pid",b"positional_pid"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_i",b"current_i",u"current_p",b"current_p",u"positional_pid",b"positional_pid"]) -> None: ...
type___Set_PID_Request = Set_PID_Request

class EPOS_PID(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    gain_p: builtin___int = ...
    gain_i: builtin___int = ...
    gain_d: builtin___int = ...
    gain_ffv: builtin___int = ...
    gain_ffa: builtin___int = ...
    current_p: builtin___int = ...
    current_i: builtin___int = ...

    def __init__(self,
        *,
        gain_p : typing___Optional[builtin___int] = None,
        gain_i : typing___Optional[builtin___int] = None,
        gain_d : typing___Optional[builtin___int] = None,
        gain_ffv : typing___Optional[builtin___int] = None,
        gain_ffa : typing___Optional[builtin___int] = None,
        current_p : typing___Optional[builtin___int] = None,
        current_i : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_i",b"current_i",u"current_p",b"current_p",u"gain_d",b"gain_d",u"gain_ffa",b"gain_ffa",u"gain_ffv",b"gain_ffv",u"gain_i",b"gain_i",u"gain_p",b"gain_p"]) -> None: ...
type___EPOS_PID = EPOS_PID

class Set_PID_V2_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type: type___PID_Request_TypeValue = ...

    @property
    def epos(self) -> type___EPOS_PID: ...

    def __init__(self,
        *,
        type : typing___Optional[type___PID_Request_TypeValue] = None,
        epos : typing___Optional[type___EPOS_PID] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"epos",b"epos",u"pid",b"pid"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"epos",b"epos",u"pid",b"pid",u"type",b"type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"pid",b"pid"]) -> typing_extensions___Literal["epos"]: ...
type___Set_PID_V2_Request = Set_PID_V2_Request

class Get_PID_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_PID_Request = Get_PID_Request

class Homing_Limit_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    limit: builtin___int = ...

    def __init__(self,
        *,
        limit : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"limit",b"limit"]) -> None: ...
type___Homing_Limit_Reply = Homing_Limit_Reply

class Settling_Time_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    duration: builtin___int = ...

    def __init__(self,
        *,
        duration : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"duration",b"duration"]) -> None: ...
type___Settling_Time_Reply = Settling_Time_Reply

class Pos_Vel_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    position: builtin___int = ...
    velocity: builtin___int = ...

    def __init__(self,
        *,
        position : typing___Optional[builtin___int] = None,
        velocity : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"position",b"position",u"velocity",b"velocity"]) -> None: ...
type___Pos_Vel_Reply = Pos_Vel_Reply

class Get_PID_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    valid: builtin___bool = ...

    @property
    def epos(self) -> type___EPOS_PID: ...

    def __init__(self,
        *,
        valid : typing___Optional[builtin___bool] = None,
        epos : typing___Optional[type___EPOS_PID] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"epos",b"epos",u"pid",b"pid"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"epos",b"epos",u"pid",b"pid",u"valid",b"valid"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"pid",b"pid"]) -> typing_extensions___Literal["epos"]: ...
type___Get_PID_Reply = Get_PID_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def can(self) -> generated___lib___drivers___nanopb___proto___can_open_pb2___Request: ...

    @property
    def setup_pdos(self) -> type___Setup_PDOs_Request: ...

    @property
    def enable(self) -> type___Enable_Request: ...

    @property
    def disable(self) -> type___Disable_Request: ...

    @property
    def home(self) -> type___Home_Request: ...

    @property
    def settle(self) -> type___Await_Settling_Request: ...

    @property
    def pos_vel(self) -> type___Get_Pos_Vel_Request: ...

    @property
    def go_to(self) -> type___Go_To_Request: ...

    @property
    def status(self) -> type___Await_Status_Request: ...

    @property
    def positional_pid(self) -> type___Set_Positional_PID_Request: ...

    @property
    def pid(self) -> type___Set_PID_Request: ...

    @property
    def pid_v2(self) -> type___Set_PID_V2_Request: ...

    @property
    def get_pid(self) -> type___Get_PID_Request: ...

    def __init__(self,
        *,
        can : typing___Optional[generated___lib___drivers___nanopb___proto___can_open_pb2___Request] = None,
        setup_pdos : typing___Optional[type___Setup_PDOs_Request] = None,
        enable : typing___Optional[type___Enable_Request] = None,
        disable : typing___Optional[type___Disable_Request] = None,
        home : typing___Optional[type___Home_Request] = None,
        settle : typing___Optional[type___Await_Settling_Request] = None,
        pos_vel : typing___Optional[type___Get_Pos_Vel_Request] = None,
        go_to : typing___Optional[type___Go_To_Request] = None,
        status : typing___Optional[type___Await_Status_Request] = None,
        positional_pid : typing___Optional[type___Set_Positional_PID_Request] = None,
        pid : typing___Optional[type___Set_PID_Request] = None,
        pid_v2 : typing___Optional[type___Set_PID_V2_Request] = None,
        get_pid : typing___Optional[type___Get_PID_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"can",b"can",u"disable",b"disable",u"enable",b"enable",u"get_pid",b"get_pid",u"go_to",b"go_to",u"home",b"home",u"pid",b"pid",u"pid_v2",b"pid_v2",u"pos_vel",b"pos_vel",u"positional_pid",b"positional_pid",u"request",b"request",u"settle",b"settle",u"setup_pdos",b"setup_pdos",u"status",b"status"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"can",b"can",u"disable",b"disable",u"enable",b"enable",u"get_pid",b"get_pid",u"go_to",b"go_to",u"home",b"home",u"pid",b"pid",u"pid_v2",b"pid_v2",u"pos_vel",b"pos_vel",u"positional_pid",b"positional_pid",u"request",b"request",u"settle",b"settle",u"setup_pdos",b"setup_pdos",u"status",b"status"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["can","setup_pdos","enable","disable","home","settle","pos_vel","go_to","status","positional_pid","pid","pid_v2","get_pid"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def can(self) -> generated___lib___drivers___nanopb___proto___can_open_pb2___Reply: ...

    @property
    def limit(self) -> type___Homing_Limit_Reply: ...

    @property
    def settle(self) -> type___Settling_Time_Reply: ...

    @property
    def pos_vel(self) -> type___Pos_Vel_Reply: ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def pid(self) -> type___Get_PID_Reply: ...

    def __init__(self,
        *,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        can : typing___Optional[generated___lib___drivers___nanopb___proto___can_open_pb2___Reply] = None,
        limit : typing___Optional[type___Homing_Limit_Reply] = None,
        settle : typing___Optional[type___Settling_Time_Reply] = None,
        pos_vel : typing___Optional[type___Pos_Vel_Reply] = None,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        pid : typing___Optional[type___Get_PID_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"can",b"can",u"error",b"error",u"limit",b"limit",u"pid",b"pid",u"pos_vel",b"pos_vel",u"reply",b"reply",u"settle",b"settle"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"can",b"can",u"error",b"error",u"limit",b"limit",u"pid",b"pid",u"pos_vel",b"pos_vel",u"reply",b"reply",u"settle",b"settle"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["ack","can","limit","settle","pos_vel","error","pid"]: ...
type___Reply = Reply

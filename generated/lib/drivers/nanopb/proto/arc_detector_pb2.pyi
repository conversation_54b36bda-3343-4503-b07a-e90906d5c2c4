"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Config(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...
    upperLimit: builtin___float = ...
    lowerLimit: builtin___float = ...
    alarmPeriod: builtin___int = ...
    alarmThreshold: builtin___int = ...
    initialDelay: builtin___int = ...
    sampleInterval: builtin___int = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        upperLimit : typing___Optional[builtin___float] = None,
        lowerLimit : typing___Optional[builtin___float] = None,
        alarmPeriod : typing___Optional[builtin___int] = None,
        alarmThreshold : typing___Optional[builtin___int] = None,
        initialDelay : typing___Optional[builtin___int] = None,
        sampleInterval : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"alarmPeriod",b"alarmPeriod",u"alarmThreshold",b"alarmThreshold",u"enabled",b"enabled",u"initialDelay",b"initialDelay",u"lowerLimit",b"lowerLimit",u"sampleInterval",b"sampleInterval",u"upperLimit",b"upperLimit"]) -> None: ...
type___Config = Config

class Reset_Alarm_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Reset_Alarm_Request = Reset_Alarm_Request

class Status_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Status_Request = Status_Request

class Status_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...
    alarm: builtin___bool = ...
    minCurrent: builtin___float = ...
    maxCurrent: builtin___float = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        alarm : typing___Optional[builtin___bool] = None,
        minCurrent : typing___Optional[builtin___float] = None,
        maxCurrent : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"alarm",b"alarm",u"enabled",b"enabled",u"maxCurrent",b"maxCurrent",u"minCurrent",b"minCurrent"]) -> None: ...
type___Status_Reply = Status_Reply

class Set_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def newConfig(self) -> type___Config: ...

    def __init__(self,
        *,
        newConfig : typing___Optional[type___Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"newConfig",b"newConfig"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"newConfig",b"newConfig"]) -> None: ...
type___Set_Config_Request = Set_Config_Request

class Get_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Config_Request = Get_Config_Request

class Config_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def conf(self) -> type___Config: ...

    def __init__(self,
        *,
        conf : typing___Optional[type___Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"conf",b"conf"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"conf",b"conf"]) -> None: ...
type___Config_Reply = Config_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def setConf(self) -> type___Set_Config_Request: ...

    @property
    def getConf(self) -> type___Get_Config_Request: ...

    @property
    def status(self) -> type___Status_Request: ...

    @property
    def reset(self) -> type___Reset_Alarm_Request: ...

    def __init__(self,
        *,
        setConf : typing___Optional[type___Set_Config_Request] = None,
        getConf : typing___Optional[type___Get_Config_Request] = None,
        status : typing___Optional[type___Status_Request] = None,
        reset : typing___Optional[type___Reset_Alarm_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"getConf",b"getConf",u"request",b"request",u"reset",b"reset",u"setConf",b"setConf",u"status",b"status"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"getConf",b"getConf",u"request",b"request",u"reset",b"reset",u"setConf",b"setConf",u"status",b"status"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["setConf","getConf","status","reset"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def status(self) -> type___Status_Reply: ...

    @property
    def conf(self) -> type___Config_Reply: ...

    def __init__(self,
        *,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        status : typing___Optional[type___Status_Reply] = None,
        conf : typing___Optional[type___Config_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"conf",b"conf",u"error",b"error",u"reply",b"reply",u"status",b"status"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"conf",b"conf",u"error",b"error",u"reply",b"reply",u"status",b"status"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","status","conf"]: ...
type___Reply = Reply

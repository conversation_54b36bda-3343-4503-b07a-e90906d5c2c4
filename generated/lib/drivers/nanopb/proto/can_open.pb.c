/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "can_open.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(can_open_SDO_Request, can_open_SDO_Request, AUTO)


PB_BIND(can_open_PDO_Request, can_open_PDO_Request, AUTO)


PB_BIND(can_open_RTR_PDO_Request, can_open_RTR_PDO_Request, AUTO)


PB_BIND(can_open_NMT_Request, can_open_NMT_Request, AUTO)


PB_BIND(can_open_Await_Request, can_open_Await_Request, AUTO)


PB_BIND(can_open_SDO_Download_Request, can_open_SDO_Download_Request, AUTO)


PB_BIND(can_open_SDO_Upload_Request, can_open_SDO_Upload_Request, AUTO)


PB_BIND(can_open_NMT_Reset_Request, can_open_NMT_Reset_Request, AUTO)


PB_BIND(can_open_NMT_Start_Request, can_open_NMT_Start_Request, AUTO)


PB_BIND(can_open_NMT_Stop_Request, can_open_NMT_Stop_Request, AUTO)


PB_BIND(can_open_ACK_Reply, can_open_ACK_Reply, AUTO)


PB_BIND(can_open_SDO_Packet, can_open_SDO_Packet, AUTO)


PB_BIND(can_open_PDO_Packet, can_open_PDO_Packet, AUTO)


PB_BIND(can_open_NMT_Packet, can_open_NMT_Packet, AUTO)


PB_BIND(can_open_Message_Reply, can_open_Message_Reply, AUTO)


PB_BIND(can_open_Request, can_open_Request, AUTO)


PB_BIND(can_open_Reply, can_open_Reply, 2)




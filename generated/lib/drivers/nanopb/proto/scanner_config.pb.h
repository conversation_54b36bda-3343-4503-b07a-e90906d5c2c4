/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_SCANNER_CONFIG_SCANNER_CONFIG_PB_H_INCLUDED
#define PB_SCANNER_CONFIG_SCANNER_CONFIG_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _scanner_config_Get_Camera_Serial_Number_Config_Request {
    char dummy_field;
} scanner_config_Get_Camera_Serial_Number_Config_Request;

typedef struct _scanner_config_Get_Color_Config_Request {
    char dummy_field;
} scanner_config_Get_Color_Config_Request;

typedef struct _scanner_config_Get_Delta_Target_Config_Request {
    char dummy_field;
} scanner_config_Get_Delta_Target_Config_Request;

typedef struct _scanner_config_Get_HW_Revision_Request {
    char dummy_field;
} scanner_config_Get_HW_Revision_Request;

typedef struct _scanner_config_Get_Scanner_Barcode_Str_Request {
    char dummy_field;
} scanner_config_Get_Scanner_Barcode_Str_Request;

typedef struct _scanner_config_Camera_Serial_Number_Config {
    pb_byte_t serial_number[32];
} scanner_config_Camera_Serial_Number_Config;

typedef struct _scanner_config_Color_Config {
    float red;
    float green;
    float blue;
} scanner_config_Color_Config;

typedef struct _scanner_config_Delta_Target_Config {
    float pan_skew;
    float tilt_skew;
} scanner_config_Delta_Target_Config;

typedef struct _scanner_config_Get_HW_Revision_Reply {
    uint32_t revision;
} scanner_config_Get_HW_Revision_Reply;

typedef struct _scanner_config_Scanner_Barcode_Str_Config {
    pb_byte_t barcode[16];
} scanner_config_Scanner_Barcode_Str_Config;

typedef struct _scanner_config_Set_HW_Revision_Request {
    uint32_t revision;
} scanner_config_Set_HW_Revision_Request;

typedef struct _scanner_config_Get_Camera_Serial_Number_Config_Reply {
    bool has_config;
    scanner_config_Camera_Serial_Number_Config config;
} scanner_config_Get_Camera_Serial_Number_Config_Reply;

typedef struct _scanner_config_Get_Color_Config_Reply {
    bool has_config;
    scanner_config_Color_Config config;
} scanner_config_Get_Color_Config_Reply;

typedef struct _scanner_config_Get_Delta_Target_Config_Reply {
    bool has_config;
    scanner_config_Delta_Target_Config config;
} scanner_config_Get_Delta_Target_Config_Reply;

typedef struct _scanner_config_Get_Scanner_Barcode_Str_Reply {
    bool has_config;
    scanner_config_Scanner_Barcode_Str_Config config;
} scanner_config_Get_Scanner_Barcode_Str_Reply;

typedef struct _scanner_config_Set_Camera_Serial_Number_Config_Request {
    bool has_config;
    scanner_config_Camera_Serial_Number_Config config;
} scanner_config_Set_Camera_Serial_Number_Config_Request;

typedef struct _scanner_config_Set_Color_Config_Request {
    bool has_config;
    scanner_config_Color_Config config;
} scanner_config_Set_Color_Config_Request;

typedef struct _scanner_config_Set_Delta_Target_Config_Request {
    bool has_config;
    scanner_config_Delta_Target_Config config;
} scanner_config_Set_Delta_Target_Config_Request;

typedef struct _scanner_config_Set_Scanner_Barcode_Str_Request {
    bool has_config;
    scanner_config_Scanner_Barcode_Str_Config config;
} scanner_config_Set_Scanner_Barcode_Str_Request;

typedef struct _scanner_config_Reply {
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        scanner_config_Get_Delta_Target_Config_Reply dt;
        scanner_config_Get_Color_Config_Reply color;
        scanner_config_Get_Camera_Serial_Number_Config_Reply sn;
        scanner_config_Get_HW_Revision_Reply hw;
        scanner_config_Get_Scanner_Barcode_Str_Reply bc;
    } reply;
} scanner_config_Reply;

typedef struct _scanner_config_Request {
    pb_size_t which_request;
    union {
        scanner_config_Set_Delta_Target_Config_Request set_dt;
        scanner_config_Get_Delta_Target_Config_Request get_dt;
        scanner_config_Set_Color_Config_Request set_color;
        scanner_config_Get_Color_Config_Request get_color;
        scanner_config_Set_Camera_Serial_Number_Config_Request set_sn;
        scanner_config_Get_Camera_Serial_Number_Config_Request get_sn;
        scanner_config_Set_HW_Revision_Request set_hw;
        scanner_config_Get_HW_Revision_Request get_hw;
        scanner_config_Set_Scanner_Barcode_Str_Request set_bc;
        scanner_config_Get_Scanner_Barcode_Str_Request get_bc;
    } request;
} scanner_config_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define scanner_config_Delta_Target_Config_init_default {0, 0}
#define scanner_config_Color_Config_init_default {0, 0, 0}
#define scanner_config_Camera_Serial_Number_Config_init_default {{0}}
#define scanner_config_Scanner_Barcode_Str_Config_init_default {{0}}
#define scanner_config_Set_Delta_Target_Config_Request_init_default {false, scanner_config_Delta_Target_Config_init_default}
#define scanner_config_Get_Delta_Target_Config_Request_init_default {0}
#define scanner_config_Set_Color_Config_Request_init_default {false, scanner_config_Color_Config_init_default}
#define scanner_config_Get_Color_Config_Request_init_default {0}
#define scanner_config_Set_Camera_Serial_Number_Config_Request_init_default {false, scanner_config_Camera_Serial_Number_Config_init_default}
#define scanner_config_Get_Camera_Serial_Number_Config_Request_init_default {0}
#define scanner_config_Set_HW_Revision_Request_init_default {0}
#define scanner_config_Get_HW_Revision_Request_init_default {0}
#define scanner_config_Set_Scanner_Barcode_Str_Request_init_default {false, scanner_config_Scanner_Barcode_Str_Config_init_default}
#define scanner_config_Get_Scanner_Barcode_Str_Request_init_default {0}
#define scanner_config_Get_Delta_Target_Config_Reply_init_default {false, scanner_config_Delta_Target_Config_init_default}
#define scanner_config_Get_Color_Config_Reply_init_default {false, scanner_config_Color_Config_init_default}
#define scanner_config_Get_Camera_Serial_Number_Config_Reply_init_default {false, scanner_config_Camera_Serial_Number_Config_init_default}
#define scanner_config_Get_HW_Revision_Reply_init_default {0}
#define scanner_config_Get_Scanner_Barcode_Str_Reply_init_default {false, scanner_config_Scanner_Barcode_Str_Config_init_default}
#define scanner_config_Request_init_default      {0, {scanner_config_Set_Delta_Target_Config_Request_init_default}}
#define scanner_config_Reply_init_default        {0, {error_Error_init_default}}
#define scanner_config_Delta_Target_Config_init_zero {0, 0}
#define scanner_config_Color_Config_init_zero    {0, 0, 0}
#define scanner_config_Camera_Serial_Number_Config_init_zero {{0}}
#define scanner_config_Scanner_Barcode_Str_Config_init_zero {{0}}
#define scanner_config_Set_Delta_Target_Config_Request_init_zero {false, scanner_config_Delta_Target_Config_init_zero}
#define scanner_config_Get_Delta_Target_Config_Request_init_zero {0}
#define scanner_config_Set_Color_Config_Request_init_zero {false, scanner_config_Color_Config_init_zero}
#define scanner_config_Get_Color_Config_Request_init_zero {0}
#define scanner_config_Set_Camera_Serial_Number_Config_Request_init_zero {false, scanner_config_Camera_Serial_Number_Config_init_zero}
#define scanner_config_Get_Camera_Serial_Number_Config_Request_init_zero {0}
#define scanner_config_Set_HW_Revision_Request_init_zero {0}
#define scanner_config_Get_HW_Revision_Request_init_zero {0}
#define scanner_config_Set_Scanner_Barcode_Str_Request_init_zero {false, scanner_config_Scanner_Barcode_Str_Config_init_zero}
#define scanner_config_Get_Scanner_Barcode_Str_Request_init_zero {0}
#define scanner_config_Get_Delta_Target_Config_Reply_init_zero {false, scanner_config_Delta_Target_Config_init_zero}
#define scanner_config_Get_Color_Config_Reply_init_zero {false, scanner_config_Color_Config_init_zero}
#define scanner_config_Get_Camera_Serial_Number_Config_Reply_init_zero {false, scanner_config_Camera_Serial_Number_Config_init_zero}
#define scanner_config_Get_HW_Revision_Reply_init_zero {0}
#define scanner_config_Get_Scanner_Barcode_Str_Reply_init_zero {false, scanner_config_Scanner_Barcode_Str_Config_init_zero}
#define scanner_config_Request_init_zero         {0, {scanner_config_Set_Delta_Target_Config_Request_init_zero}}
#define scanner_config_Reply_init_zero           {0, {error_Error_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define scanner_config_Camera_Serial_Number_Config_serial_number_tag 1
#define scanner_config_Color_Config_red_tag      1
#define scanner_config_Color_Config_green_tag    2
#define scanner_config_Color_Config_blue_tag     3
#define scanner_config_Delta_Target_Config_pan_skew_tag 1
#define scanner_config_Delta_Target_Config_tilt_skew_tag 2
#define scanner_config_Get_HW_Revision_Reply_revision_tag 1
#define scanner_config_Scanner_Barcode_Str_Config_barcode_tag 1
#define scanner_config_Set_HW_Revision_Request_revision_tag 1
#define scanner_config_Get_Camera_Serial_Number_Config_Reply_config_tag 1
#define scanner_config_Get_Color_Config_Reply_config_tag 1
#define scanner_config_Get_Delta_Target_Config_Reply_config_tag 1
#define scanner_config_Get_Scanner_Barcode_Str_Reply_config_tag 1
#define scanner_config_Set_Camera_Serial_Number_Config_Request_config_tag 1
#define scanner_config_Set_Color_Config_Request_config_tag 1
#define scanner_config_Set_Delta_Target_Config_Request_config_tag 1
#define scanner_config_Set_Scanner_Barcode_Str_Request_config_tag 1
#define scanner_config_Reply_error_tag           1
#define scanner_config_Reply_ack_tag             2
#define scanner_config_Reply_dt_tag              3
#define scanner_config_Reply_color_tag           4
#define scanner_config_Reply_sn_tag              5
#define scanner_config_Reply_hw_tag              6
#define scanner_config_Reply_bc_tag              7
#define scanner_config_Request_set_dt_tag        1
#define scanner_config_Request_get_dt_tag        2
#define scanner_config_Request_set_color_tag     3
#define scanner_config_Request_get_color_tag     4
#define scanner_config_Request_set_sn_tag        5
#define scanner_config_Request_get_sn_tag        6
#define scanner_config_Request_set_hw_tag        7
#define scanner_config_Request_get_hw_tag        8
#define scanner_config_Request_set_bc_tag        9
#define scanner_config_Request_get_bc_tag        10

/* Struct field encoding specification for nanopb */
#define scanner_config_Delta_Target_Config_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    pan_skew,          1) \
X(a, STATIC,   SINGULAR, FLOAT,    tilt_skew,         2)
#define scanner_config_Delta_Target_Config_CALLBACK NULL
#define scanner_config_Delta_Target_Config_DEFAULT NULL

#define scanner_config_Color_Config_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    red,               1) \
X(a, STATIC,   SINGULAR, FLOAT,    green,             2) \
X(a, STATIC,   SINGULAR, FLOAT,    blue,              3)
#define scanner_config_Color_Config_CALLBACK NULL
#define scanner_config_Color_Config_DEFAULT NULL

#define scanner_config_Camera_Serial_Number_Config_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, serial_number,     1)
#define scanner_config_Camera_Serial_Number_Config_CALLBACK NULL
#define scanner_config_Camera_Serial_Number_Config_DEFAULT NULL

#define scanner_config_Scanner_Barcode_Str_Config_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, barcode,           1)
#define scanner_config_Scanner_Barcode_Str_Config_CALLBACK NULL
#define scanner_config_Scanner_Barcode_Str_Config_DEFAULT NULL

#define scanner_config_Set_Delta_Target_Config_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            1)
#define scanner_config_Set_Delta_Target_Config_Request_CALLBACK NULL
#define scanner_config_Set_Delta_Target_Config_Request_DEFAULT NULL
#define scanner_config_Set_Delta_Target_Config_Request_config_MSGTYPE scanner_config_Delta_Target_Config

#define scanner_config_Get_Delta_Target_Config_Request_FIELDLIST(X, a) \

#define scanner_config_Get_Delta_Target_Config_Request_CALLBACK NULL
#define scanner_config_Get_Delta_Target_Config_Request_DEFAULT NULL

#define scanner_config_Set_Color_Config_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            1)
#define scanner_config_Set_Color_Config_Request_CALLBACK NULL
#define scanner_config_Set_Color_Config_Request_DEFAULT NULL
#define scanner_config_Set_Color_Config_Request_config_MSGTYPE scanner_config_Color_Config

#define scanner_config_Get_Color_Config_Request_FIELDLIST(X, a) \

#define scanner_config_Get_Color_Config_Request_CALLBACK NULL
#define scanner_config_Get_Color_Config_Request_DEFAULT NULL

#define scanner_config_Set_Camera_Serial_Number_Config_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            1)
#define scanner_config_Set_Camera_Serial_Number_Config_Request_CALLBACK NULL
#define scanner_config_Set_Camera_Serial_Number_Config_Request_DEFAULT NULL
#define scanner_config_Set_Camera_Serial_Number_Config_Request_config_MSGTYPE scanner_config_Camera_Serial_Number_Config

#define scanner_config_Get_Camera_Serial_Number_Config_Request_FIELDLIST(X, a) \

#define scanner_config_Get_Camera_Serial_Number_Config_Request_CALLBACK NULL
#define scanner_config_Get_Camera_Serial_Number_Config_Request_DEFAULT NULL

#define scanner_config_Set_HW_Revision_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   revision,          1)
#define scanner_config_Set_HW_Revision_Request_CALLBACK NULL
#define scanner_config_Set_HW_Revision_Request_DEFAULT NULL

#define scanner_config_Get_HW_Revision_Request_FIELDLIST(X, a) \

#define scanner_config_Get_HW_Revision_Request_CALLBACK NULL
#define scanner_config_Get_HW_Revision_Request_DEFAULT NULL

#define scanner_config_Set_Scanner_Barcode_Str_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            1)
#define scanner_config_Set_Scanner_Barcode_Str_Request_CALLBACK NULL
#define scanner_config_Set_Scanner_Barcode_Str_Request_DEFAULT NULL
#define scanner_config_Set_Scanner_Barcode_Str_Request_config_MSGTYPE scanner_config_Scanner_Barcode_Str_Config

#define scanner_config_Get_Scanner_Barcode_Str_Request_FIELDLIST(X, a) \

#define scanner_config_Get_Scanner_Barcode_Str_Request_CALLBACK NULL
#define scanner_config_Get_Scanner_Barcode_Str_Request_DEFAULT NULL

#define scanner_config_Get_Delta_Target_Config_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            1)
#define scanner_config_Get_Delta_Target_Config_Reply_CALLBACK NULL
#define scanner_config_Get_Delta_Target_Config_Reply_DEFAULT NULL
#define scanner_config_Get_Delta_Target_Config_Reply_config_MSGTYPE scanner_config_Delta_Target_Config

#define scanner_config_Get_Color_Config_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            1)
#define scanner_config_Get_Color_Config_Reply_CALLBACK NULL
#define scanner_config_Get_Color_Config_Reply_DEFAULT NULL
#define scanner_config_Get_Color_Config_Reply_config_MSGTYPE scanner_config_Color_Config

#define scanner_config_Get_Camera_Serial_Number_Config_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            1)
#define scanner_config_Get_Camera_Serial_Number_Config_Reply_CALLBACK NULL
#define scanner_config_Get_Camera_Serial_Number_Config_Reply_DEFAULT NULL
#define scanner_config_Get_Camera_Serial_Number_Config_Reply_config_MSGTYPE scanner_config_Camera_Serial_Number_Config

#define scanner_config_Get_HW_Revision_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   revision,          1)
#define scanner_config_Get_HW_Revision_Reply_CALLBACK NULL
#define scanner_config_Get_HW_Revision_Reply_DEFAULT NULL

#define scanner_config_Get_Scanner_Barcode_Str_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            1)
#define scanner_config_Get_Scanner_Barcode_Str_Reply_CALLBACK NULL
#define scanner_config_Get_Scanner_Barcode_Str_Reply_DEFAULT NULL
#define scanner_config_Get_Scanner_Barcode_Str_Reply_config_MSGTYPE scanner_config_Scanner_Barcode_Str_Config

#define scanner_config_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set_dt,request.set_dt),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_dt,request.get_dt),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set_color,request.set_color),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_color,request.get_color),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set_sn,request.set_sn),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_sn,request.get_sn),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set_hw,request.set_hw),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_hw,request.get_hw),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set_bc,request.set_bc),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_bc,request.get_bc),  10)
#define scanner_config_Request_CALLBACK NULL
#define scanner_config_Request_DEFAULT NULL
#define scanner_config_Request_request_set_dt_MSGTYPE scanner_config_Set_Delta_Target_Config_Request
#define scanner_config_Request_request_get_dt_MSGTYPE scanner_config_Get_Delta_Target_Config_Request
#define scanner_config_Request_request_set_color_MSGTYPE scanner_config_Set_Color_Config_Request
#define scanner_config_Request_request_get_color_MSGTYPE scanner_config_Get_Color_Config_Request
#define scanner_config_Request_request_set_sn_MSGTYPE scanner_config_Set_Camera_Serial_Number_Config_Request
#define scanner_config_Request_request_get_sn_MSGTYPE scanner_config_Get_Camera_Serial_Number_Config_Request
#define scanner_config_Request_request_set_hw_MSGTYPE scanner_config_Set_HW_Revision_Request
#define scanner_config_Request_request_get_hw_MSGTYPE scanner_config_Get_HW_Revision_Request
#define scanner_config_Request_request_set_bc_MSGTYPE scanner_config_Set_Scanner_Barcode_Str_Request
#define scanner_config_Request_request_get_bc_MSGTYPE scanner_config_Get_Scanner_Barcode_Str_Request

#define scanner_config_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,dt,reply.dt),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,color,reply.color),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,sn,reply.sn),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,hw,reply.hw),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,bc,reply.bc),   7)
#define scanner_config_Reply_CALLBACK NULL
#define scanner_config_Reply_DEFAULT NULL
#define scanner_config_Reply_reply_error_MSGTYPE error_Error
#define scanner_config_Reply_reply_ack_MSGTYPE ack_Ack
#define scanner_config_Reply_reply_dt_MSGTYPE scanner_config_Get_Delta_Target_Config_Reply
#define scanner_config_Reply_reply_color_MSGTYPE scanner_config_Get_Color_Config_Reply
#define scanner_config_Reply_reply_sn_MSGTYPE scanner_config_Get_Camera_Serial_Number_Config_Reply
#define scanner_config_Reply_reply_hw_MSGTYPE scanner_config_Get_HW_Revision_Reply
#define scanner_config_Reply_reply_bc_MSGTYPE scanner_config_Get_Scanner_Barcode_Str_Reply

extern const pb_msgdesc_t scanner_config_Delta_Target_Config_msg;
extern const pb_msgdesc_t scanner_config_Color_Config_msg;
extern const pb_msgdesc_t scanner_config_Camera_Serial_Number_Config_msg;
extern const pb_msgdesc_t scanner_config_Scanner_Barcode_Str_Config_msg;
extern const pb_msgdesc_t scanner_config_Set_Delta_Target_Config_Request_msg;
extern const pb_msgdesc_t scanner_config_Get_Delta_Target_Config_Request_msg;
extern const pb_msgdesc_t scanner_config_Set_Color_Config_Request_msg;
extern const pb_msgdesc_t scanner_config_Get_Color_Config_Request_msg;
extern const pb_msgdesc_t scanner_config_Set_Camera_Serial_Number_Config_Request_msg;
extern const pb_msgdesc_t scanner_config_Get_Camera_Serial_Number_Config_Request_msg;
extern const pb_msgdesc_t scanner_config_Set_HW_Revision_Request_msg;
extern const pb_msgdesc_t scanner_config_Get_HW_Revision_Request_msg;
extern const pb_msgdesc_t scanner_config_Set_Scanner_Barcode_Str_Request_msg;
extern const pb_msgdesc_t scanner_config_Get_Scanner_Barcode_Str_Request_msg;
extern const pb_msgdesc_t scanner_config_Get_Delta_Target_Config_Reply_msg;
extern const pb_msgdesc_t scanner_config_Get_Color_Config_Reply_msg;
extern const pb_msgdesc_t scanner_config_Get_Camera_Serial_Number_Config_Reply_msg;
extern const pb_msgdesc_t scanner_config_Get_HW_Revision_Reply_msg;
extern const pb_msgdesc_t scanner_config_Get_Scanner_Barcode_Str_Reply_msg;
extern const pb_msgdesc_t scanner_config_Request_msg;
extern const pb_msgdesc_t scanner_config_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define scanner_config_Delta_Target_Config_fields &scanner_config_Delta_Target_Config_msg
#define scanner_config_Color_Config_fields &scanner_config_Color_Config_msg
#define scanner_config_Camera_Serial_Number_Config_fields &scanner_config_Camera_Serial_Number_Config_msg
#define scanner_config_Scanner_Barcode_Str_Config_fields &scanner_config_Scanner_Barcode_Str_Config_msg
#define scanner_config_Set_Delta_Target_Config_Request_fields &scanner_config_Set_Delta_Target_Config_Request_msg
#define scanner_config_Get_Delta_Target_Config_Request_fields &scanner_config_Get_Delta_Target_Config_Request_msg
#define scanner_config_Set_Color_Config_Request_fields &scanner_config_Set_Color_Config_Request_msg
#define scanner_config_Get_Color_Config_Request_fields &scanner_config_Get_Color_Config_Request_msg
#define scanner_config_Set_Camera_Serial_Number_Config_Request_fields &scanner_config_Set_Camera_Serial_Number_Config_Request_msg
#define scanner_config_Get_Camera_Serial_Number_Config_Request_fields &scanner_config_Get_Camera_Serial_Number_Config_Request_msg
#define scanner_config_Set_HW_Revision_Request_fields &scanner_config_Set_HW_Revision_Request_msg
#define scanner_config_Get_HW_Revision_Request_fields &scanner_config_Get_HW_Revision_Request_msg
#define scanner_config_Set_Scanner_Barcode_Str_Request_fields &scanner_config_Set_Scanner_Barcode_Str_Request_msg
#define scanner_config_Get_Scanner_Barcode_Str_Request_fields &scanner_config_Get_Scanner_Barcode_Str_Request_msg
#define scanner_config_Get_Delta_Target_Config_Reply_fields &scanner_config_Get_Delta_Target_Config_Reply_msg
#define scanner_config_Get_Color_Config_Reply_fields &scanner_config_Get_Color_Config_Reply_msg
#define scanner_config_Get_Camera_Serial_Number_Config_Reply_fields &scanner_config_Get_Camera_Serial_Number_Config_Reply_msg
#define scanner_config_Get_HW_Revision_Reply_fields &scanner_config_Get_HW_Revision_Reply_msg
#define scanner_config_Get_Scanner_Barcode_Str_Reply_fields &scanner_config_Get_Scanner_Barcode_Str_Reply_msg
#define scanner_config_Request_fields &scanner_config_Request_msg
#define scanner_config_Reply_fields &scanner_config_Reply_msg

/* Maximum encoded size of messages (where known) */
#define scanner_config_Delta_Target_Config_size  10
#define scanner_config_Color_Config_size         15
#define scanner_config_Camera_Serial_Number_Config_size 34
#define scanner_config_Scanner_Barcode_Str_Config_size 18
#define scanner_config_Set_Delta_Target_Config_Request_size 12
#define scanner_config_Get_Delta_Target_Config_Request_size 0
#define scanner_config_Set_Color_Config_Request_size 17
#define scanner_config_Get_Color_Config_Request_size 0
#define scanner_config_Set_Camera_Serial_Number_Config_Request_size 36
#define scanner_config_Get_Camera_Serial_Number_Config_Request_size 0
#define scanner_config_Set_HW_Revision_Request_size 6
#define scanner_config_Get_HW_Revision_Request_size 0
#define scanner_config_Set_Scanner_Barcode_Str_Request_size 20
#define scanner_config_Get_Scanner_Barcode_Str_Request_size 0
#define scanner_config_Get_Delta_Target_Config_Reply_size 12
#define scanner_config_Get_Color_Config_Reply_size 17
#define scanner_config_Get_Camera_Serial_Number_Config_Reply_size 36
#define scanner_config_Get_HW_Revision_Reply_size 6
#define scanner_config_Get_Scanner_Barcode_Str_Reply_size 20
#define scanner_config_Request_size              38
#if defined(error_Error_size) && defined(ack_Ack_size)
typedef union scanner_config_Reply_reply_size_union {char f1[(6 + error_Error_size)]; char f2[(6 + ack_Ack_size)]; char f0[38];} scanner_config_Reply_reply_size_union;
#define scanner_config_Reply_size                (0 + sizeof(scanner_config_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.epos_pb2 import (
    Home_Params as generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params,
    Reply as generated___lib___drivers___nanopb___proto___epos_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___epos_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from generated.lib.drivers.nanopb.proto.time_pb2 import (
    Timestamp as generated___lib___drivers___nanopb___proto___time_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

GoToModeValue = typing___NewType('GoToModeValue', builtin___int)
type___GoToModeValue = GoToModeValue
GoToMode: _GoToMode
class _GoToMode(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[GoToModeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    IMMEDIATE = typing___cast(GoToModeValue, 0)
    REACHED = typing___cast(GoToModeValue, 1)
    SETTLED = typing___cast(GoToModeValue, 2)
    TRAJECTORY = typing___cast(GoToModeValue, 3)
IMMEDIATE = typing___cast(GoToModeValue, 0)
REACHED = typing___cast(GoToModeValue, 1)
SETTLED = typing___cast(GoToModeValue, 2)
TRAJECTORY = typing___cast(GoToModeValue, 3)

class Config(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    max_profile_velocity: builtin___int = ...
    settle_window: builtin___int = ...
    settle_timeout: builtin___int = ...
    max_diff_millis: builtin___int = ...

    def __init__(self,
        *,
        max_profile_velocity : typing___Optional[builtin___int] = None,
        settle_window : typing___Optional[builtin___int] = None,
        settle_timeout : typing___Optional[builtin___int] = None,
        max_diff_millis : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"max_diff_millis",b"max_diff_millis",u"max_profile_velocity",b"max_profile_velocity",u"settle_timeout",b"settle_timeout",u"settle_window",b"settle_window"]) -> None: ...
type___Config = Config

class Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    node_id: builtin___int = ...

    @property
    def config(self) -> type___Config: ...

    def __init__(self,
        *,
        node_id : typing___Optional[builtin___int] = None,
        config : typing___Optional[type___Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"node_id",b"node_id"]) -> None: ...
type___Config_Request = Config_Request

class Boot_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def params(self) -> generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params: ...

    def __init__(self,
        *,
        params : typing___Optional[generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"params",b"params"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"params",b"params"]) -> None: ...
type___Boot_Request = Boot_Request

class Stop_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Stop_Request = Stop_Request

class Go_To_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    position: builtin___int = ...
    velocity: builtin___int = ...
    await_settle: builtin___bool = ...

    def __init__(self,
        *,
        position : typing___Optional[builtin___int] = None,
        velocity : typing___Optional[builtin___int] = None,
        await_settle : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"await_settle",b"await_settle",u"position",b"position",u"velocity",b"velocity"]) -> None: ...
type___Go_To_Request = Go_To_Request

class Get_Limits_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Limits_Request = Get_Limits_Request

class Go_To_Delta_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    delta_position: builtin___int = ...
    velocity: builtin___int = ...
    mode: type___GoToModeValue = ...

    def __init__(self,
        *,
        delta_position : typing___Optional[builtin___int] = None,
        velocity : typing___Optional[builtin___int] = None,
        mode : typing___Optional[type___GoToModeValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"delta_position",b"delta_position",u"mode",b"mode",u"velocity",b"velocity"]) -> None: ...
type___Go_To_Delta_Request = Go_To_Delta_Request

class Go_To_Delta_Follow_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    follow_velocity_vector: builtin___int = ...
    follow_velocity_mrpm: builtin___int = ...
    interval_sleep_time_ms: builtin___int = ...
    fast_return: builtin___bool = ...

    @property
    def delta(self) -> type___Go_To_Delta_Request: ...

    def __init__(self,
        *,
        delta : typing___Optional[type___Go_To_Delta_Request] = None,
        follow_velocity_vector : typing___Optional[builtin___int] = None,
        follow_velocity_mrpm : typing___Optional[builtin___int] = None,
        interval_sleep_time_ms : typing___Optional[builtin___int] = None,
        fast_return : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"delta",b"delta"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"delta",b"delta",u"fast_return",b"fast_return",u"follow_velocity_mrpm",b"follow_velocity_mrpm",u"follow_velocity_vector",b"follow_velocity_vector",u"interval_sleep_time_ms",b"interval_sleep_time_ms"]) -> None: ...
type___Go_To_Delta_Follow_Request = Go_To_Delta_Follow_Request

class Go_To_Calibrate_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    position: builtin___int = ...
    velocity: builtin___int = ...
    window: builtin___int = ...
    time_window_ms: builtin___int = ...
    timeout_ms: builtin___int = ...
    period_ms: builtin___int = ...

    def __init__(self,
        *,
        position : typing___Optional[builtin___int] = None,
        velocity : typing___Optional[builtin___int] = None,
        window : typing___Optional[builtin___int] = None,
        time_window_ms : typing___Optional[builtin___int] = None,
        timeout_ms : typing___Optional[builtin___int] = None,
        period_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"period_ms",b"period_ms",u"position",b"position",u"time_window_ms",b"time_window_ms",u"timeout_ms",b"timeout_ms",u"velocity",b"velocity",u"window",b"window"]) -> None: ...
type___Go_To_Calibrate_Request = Go_To_Calibrate_Request

class Go_To_Timestamp_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    mode: type___GoToModeValue = ...
    position: builtin___int = ...
    velocity_mrpm: builtin___int = ...
    follow_velocity: builtin___int = ...
    follow_accel: builtin___int = ...
    interval_sleep_time_ms: builtin___int = ...

    @property
    def timestamp(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    def __init__(self,
        *,
        timestamp : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        mode : typing___Optional[type___GoToModeValue] = None,
        position : typing___Optional[builtin___int] = None,
        velocity_mrpm : typing___Optional[builtin___int] = None,
        follow_velocity : typing___Optional[builtin___int] = None,
        follow_accel : typing___Optional[builtin___int] = None,
        interval_sleep_time_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"follow_accel",b"follow_accel",u"follow_velocity",b"follow_velocity",u"interval_sleep_time_ms",b"interval_sleep_time_ms",u"mode",b"mode",u"position",b"position",u"timestamp",b"timestamp",u"velocity_mrpm",b"velocity_mrpm"]) -> None: ...
type___Go_To_Timestamp_Request = Go_To_Timestamp_Request

class Go_To_Follow_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    position: builtin___int = ...
    velocity_mrpm: builtin___int = ...
    follow_velocity: builtin___int = ...
    follow_accel: builtin___int = ...
    interval_sleep_time_ms: builtin___int = ...

    @property
    def timestamp(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    def __init__(self,
        *,
        timestamp : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        position : typing___Optional[builtin___int] = None,
        velocity_mrpm : typing___Optional[builtin___int] = None,
        follow_velocity : typing___Optional[builtin___int] = None,
        follow_accel : typing___Optional[builtin___int] = None,
        interval_sleep_time_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"follow_accel",b"follow_accel",u"follow_velocity",b"follow_velocity",u"interval_sleep_time_ms",b"interval_sleep_time_ms",u"position",b"position",u"timestamp",b"timestamp",u"velocity_mrpm",b"velocity_mrpm"]) -> None: ...
type___Go_To_Follow_Request = Go_To_Follow_Request

class Follow_Timestamp_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    follow_velocity: builtin___int = ...
    follow_accel: builtin___int = ...

    @property
    def timestamp(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    def __init__(self,
        *,
        timestamp : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        follow_velocity : typing___Optional[builtin___int] = None,
        follow_accel : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"follow_accel",b"follow_accel",u"follow_velocity",b"follow_velocity",u"timestamp",b"timestamp"]) -> None: ...
type___Follow_Timestamp_Request = Follow_Timestamp_Request

class Limits_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    min: builtin___int = ...
    max: builtin___int = ...

    def __init__(self,
        *,
        min : typing___Optional[builtin___int] = None,
        max : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"max",b"max",u"min",b"min"]) -> None: ...
type___Limits_Reply = Limits_Reply

class Position_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    position: builtin___int = ...

    def __init__(self,
        *,
        position : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"position",b"position"]) -> None: ...
type___Position_Reply = Position_Reply

class Settle_Time_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    settle_time: builtin___int = ...

    def __init__(self,
        *,
        settle_time : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"settle_time",b"settle_time"]) -> None: ...
type___Settle_Time_Reply = Settle_Time_Reply

class Go_To_Timestamp_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pre_position: builtin___int = ...
    post_position: builtin___int = ...

    @property
    def pre_timestamp(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    @property
    def post_timestamp(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    def __init__(self,
        *,
        pre_position : typing___Optional[builtin___int] = None,
        post_position : typing___Optional[builtin___int] = None,
        pre_timestamp : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        post_timestamp : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"post_timestamp",b"post_timestamp",u"pre_timestamp",b"pre_timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"post_position",b"post_position",u"post_timestamp",b"post_timestamp",u"pre_position",b"pre_position",u"pre_timestamp",b"pre_timestamp"]) -> None: ...
type___Go_To_Timestamp_Reply = Go_To_Timestamp_Reply

class Follow_Timestamp_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pre_position: builtin___int = ...

    @property
    def pre_timestamp(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    def __init__(self,
        *,
        pre_position : typing___Optional[builtin___int] = None,
        pre_timestamp : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pre_timestamp",b"pre_timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pre_position",b"pre_position",u"pre_timestamp",b"pre_timestamp"]) -> None: ...
type___Follow_Timestamp_Reply = Follow_Timestamp_Reply

class Go_To_Follow_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pre_position: builtin___int = ...

    @property
    def pre_timestamp(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    def __init__(self,
        *,
        pre_position : typing___Optional[builtin___int] = None,
        pre_timestamp : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pre_timestamp",b"pre_timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pre_position",b"pre_position",u"pre_timestamp",b"pre_timestamp"]) -> None: ...
type___Go_To_Follow_Reply = Go_To_Follow_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Config_Request: ...

    @property
    def boot(self) -> type___Boot_Request: ...

    @property
    def stop(self) -> type___Stop_Request: ...

    @property
    def go_to(self) -> type___Go_To_Request: ...

    @property
    def limit(self) -> type___Get_Limits_Request: ...

    @property
    def epos(self) -> generated___lib___drivers___nanopb___proto___epos_pb2___Request: ...

    @property
    def delta(self) -> type___Go_To_Delta_Request: ...

    @property
    def follow(self) -> type___Go_To_Delta_Follow_Request: ...

    @property
    def calibrate(self) -> type___Go_To_Calibrate_Request: ...

    @property
    def go_to_timestamp(self) -> type___Go_To_Timestamp_Request: ...

    @property
    def follow_timestamp(self) -> type___Follow_Timestamp_Request: ...

    @property
    def go_to_follow(self) -> type___Go_To_Follow_Request: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Config_Request] = None,
        boot : typing___Optional[type___Boot_Request] = None,
        stop : typing___Optional[type___Stop_Request] = None,
        go_to : typing___Optional[type___Go_To_Request] = None,
        limit : typing___Optional[type___Get_Limits_Request] = None,
        epos : typing___Optional[generated___lib___drivers___nanopb___proto___epos_pb2___Request] = None,
        delta : typing___Optional[type___Go_To_Delta_Request] = None,
        follow : typing___Optional[type___Go_To_Delta_Follow_Request] = None,
        calibrate : typing___Optional[type___Go_To_Calibrate_Request] = None,
        go_to_timestamp : typing___Optional[type___Go_To_Timestamp_Request] = None,
        follow_timestamp : typing___Optional[type___Follow_Timestamp_Request] = None,
        go_to_follow : typing___Optional[type___Go_To_Follow_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"boot",b"boot",u"calibrate",b"calibrate",u"config",b"config",u"delta",b"delta",u"epos",b"epos",u"follow",b"follow",u"follow_timestamp",b"follow_timestamp",u"go_to",b"go_to",u"go_to_follow",b"go_to_follow",u"go_to_timestamp",b"go_to_timestamp",u"limit",b"limit",u"request",b"request",u"stop",b"stop"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"boot",b"boot",u"calibrate",b"calibrate",u"config",b"config",u"delta",b"delta",u"epos",b"epos",u"follow",b"follow",u"follow_timestamp",b"follow_timestamp",u"go_to",b"go_to",u"go_to_follow",b"go_to_follow",u"go_to_timestamp",b"go_to_timestamp",u"limit",b"limit",u"request",b"request",u"stop",b"stop"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["config","boot","stop","go_to","limit","epos","delta","follow","calibrate","go_to_timestamp","follow_timestamp","go_to_follow"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def limit(self) -> type___Limits_Reply: ...

    @property
    def epos(self) -> generated___lib___drivers___nanopb___proto___epos_pb2___Reply: ...

    @property
    def pos(self) -> type___Position_Reply: ...

    @property
    def settle(self) -> type___Settle_Time_Reply: ...

    @property
    def go_to_timestamp(self) -> type___Go_To_Timestamp_Reply: ...

    @property
    def follow_timestamp(self) -> type___Follow_Timestamp_Reply: ...

    @property
    def go_to_follow(self) -> type___Go_To_Follow_Reply: ...

    def __init__(self,
        *,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        limit : typing___Optional[type___Limits_Reply] = None,
        epos : typing___Optional[generated___lib___drivers___nanopb___proto___epos_pb2___Reply] = None,
        pos : typing___Optional[type___Position_Reply] = None,
        settle : typing___Optional[type___Settle_Time_Reply] = None,
        go_to_timestamp : typing___Optional[type___Go_To_Timestamp_Reply] = None,
        follow_timestamp : typing___Optional[type___Follow_Timestamp_Reply] = None,
        go_to_follow : typing___Optional[type___Go_To_Follow_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"epos",b"epos",u"error",b"error",u"follow_timestamp",b"follow_timestamp",u"go_to_follow",b"go_to_follow",u"go_to_timestamp",b"go_to_timestamp",u"limit",b"limit",u"pos",b"pos",u"reply",b"reply",u"settle",b"settle"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"epos",b"epos",u"error",b"error",u"follow_timestamp",b"follow_timestamp",u"go_to_follow",b"go_to_follow",u"go_to_timestamp",b"go_to_timestamp",u"limit",b"limit",u"pos",b"pos",u"reply",b"reply",u"settle",b"settle"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","limit","epos","pos","settle","go_to_timestamp","follow_timestamp","go_to_follow"]: ...
type___Reply = Reply

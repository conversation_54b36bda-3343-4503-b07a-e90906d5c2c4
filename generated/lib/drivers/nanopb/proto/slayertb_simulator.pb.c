/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "slayertb_simulator.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(slayertb_simulator_GetValueRequest, slayertb_simulator_GetValueRequest, AUTO)


PB_BIND(slayertb_simulator_GetValueReply, slayertb_simulator_GetValueReply, AUTO)


PB_BIND(slayertb_simulator_SetValueRequest, slayertb_simulator_SetValueRequest, AUTO)


PB_BIND(slayertb_simulator_SetValueReply, slayertb_simulator_SetValueReply, AUTO)


PB_BIND(slayertb_simulator_UseTempSensorRequest, slayertb_simulator_UseTempSensorRequest, AUTO)


PB_BIND(slayertb_simulator_UseTempSensorReply, slayertb_simulator_UseTempSensorReply, AUTO)


PB_BIND(slayertb_simulator_Reply, slayertb_simulator_Reply, 2)


PB_BIND(slayertb_simulator_Request, slayertb_simulator_Request, 2)





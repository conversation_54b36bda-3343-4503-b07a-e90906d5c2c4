/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "laser.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(laser_Raw_Data_Request, laser_Raw_Data_Request, AUTO)


PB_BIND(laser_Raw_Data_Reply, laser_Raw_Data_Reply, AUTO)


PB_BIND(laser_Laser_Inventory_Request, laser_Laser_Inventory_Request, AUTO)


PB_BIND(laser_Laser_Inventory_Reply, laser_Laser_Inventory_Reply, AUTO)


PB_BIND(laser_Laser_Set_Type_Request, laser_Laser_Set_Type_Request, AUTO)


PB_BIND(laser_Laser_Reset_Request, laser_Laser_Reset_Request, AUTO)


PB_BIND(laser_Jlight_Status, laser_Jlight_Status, AUTO)


PB_BIND(laser_Bwt_Status, laser_Bwt_Status, AUTO)


PB_BIND(laser_Bwt_Passthrough_Request, laser_Bwt_Passthrough_Request, AUTO)


PB_BIND(laser_Bwt_Passthrough_Reply, laser_Bwt_Passthrough_Reply, AUTO)


PB_BIND(laser_Bwt_Transport_Get_Config_Request, laser_Bwt_Transport_Get_Config_Request, AUTO)


PB_BIND(laser_Bwt_Transport_Config, laser_Bwt_Transport_Config, AUTO)


PB_BIND(laser_Diode_Status_Request, laser_Diode_Status_Request, AUTO)


PB_BIND(laser_Diode_Status_Reply, laser_Diode_Status_Reply, AUTO)


PB_BIND(laser_Diode_Set_Current_Request, laser_Diode_Set_Current_Request, AUTO)


PB_BIND(laser_Laser_Request, laser_Laser_Request, AUTO)


PB_BIND(laser_Get_Laser_Request, laser_Get_Laser_Request, AUTO)


PB_BIND(laser_Intensity_Request, laser_Intensity_Request, AUTO)


PB_BIND(laser_Laser_Reply, laser_Laser_Reply, AUTO)


PB_BIND(laser_Laser_State_Reply, laser_Laser_State_Reply, AUTO)


PB_BIND(laser_Laser_Status_Reply, laser_Laser_Status_Reply, AUTO)


PB_BIND(laser_Request, laser_Request, AUTO)


PB_BIND(laser_Reply, laser_Reply, 2)






"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ots_tractor_pb2 import (
    GearState as generated___lib___drivers___nanopb___proto___ots_tractor_pb2___GearState,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

HHStateStatusValue = typing___NewType('HHStateStatusValue', builtin___int)
type___HHStateStatusValue = HHStateStatusValue
HHStateStatus: _HHStateStatus
class _HHStateStatus(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[HHStateStatusValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    HH_UNKNOWN = typing___cast(HHStateStatusValue, 0)
    HH_DISABLED = typing___cast(HHStateStatusValue, 1)
    HH_OPERATIONAL = typing___cast(HHStateStatusValue, 2)
    HH_STOPPED = typing___cast(HHStateStatusValue, 3)
    HH_SAFE = typing___cast(HHStateStatusValue, 4)
    HH_ESTOP = typing___cast(HHStateStatusValue, 5)
HH_UNKNOWN = typing___cast(HHStateStatusValue, 0)
HH_DISABLED = typing___cast(HHStateStatusValue, 1)
HH_OPERATIONAL = typing___cast(HHStateStatusValue, 2)
HH_STOPPED = typing___cast(HHStateStatusValue, 3)
HH_SAFE = typing___cast(HHStateStatusValue, 4)
HH_ESTOP = typing___cast(HHStateStatusValue, 5)

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

class HHState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    state: type___HHStateStatusValue = ...

    def __init__(self,
        *,
        state : typing___Optional[type___HHStateStatusValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"state",b"state"]) -> None: ...
type___HHState = HHState

class BrakeState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    force_left: builtin___int = ...
    force_right: builtin___int = ...

    def __init__(self,
        *,
        force_left : typing___Optional[builtin___int] = None,
        force_right : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"force_left",b"force_left",u"force_right",b"force_right"]) -> None: ...
type___BrakeState = BrakeState

class SafetySensorsState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    triggered_sensor_1: builtin___bool = ...
    triggered_sensor_2: builtin___bool = ...
    triggered_sensor_3: builtin___bool = ...
    triggered_sensor_4: builtin___bool = ...

    def __init__(self,
        *,
        triggered_sensor_1 : typing___Optional[builtin___bool] = None,
        triggered_sensor_2 : typing___Optional[builtin___bool] = None,
        triggered_sensor_3 : typing___Optional[builtin___bool] = None,
        triggered_sensor_4 : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"triggered_sensor_1",b"triggered_sensor_1",u"triggered_sensor_2",b"triggered_sensor_2",u"triggered_sensor_3",b"triggered_sensor_3",u"triggered_sensor_4",b"triggered_sensor_4"]) -> None: ...
type___SafetySensorsState = SafetySensorsState

class SafetySensorBypassState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    bypass: builtin___bool = ...

    def __init__(self,
        *,
        bypass : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bypass",b"bypass"]) -> None: ...
type___SafetySensorBypassState = SafetySensorBypassState

class TractorStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    error_flag: builtin___int = ...
    ground_speed: builtin___float = ...
    wheel_angle: builtin___float = ...
    hitch_lift_percentage: builtin___float = ...
    safety_triggered: builtin___bool = ...
    safety_bypass: builtin___bool = ...
    remote_lockout: builtin___bool = ...
    rpms: builtin___int = ...

    @property
    def state(self) -> type___HHState: ...

    @property
    def gear(self) -> generated___lib___drivers___nanopb___proto___ots_tractor_pb2___GearState: ...

    def __init__(self,
        *,
        state : typing___Optional[type___HHState] = None,
        error_flag : typing___Optional[builtin___int] = None,
        ground_speed : typing___Optional[builtin___float] = None,
        wheel_angle : typing___Optional[builtin___float] = None,
        hitch_lift_percentage : typing___Optional[builtin___float] = None,
        gear : typing___Optional[generated___lib___drivers___nanopb___proto___ots_tractor_pb2___GearState] = None,
        safety_triggered : typing___Optional[builtin___bool] = None,
        safety_bypass : typing___Optional[builtin___bool] = None,
        remote_lockout : typing___Optional[builtin___bool] = None,
        rpms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"gear",b"gear",u"state",b"state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"error_flag",b"error_flag",u"gear",b"gear",u"ground_speed",b"ground_speed",u"hitch_lift_percentage",b"hitch_lift_percentage",u"remote_lockout",b"remote_lockout",u"rpms",b"rpms",u"safety_bypass",b"safety_bypass",u"safety_triggered",b"safety_triggered",u"state",b"state",u"wheel_angle",b"wheel_angle"]) -> None: ...
type___TractorStatus = TractorStatus

class PetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___PetRequest = PetRequest

class SteeringState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    angle: builtin___float = ...

    def __init__(self,
        *,
        angle : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"angle",b"angle"]) -> None: ...
type___SteeringState = SteeringState

class SteeringCfgState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    kp: builtin___float = ...
    ki: builtin___float = ...
    kd: builtin___float = ...
    integral_limit: builtin___float = ...
    update_rate_hz: builtin___int = ...
    min_steering_valve_current: builtin___int = ...
    max_steering_valve_current: builtin___int = ...

    def __init__(self,
        *,
        kp : typing___Optional[builtin___float] = None,
        ki : typing___Optional[builtin___float] = None,
        kd : typing___Optional[builtin___float] = None,
        integral_limit : typing___Optional[builtin___float] = None,
        update_rate_hz : typing___Optional[builtin___int] = None,
        min_steering_valve_current : typing___Optional[builtin___int] = None,
        max_steering_valve_current : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"integral_limit",b"integral_limit",u"kd",b"kd",u"ki",b"ki",u"kp",b"kp",u"max_steering_valve_current",b"max_steering_valve_current",u"min_steering_valve_current",b"min_steering_valve_current",u"update_rate_hz",b"update_rate_hz"]) -> None: ...
type___SteeringCfgState = SteeringCfgState

class PetLossState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    use_stop: builtin___bool = ...

    def __init__(self,
        *,
        use_stop : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"use_stop",b"use_stop"]) -> None: ...
type___PetLossState = PetLossState

class SpeedLimitState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    speed_limit_mph: builtin___float = ...

    def __init__(self,
        *,
        speed_limit_mph : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"speed_limit_mph",b"speed_limit_mph"]) -> None: ...
type___SpeedLimitState = SpeedLimitState

class SetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def hh_state(self) -> type___HHState: ...

    @property
    def brakes(self) -> type___BrakeState: ...

    @property
    def safety_bypass(self) -> type___SafetySensorBypassState: ...

    @property
    def steering(self) -> type___SteeringState: ...

    @property
    def steering_cfg(self) -> type___SteeringCfgState: ...

    @property
    def pet_loss(self) -> type___PetLossState: ...

    @property
    def speed_limit(self) -> type___SpeedLimitState: ...

    def __init__(self,
        *,
        hh_state : typing___Optional[type___HHState] = None,
        brakes : typing___Optional[type___BrakeState] = None,
        safety_bypass : typing___Optional[type___SafetySensorBypassState] = None,
        steering : typing___Optional[type___SteeringState] = None,
        steering_cfg : typing___Optional[type___SteeringCfgState] = None,
        pet_loss : typing___Optional[type___PetLossState] = None,
        speed_limit : typing___Optional[type___SpeedLimitState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"brakes",b"brakes",u"hh_state",b"hh_state",u"pet_loss",b"pet_loss",u"safety_bypass",b"safety_bypass",u"set",b"set",u"speed_limit",b"speed_limit",u"steering",b"steering",u"steering_cfg",b"steering_cfg"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"brakes",b"brakes",u"hh_state",b"hh_state",u"pet_loss",b"pet_loss",u"safety_bypass",b"safety_bypass",u"set",b"set",u"speed_limit",b"speed_limit",u"steering",b"steering",u"steering_cfg",b"steering_cfg"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"set",b"set"]) -> typing_extensions___Literal["hh_state","brakes","safety_bypass","steering","steering_cfg","pet_loss","speed_limit"]: ...
type___SetRequest = SetRequest

class SetReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def hh_state(self) -> type___HHState: ...

    @property
    def brakes(self) -> type___BrakeState: ...

    @property
    def safety_bypass(self) -> type___SafetySensorBypassState: ...

    @property
    def steering(self) -> type___SteeringState: ...

    @property
    def steering_cfg(self) -> type___SteeringCfgState: ...

    @property
    def pet_loss(self) -> type___PetLossState: ...

    @property
    def speed_limit(self) -> type___SpeedLimitState: ...

    def __init__(self,
        *,
        hh_state : typing___Optional[type___HHState] = None,
        brakes : typing___Optional[type___BrakeState] = None,
        safety_bypass : typing___Optional[type___SafetySensorBypassState] = None,
        steering : typing___Optional[type___SteeringState] = None,
        steering_cfg : typing___Optional[type___SteeringCfgState] = None,
        pet_loss : typing___Optional[type___PetLossState] = None,
        speed_limit : typing___Optional[type___SpeedLimitState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"brakes",b"brakes",u"hh_state",b"hh_state",u"pet_loss",b"pet_loss",u"safety_bypass",b"safety_bypass",u"set",b"set",u"speed_limit",b"speed_limit",u"steering",b"steering",u"steering_cfg",b"steering_cfg"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"brakes",b"brakes",u"hh_state",b"hh_state",u"pet_loss",b"pet_loss",u"safety_bypass",b"safety_bypass",u"set",b"set",u"speed_limit",b"speed_limit",u"steering",b"steering",u"steering_cfg",b"steering_cfg"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"set",b"set"]) -> typing_extensions___Literal["hh_state","brakes","safety_bypass","steering","steering_cfg","pet_loss","speed_limit"]: ...
type___SetReply = SetReply

class GetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def status(self) -> type___Empty: ...

    @property
    def hh_state(self) -> type___Empty: ...

    @property
    def brakes(self) -> type___Empty: ...

    @property
    def safety(self) -> type___Empty: ...

    @property
    def safety_bypass(self) -> type___Empty: ...

    @property
    def steering(self) -> type___Empty: ...

    @property
    def steering_cfg(self) -> type___Empty: ...

    @property
    def pet_loss(self) -> type___Empty: ...

    @property
    def speed_limit(self) -> type___Empty: ...

    def __init__(self,
        *,
        status : typing___Optional[type___Empty] = None,
        hh_state : typing___Optional[type___Empty] = None,
        brakes : typing___Optional[type___Empty] = None,
        safety : typing___Optional[type___Empty] = None,
        safety_bypass : typing___Optional[type___Empty] = None,
        steering : typing___Optional[type___Empty] = None,
        steering_cfg : typing___Optional[type___Empty] = None,
        pet_loss : typing___Optional[type___Empty] = None,
        speed_limit : typing___Optional[type___Empty] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"brakes",b"brakes",u"get",b"get",u"hh_state",b"hh_state",u"pet_loss",b"pet_loss",u"safety",b"safety",u"safety_bypass",b"safety_bypass",u"speed_limit",b"speed_limit",u"status",b"status",u"steering",b"steering",u"steering_cfg",b"steering_cfg"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"brakes",b"brakes",u"get",b"get",u"hh_state",b"hh_state",u"pet_loss",b"pet_loss",u"safety",b"safety",u"safety_bypass",b"safety_bypass",u"speed_limit",b"speed_limit",u"status",b"status",u"steering",b"steering",u"steering_cfg",b"steering_cfg"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"get",b"get"]) -> typing_extensions___Literal["status","hh_state","brakes","safety","safety_bypass","steering","steering_cfg","pet_loss","speed_limit"]: ...
type___GetRequest = GetRequest

class GetReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def status(self) -> type___TractorStatus: ...

    @property
    def hh_state(self) -> type___HHState: ...

    @property
    def brakes(self) -> type___BrakeState: ...

    @property
    def safety(self) -> type___SafetySensorsState: ...

    @property
    def safety_bypass(self) -> type___SafetySensorBypassState: ...

    @property
    def steering(self) -> type___SteeringState: ...

    @property
    def steering_cfg(self) -> type___SteeringCfgState: ...

    @property
    def pet_loss(self) -> type___PetLossState: ...

    @property
    def speed_limit(self) -> type___SpeedLimitState: ...

    def __init__(self,
        *,
        status : typing___Optional[type___TractorStatus] = None,
        hh_state : typing___Optional[type___HHState] = None,
        brakes : typing___Optional[type___BrakeState] = None,
        safety : typing___Optional[type___SafetySensorsState] = None,
        safety_bypass : typing___Optional[type___SafetySensorBypassState] = None,
        steering : typing___Optional[type___SteeringState] = None,
        steering_cfg : typing___Optional[type___SteeringCfgState] = None,
        pet_loss : typing___Optional[type___PetLossState] = None,
        speed_limit : typing___Optional[type___SpeedLimitState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"brakes",b"brakes",u"get",b"get",u"hh_state",b"hh_state",u"pet_loss",b"pet_loss",u"safety",b"safety",u"safety_bypass",b"safety_bypass",u"speed_limit",b"speed_limit",u"status",b"status",u"steering",b"steering",u"steering_cfg",b"steering_cfg"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"brakes",b"brakes",u"get",b"get",u"hh_state",b"hh_state",u"pet_loss",b"pet_loss",u"safety",b"safety",u"safety_bypass",b"safety_bypass",u"speed_limit",b"speed_limit",u"status",b"status",u"steering",b"steering",u"steering_cfg",b"steering_cfg"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"get",b"get"]) -> typing_extensions___Literal["status","hh_state","brakes","safety","safety_bypass","steering","steering_cfg","pet_loss","speed_limit"]: ...
type___GetReply = GetReply

class DebugRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___DebugRequest = DebugRequest

class DebugReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___DebugReply = DebugReply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def set(self) -> type___SetRequest: ...

    @property
    def get(self) -> type___GetRequest: ...

    @property
    def pet(self) -> type___PetRequest: ...

    @property
    def debug(self) -> type___DebugRequest: ...

    def __init__(self,
        *,
        set : typing___Optional[type___SetRequest] = None,
        get : typing___Optional[type___GetRequest] = None,
        pet : typing___Optional[type___PetRequest] = None,
        debug : typing___Optional[type___DebugRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"debug",b"debug",u"get",b"get",u"pet",b"pet",u"request",b"request",u"set",b"set"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"debug",b"debug",u"get",b"get",u"pet",b"pet",u"request",b"request",u"set",b"set"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["set","get","pet","debug"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def set(self) -> type___SetReply: ...

    @property
    def get(self) -> type___GetReply: ...

    @property
    def pet(self) -> type___TractorStatus: ...

    @property
    def debug(self) -> type___DebugReply: ...

    def __init__(self,
        *,
        set : typing___Optional[type___SetReply] = None,
        get : typing___Optional[type___GetReply] = None,
        pet : typing___Optional[type___TractorStatus] = None,
        debug : typing___Optional[type___DebugReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"debug",b"debug",u"get",b"get",u"pet",b"pet",u"reply",b"reply",u"set",b"set"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"debug",b"debug",u"get",b"get",u"pet",b"pet",u"reply",b"reply",u"set",b"set"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["set","get","pet","debug"]: ...
type___Reply = Reply

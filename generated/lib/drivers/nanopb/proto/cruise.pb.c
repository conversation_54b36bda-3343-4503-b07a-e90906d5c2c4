/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "cruise.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(cruise_Throttle_Request, cruise_Throttle_Request, AUTO)


PB_BIND(cruise_Throttle_Reply, cruise_Throttle_Reply, AUTO)


PB_BIND(cruise_Status_Request, cruise_Status_Request, AUTO)


PB_BIND(cruise_Status_Reply, cruise_Status_Reply, AUTO)


PB_BIND(cruise_Enable_Request, cruise_Enable_Request, AUTO)


PB_BIND(cruise_Enable_Reply, cruise_Enable_Reply, AUTO)


PB_BIND(cruise_Gear_Request, cruise_Gear_Request, AUTO)


PB_BIND(cruise_Gear_Reply, cruise_Gear_Reply, AUTO)


PB_BIND(cruise_Variant_Request, cruise_Variant_Request, AUTO)


PB_BIND(cruise_Variant_Reply, cruise_Variant_Reply, AUTO)


PB_BIND(cruise_Request, cruise_Request, 2)


PB_BIND(cruise_Reply, cruise_Reply, 2)





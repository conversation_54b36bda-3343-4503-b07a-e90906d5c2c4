# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/diagnostic.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/diagnostic.proto',
  package='diagnostic',
  syntax='proto3',
  serialized_options=b'Z\021nanopb/diagnostic',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n3generated/lib/drivers/nanopb/proto/diagnostic.proto\x12\ndiagnostic\"\x11\n\x04Ping\x12\t\n\x01x\x18\x01 \x01(\x05\"\x11\n\x04Pong\x12\t\n\x01x\x18\x01 \x01(\x05\x42\x13Z\x11nanopb/diagnosticb\x06proto3'
)




_PING = _descriptor.Descriptor(
  name='Ping',
  full_name='diagnostic.Ping',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='diagnostic.Ping.x', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=67,
  serialized_end=84,
)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='diagnostic.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='diagnostic.Pong.x', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=86,
  serialized_end=103,
)

DESCRIPTOR.message_types_by_name['Ping'] = _PING
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Ping = _reflection.GeneratedProtocolMessageType('Ping', (_message.Message,), {
  'DESCRIPTOR' : _PING,
  '__module__' : 'generated.lib.drivers.nanopb.proto.diagnostic_pb2'
  # @@protoc_insertion_point(class_scope:diagnostic.Ping)
  })
_sym_db.RegisterMessage(Ping)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), {
  'DESCRIPTOR' : _PONG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.diagnostic_pb2'
  # @@protoc_insertion_point(class_scope:diagnostic.Pong)
  })
_sym_db.RegisterMessage(Pong)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

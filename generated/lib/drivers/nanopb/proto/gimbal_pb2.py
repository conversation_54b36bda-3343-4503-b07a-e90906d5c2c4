# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/gimbal.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import servo_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2
from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2
from generated.lib.drivers.nanopb.proto import epos_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/gimbal.proto',
  package='gimbal',
  syntax='proto3',
  serialized_options=b'Z\rnanopb/gimbal',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n/generated/lib/drivers/nanopb/proto/gimbal.proto\x12\x06gimbal\x1a.generated/lib/drivers/nanopb/proto/servo.proto\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\x1a-generated/lib/drivers/nanopb/proto/epos.proto\"]\n\x0c\x42oot_Request\x12%\n\npan_params\x18\x01 \x01(\x0b\x32\x11.epos.Home_Params\x12&\n\x0btilt_params\x18\x02 \x01(\x0b\x32\x11.epos.Home_Params\"\x0e\n\x0cStop_Request\"K\n\x0eServos_Request\x12\x1b\n\x03pan\x18\x01 \x01(\x0b\x32\x0e.servo.Request\x12\x1c\n\x04tilt\x18\x02 \x01(\x0b\x32\x0e.servo.Request\",\n\x14GetPositionAtRequest\x12\x14\n\x0ctimestamp_us\x18\x01 \x01(\x04\"E\n\x0cServos_Reply\x12\x19\n\x03pan\x18\x01 \x01(\x0b\x32\x0c.servo.Reply\x12\x1a\n\x04tilt\x18\x02 \x01(\x0b\x32\x0c.servo.Reply\"L\n\nPositionAt\x12\x0b\n\x03pan\x18\x01 \x01(\x05\x12\x0c\n\x04tilt\x18\x02 \x01(\x05\x12\x14\n\x0ctimestamp_us\x18\x03 \x01(\x04\x12\r\n\x05valid\x18\x04 \x01(\x08\"`\n\x12GetPositionAtReply\x12%\n\trequested\x18\x01 \x01(\x0b\x32\x12.gimbal.PositionAt\x12#\n\x07\x63urrent\x18\x02 \x01(\x0b\x32\x12.gimbal.PositionAt\"\xbc\x01\n\x07Request\x12$\n\x04\x62oot\x18\x01 \x01(\x0b\x32\x14.gimbal.Boot_RequestH\x00\x12$\n\x04stop\x18\x02 \x01(\x0b\x32\x14.gimbal.Stop_RequestH\x00\x12(\n\x06servos\x18\x03 \x01(\x0b\x32\x16.gimbal.Servos_RequestH\x00\x12\x30\n\x08position\x18\x04 \x01(\x0b\x32\x1c.gimbal.GetPositionAtRequestH\x00\x42\t\n\x07request\"\xa0\x01\n\x05Reply\x12\x1d\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x02 \x01(\x0b\x32\x08.ack.AckH\x00\x12&\n\x06servos\x18\x03 \x01(\x0b\x32\x14.gimbal.Servos_ReplyH\x00\x12.\n\x08position\x18\x04 \x01(\x0b\x32\x1a.gimbal.GetPositionAtReplyH\x00\x42\x07\n\x05replyB\x0fZ\rnanopb/gimbalb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2.DESCRIPTOR,])




_BOOT_REQUEST = _descriptor.Descriptor(
  name='Boot_Request',
  full_name='gimbal.Boot_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan_params', full_name='gimbal.Boot_Request.pan_params', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt_params', full_name='gimbal.Boot_Request.tilt_params', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=248,
  serialized_end=341,
)


_STOP_REQUEST = _descriptor.Descriptor(
  name='Stop_Request',
  full_name='gimbal.Stop_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=343,
  serialized_end=357,
)


_SERVOS_REQUEST = _descriptor.Descriptor(
  name='Servos_Request',
  full_name='gimbal.Servos_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan', full_name='gimbal.Servos_Request.pan', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt', full_name='gimbal.Servos_Request.tilt', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=359,
  serialized_end=434,
)


_GETPOSITIONATREQUEST = _descriptor.Descriptor(
  name='GetPositionAtRequest',
  full_name='gimbal.GetPositionAtRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_us', full_name='gimbal.GetPositionAtRequest.timestamp_us', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=436,
  serialized_end=480,
)


_SERVOS_REPLY = _descriptor.Descriptor(
  name='Servos_Reply',
  full_name='gimbal.Servos_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan', full_name='gimbal.Servos_Reply.pan', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt', full_name='gimbal.Servos_Reply.tilt', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=482,
  serialized_end=551,
)


_POSITIONAT = _descriptor.Descriptor(
  name='PositionAt',
  full_name='gimbal.PositionAt',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan', full_name='gimbal.PositionAt.pan', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt', full_name='gimbal.PositionAt.tilt', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_us', full_name='gimbal.PositionAt.timestamp_us', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='valid', full_name='gimbal.PositionAt.valid', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=553,
  serialized_end=629,
)


_GETPOSITIONATREPLY = _descriptor.Descriptor(
  name='GetPositionAtReply',
  full_name='gimbal.GetPositionAtReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='requested', full_name='gimbal.GetPositionAtReply.requested', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current', full_name='gimbal.GetPositionAtReply.current', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=631,
  serialized_end=727,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='gimbal.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='boot', full_name='gimbal.Request.boot', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stop', full_name='gimbal.Request.stop', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='servos', full_name='gimbal.Request.servos', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position', full_name='gimbal.Request.position', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='gimbal.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=730,
  serialized_end=918,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='gimbal.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='gimbal.Reply.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='gimbal.Reply.ack', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='servos', full_name='gimbal.Reply.servos', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position', full_name='gimbal.Reply.position', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='gimbal.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=921,
  serialized_end=1081,
)

_BOOT_REQUEST.fields_by_name['pan_params'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2._HOME_PARAMS
_BOOT_REQUEST.fields_by_name['tilt_params'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2._HOME_PARAMS
_SERVOS_REQUEST.fields_by_name['pan'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2._REQUEST
_SERVOS_REQUEST.fields_by_name['tilt'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2._REQUEST
_SERVOS_REPLY.fields_by_name['pan'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2._REPLY
_SERVOS_REPLY.fields_by_name['tilt'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2._REPLY
_GETPOSITIONATREPLY.fields_by_name['requested'].message_type = _POSITIONAT
_GETPOSITIONATREPLY.fields_by_name['current'].message_type = _POSITIONAT
_REQUEST.fields_by_name['boot'].message_type = _BOOT_REQUEST
_REQUEST.fields_by_name['stop'].message_type = _STOP_REQUEST
_REQUEST.fields_by_name['servos'].message_type = _SERVOS_REQUEST
_REQUEST.fields_by_name['position'].message_type = _GETPOSITIONATREQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['boot'])
_REQUEST.fields_by_name['boot'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['stop'])
_REQUEST.fields_by_name['stop'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['servos'])
_REQUEST.fields_by_name['servos'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['position'])
_REQUEST.fields_by_name['position'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['servos'].message_type = _SERVOS_REPLY
_REPLY.fields_by_name['position'].message_type = _GETPOSITIONATREPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['servos'])
_REPLY.fields_by_name['servos'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['position'])
_REPLY.fields_by_name['position'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Boot_Request'] = _BOOT_REQUEST
DESCRIPTOR.message_types_by_name['Stop_Request'] = _STOP_REQUEST
DESCRIPTOR.message_types_by_name['Servos_Request'] = _SERVOS_REQUEST
DESCRIPTOR.message_types_by_name['GetPositionAtRequest'] = _GETPOSITIONATREQUEST
DESCRIPTOR.message_types_by_name['Servos_Reply'] = _SERVOS_REPLY
DESCRIPTOR.message_types_by_name['PositionAt'] = _POSITIONAT
DESCRIPTOR.message_types_by_name['GetPositionAtReply'] = _GETPOSITIONATREPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Boot_Request = _reflection.GeneratedProtocolMessageType('Boot_Request', (_message.Message,), {
  'DESCRIPTOR' : _BOOT_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gimbal_pb2'
  # @@protoc_insertion_point(class_scope:gimbal.Boot_Request)
  })
_sym_db.RegisterMessage(Boot_Request)

Stop_Request = _reflection.GeneratedProtocolMessageType('Stop_Request', (_message.Message,), {
  'DESCRIPTOR' : _STOP_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gimbal_pb2'
  # @@protoc_insertion_point(class_scope:gimbal.Stop_Request)
  })
_sym_db.RegisterMessage(Stop_Request)

Servos_Request = _reflection.GeneratedProtocolMessageType('Servos_Request', (_message.Message,), {
  'DESCRIPTOR' : _SERVOS_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gimbal_pb2'
  # @@protoc_insertion_point(class_scope:gimbal.Servos_Request)
  })
_sym_db.RegisterMessage(Servos_Request)

GetPositionAtRequest = _reflection.GeneratedProtocolMessageType('GetPositionAtRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPOSITIONATREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gimbal_pb2'
  # @@protoc_insertion_point(class_scope:gimbal.GetPositionAtRequest)
  })
_sym_db.RegisterMessage(GetPositionAtRequest)

Servos_Reply = _reflection.GeneratedProtocolMessageType('Servos_Reply', (_message.Message,), {
  'DESCRIPTOR' : _SERVOS_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gimbal_pb2'
  # @@protoc_insertion_point(class_scope:gimbal.Servos_Reply)
  })
_sym_db.RegisterMessage(Servos_Reply)

PositionAt = _reflection.GeneratedProtocolMessageType('PositionAt', (_message.Message,), {
  'DESCRIPTOR' : _POSITIONAT,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gimbal_pb2'
  # @@protoc_insertion_point(class_scope:gimbal.PositionAt)
  })
_sym_db.RegisterMessage(PositionAt)

GetPositionAtReply = _reflection.GeneratedProtocolMessageType('GetPositionAtReply', (_message.Message,), {
  'DESCRIPTOR' : _GETPOSITIONATREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gimbal_pb2'
  # @@protoc_insertion_point(class_scope:gimbal.GetPositionAtReply)
  })
_sym_db.RegisterMessage(GetPositionAtReply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gimbal_pb2'
  # @@protoc_insertion_point(class_scope:gimbal.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gimbal_pb2'
  # @@protoc_insertion_point(class_scope:gimbal.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

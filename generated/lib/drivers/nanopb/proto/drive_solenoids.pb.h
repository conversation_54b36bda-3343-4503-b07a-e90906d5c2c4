/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_DRIVE_SOLENOIDS_DRIVE_SOLENOIDS_PB_H_INCLUDED
#define PB_DRIVE_SOLENOIDS_DRIVE_SOLENOIDS_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _drive_solenoids_Drive_Request_Direction {
    drive_solenoids_Drive_Request_Direction_forward = 0,
    drive_solenoids_Drive_Request_Direction_backward = 1,
    drive_solenoids_Drive_Request_Direction_stop = 3
} drive_solenoids_Drive_Request_Direction;

typedef enum _drive_solenoids_Turn_Request_Direction {
    drive_solenoids_Turn_Request_Direction_left = 0,
    drive_solenoids_Turn_Request_Direction_right = 1,
    drive_solenoids_Turn_Request_Direction_straight = 2
} drive_solenoids_Turn_Request_Direction;

/* Struct definitions */
typedef struct _drive_solenoids_Drive_Reply {
    char dummy_field;
} drive_solenoids_Drive_Reply;

typedef struct _drive_solenoids_Turn_Reply {
    char dummy_field;
} drive_solenoids_Turn_Reply;

typedef struct _drive_solenoids_Drive_Request {
    drive_solenoids_Drive_Request_Direction dir;
    float duty_cycle;
} drive_solenoids_Drive_Request;

typedef struct _drive_solenoids_Reply {
    pb_size_t which_reply;
    union {
        drive_solenoids_Drive_Reply drive;
        drive_solenoids_Turn_Reply turn;
    } reply;
} drive_solenoids_Reply;

typedef struct _drive_solenoids_Turn_Request {
    drive_solenoids_Turn_Request_Direction dir;
    float duty_cycle;
} drive_solenoids_Turn_Request;

typedef struct _drive_solenoids_Request {
    pb_size_t which_request;
    union {
        drive_solenoids_Drive_Request drive;
        drive_solenoids_Turn_Request turn;
    } request;
} drive_solenoids_Request;


/* Helper constants for enums */
#define _drive_solenoids_Drive_Request_Direction_MIN drive_solenoids_Drive_Request_Direction_forward
#define _drive_solenoids_Drive_Request_Direction_MAX drive_solenoids_Drive_Request_Direction_stop
#define _drive_solenoids_Drive_Request_Direction_ARRAYSIZE ((drive_solenoids_Drive_Request_Direction)(drive_solenoids_Drive_Request_Direction_stop+1))

#define _drive_solenoids_Turn_Request_Direction_MIN drive_solenoids_Turn_Request_Direction_left
#define _drive_solenoids_Turn_Request_Direction_MAX drive_solenoids_Turn_Request_Direction_straight
#define _drive_solenoids_Turn_Request_Direction_ARRAYSIZE ((drive_solenoids_Turn_Request_Direction)(drive_solenoids_Turn_Request_Direction_straight+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define drive_solenoids_Drive_Request_init_default {_drive_solenoids_Drive_Request_Direction_MIN, 0}
#define drive_solenoids_Drive_Reply_init_default {0}
#define drive_solenoids_Turn_Request_init_default {_drive_solenoids_Turn_Request_Direction_MIN, 0}
#define drive_solenoids_Turn_Reply_init_default  {0}
#define drive_solenoids_Request_init_default     {0, {drive_solenoids_Drive_Request_init_default}}
#define drive_solenoids_Reply_init_default       {0, {drive_solenoids_Drive_Reply_init_default}}
#define drive_solenoids_Drive_Request_init_zero  {_drive_solenoids_Drive_Request_Direction_MIN, 0}
#define drive_solenoids_Drive_Reply_init_zero    {0}
#define drive_solenoids_Turn_Request_init_zero   {_drive_solenoids_Turn_Request_Direction_MIN, 0}
#define drive_solenoids_Turn_Reply_init_zero     {0}
#define drive_solenoids_Request_init_zero        {0, {drive_solenoids_Drive_Request_init_zero}}
#define drive_solenoids_Reply_init_zero          {0, {drive_solenoids_Drive_Reply_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define drive_solenoids_Drive_Request_dir_tag    1
#define drive_solenoids_Drive_Request_duty_cycle_tag 2
#define drive_solenoids_Reply_drive_tag          1
#define drive_solenoids_Reply_turn_tag           2
#define drive_solenoids_Turn_Request_dir_tag     1
#define drive_solenoids_Turn_Request_duty_cycle_tag 2
#define drive_solenoids_Request_drive_tag        1
#define drive_solenoids_Request_turn_tag         2

/* Struct field encoding specification for nanopb */
#define drive_solenoids_Drive_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    dir,               1) \
X(a, STATIC,   SINGULAR, FLOAT,    duty_cycle,        2)
#define drive_solenoids_Drive_Request_CALLBACK NULL
#define drive_solenoids_Drive_Request_DEFAULT NULL

#define drive_solenoids_Drive_Reply_FIELDLIST(X, a) \

#define drive_solenoids_Drive_Reply_CALLBACK NULL
#define drive_solenoids_Drive_Reply_DEFAULT NULL

#define drive_solenoids_Turn_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    dir,               1) \
X(a, STATIC,   SINGULAR, FLOAT,    duty_cycle,        2)
#define drive_solenoids_Turn_Request_CALLBACK NULL
#define drive_solenoids_Turn_Request_DEFAULT NULL

#define drive_solenoids_Turn_Reply_FIELDLIST(X, a) \

#define drive_solenoids_Turn_Reply_CALLBACK NULL
#define drive_solenoids_Turn_Reply_DEFAULT NULL

#define drive_solenoids_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,drive,request.drive),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,turn,request.turn),   2)
#define drive_solenoids_Request_CALLBACK NULL
#define drive_solenoids_Request_DEFAULT NULL
#define drive_solenoids_Request_request_drive_MSGTYPE drive_solenoids_Drive_Request
#define drive_solenoids_Request_request_turn_MSGTYPE drive_solenoids_Turn_Request

#define drive_solenoids_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,drive,reply.drive),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,turn,reply.turn),   2)
#define drive_solenoids_Reply_CALLBACK NULL
#define drive_solenoids_Reply_DEFAULT NULL
#define drive_solenoids_Reply_reply_drive_MSGTYPE drive_solenoids_Drive_Reply
#define drive_solenoids_Reply_reply_turn_MSGTYPE drive_solenoids_Turn_Reply

extern const pb_msgdesc_t drive_solenoids_Drive_Request_msg;
extern const pb_msgdesc_t drive_solenoids_Drive_Reply_msg;
extern const pb_msgdesc_t drive_solenoids_Turn_Request_msg;
extern const pb_msgdesc_t drive_solenoids_Turn_Reply_msg;
extern const pb_msgdesc_t drive_solenoids_Request_msg;
extern const pb_msgdesc_t drive_solenoids_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define drive_solenoids_Drive_Request_fields &drive_solenoids_Drive_Request_msg
#define drive_solenoids_Drive_Reply_fields &drive_solenoids_Drive_Reply_msg
#define drive_solenoids_Turn_Request_fields &drive_solenoids_Turn_Request_msg
#define drive_solenoids_Turn_Reply_fields &drive_solenoids_Turn_Reply_msg
#define drive_solenoids_Request_fields &drive_solenoids_Request_msg
#define drive_solenoids_Reply_fields &drive_solenoids_Reply_msg

/* Maximum encoded size of messages (where known) */
#define drive_solenoids_Drive_Request_size       7
#define drive_solenoids_Drive_Reply_size         0
#define drive_solenoids_Turn_Request_size        7
#define drive_solenoids_Turn_Reply_size          0
#define drive_solenoids_Request_size             9
#define drive_solenoids_Reply_size               2

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "reaper_module_controller.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(reaper_module_controller_NetworkConfigRequest, reaper_module_controller_NetworkConfigRequest, AUTO)


PB_BIND(reaper_module_controller_NetworkResetRequest, reaper_module_controller_NetworkResetRequest, AUTO)


PB_BIND(reaper_module_controller_NetworkPowerCycleRequest, reaper_module_controller_NetworkPowerCycleRequest, AUTO)


PB_BIND(reaper_module_controller_NetworkRequest, reaper_module_controller_NetworkRequest, AUTO)


PB_BIND(reaper_module_controller_NetworkAddress, reaper_module_controller_NetworkAddress, AUTO)


PB_BIND(reaper_module_controller_NetworkConfigReply, reaper_module_controller_NetworkConfigReply, AUTO)


PB_BIND(reaper_module_controller_NetworkReply, reaper_module_controller_NetworkReply, AUTO)


PB_BIND(reaper_module_controller_ScannerStatus, reaper_module_controller_ScannerStatus, AUTO)


PB_BIND(reaper_module_controller_ScannerGetStatusRequest, reaper_module_controller_ScannerGetStatusRequest, AUTO)


PB_BIND(reaper_module_controller_ScannerSetPowerRequest, reaper_module_controller_ScannerSetPowerRequest, AUTO)


PB_BIND(reaper_module_controller_ScannerResetOcpRequest, reaper_module_controller_ScannerResetOcpRequest, AUTO)


PB_BIND(reaper_module_controller_ScannerRequest, reaper_module_controller_ScannerRequest, AUTO)


PB_BIND(reaper_module_controller_ScannerStatusReply, reaper_module_controller_ScannerStatusReply, AUTO)


PB_BIND(reaper_module_controller_ScannerReply, reaper_module_controller_ScannerReply, AUTO)


PB_BIND(reaper_module_controller_PowerRequest, reaper_module_controller_PowerRequest, AUTO)


PB_BIND(reaper_module_controller_PowerReply, reaper_module_controller_PowerReply, AUTO)


PB_BIND(reaper_module_controller_StrobeStatusRequest, reaper_module_controller_StrobeStatusRequest, AUTO)


PB_BIND(reaper_module_controller_SetStrobeStateRequest, reaper_module_controller_SetStrobeStateRequest, AUTO)


PB_BIND(reaper_module_controller_TimedStrobeDisableRequest, reaper_module_controller_TimedStrobeDisableRequest, AUTO)


PB_BIND(reaper_module_controller_StrobeStatusReply, reaper_module_controller_StrobeStatusReply, AUTO)


PB_BIND(reaper_module_controller_StrobeRequest, reaper_module_controller_StrobeRequest, 2)


PB_BIND(reaper_module_controller_StrobeReply, reaper_module_controller_StrobeReply, AUTO)


PB_BIND(reaper_module_controller_ModuleIdentity, reaper_module_controller_ModuleIdentity, AUTO)


PB_BIND(reaper_module_controller_GetModuleIdentityRequest, reaper_module_controller_GetModuleIdentityRequest, AUTO)


PB_BIND(reaper_module_controller_SetOtpLockRequest, reaper_module_controller_SetOtpLockRequest, AUTO)


PB_BIND(reaper_module_controller_SetBoardIdRequest, reaper_module_controller_SetBoardIdRequest, AUTO)


PB_BIND(reaper_module_controller_ConfigRequest, reaper_module_controller_ConfigRequest, AUTO)


PB_BIND(reaper_module_controller_ConfigReply, reaper_module_controller_ConfigReply, AUTO)


PB_BIND(reaper_module_controller_ThermostatConfig, reaper_module_controller_ThermostatConfig, AUTO)


PB_BIND(reaper_module_controller_FanSetRequest, reaper_module_controller_FanSetRequest, AUTO)


PB_BIND(reaper_module_controller_FanThermostatConfigRequest, reaper_module_controller_FanThermostatConfigRequest, AUTO)


PB_BIND(reaper_module_controller_FanRequest, reaper_module_controller_FanRequest, AUTO)


PB_BIND(reaper_module_controller_FanReply, reaper_module_controller_FanReply, AUTO)


PB_BIND(reaper_module_controller_RelayRequest, reaper_module_controller_RelayRequest, AUTO)


PB_BIND(reaper_module_controller_RelayReply, reaper_module_controller_RelayReply, AUTO)


PB_BIND(reaper_module_controller_SensorRequest, reaper_module_controller_SensorRequest, AUTO)


PB_BIND(reaper_module_controller_SensorReply, reaper_module_controller_SensorReply, AUTO)


PB_BIND(reaper_module_controller_SensorReply_envdata, reaper_module_controller_SensorReply_envdata, AUTO)


PB_BIND(reaper_module_controller_SensorReply_imudata, reaper_module_controller_SensorReply_imudata, AUTO)


PB_BIND(reaper_module_controller_SensorReply_thermdata, reaper_module_controller_SensorReply_thermdata, AUTO)


PB_BIND(reaper_module_controller_SensorReply_leakdata, reaper_module_controller_SensorReply_leakdata, AUTO)


PB_BIND(reaper_module_controller_SensorReply_pressdata, reaper_module_controller_SensorReply_pressdata, AUTO)


PB_BIND(reaper_module_controller_StatusRequest, reaper_module_controller_StatusRequest, AUTO)


PB_BIND(reaper_module_controller_StatusReply, reaper_module_controller_StatusReply, 2)


PB_BIND(reaper_module_controller_CoreDumpStart, reaper_module_controller_CoreDumpStart, AUTO)


PB_BIND(reaper_module_controller_CoreDumpEnd, reaper_module_controller_CoreDumpEnd, AUTO)


PB_BIND(reaper_module_controller_CoreDumpData, reaper_module_controller_CoreDumpData, 2)


PB_BIND(reaper_module_controller_CoreDumpReply, reaper_module_controller_CoreDumpReply, 2)


PB_BIND(reaper_module_controller_UdpRequest, reaper_module_controller_UdpRequest, 2)


PB_BIND(reaper_module_controller_UdpReply, reaper_module_controller_UdpReply, 2)


PB_BIND(reaper_module_controller_OobRequest, reaper_module_controller_OobRequest, 2)


PB_BIND(reaper_module_controller_OobReply, reaper_module_controller_OobReply, 2)






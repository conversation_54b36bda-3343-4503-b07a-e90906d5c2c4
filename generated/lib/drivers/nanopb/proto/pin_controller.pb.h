/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_PIN_CONTROLLER_PIN_CONTROLLER_PB_H_INCLUDED
#define PB_PIN_CONTROLLER_PIN_CONTROLLER_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"
#include "generated/lib/drivers/nanopb/proto/version.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _pin_controller_DigitalPinValue {
    pin_controller_DigitalPinValue_LOW = 0,
    pin_controller_DigitalPinValue_HIGH = 1
} pin_controller_DigitalPinValue;

typedef enum _pin_controller_PinMode {
    pin_controller_PinMode_INPUT = 0,
    pin_controller_PinMode_OUTPUT = 1,
    pin_controller_PinMode_INPUT_PULL_UP = 2
} pin_controller_PinMode;

/* Struct definitions */
typedef struct _pin_controller_AnalogRead {
    uint32_t pin_number;
} pin_controller_AnalogRead;

typedef struct _pin_controller_AnalogReadValue {
    uint32_t pin_number;
    uint32_t value;
} pin_controller_AnalogReadValue;

typedef struct _pin_controller_DigitalRead {
    uint32_t pin_number;
} pin_controller_DigitalRead;

typedef struct _pin_controller_DigitalReadValue {
    uint32_t pin_number;
    pin_controller_DigitalPinValue value;
} pin_controller_DigitalReadValue;

typedef struct _pin_controller_DigitalWrite {
    uint32_t pin_number;
    pin_controller_DigitalPinValue value;
} pin_controller_DigitalWrite;

typedef struct _pin_controller_PWMConfiguration {
    uint32_t pin_number;
    uint32_t value_8bit;
} pin_controller_PWMConfiguration;

typedef struct _pin_controller_PinConfiguration {
    uint32_t pin_number;
    pin_controller_PinMode pin_mode;
} pin_controller_PinConfiguration;

typedef struct _pin_controller_PinReply {
    pb_size_t which_reply;
    union {
        ack_Ack ack;
        pin_controller_DigitalReadValue read;
        pin_controller_AnalogReadValue analog;
    } reply;
} pin_controller_PinReply;

typedef struct _pin_controller_PinRequest {
    pb_size_t which_request;
    union {
        pin_controller_PinConfiguration pin;
        pin_controller_PWMConfiguration pwm;
        pin_controller_DigitalWrite write;
        pin_controller_DigitalRead read;
        pin_controller_AnalogRead analog;
    } request;
} pin_controller_PinRequest;

typedef struct _pin_controller_Reply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        ack_Ack ack;
        version_Version_Reply version;
        pin_controller_PinReply pin;
    } reply;
} pin_controller_Reply;

typedef struct _pin_controller_Request {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        version_Version_Request version;
        pin_controller_PinRequest pin;
    } request;
} pin_controller_Request;


/* Helper constants for enums */
#define _pin_controller_DigitalPinValue_MIN pin_controller_DigitalPinValue_LOW
#define _pin_controller_DigitalPinValue_MAX pin_controller_DigitalPinValue_HIGH
#define _pin_controller_DigitalPinValue_ARRAYSIZE ((pin_controller_DigitalPinValue)(pin_controller_DigitalPinValue_HIGH+1))

#define _pin_controller_PinMode_MIN pin_controller_PinMode_INPUT
#define _pin_controller_PinMode_MAX pin_controller_PinMode_INPUT_PULL_UP
#define _pin_controller_PinMode_ARRAYSIZE ((pin_controller_PinMode)(pin_controller_PinMode_INPUT_PULL_UP+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define pin_controller_AnalogReadValue_init_default {0, 0}
#define pin_controller_DigitalReadValue_init_default {0, _pin_controller_DigitalPinValue_MIN}
#define pin_controller_PinReply_init_default     {0, {ack_Ack_init_default}}
#define pin_controller_AnalogRead_init_default   {0}
#define pin_controller_DigitalRead_init_default  {0}
#define pin_controller_DigitalWrite_init_default {0, _pin_controller_DigitalPinValue_MIN}
#define pin_controller_PinConfiguration_init_default {0, _pin_controller_PinMode_MIN}
#define pin_controller_PWMConfiguration_init_default {0, 0}
#define pin_controller_PinRequest_init_default   {0, {pin_controller_PinConfiguration_init_default}}
#define pin_controller_Reply_init_default        {false, request_RequestHeader_init_default, 0, {diagnostic_Pong_init_default}}
#define pin_controller_Request_init_default      {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define pin_controller_AnalogReadValue_init_zero {0, 0}
#define pin_controller_DigitalReadValue_init_zero {0, _pin_controller_DigitalPinValue_MIN}
#define pin_controller_PinReply_init_zero        {0, {ack_Ack_init_zero}}
#define pin_controller_AnalogRead_init_zero      {0}
#define pin_controller_DigitalRead_init_zero     {0}
#define pin_controller_DigitalWrite_init_zero    {0, _pin_controller_DigitalPinValue_MIN}
#define pin_controller_PinConfiguration_init_zero {0, _pin_controller_PinMode_MIN}
#define pin_controller_PWMConfiguration_init_zero {0, 0}
#define pin_controller_PinRequest_init_zero      {0, {pin_controller_PinConfiguration_init_zero}}
#define pin_controller_Reply_init_zero           {false, request_RequestHeader_init_zero, 0, {diagnostic_Pong_init_zero}}
#define pin_controller_Request_init_zero         {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define pin_controller_AnalogRead_pin_number_tag 1
#define pin_controller_AnalogReadValue_pin_number_tag 1
#define pin_controller_AnalogReadValue_value_tag 2
#define pin_controller_DigitalRead_pin_number_tag 1
#define pin_controller_DigitalReadValue_pin_number_tag 1
#define pin_controller_DigitalReadValue_value_tag 2
#define pin_controller_DigitalWrite_pin_number_tag 1
#define pin_controller_DigitalWrite_value_tag    2
#define pin_controller_PWMConfiguration_pin_number_tag 1
#define pin_controller_PWMConfiguration_value_8bit_tag 2
#define pin_controller_PinConfiguration_pin_number_tag 1
#define pin_controller_PinConfiguration_pin_mode_tag 2
#define pin_controller_PinReply_ack_tag          1
#define pin_controller_PinReply_read_tag         2
#define pin_controller_PinReply_analog_tag       3
#define pin_controller_PinRequest_pin_tag        1
#define pin_controller_PinRequest_pwm_tag        2
#define pin_controller_PinRequest_write_tag      3
#define pin_controller_PinRequest_read_tag       4
#define pin_controller_PinRequest_analog_tag     5
#define pin_controller_Reply_header_tag          1
#define pin_controller_Reply_pong_tag            2
#define pin_controller_Reply_ack_tag             3
#define pin_controller_Reply_version_tag         4
#define pin_controller_Reply_pin_tag             5
#define pin_controller_Request_header_tag        1
#define pin_controller_Request_ping_tag          2
#define pin_controller_Request_version_tag       3
#define pin_controller_Request_pin_tag           4

/* Struct field encoding specification for nanopb */
#define pin_controller_AnalogReadValue_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   pin_number,        1) \
X(a, STATIC,   SINGULAR, UINT32,   value,             2)
#define pin_controller_AnalogReadValue_CALLBACK NULL
#define pin_controller_AnalogReadValue_DEFAULT NULL

#define pin_controller_DigitalReadValue_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   pin_number,        1) \
X(a, STATIC,   SINGULAR, UENUM,    value,             2)
#define pin_controller_DigitalReadValue_CALLBACK NULL
#define pin_controller_DigitalReadValue_DEFAULT NULL

#define pin_controller_PinReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,read,reply.read),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,analog,reply.analog),   3)
#define pin_controller_PinReply_CALLBACK NULL
#define pin_controller_PinReply_DEFAULT NULL
#define pin_controller_PinReply_reply_ack_MSGTYPE ack_Ack
#define pin_controller_PinReply_reply_read_MSGTYPE pin_controller_DigitalReadValue
#define pin_controller_PinReply_reply_analog_MSGTYPE pin_controller_AnalogReadValue

#define pin_controller_AnalogRead_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   pin_number,        1)
#define pin_controller_AnalogRead_CALLBACK NULL
#define pin_controller_AnalogRead_DEFAULT NULL

#define pin_controller_DigitalRead_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   pin_number,        1)
#define pin_controller_DigitalRead_CALLBACK NULL
#define pin_controller_DigitalRead_DEFAULT NULL

#define pin_controller_DigitalWrite_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   pin_number,        1) \
X(a, STATIC,   SINGULAR, UENUM,    value,             2)
#define pin_controller_DigitalWrite_CALLBACK NULL
#define pin_controller_DigitalWrite_DEFAULT NULL

#define pin_controller_PinConfiguration_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   pin_number,        1) \
X(a, STATIC,   SINGULAR, UENUM,    pin_mode,          2)
#define pin_controller_PinConfiguration_CALLBACK NULL
#define pin_controller_PinConfiguration_DEFAULT NULL

#define pin_controller_PWMConfiguration_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   pin_number,        1) \
X(a, STATIC,   SINGULAR, UINT32,   value_8bit,        2)
#define pin_controller_PWMConfiguration_CALLBACK NULL
#define pin_controller_PWMConfiguration_DEFAULT NULL

#define pin_controller_PinRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pin,request.pin),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pwm,request.pwm),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,write,request.write),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,read,request.read),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,analog,request.analog),   5)
#define pin_controller_PinRequest_CALLBACK NULL
#define pin_controller_PinRequest_DEFAULT NULL
#define pin_controller_PinRequest_request_pin_MSGTYPE pin_controller_PinConfiguration
#define pin_controller_PinRequest_request_pwm_MSGTYPE pin_controller_PWMConfiguration
#define pin_controller_PinRequest_request_write_MSGTYPE pin_controller_DigitalWrite
#define pin_controller_PinRequest_request_read_MSGTYPE pin_controller_DigitalRead
#define pin_controller_PinRequest_request_analog_MSGTYPE pin_controller_AnalogRead

#define pin_controller_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,version,reply.version),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pin,reply.pin),   5)
#define pin_controller_Reply_CALLBACK NULL
#define pin_controller_Reply_DEFAULT NULL
#define pin_controller_Reply_header_MSGTYPE request_RequestHeader
#define pin_controller_Reply_reply_pong_MSGTYPE diagnostic_Pong
#define pin_controller_Reply_reply_ack_MSGTYPE ack_Ack
#define pin_controller_Reply_reply_version_MSGTYPE version_Version_Reply
#define pin_controller_Reply_reply_pin_MSGTYPE pin_controller_PinReply

#define pin_controller_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,version,request.version),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pin,request.pin),   4)
#define pin_controller_Request_CALLBACK NULL
#define pin_controller_Request_DEFAULT NULL
#define pin_controller_Request_header_MSGTYPE request_RequestHeader
#define pin_controller_Request_request_ping_MSGTYPE diagnostic_Ping
#define pin_controller_Request_request_version_MSGTYPE version_Version_Request
#define pin_controller_Request_request_pin_MSGTYPE pin_controller_PinRequest

extern const pb_msgdesc_t pin_controller_AnalogReadValue_msg;
extern const pb_msgdesc_t pin_controller_DigitalReadValue_msg;
extern const pb_msgdesc_t pin_controller_PinReply_msg;
extern const pb_msgdesc_t pin_controller_AnalogRead_msg;
extern const pb_msgdesc_t pin_controller_DigitalRead_msg;
extern const pb_msgdesc_t pin_controller_DigitalWrite_msg;
extern const pb_msgdesc_t pin_controller_PinConfiguration_msg;
extern const pb_msgdesc_t pin_controller_PWMConfiguration_msg;
extern const pb_msgdesc_t pin_controller_PinRequest_msg;
extern const pb_msgdesc_t pin_controller_Reply_msg;
extern const pb_msgdesc_t pin_controller_Request_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define pin_controller_AnalogReadValue_fields &pin_controller_AnalogReadValue_msg
#define pin_controller_DigitalReadValue_fields &pin_controller_DigitalReadValue_msg
#define pin_controller_PinReply_fields &pin_controller_PinReply_msg
#define pin_controller_AnalogRead_fields &pin_controller_AnalogRead_msg
#define pin_controller_DigitalRead_fields &pin_controller_DigitalRead_msg
#define pin_controller_DigitalWrite_fields &pin_controller_DigitalWrite_msg
#define pin_controller_PinConfiguration_fields &pin_controller_PinConfiguration_msg
#define pin_controller_PWMConfiguration_fields &pin_controller_PWMConfiguration_msg
#define pin_controller_PinRequest_fields &pin_controller_PinRequest_msg
#define pin_controller_Reply_fields &pin_controller_Reply_msg
#define pin_controller_Request_fields &pin_controller_Request_msg

/* Maximum encoded size of messages (where known) */
#define pin_controller_AnalogReadValue_size      12
#define pin_controller_DigitalReadValue_size     8
#if defined(ack_Ack_size)
typedef union pin_controller_PinReply_reply_size_union {char f1[(6 + ack_Ack_size)]; char f0[14];} pin_controller_PinReply_reply_size_union;
#define pin_controller_PinReply_size             (0 + sizeof(pin_controller_PinReply_reply_size_union))
#endif
#define pin_controller_AnalogRead_size           6
#define pin_controller_DigitalRead_size          6
#define pin_controller_DigitalWrite_size         8
#define pin_controller_PinConfiguration_size     8
#define pin_controller_PWMConfiguration_size     12
#define pin_controller_PinRequest_size           14
#if defined(request_RequestHeader_size) && defined(diagnostic_Pong_size) && defined(ack_Ack_size) && defined(version_Version_Reply_size) && defined(ack_Ack_size)
typedef union pin_controller_Reply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f3[(6 + ack_Ack_size)]; char f4[(6 + version_Version_Reply_size)]; char f5[(6 + sizeof(pin_controller_PinReply_reply_size_union))];} pin_controller_Reply_reply_size_union;
#define pin_controller_Reply_size                (6 + request_RequestHeader_size + sizeof(pin_controller_Reply_reply_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size) && defined(version_Version_Request_size)
typedef union pin_controller_Request_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f3[(6 + version_Version_Request_size)]; char f0[16];} pin_controller_Request_request_size_union;
#define pin_controller_Request_size              (6 + request_RequestHeader_size + sizeof(pin_controller_Request_request_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timeout_ms: builtin___int = ...

    def __init__(self,
        *,
        timeout_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timeout_ms",b"timeout_ms"]) -> None: ...
type___Config_Request = Config_Request

class Arm_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    armed: builtin___bool = ...

    def __init__(self,
        *,
        armed : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"armed",b"armed"]) -> None: ...
type___Arm_Request = Arm_Request

class Pet_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    firing: builtin___bool = ...

    def __init__(self,
        *,
        firing : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"firing",b"firing"]) -> None: ...
type___Pet_Request = Pet_Request

class Get_State_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_State_Request = Get_State_Request

class State_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    armed: builtin___bool = ...
    petted: builtin___bool = ...

    def __init__(self,
        *,
        armed : typing___Optional[builtin___bool] = None,
        petted : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"armed",b"armed",u"petted",b"petted"]) -> None: ...
type___State_Reply = State_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Config_Request: ...

    @property
    def arm(self) -> type___Arm_Request: ...

    @property
    def pet(self) -> type___Pet_Request: ...

    @property
    def state(self) -> type___Get_State_Request: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Config_Request] = None,
        arm : typing___Optional[type___Arm_Request] = None,
        pet : typing___Optional[type___Pet_Request] = None,
        state : typing___Optional[type___Get_State_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"arm",b"arm",u"config",b"config",u"pet",b"pet",u"request",b"request",u"state",b"state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"arm",b"arm",u"config",b"config",u"pet",b"pet",u"request",b"request",u"state",b"state"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["config","arm","pet","state"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def state(self) -> type___State_Reply: ...

    def __init__(self,
        *,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        state : typing___Optional[type___State_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"reply",b"reply",u"state",b"state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"reply",b"reply",u"state",b"state"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","state"]: ...
type___Reply = Reply

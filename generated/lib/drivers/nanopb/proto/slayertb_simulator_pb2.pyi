"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.diagnostic_pb2 import (
    Ping as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping,
    Pong as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong,
)

from generated.lib.drivers.nanopb.proto.request_pb2 import (
    RequestHeader as generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

SensorValue = typing___NewType('SensorValue', builtin___int)
type___SensorValue = SensorValue
Sensor: _Sensor
class _Sensor(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[SensorValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    LIFTED = typing___cast(SensorValue, 0)
    LEFT_ESTOP = typing___cast(SensorValue, 1)
    RIGHT_ESTOP = typing___cast(SensorValue, 2)
    CAB_ESTOP = typing___cast(SensorValue, 3)
    LASER_KEY = typing___cast(SensorValue, 4)
    LASER_INTERLOCK = typing___cast(SensorValue, 5)
    WATER_PROTECT = typing___cast(SensorValue, 6)
    TRACTOR_POWER = typing___cast(SensorValue, 7)
    TEMP = typing___cast(SensorValue, 8)
    HUMIDITY = typing___cast(SensorValue, 9)
    BATTERY_VOLTAGE = typing___cast(SensorValue, 10)
    CHILLER_RUN_SIGNAL = typing___cast(SensorValue, 11)
    CAB_ESTOP_SIGNAL = typing___cast(SensorValue, 12)
    BEACON_LEFT_SIGNAL = typing___cast(SensorValue, 13)
    BEACON_RIGHT_SIGNAL = typing___cast(SensorValue, 14)
LIFTED = typing___cast(SensorValue, 0)
LEFT_ESTOP = typing___cast(SensorValue, 1)
RIGHT_ESTOP = typing___cast(SensorValue, 2)
CAB_ESTOP = typing___cast(SensorValue, 3)
LASER_KEY = typing___cast(SensorValue, 4)
LASER_INTERLOCK = typing___cast(SensorValue, 5)
WATER_PROTECT = typing___cast(SensorValue, 6)
TRACTOR_POWER = typing___cast(SensorValue, 7)
TEMP = typing___cast(SensorValue, 8)
HUMIDITY = typing___cast(SensorValue, 9)
BATTERY_VOLTAGE = typing___cast(SensorValue, 10)
CHILLER_RUN_SIGNAL = typing___cast(SensorValue, 11)
CAB_ESTOP_SIGNAL = typing___cast(SensorValue, 12)
BEACON_LEFT_SIGNAL = typing___cast(SensorValue, 13)
BEACON_RIGHT_SIGNAL = typing___cast(SensorValue, 14)

class GetValueRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    sensor: type___SensorValue = ...

    def __init__(self,
        *,
        sensor : typing___Optional[type___SensorValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"sensor",b"sensor"]) -> None: ...
type___GetValueRequest = GetValueRequest

class GetValueReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    boolean_value: builtin___bool = ...
    int_value: builtin___int = ...

    def __init__(self,
        *,
        boolean_value : typing___Optional[builtin___bool] = None,
        int_value : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"boolean_value",b"boolean_value",u"int_value",b"int_value",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"boolean_value",b"boolean_value",u"int_value",b"int_value",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["boolean_value","int_value"]: ...
type___GetValueReply = GetValueReply

class SetValueRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    sensor: type___SensorValue = ...
    boolean_value: builtin___bool = ...
    int_value: builtin___int = ...

    def __init__(self,
        *,
        sensor : typing___Optional[type___SensorValue] = None,
        boolean_value : typing___Optional[builtin___bool] = None,
        int_value : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"boolean_value",b"boolean_value",u"int_value",b"int_value",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"boolean_value",b"boolean_value",u"int_value",b"int_value",u"sensor",b"sensor",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["boolean_value","int_value"]: ...
type___SetValueRequest = SetValueRequest

class SetValueReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetValueReply = SetValueReply

class UseTempSensorRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    on: builtin___bool = ...

    def __init__(self,
        *,
        on : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"on",b"on"]) -> None: ...
type___UseTempSensorRequest = UseTempSensorRequest

class UseTempSensorReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___UseTempSensorReply = UseTempSensorReply

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def get_value(self) -> type___GetValueReply: ...

    @property
    def set_value(self) -> type___SetValueReply: ...

    @property
    def use_temp_sensor(self) -> type___UseTempSensorReply: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        get_value : typing___Optional[type___GetValueReply] = None,
        set_value : typing___Optional[type___SetValueReply] = None,
        use_temp_sensor : typing___Optional[type___UseTempSensorReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"get_value",b"get_value",u"header",b"header",u"pong",b"pong",u"reply",b"reply",u"set_value",b"set_value",u"use_temp_sensor",b"use_temp_sensor"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"get_value",b"get_value",u"header",b"header",u"pong",b"pong",u"reply",b"reply",u"set_value",b"set_value",u"use_temp_sensor",b"use_temp_sensor"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["pong","get_value","set_value","use_temp_sensor"]: ...
type___Reply = Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def get_value(self) -> type___GetValueRequest: ...

    @property
    def set_value(self) -> type___SetValueRequest: ...

    @property
    def use_temp_sensor(self) -> type___UseTempSensorRequest: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        get_value : typing___Optional[type___GetValueRequest] = None,
        set_value : typing___Optional[type___SetValueRequest] = None,
        use_temp_sensor : typing___Optional[type___UseTempSensorRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"get_value",b"get_value",u"header",b"header",u"ping",b"ping",u"request",b"request",u"set_value",b"set_value",u"use_temp_sensor",b"use_temp_sensor"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"get_value",b"get_value",u"header",b"header",u"ping",b"ping",u"request",b"request",u"set_value",b"set_value",u"use_temp_sensor",b"use_temp_sensor"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","get_value","set_value","use_temp_sensor"]: ...
type___Request = Request

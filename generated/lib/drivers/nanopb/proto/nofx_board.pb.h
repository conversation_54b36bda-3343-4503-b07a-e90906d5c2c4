/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_NOFX_BOARD_NOFX_BOARD_PB_H_INCLUDED
#define PB_NOFX_BOARD_NOFX_BOARD_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/park_brake.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"
#include "generated/lib/drivers/nanopb/proto/rotary_encoder.pb.h"
#include "generated/lib/drivers/nanopb/proto/version.pb.h"
#include "generated/lib/drivers/nanopb/proto/sensors.pb.h"
#include "generated/lib/drivers/nanopb/proto/drive_solenoids.pb.h"
#include "generated/lib/drivers/nanopb/proto/time.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _nofx_board_Reply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        rotary_encoder_Reply rotary_encoder;
        park_brake_Reply park_brake;
        park_brake_Query_Reply park_brake_query;
        version_Version_Reply version;
        sensors_Reply sensors;
        time_Reply time;
        drive_solenoids_Reply drive_solenoids;
    } reply;
} nofx_board_Reply;

typedef struct _nofx_board_Request {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        rotary_encoder_Request rotary_encoder;
        park_brake_Request park_brake;
        park_brake_Query_Request park_brake_query;
        version_Version_Request version;
        version_Reset_Request reset;
        sensors_Request sensors;
        time_Request time;
        drive_solenoids_Request drive_solenoids;
    } request;
} nofx_board_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define nofx_board_Reply_init_default            {false, request_RequestHeader_init_default, 0, {diagnostic_Pong_init_default}}
#define nofx_board_Request_init_default          {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define nofx_board_Reply_init_zero               {false, request_RequestHeader_init_zero, 0, {diagnostic_Pong_init_zero}}
#define nofx_board_Request_init_zero             {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define nofx_board_Reply_header_tag              1
#define nofx_board_Reply_pong_tag                2
#define nofx_board_Reply_rotary_encoder_tag      3
#define nofx_board_Reply_park_brake_tag          4
#define nofx_board_Reply_park_brake_query_tag    5
#define nofx_board_Reply_version_tag             6
#define nofx_board_Reply_sensors_tag             7
#define nofx_board_Reply_time_tag                8
#define nofx_board_Reply_drive_solenoids_tag     9
#define nofx_board_Request_header_tag            1
#define nofx_board_Request_ping_tag              2
#define nofx_board_Request_rotary_encoder_tag    3
#define nofx_board_Request_park_brake_tag        4
#define nofx_board_Request_park_brake_query_tag  5
#define nofx_board_Request_version_tag           6
#define nofx_board_Request_reset_tag             7
#define nofx_board_Request_sensors_tag           8
#define nofx_board_Request_time_tag              9
#define nofx_board_Request_drive_solenoids_tag   10

/* Struct field encoding specification for nanopb */
#define nofx_board_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,rotary_encoder,reply.rotary_encoder),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,park_brake,reply.park_brake),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,park_brake_query,reply.park_brake_query),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,version,reply.version),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,sensors,reply.sensors),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,time,reply.time),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,drive_solenoids,reply.drive_solenoids),   9)
#define nofx_board_Reply_CALLBACK NULL
#define nofx_board_Reply_DEFAULT NULL
#define nofx_board_Reply_header_MSGTYPE request_RequestHeader
#define nofx_board_Reply_reply_pong_MSGTYPE diagnostic_Pong
#define nofx_board_Reply_reply_rotary_encoder_MSGTYPE rotary_encoder_Reply
#define nofx_board_Reply_reply_park_brake_MSGTYPE park_brake_Reply
#define nofx_board_Reply_reply_park_brake_query_MSGTYPE park_brake_Query_Reply
#define nofx_board_Reply_reply_version_MSGTYPE version_Version_Reply
#define nofx_board_Reply_reply_sensors_MSGTYPE sensors_Reply
#define nofx_board_Reply_reply_time_MSGTYPE time_Reply
#define nofx_board_Reply_reply_drive_solenoids_MSGTYPE drive_solenoids_Reply

#define nofx_board_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,rotary_encoder,request.rotary_encoder),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,park_brake,request.park_brake),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,park_brake_query,request.park_brake_query),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,version,request.version),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,sensors,request.sensors),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,time,request.time),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,drive_solenoids,request.drive_solenoids),  10)
#define nofx_board_Request_CALLBACK NULL
#define nofx_board_Request_DEFAULT NULL
#define nofx_board_Request_header_MSGTYPE request_RequestHeader
#define nofx_board_Request_request_ping_MSGTYPE diagnostic_Ping
#define nofx_board_Request_request_rotary_encoder_MSGTYPE rotary_encoder_Request
#define nofx_board_Request_request_park_brake_MSGTYPE park_brake_Request
#define nofx_board_Request_request_park_brake_query_MSGTYPE park_brake_Query_Request
#define nofx_board_Request_request_version_MSGTYPE version_Version_Request
#define nofx_board_Request_request_reset_MSGTYPE version_Reset_Request
#define nofx_board_Request_request_sensors_MSGTYPE sensors_Request
#define nofx_board_Request_request_time_MSGTYPE time_Request
#define nofx_board_Request_request_drive_solenoids_MSGTYPE drive_solenoids_Request

extern const pb_msgdesc_t nofx_board_Reply_msg;
extern const pb_msgdesc_t nofx_board_Request_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define nofx_board_Reply_fields &nofx_board_Reply_msg
#define nofx_board_Request_fields &nofx_board_Request_msg

/* Maximum encoded size of messages (where known) */
#if defined(request_RequestHeader_size) && defined(diagnostic_Pong_size) && defined(rotary_encoder_Reply_size) && defined(park_brake_Reply_size) && defined(park_brake_Query_Reply_size) && defined(version_Version_Reply_size) && defined(sensors_Reply_size) && defined(time_Reply_size) && defined(drive_solenoids_Reply_size)
typedef union nofx_board_Reply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f3[(6 + rotary_encoder_Reply_size)]; char f4[(6 + park_brake_Reply_size)]; char f5[(6 + park_brake_Query_Reply_size)]; char f6[(6 + version_Version_Reply_size)]; char f7[(6 + sensors_Reply_size)]; char f8[(6 + time_Reply_size)]; char f9[(6 + drive_solenoids_Reply_size)];} nofx_board_Reply_reply_size_union;
#define nofx_board_Reply_size                    (6 + request_RequestHeader_size + sizeof(nofx_board_Reply_reply_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size) && defined(rotary_encoder_Request_size) && defined(park_brake_Request_size) && defined(park_brake_Query_Request_size) && defined(version_Version_Request_size) && defined(version_Reset_Request_size) && defined(sensors_Request_size) && defined(time_Request_size) && defined(drive_solenoids_Request_size)
typedef union nofx_board_Request_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f3[(6 + rotary_encoder_Request_size)]; char f4[(6 + park_brake_Request_size)]; char f5[(6 + park_brake_Query_Request_size)]; char f6[(6 + version_Version_Request_size)]; char f7[(6 + version_Reset_Request_size)]; char f8[(6 + sensors_Request_size)]; char f9[(6 + time_Request_size)]; char f10[(6 + drive_solenoids_Request_size)];} nofx_board_Request_request_size_union;
#define nofx_board_Request_size                  (6 + request_RequestHeader_size + sizeof(nofx_board_Request_request_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

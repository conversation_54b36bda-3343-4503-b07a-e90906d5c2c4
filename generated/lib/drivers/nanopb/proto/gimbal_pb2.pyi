"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.epos_pb2 import (
    Home_Params as generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from generated.lib.drivers.nanopb.proto.servo_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___servo_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___servo_pb2___Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Boot_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def pan_params(self) -> generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params: ...

    @property
    def tilt_params(self) -> generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params: ...

    def __init__(self,
        *,
        pan_params : typing___Optional[generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params] = None,
        tilt_params : typing___Optional[generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pan_params",b"pan_params",u"tilt_params",b"tilt_params"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan_params",b"pan_params",u"tilt_params",b"tilt_params"]) -> None: ...
type___Boot_Request = Boot_Request

class Stop_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Stop_Request = Stop_Request

class Servos_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def pan(self) -> generated___lib___drivers___nanopb___proto___servo_pb2___Request: ...

    @property
    def tilt(self) -> generated___lib___drivers___nanopb___proto___servo_pb2___Request: ...

    def __init__(self,
        *,
        pan : typing___Optional[generated___lib___drivers___nanopb___proto___servo_pb2___Request] = None,
        tilt : typing___Optional[generated___lib___drivers___nanopb___proto___servo_pb2___Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pan",b"pan",u"tilt",b"tilt"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan",b"pan",u"tilt",b"tilt"]) -> None: ...
type___Servos_Request = Servos_Request

class GetPositionAtRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_us: builtin___int = ...

    def __init__(self,
        *,
        timestamp_us : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_us",b"timestamp_us"]) -> None: ...
type___GetPositionAtRequest = GetPositionAtRequest

class Servos_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def pan(self) -> generated___lib___drivers___nanopb___proto___servo_pb2___Reply: ...

    @property
    def tilt(self) -> generated___lib___drivers___nanopb___proto___servo_pb2___Reply: ...

    def __init__(self,
        *,
        pan : typing___Optional[generated___lib___drivers___nanopb___proto___servo_pb2___Reply] = None,
        tilt : typing___Optional[generated___lib___drivers___nanopb___proto___servo_pb2___Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pan",b"pan",u"tilt",b"tilt"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan",b"pan",u"tilt",b"tilt"]) -> None: ...
type___Servos_Reply = Servos_Reply

class PositionAt(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pan: builtin___int = ...
    tilt: builtin___int = ...
    timestamp_us: builtin___int = ...
    valid: builtin___bool = ...

    def __init__(self,
        *,
        pan : typing___Optional[builtin___int] = None,
        tilt : typing___Optional[builtin___int] = None,
        timestamp_us : typing___Optional[builtin___int] = None,
        valid : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan",b"pan",u"tilt",b"tilt",u"timestamp_us",b"timestamp_us",u"valid",b"valid"]) -> None: ...
type___PositionAt = PositionAt

class GetPositionAtReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def requested(self) -> type___PositionAt: ...

    @property
    def current(self) -> type___PositionAt: ...

    def __init__(self,
        *,
        requested : typing___Optional[type___PositionAt] = None,
        current : typing___Optional[type___PositionAt] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"current",b"current",u"requested",b"requested"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current",b"current",u"requested",b"requested"]) -> None: ...
type___GetPositionAtReply = GetPositionAtReply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def boot(self) -> type___Boot_Request: ...

    @property
    def stop(self) -> type___Stop_Request: ...

    @property
    def servos(self) -> type___Servos_Request: ...

    @property
    def position(self) -> type___GetPositionAtRequest: ...

    def __init__(self,
        *,
        boot : typing___Optional[type___Boot_Request] = None,
        stop : typing___Optional[type___Stop_Request] = None,
        servos : typing___Optional[type___Servos_Request] = None,
        position : typing___Optional[type___GetPositionAtRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"boot",b"boot",u"position",b"position",u"request",b"request",u"servos",b"servos",u"stop",b"stop"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"boot",b"boot",u"position",b"position",u"request",b"request",u"servos",b"servos",u"stop",b"stop"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["boot","stop","servos","position"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def servos(self) -> type___Servos_Reply: ...

    @property
    def position(self) -> type___GetPositionAtReply: ...

    def __init__(self,
        *,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        servos : typing___Optional[type___Servos_Reply] = None,
        position : typing___Optional[type___GetPositionAtReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"position",b"position",u"reply",b"reply",u"servos",b"servos"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"position",b"position",u"reply",b"reply",u"servos",b"servos"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","servos","position"]: ...
type___Reply = Reply

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/cruise.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import diagnostic_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/cruise.proto',
  package='cruise',
  syntax='proto3',
  serialized_options=b'Z\rnanopb/cruise',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n/generated/lib/drivers/nanopb/proto/cruise.proto\x12\x06\x63ruise\x1a\x33generated/lib/drivers/nanopb/proto/diagnostic.proto\"\"\n\x10Throttle_Request\x12\x0e\n\x06\x63hange\x18\x01 \x01(\x05\"!\n\x0eThrottle_Reply\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x10\n\x0eStatus_Request\"\x97\x01\n\x0cStatus_Reply\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x13\n\x0bunavailable\x18\x02 \x01(\x08\x12\x13\n\x0bspeed_ticks\x18\x03 \x01(\x05\x12\x1c\n\x14speed_lever_position\x18\x04 \x01(\x05\x12\x16\n\x0espeed_lever_s1\x18\x05 \x01(\x08\x12\x16\n\x0espeed_lever_s2\x18\x06 \x01(\x08\"!\n\x0e\x45nable_Request\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"\x1f\n\x0c\x45nable_Reply\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"\x1c\n\x0cGear_Request\x12\x0c\n\x04gear\x18\x01 \x01(\x05\"\x1d\n\nGear_Reply\x12\x0f\n\x07success\x18\x01 \x01(\x08\">\n\x0fVariant_Request\x12+\n\x07variant\x18\x01 \x01(\x0e\x32\x1a.cruise.TractorVariantType\"<\n\rVariant_Reply\x12+\n\x07variant\x18\x01 \x01(\x0e\x32\x1a.cruise.TractorVariantType\"\x8a\x02\n\x07Request\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12,\n\x08throttle\x18\x03 \x01(\x0b\x32\x18.cruise.Throttle_RequestH\x00\x12(\n\x06status\x18\x04 \x01(\x0b\x32\x16.cruise.Status_RequestH\x00\x12(\n\x06\x65nable\x18\x05 \x01(\x0b\x32\x16.cruise.Enable_RequestH\x00\x12$\n\x04gear\x18\x06 \x01(\x0b\x32\x14.cruise.Gear_RequestH\x00\x12*\n\x07variant\x18\x07 \x01(\x0b\x32\x17.cruise.Variant_RequestH\x00\x42\t\n\x07request\"\xfc\x01\n\x05Reply\x12 \n\x04pong\x18\x02 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12*\n\x08throttle\x18\x03 \x01(\x0b\x32\x16.cruise.Throttle_ReplyH\x00\x12&\n\x06status\x18\x04 \x01(\x0b\x32\x14.cruise.Status_ReplyH\x00\x12&\n\x06\x65nable\x18\x05 \x01(\x0b\x32\x14.cruise.Enable_ReplyH\x00\x12\"\n\x04gear\x18\x06 \x01(\x0b\x32\x12.cruise.Gear_ReplyH\x00\x12(\n\x07variant\x18\x07 \x01(\x0b\x32\x15.cruise.Variant_ReplyH\x00\x42\x07\n\x05reply*r\n\x12TractorVariantType\x12\x0e\n\nTV_UNKNOWN\x10\x00\x12\r\n\tTV_JD_6LH\x10\x01\x12\x0e\n\nTV_JD_6LHM\x10\x02\x12\x0e\n\nTV_JD_6PRO\x10\x03\x12\r\n\tTV_JD_7RH\x10\x04\x12\x0e\n\nTV_JD_8PRO\x10\x05\x42\x0fZ\rnanopb/cruiseb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2.DESCRIPTOR,])

_TRACTORVARIANTTYPE = _descriptor.EnumDescriptor(
  name='TractorVariantType',
  full_name='cruise.TractorVariantType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TV_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_6LH', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_6LHM', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_6PRO', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_7RH', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_8PRO', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1134,
  serialized_end=1248,
)
_sym_db.RegisterEnumDescriptor(_TRACTORVARIANTTYPE)

TractorVariantType = enum_type_wrapper.EnumTypeWrapper(_TRACTORVARIANTTYPE)
TV_UNKNOWN = 0
TV_JD_6LH = 1
TV_JD_6LHM = 2
TV_JD_6PRO = 3
TV_JD_7RH = 4
TV_JD_8PRO = 5



_THROTTLE_REQUEST = _descriptor.Descriptor(
  name='Throttle_Request',
  full_name='cruise.Throttle_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='change', full_name='cruise.Throttle_Request.change', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=112,
  serialized_end=146,
)


_THROTTLE_REPLY = _descriptor.Descriptor(
  name='Throttle_Reply',
  full_name='cruise.Throttle_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='cruise.Throttle_Reply.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=148,
  serialized_end=181,
)


_STATUS_REQUEST = _descriptor.Descriptor(
  name='Status_Request',
  full_name='cruise.Status_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=183,
  serialized_end=199,
)


_STATUS_REPLY = _descriptor.Descriptor(
  name='Status_Reply',
  full_name='cruise.Status_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='cruise.Status_Reply.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unavailable', full_name='cruise.Status_Reply.unavailable', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_ticks', full_name='cruise.Status_Reply.speed_ticks', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_lever_position', full_name='cruise.Status_Reply.speed_lever_position', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_lever_s1', full_name='cruise.Status_Reply.speed_lever_s1', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_lever_s2', full_name='cruise.Status_Reply.speed_lever_s2', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=202,
  serialized_end=353,
)


_ENABLE_REQUEST = _descriptor.Descriptor(
  name='Enable_Request',
  full_name='cruise.Enable_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='cruise.Enable_Request.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=355,
  serialized_end=388,
)


_ENABLE_REPLY = _descriptor.Descriptor(
  name='Enable_Reply',
  full_name='cruise.Enable_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='cruise.Enable_Reply.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=390,
  serialized_end=421,
)


_GEAR_REQUEST = _descriptor.Descriptor(
  name='Gear_Request',
  full_name='cruise.Gear_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gear', full_name='cruise.Gear_Request.gear', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=423,
  serialized_end=451,
)


_GEAR_REPLY = _descriptor.Descriptor(
  name='Gear_Reply',
  full_name='cruise.Gear_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='cruise.Gear_Reply.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=453,
  serialized_end=482,
)


_VARIANT_REQUEST = _descriptor.Descriptor(
  name='Variant_Request',
  full_name='cruise.Variant_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='variant', full_name='cruise.Variant_Request.variant', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=484,
  serialized_end=546,
)


_VARIANT_REPLY = _descriptor.Descriptor(
  name='Variant_Reply',
  full_name='cruise.Variant_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='variant', full_name='cruise.Variant_Reply.variant', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=548,
  serialized_end=608,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='cruise.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ping', full_name='cruise.Request.ping', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='throttle', full_name='cruise.Request.throttle', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='cruise.Request.status', index=2,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enable', full_name='cruise.Request.enable', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gear', full_name='cruise.Request.gear', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='variant', full_name='cruise.Request.variant', index=5,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='cruise.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=611,
  serialized_end=877,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='cruise.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pong', full_name='cruise.Reply.pong', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='throttle', full_name='cruise.Reply.throttle', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='cruise.Reply.status', index=2,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enable', full_name='cruise.Reply.enable', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gear', full_name='cruise.Reply.gear', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='variant', full_name='cruise.Reply.variant', index=5,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='cruise.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=880,
  serialized_end=1132,
)

_VARIANT_REQUEST.fields_by_name['variant'].enum_type = _TRACTORVARIANTTYPE
_VARIANT_REPLY.fields_by_name['variant'].enum_type = _TRACTORVARIANTTYPE
_REQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_REQUEST.fields_by_name['throttle'].message_type = _THROTTLE_REQUEST
_REQUEST.fields_by_name['status'].message_type = _STATUS_REQUEST
_REQUEST.fields_by_name['enable'].message_type = _ENABLE_REQUEST
_REQUEST.fields_by_name['gear'].message_type = _GEAR_REQUEST
_REQUEST.fields_by_name['variant'].message_type = _VARIANT_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ping'])
_REQUEST.fields_by_name['ping'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['throttle'])
_REQUEST.fields_by_name['throttle'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['status'])
_REQUEST.fields_by_name['status'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['enable'])
_REQUEST.fields_by_name['enable'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['gear'])
_REQUEST.fields_by_name['gear'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['variant'])
_REQUEST.fields_by_name['variant'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_REPLY.fields_by_name['throttle'].message_type = _THROTTLE_REPLY
_REPLY.fields_by_name['status'].message_type = _STATUS_REPLY
_REPLY.fields_by_name['enable'].message_type = _ENABLE_REPLY
_REPLY.fields_by_name['gear'].message_type = _GEAR_REPLY
_REPLY.fields_by_name['variant'].message_type = _VARIANT_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pong'])
_REPLY.fields_by_name['pong'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['throttle'])
_REPLY.fields_by_name['throttle'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['status'])
_REPLY.fields_by_name['status'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['enable'])
_REPLY.fields_by_name['enable'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['gear'])
_REPLY.fields_by_name['gear'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['variant'])
_REPLY.fields_by_name['variant'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Throttle_Request'] = _THROTTLE_REQUEST
DESCRIPTOR.message_types_by_name['Throttle_Reply'] = _THROTTLE_REPLY
DESCRIPTOR.message_types_by_name['Status_Request'] = _STATUS_REQUEST
DESCRIPTOR.message_types_by_name['Status_Reply'] = _STATUS_REPLY
DESCRIPTOR.message_types_by_name['Enable_Request'] = _ENABLE_REQUEST
DESCRIPTOR.message_types_by_name['Enable_Reply'] = _ENABLE_REPLY
DESCRIPTOR.message_types_by_name['Gear_Request'] = _GEAR_REQUEST
DESCRIPTOR.message_types_by_name['Gear_Reply'] = _GEAR_REPLY
DESCRIPTOR.message_types_by_name['Variant_Request'] = _VARIANT_REQUEST
DESCRIPTOR.message_types_by_name['Variant_Reply'] = _VARIANT_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.enum_types_by_name['TractorVariantType'] = _TRACTORVARIANTTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Throttle_Request = _reflection.GeneratedProtocolMessageType('Throttle_Request', (_message.Message,), {
  'DESCRIPTOR' : _THROTTLE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Throttle_Request)
  })
_sym_db.RegisterMessage(Throttle_Request)

Throttle_Reply = _reflection.GeneratedProtocolMessageType('Throttle_Reply', (_message.Message,), {
  'DESCRIPTOR' : _THROTTLE_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Throttle_Reply)
  })
_sym_db.RegisterMessage(Throttle_Reply)

Status_Request = _reflection.GeneratedProtocolMessageType('Status_Request', (_message.Message,), {
  'DESCRIPTOR' : _STATUS_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Status_Request)
  })
_sym_db.RegisterMessage(Status_Request)

Status_Reply = _reflection.GeneratedProtocolMessageType('Status_Reply', (_message.Message,), {
  'DESCRIPTOR' : _STATUS_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Status_Reply)
  })
_sym_db.RegisterMessage(Status_Reply)

Enable_Request = _reflection.GeneratedProtocolMessageType('Enable_Request', (_message.Message,), {
  'DESCRIPTOR' : _ENABLE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Enable_Request)
  })
_sym_db.RegisterMessage(Enable_Request)

Enable_Reply = _reflection.GeneratedProtocolMessageType('Enable_Reply', (_message.Message,), {
  'DESCRIPTOR' : _ENABLE_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Enable_Reply)
  })
_sym_db.RegisterMessage(Enable_Reply)

Gear_Request = _reflection.GeneratedProtocolMessageType('Gear_Request', (_message.Message,), {
  'DESCRIPTOR' : _GEAR_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Gear_Request)
  })
_sym_db.RegisterMessage(Gear_Request)

Gear_Reply = _reflection.GeneratedProtocolMessageType('Gear_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GEAR_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Gear_Reply)
  })
_sym_db.RegisterMessage(Gear_Reply)

Variant_Request = _reflection.GeneratedProtocolMessageType('Variant_Request', (_message.Message,), {
  'DESCRIPTOR' : _VARIANT_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Variant_Request)
  })
_sym_db.RegisterMessage(Variant_Request)

Variant_Reply = _reflection.GeneratedProtocolMessageType('Variant_Reply', (_message.Message,), {
  'DESCRIPTOR' : _VARIANT_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Variant_Reply)
  })
_sym_db.RegisterMessage(Variant_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.cruise_pb2'
  # @@protoc_insertion_point(class_scope:cruise.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

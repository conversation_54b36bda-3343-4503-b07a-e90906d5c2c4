/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "lens.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(lens_Set_Request, lens_Set_Request, AUTO)


PB_BIND(lens_Get_Request, lens_Get_Request, AUTO)


PB_BIND(lens_Get_Reply, lens_Get_Reply, AUTO)


PB_BIND(lens_Request, lens_Request, AUTO)


PB_BIND(lens_Reply, lens_Reply, 2)




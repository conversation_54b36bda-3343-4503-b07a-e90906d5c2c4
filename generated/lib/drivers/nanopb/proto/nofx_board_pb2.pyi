"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.diagnostic_pb2 import (
    Ping as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping,
    Pong as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong,
)

from generated.lib.drivers.nanopb.proto.drive_solenoids_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___drive_solenoids_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___drive_solenoids_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.park_brake_pb2 import (
    Query_Reply as generated___lib___drivers___nanopb___proto___park_brake_pb2___Query_Reply,
    Query_Request as generated___lib___drivers___nanopb___proto___park_brake_pb2___Query_Request,
    Reply as generated___lib___drivers___nanopb___proto___park_brake_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___park_brake_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.request_pb2 import (
    RequestHeader as generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader,
)

from generated.lib.drivers.nanopb.proto.rotary_encoder_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___rotary_encoder_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___rotary_encoder_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.sensors_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___sensors_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___sensors_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.time_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___time_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___time_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.version_pb2 import (
    Reset_Request as generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request,
    Version_Reply as generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply,
    Version_Request as generated___lib___drivers___nanopb___proto___version_pb2___Version_Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def rotary_encoder(self) -> generated___lib___drivers___nanopb___proto___rotary_encoder_pb2___Reply: ...

    @property
    def park_brake(self) -> generated___lib___drivers___nanopb___proto___park_brake_pb2___Reply: ...

    @property
    def park_brake_query(self) -> generated___lib___drivers___nanopb___proto___park_brake_pb2___Query_Reply: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply: ...

    @property
    def sensors(self) -> generated___lib___drivers___nanopb___proto___sensors_pb2___Reply: ...

    @property
    def time(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Reply: ...

    @property
    def drive_solenoids(self) -> generated___lib___drivers___nanopb___proto___drive_solenoids_pb2___Reply: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        rotary_encoder : typing___Optional[generated___lib___drivers___nanopb___proto___rotary_encoder_pb2___Reply] = None,
        park_brake : typing___Optional[generated___lib___drivers___nanopb___proto___park_brake_pb2___Reply] = None,
        park_brake_query : typing___Optional[generated___lib___drivers___nanopb___proto___park_brake_pb2___Query_Reply] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply] = None,
        sensors : typing___Optional[generated___lib___drivers___nanopb___proto___sensors_pb2___Reply] = None,
        time : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Reply] = None,
        drive_solenoids : typing___Optional[generated___lib___drivers___nanopb___proto___drive_solenoids_pb2___Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"drive_solenoids",b"drive_solenoids",u"header",b"header",u"park_brake",b"park_brake",u"park_brake_query",b"park_brake_query",u"pong",b"pong",u"reply",b"reply",u"rotary_encoder",b"rotary_encoder",u"sensors",b"sensors",u"time",b"time",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"drive_solenoids",b"drive_solenoids",u"header",b"header",u"park_brake",b"park_brake",u"park_brake_query",b"park_brake_query",u"pong",b"pong",u"reply",b"reply",u"rotary_encoder",b"rotary_encoder",u"sensors",b"sensors",u"time",b"time",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["pong","rotary_encoder","park_brake","park_brake_query","version","sensors","time","drive_solenoids"]: ...
type___Reply = Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def rotary_encoder(self) -> generated___lib___drivers___nanopb___proto___rotary_encoder_pb2___Request: ...

    @property
    def park_brake(self) -> generated___lib___drivers___nanopb___proto___park_brake_pb2___Request: ...

    @property
    def park_brake_query(self) -> generated___lib___drivers___nanopb___proto___park_brake_pb2___Query_Request: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Request: ...

    @property
    def reset(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request: ...

    @property
    def sensors(self) -> generated___lib___drivers___nanopb___proto___sensors_pb2___Request: ...

    @property
    def time(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Request: ...

    @property
    def drive_solenoids(self) -> generated___lib___drivers___nanopb___proto___drive_solenoids_pb2___Request: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        rotary_encoder : typing___Optional[generated___lib___drivers___nanopb___proto___rotary_encoder_pb2___Request] = None,
        park_brake : typing___Optional[generated___lib___drivers___nanopb___proto___park_brake_pb2___Request] = None,
        park_brake_query : typing___Optional[generated___lib___drivers___nanopb___proto___park_brake_pb2___Query_Request] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Request] = None,
        reset : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request] = None,
        sensors : typing___Optional[generated___lib___drivers___nanopb___proto___sensors_pb2___Request] = None,
        time : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Request] = None,
        drive_solenoids : typing___Optional[generated___lib___drivers___nanopb___proto___drive_solenoids_pb2___Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"drive_solenoids",b"drive_solenoids",u"header",b"header",u"park_brake",b"park_brake",u"park_brake_query",b"park_brake_query",u"ping",b"ping",u"request",b"request",u"reset",b"reset",u"rotary_encoder",b"rotary_encoder",u"sensors",b"sensors",u"time",b"time",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"drive_solenoids",b"drive_solenoids",u"header",b"header",u"park_brake",b"park_brake",u"park_brake_query",b"park_brake_query",u"ping",b"ping",u"request",b"request",u"reset",b"reset",u"rotary_encoder",b"rotary_encoder",u"sensors",b"sensors",u"time",b"time",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","rotary_encoder","park_brake","park_brake_query","version","reset","sensors","time","drive_solenoids"]: ...
type___Request = Request

/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "experiment_board.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(experiment_board_ExperimentItem, experiment_board_ExperimentItem, AUTO)


PB_BIND(experiment_board_ExperimentRepeated, experiment_board_ExperimentRepeated, AUTO)


PB_BIND(experiment_board_ExperimentResult, experiment_board_ExperimentResult, AUTO)


PB_BIND(experiment_board_ExperimentReply, experiment_board_ExperimentReply, 2)


PB_BIND(experiment_board_ExperimentRequest, experiment_board_ExperimentRequest, 2)




# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/rotary_encoder.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import time_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/rotary_encoder.proto',
  package='rotary_encoder',
  syntax='proto3',
  serialized_options=b'Z\025nanopb/rotary_encoder',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n7generated/lib/drivers/nanopb/proto/rotary_encoder.proto\x12\x0erotary_encoder\x1a-generated/lib/drivers/nanopb/proto/time.proto\"\xd4\x02\n\x1cRotaryEncodersConfig_Request\x12\x42\n\x07\x46L_type\x18\x01 \x01(\x0e\x32\x31.rotary_encoder.RotaryEncodersConfig_Request.Type\x12\x42\n\x07\x46R_type\x18\x02 \x01(\x0e\x32\x31.rotary_encoder.RotaryEncodersConfig_Request.Type\x12\x42\n\x07\x42L_type\x18\x03 \x01(\x0e\x32\x31.rotary_encoder.RotaryEncodersConfig_Request.Type\x12\x42\n\x07\x42R_type\x18\x04 \x01(\x0e\x32\x31.rotary_encoder.RotaryEncodersConfig_Request.Type\"$\n\x04Type\x12\x08\n\x04TICK\x10\x00\x12\x08\n\x04QUAD\x10\x01\x12\x08\n\x04NONE\x10\x02\"\x1c\n\x1aRotaryEncodersConfig_Reply\"\x17\n\x15RotaryEncoder_Request\"\xa1\x01\n\x13RotaryEncoder_Reply\x12\x18\n\x10\x66ront_left_ticks\x18\x01 \x01(\x03\x12\x19\n\x11\x66ront_right_ticks\x18\x02 \x01(\x03\x12\x17\n\x0f\x62\x61\x63k_left_ticks\x18\x03 \x01(\x03\x12\x18\n\x10\x62\x61\x63k_right_ticks\x18\x04 \x01(\x03\x12\"\n\ttimestamp\x18\x05 \x01(\x0b\x32\x0f.time.Timestamp\"^\n\x1dRotaryEncoderSnapshot_Request\x12\x1e\n\x05\x66irst\x18\x01 \x01(\x0b\x32\x0f.time.Timestamp\x12\x1d\n\x04last\x18\x02 \x01(\x0b\x32\x0f.time.Timestamp\"\xc5\x02\n\x1bRotaryEncoderSnapshot_Reply\x12\x39\n\x0c\x66irst_before\x18\x01 \x01(\x0b\x32#.rotary_encoder.RotaryEncoder_Reply\x12\x38\n\x0b\x66irst_after\x18\x02 \x01(\x0b\x32#.rotary_encoder.RotaryEncoder_Reply\x12\x38\n\x0blast_before\x18\x03 \x01(\x0b\x32#.rotary_encoder.RotaryEncoder_Reply\x12\x37\n\nlast_after\x18\x04 \x01(\x0b\x32#.rotary_encoder.RotaryEncoder_Reply\x12>\n\x07request\x18\x05 \x01(\x0b\x32-.rotary_encoder.RotaryEncoderSnapshot_Request\"$\n\"RotaryEncoderHistoryVerify_Request\"\x86\x02\n RotaryEncoderHistoryVerify_Reply\x12\x16\n\x0enum_time_warps\x18\x01 \x01(\r\x12\x18\n\x10num_ooo_elements\x18\x02 \x01(\r\x12\x19\n\x11max_usec_distance\x18\x03 \x01(\r\x12\x18\n\x10num_epoch_resets\x18\x04 \x01(\r\x12\x16\n\x0enum_plus1_usec\x18\x05 \x01(\r\x12\x19\n\x11num_missed_signal\x18\x06 \x01(\r\x12\x19\n\x11num_reject_signal\x18\x07 \x01(\r\x12\x16\n\x0enum_short_time\x18\x08 \x01(\r\x12\x15\n\rnum_long_time\x18\t \x01(\r\"\xa5\x02\n\x07Request\x12\x37\n\x06rotary\x18\x01 \x01(\x0b\x32%.rotary_encoder.RotaryEncoder_RequestH\x00\x12>\n\x06\x63onfig\x18\x02 \x01(\x0b\x32,.rotary_encoder.RotaryEncodersConfig_RequestH\x00\x12H\n\x0frotary_snapshot\x18\x03 \x01(\x0b\x32-.rotary_encoder.RotaryEncoderSnapshot_RequestH\x00\x12L\n\x0ehistory_verify\x18\x04 \x01(\x0b\x32\x32.rotary_encoder.RotaryEncoderHistoryVerify_RequestH\x00\x42\t\n\x07request\"\x99\x02\n\x05Reply\x12\x35\n\x06rotary\x18\x01 \x01(\x0b\x32#.rotary_encoder.RotaryEncoder_ReplyH\x00\x12<\n\x06\x63onfig\x18\x02 \x01(\x0b\x32*.rotary_encoder.RotaryEncodersConfig_ReplyH\x00\x12\x46\n\x0frotary_snapshot\x18\x03 \x01(\x0b\x32+.rotary_encoder.RotaryEncoderSnapshot_ReplyH\x00\x12J\n\x0ehistory_verify\x18\x04 \x01(\x0b\x32\x30.rotary_encoder.RotaryEncoderHistoryVerify_ReplyH\x00\x42\x07\n\x05replyB\x17Z\x15nanopb/rotary_encoderb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2.DESCRIPTOR,])



_ROTARYENCODERSCONFIG_REQUEST_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='rotary_encoder.RotaryEncodersConfig_Request.Type',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TICK', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='QUAD', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NONE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=427,
  serialized_end=463,
)
_sym_db.RegisterEnumDescriptor(_ROTARYENCODERSCONFIG_REQUEST_TYPE)


_ROTARYENCODERSCONFIG_REQUEST = _descriptor.Descriptor(
  name='RotaryEncodersConfig_Request',
  full_name='rotary_encoder.RotaryEncodersConfig_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='FL_type', full_name='rotary_encoder.RotaryEncodersConfig_Request.FL_type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='FR_type', full_name='rotary_encoder.RotaryEncodersConfig_Request.FR_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='BL_type', full_name='rotary_encoder.RotaryEncodersConfig_Request.BL_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='BR_type', full_name='rotary_encoder.RotaryEncodersConfig_Request.BR_type', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ROTARYENCODERSCONFIG_REQUEST_TYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=123,
  serialized_end=463,
)


_ROTARYENCODERSCONFIG_REPLY = _descriptor.Descriptor(
  name='RotaryEncodersConfig_Reply',
  full_name='rotary_encoder.RotaryEncodersConfig_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=465,
  serialized_end=493,
)


_ROTARYENCODER_REQUEST = _descriptor.Descriptor(
  name='RotaryEncoder_Request',
  full_name='rotary_encoder.RotaryEncoder_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=495,
  serialized_end=518,
)


_ROTARYENCODER_REPLY = _descriptor.Descriptor(
  name='RotaryEncoder_Reply',
  full_name='rotary_encoder.RotaryEncoder_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='front_left_ticks', full_name='rotary_encoder.RotaryEncoder_Reply.front_left_ticks', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_right_ticks', full_name='rotary_encoder.RotaryEncoder_Reply.front_right_ticks', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='back_left_ticks', full_name='rotary_encoder.RotaryEncoder_Reply.back_left_ticks', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='back_right_ticks', full_name='rotary_encoder.RotaryEncoder_Reply.back_right_ticks', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='rotary_encoder.RotaryEncoder_Reply.timestamp', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=521,
  serialized_end=682,
)


_ROTARYENCODERSNAPSHOT_REQUEST = _descriptor.Descriptor(
  name='RotaryEncoderSnapshot_Request',
  full_name='rotary_encoder.RotaryEncoderSnapshot_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='first', full_name='rotary_encoder.RotaryEncoderSnapshot_Request.first', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last', full_name='rotary_encoder.RotaryEncoderSnapshot_Request.last', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=684,
  serialized_end=778,
)


_ROTARYENCODERSNAPSHOT_REPLY = _descriptor.Descriptor(
  name='RotaryEncoderSnapshot_Reply',
  full_name='rotary_encoder.RotaryEncoderSnapshot_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='first_before', full_name='rotary_encoder.RotaryEncoderSnapshot_Reply.first_before', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='first_after', full_name='rotary_encoder.RotaryEncoderSnapshot_Reply.first_after', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_before', full_name='rotary_encoder.RotaryEncoderSnapshot_Reply.last_before', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_after', full_name='rotary_encoder.RotaryEncoderSnapshot_Reply.last_after', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='request', full_name='rotary_encoder.RotaryEncoderSnapshot_Reply.request', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=781,
  serialized_end=1106,
)


_ROTARYENCODERHISTORYVERIFY_REQUEST = _descriptor.Descriptor(
  name='RotaryEncoderHistoryVerify_Request',
  full_name='rotary_encoder.RotaryEncoderHistoryVerify_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1108,
  serialized_end=1144,
)


_ROTARYENCODERHISTORYVERIFY_REPLY = _descriptor.Descriptor(
  name='RotaryEncoderHistoryVerify_Reply',
  full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='num_time_warps', full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply.num_time_warps', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_ooo_elements', full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply.num_ooo_elements', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_usec_distance', full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply.max_usec_distance', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_epoch_resets', full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply.num_epoch_resets', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_plus1_usec', full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply.num_plus1_usec', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_missed_signal', full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply.num_missed_signal', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_reject_signal', full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply.num_reject_signal', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_short_time', full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply.num_short_time', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_long_time', full_name='rotary_encoder.RotaryEncoderHistoryVerify_Reply.num_long_time', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1147,
  serialized_end=1409,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='rotary_encoder.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='rotary', full_name='rotary_encoder.Request.rotary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='rotary_encoder.Request.config', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rotary_snapshot', full_name='rotary_encoder.Request.rotary_snapshot', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='history_verify', full_name='rotary_encoder.Request.history_verify', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='rotary_encoder.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1412,
  serialized_end=1705,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='rotary_encoder.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='rotary', full_name='rotary_encoder.Reply.rotary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='rotary_encoder.Reply.config', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rotary_snapshot', full_name='rotary_encoder.Reply.rotary_snapshot', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='history_verify', full_name='rotary_encoder.Reply.history_verify', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='rotary_encoder.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1708,
  serialized_end=1989,
)

_ROTARYENCODERSCONFIG_REQUEST.fields_by_name['FL_type'].enum_type = _ROTARYENCODERSCONFIG_REQUEST_TYPE
_ROTARYENCODERSCONFIG_REQUEST.fields_by_name['FR_type'].enum_type = _ROTARYENCODERSCONFIG_REQUEST_TYPE
_ROTARYENCODERSCONFIG_REQUEST.fields_by_name['BL_type'].enum_type = _ROTARYENCODERSCONFIG_REQUEST_TYPE
_ROTARYENCODERSCONFIG_REQUEST.fields_by_name['BR_type'].enum_type = _ROTARYENCODERSCONFIG_REQUEST_TYPE
_ROTARYENCODERSCONFIG_REQUEST_TYPE.containing_type = _ROTARYENCODERSCONFIG_REQUEST
_ROTARYENCODER_REPLY.fields_by_name['timestamp'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_ROTARYENCODERSNAPSHOT_REQUEST.fields_by_name['first'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_ROTARYENCODERSNAPSHOT_REQUEST.fields_by_name['last'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_ROTARYENCODERSNAPSHOT_REPLY.fields_by_name['first_before'].message_type = _ROTARYENCODER_REPLY
_ROTARYENCODERSNAPSHOT_REPLY.fields_by_name['first_after'].message_type = _ROTARYENCODER_REPLY
_ROTARYENCODERSNAPSHOT_REPLY.fields_by_name['last_before'].message_type = _ROTARYENCODER_REPLY
_ROTARYENCODERSNAPSHOT_REPLY.fields_by_name['last_after'].message_type = _ROTARYENCODER_REPLY
_ROTARYENCODERSNAPSHOT_REPLY.fields_by_name['request'].message_type = _ROTARYENCODERSNAPSHOT_REQUEST
_REQUEST.fields_by_name['rotary'].message_type = _ROTARYENCODER_REQUEST
_REQUEST.fields_by_name['config'].message_type = _ROTARYENCODERSCONFIG_REQUEST
_REQUEST.fields_by_name['rotary_snapshot'].message_type = _ROTARYENCODERSNAPSHOT_REQUEST
_REQUEST.fields_by_name['history_verify'].message_type = _ROTARYENCODERHISTORYVERIFY_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['rotary'])
_REQUEST.fields_by_name['rotary'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['config'])
_REQUEST.fields_by_name['config'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['rotary_snapshot'])
_REQUEST.fields_by_name['rotary_snapshot'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['history_verify'])
_REQUEST.fields_by_name['history_verify'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['rotary'].message_type = _ROTARYENCODER_REPLY
_REPLY.fields_by_name['config'].message_type = _ROTARYENCODERSCONFIG_REPLY
_REPLY.fields_by_name['rotary_snapshot'].message_type = _ROTARYENCODERSNAPSHOT_REPLY
_REPLY.fields_by_name['history_verify'].message_type = _ROTARYENCODERHISTORYVERIFY_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['rotary'])
_REPLY.fields_by_name['rotary'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['config'])
_REPLY.fields_by_name['config'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['rotary_snapshot'])
_REPLY.fields_by_name['rotary_snapshot'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['history_verify'])
_REPLY.fields_by_name['history_verify'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['RotaryEncodersConfig_Request'] = _ROTARYENCODERSCONFIG_REQUEST
DESCRIPTOR.message_types_by_name['RotaryEncodersConfig_Reply'] = _ROTARYENCODERSCONFIG_REPLY
DESCRIPTOR.message_types_by_name['RotaryEncoder_Request'] = _ROTARYENCODER_REQUEST
DESCRIPTOR.message_types_by_name['RotaryEncoder_Reply'] = _ROTARYENCODER_REPLY
DESCRIPTOR.message_types_by_name['RotaryEncoderSnapshot_Request'] = _ROTARYENCODERSNAPSHOT_REQUEST
DESCRIPTOR.message_types_by_name['RotaryEncoderSnapshot_Reply'] = _ROTARYENCODERSNAPSHOT_REPLY
DESCRIPTOR.message_types_by_name['RotaryEncoderHistoryVerify_Request'] = _ROTARYENCODERHISTORYVERIFY_REQUEST
DESCRIPTOR.message_types_by_name['RotaryEncoderHistoryVerify_Reply'] = _ROTARYENCODERHISTORYVERIFY_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RotaryEncodersConfig_Request = _reflection.GeneratedProtocolMessageType('RotaryEncodersConfig_Request', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODERSCONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.RotaryEncodersConfig_Request)
  })
_sym_db.RegisterMessage(RotaryEncodersConfig_Request)

RotaryEncodersConfig_Reply = _reflection.GeneratedProtocolMessageType('RotaryEncodersConfig_Reply', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODERSCONFIG_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.RotaryEncodersConfig_Reply)
  })
_sym_db.RegisterMessage(RotaryEncodersConfig_Reply)

RotaryEncoder_Request = _reflection.GeneratedProtocolMessageType('RotaryEncoder_Request', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODER_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.RotaryEncoder_Request)
  })
_sym_db.RegisterMessage(RotaryEncoder_Request)

RotaryEncoder_Reply = _reflection.GeneratedProtocolMessageType('RotaryEncoder_Reply', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODER_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.RotaryEncoder_Reply)
  })
_sym_db.RegisterMessage(RotaryEncoder_Reply)

RotaryEncoderSnapshot_Request = _reflection.GeneratedProtocolMessageType('RotaryEncoderSnapshot_Request', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODERSNAPSHOT_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.RotaryEncoderSnapshot_Request)
  })
_sym_db.RegisterMessage(RotaryEncoderSnapshot_Request)

RotaryEncoderSnapshot_Reply = _reflection.GeneratedProtocolMessageType('RotaryEncoderSnapshot_Reply', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODERSNAPSHOT_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.RotaryEncoderSnapshot_Reply)
  })
_sym_db.RegisterMessage(RotaryEncoderSnapshot_Reply)

RotaryEncoderHistoryVerify_Request = _reflection.GeneratedProtocolMessageType('RotaryEncoderHistoryVerify_Request', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODERHISTORYVERIFY_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.RotaryEncoderHistoryVerify_Request)
  })
_sym_db.RegisterMessage(RotaryEncoderHistoryVerify_Request)

RotaryEncoderHistoryVerify_Reply = _reflection.GeneratedProtocolMessageType('RotaryEncoderHistoryVerify_Reply', (_message.Message,), {
  'DESCRIPTOR' : _ROTARYENCODERHISTORYVERIFY_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.RotaryEncoderHistoryVerify_Reply)
  })
_sym_db.RegisterMessage(RotaryEncoderHistoryVerify_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.rotary_encoder_pb2'
  # @@protoc_insertion_point(class_scope:rotary_encoder.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

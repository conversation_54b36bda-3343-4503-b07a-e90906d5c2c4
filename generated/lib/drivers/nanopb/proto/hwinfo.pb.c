/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "hwinfo.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(hwinfo_BoardVersionRequest, hwinfo_BoardVersionRequest, AUTO)


PB_BIND(hwinfo_BoardIdentityRequest, hwinfo_BoardIdentityRequest, AUTO)


PB_BIND(hwinfo_Request, hwinfo_Request, AUTO)


PB_BIND(hwinfo_BoardVersionReply, hwinfo_BoardVersionReply, AUTO)


PB_BIND(hwinfo_BoardIdentityReply, hwinfo_BoardIdentityReply, AUTO)


PB_BIND(hwinfo_Reply, hwinfo_Reply, AUTO)




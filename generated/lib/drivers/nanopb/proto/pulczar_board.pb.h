/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_PULCZAR_BOARD_PULCZAR_BOARD_PB_H_INCLUDED
#define PB_PULCZAR_BOARD_PULCZAR_BOARD_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/hwinfo.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"
#include "generated/lib/drivers/nanopb/proto/version.pb.h"
#include "generated/lib/drivers/nanopb/proto/time.pb.h"
#include "generated/lib/drivers/nanopb/proto/pulczar.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _pulczar_board_Reply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        pulczar_Reply pulczar;
        version_Version_Reply version;
        time_Reply time;
        hwinfo_Reply hwinfo;
    } reply;
} pulczar_board_Reply;

typedef struct _pulczar_board_Request {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        pulczar_Request pulczar;
        version_Version_Request version;
        time_Request time;
        hwinfo_Request hwinfo;
    } request;
} pulczar_board_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define pulczar_board_Reply_init_default         {false, request_RequestHeader_init_default, 0, {diagnostic_Pong_init_default}}
#define pulczar_board_Request_init_default       {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define pulczar_board_Reply_init_zero            {false, request_RequestHeader_init_zero, 0, {diagnostic_Pong_init_zero}}
#define pulczar_board_Request_init_zero          {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define pulczar_board_Reply_header_tag           1
#define pulczar_board_Reply_pong_tag             2
#define pulczar_board_Reply_pulczar_tag          3
#define pulczar_board_Reply_version_tag          4
#define pulczar_board_Reply_time_tag             5
#define pulczar_board_Reply_hwinfo_tag           6
#define pulczar_board_Request_header_tag         1
#define pulczar_board_Request_ping_tag           2
#define pulczar_board_Request_pulczar_tag        3
#define pulczar_board_Request_version_tag        4
#define pulczar_board_Request_time_tag           5
#define pulczar_board_Request_hwinfo_tag         6

/* Struct field encoding specification for nanopb */
#define pulczar_board_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pulczar,reply.pulczar),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,version,reply.version),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,time,reply.time),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,hwinfo,reply.hwinfo),   6)
#define pulczar_board_Reply_CALLBACK NULL
#define pulczar_board_Reply_DEFAULT NULL
#define pulczar_board_Reply_header_MSGTYPE request_RequestHeader
#define pulczar_board_Reply_reply_pong_MSGTYPE diagnostic_Pong
#define pulczar_board_Reply_reply_pulczar_MSGTYPE pulczar_Reply
#define pulczar_board_Reply_reply_version_MSGTYPE version_Version_Reply
#define pulczar_board_Reply_reply_time_MSGTYPE time_Reply
#define pulczar_board_Reply_reply_hwinfo_MSGTYPE hwinfo_Reply

#define pulczar_board_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pulczar,request.pulczar),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,version,request.version),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,time,request.time),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,hwinfo,request.hwinfo),   6)
#define pulczar_board_Request_CALLBACK NULL
#define pulczar_board_Request_DEFAULT NULL
#define pulczar_board_Request_header_MSGTYPE request_RequestHeader
#define pulczar_board_Request_request_ping_MSGTYPE diagnostic_Ping
#define pulczar_board_Request_request_pulczar_MSGTYPE pulczar_Request
#define pulczar_board_Request_request_version_MSGTYPE version_Version_Request
#define pulczar_board_Request_request_time_MSGTYPE time_Request
#define pulczar_board_Request_request_hwinfo_MSGTYPE hwinfo_Request

extern const pb_msgdesc_t pulczar_board_Reply_msg;
extern const pb_msgdesc_t pulczar_board_Request_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define pulczar_board_Reply_fields &pulczar_board_Reply_msg
#define pulczar_board_Request_fields &pulczar_board_Request_msg

/* Maximum encoded size of messages (where known) */
#if defined(request_RequestHeader_size) && defined(diagnostic_Pong_size) && defined(pulczar_Reply_size) && defined(version_Version_Reply_size) && defined(time_Reply_size) && defined(hwinfo_Reply_size)
typedef union pulczar_board_Reply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f3[(6 + pulczar_Reply_size)]; char f4[(6 + version_Version_Reply_size)]; char f5[(6 + time_Reply_size)]; char f6[(6 + hwinfo_Reply_size)];} pulczar_board_Reply_reply_size_union;
#define pulczar_board_Reply_size                 (6 + request_RequestHeader_size + sizeof(pulczar_board_Reply_reply_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size) && defined(pulczar_Request_size) && defined(version_Version_Request_size) && defined(time_Request_size) && defined(hwinfo_Request_size)
typedef union pulczar_board_Request_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f3[(6 + pulczar_Request_size)]; char f4[(6 + version_Version_Request_size)]; char f5[(6 + time_Request_size)]; char f6[(6 + hwinfo_Request_size)];} pulczar_board_Request_request_size_union;
#define pulczar_board_Request_size               (6 + request_RequestHeader_size + sizeof(pulczar_board_Request_request_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

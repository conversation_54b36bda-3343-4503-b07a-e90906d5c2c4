/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_DAWG_DAWG_PB_H_INCLUDED
#define PB_DAWG_DAWG_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _dawg_Get_State_Request {
    char dummy_field;
} dawg_Get_State_Request;

typedef struct _dawg_Arm_Request {
    bool armed;
} dawg_Arm_Request;

typedef struct _dawg_Config_Request {
    uint32_t timeout_ms;
} dawg_Config_Request;

typedef struct _dawg_Pet_Request {
    bool firing;
} dawg_Pet_Request;

typedef struct _dawg_State_Reply {
    bool armed;
    bool petted;
} dawg_State_Reply;

typedef struct _dawg_Reply {
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        dawg_State_Reply state;
    } reply;
} dawg_Reply;

typedef struct _dawg_Request {
    pb_size_t which_request;
    union {
        dawg_Config_Request config;
        dawg_Arm_Request arm;
        dawg_Pet_Request pet;
        dawg_Get_State_Request state;
    } request;
} dawg_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define dawg_Config_Request_init_default         {0}
#define dawg_Arm_Request_init_default            {0}
#define dawg_Pet_Request_init_default            {0}
#define dawg_Get_State_Request_init_default      {0}
#define dawg_State_Reply_init_default            {0, 0}
#define dawg_Request_init_default                {0, {dawg_Config_Request_init_default}}
#define dawg_Reply_init_default                  {0, {error_Error_init_default}}
#define dawg_Config_Request_init_zero            {0}
#define dawg_Arm_Request_init_zero               {0}
#define dawg_Pet_Request_init_zero               {0}
#define dawg_Get_State_Request_init_zero         {0}
#define dawg_State_Reply_init_zero               {0, 0}
#define dawg_Request_init_zero                   {0, {dawg_Config_Request_init_zero}}
#define dawg_Reply_init_zero                     {0, {error_Error_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define dawg_Arm_Request_armed_tag               1
#define dawg_Config_Request_timeout_ms_tag       1
#define dawg_Pet_Request_firing_tag              1
#define dawg_State_Reply_armed_tag               1
#define dawg_State_Reply_petted_tag              2
#define dawg_Reply_error_tag                     1
#define dawg_Reply_ack_tag                       2
#define dawg_Reply_state_tag                     3
#define dawg_Request_config_tag                  1
#define dawg_Request_arm_tag                     2
#define dawg_Request_pet_tag                     3
#define dawg_Request_state_tag                   4

/* Struct field encoding specification for nanopb */
#define dawg_Config_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   timeout_ms,        1)
#define dawg_Config_Request_CALLBACK NULL
#define dawg_Config_Request_DEFAULT NULL

#define dawg_Arm_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     armed,             1)
#define dawg_Arm_Request_CALLBACK NULL
#define dawg_Arm_Request_DEFAULT NULL

#define dawg_Pet_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     firing,            1)
#define dawg_Pet_Request_CALLBACK NULL
#define dawg_Pet_Request_DEFAULT NULL

#define dawg_Get_State_Request_FIELDLIST(X, a) \

#define dawg_Get_State_Request_CALLBACK NULL
#define dawg_Get_State_Request_DEFAULT NULL

#define dawg_State_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     armed,             1) \
X(a, STATIC,   SINGULAR, BOOL,     petted,            2)
#define dawg_State_Reply_CALLBACK NULL
#define dawg_State_Reply_DEFAULT NULL

#define dawg_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,config,request.config),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,arm,request.arm),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pet,request.pet),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,state,request.state),   4)
#define dawg_Request_CALLBACK NULL
#define dawg_Request_DEFAULT NULL
#define dawg_Request_request_config_MSGTYPE dawg_Config_Request
#define dawg_Request_request_arm_MSGTYPE dawg_Arm_Request
#define dawg_Request_request_pet_MSGTYPE dawg_Pet_Request
#define dawg_Request_request_state_MSGTYPE dawg_Get_State_Request

#define dawg_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,state,reply.state),   3)
#define dawg_Reply_CALLBACK NULL
#define dawg_Reply_DEFAULT NULL
#define dawg_Reply_reply_error_MSGTYPE error_Error
#define dawg_Reply_reply_ack_MSGTYPE ack_Ack
#define dawg_Reply_reply_state_MSGTYPE dawg_State_Reply

extern const pb_msgdesc_t dawg_Config_Request_msg;
extern const pb_msgdesc_t dawg_Arm_Request_msg;
extern const pb_msgdesc_t dawg_Pet_Request_msg;
extern const pb_msgdesc_t dawg_Get_State_Request_msg;
extern const pb_msgdesc_t dawg_State_Reply_msg;
extern const pb_msgdesc_t dawg_Request_msg;
extern const pb_msgdesc_t dawg_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define dawg_Config_Request_fields &dawg_Config_Request_msg
#define dawg_Arm_Request_fields &dawg_Arm_Request_msg
#define dawg_Pet_Request_fields &dawg_Pet_Request_msg
#define dawg_Get_State_Request_fields &dawg_Get_State_Request_msg
#define dawg_State_Reply_fields &dawg_State_Reply_msg
#define dawg_Request_fields &dawg_Request_msg
#define dawg_Reply_fields &dawg_Reply_msg

/* Maximum encoded size of messages (where known) */
#define dawg_Config_Request_size                 6
#define dawg_Arm_Request_size                    2
#define dawg_Pet_Request_size                    2
#define dawg_Get_State_Request_size              0
#define dawg_State_Reply_size                    4
#define dawg_Request_size                        8
#if defined(error_Error_size) && defined(ack_Ack_size)
typedef union dawg_Reply_reply_size_union {char f1[(6 + error_Error_size)]; char f2[(6 + ack_Ack_size)]; char f0[6];} dawg_Reply_reply_size_union;
#define dawg_Reply_size                          (0 + sizeof(dawg_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

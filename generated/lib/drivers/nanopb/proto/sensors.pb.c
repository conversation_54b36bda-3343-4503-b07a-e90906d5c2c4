/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "sensors.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(sensors_FuelGauge_Request, sensors_FuelGauge_Request, AUTO)


PB_BIND(sensors_FuelGauge_Reply, sensors_FuelGauge_Reply, AUTO)


PB_BIND(sensors_Request, sensors_Request, AUTO)


PB_BIND(sensors_Reply, sensors_Reply, AUTO)




/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_SERVO_SERVO_PB_H_INCLUDED
#define PB_SERVO_SERVO_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/epos.pb.h"
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"
#include "generated/lib/drivers/nanopb/proto/time.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _servo_GoToMode {
    servo_GoToMode_IMMEDIATE = 0,
    servo_GoToMode_REACHED = 1,
    servo_GoToMode_SETTLED = 2,
    servo_GoToMode_TRAJECTORY = 3
} servo_GoToMode;

/* Struct definitions */
typedef struct _servo_Get_Limits_Request {
    char dummy_field;
} servo_Get_Limits_Request;

typedef struct _servo_Stop_Request {
    char dummy_field;
} servo_Stop_Request;

typedef struct _servo_Boot_Request {
    bool has_params;
    epos_Home_Params params;
} servo_Boot_Request;

typedef struct _servo_Config {
    uint32_t max_profile_velocity;
    uint32_t settle_window;
    uint32_t settle_timeout;
    uint32_t max_diff_millis;
} servo_Config;

typedef struct _servo_Follow_Timestamp_Reply {
    int32_t pre_position;
    bool has_pre_timestamp;
    time_Timestamp pre_timestamp;
} servo_Follow_Timestamp_Reply;

typedef struct _servo_Follow_Timestamp_Request {
    bool has_timestamp;
    time_Timestamp timestamp;
    int32_t follow_velocity;
    int32_t follow_accel;
} servo_Follow_Timestamp_Request;

typedef struct _servo_Go_To_Calibrate_Request {
    int32_t position;
    uint32_t velocity;
    uint32_t window;
    uint32_t time_window_ms;
    uint32_t timeout_ms;
    uint32_t period_ms;
} servo_Go_To_Calibrate_Request;

typedef struct _servo_Go_To_Delta_Request {
    int32_t delta_position;
    uint32_t velocity;
    servo_GoToMode mode;
} servo_Go_To_Delta_Request;

typedef struct _servo_Go_To_Follow_Reply {
    int32_t pre_position;
    bool has_pre_timestamp;
    time_Timestamp pre_timestamp;
} servo_Go_To_Follow_Reply;

typedef struct _servo_Go_To_Follow_Request {
    bool has_timestamp;
    time_Timestamp timestamp;
    int32_t position;
    uint32_t velocity_mrpm;
    int32_t follow_velocity;
    int32_t follow_accel;
    uint32_t interval_sleep_time_ms;
} servo_Go_To_Follow_Request;

typedef struct _servo_Go_To_Request {
    int32_t position;
    uint32_t velocity;
    bool await_settle;
} servo_Go_To_Request;

typedef struct _servo_Go_To_Timestamp_Reply {
    int32_t pre_position;
    int32_t post_position;
    bool has_pre_timestamp;
    time_Timestamp pre_timestamp;
    bool has_post_timestamp;
    time_Timestamp post_timestamp;
} servo_Go_To_Timestamp_Reply;

typedef struct _servo_Go_To_Timestamp_Request {
    bool has_timestamp;
    time_Timestamp timestamp;
    servo_GoToMode mode;
    int32_t position;
    uint32_t velocity_mrpm;
    int32_t follow_velocity;
    int32_t follow_accel;
    uint32_t interval_sleep_time_ms;
} servo_Go_To_Timestamp_Request;

typedef struct _servo_Limits_Reply {
    int32_t min;
    int32_t max;
} servo_Limits_Reply;

typedef struct _servo_Position_Reply {
    int32_t position;
} servo_Position_Reply;

typedef struct _servo_Settle_Time_Reply {
    uint32_t settle_time;
} servo_Settle_Time_Reply;

typedef struct _servo_Config_Request {
    uint32_t node_id;
    bool has_config;
    servo_Config config;
} servo_Config_Request;

typedef struct _servo_Go_To_Delta_Follow_Request {
    bool has_delta;
    servo_Go_To_Delta_Request delta;
    int32_t follow_velocity_vector;
    uint32_t follow_velocity_mrpm;
    uint32_t interval_sleep_time_ms;
    bool fast_return;
} servo_Go_To_Delta_Follow_Request;

typedef struct _servo_Reply {
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        servo_Limits_Reply limit;
        epos_Reply epos;
        servo_Position_Reply pos;
        servo_Settle_Time_Reply settle;
        servo_Go_To_Timestamp_Reply go_to_timestamp;
        servo_Follow_Timestamp_Reply follow_timestamp;
        servo_Go_To_Follow_Reply go_to_follow;
    } reply;
} servo_Reply;

typedef struct _servo_Request {
    pb_size_t which_request;
    union {
        servo_Config_Request config;
        servo_Boot_Request boot;
        servo_Stop_Request stop;
        servo_Go_To_Request go_to;
        servo_Get_Limits_Request limit;
        epos_Request epos;
        servo_Go_To_Delta_Request delta;
        servo_Go_To_Delta_Follow_Request follow;
        servo_Go_To_Calibrate_Request calibrate;
        servo_Go_To_Timestamp_Request go_to_timestamp;
        servo_Follow_Timestamp_Request follow_timestamp;
        servo_Go_To_Follow_Request go_to_follow;
    } request;
} servo_Request;


/* Helper constants for enums */
#define _servo_GoToMode_MIN servo_GoToMode_IMMEDIATE
#define _servo_GoToMode_MAX servo_GoToMode_TRAJECTORY
#define _servo_GoToMode_ARRAYSIZE ((servo_GoToMode)(servo_GoToMode_TRAJECTORY+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define servo_Config_init_default                {0, 0, 0, 0}
#define servo_Config_Request_init_default        {0, false, servo_Config_init_default}
#define servo_Boot_Request_init_default          {false, epos_Home_Params_init_default}
#define servo_Stop_Request_init_default          {0}
#define servo_Go_To_Request_init_default         {0, 0, 0}
#define servo_Get_Limits_Request_init_default    {0}
#define servo_Go_To_Delta_Request_init_default   {0, 0, _servo_GoToMode_MIN}
#define servo_Go_To_Delta_Follow_Request_init_default {false, servo_Go_To_Delta_Request_init_default, 0, 0, 0, 0}
#define servo_Go_To_Calibrate_Request_init_default {0, 0, 0, 0, 0, 0}
#define servo_Go_To_Timestamp_Request_init_default {false, time_Timestamp_init_default, _servo_GoToMode_MIN, 0, 0, 0, 0, 0}
#define servo_Go_To_Follow_Request_init_default  {false, time_Timestamp_init_default, 0, 0, 0, 0, 0}
#define servo_Follow_Timestamp_Request_init_default {false, time_Timestamp_init_default, 0, 0}
#define servo_Limits_Reply_init_default          {0, 0}
#define servo_Position_Reply_init_default        {0}
#define servo_Settle_Time_Reply_init_default     {0}
#define servo_Go_To_Timestamp_Reply_init_default {0, 0, false, time_Timestamp_init_default, false, time_Timestamp_init_default}
#define servo_Follow_Timestamp_Reply_init_default {0, false, time_Timestamp_init_default}
#define servo_Go_To_Follow_Reply_init_default    {0, false, time_Timestamp_init_default}
#define servo_Request_init_default               {0, {servo_Config_Request_init_default}}
#define servo_Reply_init_default                 {0, {error_Error_init_default}}
#define servo_Config_init_zero                   {0, 0, 0, 0}
#define servo_Config_Request_init_zero           {0, false, servo_Config_init_zero}
#define servo_Boot_Request_init_zero             {false, epos_Home_Params_init_zero}
#define servo_Stop_Request_init_zero             {0}
#define servo_Go_To_Request_init_zero            {0, 0, 0}
#define servo_Get_Limits_Request_init_zero       {0}
#define servo_Go_To_Delta_Request_init_zero      {0, 0, _servo_GoToMode_MIN}
#define servo_Go_To_Delta_Follow_Request_init_zero {false, servo_Go_To_Delta_Request_init_zero, 0, 0, 0, 0}
#define servo_Go_To_Calibrate_Request_init_zero  {0, 0, 0, 0, 0, 0}
#define servo_Go_To_Timestamp_Request_init_zero  {false, time_Timestamp_init_zero, _servo_GoToMode_MIN, 0, 0, 0, 0, 0}
#define servo_Go_To_Follow_Request_init_zero     {false, time_Timestamp_init_zero, 0, 0, 0, 0, 0}
#define servo_Follow_Timestamp_Request_init_zero {false, time_Timestamp_init_zero, 0, 0}
#define servo_Limits_Reply_init_zero             {0, 0}
#define servo_Position_Reply_init_zero           {0}
#define servo_Settle_Time_Reply_init_zero        {0}
#define servo_Go_To_Timestamp_Reply_init_zero    {0, 0, false, time_Timestamp_init_zero, false, time_Timestamp_init_zero}
#define servo_Follow_Timestamp_Reply_init_zero   {0, false, time_Timestamp_init_zero}
#define servo_Go_To_Follow_Reply_init_zero       {0, false, time_Timestamp_init_zero}
#define servo_Request_init_zero                  {0, {servo_Config_Request_init_zero}}
#define servo_Reply_init_zero                    {0, {error_Error_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define servo_Boot_Request_params_tag            1
#define servo_Config_max_profile_velocity_tag    1
#define servo_Config_settle_window_tag           2
#define servo_Config_settle_timeout_tag          3
#define servo_Config_max_diff_millis_tag         4
#define servo_Follow_Timestamp_Reply_pre_position_tag 1
#define servo_Follow_Timestamp_Reply_pre_timestamp_tag 2
#define servo_Follow_Timestamp_Request_timestamp_tag 1
#define servo_Follow_Timestamp_Request_follow_velocity_tag 2
#define servo_Follow_Timestamp_Request_follow_accel_tag 3
#define servo_Go_To_Calibrate_Request_position_tag 1
#define servo_Go_To_Calibrate_Request_velocity_tag 2
#define servo_Go_To_Calibrate_Request_window_tag 3
#define servo_Go_To_Calibrate_Request_time_window_ms_tag 4
#define servo_Go_To_Calibrate_Request_timeout_ms_tag 5
#define servo_Go_To_Calibrate_Request_period_ms_tag 6
#define servo_Go_To_Delta_Request_delta_position_tag 1
#define servo_Go_To_Delta_Request_velocity_tag   2
#define servo_Go_To_Delta_Request_mode_tag       3
#define servo_Go_To_Follow_Reply_pre_position_tag 1
#define servo_Go_To_Follow_Reply_pre_timestamp_tag 2
#define servo_Go_To_Follow_Request_timestamp_tag 1
#define servo_Go_To_Follow_Request_position_tag  2
#define servo_Go_To_Follow_Request_velocity_mrpm_tag 3
#define servo_Go_To_Follow_Request_follow_velocity_tag 4
#define servo_Go_To_Follow_Request_follow_accel_tag 5
#define servo_Go_To_Follow_Request_interval_sleep_time_ms_tag 6
#define servo_Go_To_Request_position_tag         1
#define servo_Go_To_Request_velocity_tag         2
#define servo_Go_To_Request_await_settle_tag     3
#define servo_Go_To_Timestamp_Reply_pre_position_tag 1
#define servo_Go_To_Timestamp_Reply_post_position_tag 2
#define servo_Go_To_Timestamp_Reply_pre_timestamp_tag 3
#define servo_Go_To_Timestamp_Reply_post_timestamp_tag 4
#define servo_Go_To_Timestamp_Request_timestamp_tag 1
#define servo_Go_To_Timestamp_Request_mode_tag   2
#define servo_Go_To_Timestamp_Request_position_tag 3
#define servo_Go_To_Timestamp_Request_velocity_mrpm_tag 4
#define servo_Go_To_Timestamp_Request_follow_velocity_tag 5
#define servo_Go_To_Timestamp_Request_follow_accel_tag 6
#define servo_Go_To_Timestamp_Request_interval_sleep_time_ms_tag 7
#define servo_Limits_Reply_min_tag               1
#define servo_Limits_Reply_max_tag               2
#define servo_Position_Reply_position_tag        1
#define servo_Settle_Time_Reply_settle_time_tag  1
#define servo_Config_Request_node_id_tag         1
#define servo_Config_Request_config_tag          2
#define servo_Go_To_Delta_Follow_Request_delta_tag 1
#define servo_Go_To_Delta_Follow_Request_follow_velocity_vector_tag 2
#define servo_Go_To_Delta_Follow_Request_follow_velocity_mrpm_tag 3
#define servo_Go_To_Delta_Follow_Request_interval_sleep_time_ms_tag 4
#define servo_Go_To_Delta_Follow_Request_fast_return_tag 5
#define servo_Reply_error_tag                    1
#define servo_Reply_ack_tag                      2
#define servo_Reply_limit_tag                    3
#define servo_Reply_epos_tag                     4
#define servo_Reply_pos_tag                      5
#define servo_Reply_settle_tag                   6
#define servo_Reply_go_to_timestamp_tag          7
#define servo_Reply_follow_timestamp_tag         8
#define servo_Reply_go_to_follow_tag             9
#define servo_Request_config_tag                 1
#define servo_Request_boot_tag                   2
#define servo_Request_stop_tag                   3
#define servo_Request_go_to_tag                  4
#define servo_Request_limit_tag                  5
#define servo_Request_epos_tag                   6
#define servo_Request_delta_tag                  7
#define servo_Request_follow_tag                 8
#define servo_Request_calibrate_tag              9
#define servo_Request_go_to_timestamp_tag        10
#define servo_Request_follow_timestamp_tag       11
#define servo_Request_go_to_follow_tag           12

/* Struct field encoding specification for nanopb */
#define servo_Config_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   max_profile_velocity,   1) \
X(a, STATIC,   SINGULAR, UINT32,   settle_window,     2) \
X(a, STATIC,   SINGULAR, UINT32,   settle_timeout,    3) \
X(a, STATIC,   SINGULAR, UINT32,   max_diff_millis,   4)
#define servo_Config_CALLBACK NULL
#define servo_Config_DEFAULT NULL

#define servo_Config_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            2)
#define servo_Config_Request_CALLBACK NULL
#define servo_Config_Request_DEFAULT NULL
#define servo_Config_Request_config_MSGTYPE servo_Config

#define servo_Boot_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  params,            1)
#define servo_Boot_Request_CALLBACK NULL
#define servo_Boot_Request_DEFAULT NULL
#define servo_Boot_Request_params_MSGTYPE epos_Home_Params

#define servo_Stop_Request_FIELDLIST(X, a) \

#define servo_Stop_Request_CALLBACK NULL
#define servo_Stop_Request_DEFAULT NULL

#define servo_Go_To_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    position,          1) \
X(a, STATIC,   SINGULAR, UINT32,   velocity,          2) \
X(a, STATIC,   SINGULAR, BOOL,     await_settle,      3)
#define servo_Go_To_Request_CALLBACK NULL
#define servo_Go_To_Request_DEFAULT NULL

#define servo_Get_Limits_Request_FIELDLIST(X, a) \

#define servo_Get_Limits_Request_CALLBACK NULL
#define servo_Get_Limits_Request_DEFAULT NULL

#define servo_Go_To_Delta_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    delta_position,    1) \
X(a, STATIC,   SINGULAR, UINT32,   velocity,          2) \
X(a, STATIC,   SINGULAR, UENUM,    mode,              3)
#define servo_Go_To_Delta_Request_CALLBACK NULL
#define servo_Go_To_Delta_Request_DEFAULT NULL

#define servo_Go_To_Delta_Follow_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  delta,             1) \
X(a, STATIC,   SINGULAR, INT32,    follow_velocity_vector,   2) \
X(a, STATIC,   SINGULAR, UINT32,   follow_velocity_mrpm,   3) \
X(a, STATIC,   SINGULAR, UINT32,   interval_sleep_time_ms,   4) \
X(a, STATIC,   SINGULAR, BOOL,     fast_return,       5)
#define servo_Go_To_Delta_Follow_Request_CALLBACK NULL
#define servo_Go_To_Delta_Follow_Request_DEFAULT NULL
#define servo_Go_To_Delta_Follow_Request_delta_MSGTYPE servo_Go_To_Delta_Request

#define servo_Go_To_Calibrate_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    position,          1) \
X(a, STATIC,   SINGULAR, UINT32,   velocity,          2) \
X(a, STATIC,   SINGULAR, UINT32,   window,            3) \
X(a, STATIC,   SINGULAR, UINT32,   time_window_ms,    4) \
X(a, STATIC,   SINGULAR, UINT32,   timeout_ms,        5) \
X(a, STATIC,   SINGULAR, UINT32,   period_ms,         6)
#define servo_Go_To_Calibrate_Request_CALLBACK NULL
#define servo_Go_To_Calibrate_Request_DEFAULT NULL

#define servo_Go_To_Timestamp_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  timestamp,         1) \
X(a, STATIC,   SINGULAR, UENUM,    mode,              2) \
X(a, STATIC,   SINGULAR, INT32,    position,          3) \
X(a, STATIC,   SINGULAR, UINT32,   velocity_mrpm,     4) \
X(a, STATIC,   SINGULAR, INT32,    follow_velocity,   5) \
X(a, STATIC,   SINGULAR, INT32,    follow_accel,      6) \
X(a, STATIC,   SINGULAR, UINT32,   interval_sleep_time_ms,   7)
#define servo_Go_To_Timestamp_Request_CALLBACK NULL
#define servo_Go_To_Timestamp_Request_DEFAULT NULL
#define servo_Go_To_Timestamp_Request_timestamp_MSGTYPE time_Timestamp

#define servo_Go_To_Follow_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  timestamp,         1) \
X(a, STATIC,   SINGULAR, INT32,    position,          2) \
X(a, STATIC,   SINGULAR, UINT32,   velocity_mrpm,     3) \
X(a, STATIC,   SINGULAR, INT32,    follow_velocity,   4) \
X(a, STATIC,   SINGULAR, INT32,    follow_accel,      5) \
X(a, STATIC,   SINGULAR, UINT32,   interval_sleep_time_ms,   6)
#define servo_Go_To_Follow_Request_CALLBACK NULL
#define servo_Go_To_Follow_Request_DEFAULT NULL
#define servo_Go_To_Follow_Request_timestamp_MSGTYPE time_Timestamp

#define servo_Follow_Timestamp_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  timestamp,         1) \
X(a, STATIC,   SINGULAR, INT32,    follow_velocity,   2) \
X(a, STATIC,   SINGULAR, INT32,    follow_accel,      3)
#define servo_Follow_Timestamp_Request_CALLBACK NULL
#define servo_Follow_Timestamp_Request_DEFAULT NULL
#define servo_Follow_Timestamp_Request_timestamp_MSGTYPE time_Timestamp

#define servo_Limits_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    min,               1) \
X(a, STATIC,   SINGULAR, INT32,    max,               2)
#define servo_Limits_Reply_CALLBACK NULL
#define servo_Limits_Reply_DEFAULT NULL

#define servo_Position_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    position,          1)
#define servo_Position_Reply_CALLBACK NULL
#define servo_Position_Reply_DEFAULT NULL

#define servo_Settle_Time_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   settle_time,       1)
#define servo_Settle_Time_Reply_CALLBACK NULL
#define servo_Settle_Time_Reply_DEFAULT NULL

#define servo_Go_To_Timestamp_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    pre_position,      1) \
X(a, STATIC,   SINGULAR, INT32,    post_position,     2) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pre_timestamp,     3) \
X(a, STATIC,   OPTIONAL, MESSAGE,  post_timestamp,    4)
#define servo_Go_To_Timestamp_Reply_CALLBACK NULL
#define servo_Go_To_Timestamp_Reply_DEFAULT NULL
#define servo_Go_To_Timestamp_Reply_pre_timestamp_MSGTYPE time_Timestamp
#define servo_Go_To_Timestamp_Reply_post_timestamp_MSGTYPE time_Timestamp

#define servo_Follow_Timestamp_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    pre_position,      1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pre_timestamp,     2)
#define servo_Follow_Timestamp_Reply_CALLBACK NULL
#define servo_Follow_Timestamp_Reply_DEFAULT NULL
#define servo_Follow_Timestamp_Reply_pre_timestamp_MSGTYPE time_Timestamp

#define servo_Go_To_Follow_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    pre_position,      1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pre_timestamp,     2)
#define servo_Go_To_Follow_Reply_CALLBACK NULL
#define servo_Go_To_Follow_Reply_DEFAULT NULL
#define servo_Go_To_Follow_Reply_pre_timestamp_MSGTYPE time_Timestamp

#define servo_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,config,request.config),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,boot,request.boot),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,stop,request.stop),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,go_to,request.go_to),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,limit,request.limit),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,epos,request.epos),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,delta,request.delta),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,follow,request.follow),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,calibrate,request.calibrate),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,go_to_timestamp,request.go_to_timestamp),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,follow_timestamp,request.follow_timestamp),  11) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,go_to_follow,request.go_to_follow),  12)
#define servo_Request_CALLBACK NULL
#define servo_Request_DEFAULT NULL
#define servo_Request_request_config_MSGTYPE servo_Config_Request
#define servo_Request_request_boot_MSGTYPE servo_Boot_Request
#define servo_Request_request_stop_MSGTYPE servo_Stop_Request
#define servo_Request_request_go_to_MSGTYPE servo_Go_To_Request
#define servo_Request_request_limit_MSGTYPE servo_Get_Limits_Request
#define servo_Request_request_epos_MSGTYPE epos_Request
#define servo_Request_request_delta_MSGTYPE servo_Go_To_Delta_Request
#define servo_Request_request_follow_MSGTYPE servo_Go_To_Delta_Follow_Request
#define servo_Request_request_calibrate_MSGTYPE servo_Go_To_Calibrate_Request
#define servo_Request_request_go_to_timestamp_MSGTYPE servo_Go_To_Timestamp_Request
#define servo_Request_request_follow_timestamp_MSGTYPE servo_Follow_Timestamp_Request
#define servo_Request_request_go_to_follow_MSGTYPE servo_Go_To_Follow_Request

#define servo_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,limit,reply.limit),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,epos,reply.epos),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pos,reply.pos),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,settle,reply.settle),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,go_to_timestamp,reply.go_to_timestamp),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,follow_timestamp,reply.follow_timestamp),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,go_to_follow,reply.go_to_follow),   9)
#define servo_Reply_CALLBACK NULL
#define servo_Reply_DEFAULT NULL
#define servo_Reply_reply_error_MSGTYPE error_Error
#define servo_Reply_reply_ack_MSGTYPE ack_Ack
#define servo_Reply_reply_limit_MSGTYPE servo_Limits_Reply
#define servo_Reply_reply_epos_MSGTYPE epos_Reply
#define servo_Reply_reply_pos_MSGTYPE servo_Position_Reply
#define servo_Reply_reply_settle_MSGTYPE servo_Settle_Time_Reply
#define servo_Reply_reply_go_to_timestamp_MSGTYPE servo_Go_To_Timestamp_Reply
#define servo_Reply_reply_follow_timestamp_MSGTYPE servo_Follow_Timestamp_Reply
#define servo_Reply_reply_go_to_follow_MSGTYPE servo_Go_To_Follow_Reply

extern const pb_msgdesc_t servo_Config_msg;
extern const pb_msgdesc_t servo_Config_Request_msg;
extern const pb_msgdesc_t servo_Boot_Request_msg;
extern const pb_msgdesc_t servo_Stop_Request_msg;
extern const pb_msgdesc_t servo_Go_To_Request_msg;
extern const pb_msgdesc_t servo_Get_Limits_Request_msg;
extern const pb_msgdesc_t servo_Go_To_Delta_Request_msg;
extern const pb_msgdesc_t servo_Go_To_Delta_Follow_Request_msg;
extern const pb_msgdesc_t servo_Go_To_Calibrate_Request_msg;
extern const pb_msgdesc_t servo_Go_To_Timestamp_Request_msg;
extern const pb_msgdesc_t servo_Go_To_Follow_Request_msg;
extern const pb_msgdesc_t servo_Follow_Timestamp_Request_msg;
extern const pb_msgdesc_t servo_Limits_Reply_msg;
extern const pb_msgdesc_t servo_Position_Reply_msg;
extern const pb_msgdesc_t servo_Settle_Time_Reply_msg;
extern const pb_msgdesc_t servo_Go_To_Timestamp_Reply_msg;
extern const pb_msgdesc_t servo_Follow_Timestamp_Reply_msg;
extern const pb_msgdesc_t servo_Go_To_Follow_Reply_msg;
extern const pb_msgdesc_t servo_Request_msg;
extern const pb_msgdesc_t servo_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define servo_Config_fields &servo_Config_msg
#define servo_Config_Request_fields &servo_Config_Request_msg
#define servo_Boot_Request_fields &servo_Boot_Request_msg
#define servo_Stop_Request_fields &servo_Stop_Request_msg
#define servo_Go_To_Request_fields &servo_Go_To_Request_msg
#define servo_Get_Limits_Request_fields &servo_Get_Limits_Request_msg
#define servo_Go_To_Delta_Request_fields &servo_Go_To_Delta_Request_msg
#define servo_Go_To_Delta_Follow_Request_fields &servo_Go_To_Delta_Follow_Request_msg
#define servo_Go_To_Calibrate_Request_fields &servo_Go_To_Calibrate_Request_msg
#define servo_Go_To_Timestamp_Request_fields &servo_Go_To_Timestamp_Request_msg
#define servo_Go_To_Follow_Request_fields &servo_Go_To_Follow_Request_msg
#define servo_Follow_Timestamp_Request_fields &servo_Follow_Timestamp_Request_msg
#define servo_Limits_Reply_fields &servo_Limits_Reply_msg
#define servo_Position_Reply_fields &servo_Position_Reply_msg
#define servo_Settle_Time_Reply_fields &servo_Settle_Time_Reply_msg
#define servo_Go_To_Timestamp_Reply_fields &servo_Go_To_Timestamp_Reply_msg
#define servo_Follow_Timestamp_Reply_fields &servo_Follow_Timestamp_Reply_msg
#define servo_Go_To_Follow_Reply_fields &servo_Go_To_Follow_Reply_msg
#define servo_Request_fields &servo_Request_msg
#define servo_Reply_fields &servo_Reply_msg

/* Maximum encoded size of messages (where known) */
#define servo_Config_size                        24
#define servo_Config_Request_size                32
#if defined(epos_Home_Params_size)
#define servo_Boot_Request_size                  (6 + epos_Home_Params_size)
#endif
#define servo_Stop_Request_size                  0
#define servo_Go_To_Request_size                 19
#define servo_Get_Limits_Request_size            0
#define servo_Go_To_Delta_Request_size           19
#define servo_Go_To_Delta_Follow_Request_size    46
#define servo_Go_To_Calibrate_Request_size       41
#if defined(time_Timestamp_size)
#define servo_Go_To_Timestamp_Request_size       (53 + time_Timestamp_size)
#endif
#if defined(time_Timestamp_size)
#define servo_Go_To_Follow_Request_size          (51 + time_Timestamp_size)
#endif
#if defined(time_Timestamp_size)
#define servo_Follow_Timestamp_Request_size      (28 + time_Timestamp_size)
#endif
#define servo_Limits_Reply_size                  22
#define servo_Position_Reply_size                11
#define servo_Settle_Time_Reply_size             6
#if defined(time_Timestamp_size) && defined(time_Timestamp_size)
#define servo_Go_To_Timestamp_Reply_size         (34 + time_Timestamp_size + time_Timestamp_size)
#endif
#if defined(time_Timestamp_size)
#define servo_Follow_Timestamp_Reply_size        (17 + time_Timestamp_size)
#endif
#if defined(time_Timestamp_size)
#define servo_Go_To_Follow_Reply_size            (17 + time_Timestamp_size)
#endif
#if defined(epos_Home_Params_size) && defined(epos_Request_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size)
typedef union servo_Request_request_size_union {char f2[(12 + epos_Home_Params_size)]; char f6[(6 + epos_Request_size)]; char f10[(59 + time_Timestamp_size)]; char f11[(34 + time_Timestamp_size)]; char f12[(57 + time_Timestamp_size)]; char f0[48];} servo_Request_request_size_union;
#define servo_Request_size                       (0 + sizeof(servo_Request_request_size_union))
#endif
#if defined(error_Error_size) && defined(ack_Ack_size) && defined(epos_Reply_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size)
typedef union servo_Reply_reply_size_union {char f1[(6 + error_Error_size)]; char f2[(6 + ack_Ack_size)]; char f4[(6 + epos_Reply_size)]; char f7[(40 + time_Timestamp_size + time_Timestamp_size)]; char f8[(23 + time_Timestamp_size)]; char f9[(23 + time_Timestamp_size)]; char f0[24];} servo_Reply_reply_size_union;
#define servo_Reply_size                         (0 + sizeof(servo_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

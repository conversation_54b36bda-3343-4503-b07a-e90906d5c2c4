/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_VERSION_VERSION_PB_H_INCLUDED
#define PB_VERSION_VERSION_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _version_Reset_Request {
    char dummy_field;
} version_Reset_Request;

typedef struct _version_Version_Request {
    char dummy_field;
} version_Version_Request;

typedef struct _version_Version_Reply {
    uint32_t major;
    uint32_t minor;
} version_Version_Reply;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define version_Reset_Request_init_default       {0}
#define version_Version_Request_init_default     {0}
#define version_Version_Reply_init_default       {0, 0}
#define version_Reset_Request_init_zero          {0}
#define version_Version_Request_init_zero        {0}
#define version_Version_Reply_init_zero          {0, 0}

/* Field tags (for use in manual encoding/decoding) */
#define version_Version_Reply_major_tag          1
#define version_Version_Reply_minor_tag          2

/* Struct field encoding specification for nanopb */
#define version_Reset_Request_FIELDLIST(X, a) \

#define version_Reset_Request_CALLBACK NULL
#define version_Reset_Request_DEFAULT NULL

#define version_Version_Request_FIELDLIST(X, a) \

#define version_Version_Request_CALLBACK NULL
#define version_Version_Request_DEFAULT NULL

#define version_Version_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   major,             1) \
X(a, STATIC,   SINGULAR, UINT32,   minor,             2)
#define version_Version_Reply_CALLBACK NULL
#define version_Version_Reply_DEFAULT NULL

extern const pb_msgdesc_t version_Reset_Request_msg;
extern const pb_msgdesc_t version_Version_Request_msg;
extern const pb_msgdesc_t version_Version_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define version_Reset_Request_fields &version_Reset_Request_msg
#define version_Version_Request_fields &version_Version_Request_msg
#define version_Version_Reply_fields &version_Version_Reply_msg

/* Maximum encoded size of messages (where known) */
#define version_Reset_Request_size               0
#define version_Version_Request_size             0
#define version_Version_Reply_size               12

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/row_module.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import scanner_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_scanner__pb2
from generated.lib.drivers.nanopb.proto import dawg_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_dawg__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/row_module.proto',
  package='row_module',
  syntax='proto3',
  serialized_options=b'Z\021nanopb/row_module',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n3generated/lib/drivers/nanopb/proto/row_module.proto\x12\nrow_module\x1a\x30generated/lib/drivers/nanopb/proto/scanner.proto\x1a-generated/lib/drivers/nanopb/proto/dawg.proto\"\x0f\n\rReset_Request\"\x16\n\x14\x43lear_Config_Request\"H\n\x0fScanner_Request\x12\x12\n\nscanner_id\x18\x01 \x01(\r\x12!\n\x07request\x18\x02 \x01(\x0b\x32\x10.scanner.Request\"\x0b\n\tACK_Reply\"\xc2\x01\n\x07Request\x12*\n\x05reset\x18\x01 \x01(\x0b\x32\x19.row_module.Reset_RequestH\x00\x12\x31\n\x05\x63lear\x18\x02 \x01(\x0b\x32 .row_module.Clear_Config_RequestH\x00\x12.\n\x07scanner\x18\x03 \x01(\x0b\x32\x1b.row_module.Scanner_RequestH\x00\x12\x1d\n\x04\x64\x61wg\x18\x04 \x01(\x0b\x32\r.dawg.RequestH\x00\x42\t\n\x07request\"v\n\x05Reply\x12$\n\x03\x61\x63k\x18\x01 \x01(\x0b\x32\x15.row_module.ACK_ReplyH\x00\x12!\n\x07scanner\x18\x02 \x01(\x0b\x32\x0e.scanner.ReplyH\x00\x12\x1b\n\x04\x64\x61wg\x18\x03 \x01(\x0b\x32\x0b.dawg.ReplyH\x00\x42\x07\n\x05replyB\x13Z\x11nanopb/row_moduleb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_scanner__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_dawg__pb2.DESCRIPTOR,])




_RESET_REQUEST = _descriptor.Descriptor(
  name='Reset_Request',
  full_name='row_module.Reset_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=164,
  serialized_end=179,
)


_CLEAR_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Clear_Config_Request',
  full_name='row_module.Clear_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=181,
  serialized_end=203,
)


_SCANNER_REQUEST = _descriptor.Descriptor(
  name='Scanner_Request',
  full_name='row_module.Scanner_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_id', full_name='row_module.Scanner_Request.scanner_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='request', full_name='row_module.Scanner_Request.request', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=205,
  serialized_end=277,
)


_ACK_REPLY = _descriptor.Descriptor(
  name='ACK_Reply',
  full_name='row_module.ACK_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=279,
  serialized_end=290,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='row_module.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='reset', full_name='row_module.Request.reset', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='clear', full_name='row_module.Request.clear', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner', full_name='row_module.Request.scanner', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dawg', full_name='row_module.Request.dawg', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='row_module.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=293,
  serialized_end=487,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='row_module.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ack', full_name='row_module.Reply.ack', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner', full_name='row_module.Reply.scanner', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dawg', full_name='row_module.Reply.dawg', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='row_module.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=489,
  serialized_end=607,
)

_SCANNER_REQUEST.fields_by_name['request'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_scanner__pb2._REQUEST
_REQUEST.fields_by_name['reset'].message_type = _RESET_REQUEST
_REQUEST.fields_by_name['clear'].message_type = _CLEAR_CONFIG_REQUEST
_REQUEST.fields_by_name['scanner'].message_type = _SCANNER_REQUEST
_REQUEST.fields_by_name['dawg'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_dawg__pb2._REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['reset'])
_REQUEST.fields_by_name['reset'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['clear'])
_REQUEST.fields_by_name['clear'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['scanner'])
_REQUEST.fields_by_name['scanner'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['dawg'])
_REQUEST.fields_by_name['dawg'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['ack'].message_type = _ACK_REPLY
_REPLY.fields_by_name['scanner'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_scanner__pb2._REPLY
_REPLY.fields_by_name['dawg'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_dawg__pb2._REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['scanner'])
_REPLY.fields_by_name['scanner'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['dawg'])
_REPLY.fields_by_name['dawg'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Reset_Request'] = _RESET_REQUEST
DESCRIPTOR.message_types_by_name['Clear_Config_Request'] = _CLEAR_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Scanner_Request'] = _SCANNER_REQUEST
DESCRIPTOR.message_types_by_name['ACK_Reply'] = _ACK_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Reset_Request = _reflection.GeneratedProtocolMessageType('Reset_Request', (_message.Message,), {
  'DESCRIPTOR' : _RESET_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.row_module_pb2'
  # @@protoc_insertion_point(class_scope:row_module.Reset_Request)
  })
_sym_db.RegisterMessage(Reset_Request)

Clear_Config_Request = _reflection.GeneratedProtocolMessageType('Clear_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _CLEAR_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.row_module_pb2'
  # @@protoc_insertion_point(class_scope:row_module.Clear_Config_Request)
  })
_sym_db.RegisterMessage(Clear_Config_Request)

Scanner_Request = _reflection.GeneratedProtocolMessageType('Scanner_Request', (_message.Message,), {
  'DESCRIPTOR' : _SCANNER_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.row_module_pb2'
  # @@protoc_insertion_point(class_scope:row_module.Scanner_Request)
  })
_sym_db.RegisterMessage(Scanner_Request)

ACK_Reply = _reflection.GeneratedProtocolMessageType('ACK_Reply', (_message.Message,), {
  'DESCRIPTOR' : _ACK_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.row_module_pb2'
  # @@protoc_insertion_point(class_scope:row_module.ACK_Reply)
  })
_sym_db.RegisterMessage(ACK_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.row_module_pb2'
  # @@protoc_insertion_point(class_scope:row_module.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.row_module_pb2'
  # @@protoc_insertion_point(class_scope:row_module.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_REAPER_MODULE_CONTROLLER_REAPER_MODULE_CONTROLLER_PB_H_INCLUDED
#define PB_REAPER_MODULE_CONTROLLER_REAPER_MODULE_CONTROLLER_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/hwinfo.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"
#include "generated/lib/drivers/nanopb/proto/strobe_control.pb.h"
#include "generated/lib/drivers/nanopb/proto/time.pb.h"
#include "generated/lib/drivers/nanopb/proto/version.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _reaper_module_controller_NetworkAddressSource {
    reaper_module_controller_NetworkAddressSource_MANUAL = 0,
    reaper_module_controller_NetworkAddressSource_STATIC = 1,
    reaper_module_controller_NetworkAddressSource_DHCP = 2
} reaper_module_controller_NetworkAddressSource;

typedef enum _reaper_module_controller_ThermostatSource {
    reaper_module_controller_ThermostatSource_DEFAULT = 0,
    reaper_module_controller_ThermostatSource_ENVIRO_INTERNAL = 1,
    reaper_module_controller_ThermostatSource_ENVIRO_EXTERNAL = 2
} reaper_module_controller_ThermostatSource;

/* Struct definitions */
typedef struct _reaper_module_controller_CoreDumpEnd {
    char dummy_field;
} reaper_module_controller_CoreDumpEnd;

typedef struct _reaper_module_controller_CoreDumpStart {
    char dummy_field;
} reaper_module_controller_CoreDumpStart;

typedef struct _reaper_module_controller_GetModuleIdentityRequest {
    char dummy_field;
} reaper_module_controller_GetModuleIdentityRequest;

typedef struct _reaper_module_controller_NetworkConfigRequest {
    char dummy_field;
} reaper_module_controller_NetworkConfigRequest;

typedef struct _reaper_module_controller_NetworkPowerCycleRequest {
    char dummy_field;
} reaper_module_controller_NetworkPowerCycleRequest;

typedef struct _reaper_module_controller_NetworkResetRequest {
    char dummy_field;
} reaper_module_controller_NetworkResetRequest;

typedef struct _reaper_module_controller_ScannerGetStatusRequest {
    char dummy_field;
} reaper_module_controller_ScannerGetStatusRequest;

typedef struct _reaper_module_controller_SensorRequest {
    char dummy_field;
} reaper_module_controller_SensorRequest;

typedef struct _reaper_module_controller_StatusRequest {
    char dummy_field;
} reaper_module_controller_StatusRequest;

typedef struct _reaper_module_controller_StrobeStatusRequest {
    char dummy_field;
} reaper_module_controller_StrobeStatusRequest;

typedef PB_BYTES_ARRAY_T(256) reaper_module_controller_CoreDumpData_data_t;
typedef struct _reaper_module_controller_CoreDumpData {
    bool is_last;
    uint32_t offset;
    reaper_module_controller_CoreDumpData_data_t data;
} reaper_module_controller_CoreDumpData;

typedef struct _reaper_module_controller_FanSetRequest {
    pb_size_t which__fan1;
    union {
        bool fan1;
    } _fan1;
    pb_size_t which__fan2;
    union {
        bool fan2;
    } _fan2;
} reaper_module_controller_FanSetRequest;

typedef struct _reaper_module_controller_ModuleIdentity {
    uint32_t number;
} reaper_module_controller_ModuleIdentity;

typedef struct _reaper_module_controller_NetworkAddress {
    reaper_module_controller_NetworkAddressSource source;
    pb_byte_t unicast[4];
    pb_byte_t subnet[4];
    pb_size_t which__gateway;
    union {
        pb_byte_t gateway[4];
    } _gateway;
} reaper_module_controller_NetworkAddress;

typedef struct _reaper_module_controller_NetworkRequest {
    pb_size_t which_request;
    union {
        reaper_module_controller_NetworkConfigRequest config;
        reaper_module_controller_NetworkResetRequest reset;
        reaper_module_controller_NetworkPowerCycleRequest powerCycle;
    } request;
} reaper_module_controller_NetworkRequest;

typedef struct _reaper_module_controller_PowerReply {
    bool relayBoard;
    bool strobeBoard;
    bool ethSwitch;
    bool predictCam;
} reaper_module_controller_PowerReply;

typedef struct _reaper_module_controller_PowerRequest {
    pb_size_t which__relayBoard;
    union {
        bool relayBoard;
    } _relayBoard;
    pb_size_t which__strobeBoard;
    union {
        bool strobeBoard;
    } _strobeBoard;
    pb_size_t which__ethSwitch;
    union {
        bool ethSwitch;
    } _ethSwitch;
    pb_size_t which__predictCam;
    union {
        bool predictCam;
    } _predictCam;
} reaper_module_controller_PowerRequest;

typedef struct _reaper_module_controller_RelayReply {
    bool pc;
    bool btl;
    bool laser;
} reaper_module_controller_RelayReply;

typedef struct _reaper_module_controller_RelayRequest {
    pb_size_t which__pc;
    union {
        bool pc;
    } _pc;
    pb_size_t which__btl;
    union {
        bool btl;
    } _btl;
    pb_size_t which__laser;
    union {
        bool laser;
    } _laser;
} reaper_module_controller_RelayRequest;

typedef struct _reaper_module_controller_ScannerResetOcpRequest {
    pb_size_t which__scannerA;
    union {
        bool scannerA;
    } _scannerA;
    pb_size_t which__scannerB;
    union {
        bool scannerB;
    } _scannerB;
} reaper_module_controller_ScannerResetOcpRequest;

typedef struct _reaper_module_controller_ScannerSetPowerRequest {
    pb_size_t which__scannerA;
    union {
        bool scannerA;
    } _scannerA;
    pb_size_t which__scannerB;
    union {
        bool scannerB;
    } _scannerB;
} reaper_module_controller_ScannerSetPowerRequest;

typedef struct _reaper_module_controller_ScannerStatus {
    bool powerEnabled;
    bool fuseBlown;
    uint64_t fuseTimestamp;
    float current;
    uint64_t currentTimestamp;
} reaper_module_controller_ScannerStatus;

typedef struct _reaper_module_controller_SensorReply_envdata {
    uint64_t timestamp;
    float temp;
    float humidity;
    float pressure;
} reaper_module_controller_SensorReply_envdata;

typedef struct _reaper_module_controller_SensorReply_imudata {
    uint64_t timestamp;
    pb_size_t accel_count;
    float accel[3];
    pb_size_t gyro_count;
    float gyro[3];
} reaper_module_controller_SensorReply_imudata;

typedef struct _reaper_module_controller_SensorReply_leakdata {
    uint64_t timestamp;
    bool active;
} reaper_module_controller_SensorReply_leakdata;

typedef struct _reaper_module_controller_SensorReply_pressdata {
    uint64_t timestamp;
    float pressure;
    float temperature;
} reaper_module_controller_SensorReply_pressdata;

typedef struct _reaper_module_controller_SensorReply_thermdata {
    uint64_t timestamp;
    float temp;
} reaper_module_controller_SensorReply_thermdata;

typedef struct _reaper_module_controller_SetBoardIdRequest {
    pb_size_t which__cbsn;
    union {
        char cbsn[16];
    } _cbsn;
    pb_size_t which__assySn;
    union {
        char assySn[32];
    } _assySn;
} reaper_module_controller_SetBoardIdRequest;

typedef struct _reaper_module_controller_SetOtpLockRequest {
    bool lock;
    uint32_t key;
} reaper_module_controller_SetOtpLockRequest;

typedef struct _reaper_module_controller_SetStrobeStateRequest {
    bool enabled;
} reaper_module_controller_SetStrobeStateRequest;

typedef struct _reaper_module_controller_StrobeStatusReply {
    float voltage;
    uint64_t voltageTimestamp;
    float current;
    uint64_t currentTimestamp;
    float temperature;
    uint64_t temperatureTimestamp;
    bool ready;
    bool enabled;
    bool firing;
    pb_size_t which__exposureUs;
    union {
        uint32_t exposureUs;
    } _exposureUs;
    pb_size_t which__periodUs;
    union {
        uint32_t periodUs;
    } _periodUs;
    pb_size_t which__targetsPerPredict;
    union {
        uint32_t targetsPerPredict;
    } _targetsPerPredict;
} reaper_module_controller_StrobeStatusReply;

typedef struct _reaper_module_controller_ThermostatConfig {
    float setpoint;
    float hysteresis;
    reaper_module_controller_ThermostatSource source;
} reaper_module_controller_ThermostatConfig;

typedef struct _reaper_module_controller_TimedStrobeDisableRequest {
    uint32_t durationMsec;
} reaper_module_controller_TimedStrobeDisableRequest;

typedef struct _reaper_module_controller_ConfigReply {
    pb_size_t which_reply;
    union {
        reaper_module_controller_ModuleIdentity identity;
    } reply;
} reaper_module_controller_ConfigReply;

typedef struct _reaper_module_controller_ConfigRequest {
    pb_size_t which_request;
    union {
        reaper_module_controller_GetModuleIdentityRequest getIdentity;
        reaper_module_controller_ModuleIdentity setIdentity;
        reaper_module_controller_SetOtpLockRequest otpLock;
        reaper_module_controller_SetBoardIdRequest setBoardIdentity;
    } request;
} reaper_module_controller_ConfigRequest;

typedef struct _reaper_module_controller_CoreDumpReply {
    pb_size_t which_payload;
    union {
        reaper_module_controller_CoreDumpStart start;
        reaper_module_controller_CoreDumpEnd end;
        reaper_module_controller_CoreDumpData data;
    } payload;
} reaper_module_controller_CoreDumpReply;

typedef struct _reaper_module_controller_FanReply {
    bool fan1;
    bool fan2;
    bool thermostatEnabled;
    pb_size_t which__thermostatConfig;
    union {
        reaper_module_controller_ThermostatConfig thermostatConfig;
    } _thermostatConfig;
    pb_size_t which__thermostatActual;
    union {
        float thermostatActual;
    } _thermostatActual;
} reaper_module_controller_FanReply;

typedef struct _reaper_module_controller_FanThermostatConfigRequest {
    pb_size_t which__enabled;
    union {
        bool enabled;
    } _enabled;
    pb_size_t which__config;
    union {
        reaper_module_controller_ThermostatConfig config;
    } _config;
} reaper_module_controller_FanThermostatConfigRequest;

typedef struct _reaper_module_controller_NetworkConfigReply {
    bool linkUp;
    pb_byte_t mac[6];
    pb_size_t addresses_count;
    reaper_module_controller_NetworkAddress addresses[4];
} reaper_module_controller_NetworkConfigReply;

typedef struct _reaper_module_controller_ScannerRequest {
    pb_size_t which_request;
    union {
        reaper_module_controller_ScannerGetStatusRequest status;
        reaper_module_controller_ScannerSetPowerRequest power;
        reaper_module_controller_ScannerResetOcpRequest ocp;
    } request;
} reaper_module_controller_ScannerRequest;

typedef struct _reaper_module_controller_ScannerStatusReply {
    pb_size_t data_count;
    reaper_module_controller_ScannerStatus data[2];
} reaper_module_controller_ScannerStatusReply;

typedef struct _reaper_module_controller_SensorReply {
    pb_size_t env_count;
    reaper_module_controller_SensorReply_envdata env[2];
    bool has_imu;
    reaper_module_controller_SensorReply_imudata imu;
    pb_size_t therm_count;
    reaper_module_controller_SensorReply_thermdata therm[2];
    pb_size_t leak_count;
    reaper_module_controller_SensorReply_leakdata leak[2];
    pb_size_t press_count;
    reaper_module_controller_SensorReply_pressdata press[2];
    bool hasExternalEnv;
} reaper_module_controller_SensorReply;

typedef struct _reaper_module_controller_StrobeReply {
    pb_size_t which_reply;
    union {
        reaper_module_controller_StrobeStatusReply status;
    } reply;
} reaper_module_controller_StrobeReply;

typedef struct _reaper_module_controller_StrobeRequest {
    pb_size_t which_request;
    union {
        strobe_control_Request setWaveform;
        reaper_module_controller_StrobeStatusRequest getStatus;
        reaper_module_controller_SetStrobeStateRequest setState;
        reaper_module_controller_TimedStrobeDisableRequest timedDisable;
    } request;
} reaper_module_controller_StrobeRequest;

typedef struct _reaper_module_controller_FanRequest {
    pb_size_t which_request;
    union {
        reaper_module_controller_FanSetRequest set;
        reaper_module_controller_FanThermostatConfigRequest thermoConfig;
    } request;
} reaper_module_controller_FanRequest;

typedef struct _reaper_module_controller_NetworkReply {
    pb_size_t which_reply;
    union {
        reaper_module_controller_NetworkConfigReply config;
    } reply;
} reaper_module_controller_NetworkReply;

typedef struct _reaper_module_controller_OobRequest {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        version_Version_Request version;
        version_Reset_Request reset;
        reaper_module_controller_ConfigRequest config;
        reaper_module_controller_NetworkRequest network;
        hwinfo_Request hwinfo;
        reaper_module_controller_StrobeRequest strobe;
        reaper_module_controller_PowerRequest power;
        reaper_module_controller_RelayRequest relay;
    } request;
} reaper_module_controller_OobRequest;

typedef struct _reaper_module_controller_ScannerReply {
    pb_size_t which_reply;
    union {
        reaper_module_controller_ScannerStatusReply status;
    } reply;
} reaper_module_controller_ScannerReply;

typedef struct _reaper_module_controller_StatusReply {
    bool has_sensors;
    reaper_module_controller_SensorReply sensors;
    bool has_relays;
    reaper_module_controller_RelayReply relays;
    bool has_power;
    reaper_module_controller_PowerReply power;
    bool has_strobe;
    reaper_module_controller_StrobeStatusReply strobe;
    pb_size_t scanners_count;
    reaper_module_controller_ScannerStatus scanners[2];
} reaper_module_controller_StatusReply;

typedef struct _reaper_module_controller_OobReply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        diagnostic_Pong pong;
        version_Version_Reply version;
        reaper_module_controller_ConfigReply config;
        reaper_module_controller_NetworkReply network;
        hwinfo_Reply hwinfo;
        reaper_module_controller_StrobeReply strobe;
        reaper_module_controller_PowerReply power;
        reaper_module_controller_RelayReply relay;
        reaper_module_controller_CoreDumpReply coredump;
    } reply;
} reaper_module_controller_OobReply;

typedef struct _reaper_module_controller_UdpReply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        diagnostic_Pong pong;
        version_Version_Reply version;
        time_Reply time;
        reaper_module_controller_SensorReply sensor;
        reaper_module_controller_RelayReply relay;
        reaper_module_controller_FanReply fan;
        reaper_module_controller_ConfigReply config;
        reaper_module_controller_StrobeReply strobe;
        reaper_module_controller_PowerReply power;
        reaper_module_controller_ScannerReply scanner;
        reaper_module_controller_NetworkReply network;
        hwinfo_Reply hwinfo;
        reaper_module_controller_StatusReply status;
    } reply;
} reaper_module_controller_UdpReply;

typedef struct _reaper_module_controller_UdpRequest {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        version_Version_Request version;
        version_Reset_Request reset;
        time_Request time;
        reaper_module_controller_SensorRequest sensor;
        reaper_module_controller_RelayRequest relay;
        reaper_module_controller_FanRequest fan;
        reaper_module_controller_ConfigRequest config;
        reaper_module_controller_StrobeRequest strobe;
        reaper_module_controller_PowerRequest power;
        reaper_module_controller_ScannerRequest scanner;
        reaper_module_controller_NetworkRequest network;
        hwinfo_Request hwinfo;
        reaper_module_controller_StatusRequest status;
    } request;
} reaper_module_controller_UdpRequest;


/* Helper constants for enums */
#define _reaper_module_controller_NetworkAddressSource_MIN reaper_module_controller_NetworkAddressSource_MANUAL
#define _reaper_module_controller_NetworkAddressSource_MAX reaper_module_controller_NetworkAddressSource_DHCP
#define _reaper_module_controller_NetworkAddressSource_ARRAYSIZE ((reaper_module_controller_NetworkAddressSource)(reaper_module_controller_NetworkAddressSource_DHCP+1))

#define _reaper_module_controller_ThermostatSource_MIN reaper_module_controller_ThermostatSource_DEFAULT
#define _reaper_module_controller_ThermostatSource_MAX reaper_module_controller_ThermostatSource_ENVIRO_EXTERNAL
#define _reaper_module_controller_ThermostatSource_ARRAYSIZE ((reaper_module_controller_ThermostatSource)(reaper_module_controller_ThermostatSource_ENVIRO_EXTERNAL+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define reaper_module_controller_NetworkConfigRequest_init_default {0}
#define reaper_module_controller_NetworkResetRequest_init_default {0}
#define reaper_module_controller_NetworkPowerCycleRequest_init_default {0}
#define reaper_module_controller_NetworkRequest_init_default {0, {reaper_module_controller_NetworkConfigRequest_init_default}}
#define reaper_module_controller_NetworkAddress_init_default {_reaper_module_controller_NetworkAddressSource_MIN, {0}, {0}, 0, {{0}}}
#define reaper_module_controller_NetworkConfigReply_init_default {0, {0}, 0, {reaper_module_controller_NetworkAddress_init_default, reaper_module_controller_NetworkAddress_init_default, reaper_module_controller_NetworkAddress_init_default, reaper_module_controller_NetworkAddress_init_default}}
#define reaper_module_controller_NetworkReply_init_default {0, {reaper_module_controller_NetworkConfigReply_init_default}}
#define reaper_module_controller_ScannerStatus_init_default {0, 0, 0, 0, 0}
#define reaper_module_controller_ScannerGetStatusRequest_init_default {0}
#define reaper_module_controller_ScannerSetPowerRequest_init_default {0, {0}, 0, {0}}
#define reaper_module_controller_ScannerResetOcpRequest_init_default {0, {0}, 0, {0}}
#define reaper_module_controller_ScannerRequest_init_default {0, {reaper_module_controller_ScannerGetStatusRequest_init_default}}
#define reaper_module_controller_ScannerStatusReply_init_default {0, {reaper_module_controller_ScannerStatus_init_default, reaper_module_controller_ScannerStatus_init_default}}
#define reaper_module_controller_ScannerReply_init_default {0, {reaper_module_controller_ScannerStatusReply_init_default}}
#define reaper_module_controller_PowerRequest_init_default {0, {0}, 0, {0}, 0, {0}, 0, {0}}
#define reaper_module_controller_PowerReply_init_default {0, 0, 0, 0}
#define reaper_module_controller_StrobeStatusRequest_init_default {0}
#define reaper_module_controller_SetStrobeStateRequest_init_default {0}
#define reaper_module_controller_TimedStrobeDisableRequest_init_default {0}
#define reaper_module_controller_StrobeStatusReply_init_default {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, {0}, 0, {0}, 0, {0}}
#define reaper_module_controller_StrobeRequest_init_default {0, {strobe_control_Request_init_default}}
#define reaper_module_controller_StrobeReply_init_default {0, {reaper_module_controller_StrobeStatusReply_init_default}}
#define reaper_module_controller_ModuleIdentity_init_default {0}
#define reaper_module_controller_GetModuleIdentityRequest_init_default {0}
#define reaper_module_controller_SetOtpLockRequest_init_default {0, 0}
#define reaper_module_controller_SetBoardIdRequest_init_default {0, {""}, 0, {""}}
#define reaper_module_controller_ConfigRequest_init_default {0, {reaper_module_controller_GetModuleIdentityRequest_init_default}}
#define reaper_module_controller_ConfigReply_init_default {0, {reaper_module_controller_ModuleIdentity_init_default}}
#define reaper_module_controller_ThermostatConfig_init_default {0, 0, _reaper_module_controller_ThermostatSource_MIN}
#define reaper_module_controller_FanSetRequest_init_default {0, {0}, 0, {0}}
#define reaper_module_controller_FanThermostatConfigRequest_init_default {0, {0}, 0, {reaper_module_controller_ThermostatConfig_init_default}}
#define reaper_module_controller_FanRequest_init_default {0, {reaper_module_controller_FanSetRequest_init_default}}
#define reaper_module_controller_FanReply_init_default {0, 0, 0, 0, {reaper_module_controller_ThermostatConfig_init_default}, 0, {0}}
#define reaper_module_controller_RelayRequest_init_default {0, {0}, 0, {0}, 0, {0}}
#define reaper_module_controller_RelayReply_init_default {0, 0, 0}
#define reaper_module_controller_SensorRequest_init_default {0}
#define reaper_module_controller_SensorReply_init_default {0, {reaper_module_controller_SensorReply_envdata_init_default, reaper_module_controller_SensorReply_envdata_init_default}, false, reaper_module_controller_SensorReply_imudata_init_default, 0, {reaper_module_controller_SensorReply_thermdata_init_default, reaper_module_controller_SensorReply_thermdata_init_default}, 0, {reaper_module_controller_SensorReply_leakdata_init_default, reaper_module_controller_SensorReply_leakdata_init_default}, 0, {reaper_module_controller_SensorReply_pressdata_init_default, reaper_module_controller_SensorReply_pressdata_init_default}, 0}
#define reaper_module_controller_SensorReply_envdata_init_default {0, 0, 0, 0}
#define reaper_module_controller_SensorReply_imudata_init_default {0, 0, {0, 0, 0}, 0, {0, 0, 0}}
#define reaper_module_controller_SensorReply_thermdata_init_default {0, 0}
#define reaper_module_controller_SensorReply_leakdata_init_default {0, 0}
#define reaper_module_controller_SensorReply_pressdata_init_default {0, 0, 0}
#define reaper_module_controller_StatusRequest_init_default {0}
#define reaper_module_controller_StatusReply_init_default {false, reaper_module_controller_SensorReply_init_default, false, reaper_module_controller_RelayReply_init_default, false, reaper_module_controller_PowerReply_init_default, false, reaper_module_controller_StrobeStatusReply_init_default, 0, {reaper_module_controller_ScannerStatus_init_default, reaper_module_controller_ScannerStatus_init_default}}
#define reaper_module_controller_CoreDumpStart_init_default {0}
#define reaper_module_controller_CoreDumpEnd_init_default {0}
#define reaper_module_controller_CoreDumpData_init_default {0, 0, {0, {0}}}
#define reaper_module_controller_CoreDumpReply_init_default {0, {reaper_module_controller_CoreDumpStart_init_default}}
#define reaper_module_controller_UdpRequest_init_default {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define reaper_module_controller_UdpReply_init_default {false, request_RequestHeader_init_default, 0, {error_Error_init_default}}
#define reaper_module_controller_OobRequest_init_default {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define reaper_module_controller_OobReply_init_default {false, request_RequestHeader_init_default, 0, {error_Error_init_default}}
#define reaper_module_controller_NetworkConfigRequest_init_zero {0}
#define reaper_module_controller_NetworkResetRequest_init_zero {0}
#define reaper_module_controller_NetworkPowerCycleRequest_init_zero {0}
#define reaper_module_controller_NetworkRequest_init_zero {0, {reaper_module_controller_NetworkConfigRequest_init_zero}}
#define reaper_module_controller_NetworkAddress_init_zero {_reaper_module_controller_NetworkAddressSource_MIN, {0}, {0}, 0, {{0}}}
#define reaper_module_controller_NetworkConfigReply_init_zero {0, {0}, 0, {reaper_module_controller_NetworkAddress_init_zero, reaper_module_controller_NetworkAddress_init_zero, reaper_module_controller_NetworkAddress_init_zero, reaper_module_controller_NetworkAddress_init_zero}}
#define reaper_module_controller_NetworkReply_init_zero {0, {reaper_module_controller_NetworkConfigReply_init_zero}}
#define reaper_module_controller_ScannerStatus_init_zero {0, 0, 0, 0, 0}
#define reaper_module_controller_ScannerGetStatusRequest_init_zero {0}
#define reaper_module_controller_ScannerSetPowerRequest_init_zero {0, {0}, 0, {0}}
#define reaper_module_controller_ScannerResetOcpRequest_init_zero {0, {0}, 0, {0}}
#define reaper_module_controller_ScannerRequest_init_zero {0, {reaper_module_controller_ScannerGetStatusRequest_init_zero}}
#define reaper_module_controller_ScannerStatusReply_init_zero {0, {reaper_module_controller_ScannerStatus_init_zero, reaper_module_controller_ScannerStatus_init_zero}}
#define reaper_module_controller_ScannerReply_init_zero {0, {reaper_module_controller_ScannerStatusReply_init_zero}}
#define reaper_module_controller_PowerRequest_init_zero {0, {0}, 0, {0}, 0, {0}, 0, {0}}
#define reaper_module_controller_PowerReply_init_zero {0, 0, 0, 0}
#define reaper_module_controller_StrobeStatusRequest_init_zero {0}
#define reaper_module_controller_SetStrobeStateRequest_init_zero {0}
#define reaper_module_controller_TimedStrobeDisableRequest_init_zero {0}
#define reaper_module_controller_StrobeStatusReply_init_zero {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, {0}, 0, {0}, 0, {0}}
#define reaper_module_controller_StrobeRequest_init_zero {0, {strobe_control_Request_init_zero}}
#define reaper_module_controller_StrobeReply_init_zero {0, {reaper_module_controller_StrobeStatusReply_init_zero}}
#define reaper_module_controller_ModuleIdentity_init_zero {0}
#define reaper_module_controller_GetModuleIdentityRequest_init_zero {0}
#define reaper_module_controller_SetOtpLockRequest_init_zero {0, 0}
#define reaper_module_controller_SetBoardIdRequest_init_zero {0, {""}, 0, {""}}
#define reaper_module_controller_ConfigRequest_init_zero {0, {reaper_module_controller_GetModuleIdentityRequest_init_zero}}
#define reaper_module_controller_ConfigReply_init_zero {0, {reaper_module_controller_ModuleIdentity_init_zero}}
#define reaper_module_controller_ThermostatConfig_init_zero {0, 0, _reaper_module_controller_ThermostatSource_MIN}
#define reaper_module_controller_FanSetRequest_init_zero {0, {0}, 0, {0}}
#define reaper_module_controller_FanThermostatConfigRequest_init_zero {0, {0}, 0, {reaper_module_controller_ThermostatConfig_init_zero}}
#define reaper_module_controller_FanRequest_init_zero {0, {reaper_module_controller_FanSetRequest_init_zero}}
#define reaper_module_controller_FanReply_init_zero {0, 0, 0, 0, {reaper_module_controller_ThermostatConfig_init_zero}, 0, {0}}
#define reaper_module_controller_RelayRequest_init_zero {0, {0}, 0, {0}, 0, {0}}
#define reaper_module_controller_RelayReply_init_zero {0, 0, 0}
#define reaper_module_controller_SensorRequest_init_zero {0}
#define reaper_module_controller_SensorReply_init_zero {0, {reaper_module_controller_SensorReply_envdata_init_zero, reaper_module_controller_SensorReply_envdata_init_zero}, false, reaper_module_controller_SensorReply_imudata_init_zero, 0, {reaper_module_controller_SensorReply_thermdata_init_zero, reaper_module_controller_SensorReply_thermdata_init_zero}, 0, {reaper_module_controller_SensorReply_leakdata_init_zero, reaper_module_controller_SensorReply_leakdata_init_zero}, 0, {reaper_module_controller_SensorReply_pressdata_init_zero, reaper_module_controller_SensorReply_pressdata_init_zero}, 0}
#define reaper_module_controller_SensorReply_envdata_init_zero {0, 0, 0, 0}
#define reaper_module_controller_SensorReply_imudata_init_zero {0, 0, {0, 0, 0}, 0, {0, 0, 0}}
#define reaper_module_controller_SensorReply_thermdata_init_zero {0, 0}
#define reaper_module_controller_SensorReply_leakdata_init_zero {0, 0}
#define reaper_module_controller_SensorReply_pressdata_init_zero {0, 0, 0}
#define reaper_module_controller_StatusRequest_init_zero {0}
#define reaper_module_controller_StatusReply_init_zero {false, reaper_module_controller_SensorReply_init_zero, false, reaper_module_controller_RelayReply_init_zero, false, reaper_module_controller_PowerReply_init_zero, false, reaper_module_controller_StrobeStatusReply_init_zero, 0, {reaper_module_controller_ScannerStatus_init_zero, reaper_module_controller_ScannerStatus_init_zero}}
#define reaper_module_controller_CoreDumpStart_init_zero {0}
#define reaper_module_controller_CoreDumpEnd_init_zero {0}
#define reaper_module_controller_CoreDumpData_init_zero {0, 0, {0, {0}}}
#define reaper_module_controller_CoreDumpReply_init_zero {0, {reaper_module_controller_CoreDumpStart_init_zero}}
#define reaper_module_controller_UdpRequest_init_zero {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}
#define reaper_module_controller_UdpReply_init_zero {false, request_RequestHeader_init_zero, 0, {error_Error_init_zero}}
#define reaper_module_controller_OobRequest_init_zero {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}
#define reaper_module_controller_OobReply_init_zero {false, request_RequestHeader_init_zero, 0, {error_Error_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define reaper_module_controller_CoreDumpData_is_last_tag 1
#define reaper_module_controller_CoreDumpData_offset_tag 2
#define reaper_module_controller_CoreDumpData_data_tag 3
#define reaper_module_controller_FanSetRequest_fan1_tag 1
#define reaper_module_controller_FanSetRequest_fan2_tag 2
#define reaper_module_controller_ModuleIdentity_number_tag 2
#define reaper_module_controller_NetworkAddress_source_tag 1
#define reaper_module_controller_NetworkAddress_unicast_tag 2
#define reaper_module_controller_NetworkAddress_subnet_tag 3
#define reaper_module_controller_NetworkAddress_gateway_tag 4
#define reaper_module_controller_NetworkRequest_config_tag 1
#define reaper_module_controller_NetworkRequest_reset_tag 2
#define reaper_module_controller_NetworkRequest_powerCycle_tag 3
#define reaper_module_controller_PowerReply_relayBoard_tag 1
#define reaper_module_controller_PowerReply_strobeBoard_tag 2
#define reaper_module_controller_PowerReply_ethSwitch_tag 3
#define reaper_module_controller_PowerReply_predictCam_tag 4
#define reaper_module_controller_PowerRequest_relayBoard_tag 1
#define reaper_module_controller_PowerRequest_strobeBoard_tag 2
#define reaper_module_controller_PowerRequest_ethSwitch_tag 3
#define reaper_module_controller_PowerRequest_predictCam_tag 4
#define reaper_module_controller_RelayReply_pc_tag 1
#define reaper_module_controller_RelayReply_btl_tag 2
#define reaper_module_controller_RelayReply_laser_tag 3
#define reaper_module_controller_RelayRequest_pc_tag 1
#define reaper_module_controller_RelayRequest_btl_tag 2
#define reaper_module_controller_RelayRequest_laser_tag 3
#define reaper_module_controller_ScannerResetOcpRequest_scannerA_tag 1
#define reaper_module_controller_ScannerResetOcpRequest_scannerB_tag 2
#define reaper_module_controller_ScannerSetPowerRequest_scannerA_tag 1
#define reaper_module_controller_ScannerSetPowerRequest_scannerB_tag 2
#define reaper_module_controller_ScannerStatus_powerEnabled_tag 1
#define reaper_module_controller_ScannerStatus_fuseBlown_tag 2
#define reaper_module_controller_ScannerStatus_fuseTimestamp_tag 3
#define reaper_module_controller_ScannerStatus_current_tag 4
#define reaper_module_controller_ScannerStatus_currentTimestamp_tag 5
#define reaper_module_controller_SensorReply_envdata_timestamp_tag 1
#define reaper_module_controller_SensorReply_envdata_temp_tag 2
#define reaper_module_controller_SensorReply_envdata_humidity_tag 3
#define reaper_module_controller_SensorReply_envdata_pressure_tag 4
#define reaper_module_controller_SensorReply_imudata_timestamp_tag 1
#define reaper_module_controller_SensorReply_imudata_accel_tag 2
#define reaper_module_controller_SensorReply_imudata_gyro_tag 3
#define reaper_module_controller_SensorReply_leakdata_timestamp_tag 1
#define reaper_module_controller_SensorReply_leakdata_active_tag 2
#define reaper_module_controller_SensorReply_pressdata_timestamp_tag 1
#define reaper_module_controller_SensorReply_pressdata_pressure_tag 2
#define reaper_module_controller_SensorReply_pressdata_temperature_tag 3
#define reaper_module_controller_SensorReply_thermdata_timestamp_tag 1
#define reaper_module_controller_SensorReply_thermdata_temp_tag 2
#define reaper_module_controller_SetBoardIdRequest_cbsn_tag 1
#define reaper_module_controller_SetBoardIdRequest_assySn_tag 2
#define reaper_module_controller_SetOtpLockRequest_lock_tag 1
#define reaper_module_controller_SetOtpLockRequest_key_tag 2
#define reaper_module_controller_SetStrobeStateRequest_enabled_tag 1
#define reaper_module_controller_StrobeStatusReply_voltage_tag 1
#define reaper_module_controller_StrobeStatusReply_voltageTimestamp_tag 2
#define reaper_module_controller_StrobeStatusReply_current_tag 3
#define reaper_module_controller_StrobeStatusReply_currentTimestamp_tag 4
#define reaper_module_controller_StrobeStatusReply_temperature_tag 5
#define reaper_module_controller_StrobeStatusReply_temperatureTimestamp_tag 6
#define reaper_module_controller_StrobeStatusReply_ready_tag 7
#define reaper_module_controller_StrobeStatusReply_enabled_tag 8
#define reaper_module_controller_StrobeStatusReply_firing_tag 9
#define reaper_module_controller_StrobeStatusReply_exposureUs_tag 10
#define reaper_module_controller_StrobeStatusReply_periodUs_tag 11
#define reaper_module_controller_StrobeStatusReply_targetsPerPredict_tag 12
#define reaper_module_controller_ThermostatConfig_setpoint_tag 1
#define reaper_module_controller_ThermostatConfig_hysteresis_tag 2
#define reaper_module_controller_ThermostatConfig_source_tag 3
#define reaper_module_controller_TimedStrobeDisableRequest_durationMsec_tag 1
#define reaper_module_controller_ConfigReply_identity_tag 1
#define reaper_module_controller_ConfigRequest_getIdentity_tag 1
#define reaper_module_controller_ConfigRequest_setIdentity_tag 2
#define reaper_module_controller_ConfigRequest_otpLock_tag 3
#define reaper_module_controller_ConfigRequest_setBoardIdentity_tag 4
#define reaper_module_controller_CoreDumpReply_start_tag 1
#define reaper_module_controller_CoreDumpReply_end_tag 2
#define reaper_module_controller_CoreDumpReply_data_tag 3
#define reaper_module_controller_FanReply_fan1_tag 1
#define reaper_module_controller_FanReply_fan2_tag 2
#define reaper_module_controller_FanReply_thermostatEnabled_tag 3
#define reaper_module_controller_FanReply_thermostatConfig_tag 4
#define reaper_module_controller_FanReply_thermostatActual_tag 5
#define reaper_module_controller_FanThermostatConfigRequest_enabled_tag 1
#define reaper_module_controller_FanThermostatConfigRequest_config_tag 2
#define reaper_module_controller_NetworkConfigReply_linkUp_tag 1
#define reaper_module_controller_NetworkConfigReply_mac_tag 2
#define reaper_module_controller_NetworkConfigReply_addresses_tag 3
#define reaper_module_controller_ScannerRequest_status_tag 1
#define reaper_module_controller_ScannerRequest_power_tag 2
#define reaper_module_controller_ScannerRequest_ocp_tag 3
#define reaper_module_controller_ScannerStatusReply_data_tag 1
#define reaper_module_controller_SensorReply_env_tag 1
#define reaper_module_controller_SensorReply_imu_tag 2
#define reaper_module_controller_SensorReply_therm_tag 3
#define reaper_module_controller_SensorReply_leak_tag 4
#define reaper_module_controller_SensorReply_press_tag 5
#define reaper_module_controller_SensorReply_hasExternalEnv_tag 6
#define reaper_module_controller_StrobeReply_status_tag 1
#define reaper_module_controller_StrobeRequest_setWaveform_tag 1
#define reaper_module_controller_StrobeRequest_getStatus_tag 2
#define reaper_module_controller_StrobeRequest_setState_tag 3
#define reaper_module_controller_StrobeRequest_timedDisable_tag 4
#define reaper_module_controller_FanRequest_set_tag 1
#define reaper_module_controller_FanRequest_thermoConfig_tag 2
#define reaper_module_controller_NetworkReply_config_tag 1
#define reaper_module_controller_OobRequest_header_tag 1
#define reaper_module_controller_OobRequest_ping_tag 2
#define reaper_module_controller_OobRequest_version_tag 3
#define reaper_module_controller_OobRequest_reset_tag 4
#define reaper_module_controller_OobRequest_config_tag 5
#define reaper_module_controller_OobRequest_network_tag 6
#define reaper_module_controller_OobRequest_hwinfo_tag 7
#define reaper_module_controller_OobRequest_strobe_tag 8
#define reaper_module_controller_OobRequest_power_tag 9
#define reaper_module_controller_OobRequest_relay_tag 10
#define reaper_module_controller_ScannerReply_status_tag 1
#define reaper_module_controller_StatusReply_sensors_tag 1
#define reaper_module_controller_StatusReply_relays_tag 2
#define reaper_module_controller_StatusReply_power_tag 3
#define reaper_module_controller_StatusReply_strobe_tag 4
#define reaper_module_controller_StatusReply_scanners_tag 5
#define reaper_module_controller_OobReply_header_tag 1
#define reaper_module_controller_OobReply_error_tag 2
#define reaper_module_controller_OobReply_ack_tag 3
#define reaper_module_controller_OobReply_pong_tag 4
#define reaper_module_controller_OobReply_version_tag 5
#define reaper_module_controller_OobReply_config_tag 6
#define reaper_module_controller_OobReply_network_tag 7
#define reaper_module_controller_OobReply_hwinfo_tag 8
#define reaper_module_controller_OobReply_strobe_tag 9
#define reaper_module_controller_OobReply_power_tag 10
#define reaper_module_controller_OobReply_relay_tag 11
#define reaper_module_controller_OobReply_coredump_tag 12
#define reaper_module_controller_UdpReply_header_tag 1
#define reaper_module_controller_UdpReply_error_tag 2
#define reaper_module_controller_UdpReply_ack_tag 3
#define reaper_module_controller_UdpReply_pong_tag 4
#define reaper_module_controller_UdpReply_version_tag 5
#define reaper_module_controller_UdpReply_time_tag 6
#define reaper_module_controller_UdpReply_sensor_tag 7
#define reaper_module_controller_UdpReply_relay_tag 8
#define reaper_module_controller_UdpReply_fan_tag 9
#define reaper_module_controller_UdpReply_config_tag 10
#define reaper_module_controller_UdpReply_strobe_tag 11
#define reaper_module_controller_UdpReply_power_tag 12
#define reaper_module_controller_UdpReply_scanner_tag 13
#define reaper_module_controller_UdpReply_network_tag 14
#define reaper_module_controller_UdpReply_hwinfo_tag 15
#define reaper_module_controller_UdpReply_status_tag 16
#define reaper_module_controller_UdpRequest_header_tag 1
#define reaper_module_controller_UdpRequest_ping_tag 2
#define reaper_module_controller_UdpRequest_version_tag 3
#define reaper_module_controller_UdpRequest_reset_tag 4
#define reaper_module_controller_UdpRequest_time_tag 5
#define reaper_module_controller_UdpRequest_sensor_tag 6
#define reaper_module_controller_UdpRequest_relay_tag 7
#define reaper_module_controller_UdpRequest_fan_tag 8
#define reaper_module_controller_UdpRequest_config_tag 9
#define reaper_module_controller_UdpRequest_strobe_tag 10
#define reaper_module_controller_UdpRequest_power_tag 11
#define reaper_module_controller_UdpRequest_scanner_tag 12
#define reaper_module_controller_UdpRequest_network_tag 13
#define reaper_module_controller_UdpRequest_hwinfo_tag 14
#define reaper_module_controller_UdpRequest_status_tag 15

/* Struct field encoding specification for nanopb */
#define reaper_module_controller_NetworkConfigRequest_FIELDLIST(X, a) \

#define reaper_module_controller_NetworkConfigRequest_CALLBACK NULL
#define reaper_module_controller_NetworkConfigRequest_DEFAULT NULL

#define reaper_module_controller_NetworkResetRequest_FIELDLIST(X, a) \

#define reaper_module_controller_NetworkResetRequest_CALLBACK NULL
#define reaper_module_controller_NetworkResetRequest_DEFAULT NULL

#define reaper_module_controller_NetworkPowerCycleRequest_FIELDLIST(X, a) \

#define reaper_module_controller_NetworkPowerCycleRequest_CALLBACK NULL
#define reaper_module_controller_NetworkPowerCycleRequest_DEFAULT NULL

#define reaper_module_controller_NetworkRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,config,request.config),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,powerCycle,request.powerCycle),   3)
#define reaper_module_controller_NetworkRequest_CALLBACK NULL
#define reaper_module_controller_NetworkRequest_DEFAULT NULL
#define reaper_module_controller_NetworkRequest_request_config_MSGTYPE reaper_module_controller_NetworkConfigRequest
#define reaper_module_controller_NetworkRequest_request_reset_MSGTYPE reaper_module_controller_NetworkResetRequest
#define reaper_module_controller_NetworkRequest_request_powerCycle_MSGTYPE reaper_module_controller_NetworkPowerCycleRequest

#define reaper_module_controller_NetworkAddress_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    source,            1) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, unicast,           2) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, subnet,            3) \
X(a, STATIC,   ONEOF,    FIXED_LENGTH_BYTES, (_gateway,gateway,_gateway.gateway),   4)
#define reaper_module_controller_NetworkAddress_CALLBACK NULL
#define reaper_module_controller_NetworkAddress_DEFAULT NULL

#define reaper_module_controller_NetworkConfigReply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     linkUp,            1) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, mac,               2) \
X(a, STATIC,   REPEATED, MESSAGE,  addresses,         3)
#define reaper_module_controller_NetworkConfigReply_CALLBACK NULL
#define reaper_module_controller_NetworkConfigReply_DEFAULT NULL
#define reaper_module_controller_NetworkConfigReply_addresses_MSGTYPE reaper_module_controller_NetworkAddress

#define reaper_module_controller_NetworkReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,config,reply.config),   1)
#define reaper_module_controller_NetworkReply_CALLBACK NULL
#define reaper_module_controller_NetworkReply_DEFAULT NULL
#define reaper_module_controller_NetworkReply_reply_config_MSGTYPE reaper_module_controller_NetworkConfigReply

#define reaper_module_controller_ScannerStatus_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     powerEnabled,      1) \
X(a, STATIC,   SINGULAR, BOOL,     fuseBlown,         2) \
X(a, STATIC,   SINGULAR, UINT64,   fuseTimestamp,     3) \
X(a, STATIC,   SINGULAR, FLOAT,    current,           4) \
X(a, STATIC,   SINGULAR, UINT64,   currentTimestamp,   5)
#define reaper_module_controller_ScannerStatus_CALLBACK NULL
#define reaper_module_controller_ScannerStatus_DEFAULT NULL

#define reaper_module_controller_ScannerGetStatusRequest_FIELDLIST(X, a) \

#define reaper_module_controller_ScannerGetStatusRequest_CALLBACK NULL
#define reaper_module_controller_ScannerGetStatusRequest_DEFAULT NULL

#define reaper_module_controller_ScannerSetPowerRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    BOOL,     (_scannerA,scannerA,_scannerA.scannerA),   1) \
X(a, STATIC,   ONEOF,    BOOL,     (_scannerB,scannerB,_scannerB.scannerB),   2)
#define reaper_module_controller_ScannerSetPowerRequest_CALLBACK NULL
#define reaper_module_controller_ScannerSetPowerRequest_DEFAULT NULL

#define reaper_module_controller_ScannerResetOcpRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    BOOL,     (_scannerA,scannerA,_scannerA.scannerA),   1) \
X(a, STATIC,   ONEOF,    BOOL,     (_scannerB,scannerB,_scannerB.scannerB),   2)
#define reaper_module_controller_ScannerResetOcpRequest_CALLBACK NULL
#define reaper_module_controller_ScannerResetOcpRequest_DEFAULT NULL

#define reaper_module_controller_ScannerRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,status,request.status),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,power,request.power),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ocp,request.ocp),   3)
#define reaper_module_controller_ScannerRequest_CALLBACK NULL
#define reaper_module_controller_ScannerRequest_DEFAULT NULL
#define reaper_module_controller_ScannerRequest_request_status_MSGTYPE reaper_module_controller_ScannerGetStatusRequest
#define reaper_module_controller_ScannerRequest_request_power_MSGTYPE reaper_module_controller_ScannerSetPowerRequest
#define reaper_module_controller_ScannerRequest_request_ocp_MSGTYPE reaper_module_controller_ScannerResetOcpRequest

#define reaper_module_controller_ScannerStatusReply_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  data,              1)
#define reaper_module_controller_ScannerStatusReply_CALLBACK NULL
#define reaper_module_controller_ScannerStatusReply_DEFAULT NULL
#define reaper_module_controller_ScannerStatusReply_data_MSGTYPE reaper_module_controller_ScannerStatus

#define reaper_module_controller_ScannerReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,status,reply.status),   1)
#define reaper_module_controller_ScannerReply_CALLBACK NULL
#define reaper_module_controller_ScannerReply_DEFAULT NULL
#define reaper_module_controller_ScannerReply_reply_status_MSGTYPE reaper_module_controller_ScannerStatusReply

#define reaper_module_controller_PowerRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    BOOL,     (_relayBoard,relayBoard,_relayBoard.relayBoard),   1) \
X(a, STATIC,   ONEOF,    BOOL,     (_strobeBoard,strobeBoard,_strobeBoard.strobeBoard),   2) \
X(a, STATIC,   ONEOF,    BOOL,     (_ethSwitch,ethSwitch,_ethSwitch.ethSwitch),   3) \
X(a, STATIC,   ONEOF,    BOOL,     (_predictCam,predictCam,_predictCam.predictCam),   4)
#define reaper_module_controller_PowerRequest_CALLBACK NULL
#define reaper_module_controller_PowerRequest_DEFAULT NULL

#define reaper_module_controller_PowerReply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     relayBoard,        1) \
X(a, STATIC,   SINGULAR, BOOL,     strobeBoard,       2) \
X(a, STATIC,   SINGULAR, BOOL,     ethSwitch,         3) \
X(a, STATIC,   SINGULAR, BOOL,     predictCam,        4)
#define reaper_module_controller_PowerReply_CALLBACK NULL
#define reaper_module_controller_PowerReply_DEFAULT NULL

#define reaper_module_controller_StrobeStatusRequest_FIELDLIST(X, a) \

#define reaper_module_controller_StrobeStatusRequest_CALLBACK NULL
#define reaper_module_controller_StrobeStatusRequest_DEFAULT NULL

#define reaper_module_controller_SetStrobeStateRequest_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     enabled,           1)
#define reaper_module_controller_SetStrobeStateRequest_CALLBACK NULL
#define reaper_module_controller_SetStrobeStateRequest_DEFAULT NULL

#define reaper_module_controller_TimedStrobeDisableRequest_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   durationMsec,      1)
#define reaper_module_controller_TimedStrobeDisableRequest_CALLBACK NULL
#define reaper_module_controller_TimedStrobeDisableRequest_DEFAULT NULL

#define reaper_module_controller_StrobeStatusReply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    voltage,           1) \
X(a, STATIC,   SINGULAR, UINT64,   voltageTimestamp,   2) \
X(a, STATIC,   SINGULAR, FLOAT,    current,           3) \
X(a, STATIC,   SINGULAR, UINT64,   currentTimestamp,   4) \
X(a, STATIC,   SINGULAR, FLOAT,    temperature,       5) \
X(a, STATIC,   SINGULAR, UINT64,   temperatureTimestamp,   6) \
X(a, STATIC,   SINGULAR, BOOL,     ready,             7) \
X(a, STATIC,   SINGULAR, BOOL,     enabled,           8) \
X(a, STATIC,   SINGULAR, BOOL,     firing,            9) \
X(a, STATIC,   ONEOF,    UINT32,   (_exposureUs,exposureUs,_exposureUs.exposureUs),  10) \
X(a, STATIC,   ONEOF,    UINT32,   (_periodUs,periodUs,_periodUs.periodUs),  11) \
X(a, STATIC,   ONEOF,    UINT32,   (_targetsPerPredict,targetsPerPredict,_targetsPerPredict.targetsPerPredict),  12)
#define reaper_module_controller_StrobeStatusReply_CALLBACK NULL
#define reaper_module_controller_StrobeStatusReply_DEFAULT NULL

#define reaper_module_controller_StrobeRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,setWaveform,request.setWaveform),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,getStatus,request.getStatus),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,setState,request.setState),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,timedDisable,request.timedDisable),   4)
#define reaper_module_controller_StrobeRequest_CALLBACK NULL
#define reaper_module_controller_StrobeRequest_DEFAULT NULL
#define reaper_module_controller_StrobeRequest_request_setWaveform_MSGTYPE strobe_control_Request
#define reaper_module_controller_StrobeRequest_request_getStatus_MSGTYPE reaper_module_controller_StrobeStatusRequest
#define reaper_module_controller_StrobeRequest_request_setState_MSGTYPE reaper_module_controller_SetStrobeStateRequest
#define reaper_module_controller_StrobeRequest_request_timedDisable_MSGTYPE reaper_module_controller_TimedStrobeDisableRequest

#define reaper_module_controller_StrobeReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,status,reply.status),   1)
#define reaper_module_controller_StrobeReply_CALLBACK NULL
#define reaper_module_controller_StrobeReply_DEFAULT NULL
#define reaper_module_controller_StrobeReply_reply_status_MSGTYPE reaper_module_controller_StrobeStatusReply

#define reaper_module_controller_ModuleIdentity_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   number,            2)
#define reaper_module_controller_ModuleIdentity_CALLBACK NULL
#define reaper_module_controller_ModuleIdentity_DEFAULT NULL

#define reaper_module_controller_GetModuleIdentityRequest_FIELDLIST(X, a) \

#define reaper_module_controller_GetModuleIdentityRequest_CALLBACK NULL
#define reaper_module_controller_GetModuleIdentityRequest_DEFAULT NULL

#define reaper_module_controller_SetOtpLockRequest_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     lock,              1) \
X(a, STATIC,   SINGULAR, FIXED32,  key,               2)
#define reaper_module_controller_SetOtpLockRequest_CALLBACK NULL
#define reaper_module_controller_SetOtpLockRequest_DEFAULT NULL

#define reaper_module_controller_SetBoardIdRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    STRING,   (_cbsn,cbsn,_cbsn.cbsn),   1) \
X(a, STATIC,   ONEOF,    STRING,   (_assySn,assySn,_assySn.assySn),   2)
#define reaper_module_controller_SetBoardIdRequest_CALLBACK NULL
#define reaper_module_controller_SetBoardIdRequest_DEFAULT NULL

#define reaper_module_controller_ConfigRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,getIdentity,request.getIdentity),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,setIdentity,request.setIdentity),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,otpLock,request.otpLock),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,setBoardIdentity,request.setBoardIdentity),   4)
#define reaper_module_controller_ConfigRequest_CALLBACK NULL
#define reaper_module_controller_ConfigRequest_DEFAULT NULL
#define reaper_module_controller_ConfigRequest_request_getIdentity_MSGTYPE reaper_module_controller_GetModuleIdentityRequest
#define reaper_module_controller_ConfigRequest_request_setIdentity_MSGTYPE reaper_module_controller_ModuleIdentity
#define reaper_module_controller_ConfigRequest_request_otpLock_MSGTYPE reaper_module_controller_SetOtpLockRequest
#define reaper_module_controller_ConfigRequest_request_setBoardIdentity_MSGTYPE reaper_module_controller_SetBoardIdRequest

#define reaper_module_controller_ConfigReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,identity,reply.identity),   1)
#define reaper_module_controller_ConfigReply_CALLBACK NULL
#define reaper_module_controller_ConfigReply_DEFAULT NULL
#define reaper_module_controller_ConfigReply_reply_identity_MSGTYPE reaper_module_controller_ModuleIdentity

#define reaper_module_controller_ThermostatConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    setpoint,          1) \
X(a, STATIC,   SINGULAR, FLOAT,    hysteresis,        2) \
X(a, STATIC,   SINGULAR, UENUM,    source,            3)
#define reaper_module_controller_ThermostatConfig_CALLBACK NULL
#define reaper_module_controller_ThermostatConfig_DEFAULT NULL

#define reaper_module_controller_FanSetRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    BOOL,     (_fan1,fan1,_fan1.fan1),   1) \
X(a, STATIC,   ONEOF,    BOOL,     (_fan2,fan2,_fan2.fan2),   2)
#define reaper_module_controller_FanSetRequest_CALLBACK NULL
#define reaper_module_controller_FanSetRequest_DEFAULT NULL

#define reaper_module_controller_FanThermostatConfigRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    BOOL,     (_enabled,enabled,_enabled.enabled),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_config,config,_config.config),   2)
#define reaper_module_controller_FanThermostatConfigRequest_CALLBACK NULL
#define reaper_module_controller_FanThermostatConfigRequest_DEFAULT NULL
#define reaper_module_controller_FanThermostatConfigRequest__config_config_MSGTYPE reaper_module_controller_ThermostatConfig

#define reaper_module_controller_FanRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set,request.set),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,thermoConfig,request.thermoConfig),   2)
#define reaper_module_controller_FanRequest_CALLBACK NULL
#define reaper_module_controller_FanRequest_DEFAULT NULL
#define reaper_module_controller_FanRequest_request_set_MSGTYPE reaper_module_controller_FanSetRequest
#define reaper_module_controller_FanRequest_request_thermoConfig_MSGTYPE reaper_module_controller_FanThermostatConfigRequest

#define reaper_module_controller_FanReply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     fan1,              1) \
X(a, STATIC,   SINGULAR, BOOL,     fan2,              2) \
X(a, STATIC,   SINGULAR, BOOL,     thermostatEnabled,   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_thermostatConfig,thermostatConfig,_thermostatConfig.thermostatConfig),   4) \
X(a, STATIC,   ONEOF,    FLOAT,    (_thermostatActual,thermostatActual,_thermostatActual.thermostatActual),   5)
#define reaper_module_controller_FanReply_CALLBACK NULL
#define reaper_module_controller_FanReply_DEFAULT NULL
#define reaper_module_controller_FanReply__thermostatConfig_thermostatConfig_MSGTYPE reaper_module_controller_ThermostatConfig

#define reaper_module_controller_RelayRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    BOOL,     (_pc,pc,_pc.pc),   1) \
X(a, STATIC,   ONEOF,    BOOL,     (_btl,btl,_btl.btl),   2) \
X(a, STATIC,   ONEOF,    BOOL,     (_laser,laser,_laser.laser),   3)
#define reaper_module_controller_RelayRequest_CALLBACK NULL
#define reaper_module_controller_RelayRequest_DEFAULT NULL

#define reaper_module_controller_RelayReply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     pc,                1) \
X(a, STATIC,   SINGULAR, BOOL,     btl,               2) \
X(a, STATIC,   SINGULAR, BOOL,     laser,             3)
#define reaper_module_controller_RelayReply_CALLBACK NULL
#define reaper_module_controller_RelayReply_DEFAULT NULL

#define reaper_module_controller_SensorRequest_FIELDLIST(X, a) \

#define reaper_module_controller_SensorRequest_CALLBACK NULL
#define reaper_module_controller_SensorRequest_DEFAULT NULL

#define reaper_module_controller_SensorReply_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  env,               1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  imu,               2) \
X(a, STATIC,   REPEATED, MESSAGE,  therm,             3) \
X(a, STATIC,   REPEATED, MESSAGE,  leak,              4) \
X(a, STATIC,   REPEATED, MESSAGE,  press,             5) \
X(a, STATIC,   SINGULAR, BOOL,     hasExternalEnv,    6)
#define reaper_module_controller_SensorReply_CALLBACK NULL
#define reaper_module_controller_SensorReply_DEFAULT NULL
#define reaper_module_controller_SensorReply_env_MSGTYPE reaper_module_controller_SensorReply_envdata
#define reaper_module_controller_SensorReply_imu_MSGTYPE reaper_module_controller_SensorReply_imudata
#define reaper_module_controller_SensorReply_therm_MSGTYPE reaper_module_controller_SensorReply_thermdata
#define reaper_module_controller_SensorReply_leak_MSGTYPE reaper_module_controller_SensorReply_leakdata
#define reaper_module_controller_SensorReply_press_MSGTYPE reaper_module_controller_SensorReply_pressdata

#define reaper_module_controller_SensorReply_envdata_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp,         1) \
X(a, STATIC,   SINGULAR, FLOAT,    temp,              2) \
X(a, STATIC,   SINGULAR, FLOAT,    humidity,          3) \
X(a, STATIC,   SINGULAR, FLOAT,    pressure,          4)
#define reaper_module_controller_SensorReply_envdata_CALLBACK NULL
#define reaper_module_controller_SensorReply_envdata_DEFAULT NULL

#define reaper_module_controller_SensorReply_imudata_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp,         1) \
X(a, STATIC,   REPEATED, FLOAT,    accel,             2) \
X(a, STATIC,   REPEATED, FLOAT,    gyro,              3)
#define reaper_module_controller_SensorReply_imudata_CALLBACK NULL
#define reaper_module_controller_SensorReply_imudata_DEFAULT NULL

#define reaper_module_controller_SensorReply_thermdata_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp,         1) \
X(a, STATIC,   SINGULAR, FLOAT,    temp,              2)
#define reaper_module_controller_SensorReply_thermdata_CALLBACK NULL
#define reaper_module_controller_SensorReply_thermdata_DEFAULT NULL

#define reaper_module_controller_SensorReply_leakdata_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp,         1) \
X(a, STATIC,   SINGULAR, BOOL,     active,            2)
#define reaper_module_controller_SensorReply_leakdata_CALLBACK NULL
#define reaper_module_controller_SensorReply_leakdata_DEFAULT NULL

#define reaper_module_controller_SensorReply_pressdata_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp,         1) \
X(a, STATIC,   SINGULAR, FLOAT,    pressure,          2) \
X(a, STATIC,   SINGULAR, FLOAT,    temperature,       3)
#define reaper_module_controller_SensorReply_pressdata_CALLBACK NULL
#define reaper_module_controller_SensorReply_pressdata_DEFAULT NULL

#define reaper_module_controller_StatusRequest_FIELDLIST(X, a) \

#define reaper_module_controller_StatusRequest_CALLBACK NULL
#define reaper_module_controller_StatusRequest_DEFAULT NULL

#define reaper_module_controller_StatusReply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  sensors,           1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  relays,            2) \
X(a, STATIC,   OPTIONAL, MESSAGE,  power,             3) \
X(a, STATIC,   OPTIONAL, MESSAGE,  strobe,            4) \
X(a, STATIC,   REPEATED, MESSAGE,  scanners,          5)
#define reaper_module_controller_StatusReply_CALLBACK NULL
#define reaper_module_controller_StatusReply_DEFAULT NULL
#define reaper_module_controller_StatusReply_sensors_MSGTYPE reaper_module_controller_SensorReply
#define reaper_module_controller_StatusReply_relays_MSGTYPE reaper_module_controller_RelayReply
#define reaper_module_controller_StatusReply_power_MSGTYPE reaper_module_controller_PowerReply
#define reaper_module_controller_StatusReply_strobe_MSGTYPE reaper_module_controller_StrobeStatusReply
#define reaper_module_controller_StatusReply_scanners_MSGTYPE reaper_module_controller_ScannerStatus

#define reaper_module_controller_CoreDumpStart_FIELDLIST(X, a) \

#define reaper_module_controller_CoreDumpStart_CALLBACK NULL
#define reaper_module_controller_CoreDumpStart_DEFAULT NULL

#define reaper_module_controller_CoreDumpEnd_FIELDLIST(X, a) \

#define reaper_module_controller_CoreDumpEnd_CALLBACK NULL
#define reaper_module_controller_CoreDumpEnd_DEFAULT NULL

#define reaper_module_controller_CoreDumpData_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     is_last,           1) \
X(a, STATIC,   SINGULAR, UINT32,   offset,            2) \
X(a, STATIC,   SINGULAR, BYTES,    data,              3)
#define reaper_module_controller_CoreDumpData_CALLBACK NULL
#define reaper_module_controller_CoreDumpData_DEFAULT NULL

#define reaper_module_controller_CoreDumpReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (payload,start,payload.start),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (payload,end,payload.end),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (payload,data,payload.data),   3)
#define reaper_module_controller_CoreDumpReply_CALLBACK NULL
#define reaper_module_controller_CoreDumpReply_DEFAULT NULL
#define reaper_module_controller_CoreDumpReply_payload_start_MSGTYPE reaper_module_controller_CoreDumpStart
#define reaper_module_controller_CoreDumpReply_payload_end_MSGTYPE reaper_module_controller_CoreDumpEnd
#define reaper_module_controller_CoreDumpReply_payload_data_MSGTYPE reaper_module_controller_CoreDumpData

#define reaper_module_controller_UdpRequest_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,version,request.version),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,time,request.time),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,sensor,request.sensor),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,relay,request.relay),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,fan,request.fan),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,config,request.config),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,strobe,request.strobe),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,power,request.power),  11) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,scanner,request.scanner),  12) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,network,request.network),  13) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,hwinfo,request.hwinfo),  14) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,status,request.status),  15)
#define reaper_module_controller_UdpRequest_CALLBACK NULL
#define reaper_module_controller_UdpRequest_DEFAULT NULL
#define reaper_module_controller_UdpRequest_header_MSGTYPE request_RequestHeader
#define reaper_module_controller_UdpRequest_request_ping_MSGTYPE diagnostic_Ping
#define reaper_module_controller_UdpRequest_request_version_MSGTYPE version_Version_Request
#define reaper_module_controller_UdpRequest_request_reset_MSGTYPE version_Reset_Request
#define reaper_module_controller_UdpRequest_request_time_MSGTYPE time_Request
#define reaper_module_controller_UdpRequest_request_sensor_MSGTYPE reaper_module_controller_SensorRequest
#define reaper_module_controller_UdpRequest_request_relay_MSGTYPE reaper_module_controller_RelayRequest
#define reaper_module_controller_UdpRequest_request_fan_MSGTYPE reaper_module_controller_FanRequest
#define reaper_module_controller_UdpRequest_request_config_MSGTYPE reaper_module_controller_ConfigRequest
#define reaper_module_controller_UdpRequest_request_strobe_MSGTYPE reaper_module_controller_StrobeRequest
#define reaper_module_controller_UdpRequest_request_power_MSGTYPE reaper_module_controller_PowerRequest
#define reaper_module_controller_UdpRequest_request_scanner_MSGTYPE reaper_module_controller_ScannerRequest
#define reaper_module_controller_UdpRequest_request_network_MSGTYPE reaper_module_controller_NetworkRequest
#define reaper_module_controller_UdpRequest_request_hwinfo_MSGTYPE hwinfo_Request
#define reaper_module_controller_UdpRequest_request_status_MSGTYPE reaper_module_controller_StatusRequest

#define reaper_module_controller_UdpReply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,version,reply.version),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,time,reply.time),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,sensor,reply.sensor),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,relay,reply.relay),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,fan,reply.fan),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,config,reply.config),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,strobe,reply.strobe),  11) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,power,reply.power),  12) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,scanner,reply.scanner),  13) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,network,reply.network),  14) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,hwinfo,reply.hwinfo),  15) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,status,reply.status),  16)
#define reaper_module_controller_UdpReply_CALLBACK NULL
#define reaper_module_controller_UdpReply_DEFAULT NULL
#define reaper_module_controller_UdpReply_header_MSGTYPE request_RequestHeader
#define reaper_module_controller_UdpReply_reply_error_MSGTYPE error_Error
#define reaper_module_controller_UdpReply_reply_ack_MSGTYPE ack_Ack
#define reaper_module_controller_UdpReply_reply_pong_MSGTYPE diagnostic_Pong
#define reaper_module_controller_UdpReply_reply_version_MSGTYPE version_Version_Reply
#define reaper_module_controller_UdpReply_reply_time_MSGTYPE time_Reply
#define reaper_module_controller_UdpReply_reply_sensor_MSGTYPE reaper_module_controller_SensorReply
#define reaper_module_controller_UdpReply_reply_relay_MSGTYPE reaper_module_controller_RelayReply
#define reaper_module_controller_UdpReply_reply_fan_MSGTYPE reaper_module_controller_FanReply
#define reaper_module_controller_UdpReply_reply_config_MSGTYPE reaper_module_controller_ConfigReply
#define reaper_module_controller_UdpReply_reply_strobe_MSGTYPE reaper_module_controller_StrobeReply
#define reaper_module_controller_UdpReply_reply_power_MSGTYPE reaper_module_controller_PowerReply
#define reaper_module_controller_UdpReply_reply_scanner_MSGTYPE reaper_module_controller_ScannerReply
#define reaper_module_controller_UdpReply_reply_network_MSGTYPE reaper_module_controller_NetworkReply
#define reaper_module_controller_UdpReply_reply_hwinfo_MSGTYPE hwinfo_Reply
#define reaper_module_controller_UdpReply_reply_status_MSGTYPE reaper_module_controller_StatusReply

#define reaper_module_controller_OobRequest_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,version,request.version),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,config,request.config),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,network,request.network),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,hwinfo,request.hwinfo),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,strobe,request.strobe),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,power,request.power),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,relay,request.relay),  10)
#define reaper_module_controller_OobRequest_CALLBACK NULL
#define reaper_module_controller_OobRequest_DEFAULT NULL
#define reaper_module_controller_OobRequest_header_MSGTYPE request_RequestHeader
#define reaper_module_controller_OobRequest_request_ping_MSGTYPE diagnostic_Ping
#define reaper_module_controller_OobRequest_request_version_MSGTYPE version_Version_Request
#define reaper_module_controller_OobRequest_request_reset_MSGTYPE version_Reset_Request
#define reaper_module_controller_OobRequest_request_config_MSGTYPE reaper_module_controller_ConfigRequest
#define reaper_module_controller_OobRequest_request_network_MSGTYPE reaper_module_controller_NetworkRequest
#define reaper_module_controller_OobRequest_request_hwinfo_MSGTYPE hwinfo_Request
#define reaper_module_controller_OobRequest_request_strobe_MSGTYPE reaper_module_controller_StrobeRequest
#define reaper_module_controller_OobRequest_request_power_MSGTYPE reaper_module_controller_PowerRequest
#define reaper_module_controller_OobRequest_request_relay_MSGTYPE reaper_module_controller_RelayRequest

#define reaper_module_controller_OobReply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,version,reply.version),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,config,reply.config),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,network,reply.network),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,hwinfo,reply.hwinfo),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,strobe,reply.strobe),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,power,reply.power),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,relay,reply.relay),  11) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,coredump,reply.coredump),  12)
#define reaper_module_controller_OobReply_CALLBACK NULL
#define reaper_module_controller_OobReply_DEFAULT NULL
#define reaper_module_controller_OobReply_header_MSGTYPE request_RequestHeader
#define reaper_module_controller_OobReply_reply_error_MSGTYPE error_Error
#define reaper_module_controller_OobReply_reply_ack_MSGTYPE ack_Ack
#define reaper_module_controller_OobReply_reply_pong_MSGTYPE diagnostic_Pong
#define reaper_module_controller_OobReply_reply_version_MSGTYPE version_Version_Reply
#define reaper_module_controller_OobReply_reply_config_MSGTYPE reaper_module_controller_ConfigReply
#define reaper_module_controller_OobReply_reply_network_MSGTYPE reaper_module_controller_NetworkReply
#define reaper_module_controller_OobReply_reply_hwinfo_MSGTYPE hwinfo_Reply
#define reaper_module_controller_OobReply_reply_strobe_MSGTYPE reaper_module_controller_StrobeReply
#define reaper_module_controller_OobReply_reply_power_MSGTYPE reaper_module_controller_PowerReply
#define reaper_module_controller_OobReply_reply_relay_MSGTYPE reaper_module_controller_RelayReply
#define reaper_module_controller_OobReply_reply_coredump_MSGTYPE reaper_module_controller_CoreDumpReply

extern const pb_msgdesc_t reaper_module_controller_NetworkConfigRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_NetworkResetRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_NetworkPowerCycleRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_NetworkRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_NetworkAddress_msg;
extern const pb_msgdesc_t reaper_module_controller_NetworkConfigReply_msg;
extern const pb_msgdesc_t reaper_module_controller_NetworkReply_msg;
extern const pb_msgdesc_t reaper_module_controller_ScannerStatus_msg;
extern const pb_msgdesc_t reaper_module_controller_ScannerGetStatusRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_ScannerSetPowerRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_ScannerResetOcpRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_ScannerRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_ScannerStatusReply_msg;
extern const pb_msgdesc_t reaper_module_controller_ScannerReply_msg;
extern const pb_msgdesc_t reaper_module_controller_PowerRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_PowerReply_msg;
extern const pb_msgdesc_t reaper_module_controller_StrobeStatusRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_SetStrobeStateRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_TimedStrobeDisableRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_StrobeStatusReply_msg;
extern const pb_msgdesc_t reaper_module_controller_StrobeRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_StrobeReply_msg;
extern const pb_msgdesc_t reaper_module_controller_ModuleIdentity_msg;
extern const pb_msgdesc_t reaper_module_controller_GetModuleIdentityRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_SetOtpLockRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_SetBoardIdRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_ConfigRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_ConfigReply_msg;
extern const pb_msgdesc_t reaper_module_controller_ThermostatConfig_msg;
extern const pb_msgdesc_t reaper_module_controller_FanSetRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_FanThermostatConfigRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_FanRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_FanReply_msg;
extern const pb_msgdesc_t reaper_module_controller_RelayRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_RelayReply_msg;
extern const pb_msgdesc_t reaper_module_controller_SensorRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_SensorReply_msg;
extern const pb_msgdesc_t reaper_module_controller_SensorReply_envdata_msg;
extern const pb_msgdesc_t reaper_module_controller_SensorReply_imudata_msg;
extern const pb_msgdesc_t reaper_module_controller_SensorReply_thermdata_msg;
extern const pb_msgdesc_t reaper_module_controller_SensorReply_leakdata_msg;
extern const pb_msgdesc_t reaper_module_controller_SensorReply_pressdata_msg;
extern const pb_msgdesc_t reaper_module_controller_StatusRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_StatusReply_msg;
extern const pb_msgdesc_t reaper_module_controller_CoreDumpStart_msg;
extern const pb_msgdesc_t reaper_module_controller_CoreDumpEnd_msg;
extern const pb_msgdesc_t reaper_module_controller_CoreDumpData_msg;
extern const pb_msgdesc_t reaper_module_controller_CoreDumpReply_msg;
extern const pb_msgdesc_t reaper_module_controller_UdpRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_UdpReply_msg;
extern const pb_msgdesc_t reaper_module_controller_OobRequest_msg;
extern const pb_msgdesc_t reaper_module_controller_OobReply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define reaper_module_controller_NetworkConfigRequest_fields &reaper_module_controller_NetworkConfigRequest_msg
#define reaper_module_controller_NetworkResetRequest_fields &reaper_module_controller_NetworkResetRequest_msg
#define reaper_module_controller_NetworkPowerCycleRequest_fields &reaper_module_controller_NetworkPowerCycleRequest_msg
#define reaper_module_controller_NetworkRequest_fields &reaper_module_controller_NetworkRequest_msg
#define reaper_module_controller_NetworkAddress_fields &reaper_module_controller_NetworkAddress_msg
#define reaper_module_controller_NetworkConfigReply_fields &reaper_module_controller_NetworkConfigReply_msg
#define reaper_module_controller_NetworkReply_fields &reaper_module_controller_NetworkReply_msg
#define reaper_module_controller_ScannerStatus_fields &reaper_module_controller_ScannerStatus_msg
#define reaper_module_controller_ScannerGetStatusRequest_fields &reaper_module_controller_ScannerGetStatusRequest_msg
#define reaper_module_controller_ScannerSetPowerRequest_fields &reaper_module_controller_ScannerSetPowerRequest_msg
#define reaper_module_controller_ScannerResetOcpRequest_fields &reaper_module_controller_ScannerResetOcpRequest_msg
#define reaper_module_controller_ScannerRequest_fields &reaper_module_controller_ScannerRequest_msg
#define reaper_module_controller_ScannerStatusReply_fields &reaper_module_controller_ScannerStatusReply_msg
#define reaper_module_controller_ScannerReply_fields &reaper_module_controller_ScannerReply_msg
#define reaper_module_controller_PowerRequest_fields &reaper_module_controller_PowerRequest_msg
#define reaper_module_controller_PowerReply_fields &reaper_module_controller_PowerReply_msg
#define reaper_module_controller_StrobeStatusRequest_fields &reaper_module_controller_StrobeStatusRequest_msg
#define reaper_module_controller_SetStrobeStateRequest_fields &reaper_module_controller_SetStrobeStateRequest_msg
#define reaper_module_controller_TimedStrobeDisableRequest_fields &reaper_module_controller_TimedStrobeDisableRequest_msg
#define reaper_module_controller_StrobeStatusReply_fields &reaper_module_controller_StrobeStatusReply_msg
#define reaper_module_controller_StrobeRequest_fields &reaper_module_controller_StrobeRequest_msg
#define reaper_module_controller_StrobeReply_fields &reaper_module_controller_StrobeReply_msg
#define reaper_module_controller_ModuleIdentity_fields &reaper_module_controller_ModuleIdentity_msg
#define reaper_module_controller_GetModuleIdentityRequest_fields &reaper_module_controller_GetModuleIdentityRequest_msg
#define reaper_module_controller_SetOtpLockRequest_fields &reaper_module_controller_SetOtpLockRequest_msg
#define reaper_module_controller_SetBoardIdRequest_fields &reaper_module_controller_SetBoardIdRequest_msg
#define reaper_module_controller_ConfigRequest_fields &reaper_module_controller_ConfigRequest_msg
#define reaper_module_controller_ConfigReply_fields &reaper_module_controller_ConfigReply_msg
#define reaper_module_controller_ThermostatConfig_fields &reaper_module_controller_ThermostatConfig_msg
#define reaper_module_controller_FanSetRequest_fields &reaper_module_controller_FanSetRequest_msg
#define reaper_module_controller_FanThermostatConfigRequest_fields &reaper_module_controller_FanThermostatConfigRequest_msg
#define reaper_module_controller_FanRequest_fields &reaper_module_controller_FanRequest_msg
#define reaper_module_controller_FanReply_fields &reaper_module_controller_FanReply_msg
#define reaper_module_controller_RelayRequest_fields &reaper_module_controller_RelayRequest_msg
#define reaper_module_controller_RelayReply_fields &reaper_module_controller_RelayReply_msg
#define reaper_module_controller_SensorRequest_fields &reaper_module_controller_SensorRequest_msg
#define reaper_module_controller_SensorReply_fields &reaper_module_controller_SensorReply_msg
#define reaper_module_controller_SensorReply_envdata_fields &reaper_module_controller_SensorReply_envdata_msg
#define reaper_module_controller_SensorReply_imudata_fields &reaper_module_controller_SensorReply_imudata_msg
#define reaper_module_controller_SensorReply_thermdata_fields &reaper_module_controller_SensorReply_thermdata_msg
#define reaper_module_controller_SensorReply_leakdata_fields &reaper_module_controller_SensorReply_leakdata_msg
#define reaper_module_controller_SensorReply_pressdata_fields &reaper_module_controller_SensorReply_pressdata_msg
#define reaper_module_controller_StatusRequest_fields &reaper_module_controller_StatusRequest_msg
#define reaper_module_controller_StatusReply_fields &reaper_module_controller_StatusReply_msg
#define reaper_module_controller_CoreDumpStart_fields &reaper_module_controller_CoreDumpStart_msg
#define reaper_module_controller_CoreDumpEnd_fields &reaper_module_controller_CoreDumpEnd_msg
#define reaper_module_controller_CoreDumpData_fields &reaper_module_controller_CoreDumpData_msg
#define reaper_module_controller_CoreDumpReply_fields &reaper_module_controller_CoreDumpReply_msg
#define reaper_module_controller_UdpRequest_fields &reaper_module_controller_UdpRequest_msg
#define reaper_module_controller_UdpReply_fields &reaper_module_controller_UdpReply_msg
#define reaper_module_controller_OobRequest_fields &reaper_module_controller_OobRequest_msg
#define reaper_module_controller_OobReply_fields &reaper_module_controller_OobReply_msg

/* Maximum encoded size of messages (where known) */
#define reaper_module_controller_NetworkConfigRequest_size 0
#define reaper_module_controller_NetworkResetRequest_size 0
#define reaper_module_controller_NetworkPowerCycleRequest_size 0
#define reaper_module_controller_NetworkRequest_size 2
#define reaper_module_controller_NetworkAddress_size 20
#define reaper_module_controller_NetworkConfigReply_size 98
#define reaper_module_controller_NetworkReply_size 100
#define reaper_module_controller_ScannerStatus_size 31
#define reaper_module_controller_ScannerGetStatusRequest_size 0
#define reaper_module_controller_ScannerSetPowerRequest_size 4
#define reaper_module_controller_ScannerResetOcpRequest_size 4
#define reaper_module_controller_ScannerRequest_size 6
#define reaper_module_controller_ScannerStatusReply_size 66
#define reaper_module_controller_ScannerReply_size 68
#define reaper_module_controller_PowerRequest_size 8
#define reaper_module_controller_PowerReply_size 8
#define reaper_module_controller_StrobeStatusRequest_size 0
#define reaper_module_controller_SetStrobeStateRequest_size 2
#define reaper_module_controller_TimedStrobeDisableRequest_size 6
#define reaper_module_controller_StrobeStatusReply_size 72
#if defined(strobe_control_Request_size)
typedef union reaper_module_controller_StrobeRequest_request_size_union {char f1[(6 + strobe_control_Request_size)]; char f0[8];} reaper_module_controller_StrobeRequest_request_size_union;
#define reaper_module_controller_StrobeRequest_size (0 + sizeof(reaper_module_controller_StrobeRequest_request_size_union))
#endif
#define reaper_module_controller_StrobeReply_size 74
#define reaper_module_controller_ModuleIdentity_size 6
#define reaper_module_controller_GetModuleIdentityRequest_size 0
#define reaper_module_controller_SetOtpLockRequest_size 7
#define reaper_module_controller_SetBoardIdRequest_size 50
#define reaper_module_controller_ConfigRequest_size 52
#define reaper_module_controller_ConfigReply_size 8
#define reaper_module_controller_ThermostatConfig_size 12
#define reaper_module_controller_FanSetRequest_size 4
#define reaper_module_controller_FanThermostatConfigRequest_size 16
#define reaper_module_controller_FanRequest_size 18
#define reaper_module_controller_FanReply_size   25
#define reaper_module_controller_RelayRequest_size 6
#define reaper_module_controller_RelayReply_size 6
#define reaper_module_controller_SensorRequest_size 0
#define reaper_module_controller_SensorReply_size 213
#define reaper_module_controller_SensorReply_envdata_size 26
#define reaper_module_controller_SensorReply_imudata_size 41
#define reaper_module_controller_SensorReply_thermdata_size 16
#define reaper_module_controller_SensorReply_leakdata_size 13
#define reaper_module_controller_SensorReply_pressdata_size 21
#define reaper_module_controller_StatusRequest_size 0
#define reaper_module_controller_StatusReply_size 374
#define reaper_module_controller_CoreDumpStart_size 0
#define reaper_module_controller_CoreDumpEnd_size 0
#define reaper_module_controller_CoreDumpData_size 267
#define reaper_module_controller_CoreDumpReply_size 270
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size) && defined(version_Version_Request_size) && defined(version_Reset_Request_size) && defined(time_Request_size) && defined(strobe_control_Request_size) && defined(hwinfo_Request_size)
typedef union reaper_module_controller_UdpRequest_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f3[(6 + version_Version_Request_size)]; char f4[(6 + version_Reset_Request_size)]; char f5[(6 + time_Request_size)]; char f10[(6 + sizeof(reaper_module_controller_StrobeRequest_request_size_union))]; char f14[(6 + hwinfo_Request_size)]; char f0[54];} reaper_module_controller_UdpRequest_request_size_union;
#define reaper_module_controller_UdpRequest_size (6 + request_RequestHeader_size + sizeof(reaper_module_controller_UdpRequest_request_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(error_Error_size) && defined(ack_Ack_size) && defined(diagnostic_Pong_size) && defined(version_Version_Reply_size) && defined(time_Reply_size) && defined(hwinfo_Reply_size)
typedef union reaper_module_controller_UdpReply_reply_size_union {char f2[(6 + error_Error_size)]; char f3[(6 + ack_Ack_size)]; char f4[(6 + diagnostic_Pong_size)]; char f5[(6 + version_Version_Reply_size)]; char f6[(6 + time_Reply_size)]; char f15[(6 + hwinfo_Reply_size)]; char f0[378];} reaper_module_controller_UdpReply_reply_size_union;
#define reaper_module_controller_UdpReply_size   (6 + request_RequestHeader_size + sizeof(reaper_module_controller_UdpReply_reply_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size) && defined(version_Version_Request_size) && defined(version_Reset_Request_size) && defined(hwinfo_Request_size) && defined(strobe_control_Request_size)
typedef union reaper_module_controller_OobRequest_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f3[(6 + version_Version_Request_size)]; char f4[(6 + version_Reset_Request_size)]; char f7[(6 + hwinfo_Request_size)]; char f8[(6 + sizeof(reaper_module_controller_StrobeRequest_request_size_union))]; char f0[54];} reaper_module_controller_OobRequest_request_size_union;
#define reaper_module_controller_OobRequest_size (6 + request_RequestHeader_size + sizeof(reaper_module_controller_OobRequest_request_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(error_Error_size) && defined(ack_Ack_size) && defined(diagnostic_Pong_size) && defined(version_Version_Reply_size) && defined(hwinfo_Reply_size)
typedef union reaper_module_controller_OobReply_reply_size_union {char f2[(6 + error_Error_size)]; char f3[(6 + ack_Ack_size)]; char f4[(6 + diagnostic_Pong_size)]; char f5[(6 + version_Version_Reply_size)]; char f8[(6 + hwinfo_Reply_size)]; char f0[273];} reaper_module_controller_OobReply_reply_size_union;
#define reaper_module_controller_OobReply_size   (6 + request_RequestHeader_size + sizeof(reaper_module_controller_OobReply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

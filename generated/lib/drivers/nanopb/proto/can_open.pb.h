/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_CAN_OPEN_CAN_OPEN_PB_H_INCLUDED
#define PB_CAN_OPEN_CAN_OPEN_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _can_open_ACK_Reply {
    char dummy_field;
} can_open_ACK_Reply;

typedef struct _can_open_NMT_Reset_Request {
    char dummy_field;
} can_open_NMT_Reset_Request;

typedef struct _can_open_NMT_Start_Request {
    char dummy_field;
} can_open_NMT_Start_Request;

typedef struct _can_open_NMT_Stop_Request {
    char dummy_field;
} can_open_NMT_Stop_Request;

typedef struct _can_open_Await_Request {
    uint32_t timeout_ms;
    uint32_t func;
} can_open_Await_Request;

typedef struct _can_open_NMT_Packet {
    uint32_t state;
    uint32_t node_id;
} can_open_NMT_Packet;

typedef struct _can_open_NMT_Request {
    uint32_t state;
} can_open_NMT_Request;

typedef struct _can_open_PDO_Packet {
    pb_byte_t data[8];
} can_open_PDO_Packet;

typedef struct _can_open_PDO_Request {
    uint32_t func;
    uint32_t size;
    pb_byte_t data[8];
} can_open_PDO_Request;

typedef struct _can_open_RTR_PDO_Request {
    uint32_t func;
} can_open_RTR_PDO_Request;

typedef struct _can_open_SDO_Download_Request {
    uint32_t index;
    uint32_t subindex;
    uint32_t value;
} can_open_SDO_Download_Request;

typedef struct _can_open_SDO_Packet {
    uint32_t spec;
    uint32_t index;
    uint32_t subindex;
    pb_byte_t data[4];
} can_open_SDO_Packet;

typedef struct _can_open_SDO_Request {
    uint32_t index;
    uint32_t subindex;
    uint32_t value;
    uint32_t cs;
    uint32_t expedited;
} can_open_SDO_Request;

typedef struct _can_open_SDO_Upload_Request {
    uint32_t index;
    uint32_t subindex;
} can_open_SDO_Upload_Request;

typedef struct _can_open_Message_Reply {
    uint32_t func;
    uint32_t node_id;
    pb_size_t which_pkt;
    union {
        can_open_SDO_Packet sdo;
        can_open_PDO_Packet pdo;
        can_open_NMT_Packet nmt;
    } pkt;
} can_open_Message_Reply;

typedef struct _can_open_Request {
    pb_size_t which_request;
    union {
        can_open_SDO_Request sdo;
        can_open_PDO_Request pdo;
        can_open_RTR_PDO_Request rtr;
        can_open_NMT_Request nmt;
        can_open_Await_Request await;
        can_open_SDO_Download_Request sdo_download;
        can_open_SDO_Upload_Request sdo_upload;
        can_open_NMT_Reset_Request reset;
        can_open_NMT_Start_Request start;
        can_open_NMT_Stop_Request stop;
    } request;
} can_open_Request;

typedef struct _can_open_Reply {
    pb_size_t which_reply;
    union {
        ack_Ack ack;
        can_open_Message_Reply msg;
        error_Error error;
    } reply;
} can_open_Reply;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define can_open_SDO_Request_init_default        {0, 0, 0, 0, 0}
#define can_open_PDO_Request_init_default        {0, 0, {0}}
#define can_open_RTR_PDO_Request_init_default    {0}
#define can_open_NMT_Request_init_default        {0}
#define can_open_Await_Request_init_default      {0, 0}
#define can_open_SDO_Download_Request_init_default {0, 0, 0}
#define can_open_SDO_Upload_Request_init_default {0, 0}
#define can_open_NMT_Reset_Request_init_default  {0}
#define can_open_NMT_Start_Request_init_default  {0}
#define can_open_NMT_Stop_Request_init_default   {0}
#define can_open_ACK_Reply_init_default          {0}
#define can_open_SDO_Packet_init_default         {0, 0, 0, {0}}
#define can_open_PDO_Packet_init_default         {{0}}
#define can_open_NMT_Packet_init_default         {0, 0}
#define can_open_Message_Reply_init_default      {0, 0, 0, {can_open_SDO_Packet_init_default}}
#define can_open_Request_init_default            {0, {can_open_SDO_Request_init_default}}
#define can_open_Reply_init_default              {0, {ack_Ack_init_default}}
#define can_open_SDO_Request_init_zero           {0, 0, 0, 0, 0}
#define can_open_PDO_Request_init_zero           {0, 0, {0}}
#define can_open_RTR_PDO_Request_init_zero       {0}
#define can_open_NMT_Request_init_zero           {0}
#define can_open_Await_Request_init_zero         {0, 0}
#define can_open_SDO_Download_Request_init_zero  {0, 0, 0}
#define can_open_SDO_Upload_Request_init_zero    {0, 0}
#define can_open_NMT_Reset_Request_init_zero     {0}
#define can_open_NMT_Start_Request_init_zero     {0}
#define can_open_NMT_Stop_Request_init_zero      {0}
#define can_open_ACK_Reply_init_zero             {0}
#define can_open_SDO_Packet_init_zero            {0, 0, 0, {0}}
#define can_open_PDO_Packet_init_zero            {{0}}
#define can_open_NMT_Packet_init_zero            {0, 0}
#define can_open_Message_Reply_init_zero         {0, 0, 0, {can_open_SDO_Packet_init_zero}}
#define can_open_Request_init_zero               {0, {can_open_SDO_Request_init_zero}}
#define can_open_Reply_init_zero                 {0, {ack_Ack_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define can_open_Await_Request_timeout_ms_tag    1
#define can_open_Await_Request_func_tag          2
#define can_open_NMT_Packet_state_tag            1
#define can_open_NMT_Packet_node_id_tag          2
#define can_open_NMT_Request_state_tag           1
#define can_open_PDO_Packet_data_tag             1
#define can_open_PDO_Request_func_tag            1
#define can_open_PDO_Request_size_tag            2
#define can_open_PDO_Request_data_tag            3
#define can_open_RTR_PDO_Request_func_tag        1
#define can_open_SDO_Download_Request_index_tag  1
#define can_open_SDO_Download_Request_subindex_tag 2
#define can_open_SDO_Download_Request_value_tag  3
#define can_open_SDO_Packet_spec_tag             1
#define can_open_SDO_Packet_index_tag            2
#define can_open_SDO_Packet_subindex_tag         3
#define can_open_SDO_Packet_data_tag             4
#define can_open_SDO_Request_index_tag           1
#define can_open_SDO_Request_subindex_tag        2
#define can_open_SDO_Request_value_tag           3
#define can_open_SDO_Request_cs_tag              4
#define can_open_SDO_Request_expedited_tag       5
#define can_open_SDO_Upload_Request_index_tag    1
#define can_open_SDO_Upload_Request_subindex_tag 2
#define can_open_Message_Reply_func_tag          1
#define can_open_Message_Reply_node_id_tag       2
#define can_open_Message_Reply_sdo_tag           3
#define can_open_Message_Reply_pdo_tag           4
#define can_open_Message_Reply_nmt_tag           5
#define can_open_Request_sdo_tag                 1
#define can_open_Request_pdo_tag                 2
#define can_open_Request_rtr_tag                 3
#define can_open_Request_nmt_tag                 4
#define can_open_Request_await_tag               5
#define can_open_Request_sdo_download_tag        6
#define can_open_Request_sdo_upload_tag          7
#define can_open_Request_reset_tag               8
#define can_open_Request_start_tag               9
#define can_open_Request_stop_tag                10
#define can_open_Reply_ack_tag                   1
#define can_open_Reply_msg_tag                   2
#define can_open_Reply_error_tag                 3

/* Struct field encoding specification for nanopb */
#define can_open_SDO_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   index,             1) \
X(a, STATIC,   SINGULAR, UINT32,   subindex,          2) \
X(a, STATIC,   SINGULAR, UINT32,   value,             3) \
X(a, STATIC,   SINGULAR, UINT32,   cs,                4) \
X(a, STATIC,   SINGULAR, UINT32,   expedited,         5)
#define can_open_SDO_Request_CALLBACK NULL
#define can_open_SDO_Request_DEFAULT NULL

#define can_open_PDO_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   func,              1) \
X(a, STATIC,   SINGULAR, UINT32,   size,              2) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, data,              3)
#define can_open_PDO_Request_CALLBACK NULL
#define can_open_PDO_Request_DEFAULT NULL

#define can_open_RTR_PDO_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   func,              1)
#define can_open_RTR_PDO_Request_CALLBACK NULL
#define can_open_RTR_PDO_Request_DEFAULT NULL

#define can_open_NMT_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   state,             1)
#define can_open_NMT_Request_CALLBACK NULL
#define can_open_NMT_Request_DEFAULT NULL

#define can_open_Await_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   timeout_ms,        1) \
X(a, STATIC,   SINGULAR, UINT32,   func,              2)
#define can_open_Await_Request_CALLBACK NULL
#define can_open_Await_Request_DEFAULT NULL

#define can_open_SDO_Download_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   index,             1) \
X(a, STATIC,   SINGULAR, UINT32,   subindex,          2) \
X(a, STATIC,   SINGULAR, UINT32,   value,             3)
#define can_open_SDO_Download_Request_CALLBACK NULL
#define can_open_SDO_Download_Request_DEFAULT NULL

#define can_open_SDO_Upload_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   index,             1) \
X(a, STATIC,   SINGULAR, UINT32,   subindex,          2)
#define can_open_SDO_Upload_Request_CALLBACK NULL
#define can_open_SDO_Upload_Request_DEFAULT NULL

#define can_open_NMT_Reset_Request_FIELDLIST(X, a) \

#define can_open_NMT_Reset_Request_CALLBACK NULL
#define can_open_NMT_Reset_Request_DEFAULT NULL

#define can_open_NMT_Start_Request_FIELDLIST(X, a) \

#define can_open_NMT_Start_Request_CALLBACK NULL
#define can_open_NMT_Start_Request_DEFAULT NULL

#define can_open_NMT_Stop_Request_FIELDLIST(X, a) \

#define can_open_NMT_Stop_Request_CALLBACK NULL
#define can_open_NMT_Stop_Request_DEFAULT NULL

#define can_open_ACK_Reply_FIELDLIST(X, a) \

#define can_open_ACK_Reply_CALLBACK NULL
#define can_open_ACK_Reply_DEFAULT NULL

#define can_open_SDO_Packet_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   spec,              1) \
X(a, STATIC,   SINGULAR, UINT32,   index,             2) \
X(a, STATIC,   SINGULAR, UINT32,   subindex,          3) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, data,              4)
#define can_open_SDO_Packet_CALLBACK NULL
#define can_open_SDO_Packet_DEFAULT NULL

#define can_open_PDO_Packet_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, data,              1)
#define can_open_PDO_Packet_CALLBACK NULL
#define can_open_PDO_Packet_DEFAULT NULL

#define can_open_NMT_Packet_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   state,             1) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           2)
#define can_open_NMT_Packet_CALLBACK NULL
#define can_open_NMT_Packet_DEFAULT NULL

#define can_open_Message_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   func,              1) \
X(a, STATIC,   SINGULAR, UINT32,   node_id,           2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (pkt,sdo,pkt.sdo),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (pkt,pdo,pkt.pdo),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (pkt,nmt,pkt.nmt),   5)
#define can_open_Message_Reply_CALLBACK NULL
#define can_open_Message_Reply_DEFAULT NULL
#define can_open_Message_Reply_pkt_sdo_MSGTYPE can_open_SDO_Packet
#define can_open_Message_Reply_pkt_pdo_MSGTYPE can_open_PDO_Packet
#define can_open_Message_Reply_pkt_nmt_MSGTYPE can_open_NMT_Packet

#define can_open_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,sdo,request.sdo),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pdo,request.pdo),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,rtr,request.rtr),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,nmt,request.nmt),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,await,request.await),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,sdo_download,request.sdo_download),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,sdo_upload,request.sdo_upload),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,start,request.start),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,stop,request.stop),  10)
#define can_open_Request_CALLBACK NULL
#define can_open_Request_DEFAULT NULL
#define can_open_Request_request_sdo_MSGTYPE can_open_SDO_Request
#define can_open_Request_request_pdo_MSGTYPE can_open_PDO_Request
#define can_open_Request_request_rtr_MSGTYPE can_open_RTR_PDO_Request
#define can_open_Request_request_nmt_MSGTYPE can_open_NMT_Request
#define can_open_Request_request_await_MSGTYPE can_open_Await_Request
#define can_open_Request_request_sdo_download_MSGTYPE can_open_SDO_Download_Request
#define can_open_Request_request_sdo_upload_MSGTYPE can_open_SDO_Upload_Request
#define can_open_Request_request_reset_MSGTYPE can_open_NMT_Reset_Request
#define can_open_Request_request_start_MSGTYPE can_open_NMT_Start_Request
#define can_open_Request_request_stop_MSGTYPE can_open_NMT_Stop_Request

#define can_open_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,msg,reply.msg),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   3)
#define can_open_Reply_CALLBACK NULL
#define can_open_Reply_DEFAULT NULL
#define can_open_Reply_reply_ack_MSGTYPE ack_Ack
#define can_open_Reply_reply_msg_MSGTYPE can_open_Message_Reply
#define can_open_Reply_reply_error_MSGTYPE error_Error

extern const pb_msgdesc_t can_open_SDO_Request_msg;
extern const pb_msgdesc_t can_open_PDO_Request_msg;
extern const pb_msgdesc_t can_open_RTR_PDO_Request_msg;
extern const pb_msgdesc_t can_open_NMT_Request_msg;
extern const pb_msgdesc_t can_open_Await_Request_msg;
extern const pb_msgdesc_t can_open_SDO_Download_Request_msg;
extern const pb_msgdesc_t can_open_SDO_Upload_Request_msg;
extern const pb_msgdesc_t can_open_NMT_Reset_Request_msg;
extern const pb_msgdesc_t can_open_NMT_Start_Request_msg;
extern const pb_msgdesc_t can_open_NMT_Stop_Request_msg;
extern const pb_msgdesc_t can_open_ACK_Reply_msg;
extern const pb_msgdesc_t can_open_SDO_Packet_msg;
extern const pb_msgdesc_t can_open_PDO_Packet_msg;
extern const pb_msgdesc_t can_open_NMT_Packet_msg;
extern const pb_msgdesc_t can_open_Message_Reply_msg;
extern const pb_msgdesc_t can_open_Request_msg;
extern const pb_msgdesc_t can_open_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define can_open_SDO_Request_fields &can_open_SDO_Request_msg
#define can_open_PDO_Request_fields &can_open_PDO_Request_msg
#define can_open_RTR_PDO_Request_fields &can_open_RTR_PDO_Request_msg
#define can_open_NMT_Request_fields &can_open_NMT_Request_msg
#define can_open_Await_Request_fields &can_open_Await_Request_msg
#define can_open_SDO_Download_Request_fields &can_open_SDO_Download_Request_msg
#define can_open_SDO_Upload_Request_fields &can_open_SDO_Upload_Request_msg
#define can_open_NMT_Reset_Request_fields &can_open_NMT_Reset_Request_msg
#define can_open_NMT_Start_Request_fields &can_open_NMT_Start_Request_msg
#define can_open_NMT_Stop_Request_fields &can_open_NMT_Stop_Request_msg
#define can_open_ACK_Reply_fields &can_open_ACK_Reply_msg
#define can_open_SDO_Packet_fields &can_open_SDO_Packet_msg
#define can_open_PDO_Packet_fields &can_open_PDO_Packet_msg
#define can_open_NMT_Packet_fields &can_open_NMT_Packet_msg
#define can_open_Message_Reply_fields &can_open_Message_Reply_msg
#define can_open_Request_fields &can_open_Request_msg
#define can_open_Reply_fields &can_open_Reply_msg

/* Maximum encoded size of messages (where known) */
#define can_open_SDO_Request_size                30
#define can_open_PDO_Request_size                22
#define can_open_RTR_PDO_Request_size            6
#define can_open_NMT_Request_size                6
#define can_open_Await_Request_size              12
#define can_open_SDO_Download_Request_size       18
#define can_open_SDO_Upload_Request_size         12
#define can_open_NMT_Reset_Request_size          0
#define can_open_NMT_Start_Request_size          0
#define can_open_NMT_Stop_Request_size           0
#define can_open_ACK_Reply_size                  0
#define can_open_SDO_Packet_size                 24
#define can_open_PDO_Packet_size                 10
#define can_open_NMT_Packet_size                 12
#define can_open_Message_Reply_size              38
#define can_open_Request_size                    32
#if defined(ack_Ack_size) && defined(error_Error_size)
typedef union can_open_Reply_reply_size_union {char f1[(6 + ack_Ack_size)]; char f3[(6 + error_Error_size)]; char f0[40];} can_open_Reply_reply_size_union;
#define can_open_Reply_size                      (0 + sizeof(can_open_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

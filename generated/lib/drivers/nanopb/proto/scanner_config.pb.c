/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "scanner_config.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(scanner_config_Delta_Target_Config, scanner_config_Delta_Target_Config, AUTO)


PB_BIND(scanner_config_Color_Config, scanner_config_Color_Config, AUTO)


PB_BIND(scanner_config_Camera_Serial_Number_Config, scanner_config_Camera_Serial_Number_Config, AUTO)


PB_BIND(scanner_config_Scanner_Barcode_Str_Config, scanner_config_Scanner_Barcode_Str_Config, AUTO)


PB_BIND(scanner_config_Set_Delta_Target_Config_Request, scanner_config_Set_Delta_Target_Config_Request, AUTO)


PB_BIND(scanner_config_Get_Delta_Target_Config_Request, scanner_config_Get_Delta_Target_Config_Request, AUTO)


PB_BIND(scanner_config_Set_Color_Config_Request, scanner_config_Set_Color_Config_Request, AUTO)


PB_BIND(scanner_config_Get_Color_Config_Request, scanner_config_Get_Color_Config_Request, AUTO)


PB_BIND(scanner_config_Set_Camera_Serial_Number_Config_Request, scanner_config_Set_Camera_Serial_Number_Config_Request, AUTO)


PB_BIND(scanner_config_Get_Camera_Serial_Number_Config_Request, scanner_config_Get_Camera_Serial_Number_Config_Request, AUTO)


PB_BIND(scanner_config_Set_HW_Revision_Request, scanner_config_Set_HW_Revision_Request, AUTO)


PB_BIND(scanner_config_Get_HW_Revision_Request, scanner_config_Get_HW_Revision_Request, AUTO)


PB_BIND(scanner_config_Set_Scanner_Barcode_Str_Request, scanner_config_Set_Scanner_Barcode_Str_Request, AUTO)


PB_BIND(scanner_config_Get_Scanner_Barcode_Str_Request, scanner_config_Get_Scanner_Barcode_Str_Request, AUTO)


PB_BIND(scanner_config_Get_Delta_Target_Config_Reply, scanner_config_Get_Delta_Target_Config_Reply, AUTO)


PB_BIND(scanner_config_Get_Color_Config_Reply, scanner_config_Get_Color_Config_Reply, AUTO)


PB_BIND(scanner_config_Get_Camera_Serial_Number_Config_Reply, scanner_config_Get_Camera_Serial_Number_Config_Reply, AUTO)


PB_BIND(scanner_config_Get_HW_Revision_Reply, scanner_config_Get_HW_Revision_Reply, AUTO)


PB_BIND(scanner_config_Get_Scanner_Barcode_Str_Reply, scanner_config_Get_Scanner_Barcode_Str_Reply, AUTO)


PB_BIND(scanner_config_Request, scanner_config_Request, AUTO)


PB_BIND(scanner_config_Reply, scanner_config_Reply, 2)




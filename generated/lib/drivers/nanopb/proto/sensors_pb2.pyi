"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class FuelGauge_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___FuelGauge_Request = FuelGauge_Request

class FuelGauge_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    value: builtin___float = ...

    def __init__(self,
        *,
        value : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> None: ...
type___FuelGauge_Reply = FuelGauge_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def fuel_gauge(self) -> type___FuelGauge_Request: ...

    def __init__(self,
        *,
        fuel_gauge : typing___Optional[type___FuelGauge_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"fuel_gauge",b"fuel_gauge",u"request",b"request"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"fuel_gauge",b"fuel_gauge",u"request",b"request"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["fuel_gauge"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def fuel_gauge(self) -> type___FuelGauge_Reply: ...

    def __init__(self,
        *,
        fuel_gauge : typing___Optional[type___FuelGauge_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"fuel_gauge",b"fuel_gauge",u"reply",b"reply"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"fuel_gauge",b"fuel_gauge",u"reply",b"reply"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["fuel_gauge"]: ...
type___Reply = Reply

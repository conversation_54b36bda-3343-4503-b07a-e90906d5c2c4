/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_STROBE_CONTROL_STROBE_CONTROL_PB_H_INCLUDED
#define PB_STROBE_CONTROL_STROBE_CONTROL_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _strobe_control_Reply {
    bool ok;
} strobe_control_Reply;

typedef struct _strobe_control_Request {
    uint32_t exposure_us;
    uint32_t period_us;
    uint32_t targets_per_predict_ratio;
} strobe_control_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define strobe_control_Request_init_default      {0, 0, 0}
#define strobe_control_Reply_init_default        {0}
#define strobe_control_Request_init_zero         {0, 0, 0}
#define strobe_control_Reply_init_zero           {0}

/* Field tags (for use in manual encoding/decoding) */
#define strobe_control_Reply_ok_tag              1
#define strobe_control_Request_exposure_us_tag   1
#define strobe_control_Request_period_us_tag     2
#define strobe_control_Request_targets_per_predict_ratio_tag 3

/* Struct field encoding specification for nanopb */
#define strobe_control_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   exposure_us,       1) \
X(a, STATIC,   SINGULAR, UINT32,   period_us,         2) \
X(a, STATIC,   SINGULAR, UINT32,   targets_per_predict_ratio,   3)
#define strobe_control_Request_CALLBACK NULL
#define strobe_control_Request_DEFAULT NULL

#define strobe_control_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     ok,                1)
#define strobe_control_Reply_CALLBACK NULL
#define strobe_control_Reply_DEFAULT NULL

extern const pb_msgdesc_t strobe_control_Request_msg;
extern const pb_msgdesc_t strobe_control_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define strobe_control_Request_fields &strobe_control_Request_msg
#define strobe_control_Reply_fields &strobe_control_Reply_msg

/* Maximum encoded size of messages (where known) */
#define strobe_control_Request_size              18
#define strobe_control_Reply_size                2

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_OTS_TRACTOR_OTS_TRACTOR_PB_H_INCLUDED
#define PB_OTS_TRACTOR_OTS_TRACTOR_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _ots_tractor_TractorVariantType {
    ots_tractor_TractorVariantType_TV_UNKNOWN = 0,
    ots_tractor_TractorVariantType_TV_JD_6LH = 1,
    ots_tractor_TractorVariantType_TV_JD_6LHM = 2,
    ots_tractor_TractorVariantType_TV_JD_6PRO = 3,
    ots_tractor_TractorVariantType_TV_JD_7LH = 4,
    ots_tractor_TractorVariantType_TV_JD_7PRO = 5,
    ots_tractor_TractorVariantType_TV_JD_8RH = 6
} ots_tractor_TractorVariantType;

typedef enum _ots_tractor_Gear {
    ots_tractor_Gear_GEAR_PARK = 0,
    ots_tractor_Gear_GEAR_REVERSE = 1,
    ots_tractor_Gear_GEAR_NEUTRAL = 2,
    ots_tractor_Gear_GEAR_FORWARD = 3,
    ots_tractor_Gear_GEAR_POWERZERO = 4
} ots_tractor_Gear;

typedef enum _ots_tractor_Lights {
    ots_tractor_Lights_LIGHTS_OFF = 0,
    ots_tractor_Lights_LIGHTS_LOW = 1,
    ots_tractor_Lights_LIGHTS_HIGH = 2
} ots_tractor_Lights;

typedef enum _ots_tractor_HitchCmd {
    ots_tractor_HitchCmd_LIFT = 0,
    ots_tractor_HitchCmd_LOWER = 1,
    ots_tractor_HitchCmd_PRECISE = 2
} ots_tractor_HitchCmd;

/* Struct definitions */
typedef struct _ots_tractor_Empty {
    char dummy_field;
} ots_tractor_Empty;

typedef struct _ots_tractor_HitchReply {
    char dummy_field;
} ots_tractor_HitchReply;

typedef struct _ots_tractor_ScvReply {
    char dummy_field;
} ots_tractor_ScvReply;

typedef struct _ots_tractor_EngineRpmState {
    int32_t rpms;
} ots_tractor_EngineRpmState;

typedef struct _ots_tractor_EngineTemp {
    float engine_temp;
} ots_tractor_EngineTemp;

typedef struct _ots_tractor_FuelLevel {
    float fuel_level;
} ots_tractor_FuelLevel;

typedef struct _ots_tractor_GearState {
    ots_tractor_Gear gear;
} ots_tractor_GearState;

typedef struct _ots_tractor_GetRequest {
    pb_size_t which_get;
    union {
        ots_tractor_Empty gear;
        ots_tractor_Empty lights;
        ots_tractor_Empty speed_control;
        ots_tractor_Empty rpms;
        ots_tractor_Empty front_pto;
        ots_tractor_Empty rear_pto;
        ots_tractor_Empty variant;
        ots_tractor_Empty wheel_cal;
        ots_tractor_Empty fuel_level;
        ots_tractor_Empty engine_temp;
    } get;
} ots_tractor_GetRequest;

typedef struct _ots_tractor_HitchRequest {
    int32_t hitch_lift_force;
} ots_tractor_HitchRequest;

typedef struct _ots_tractor_HitchV2Request {
    ots_tractor_HitchCmd hitch;
    float precise_hitch_percent;
} ots_tractor_HitchV2Request;

typedef struct _ots_tractor_LightsState {
    ots_tractor_Lights lights;
} ots_tractor_LightsState;

typedef struct _ots_tractor_PtoState {
    bool enabled;
} ots_tractor_PtoState;

typedef struct _ots_tractor_ScvRequest {
    int32_t scv_id;
    int32_t force;
    int32_t cmd_time_ms;
} ots_tractor_ScvRequest;

typedef struct _ots_tractor_SpeedControlState {
    float speed;
} ots_tractor_SpeedControlState;

typedef struct _ots_tractor_TractorVariantState {
    ots_tractor_TractorVariantType variant;
} ots_tractor_TractorVariantState;

typedef struct _ots_tractor_WheelAngleCalState {
    float sensor_deg_per_bit;
    uint32_t sensor_center_val;
    float center_trim_deg;
    float right_lock_deg;
    uint32_t sensor_full_left_val;
    uint32_t sensor_full_right_val;
} ots_tractor_WheelAngleCalState;

typedef struct _ots_tractor_GetReply {
    pb_size_t which_get;
    union {
        ots_tractor_GearState gear;
        ots_tractor_LightsState lights;
        ots_tractor_SpeedControlState speed_control;
        ots_tractor_EngineRpmState rpms;
        ots_tractor_PtoState front_pto;
        ots_tractor_PtoState rear_pto;
        ots_tractor_TractorVariantState variant;
        ots_tractor_WheelAngleCalState wheel_cal;
        ots_tractor_FuelLevel fuel_level;
        ots_tractor_EngineTemp engine_temp;
    } get;
} ots_tractor_GetReply;

typedef struct _ots_tractor_SetReply {
    pb_size_t which_set;
    union {
        ots_tractor_GearState gear;
        ots_tractor_LightsState lights;
        ots_tractor_SpeedControlState speed_control;
        ots_tractor_HitchReply hitch;
        ots_tractor_ScvReply scv;
        ots_tractor_EngineRpmState rpms;
        ots_tractor_PtoState front_pto;
        ots_tractor_PtoState rear_pto;
        ots_tractor_TractorVariantState variant;
        ots_tractor_WheelAngleCalState wheel_cal;
        ots_tractor_Empty ignition_off;
        ots_tractor_HitchReply hitch_V2;
    } set;
} ots_tractor_SetReply;

typedef struct _ots_tractor_SetRequest {
    pb_size_t which_set;
    union {
        ots_tractor_GearState gear;
        ots_tractor_LightsState lights;
        ots_tractor_SpeedControlState speed_control;
        ots_tractor_HitchRequest hitch;
        ots_tractor_ScvRequest scv;
        ots_tractor_EngineRpmState rpms;
        ots_tractor_PtoState front_pto;
        ots_tractor_PtoState rear_pto;
        ots_tractor_TractorVariantState variant;
        ots_tractor_WheelAngleCalState wheel_cal;
        ots_tractor_Empty ignition_off;
        ots_tractor_HitchV2Request hitch_V2;
    } set;
} ots_tractor_SetRequest;

typedef struct _ots_tractor_Reply {
    pb_size_t which_reply;
    union {
        ots_tractor_SetReply set;
        ots_tractor_GetReply get;
    } reply;
} ots_tractor_Reply;

typedef struct _ots_tractor_Request {
    pb_size_t which_request;
    union {
        ots_tractor_SetRequest set;
        ots_tractor_GetRequest get;
    } request;
} ots_tractor_Request;


/* Helper constants for enums */
#define _ots_tractor_TractorVariantType_MIN ots_tractor_TractorVariantType_TV_UNKNOWN
#define _ots_tractor_TractorVariantType_MAX ots_tractor_TractorVariantType_TV_JD_8RH
#define _ots_tractor_TractorVariantType_ARRAYSIZE ((ots_tractor_TractorVariantType)(ots_tractor_TractorVariantType_TV_JD_8RH+1))

#define _ots_tractor_Gear_MIN ots_tractor_Gear_GEAR_PARK
#define _ots_tractor_Gear_MAX ots_tractor_Gear_GEAR_POWERZERO
#define _ots_tractor_Gear_ARRAYSIZE ((ots_tractor_Gear)(ots_tractor_Gear_GEAR_POWERZERO+1))

#define _ots_tractor_Lights_MIN ots_tractor_Lights_LIGHTS_OFF
#define _ots_tractor_Lights_MAX ots_tractor_Lights_LIGHTS_HIGH
#define _ots_tractor_Lights_ARRAYSIZE ((ots_tractor_Lights)(ots_tractor_Lights_LIGHTS_HIGH+1))

#define _ots_tractor_HitchCmd_MIN ots_tractor_HitchCmd_LIFT
#define _ots_tractor_HitchCmd_MAX ots_tractor_HitchCmd_PRECISE
#define _ots_tractor_HitchCmd_ARRAYSIZE ((ots_tractor_HitchCmd)(ots_tractor_HitchCmd_PRECISE+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define ots_tractor_GearState_init_default       {_ots_tractor_Gear_MIN}
#define ots_tractor_LightsState_init_default     {_ots_tractor_Lights_MIN}
#define ots_tractor_SpeedControlState_init_default {0}
#define ots_tractor_EngineRpmState_init_default  {0}
#define ots_tractor_PtoState_init_default        {0}
#define ots_tractor_HitchV2Request_init_default  {_ots_tractor_HitchCmd_MIN, 0}
#define ots_tractor_HitchRequest_init_default    {0}
#define ots_tractor_HitchReply_init_default      {0}
#define ots_tractor_ScvRequest_init_default      {0, 0, 0}
#define ots_tractor_ScvReply_init_default        {0}
#define ots_tractor_TractorVariantState_init_default {_ots_tractor_TractorVariantType_MIN}
#define ots_tractor_WheelAngleCalState_init_default {0, 0, 0, 0, 0, 0}
#define ots_tractor_FuelLevel_init_default       {0}
#define ots_tractor_EngineTemp_init_default      {0}
#define ots_tractor_Empty_init_default           {0}
#define ots_tractor_SetRequest_init_default      {0, {ots_tractor_GearState_init_default}}
#define ots_tractor_SetReply_init_default        {0, {ots_tractor_GearState_init_default}}
#define ots_tractor_GetRequest_init_default      {0, {ots_tractor_Empty_init_default}}
#define ots_tractor_GetReply_init_default        {0, {ots_tractor_GearState_init_default}}
#define ots_tractor_Request_init_default         {0, {ots_tractor_SetRequest_init_default}}
#define ots_tractor_Reply_init_default           {0, {ots_tractor_SetReply_init_default}}
#define ots_tractor_GearState_init_zero          {_ots_tractor_Gear_MIN}
#define ots_tractor_LightsState_init_zero        {_ots_tractor_Lights_MIN}
#define ots_tractor_SpeedControlState_init_zero  {0}
#define ots_tractor_EngineRpmState_init_zero     {0}
#define ots_tractor_PtoState_init_zero           {0}
#define ots_tractor_HitchV2Request_init_zero     {_ots_tractor_HitchCmd_MIN, 0}
#define ots_tractor_HitchRequest_init_zero       {0}
#define ots_tractor_HitchReply_init_zero         {0}
#define ots_tractor_ScvRequest_init_zero         {0, 0, 0}
#define ots_tractor_ScvReply_init_zero           {0}
#define ots_tractor_TractorVariantState_init_zero {_ots_tractor_TractorVariantType_MIN}
#define ots_tractor_WheelAngleCalState_init_zero {0, 0, 0, 0, 0, 0}
#define ots_tractor_FuelLevel_init_zero          {0}
#define ots_tractor_EngineTemp_init_zero         {0}
#define ots_tractor_Empty_init_zero              {0}
#define ots_tractor_SetRequest_init_zero         {0, {ots_tractor_GearState_init_zero}}
#define ots_tractor_SetReply_init_zero           {0, {ots_tractor_GearState_init_zero}}
#define ots_tractor_GetRequest_init_zero         {0, {ots_tractor_Empty_init_zero}}
#define ots_tractor_GetReply_init_zero           {0, {ots_tractor_GearState_init_zero}}
#define ots_tractor_Request_init_zero            {0, {ots_tractor_SetRequest_init_zero}}
#define ots_tractor_Reply_init_zero              {0, {ots_tractor_SetReply_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define ots_tractor_EngineRpmState_rpms_tag      1
#define ots_tractor_EngineTemp_engine_temp_tag   1
#define ots_tractor_FuelLevel_fuel_level_tag     1
#define ots_tractor_GearState_gear_tag           1
#define ots_tractor_GetRequest_gear_tag          1
#define ots_tractor_GetRequest_lights_tag        2
#define ots_tractor_GetRequest_speed_control_tag 3
#define ots_tractor_GetRequest_rpms_tag          4
#define ots_tractor_GetRequest_front_pto_tag     5
#define ots_tractor_GetRequest_rear_pto_tag      6
#define ots_tractor_GetRequest_variant_tag       7
#define ots_tractor_GetRequest_wheel_cal_tag     8
#define ots_tractor_GetRequest_fuel_level_tag    9
#define ots_tractor_GetRequest_engine_temp_tag   10
#define ots_tractor_HitchRequest_hitch_lift_force_tag 1
#define ots_tractor_HitchV2Request_hitch_tag     1
#define ots_tractor_HitchV2Request_precise_hitch_percent_tag 2
#define ots_tractor_LightsState_lights_tag       1
#define ots_tractor_PtoState_enabled_tag         1
#define ots_tractor_ScvRequest_scv_id_tag        1
#define ots_tractor_ScvRequest_force_tag         2
#define ots_tractor_ScvRequest_cmd_time_ms_tag   3
#define ots_tractor_SpeedControlState_speed_tag  1
#define ots_tractor_TractorVariantState_variant_tag 1
#define ots_tractor_WheelAngleCalState_sensor_deg_per_bit_tag 1
#define ots_tractor_WheelAngleCalState_sensor_center_val_tag 2
#define ots_tractor_WheelAngleCalState_center_trim_deg_tag 3
#define ots_tractor_WheelAngleCalState_right_lock_deg_tag 4
#define ots_tractor_WheelAngleCalState_sensor_full_left_val_tag 5
#define ots_tractor_WheelAngleCalState_sensor_full_right_val_tag 6
#define ots_tractor_GetReply_gear_tag            1
#define ots_tractor_GetReply_lights_tag          2
#define ots_tractor_GetReply_speed_control_tag   3
#define ots_tractor_GetReply_rpms_tag            4
#define ots_tractor_GetReply_front_pto_tag       5
#define ots_tractor_GetReply_rear_pto_tag        6
#define ots_tractor_GetReply_variant_tag         7
#define ots_tractor_GetReply_wheel_cal_tag       8
#define ots_tractor_GetReply_fuel_level_tag      9
#define ots_tractor_GetReply_engine_temp_tag     10
#define ots_tractor_SetReply_gear_tag            1
#define ots_tractor_SetReply_lights_tag          2
#define ots_tractor_SetReply_speed_control_tag   3
#define ots_tractor_SetReply_hitch_tag           4
#define ots_tractor_SetReply_scv_tag             5
#define ots_tractor_SetReply_rpms_tag            6
#define ots_tractor_SetReply_front_pto_tag       7
#define ots_tractor_SetReply_rear_pto_tag        8
#define ots_tractor_SetReply_variant_tag         9
#define ots_tractor_SetReply_wheel_cal_tag       10
#define ots_tractor_SetReply_ignition_off_tag    11
#define ots_tractor_SetReply_hitch_V2_tag        12
#define ots_tractor_SetRequest_gear_tag          1
#define ots_tractor_SetRequest_lights_tag        2
#define ots_tractor_SetRequest_speed_control_tag 3
#define ots_tractor_SetRequest_hitch_tag         4
#define ots_tractor_SetRequest_scv_tag           5
#define ots_tractor_SetRequest_rpms_tag          6
#define ots_tractor_SetRequest_front_pto_tag     7
#define ots_tractor_SetRequest_rear_pto_tag      8
#define ots_tractor_SetRequest_variant_tag       9
#define ots_tractor_SetRequest_wheel_cal_tag     10
#define ots_tractor_SetRequest_ignition_off_tag  11
#define ots_tractor_SetRequest_hitch_V2_tag      12
#define ots_tractor_Reply_set_tag                1
#define ots_tractor_Reply_get_tag                2
#define ots_tractor_Request_set_tag              1
#define ots_tractor_Request_get_tag              2

/* Struct field encoding specification for nanopb */
#define ots_tractor_GearState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    gear,              1)
#define ots_tractor_GearState_CALLBACK NULL
#define ots_tractor_GearState_DEFAULT NULL

#define ots_tractor_LightsState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    lights,            1)
#define ots_tractor_LightsState_CALLBACK NULL
#define ots_tractor_LightsState_DEFAULT NULL

#define ots_tractor_SpeedControlState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    speed,             1)
#define ots_tractor_SpeedControlState_CALLBACK NULL
#define ots_tractor_SpeedControlState_DEFAULT NULL

#define ots_tractor_EngineRpmState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    rpms,              1)
#define ots_tractor_EngineRpmState_CALLBACK NULL
#define ots_tractor_EngineRpmState_DEFAULT NULL

#define ots_tractor_PtoState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     enabled,           1)
#define ots_tractor_PtoState_CALLBACK NULL
#define ots_tractor_PtoState_DEFAULT NULL

#define ots_tractor_HitchV2Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    hitch,             1) \
X(a, STATIC,   SINGULAR, FLOAT,    precise_hitch_percent,   2)
#define ots_tractor_HitchV2Request_CALLBACK NULL
#define ots_tractor_HitchV2Request_DEFAULT NULL

#define ots_tractor_HitchRequest_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    hitch_lift_force,   1)
#define ots_tractor_HitchRequest_CALLBACK NULL
#define ots_tractor_HitchRequest_DEFAULT NULL

#define ots_tractor_HitchReply_FIELDLIST(X, a) \

#define ots_tractor_HitchReply_CALLBACK NULL
#define ots_tractor_HitchReply_DEFAULT NULL

#define ots_tractor_ScvRequest_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    scv_id,            1) \
X(a, STATIC,   SINGULAR, INT32,    force,             2) \
X(a, STATIC,   SINGULAR, INT32,    cmd_time_ms,       3)
#define ots_tractor_ScvRequest_CALLBACK NULL
#define ots_tractor_ScvRequest_DEFAULT NULL

#define ots_tractor_ScvReply_FIELDLIST(X, a) \

#define ots_tractor_ScvReply_CALLBACK NULL
#define ots_tractor_ScvReply_DEFAULT NULL

#define ots_tractor_TractorVariantState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    variant,           1)
#define ots_tractor_TractorVariantState_CALLBACK NULL
#define ots_tractor_TractorVariantState_DEFAULT NULL

#define ots_tractor_WheelAngleCalState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    sensor_deg_per_bit,   1) \
X(a, STATIC,   SINGULAR, UINT32,   sensor_center_val,   2) \
X(a, STATIC,   SINGULAR, FLOAT,    center_trim_deg,   3) \
X(a, STATIC,   SINGULAR, FLOAT,    right_lock_deg,    4) \
X(a, STATIC,   SINGULAR, UINT32,   sensor_full_left_val,   5) \
X(a, STATIC,   SINGULAR, UINT32,   sensor_full_right_val,   6)
#define ots_tractor_WheelAngleCalState_CALLBACK NULL
#define ots_tractor_WheelAngleCalState_DEFAULT NULL

#define ots_tractor_FuelLevel_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    fuel_level,        1)
#define ots_tractor_FuelLevel_CALLBACK NULL
#define ots_tractor_FuelLevel_DEFAULT NULL

#define ots_tractor_EngineTemp_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    engine_temp,       1)
#define ots_tractor_EngineTemp_CALLBACK NULL
#define ots_tractor_EngineTemp_DEFAULT NULL

#define ots_tractor_Empty_FIELDLIST(X, a) \

#define ots_tractor_Empty_CALLBACK NULL
#define ots_tractor_Empty_DEFAULT NULL

#define ots_tractor_SetRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,gear,set.gear),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,lights,set.lights),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,speed_control,set.speed_control),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,hitch,set.hitch),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,scv,set.scv),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,rpms,set.rpms),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,front_pto,set.front_pto),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,rear_pto,set.rear_pto),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,variant,set.variant),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,wheel_cal,set.wheel_cal),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,ignition_off,set.ignition_off),  11) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,hitch_V2,set.hitch_V2),  12)
#define ots_tractor_SetRequest_CALLBACK NULL
#define ots_tractor_SetRequest_DEFAULT NULL
#define ots_tractor_SetRequest_set_gear_MSGTYPE ots_tractor_GearState
#define ots_tractor_SetRequest_set_lights_MSGTYPE ots_tractor_LightsState
#define ots_tractor_SetRequest_set_speed_control_MSGTYPE ots_tractor_SpeedControlState
#define ots_tractor_SetRequest_set_hitch_MSGTYPE ots_tractor_HitchRequest
#define ots_tractor_SetRequest_set_scv_MSGTYPE ots_tractor_ScvRequest
#define ots_tractor_SetRequest_set_rpms_MSGTYPE ots_tractor_EngineRpmState
#define ots_tractor_SetRequest_set_front_pto_MSGTYPE ots_tractor_PtoState
#define ots_tractor_SetRequest_set_rear_pto_MSGTYPE ots_tractor_PtoState
#define ots_tractor_SetRequest_set_variant_MSGTYPE ots_tractor_TractorVariantState
#define ots_tractor_SetRequest_set_wheel_cal_MSGTYPE ots_tractor_WheelAngleCalState
#define ots_tractor_SetRequest_set_ignition_off_MSGTYPE ots_tractor_Empty
#define ots_tractor_SetRequest_set_hitch_V2_MSGTYPE ots_tractor_HitchV2Request

#define ots_tractor_SetReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,gear,set.gear),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,lights,set.lights),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,speed_control,set.speed_control),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,hitch,set.hitch),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,scv,set.scv),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,rpms,set.rpms),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,front_pto,set.front_pto),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,rear_pto,set.rear_pto),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,variant,set.variant),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,wheel_cal,set.wheel_cal),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,ignition_off,set.ignition_off),  11) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,hitch_V2,set.hitch_V2),  12)
#define ots_tractor_SetReply_CALLBACK NULL
#define ots_tractor_SetReply_DEFAULT NULL
#define ots_tractor_SetReply_set_gear_MSGTYPE ots_tractor_GearState
#define ots_tractor_SetReply_set_lights_MSGTYPE ots_tractor_LightsState
#define ots_tractor_SetReply_set_speed_control_MSGTYPE ots_tractor_SpeedControlState
#define ots_tractor_SetReply_set_hitch_MSGTYPE ots_tractor_HitchReply
#define ots_tractor_SetReply_set_scv_MSGTYPE ots_tractor_ScvReply
#define ots_tractor_SetReply_set_rpms_MSGTYPE ots_tractor_EngineRpmState
#define ots_tractor_SetReply_set_front_pto_MSGTYPE ots_tractor_PtoState
#define ots_tractor_SetReply_set_rear_pto_MSGTYPE ots_tractor_PtoState
#define ots_tractor_SetReply_set_variant_MSGTYPE ots_tractor_TractorVariantState
#define ots_tractor_SetReply_set_wheel_cal_MSGTYPE ots_tractor_WheelAngleCalState
#define ots_tractor_SetReply_set_ignition_off_MSGTYPE ots_tractor_Empty
#define ots_tractor_SetReply_set_hitch_V2_MSGTYPE ots_tractor_HitchReply

#define ots_tractor_GetRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,gear,get.gear),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,lights,get.lights),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,speed_control,get.speed_control),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,rpms,get.rpms),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,front_pto,get.front_pto),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,rear_pto,get.rear_pto),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,variant,get.variant),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,wheel_cal,get.wheel_cal),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,fuel_level,get.fuel_level),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,engine_temp,get.engine_temp),  10)
#define ots_tractor_GetRequest_CALLBACK NULL
#define ots_tractor_GetRequest_DEFAULT NULL
#define ots_tractor_GetRequest_get_gear_MSGTYPE ots_tractor_Empty
#define ots_tractor_GetRequest_get_lights_MSGTYPE ots_tractor_Empty
#define ots_tractor_GetRequest_get_speed_control_MSGTYPE ots_tractor_Empty
#define ots_tractor_GetRequest_get_rpms_MSGTYPE ots_tractor_Empty
#define ots_tractor_GetRequest_get_front_pto_MSGTYPE ots_tractor_Empty
#define ots_tractor_GetRequest_get_rear_pto_MSGTYPE ots_tractor_Empty
#define ots_tractor_GetRequest_get_variant_MSGTYPE ots_tractor_Empty
#define ots_tractor_GetRequest_get_wheel_cal_MSGTYPE ots_tractor_Empty
#define ots_tractor_GetRequest_get_fuel_level_MSGTYPE ots_tractor_Empty
#define ots_tractor_GetRequest_get_engine_temp_MSGTYPE ots_tractor_Empty

#define ots_tractor_GetReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,gear,get.gear),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,lights,get.lights),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,speed_control,get.speed_control),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,rpms,get.rpms),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,front_pto,get.front_pto),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,rear_pto,get.rear_pto),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,variant,get.variant),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,wheel_cal,get.wheel_cal),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,fuel_level,get.fuel_level),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,engine_temp,get.engine_temp),  10)
#define ots_tractor_GetReply_CALLBACK NULL
#define ots_tractor_GetReply_DEFAULT NULL
#define ots_tractor_GetReply_get_gear_MSGTYPE ots_tractor_GearState
#define ots_tractor_GetReply_get_lights_MSGTYPE ots_tractor_LightsState
#define ots_tractor_GetReply_get_speed_control_MSGTYPE ots_tractor_SpeedControlState
#define ots_tractor_GetReply_get_rpms_MSGTYPE ots_tractor_EngineRpmState
#define ots_tractor_GetReply_get_front_pto_MSGTYPE ots_tractor_PtoState
#define ots_tractor_GetReply_get_rear_pto_MSGTYPE ots_tractor_PtoState
#define ots_tractor_GetReply_get_variant_MSGTYPE ots_tractor_TractorVariantState
#define ots_tractor_GetReply_get_wheel_cal_MSGTYPE ots_tractor_WheelAngleCalState
#define ots_tractor_GetReply_get_fuel_level_MSGTYPE ots_tractor_FuelLevel
#define ots_tractor_GetReply_get_engine_temp_MSGTYPE ots_tractor_EngineTemp

#define ots_tractor_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set,request.set),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get,request.get),   2)
#define ots_tractor_Request_CALLBACK NULL
#define ots_tractor_Request_DEFAULT NULL
#define ots_tractor_Request_request_set_MSGTYPE ots_tractor_SetRequest
#define ots_tractor_Request_request_get_MSGTYPE ots_tractor_GetRequest

#define ots_tractor_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,set,reply.set),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,get,reply.get),   2)
#define ots_tractor_Reply_CALLBACK NULL
#define ots_tractor_Reply_DEFAULT NULL
#define ots_tractor_Reply_reply_set_MSGTYPE ots_tractor_SetReply
#define ots_tractor_Reply_reply_get_MSGTYPE ots_tractor_GetReply

extern const pb_msgdesc_t ots_tractor_GearState_msg;
extern const pb_msgdesc_t ots_tractor_LightsState_msg;
extern const pb_msgdesc_t ots_tractor_SpeedControlState_msg;
extern const pb_msgdesc_t ots_tractor_EngineRpmState_msg;
extern const pb_msgdesc_t ots_tractor_PtoState_msg;
extern const pb_msgdesc_t ots_tractor_HitchV2Request_msg;
extern const pb_msgdesc_t ots_tractor_HitchRequest_msg;
extern const pb_msgdesc_t ots_tractor_HitchReply_msg;
extern const pb_msgdesc_t ots_tractor_ScvRequest_msg;
extern const pb_msgdesc_t ots_tractor_ScvReply_msg;
extern const pb_msgdesc_t ots_tractor_TractorVariantState_msg;
extern const pb_msgdesc_t ots_tractor_WheelAngleCalState_msg;
extern const pb_msgdesc_t ots_tractor_FuelLevel_msg;
extern const pb_msgdesc_t ots_tractor_EngineTemp_msg;
extern const pb_msgdesc_t ots_tractor_Empty_msg;
extern const pb_msgdesc_t ots_tractor_SetRequest_msg;
extern const pb_msgdesc_t ots_tractor_SetReply_msg;
extern const pb_msgdesc_t ots_tractor_GetRequest_msg;
extern const pb_msgdesc_t ots_tractor_GetReply_msg;
extern const pb_msgdesc_t ots_tractor_Request_msg;
extern const pb_msgdesc_t ots_tractor_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define ots_tractor_GearState_fields &ots_tractor_GearState_msg
#define ots_tractor_LightsState_fields &ots_tractor_LightsState_msg
#define ots_tractor_SpeedControlState_fields &ots_tractor_SpeedControlState_msg
#define ots_tractor_EngineRpmState_fields &ots_tractor_EngineRpmState_msg
#define ots_tractor_PtoState_fields &ots_tractor_PtoState_msg
#define ots_tractor_HitchV2Request_fields &ots_tractor_HitchV2Request_msg
#define ots_tractor_HitchRequest_fields &ots_tractor_HitchRequest_msg
#define ots_tractor_HitchReply_fields &ots_tractor_HitchReply_msg
#define ots_tractor_ScvRequest_fields &ots_tractor_ScvRequest_msg
#define ots_tractor_ScvReply_fields &ots_tractor_ScvReply_msg
#define ots_tractor_TractorVariantState_fields &ots_tractor_TractorVariantState_msg
#define ots_tractor_WheelAngleCalState_fields &ots_tractor_WheelAngleCalState_msg
#define ots_tractor_FuelLevel_fields &ots_tractor_FuelLevel_msg
#define ots_tractor_EngineTemp_fields &ots_tractor_EngineTemp_msg
#define ots_tractor_Empty_fields &ots_tractor_Empty_msg
#define ots_tractor_SetRequest_fields &ots_tractor_SetRequest_msg
#define ots_tractor_SetReply_fields &ots_tractor_SetReply_msg
#define ots_tractor_GetRequest_fields &ots_tractor_GetRequest_msg
#define ots_tractor_GetReply_fields &ots_tractor_GetReply_msg
#define ots_tractor_Request_fields &ots_tractor_Request_msg
#define ots_tractor_Reply_fields &ots_tractor_Reply_msg

/* Maximum encoded size of messages (where known) */
#define ots_tractor_GearState_size               2
#define ots_tractor_LightsState_size             2
#define ots_tractor_SpeedControlState_size       5
#define ots_tractor_EngineRpmState_size          11
#define ots_tractor_PtoState_size                2
#define ots_tractor_HitchV2Request_size          7
#define ots_tractor_HitchRequest_size            11
#define ots_tractor_HitchReply_size              0
#define ots_tractor_ScvRequest_size              33
#define ots_tractor_ScvReply_size                0
#define ots_tractor_TractorVariantState_size     2
#define ots_tractor_WheelAngleCalState_size      33
#define ots_tractor_FuelLevel_size               5
#define ots_tractor_EngineTemp_size              5
#define ots_tractor_Empty_size                   0
#define ots_tractor_SetRequest_size              35
#define ots_tractor_SetReply_size                35
#define ots_tractor_GetRequest_size              2
#define ots_tractor_GetReply_size                35
#define ots_tractor_Request_size                 37
#define ots_tractor_Reply_size                   37

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

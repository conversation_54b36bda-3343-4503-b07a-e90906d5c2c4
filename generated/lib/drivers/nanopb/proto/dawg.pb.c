/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "dawg.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(dawg_Config_Request, dawg_Config_Request, AUTO)


PB_BIND(dawg_Arm_Request, dawg_Arm_Request, AUTO)


PB_BIND(dawg_Pet_Request, dawg_Pet_Request, AUTO)


PB_BIND(dawg_Get_State_Request, dawg_Get_State_Request, AUTO)


PB_BIND(dawg_State_Reply, dawg_State_Reply, AUTO)


PB_BIND(dawg_Request, dawg_Request, AUTO)


PB_BIND(dawg_Reply, dawg_Reply, 2)




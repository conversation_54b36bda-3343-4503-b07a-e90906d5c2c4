# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/gps.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/gps.proto',
  package='gps',
  syntax='proto3',
  serialized_options=b'Z\nnanopb/gps',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n,generated/lib/drivers/nanopb/proto/gps.proto\x12\x03gps\"\x12\n\x10Position_Request\"+\n\x0eSpartn_Request\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x08\"\x0e\n\x0cSpartn_Reply\")\n\x0cRtcm_Request\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x12\x0b\n\x03\x65nd\x18\x02 \x01(\x08\"\x0c\n\nRtcm_Reply\"4\n\x11ValueWithAccuracy\x12\r\n\x05value\x18\x01 \x01(\x01\x12\x10\n\x08\x61\x63\x63uracy\x18\x02 \x01(\x01\"\xa7\x03\n\x0b\x44ualGpsData\x12\x12\n\ngnss_valid\x18\x01 \x01(\x08\x12\x18\n\x10\x64iff_corrections\x18\x02 \x01(\x08\x12\x16\n\x0eis_moving_base\x18\x03 \x01(\x08\x12,\n\rcarrier_phase\x18\x04 \x01(\x0e\x32\x15.gps.CarrierPhaseSoln\x12\x14\n\x0ctimestamp_ms\x18\x05 \x01(\x04\x12*\n\x05north\x18\x06 \x01(\x0b\x32\x16.gps.ValueWithAccuracyH\x00\x88\x01\x01\x12)\n\x04\x65\x61st\x18\x07 \x01(\x0b\x32\x16.gps.ValueWithAccuracyH\x01\x88\x01\x01\x12)\n\x04\x64own\x18\x08 \x01(\x0b\x32\x16.gps.ValueWithAccuracyH\x02\x88\x01\x01\x12+\n\x06length\x18\t \x01(\x0b\x32\x16.gps.ValueWithAccuracyH\x03\x88\x01\x01\x12,\n\x07heading\x18\n \x01(\x0b\x32\x16.gps.ValueWithAccuracyH\x04\x88\x01\x01\x42\x08\n\x06_northB\x07\n\x05_eastB\x07\n\x05_downB\t\n\x07_lengthB\n\n\x08_heading\"\xfc\x01\n\x0ePosition_Reply\x12\x10\n\x08have_fix\x18\x01 \x01(\x08\x12\x10\n\x08latitude\x18\x02 \x01(\x01\x12\x11\n\tlongitude\x18\x03 \x01(\x01\x12\x10\n\x08num_sats\x18\x04 \x01(\x05\x12\x0c\n\x04hdop\x18\x05 \x01(\x02\x12\x14\n\x0ctimestamp_ms\x18\x06 \x01(\x04\x12\x11\n\theight_mm\x18\x07 \x01(\x05\x12\x17\n\x0fhave_approx_fix\x18\x08 \x01(\x08\x12\x10\n\x08\x66ix_type\x18\t \x01(\x05\x12\x11\n\tfix_flags\x18\n \x01(\x05\x12#\n\x04\x64ual\x18\x0b \x01(\x0b\x32\x10.gps.DualGpsDataH\x00\x88\x01\x01\x42\x07\n\x05_dual\"3\n\x19HeadingCorrection_Request\x12\x16\n\x0eheading_offset\x18\x01 \x01(\x02\"\x19\n\x17HeadingCorrection_Reply\"\x14\n\x12GetLastGga_Request\"(\n\x10GetLastGga_Reply\x12\x14\n\x0craw_sentence\x18\x01 \x01(\t\"\xef\x01\n\x07Request\x12)\n\x08position\x18\x01 \x01(\x0b\x32\x15.gps.Position_RequestH\x00\x12%\n\x06spartn\x18\x02 \x01(\x0b\x32\x13.gps.Spartn_RequestH\x00\x12<\n\x12heading_correction\x18\x03 \x01(\x0b\x32\x1e.gps.HeadingCorrection_RequestH\x00\x12!\n\x04rtcm\x18\x04 \x01(\x0b\x32\x11.gps.Rtcm_RequestH\x00\x12&\n\x03gga\x18\x05 \x01(\x0b\x32\x17.gps.GetLastGga_RequestH\x00\x42\t\n\x07request\"\xe1\x01\n\x05Reply\x12\'\n\x08position\x18\x01 \x01(\x0b\x32\x13.gps.Position_ReplyH\x00\x12#\n\x06spartn\x18\x02 \x01(\x0b\x32\x11.gps.Spartn_ReplyH\x00\x12:\n\x12heading_correction\x18\x03 \x01(\x0b\x32\x1c.gps.HeadingCorrection_ReplyH\x00\x12\x1f\n\x04rtcm\x18\x04 \x01(\x0b\x32\x0f.gps.Rtcm_ReplyH\x00\x12$\n\x03gga\x18\x05 \x01(\x0b\x32\x15.gps.GetLastGga_ReplyH\x00\x42\x07\n\x05reply*5\n\x10\x43\x61rrierPhaseSoln\x12\x08\n\x04NONE\x10\x00\x12\x0c\n\x08\x46LOATING\x10\x01\x12\t\n\x05\x46IXED\x10\x02\x42\x0cZ\nnanopb/gpsb\x06proto3'
)

_CARRIERPHASESOLN = _descriptor.EnumDescriptor(
  name='CarrierPhaseSoln',
  full_name='gps.CarrierPhaseSoln',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FLOATING', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FIXED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1540,
  serialized_end=1593,
)
_sym_db.RegisterEnumDescriptor(_CARRIERPHASESOLN)

CarrierPhaseSoln = enum_type_wrapper.EnumTypeWrapper(_CARRIERPHASESOLN)
NONE = 0
FLOATING = 1
FIXED = 2



_POSITION_REQUEST = _descriptor.Descriptor(
  name='Position_Request',
  full_name='gps.Position_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=53,
  serialized_end=71,
)


_SPARTN_REQUEST = _descriptor.Descriptor(
  name='Spartn_Request',
  full_name='gps.Spartn_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='gps.Spartn_Request.data', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end', full_name='gps.Spartn_Request.end', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=73,
  serialized_end=116,
)


_SPARTN_REPLY = _descriptor.Descriptor(
  name='Spartn_Reply',
  full_name='gps.Spartn_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=118,
  serialized_end=132,
)


_RTCM_REQUEST = _descriptor.Descriptor(
  name='Rtcm_Request',
  full_name='gps.Rtcm_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='gps.Rtcm_Request.data', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end', full_name='gps.Rtcm_Request.end', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=134,
  serialized_end=175,
)


_RTCM_REPLY = _descriptor.Descriptor(
  name='Rtcm_Reply',
  full_name='gps.Rtcm_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=177,
  serialized_end=189,
)


_VALUEWITHACCURACY = _descriptor.Descriptor(
  name='ValueWithAccuracy',
  full_name='gps.ValueWithAccuracy',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='gps.ValueWithAccuracy.value', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='accuracy', full_name='gps.ValueWithAccuracy.accuracy', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=191,
  serialized_end=243,
)


_DUALGPSDATA = _descriptor.Descriptor(
  name='DualGpsData',
  full_name='gps.DualGpsData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gnss_valid', full_name='gps.DualGpsData.gnss_valid', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='diff_corrections', full_name='gps.DualGpsData.diff_corrections', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_moving_base', full_name='gps.DualGpsData.is_moving_base', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='carrier_phase', full_name='gps.DualGpsData.carrier_phase', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='gps.DualGpsData.timestamp_ms', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='north', full_name='gps.DualGpsData.north', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='east', full_name='gps.DualGpsData.east', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='down', full_name='gps.DualGpsData.down', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='length', full_name='gps.DualGpsData.length', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading', full_name='gps.DualGpsData.heading', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_north', full_name='gps.DualGpsData._north',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_east', full_name='gps.DualGpsData._east',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_down', full_name='gps.DualGpsData._down',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_length', full_name='gps.DualGpsData._length',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_heading', full_name='gps.DualGpsData._heading',
      index=4, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=246,
  serialized_end=669,
)


_POSITION_REPLY = _descriptor.Descriptor(
  name='Position_Reply',
  full_name='gps.Position_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='have_fix', full_name='gps.Position_Reply.have_fix', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latitude', full_name='gps.Position_Reply.latitude', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='longitude', full_name='gps.Position_Reply.longitude', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_sats', full_name='gps.Position_Reply.num_sats', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hdop', full_name='gps.Position_Reply.hdop', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='gps.Position_Reply.timestamp_ms', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height_mm', full_name='gps.Position_Reply.height_mm', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='have_approx_fix', full_name='gps.Position_Reply.have_approx_fix', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fix_type', full_name='gps.Position_Reply.fix_type', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fix_flags', full_name='gps.Position_Reply.fix_flags', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dual', full_name='gps.Position_Reply.dual', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_dual', full_name='gps.Position_Reply._dual',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=672,
  serialized_end=924,
)


_HEADINGCORRECTION_REQUEST = _descriptor.Descriptor(
  name='HeadingCorrection_Request',
  full_name='gps.HeadingCorrection_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='heading_offset', full_name='gps.HeadingCorrection_Request.heading_offset', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=926,
  serialized_end=977,
)


_HEADINGCORRECTION_REPLY = _descriptor.Descriptor(
  name='HeadingCorrection_Reply',
  full_name='gps.HeadingCorrection_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=979,
  serialized_end=1004,
)


_GETLASTGGA_REQUEST = _descriptor.Descriptor(
  name='GetLastGga_Request',
  full_name='gps.GetLastGga_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1006,
  serialized_end=1026,
)


_GETLASTGGA_REPLY = _descriptor.Descriptor(
  name='GetLastGga_Reply',
  full_name='gps.GetLastGga_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='raw_sentence', full_name='gps.GetLastGga_Reply.raw_sentence', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1028,
  serialized_end=1068,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='gps.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='position', full_name='gps.Request.position', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='spartn', full_name='gps.Request.spartn', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading_correction', full_name='gps.Request.heading_correction', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rtcm', full_name='gps.Request.rtcm', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gga', full_name='gps.Request.gga', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='gps.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1071,
  serialized_end=1310,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='gps.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='position', full_name='gps.Reply.position', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='spartn', full_name='gps.Reply.spartn', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading_correction', full_name='gps.Reply.heading_correction', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rtcm', full_name='gps.Reply.rtcm', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gga', full_name='gps.Reply.gga', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='gps.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1313,
  serialized_end=1538,
)

_DUALGPSDATA.fields_by_name['carrier_phase'].enum_type = _CARRIERPHASESOLN
_DUALGPSDATA.fields_by_name['north'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.fields_by_name['east'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.fields_by_name['down'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.fields_by_name['length'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.fields_by_name['heading'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.oneofs_by_name['_north'].fields.append(
  _DUALGPSDATA.fields_by_name['north'])
_DUALGPSDATA.fields_by_name['north'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_north']
_DUALGPSDATA.oneofs_by_name['_east'].fields.append(
  _DUALGPSDATA.fields_by_name['east'])
_DUALGPSDATA.fields_by_name['east'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_east']
_DUALGPSDATA.oneofs_by_name['_down'].fields.append(
  _DUALGPSDATA.fields_by_name['down'])
_DUALGPSDATA.fields_by_name['down'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_down']
_DUALGPSDATA.oneofs_by_name['_length'].fields.append(
  _DUALGPSDATA.fields_by_name['length'])
_DUALGPSDATA.fields_by_name['length'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_length']
_DUALGPSDATA.oneofs_by_name['_heading'].fields.append(
  _DUALGPSDATA.fields_by_name['heading'])
_DUALGPSDATA.fields_by_name['heading'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_heading']
_POSITION_REPLY.fields_by_name['dual'].message_type = _DUALGPSDATA
_POSITION_REPLY.oneofs_by_name['_dual'].fields.append(
  _POSITION_REPLY.fields_by_name['dual'])
_POSITION_REPLY.fields_by_name['dual'].containing_oneof = _POSITION_REPLY.oneofs_by_name['_dual']
_REQUEST.fields_by_name['position'].message_type = _POSITION_REQUEST
_REQUEST.fields_by_name['spartn'].message_type = _SPARTN_REQUEST
_REQUEST.fields_by_name['heading_correction'].message_type = _HEADINGCORRECTION_REQUEST
_REQUEST.fields_by_name['rtcm'].message_type = _RTCM_REQUEST
_REQUEST.fields_by_name['gga'].message_type = _GETLASTGGA_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['position'])
_REQUEST.fields_by_name['position'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['spartn'])
_REQUEST.fields_by_name['spartn'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['heading_correction'])
_REQUEST.fields_by_name['heading_correction'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['rtcm'])
_REQUEST.fields_by_name['rtcm'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['gga'])
_REQUEST.fields_by_name['gga'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['position'].message_type = _POSITION_REPLY
_REPLY.fields_by_name['spartn'].message_type = _SPARTN_REPLY
_REPLY.fields_by_name['heading_correction'].message_type = _HEADINGCORRECTION_REPLY
_REPLY.fields_by_name['rtcm'].message_type = _RTCM_REPLY
_REPLY.fields_by_name['gga'].message_type = _GETLASTGGA_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['position'])
_REPLY.fields_by_name['position'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['spartn'])
_REPLY.fields_by_name['spartn'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['heading_correction'])
_REPLY.fields_by_name['heading_correction'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['rtcm'])
_REPLY.fields_by_name['rtcm'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['gga'])
_REPLY.fields_by_name['gga'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Position_Request'] = _POSITION_REQUEST
DESCRIPTOR.message_types_by_name['Spartn_Request'] = _SPARTN_REQUEST
DESCRIPTOR.message_types_by_name['Spartn_Reply'] = _SPARTN_REPLY
DESCRIPTOR.message_types_by_name['Rtcm_Request'] = _RTCM_REQUEST
DESCRIPTOR.message_types_by_name['Rtcm_Reply'] = _RTCM_REPLY
DESCRIPTOR.message_types_by_name['ValueWithAccuracy'] = _VALUEWITHACCURACY
DESCRIPTOR.message_types_by_name['DualGpsData'] = _DUALGPSDATA
DESCRIPTOR.message_types_by_name['Position_Reply'] = _POSITION_REPLY
DESCRIPTOR.message_types_by_name['HeadingCorrection_Request'] = _HEADINGCORRECTION_REQUEST
DESCRIPTOR.message_types_by_name['HeadingCorrection_Reply'] = _HEADINGCORRECTION_REPLY
DESCRIPTOR.message_types_by_name['GetLastGga_Request'] = _GETLASTGGA_REQUEST
DESCRIPTOR.message_types_by_name['GetLastGga_Reply'] = _GETLASTGGA_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.enum_types_by_name['CarrierPhaseSoln'] = _CARRIERPHASESOLN
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Position_Request = _reflection.GeneratedProtocolMessageType('Position_Request', (_message.Message,), {
  'DESCRIPTOR' : _POSITION_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.Position_Request)
  })
_sym_db.RegisterMessage(Position_Request)

Spartn_Request = _reflection.GeneratedProtocolMessageType('Spartn_Request', (_message.Message,), {
  'DESCRIPTOR' : _SPARTN_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.Spartn_Request)
  })
_sym_db.RegisterMessage(Spartn_Request)

Spartn_Reply = _reflection.GeneratedProtocolMessageType('Spartn_Reply', (_message.Message,), {
  'DESCRIPTOR' : _SPARTN_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.Spartn_Reply)
  })
_sym_db.RegisterMessage(Spartn_Reply)

Rtcm_Request = _reflection.GeneratedProtocolMessageType('Rtcm_Request', (_message.Message,), {
  'DESCRIPTOR' : _RTCM_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.Rtcm_Request)
  })
_sym_db.RegisterMessage(Rtcm_Request)

Rtcm_Reply = _reflection.GeneratedProtocolMessageType('Rtcm_Reply', (_message.Message,), {
  'DESCRIPTOR' : _RTCM_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.Rtcm_Reply)
  })
_sym_db.RegisterMessage(Rtcm_Reply)

ValueWithAccuracy = _reflection.GeneratedProtocolMessageType('ValueWithAccuracy', (_message.Message,), {
  'DESCRIPTOR' : _VALUEWITHACCURACY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.ValueWithAccuracy)
  })
_sym_db.RegisterMessage(ValueWithAccuracy)

DualGpsData = _reflection.GeneratedProtocolMessageType('DualGpsData', (_message.Message,), {
  'DESCRIPTOR' : _DUALGPSDATA,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.DualGpsData)
  })
_sym_db.RegisterMessage(DualGpsData)

Position_Reply = _reflection.GeneratedProtocolMessageType('Position_Reply', (_message.Message,), {
  'DESCRIPTOR' : _POSITION_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.Position_Reply)
  })
_sym_db.RegisterMessage(Position_Reply)

HeadingCorrection_Request = _reflection.GeneratedProtocolMessageType('HeadingCorrection_Request', (_message.Message,), {
  'DESCRIPTOR' : _HEADINGCORRECTION_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.HeadingCorrection_Request)
  })
_sym_db.RegisterMessage(HeadingCorrection_Request)

HeadingCorrection_Reply = _reflection.GeneratedProtocolMessageType('HeadingCorrection_Reply', (_message.Message,), {
  'DESCRIPTOR' : _HEADINGCORRECTION_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.HeadingCorrection_Reply)
  })
_sym_db.RegisterMessage(HeadingCorrection_Reply)

GetLastGga_Request = _reflection.GeneratedProtocolMessageType('GetLastGga_Request', (_message.Message,), {
  'DESCRIPTOR' : _GETLASTGGA_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.GetLastGga_Request)
  })
_sym_db.RegisterMessage(GetLastGga_Request)

GetLastGga_Reply = _reflection.GeneratedProtocolMessageType('GetLastGga_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GETLASTGGA_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.GetLastGga_Reply)
  })
_sym_db.RegisterMessage(GetLastGga_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.gps_pb2'
  # @@protoc_insertion_point(class_scope:gps.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

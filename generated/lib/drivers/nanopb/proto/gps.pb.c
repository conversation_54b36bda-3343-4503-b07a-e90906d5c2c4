/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "gps.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(gps_Position_Request, gps_Position_Request, AUTO)


PB_BIND(gps_Spartn_Request, gps_Spartn_Request, AUTO)


PB_BIND(gps_Spartn_Reply, gps_Spartn_Reply, AUTO)


PB_BIND(gps_Rtcm_Request, gps_Rtcm_Request, AUTO)


PB_BIND(gps_Rtcm_Reply, gps_Rtcm_Reply, AUTO)


PB_BIND(gps_ValueWithAccuracy, gps_ValueWithAccuracy, AUTO)


PB_BIND(gps_DualGpsData, gps_DualGpsData, AUTO)


PB_BIND(gps_Position_Reply, gps_Position_Reply, AUTO)


PB_BIND(gps_HeadingCorrection_Request, gps_HeadingCorrection_Request, AUTO)


PB_BIND(gps_HeadingCorrection_Reply, gps_HeadingCorrection_Reply, AUTO)


PB_BIND(gps_GetLastGga_Request, gps_GetLastGga_Request, AUTO)


PB_BIND(gps_GetLastGga_Reply, gps_GetLastGga_Reply, AUTO)


PB_BIND(gps_Request, gps_Request, AUTO)


PB_BIND(gps_Reply, gps_Reply, AUTO)




#ifndef PB_CONVERT_DOUBLE_FLOAT
/* On some platforms (such as AVR), double is really float.
 * To be able to encode/decode double on these platforms, you need.
 * to define PB_CONVERT_DOUBLE_FLOAT in pb.h or compiler command line.
 */
PB_STATIC_ASSERT(sizeof(double) == 8, DOUBLE_MUST_BE_8_BYTES)
#endif


# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/nofx_board.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import diagnostic_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2
from generated.lib.drivers.nanopb.proto import park_brake_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_park__brake__pb2
from generated.lib.drivers.nanopb.proto import request_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2
from generated.lib.drivers.nanopb.proto import rotary_encoder_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_rotary__encoder__pb2
from generated.lib.drivers.nanopb.proto import version_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2
from generated.lib.drivers.nanopb.proto import sensors_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_sensors__pb2
from generated.lib.drivers.nanopb.proto import drive_solenoids_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_drive__solenoids__pb2
from generated.lib.drivers.nanopb.proto import time_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/nofx_board.proto',
  package='nofx_board',
  syntax='proto3',
  serialized_options=b'Z\021nanopb/nofx_board',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n3generated/lib/drivers/nanopb/proto/nofx_board.proto\x12\nnofx_board\x1a\x33generated/lib/drivers/nanopb/proto/diagnostic.proto\x1a\x33generated/lib/drivers/nanopb/proto/park_brake.proto\x1a\x30generated/lib/drivers/nanopb/proto/request.proto\x1a\x37generated/lib/drivers/nanopb/proto/rotary_encoder.proto\x1a\x30generated/lib/drivers/nanopb/proto/version.proto\x1a\x30generated/lib/drivers/nanopb/proto/sensors.proto\x1a\x38generated/lib/drivers/nanopb/proto/drive_solenoids.proto\x1a-generated/lib/drivers/nanopb/proto/time.proto\"\x87\x03\n\x05Reply\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04pong\x18\x02 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12/\n\x0erotary_encoder\x18\x03 \x01(\x0b\x32\x15.rotary_encoder.ReplyH\x00\x12\'\n\npark_brake\x18\x04 \x01(\x0b\x32\x11.park_brake.ReplyH\x00\x12\x33\n\x10park_brake_query\x18\x05 \x01(\x0b\x32\x17.park_brake.Query_ReplyH\x00\x12)\n\x07version\x18\x06 \x01(\x0b\x32\x16.version.Version_ReplyH\x00\x12!\n\x07sensors\x18\x07 \x01(\x0b\x32\x0e.sensors.ReplyH\x00\x12\x1b\n\x04time\x18\x08 \x01(\x0b\x32\x0b.time.ReplyH\x00\x12\x31\n\x0f\x64rive_solenoids\x18\t \x01(\x0b\x32\x16.drive_solenoids.ReplyH\x00\x42\x07\n\x05reply\"\xc2\x03\n\x07Request\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12\x31\n\x0erotary_encoder\x18\x03 \x01(\x0b\x32\x17.rotary_encoder.RequestH\x00\x12)\n\npark_brake\x18\x04 \x01(\x0b\x32\x13.park_brake.RequestH\x00\x12\x35\n\x10park_brake_query\x18\x05 \x01(\x0b\x32\x19.park_brake.Query_RequestH\x00\x12+\n\x07version\x18\x06 \x01(\x0b\x32\x18.version.Version_RequestH\x00\x12\'\n\x05reset\x18\x07 \x01(\x0b\x32\x16.version.Reset_RequestH\x00\x12#\n\x07sensors\x18\x08 \x01(\x0b\x32\x10.sensors.RequestH\x00\x12\x1d\n\x04time\x18\t \x01(\x0b\x32\r.time.RequestH\x00\x12\x33\n\x0f\x64rive_solenoids\x18\n \x01(\x0b\x32\x18.drive_solenoids.RequestH\x00\x42\t\n\x07requestB\x13Z\x11nanopb/nofx_boardb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_park__brake__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_rotary__encoder__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_sensors__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_drive__solenoids__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2.DESCRIPTOR,])




_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='nofx_board.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='nofx_board.Reply.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pong', full_name='nofx_board.Reply.pong', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rotary_encoder', full_name='nofx_board.Reply.rotary_encoder', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='park_brake', full_name='nofx_board.Reply.park_brake', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='park_brake_query', full_name='nofx_board.Reply.park_brake_query', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='nofx_board.Reply.version', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensors', full_name='nofx_board.Reply.sensors', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time', full_name='nofx_board.Reply.time', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='drive_solenoids', full_name='nofx_board.Reply.drive_solenoids', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='nofx_board.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=486,
  serialized_end=877,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='nofx_board.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='nofx_board.Request.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ping', full_name='nofx_board.Request.ping', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rotary_encoder', full_name='nofx_board.Request.rotary_encoder', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='park_brake', full_name='nofx_board.Request.park_brake', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='park_brake_query', full_name='nofx_board.Request.park_brake_query', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='nofx_board.Request.version', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reset', full_name='nofx_board.Request.reset', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensors', full_name='nofx_board.Request.sensors', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time', full_name='nofx_board.Request.time', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='drive_solenoids', full_name='nofx_board.Request.drive_solenoids', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='nofx_board.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=880,
  serialized_end=1330,
)

_REPLY.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_REPLY.fields_by_name['rotary_encoder'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_rotary__encoder__pb2._REPLY
_REPLY.fields_by_name['park_brake'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_park__brake__pb2._REPLY
_REPLY.fields_by_name['park_brake_query'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_park__brake__pb2._QUERY_REPLY
_REPLY.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REPLY
_REPLY.fields_by_name['sensors'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_sensors__pb2._REPLY
_REPLY.fields_by_name['time'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._REPLY
_REPLY.fields_by_name['drive_solenoids'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_drive__solenoids__pb2._REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pong'])
_REPLY.fields_by_name['pong'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['rotary_encoder'])
_REPLY.fields_by_name['rotary_encoder'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['park_brake'])
_REPLY.fields_by_name['park_brake'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['park_brake_query'])
_REPLY.fields_by_name['park_brake_query'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['version'])
_REPLY.fields_by_name['version'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['sensors'])
_REPLY.fields_by_name['sensors'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['time'])
_REPLY.fields_by_name['time'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['drive_solenoids'])
_REPLY.fields_by_name['drive_solenoids'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REQUEST.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_REQUEST.fields_by_name['rotary_encoder'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_rotary__encoder__pb2._REQUEST
_REQUEST.fields_by_name['park_brake'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_park__brake__pb2._REQUEST
_REQUEST.fields_by_name['park_brake_query'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_park__brake__pb2._QUERY_REQUEST
_REQUEST.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REQUEST
_REQUEST.fields_by_name['reset'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._RESET_REQUEST
_REQUEST.fields_by_name['sensors'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_sensors__pb2._REQUEST
_REQUEST.fields_by_name['time'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._REQUEST
_REQUEST.fields_by_name['drive_solenoids'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_drive__solenoids__pb2._REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ping'])
_REQUEST.fields_by_name['ping'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['rotary_encoder'])
_REQUEST.fields_by_name['rotary_encoder'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['park_brake'])
_REQUEST.fields_by_name['park_brake'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['park_brake_query'])
_REQUEST.fields_by_name['park_brake_query'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['version'])
_REQUEST.fields_by_name['version'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['reset'])
_REQUEST.fields_by_name['reset'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['sensors'])
_REQUEST.fields_by_name['sensors'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['time'])
_REQUEST.fields_by_name['time'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['drive_solenoids'])
_REQUEST.fields_by_name['drive_solenoids'].containing_oneof = _REQUEST.oneofs_by_name['request']
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.nofx_board_pb2'
  # @@protoc_insertion_point(class_scope:nofx_board.Reply)
  })
_sym_db.RegisterMessage(Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.nofx_board_pb2'
  # @@protoc_insertion_point(class_scope:nofx_board.Request)
  })
_sym_db.RegisterMessage(Request)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

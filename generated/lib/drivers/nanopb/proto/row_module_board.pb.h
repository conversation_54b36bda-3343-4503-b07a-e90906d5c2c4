/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_ROW_MODULE_BOARD_ROW_MODULE_BOARD_PB_H_INCLUDED
#define PB_ROW_MODULE_BOARD_ROW_MODULE_BOARD_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"
#include "generated/lib/drivers/nanopb/proto/row_module.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _row_module_board_Reply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        row_module_Reply row_module;
    } reply;
} row_module_board_Reply;

typedef struct _row_module_board_Request {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        row_module_Request row_module;
    } request;
} row_module_board_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define row_module_board_Reply_init_default      {false, request_RequestHeader_init_default, 0, {diagnostic_Pong_init_default}}
#define row_module_board_Request_init_default    {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define row_module_board_Reply_init_zero         {false, request_RequestHeader_init_zero, 0, {diagnostic_Pong_init_zero}}
#define row_module_board_Request_init_zero       {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define row_module_board_Reply_header_tag        1
#define row_module_board_Reply_pong_tag          2
#define row_module_board_Reply_row_module_tag    3
#define row_module_board_Request_header_tag      1
#define row_module_board_Request_ping_tag        2
#define row_module_board_Request_row_module_tag  3

/* Struct field encoding specification for nanopb */
#define row_module_board_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,row_module,reply.row_module),   3)
#define row_module_board_Reply_CALLBACK NULL
#define row_module_board_Reply_DEFAULT NULL
#define row_module_board_Reply_header_MSGTYPE request_RequestHeader
#define row_module_board_Reply_reply_pong_MSGTYPE diagnostic_Pong
#define row_module_board_Reply_reply_row_module_MSGTYPE row_module_Reply

#define row_module_board_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,row_module,request.row_module),   3)
#define row_module_board_Request_CALLBACK NULL
#define row_module_board_Request_DEFAULT NULL
#define row_module_board_Request_header_MSGTYPE request_RequestHeader
#define row_module_board_Request_request_ping_MSGTYPE diagnostic_Ping
#define row_module_board_Request_request_row_module_MSGTYPE row_module_Request

extern const pb_msgdesc_t row_module_board_Reply_msg;
extern const pb_msgdesc_t row_module_board_Request_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define row_module_board_Reply_fields &row_module_board_Reply_msg
#define row_module_board_Request_fields &row_module_board_Request_msg

/* Maximum encoded size of messages (where known) */
#if defined(request_RequestHeader_size) && defined(diagnostic_Pong_size) && defined(row_module_Reply_size)
typedef union row_module_board_Reply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f3[(6 + row_module_Reply_size)];} row_module_board_Reply_reply_size_union;
#define row_module_board_Reply_size              (6 + request_RequestHeader_size + sizeof(row_module_board_Reply_reply_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size) && defined(row_module_Request_size)
typedef union row_module_board_Request_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f3[(6 + row_module_Request_size)];} row_module_board_Request_request_size_union;
#define row_module_board_Request_size            (6 + request_RequestHeader_size + sizeof(row_module_board_Request_request_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

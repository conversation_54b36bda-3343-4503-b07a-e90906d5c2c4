"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.time_pb2 import (
    Timestamp as generated___lib___drivers___nanopb___proto___time_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class RotaryEncodersConfig_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    TypeValue = typing___NewType('TypeValue', builtin___int)
    type___TypeValue = TypeValue
    Type: _Type
    class _Type(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[RotaryEncodersConfig_Request.TypeValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        TICK = typing___cast(RotaryEncodersConfig_Request.TypeValue, 0)
        QUAD = typing___cast(RotaryEncodersConfig_Request.TypeValue, 1)
        NONE = typing___cast(RotaryEncodersConfig_Request.TypeValue, 2)
    TICK = typing___cast(RotaryEncodersConfig_Request.TypeValue, 0)
    QUAD = typing___cast(RotaryEncodersConfig_Request.TypeValue, 1)
    NONE = typing___cast(RotaryEncodersConfig_Request.TypeValue, 2)

    FL_type: type___RotaryEncodersConfig_Request.TypeValue = ...
    FR_type: type___RotaryEncodersConfig_Request.TypeValue = ...
    BL_type: type___RotaryEncodersConfig_Request.TypeValue = ...
    BR_type: type___RotaryEncodersConfig_Request.TypeValue = ...

    def __init__(self,
        *,
        FL_type : typing___Optional[type___RotaryEncodersConfig_Request.TypeValue] = None,
        FR_type : typing___Optional[type___RotaryEncodersConfig_Request.TypeValue] = None,
        BL_type : typing___Optional[type___RotaryEncodersConfig_Request.TypeValue] = None,
        BR_type : typing___Optional[type___RotaryEncodersConfig_Request.TypeValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"BL_type",b"BL_type",u"BR_type",b"BR_type",u"FL_type",b"FL_type",u"FR_type",b"FR_type"]) -> None: ...
type___RotaryEncodersConfig_Request = RotaryEncodersConfig_Request

class RotaryEncodersConfig_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___RotaryEncodersConfig_Reply = RotaryEncodersConfig_Reply

class RotaryEncoder_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___RotaryEncoder_Request = RotaryEncoder_Request

class RotaryEncoder_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    front_left_ticks: builtin___int = ...
    front_right_ticks: builtin___int = ...
    back_left_ticks: builtin___int = ...
    back_right_ticks: builtin___int = ...

    @property
    def timestamp(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    def __init__(self,
        *,
        front_left_ticks : typing___Optional[builtin___int] = None,
        front_right_ticks : typing___Optional[builtin___int] = None,
        back_left_ticks : typing___Optional[builtin___int] = None,
        back_right_ticks : typing___Optional[builtin___int] = None,
        timestamp : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"back_left_ticks",b"back_left_ticks",u"back_right_ticks",b"back_right_ticks",u"front_left_ticks",b"front_left_ticks",u"front_right_ticks",b"front_right_ticks",u"timestamp",b"timestamp"]) -> None: ...
type___RotaryEncoder_Reply = RotaryEncoder_Reply

class RotaryEncoderSnapshot_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def first(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    @property
    def last(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Timestamp: ...

    def __init__(self,
        *,
        first : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        last : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"first",b"first",u"last",b"last"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"first",b"first",u"last",b"last"]) -> None: ...
type___RotaryEncoderSnapshot_Request = RotaryEncoderSnapshot_Request

class RotaryEncoderSnapshot_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def first_before(self) -> type___RotaryEncoder_Reply: ...

    @property
    def first_after(self) -> type___RotaryEncoder_Reply: ...

    @property
    def last_before(self) -> type___RotaryEncoder_Reply: ...

    @property
    def last_after(self) -> type___RotaryEncoder_Reply: ...

    @property
    def request(self) -> type___RotaryEncoderSnapshot_Request: ...

    def __init__(self,
        *,
        first_before : typing___Optional[type___RotaryEncoder_Reply] = None,
        first_after : typing___Optional[type___RotaryEncoder_Reply] = None,
        last_before : typing___Optional[type___RotaryEncoder_Reply] = None,
        last_after : typing___Optional[type___RotaryEncoder_Reply] = None,
        request : typing___Optional[type___RotaryEncoderSnapshot_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"first_after",b"first_after",u"first_before",b"first_before",u"last_after",b"last_after",u"last_before",b"last_before",u"request",b"request"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"first_after",b"first_after",u"first_before",b"first_before",u"last_after",b"last_after",u"last_before",b"last_before",u"request",b"request"]) -> None: ...
type___RotaryEncoderSnapshot_Reply = RotaryEncoderSnapshot_Reply

class RotaryEncoderHistoryVerify_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___RotaryEncoderHistoryVerify_Request = RotaryEncoderHistoryVerify_Request

class RotaryEncoderHistoryVerify_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    num_time_warps: builtin___int = ...
    num_ooo_elements: builtin___int = ...
    max_usec_distance: builtin___int = ...
    num_epoch_resets: builtin___int = ...
    num_plus1_usec: builtin___int = ...
    num_missed_signal: builtin___int = ...
    num_reject_signal: builtin___int = ...
    num_short_time: builtin___int = ...
    num_long_time: builtin___int = ...

    def __init__(self,
        *,
        num_time_warps : typing___Optional[builtin___int] = None,
        num_ooo_elements : typing___Optional[builtin___int] = None,
        max_usec_distance : typing___Optional[builtin___int] = None,
        num_epoch_resets : typing___Optional[builtin___int] = None,
        num_plus1_usec : typing___Optional[builtin___int] = None,
        num_missed_signal : typing___Optional[builtin___int] = None,
        num_reject_signal : typing___Optional[builtin___int] = None,
        num_short_time : typing___Optional[builtin___int] = None,
        num_long_time : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"max_usec_distance",b"max_usec_distance",u"num_epoch_resets",b"num_epoch_resets",u"num_long_time",b"num_long_time",u"num_missed_signal",b"num_missed_signal",u"num_ooo_elements",b"num_ooo_elements",u"num_plus1_usec",b"num_plus1_usec",u"num_reject_signal",b"num_reject_signal",u"num_short_time",b"num_short_time",u"num_time_warps",b"num_time_warps"]) -> None: ...
type___RotaryEncoderHistoryVerify_Reply = RotaryEncoderHistoryVerify_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def rotary(self) -> type___RotaryEncoder_Request: ...

    @property
    def config(self) -> type___RotaryEncodersConfig_Request: ...

    @property
    def rotary_snapshot(self) -> type___RotaryEncoderSnapshot_Request: ...

    @property
    def history_verify(self) -> type___RotaryEncoderHistoryVerify_Request: ...

    def __init__(self,
        *,
        rotary : typing___Optional[type___RotaryEncoder_Request] = None,
        config : typing___Optional[type___RotaryEncodersConfig_Request] = None,
        rotary_snapshot : typing___Optional[type___RotaryEncoderSnapshot_Request] = None,
        history_verify : typing___Optional[type___RotaryEncoderHistoryVerify_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config",u"history_verify",b"history_verify",u"request",b"request",u"rotary",b"rotary",u"rotary_snapshot",b"rotary_snapshot"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"history_verify",b"history_verify",u"request",b"request",u"rotary",b"rotary",u"rotary_snapshot",b"rotary_snapshot"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["rotary","config","rotary_snapshot","history_verify"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def rotary(self) -> type___RotaryEncoder_Reply: ...

    @property
    def config(self) -> type___RotaryEncodersConfig_Reply: ...

    @property
    def rotary_snapshot(self) -> type___RotaryEncoderSnapshot_Reply: ...

    @property
    def history_verify(self) -> type___RotaryEncoderHistoryVerify_Reply: ...

    def __init__(self,
        *,
        rotary : typing___Optional[type___RotaryEncoder_Reply] = None,
        config : typing___Optional[type___RotaryEncodersConfig_Reply] = None,
        rotary_snapshot : typing___Optional[type___RotaryEncoderSnapshot_Reply] = None,
        history_verify : typing___Optional[type___RotaryEncoderHistoryVerify_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config",u"history_verify",b"history_verify",u"reply",b"reply",u"rotary",b"rotary",u"rotary_snapshot",b"rotary_snapshot"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"history_verify",b"history_verify",u"reply",b"reply",u"rotary",b"rotary",u"rotary_snapshot",b"rotary_snapshot"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["rotary","config","rotary_snapshot","history_verify"]: ...
type___Reply = Reply

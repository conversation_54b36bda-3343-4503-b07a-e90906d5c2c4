# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/epos.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import can_open_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_can__open__pb2
from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/epos.proto',
  package='epos',
  syntax='proto3',
  serialized_options=b'Z\013nanopb/epos',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n-generated/lib/drivers/nanopb/proto/epos.proto\x12\x04\x65pos\x1a\x31generated/lib/drivers/nanopb/proto/can_open.proto\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\"\x14\n\x12Setup_PDOs_Request\"\x10\n\x0e\x45nable_Request\"\x11\n\x0f\x44isable_Request\"5\n\x10Hard_Home_Params\x12\x11\n\tstep_size\x18\x01 \x01(\x05\x12\x0e\n\x06offset\x18\x02 \x01(\r\"?\n\x12Switch_Home_Params\x12\x11\n\tstep_size\x18\x01 \x01(\x05\x12\x16\n\x0ethreshold_step\x18\x02 \x01(\r\",\n\x1b\x41\x63tual_Position_Home_Params\x12\r\n\x05range\x18\x01 \x01(\r\"\x8a\x02\n\x0bHome_Params\x12\x14\n\x0cmin_position\x18\x01 \x01(\x05\x12\x14\n\x0cmax_position\x18\x02 \x01(\x05\x12\x18\n\x10profile_velocity\x18\x03 \x01(\r\x12+\n\thard_stop\x18\x04 \x01(\x0b\x32\x16.epos.Hard_Home_ParamsH\x00\x12\x30\n\x0climit_switch\x18\x05 \x01(\x0b\x32\x18.epos.Switch_Home_ParamsH\x00\x12<\n\x0f\x61\x63tual_position\x18\x06 \x01(\x0b\x32!.epos.Actual_Position_Home_ParamsH\x00\x12\x0e\n\x06invert\x18\x07 \x01(\x08\x42\x08\n\x06params\"1\n\x0cHome_Request\x12!\n\x06params\x18\x01 \x01(\x0b\x32\x11.epos.Home_Params\"U\n\x16\x41wait_Settling_Request\x12\x17\n\x0ftarget_position\x18\x01 \x01(\x05\x12\x0e\n\x06window\x18\x02 \x01(\r\x12\x12\n\ntimeout_ms\x18\x03 \x01(\r\"\x15\n\x13Get_Pos_Vel_Request\"W\n\rGo_To_Request\x12\x10\n\x08position\x18\x01 \x01(\x05\x12\x10\n\x08velocity\x18\x02 \x01(\r\x12\x0e\n\x06window\x18\x03 \x01(\r\x12\x12\n\ntimeout_ms\x18\x04 \x01(\r\"R\n\x14\x41wait_Status_Request\x12\x12\n\ntimeout_ms\x18\x01 \x01(\r\x12\x10\n\x08\x65xpected\x18\x02 \x01(\r\x12\x14\n\x0c\x65xpected_neg\x18\x03 \x01(\r\"p\n\x1aSet_Positional_PID_Request\x12\x0e\n\x06gain_p\x18\x01 \x01(\r\x12\x0e\n\x06gain_i\x18\x02 \x01(\r\x12\x0e\n\x06gain_d\x18\x03 \x01(\r\x12\x10\n\x08gain_ffv\x18\x04 \x01(\r\x12\x10\n\x08gain_ffa\x18\x05 \x01(\r\"q\n\x0fSet_PID_Request\x12\x11\n\tcurrent_p\x18\x01 \x01(\r\x12\x11\n\tcurrent_i\x18\x02 \x01(\r\x12\x38\n\x0epositional_pid\x18\x03 \x01(\x0b\x32 .epos.Set_Positional_PID_Request\"\x84\x01\n\x08\x45POS_PID\x12\x0e\n\x06gain_p\x18\x01 \x01(\r\x12\x0e\n\x06gain_i\x18\x02 \x01(\r\x12\x0e\n\x06gain_d\x18\x03 \x01(\r\x12\x10\n\x08gain_ffv\x18\x04 \x01(\r\x12\x10\n\x08gain_ffa\x18\x05 \x01(\r\x12\x11\n\tcurrent_p\x18\x06 \x01(\r\x12\x11\n\tcurrent_i\x18\x07 \x01(\r\"a\n\x12Set_PID_V2_Request\x12$\n\x04type\x18\x01 \x01(\x0e\x32\x16.epos.PID_Request_Type\x12\x1e\n\x04\x65pos\x18\x02 \x01(\x0b\x32\x0e.epos.EPOS_PIDH\x00\x42\x05\n\x03pid\"\x11\n\x0fGet_PID_Request\"#\n\x12Homing_Limit_Reply\x12\r\n\x05limit\x18\x01 \x01(\x05\"\'\n\x13Settling_Time_Reply\x12\x10\n\x08\x64uration\x18\x01 \x01(\r\"3\n\rPos_Vel_Reply\x12\x10\n\x08position\x18\x01 \x01(\x05\x12\x10\n\x08velocity\x18\x02 \x01(\x05\"E\n\rGet_PID_Reply\x12\r\n\x05valid\x18\x01 \x01(\x08\x12\x1e\n\x04\x65pos\x18\x02 \x01(\x0b\x32\x0e.epos.EPOS_PIDH\x00\x42\x05\n\x03pid\"\xc6\x04\n\x07Request\x12 \n\x03\x63\x61n\x18\x01 \x01(\x0b\x32\x11.can_open.RequestH\x00\x12.\n\nsetup_pdos\x18\x02 \x01(\x0b\x32\x18.epos.Setup_PDOs_RequestH\x00\x12&\n\x06\x65nable\x18\x03 \x01(\x0b\x32\x14.epos.Enable_RequestH\x00\x12(\n\x07\x64isable\x18\x04 \x01(\x0b\x32\x15.epos.Disable_RequestH\x00\x12\"\n\x04home\x18\x05 \x01(\x0b\x32\x12.epos.Home_RequestH\x00\x12.\n\x06settle\x18\x06 \x01(\x0b\x32\x1c.epos.Await_Settling_RequestH\x00\x12,\n\x07pos_vel\x18\x07 \x01(\x0b\x32\x19.epos.Get_Pos_Vel_RequestH\x00\x12$\n\x05go_to\x18\x08 \x01(\x0b\x32\x13.epos.Go_To_RequestH\x00\x12,\n\x06status\x18\t \x01(\x0b\x32\x1a.epos.Await_Status_RequestH\x00\x12:\n\x0epositional_pid\x18\n \x01(\x0b\x32 .epos.Set_Positional_PID_RequestH\x00\x12$\n\x03pid\x18\x0b \x01(\x0b\x32\x15.epos.Set_PID_RequestH\x00\x12*\n\x06pid_v2\x18\x0c \x01(\x0b\x32\x18.epos.Set_PID_V2_RequestH\x00\x12(\n\x07get_pid\x18\r \x01(\x0b\x32\x15.epos.Get_PID_RequestH\x00\x42\t\n\x07request\"\x8c\x02\n\x05Reply\x12\x17\n\x03\x61\x63k\x18\x01 \x01(\x0b\x32\x08.ack.AckH\x00\x12\x1e\n\x03\x63\x61n\x18\x02 \x01(\x0b\x32\x0f.can_open.ReplyH\x00\x12)\n\x05limit\x18\x03 \x01(\x0b\x32\x18.epos.Homing_Limit_ReplyH\x00\x12+\n\x06settle\x18\x04 \x01(\x0b\x32\x19.epos.Settling_Time_ReplyH\x00\x12&\n\x07pos_vel\x18\x05 \x01(\x0b\x32\x13.epos.Pos_Vel_ReplyH\x00\x12\x1d\n\x05\x65rror\x18\x06 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\"\n\x03pid\x18\x07 \x01(\x0b\x32\x13.epos.Get_PID_ReplyH\x00\x42\x07\n\x05reply*X\n\x10PID_Request_Type\x12\x18\n\x14PID_REQUEST_FALLBACK\x10\x00\x12\x14\n\x10PID_REQUEST_TEST\x10\x01\x12\x14\n\x10PID_REQUEST_SAVE\x10\x02\x42\rZ\x0bnanopb/eposb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_can__open__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,])

_PID_REQUEST_TYPE = _descriptor.EnumDescriptor(
  name='PID_Request_Type',
  full_name='epos.PID_Request_Type',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PID_REQUEST_FALLBACK', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PID_REQUEST_TEST', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PID_REQUEST_SAVE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2568,
  serialized_end=2656,
)
_sym_db.RegisterEnumDescriptor(_PID_REQUEST_TYPE)

PID_Request_Type = enum_type_wrapper.EnumTypeWrapper(_PID_REQUEST_TYPE)
PID_REQUEST_FALLBACK = 0
PID_REQUEST_TEST = 1
PID_REQUEST_SAVE = 2



_SETUP_PDOS_REQUEST = _descriptor.Descriptor(
  name='Setup_PDOs_Request',
  full_name='epos.Setup_PDOs_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=200,
  serialized_end=220,
)


_ENABLE_REQUEST = _descriptor.Descriptor(
  name='Enable_Request',
  full_name='epos.Enable_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=222,
  serialized_end=238,
)


_DISABLE_REQUEST = _descriptor.Descriptor(
  name='Disable_Request',
  full_name='epos.Disable_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=240,
  serialized_end=257,
)


_HARD_HOME_PARAMS = _descriptor.Descriptor(
  name='Hard_Home_Params',
  full_name='epos.Hard_Home_Params',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='step_size', full_name='epos.Hard_Home_Params.step_size', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='offset', full_name='epos.Hard_Home_Params.offset', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=259,
  serialized_end=312,
)


_SWITCH_HOME_PARAMS = _descriptor.Descriptor(
  name='Switch_Home_Params',
  full_name='epos.Switch_Home_Params',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='step_size', full_name='epos.Switch_Home_Params.step_size', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='threshold_step', full_name='epos.Switch_Home_Params.threshold_step', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=314,
  serialized_end=377,
)


_ACTUAL_POSITION_HOME_PARAMS = _descriptor.Descriptor(
  name='Actual_Position_Home_Params',
  full_name='epos.Actual_Position_Home_Params',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='range', full_name='epos.Actual_Position_Home_Params.range', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=379,
  serialized_end=423,
)


_HOME_PARAMS = _descriptor.Descriptor(
  name='Home_Params',
  full_name='epos.Home_Params',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min_position', full_name='epos.Home_Params.min_position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_position', full_name='epos.Home_Params.max_position', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='profile_velocity', full_name='epos.Home_Params.profile_velocity', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hard_stop', full_name='epos.Home_Params.hard_stop', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='limit_switch', full_name='epos.Home_Params.limit_switch', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='actual_position', full_name='epos.Home_Params.actual_position', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='invert', full_name='epos.Home_Params.invert', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='params', full_name='epos.Home_Params.params',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=426,
  serialized_end=692,
)


_HOME_REQUEST = _descriptor.Descriptor(
  name='Home_Request',
  full_name='epos.Home_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='params', full_name='epos.Home_Request.params', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=694,
  serialized_end=743,
)


_AWAIT_SETTLING_REQUEST = _descriptor.Descriptor(
  name='Await_Settling_Request',
  full_name='epos.Await_Settling_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_position', full_name='epos.Await_Settling_Request.target_position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='window', full_name='epos.Await_Settling_Request.window', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timeout_ms', full_name='epos.Await_Settling_Request.timeout_ms', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=745,
  serialized_end=830,
)


_GET_POS_VEL_REQUEST = _descriptor.Descriptor(
  name='Get_Pos_Vel_Request',
  full_name='epos.Get_Pos_Vel_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=832,
  serialized_end=853,
)


_GO_TO_REQUEST = _descriptor.Descriptor(
  name='Go_To_Request',
  full_name='epos.Go_To_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='position', full_name='epos.Go_To_Request.position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='epos.Go_To_Request.velocity', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='window', full_name='epos.Go_To_Request.window', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timeout_ms', full_name='epos.Go_To_Request.timeout_ms', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=855,
  serialized_end=942,
)


_AWAIT_STATUS_REQUEST = _descriptor.Descriptor(
  name='Await_Status_Request',
  full_name='epos.Await_Status_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timeout_ms', full_name='epos.Await_Status_Request.timeout_ms', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expected', full_name='epos.Await_Status_Request.expected', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expected_neg', full_name='epos.Await_Status_Request.expected_neg', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=944,
  serialized_end=1026,
)


_SET_POSITIONAL_PID_REQUEST = _descriptor.Descriptor(
  name='Set_Positional_PID_Request',
  full_name='epos.Set_Positional_PID_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gain_p', full_name='epos.Set_Positional_PID_Request.gain_p', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_i', full_name='epos.Set_Positional_PID_Request.gain_i', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_d', full_name='epos.Set_Positional_PID_Request.gain_d', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_ffv', full_name='epos.Set_Positional_PID_Request.gain_ffv', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_ffa', full_name='epos.Set_Positional_PID_Request.gain_ffa', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1028,
  serialized_end=1140,
)


_SET_PID_REQUEST = _descriptor.Descriptor(
  name='Set_PID_Request',
  full_name='epos.Set_PID_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_p', full_name='epos.Set_PID_Request.current_p', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_i', full_name='epos.Set_PID_Request.current_i', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='positional_pid', full_name='epos.Set_PID_Request.positional_pid', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1142,
  serialized_end=1255,
)


_EPOS_PID = _descriptor.Descriptor(
  name='EPOS_PID',
  full_name='epos.EPOS_PID',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gain_p', full_name='epos.EPOS_PID.gain_p', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_i', full_name='epos.EPOS_PID.gain_i', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_d', full_name='epos.EPOS_PID.gain_d', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_ffv', full_name='epos.EPOS_PID.gain_ffv', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gain_ffa', full_name='epos.EPOS_PID.gain_ffa', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_p', full_name='epos.EPOS_PID.current_p', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_i', full_name='epos.EPOS_PID.current_i', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1258,
  serialized_end=1390,
)


_SET_PID_V2_REQUEST = _descriptor.Descriptor(
  name='Set_PID_V2_Request',
  full_name='epos.Set_PID_V2_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='epos.Set_PID_V2_Request.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='epos', full_name='epos.Set_PID_V2_Request.epos', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='pid', full_name='epos.Set_PID_V2_Request.pid',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1392,
  serialized_end=1489,
)


_GET_PID_REQUEST = _descriptor.Descriptor(
  name='Get_PID_Request',
  full_name='epos.Get_PID_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1491,
  serialized_end=1508,
)


_HOMING_LIMIT_REPLY = _descriptor.Descriptor(
  name='Homing_Limit_Reply',
  full_name='epos.Homing_Limit_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='limit', full_name='epos.Homing_Limit_Reply.limit', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1510,
  serialized_end=1545,
)


_SETTLING_TIME_REPLY = _descriptor.Descriptor(
  name='Settling_Time_Reply',
  full_name='epos.Settling_Time_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='duration', full_name='epos.Settling_Time_Reply.duration', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1547,
  serialized_end=1586,
)


_POS_VEL_REPLY = _descriptor.Descriptor(
  name='Pos_Vel_Reply',
  full_name='epos.Pos_Vel_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='position', full_name='epos.Pos_Vel_Reply.position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='epos.Pos_Vel_Reply.velocity', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1588,
  serialized_end=1639,
)


_GET_PID_REPLY = _descriptor.Descriptor(
  name='Get_PID_Reply',
  full_name='epos.Get_PID_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='valid', full_name='epos.Get_PID_Reply.valid', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='epos', full_name='epos.Get_PID_Reply.epos', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='pid', full_name='epos.Get_PID_Reply.pid',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1641,
  serialized_end=1710,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='epos.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='can', full_name='epos.Request.can', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='setup_pdos', full_name='epos.Request.setup_pdos', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enable', full_name='epos.Request.enable', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='disable', full_name='epos.Request.disable', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='home', full_name='epos.Request.home', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='settle', full_name='epos.Request.settle', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pos_vel', full_name='epos.Request.pos_vel', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='go_to', full_name='epos.Request.go_to', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='epos.Request.status', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='positional_pid', full_name='epos.Request.positional_pid', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pid', full_name='epos.Request.pid', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pid_v2', full_name='epos.Request.pid_v2', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_pid', full_name='epos.Request.get_pid', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='epos.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1713,
  serialized_end=2295,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='epos.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ack', full_name='epos.Reply.ack', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='can', full_name='epos.Reply.can', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='limit', full_name='epos.Reply.limit', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='settle', full_name='epos.Reply.settle', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pos_vel', full_name='epos.Reply.pos_vel', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error', full_name='epos.Reply.error', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pid', full_name='epos.Reply.pid', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='epos.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2298,
  serialized_end=2566,
)

_HOME_PARAMS.fields_by_name['hard_stop'].message_type = _HARD_HOME_PARAMS
_HOME_PARAMS.fields_by_name['limit_switch'].message_type = _SWITCH_HOME_PARAMS
_HOME_PARAMS.fields_by_name['actual_position'].message_type = _ACTUAL_POSITION_HOME_PARAMS
_HOME_PARAMS.oneofs_by_name['params'].fields.append(
  _HOME_PARAMS.fields_by_name['hard_stop'])
_HOME_PARAMS.fields_by_name['hard_stop'].containing_oneof = _HOME_PARAMS.oneofs_by_name['params']
_HOME_PARAMS.oneofs_by_name['params'].fields.append(
  _HOME_PARAMS.fields_by_name['limit_switch'])
_HOME_PARAMS.fields_by_name['limit_switch'].containing_oneof = _HOME_PARAMS.oneofs_by_name['params']
_HOME_PARAMS.oneofs_by_name['params'].fields.append(
  _HOME_PARAMS.fields_by_name['actual_position'])
_HOME_PARAMS.fields_by_name['actual_position'].containing_oneof = _HOME_PARAMS.oneofs_by_name['params']
_HOME_REQUEST.fields_by_name['params'].message_type = _HOME_PARAMS
_SET_PID_REQUEST.fields_by_name['positional_pid'].message_type = _SET_POSITIONAL_PID_REQUEST
_SET_PID_V2_REQUEST.fields_by_name['type'].enum_type = _PID_REQUEST_TYPE
_SET_PID_V2_REQUEST.fields_by_name['epos'].message_type = _EPOS_PID
_SET_PID_V2_REQUEST.oneofs_by_name['pid'].fields.append(
  _SET_PID_V2_REQUEST.fields_by_name['epos'])
_SET_PID_V2_REQUEST.fields_by_name['epos'].containing_oneof = _SET_PID_V2_REQUEST.oneofs_by_name['pid']
_GET_PID_REPLY.fields_by_name['epos'].message_type = _EPOS_PID
_GET_PID_REPLY.oneofs_by_name['pid'].fields.append(
  _GET_PID_REPLY.fields_by_name['epos'])
_GET_PID_REPLY.fields_by_name['epos'].containing_oneof = _GET_PID_REPLY.oneofs_by_name['pid']
_REQUEST.fields_by_name['can'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_can__open__pb2._REQUEST
_REQUEST.fields_by_name['setup_pdos'].message_type = _SETUP_PDOS_REQUEST
_REQUEST.fields_by_name['enable'].message_type = _ENABLE_REQUEST
_REQUEST.fields_by_name['disable'].message_type = _DISABLE_REQUEST
_REQUEST.fields_by_name['home'].message_type = _HOME_REQUEST
_REQUEST.fields_by_name['settle'].message_type = _AWAIT_SETTLING_REQUEST
_REQUEST.fields_by_name['pos_vel'].message_type = _GET_POS_VEL_REQUEST
_REQUEST.fields_by_name['go_to'].message_type = _GO_TO_REQUEST
_REQUEST.fields_by_name['status'].message_type = _AWAIT_STATUS_REQUEST
_REQUEST.fields_by_name['positional_pid'].message_type = _SET_POSITIONAL_PID_REQUEST
_REQUEST.fields_by_name['pid'].message_type = _SET_PID_REQUEST
_REQUEST.fields_by_name['pid_v2'].message_type = _SET_PID_V2_REQUEST
_REQUEST.fields_by_name['get_pid'].message_type = _GET_PID_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['can'])
_REQUEST.fields_by_name['can'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['setup_pdos'])
_REQUEST.fields_by_name['setup_pdos'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['enable'])
_REQUEST.fields_by_name['enable'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['disable'])
_REQUEST.fields_by_name['disable'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['home'])
_REQUEST.fields_by_name['home'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['settle'])
_REQUEST.fields_by_name['settle'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['pos_vel'])
_REQUEST.fields_by_name['pos_vel'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['go_to'])
_REQUEST.fields_by_name['go_to'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['status'])
_REQUEST.fields_by_name['status'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['positional_pid'])
_REQUEST.fields_by_name['positional_pid'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['pid'])
_REQUEST.fields_by_name['pid'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['pid_v2'])
_REQUEST.fields_by_name['pid_v2'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get_pid'])
_REQUEST.fields_by_name['get_pid'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['can'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_can__open__pb2._REPLY
_REPLY.fields_by_name['limit'].message_type = _HOMING_LIMIT_REPLY
_REPLY.fields_by_name['settle'].message_type = _SETTLING_TIME_REPLY
_REPLY.fields_by_name['pos_vel'].message_type = _POS_VEL_REPLY
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['pid'].message_type = _GET_PID_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['can'])
_REPLY.fields_by_name['can'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['limit'])
_REPLY.fields_by_name['limit'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['settle'])
_REPLY.fields_by_name['settle'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pos_vel'])
_REPLY.fields_by_name['pos_vel'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pid'])
_REPLY.fields_by_name['pid'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Setup_PDOs_Request'] = _SETUP_PDOS_REQUEST
DESCRIPTOR.message_types_by_name['Enable_Request'] = _ENABLE_REQUEST
DESCRIPTOR.message_types_by_name['Disable_Request'] = _DISABLE_REQUEST
DESCRIPTOR.message_types_by_name['Hard_Home_Params'] = _HARD_HOME_PARAMS
DESCRIPTOR.message_types_by_name['Switch_Home_Params'] = _SWITCH_HOME_PARAMS
DESCRIPTOR.message_types_by_name['Actual_Position_Home_Params'] = _ACTUAL_POSITION_HOME_PARAMS
DESCRIPTOR.message_types_by_name['Home_Params'] = _HOME_PARAMS
DESCRIPTOR.message_types_by_name['Home_Request'] = _HOME_REQUEST
DESCRIPTOR.message_types_by_name['Await_Settling_Request'] = _AWAIT_SETTLING_REQUEST
DESCRIPTOR.message_types_by_name['Get_Pos_Vel_Request'] = _GET_POS_VEL_REQUEST
DESCRIPTOR.message_types_by_name['Go_To_Request'] = _GO_TO_REQUEST
DESCRIPTOR.message_types_by_name['Await_Status_Request'] = _AWAIT_STATUS_REQUEST
DESCRIPTOR.message_types_by_name['Set_Positional_PID_Request'] = _SET_POSITIONAL_PID_REQUEST
DESCRIPTOR.message_types_by_name['Set_PID_Request'] = _SET_PID_REQUEST
DESCRIPTOR.message_types_by_name['EPOS_PID'] = _EPOS_PID
DESCRIPTOR.message_types_by_name['Set_PID_V2_Request'] = _SET_PID_V2_REQUEST
DESCRIPTOR.message_types_by_name['Get_PID_Request'] = _GET_PID_REQUEST
DESCRIPTOR.message_types_by_name['Homing_Limit_Reply'] = _HOMING_LIMIT_REPLY
DESCRIPTOR.message_types_by_name['Settling_Time_Reply'] = _SETTLING_TIME_REPLY
DESCRIPTOR.message_types_by_name['Pos_Vel_Reply'] = _POS_VEL_REPLY
DESCRIPTOR.message_types_by_name['Get_PID_Reply'] = _GET_PID_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.enum_types_by_name['PID_Request_Type'] = _PID_REQUEST_TYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Setup_PDOs_Request = _reflection.GeneratedProtocolMessageType('Setup_PDOs_Request', (_message.Message,), {
  'DESCRIPTOR' : _SETUP_PDOS_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Setup_PDOs_Request)
  })
_sym_db.RegisterMessage(Setup_PDOs_Request)

Enable_Request = _reflection.GeneratedProtocolMessageType('Enable_Request', (_message.Message,), {
  'DESCRIPTOR' : _ENABLE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Enable_Request)
  })
_sym_db.RegisterMessage(Enable_Request)

Disable_Request = _reflection.GeneratedProtocolMessageType('Disable_Request', (_message.Message,), {
  'DESCRIPTOR' : _DISABLE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Disable_Request)
  })
_sym_db.RegisterMessage(Disable_Request)

Hard_Home_Params = _reflection.GeneratedProtocolMessageType('Hard_Home_Params', (_message.Message,), {
  'DESCRIPTOR' : _HARD_HOME_PARAMS,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Hard_Home_Params)
  })
_sym_db.RegisterMessage(Hard_Home_Params)

Switch_Home_Params = _reflection.GeneratedProtocolMessageType('Switch_Home_Params', (_message.Message,), {
  'DESCRIPTOR' : _SWITCH_HOME_PARAMS,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Switch_Home_Params)
  })
_sym_db.RegisterMessage(Switch_Home_Params)

Actual_Position_Home_Params = _reflection.GeneratedProtocolMessageType('Actual_Position_Home_Params', (_message.Message,), {
  'DESCRIPTOR' : _ACTUAL_POSITION_HOME_PARAMS,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Actual_Position_Home_Params)
  })
_sym_db.RegisterMessage(Actual_Position_Home_Params)

Home_Params = _reflection.GeneratedProtocolMessageType('Home_Params', (_message.Message,), {
  'DESCRIPTOR' : _HOME_PARAMS,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Home_Params)
  })
_sym_db.RegisterMessage(Home_Params)

Home_Request = _reflection.GeneratedProtocolMessageType('Home_Request', (_message.Message,), {
  'DESCRIPTOR' : _HOME_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Home_Request)
  })
_sym_db.RegisterMessage(Home_Request)

Await_Settling_Request = _reflection.GeneratedProtocolMessageType('Await_Settling_Request', (_message.Message,), {
  'DESCRIPTOR' : _AWAIT_SETTLING_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Await_Settling_Request)
  })
_sym_db.RegisterMessage(Await_Settling_Request)

Get_Pos_Vel_Request = _reflection.GeneratedProtocolMessageType('Get_Pos_Vel_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_POS_VEL_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Get_Pos_Vel_Request)
  })
_sym_db.RegisterMessage(Get_Pos_Vel_Request)

Go_To_Request = _reflection.GeneratedProtocolMessageType('Go_To_Request', (_message.Message,), {
  'DESCRIPTOR' : _GO_TO_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Go_To_Request)
  })
_sym_db.RegisterMessage(Go_To_Request)

Await_Status_Request = _reflection.GeneratedProtocolMessageType('Await_Status_Request', (_message.Message,), {
  'DESCRIPTOR' : _AWAIT_STATUS_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Await_Status_Request)
  })
_sym_db.RegisterMessage(Await_Status_Request)

Set_Positional_PID_Request = _reflection.GeneratedProtocolMessageType('Set_Positional_PID_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_POSITIONAL_PID_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Set_Positional_PID_Request)
  })
_sym_db.RegisterMessage(Set_Positional_PID_Request)

Set_PID_Request = _reflection.GeneratedProtocolMessageType('Set_PID_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_PID_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Set_PID_Request)
  })
_sym_db.RegisterMessage(Set_PID_Request)

EPOS_PID = _reflection.GeneratedProtocolMessageType('EPOS_PID', (_message.Message,), {
  'DESCRIPTOR' : _EPOS_PID,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.EPOS_PID)
  })
_sym_db.RegisterMessage(EPOS_PID)

Set_PID_V2_Request = _reflection.GeneratedProtocolMessageType('Set_PID_V2_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_PID_V2_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Set_PID_V2_Request)
  })
_sym_db.RegisterMessage(Set_PID_V2_Request)

Get_PID_Request = _reflection.GeneratedProtocolMessageType('Get_PID_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_PID_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Get_PID_Request)
  })
_sym_db.RegisterMessage(Get_PID_Request)

Homing_Limit_Reply = _reflection.GeneratedProtocolMessageType('Homing_Limit_Reply', (_message.Message,), {
  'DESCRIPTOR' : _HOMING_LIMIT_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Homing_Limit_Reply)
  })
_sym_db.RegisterMessage(Homing_Limit_Reply)

Settling_Time_Reply = _reflection.GeneratedProtocolMessageType('Settling_Time_Reply', (_message.Message,), {
  'DESCRIPTOR' : _SETTLING_TIME_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Settling_Time_Reply)
  })
_sym_db.RegisterMessage(Settling_Time_Reply)

Pos_Vel_Reply = _reflection.GeneratedProtocolMessageType('Pos_Vel_Reply', (_message.Message,), {
  'DESCRIPTOR' : _POS_VEL_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Pos_Vel_Reply)
  })
_sym_db.RegisterMessage(Pos_Vel_Reply)

Get_PID_Reply = _reflection.GeneratedProtocolMessageType('Get_PID_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GET_PID_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Get_PID_Reply)
  })
_sym_db.RegisterMessage(Get_PID_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.epos_pb2'
  # @@protoc_insertion_point(class_scope:epos.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

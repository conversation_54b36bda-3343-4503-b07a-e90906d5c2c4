/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "arc_detector.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(arc_detector_Config, arc_detector_Config, AUTO)


PB_BIND(arc_detector_Reset_Alarm_Request, arc_detector_Reset_Alarm_Request, AUTO)


PB_BIND(arc_detector_Status_Request, arc_detector_Status_Request, AUTO)


PB_BIND(arc_detector_Status_Reply, arc_detector_Status_Reply, AUTO)


PB_BIND(arc_detector_Set_Config_Request, arc_detector_Set_Config_Request, AUTO)


PB_BIND(arc_detector_Get_Config_Request, arc_detector_Get_Config_Request, AUTO)


PB_BIND(arc_detector_Config_Reply, arc_detector_Config_Reply, AUTO)


PB_BIND(arc_detector_Request, arc_detector_Request, AUTO)


PB_BIND(arc_detector_Reply, arc_detector_Reply, 2)




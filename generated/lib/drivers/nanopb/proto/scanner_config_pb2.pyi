"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Delta_Target_Config(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pan_skew: builtin___float = ...
    tilt_skew: builtin___float = ...

    def __init__(self,
        *,
        pan_skew : typing___Optional[builtin___float] = None,
        tilt_skew : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan_skew",b"pan_skew",u"tilt_skew",b"tilt_skew"]) -> None: ...
type___Delta_Target_Config = Delta_Target_Config

class Color_Config(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    red: builtin___float = ...
    green: builtin___float = ...
    blue: builtin___float = ...

    def __init__(self,
        *,
        red : typing___Optional[builtin___float] = None,
        green : typing___Optional[builtin___float] = None,
        blue : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"blue",b"blue",u"green",b"green",u"red",b"red"]) -> None: ...
type___Color_Config = Color_Config

class Camera_Serial_Number_Config(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    serial_number: builtin___bytes = ...

    def __init__(self,
        *,
        serial_number : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"serial_number",b"serial_number"]) -> None: ...
type___Camera_Serial_Number_Config = Camera_Serial_Number_Config

class Scanner_Barcode_Str_Config(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    barcode: builtin___bytes = ...

    def __init__(self,
        *,
        barcode : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"barcode",b"barcode"]) -> None: ...
type___Scanner_Barcode_Str_Config = Scanner_Barcode_Str_Config

class Set_Delta_Target_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Delta_Target_Config: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Delta_Target_Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___Set_Delta_Target_Config_Request = Set_Delta_Target_Config_Request

class Get_Delta_Target_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Delta_Target_Config_Request = Get_Delta_Target_Config_Request

class Set_Color_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Color_Config: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Color_Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___Set_Color_Config_Request = Set_Color_Config_Request

class Get_Color_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Color_Config_Request = Get_Color_Config_Request

class Set_Camera_Serial_Number_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Camera_Serial_Number_Config: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Camera_Serial_Number_Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___Set_Camera_Serial_Number_Config_Request = Set_Camera_Serial_Number_Config_Request

class Get_Camera_Serial_Number_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Camera_Serial_Number_Config_Request = Get_Camera_Serial_Number_Config_Request

class Set_HW_Revision_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    revision: builtin___int = ...

    def __init__(self,
        *,
        revision : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"revision",b"revision"]) -> None: ...
type___Set_HW_Revision_Request = Set_HW_Revision_Request

class Get_HW_Revision_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_HW_Revision_Request = Get_HW_Revision_Request

class Set_Scanner_Barcode_Str_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Scanner_Barcode_Str_Config: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Scanner_Barcode_Str_Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___Set_Scanner_Barcode_Str_Request = Set_Scanner_Barcode_Str_Request

class Get_Scanner_Barcode_Str_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Scanner_Barcode_Str_Request = Get_Scanner_Barcode_Str_Request

class Get_Delta_Target_Config_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Delta_Target_Config: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Delta_Target_Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___Get_Delta_Target_Config_Reply = Get_Delta_Target_Config_Reply

class Get_Color_Config_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Color_Config: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Color_Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___Get_Color_Config_Reply = Get_Color_Config_Reply

class Get_Camera_Serial_Number_Config_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Camera_Serial_Number_Config: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Camera_Serial_Number_Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___Get_Camera_Serial_Number_Config_Reply = Get_Camera_Serial_Number_Config_Reply

class Get_HW_Revision_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    revision: builtin___int = ...

    def __init__(self,
        *,
        revision : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"revision",b"revision"]) -> None: ...
type___Get_HW_Revision_Reply = Get_HW_Revision_Reply

class Get_Scanner_Barcode_Str_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___Scanner_Barcode_Str_Config: ...

    def __init__(self,
        *,
        config : typing___Optional[type___Scanner_Barcode_Str_Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> None: ...
type___Get_Scanner_Barcode_Str_Reply = Get_Scanner_Barcode_Str_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def set_dt(self) -> type___Set_Delta_Target_Config_Request: ...

    @property
    def get_dt(self) -> type___Get_Delta_Target_Config_Request: ...

    @property
    def set_color(self) -> type___Set_Color_Config_Request: ...

    @property
    def get_color(self) -> type___Get_Color_Config_Request: ...

    @property
    def set_sn(self) -> type___Set_Camera_Serial_Number_Config_Request: ...

    @property
    def get_sn(self) -> type___Get_Camera_Serial_Number_Config_Request: ...

    @property
    def set_hw(self) -> type___Set_HW_Revision_Request: ...

    @property
    def get_hw(self) -> type___Get_HW_Revision_Request: ...

    @property
    def set_bc(self) -> type___Set_Scanner_Barcode_Str_Request: ...

    @property
    def get_bc(self) -> type___Get_Scanner_Barcode_Str_Request: ...

    def __init__(self,
        *,
        set_dt : typing___Optional[type___Set_Delta_Target_Config_Request] = None,
        get_dt : typing___Optional[type___Get_Delta_Target_Config_Request] = None,
        set_color : typing___Optional[type___Set_Color_Config_Request] = None,
        get_color : typing___Optional[type___Get_Color_Config_Request] = None,
        set_sn : typing___Optional[type___Set_Camera_Serial_Number_Config_Request] = None,
        get_sn : typing___Optional[type___Get_Camera_Serial_Number_Config_Request] = None,
        set_hw : typing___Optional[type___Set_HW_Revision_Request] = None,
        get_hw : typing___Optional[type___Get_HW_Revision_Request] = None,
        set_bc : typing___Optional[type___Set_Scanner_Barcode_Str_Request] = None,
        get_bc : typing___Optional[type___Get_Scanner_Barcode_Str_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"get_bc",b"get_bc",u"get_color",b"get_color",u"get_dt",b"get_dt",u"get_hw",b"get_hw",u"get_sn",b"get_sn",u"request",b"request",u"set_bc",b"set_bc",u"set_color",b"set_color",u"set_dt",b"set_dt",u"set_hw",b"set_hw",u"set_sn",b"set_sn"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"get_bc",b"get_bc",u"get_color",b"get_color",u"get_dt",b"get_dt",u"get_hw",b"get_hw",u"get_sn",b"get_sn",u"request",b"request",u"set_bc",b"set_bc",u"set_color",b"set_color",u"set_dt",b"set_dt",u"set_hw",b"set_hw",u"set_sn",b"set_sn"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["set_dt","get_dt","set_color","get_color","set_sn","get_sn","set_hw","get_hw","set_bc","get_bc"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def dt(self) -> type___Get_Delta_Target_Config_Reply: ...

    @property
    def color(self) -> type___Get_Color_Config_Reply: ...

    @property
    def sn(self) -> type___Get_Camera_Serial_Number_Config_Reply: ...

    @property
    def hw(self) -> type___Get_HW_Revision_Reply: ...

    @property
    def bc(self) -> type___Get_Scanner_Barcode_Str_Reply: ...

    def __init__(self,
        *,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        dt : typing___Optional[type___Get_Delta_Target_Config_Reply] = None,
        color : typing___Optional[type___Get_Color_Config_Reply] = None,
        sn : typing___Optional[type___Get_Camera_Serial_Number_Config_Reply] = None,
        hw : typing___Optional[type___Get_HW_Revision_Reply] = None,
        bc : typing___Optional[type___Get_Scanner_Barcode_Str_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"bc",b"bc",u"color",b"color",u"dt",b"dt",u"error",b"error",u"hw",b"hw",u"reply",b"reply",u"sn",b"sn"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"bc",b"bc",u"color",b"color",u"dt",b"dt",u"error",b"error",u"hw",b"hw",u"reply",b"reply",u"sn",b"sn"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","dt","color","sn","hw","bc"]: ...
type___Reply = Reply

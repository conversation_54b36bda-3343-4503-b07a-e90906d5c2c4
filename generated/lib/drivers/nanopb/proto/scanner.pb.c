/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "scanner.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(scanner_Laser_Request, scanner_Laser_Request, AUTO)


PB_BIND(scanner_Get_Laser_Request, scanner_Get_Laser_Request, AUTO)


PB_BIND(scanner_Intensity_Request, scanner_Intensity_Request, AUTO)


PB_BIND(scanner_Boot_Request, scanner_Boot_Request, 2)


PB_BIND(scanner_Stop_Request, scanner_Stop_Request, AUTO)


PB_BIND(scanner_Gimbal_Request, scanner_Gimbal_Request, 2)


PB_BIND(scanner_Error_Reply, scanner_Error_Reply, AUTO)


PB_BIND(scanner_ACK_Reply, scanner_ACK_Reply, AUTO)


PB_BIND(scanner_Laser_State_Reply, scanner_Laser_State_Reply, AUTO)


PB_BIND(scanner_Gimbal_Reply, scanner_Gimbal_Reply, 2)


PB_BIND(scanner_Request, scanner_Request, 2)


PB_BIND(scanner_Reply, scanner_Reply, 2)




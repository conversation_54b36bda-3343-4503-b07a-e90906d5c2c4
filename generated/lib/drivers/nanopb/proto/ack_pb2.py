# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/ack.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/ack.proto',
  package='ack',
  syntax='proto3',
  serialized_options=b'Z\nnanopb/ack',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n,generated/lib/drivers/nanopb/proto/ack.proto\x12\x03\x61\x63k\"\x05\n\x03\x41\x63kB\x0cZ\nnanopb/ackb\x06proto3'
)




_ACK = _descriptor.Descriptor(
  name='Ack',
  full_name='ack.Ack',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=53,
  serialized_end=58,
)

DESCRIPTOR.message_types_by_name['Ack'] = _ACK
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Ack = _reflection.GeneratedProtocolMessageType('Ack', (_message.Message,), {
  'DESCRIPTOR' : _ACK,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ack_pb2'
  # @@protoc_insertion_point(class_scope:ack.Ack)
  })
_sym_db.RegisterMessage(Ack)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_SCANNER_SCANNER_PB_H_INCLUDED
#define PB_SCANNER_SCANNER_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/servo.pb.h"
#include "generated/lib/drivers/nanopb/proto/epos.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _scanner_ACK_Reply {
    char dummy_field;
} scanner_ACK_Reply;

typedef struct _scanner_Error_Reply {
    char dummy_field;
} scanner_Error_Reply;

typedef struct _scanner_Get_Laser_Request {
    char dummy_field;
} scanner_Get_Laser_Request;

typedef struct _scanner_Stop_Request {
    char dummy_field;
} scanner_Stop_Request;

typedef struct _scanner_Boot_Request {
    bool has_pan_params;
    epos_Home_Params pan_params;
    bool has_tilt_params;
    epos_Home_Params tilt_params;
} scanner_Boot_Request;

typedef struct _scanner_Gimbal_Reply {
    bool has_pan;
    servo_Reply pan;
    bool has_tilt;
    servo_Reply tilt;
} scanner_Gimbal_Reply;

typedef struct _scanner_Gimbal_Request {
    bool has_pan;
    servo_Request pan;
    bool has_tilt;
    servo_Request tilt;
} scanner_Gimbal_Request;

typedef struct _scanner_Intensity_Request {
    int32_t intensity;
} scanner_Intensity_Request;

typedef struct _scanner_Laser_Request {
    bool on;
} scanner_Laser_Request;

typedef struct _scanner_Laser_State_Reply {
    bool on;
} scanner_Laser_State_Reply;

typedef struct _scanner_Reply {
    pb_size_t which_reply;
    union {
        scanner_Error_Reply error;
        scanner_ACK_Reply ack;
        scanner_Laser_State_Reply laser;
        scanner_Gimbal_Reply gimbal;
    } reply;
} scanner_Reply;

typedef struct _scanner_Request {
    pb_size_t which_request;
    union {
        scanner_Laser_Request laser;
        scanner_Get_Laser_Request get_laser;
        scanner_Boot_Request boot;
        scanner_Stop_Request stop;
        scanner_Gimbal_Request gimbal;
        scanner_Intensity_Request intensity;
    } request;
} scanner_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define scanner_Laser_Request_init_default       {0}
#define scanner_Get_Laser_Request_init_default   {0}
#define scanner_Intensity_Request_init_default   {0}
#define scanner_Boot_Request_init_default        {false, epos_Home_Params_init_default, false, epos_Home_Params_init_default}
#define scanner_Stop_Request_init_default        {0}
#define scanner_Gimbal_Request_init_default      {false, servo_Request_init_default, false, servo_Request_init_default}
#define scanner_Error_Reply_init_default         {0}
#define scanner_ACK_Reply_init_default           {0}
#define scanner_Laser_State_Reply_init_default   {0}
#define scanner_Gimbal_Reply_init_default        {false, servo_Reply_init_default, false, servo_Reply_init_default}
#define scanner_Request_init_default             {0, {scanner_Laser_Request_init_default}}
#define scanner_Reply_init_default               {0, {scanner_Error_Reply_init_default}}
#define scanner_Laser_Request_init_zero          {0}
#define scanner_Get_Laser_Request_init_zero      {0}
#define scanner_Intensity_Request_init_zero      {0}
#define scanner_Boot_Request_init_zero           {false, epos_Home_Params_init_zero, false, epos_Home_Params_init_zero}
#define scanner_Stop_Request_init_zero           {0}
#define scanner_Gimbal_Request_init_zero         {false, servo_Request_init_zero, false, servo_Request_init_zero}
#define scanner_Error_Reply_init_zero            {0}
#define scanner_ACK_Reply_init_zero              {0}
#define scanner_Laser_State_Reply_init_zero      {0}
#define scanner_Gimbal_Reply_init_zero           {false, servo_Reply_init_zero, false, servo_Reply_init_zero}
#define scanner_Request_init_zero                {0, {scanner_Laser_Request_init_zero}}
#define scanner_Reply_init_zero                  {0, {scanner_Error_Reply_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define scanner_Boot_Request_pan_params_tag      1
#define scanner_Boot_Request_tilt_params_tag     2
#define scanner_Gimbal_Reply_pan_tag             1
#define scanner_Gimbal_Reply_tilt_tag            2
#define scanner_Gimbal_Request_pan_tag           1
#define scanner_Gimbal_Request_tilt_tag          2
#define scanner_Intensity_Request_intensity_tag  1
#define scanner_Laser_Request_on_tag             1
#define scanner_Laser_State_Reply_on_tag         1
#define scanner_Reply_error_tag                  1
#define scanner_Reply_ack_tag                    2
#define scanner_Reply_laser_tag                  3
#define scanner_Reply_gimbal_tag                 4
#define scanner_Request_laser_tag                1
#define scanner_Request_get_laser_tag            2
#define scanner_Request_boot_tag                 3
#define scanner_Request_stop_tag                 4
#define scanner_Request_gimbal_tag               5
#define scanner_Request_intensity_tag            6

/* Struct field encoding specification for nanopb */
#define scanner_Laser_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     on,                1)
#define scanner_Laser_Request_CALLBACK NULL
#define scanner_Laser_Request_DEFAULT NULL

#define scanner_Get_Laser_Request_FIELDLIST(X, a) \

#define scanner_Get_Laser_Request_CALLBACK NULL
#define scanner_Get_Laser_Request_DEFAULT NULL

#define scanner_Intensity_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    intensity,         1)
#define scanner_Intensity_Request_CALLBACK NULL
#define scanner_Intensity_Request_DEFAULT NULL

#define scanner_Boot_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pan_params,        1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  tilt_params,       2)
#define scanner_Boot_Request_CALLBACK NULL
#define scanner_Boot_Request_DEFAULT NULL
#define scanner_Boot_Request_pan_params_MSGTYPE epos_Home_Params
#define scanner_Boot_Request_tilt_params_MSGTYPE epos_Home_Params

#define scanner_Stop_Request_FIELDLIST(X, a) \

#define scanner_Stop_Request_CALLBACK NULL
#define scanner_Stop_Request_DEFAULT NULL

#define scanner_Gimbal_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pan,               1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  tilt,              2)
#define scanner_Gimbal_Request_CALLBACK NULL
#define scanner_Gimbal_Request_DEFAULT NULL
#define scanner_Gimbal_Request_pan_MSGTYPE servo_Request
#define scanner_Gimbal_Request_tilt_MSGTYPE servo_Request

#define scanner_Error_Reply_FIELDLIST(X, a) \

#define scanner_Error_Reply_CALLBACK NULL
#define scanner_Error_Reply_DEFAULT NULL

#define scanner_ACK_Reply_FIELDLIST(X, a) \

#define scanner_ACK_Reply_CALLBACK NULL
#define scanner_ACK_Reply_DEFAULT NULL

#define scanner_Laser_State_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     on,                1)
#define scanner_Laser_State_Reply_CALLBACK NULL
#define scanner_Laser_State_Reply_DEFAULT NULL

#define scanner_Gimbal_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pan,               1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  tilt,              2)
#define scanner_Gimbal_Reply_CALLBACK NULL
#define scanner_Gimbal_Reply_DEFAULT NULL
#define scanner_Gimbal_Reply_pan_MSGTYPE servo_Reply
#define scanner_Gimbal_Reply_tilt_MSGTYPE servo_Reply

#define scanner_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,laser,request.laser),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_laser,request.get_laser),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,boot,request.boot),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,stop,request.stop),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,gimbal,request.gimbal),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,intensity,request.intensity),   6)
#define scanner_Request_CALLBACK NULL
#define scanner_Request_DEFAULT NULL
#define scanner_Request_request_laser_MSGTYPE scanner_Laser_Request
#define scanner_Request_request_get_laser_MSGTYPE scanner_Get_Laser_Request
#define scanner_Request_request_boot_MSGTYPE scanner_Boot_Request
#define scanner_Request_request_stop_MSGTYPE scanner_Stop_Request
#define scanner_Request_request_gimbal_MSGTYPE scanner_Gimbal_Request
#define scanner_Request_request_intensity_MSGTYPE scanner_Intensity_Request

#define scanner_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,laser,reply.laser),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,gimbal,reply.gimbal),   4)
#define scanner_Reply_CALLBACK NULL
#define scanner_Reply_DEFAULT NULL
#define scanner_Reply_reply_error_MSGTYPE scanner_Error_Reply
#define scanner_Reply_reply_ack_MSGTYPE scanner_ACK_Reply
#define scanner_Reply_reply_laser_MSGTYPE scanner_Laser_State_Reply
#define scanner_Reply_reply_gimbal_MSGTYPE scanner_Gimbal_Reply

extern const pb_msgdesc_t scanner_Laser_Request_msg;
extern const pb_msgdesc_t scanner_Get_Laser_Request_msg;
extern const pb_msgdesc_t scanner_Intensity_Request_msg;
extern const pb_msgdesc_t scanner_Boot_Request_msg;
extern const pb_msgdesc_t scanner_Stop_Request_msg;
extern const pb_msgdesc_t scanner_Gimbal_Request_msg;
extern const pb_msgdesc_t scanner_Error_Reply_msg;
extern const pb_msgdesc_t scanner_ACK_Reply_msg;
extern const pb_msgdesc_t scanner_Laser_State_Reply_msg;
extern const pb_msgdesc_t scanner_Gimbal_Reply_msg;
extern const pb_msgdesc_t scanner_Request_msg;
extern const pb_msgdesc_t scanner_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define scanner_Laser_Request_fields &scanner_Laser_Request_msg
#define scanner_Get_Laser_Request_fields &scanner_Get_Laser_Request_msg
#define scanner_Intensity_Request_fields &scanner_Intensity_Request_msg
#define scanner_Boot_Request_fields &scanner_Boot_Request_msg
#define scanner_Stop_Request_fields &scanner_Stop_Request_msg
#define scanner_Gimbal_Request_fields &scanner_Gimbal_Request_msg
#define scanner_Error_Reply_fields &scanner_Error_Reply_msg
#define scanner_ACK_Reply_fields &scanner_ACK_Reply_msg
#define scanner_Laser_State_Reply_fields &scanner_Laser_State_Reply_msg
#define scanner_Gimbal_Reply_fields &scanner_Gimbal_Reply_msg
#define scanner_Request_fields &scanner_Request_msg
#define scanner_Reply_fields &scanner_Reply_msg

/* Maximum encoded size of messages (where known) */
#define scanner_Laser_Request_size               2
#define scanner_Get_Laser_Request_size           0
#define scanner_Intensity_Request_size           11
#if defined(epos_Home_Params_size) && defined(epos_Home_Params_size)
#define scanner_Boot_Request_size                (12 + epos_Home_Params_size + epos_Home_Params_size)
#endif
#define scanner_Stop_Request_size                0
#if defined(servo_Request_size) && defined(servo_Request_size)
#define scanner_Gimbal_Request_size              (12 + servo_Request_size + servo_Request_size)
#endif
#define scanner_Error_Reply_size                 0
#define scanner_ACK_Reply_size                   0
#define scanner_Laser_State_Reply_size           2
#if defined(servo_Reply_size) && defined(servo_Reply_size)
#define scanner_Gimbal_Reply_size                (12 + servo_Reply_size + servo_Reply_size)
#endif
#if defined(epos_Home_Params_size) && defined(epos_Home_Params_size) && defined(servo_Request_size) && defined(servo_Request_size)
typedef union scanner_Request_request_size_union {char f3[(18 + epos_Home_Params_size + epos_Home_Params_size)]; char f5[(18 + servo_Request_size + servo_Request_size)]; char f0[13];} scanner_Request_request_size_union;
#define scanner_Request_size                     (0 + sizeof(scanner_Request_request_size_union))
#endif
#if defined(servo_Reply_size) && defined(servo_Reply_size)
typedef union scanner_Reply_reply_size_union {char f4[(18 + servo_Reply_size + servo_Reply_size)]; char f0[4];} scanner_Reply_reply_size_union;
#define scanner_Reply_size                       (0 + sizeof(scanner_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    have_fix: builtin___bool = ...
    heading_deg: builtin___float = ...
    accuracy_deg: builtin___float = ...
    timestamp_ms: builtin___int = ...
    have_approx_fix: builtin___bool = ...

    def __init__(self,
        *,
        have_fix : typing___Optional[builtin___bool] = None,
        heading_deg : typing___Optional[builtin___float] = None,
        accuracy_deg : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        have_approx_fix : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"accuracy_deg",b"accuracy_deg",u"have_approx_fix",b"have_approx_fix",u"have_fix",b"have_fix",u"heading_deg",b"heading_deg",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___Reply = Reply

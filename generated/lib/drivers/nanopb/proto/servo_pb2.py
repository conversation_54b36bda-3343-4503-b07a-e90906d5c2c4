# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/servo.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import epos_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2
from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2
from generated.lib.drivers.nanopb.proto import time_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/servo.proto',
  package='servo',
  syntax='proto3',
  serialized_options=b'Z\014nanopb/servo',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n.generated/lib/drivers/nanopb/proto/servo.proto\x12\x05servo\x1a-generated/lib/drivers/nanopb/proto/epos.proto\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\x1a-generated/lib/drivers/nanopb/proto/time.proto\"n\n\x06\x43onfig\x12\x1c\n\x14max_profile_velocity\x18\x01 \x01(\r\x12\x15\n\rsettle_window\x18\x02 \x01(\r\x12\x16\n\x0esettle_timeout\x18\x03 \x01(\r\x12\x17\n\x0fmax_diff_millis\x18\x04 \x01(\r\"@\n\x0e\x43onfig_Request\x12\x0f\n\x07node_id\x18\x01 \x01(\r\x12\x1d\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\r.servo.Config\"1\n\x0c\x42oot_Request\x12!\n\x06params\x18\x01 \x01(\x0b\x32\x11.epos.Home_Params\"\x0e\n\x0cStop_Request\"I\n\rGo_To_Request\x12\x10\n\x08position\x18\x01 \x01(\x05\x12\x10\n\x08velocity\x18\x02 \x01(\r\x12\x14\n\x0c\x61wait_settle\x18\x03 \x01(\x08\"\x14\n\x12Get_Limits_Request\"^\n\x13Go_To_Delta_Request\x12\x16\n\x0e\x64\x65lta_position\x18\x01 \x01(\x05\x12\x10\n\x08velocity\x18\x02 \x01(\r\x12\x1d\n\x04mode\x18\x03 \x01(\x0e\x32\x0f.servo.GoToMode\"\xba\x01\n\x1aGo_To_Delta_Follow_Request\x12)\n\x05\x64\x65lta\x18\x01 \x01(\x0b\x32\x1a.servo.Go_To_Delta_Request\x12\x1e\n\x16\x66ollow_velocity_vector\x18\x02 \x01(\x05\x12\x1c\n\x14\x66ollow_velocity_mrpm\x18\x03 \x01(\r\x12\x1e\n\x16interval_sleep_time_ms\x18\x04 \x01(\r\x12\x13\n\x0b\x66\x61st_return\x18\x05 \x01(\x08\"\x8c\x01\n\x17Go_To_Calibrate_Request\x12\x10\n\x08position\x18\x01 \x01(\x05\x12\x10\n\x08velocity\x18\x02 \x01(\r\x12\x0e\n\x06window\x18\x03 \x01(\r\x12\x16\n\x0etime_window_ms\x18\x04 \x01(\r\x12\x12\n\ntimeout_ms\x18\x05 \x01(\r\x12\x11\n\tperiod_ms\x18\x06 \x01(\r\"\xd4\x01\n\x17Go_To_Timestamp_Request\x12\"\n\ttimestamp\x18\x01 \x01(\x0b\x32\x0f.time.Timestamp\x12\x1d\n\x04mode\x18\x02 \x01(\x0e\x32\x0f.servo.GoToMode\x12\x10\n\x08position\x18\x03 \x01(\x05\x12\x15\n\rvelocity_mrpm\x18\x04 \x01(\r\x12\x17\n\x0f\x66ollow_velocity\x18\x05 \x01(\x05\x12\x14\n\x0c\x66ollow_accel\x18\x06 \x01(\x05\x12\x1e\n\x16interval_sleep_time_ms\x18\x07 \x01(\r\"\xb2\x01\n\x14Go_To_Follow_Request\x12\"\n\ttimestamp\x18\x01 \x01(\x0b\x32\x0f.time.Timestamp\x12\x10\n\x08position\x18\x02 \x01(\x05\x12\x15\n\rvelocity_mrpm\x18\x03 \x01(\r\x12\x17\n\x0f\x66ollow_velocity\x18\x04 \x01(\x05\x12\x14\n\x0c\x66ollow_accel\x18\x05 \x01(\x05\x12\x1e\n\x16interval_sleep_time_ms\x18\x06 \x01(\r\"m\n\x18\x46ollow_Timestamp_Request\x12\"\n\ttimestamp\x18\x01 \x01(\x0b\x32\x0f.time.Timestamp\x12\x17\n\x0f\x66ollow_velocity\x18\x02 \x01(\x05\x12\x14\n\x0c\x66ollow_accel\x18\x03 \x01(\x05\"(\n\x0cLimits_Reply\x12\x0b\n\x03min\x18\x01 \x01(\x05\x12\x0b\n\x03max\x18\x02 \x01(\x05\"\"\n\x0ePosition_Reply\x12\x10\n\x08position\x18\x01 \x01(\x05\"(\n\x11Settle_Time_Reply\x12\x13\n\x0bsettle_time\x18\x01 \x01(\r\"\x95\x01\n\x15Go_To_Timestamp_Reply\x12\x14\n\x0cpre_position\x18\x01 \x01(\x05\x12\x15\n\rpost_position\x18\x02 \x01(\x05\x12&\n\rpre_timestamp\x18\x03 \x01(\x0b\x32\x0f.time.Timestamp\x12\'\n\x0epost_timestamp\x18\x04 \x01(\x0b\x32\x0f.time.Timestamp\"V\n\x16\x46ollow_Timestamp_Reply\x12\x14\n\x0cpre_position\x18\x01 \x01(\x05\x12&\n\rpre_timestamp\x18\x02 \x01(\x0b\x32\x0f.time.Timestamp\"R\n\x12Go_To_Follow_Reply\x12\x14\n\x0cpre_position\x18\x01 \x01(\x05\x12&\n\rpre_timestamp\x18\x02 \x01(\x0b\x32\x0f.time.Timestamp\"\xbd\x04\n\x07Request\x12\'\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x15.servo.Config_RequestH\x00\x12#\n\x04\x62oot\x18\x02 \x01(\x0b\x32\x13.servo.Boot_RequestH\x00\x12#\n\x04stop\x18\x03 \x01(\x0b\x32\x13.servo.Stop_RequestH\x00\x12%\n\x05go_to\x18\x04 \x01(\x0b\x32\x14.servo.Go_To_RequestH\x00\x12*\n\x05limit\x18\x05 \x01(\x0b\x32\x19.servo.Get_Limits_RequestH\x00\x12\x1d\n\x04\x65pos\x18\x06 \x01(\x0b\x32\r.epos.RequestH\x00\x12+\n\x05\x64\x65lta\x18\x07 \x01(\x0b\x32\x1a.servo.Go_To_Delta_RequestH\x00\x12\x33\n\x06\x66ollow\x18\x08 \x01(\x0b\x32!.servo.Go_To_Delta_Follow_RequestH\x00\x12\x33\n\tcalibrate\x18\t \x01(\x0b\x32\x1e.servo.Go_To_Calibrate_RequestH\x00\x12\x39\n\x0fgo_to_timestamp\x18\n \x01(\x0b\x32\x1e.servo.Go_To_Timestamp_RequestH\x00\x12;\n\x10\x66ollow_timestamp\x18\x0b \x01(\x0b\x32\x1f.servo.Follow_Timestamp_RequestH\x00\x12\x33\n\x0cgo_to_follow\x18\x0c \x01(\x0b\x32\x1b.servo.Go_To_Follow_RequestH\x00\x42\t\n\x07request\"\x84\x03\n\x05Reply\x12\x1d\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x02 \x01(\x0b\x32\x08.ack.AckH\x00\x12$\n\x05limit\x18\x03 \x01(\x0b\x32\x13.servo.Limits_ReplyH\x00\x12\x1b\n\x04\x65pos\x18\x04 \x01(\x0b\x32\x0b.epos.ReplyH\x00\x12$\n\x03pos\x18\x05 \x01(\x0b\x32\x15.servo.Position_ReplyH\x00\x12*\n\x06settle\x18\x06 \x01(\x0b\x32\x18.servo.Settle_Time_ReplyH\x00\x12\x37\n\x0fgo_to_timestamp\x18\x07 \x01(\x0b\x32\x1c.servo.Go_To_Timestamp_ReplyH\x00\x12\x39\n\x10\x66ollow_timestamp\x18\x08 \x01(\x0b\x32\x1d.servo.Follow_Timestamp_ReplyH\x00\x12\x31\n\x0cgo_to_follow\x18\t \x01(\x0b\x32\x19.servo.Go_To_Follow_ReplyH\x00\x42\x07\n\x05reply*C\n\x08GoToMode\x12\r\n\tIMMEDIATE\x10\x00\x12\x0b\n\x07REACHED\x10\x01\x12\x0b\n\x07SETTLED\x10\x02\x12\x0e\n\nTRAJECTORY\x10\x03\x42\x0eZ\x0cnanopb/servob\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2.DESCRIPTOR,])

_GOTOMODE = _descriptor.EnumDescriptor(
  name='GoToMode',
  full_name='servo.GoToMode',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='IMMEDIATE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='REACHED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SETTLED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TRAJECTORY', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2933,
  serialized_end=3000,
)
_sym_db.RegisterEnumDescriptor(_GOTOMODE)

GoToMode = enum_type_wrapper.EnumTypeWrapper(_GOTOMODE)
IMMEDIATE = 0
REACHED = 1
SETTLED = 2
TRAJECTORY = 3



_CONFIG = _descriptor.Descriptor(
  name='Config',
  full_name='servo.Config',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='max_profile_velocity', full_name='servo.Config.max_profile_velocity', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='settle_window', full_name='servo.Config.settle_window', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='settle_timeout', full_name='servo.Config.settle_timeout', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_diff_millis', full_name='servo.Config.max_diff_millis', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=245,
  serialized_end=355,
)


_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Config_Request',
  full_name='servo.Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='node_id', full_name='servo.Config_Request.node_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='servo.Config_Request.config', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=357,
  serialized_end=421,
)


_BOOT_REQUEST = _descriptor.Descriptor(
  name='Boot_Request',
  full_name='servo.Boot_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='params', full_name='servo.Boot_Request.params', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=423,
  serialized_end=472,
)


_STOP_REQUEST = _descriptor.Descriptor(
  name='Stop_Request',
  full_name='servo.Stop_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=474,
  serialized_end=488,
)


_GO_TO_REQUEST = _descriptor.Descriptor(
  name='Go_To_Request',
  full_name='servo.Go_To_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='position', full_name='servo.Go_To_Request.position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='servo.Go_To_Request.velocity', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='await_settle', full_name='servo.Go_To_Request.await_settle', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=490,
  serialized_end=563,
)


_GET_LIMITS_REQUEST = _descriptor.Descriptor(
  name='Get_Limits_Request',
  full_name='servo.Get_Limits_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=565,
  serialized_end=585,
)


_GO_TO_DELTA_REQUEST = _descriptor.Descriptor(
  name='Go_To_Delta_Request',
  full_name='servo.Go_To_Delta_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='delta_position', full_name='servo.Go_To_Delta_Request.delta_position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='servo.Go_To_Delta_Request.velocity', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mode', full_name='servo.Go_To_Delta_Request.mode', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=587,
  serialized_end=681,
)


_GO_TO_DELTA_FOLLOW_REQUEST = _descriptor.Descriptor(
  name='Go_To_Delta_Follow_Request',
  full_name='servo.Go_To_Delta_Follow_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='delta', full_name='servo.Go_To_Delta_Follow_Request.delta', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_velocity_vector', full_name='servo.Go_To_Delta_Follow_Request.follow_velocity_vector', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_velocity_mrpm', full_name='servo.Go_To_Delta_Follow_Request.follow_velocity_mrpm', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='interval_sleep_time_ms', full_name='servo.Go_To_Delta_Follow_Request.interval_sleep_time_ms', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fast_return', full_name='servo.Go_To_Delta_Follow_Request.fast_return', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=684,
  serialized_end=870,
)


_GO_TO_CALIBRATE_REQUEST = _descriptor.Descriptor(
  name='Go_To_Calibrate_Request',
  full_name='servo.Go_To_Calibrate_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='position', full_name='servo.Go_To_Calibrate_Request.position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='servo.Go_To_Calibrate_Request.velocity', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='window', full_name='servo.Go_To_Calibrate_Request.window', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time_window_ms', full_name='servo.Go_To_Calibrate_Request.time_window_ms', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timeout_ms', full_name='servo.Go_To_Calibrate_Request.timeout_ms', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='period_ms', full_name='servo.Go_To_Calibrate_Request.period_ms', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=873,
  serialized_end=1013,
)


_GO_TO_TIMESTAMP_REQUEST = _descriptor.Descriptor(
  name='Go_To_Timestamp_Request',
  full_name='servo.Go_To_Timestamp_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='servo.Go_To_Timestamp_Request.timestamp', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mode', full_name='servo.Go_To_Timestamp_Request.mode', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position', full_name='servo.Go_To_Timestamp_Request.position', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity_mrpm', full_name='servo.Go_To_Timestamp_Request.velocity_mrpm', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_velocity', full_name='servo.Go_To_Timestamp_Request.follow_velocity', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_accel', full_name='servo.Go_To_Timestamp_Request.follow_accel', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='interval_sleep_time_ms', full_name='servo.Go_To_Timestamp_Request.interval_sleep_time_ms', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1016,
  serialized_end=1228,
)


_GO_TO_FOLLOW_REQUEST = _descriptor.Descriptor(
  name='Go_To_Follow_Request',
  full_name='servo.Go_To_Follow_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='servo.Go_To_Follow_Request.timestamp', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position', full_name='servo.Go_To_Follow_Request.position', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity_mrpm', full_name='servo.Go_To_Follow_Request.velocity_mrpm', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_velocity', full_name='servo.Go_To_Follow_Request.follow_velocity', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_accel', full_name='servo.Go_To_Follow_Request.follow_accel', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='interval_sleep_time_ms', full_name='servo.Go_To_Follow_Request.interval_sleep_time_ms', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1231,
  serialized_end=1409,
)


_FOLLOW_TIMESTAMP_REQUEST = _descriptor.Descriptor(
  name='Follow_Timestamp_Request',
  full_name='servo.Follow_Timestamp_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='servo.Follow_Timestamp_Request.timestamp', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_velocity', full_name='servo.Follow_Timestamp_Request.follow_velocity', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_accel', full_name='servo.Follow_Timestamp_Request.follow_accel', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1411,
  serialized_end=1520,
)


_LIMITS_REPLY = _descriptor.Descriptor(
  name='Limits_Reply',
  full_name='servo.Limits_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min', full_name='servo.Limits_Reply.min', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max', full_name='servo.Limits_Reply.max', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1522,
  serialized_end=1562,
)


_POSITION_REPLY = _descriptor.Descriptor(
  name='Position_Reply',
  full_name='servo.Position_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='position', full_name='servo.Position_Reply.position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1564,
  serialized_end=1598,
)


_SETTLE_TIME_REPLY = _descriptor.Descriptor(
  name='Settle_Time_Reply',
  full_name='servo.Settle_Time_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='settle_time', full_name='servo.Settle_Time_Reply.settle_time', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1600,
  serialized_end=1640,
)


_GO_TO_TIMESTAMP_REPLY = _descriptor.Descriptor(
  name='Go_To_Timestamp_Reply',
  full_name='servo.Go_To_Timestamp_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pre_position', full_name='servo.Go_To_Timestamp_Reply.pre_position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='post_position', full_name='servo.Go_To_Timestamp_Reply.post_position', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pre_timestamp', full_name='servo.Go_To_Timestamp_Reply.pre_timestamp', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='post_timestamp', full_name='servo.Go_To_Timestamp_Reply.post_timestamp', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1643,
  serialized_end=1792,
)


_FOLLOW_TIMESTAMP_REPLY = _descriptor.Descriptor(
  name='Follow_Timestamp_Reply',
  full_name='servo.Follow_Timestamp_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pre_position', full_name='servo.Follow_Timestamp_Reply.pre_position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pre_timestamp', full_name='servo.Follow_Timestamp_Reply.pre_timestamp', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1794,
  serialized_end=1880,
)


_GO_TO_FOLLOW_REPLY = _descriptor.Descriptor(
  name='Go_To_Follow_Reply',
  full_name='servo.Go_To_Follow_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pre_position', full_name='servo.Go_To_Follow_Reply.pre_position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pre_timestamp', full_name='servo.Go_To_Follow_Reply.pre_timestamp', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1882,
  serialized_end=1964,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='servo.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='servo.Request.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='boot', full_name='servo.Request.boot', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stop', full_name='servo.Request.stop', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='go_to', full_name='servo.Request.go_to', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='limit', full_name='servo.Request.limit', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='epos', full_name='servo.Request.epos', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='delta', full_name='servo.Request.delta', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow', full_name='servo.Request.follow', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='calibrate', full_name='servo.Request.calibrate', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='go_to_timestamp', full_name='servo.Request.go_to_timestamp', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_timestamp', full_name='servo.Request.follow_timestamp', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='go_to_follow', full_name='servo.Request.go_to_follow', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='servo.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1967,
  serialized_end=2540,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='servo.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='servo.Reply.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='servo.Reply.ack', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='limit', full_name='servo.Reply.limit', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='epos', full_name='servo.Reply.epos', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pos', full_name='servo.Reply.pos', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='settle', full_name='servo.Reply.settle', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='go_to_timestamp', full_name='servo.Reply.go_to_timestamp', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='follow_timestamp', full_name='servo.Reply.follow_timestamp', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='go_to_follow', full_name='servo.Reply.go_to_follow', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='servo.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2543,
  serialized_end=2931,
)

_CONFIG_REQUEST.fields_by_name['config'].message_type = _CONFIG
_BOOT_REQUEST.fields_by_name['params'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2._HOME_PARAMS
_GO_TO_DELTA_REQUEST.fields_by_name['mode'].enum_type = _GOTOMODE
_GO_TO_DELTA_FOLLOW_REQUEST.fields_by_name['delta'].message_type = _GO_TO_DELTA_REQUEST
_GO_TO_TIMESTAMP_REQUEST.fields_by_name['timestamp'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_GO_TO_TIMESTAMP_REQUEST.fields_by_name['mode'].enum_type = _GOTOMODE
_GO_TO_FOLLOW_REQUEST.fields_by_name['timestamp'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_FOLLOW_TIMESTAMP_REQUEST.fields_by_name['timestamp'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_GO_TO_TIMESTAMP_REPLY.fields_by_name['pre_timestamp'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_GO_TO_TIMESTAMP_REPLY.fields_by_name['post_timestamp'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_FOLLOW_TIMESTAMP_REPLY.fields_by_name['pre_timestamp'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_GO_TO_FOLLOW_REPLY.fields_by_name['pre_timestamp'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._TIMESTAMP
_REQUEST.fields_by_name['config'].message_type = _CONFIG_REQUEST
_REQUEST.fields_by_name['boot'].message_type = _BOOT_REQUEST
_REQUEST.fields_by_name['stop'].message_type = _STOP_REQUEST
_REQUEST.fields_by_name['go_to'].message_type = _GO_TO_REQUEST
_REQUEST.fields_by_name['limit'].message_type = _GET_LIMITS_REQUEST
_REQUEST.fields_by_name['epos'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2._REQUEST
_REQUEST.fields_by_name['delta'].message_type = _GO_TO_DELTA_REQUEST
_REQUEST.fields_by_name['follow'].message_type = _GO_TO_DELTA_FOLLOW_REQUEST
_REQUEST.fields_by_name['calibrate'].message_type = _GO_TO_CALIBRATE_REQUEST
_REQUEST.fields_by_name['go_to_timestamp'].message_type = _GO_TO_TIMESTAMP_REQUEST
_REQUEST.fields_by_name['follow_timestamp'].message_type = _FOLLOW_TIMESTAMP_REQUEST
_REQUEST.fields_by_name['go_to_follow'].message_type = _GO_TO_FOLLOW_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['config'])
_REQUEST.fields_by_name['config'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['boot'])
_REQUEST.fields_by_name['boot'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['stop'])
_REQUEST.fields_by_name['stop'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['go_to'])
_REQUEST.fields_by_name['go_to'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['limit'])
_REQUEST.fields_by_name['limit'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['epos'])
_REQUEST.fields_by_name['epos'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['delta'])
_REQUEST.fields_by_name['delta'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['follow'])
_REQUEST.fields_by_name['follow'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['calibrate'])
_REQUEST.fields_by_name['calibrate'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['go_to_timestamp'])
_REQUEST.fields_by_name['go_to_timestamp'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['follow_timestamp'])
_REQUEST.fields_by_name['follow_timestamp'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['go_to_follow'])
_REQUEST.fields_by_name['go_to_follow'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['limit'].message_type = _LIMITS_REPLY
_REPLY.fields_by_name['epos'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2._REPLY
_REPLY.fields_by_name['pos'].message_type = _POSITION_REPLY
_REPLY.fields_by_name['settle'].message_type = _SETTLE_TIME_REPLY
_REPLY.fields_by_name['go_to_timestamp'].message_type = _GO_TO_TIMESTAMP_REPLY
_REPLY.fields_by_name['follow_timestamp'].message_type = _FOLLOW_TIMESTAMP_REPLY
_REPLY.fields_by_name['go_to_follow'].message_type = _GO_TO_FOLLOW_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['limit'])
_REPLY.fields_by_name['limit'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['epos'])
_REPLY.fields_by_name['epos'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pos'])
_REPLY.fields_by_name['pos'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['settle'])
_REPLY.fields_by_name['settle'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['go_to_timestamp'])
_REPLY.fields_by_name['go_to_timestamp'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['follow_timestamp'])
_REPLY.fields_by_name['follow_timestamp'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['go_to_follow'])
_REPLY.fields_by_name['go_to_follow'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Config'] = _CONFIG
DESCRIPTOR.message_types_by_name['Config_Request'] = _CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Boot_Request'] = _BOOT_REQUEST
DESCRIPTOR.message_types_by_name['Stop_Request'] = _STOP_REQUEST
DESCRIPTOR.message_types_by_name['Go_To_Request'] = _GO_TO_REQUEST
DESCRIPTOR.message_types_by_name['Get_Limits_Request'] = _GET_LIMITS_REQUEST
DESCRIPTOR.message_types_by_name['Go_To_Delta_Request'] = _GO_TO_DELTA_REQUEST
DESCRIPTOR.message_types_by_name['Go_To_Delta_Follow_Request'] = _GO_TO_DELTA_FOLLOW_REQUEST
DESCRIPTOR.message_types_by_name['Go_To_Calibrate_Request'] = _GO_TO_CALIBRATE_REQUEST
DESCRIPTOR.message_types_by_name['Go_To_Timestamp_Request'] = _GO_TO_TIMESTAMP_REQUEST
DESCRIPTOR.message_types_by_name['Go_To_Follow_Request'] = _GO_TO_FOLLOW_REQUEST
DESCRIPTOR.message_types_by_name['Follow_Timestamp_Request'] = _FOLLOW_TIMESTAMP_REQUEST
DESCRIPTOR.message_types_by_name['Limits_Reply'] = _LIMITS_REPLY
DESCRIPTOR.message_types_by_name['Position_Reply'] = _POSITION_REPLY
DESCRIPTOR.message_types_by_name['Settle_Time_Reply'] = _SETTLE_TIME_REPLY
DESCRIPTOR.message_types_by_name['Go_To_Timestamp_Reply'] = _GO_TO_TIMESTAMP_REPLY
DESCRIPTOR.message_types_by_name['Follow_Timestamp_Reply'] = _FOLLOW_TIMESTAMP_REPLY
DESCRIPTOR.message_types_by_name['Go_To_Follow_Reply'] = _GO_TO_FOLLOW_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.enum_types_by_name['GoToMode'] = _GOTOMODE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Config)
  })
_sym_db.RegisterMessage(Config)

Config_Request = _reflection.GeneratedProtocolMessageType('Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Config_Request)
  })
_sym_db.RegisterMessage(Config_Request)

Boot_Request = _reflection.GeneratedProtocolMessageType('Boot_Request', (_message.Message,), {
  'DESCRIPTOR' : _BOOT_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Boot_Request)
  })
_sym_db.RegisterMessage(Boot_Request)

Stop_Request = _reflection.GeneratedProtocolMessageType('Stop_Request', (_message.Message,), {
  'DESCRIPTOR' : _STOP_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Stop_Request)
  })
_sym_db.RegisterMessage(Stop_Request)

Go_To_Request = _reflection.GeneratedProtocolMessageType('Go_To_Request', (_message.Message,), {
  'DESCRIPTOR' : _GO_TO_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Go_To_Request)
  })
_sym_db.RegisterMessage(Go_To_Request)

Get_Limits_Request = _reflection.GeneratedProtocolMessageType('Get_Limits_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_LIMITS_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Get_Limits_Request)
  })
_sym_db.RegisterMessage(Get_Limits_Request)

Go_To_Delta_Request = _reflection.GeneratedProtocolMessageType('Go_To_Delta_Request', (_message.Message,), {
  'DESCRIPTOR' : _GO_TO_DELTA_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Go_To_Delta_Request)
  })
_sym_db.RegisterMessage(Go_To_Delta_Request)

Go_To_Delta_Follow_Request = _reflection.GeneratedProtocolMessageType('Go_To_Delta_Follow_Request', (_message.Message,), {
  'DESCRIPTOR' : _GO_TO_DELTA_FOLLOW_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Go_To_Delta_Follow_Request)
  })
_sym_db.RegisterMessage(Go_To_Delta_Follow_Request)

Go_To_Calibrate_Request = _reflection.GeneratedProtocolMessageType('Go_To_Calibrate_Request', (_message.Message,), {
  'DESCRIPTOR' : _GO_TO_CALIBRATE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Go_To_Calibrate_Request)
  })
_sym_db.RegisterMessage(Go_To_Calibrate_Request)

Go_To_Timestamp_Request = _reflection.GeneratedProtocolMessageType('Go_To_Timestamp_Request', (_message.Message,), {
  'DESCRIPTOR' : _GO_TO_TIMESTAMP_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Go_To_Timestamp_Request)
  })
_sym_db.RegisterMessage(Go_To_Timestamp_Request)

Go_To_Follow_Request = _reflection.GeneratedProtocolMessageType('Go_To_Follow_Request', (_message.Message,), {
  'DESCRIPTOR' : _GO_TO_FOLLOW_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Go_To_Follow_Request)
  })
_sym_db.RegisterMessage(Go_To_Follow_Request)

Follow_Timestamp_Request = _reflection.GeneratedProtocolMessageType('Follow_Timestamp_Request', (_message.Message,), {
  'DESCRIPTOR' : _FOLLOW_TIMESTAMP_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Follow_Timestamp_Request)
  })
_sym_db.RegisterMessage(Follow_Timestamp_Request)

Limits_Reply = _reflection.GeneratedProtocolMessageType('Limits_Reply', (_message.Message,), {
  'DESCRIPTOR' : _LIMITS_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Limits_Reply)
  })
_sym_db.RegisterMessage(Limits_Reply)

Position_Reply = _reflection.GeneratedProtocolMessageType('Position_Reply', (_message.Message,), {
  'DESCRIPTOR' : _POSITION_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Position_Reply)
  })
_sym_db.RegisterMessage(Position_Reply)

Settle_Time_Reply = _reflection.GeneratedProtocolMessageType('Settle_Time_Reply', (_message.Message,), {
  'DESCRIPTOR' : _SETTLE_TIME_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Settle_Time_Reply)
  })
_sym_db.RegisterMessage(Settle_Time_Reply)

Go_To_Timestamp_Reply = _reflection.GeneratedProtocolMessageType('Go_To_Timestamp_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GO_TO_TIMESTAMP_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Go_To_Timestamp_Reply)
  })
_sym_db.RegisterMessage(Go_To_Timestamp_Reply)

Follow_Timestamp_Reply = _reflection.GeneratedProtocolMessageType('Follow_Timestamp_Reply', (_message.Message,), {
  'DESCRIPTOR' : _FOLLOW_TIMESTAMP_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Follow_Timestamp_Reply)
  })
_sym_db.RegisterMessage(Follow_Timestamp_Reply)

Go_To_Follow_Reply = _reflection.GeneratedProtocolMessageType('Go_To_Follow_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GO_TO_FOLLOW_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Go_To_Follow_Reply)
  })
_sym_db.RegisterMessage(Go_To_Follow_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.servo_pb2'
  # @@protoc_insertion_point(class_scope:servo.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

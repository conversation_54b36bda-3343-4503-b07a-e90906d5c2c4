# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/sensors.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/sensors.proto',
  package='sensors',
  syntax='proto3',
  serialized_options=b'Z\016nanopb/sensors',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n0generated/lib/drivers/nanopb/proto/sensors.proto\x12\x07sensors\"\x13\n\x11\x46uelGauge_Request\" \n\x0f\x46uelGauge_Reply\x12\r\n\x05value\x18\x01 \x01(\x02\"F\n\x07Request\x12\x30\n\nfuel_gauge\x18\x01 \x01(\x0b\x32\x1a.sensors.FuelGauge_RequestH\x00\x42\t\n\x07request\"@\n\x05Reply\x12.\n\nfuel_gauge\x18\x01 \x01(\x0b\x32\x18.sensors.FuelGauge_ReplyH\x00\x42\x07\n\x05replyB\x10Z\x0enanopb/sensorsb\x06proto3'
)




_FUELGAUGE_REQUEST = _descriptor.Descriptor(
  name='FuelGauge_Request',
  full_name='sensors.FuelGauge_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=61,
  serialized_end=80,
)


_FUELGAUGE_REPLY = _descriptor.Descriptor(
  name='FuelGauge_Reply',
  full_name='sensors.FuelGauge_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='sensors.FuelGauge_Reply.value', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=82,
  serialized_end=114,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='sensors.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fuel_gauge', full_name='sensors.Request.fuel_gauge', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='sensors.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=116,
  serialized_end=186,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='sensors.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fuel_gauge', full_name='sensors.Reply.fuel_gauge', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='sensors.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=188,
  serialized_end=252,
)

_REQUEST.fields_by_name['fuel_gauge'].message_type = _FUELGAUGE_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['fuel_gauge'])
_REQUEST.fields_by_name['fuel_gauge'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['fuel_gauge'].message_type = _FUELGAUGE_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['fuel_gauge'])
_REPLY.fields_by_name['fuel_gauge'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['FuelGauge_Request'] = _FUELGAUGE_REQUEST
DESCRIPTOR.message_types_by_name['FuelGauge_Reply'] = _FUELGAUGE_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

FuelGauge_Request = _reflection.GeneratedProtocolMessageType('FuelGauge_Request', (_message.Message,), {
  'DESCRIPTOR' : _FUELGAUGE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.sensors_pb2'
  # @@protoc_insertion_point(class_scope:sensors.FuelGauge_Request)
  })
_sym_db.RegisterMessage(FuelGauge_Request)

FuelGauge_Reply = _reflection.GeneratedProtocolMessageType('FuelGauge_Reply', (_message.Message,), {
  'DESCRIPTOR' : _FUELGAUGE_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.sensors_pb2'
  # @@protoc_insertion_point(class_scope:sensors.FuelGauge_Reply)
  })
_sym_db.RegisterMessage(FuelGauge_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.sensors_pb2'
  # @@protoc_insertion_point(class_scope:sensors.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.sensors_pb2'
  # @@protoc_insertion_point(class_scope:sensors.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.diagnostic_pb2 import (
    Ping as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping,
    Pong as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong,
)

from generated.lib.drivers.nanopb.proto.request_pb2 import (
    RequestHeader as generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader,
)

from generated.lib.drivers.nanopb.proto.row_module_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___row_module_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___row_module_pb2___Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def row_module(self) -> generated___lib___drivers___nanopb___proto___row_module_pb2___Reply: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        row_module : typing___Optional[generated___lib___drivers___nanopb___proto___row_module_pb2___Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"header",b"header",u"pong",b"pong",u"reply",b"reply",u"row_module",b"row_module"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"header",b"header",u"pong",b"pong",u"reply",b"reply",u"row_module",b"row_module"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["pong","row_module"]: ...
type___Reply = Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def row_module(self) -> generated___lib___drivers___nanopb___proto___row_module_pb2___Request: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        row_module : typing___Optional[generated___lib___drivers___nanopb___proto___row_module_pb2___Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"header",b"header",u"ping",b"ping",u"request",b"request",u"row_module",b"row_module"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"header",b"header",u"ping",b"ping",u"request",b"request",u"row_module",b"row_module"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","row_module"]: ...
type___Request = Request

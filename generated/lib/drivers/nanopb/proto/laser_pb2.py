# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/laser.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/laser.proto',
  package='laser',
  syntax='proto3',
  serialized_options=b'Z\014nanopb/laser',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n.generated/lib/drivers/nanopb/proto/laser.proto\x12\x05laser\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\"\x12\n\x10Raw_Data_Request\"\x90\x01\n\x0eRaw_Data_Reply\x12\x17\n\ntherm1_raw\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x17\n\ntherm2_raw\x18\x02 \x01(\x05H\x01\x88\x01\x01\x12\x1b\n\x0ephotodiode_raw\x18\x03 \x01(\x05H\x02\x88\x01\x01\x42\r\n\x0b_therm1_rawB\r\n\x0b_therm2_rawB\x11\n\x0f_photodiode_raw\"\x19\n\x17Laser_Inventory_Request\"e\n\x15Laser_Inventory_Reply\x12\r\n\x05model\x18\x01 \x01(\t\x12\x0e\n\x06serial\x18\x02 \x01(\t\x12\r\n\x05power\x18\x03 \x01(\r\x12\x1e\n\x04type\x18\x04 \x01(\x0e\x32\x10.laser.LaserType\"8\n\x16Laser_Set_Type_Request\x12\x1e\n\x04type\x18\x01 \x01(\x0e\x32\x10.laser.LaserType\"8\n\x13Laser_Reset_Request\x12\x11\n\ttransport\x18\x01 \x01(\x08\x12\x0e\n\x06\x66\x61ults\x18\x02 \x01(\x08\"t\n\rJlight_Status\x12\x0f\n\x07psuTemp\x18\x01 \x01(\x02\x12\x15\n\rpsuInputVolts\x18\x02 \x01(\x02\x12\x16\n\x0epsuOutputVolts\x18\x03 \x03(\x02\x12#\n\x06\x66\x61ults\x18\x04 \x03(\x0e\x32\x13.laser.Jlight_Fault\"\x1c\n\nBwt_Status\x12\x0e\n\x06\x66\x61ults\x18\x01 \x03(\x05\"*\n\x17\x42wt_Passthrough_Request\x12\x0f\n\x07\x63ommand\x18\x01 \x01(\t\")\n\x15\x42wt_Passthrough_Reply\x12\x10\n\x08response\x18\x01 \x01(\t\"\"\n Bwt_Transport_Get_Config_Request\"H\n\x14\x42wt_Transport_Config\x12\x14\n\x0clog_messages\x18\x01 \x01(\x08\x12\x1a\n\x12intercommand_delay\x18\x02 \x01(\x08\"\x16\n\x14\x44iode_Status_Request\"\xdd\x01\n\x12\x44iode_Status_Reply\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0c\n\x04temp\x18\x02 \x03(\x02\x12\x10\n\x08humidity\x18\x03 \x03(\x02\x12\x0f\n\x07\x63urrent\x18\x04 \x03(\x02\x12\x0f\n\x07\x66\x61ulted\x18\x05 \x01(\x08\x12\x13\n\x0bthermistors\x18\x06 \x03(\x02\x12&\n\textra_bwt\x18\x07 \x01(\x0b\x32\x11.laser.Bwt_StatusH\x00\x12,\n\x0c\x65xtra_jlight\x18\x08 \x01(\x0b\x32\x14.laser.Jlight_StatusH\x00\x42\x07\n\x05\x65xtra\"<\n\x19\x44iode_Set_Current_Request\x12\x0f\n\x07\x63urrent\x18\x01 \x03(\r\x12\x0e\n\x06\x63ommit\x18\x02 \x01(\x08\"\x1b\n\rLaser_Request\x12\n\n\x02on\x18\x01 \x01(\x08\"\x13\n\x11Get_Laser_Request\"&\n\x11Intensity_Request\x12\x11\n\tintensity\x18\x01 \x01(\x05\"}\n\x0bLaser_Reply\x12\x1d\n\x15raw_therm1_reading_mv\x18\x01 \x01(\x05\x12\x1d\n\x15raw_therm2_reading_mv\x18\x02 \x01(\x05\x12\n\n\x02on\x18\x03 \x01(\x08\x12\x12\n\nlpsu_state\x18\x04 \x01(\x08\x12\x10\n\x08\x66ireable\x18\x05 \x01(\x08\"\x1f\n\x11Laser_State_Reply\x12\n\n\x02on\x18\x01 \x01(\x08\"c\n\x12Laser_Status_Reply\x12\x12\n\nlpsu_state\x18\x01 \x01(\x08\x12\x14\n\x0clpsu_current\x18\x02 \x01(\x02\x12\r\n\x05power\x18\x03 \x01(\x02\x12\x14\n\x0c\x61rc_detected\x18\x04 \x01(\x08\"\x96\x05\n\x07Request\x12%\n\x05laser\x18\x01 \x01(\x0b\x32\x14.laser.Laser_RequestH\x00\x12-\n\tget_laser\x18\x02 \x01(\x0b\x32\x18.laser.Get_Laser_RequestH\x00\x12-\n\tintensity\x18\x03 \x01(\x0b\x32\x18.laser.Intensity_RequestH\x00\x12+\n\x08raw_data\x18\x04 \x01(\x0b\x32\x17.laser.Raw_Data_RequestH\x00\x12\x33\n\x0c\x64iode_status\x18\x05 \x01(\x0b\x32\x1b.laser.Diode_Status_RequestH\x00\x12\x39\n\x0flaser_inventory\x18\x06 \x01(\x0b\x32\x1e.laser.Laser_Inventory_RequestH\x00\x12\x39\n\x0f\x62wt_passthrough\x18\x07 \x01(\x0b\x32\x1e.laser.Bwt_Passthrough_RequestH\x00\x12\x35\n\x0e\x62wt_set_config\x18\t \x01(\x0b\x32\x1b.laser.Bwt_Transport_ConfigH\x00\x12\x41\n\x0e\x62wt_get_config\x18\n \x01(\x0b\x32\'.laser.Bwt_Transport_Get_Config_RequestH\x00\x12\x31\n\x0blaser_reset\x18\x0b \x01(\x0b\x32\x1a.laser.Laser_Reset_RequestH\x00\x12=\n\x11\x64iode_set_current\x18\x0c \x01(\x0b\x32 .laser.Diode_Set_Current_RequestH\x00\x12\x31\n\x08set_type\x18\r \x01(\x0b\x32\x1d.laser.Laser_Set_Type_RequestH\x00\x42\t\n\x07requestJ\x04\x08\x08\x10\t\"\xa1\x03\n\x05Reply\x12\x1d\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x02 \x01(\x0b\x32\x08.ack.AckH\x00\x12)\n\x05laser\x18\x03 \x01(\x0b\x32\x18.laser.Laser_State_ReplyH\x00\x12)\n\x0blaser_reply\x18\x04 \x01(\x0b\x32\x12.laser.Laser_ReplyH\x00\x12)\n\x08raw_data\x18\x05 \x01(\x0b\x32\x15.laser.Raw_Data_ReplyH\x00\x12\x31\n\x0c\x64iode_status\x18\x06 \x01(\x0b\x32\x19.laser.Diode_Status_ReplyH\x00\x12\x37\n\x0flaser_inventory\x18\x07 \x01(\x0b\x32\x1c.laser.Laser_Inventory_ReplyH\x00\x12\x37\n\x0f\x62wt_passthrough\x18\x08 \x01(\x0b\x32\x1c.laser.Bwt_Passthrough_ReplyH\x00\x12\x31\n\nbwt_config\x18\t \x01(\x0b\x32\x1b.laser.Bwt_Transport_ConfigH\x00\x42\x07\n\x05reply*j\n\tLaserType\x12\x15\n\x11LASERTYPE_UNKNOWN\x10\x00\x12\x11\n\rLASERTYPE_CO2\x10\x01\x12\x17\n\x13LASERTYPE_DIODE_BWT\x10\x02\x12\x1a\n\x16LASERTYPE_DIODE_JLIGHT\x10\x03*\xc5\x02\n\x0cJlight_Fault\x12\x18\n\x14JLIGHT_FAULT_UNKNOWN\x10\x00\x12\x1a\n\x16JLIGHT_FAULT_INTERLOCK\x10\x01\x12\x17\n\x13JLIGHT_FAULT_SYSTEM\x10\x02\x12 \n\x1cJLIGHT_FAULT_CH1_OVERCURRENT\x10\x03\x12 \n\x1cJLIGHT_FAULT_CH2_OVERCURRENT\x10\x04\x12 \n\x1cJLIGHT_FAULT_CH1_OVERVOLTAGE\x10\x05\x12 \n\x1cJLIGHT_FAULT_CH2_OVERVOLTAGE\x10\x06\x12\x1d\n\x19JLIGHT_FAULT_CH1_OVERTEMP\x10\x07\x12\x1d\n\x19JLIGHT_FAULT_CH2_OVERTEMP\x10\x08\x12 \n\x1cJLIGHT_FAULT_DRIVER_OVERTEMP\x10\tB\x0eZ\x0cnanopb/laserb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,])

_LASERTYPE = _descriptor.EnumDescriptor(
  name='LaserType',
  full_name='laser.LaserType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LASERTYPE_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LASERTYPE_CO2', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LASERTYPE_DIODE_BWT', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LASERTYPE_DIODE_JLIGHT', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2655,
  serialized_end=2761,
)
_sym_db.RegisterEnumDescriptor(_LASERTYPE)

LaserType = enum_type_wrapper.EnumTypeWrapper(_LASERTYPE)
_JLIGHT_FAULT = _descriptor.EnumDescriptor(
  name='Jlight_Fault',
  full_name='laser.Jlight_Fault',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_INTERLOCK', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_SYSTEM', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_CH1_OVERCURRENT', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_CH2_OVERCURRENT', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_CH1_OVERVOLTAGE', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_CH2_OVERVOLTAGE', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_CH1_OVERTEMP', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_CH2_OVERTEMP', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JLIGHT_FAULT_DRIVER_OVERTEMP', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2764,
  serialized_end=3089,
)
_sym_db.RegisterEnumDescriptor(_JLIGHT_FAULT)

Jlight_Fault = enum_type_wrapper.EnumTypeWrapper(_JLIGHT_FAULT)
LASERTYPE_UNKNOWN = 0
LASERTYPE_CO2 = 1
LASERTYPE_DIODE_BWT = 2
LASERTYPE_DIODE_JLIGHT = 3
JLIGHT_FAULT_UNKNOWN = 0
JLIGHT_FAULT_INTERLOCK = 1
JLIGHT_FAULT_SYSTEM = 2
JLIGHT_FAULT_CH1_OVERCURRENT = 3
JLIGHT_FAULT_CH2_OVERCURRENT = 4
JLIGHT_FAULT_CH1_OVERVOLTAGE = 5
JLIGHT_FAULT_CH2_OVERVOLTAGE = 6
JLIGHT_FAULT_CH1_OVERTEMP = 7
JLIGHT_FAULT_CH2_OVERTEMP = 8
JLIGHT_FAULT_DRIVER_OVERTEMP = 9



_RAW_DATA_REQUEST = _descriptor.Descriptor(
  name='Raw_Data_Request',
  full_name='laser.Raw_Data_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=151,
  serialized_end=169,
)


_RAW_DATA_REPLY = _descriptor.Descriptor(
  name='Raw_Data_Reply',
  full_name='laser.Raw_Data_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='therm1_raw', full_name='laser.Raw_Data_Reply.therm1_raw', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='therm2_raw', full_name='laser.Raw_Data_Reply.therm2_raw', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='photodiode_raw', full_name='laser.Raw_Data_Reply.photodiode_raw', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_therm1_raw', full_name='laser.Raw_Data_Reply._therm1_raw',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_therm2_raw', full_name='laser.Raw_Data_Reply._therm2_raw',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_photodiode_raw', full_name='laser.Raw_Data_Reply._photodiode_raw',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=172,
  serialized_end=316,
)


_LASER_INVENTORY_REQUEST = _descriptor.Descriptor(
  name='Laser_Inventory_Request',
  full_name='laser.Laser_Inventory_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=318,
  serialized_end=343,
)


_LASER_INVENTORY_REPLY = _descriptor.Descriptor(
  name='Laser_Inventory_Reply',
  full_name='laser.Laser_Inventory_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model', full_name='laser.Laser_Inventory_Reply.model', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='serial', full_name='laser.Laser_Inventory_Reply.serial', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='laser.Laser_Inventory_Reply.power', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='laser.Laser_Inventory_Reply.type', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=345,
  serialized_end=446,
)


_LASER_SET_TYPE_REQUEST = _descriptor.Descriptor(
  name='Laser_Set_Type_Request',
  full_name='laser.Laser_Set_Type_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='laser.Laser_Set_Type_Request.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=448,
  serialized_end=504,
)


_LASER_RESET_REQUEST = _descriptor.Descriptor(
  name='Laser_Reset_Request',
  full_name='laser.Laser_Reset_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='transport', full_name='laser.Laser_Reset_Request.transport', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='faults', full_name='laser.Laser_Reset_Request.faults', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=506,
  serialized_end=562,
)


_JLIGHT_STATUS = _descriptor.Descriptor(
  name='Jlight_Status',
  full_name='laser.Jlight_Status',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='psuTemp', full_name='laser.Jlight_Status.psuTemp', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='psuInputVolts', full_name='laser.Jlight_Status.psuInputVolts', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='psuOutputVolts', full_name='laser.Jlight_Status.psuOutputVolts', index=2,
      number=3, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='faults', full_name='laser.Jlight_Status.faults', index=3,
      number=4, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=564,
  serialized_end=680,
)


_BWT_STATUS = _descriptor.Descriptor(
  name='Bwt_Status',
  full_name='laser.Bwt_Status',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='faults', full_name='laser.Bwt_Status.faults', index=0,
      number=1, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=682,
  serialized_end=710,
)


_BWT_PASSTHROUGH_REQUEST = _descriptor.Descriptor(
  name='Bwt_Passthrough_Request',
  full_name='laser.Bwt_Passthrough_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='command', full_name='laser.Bwt_Passthrough_Request.command', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=712,
  serialized_end=754,
)


_BWT_PASSTHROUGH_REPLY = _descriptor.Descriptor(
  name='Bwt_Passthrough_Reply',
  full_name='laser.Bwt_Passthrough_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='response', full_name='laser.Bwt_Passthrough_Reply.response', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=756,
  serialized_end=797,
)


_BWT_TRANSPORT_GET_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Bwt_Transport_Get_Config_Request',
  full_name='laser.Bwt_Transport_Get_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=799,
  serialized_end=833,
)


_BWT_TRANSPORT_CONFIG = _descriptor.Descriptor(
  name='Bwt_Transport_Config',
  full_name='laser.Bwt_Transport_Config',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='log_messages', full_name='laser.Bwt_Transport_Config.log_messages', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intercommand_delay', full_name='laser.Bwt_Transport_Config.intercommand_delay', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=835,
  serialized_end=907,
)


_DIODE_STATUS_REQUEST = _descriptor.Descriptor(
  name='Diode_Status_Request',
  full_name='laser.Diode_Status_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=909,
  serialized_end=931,
)


_DIODE_STATUS_REPLY = _descriptor.Descriptor(
  name='Diode_Status_Reply',
  full_name='laser.Diode_Status_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='laser.Diode_Status_Reply.timestamp', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp', full_name='laser.Diode_Status_Reply.temp', index=1,
      number=2, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity', full_name='laser.Diode_Status_Reply.humidity', index=2,
      number=3, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current', full_name='laser.Diode_Status_Reply.current', index=3,
      number=4, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='faulted', full_name='laser.Diode_Status_Reply.faulted', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thermistors', full_name='laser.Diode_Status_Reply.thermistors', index=5,
      number=6, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='extra_bwt', full_name='laser.Diode_Status_Reply.extra_bwt', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='extra_jlight', full_name='laser.Diode_Status_Reply.extra_jlight', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='extra', full_name='laser.Diode_Status_Reply.extra',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=934,
  serialized_end=1155,
)


_DIODE_SET_CURRENT_REQUEST = _descriptor.Descriptor(
  name='Diode_Set_Current_Request',
  full_name='laser.Diode_Set_Current_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current', full_name='laser.Diode_Set_Current_Request.current', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='commit', full_name='laser.Diode_Set_Current_Request.commit', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1157,
  serialized_end=1217,
)


_LASER_REQUEST = _descriptor.Descriptor(
  name='Laser_Request',
  full_name='laser.Laser_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='on', full_name='laser.Laser_Request.on', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1219,
  serialized_end=1246,
)


_GET_LASER_REQUEST = _descriptor.Descriptor(
  name='Get_Laser_Request',
  full_name='laser.Get_Laser_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1248,
  serialized_end=1267,
)


_INTENSITY_REQUEST = _descriptor.Descriptor(
  name='Intensity_Request',
  full_name='laser.Intensity_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='intensity', full_name='laser.Intensity_Request.intensity', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1269,
  serialized_end=1307,
)


_LASER_REPLY = _descriptor.Descriptor(
  name='Laser_Reply',
  full_name='laser.Laser_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='raw_therm1_reading_mv', full_name='laser.Laser_Reply.raw_therm1_reading_mv', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='raw_therm2_reading_mv', full_name='laser.Laser_Reply.raw_therm2_reading_mv', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='on', full_name='laser.Laser_Reply.on', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lpsu_state', full_name='laser.Laser_Reply.lpsu_state', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fireable', full_name='laser.Laser_Reply.fireable', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1309,
  serialized_end=1434,
)


_LASER_STATE_REPLY = _descriptor.Descriptor(
  name='Laser_State_Reply',
  full_name='laser.Laser_State_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='on', full_name='laser.Laser_State_Reply.on', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1436,
  serialized_end=1467,
)


_LASER_STATUS_REPLY = _descriptor.Descriptor(
  name='Laser_Status_Reply',
  full_name='laser.Laser_Status_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lpsu_state', full_name='laser.Laser_Status_Reply.lpsu_state', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lpsu_current', full_name='laser.Laser_Status_Reply.lpsu_current', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='laser.Laser_Status_Reply.power', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='arc_detected', full_name='laser.Laser_Status_Reply.arc_detected', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1469,
  serialized_end=1568,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='laser.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='laser', full_name='laser.Request.laser', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_laser', full_name='laser.Request.get_laser', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intensity', full_name='laser.Request.intensity', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='raw_data', full_name='laser.Request.raw_data', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='diode_status', full_name='laser.Request.diode_status', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_inventory', full_name='laser.Request.laser_inventory', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bwt_passthrough', full_name='laser.Request.bwt_passthrough', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bwt_set_config', full_name='laser.Request.bwt_set_config', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bwt_get_config', full_name='laser.Request.bwt_get_config', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_reset', full_name='laser.Request.laser_reset', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='diode_set_current', full_name='laser.Request.diode_set_current', index=10,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_type', full_name='laser.Request.set_type', index=11,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='laser.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1571,
  serialized_end=2233,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='laser.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='laser.Reply.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='laser.Reply.ack', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser', full_name='laser.Reply.laser', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_reply', full_name='laser.Reply.laser_reply', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='raw_data', full_name='laser.Reply.raw_data', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='diode_status', full_name='laser.Reply.diode_status', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_inventory', full_name='laser.Reply.laser_inventory', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bwt_passthrough', full_name='laser.Reply.bwt_passthrough', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bwt_config', full_name='laser.Reply.bwt_config', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='laser.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2236,
  serialized_end=2653,
)

_RAW_DATA_REPLY.oneofs_by_name['_therm1_raw'].fields.append(
  _RAW_DATA_REPLY.fields_by_name['therm1_raw'])
_RAW_DATA_REPLY.fields_by_name['therm1_raw'].containing_oneof = _RAW_DATA_REPLY.oneofs_by_name['_therm1_raw']
_RAW_DATA_REPLY.oneofs_by_name['_therm2_raw'].fields.append(
  _RAW_DATA_REPLY.fields_by_name['therm2_raw'])
_RAW_DATA_REPLY.fields_by_name['therm2_raw'].containing_oneof = _RAW_DATA_REPLY.oneofs_by_name['_therm2_raw']
_RAW_DATA_REPLY.oneofs_by_name['_photodiode_raw'].fields.append(
  _RAW_DATA_REPLY.fields_by_name['photodiode_raw'])
_RAW_DATA_REPLY.fields_by_name['photodiode_raw'].containing_oneof = _RAW_DATA_REPLY.oneofs_by_name['_photodiode_raw']
_LASER_INVENTORY_REPLY.fields_by_name['type'].enum_type = _LASERTYPE
_LASER_SET_TYPE_REQUEST.fields_by_name['type'].enum_type = _LASERTYPE
_JLIGHT_STATUS.fields_by_name['faults'].enum_type = _JLIGHT_FAULT
_DIODE_STATUS_REPLY.fields_by_name['extra_bwt'].message_type = _BWT_STATUS
_DIODE_STATUS_REPLY.fields_by_name['extra_jlight'].message_type = _JLIGHT_STATUS
_DIODE_STATUS_REPLY.oneofs_by_name['extra'].fields.append(
  _DIODE_STATUS_REPLY.fields_by_name['extra_bwt'])
_DIODE_STATUS_REPLY.fields_by_name['extra_bwt'].containing_oneof = _DIODE_STATUS_REPLY.oneofs_by_name['extra']
_DIODE_STATUS_REPLY.oneofs_by_name['extra'].fields.append(
  _DIODE_STATUS_REPLY.fields_by_name['extra_jlight'])
_DIODE_STATUS_REPLY.fields_by_name['extra_jlight'].containing_oneof = _DIODE_STATUS_REPLY.oneofs_by_name['extra']
_REQUEST.fields_by_name['laser'].message_type = _LASER_REQUEST
_REQUEST.fields_by_name['get_laser'].message_type = _GET_LASER_REQUEST
_REQUEST.fields_by_name['intensity'].message_type = _INTENSITY_REQUEST
_REQUEST.fields_by_name['raw_data'].message_type = _RAW_DATA_REQUEST
_REQUEST.fields_by_name['diode_status'].message_type = _DIODE_STATUS_REQUEST
_REQUEST.fields_by_name['laser_inventory'].message_type = _LASER_INVENTORY_REQUEST
_REQUEST.fields_by_name['bwt_passthrough'].message_type = _BWT_PASSTHROUGH_REQUEST
_REQUEST.fields_by_name['bwt_set_config'].message_type = _BWT_TRANSPORT_CONFIG
_REQUEST.fields_by_name['bwt_get_config'].message_type = _BWT_TRANSPORT_GET_CONFIG_REQUEST
_REQUEST.fields_by_name['laser_reset'].message_type = _LASER_RESET_REQUEST
_REQUEST.fields_by_name['diode_set_current'].message_type = _DIODE_SET_CURRENT_REQUEST
_REQUEST.fields_by_name['set_type'].message_type = _LASER_SET_TYPE_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['laser'])
_REQUEST.fields_by_name['laser'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get_laser'])
_REQUEST.fields_by_name['get_laser'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['intensity'])
_REQUEST.fields_by_name['intensity'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['raw_data'])
_REQUEST.fields_by_name['raw_data'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['diode_status'])
_REQUEST.fields_by_name['diode_status'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['laser_inventory'])
_REQUEST.fields_by_name['laser_inventory'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['bwt_passthrough'])
_REQUEST.fields_by_name['bwt_passthrough'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['bwt_set_config'])
_REQUEST.fields_by_name['bwt_set_config'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['bwt_get_config'])
_REQUEST.fields_by_name['bwt_get_config'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['laser_reset'])
_REQUEST.fields_by_name['laser_reset'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['diode_set_current'])
_REQUEST.fields_by_name['diode_set_current'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set_type'])
_REQUEST.fields_by_name['set_type'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['laser'].message_type = _LASER_STATE_REPLY
_REPLY.fields_by_name['laser_reply'].message_type = _LASER_REPLY
_REPLY.fields_by_name['raw_data'].message_type = _RAW_DATA_REPLY
_REPLY.fields_by_name['diode_status'].message_type = _DIODE_STATUS_REPLY
_REPLY.fields_by_name['laser_inventory'].message_type = _LASER_INVENTORY_REPLY
_REPLY.fields_by_name['bwt_passthrough'].message_type = _BWT_PASSTHROUGH_REPLY
_REPLY.fields_by_name['bwt_config'].message_type = _BWT_TRANSPORT_CONFIG
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['laser'])
_REPLY.fields_by_name['laser'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['laser_reply'])
_REPLY.fields_by_name['laser_reply'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['raw_data'])
_REPLY.fields_by_name['raw_data'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['diode_status'])
_REPLY.fields_by_name['diode_status'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['laser_inventory'])
_REPLY.fields_by_name['laser_inventory'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['bwt_passthrough'])
_REPLY.fields_by_name['bwt_passthrough'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['bwt_config'])
_REPLY.fields_by_name['bwt_config'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Raw_Data_Request'] = _RAW_DATA_REQUEST
DESCRIPTOR.message_types_by_name['Raw_Data_Reply'] = _RAW_DATA_REPLY
DESCRIPTOR.message_types_by_name['Laser_Inventory_Request'] = _LASER_INVENTORY_REQUEST
DESCRIPTOR.message_types_by_name['Laser_Inventory_Reply'] = _LASER_INVENTORY_REPLY
DESCRIPTOR.message_types_by_name['Laser_Set_Type_Request'] = _LASER_SET_TYPE_REQUEST
DESCRIPTOR.message_types_by_name['Laser_Reset_Request'] = _LASER_RESET_REQUEST
DESCRIPTOR.message_types_by_name['Jlight_Status'] = _JLIGHT_STATUS
DESCRIPTOR.message_types_by_name['Bwt_Status'] = _BWT_STATUS
DESCRIPTOR.message_types_by_name['Bwt_Passthrough_Request'] = _BWT_PASSTHROUGH_REQUEST
DESCRIPTOR.message_types_by_name['Bwt_Passthrough_Reply'] = _BWT_PASSTHROUGH_REPLY
DESCRIPTOR.message_types_by_name['Bwt_Transport_Get_Config_Request'] = _BWT_TRANSPORT_GET_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Bwt_Transport_Config'] = _BWT_TRANSPORT_CONFIG
DESCRIPTOR.message_types_by_name['Diode_Status_Request'] = _DIODE_STATUS_REQUEST
DESCRIPTOR.message_types_by_name['Diode_Status_Reply'] = _DIODE_STATUS_REPLY
DESCRIPTOR.message_types_by_name['Diode_Set_Current_Request'] = _DIODE_SET_CURRENT_REQUEST
DESCRIPTOR.message_types_by_name['Laser_Request'] = _LASER_REQUEST
DESCRIPTOR.message_types_by_name['Get_Laser_Request'] = _GET_LASER_REQUEST
DESCRIPTOR.message_types_by_name['Intensity_Request'] = _INTENSITY_REQUEST
DESCRIPTOR.message_types_by_name['Laser_Reply'] = _LASER_REPLY
DESCRIPTOR.message_types_by_name['Laser_State_Reply'] = _LASER_STATE_REPLY
DESCRIPTOR.message_types_by_name['Laser_Status_Reply'] = _LASER_STATUS_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.enum_types_by_name['LaserType'] = _LASERTYPE
DESCRIPTOR.enum_types_by_name['Jlight_Fault'] = _JLIGHT_FAULT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Raw_Data_Request = _reflection.GeneratedProtocolMessageType('Raw_Data_Request', (_message.Message,), {
  'DESCRIPTOR' : _RAW_DATA_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Raw_Data_Request)
  })
_sym_db.RegisterMessage(Raw_Data_Request)

Raw_Data_Reply = _reflection.GeneratedProtocolMessageType('Raw_Data_Reply', (_message.Message,), {
  'DESCRIPTOR' : _RAW_DATA_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Raw_Data_Reply)
  })
_sym_db.RegisterMessage(Raw_Data_Reply)

Laser_Inventory_Request = _reflection.GeneratedProtocolMessageType('Laser_Inventory_Request', (_message.Message,), {
  'DESCRIPTOR' : _LASER_INVENTORY_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Laser_Inventory_Request)
  })
_sym_db.RegisterMessage(Laser_Inventory_Request)

Laser_Inventory_Reply = _reflection.GeneratedProtocolMessageType('Laser_Inventory_Reply', (_message.Message,), {
  'DESCRIPTOR' : _LASER_INVENTORY_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Laser_Inventory_Reply)
  })
_sym_db.RegisterMessage(Laser_Inventory_Reply)

Laser_Set_Type_Request = _reflection.GeneratedProtocolMessageType('Laser_Set_Type_Request', (_message.Message,), {
  'DESCRIPTOR' : _LASER_SET_TYPE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Laser_Set_Type_Request)
  })
_sym_db.RegisterMessage(Laser_Set_Type_Request)

Laser_Reset_Request = _reflection.GeneratedProtocolMessageType('Laser_Reset_Request', (_message.Message,), {
  'DESCRIPTOR' : _LASER_RESET_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Laser_Reset_Request)
  })
_sym_db.RegisterMessage(Laser_Reset_Request)

Jlight_Status = _reflection.GeneratedProtocolMessageType('Jlight_Status', (_message.Message,), {
  'DESCRIPTOR' : _JLIGHT_STATUS,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Jlight_Status)
  })
_sym_db.RegisterMessage(Jlight_Status)

Bwt_Status = _reflection.GeneratedProtocolMessageType('Bwt_Status', (_message.Message,), {
  'DESCRIPTOR' : _BWT_STATUS,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Bwt_Status)
  })
_sym_db.RegisterMessage(Bwt_Status)

Bwt_Passthrough_Request = _reflection.GeneratedProtocolMessageType('Bwt_Passthrough_Request', (_message.Message,), {
  'DESCRIPTOR' : _BWT_PASSTHROUGH_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Bwt_Passthrough_Request)
  })
_sym_db.RegisterMessage(Bwt_Passthrough_Request)

Bwt_Passthrough_Reply = _reflection.GeneratedProtocolMessageType('Bwt_Passthrough_Reply', (_message.Message,), {
  'DESCRIPTOR' : _BWT_PASSTHROUGH_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Bwt_Passthrough_Reply)
  })
_sym_db.RegisterMessage(Bwt_Passthrough_Reply)

Bwt_Transport_Get_Config_Request = _reflection.GeneratedProtocolMessageType('Bwt_Transport_Get_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _BWT_TRANSPORT_GET_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Bwt_Transport_Get_Config_Request)
  })
_sym_db.RegisterMessage(Bwt_Transport_Get_Config_Request)

Bwt_Transport_Config = _reflection.GeneratedProtocolMessageType('Bwt_Transport_Config', (_message.Message,), {
  'DESCRIPTOR' : _BWT_TRANSPORT_CONFIG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Bwt_Transport_Config)
  })
_sym_db.RegisterMessage(Bwt_Transport_Config)

Diode_Status_Request = _reflection.GeneratedProtocolMessageType('Diode_Status_Request', (_message.Message,), {
  'DESCRIPTOR' : _DIODE_STATUS_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Diode_Status_Request)
  })
_sym_db.RegisterMessage(Diode_Status_Request)

Diode_Status_Reply = _reflection.GeneratedProtocolMessageType('Diode_Status_Reply', (_message.Message,), {
  'DESCRIPTOR' : _DIODE_STATUS_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Diode_Status_Reply)
  })
_sym_db.RegisterMessage(Diode_Status_Reply)

Diode_Set_Current_Request = _reflection.GeneratedProtocolMessageType('Diode_Set_Current_Request', (_message.Message,), {
  'DESCRIPTOR' : _DIODE_SET_CURRENT_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Diode_Set_Current_Request)
  })
_sym_db.RegisterMessage(Diode_Set_Current_Request)

Laser_Request = _reflection.GeneratedProtocolMessageType('Laser_Request', (_message.Message,), {
  'DESCRIPTOR' : _LASER_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Laser_Request)
  })
_sym_db.RegisterMessage(Laser_Request)

Get_Laser_Request = _reflection.GeneratedProtocolMessageType('Get_Laser_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_LASER_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Get_Laser_Request)
  })
_sym_db.RegisterMessage(Get_Laser_Request)

Intensity_Request = _reflection.GeneratedProtocolMessageType('Intensity_Request', (_message.Message,), {
  'DESCRIPTOR' : _INTENSITY_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Intensity_Request)
  })
_sym_db.RegisterMessage(Intensity_Request)

Laser_Reply = _reflection.GeneratedProtocolMessageType('Laser_Reply', (_message.Message,), {
  'DESCRIPTOR' : _LASER_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Laser_Reply)
  })
_sym_db.RegisterMessage(Laser_Reply)

Laser_State_Reply = _reflection.GeneratedProtocolMessageType('Laser_State_Reply', (_message.Message,), {
  'DESCRIPTOR' : _LASER_STATE_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Laser_State_Reply)
  })
_sym_db.RegisterMessage(Laser_State_Reply)

Laser_Status_Reply = _reflection.GeneratedProtocolMessageType('Laser_Status_Reply', (_message.Message,), {
  'DESCRIPTOR' : _LASER_STATUS_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Laser_Status_Reply)
  })
_sym_db.RegisterMessage(Laser_Status_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:laser.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

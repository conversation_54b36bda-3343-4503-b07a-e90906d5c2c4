"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    exposure_us: builtin___int = ...
    period_us: builtin___int = ...
    targets_per_predict_ratio: builtin___int = ...

    def __init__(self,
        *,
        exposure_us : typing___Optional[builtin___int] = None,
        period_us : typing___Optional[builtin___int] = None,
        targets_per_predict_ratio : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"exposure_us",b"exposure_us",u"period_us",b"period_us",u"targets_per_predict_ratio",b"targets_per_predict_ratio"]) -> None: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ok: builtin___bool = ...

    def __init__(self,
        *,
        ok : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ok",b"ok"]) -> None: ...
type___Reply = Reply

/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_HWINFO_HWINFO_PB_H_INCLUDED
#define PB_HWINFO_HWINFO_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _hwinfo_BoardIdentityRequest {
    char dummy_field;
} hwinfo_BoardIdentityRequest;

typedef struct _hwinfo_BoardVersionRequest {
    char dummy_field;
} hwinfo_BoardVersionRequest;

typedef struct _hwinfo_BoardIdentityReply {
    pb_size_t which__cbsn;
    union {
        char cbsn[16];
    } _cbsn;
    pb_size_t which__assySn;
    union {
        char assySn[32];
    } _assySn;
} hwinfo_BoardIdentityReply;

typedef struct _hwinfo_BoardVersionReply {
    char model[32];
    uint32_t rev;
} hwinfo_BoardVersionReply;

typedef struct _hwinfo_Request {
    pb_size_t which_request;
    union {
        hwinfo_BoardVersionRequest version;
        hwinfo_BoardIdentityRequest identity;
    } request;
} hwinfo_Request;

typedef struct _hwinfo_Reply {
    pb_size_t which_reply;
    union {
        hwinfo_BoardVersionReply version;
        hwinfo_BoardIdentityReply identity;
    } reply;
} hwinfo_Reply;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define hwinfo_BoardVersionRequest_init_default  {0}
#define hwinfo_BoardIdentityRequest_init_default {0}
#define hwinfo_Request_init_default              {0, {hwinfo_BoardVersionRequest_init_default}}
#define hwinfo_BoardVersionReply_init_default    {"", 0}
#define hwinfo_BoardIdentityReply_init_default   {0, {""}, 0, {""}}
#define hwinfo_Reply_init_default                {0, {hwinfo_BoardVersionReply_init_default}}
#define hwinfo_BoardVersionRequest_init_zero     {0}
#define hwinfo_BoardIdentityRequest_init_zero    {0}
#define hwinfo_Request_init_zero                 {0, {hwinfo_BoardVersionRequest_init_zero}}
#define hwinfo_BoardVersionReply_init_zero       {"", 0}
#define hwinfo_BoardIdentityReply_init_zero      {0, {""}, 0, {""}}
#define hwinfo_Reply_init_zero                   {0, {hwinfo_BoardVersionReply_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define hwinfo_BoardIdentityReply_cbsn_tag       1
#define hwinfo_BoardIdentityReply_assySn_tag     2
#define hwinfo_BoardVersionReply_model_tag       1
#define hwinfo_BoardVersionReply_rev_tag         2
#define hwinfo_Request_version_tag               1
#define hwinfo_Request_identity_tag              2
#define hwinfo_Reply_version_tag                 1
#define hwinfo_Reply_identity_tag                2

/* Struct field encoding specification for nanopb */
#define hwinfo_BoardVersionRequest_FIELDLIST(X, a) \

#define hwinfo_BoardVersionRequest_CALLBACK NULL
#define hwinfo_BoardVersionRequest_DEFAULT NULL

#define hwinfo_BoardIdentityRequest_FIELDLIST(X, a) \

#define hwinfo_BoardIdentityRequest_CALLBACK NULL
#define hwinfo_BoardIdentityRequest_DEFAULT NULL

#define hwinfo_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,version,request.version),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,identity,request.identity),   2)
#define hwinfo_Request_CALLBACK NULL
#define hwinfo_Request_DEFAULT NULL
#define hwinfo_Request_request_version_MSGTYPE hwinfo_BoardVersionRequest
#define hwinfo_Request_request_identity_MSGTYPE hwinfo_BoardIdentityRequest

#define hwinfo_BoardVersionReply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   model,             1) \
X(a, STATIC,   SINGULAR, UINT32,   rev,               2)
#define hwinfo_BoardVersionReply_CALLBACK NULL
#define hwinfo_BoardVersionReply_DEFAULT NULL

#define hwinfo_BoardIdentityReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    STRING,   (_cbsn,cbsn,_cbsn.cbsn),   1) \
X(a, STATIC,   ONEOF,    STRING,   (_assySn,assySn,_assySn.assySn),   2)
#define hwinfo_BoardIdentityReply_CALLBACK NULL
#define hwinfo_BoardIdentityReply_DEFAULT NULL

#define hwinfo_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,version,reply.version),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,identity,reply.identity),   2)
#define hwinfo_Reply_CALLBACK NULL
#define hwinfo_Reply_DEFAULT NULL
#define hwinfo_Reply_reply_version_MSGTYPE hwinfo_BoardVersionReply
#define hwinfo_Reply_reply_identity_MSGTYPE hwinfo_BoardIdentityReply

extern const pb_msgdesc_t hwinfo_BoardVersionRequest_msg;
extern const pb_msgdesc_t hwinfo_BoardIdentityRequest_msg;
extern const pb_msgdesc_t hwinfo_Request_msg;
extern const pb_msgdesc_t hwinfo_BoardVersionReply_msg;
extern const pb_msgdesc_t hwinfo_BoardIdentityReply_msg;
extern const pb_msgdesc_t hwinfo_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define hwinfo_BoardVersionRequest_fields &hwinfo_BoardVersionRequest_msg
#define hwinfo_BoardIdentityRequest_fields &hwinfo_BoardIdentityRequest_msg
#define hwinfo_Request_fields &hwinfo_Request_msg
#define hwinfo_BoardVersionReply_fields &hwinfo_BoardVersionReply_msg
#define hwinfo_BoardIdentityReply_fields &hwinfo_BoardIdentityReply_msg
#define hwinfo_Reply_fields &hwinfo_Reply_msg

/* Maximum encoded size of messages (where known) */
#define hwinfo_BoardVersionRequest_size          0
#define hwinfo_BoardIdentityRequest_size         0
#define hwinfo_Request_size                      2
#define hwinfo_BoardVersionReply_size            39
#define hwinfo_BoardIdentityReply_size           50
#define hwinfo_Reply_size                        52

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

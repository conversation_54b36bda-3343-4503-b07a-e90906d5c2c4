/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_ROW_MODULE_ROW_MODULE_PB_H_INCLUDED
#define PB_ROW_MODULE_ROW_MODULE_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/scanner.pb.h"
#include "generated/lib/drivers/nanopb/proto/dawg.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _row_module_ACK_Reply {
    char dummy_field;
} row_module_ACK_Reply;

typedef struct _row_module_Clear_Config_Request {
    char dummy_field;
} row_module_Clear_Config_Request;

typedef struct _row_module_Reset_Request {
    char dummy_field;
} row_module_Reset_Request;

typedef struct _row_module_Reply {
    pb_size_t which_reply;
    union {
        row_module_ACK_Reply ack;
        scanner_Reply scanner;
        dawg_Reply dawg;
    } reply;
} row_module_Reply;

typedef struct _row_module_Scanner_Request {
    uint32_t scanner_id;
    bool has_request;
    scanner_Request request;
} row_module_Scanner_Request;

typedef struct _row_module_Request {
    pb_size_t which_request;
    union {
        row_module_Reset_Request reset;
        row_module_Clear_Config_Request clear;
        row_module_Scanner_Request scanner;
        dawg_Request dawg;
    } request;
} row_module_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define row_module_Reset_Request_init_default    {0}
#define row_module_Clear_Config_Request_init_default {0}
#define row_module_Scanner_Request_init_default  {0, false, scanner_Request_init_default}
#define row_module_ACK_Reply_init_default        {0}
#define row_module_Request_init_default          {0, {row_module_Reset_Request_init_default}}
#define row_module_Reply_init_default            {0, {row_module_ACK_Reply_init_default}}
#define row_module_Reset_Request_init_zero       {0}
#define row_module_Clear_Config_Request_init_zero {0}
#define row_module_Scanner_Request_init_zero     {0, false, scanner_Request_init_zero}
#define row_module_ACK_Reply_init_zero           {0}
#define row_module_Request_init_zero             {0, {row_module_Reset_Request_init_zero}}
#define row_module_Reply_init_zero               {0, {row_module_ACK_Reply_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define row_module_Reply_ack_tag                 1
#define row_module_Reply_scanner_tag             2
#define row_module_Reply_dawg_tag                3
#define row_module_Scanner_Request_scanner_id_tag 1
#define row_module_Scanner_Request_request_tag   2
#define row_module_Request_reset_tag             1
#define row_module_Request_clear_tag             2
#define row_module_Request_scanner_tag           3
#define row_module_Request_dawg_tag              4

/* Struct field encoding specification for nanopb */
#define row_module_Reset_Request_FIELDLIST(X, a) \

#define row_module_Reset_Request_CALLBACK NULL
#define row_module_Reset_Request_DEFAULT NULL

#define row_module_Clear_Config_Request_FIELDLIST(X, a) \

#define row_module_Clear_Config_Request_CALLBACK NULL
#define row_module_Clear_Config_Request_DEFAULT NULL

#define row_module_Scanner_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   scanner_id,        1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  request,           2)
#define row_module_Scanner_Request_CALLBACK NULL
#define row_module_Scanner_Request_DEFAULT NULL
#define row_module_Scanner_Request_request_MSGTYPE scanner_Request

#define row_module_ACK_Reply_FIELDLIST(X, a) \

#define row_module_ACK_Reply_CALLBACK NULL
#define row_module_ACK_Reply_DEFAULT NULL

#define row_module_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,clear,request.clear),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,scanner,request.scanner),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,dawg,request.dawg),   4)
#define row_module_Request_CALLBACK NULL
#define row_module_Request_DEFAULT NULL
#define row_module_Request_request_reset_MSGTYPE row_module_Reset_Request
#define row_module_Request_request_clear_MSGTYPE row_module_Clear_Config_Request
#define row_module_Request_request_scanner_MSGTYPE row_module_Scanner_Request
#define row_module_Request_request_dawg_MSGTYPE dawg_Request

#define row_module_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,scanner,reply.scanner),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,dawg,reply.dawg),   3)
#define row_module_Reply_CALLBACK NULL
#define row_module_Reply_DEFAULT NULL
#define row_module_Reply_reply_ack_MSGTYPE row_module_ACK_Reply
#define row_module_Reply_reply_scanner_MSGTYPE scanner_Reply
#define row_module_Reply_reply_dawg_MSGTYPE dawg_Reply

extern const pb_msgdesc_t row_module_Reset_Request_msg;
extern const pb_msgdesc_t row_module_Clear_Config_Request_msg;
extern const pb_msgdesc_t row_module_Scanner_Request_msg;
extern const pb_msgdesc_t row_module_ACK_Reply_msg;
extern const pb_msgdesc_t row_module_Request_msg;
extern const pb_msgdesc_t row_module_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define row_module_Reset_Request_fields &row_module_Reset_Request_msg
#define row_module_Clear_Config_Request_fields &row_module_Clear_Config_Request_msg
#define row_module_Scanner_Request_fields &row_module_Scanner_Request_msg
#define row_module_ACK_Reply_fields &row_module_ACK_Reply_msg
#define row_module_Request_fields &row_module_Request_msg
#define row_module_Reply_fields &row_module_Reply_msg

/* Maximum encoded size of messages (where known) */
#define row_module_Reset_Request_size            0
#define row_module_Clear_Config_Request_size     0
#if defined(scanner_Request_size)
#define row_module_Scanner_Request_size          (12 + scanner_Request_size)
#endif
#define row_module_ACK_Reply_size                0
#if defined(scanner_Request_size) && defined(dawg_Request_size)
typedef union row_module_Request_request_size_union {char f3[(18 + scanner_Request_size)]; char f4[(6 + dawg_Request_size)]; char f0[2];} row_module_Request_request_size_union;
#define row_module_Request_size                  (0 + sizeof(row_module_Request_request_size_union))
#endif
#if defined(scanner_Reply_size) && defined(dawg_Reply_size)
typedef union row_module_Reply_reply_size_union {char f2[(6 + scanner_Reply_size)]; char f3[(6 + dawg_Reply_size)]; char f0[2];} row_module_Reply_reply_size_union;
#define row_module_Reply_size                    (0 + sizeof(row_module_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

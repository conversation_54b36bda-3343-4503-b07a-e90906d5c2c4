/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "benjamin_gps_board.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(benjamin_gps_board_PtpConfig_Request, benjamin_gps_board_PtpConfig_Request, AUTO)


PB_BIND(benjamin_gps_board_Reply, benjamin_gps_board_Reply, 2)


PB_BIND(benjamin_gps_board_Request, benjamin_gps_board_Request, 2)




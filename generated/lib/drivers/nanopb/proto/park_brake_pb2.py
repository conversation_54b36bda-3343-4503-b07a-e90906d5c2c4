# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/park_brake.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/park_brake.proto',
  package='park_brake',
  syntax='proto3',
  serialized_options=b'Z\021nanopb/park_brake',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n3generated/lib/drivers/nanopb/proto/park_brake.proto\x12\npark_brake\"\x18\n\x07Request\x12\r\n\x05onoff\x18\x01 \x01(\x08\"\x07\n\x05Reply\"\x0f\n\rQuery_Request\"\x1c\n\x0bQuery_Reply\x12\r\n\x05onoff\x18\x01 \x01(\x08\x42\x13Z\x11nanopb/park_brakeb\x06proto3'
)




_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='park_brake.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='onoff', full_name='park_brake.Request.onoff', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=67,
  serialized_end=91,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='park_brake.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=93,
  serialized_end=100,
)


_QUERY_REQUEST = _descriptor.Descriptor(
  name='Query_Request',
  full_name='park_brake.Query_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=102,
  serialized_end=117,
)


_QUERY_REPLY = _descriptor.Descriptor(
  name='Query_Reply',
  full_name='park_brake.Query_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='onoff', full_name='park_brake.Query_Reply.onoff', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=119,
  serialized_end=147,
)

DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.message_types_by_name['Query_Request'] = _QUERY_REQUEST
DESCRIPTOR.message_types_by_name['Query_Reply'] = _QUERY_REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.park_brake_pb2'
  # @@protoc_insertion_point(class_scope:park_brake.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.park_brake_pb2'
  # @@protoc_insertion_point(class_scope:park_brake.Reply)
  })
_sym_db.RegisterMessage(Reply)

Query_Request = _reflection.GeneratedProtocolMessageType('Query_Request', (_message.Message,), {
  'DESCRIPTOR' : _QUERY_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.park_brake_pb2'
  # @@protoc_insertion_point(class_scope:park_brake.Query_Request)
  })
_sym_db.RegisterMessage(Query_Request)

Query_Reply = _reflection.GeneratedProtocolMessageType('Query_Reply', (_message.Message,), {
  'DESCRIPTOR' : _QUERY_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.park_brake_pb2'
  # @@protoc_insertion_point(class_scope:park_brake.Query_Reply)
  })
_sym_db.RegisterMessage(Query_Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/can_open.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/can_open.proto',
  package='can_open',
  syntax='proto3',
  serialized_options=b'Z\017nanopb/can_open',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n1generated/lib/drivers/nanopb/proto/can_open.proto\x12\x08\x63\x61n_open\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\"\\\n\x0bSDO_Request\x12\r\n\x05index\x18\x01 \x01(\r\x12\x10\n\x08subindex\x18\x02 \x01(\r\x12\r\n\x05value\x18\x03 \x01(\r\x12\n\n\x02\x63s\x18\x04 \x01(\r\x12\x11\n\texpedited\x18\x05 \x01(\r\"7\n\x0bPDO_Request\x12\x0c\n\x04\x66unc\x18\x01 \x01(\r\x12\x0c\n\x04size\x18\x02 \x01(\r\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\x0c\"\x1f\n\x0fRTR_PDO_Request\x12\x0c\n\x04\x66unc\x18\x01 \x01(\r\"\x1c\n\x0bNMT_Request\x12\r\n\x05state\x18\x01 \x01(\r\"1\n\rAwait_Request\x12\x12\n\ntimeout_ms\x18\x01 \x01(\r\x12\x0c\n\x04\x66unc\x18\x02 \x01(\r\"F\n\x14SDO_Download_Request\x12\r\n\x05index\x18\x01 \x01(\r\x12\x10\n\x08subindex\x18\x02 \x01(\r\x12\r\n\x05value\x18\x03 \x01(\r\"5\n\x12SDO_Upload_Request\x12\r\n\x05index\x18\x01 \x01(\r\x12\x10\n\x08subindex\x18\x02 \x01(\r\"\x13\n\x11NMT_Reset_Request\"\x13\n\x11NMT_Start_Request\"\x12\n\x10NMT_Stop_Request\"\x0b\n\tACK_Reply\"I\n\nSDO_Packet\x12\x0c\n\x04spec\x18\x01 \x01(\r\x12\r\n\x05index\x18\x02 \x01(\r\x12\x10\n\x08subindex\x18\x03 \x01(\r\x12\x0c\n\x04\x64\x61ta\x18\x04 \x01(\x0c\"\x1a\n\nPDO_Packet\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\",\n\nNMT_Packet\x12\r\n\x05state\x18\x01 \x01(\r\x12\x0f\n\x07node_id\x18\x02 \x01(\r\"\xa4\x01\n\rMessage_Reply\x12\x0c\n\x04\x66unc\x18\x01 \x01(\r\x12\x0f\n\x07node_id\x18\x02 \x01(\r\x12#\n\x03sdo\x18\x03 \x01(\x0b\x32\x14.can_open.SDO_PacketH\x00\x12#\n\x03pdo\x18\x04 \x01(\x0b\x32\x14.can_open.PDO_PacketH\x00\x12#\n\x03nmt\x18\x05 \x01(\x0b\x32\x14.can_open.NMT_PacketH\x00\x42\x05\n\x03pkt\"\xce\x03\n\x07Request\x12$\n\x03sdo\x18\x01 \x01(\x0b\x32\x15.can_open.SDO_RequestH\x00\x12$\n\x03pdo\x18\x02 \x01(\x0b\x32\x15.can_open.PDO_RequestH\x00\x12(\n\x03rtr\x18\x03 \x01(\x0b\x32\x19.can_open.RTR_PDO_RequestH\x00\x12$\n\x03nmt\x18\x04 \x01(\x0b\x32\x15.can_open.NMT_RequestH\x00\x12(\n\x05\x61wait\x18\x05 \x01(\x0b\x32\x17.can_open.Await_RequestH\x00\x12\x36\n\x0csdo_download\x18\x06 \x01(\x0b\x32\x1e.can_open.SDO_Download_RequestH\x00\x12\x32\n\nsdo_upload\x18\x07 \x01(\x0b\x32\x1c.can_open.SDO_Upload_RequestH\x00\x12,\n\x05reset\x18\x08 \x01(\x0b\x32\x1b.can_open.NMT_Reset_RequestH\x00\x12,\n\x05start\x18\t \x01(\x0b\x32\x1b.can_open.NMT_Start_RequestH\x00\x12*\n\x04stop\x18\n \x01(\x0b\x32\x1a.can_open.NMT_Stop_RequestH\x00\x42\t\n\x07request\"p\n\x05Reply\x12\x17\n\x03\x61\x63k\x18\x01 \x01(\x0b\x32\x08.ack.AckH\x00\x12&\n\x03msg\x18\x02 \x01(\x0b\x32\x17.can_open.Message_ReplyH\x00\x12\x1d\n\x05\x65rror\x18\x03 \x01(\x0b\x32\x0c.error.ErrorH\x00\x42\x07\n\x05replyB\x11Z\x0fnanopb/can_openb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,])




_SDO_REQUEST = _descriptor.Descriptor(
  name='SDO_Request',
  full_name='can_open.SDO_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='index', full_name='can_open.SDO_Request.index', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='subindex', full_name='can_open.SDO_Request.subindex', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='can_open.SDO_Request.value', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cs', full_name='can_open.SDO_Request.cs', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expedited', full_name='can_open.SDO_Request.expedited', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=157,
  serialized_end=249,
)


_PDO_REQUEST = _descriptor.Descriptor(
  name='PDO_Request',
  full_name='can_open.PDO_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='func', full_name='can_open.PDO_Request.func', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size', full_name='can_open.PDO_Request.size', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='can_open.PDO_Request.data', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=251,
  serialized_end=306,
)


_RTR_PDO_REQUEST = _descriptor.Descriptor(
  name='RTR_PDO_Request',
  full_name='can_open.RTR_PDO_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='func', full_name='can_open.RTR_PDO_Request.func', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=308,
  serialized_end=339,
)


_NMT_REQUEST = _descriptor.Descriptor(
  name='NMT_Request',
  full_name='can_open.NMT_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='state', full_name='can_open.NMT_Request.state', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=341,
  serialized_end=369,
)


_AWAIT_REQUEST = _descriptor.Descriptor(
  name='Await_Request',
  full_name='can_open.Await_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timeout_ms', full_name='can_open.Await_Request.timeout_ms', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='func', full_name='can_open.Await_Request.func', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=371,
  serialized_end=420,
)


_SDO_DOWNLOAD_REQUEST = _descriptor.Descriptor(
  name='SDO_Download_Request',
  full_name='can_open.SDO_Download_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='index', full_name='can_open.SDO_Download_Request.index', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='subindex', full_name='can_open.SDO_Download_Request.subindex', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='can_open.SDO_Download_Request.value', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=422,
  serialized_end=492,
)


_SDO_UPLOAD_REQUEST = _descriptor.Descriptor(
  name='SDO_Upload_Request',
  full_name='can_open.SDO_Upload_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='index', full_name='can_open.SDO_Upload_Request.index', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='subindex', full_name='can_open.SDO_Upload_Request.subindex', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=494,
  serialized_end=547,
)


_NMT_RESET_REQUEST = _descriptor.Descriptor(
  name='NMT_Reset_Request',
  full_name='can_open.NMT_Reset_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=549,
  serialized_end=568,
)


_NMT_START_REQUEST = _descriptor.Descriptor(
  name='NMT_Start_Request',
  full_name='can_open.NMT_Start_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=570,
  serialized_end=589,
)


_NMT_STOP_REQUEST = _descriptor.Descriptor(
  name='NMT_Stop_Request',
  full_name='can_open.NMT_Stop_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=591,
  serialized_end=609,
)


_ACK_REPLY = _descriptor.Descriptor(
  name='ACK_Reply',
  full_name='can_open.ACK_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=611,
  serialized_end=622,
)


_SDO_PACKET = _descriptor.Descriptor(
  name='SDO_Packet',
  full_name='can_open.SDO_Packet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='spec', full_name='can_open.SDO_Packet.spec', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='index', full_name='can_open.SDO_Packet.index', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='subindex', full_name='can_open.SDO_Packet.subindex', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='can_open.SDO_Packet.data', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=624,
  serialized_end=697,
)


_PDO_PACKET = _descriptor.Descriptor(
  name='PDO_Packet',
  full_name='can_open.PDO_Packet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='can_open.PDO_Packet.data', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=699,
  serialized_end=725,
)


_NMT_PACKET = _descriptor.Descriptor(
  name='NMT_Packet',
  full_name='can_open.NMT_Packet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='state', full_name='can_open.NMT_Packet.state', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='node_id', full_name='can_open.NMT_Packet.node_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=727,
  serialized_end=771,
)


_MESSAGE_REPLY = _descriptor.Descriptor(
  name='Message_Reply',
  full_name='can_open.Message_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='func', full_name='can_open.Message_Reply.func', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='node_id', full_name='can_open.Message_Reply.node_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sdo', full_name='can_open.Message_Reply.sdo', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pdo', full_name='can_open.Message_Reply.pdo', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nmt', full_name='can_open.Message_Reply.nmt', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='pkt', full_name='can_open.Message_Reply.pkt',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=774,
  serialized_end=938,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='can_open.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sdo', full_name='can_open.Request.sdo', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pdo', full_name='can_open.Request.pdo', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rtr', full_name='can_open.Request.rtr', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nmt', full_name='can_open.Request.nmt', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='await', full_name='can_open.Request.await', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sdo_download', full_name='can_open.Request.sdo_download', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sdo_upload', full_name='can_open.Request.sdo_upload', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reset', full_name='can_open.Request.reset', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start', full_name='can_open.Request.start', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stop', full_name='can_open.Request.stop', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='can_open.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=941,
  serialized_end=1403,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='can_open.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ack', full_name='can_open.Reply.ack', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='msg', full_name='can_open.Reply.msg', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error', full_name='can_open.Reply.error', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='can_open.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1405,
  serialized_end=1517,
)

_MESSAGE_REPLY.fields_by_name['sdo'].message_type = _SDO_PACKET
_MESSAGE_REPLY.fields_by_name['pdo'].message_type = _PDO_PACKET
_MESSAGE_REPLY.fields_by_name['nmt'].message_type = _NMT_PACKET
_MESSAGE_REPLY.oneofs_by_name['pkt'].fields.append(
  _MESSAGE_REPLY.fields_by_name['sdo'])
_MESSAGE_REPLY.fields_by_name['sdo'].containing_oneof = _MESSAGE_REPLY.oneofs_by_name['pkt']
_MESSAGE_REPLY.oneofs_by_name['pkt'].fields.append(
  _MESSAGE_REPLY.fields_by_name['pdo'])
_MESSAGE_REPLY.fields_by_name['pdo'].containing_oneof = _MESSAGE_REPLY.oneofs_by_name['pkt']
_MESSAGE_REPLY.oneofs_by_name['pkt'].fields.append(
  _MESSAGE_REPLY.fields_by_name['nmt'])
_MESSAGE_REPLY.fields_by_name['nmt'].containing_oneof = _MESSAGE_REPLY.oneofs_by_name['pkt']
_REQUEST.fields_by_name['sdo'].message_type = _SDO_REQUEST
_REQUEST.fields_by_name['pdo'].message_type = _PDO_REQUEST
_REQUEST.fields_by_name['rtr'].message_type = _RTR_PDO_REQUEST
_REQUEST.fields_by_name['nmt'].message_type = _NMT_REQUEST
_REQUEST.fields_by_name['await'].message_type = _AWAIT_REQUEST
_REQUEST.fields_by_name['sdo_download'].message_type = _SDO_DOWNLOAD_REQUEST
_REQUEST.fields_by_name['sdo_upload'].message_type = _SDO_UPLOAD_REQUEST
_REQUEST.fields_by_name['reset'].message_type = _NMT_RESET_REQUEST
_REQUEST.fields_by_name['start'].message_type = _NMT_START_REQUEST
_REQUEST.fields_by_name['stop'].message_type = _NMT_STOP_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['sdo'])
_REQUEST.fields_by_name['sdo'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['pdo'])
_REQUEST.fields_by_name['pdo'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['rtr'])
_REQUEST.fields_by_name['rtr'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['nmt'])
_REQUEST.fields_by_name['nmt'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['await'])
_REQUEST.fields_by_name['await'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['sdo_download'])
_REQUEST.fields_by_name['sdo_download'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['sdo_upload'])
_REQUEST.fields_by_name['sdo_upload'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['reset'])
_REQUEST.fields_by_name['reset'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['start'])
_REQUEST.fields_by_name['start'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['stop'])
_REQUEST.fields_by_name['stop'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['msg'].message_type = _MESSAGE_REPLY
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['msg'])
_REPLY.fields_by_name['msg'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['SDO_Request'] = _SDO_REQUEST
DESCRIPTOR.message_types_by_name['PDO_Request'] = _PDO_REQUEST
DESCRIPTOR.message_types_by_name['RTR_PDO_Request'] = _RTR_PDO_REQUEST
DESCRIPTOR.message_types_by_name['NMT_Request'] = _NMT_REQUEST
DESCRIPTOR.message_types_by_name['Await_Request'] = _AWAIT_REQUEST
DESCRIPTOR.message_types_by_name['SDO_Download_Request'] = _SDO_DOWNLOAD_REQUEST
DESCRIPTOR.message_types_by_name['SDO_Upload_Request'] = _SDO_UPLOAD_REQUEST
DESCRIPTOR.message_types_by_name['NMT_Reset_Request'] = _NMT_RESET_REQUEST
DESCRIPTOR.message_types_by_name['NMT_Start_Request'] = _NMT_START_REQUEST
DESCRIPTOR.message_types_by_name['NMT_Stop_Request'] = _NMT_STOP_REQUEST
DESCRIPTOR.message_types_by_name['ACK_Reply'] = _ACK_REPLY
DESCRIPTOR.message_types_by_name['SDO_Packet'] = _SDO_PACKET
DESCRIPTOR.message_types_by_name['PDO_Packet'] = _PDO_PACKET
DESCRIPTOR.message_types_by_name['NMT_Packet'] = _NMT_PACKET
DESCRIPTOR.message_types_by_name['Message_Reply'] = _MESSAGE_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SDO_Request = _reflection.GeneratedProtocolMessageType('SDO_Request', (_message.Message,), {
  'DESCRIPTOR' : _SDO_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.SDO_Request)
  })
_sym_db.RegisterMessage(SDO_Request)

PDO_Request = _reflection.GeneratedProtocolMessageType('PDO_Request', (_message.Message,), {
  'DESCRIPTOR' : _PDO_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.PDO_Request)
  })
_sym_db.RegisterMessage(PDO_Request)

RTR_PDO_Request = _reflection.GeneratedProtocolMessageType('RTR_PDO_Request', (_message.Message,), {
  'DESCRIPTOR' : _RTR_PDO_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.RTR_PDO_Request)
  })
_sym_db.RegisterMessage(RTR_PDO_Request)

NMT_Request = _reflection.GeneratedProtocolMessageType('NMT_Request', (_message.Message,), {
  'DESCRIPTOR' : _NMT_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.NMT_Request)
  })
_sym_db.RegisterMessage(NMT_Request)

Await_Request = _reflection.GeneratedProtocolMessageType('Await_Request', (_message.Message,), {
  'DESCRIPTOR' : _AWAIT_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.Await_Request)
  })
_sym_db.RegisterMessage(Await_Request)

SDO_Download_Request = _reflection.GeneratedProtocolMessageType('SDO_Download_Request', (_message.Message,), {
  'DESCRIPTOR' : _SDO_DOWNLOAD_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.SDO_Download_Request)
  })
_sym_db.RegisterMessage(SDO_Download_Request)

SDO_Upload_Request = _reflection.GeneratedProtocolMessageType('SDO_Upload_Request', (_message.Message,), {
  'DESCRIPTOR' : _SDO_UPLOAD_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.SDO_Upload_Request)
  })
_sym_db.RegisterMessage(SDO_Upload_Request)

NMT_Reset_Request = _reflection.GeneratedProtocolMessageType('NMT_Reset_Request', (_message.Message,), {
  'DESCRIPTOR' : _NMT_RESET_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.NMT_Reset_Request)
  })
_sym_db.RegisterMessage(NMT_Reset_Request)

NMT_Start_Request = _reflection.GeneratedProtocolMessageType('NMT_Start_Request', (_message.Message,), {
  'DESCRIPTOR' : _NMT_START_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.NMT_Start_Request)
  })
_sym_db.RegisterMessage(NMT_Start_Request)

NMT_Stop_Request = _reflection.GeneratedProtocolMessageType('NMT_Stop_Request', (_message.Message,), {
  'DESCRIPTOR' : _NMT_STOP_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.NMT_Stop_Request)
  })
_sym_db.RegisterMessage(NMT_Stop_Request)

ACK_Reply = _reflection.GeneratedProtocolMessageType('ACK_Reply', (_message.Message,), {
  'DESCRIPTOR' : _ACK_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.ACK_Reply)
  })
_sym_db.RegisterMessage(ACK_Reply)

SDO_Packet = _reflection.GeneratedProtocolMessageType('SDO_Packet', (_message.Message,), {
  'DESCRIPTOR' : _SDO_PACKET,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.SDO_Packet)
  })
_sym_db.RegisterMessage(SDO_Packet)

PDO_Packet = _reflection.GeneratedProtocolMessageType('PDO_Packet', (_message.Message,), {
  'DESCRIPTOR' : _PDO_PACKET,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.PDO_Packet)
  })
_sym_db.RegisterMessage(PDO_Packet)

NMT_Packet = _reflection.GeneratedProtocolMessageType('NMT_Packet', (_message.Message,), {
  'DESCRIPTOR' : _NMT_PACKET,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.NMT_Packet)
  })
_sym_db.RegisterMessage(NMT_Packet)

Message_Reply = _reflection.GeneratedProtocolMessageType('Message_Reply', (_message.Message,), {
  'DESCRIPTOR' : _MESSAGE_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.Message_Reply)
  })
_sym_db.RegisterMessage(Message_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.can_open_pb2'
  # @@protoc_insertion_point(class_scope:can_open.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

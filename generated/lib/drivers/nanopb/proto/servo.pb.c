/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "servo.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(servo_Config, servo_Config, AUTO)


PB_BIND(servo_Config_Request, servo_Config_Request, AUTO)


PB_BIND(servo_Boot_Request, servo_Boot_Request, 2)


PB_BIND(servo_Stop_Request, servo_Stop_Request, AUTO)


PB_BIND(servo_Go_To_Request, servo_Go_To_Request, AUTO)


PB_BIND(servo_Get_Limits_Request, servo_Get_Limits_Request, AUTO)


PB_BIND(servo_Go_To_Delta_Request, servo_Go_To_Delta_Request, AUTO)


PB_BIND(servo_Go_To_Delta_Follow_Request, servo_Go_To_Delta_Follow_Request, AUTO)


PB_BIND(servo_Go_To_Calibrate_Request, servo_Go_To_Calibrate_Request, AUTO)


PB_BIND(servo_Go_To_Timestamp_Request, servo_Go_To_Timestamp_Request, 2)


PB_BIND(servo_Go_To_Follow_Request, servo_Go_To_Follow_Request, 2)


PB_BIND(servo_Follow_Timestamp_Request, servo_Follow_Timestamp_Request, 2)


PB_BIND(servo_Limits_Reply, servo_Limits_Reply, AUTO)


PB_BIND(servo_Position_Reply, servo_Position_Reply, AUTO)


PB_BIND(servo_Settle_Time_Reply, servo_Settle_Time_Reply, AUTO)


PB_BIND(servo_Go_To_Timestamp_Reply, servo_Go_To_Timestamp_Reply, 2)


PB_BIND(servo_Follow_Timestamp_Reply, servo_Follow_Timestamp_Reply, 2)


PB_BIND(servo_Go_To_Follow_Reply, servo_Go_To_Follow_Reply, 2)


PB_BIND(servo_Request, servo_Request, 2)


PB_BIND(servo_Reply, servo_Reply, 2)





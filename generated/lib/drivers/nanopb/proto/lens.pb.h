/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_LENS_LENS_PB_H_INCLUDED
#define PB_LENS_LENS_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _lens_Get_Request {
    char dummy_field;
} lens_Get_Request;

typedef struct _lens_Get_Reply {
    uint32_t value;
} lens_Get_Reply;

typedef struct _lens_Set_Request {
    uint32_t value;
} lens_Set_Request;

typedef struct _lens_Reply {
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        lens_Get_Reply get;
    } reply;
} lens_Reply;

typedef struct _lens_Request {
    pb_size_t which_request;
    union {
        lens_Set_Request set;
        lens_Get_Request get;
    } request;
} lens_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define lens_Set_Request_init_default            {0}
#define lens_Get_Request_init_default            {0}
#define lens_Get_Reply_init_default              {0}
#define lens_Request_init_default                {0, {lens_Set_Request_init_default}}
#define lens_Reply_init_default                  {0, {error_Error_init_default}}
#define lens_Set_Request_init_zero               {0}
#define lens_Get_Request_init_zero               {0}
#define lens_Get_Reply_init_zero                 {0}
#define lens_Request_init_zero                   {0, {lens_Set_Request_init_zero}}
#define lens_Reply_init_zero                     {0, {error_Error_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define lens_Get_Reply_value_tag                 1
#define lens_Set_Request_value_tag               1
#define lens_Reply_error_tag                     1
#define lens_Reply_ack_tag                       2
#define lens_Reply_get_tag                       3
#define lens_Request_set_tag                     1
#define lens_Request_get_tag                     2

/* Struct field encoding specification for nanopb */
#define lens_Set_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   value,             1)
#define lens_Set_Request_CALLBACK NULL
#define lens_Set_Request_DEFAULT NULL

#define lens_Get_Request_FIELDLIST(X, a) \

#define lens_Get_Request_CALLBACK NULL
#define lens_Get_Request_DEFAULT NULL

#define lens_Get_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   value,             1)
#define lens_Get_Reply_CALLBACK NULL
#define lens_Get_Reply_DEFAULT NULL

#define lens_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set,request.set),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get,request.get),   2)
#define lens_Request_CALLBACK NULL
#define lens_Request_DEFAULT NULL
#define lens_Request_request_set_MSGTYPE lens_Set_Request
#define lens_Request_request_get_MSGTYPE lens_Get_Request

#define lens_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,get,reply.get),   3)
#define lens_Reply_CALLBACK NULL
#define lens_Reply_DEFAULT NULL
#define lens_Reply_reply_error_MSGTYPE error_Error
#define lens_Reply_reply_ack_MSGTYPE ack_Ack
#define lens_Reply_reply_get_MSGTYPE lens_Get_Reply

extern const pb_msgdesc_t lens_Set_Request_msg;
extern const pb_msgdesc_t lens_Get_Request_msg;
extern const pb_msgdesc_t lens_Get_Reply_msg;
extern const pb_msgdesc_t lens_Request_msg;
extern const pb_msgdesc_t lens_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define lens_Set_Request_fields &lens_Set_Request_msg
#define lens_Get_Request_fields &lens_Get_Request_msg
#define lens_Get_Reply_fields &lens_Get_Reply_msg
#define lens_Request_fields &lens_Request_msg
#define lens_Reply_fields &lens_Reply_msg

/* Maximum encoded size of messages (where known) */
#define lens_Set_Request_size                    6
#define lens_Get_Request_size                    0
#define lens_Get_Reply_size                      6
#define lens_Request_size                        8
#if defined(error_Error_size) && defined(ack_Ack_size)
typedef union lens_Reply_reply_size_union {char f1[(6 + error_Error_size)]; char f2[(6 + ack_Ack_size)]; char f0[8];} lens_Reply_reply_size_union;
#define lens_Reply_size                          (0 + sizeof(lens_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

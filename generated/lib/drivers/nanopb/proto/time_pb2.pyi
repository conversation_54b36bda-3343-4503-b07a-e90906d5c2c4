"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Timestamp(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    seconds: builtin___int = ...
    micros: builtin___int = ...

    def __init__(self,
        *,
        seconds : typing___Optional[builtin___int] = None,
        micros : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"micros",b"micros",u"seconds",b"seconds"]) -> None: ...
type___Timestamp = Timestamp

class Set_Epoch_Time_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def timestamp(self) -> type___Timestamp: ...

    def __init__(self,
        *,
        timestamp : typing___Optional[type___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> None: ...
type___Set_Epoch_Time_Request = Set_Epoch_Time_Request

class Get_Timestamp_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Timestamp_Request = Get_Timestamp_Request

class Get_Debug_Timestamp_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Debug_Timestamp_Request = Get_Debug_Timestamp_Request

class Get_Debug_Timestamp_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pps_timer_val: builtin___int = ...
    pps_ticks: builtin___int = ...
    freq_mul: builtin___float = ...
    error_ticks: builtin___int = ...
    error_ticks2: builtin___int = ...

    @property
    def timestamp(self) -> type___Timestamp: ...

    def __init__(self,
        *,
        timestamp : typing___Optional[type___Timestamp] = None,
        pps_timer_val : typing___Optional[builtin___int] = None,
        pps_ticks : typing___Optional[builtin___int] = None,
        freq_mul : typing___Optional[builtin___float] = None,
        error_ticks : typing___Optional[builtin___int] = None,
        error_ticks2 : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"error_ticks",b"error_ticks",u"error_ticks2",b"error_ticks2",u"freq_mul",b"freq_mul",u"pps_ticks",b"pps_ticks",u"pps_timer_val",b"pps_timer_val",u"timestamp",b"timestamp"]) -> None: ...
type___Get_Debug_Timestamp_Reply = Get_Debug_Timestamp_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def set(self) -> type___Set_Epoch_Time_Request: ...

    @property
    def get(self) -> type___Get_Timestamp_Request: ...

    @property
    def debug(self) -> type___Get_Debug_Timestamp_Request: ...

    def __init__(self,
        *,
        set : typing___Optional[type___Set_Epoch_Time_Request] = None,
        get : typing___Optional[type___Get_Timestamp_Request] = None,
        debug : typing___Optional[type___Get_Debug_Timestamp_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"debug",b"debug",u"get",b"get",u"request",b"request",u"set",b"set"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"debug",b"debug",u"get",b"get",u"request",b"request",u"set",b"set"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["set","get","debug"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def timestamp(self) -> type___Timestamp: ...

    @property
    def debug(self) -> type___Get_Debug_Timestamp_Reply: ...

    def __init__(self,
        *,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        timestamp : typing___Optional[type___Timestamp] = None,
        debug : typing___Optional[type___Get_Debug_Timestamp_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"debug",b"debug",u"error",b"error",u"reply",b"reply",u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"debug",b"debug",u"error",b"error",u"reply",b"reply",u"timestamp",b"timestamp"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","timestamp","debug"]: ...
type___Reply = Reply

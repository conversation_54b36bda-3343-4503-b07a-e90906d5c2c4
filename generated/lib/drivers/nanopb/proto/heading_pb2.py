# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/heading.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/heading.proto',
  package='heading',
  syntax='proto3',
  serialized_options=b'Z\016nanopb/heading',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n0generated/lib/drivers/nanopb/proto/heading.proto\x12\x07heading\"\t\n\x07Request\"s\n\x05Reply\x12\x10\n\x08have_fix\x18\x01 \x01(\x08\x12\x13\n\x0bheading_deg\x18\x02 \x01(\x01\x12\x14\n\x0c\x61\x63\x63uracy_deg\x18\x03 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x04 \x01(\x04\x12\x17\n\x0fhave_approx_fix\x18\x05 \x01(\x08\x42\x10Z\x0enanopb/headingb\x06proto3'
)




_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='heading.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=61,
  serialized_end=70,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='heading.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='have_fix', full_name='heading.Reply.have_fix', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading_deg', full_name='heading.Reply.heading_deg', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='accuracy_deg', full_name='heading.Reply.accuracy_deg', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='heading.Reply.timestamp_ms', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='have_approx_fix', full_name='heading.Reply.have_approx_fix', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=72,
  serialized_end=187,
)

DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.heading_pb2'
  # @@protoc_insertion_point(class_scope:heading.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.heading_pb2'
  # @@protoc_insertion_point(class_scope:heading.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

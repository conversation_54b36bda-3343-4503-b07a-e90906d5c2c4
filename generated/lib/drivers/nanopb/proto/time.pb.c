/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "time.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(time_Timestamp, time_Timestamp, AUTO)


PB_BIND(time_Set_Epoch_Time_Request, time_Set_Epoch_Time_Request, AUTO)


PB_BIND(time_Get_Timestamp_Request, time_Get_Timestamp_Request, AUTO)


PB_BIND(time_Get_Debug_Timestamp_Request, time_Get_Debug_Timestamp_Request, AUTO)


PB_BIND(time_Get_Debug_Timestamp_Reply, time_Get_Debug_Timestamp_Reply, AUTO)


PB_BIND(time_Request, time_Request, AUTO)


PB_BIND(time_Reply, time_Reply, 2)



#ifndef PB_CONVERT_DOUBLE_FLOAT
/* On some platforms (such as AVR), double is really float.
 * To be able to encode/decode double on these platforms, you need.
 * to define PB_CONVERT_DOUBLE_FLOAT in pb.h or compiler command line.
 */
PB_STATIC_ASSERT(sizeof(double) == 8, DOUBLE_MUST_BE_8_BYTES)
#endif


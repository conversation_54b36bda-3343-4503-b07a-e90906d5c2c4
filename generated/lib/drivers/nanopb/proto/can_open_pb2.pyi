"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class SDO_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    index: builtin___int = ...
    subindex: builtin___int = ...
    value: builtin___int = ...
    cs: builtin___int = ...
    expedited: builtin___int = ...

    def __init__(self,
        *,
        index : typing___Optional[builtin___int] = None,
        subindex : typing___Optional[builtin___int] = None,
        value : typing___Optional[builtin___int] = None,
        cs : typing___Optional[builtin___int] = None,
        expedited : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cs",b"cs",u"expedited",b"expedited",u"index",b"index",u"subindex",b"subindex",u"value",b"value"]) -> None: ...
type___SDO_Request = SDO_Request

class PDO_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    func: builtin___int = ...
    size: builtin___int = ...
    data: builtin___bytes = ...

    def __init__(self,
        *,
        func : typing___Optional[builtin___int] = None,
        size : typing___Optional[builtin___int] = None,
        data : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data",u"func",b"func",u"size",b"size"]) -> None: ...
type___PDO_Request = PDO_Request

class RTR_PDO_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    func: builtin___int = ...

    def __init__(self,
        *,
        func : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"func",b"func"]) -> None: ...
type___RTR_PDO_Request = RTR_PDO_Request

class NMT_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    state: builtin___int = ...

    def __init__(self,
        *,
        state : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"state",b"state"]) -> None: ...
type___NMT_Request = NMT_Request

class Await_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timeout_ms: builtin___int = ...
    func: builtin___int = ...

    def __init__(self,
        *,
        timeout_ms : typing___Optional[builtin___int] = None,
        func : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"func",b"func",u"timeout_ms",b"timeout_ms"]) -> None: ...
type___Await_Request = Await_Request

class SDO_Download_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    index: builtin___int = ...
    subindex: builtin___int = ...
    value: builtin___int = ...

    def __init__(self,
        *,
        index : typing___Optional[builtin___int] = None,
        subindex : typing___Optional[builtin___int] = None,
        value : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"index",b"index",u"subindex",b"subindex",u"value",b"value"]) -> None: ...
type___SDO_Download_Request = SDO_Download_Request

class SDO_Upload_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    index: builtin___int = ...
    subindex: builtin___int = ...

    def __init__(self,
        *,
        index : typing___Optional[builtin___int] = None,
        subindex : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"index",b"index",u"subindex",b"subindex"]) -> None: ...
type___SDO_Upload_Request = SDO_Upload_Request

class NMT_Reset_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___NMT_Reset_Request = NMT_Reset_Request

class NMT_Start_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___NMT_Start_Request = NMT_Start_Request

class NMT_Stop_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___NMT_Stop_Request = NMT_Stop_Request

class ACK_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ACK_Reply = ACK_Reply

class SDO_Packet(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    spec: builtin___int = ...
    index: builtin___int = ...
    subindex: builtin___int = ...
    data: builtin___bytes = ...

    def __init__(self,
        *,
        spec : typing___Optional[builtin___int] = None,
        index : typing___Optional[builtin___int] = None,
        subindex : typing___Optional[builtin___int] = None,
        data : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data",u"index",b"index",u"spec",b"spec",u"subindex",b"subindex"]) -> None: ...
type___SDO_Packet = SDO_Packet

class PDO_Packet(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    data: builtin___bytes = ...

    def __init__(self,
        *,
        data : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data"]) -> None: ...
type___PDO_Packet = PDO_Packet

class NMT_Packet(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    state: builtin___int = ...
    node_id: builtin___int = ...

    def __init__(self,
        *,
        state : typing___Optional[builtin___int] = None,
        node_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"node_id",b"node_id",u"state",b"state"]) -> None: ...
type___NMT_Packet = NMT_Packet

class Message_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    func: builtin___int = ...
    node_id: builtin___int = ...

    @property
    def sdo(self) -> type___SDO_Packet: ...

    @property
    def pdo(self) -> type___PDO_Packet: ...

    @property
    def nmt(self) -> type___NMT_Packet: ...

    def __init__(self,
        *,
        func : typing___Optional[builtin___int] = None,
        node_id : typing___Optional[builtin___int] = None,
        sdo : typing___Optional[type___SDO_Packet] = None,
        pdo : typing___Optional[type___PDO_Packet] = None,
        nmt : typing___Optional[type___NMT_Packet] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"nmt",b"nmt",u"pdo",b"pdo",u"pkt",b"pkt",u"sdo",b"sdo"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"func",b"func",u"nmt",b"nmt",u"node_id",b"node_id",u"pdo",b"pdo",u"pkt",b"pkt",u"sdo",b"sdo"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"pkt",b"pkt"]) -> typing_extensions___Literal["sdo","pdo","nmt"]: ...
type___Message_Reply = Message_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def sdo(self) -> type___SDO_Request: ...

    @property
    def pdo(self) -> type___PDO_Request: ...

    @property
    def rtr(self) -> type___RTR_PDO_Request: ...

    @property
    def nmt(self) -> type___NMT_Request: ...

    @property
    def sdo_download(self) -> type___SDO_Download_Request: ...

    @property
    def sdo_upload(self) -> type___SDO_Upload_Request: ...

    @property
    def reset(self) -> type___NMT_Reset_Request: ...

    @property
    def start(self) -> type___NMT_Start_Request: ...

    @property
    def stop(self) -> type___NMT_Stop_Request: ...

    def __init__(self,
        *,
        sdo : typing___Optional[type___SDO_Request] = None,
        pdo : typing___Optional[type___PDO_Request] = None,
        rtr : typing___Optional[type___RTR_PDO_Request] = None,
        nmt : typing___Optional[type___NMT_Request] = None,
        sdo_download : typing___Optional[type___SDO_Download_Request] = None,
        sdo_upload : typing___Optional[type___SDO_Upload_Request] = None,
        reset : typing___Optional[type___NMT_Reset_Request] = None,
        start : typing___Optional[type___NMT_Start_Request] = None,
        stop : typing___Optional[type___NMT_Stop_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"await",b"await",u"nmt",b"nmt",u"pdo",b"pdo",u"request",b"request",u"reset",b"reset",u"rtr",b"rtr",u"sdo",b"sdo",u"sdo_download",b"sdo_download",u"sdo_upload",b"sdo_upload",u"start",b"start",u"stop",b"stop"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"await",b"await",u"nmt",b"nmt",u"pdo",b"pdo",u"request",b"request",u"reset",b"reset",u"rtr",b"rtr",u"sdo",b"sdo",u"sdo_download",b"sdo_download",u"sdo_upload",b"sdo_upload",u"start",b"start",u"stop",b"stop"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["sdo","pdo","rtr","nmt","await","sdo_download","sdo_upload","reset","start","stop"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def msg(self) -> type___Message_Reply: ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    def __init__(self,
        *,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        msg : typing___Optional[type___Message_Reply] = None,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"msg",b"msg",u"reply",b"reply"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"msg",b"msg",u"reply",b"reply"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["ack","msg","error"]: ...
type___Reply = Reply

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/experiment_board.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import diagnostic_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2
from generated.lib.drivers.nanopb.proto import request_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2
from generated.lib.drivers.nanopb.proto import row_module_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_row__module__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/experiment_board.proto',
  package='experiment_board',
  syntax='proto3',
  serialized_options=b'Z\027nanopb/experiment_board',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n9generated/lib/drivers/nanopb/proto/experiment_board.proto\x12\x10\x65xperiment_board\x1a\x33generated/lib/drivers/nanopb/proto/diagnostic.proto\x1a\x30generated/lib/drivers/nanopb/proto/request.proto\x1a\x33generated/lib/drivers/nanopb/proto/row_module.proto\"\x1b\n\x0e\x45xperimentItem\x12\t\n\x01x\x18\x01 \x01(\r\"E\n\x12\x45xperimentRepeated\x12/\n\x05items\x18\x01 \x03(\x0b\x32 .experiment_board.ExperimentItem\"\x1f\n\x10\x45xperimentResult\x12\x0b\n\x03sum\x18\x01 \x01(\r\"\xc3\x01\n\x0f\x45xperimentReply\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04pong\x18\x02 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12\x34\n\x06result\x18\x03 \x01(\x0b\x32\".experiment_board.ExperimentResultH\x00\x12\'\n\nrow_module\x18\x04 \x01(\x0b\x32\x11.row_module.ReplyH\x00\x42\x07\n\x05reply\"\xca\x01\n\x11\x45xperimentRequest\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12\x35\n\x05items\x18\x03 \x01(\x0b\x32$.experiment_board.ExperimentRepeatedH\x00\x12)\n\nrow_module\x18\x04 \x01(\x0b\x32\x13.row_module.RequestH\x00\x42\t\n\x07requestB\x19Z\x17nanopb/experiment_boardb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_row__module__pb2.DESCRIPTOR,])




_EXPERIMENTITEM = _descriptor.Descriptor(
  name='ExperimentItem',
  full_name='experiment_board.ExperimentItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='experiment_board.ExperimentItem.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=235,
  serialized_end=262,
)


_EXPERIMENTREPEATED = _descriptor.Descriptor(
  name='ExperimentRepeated',
  full_name='experiment_board.ExperimentRepeated',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='items', full_name='experiment_board.ExperimentRepeated.items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=264,
  serialized_end=333,
)


_EXPERIMENTRESULT = _descriptor.Descriptor(
  name='ExperimentResult',
  full_name='experiment_board.ExperimentResult',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sum', full_name='experiment_board.ExperimentResult.sum', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=335,
  serialized_end=366,
)


_EXPERIMENTREPLY = _descriptor.Descriptor(
  name='ExperimentReply',
  full_name='experiment_board.ExperimentReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='experiment_board.ExperimentReply.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pong', full_name='experiment_board.ExperimentReply.pong', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='result', full_name='experiment_board.ExperimentReply.result', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_module', full_name='experiment_board.ExperimentReply.row_module', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='experiment_board.ExperimentReply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=369,
  serialized_end=564,
)


_EXPERIMENTREQUEST = _descriptor.Descriptor(
  name='ExperimentRequest',
  full_name='experiment_board.ExperimentRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='experiment_board.ExperimentRequest.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ping', full_name='experiment_board.ExperimentRequest.ping', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='items', full_name='experiment_board.ExperimentRequest.items', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_module', full_name='experiment_board.ExperimentRequest.row_module', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='experiment_board.ExperimentRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=567,
  serialized_end=769,
)

_EXPERIMENTREPEATED.fields_by_name['items'].message_type = _EXPERIMENTITEM
_EXPERIMENTREPLY.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_EXPERIMENTREPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_EXPERIMENTREPLY.fields_by_name['result'].message_type = _EXPERIMENTRESULT
_EXPERIMENTREPLY.fields_by_name['row_module'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_row__module__pb2._REPLY
_EXPERIMENTREPLY.oneofs_by_name['reply'].fields.append(
  _EXPERIMENTREPLY.fields_by_name['pong'])
_EXPERIMENTREPLY.fields_by_name['pong'].containing_oneof = _EXPERIMENTREPLY.oneofs_by_name['reply']
_EXPERIMENTREPLY.oneofs_by_name['reply'].fields.append(
  _EXPERIMENTREPLY.fields_by_name['result'])
_EXPERIMENTREPLY.fields_by_name['result'].containing_oneof = _EXPERIMENTREPLY.oneofs_by_name['reply']
_EXPERIMENTREPLY.oneofs_by_name['reply'].fields.append(
  _EXPERIMENTREPLY.fields_by_name['row_module'])
_EXPERIMENTREPLY.fields_by_name['row_module'].containing_oneof = _EXPERIMENTREPLY.oneofs_by_name['reply']
_EXPERIMENTREQUEST.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_EXPERIMENTREQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_EXPERIMENTREQUEST.fields_by_name['items'].message_type = _EXPERIMENTREPEATED
_EXPERIMENTREQUEST.fields_by_name['row_module'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_row__module__pb2._REQUEST
_EXPERIMENTREQUEST.oneofs_by_name['request'].fields.append(
  _EXPERIMENTREQUEST.fields_by_name['ping'])
_EXPERIMENTREQUEST.fields_by_name['ping'].containing_oneof = _EXPERIMENTREQUEST.oneofs_by_name['request']
_EXPERIMENTREQUEST.oneofs_by_name['request'].fields.append(
  _EXPERIMENTREQUEST.fields_by_name['items'])
_EXPERIMENTREQUEST.fields_by_name['items'].containing_oneof = _EXPERIMENTREQUEST.oneofs_by_name['request']
_EXPERIMENTREQUEST.oneofs_by_name['request'].fields.append(
  _EXPERIMENTREQUEST.fields_by_name['row_module'])
_EXPERIMENTREQUEST.fields_by_name['row_module'].containing_oneof = _EXPERIMENTREQUEST.oneofs_by_name['request']
DESCRIPTOR.message_types_by_name['ExperimentItem'] = _EXPERIMENTITEM
DESCRIPTOR.message_types_by_name['ExperimentRepeated'] = _EXPERIMENTREPEATED
DESCRIPTOR.message_types_by_name['ExperimentResult'] = _EXPERIMENTRESULT
DESCRIPTOR.message_types_by_name['ExperimentReply'] = _EXPERIMENTREPLY
DESCRIPTOR.message_types_by_name['ExperimentRequest'] = _EXPERIMENTREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ExperimentItem = _reflection.GeneratedProtocolMessageType('ExperimentItem', (_message.Message,), {
  'DESCRIPTOR' : _EXPERIMENTITEM,
  '__module__' : 'generated.lib.drivers.nanopb.proto.experiment_board_pb2'
  # @@protoc_insertion_point(class_scope:experiment_board.ExperimentItem)
  })
_sym_db.RegisterMessage(ExperimentItem)

ExperimentRepeated = _reflection.GeneratedProtocolMessageType('ExperimentRepeated', (_message.Message,), {
  'DESCRIPTOR' : _EXPERIMENTREPEATED,
  '__module__' : 'generated.lib.drivers.nanopb.proto.experiment_board_pb2'
  # @@protoc_insertion_point(class_scope:experiment_board.ExperimentRepeated)
  })
_sym_db.RegisterMessage(ExperimentRepeated)

ExperimentResult = _reflection.GeneratedProtocolMessageType('ExperimentResult', (_message.Message,), {
  'DESCRIPTOR' : _EXPERIMENTRESULT,
  '__module__' : 'generated.lib.drivers.nanopb.proto.experiment_board_pb2'
  # @@protoc_insertion_point(class_scope:experiment_board.ExperimentResult)
  })
_sym_db.RegisterMessage(ExperimentResult)

ExperimentReply = _reflection.GeneratedProtocolMessageType('ExperimentReply', (_message.Message,), {
  'DESCRIPTOR' : _EXPERIMENTREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.experiment_board_pb2'
  # @@protoc_insertion_point(class_scope:experiment_board.ExperimentReply)
  })
_sym_db.RegisterMessage(ExperimentReply)

ExperimentRequest = _reflection.GeneratedProtocolMessageType('ExperimentRequest', (_message.Message,), {
  'DESCRIPTOR' : _EXPERIMENTREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.experiment_board_pb2'
  # @@protoc_insertion_point(class_scope:experiment_board.ExperimentRequest)
  })
_sym_db.RegisterMessage(ExperimentRequest)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

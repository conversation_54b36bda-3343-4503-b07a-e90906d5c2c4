/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_ROTARY_ENCODER_ROTARY_ENCODER_PB_H_INCLUDED
#define PB_ROTARY_ENCODER_ROTARY_ENCODER_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/time.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _rotary_encoder_RotaryEncodersConfig_Request_Type {
    rotary_encoder_RotaryEncodersConfig_Request_Type_TICK = 0,
    rotary_encoder_RotaryEncodersConfig_Request_Type_QUAD = 1,
    rotary_encoder_RotaryEncodersConfig_Request_Type_NONE = 2
} rotary_encoder_RotaryEncodersConfig_Request_Type;

/* Struct definitions */
typedef struct _rotary_encoder_RotaryEncoderHistoryVerify_Request {
    char dummy_field;
} rotary_encoder_RotaryEncoderHistoryVerify_Request;

typedef struct _rotary_encoder_RotaryEncoder_Request {
    char dummy_field;
} rotary_encoder_RotaryEncoder_Request;

typedef struct _rotary_encoder_RotaryEncodersConfig_Reply {
    char dummy_field;
} rotary_encoder_RotaryEncodersConfig_Reply;

typedef struct _rotary_encoder_RotaryEncoderHistoryVerify_Reply {
    uint32_t num_time_warps;
    uint32_t num_ooo_elements;
    uint32_t max_usec_distance;
    uint32_t num_epoch_resets;
    uint32_t num_plus1_usec;
    uint32_t num_missed_signal;
    uint32_t num_reject_signal;
    uint32_t num_short_time;
    uint32_t num_long_time;
} rotary_encoder_RotaryEncoderHistoryVerify_Reply;

typedef struct _rotary_encoder_RotaryEncoderSnapshot_Request {
    bool has_first;
    time_Timestamp first;
    bool has_last;
    time_Timestamp last;
} rotary_encoder_RotaryEncoderSnapshot_Request;

typedef struct _rotary_encoder_RotaryEncoder_Reply {
    int64_t front_left_ticks;
    int64_t front_right_ticks;
    int64_t back_left_ticks;
    int64_t back_right_ticks;
    bool has_timestamp;
    time_Timestamp timestamp;
} rotary_encoder_RotaryEncoder_Reply;

typedef struct _rotary_encoder_RotaryEncodersConfig_Request {
    rotary_encoder_RotaryEncodersConfig_Request_Type FL_type;
    rotary_encoder_RotaryEncodersConfig_Request_Type FR_type;
    rotary_encoder_RotaryEncodersConfig_Request_Type BL_type;
    rotary_encoder_RotaryEncodersConfig_Request_Type BR_type;
} rotary_encoder_RotaryEncodersConfig_Request;

typedef struct _rotary_encoder_Request {
    pb_size_t which_request;
    union {
        rotary_encoder_RotaryEncoder_Request rotary;
        rotary_encoder_RotaryEncodersConfig_Request config;
        rotary_encoder_RotaryEncoderSnapshot_Request rotary_snapshot;
        rotary_encoder_RotaryEncoderHistoryVerify_Request history_verify;
    } request;
} rotary_encoder_Request;

typedef struct _rotary_encoder_RotaryEncoderSnapshot_Reply {
    bool has_first_before;
    rotary_encoder_RotaryEncoder_Reply first_before;
    bool has_first_after;
    rotary_encoder_RotaryEncoder_Reply first_after;
    bool has_last_before;
    rotary_encoder_RotaryEncoder_Reply last_before;
    bool has_last_after;
    rotary_encoder_RotaryEncoder_Reply last_after;
    bool has_request;
    rotary_encoder_RotaryEncoderSnapshot_Request request;
} rotary_encoder_RotaryEncoderSnapshot_Reply;

typedef struct _rotary_encoder_Reply {
    pb_size_t which_reply;
    union {
        rotary_encoder_RotaryEncoder_Reply rotary;
        rotary_encoder_RotaryEncodersConfig_Reply config;
        rotary_encoder_RotaryEncoderSnapshot_Reply rotary_snapshot;
        rotary_encoder_RotaryEncoderHistoryVerify_Reply history_verify;
    } reply;
} rotary_encoder_Reply;


/* Helper constants for enums */
#define _rotary_encoder_RotaryEncodersConfig_Request_Type_MIN rotary_encoder_RotaryEncodersConfig_Request_Type_TICK
#define _rotary_encoder_RotaryEncodersConfig_Request_Type_MAX rotary_encoder_RotaryEncodersConfig_Request_Type_NONE
#define _rotary_encoder_RotaryEncodersConfig_Request_Type_ARRAYSIZE ((rotary_encoder_RotaryEncodersConfig_Request_Type)(rotary_encoder_RotaryEncodersConfig_Request_Type_NONE+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define rotary_encoder_RotaryEncodersConfig_Request_init_default {_rotary_encoder_RotaryEncodersConfig_Request_Type_MIN, _rotary_encoder_RotaryEncodersConfig_Request_Type_MIN, _rotary_encoder_RotaryEncodersConfig_Request_Type_MIN, _rotary_encoder_RotaryEncodersConfig_Request_Type_MIN}
#define rotary_encoder_RotaryEncodersConfig_Reply_init_default {0}
#define rotary_encoder_RotaryEncoder_Request_init_default {0}
#define rotary_encoder_RotaryEncoder_Reply_init_default {0, 0, 0, 0, false, time_Timestamp_init_default}
#define rotary_encoder_RotaryEncoderSnapshot_Request_init_default {false, time_Timestamp_init_default, false, time_Timestamp_init_default}
#define rotary_encoder_RotaryEncoderSnapshot_Reply_init_default {false, rotary_encoder_RotaryEncoder_Reply_init_default, false, rotary_encoder_RotaryEncoder_Reply_init_default, false, rotary_encoder_RotaryEncoder_Reply_init_default, false, rotary_encoder_RotaryEncoder_Reply_init_default, false, rotary_encoder_RotaryEncoderSnapshot_Request_init_default}
#define rotary_encoder_RotaryEncoderHistoryVerify_Request_init_default {0}
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_init_default {0, 0, 0, 0, 0, 0, 0, 0, 0}
#define rotary_encoder_Request_init_default      {0, {rotary_encoder_RotaryEncoder_Request_init_default}}
#define rotary_encoder_Reply_init_default        {0, {rotary_encoder_RotaryEncoder_Reply_init_default}}
#define rotary_encoder_RotaryEncodersConfig_Request_init_zero {_rotary_encoder_RotaryEncodersConfig_Request_Type_MIN, _rotary_encoder_RotaryEncodersConfig_Request_Type_MIN, _rotary_encoder_RotaryEncodersConfig_Request_Type_MIN, _rotary_encoder_RotaryEncodersConfig_Request_Type_MIN}
#define rotary_encoder_RotaryEncodersConfig_Reply_init_zero {0}
#define rotary_encoder_RotaryEncoder_Request_init_zero {0}
#define rotary_encoder_RotaryEncoder_Reply_init_zero {0, 0, 0, 0, false, time_Timestamp_init_zero}
#define rotary_encoder_RotaryEncoderSnapshot_Request_init_zero {false, time_Timestamp_init_zero, false, time_Timestamp_init_zero}
#define rotary_encoder_RotaryEncoderSnapshot_Reply_init_zero {false, rotary_encoder_RotaryEncoder_Reply_init_zero, false, rotary_encoder_RotaryEncoder_Reply_init_zero, false, rotary_encoder_RotaryEncoder_Reply_init_zero, false, rotary_encoder_RotaryEncoder_Reply_init_zero, false, rotary_encoder_RotaryEncoderSnapshot_Request_init_zero}
#define rotary_encoder_RotaryEncoderHistoryVerify_Request_init_zero {0}
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_init_zero {0, 0, 0, 0, 0, 0, 0, 0, 0}
#define rotary_encoder_Request_init_zero         {0, {rotary_encoder_RotaryEncoder_Request_init_zero}}
#define rotary_encoder_Reply_init_zero           {0, {rotary_encoder_RotaryEncoder_Reply_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_num_time_warps_tag 1
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_num_ooo_elements_tag 2
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_max_usec_distance_tag 3
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_num_epoch_resets_tag 4
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_num_plus1_usec_tag 5
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_num_missed_signal_tag 6
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_num_reject_signal_tag 7
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_num_short_time_tag 8
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_num_long_time_tag 9
#define rotary_encoder_RotaryEncoderSnapshot_Request_first_tag 1
#define rotary_encoder_RotaryEncoderSnapshot_Request_last_tag 2
#define rotary_encoder_RotaryEncoder_Reply_front_left_ticks_tag 1
#define rotary_encoder_RotaryEncoder_Reply_front_right_ticks_tag 2
#define rotary_encoder_RotaryEncoder_Reply_back_left_ticks_tag 3
#define rotary_encoder_RotaryEncoder_Reply_back_right_ticks_tag 4
#define rotary_encoder_RotaryEncoder_Reply_timestamp_tag 5
#define rotary_encoder_RotaryEncodersConfig_Request_FL_type_tag 1
#define rotary_encoder_RotaryEncodersConfig_Request_FR_type_tag 2
#define rotary_encoder_RotaryEncodersConfig_Request_BL_type_tag 3
#define rotary_encoder_RotaryEncodersConfig_Request_BR_type_tag 4
#define rotary_encoder_Request_rotary_tag        1
#define rotary_encoder_Request_config_tag        2
#define rotary_encoder_Request_rotary_snapshot_tag 3
#define rotary_encoder_Request_history_verify_tag 4
#define rotary_encoder_RotaryEncoderSnapshot_Reply_first_before_tag 1
#define rotary_encoder_RotaryEncoderSnapshot_Reply_first_after_tag 2
#define rotary_encoder_RotaryEncoderSnapshot_Reply_last_before_tag 3
#define rotary_encoder_RotaryEncoderSnapshot_Reply_last_after_tag 4
#define rotary_encoder_RotaryEncoderSnapshot_Reply_request_tag 5
#define rotary_encoder_Reply_rotary_tag          1
#define rotary_encoder_Reply_config_tag          2
#define rotary_encoder_Reply_rotary_snapshot_tag 3
#define rotary_encoder_Reply_history_verify_tag  4

/* Struct field encoding specification for nanopb */
#define rotary_encoder_RotaryEncodersConfig_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    FL_type,           1) \
X(a, STATIC,   SINGULAR, UENUM,    FR_type,           2) \
X(a, STATIC,   SINGULAR, UENUM,    BL_type,           3) \
X(a, STATIC,   SINGULAR, UENUM,    BR_type,           4)
#define rotary_encoder_RotaryEncodersConfig_Request_CALLBACK NULL
#define rotary_encoder_RotaryEncodersConfig_Request_DEFAULT NULL

#define rotary_encoder_RotaryEncodersConfig_Reply_FIELDLIST(X, a) \

#define rotary_encoder_RotaryEncodersConfig_Reply_CALLBACK NULL
#define rotary_encoder_RotaryEncodersConfig_Reply_DEFAULT NULL

#define rotary_encoder_RotaryEncoder_Request_FIELDLIST(X, a) \

#define rotary_encoder_RotaryEncoder_Request_CALLBACK NULL
#define rotary_encoder_RotaryEncoder_Request_DEFAULT NULL

#define rotary_encoder_RotaryEncoder_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT64,    front_left_ticks,   1) \
X(a, STATIC,   SINGULAR, INT64,    front_right_ticks,   2) \
X(a, STATIC,   SINGULAR, INT64,    back_left_ticks,   3) \
X(a, STATIC,   SINGULAR, INT64,    back_right_ticks,   4) \
X(a, STATIC,   OPTIONAL, MESSAGE,  timestamp,         5)
#define rotary_encoder_RotaryEncoder_Reply_CALLBACK NULL
#define rotary_encoder_RotaryEncoder_Reply_DEFAULT NULL
#define rotary_encoder_RotaryEncoder_Reply_timestamp_MSGTYPE time_Timestamp

#define rotary_encoder_RotaryEncoderSnapshot_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  first,             1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  last,              2)
#define rotary_encoder_RotaryEncoderSnapshot_Request_CALLBACK NULL
#define rotary_encoder_RotaryEncoderSnapshot_Request_DEFAULT NULL
#define rotary_encoder_RotaryEncoderSnapshot_Request_first_MSGTYPE time_Timestamp
#define rotary_encoder_RotaryEncoderSnapshot_Request_last_MSGTYPE time_Timestamp

#define rotary_encoder_RotaryEncoderSnapshot_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  first_before,      1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  first_after,       2) \
X(a, STATIC,   OPTIONAL, MESSAGE,  last_before,       3) \
X(a, STATIC,   OPTIONAL, MESSAGE,  last_after,        4) \
X(a, STATIC,   OPTIONAL, MESSAGE,  request,           5)
#define rotary_encoder_RotaryEncoderSnapshot_Reply_CALLBACK NULL
#define rotary_encoder_RotaryEncoderSnapshot_Reply_DEFAULT NULL
#define rotary_encoder_RotaryEncoderSnapshot_Reply_first_before_MSGTYPE rotary_encoder_RotaryEncoder_Reply
#define rotary_encoder_RotaryEncoderSnapshot_Reply_first_after_MSGTYPE rotary_encoder_RotaryEncoder_Reply
#define rotary_encoder_RotaryEncoderSnapshot_Reply_last_before_MSGTYPE rotary_encoder_RotaryEncoder_Reply
#define rotary_encoder_RotaryEncoderSnapshot_Reply_last_after_MSGTYPE rotary_encoder_RotaryEncoder_Reply
#define rotary_encoder_RotaryEncoderSnapshot_Reply_request_MSGTYPE rotary_encoder_RotaryEncoderSnapshot_Request

#define rotary_encoder_RotaryEncoderHistoryVerify_Request_FIELDLIST(X, a) \

#define rotary_encoder_RotaryEncoderHistoryVerify_Request_CALLBACK NULL
#define rotary_encoder_RotaryEncoderHistoryVerify_Request_DEFAULT NULL

#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   num_time_warps,    1) \
X(a, STATIC,   SINGULAR, UINT32,   num_ooo_elements,   2) \
X(a, STATIC,   SINGULAR, UINT32,   max_usec_distance,   3) \
X(a, STATIC,   SINGULAR, UINT32,   num_epoch_resets,   4) \
X(a, STATIC,   SINGULAR, UINT32,   num_plus1_usec,    5) \
X(a, STATIC,   SINGULAR, UINT32,   num_missed_signal,   6) \
X(a, STATIC,   SINGULAR, UINT32,   num_reject_signal,   7) \
X(a, STATIC,   SINGULAR, UINT32,   num_short_time,    8) \
X(a, STATIC,   SINGULAR, UINT32,   num_long_time,     9)
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_CALLBACK NULL
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_DEFAULT NULL

#define rotary_encoder_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,rotary,request.rotary),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,config,request.config),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,rotary_snapshot,request.rotary_snapshot),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,history_verify,request.history_verify),   4)
#define rotary_encoder_Request_CALLBACK NULL
#define rotary_encoder_Request_DEFAULT NULL
#define rotary_encoder_Request_request_rotary_MSGTYPE rotary_encoder_RotaryEncoder_Request
#define rotary_encoder_Request_request_config_MSGTYPE rotary_encoder_RotaryEncodersConfig_Request
#define rotary_encoder_Request_request_rotary_snapshot_MSGTYPE rotary_encoder_RotaryEncoderSnapshot_Request
#define rotary_encoder_Request_request_history_verify_MSGTYPE rotary_encoder_RotaryEncoderHistoryVerify_Request

#define rotary_encoder_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,rotary,reply.rotary),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,config,reply.config),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,rotary_snapshot,reply.rotary_snapshot),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,history_verify,reply.history_verify),   4)
#define rotary_encoder_Reply_CALLBACK NULL
#define rotary_encoder_Reply_DEFAULT NULL
#define rotary_encoder_Reply_reply_rotary_MSGTYPE rotary_encoder_RotaryEncoder_Reply
#define rotary_encoder_Reply_reply_config_MSGTYPE rotary_encoder_RotaryEncodersConfig_Reply
#define rotary_encoder_Reply_reply_rotary_snapshot_MSGTYPE rotary_encoder_RotaryEncoderSnapshot_Reply
#define rotary_encoder_Reply_reply_history_verify_MSGTYPE rotary_encoder_RotaryEncoderHistoryVerify_Reply

extern const pb_msgdesc_t rotary_encoder_RotaryEncodersConfig_Request_msg;
extern const pb_msgdesc_t rotary_encoder_RotaryEncodersConfig_Reply_msg;
extern const pb_msgdesc_t rotary_encoder_RotaryEncoder_Request_msg;
extern const pb_msgdesc_t rotary_encoder_RotaryEncoder_Reply_msg;
extern const pb_msgdesc_t rotary_encoder_RotaryEncoderSnapshot_Request_msg;
extern const pb_msgdesc_t rotary_encoder_RotaryEncoderSnapshot_Reply_msg;
extern const pb_msgdesc_t rotary_encoder_RotaryEncoderHistoryVerify_Request_msg;
extern const pb_msgdesc_t rotary_encoder_RotaryEncoderHistoryVerify_Reply_msg;
extern const pb_msgdesc_t rotary_encoder_Request_msg;
extern const pb_msgdesc_t rotary_encoder_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define rotary_encoder_RotaryEncodersConfig_Request_fields &rotary_encoder_RotaryEncodersConfig_Request_msg
#define rotary_encoder_RotaryEncodersConfig_Reply_fields &rotary_encoder_RotaryEncodersConfig_Reply_msg
#define rotary_encoder_RotaryEncoder_Request_fields &rotary_encoder_RotaryEncoder_Request_msg
#define rotary_encoder_RotaryEncoder_Reply_fields &rotary_encoder_RotaryEncoder_Reply_msg
#define rotary_encoder_RotaryEncoderSnapshot_Request_fields &rotary_encoder_RotaryEncoderSnapshot_Request_msg
#define rotary_encoder_RotaryEncoderSnapshot_Reply_fields &rotary_encoder_RotaryEncoderSnapshot_Reply_msg
#define rotary_encoder_RotaryEncoderHistoryVerify_Request_fields &rotary_encoder_RotaryEncoderHistoryVerify_Request_msg
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_fields &rotary_encoder_RotaryEncoderHistoryVerify_Reply_msg
#define rotary_encoder_Request_fields &rotary_encoder_Request_msg
#define rotary_encoder_Reply_fields &rotary_encoder_Reply_msg

/* Maximum encoded size of messages (where known) */
#define rotary_encoder_RotaryEncodersConfig_Request_size 8
#define rotary_encoder_RotaryEncodersConfig_Reply_size 0
#define rotary_encoder_RotaryEncoder_Request_size 0
#if defined(time_Timestamp_size)
#define rotary_encoder_RotaryEncoder_Reply_size  (50 + time_Timestamp_size)
#endif
#if defined(time_Timestamp_size) && defined(time_Timestamp_size)
#define rotary_encoder_RotaryEncoderSnapshot_Request_size (12 + time_Timestamp_size + time_Timestamp_size)
#endif
#if defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size)
#define rotary_encoder_RotaryEncoderSnapshot_Reply_size (242 + time_Timestamp_size + time_Timestamp_size + time_Timestamp_size + time_Timestamp_size + time_Timestamp_size + time_Timestamp_size)
#endif
#define rotary_encoder_RotaryEncoderHistoryVerify_Request_size 0
#define rotary_encoder_RotaryEncoderHistoryVerify_Reply_size 54
#if defined(time_Timestamp_size) && defined(time_Timestamp_size)
typedef union rotary_encoder_Request_request_size_union {char f3[(18 + time_Timestamp_size + time_Timestamp_size)]; char f0[10];} rotary_encoder_Request_request_size_union;
#define rotary_encoder_Request_size              (0 + sizeof(rotary_encoder_Request_request_size_union))
#endif
#if defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size) && defined(time_Timestamp_size)
typedef union rotary_encoder_Reply_reply_size_union {char f1[(56 + time_Timestamp_size)]; char f3[(248 + time_Timestamp_size + time_Timestamp_size + time_Timestamp_size + time_Timestamp_size + time_Timestamp_size + time_Timestamp_size)]; char f0[56];} rotary_encoder_Reply_reply_size_union;
#define rotary_encoder_Reply_size                (0 + sizeof(rotary_encoder_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

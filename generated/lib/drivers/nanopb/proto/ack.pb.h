/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_ACK_ACK_PB_H_INCLUDED
#define PB_ACK_ACK_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _ack_Ack {
    char dummy_field;
} ack_Ack;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define ack_Ack_init_default                     {0}
#define ack_Ack_init_zero                        {0}

/* Field tags (for use in manual encoding/decoding) */

/* Struct field encoding specification for nanopb */
#define ack_Ack_FIELDLIST(X, a) \

#define ack_Ack_CALLBACK NULL
#define ack_Ack_DEFAULT NULL

extern const pb_msgdesc_t ack_Ack_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define ack_Ack_fields &ack_Ack_msg

/* Maximum encoded size of messages (where known) */
#define ack_Ack_size                             0

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

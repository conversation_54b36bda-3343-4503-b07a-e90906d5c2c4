"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.diagnostic_pb2 import (
    Ping as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping,
    Pong as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong,
)

from generated.lib.drivers.nanopb.proto.request_pb2 import (
    RequestHeader as generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader,
)

from generated.lib.drivers.nanopb.proto.row_module_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___row_module_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___row_module_pb2___Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class ExperimentItem(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___ExperimentItem = ExperimentItem

class ExperimentRepeated(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def items(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ExperimentItem]: ...

    def __init__(self,
        *,
        items : typing___Optional[typing___Iterable[type___ExperimentItem]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"items",b"items"]) -> None: ...
type___ExperimentRepeated = ExperimentRepeated

class ExperimentResult(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    sum: builtin___int = ...

    def __init__(self,
        *,
        sum : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"sum",b"sum"]) -> None: ...
type___ExperimentResult = ExperimentResult

class ExperimentReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def result(self) -> type___ExperimentResult: ...

    @property
    def row_module(self) -> generated___lib___drivers___nanopb___proto___row_module_pb2___Reply: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        result : typing___Optional[type___ExperimentResult] = None,
        row_module : typing___Optional[generated___lib___drivers___nanopb___proto___row_module_pb2___Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"header",b"header",u"pong",b"pong",u"reply",b"reply",u"result",b"result",u"row_module",b"row_module"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"header",b"header",u"pong",b"pong",u"reply",b"reply",u"result",b"result",u"row_module",b"row_module"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["pong","result","row_module"]: ...
type___ExperimentReply = ExperimentReply

class ExperimentRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def items(self) -> type___ExperimentRepeated: ...

    @property
    def row_module(self) -> generated___lib___drivers___nanopb___proto___row_module_pb2___Request: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        items : typing___Optional[type___ExperimentRepeated] = None,
        row_module : typing___Optional[generated___lib___drivers___nanopb___proto___row_module_pb2___Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"header",b"header",u"items",b"items",u"ping",b"ping",u"request",b"request",u"row_module",b"row_module"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"header",b"header",u"items",b"items",u"ping",b"ping",u"request",b"request",u"row_module",b"row_module"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","items","row_module"]: ...
type___ExperimentRequest = ExperimentRequest

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/hasselhoff_board.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import diagnostic_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2
from generated.lib.drivers.nanopb.proto import request_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2
from generated.lib.drivers.nanopb.proto import version_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2
from generated.lib.drivers.nanopb.proto import time_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2
from generated.lib.drivers.nanopb.proto import carbon_tractor_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_carbon__tractor__pb2
from generated.lib.drivers.nanopb.proto import ots_tractor_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ots__tractor__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/hasselhoff_board.proto',
  package='hasselhoff_board',
  syntax='proto3',
  serialized_options=b'Z\027nanopb/hasselhoff_board',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n9generated/lib/drivers/nanopb/proto/hasselhoff_board.proto\x12\x10hasselhoff_board\x1a\x33generated/lib/drivers/nanopb/proto/diagnostic.proto\x1a\x30generated/lib/drivers/nanopb/proto/request.proto\x1a\x30generated/lib/drivers/nanopb/proto/version.proto\x1a-generated/lib/drivers/nanopb/proto/time.proto\x1a\x37generated/lib/drivers/nanopb/proto/carbon_tractor.proto\x1a\x34generated/lib/drivers/nanopb/proto/ots_tractor.proto\"\xfe\x01\n\x05Reply\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04pong\x18\x02 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12\x1b\n\x04time\x18\x03 \x01(\x0b\x32\x0b.time.ReplyH\x00\x12)\n\x07version\x18\x04 \x01(\x0b\x32\x16.version.Version_ReplyH\x00\x12)\n\x0bots_tractor\x18\x05 \x01(\x0b\x32\x12.ots_tractor.ReplyH\x00\x12/\n\x0e\x63\x61rbon_tractor\x18\x06 \x01(\x0b\x32\x15.carbon_tractor.ReplyH\x00\x42\x07\n\x05reply\"\xb3\x02\n\x07Request\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12\x1d\n\x04time\x18\x03 \x01(\x0b\x32\r.time.RequestH\x00\x12+\n\x07version\x18\x04 \x01(\x0b\x32\x18.version.Version_RequestH\x00\x12\'\n\x05reset\x18\x05 \x01(\x0b\x32\x16.version.Reset_RequestH\x00\x12+\n\x0bots_tractor\x18\x06 \x01(\x0b\x32\x14.ots_tractor.RequestH\x00\x12\x31\n\x0e\x63\x61rbon_tractor\x18\x07 \x01(\x0b\x32\x17.carbon_tractor.RequestH\x00\x42\t\n\x07requestB\x19Z\x17nanopb/hasselhoff_boardb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_carbon__tractor__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ots__tractor__pb2.DESCRIPTOR,])




_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='hasselhoff_board.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='hasselhoff_board.Reply.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pong', full_name='hasselhoff_board.Reply.pong', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time', full_name='hasselhoff_board.Reply.time', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='hasselhoff_board.Reply.version', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ots_tractor', full_name='hasselhoff_board.Reply.ots_tractor', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='carbon_tractor', full_name='hasselhoff_board.Reply.carbon_tractor', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='hasselhoff_board.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=391,
  serialized_end=645,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='hasselhoff_board.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='hasselhoff_board.Request.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ping', full_name='hasselhoff_board.Request.ping', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time', full_name='hasselhoff_board.Request.time', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='hasselhoff_board.Request.version', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reset', full_name='hasselhoff_board.Request.reset', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ots_tractor', full_name='hasselhoff_board.Request.ots_tractor', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='carbon_tractor', full_name='hasselhoff_board.Request.carbon_tractor', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='hasselhoff_board.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=648,
  serialized_end=955,
)

_REPLY.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_REPLY.fields_by_name['time'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._REPLY
_REPLY.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REPLY
_REPLY.fields_by_name['ots_tractor'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ots__tractor__pb2._REPLY
_REPLY.fields_by_name['carbon_tractor'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_carbon__tractor__pb2._REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pong'])
_REPLY.fields_by_name['pong'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['time'])
_REPLY.fields_by_name['time'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['version'])
_REPLY.fields_by_name['version'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ots_tractor'])
_REPLY.fields_by_name['ots_tractor'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['carbon_tractor'])
_REPLY.fields_by_name['carbon_tractor'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REQUEST.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_REQUEST.fields_by_name['time'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._REQUEST
_REQUEST.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REQUEST
_REQUEST.fields_by_name['reset'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._RESET_REQUEST
_REQUEST.fields_by_name['ots_tractor'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ots__tractor__pb2._REQUEST
_REQUEST.fields_by_name['carbon_tractor'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_carbon__tractor__pb2._REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ping'])
_REQUEST.fields_by_name['ping'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['time'])
_REQUEST.fields_by_name['time'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['version'])
_REQUEST.fields_by_name['version'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['reset'])
_REQUEST.fields_by_name['reset'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ots_tractor'])
_REQUEST.fields_by_name['ots_tractor'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['carbon_tractor'])
_REQUEST.fields_by_name['carbon_tractor'].containing_oneof = _REQUEST.oneofs_by_name['request']
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.hasselhoff_board_pb2'
  # @@protoc_insertion_point(class_scope:hasselhoff_board.Reply)
  })
_sym_db.RegisterMessage(Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.hasselhoff_board_pb2'
  # @@protoc_insertion_point(class_scope:hasselhoff_board.Request)
  })
_sym_db.RegisterMessage(Request)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

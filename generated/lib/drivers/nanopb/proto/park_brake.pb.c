/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "park_brake.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(park_brake_Request, park_brake_Request, AUTO)


PB_BIND(park_brake_Reply, park_brake_Reply, AUTO)


PB_BIND(park_brake_Query_Request, park_brake_Query_Request, AUTO)


PB_BIND(park_brake_Query_Reply, park_brake_Query_Reply, AUTO)




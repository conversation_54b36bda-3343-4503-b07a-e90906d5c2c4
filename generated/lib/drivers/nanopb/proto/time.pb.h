/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_TIME_TIME_PB_H_INCLUDED
#define PB_TIME_TIME_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"
#include "generated/lib/drivers/nanopb/proto/error.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _time_Get_Debug_Timestamp_Request {
    char dummy_field;
} time_Get_Debug_Timestamp_Request;

typedef struct _time_Get_Timestamp_Request {
    char dummy_field;
} time_Get_Timestamp_Request;

typedef struct _time_Timestamp {
    uint32_t seconds;
    uint32_t micros;
} time_Timestamp;

typedef struct _time_Get_Debug_Timestamp_Reply {
    bool has_timestamp;
    time_Timestamp timestamp;
    uint32_t pps_timer_val;
    uint32_t pps_ticks;
    double freq_mul;
    int32_t error_ticks;
    int32_t error_ticks2;
} time_Get_Debug_Timestamp_Reply;

typedef struct _time_Set_Epoch_Time_Request {
    bool has_timestamp;
    time_Timestamp timestamp;
} time_Set_Epoch_Time_Request;

typedef struct _time_Reply {
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        time_Timestamp timestamp;
        time_Get_Debug_Timestamp_Reply debug;
    } reply;
} time_Reply;

typedef struct _time_Request {
    pb_size_t which_request;
    union {
        time_Set_Epoch_Time_Request set;
        time_Get_Timestamp_Request get;
        time_Get_Debug_Timestamp_Request debug;
    } request;
} time_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define time_Timestamp_init_default              {0, 0}
#define time_Set_Epoch_Time_Request_init_default {false, time_Timestamp_init_default}
#define time_Get_Timestamp_Request_init_default  {0}
#define time_Get_Debug_Timestamp_Request_init_default {0}
#define time_Get_Debug_Timestamp_Reply_init_default {false, time_Timestamp_init_default, 0, 0, 0, 0, 0}
#define time_Request_init_default                {0, {time_Set_Epoch_Time_Request_init_default}}
#define time_Reply_init_default                  {0, {error_Error_init_default}}
#define time_Timestamp_init_zero                 {0, 0}
#define time_Set_Epoch_Time_Request_init_zero    {false, time_Timestamp_init_zero}
#define time_Get_Timestamp_Request_init_zero     {0}
#define time_Get_Debug_Timestamp_Request_init_zero {0}
#define time_Get_Debug_Timestamp_Reply_init_zero {false, time_Timestamp_init_zero, 0, 0, 0, 0, 0}
#define time_Request_init_zero                   {0, {time_Set_Epoch_Time_Request_init_zero}}
#define time_Reply_init_zero                     {0, {error_Error_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define time_Timestamp_seconds_tag               1
#define time_Timestamp_micros_tag                2
#define time_Get_Debug_Timestamp_Reply_timestamp_tag 1
#define time_Get_Debug_Timestamp_Reply_pps_timer_val_tag 2
#define time_Get_Debug_Timestamp_Reply_pps_ticks_tag 3
#define time_Get_Debug_Timestamp_Reply_freq_mul_tag 4
#define time_Get_Debug_Timestamp_Reply_error_ticks_tag 5
#define time_Get_Debug_Timestamp_Reply_error_ticks2_tag 6
#define time_Set_Epoch_Time_Request_timestamp_tag 1
#define time_Reply_error_tag                     1
#define time_Reply_ack_tag                       2
#define time_Reply_timestamp_tag                 3
#define time_Reply_debug_tag                     4
#define time_Request_set_tag                     1
#define time_Request_get_tag                     2
#define time_Request_debug_tag                   3

/* Struct field encoding specification for nanopb */
#define time_Timestamp_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   seconds,           1) \
X(a, STATIC,   SINGULAR, UINT32,   micros,            2)
#define time_Timestamp_CALLBACK NULL
#define time_Timestamp_DEFAULT NULL

#define time_Set_Epoch_Time_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  timestamp,         1)
#define time_Set_Epoch_Time_Request_CALLBACK NULL
#define time_Set_Epoch_Time_Request_DEFAULT NULL
#define time_Set_Epoch_Time_Request_timestamp_MSGTYPE time_Timestamp

#define time_Get_Timestamp_Request_FIELDLIST(X, a) \

#define time_Get_Timestamp_Request_CALLBACK NULL
#define time_Get_Timestamp_Request_DEFAULT NULL

#define time_Get_Debug_Timestamp_Request_FIELDLIST(X, a) \

#define time_Get_Debug_Timestamp_Request_CALLBACK NULL
#define time_Get_Debug_Timestamp_Request_DEFAULT NULL

#define time_Get_Debug_Timestamp_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  timestamp,         1) \
X(a, STATIC,   SINGULAR, UINT32,   pps_timer_val,     2) \
X(a, STATIC,   SINGULAR, UINT32,   pps_ticks,         3) \
X(a, STATIC,   SINGULAR, DOUBLE,   freq_mul,          4) \
X(a, STATIC,   SINGULAR, INT32,    error_ticks,       5) \
X(a, STATIC,   SINGULAR, INT32,    error_ticks2,      6)
#define time_Get_Debug_Timestamp_Reply_CALLBACK NULL
#define time_Get_Debug_Timestamp_Reply_DEFAULT NULL
#define time_Get_Debug_Timestamp_Reply_timestamp_MSGTYPE time_Timestamp

#define time_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set,request.set),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get,request.get),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,debug,request.debug),   3)
#define time_Request_CALLBACK NULL
#define time_Request_DEFAULT NULL
#define time_Request_request_set_MSGTYPE time_Set_Epoch_Time_Request
#define time_Request_request_get_MSGTYPE time_Get_Timestamp_Request
#define time_Request_request_debug_MSGTYPE time_Get_Debug_Timestamp_Request

#define time_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,timestamp,reply.timestamp),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,debug,reply.debug),   4)
#define time_Reply_CALLBACK NULL
#define time_Reply_DEFAULT NULL
#define time_Reply_reply_error_MSGTYPE error_Error
#define time_Reply_reply_ack_MSGTYPE ack_Ack
#define time_Reply_reply_timestamp_MSGTYPE time_Timestamp
#define time_Reply_reply_debug_MSGTYPE time_Get_Debug_Timestamp_Reply

extern const pb_msgdesc_t time_Timestamp_msg;
extern const pb_msgdesc_t time_Set_Epoch_Time_Request_msg;
extern const pb_msgdesc_t time_Get_Timestamp_Request_msg;
extern const pb_msgdesc_t time_Get_Debug_Timestamp_Request_msg;
extern const pb_msgdesc_t time_Get_Debug_Timestamp_Reply_msg;
extern const pb_msgdesc_t time_Request_msg;
extern const pb_msgdesc_t time_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define time_Timestamp_fields &time_Timestamp_msg
#define time_Set_Epoch_Time_Request_fields &time_Set_Epoch_Time_Request_msg
#define time_Get_Timestamp_Request_fields &time_Get_Timestamp_Request_msg
#define time_Get_Debug_Timestamp_Request_fields &time_Get_Debug_Timestamp_Request_msg
#define time_Get_Debug_Timestamp_Reply_fields &time_Get_Debug_Timestamp_Reply_msg
#define time_Request_fields &time_Request_msg
#define time_Reply_fields &time_Reply_msg

/* Maximum encoded size of messages (where known) */
#define time_Timestamp_size                      12
#define time_Set_Epoch_Time_Request_size         14
#define time_Get_Timestamp_Request_size          0
#define time_Get_Debug_Timestamp_Request_size    0
#define time_Get_Debug_Timestamp_Reply_size      57
#define time_Request_size                        16
#if defined(error_Error_size) && defined(ack_Ack_size)
typedef union time_Reply_reply_size_union {char f1[(6 + error_Error_size)]; char f2[(6 + ack_Ack_size)]; char f0[59];} time_Reply_reply_size_union;
#define time_Reply_size                          (0 + sizeof(time_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

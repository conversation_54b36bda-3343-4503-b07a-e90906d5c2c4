"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Drive_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    DirectionValue = typing___NewType('DirectionValue', builtin___int)
    type___DirectionValue = DirectionValue
    Direction: _Direction
    class _Direction(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[Drive_Request.DirectionValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        forward = typing___cast(Drive_Request.DirectionValue, 0)
        backward = typing___cast(Drive_Request.DirectionValue, 1)
        stop = typing___cast(Drive_Request.DirectionValue, 3)
    forward = typing___cast(Drive_Request.DirectionValue, 0)
    backward = typing___cast(Drive_Request.DirectionValue, 1)
    stop = typing___cast(Drive_Request.DirectionValue, 3)

    dir: type___Drive_Request.DirectionValue = ...
    duty_cycle: builtin___float = ...

    def __init__(self,
        *,
        dir : typing___Optional[type___Drive_Request.DirectionValue] = None,
        duty_cycle : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"dir",b"dir",u"duty_cycle",b"duty_cycle"]) -> None: ...
type___Drive_Request = Drive_Request

class Drive_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Drive_Reply = Drive_Reply

class Turn_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    DirectionValue = typing___NewType('DirectionValue', builtin___int)
    type___DirectionValue = DirectionValue
    Direction: _Direction
    class _Direction(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[Turn_Request.DirectionValue]):
        DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
        left = typing___cast(Turn_Request.DirectionValue, 0)
        right = typing___cast(Turn_Request.DirectionValue, 1)
        straight = typing___cast(Turn_Request.DirectionValue, 2)
    left = typing___cast(Turn_Request.DirectionValue, 0)
    right = typing___cast(Turn_Request.DirectionValue, 1)
    straight = typing___cast(Turn_Request.DirectionValue, 2)

    dir: type___Turn_Request.DirectionValue = ...
    duty_cycle: builtin___float = ...

    def __init__(self,
        *,
        dir : typing___Optional[type___Turn_Request.DirectionValue] = None,
        duty_cycle : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"dir",b"dir",u"duty_cycle",b"duty_cycle"]) -> None: ...
type___Turn_Request = Turn_Request

class Turn_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Turn_Reply = Turn_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def drive(self) -> type___Drive_Request: ...

    @property
    def turn(self) -> type___Turn_Request: ...

    def __init__(self,
        *,
        drive : typing___Optional[type___Drive_Request] = None,
        turn : typing___Optional[type___Turn_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"drive",b"drive",u"request",b"request",u"turn",b"turn"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"drive",b"drive",u"request",b"request",u"turn",b"turn"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["drive","turn"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def drive(self) -> type___Drive_Reply: ...

    @property
    def turn(self) -> type___Turn_Reply: ...

    def __init__(self,
        *,
        drive : typing___Optional[type___Drive_Reply] = None,
        turn : typing___Optional[type___Turn_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"drive",b"drive",u"reply",b"reply",u"turn",b"turn"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"drive",b"drive",u"reply",b"reply",u"turn",b"turn"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["drive","turn"]: ...
type___Reply = Reply

/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_LASER_LASER_PB_H_INCLUDED
#define PB_LASER_LASER_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _laser_LaserType {
    laser_LaserType_LASERTYPE_UNKNOWN = 0,
    laser_LaserType_LASERTYPE_CO2 = 1,
    laser_LaserType_LASERTYPE_DIODE_BWT = 2,
    laser_LaserType_LASERTYPE_DIODE_JLIGHT = 3
} laser_LaserType;

typedef enum _laser_Jlight_Fault {
    laser_Jlight_Fault_JLIGHT_FAULT_UNKNOWN = 0,
    laser_Jlight_Fault_JLIGHT_FAULT_INTERLOCK = 1,
    laser_Jlight_Fault_JLIGHT_FAULT_SYSTEM = 2,
    laser_Jlight_Fault_JLIGHT_FAULT_CH1_OVERCURRENT = 3,
    laser_Jlight_Fault_JLIGHT_FAULT_CH2_OVERCURRENT = 4,
    laser_Jlight_Fault_JLIGHT_FAULT_CH1_OVERVOLTAGE = 5,
    laser_Jlight_Fault_JLIGHT_FAULT_CH2_OVERVOLTAGE = 6,
    laser_Jlight_Fault_JLIGHT_FAULT_CH1_OVERTEMP = 7,
    laser_Jlight_Fault_JLIGHT_FAULT_CH2_OVERTEMP = 8,
    laser_Jlight_Fault_JLIGHT_FAULT_DRIVER_OVERTEMP = 9
} laser_Jlight_Fault;

/* Struct definitions */
typedef struct _laser_Bwt_Transport_Get_Config_Request {
    char dummy_field;
} laser_Bwt_Transport_Get_Config_Request;

typedef struct _laser_Diode_Status_Request {
    char dummy_field;
} laser_Diode_Status_Request;

typedef struct _laser_Get_Laser_Request {
    char dummy_field;
} laser_Get_Laser_Request;

typedef struct _laser_Laser_Inventory_Request {
    char dummy_field;
} laser_Laser_Inventory_Request;

typedef struct _laser_Raw_Data_Request {
    char dummy_field;
} laser_Raw_Data_Request;

typedef struct _laser_Bwt_Passthrough_Reply {
    char response[64];
} laser_Bwt_Passthrough_Reply;

typedef struct _laser_Bwt_Passthrough_Request {
    char command[64];
} laser_Bwt_Passthrough_Request;

typedef struct _laser_Bwt_Status {
    pb_size_t faults_count;
    int32_t faults[5];
} laser_Bwt_Status;

typedef struct _laser_Bwt_Transport_Config {
    bool log_messages;
    bool intercommand_delay;
} laser_Bwt_Transport_Config;

typedef struct _laser_Diode_Set_Current_Request {
    pb_size_t current_count;
    uint32_t current[4];
    bool commit;
} laser_Diode_Set_Current_Request;

typedef struct _laser_Intensity_Request {
    int32_t intensity;
} laser_Intensity_Request;

typedef struct _laser_Jlight_Status {
    float psuTemp;
    float psuInputVolts;
    pb_size_t psuOutputVolts_count;
    float psuOutputVolts[2];
    pb_size_t faults_count;
    laser_Jlight_Fault faults[10];
} laser_Jlight_Status;

typedef struct _laser_Laser_Inventory_Reply {
    char model[32];
    char serial[32];
    uint32_t power;
    laser_LaserType type;
} laser_Laser_Inventory_Reply;

typedef struct _laser_Laser_Reply {
    int32_t raw_therm1_reading_mv;
    int32_t raw_therm2_reading_mv;
    bool on;
    bool lpsu_state;
    bool fireable;
} laser_Laser_Reply;

typedef struct _laser_Laser_Request {
    bool on;
} laser_Laser_Request;

typedef struct _laser_Laser_Reset_Request {
    bool transport;
    bool faults;
} laser_Laser_Reset_Request;

typedef struct _laser_Laser_Set_Type_Request {
    laser_LaserType type;
} laser_Laser_Set_Type_Request;

typedef struct _laser_Laser_State_Reply {
    bool on;
} laser_Laser_State_Reply;

typedef struct _laser_Laser_Status_Reply {
    bool lpsu_state;
    float lpsu_current;
    float power;
    bool arc_detected;
} laser_Laser_Status_Reply;

typedef struct _laser_Raw_Data_Reply {
    pb_size_t which__therm1_raw;
    union {
        int32_t therm1_raw;
    } _therm1_raw;
    pb_size_t which__therm2_raw;
    union {
        int32_t therm2_raw;
    } _therm2_raw;
    pb_size_t which__photodiode_raw;
    union {
        int32_t photodiode_raw;
    } _photodiode_raw;
} laser_Raw_Data_Reply;

typedef struct _laser_Diode_Status_Reply {
    uint64_t timestamp;
    pb_size_t temp_count;
    float temp[4];
    pb_size_t humidity_count;
    float humidity[4];
    pb_size_t current_count;
    float current[18];
    bool faulted;
    pb_size_t thermistors_count;
    float thermistors[2];
    pb_size_t which_extra;
    union {
        laser_Bwt_Status extra_bwt;
        laser_Jlight_Status extra_jlight;
    } extra;
} laser_Diode_Status_Reply;

typedef struct _laser_Request {
    pb_size_t which_request;
    union {
        laser_Laser_Request laser;
        laser_Get_Laser_Request get_laser;
        laser_Intensity_Request intensity;
        laser_Raw_Data_Request raw_data;
        laser_Diode_Status_Request diode_status;
        laser_Laser_Inventory_Request laser_inventory;
        laser_Bwt_Passthrough_Request bwt_passthrough;
        laser_Bwt_Transport_Config bwt_set_config;
        laser_Bwt_Transport_Get_Config_Request bwt_get_config;
        laser_Laser_Reset_Request laser_reset;
        laser_Diode_Set_Current_Request diode_set_current;
        laser_Laser_Set_Type_Request set_type;
    } request;
} laser_Request;

typedef struct _laser_Reply {
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        laser_Laser_State_Reply laser;
        laser_Laser_Reply laser_reply;
        laser_Raw_Data_Reply raw_data;
        laser_Diode_Status_Reply diode_status;
        laser_Laser_Inventory_Reply laser_inventory;
        laser_Bwt_Passthrough_Reply bwt_passthrough;
        laser_Bwt_Transport_Config bwt_config;
    } reply;
} laser_Reply;


/* Helper constants for enums */
#define _laser_LaserType_MIN laser_LaserType_LASERTYPE_UNKNOWN
#define _laser_LaserType_MAX laser_LaserType_LASERTYPE_DIODE_JLIGHT
#define _laser_LaserType_ARRAYSIZE ((laser_LaserType)(laser_LaserType_LASERTYPE_DIODE_JLIGHT+1))

#define _laser_Jlight_Fault_MIN laser_Jlight_Fault_JLIGHT_FAULT_UNKNOWN
#define _laser_Jlight_Fault_MAX laser_Jlight_Fault_JLIGHT_FAULT_DRIVER_OVERTEMP
#define _laser_Jlight_Fault_ARRAYSIZE ((laser_Jlight_Fault)(laser_Jlight_Fault_JLIGHT_FAULT_DRIVER_OVERTEMP+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define laser_Raw_Data_Request_init_default      {0}
#define laser_Raw_Data_Reply_init_default        {0, {0}, 0, {0}, 0, {0}}
#define laser_Laser_Inventory_Request_init_default {0}
#define laser_Laser_Inventory_Reply_init_default {"", "", 0, _laser_LaserType_MIN}
#define laser_Laser_Set_Type_Request_init_default {_laser_LaserType_MIN}
#define laser_Laser_Reset_Request_init_default   {0, 0}
#define laser_Jlight_Status_init_default         {0, 0, 0, {0, 0}, 0, {_laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN}}
#define laser_Bwt_Status_init_default            {0, {0, 0, 0, 0, 0}}
#define laser_Bwt_Passthrough_Request_init_default {""}
#define laser_Bwt_Passthrough_Reply_init_default {""}
#define laser_Bwt_Transport_Get_Config_Request_init_default {0}
#define laser_Bwt_Transport_Config_init_default  {0, 0}
#define laser_Diode_Status_Request_init_default  {0}
#define laser_Diode_Status_Reply_init_default    {0, 0, {0, 0, 0, 0}, 0, {0, 0, 0, 0}, 0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, 0, 0, {0, 0}, 0, {laser_Bwt_Status_init_default}}
#define laser_Diode_Set_Current_Request_init_default {0, {0, 0, 0, 0}, 0}
#define laser_Laser_Request_init_default         {0}
#define laser_Get_Laser_Request_init_default     {0}
#define laser_Intensity_Request_init_default     {0}
#define laser_Laser_Reply_init_default           {0, 0, 0, 0, 0}
#define laser_Laser_State_Reply_init_default     {0}
#define laser_Laser_Status_Reply_init_default    {0, 0, 0, 0}
#define laser_Request_init_default               {0, {laser_Laser_Request_init_default}}
#define laser_Reply_init_default                 {0, {error_Error_init_default}}
#define laser_Raw_Data_Request_init_zero         {0}
#define laser_Raw_Data_Reply_init_zero           {0, {0}, 0, {0}, 0, {0}}
#define laser_Laser_Inventory_Request_init_zero  {0}
#define laser_Laser_Inventory_Reply_init_zero    {"", "", 0, _laser_LaserType_MIN}
#define laser_Laser_Set_Type_Request_init_zero   {_laser_LaserType_MIN}
#define laser_Laser_Reset_Request_init_zero      {0, 0}
#define laser_Jlight_Status_init_zero            {0, 0, 0, {0, 0}, 0, {_laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN, _laser_Jlight_Fault_MIN}}
#define laser_Bwt_Status_init_zero               {0, {0, 0, 0, 0, 0}}
#define laser_Bwt_Passthrough_Request_init_zero  {""}
#define laser_Bwt_Passthrough_Reply_init_zero    {""}
#define laser_Bwt_Transport_Get_Config_Request_init_zero {0}
#define laser_Bwt_Transport_Config_init_zero     {0, 0}
#define laser_Diode_Status_Request_init_zero     {0}
#define laser_Diode_Status_Reply_init_zero       {0, 0, {0, 0, 0, 0}, 0, {0, 0, 0, 0}, 0, {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}, 0, 0, {0, 0}, 0, {laser_Bwt_Status_init_zero}}
#define laser_Diode_Set_Current_Request_init_zero {0, {0, 0, 0, 0}, 0}
#define laser_Laser_Request_init_zero            {0}
#define laser_Get_Laser_Request_init_zero        {0}
#define laser_Intensity_Request_init_zero        {0}
#define laser_Laser_Reply_init_zero              {0, 0, 0, 0, 0}
#define laser_Laser_State_Reply_init_zero        {0}
#define laser_Laser_Status_Reply_init_zero       {0, 0, 0, 0}
#define laser_Request_init_zero                  {0, {laser_Laser_Request_init_zero}}
#define laser_Reply_init_zero                    {0, {error_Error_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define laser_Bwt_Passthrough_Reply_response_tag 1
#define laser_Bwt_Passthrough_Request_command_tag 1
#define laser_Bwt_Status_faults_tag              1
#define laser_Bwt_Transport_Config_log_messages_tag 1
#define laser_Bwt_Transport_Config_intercommand_delay_tag 2
#define laser_Diode_Set_Current_Request_current_tag 1
#define laser_Diode_Set_Current_Request_commit_tag 2
#define laser_Intensity_Request_intensity_tag    1
#define laser_Jlight_Status_psuTemp_tag          1
#define laser_Jlight_Status_psuInputVolts_tag    2
#define laser_Jlight_Status_psuOutputVolts_tag   3
#define laser_Jlight_Status_faults_tag           4
#define laser_Laser_Inventory_Reply_model_tag    1
#define laser_Laser_Inventory_Reply_serial_tag   2
#define laser_Laser_Inventory_Reply_power_tag    3
#define laser_Laser_Inventory_Reply_type_tag     4
#define laser_Laser_Reply_raw_therm1_reading_mv_tag 1
#define laser_Laser_Reply_raw_therm2_reading_mv_tag 2
#define laser_Laser_Reply_on_tag                 3
#define laser_Laser_Reply_lpsu_state_tag         4
#define laser_Laser_Reply_fireable_tag           5
#define laser_Laser_Request_on_tag               1
#define laser_Laser_Reset_Request_transport_tag  1
#define laser_Laser_Reset_Request_faults_tag     2
#define laser_Laser_Set_Type_Request_type_tag    1
#define laser_Laser_State_Reply_on_tag           1
#define laser_Laser_Status_Reply_lpsu_state_tag  1
#define laser_Laser_Status_Reply_lpsu_current_tag 2
#define laser_Laser_Status_Reply_power_tag       3
#define laser_Laser_Status_Reply_arc_detected_tag 4
#define laser_Raw_Data_Reply_therm1_raw_tag      1
#define laser_Raw_Data_Reply_therm2_raw_tag      2
#define laser_Raw_Data_Reply_photodiode_raw_tag  3
#define laser_Diode_Status_Reply_timestamp_tag   1
#define laser_Diode_Status_Reply_temp_tag        2
#define laser_Diode_Status_Reply_humidity_tag    3
#define laser_Diode_Status_Reply_current_tag     4
#define laser_Diode_Status_Reply_faulted_tag     5
#define laser_Diode_Status_Reply_thermistors_tag 6
#define laser_Diode_Status_Reply_extra_bwt_tag   7
#define laser_Diode_Status_Reply_extra_jlight_tag 8
#define laser_Request_laser_tag                  1
#define laser_Request_get_laser_tag              2
#define laser_Request_intensity_tag              3
#define laser_Request_raw_data_tag               4
#define laser_Request_diode_status_tag           5
#define laser_Request_laser_inventory_tag        6
#define laser_Request_bwt_passthrough_tag        7
#define laser_Request_bwt_set_config_tag         9
#define laser_Request_bwt_get_config_tag         10
#define laser_Request_laser_reset_tag            11
#define laser_Request_diode_set_current_tag      12
#define laser_Request_set_type_tag               13
#define laser_Reply_error_tag                    1
#define laser_Reply_ack_tag                      2
#define laser_Reply_laser_tag                    3
#define laser_Reply_laser_reply_tag              4
#define laser_Reply_raw_data_tag                 5
#define laser_Reply_diode_status_tag             6
#define laser_Reply_laser_inventory_tag          7
#define laser_Reply_bwt_passthrough_tag          8
#define laser_Reply_bwt_config_tag               9

/* Struct field encoding specification for nanopb */
#define laser_Raw_Data_Request_FIELDLIST(X, a) \

#define laser_Raw_Data_Request_CALLBACK NULL
#define laser_Raw_Data_Request_DEFAULT NULL

#define laser_Raw_Data_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    INT32,    (_therm1_raw,therm1_raw,_therm1_raw.therm1_raw),   1) \
X(a, STATIC,   ONEOF,    INT32,    (_therm2_raw,therm2_raw,_therm2_raw.therm2_raw),   2) \
X(a, STATIC,   ONEOF,    INT32,    (_photodiode_raw,photodiode_raw,_photodiode_raw.photodiode_raw),   3)
#define laser_Raw_Data_Reply_CALLBACK NULL
#define laser_Raw_Data_Reply_DEFAULT NULL

#define laser_Laser_Inventory_Request_FIELDLIST(X, a) \

#define laser_Laser_Inventory_Request_CALLBACK NULL
#define laser_Laser_Inventory_Request_DEFAULT NULL

#define laser_Laser_Inventory_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   model,             1) \
X(a, STATIC,   SINGULAR, STRING,   serial,            2) \
X(a, STATIC,   SINGULAR, UINT32,   power,             3) \
X(a, STATIC,   SINGULAR, UENUM,    type,              4)
#define laser_Laser_Inventory_Reply_CALLBACK NULL
#define laser_Laser_Inventory_Reply_DEFAULT NULL

#define laser_Laser_Set_Type_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1)
#define laser_Laser_Set_Type_Request_CALLBACK NULL
#define laser_Laser_Set_Type_Request_DEFAULT NULL

#define laser_Laser_Reset_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     transport,         1) \
X(a, STATIC,   SINGULAR, BOOL,     faults,            2)
#define laser_Laser_Reset_Request_CALLBACK NULL
#define laser_Laser_Reset_Request_DEFAULT NULL

#define laser_Jlight_Status_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    psuTemp,           1) \
X(a, STATIC,   SINGULAR, FLOAT,    psuInputVolts,     2) \
X(a, STATIC,   REPEATED, FLOAT,    psuOutputVolts,    3) \
X(a, STATIC,   REPEATED, UENUM,    faults,            4)
#define laser_Jlight_Status_CALLBACK NULL
#define laser_Jlight_Status_DEFAULT NULL

#define laser_Bwt_Status_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, INT32,    faults,            1)
#define laser_Bwt_Status_CALLBACK NULL
#define laser_Bwt_Status_DEFAULT NULL

#define laser_Bwt_Passthrough_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   command,           1)
#define laser_Bwt_Passthrough_Request_CALLBACK NULL
#define laser_Bwt_Passthrough_Request_DEFAULT NULL

#define laser_Bwt_Passthrough_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   response,          1)
#define laser_Bwt_Passthrough_Reply_CALLBACK NULL
#define laser_Bwt_Passthrough_Reply_DEFAULT NULL

#define laser_Bwt_Transport_Get_Config_Request_FIELDLIST(X, a) \

#define laser_Bwt_Transport_Get_Config_Request_CALLBACK NULL
#define laser_Bwt_Transport_Get_Config_Request_DEFAULT NULL

#define laser_Bwt_Transport_Config_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     log_messages,      1) \
X(a, STATIC,   SINGULAR, BOOL,     intercommand_delay,   2)
#define laser_Bwt_Transport_Config_CALLBACK NULL
#define laser_Bwt_Transport_Config_DEFAULT NULL

#define laser_Diode_Status_Request_FIELDLIST(X, a) \

#define laser_Diode_Status_Request_CALLBACK NULL
#define laser_Diode_Status_Request_DEFAULT NULL

#define laser_Diode_Status_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp,         1) \
X(a, STATIC,   REPEATED, FLOAT,    temp,              2) \
X(a, STATIC,   REPEATED, FLOAT,    humidity,          3) \
X(a, STATIC,   REPEATED, FLOAT,    current,           4) \
X(a, STATIC,   SINGULAR, BOOL,     faulted,           5) \
X(a, STATIC,   REPEATED, FLOAT,    thermistors,       6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (extra,extra_bwt,extra.extra_bwt),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (extra,extra_jlight,extra.extra_jlight),   8)
#define laser_Diode_Status_Reply_CALLBACK NULL
#define laser_Diode_Status_Reply_DEFAULT NULL
#define laser_Diode_Status_Reply_extra_extra_bwt_MSGTYPE laser_Bwt_Status
#define laser_Diode_Status_Reply_extra_extra_jlight_MSGTYPE laser_Jlight_Status

#define laser_Diode_Set_Current_Request_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, UINT32,   current,           1) \
X(a, STATIC,   SINGULAR, BOOL,     commit,            2)
#define laser_Diode_Set_Current_Request_CALLBACK NULL
#define laser_Diode_Set_Current_Request_DEFAULT NULL

#define laser_Laser_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     on,                1)
#define laser_Laser_Request_CALLBACK NULL
#define laser_Laser_Request_DEFAULT NULL

#define laser_Get_Laser_Request_FIELDLIST(X, a) \

#define laser_Get_Laser_Request_CALLBACK NULL
#define laser_Get_Laser_Request_DEFAULT NULL

#define laser_Intensity_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    intensity,         1)
#define laser_Intensity_Request_CALLBACK NULL
#define laser_Intensity_Request_DEFAULT NULL

#define laser_Laser_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    raw_therm1_reading_mv,   1) \
X(a, STATIC,   SINGULAR, INT32,    raw_therm2_reading_mv,   2) \
X(a, STATIC,   SINGULAR, BOOL,     on,                3) \
X(a, STATIC,   SINGULAR, BOOL,     lpsu_state,        4) \
X(a, STATIC,   SINGULAR, BOOL,     fireable,          5)
#define laser_Laser_Reply_CALLBACK NULL
#define laser_Laser_Reply_DEFAULT NULL

#define laser_Laser_State_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     on,                1)
#define laser_Laser_State_Reply_CALLBACK NULL
#define laser_Laser_State_Reply_DEFAULT NULL

#define laser_Laser_Status_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     lpsu_state,        1) \
X(a, STATIC,   SINGULAR, FLOAT,    lpsu_current,      2) \
X(a, STATIC,   SINGULAR, FLOAT,    power,             3) \
X(a, STATIC,   SINGULAR, BOOL,     arc_detected,      4)
#define laser_Laser_Status_Reply_CALLBACK NULL
#define laser_Laser_Status_Reply_DEFAULT NULL

#define laser_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,laser,request.laser),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_laser,request.get_laser),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,intensity,request.intensity),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,raw_data,request.raw_data),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,diode_status,request.diode_status),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,laser_inventory,request.laser_inventory),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,bwt_passthrough,request.bwt_passthrough),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,bwt_set_config,request.bwt_set_config),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,bwt_get_config,request.bwt_get_config),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,laser_reset,request.laser_reset),  11) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,diode_set_current,request.diode_set_current),  12) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set_type,request.set_type),  13)
#define laser_Request_CALLBACK NULL
#define laser_Request_DEFAULT NULL
#define laser_Request_request_laser_MSGTYPE laser_Laser_Request
#define laser_Request_request_get_laser_MSGTYPE laser_Get_Laser_Request
#define laser_Request_request_intensity_MSGTYPE laser_Intensity_Request
#define laser_Request_request_raw_data_MSGTYPE laser_Raw_Data_Request
#define laser_Request_request_diode_status_MSGTYPE laser_Diode_Status_Request
#define laser_Request_request_laser_inventory_MSGTYPE laser_Laser_Inventory_Request
#define laser_Request_request_bwt_passthrough_MSGTYPE laser_Bwt_Passthrough_Request
#define laser_Request_request_bwt_set_config_MSGTYPE laser_Bwt_Transport_Config
#define laser_Request_request_bwt_get_config_MSGTYPE laser_Bwt_Transport_Get_Config_Request
#define laser_Request_request_laser_reset_MSGTYPE laser_Laser_Reset_Request
#define laser_Request_request_diode_set_current_MSGTYPE laser_Diode_Set_Current_Request
#define laser_Request_request_set_type_MSGTYPE laser_Laser_Set_Type_Request

#define laser_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,laser,reply.laser),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,laser_reply,reply.laser_reply),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,raw_data,reply.raw_data),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,diode_status,reply.diode_status),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,laser_inventory,reply.laser_inventory),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,bwt_passthrough,reply.bwt_passthrough),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,bwt_config,reply.bwt_config),   9)
#define laser_Reply_CALLBACK NULL
#define laser_Reply_DEFAULT NULL
#define laser_Reply_reply_error_MSGTYPE error_Error
#define laser_Reply_reply_ack_MSGTYPE ack_Ack
#define laser_Reply_reply_laser_MSGTYPE laser_Laser_State_Reply
#define laser_Reply_reply_laser_reply_MSGTYPE laser_Laser_Reply
#define laser_Reply_reply_raw_data_MSGTYPE laser_Raw_Data_Reply
#define laser_Reply_reply_diode_status_MSGTYPE laser_Diode_Status_Reply
#define laser_Reply_reply_laser_inventory_MSGTYPE laser_Laser_Inventory_Reply
#define laser_Reply_reply_bwt_passthrough_MSGTYPE laser_Bwt_Passthrough_Reply
#define laser_Reply_reply_bwt_config_MSGTYPE laser_Bwt_Transport_Config

extern const pb_msgdesc_t laser_Raw_Data_Request_msg;
extern const pb_msgdesc_t laser_Raw_Data_Reply_msg;
extern const pb_msgdesc_t laser_Laser_Inventory_Request_msg;
extern const pb_msgdesc_t laser_Laser_Inventory_Reply_msg;
extern const pb_msgdesc_t laser_Laser_Set_Type_Request_msg;
extern const pb_msgdesc_t laser_Laser_Reset_Request_msg;
extern const pb_msgdesc_t laser_Jlight_Status_msg;
extern const pb_msgdesc_t laser_Bwt_Status_msg;
extern const pb_msgdesc_t laser_Bwt_Passthrough_Request_msg;
extern const pb_msgdesc_t laser_Bwt_Passthrough_Reply_msg;
extern const pb_msgdesc_t laser_Bwt_Transport_Get_Config_Request_msg;
extern const pb_msgdesc_t laser_Bwt_Transport_Config_msg;
extern const pb_msgdesc_t laser_Diode_Status_Request_msg;
extern const pb_msgdesc_t laser_Diode_Status_Reply_msg;
extern const pb_msgdesc_t laser_Diode_Set_Current_Request_msg;
extern const pb_msgdesc_t laser_Laser_Request_msg;
extern const pb_msgdesc_t laser_Get_Laser_Request_msg;
extern const pb_msgdesc_t laser_Intensity_Request_msg;
extern const pb_msgdesc_t laser_Laser_Reply_msg;
extern const pb_msgdesc_t laser_Laser_State_Reply_msg;
extern const pb_msgdesc_t laser_Laser_Status_Reply_msg;
extern const pb_msgdesc_t laser_Request_msg;
extern const pb_msgdesc_t laser_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define laser_Raw_Data_Request_fields &laser_Raw_Data_Request_msg
#define laser_Raw_Data_Reply_fields &laser_Raw_Data_Reply_msg
#define laser_Laser_Inventory_Request_fields &laser_Laser_Inventory_Request_msg
#define laser_Laser_Inventory_Reply_fields &laser_Laser_Inventory_Reply_msg
#define laser_Laser_Set_Type_Request_fields &laser_Laser_Set_Type_Request_msg
#define laser_Laser_Reset_Request_fields &laser_Laser_Reset_Request_msg
#define laser_Jlight_Status_fields &laser_Jlight_Status_msg
#define laser_Bwt_Status_fields &laser_Bwt_Status_msg
#define laser_Bwt_Passthrough_Request_fields &laser_Bwt_Passthrough_Request_msg
#define laser_Bwt_Passthrough_Reply_fields &laser_Bwt_Passthrough_Reply_msg
#define laser_Bwt_Transport_Get_Config_Request_fields &laser_Bwt_Transport_Get_Config_Request_msg
#define laser_Bwt_Transport_Config_fields &laser_Bwt_Transport_Config_msg
#define laser_Diode_Status_Request_fields &laser_Diode_Status_Request_msg
#define laser_Diode_Status_Reply_fields &laser_Diode_Status_Reply_msg
#define laser_Diode_Set_Current_Request_fields &laser_Diode_Set_Current_Request_msg
#define laser_Laser_Request_fields &laser_Laser_Request_msg
#define laser_Get_Laser_Request_fields &laser_Get_Laser_Request_msg
#define laser_Intensity_Request_fields &laser_Intensity_Request_msg
#define laser_Laser_Reply_fields &laser_Laser_Reply_msg
#define laser_Laser_State_Reply_fields &laser_Laser_State_Reply_msg
#define laser_Laser_Status_Reply_fields &laser_Laser_Status_Reply_msg
#define laser_Request_fields &laser_Request_msg
#define laser_Reply_fields &laser_Reply_msg

/* Maximum encoded size of messages (where known) */
#define laser_Raw_Data_Request_size              0
#define laser_Raw_Data_Reply_size                33
#define laser_Laser_Inventory_Request_size       0
#define laser_Laser_Inventory_Reply_size         74
#define laser_Laser_Set_Type_Request_size        2
#define laser_Laser_Reset_Request_size           4
#define laser_Jlight_Status_size                 40
#define laser_Bwt_Status_size                    55
#define laser_Bwt_Passthrough_Request_size       65
#define laser_Bwt_Passthrough_Reply_size         65
#define laser_Bwt_Transport_Get_Config_Request_size 0
#define laser_Bwt_Transport_Config_size          4
#define laser_Diode_Status_Request_size          0
#define laser_Diode_Status_Reply_size            210
#define laser_Diode_Set_Current_Request_size     26
#define laser_Laser_Request_size                 2
#define laser_Get_Laser_Request_size             0
#define laser_Intensity_Request_size             11
#define laser_Laser_Reply_size                   28
#define laser_Laser_State_Reply_size             2
#define laser_Laser_Status_Reply_size            14
#define laser_Request_size                       67
#if defined(error_Error_size) && defined(ack_Ack_size)
typedef union laser_Reply_reply_size_union {char f1[(6 + error_Error_size)]; char f2[(6 + ack_Ack_size)]; char f0[213];} laser_Reply_reply_size_union;
#define laser_Reply_size                         (0 + sizeof(laser_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

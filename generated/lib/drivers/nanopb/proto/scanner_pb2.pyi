"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.epos_pb2 import (
    Home_Params as generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params,
)

from generated.lib.drivers.nanopb.proto.servo_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___servo_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___servo_pb2___Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Laser_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    on: builtin___bool = ...

    def __init__(self,
        *,
        on : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"on",b"on"]) -> None: ...
type___Laser_Request = Laser_Request

class Get_Laser_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Laser_Request = Get_Laser_Request

class Intensity_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    intensity: builtin___int = ...

    def __init__(self,
        *,
        intensity : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"intensity",b"intensity"]) -> None: ...
type___Intensity_Request = Intensity_Request

class Boot_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def pan_params(self) -> generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params: ...

    @property
    def tilt_params(self) -> generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params: ...

    def __init__(self,
        *,
        pan_params : typing___Optional[generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params] = None,
        tilt_params : typing___Optional[generated___lib___drivers___nanopb___proto___epos_pb2___Home_Params] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pan_params",b"pan_params",u"tilt_params",b"tilt_params"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan_params",b"pan_params",u"tilt_params",b"tilt_params"]) -> None: ...
type___Boot_Request = Boot_Request

class Stop_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Stop_Request = Stop_Request

class Gimbal_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def pan(self) -> generated___lib___drivers___nanopb___proto___servo_pb2___Request: ...

    @property
    def tilt(self) -> generated___lib___drivers___nanopb___proto___servo_pb2___Request: ...

    def __init__(self,
        *,
        pan : typing___Optional[generated___lib___drivers___nanopb___proto___servo_pb2___Request] = None,
        tilt : typing___Optional[generated___lib___drivers___nanopb___proto___servo_pb2___Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pan",b"pan",u"tilt",b"tilt"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan",b"pan",u"tilt",b"tilt"]) -> None: ...
type___Gimbal_Request = Gimbal_Request

class Error_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Error_Reply = Error_Reply

class ACK_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ACK_Reply = ACK_Reply

class Laser_State_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    on: builtin___bool = ...

    def __init__(self,
        *,
        on : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"on",b"on"]) -> None: ...
type___Laser_State_Reply = Laser_State_Reply

class Gimbal_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def pan(self) -> generated___lib___drivers___nanopb___proto___servo_pb2___Reply: ...

    @property
    def tilt(self) -> generated___lib___drivers___nanopb___proto___servo_pb2___Reply: ...

    def __init__(self,
        *,
        pan : typing___Optional[generated___lib___drivers___nanopb___proto___servo_pb2___Reply] = None,
        tilt : typing___Optional[generated___lib___drivers___nanopb___proto___servo_pb2___Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pan",b"pan",u"tilt",b"tilt"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pan",b"pan",u"tilt",b"tilt"]) -> None: ...
type___Gimbal_Reply = Gimbal_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def laser(self) -> type___Laser_Request: ...

    @property
    def get_laser(self) -> type___Get_Laser_Request: ...

    @property
    def boot(self) -> type___Boot_Request: ...

    @property
    def stop(self) -> type___Stop_Request: ...

    @property
    def gimbal(self) -> type___Gimbal_Request: ...

    @property
    def intensity(self) -> type___Intensity_Request: ...

    def __init__(self,
        *,
        laser : typing___Optional[type___Laser_Request] = None,
        get_laser : typing___Optional[type___Get_Laser_Request] = None,
        boot : typing___Optional[type___Boot_Request] = None,
        stop : typing___Optional[type___Stop_Request] = None,
        gimbal : typing___Optional[type___Gimbal_Request] = None,
        intensity : typing___Optional[type___Intensity_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"boot",b"boot",u"get_laser",b"get_laser",u"gimbal",b"gimbal",u"intensity",b"intensity",u"laser",b"laser",u"request",b"request",u"stop",b"stop"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"boot",b"boot",u"get_laser",b"get_laser",u"gimbal",b"gimbal",u"intensity",b"intensity",u"laser",b"laser",u"request",b"request",u"stop",b"stop"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["laser","get_laser","boot","stop","gimbal","intensity"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def error(self) -> type___Error_Reply: ...

    @property
    def ack(self) -> type___ACK_Reply: ...

    @property
    def laser(self) -> type___Laser_State_Reply: ...

    @property
    def gimbal(self) -> type___Gimbal_Reply: ...

    def __init__(self,
        *,
        error : typing___Optional[type___Error_Reply] = None,
        ack : typing___Optional[type___ACK_Reply] = None,
        laser : typing___Optional[type___Laser_State_Reply] = None,
        gimbal : typing___Optional[type___Gimbal_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"gimbal",b"gimbal",u"laser",b"laser",u"reply",b"reply"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"gimbal",b"gimbal",u"laser",b"laser",u"reply",b"reply"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","laser","gimbal"]: ...
type___Reply = Reply

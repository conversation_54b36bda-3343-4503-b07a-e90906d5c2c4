# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/error.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/error.proto',
  package='error',
  syntax='proto3',
  serialized_options=b'Z\014nanopb/error',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n.generated/lib/drivers/nanopb/proto/error.proto\x12\x05\x65rror\"\x15\n\x05\x45rror\x12\x0c\n\x04\x63ode\x18\x01 \x01(\rB\x0eZ\x0cnanopb/errorb\x06proto3'
)




_ERROR = _descriptor.Descriptor(
  name='Error',
  full_name='error.Error',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='error.Error.code', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=57,
  serialized_end=78,
)

DESCRIPTOR.message_types_by_name['Error'] = _ERROR
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Error = _reflection.GeneratedProtocolMessageType('Error', (_message.Message,), {
  'DESCRIPTOR' : _ERROR,
  '__module__' : 'generated.lib.drivers.nanopb.proto.error_pb2'
  # @@protoc_insertion_point(class_scope:error.Error)
  })
_sym_db.RegisterMessage(Error)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

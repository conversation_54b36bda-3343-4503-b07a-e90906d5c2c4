"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class BoardVersionRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___BoardVersionRequest = BoardVersionRequest

class BoardIdentityRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___BoardIdentityRequest = BoardIdentityRequest

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def version(self) -> type___BoardVersionRequest: ...

    @property
    def identity(self) -> type___BoardIdentityRequest: ...

    def __init__(self,
        *,
        version : typing___Optional[type___BoardVersionRequest] = None,
        identity : typing___Optional[type___BoardIdentityRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"identity",b"identity",u"request",b"request",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"identity",b"identity",u"request",b"request",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["version","identity"]: ...
type___Request = Request

class BoardVersionReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model: typing___Text = ...
    rev: builtin___int = ...

    def __init__(self,
        *,
        model : typing___Optional[typing___Text] = None,
        rev : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"model",b"model",u"rev",b"rev"]) -> None: ...
type___BoardVersionReply = BoardVersionReply

class BoardIdentityReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cbsn: typing___Text = ...
    assySn: typing___Text = ...

    def __init__(self,
        *,
        cbsn : typing___Optional[typing___Text] = None,
        assySn : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_assySn",b"_assySn",u"_cbsn",b"_cbsn",u"assySn",b"assySn",u"cbsn",b"cbsn"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_assySn",b"_assySn",u"_cbsn",b"_cbsn",u"assySn",b"assySn",u"cbsn",b"cbsn"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_assySn",b"_assySn"]) -> typing_extensions___Literal["assySn"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_cbsn",b"_cbsn"]) -> typing_extensions___Literal["cbsn"]: ...
type___BoardIdentityReply = BoardIdentityReply

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def version(self) -> type___BoardVersionReply: ...

    @property
    def identity(self) -> type___BoardIdentityReply: ...

    def __init__(self,
        *,
        version : typing___Optional[type___BoardVersionReply] = None,
        identity : typing___Optional[type___BoardIdentityReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"identity",b"identity",u"reply",b"reply",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"identity",b"identity",u"reply",b"reply",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["version","identity"]: ...
type___Reply = Reply

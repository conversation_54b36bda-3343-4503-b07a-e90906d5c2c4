/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_PULCZAR_PULCZAR_PB_H_INCLUDED
#define PB_PULCZAR_PULCZAR_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/gimbal.pb.h"
#include "generated/lib/drivers/nanopb/proto/laser.pb.h"
#include "generated/lib/drivers/nanopb/proto/lens.pb.h"
#include "generated/lib/drivers/nanopb/proto/dawg.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"
#include "generated/lib/drivers/nanopb/proto/scanner_config.pb.h"
#include "generated/lib/drivers/nanopb/proto/arc_detector.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _pulczar_AmbientTempGetStateRequest {
    char dummy_field;
} pulczar_AmbientTempGetStateRequest;

typedef struct _pulczar_Clear_Config_Request {
    char dummy_field;
} pulczar_Clear_Config_Request;

typedef struct _pulczar_HwStatus_Request {
    char dummy_field;
} pulczar_HwStatus_Request;

typedef struct _pulczar_Reset_Request {
    char dummy_field;
} pulczar_Reset_Request;

typedef struct _pulczar_Status_Request {
    char dummy_field;
} pulczar_Status_Request;

typedef struct _pulczar_AmbientTempConfig {
    bool enabled;
    bool use_thermistor;
    pb_size_t which__temp;
    union {
        float temp;
    } _temp;
} pulczar_AmbientTempConfig;

typedef struct _pulczar_HwStatus_Servo {
    bool connected;
    uint32_t controller_sn;
    uint64_t sensor_time_ms;
    float output_stage_temp_c;
    float motor_supply_v;
    float motor_current_a;
    uint64_t encoder_time_ms;
    int64_t encoder_ticks;
} pulczar_HwStatus_Servo;

typedef struct _pulczar_Override_Request {
    uint32_t override;
} pulczar_Override_Request;

typedef struct _pulczar_Power_Reply {
    bool targetCam;
    bool firingBoard;
} pulczar_Power_Reply;

typedef struct _pulczar_Power_Request {
    pb_size_t which__targetCam;
    union {
        bool targetCam;
    } _targetCam;
    pb_size_t which__firingBoard;
    union {
        bool firingBoard;
    } _firingBoard;
} pulczar_Power_Request;

typedef struct _pulczar_Status_Reply {
    uint32_t status;
    bool has_laser_status;
    laser_Laser_Status_Reply laser_status;
} pulczar_Status_Reply;

typedef struct _pulczar_AmbientTempReply {
    float sense_temp;
    bool has_config;
    pulczar_AmbientTempConfig config;
} pulczar_AmbientTempReply;

typedef struct _pulczar_AmbientTempRequest {
    pb_size_t which_request;
    union {
        pulczar_AmbientTempConfig set_config;
        pulczar_AmbientTempGetStateRequest get_config;
    } request;
} pulczar_AmbientTempRequest;

typedef struct _pulczar_HwStatus_Reaper_Reply {
    bool laser_connected;
    bool has_laser_inventory;
    laser_Laser_Inventory_Reply laser_inventory;
    bool has_laser_status;
    laser_Diode_Status_Reply laser_status;
    pb_size_t laser_therm_temp_c_count;
    float laser_therm_temp_c[2];
    float laser_power_w;
    float laser_power_raw_mv;
    pb_size_t which__servo_pan;
    union {
        pulczar_HwStatus_Servo servo_pan;
    } _servo_pan;
    pb_size_t which__servo_tilt;
    union {
        pulczar_HwStatus_Servo servo_tilt;
    } _servo_tilt;
    bool target_cam_power_on;
} pulczar_HwStatus_Reaper_Reply;

typedef struct _pulczar_HwStatus_Slayer_Reply {
    float lpm_thermistor_beam_raw_mv;
    float lpm_thermistor_beam_temp_c;
    float lpm_thermistor_ambient_raw_mv;
    float lpm_thermistor_ambient_temp_c;
    bool lpsu_status;
    float lpsu_current_ma;
    pb_size_t which__servo_pan;
    union {
        pulczar_HwStatus_Servo servo_pan;
    } _servo_pan;
    pb_size_t which__servo_tilt;
    union {
        pulczar_HwStatus_Servo servo_tilt;
    } _servo_tilt;
    bool target_cam_power_on;
} pulczar_HwStatus_Slayer_Reply;

typedef struct _pulczar_HwStatus_Reply {
    pb_size_t which_reply;
    union {
        pulczar_HwStatus_Slayer_Reply slayer;
        pulczar_HwStatus_Reaper_Reply reaper;
    } reply;
} pulczar_HwStatus_Reply;

typedef struct _pulczar_Request {
    pb_size_t which_request;
    union {
        pulczar_Reset_Request reset;
        pulczar_Clear_Config_Request clear;
        gimbal_Request gimbal;
        dawg_Request dawg;
        laser_Request laser;
        pulczar_Status_Request status;
        lens_Request lens;
        pulczar_Override_Request override;
        scanner_config_Request conf;
        arc_detector_Request arc;
        pulczar_Power_Request power;
        pulczar_HwStatus_Request hw_status;
        pulczar_AmbientTempRequest ambient_temp;
    } request;
} pulczar_Request;

typedef struct _pulczar_Reply {
    pb_size_t which_reply;
    union {
        ack_Ack ack;
        gimbal_Reply gimbal;
        dawg_Reply dawg;
        laser_Reply laser;
        pulczar_Status_Reply status;
        lens_Reply lens;
        scanner_config_Reply conf;
        arc_detector_Reply arc;
        pulczar_Power_Reply power;
        pulczar_HwStatus_Reply hw_status;
        pulczar_AmbientTempReply ambient_temp;
    } reply;
} pulczar_Reply;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define pulczar_AmbientTempConfig_init_default   {0, 0, 0, {0}}
#define pulczar_AmbientTempGetStateRequest_init_default {0}
#define pulczar_AmbientTempRequest_init_default  {0, {pulczar_AmbientTempConfig_init_default}}
#define pulczar_AmbientTempReply_init_default    {0, false, pulczar_AmbientTempConfig_init_default}
#define pulczar_HwStatus_Request_init_default    {0}
#define pulczar_HwStatus_Servo_init_default      {0, 0, 0, 0, 0, 0, 0, 0}
#define pulczar_HwStatus_Slayer_Reply_init_default {0, 0, 0, 0, 0, 0, 0, {pulczar_HwStatus_Servo_init_default}, 0, {pulczar_HwStatus_Servo_init_default}, 0}
#define pulczar_HwStatus_Reaper_Reply_init_default {0, false, laser_Laser_Inventory_Reply_init_default, false, laser_Diode_Status_Reply_init_default, 0, {0, 0}, 0, 0, 0, {pulczar_HwStatus_Servo_init_default}, 0, {pulczar_HwStatus_Servo_init_default}, 0}
#define pulczar_HwStatus_Reply_init_default      {0, {pulczar_HwStatus_Slayer_Reply_init_default}}
#define pulczar_Reset_Request_init_default       {0}
#define pulczar_Clear_Config_Request_init_default {0}
#define pulczar_Status_Request_init_default      {0}
#define pulczar_Override_Request_init_default    {0}
#define pulczar_Power_Request_init_default       {0, {0}, 0, {0}}
#define pulczar_Status_Reply_init_default        {0, false, laser_Laser_Status_Reply_init_default}
#define pulczar_Power_Reply_init_default         {0, 0}
#define pulczar_Request_init_default             {0, {pulczar_Reset_Request_init_default}}
#define pulczar_Reply_init_default               {0, {ack_Ack_init_default}}
#define pulczar_AmbientTempConfig_init_zero      {0, 0, 0, {0}}
#define pulczar_AmbientTempGetStateRequest_init_zero {0}
#define pulczar_AmbientTempRequest_init_zero     {0, {pulczar_AmbientTempConfig_init_zero}}
#define pulczar_AmbientTempReply_init_zero       {0, false, pulczar_AmbientTempConfig_init_zero}
#define pulczar_HwStatus_Request_init_zero       {0}
#define pulczar_HwStatus_Servo_init_zero         {0, 0, 0, 0, 0, 0, 0, 0}
#define pulczar_HwStatus_Slayer_Reply_init_zero  {0, 0, 0, 0, 0, 0, 0, {pulczar_HwStatus_Servo_init_zero}, 0, {pulczar_HwStatus_Servo_init_zero}, 0}
#define pulczar_HwStatus_Reaper_Reply_init_zero  {0, false, laser_Laser_Inventory_Reply_init_zero, false, laser_Diode_Status_Reply_init_zero, 0, {0, 0}, 0, 0, 0, {pulczar_HwStatus_Servo_init_zero}, 0, {pulczar_HwStatus_Servo_init_zero}, 0}
#define pulczar_HwStatus_Reply_init_zero         {0, {pulczar_HwStatus_Slayer_Reply_init_zero}}
#define pulczar_Reset_Request_init_zero          {0}
#define pulczar_Clear_Config_Request_init_zero   {0}
#define pulczar_Status_Request_init_zero         {0}
#define pulczar_Override_Request_init_zero       {0}
#define pulczar_Power_Request_init_zero          {0, {0}, 0, {0}}
#define pulczar_Status_Reply_init_zero           {0, false, laser_Laser_Status_Reply_init_zero}
#define pulczar_Power_Reply_init_zero            {0, 0}
#define pulczar_Request_init_zero                {0, {pulczar_Reset_Request_init_zero}}
#define pulczar_Reply_init_zero                  {0, {ack_Ack_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define pulczar_AmbientTempConfig_enabled_tag    1
#define pulczar_AmbientTempConfig_use_thermistor_tag 2
#define pulczar_AmbientTempConfig_temp_tag       3
#define pulczar_HwStatus_Servo_connected_tag     1
#define pulczar_HwStatus_Servo_controller_sn_tag 2
#define pulczar_HwStatus_Servo_sensor_time_ms_tag 3
#define pulczar_HwStatus_Servo_output_stage_temp_c_tag 4
#define pulczar_HwStatus_Servo_motor_supply_v_tag 5
#define pulczar_HwStatus_Servo_motor_current_a_tag 6
#define pulczar_HwStatus_Servo_encoder_time_ms_tag 7
#define pulczar_HwStatus_Servo_encoder_ticks_tag 8
#define pulczar_Override_Request_override_tag    1
#define pulczar_Power_Reply_targetCam_tag        1
#define pulczar_Power_Reply_firingBoard_tag      2
#define pulczar_Power_Request_targetCam_tag      1
#define pulczar_Power_Request_firingBoard_tag    2
#define pulczar_Status_Reply_status_tag          1
#define pulczar_Status_Reply_laser_status_tag    2
#define pulczar_AmbientTempReply_sense_temp_tag  1
#define pulczar_AmbientTempReply_config_tag      2
#define pulczar_AmbientTempRequest_set_config_tag 1
#define pulczar_AmbientTempRequest_get_config_tag 2
#define pulczar_HwStatus_Reaper_Reply_laser_connected_tag 1
#define pulczar_HwStatus_Reaper_Reply_laser_inventory_tag 2
#define pulczar_HwStatus_Reaper_Reply_laser_status_tag 3
#define pulczar_HwStatus_Reaper_Reply_laser_therm_temp_c_tag 4
#define pulczar_HwStatus_Reaper_Reply_laser_power_w_tag 5
#define pulczar_HwStatus_Reaper_Reply_laser_power_raw_mv_tag 6
#define pulczar_HwStatus_Reaper_Reply_servo_pan_tag 7
#define pulczar_HwStatus_Reaper_Reply_servo_tilt_tag 8
#define pulczar_HwStatus_Reaper_Reply_target_cam_power_on_tag 9
#define pulczar_HwStatus_Slayer_Reply_lpm_thermistor_beam_raw_mv_tag 1
#define pulczar_HwStatus_Slayer_Reply_lpm_thermistor_beam_temp_c_tag 2
#define pulczar_HwStatus_Slayer_Reply_lpm_thermistor_ambient_raw_mv_tag 3
#define pulczar_HwStatus_Slayer_Reply_lpm_thermistor_ambient_temp_c_tag 4
#define pulczar_HwStatus_Slayer_Reply_lpsu_status_tag 5
#define pulczar_HwStatus_Slayer_Reply_lpsu_current_ma_tag 6
#define pulczar_HwStatus_Slayer_Reply_servo_pan_tag 7
#define pulczar_HwStatus_Slayer_Reply_servo_tilt_tag 8
#define pulczar_HwStatus_Slayer_Reply_target_cam_power_on_tag 9
#define pulczar_HwStatus_Reply_slayer_tag        1
#define pulczar_HwStatus_Reply_reaper_tag        2
#define pulczar_Request_reset_tag                1
#define pulczar_Request_clear_tag                2
#define pulczar_Request_gimbal_tag               3
#define pulczar_Request_dawg_tag                 4
#define pulczar_Request_laser_tag                5
#define pulczar_Request_status_tag               6
#define pulczar_Request_lens_tag                 7
#define pulczar_Request_override_tag             8
#define pulczar_Request_conf_tag                 9
#define pulczar_Request_arc_tag                  10
#define pulczar_Request_power_tag                11
#define pulczar_Request_hw_status_tag            12
#define pulczar_Request_ambient_temp_tag         13
#define pulczar_Reply_ack_tag                    1
#define pulczar_Reply_gimbal_tag                 2
#define pulczar_Reply_dawg_tag                   3
#define pulczar_Reply_laser_tag                  4
#define pulczar_Reply_status_tag                 5
#define pulczar_Reply_lens_tag                   6
#define pulczar_Reply_conf_tag                   7
#define pulczar_Reply_arc_tag                    8
#define pulczar_Reply_power_tag                  9
#define pulczar_Reply_hw_status_tag              10
#define pulczar_Reply_ambient_temp_tag           11

/* Struct field encoding specification for nanopb */
#define pulczar_AmbientTempConfig_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     enabled,           1) \
X(a, STATIC,   SINGULAR, BOOL,     use_thermistor,    2) \
X(a, STATIC,   ONEOF,    FLOAT,    (_temp,temp,_temp.temp),   3)
#define pulczar_AmbientTempConfig_CALLBACK NULL
#define pulczar_AmbientTempConfig_DEFAULT NULL

#define pulczar_AmbientTempGetStateRequest_FIELDLIST(X, a) \

#define pulczar_AmbientTempGetStateRequest_CALLBACK NULL
#define pulczar_AmbientTempGetStateRequest_DEFAULT NULL

#define pulczar_AmbientTempRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set_config,request.set_config),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_config,request.get_config),   2)
#define pulczar_AmbientTempRequest_CALLBACK NULL
#define pulczar_AmbientTempRequest_DEFAULT NULL
#define pulczar_AmbientTempRequest_request_set_config_MSGTYPE pulczar_AmbientTempConfig
#define pulczar_AmbientTempRequest_request_get_config_MSGTYPE pulczar_AmbientTempGetStateRequest

#define pulczar_AmbientTempReply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    sense_temp,        1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  config,            2)
#define pulczar_AmbientTempReply_CALLBACK NULL
#define pulczar_AmbientTempReply_DEFAULT NULL
#define pulczar_AmbientTempReply_config_MSGTYPE pulczar_AmbientTempConfig

#define pulczar_HwStatus_Request_FIELDLIST(X, a) \

#define pulczar_HwStatus_Request_CALLBACK NULL
#define pulczar_HwStatus_Request_DEFAULT NULL

#define pulczar_HwStatus_Servo_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     connected,         1) \
X(a, STATIC,   SINGULAR, UINT32,   controller_sn,     2) \
X(a, STATIC,   SINGULAR, UINT64,   sensor_time_ms,    3) \
X(a, STATIC,   SINGULAR, FLOAT,    output_stage_temp_c,   4) \
X(a, STATIC,   SINGULAR, FLOAT,    motor_supply_v,    5) \
X(a, STATIC,   SINGULAR, FLOAT,    motor_current_a,   6) \
X(a, STATIC,   SINGULAR, UINT64,   encoder_time_ms,   7) \
X(a, STATIC,   SINGULAR, INT64,    encoder_ticks,     8)
#define pulczar_HwStatus_Servo_CALLBACK NULL
#define pulczar_HwStatus_Servo_DEFAULT NULL

#define pulczar_HwStatus_Slayer_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    lpm_thermistor_beam_raw_mv,   1) \
X(a, STATIC,   SINGULAR, FLOAT,    lpm_thermistor_beam_temp_c,   2) \
X(a, STATIC,   SINGULAR, FLOAT,    lpm_thermistor_ambient_raw_mv,   3) \
X(a, STATIC,   SINGULAR, FLOAT,    lpm_thermistor_ambient_temp_c,   4) \
X(a, STATIC,   SINGULAR, BOOL,     lpsu_status,       5) \
X(a, STATIC,   SINGULAR, FLOAT,    lpsu_current_ma,   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_servo_pan,servo_pan,_servo_pan.servo_pan),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_servo_tilt,servo_tilt,_servo_tilt.servo_tilt),   8) \
X(a, STATIC,   SINGULAR, BOOL,     target_cam_power_on,   9)
#define pulczar_HwStatus_Slayer_Reply_CALLBACK NULL
#define pulczar_HwStatus_Slayer_Reply_DEFAULT NULL
#define pulczar_HwStatus_Slayer_Reply__servo_pan_servo_pan_MSGTYPE pulczar_HwStatus_Servo
#define pulczar_HwStatus_Slayer_Reply__servo_tilt_servo_tilt_MSGTYPE pulczar_HwStatus_Servo

#define pulczar_HwStatus_Reaper_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     laser_connected,   1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  laser_inventory,   2) \
X(a, STATIC,   OPTIONAL, MESSAGE,  laser_status,      3) \
X(a, STATIC,   REPEATED, FLOAT,    laser_therm_temp_c,   4) \
X(a, STATIC,   SINGULAR, FLOAT,    laser_power_w,     5) \
X(a, STATIC,   SINGULAR, FLOAT,    laser_power_raw_mv,   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_servo_pan,servo_pan,_servo_pan.servo_pan),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_servo_tilt,servo_tilt,_servo_tilt.servo_tilt),   8) \
X(a, STATIC,   SINGULAR, BOOL,     target_cam_power_on,   9)
#define pulczar_HwStatus_Reaper_Reply_CALLBACK NULL
#define pulczar_HwStatus_Reaper_Reply_DEFAULT NULL
#define pulczar_HwStatus_Reaper_Reply_laser_inventory_MSGTYPE laser_Laser_Inventory_Reply
#define pulczar_HwStatus_Reaper_Reply_laser_status_MSGTYPE laser_Diode_Status_Reply
#define pulczar_HwStatus_Reaper_Reply__servo_pan_servo_pan_MSGTYPE pulczar_HwStatus_Servo
#define pulczar_HwStatus_Reaper_Reply__servo_tilt_servo_tilt_MSGTYPE pulczar_HwStatus_Servo

#define pulczar_HwStatus_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,slayer,reply.slayer),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,reaper,reply.reaper),   2)
#define pulczar_HwStatus_Reply_CALLBACK NULL
#define pulczar_HwStatus_Reply_DEFAULT NULL
#define pulczar_HwStatus_Reply_reply_slayer_MSGTYPE pulczar_HwStatus_Slayer_Reply
#define pulczar_HwStatus_Reply_reply_reaper_MSGTYPE pulczar_HwStatus_Reaper_Reply

#define pulczar_Reset_Request_FIELDLIST(X, a) \

#define pulczar_Reset_Request_CALLBACK NULL
#define pulczar_Reset_Request_DEFAULT NULL

#define pulczar_Clear_Config_Request_FIELDLIST(X, a) \

#define pulczar_Clear_Config_Request_CALLBACK NULL
#define pulczar_Clear_Config_Request_DEFAULT NULL

#define pulczar_Status_Request_FIELDLIST(X, a) \

#define pulczar_Status_Request_CALLBACK NULL
#define pulczar_Status_Request_DEFAULT NULL

#define pulczar_Override_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   override,          1)
#define pulczar_Override_Request_CALLBACK NULL
#define pulczar_Override_Request_DEFAULT NULL

#define pulczar_Power_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    BOOL,     (_targetCam,targetCam,_targetCam.targetCam),   1) \
X(a, STATIC,   ONEOF,    BOOL,     (_firingBoard,firingBoard,_firingBoard.firingBoard),   2)
#define pulczar_Power_Request_CALLBACK NULL
#define pulczar_Power_Request_DEFAULT NULL

#define pulczar_Status_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   status,            1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  laser_status,      2)
#define pulczar_Status_Reply_CALLBACK NULL
#define pulczar_Status_Reply_DEFAULT NULL
#define pulczar_Status_Reply_laser_status_MSGTYPE laser_Laser_Status_Reply

#define pulczar_Power_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     targetCam,         1) \
X(a, STATIC,   SINGULAR, BOOL,     firingBoard,       2)
#define pulczar_Power_Reply_CALLBACK NULL
#define pulczar_Power_Reply_DEFAULT NULL

#define pulczar_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,clear,request.clear),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,gimbal,request.gimbal),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,dawg,request.dawg),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,laser,request.laser),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,status,request.status),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,lens,request.lens),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,override,request.override),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,conf,request.conf),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,arc,request.arc),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,power,request.power),  11) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,hw_status,request.hw_status),  12) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ambient_temp,request.ambient_temp),  13)
#define pulczar_Request_CALLBACK NULL
#define pulczar_Request_DEFAULT NULL
#define pulczar_Request_request_reset_MSGTYPE pulczar_Reset_Request
#define pulczar_Request_request_clear_MSGTYPE pulczar_Clear_Config_Request
#define pulczar_Request_request_gimbal_MSGTYPE gimbal_Request
#define pulczar_Request_request_dawg_MSGTYPE dawg_Request
#define pulczar_Request_request_laser_MSGTYPE laser_Request
#define pulczar_Request_request_status_MSGTYPE pulczar_Status_Request
#define pulczar_Request_request_lens_MSGTYPE lens_Request
#define pulczar_Request_request_override_MSGTYPE pulczar_Override_Request
#define pulczar_Request_request_conf_MSGTYPE scanner_config_Request
#define pulczar_Request_request_arc_MSGTYPE arc_detector_Request
#define pulczar_Request_request_power_MSGTYPE pulczar_Power_Request
#define pulczar_Request_request_hw_status_MSGTYPE pulczar_HwStatus_Request
#define pulczar_Request_request_ambient_temp_MSGTYPE pulczar_AmbientTempRequest

#define pulczar_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,gimbal,reply.gimbal),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,dawg,reply.dawg),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,laser,reply.laser),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,status,reply.status),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,lens,reply.lens),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,conf,reply.conf),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,arc,reply.arc),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,power,reply.power),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,hw_status,reply.hw_status),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ambient_temp,reply.ambient_temp),  11)
#define pulczar_Reply_CALLBACK NULL
#define pulczar_Reply_DEFAULT NULL
#define pulczar_Reply_reply_ack_MSGTYPE ack_Ack
#define pulczar_Reply_reply_gimbal_MSGTYPE gimbal_Reply
#define pulczar_Reply_reply_dawg_MSGTYPE dawg_Reply
#define pulczar_Reply_reply_laser_MSGTYPE laser_Reply
#define pulczar_Reply_reply_status_MSGTYPE pulczar_Status_Reply
#define pulczar_Reply_reply_lens_MSGTYPE lens_Reply
#define pulczar_Reply_reply_conf_MSGTYPE scanner_config_Reply
#define pulczar_Reply_reply_arc_MSGTYPE arc_detector_Reply
#define pulczar_Reply_reply_power_MSGTYPE pulczar_Power_Reply
#define pulczar_Reply_reply_hw_status_MSGTYPE pulczar_HwStatus_Reply
#define pulczar_Reply_reply_ambient_temp_MSGTYPE pulczar_AmbientTempReply

extern const pb_msgdesc_t pulczar_AmbientTempConfig_msg;
extern const pb_msgdesc_t pulczar_AmbientTempGetStateRequest_msg;
extern const pb_msgdesc_t pulczar_AmbientTempRequest_msg;
extern const pb_msgdesc_t pulczar_AmbientTempReply_msg;
extern const pb_msgdesc_t pulczar_HwStatus_Request_msg;
extern const pb_msgdesc_t pulczar_HwStatus_Servo_msg;
extern const pb_msgdesc_t pulczar_HwStatus_Slayer_Reply_msg;
extern const pb_msgdesc_t pulczar_HwStatus_Reaper_Reply_msg;
extern const pb_msgdesc_t pulczar_HwStatus_Reply_msg;
extern const pb_msgdesc_t pulczar_Reset_Request_msg;
extern const pb_msgdesc_t pulczar_Clear_Config_Request_msg;
extern const pb_msgdesc_t pulczar_Status_Request_msg;
extern const pb_msgdesc_t pulczar_Override_Request_msg;
extern const pb_msgdesc_t pulczar_Power_Request_msg;
extern const pb_msgdesc_t pulczar_Status_Reply_msg;
extern const pb_msgdesc_t pulczar_Power_Reply_msg;
extern const pb_msgdesc_t pulczar_Request_msg;
extern const pb_msgdesc_t pulczar_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define pulczar_AmbientTempConfig_fields &pulczar_AmbientTempConfig_msg
#define pulczar_AmbientTempGetStateRequest_fields &pulczar_AmbientTempGetStateRequest_msg
#define pulczar_AmbientTempRequest_fields &pulczar_AmbientTempRequest_msg
#define pulczar_AmbientTempReply_fields &pulczar_AmbientTempReply_msg
#define pulczar_HwStatus_Request_fields &pulczar_HwStatus_Request_msg
#define pulczar_HwStatus_Servo_fields &pulczar_HwStatus_Servo_msg
#define pulczar_HwStatus_Slayer_Reply_fields &pulczar_HwStatus_Slayer_Reply_msg
#define pulczar_HwStatus_Reaper_Reply_fields &pulczar_HwStatus_Reaper_Reply_msg
#define pulczar_HwStatus_Reply_fields &pulczar_HwStatus_Reply_msg
#define pulczar_Reset_Request_fields &pulczar_Reset_Request_msg
#define pulczar_Clear_Config_Request_fields &pulczar_Clear_Config_Request_msg
#define pulczar_Status_Request_fields &pulczar_Status_Request_msg
#define pulczar_Override_Request_fields &pulczar_Override_Request_msg
#define pulczar_Power_Request_fields &pulczar_Power_Request_msg
#define pulczar_Status_Reply_fields &pulczar_Status_Reply_msg
#define pulczar_Power_Reply_fields &pulczar_Power_Reply_msg
#define pulczar_Request_fields &pulczar_Request_msg
#define pulczar_Reply_fields &pulczar_Reply_msg

/* Maximum encoded size of messages (where known) */
#define pulczar_AmbientTempConfig_size           9
#define pulczar_AmbientTempGetStateRequest_size  0
#define pulczar_AmbientTempRequest_size          11
#define pulczar_AmbientTempReply_size            16
#define pulczar_HwStatus_Request_size            0
#define pulczar_HwStatus_Servo_size              56
#define pulczar_HwStatus_Slayer_Reply_size       145
#if defined(laser_Laser_Inventory_Reply_size) && defined(laser_Diode_Status_Reply_size)
#define pulczar_HwStatus_Reaper_Reply_size       (152 + laser_Laser_Inventory_Reply_size + laser_Diode_Status_Reply_size)
#endif
#if defined(laser_Laser_Inventory_Reply_size) && defined(laser_Diode_Status_Reply_size)
typedef union pulczar_HwStatus_Reply_reply_size_union {char f2[(158 + laser_Laser_Inventory_Reply_size + laser_Diode_Status_Reply_size)]; char f0[148];} pulczar_HwStatus_Reply_reply_size_union;
#define pulczar_HwStatus_Reply_size              (0 + sizeof(pulczar_HwStatus_Reply_reply_size_union))
#endif
#define pulczar_Reset_Request_size               0
#define pulczar_Clear_Config_Request_size        0
#define pulczar_Status_Request_size              0
#define pulczar_Override_Request_size            6
#define pulczar_Power_Request_size               4
#if defined(laser_Laser_Status_Reply_size)
#define pulczar_Status_Reply_size                (12 + laser_Laser_Status_Reply_size)
#endif
#define pulczar_Power_Reply_size                 4
#if defined(gimbal_Request_size) && defined(dawg_Request_size) && defined(laser_Request_size) && defined(lens_Request_size) && defined(scanner_config_Request_size) && defined(arc_detector_Request_size)
typedef union pulczar_Request_request_size_union {char f3[(6 + gimbal_Request_size)]; char f4[(6 + dawg_Request_size)]; char f5[(6 + laser_Request_size)]; char f7[(6 + lens_Request_size)]; char f9[(6 + scanner_config_Request_size)]; char f10[(6 + arc_detector_Request_size)]; char f0[13];} pulczar_Request_request_size_union;
#define pulczar_Request_size                     (0 + sizeof(pulczar_Request_request_size_union))
#endif
#if defined(ack_Ack_size) && defined(gimbal_Reply_size) && defined(dawg_Reply_size) && defined(laser_Reply_size) && defined(laser_Laser_Status_Reply_size) && defined(lens_Reply_size) && defined(scanner_config_Reply_size) && defined(arc_detector_Reply_size) && defined(laser_Laser_Inventory_Reply_size) && defined(laser_Diode_Status_Reply_size)
typedef union pulczar_Reply_reply_size_union {char f1[(6 + ack_Ack_size)]; char f2[(6 + gimbal_Reply_size)]; char f3[(6 + dawg_Reply_size)]; char f4[(6 + laser_Reply_size)]; char f5[(18 + laser_Laser_Status_Reply_size)]; char f6[(6 + lens_Reply_size)]; char f7[(6 + scanner_config_Reply_size)]; char f8[(6 + arc_detector_Reply_size)]; char f10[(6 + sizeof(pulczar_HwStatus_Reply_reply_size_union))]; char f0[18];} pulczar_Reply_reply_size_union;
#define pulczar_Reply_size                       (0 + sizeof(pulczar_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

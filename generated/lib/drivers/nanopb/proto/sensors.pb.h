/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_SENSORS_SENSORS_PB_H_INCLUDED
#define PB_SENSORS_SENSORS_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _sensors_FuelGauge_Request {
    char dummy_field;
} sensors_FuelGauge_Request;

typedef struct _sensors_FuelGauge_Reply {
    float value;
} sensors_FuelGauge_Reply;

typedef struct _sensors_Request {
    pb_size_t which_request;
    union {
        sensors_FuelGauge_Request fuel_gauge;
    } request;
} sensors_Request;

typedef struct _sensors_Reply {
    pb_size_t which_reply;
    union {
        sensors_FuelGauge_Reply fuel_gauge;
    } reply;
} sensors_Reply;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define sensors_FuelGauge_Request_init_default   {0}
#define sensors_FuelGauge_Reply_init_default     {0}
#define sensors_Request_init_default             {0, {sensors_FuelGauge_Request_init_default}}
#define sensors_Reply_init_default               {0, {sensors_FuelGauge_Reply_init_default}}
#define sensors_FuelGauge_Request_init_zero      {0}
#define sensors_FuelGauge_Reply_init_zero        {0}
#define sensors_Request_init_zero                {0, {sensors_FuelGauge_Request_init_zero}}
#define sensors_Reply_init_zero                  {0, {sensors_FuelGauge_Reply_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define sensors_FuelGauge_Reply_value_tag        1
#define sensors_Request_fuel_gauge_tag           1
#define sensors_Reply_fuel_gauge_tag             1

/* Struct field encoding specification for nanopb */
#define sensors_FuelGauge_Request_FIELDLIST(X, a) \

#define sensors_FuelGauge_Request_CALLBACK NULL
#define sensors_FuelGauge_Request_DEFAULT NULL

#define sensors_FuelGauge_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    value,             1)
#define sensors_FuelGauge_Reply_CALLBACK NULL
#define sensors_FuelGauge_Reply_DEFAULT NULL

#define sensors_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,fuel_gauge,request.fuel_gauge),   1)
#define sensors_Request_CALLBACK NULL
#define sensors_Request_DEFAULT NULL
#define sensors_Request_request_fuel_gauge_MSGTYPE sensors_FuelGauge_Request

#define sensors_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,fuel_gauge,reply.fuel_gauge),   1)
#define sensors_Reply_CALLBACK NULL
#define sensors_Reply_DEFAULT NULL
#define sensors_Reply_reply_fuel_gauge_MSGTYPE sensors_FuelGauge_Reply

extern const pb_msgdesc_t sensors_FuelGauge_Request_msg;
extern const pb_msgdesc_t sensors_FuelGauge_Reply_msg;
extern const pb_msgdesc_t sensors_Request_msg;
extern const pb_msgdesc_t sensors_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define sensors_FuelGauge_Request_fields &sensors_FuelGauge_Request_msg
#define sensors_FuelGauge_Reply_fields &sensors_FuelGauge_Reply_msg
#define sensors_Request_fields &sensors_Request_msg
#define sensors_Reply_fields &sensors_Reply_msg

/* Maximum encoded size of messages (where known) */
#define sensors_FuelGauge_Request_size           0
#define sensors_FuelGauge_Reply_size             5
#define sensors_Request_size                     2
#define sensors_Reply_size                       7

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

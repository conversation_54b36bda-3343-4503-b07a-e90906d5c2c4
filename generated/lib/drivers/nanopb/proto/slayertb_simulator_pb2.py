# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/slayertb_simulator.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import diagnostic_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2
from generated.lib.drivers.nanopb.proto import request_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/slayertb_simulator.proto',
  package='slayertb_simulator',
  syntax='proto3',
  serialized_options=b'Z\031nanopb/slayertb_simulator',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n;generated/lib/drivers/nanopb/proto/slayertb_simulator.proto\x12\x12slayertb_simulator\x1a\x33generated/lib/drivers/nanopb/proto/diagnostic.proto\x1a\x30generated/lib/drivers/nanopb/proto/request.proto\"=\n\x0fGetValueRequest\x12*\n\x06sensor\x18\x01 \x01(\x0e\x32\x1a.slayertb_simulator.Sensor\"F\n\rGetValueReply\x12\x17\n\rboolean_value\x18\x01 \x01(\x08H\x00\x12\x13\n\tint_value\x18\x02 \x01(\x05H\x00\x42\x07\n\x05value\"t\n\x0fSetValueRequest\x12*\n\x06sensor\x18\x01 \x01(\x0e\x32\x1a.slayertb_simulator.Sensor\x12\x17\n\rboolean_value\x18\x02 \x01(\x08H\x00\x12\x13\n\tint_value\x18\x03 \x01(\x05H\x00\x42\x07\n\x05value\"\x0f\n\rSetValueReply\"\"\n\x14UseTempSensorRequest\x12\n\n\x02on\x18\x01 \x01(\x08\"\x14\n\x12UseTempSensorReply\"\x8d\x02\n\x05Reply\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04pong\x18\x02 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12\x36\n\tget_value\x18\x03 \x01(\x0b\x32!.slayertb_simulator.GetValueReplyH\x00\x12\x36\n\tset_value\x18\x04 \x01(\x0b\x32!.slayertb_simulator.SetValueReplyH\x00\x12\x41\n\x0fuse_temp_sensor\x18\x05 \x01(\x0b\x32&.slayertb_simulator.UseTempSensorReplyH\x00\x42\x07\n\x05reply\"\x97\x02\n\x07Request\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12\x38\n\tget_value\x18\x03 \x01(\x0b\x32#.slayertb_simulator.GetValueRequestH\x00\x12\x38\n\tset_value\x18\x04 \x01(\x0b\x32#.slayertb_simulator.SetValueRequestH\x00\x12\x43\n\x0fuse_temp_sensor\x18\x05 \x01(\x0b\x32(.slayertb_simulator.UseTempSensorRequestH\x00\x42\t\n\x07request*\x9a\x02\n\x06Sensor\x12\n\n\x06LIFTED\x10\x00\x12\x0e\n\nLEFT_ESTOP\x10\x01\x12\x0f\n\x0bRIGHT_ESTOP\x10\x02\x12\r\n\tCAB_ESTOP\x10\x03\x12\r\n\tLASER_KEY\x10\x04\x12\x13\n\x0fLASER_INTERLOCK\x10\x05\x12\x11\n\rWATER_PROTECT\x10\x06\x12\x11\n\rTRACTOR_POWER\x10\x07\x12\x08\n\x04TEMP\x10\x08\x12\x0c\n\x08HUMIDITY\x10\t\x12\x13\n\x0f\x42\x41TTERY_VOLTAGE\x10\n\x12\x16\n\x12\x43HILLER_RUN_SIGNAL\x10\x0b\x12\x14\n\x10\x43\x41\x42_ESTOP_SIGNAL\x10\x0c\x12\x16\n\x12\x42\x45\x41\x43ON_LEFT_SIGNAL\x10\r\x12\x17\n\x13\x42\x45\x41\x43ON_RIGHT_SIGNAL\x10\x0e\x42\x1bZ\x19nanopb/slayertb_simulatorb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2.DESCRIPTOR,])

_SENSOR = _descriptor.EnumDescriptor(
  name='Sensor',
  full_name='slayertb_simulator.Sensor',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LIFTED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LEFT_ESTOP', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RIGHT_ESTOP', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAB_ESTOP', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LASER_KEY', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LASER_INTERLOCK', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='WATER_PROTECT', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TRACTOR_POWER', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TEMP', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HUMIDITY', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BATTERY_VOLTAGE', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CHILLER_RUN_SIGNAL', index=11, number=11,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAB_ESTOP_SIGNAL', index=12, number=12,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BEACON_LEFT_SIGNAL', index=13, number=13,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BEACON_RIGHT_SIGNAL', index=14, number=14,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1069,
  serialized_end=1351,
)
_sym_db.RegisterEnumDescriptor(_SENSOR)

Sensor = enum_type_wrapper.EnumTypeWrapper(_SENSOR)
LIFTED = 0
LEFT_ESTOP = 1
RIGHT_ESTOP = 2
CAB_ESTOP = 3
LASER_KEY = 4
LASER_INTERLOCK = 5
WATER_PROTECT = 6
TRACTOR_POWER = 7
TEMP = 8
HUMIDITY = 9
BATTERY_VOLTAGE = 10
CHILLER_RUN_SIGNAL = 11
CAB_ESTOP_SIGNAL = 12
BEACON_LEFT_SIGNAL = 13
BEACON_RIGHT_SIGNAL = 14



_GETVALUEREQUEST = _descriptor.Descriptor(
  name='GetValueRequest',
  full_name='slayertb_simulator.GetValueRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor', full_name='slayertb_simulator.GetValueRequest.sensor', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=186,
  serialized_end=247,
)


_GETVALUEREPLY = _descriptor.Descriptor(
  name='GetValueReply',
  full_name='slayertb_simulator.GetValueReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='boolean_value', full_name='slayertb_simulator.GetValueReply.boolean_value', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='int_value', full_name='slayertb_simulator.GetValueReply.int_value', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='slayertb_simulator.GetValueReply.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=249,
  serialized_end=319,
)


_SETVALUEREQUEST = _descriptor.Descriptor(
  name='SetValueRequest',
  full_name='slayertb_simulator.SetValueRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor', full_name='slayertb_simulator.SetValueRequest.sensor', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='boolean_value', full_name='slayertb_simulator.SetValueRequest.boolean_value', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='int_value', full_name='slayertb_simulator.SetValueRequest.int_value', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='slayertb_simulator.SetValueRequest.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=321,
  serialized_end=437,
)


_SETVALUEREPLY = _descriptor.Descriptor(
  name='SetValueReply',
  full_name='slayertb_simulator.SetValueReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=439,
  serialized_end=454,
)


_USETEMPSENSORREQUEST = _descriptor.Descriptor(
  name='UseTempSensorRequest',
  full_name='slayertb_simulator.UseTempSensorRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='on', full_name='slayertb_simulator.UseTempSensorRequest.on', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=456,
  serialized_end=490,
)


_USETEMPSENSORREPLY = _descriptor.Descriptor(
  name='UseTempSensorReply',
  full_name='slayertb_simulator.UseTempSensorReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=492,
  serialized_end=512,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='slayertb_simulator.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='slayertb_simulator.Reply.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pong', full_name='slayertb_simulator.Reply.pong', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_value', full_name='slayertb_simulator.Reply.get_value', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_value', full_name='slayertb_simulator.Reply.set_value', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_temp_sensor', full_name='slayertb_simulator.Reply.use_temp_sensor', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='slayertb_simulator.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=515,
  serialized_end=784,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='slayertb_simulator.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='slayertb_simulator.Request.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ping', full_name='slayertb_simulator.Request.ping', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_value', full_name='slayertb_simulator.Request.get_value', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_value', full_name='slayertb_simulator.Request.set_value', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_temp_sensor', full_name='slayertb_simulator.Request.use_temp_sensor', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='slayertb_simulator.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=787,
  serialized_end=1066,
)

_GETVALUEREQUEST.fields_by_name['sensor'].enum_type = _SENSOR
_GETVALUEREPLY.oneofs_by_name['value'].fields.append(
  _GETVALUEREPLY.fields_by_name['boolean_value'])
_GETVALUEREPLY.fields_by_name['boolean_value'].containing_oneof = _GETVALUEREPLY.oneofs_by_name['value']
_GETVALUEREPLY.oneofs_by_name['value'].fields.append(
  _GETVALUEREPLY.fields_by_name['int_value'])
_GETVALUEREPLY.fields_by_name['int_value'].containing_oneof = _GETVALUEREPLY.oneofs_by_name['value']
_SETVALUEREQUEST.fields_by_name['sensor'].enum_type = _SENSOR
_SETVALUEREQUEST.oneofs_by_name['value'].fields.append(
  _SETVALUEREQUEST.fields_by_name['boolean_value'])
_SETVALUEREQUEST.fields_by_name['boolean_value'].containing_oneof = _SETVALUEREQUEST.oneofs_by_name['value']
_SETVALUEREQUEST.oneofs_by_name['value'].fields.append(
  _SETVALUEREQUEST.fields_by_name['int_value'])
_SETVALUEREQUEST.fields_by_name['int_value'].containing_oneof = _SETVALUEREQUEST.oneofs_by_name['value']
_REPLY.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_REPLY.fields_by_name['get_value'].message_type = _GETVALUEREPLY
_REPLY.fields_by_name['set_value'].message_type = _SETVALUEREPLY
_REPLY.fields_by_name['use_temp_sensor'].message_type = _USETEMPSENSORREPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pong'])
_REPLY.fields_by_name['pong'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['get_value'])
_REPLY.fields_by_name['get_value'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['set_value'])
_REPLY.fields_by_name['set_value'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['use_temp_sensor'])
_REPLY.fields_by_name['use_temp_sensor'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REQUEST.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_REQUEST.fields_by_name['get_value'].message_type = _GETVALUEREQUEST
_REQUEST.fields_by_name['set_value'].message_type = _SETVALUEREQUEST
_REQUEST.fields_by_name['use_temp_sensor'].message_type = _USETEMPSENSORREQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ping'])
_REQUEST.fields_by_name['ping'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get_value'])
_REQUEST.fields_by_name['get_value'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set_value'])
_REQUEST.fields_by_name['set_value'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['use_temp_sensor'])
_REQUEST.fields_by_name['use_temp_sensor'].containing_oneof = _REQUEST.oneofs_by_name['request']
DESCRIPTOR.message_types_by_name['GetValueRequest'] = _GETVALUEREQUEST
DESCRIPTOR.message_types_by_name['GetValueReply'] = _GETVALUEREPLY
DESCRIPTOR.message_types_by_name['SetValueRequest'] = _SETVALUEREQUEST
DESCRIPTOR.message_types_by_name['SetValueReply'] = _SETVALUEREPLY
DESCRIPTOR.message_types_by_name['UseTempSensorRequest'] = _USETEMPSENSORREQUEST
DESCRIPTOR.message_types_by_name['UseTempSensorReply'] = _USETEMPSENSORREPLY
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.enum_types_by_name['Sensor'] = _SENSOR
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetValueRequest = _reflection.GeneratedProtocolMessageType('GetValueRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETVALUEREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.slayertb_simulator_pb2'
  # @@protoc_insertion_point(class_scope:slayertb_simulator.GetValueRequest)
  })
_sym_db.RegisterMessage(GetValueRequest)

GetValueReply = _reflection.GeneratedProtocolMessageType('GetValueReply', (_message.Message,), {
  'DESCRIPTOR' : _GETVALUEREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.slayertb_simulator_pb2'
  # @@protoc_insertion_point(class_scope:slayertb_simulator.GetValueReply)
  })
_sym_db.RegisterMessage(GetValueReply)

SetValueRequest = _reflection.GeneratedProtocolMessageType('SetValueRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETVALUEREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.slayertb_simulator_pb2'
  # @@protoc_insertion_point(class_scope:slayertb_simulator.SetValueRequest)
  })
_sym_db.RegisterMessage(SetValueRequest)

SetValueReply = _reflection.GeneratedProtocolMessageType('SetValueReply', (_message.Message,), {
  'DESCRIPTOR' : _SETVALUEREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.slayertb_simulator_pb2'
  # @@protoc_insertion_point(class_scope:slayertb_simulator.SetValueReply)
  })
_sym_db.RegisterMessage(SetValueReply)

UseTempSensorRequest = _reflection.GeneratedProtocolMessageType('UseTempSensorRequest', (_message.Message,), {
  'DESCRIPTOR' : _USETEMPSENSORREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.slayertb_simulator_pb2'
  # @@protoc_insertion_point(class_scope:slayertb_simulator.UseTempSensorRequest)
  })
_sym_db.RegisterMessage(UseTempSensorRequest)

UseTempSensorReply = _reflection.GeneratedProtocolMessageType('UseTempSensorReply', (_message.Message,), {
  'DESCRIPTOR' : _USETEMPSENSORREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.slayertb_simulator_pb2'
  # @@protoc_insertion_point(class_scope:slayertb_simulator.UseTempSensorReply)
  })
_sym_db.RegisterMessage(UseTempSensorReply)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.slayertb_simulator_pb2'
  # @@protoc_insertion_point(class_scope:slayertb_simulator.Reply)
  })
_sym_db.RegisterMessage(Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.slayertb_simulator_pb2'
  # @@protoc_insertion_point(class_scope:slayertb_simulator.Request)
  })
_sym_db.RegisterMessage(Request)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

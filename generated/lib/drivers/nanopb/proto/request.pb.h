/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_REQUEST_REQUEST_PB_H_INCLUDED
#define PB_REQUEST_REQUEST_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _request_RequestHeader {
    int32_t requestId;
} request_RequestHeader;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define request_RequestHeader_init_default       {0}
#define request_RequestHeader_init_zero          {0}

/* Field tags (for use in manual encoding/decoding) */
#define request_RequestHeader_requestId_tag      1

/* Struct field encoding specification for nanopb */
#define request_RequestHeader_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    requestId,         1)
#define request_RequestHeader_CALLBACK NULL
#define request_RequestHeader_DEFAULT NULL

extern const pb_msgdesc_t request_RequestHeader_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define request_RequestHeader_fields &request_RequestHeader_msg

/* Maximum encoded size of messages (where known) */
#define request_RequestHeader_size               11

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

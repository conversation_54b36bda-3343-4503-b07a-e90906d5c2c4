# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/reaper_module_controller.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2
from generated.lib.drivers.nanopb.proto import diagnostic_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2
from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import hwinfo_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2
from generated.lib.drivers.nanopb.proto import request_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2
from generated.lib.drivers.nanopb.proto import strobe_control_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_strobe__control__pb2
from generated.lib.drivers.nanopb.proto import time_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2
from generated.lib.drivers.nanopb.proto import version_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/reaper_module_controller.proto',
  package='reaper_module_controller',
  syntax='proto3',
  serialized_options=b'Z\037nanopb/reaper_module_controller',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\nAgenerated/lib/drivers/nanopb/proto/reaper_module_controller.proto\x12\x18reaper_module_controller\x1a,generated/lib/drivers/nanopb/proto/ack.proto\x1a\x33generated/lib/drivers/nanopb/proto/diagnostic.proto\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a/generated/lib/drivers/nanopb/proto/hwinfo.proto\x1a\x30generated/lib/drivers/nanopb/proto/request.proto\x1a\x37generated/lib/drivers/nanopb/proto/strobe_control.proto\x1a-generated/lib/drivers/nanopb/proto/time.proto\x1a\x30generated/lib/drivers/nanopb/proto/version.proto\"\x16\n\x14NetworkConfigRequest\"\x15\n\x13NetworkResetRequest\"\x1a\n\x18NetworkPowerCycleRequest\"\xe7\x01\n\x0eNetworkRequest\x12@\n\x06\x63onfig\x18\x01 \x01(\x0b\x32..reaper_module_controller.NetworkConfigRequestH\x00\x12>\n\x05reset\x18\x02 \x01(\x0b\x32-.reaper_module_controller.NetworkResetRequestH\x00\x12H\n\npowerCycle\x18\x03 \x01(\x0b\x32\x32.reaper_module_controller.NetworkPowerCycleRequestH\x00\x42\t\n\x07request\"\x93\x01\n\x0eNetworkAddress\x12>\n\x06source\x18\x01 \x01(\x0e\x32..reaper_module_controller.NetworkAddressSource\x12\x0f\n\x07unicast\x18\x02 \x01(\x0c\x12\x0e\n\x06subnet\x18\x03 \x01(\x0c\x12\x14\n\x07gateway\x18\x04 \x01(\x0cH\x00\x88\x01\x01\x42\n\n\x08_gateway\"n\n\x12NetworkConfigReply\x12\x0e\n\x06linkUp\x18\x01 \x01(\x08\x12\x0b\n\x03mac\x18\x02 \x01(\x0c\x12;\n\taddresses\x18\x03 \x03(\x0b\x32(.reaper_module_controller.NetworkAddress\"W\n\x0cNetworkReply\x12>\n\x06\x63onfig\x18\x01 \x01(\x0b\x32,.reaper_module_controller.NetworkConfigReplyH\x00\x42\x07\n\x05reply\"z\n\rScannerStatus\x12\x14\n\x0cpowerEnabled\x18\x01 \x01(\x08\x12\x11\n\tfuseBlown\x18\x02 \x01(\x08\x12\x15\n\rfuseTimestamp\x18\x03 \x01(\x04\x12\x0f\n\x07\x63urrent\x18\x04 \x01(\x02\x12\x18\n\x10\x63urrentTimestamp\x18\x05 \x01(\x04\"\x19\n\x17ScannerGetStatusRequest\"`\n\x16ScannerSetPowerRequest\x12\x15\n\x08scannerA\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x15\n\x08scannerB\x18\x02 \x01(\x08H\x01\x88\x01\x01\x42\x0b\n\t_scannerAB\x0b\n\t_scannerB\"`\n\x16ScannerResetOcpRequest\x12\x15\n\x08scannerA\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x15\n\x08scannerB\x18\x02 \x01(\x08H\x01\x88\x01\x01\x42\x0b\n\t_scannerAB\x0b\n\t_scannerB\"\xe4\x01\n\x0eScannerRequest\x12\x43\n\x06status\x18\x01 \x01(\x0b\x32\x31.reaper_module_controller.ScannerGetStatusRequestH\x00\x12\x41\n\x05power\x18\x02 \x01(\x0b\x32\x30.reaper_module_controller.ScannerSetPowerRequestH\x00\x12?\n\x03ocp\x18\x03 \x01(\x0b\x32\x30.reaper_module_controller.ScannerResetOcpRequestH\x00\x42\t\n\x07request\"K\n\x12ScannerStatusReply\x12\x35\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\'.reaper_module_controller.ScannerStatus\"W\n\x0cScannerReply\x12>\n\x06status\x18\x01 \x01(\x0b\x32,.reaper_module_controller.ScannerStatusReplyH\x00\x42\x07\n\x05reply\"\xae\x01\n\x0cPowerRequest\x12\x17\n\nrelayBoard\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x18\n\x0bstrobeBoard\x18\x02 \x01(\x08H\x01\x88\x01\x01\x12\x16\n\tethSwitch\x18\x03 \x01(\x08H\x02\x88\x01\x01\x12\x17\n\npredictCam\x18\x04 \x01(\x08H\x03\x88\x01\x01\x42\r\n\x0b_relayBoardB\x0e\n\x0c_strobeBoardB\x0c\n\n_ethSwitchB\r\n\x0b_predictCam\"\\\n\nPowerReply\x12\x12\n\nrelayBoard\x18\x01 \x01(\x08\x12\x13\n\x0bstrobeBoard\x18\x02 \x01(\x08\x12\x11\n\tethSwitch\x18\x03 \x01(\x08\x12\x12\n\npredictCam\x18\x04 \x01(\x08\"\x15\n\x13StrobeStatusRequest\"(\n\x15SetStrobeStateRequest\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"1\n\x19TimedStrobeDisableRequest\x12\x14\n\x0c\x64urationMsec\x18\x01 \x01(\r\"\xce\x02\n\x11StrobeStatusReply\x12\x0f\n\x07voltage\x18\x01 \x01(\x02\x12\x18\n\x10voltageTimestamp\x18\x02 \x01(\x04\x12\x0f\n\x07\x63urrent\x18\x03 \x01(\x02\x12\x18\n\x10\x63urrentTimestamp\x18\x04 \x01(\x04\x12\x13\n\x0btemperature\x18\x05 \x01(\x02\x12\x1c\n\x14temperatureTimestamp\x18\x06 \x01(\x04\x12\r\n\x05ready\x18\x07 \x01(\x08\x12\x0f\n\x07\x65nabled\x18\x08 \x01(\x08\x12\x0e\n\x06\x66iring\x18\t \x01(\x08\x12\x17\n\nexposureUs\x18\n \x01(\rH\x00\x88\x01\x01\x12\x15\n\x08periodUs\x18\x0b \x01(\rH\x01\x88\x01\x01\x12\x1e\n\x11targetsPerPredict\x18\x0c \x01(\rH\x02\x88\x01\x01\x42\r\n\x0b_exposureUsB\x0b\n\t_periodUsB\x14\n\x12_targetsPerPredict\"\xa0\x02\n\rStrobeRequest\x12.\n\x0bsetWaveform\x18\x01 \x01(\x0b\x32\x17.strobe_control.RequestH\x00\x12\x42\n\tgetStatus\x18\x02 \x01(\x0b\x32-.reaper_module_controller.StrobeStatusRequestH\x00\x12\x43\n\x08setState\x18\x03 \x01(\x0b\x32/.reaper_module_controller.SetStrobeStateRequestH\x00\x12K\n\x0ctimedDisable\x18\x04 \x01(\x0b\x32\x33.reaper_module_controller.TimedStrobeDisableRequestH\x00\x42\t\n\x07request\"U\n\x0bStrobeReply\x12=\n\x06status\x18\x01 \x01(\x0b\x32+.reaper_module_controller.StrobeStatusReplyH\x00\x42\x07\n\x05reply\" \n\x0eModuleIdentity\x12\x0e\n\x06number\x18\x02 \x01(\r\"\x1a\n\x18GetModuleIdentityRequest\".\n\x11SetOtpLockRequest\x12\x0c\n\x04lock\x18\x01 \x01(\x08\x12\x0b\n\x03key\x18\x02 \x01(\x07\"O\n\x11SetBoardIdRequest\x12\x11\n\x04\x63\x62sn\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x13\n\x06\x61ssySn\x18\x02 \x01(\tH\x01\x88\x01\x01\x42\x07\n\x05_cbsnB\t\n\x07_assySn\"\xaf\x02\n\rConfigRequest\x12I\n\x0bgetIdentity\x18\x01 \x01(\x0b\x32\x32.reaper_module_controller.GetModuleIdentityRequestH\x00\x12?\n\x0bsetIdentity\x18\x02 \x01(\x0b\x32(.reaper_module_controller.ModuleIdentityH\x00\x12>\n\x07otpLock\x18\x03 \x01(\x0b\x32+.reaper_module_controller.SetOtpLockRequestH\x00\x12G\n\x10setBoardIdentity\x18\x04 \x01(\x0b\x32+.reaper_module_controller.SetBoardIdRequestH\x00\x42\t\n\x07request\"T\n\x0b\x43onfigReply\x12<\n\x08identity\x18\x01 \x01(\x0b\x32(.reaper_module_controller.ModuleIdentityH\x00\x42\x07\n\x05reply\"t\n\x10ThermostatConfig\x12\x10\n\x08setpoint\x18\x01 \x01(\x02\x12\x12\n\nhysteresis\x18\x02 \x01(\x02\x12:\n\x06source\x18\x03 \x01(\x0e\x32*.reaper_module_controller.ThermostatSource\"G\n\rFanSetRequest\x12\x11\n\x04\x66\x61n1\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x11\n\x04\x66\x61n2\x18\x02 \x01(\x08H\x01\x88\x01\x01\x42\x07\n\x05_fan1B\x07\n\x05_fan2\"\x8a\x01\n\x1a\x46\x61nThermostatConfigRequest\x12\x14\n\x07\x65nabled\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12?\n\x06\x63onfig\x18\x02 \x01(\x0b\x32*.reaper_module_controller.ThermostatConfigH\x01\x88\x01\x01\x42\n\n\x08_enabledB\t\n\x07_config\"\x9d\x01\n\nFanRequest\x12\x36\n\x03set\x18\x01 \x01(\x0b\x32\'.reaper_module_controller.FanSetRequestH\x00\x12L\n\x0cthermoConfig\x18\x02 \x01(\x0b\x32\x34.reaper_module_controller.FanThermostatConfigRequestH\x00\x42\t\n\x07request\"\xd5\x01\n\x08\x46\x61nReply\x12\x0c\n\x04\x66\x61n1\x18\x01 \x01(\x08\x12\x0c\n\x04\x66\x61n2\x18\x02 \x01(\x08\x12\x19\n\x11thermostatEnabled\x18\x03 \x01(\x08\x12I\n\x10thermostatConfig\x18\x04 \x01(\x0b\x32*.reaper_module_controller.ThermostatConfigH\x00\x88\x01\x01\x12\x1d\n\x10thermostatActual\x18\x05 \x01(\x02H\x01\x88\x01\x01\x42\x13\n\x11_thermostatConfigB\x13\n\x11_thermostatActual\"^\n\x0cRelayRequest\x12\x0f\n\x02pc\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x10\n\x03\x62tl\x18\x02 \x01(\x08H\x01\x88\x01\x01\x12\x12\n\x05laser\x18\x03 \x01(\x08H\x02\x88\x01\x01\x42\x05\n\x03_pcB\x06\n\x04_btlB\x08\n\x06_laser\"4\n\nRelayReply\x12\n\n\x02pc\x18\x01 \x01(\x08\x12\x0b\n\x03\x62tl\x18\x02 \x01(\x08\x12\r\n\x05laser\x18\x03 \x01(\x08\"\x0f\n\rSensorRequest\"\x8a\x05\n\x0bSensorReply\x12:\n\x03\x65nv\x18\x01 \x03(\x0b\x32-.reaper_module_controller.SensorReply.envdata\x12:\n\x03imu\x18\x02 \x01(\x0b\x32-.reaper_module_controller.SensorReply.imudata\x12>\n\x05therm\x18\x03 \x03(\x0b\x32/.reaper_module_controller.SensorReply.thermdata\x12<\n\x04leak\x18\x04 \x03(\x0b\x32..reaper_module_controller.SensorReply.leakdata\x12>\n\x05press\x18\x05 \x03(\x0b\x32/.reaper_module_controller.SensorReply.pressdata\x12\x16\n\x0ehasExternalEnv\x18\x06 \x01(\x08\x1aN\n\x07\x65nvdata\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0c\n\x04temp\x18\x02 \x01(\x02\x12\x10\n\x08humidity\x18\x03 \x01(\x02\x12\x10\n\x08pressure\x18\x04 \x01(\x02\x1a\x39\n\x07imudata\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\r\n\x05\x61\x63\x63\x65l\x18\x02 \x03(\x02\x12\x0c\n\x04gyro\x18\x03 \x03(\x02\x1a,\n\tthermdata\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0c\n\x04temp\x18\x02 \x01(\x02\x1a-\n\x08leakdata\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tive\x18\x02 \x01(\x08\x1a\x45\n\tpressdata\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x10\n\x08pressure\x18\x02 \x01(\x02\x12\x13\n\x0btemperature\x18\x03 \x01(\x02\"\x0f\n\rStatusRequest\"\xa8\x02\n\x0bStatusReply\x12\x36\n\x07sensors\x18\x01 \x01(\x0b\x32%.reaper_module_controller.SensorReply\x12\x34\n\x06relays\x18\x02 \x01(\x0b\x32$.reaper_module_controller.RelayReply\x12\x33\n\x05power\x18\x03 \x01(\x0b\x32$.reaper_module_controller.PowerReply\x12;\n\x06strobe\x18\x04 \x01(\x0b\x32+.reaper_module_controller.StrobeStatusReply\x12\x39\n\x08scanners\x18\x05 \x03(\x0b\x32\'.reaper_module_controller.ScannerStatus\"\x0f\n\rCoreDumpStart\"\r\n\x0b\x43oreDumpEnd\"=\n\x0c\x43oreDumpData\x12\x0f\n\x07is_last\x18\x01 \x01(\x08\x12\x0e\n\x06offset\x18\x02 \x01(\r\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\x0c\"\xc2\x01\n\rCoreDumpReply\x12\x38\n\x05start\x18\x01 \x01(\x0b\x32\'.reaper_module_controller.CoreDumpStartH\x00\x12\x34\n\x03\x65nd\x18\x02 \x01(\x0b\x32%.reaper_module_controller.CoreDumpEndH\x00\x12\x36\n\x04\x64\x61ta\x18\x03 \x01(\x0b\x32&.reaper_module_controller.CoreDumpDataH\x00\x42\t\n\x07payload\"\x86\x06\n\nUdpRequest\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12+\n\x07version\x18\x03 \x01(\x0b\x32\x18.version.Version_RequestH\x00\x12\'\n\x05reset\x18\x04 \x01(\x0b\x32\x16.version.Reset_RequestH\x00\x12\x1d\n\x04time\x18\x05 \x01(\x0b\x32\r.time.RequestH\x00\x12\x39\n\x06sensor\x18\x06 \x01(\x0b\x32\'.reaper_module_controller.SensorRequestH\x00\x12\x37\n\x05relay\x18\x07 \x01(\x0b\x32&.reaper_module_controller.RelayRequestH\x00\x12\x33\n\x03\x66\x61n\x18\x08 \x01(\x0b\x32$.reaper_module_controller.FanRequestH\x00\x12\x39\n\x06\x63onfig\x18\t \x01(\x0b\x32\'.reaper_module_controller.ConfigRequestH\x00\x12\x39\n\x06strobe\x18\n \x01(\x0b\x32\'.reaper_module_controller.StrobeRequestH\x00\x12\x37\n\x05power\x18\x0b \x01(\x0b\x32&.reaper_module_controller.PowerRequestH\x00\x12;\n\x07scanner\x18\x0c \x01(\x0b\x32(.reaper_module_controller.ScannerRequestH\x00\x12;\n\x07network\x18\r \x01(\x0b\x32(.reaper_module_controller.NetworkRequestH\x00\x12!\n\x06hwinfo\x18\x0e \x01(\x0b\x32\x0f.hwinfo.RequestH\x00\x12\x39\n\x06status\x18\x0f \x01(\x0b\x32\'.reaper_module_controller.StatusRequestH\x00\x42\t\n\x07request\"\xf9\x05\n\x08UdpReply\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12\x1d\n\x05\x65rror\x18\x02 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x03 \x01(\x0b\x32\x08.ack.AckH\x00\x12 \n\x04pong\x18\x04 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12)\n\x07version\x18\x05 \x01(\x0b\x32\x16.version.Version_ReplyH\x00\x12\x1b\n\x04time\x18\x06 \x01(\x0b\x32\x0b.time.ReplyH\x00\x12\x37\n\x06sensor\x18\x07 \x01(\x0b\x32%.reaper_module_controller.SensorReplyH\x00\x12\x35\n\x05relay\x18\x08 \x01(\x0b\x32$.reaper_module_controller.RelayReplyH\x00\x12\x31\n\x03\x66\x61n\x18\t \x01(\x0b\x32\".reaper_module_controller.FanReplyH\x00\x12\x37\n\x06\x63onfig\x18\n \x01(\x0b\x32%.reaper_module_controller.ConfigReplyH\x00\x12\x37\n\x06strobe\x18\x0b \x01(\x0b\x32%.reaper_module_controller.StrobeReplyH\x00\x12\x35\n\x05power\x18\x0c \x01(\x0b\x32$.reaper_module_controller.PowerReplyH\x00\x12\x39\n\x07scanner\x18\r \x01(\x0b\x32&.reaper_module_controller.ScannerReplyH\x00\x12\x39\n\x07network\x18\x0e \x01(\x0b\x32&.reaper_module_controller.NetworkReplyH\x00\x12\x1f\n\x06hwinfo\x18\x0f \x01(\x0b\x32\r.hwinfo.ReplyH\x00\x12\x37\n\x06status\x18\x10 \x01(\x0b\x32%.reaper_module_controller.StatusReplyH\x00\x42\x07\n\x05reply\"\xff\x03\n\nOobRequest\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12+\n\x07version\x18\x03 \x01(\x0b\x32\x18.version.Version_RequestH\x00\x12\'\n\x05reset\x18\x04 \x01(\x0b\x32\x16.version.Reset_RequestH\x00\x12\x39\n\x06\x63onfig\x18\x05 \x01(\x0b\x32\'.reaper_module_controller.ConfigRequestH\x00\x12;\n\x07network\x18\x06 \x01(\x0b\x32(.reaper_module_controller.NetworkRequestH\x00\x12!\n\x06hwinfo\x18\x07 \x01(\x0b\x32\x0f.hwinfo.RequestH\x00\x12\x39\n\x06strobe\x18\x08 \x01(\x0b\x32\'.reaper_module_controller.StrobeRequestH\x00\x12\x37\n\x05power\x18\t \x01(\x0b\x32&.reaper_module_controller.PowerRequestH\x00\x12\x37\n\x05relay\x18\n \x01(\x0b\x32&.reaper_module_controller.RelayRequestH\x00\x42\t\n\x07request\"\xb9\x04\n\x08OobReply\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12\x1d\n\x05\x65rror\x18\x02 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x03 \x01(\x0b\x32\x08.ack.AckH\x00\x12 \n\x04pong\x18\x04 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12)\n\x07version\x18\x05 \x01(\x0b\x32\x16.version.Version_ReplyH\x00\x12\x37\n\x06\x63onfig\x18\x06 \x01(\x0b\x32%.reaper_module_controller.ConfigReplyH\x00\x12\x39\n\x07network\x18\x07 \x01(\x0b\x32&.reaper_module_controller.NetworkReplyH\x00\x12\x1f\n\x06hwinfo\x18\x08 \x01(\x0b\x32\r.hwinfo.ReplyH\x00\x12\x37\n\x06strobe\x18\t \x01(\x0b\x32%.reaper_module_controller.StrobeReplyH\x00\x12\x35\n\x05power\x18\n \x01(\x0b\x32$.reaper_module_controller.PowerReplyH\x00\x12\x35\n\x05relay\x18\x0b \x01(\x0b\x32$.reaper_module_controller.RelayReplyH\x00\x12;\n\x08\x63oredump\x18\x0c \x01(\x0b\x32\'.reaper_module_controller.CoreDumpReplyH\x00\x42\x07\n\x05reply*8\n\x14NetworkAddressSource\x12\n\n\x06MANUAL\x10\x00\x12\n\n\x06STATIC\x10\x01\x12\x08\n\x04\x44HCP\x10\x02*I\n\x10ThermostatSource\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x13\n\x0f\x45NVIRO_INTERNAL\x10\x01\x12\x13\n\x0f\x45NVIRO_EXTERNAL\x10\x02\x42!Z\x1fnanopb/reaper_module_controllerb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_strobe__control__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2.DESCRIPTOR,])

_NETWORKADDRESSSOURCE = _descriptor.EnumDescriptor(
  name='NetworkAddressSource',
  full_name='reaper_module_controller.NetworkAddressSource',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='MANUAL', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATIC', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DHCP', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=8347,
  serialized_end=8403,
)
_sym_db.RegisterEnumDescriptor(_NETWORKADDRESSSOURCE)

NetworkAddressSource = enum_type_wrapper.EnumTypeWrapper(_NETWORKADDRESSSOURCE)
_THERMOSTATSOURCE = _descriptor.EnumDescriptor(
  name='ThermostatSource',
  full_name='reaper_module_controller.ThermostatSource',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DEFAULT', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ENVIRO_INTERNAL', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ENVIRO_EXTERNAL', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=8405,
  serialized_end=8478,
)
_sym_db.RegisterEnumDescriptor(_THERMOSTATSOURCE)

ThermostatSource = enum_type_wrapper.EnumTypeWrapper(_THERMOSTATSOURCE)
MANUAL = 0
STATIC = 1
DHCP = 2
DEFAULT = 0
ENVIRO_INTERNAL = 1
ENVIRO_EXTERNAL = 2



_NETWORKCONFIGREQUEST = _descriptor.Descriptor(
  name='NetworkConfigRequest',
  full_name='reaper_module_controller.NetworkConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=495,
  serialized_end=517,
)


_NETWORKRESETREQUEST = _descriptor.Descriptor(
  name='NetworkResetRequest',
  full_name='reaper_module_controller.NetworkResetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=519,
  serialized_end=540,
)


_NETWORKPOWERCYCLEREQUEST = _descriptor.Descriptor(
  name='NetworkPowerCycleRequest',
  full_name='reaper_module_controller.NetworkPowerCycleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=542,
  serialized_end=568,
)


_NETWORKREQUEST = _descriptor.Descriptor(
  name='NetworkRequest',
  full_name='reaper_module_controller.NetworkRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='reaper_module_controller.NetworkRequest.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reset', full_name='reaper_module_controller.NetworkRequest.reset', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='powerCycle', full_name='reaper_module_controller.NetworkRequest.powerCycle', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='reaper_module_controller.NetworkRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=571,
  serialized_end=802,
)


_NETWORKADDRESS = _descriptor.Descriptor(
  name='NetworkAddress',
  full_name='reaper_module_controller.NetworkAddress',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='source', full_name='reaper_module_controller.NetworkAddress.source', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unicast', full_name='reaper_module_controller.NetworkAddress.unicast', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='subnet', full_name='reaper_module_controller.NetworkAddress.subnet', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gateway', full_name='reaper_module_controller.NetworkAddress.gateway', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_gateway', full_name='reaper_module_controller.NetworkAddress._gateway',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=805,
  serialized_end=952,
)


_NETWORKCONFIGREPLY = _descriptor.Descriptor(
  name='NetworkConfigReply',
  full_name='reaper_module_controller.NetworkConfigReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='linkUp', full_name='reaper_module_controller.NetworkConfigReply.linkUp', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mac', full_name='reaper_module_controller.NetworkConfigReply.mac', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='addresses', full_name='reaper_module_controller.NetworkConfigReply.addresses', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=954,
  serialized_end=1064,
)


_NETWORKREPLY = _descriptor.Descriptor(
  name='NetworkReply',
  full_name='reaper_module_controller.NetworkReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='reaper_module_controller.NetworkReply.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='reaper_module_controller.NetworkReply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1066,
  serialized_end=1153,
)


_SCANNERSTATUS = _descriptor.Descriptor(
  name='ScannerStatus',
  full_name='reaper_module_controller.ScannerStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='powerEnabled', full_name='reaper_module_controller.ScannerStatus.powerEnabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fuseBlown', full_name='reaper_module_controller.ScannerStatus.fuseBlown', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fuseTimestamp', full_name='reaper_module_controller.ScannerStatus.fuseTimestamp', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current', full_name='reaper_module_controller.ScannerStatus.current', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='currentTimestamp', full_name='reaper_module_controller.ScannerStatus.currentTimestamp', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1155,
  serialized_end=1277,
)


_SCANNERGETSTATUSREQUEST = _descriptor.Descriptor(
  name='ScannerGetStatusRequest',
  full_name='reaper_module_controller.ScannerGetStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1279,
  serialized_end=1304,
)


_SCANNERSETPOWERREQUEST = _descriptor.Descriptor(
  name='ScannerSetPowerRequest',
  full_name='reaper_module_controller.ScannerSetPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scannerA', full_name='reaper_module_controller.ScannerSetPowerRequest.scannerA', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scannerB', full_name='reaper_module_controller.ScannerSetPowerRequest.scannerB', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_scannerA', full_name='reaper_module_controller.ScannerSetPowerRequest._scannerA',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_scannerB', full_name='reaper_module_controller.ScannerSetPowerRequest._scannerB',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1306,
  serialized_end=1402,
)


_SCANNERRESETOCPREQUEST = _descriptor.Descriptor(
  name='ScannerResetOcpRequest',
  full_name='reaper_module_controller.ScannerResetOcpRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scannerA', full_name='reaper_module_controller.ScannerResetOcpRequest.scannerA', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scannerB', full_name='reaper_module_controller.ScannerResetOcpRequest.scannerB', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_scannerA', full_name='reaper_module_controller.ScannerResetOcpRequest._scannerA',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_scannerB', full_name='reaper_module_controller.ScannerResetOcpRequest._scannerB',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1404,
  serialized_end=1500,
)


_SCANNERREQUEST = _descriptor.Descriptor(
  name='ScannerRequest',
  full_name='reaper_module_controller.ScannerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='reaper_module_controller.ScannerRequest.status', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='reaper_module_controller.ScannerRequest.power', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ocp', full_name='reaper_module_controller.ScannerRequest.ocp', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='reaper_module_controller.ScannerRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1503,
  serialized_end=1731,
)


_SCANNERSTATUSREPLY = _descriptor.Descriptor(
  name='ScannerStatusReply',
  full_name='reaper_module_controller.ScannerStatusReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='reaper_module_controller.ScannerStatusReply.data', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1733,
  serialized_end=1808,
)


_SCANNERREPLY = _descriptor.Descriptor(
  name='ScannerReply',
  full_name='reaper_module_controller.ScannerReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='reaper_module_controller.ScannerReply.status', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='reaper_module_controller.ScannerReply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1810,
  serialized_end=1897,
)


_POWERREQUEST = _descriptor.Descriptor(
  name='PowerRequest',
  full_name='reaper_module_controller.PowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='relayBoard', full_name='reaper_module_controller.PowerRequest.relayBoard', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobeBoard', full_name='reaper_module_controller.PowerRequest.strobeBoard', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ethSwitch', full_name='reaper_module_controller.PowerRequest.ethSwitch', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predictCam', full_name='reaper_module_controller.PowerRequest.predictCam', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_relayBoard', full_name='reaper_module_controller.PowerRequest._relayBoard',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_strobeBoard', full_name='reaper_module_controller.PowerRequest._strobeBoard',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_ethSwitch', full_name='reaper_module_controller.PowerRequest._ethSwitch',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_predictCam', full_name='reaper_module_controller.PowerRequest._predictCam',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1900,
  serialized_end=2074,
)


_POWERREPLY = _descriptor.Descriptor(
  name='PowerReply',
  full_name='reaper_module_controller.PowerReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='relayBoard', full_name='reaper_module_controller.PowerReply.relayBoard', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobeBoard', full_name='reaper_module_controller.PowerReply.strobeBoard', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ethSwitch', full_name='reaper_module_controller.PowerReply.ethSwitch', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predictCam', full_name='reaper_module_controller.PowerReply.predictCam', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2076,
  serialized_end=2168,
)


_STROBESTATUSREQUEST = _descriptor.Descriptor(
  name='StrobeStatusRequest',
  full_name='reaper_module_controller.StrobeStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2170,
  serialized_end=2191,
)


_SETSTROBESTATEREQUEST = _descriptor.Descriptor(
  name='SetStrobeStateRequest',
  full_name='reaper_module_controller.SetStrobeStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='reaper_module_controller.SetStrobeStateRequest.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2193,
  serialized_end=2233,
)


_TIMEDSTROBEDISABLEREQUEST = _descriptor.Descriptor(
  name='TimedStrobeDisableRequest',
  full_name='reaper_module_controller.TimedStrobeDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='durationMsec', full_name='reaper_module_controller.TimedStrobeDisableRequest.durationMsec', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2235,
  serialized_end=2284,
)


_STROBESTATUSREPLY = _descriptor.Descriptor(
  name='StrobeStatusReply',
  full_name='reaper_module_controller.StrobeStatusReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='voltage', full_name='reaper_module_controller.StrobeStatusReply.voltage', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='voltageTimestamp', full_name='reaper_module_controller.StrobeStatusReply.voltageTimestamp', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current', full_name='reaper_module_controller.StrobeStatusReply.current', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='currentTimestamp', full_name='reaper_module_controller.StrobeStatusReply.currentTimestamp', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature', full_name='reaper_module_controller.StrobeStatusReply.temperature', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperatureTimestamp', full_name='reaper_module_controller.StrobeStatusReply.temperatureTimestamp', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ready', full_name='reaper_module_controller.StrobeStatusReply.ready', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='reaper_module_controller.StrobeStatusReply.enabled', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='firing', full_name='reaper_module_controller.StrobeStatusReply.firing', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exposureUs', full_name='reaper_module_controller.StrobeStatusReply.exposureUs', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='periodUs', full_name='reaper_module_controller.StrobeStatusReply.periodUs', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='targetsPerPredict', full_name='reaper_module_controller.StrobeStatusReply.targetsPerPredict', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_exposureUs', full_name='reaper_module_controller.StrobeStatusReply._exposureUs',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_periodUs', full_name='reaper_module_controller.StrobeStatusReply._periodUs',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_targetsPerPredict', full_name='reaper_module_controller.StrobeStatusReply._targetsPerPredict',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2287,
  serialized_end=2621,
)


_STROBEREQUEST = _descriptor.Descriptor(
  name='StrobeRequest',
  full_name='reaper_module_controller.StrobeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='setWaveform', full_name='reaper_module_controller.StrobeRequest.setWaveform', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='getStatus', full_name='reaper_module_controller.StrobeRequest.getStatus', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='setState', full_name='reaper_module_controller.StrobeRequest.setState', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timedDisable', full_name='reaper_module_controller.StrobeRequest.timedDisable', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='reaper_module_controller.StrobeRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2624,
  serialized_end=2912,
)


_STROBEREPLY = _descriptor.Descriptor(
  name='StrobeReply',
  full_name='reaper_module_controller.StrobeReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='reaper_module_controller.StrobeReply.status', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='reaper_module_controller.StrobeReply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2914,
  serialized_end=2999,
)


_MODULEIDENTITY = _descriptor.Descriptor(
  name='ModuleIdentity',
  full_name='reaper_module_controller.ModuleIdentity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='number', full_name='reaper_module_controller.ModuleIdentity.number', index=0,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3001,
  serialized_end=3033,
)


_GETMODULEIDENTITYREQUEST = _descriptor.Descriptor(
  name='GetModuleIdentityRequest',
  full_name='reaper_module_controller.GetModuleIdentityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3035,
  serialized_end=3061,
)


_SETOTPLOCKREQUEST = _descriptor.Descriptor(
  name='SetOtpLockRequest',
  full_name='reaper_module_controller.SetOtpLockRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lock', full_name='reaper_module_controller.SetOtpLockRequest.lock', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='key', full_name='reaper_module_controller.SetOtpLockRequest.key', index=1,
      number=2, type=7, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3063,
  serialized_end=3109,
)


_SETBOARDIDREQUEST = _descriptor.Descriptor(
  name='SetBoardIdRequest',
  full_name='reaper_module_controller.SetBoardIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cbsn', full_name='reaper_module_controller.SetBoardIdRequest.cbsn', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='assySn', full_name='reaper_module_controller.SetBoardIdRequest.assySn', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_cbsn', full_name='reaper_module_controller.SetBoardIdRequest._cbsn',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_assySn', full_name='reaper_module_controller.SetBoardIdRequest._assySn',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3111,
  serialized_end=3190,
)


_CONFIGREQUEST = _descriptor.Descriptor(
  name='ConfigRequest',
  full_name='reaper_module_controller.ConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='getIdentity', full_name='reaper_module_controller.ConfigRequest.getIdentity', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='setIdentity', full_name='reaper_module_controller.ConfigRequest.setIdentity', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='otpLock', full_name='reaper_module_controller.ConfigRequest.otpLock', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='setBoardIdentity', full_name='reaper_module_controller.ConfigRequest.setBoardIdentity', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='reaper_module_controller.ConfigRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3193,
  serialized_end=3496,
)


_CONFIGREPLY = _descriptor.Descriptor(
  name='ConfigReply',
  full_name='reaper_module_controller.ConfigReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='identity', full_name='reaper_module_controller.ConfigReply.identity', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='reaper_module_controller.ConfigReply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3498,
  serialized_end=3582,
)


_THERMOSTATCONFIG = _descriptor.Descriptor(
  name='ThermostatConfig',
  full_name='reaper_module_controller.ThermostatConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='setpoint', full_name='reaper_module_controller.ThermostatConfig.setpoint', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hysteresis', full_name='reaper_module_controller.ThermostatConfig.hysteresis', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='source', full_name='reaper_module_controller.ThermostatConfig.source', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3584,
  serialized_end=3700,
)


_FANSETREQUEST = _descriptor.Descriptor(
  name='FanSetRequest',
  full_name='reaper_module_controller.FanSetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fan1', full_name='reaper_module_controller.FanSetRequest.fan1', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fan2', full_name='reaper_module_controller.FanSetRequest.fan2', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_fan1', full_name='reaper_module_controller.FanSetRequest._fan1',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_fan2', full_name='reaper_module_controller.FanSetRequest._fan2',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3702,
  serialized_end=3773,
)


_FANTHERMOSTATCONFIGREQUEST = _descriptor.Descriptor(
  name='FanThermostatConfigRequest',
  full_name='reaper_module_controller.FanThermostatConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='reaper_module_controller.FanThermostatConfigRequest.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='reaper_module_controller.FanThermostatConfigRequest.config', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_enabled', full_name='reaper_module_controller.FanThermostatConfigRequest._enabled',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_config', full_name='reaper_module_controller.FanThermostatConfigRequest._config',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3776,
  serialized_end=3914,
)


_FANREQUEST = _descriptor.Descriptor(
  name='FanRequest',
  full_name='reaper_module_controller.FanRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='set', full_name='reaper_module_controller.FanRequest.set', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thermoConfig', full_name='reaper_module_controller.FanRequest.thermoConfig', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='reaper_module_controller.FanRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3917,
  serialized_end=4074,
)


_FANREPLY = _descriptor.Descriptor(
  name='FanReply',
  full_name='reaper_module_controller.FanReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fan1', full_name='reaper_module_controller.FanReply.fan1', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fan2', full_name='reaper_module_controller.FanReply.fan2', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thermostatEnabled', full_name='reaper_module_controller.FanReply.thermostatEnabled', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thermostatConfig', full_name='reaper_module_controller.FanReply.thermostatConfig', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thermostatActual', full_name='reaper_module_controller.FanReply.thermostatActual', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_thermostatConfig', full_name='reaper_module_controller.FanReply._thermostatConfig',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_thermostatActual', full_name='reaper_module_controller.FanReply._thermostatActual',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=4077,
  serialized_end=4290,
)


_RELAYREQUEST = _descriptor.Descriptor(
  name='RelayRequest',
  full_name='reaper_module_controller.RelayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pc', full_name='reaper_module_controller.RelayRequest.pc', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='btl', full_name='reaper_module_controller.RelayRequest.btl', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser', full_name='reaper_module_controller.RelayRequest.laser', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_pc', full_name='reaper_module_controller.RelayRequest._pc',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_btl', full_name='reaper_module_controller.RelayRequest._btl',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_laser', full_name='reaper_module_controller.RelayRequest._laser',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=4292,
  serialized_end=4386,
)


_RELAYREPLY = _descriptor.Descriptor(
  name='RelayReply',
  full_name='reaper_module_controller.RelayReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pc', full_name='reaper_module_controller.RelayReply.pc', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='btl', full_name='reaper_module_controller.RelayReply.btl', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser', full_name='reaper_module_controller.RelayReply.laser', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4388,
  serialized_end=4440,
)


_SENSORREQUEST = _descriptor.Descriptor(
  name='SensorRequest',
  full_name='reaper_module_controller.SensorRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4442,
  serialized_end=4457,
)


_SENSORREPLY_ENVDATA = _descriptor.Descriptor(
  name='envdata',
  full_name='reaper_module_controller.SensorReply.envdata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='reaper_module_controller.SensorReply.envdata.timestamp', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp', full_name='reaper_module_controller.SensorReply.envdata.temp', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity', full_name='reaper_module_controller.SensorReply.envdata.humidity', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pressure', full_name='reaper_module_controller.SensorReply.envdata.pressure', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4809,
  serialized_end=4887,
)

_SENSORREPLY_IMUDATA = _descriptor.Descriptor(
  name='imudata',
  full_name='reaper_module_controller.SensorReply.imudata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='reaper_module_controller.SensorReply.imudata.timestamp', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='accel', full_name='reaper_module_controller.SensorReply.imudata.accel', index=1,
      number=2, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gyro', full_name='reaper_module_controller.SensorReply.imudata.gyro', index=2,
      number=3, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4889,
  serialized_end=4946,
)

_SENSORREPLY_THERMDATA = _descriptor.Descriptor(
  name='thermdata',
  full_name='reaper_module_controller.SensorReply.thermdata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='reaper_module_controller.SensorReply.thermdata.timestamp', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp', full_name='reaper_module_controller.SensorReply.thermdata.temp', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4948,
  serialized_end=4992,
)

_SENSORREPLY_LEAKDATA = _descriptor.Descriptor(
  name='leakdata',
  full_name='reaper_module_controller.SensorReply.leakdata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='reaper_module_controller.SensorReply.leakdata.timestamp', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active', full_name='reaper_module_controller.SensorReply.leakdata.active', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4994,
  serialized_end=5039,
)

_SENSORREPLY_PRESSDATA = _descriptor.Descriptor(
  name='pressdata',
  full_name='reaper_module_controller.SensorReply.pressdata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='reaper_module_controller.SensorReply.pressdata.timestamp', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pressure', full_name='reaper_module_controller.SensorReply.pressdata.pressure', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature', full_name='reaper_module_controller.SensorReply.pressdata.temperature', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5041,
  serialized_end=5110,
)

_SENSORREPLY = _descriptor.Descriptor(
  name='SensorReply',
  full_name='reaper_module_controller.SensorReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='env', full_name='reaper_module_controller.SensorReply.env', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='imu', full_name='reaper_module_controller.SensorReply.imu', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='therm', full_name='reaper_module_controller.SensorReply.therm', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='leak', full_name='reaper_module_controller.SensorReply.leak', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='press', full_name='reaper_module_controller.SensorReply.press', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hasExternalEnv', full_name='reaper_module_controller.SensorReply.hasExternalEnv', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_SENSORREPLY_ENVDATA, _SENSORREPLY_IMUDATA, _SENSORREPLY_THERMDATA, _SENSORREPLY_LEAKDATA, _SENSORREPLY_PRESSDATA, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4460,
  serialized_end=5110,
)


_STATUSREQUEST = _descriptor.Descriptor(
  name='StatusRequest',
  full_name='reaper_module_controller.StatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5112,
  serialized_end=5127,
)


_STATUSREPLY = _descriptor.Descriptor(
  name='StatusReply',
  full_name='reaper_module_controller.StatusReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensors', full_name='reaper_module_controller.StatusReply.sensors', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='relays', full_name='reaper_module_controller.StatusReply.relays', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='reaper_module_controller.StatusReply.power', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe', full_name='reaper_module_controller.StatusReply.strobe', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanners', full_name='reaper_module_controller.StatusReply.scanners', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5130,
  serialized_end=5426,
)


_COREDUMPSTART = _descriptor.Descriptor(
  name='CoreDumpStart',
  full_name='reaper_module_controller.CoreDumpStart',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5428,
  serialized_end=5443,
)


_COREDUMPEND = _descriptor.Descriptor(
  name='CoreDumpEnd',
  full_name='reaper_module_controller.CoreDumpEnd',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5445,
  serialized_end=5458,
)


_COREDUMPDATA = _descriptor.Descriptor(
  name='CoreDumpData',
  full_name='reaper_module_controller.CoreDumpData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='is_last', full_name='reaper_module_controller.CoreDumpData.is_last', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='offset', full_name='reaper_module_controller.CoreDumpData.offset', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='reaper_module_controller.CoreDumpData.data', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5460,
  serialized_end=5521,
)


_COREDUMPREPLY = _descriptor.Descriptor(
  name='CoreDumpReply',
  full_name='reaper_module_controller.CoreDumpReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start', full_name='reaper_module_controller.CoreDumpReply.start', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end', full_name='reaper_module_controller.CoreDumpReply.end', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='reaper_module_controller.CoreDumpReply.data', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='payload', full_name='reaper_module_controller.CoreDumpReply.payload',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=5524,
  serialized_end=5718,
)


_UDPREQUEST = _descriptor.Descriptor(
  name='UdpRequest',
  full_name='reaper_module_controller.UdpRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='reaper_module_controller.UdpRequest.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ping', full_name='reaper_module_controller.UdpRequest.ping', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='reaper_module_controller.UdpRequest.version', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reset', full_name='reaper_module_controller.UdpRequest.reset', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time', full_name='reaper_module_controller.UdpRequest.time', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor', full_name='reaper_module_controller.UdpRequest.sensor', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='relay', full_name='reaper_module_controller.UdpRequest.relay', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fan', full_name='reaper_module_controller.UdpRequest.fan', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='reaper_module_controller.UdpRequest.config', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe', full_name='reaper_module_controller.UdpRequest.strobe', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='reaper_module_controller.UdpRequest.power', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner', full_name='reaper_module_controller.UdpRequest.scanner', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='network', full_name='reaper_module_controller.UdpRequest.network', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hwinfo', full_name='reaper_module_controller.UdpRequest.hwinfo', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='reaper_module_controller.UdpRequest.status', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='reaper_module_controller.UdpRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=5721,
  serialized_end=6495,
)


_UDPREPLY = _descriptor.Descriptor(
  name='UdpReply',
  full_name='reaper_module_controller.UdpReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='reaper_module_controller.UdpReply.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error', full_name='reaper_module_controller.UdpReply.error', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='reaper_module_controller.UdpReply.ack', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pong', full_name='reaper_module_controller.UdpReply.pong', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='reaper_module_controller.UdpReply.version', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time', full_name='reaper_module_controller.UdpReply.time', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor', full_name='reaper_module_controller.UdpReply.sensor', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='relay', full_name='reaper_module_controller.UdpReply.relay', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fan', full_name='reaper_module_controller.UdpReply.fan', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='reaper_module_controller.UdpReply.config', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe', full_name='reaper_module_controller.UdpReply.strobe', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='reaper_module_controller.UdpReply.power', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner', full_name='reaper_module_controller.UdpReply.scanner', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='network', full_name='reaper_module_controller.UdpReply.network', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hwinfo', full_name='reaper_module_controller.UdpReply.hwinfo', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='reaper_module_controller.UdpReply.status', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='reaper_module_controller.UdpReply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=6498,
  serialized_end=7259,
)


_OOBREQUEST = _descriptor.Descriptor(
  name='OobRequest',
  full_name='reaper_module_controller.OobRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='reaper_module_controller.OobRequest.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ping', full_name='reaper_module_controller.OobRequest.ping', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='reaper_module_controller.OobRequest.version', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reset', full_name='reaper_module_controller.OobRequest.reset', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='reaper_module_controller.OobRequest.config', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='network', full_name='reaper_module_controller.OobRequest.network', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hwinfo', full_name='reaper_module_controller.OobRequest.hwinfo', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe', full_name='reaper_module_controller.OobRequest.strobe', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='reaper_module_controller.OobRequest.power', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='relay', full_name='reaper_module_controller.OobRequest.relay', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='reaper_module_controller.OobRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=7262,
  serialized_end=7773,
)


_OOBREPLY = _descriptor.Descriptor(
  name='OobReply',
  full_name='reaper_module_controller.OobReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='reaper_module_controller.OobReply.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error', full_name='reaper_module_controller.OobReply.error', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='reaper_module_controller.OobReply.ack', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pong', full_name='reaper_module_controller.OobReply.pong', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='reaper_module_controller.OobReply.version', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='reaper_module_controller.OobReply.config', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='network', full_name='reaper_module_controller.OobReply.network', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hwinfo', full_name='reaper_module_controller.OobReply.hwinfo', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe', full_name='reaper_module_controller.OobReply.strobe', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='reaper_module_controller.OobReply.power', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='relay', full_name='reaper_module_controller.OobReply.relay', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='coredump', full_name='reaper_module_controller.OobReply.coredump', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='reaper_module_controller.OobReply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=7776,
  serialized_end=8345,
)

_NETWORKREQUEST.fields_by_name['config'].message_type = _NETWORKCONFIGREQUEST
_NETWORKREQUEST.fields_by_name['reset'].message_type = _NETWORKRESETREQUEST
_NETWORKREQUEST.fields_by_name['powerCycle'].message_type = _NETWORKPOWERCYCLEREQUEST
_NETWORKREQUEST.oneofs_by_name['request'].fields.append(
  _NETWORKREQUEST.fields_by_name['config'])
_NETWORKREQUEST.fields_by_name['config'].containing_oneof = _NETWORKREQUEST.oneofs_by_name['request']
_NETWORKREQUEST.oneofs_by_name['request'].fields.append(
  _NETWORKREQUEST.fields_by_name['reset'])
_NETWORKREQUEST.fields_by_name['reset'].containing_oneof = _NETWORKREQUEST.oneofs_by_name['request']
_NETWORKREQUEST.oneofs_by_name['request'].fields.append(
  _NETWORKREQUEST.fields_by_name['powerCycle'])
_NETWORKREQUEST.fields_by_name['powerCycle'].containing_oneof = _NETWORKREQUEST.oneofs_by_name['request']
_NETWORKADDRESS.fields_by_name['source'].enum_type = _NETWORKADDRESSSOURCE
_NETWORKADDRESS.oneofs_by_name['_gateway'].fields.append(
  _NETWORKADDRESS.fields_by_name['gateway'])
_NETWORKADDRESS.fields_by_name['gateway'].containing_oneof = _NETWORKADDRESS.oneofs_by_name['_gateway']
_NETWORKCONFIGREPLY.fields_by_name['addresses'].message_type = _NETWORKADDRESS
_NETWORKREPLY.fields_by_name['config'].message_type = _NETWORKCONFIGREPLY
_NETWORKREPLY.oneofs_by_name['reply'].fields.append(
  _NETWORKREPLY.fields_by_name['config'])
_NETWORKREPLY.fields_by_name['config'].containing_oneof = _NETWORKREPLY.oneofs_by_name['reply']
_SCANNERSETPOWERREQUEST.oneofs_by_name['_scannerA'].fields.append(
  _SCANNERSETPOWERREQUEST.fields_by_name['scannerA'])
_SCANNERSETPOWERREQUEST.fields_by_name['scannerA'].containing_oneof = _SCANNERSETPOWERREQUEST.oneofs_by_name['_scannerA']
_SCANNERSETPOWERREQUEST.oneofs_by_name['_scannerB'].fields.append(
  _SCANNERSETPOWERREQUEST.fields_by_name['scannerB'])
_SCANNERSETPOWERREQUEST.fields_by_name['scannerB'].containing_oneof = _SCANNERSETPOWERREQUEST.oneofs_by_name['_scannerB']
_SCANNERRESETOCPREQUEST.oneofs_by_name['_scannerA'].fields.append(
  _SCANNERRESETOCPREQUEST.fields_by_name['scannerA'])
_SCANNERRESETOCPREQUEST.fields_by_name['scannerA'].containing_oneof = _SCANNERRESETOCPREQUEST.oneofs_by_name['_scannerA']
_SCANNERRESETOCPREQUEST.oneofs_by_name['_scannerB'].fields.append(
  _SCANNERRESETOCPREQUEST.fields_by_name['scannerB'])
_SCANNERRESETOCPREQUEST.fields_by_name['scannerB'].containing_oneof = _SCANNERRESETOCPREQUEST.oneofs_by_name['_scannerB']
_SCANNERREQUEST.fields_by_name['status'].message_type = _SCANNERGETSTATUSREQUEST
_SCANNERREQUEST.fields_by_name['power'].message_type = _SCANNERSETPOWERREQUEST
_SCANNERREQUEST.fields_by_name['ocp'].message_type = _SCANNERRESETOCPREQUEST
_SCANNERREQUEST.oneofs_by_name['request'].fields.append(
  _SCANNERREQUEST.fields_by_name['status'])
_SCANNERREQUEST.fields_by_name['status'].containing_oneof = _SCANNERREQUEST.oneofs_by_name['request']
_SCANNERREQUEST.oneofs_by_name['request'].fields.append(
  _SCANNERREQUEST.fields_by_name['power'])
_SCANNERREQUEST.fields_by_name['power'].containing_oneof = _SCANNERREQUEST.oneofs_by_name['request']
_SCANNERREQUEST.oneofs_by_name['request'].fields.append(
  _SCANNERREQUEST.fields_by_name['ocp'])
_SCANNERREQUEST.fields_by_name['ocp'].containing_oneof = _SCANNERREQUEST.oneofs_by_name['request']
_SCANNERSTATUSREPLY.fields_by_name['data'].message_type = _SCANNERSTATUS
_SCANNERREPLY.fields_by_name['status'].message_type = _SCANNERSTATUSREPLY
_SCANNERREPLY.oneofs_by_name['reply'].fields.append(
  _SCANNERREPLY.fields_by_name['status'])
_SCANNERREPLY.fields_by_name['status'].containing_oneof = _SCANNERREPLY.oneofs_by_name['reply']
_POWERREQUEST.oneofs_by_name['_relayBoard'].fields.append(
  _POWERREQUEST.fields_by_name['relayBoard'])
_POWERREQUEST.fields_by_name['relayBoard'].containing_oneof = _POWERREQUEST.oneofs_by_name['_relayBoard']
_POWERREQUEST.oneofs_by_name['_strobeBoard'].fields.append(
  _POWERREQUEST.fields_by_name['strobeBoard'])
_POWERREQUEST.fields_by_name['strobeBoard'].containing_oneof = _POWERREQUEST.oneofs_by_name['_strobeBoard']
_POWERREQUEST.oneofs_by_name['_ethSwitch'].fields.append(
  _POWERREQUEST.fields_by_name['ethSwitch'])
_POWERREQUEST.fields_by_name['ethSwitch'].containing_oneof = _POWERREQUEST.oneofs_by_name['_ethSwitch']
_POWERREQUEST.oneofs_by_name['_predictCam'].fields.append(
  _POWERREQUEST.fields_by_name['predictCam'])
_POWERREQUEST.fields_by_name['predictCam'].containing_oneof = _POWERREQUEST.oneofs_by_name['_predictCam']
_STROBESTATUSREPLY.oneofs_by_name['_exposureUs'].fields.append(
  _STROBESTATUSREPLY.fields_by_name['exposureUs'])
_STROBESTATUSREPLY.fields_by_name['exposureUs'].containing_oneof = _STROBESTATUSREPLY.oneofs_by_name['_exposureUs']
_STROBESTATUSREPLY.oneofs_by_name['_periodUs'].fields.append(
  _STROBESTATUSREPLY.fields_by_name['periodUs'])
_STROBESTATUSREPLY.fields_by_name['periodUs'].containing_oneof = _STROBESTATUSREPLY.oneofs_by_name['_periodUs']
_STROBESTATUSREPLY.oneofs_by_name['_targetsPerPredict'].fields.append(
  _STROBESTATUSREPLY.fields_by_name['targetsPerPredict'])
_STROBESTATUSREPLY.fields_by_name['targetsPerPredict'].containing_oneof = _STROBESTATUSREPLY.oneofs_by_name['_targetsPerPredict']
_STROBEREQUEST.fields_by_name['setWaveform'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_strobe__control__pb2._REQUEST
_STROBEREQUEST.fields_by_name['getStatus'].message_type = _STROBESTATUSREQUEST
_STROBEREQUEST.fields_by_name['setState'].message_type = _SETSTROBESTATEREQUEST
_STROBEREQUEST.fields_by_name['timedDisable'].message_type = _TIMEDSTROBEDISABLEREQUEST
_STROBEREQUEST.oneofs_by_name['request'].fields.append(
  _STROBEREQUEST.fields_by_name['setWaveform'])
_STROBEREQUEST.fields_by_name['setWaveform'].containing_oneof = _STROBEREQUEST.oneofs_by_name['request']
_STROBEREQUEST.oneofs_by_name['request'].fields.append(
  _STROBEREQUEST.fields_by_name['getStatus'])
_STROBEREQUEST.fields_by_name['getStatus'].containing_oneof = _STROBEREQUEST.oneofs_by_name['request']
_STROBEREQUEST.oneofs_by_name['request'].fields.append(
  _STROBEREQUEST.fields_by_name['setState'])
_STROBEREQUEST.fields_by_name['setState'].containing_oneof = _STROBEREQUEST.oneofs_by_name['request']
_STROBEREQUEST.oneofs_by_name['request'].fields.append(
  _STROBEREQUEST.fields_by_name['timedDisable'])
_STROBEREQUEST.fields_by_name['timedDisable'].containing_oneof = _STROBEREQUEST.oneofs_by_name['request']
_STROBEREPLY.fields_by_name['status'].message_type = _STROBESTATUSREPLY
_STROBEREPLY.oneofs_by_name['reply'].fields.append(
  _STROBEREPLY.fields_by_name['status'])
_STROBEREPLY.fields_by_name['status'].containing_oneof = _STROBEREPLY.oneofs_by_name['reply']
_SETBOARDIDREQUEST.oneofs_by_name['_cbsn'].fields.append(
  _SETBOARDIDREQUEST.fields_by_name['cbsn'])
_SETBOARDIDREQUEST.fields_by_name['cbsn'].containing_oneof = _SETBOARDIDREQUEST.oneofs_by_name['_cbsn']
_SETBOARDIDREQUEST.oneofs_by_name['_assySn'].fields.append(
  _SETBOARDIDREQUEST.fields_by_name['assySn'])
_SETBOARDIDREQUEST.fields_by_name['assySn'].containing_oneof = _SETBOARDIDREQUEST.oneofs_by_name['_assySn']
_CONFIGREQUEST.fields_by_name['getIdentity'].message_type = _GETMODULEIDENTITYREQUEST
_CONFIGREQUEST.fields_by_name['setIdentity'].message_type = _MODULEIDENTITY
_CONFIGREQUEST.fields_by_name['otpLock'].message_type = _SETOTPLOCKREQUEST
_CONFIGREQUEST.fields_by_name['setBoardIdentity'].message_type = _SETBOARDIDREQUEST
_CONFIGREQUEST.oneofs_by_name['request'].fields.append(
  _CONFIGREQUEST.fields_by_name['getIdentity'])
_CONFIGREQUEST.fields_by_name['getIdentity'].containing_oneof = _CONFIGREQUEST.oneofs_by_name['request']
_CONFIGREQUEST.oneofs_by_name['request'].fields.append(
  _CONFIGREQUEST.fields_by_name['setIdentity'])
_CONFIGREQUEST.fields_by_name['setIdentity'].containing_oneof = _CONFIGREQUEST.oneofs_by_name['request']
_CONFIGREQUEST.oneofs_by_name['request'].fields.append(
  _CONFIGREQUEST.fields_by_name['otpLock'])
_CONFIGREQUEST.fields_by_name['otpLock'].containing_oneof = _CONFIGREQUEST.oneofs_by_name['request']
_CONFIGREQUEST.oneofs_by_name['request'].fields.append(
  _CONFIGREQUEST.fields_by_name['setBoardIdentity'])
_CONFIGREQUEST.fields_by_name['setBoardIdentity'].containing_oneof = _CONFIGREQUEST.oneofs_by_name['request']
_CONFIGREPLY.fields_by_name['identity'].message_type = _MODULEIDENTITY
_CONFIGREPLY.oneofs_by_name['reply'].fields.append(
  _CONFIGREPLY.fields_by_name['identity'])
_CONFIGREPLY.fields_by_name['identity'].containing_oneof = _CONFIGREPLY.oneofs_by_name['reply']
_THERMOSTATCONFIG.fields_by_name['source'].enum_type = _THERMOSTATSOURCE
_FANSETREQUEST.oneofs_by_name['_fan1'].fields.append(
  _FANSETREQUEST.fields_by_name['fan1'])
_FANSETREQUEST.fields_by_name['fan1'].containing_oneof = _FANSETREQUEST.oneofs_by_name['_fan1']
_FANSETREQUEST.oneofs_by_name['_fan2'].fields.append(
  _FANSETREQUEST.fields_by_name['fan2'])
_FANSETREQUEST.fields_by_name['fan2'].containing_oneof = _FANSETREQUEST.oneofs_by_name['_fan2']
_FANTHERMOSTATCONFIGREQUEST.fields_by_name['config'].message_type = _THERMOSTATCONFIG
_FANTHERMOSTATCONFIGREQUEST.oneofs_by_name['_enabled'].fields.append(
  _FANTHERMOSTATCONFIGREQUEST.fields_by_name['enabled'])
_FANTHERMOSTATCONFIGREQUEST.fields_by_name['enabled'].containing_oneof = _FANTHERMOSTATCONFIGREQUEST.oneofs_by_name['_enabled']
_FANTHERMOSTATCONFIGREQUEST.oneofs_by_name['_config'].fields.append(
  _FANTHERMOSTATCONFIGREQUEST.fields_by_name['config'])
_FANTHERMOSTATCONFIGREQUEST.fields_by_name['config'].containing_oneof = _FANTHERMOSTATCONFIGREQUEST.oneofs_by_name['_config']
_FANREQUEST.fields_by_name['set'].message_type = _FANSETREQUEST
_FANREQUEST.fields_by_name['thermoConfig'].message_type = _FANTHERMOSTATCONFIGREQUEST
_FANREQUEST.oneofs_by_name['request'].fields.append(
  _FANREQUEST.fields_by_name['set'])
_FANREQUEST.fields_by_name['set'].containing_oneof = _FANREQUEST.oneofs_by_name['request']
_FANREQUEST.oneofs_by_name['request'].fields.append(
  _FANREQUEST.fields_by_name['thermoConfig'])
_FANREQUEST.fields_by_name['thermoConfig'].containing_oneof = _FANREQUEST.oneofs_by_name['request']
_FANREPLY.fields_by_name['thermostatConfig'].message_type = _THERMOSTATCONFIG
_FANREPLY.oneofs_by_name['_thermostatConfig'].fields.append(
  _FANREPLY.fields_by_name['thermostatConfig'])
_FANREPLY.fields_by_name['thermostatConfig'].containing_oneof = _FANREPLY.oneofs_by_name['_thermostatConfig']
_FANREPLY.oneofs_by_name['_thermostatActual'].fields.append(
  _FANREPLY.fields_by_name['thermostatActual'])
_FANREPLY.fields_by_name['thermostatActual'].containing_oneof = _FANREPLY.oneofs_by_name['_thermostatActual']
_RELAYREQUEST.oneofs_by_name['_pc'].fields.append(
  _RELAYREQUEST.fields_by_name['pc'])
_RELAYREQUEST.fields_by_name['pc'].containing_oneof = _RELAYREQUEST.oneofs_by_name['_pc']
_RELAYREQUEST.oneofs_by_name['_btl'].fields.append(
  _RELAYREQUEST.fields_by_name['btl'])
_RELAYREQUEST.fields_by_name['btl'].containing_oneof = _RELAYREQUEST.oneofs_by_name['_btl']
_RELAYREQUEST.oneofs_by_name['_laser'].fields.append(
  _RELAYREQUEST.fields_by_name['laser'])
_RELAYREQUEST.fields_by_name['laser'].containing_oneof = _RELAYREQUEST.oneofs_by_name['_laser']
_SENSORREPLY_ENVDATA.containing_type = _SENSORREPLY
_SENSORREPLY_IMUDATA.containing_type = _SENSORREPLY
_SENSORREPLY_THERMDATA.containing_type = _SENSORREPLY
_SENSORREPLY_LEAKDATA.containing_type = _SENSORREPLY
_SENSORREPLY_PRESSDATA.containing_type = _SENSORREPLY
_SENSORREPLY.fields_by_name['env'].message_type = _SENSORREPLY_ENVDATA
_SENSORREPLY.fields_by_name['imu'].message_type = _SENSORREPLY_IMUDATA
_SENSORREPLY.fields_by_name['therm'].message_type = _SENSORREPLY_THERMDATA
_SENSORREPLY.fields_by_name['leak'].message_type = _SENSORREPLY_LEAKDATA
_SENSORREPLY.fields_by_name['press'].message_type = _SENSORREPLY_PRESSDATA
_STATUSREPLY.fields_by_name['sensors'].message_type = _SENSORREPLY
_STATUSREPLY.fields_by_name['relays'].message_type = _RELAYREPLY
_STATUSREPLY.fields_by_name['power'].message_type = _POWERREPLY
_STATUSREPLY.fields_by_name['strobe'].message_type = _STROBESTATUSREPLY
_STATUSREPLY.fields_by_name['scanners'].message_type = _SCANNERSTATUS
_COREDUMPREPLY.fields_by_name['start'].message_type = _COREDUMPSTART
_COREDUMPREPLY.fields_by_name['end'].message_type = _COREDUMPEND
_COREDUMPREPLY.fields_by_name['data'].message_type = _COREDUMPDATA
_COREDUMPREPLY.oneofs_by_name['payload'].fields.append(
  _COREDUMPREPLY.fields_by_name['start'])
_COREDUMPREPLY.fields_by_name['start'].containing_oneof = _COREDUMPREPLY.oneofs_by_name['payload']
_COREDUMPREPLY.oneofs_by_name['payload'].fields.append(
  _COREDUMPREPLY.fields_by_name['end'])
_COREDUMPREPLY.fields_by_name['end'].containing_oneof = _COREDUMPREPLY.oneofs_by_name['payload']
_COREDUMPREPLY.oneofs_by_name['payload'].fields.append(
  _COREDUMPREPLY.fields_by_name['data'])
_COREDUMPREPLY.fields_by_name['data'].containing_oneof = _COREDUMPREPLY.oneofs_by_name['payload']
_UDPREQUEST.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_UDPREQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_UDPREQUEST.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REQUEST
_UDPREQUEST.fields_by_name['reset'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._RESET_REQUEST
_UDPREQUEST.fields_by_name['time'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._REQUEST
_UDPREQUEST.fields_by_name['sensor'].message_type = _SENSORREQUEST
_UDPREQUEST.fields_by_name['relay'].message_type = _RELAYREQUEST
_UDPREQUEST.fields_by_name['fan'].message_type = _FANREQUEST
_UDPREQUEST.fields_by_name['config'].message_type = _CONFIGREQUEST
_UDPREQUEST.fields_by_name['strobe'].message_type = _STROBEREQUEST
_UDPREQUEST.fields_by_name['power'].message_type = _POWERREQUEST
_UDPREQUEST.fields_by_name['scanner'].message_type = _SCANNERREQUEST
_UDPREQUEST.fields_by_name['network'].message_type = _NETWORKREQUEST
_UDPREQUEST.fields_by_name['hwinfo'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2._REQUEST
_UDPREQUEST.fields_by_name['status'].message_type = _STATUSREQUEST
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['ping'])
_UDPREQUEST.fields_by_name['ping'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['version'])
_UDPREQUEST.fields_by_name['version'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['reset'])
_UDPREQUEST.fields_by_name['reset'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['time'])
_UDPREQUEST.fields_by_name['time'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['sensor'])
_UDPREQUEST.fields_by_name['sensor'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['relay'])
_UDPREQUEST.fields_by_name['relay'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['fan'])
_UDPREQUEST.fields_by_name['fan'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['config'])
_UDPREQUEST.fields_by_name['config'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['strobe'])
_UDPREQUEST.fields_by_name['strobe'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['power'])
_UDPREQUEST.fields_by_name['power'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['scanner'])
_UDPREQUEST.fields_by_name['scanner'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['network'])
_UDPREQUEST.fields_by_name['network'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['hwinfo'])
_UDPREQUEST.fields_by_name['hwinfo'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREQUEST.oneofs_by_name['request'].fields.append(
  _UDPREQUEST.fields_by_name['status'])
_UDPREQUEST.fields_by_name['status'].containing_oneof = _UDPREQUEST.oneofs_by_name['request']
_UDPREPLY.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_UDPREPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_UDPREPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_UDPREPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_UDPREPLY.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REPLY
_UDPREPLY.fields_by_name['time'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._REPLY
_UDPREPLY.fields_by_name['sensor'].message_type = _SENSORREPLY
_UDPREPLY.fields_by_name['relay'].message_type = _RELAYREPLY
_UDPREPLY.fields_by_name['fan'].message_type = _FANREPLY
_UDPREPLY.fields_by_name['config'].message_type = _CONFIGREPLY
_UDPREPLY.fields_by_name['strobe'].message_type = _STROBEREPLY
_UDPREPLY.fields_by_name['power'].message_type = _POWERREPLY
_UDPREPLY.fields_by_name['scanner'].message_type = _SCANNERREPLY
_UDPREPLY.fields_by_name['network'].message_type = _NETWORKREPLY
_UDPREPLY.fields_by_name['hwinfo'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2._REPLY
_UDPREPLY.fields_by_name['status'].message_type = _STATUSREPLY
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['error'])
_UDPREPLY.fields_by_name['error'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['ack'])
_UDPREPLY.fields_by_name['ack'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['pong'])
_UDPREPLY.fields_by_name['pong'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['version'])
_UDPREPLY.fields_by_name['version'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['time'])
_UDPREPLY.fields_by_name['time'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['sensor'])
_UDPREPLY.fields_by_name['sensor'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['relay'])
_UDPREPLY.fields_by_name['relay'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['fan'])
_UDPREPLY.fields_by_name['fan'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['config'])
_UDPREPLY.fields_by_name['config'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['strobe'])
_UDPREPLY.fields_by_name['strobe'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['power'])
_UDPREPLY.fields_by_name['power'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['scanner'])
_UDPREPLY.fields_by_name['scanner'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['network'])
_UDPREPLY.fields_by_name['network'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['hwinfo'])
_UDPREPLY.fields_by_name['hwinfo'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_UDPREPLY.oneofs_by_name['reply'].fields.append(
  _UDPREPLY.fields_by_name['status'])
_UDPREPLY.fields_by_name['status'].containing_oneof = _UDPREPLY.oneofs_by_name['reply']
_OOBREQUEST.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_OOBREQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_OOBREQUEST.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REQUEST
_OOBREQUEST.fields_by_name['reset'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._RESET_REQUEST
_OOBREQUEST.fields_by_name['config'].message_type = _CONFIGREQUEST
_OOBREQUEST.fields_by_name['network'].message_type = _NETWORKREQUEST
_OOBREQUEST.fields_by_name['hwinfo'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2._REQUEST
_OOBREQUEST.fields_by_name['strobe'].message_type = _STROBEREQUEST
_OOBREQUEST.fields_by_name['power'].message_type = _POWERREQUEST
_OOBREQUEST.fields_by_name['relay'].message_type = _RELAYREQUEST
_OOBREQUEST.oneofs_by_name['request'].fields.append(
  _OOBREQUEST.fields_by_name['ping'])
_OOBREQUEST.fields_by_name['ping'].containing_oneof = _OOBREQUEST.oneofs_by_name['request']
_OOBREQUEST.oneofs_by_name['request'].fields.append(
  _OOBREQUEST.fields_by_name['version'])
_OOBREQUEST.fields_by_name['version'].containing_oneof = _OOBREQUEST.oneofs_by_name['request']
_OOBREQUEST.oneofs_by_name['request'].fields.append(
  _OOBREQUEST.fields_by_name['reset'])
_OOBREQUEST.fields_by_name['reset'].containing_oneof = _OOBREQUEST.oneofs_by_name['request']
_OOBREQUEST.oneofs_by_name['request'].fields.append(
  _OOBREQUEST.fields_by_name['config'])
_OOBREQUEST.fields_by_name['config'].containing_oneof = _OOBREQUEST.oneofs_by_name['request']
_OOBREQUEST.oneofs_by_name['request'].fields.append(
  _OOBREQUEST.fields_by_name['network'])
_OOBREQUEST.fields_by_name['network'].containing_oneof = _OOBREQUEST.oneofs_by_name['request']
_OOBREQUEST.oneofs_by_name['request'].fields.append(
  _OOBREQUEST.fields_by_name['hwinfo'])
_OOBREQUEST.fields_by_name['hwinfo'].containing_oneof = _OOBREQUEST.oneofs_by_name['request']
_OOBREQUEST.oneofs_by_name['request'].fields.append(
  _OOBREQUEST.fields_by_name['strobe'])
_OOBREQUEST.fields_by_name['strobe'].containing_oneof = _OOBREQUEST.oneofs_by_name['request']
_OOBREQUEST.oneofs_by_name['request'].fields.append(
  _OOBREQUEST.fields_by_name['power'])
_OOBREQUEST.fields_by_name['power'].containing_oneof = _OOBREQUEST.oneofs_by_name['request']
_OOBREQUEST.oneofs_by_name['request'].fields.append(
  _OOBREQUEST.fields_by_name['relay'])
_OOBREQUEST.fields_by_name['relay'].containing_oneof = _OOBREQUEST.oneofs_by_name['request']
_OOBREPLY.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_OOBREPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_OOBREPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_OOBREPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_OOBREPLY.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REPLY
_OOBREPLY.fields_by_name['config'].message_type = _CONFIGREPLY
_OOBREPLY.fields_by_name['network'].message_type = _NETWORKREPLY
_OOBREPLY.fields_by_name['hwinfo'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2._REPLY
_OOBREPLY.fields_by_name['strobe'].message_type = _STROBEREPLY
_OOBREPLY.fields_by_name['power'].message_type = _POWERREPLY
_OOBREPLY.fields_by_name['relay'].message_type = _RELAYREPLY
_OOBREPLY.fields_by_name['coredump'].message_type = _COREDUMPREPLY
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['error'])
_OOBREPLY.fields_by_name['error'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['ack'])
_OOBREPLY.fields_by_name['ack'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['pong'])
_OOBREPLY.fields_by_name['pong'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['version'])
_OOBREPLY.fields_by_name['version'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['config'])
_OOBREPLY.fields_by_name['config'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['network'])
_OOBREPLY.fields_by_name['network'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['hwinfo'])
_OOBREPLY.fields_by_name['hwinfo'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['strobe'])
_OOBREPLY.fields_by_name['strobe'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['power'])
_OOBREPLY.fields_by_name['power'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['relay'])
_OOBREPLY.fields_by_name['relay'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
_OOBREPLY.oneofs_by_name['reply'].fields.append(
  _OOBREPLY.fields_by_name['coredump'])
_OOBREPLY.fields_by_name['coredump'].containing_oneof = _OOBREPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['NetworkConfigRequest'] = _NETWORKCONFIGREQUEST
DESCRIPTOR.message_types_by_name['NetworkResetRequest'] = _NETWORKRESETREQUEST
DESCRIPTOR.message_types_by_name['NetworkPowerCycleRequest'] = _NETWORKPOWERCYCLEREQUEST
DESCRIPTOR.message_types_by_name['NetworkRequest'] = _NETWORKREQUEST
DESCRIPTOR.message_types_by_name['NetworkAddress'] = _NETWORKADDRESS
DESCRIPTOR.message_types_by_name['NetworkConfigReply'] = _NETWORKCONFIGREPLY
DESCRIPTOR.message_types_by_name['NetworkReply'] = _NETWORKREPLY
DESCRIPTOR.message_types_by_name['ScannerStatus'] = _SCANNERSTATUS
DESCRIPTOR.message_types_by_name['ScannerGetStatusRequest'] = _SCANNERGETSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ScannerSetPowerRequest'] = _SCANNERSETPOWERREQUEST
DESCRIPTOR.message_types_by_name['ScannerResetOcpRequest'] = _SCANNERRESETOCPREQUEST
DESCRIPTOR.message_types_by_name['ScannerRequest'] = _SCANNERREQUEST
DESCRIPTOR.message_types_by_name['ScannerStatusReply'] = _SCANNERSTATUSREPLY
DESCRIPTOR.message_types_by_name['ScannerReply'] = _SCANNERREPLY
DESCRIPTOR.message_types_by_name['PowerRequest'] = _POWERREQUEST
DESCRIPTOR.message_types_by_name['PowerReply'] = _POWERREPLY
DESCRIPTOR.message_types_by_name['StrobeStatusRequest'] = _STROBESTATUSREQUEST
DESCRIPTOR.message_types_by_name['SetStrobeStateRequest'] = _SETSTROBESTATEREQUEST
DESCRIPTOR.message_types_by_name['TimedStrobeDisableRequest'] = _TIMEDSTROBEDISABLEREQUEST
DESCRIPTOR.message_types_by_name['StrobeStatusReply'] = _STROBESTATUSREPLY
DESCRIPTOR.message_types_by_name['StrobeRequest'] = _STROBEREQUEST
DESCRIPTOR.message_types_by_name['StrobeReply'] = _STROBEREPLY
DESCRIPTOR.message_types_by_name['ModuleIdentity'] = _MODULEIDENTITY
DESCRIPTOR.message_types_by_name['GetModuleIdentityRequest'] = _GETMODULEIDENTITYREQUEST
DESCRIPTOR.message_types_by_name['SetOtpLockRequest'] = _SETOTPLOCKREQUEST
DESCRIPTOR.message_types_by_name['SetBoardIdRequest'] = _SETBOARDIDREQUEST
DESCRIPTOR.message_types_by_name['ConfigRequest'] = _CONFIGREQUEST
DESCRIPTOR.message_types_by_name['ConfigReply'] = _CONFIGREPLY
DESCRIPTOR.message_types_by_name['ThermostatConfig'] = _THERMOSTATCONFIG
DESCRIPTOR.message_types_by_name['FanSetRequest'] = _FANSETREQUEST
DESCRIPTOR.message_types_by_name['FanThermostatConfigRequest'] = _FANTHERMOSTATCONFIGREQUEST
DESCRIPTOR.message_types_by_name['FanRequest'] = _FANREQUEST
DESCRIPTOR.message_types_by_name['FanReply'] = _FANREPLY
DESCRIPTOR.message_types_by_name['RelayRequest'] = _RELAYREQUEST
DESCRIPTOR.message_types_by_name['RelayReply'] = _RELAYREPLY
DESCRIPTOR.message_types_by_name['SensorRequest'] = _SENSORREQUEST
DESCRIPTOR.message_types_by_name['SensorReply'] = _SENSORREPLY
DESCRIPTOR.message_types_by_name['StatusRequest'] = _STATUSREQUEST
DESCRIPTOR.message_types_by_name['StatusReply'] = _STATUSREPLY
DESCRIPTOR.message_types_by_name['CoreDumpStart'] = _COREDUMPSTART
DESCRIPTOR.message_types_by_name['CoreDumpEnd'] = _COREDUMPEND
DESCRIPTOR.message_types_by_name['CoreDumpData'] = _COREDUMPDATA
DESCRIPTOR.message_types_by_name['CoreDumpReply'] = _COREDUMPREPLY
DESCRIPTOR.message_types_by_name['UdpRequest'] = _UDPREQUEST
DESCRIPTOR.message_types_by_name['UdpReply'] = _UDPREPLY
DESCRIPTOR.message_types_by_name['OobRequest'] = _OOBREQUEST
DESCRIPTOR.message_types_by_name['OobReply'] = _OOBREPLY
DESCRIPTOR.enum_types_by_name['NetworkAddressSource'] = _NETWORKADDRESSSOURCE
DESCRIPTOR.enum_types_by_name['ThermostatSource'] = _THERMOSTATSOURCE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

NetworkConfigRequest = _reflection.GeneratedProtocolMessageType('NetworkConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKCONFIGREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.NetworkConfigRequest)
  })
_sym_db.RegisterMessage(NetworkConfigRequest)

NetworkResetRequest = _reflection.GeneratedProtocolMessageType('NetworkResetRequest', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKRESETREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.NetworkResetRequest)
  })
_sym_db.RegisterMessage(NetworkResetRequest)

NetworkPowerCycleRequest = _reflection.GeneratedProtocolMessageType('NetworkPowerCycleRequest', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKPOWERCYCLEREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.NetworkPowerCycleRequest)
  })
_sym_db.RegisterMessage(NetworkPowerCycleRequest)

NetworkRequest = _reflection.GeneratedProtocolMessageType('NetworkRequest', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.NetworkRequest)
  })
_sym_db.RegisterMessage(NetworkRequest)

NetworkAddress = _reflection.GeneratedProtocolMessageType('NetworkAddress', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKADDRESS,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.NetworkAddress)
  })
_sym_db.RegisterMessage(NetworkAddress)

NetworkConfigReply = _reflection.GeneratedProtocolMessageType('NetworkConfigReply', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKCONFIGREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.NetworkConfigReply)
  })
_sym_db.RegisterMessage(NetworkConfigReply)

NetworkReply = _reflection.GeneratedProtocolMessageType('NetworkReply', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.NetworkReply)
  })
_sym_db.RegisterMessage(NetworkReply)

ScannerStatus = _reflection.GeneratedProtocolMessageType('ScannerStatus', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERSTATUS,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ScannerStatus)
  })
_sym_db.RegisterMessage(ScannerStatus)

ScannerGetStatusRequest = _reflection.GeneratedProtocolMessageType('ScannerGetStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERGETSTATUSREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ScannerGetStatusRequest)
  })
_sym_db.RegisterMessage(ScannerGetStatusRequest)

ScannerSetPowerRequest = _reflection.GeneratedProtocolMessageType('ScannerSetPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERSETPOWERREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ScannerSetPowerRequest)
  })
_sym_db.RegisterMessage(ScannerSetPowerRequest)

ScannerResetOcpRequest = _reflection.GeneratedProtocolMessageType('ScannerResetOcpRequest', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERRESETOCPREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ScannerResetOcpRequest)
  })
_sym_db.RegisterMessage(ScannerResetOcpRequest)

ScannerRequest = _reflection.GeneratedProtocolMessageType('ScannerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ScannerRequest)
  })
_sym_db.RegisterMessage(ScannerRequest)

ScannerStatusReply = _reflection.GeneratedProtocolMessageType('ScannerStatusReply', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERSTATUSREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ScannerStatusReply)
  })
_sym_db.RegisterMessage(ScannerStatusReply)

ScannerReply = _reflection.GeneratedProtocolMessageType('ScannerReply', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ScannerReply)
  })
_sym_db.RegisterMessage(ScannerReply)

PowerRequest = _reflection.GeneratedProtocolMessageType('PowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _POWERREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.PowerRequest)
  })
_sym_db.RegisterMessage(PowerRequest)

PowerReply = _reflection.GeneratedProtocolMessageType('PowerReply', (_message.Message,), {
  'DESCRIPTOR' : _POWERREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.PowerReply)
  })
_sym_db.RegisterMessage(PowerReply)

StrobeStatusRequest = _reflection.GeneratedProtocolMessageType('StrobeStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _STROBESTATUSREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.StrobeStatusRequest)
  })
_sym_db.RegisterMessage(StrobeStatusRequest)

SetStrobeStateRequest = _reflection.GeneratedProtocolMessageType('SetStrobeStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETSTROBESTATEREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.SetStrobeStateRequest)
  })
_sym_db.RegisterMessage(SetStrobeStateRequest)

TimedStrobeDisableRequest = _reflection.GeneratedProtocolMessageType('TimedStrobeDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _TIMEDSTROBEDISABLEREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.TimedStrobeDisableRequest)
  })
_sym_db.RegisterMessage(TimedStrobeDisableRequest)

StrobeStatusReply = _reflection.GeneratedProtocolMessageType('StrobeStatusReply', (_message.Message,), {
  'DESCRIPTOR' : _STROBESTATUSREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.StrobeStatusReply)
  })
_sym_db.RegisterMessage(StrobeStatusReply)

StrobeRequest = _reflection.GeneratedProtocolMessageType('StrobeRequest', (_message.Message,), {
  'DESCRIPTOR' : _STROBEREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.StrobeRequest)
  })
_sym_db.RegisterMessage(StrobeRequest)

StrobeReply = _reflection.GeneratedProtocolMessageType('StrobeReply', (_message.Message,), {
  'DESCRIPTOR' : _STROBEREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.StrobeReply)
  })
_sym_db.RegisterMessage(StrobeReply)

ModuleIdentity = _reflection.GeneratedProtocolMessageType('ModuleIdentity', (_message.Message,), {
  'DESCRIPTOR' : _MODULEIDENTITY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ModuleIdentity)
  })
_sym_db.RegisterMessage(ModuleIdentity)

GetModuleIdentityRequest = _reflection.GeneratedProtocolMessageType('GetModuleIdentityRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETMODULEIDENTITYREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.GetModuleIdentityRequest)
  })
_sym_db.RegisterMessage(GetModuleIdentityRequest)

SetOtpLockRequest = _reflection.GeneratedProtocolMessageType('SetOtpLockRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETOTPLOCKREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.SetOtpLockRequest)
  })
_sym_db.RegisterMessage(SetOtpLockRequest)

SetBoardIdRequest = _reflection.GeneratedProtocolMessageType('SetBoardIdRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETBOARDIDREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.SetBoardIdRequest)
  })
_sym_db.RegisterMessage(SetBoardIdRequest)

ConfigRequest = _reflection.GeneratedProtocolMessageType('ConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONFIGREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ConfigRequest)
  })
_sym_db.RegisterMessage(ConfigRequest)

ConfigReply = _reflection.GeneratedProtocolMessageType('ConfigReply', (_message.Message,), {
  'DESCRIPTOR' : _CONFIGREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ConfigReply)
  })
_sym_db.RegisterMessage(ConfigReply)

ThermostatConfig = _reflection.GeneratedProtocolMessageType('ThermostatConfig', (_message.Message,), {
  'DESCRIPTOR' : _THERMOSTATCONFIG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.ThermostatConfig)
  })
_sym_db.RegisterMessage(ThermostatConfig)

FanSetRequest = _reflection.GeneratedProtocolMessageType('FanSetRequest', (_message.Message,), {
  'DESCRIPTOR' : _FANSETREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.FanSetRequest)
  })
_sym_db.RegisterMessage(FanSetRequest)

FanThermostatConfigRequest = _reflection.GeneratedProtocolMessageType('FanThermostatConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _FANTHERMOSTATCONFIGREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.FanThermostatConfigRequest)
  })
_sym_db.RegisterMessage(FanThermostatConfigRequest)

FanRequest = _reflection.GeneratedProtocolMessageType('FanRequest', (_message.Message,), {
  'DESCRIPTOR' : _FANREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.FanRequest)
  })
_sym_db.RegisterMessage(FanRequest)

FanReply = _reflection.GeneratedProtocolMessageType('FanReply', (_message.Message,), {
  'DESCRIPTOR' : _FANREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.FanReply)
  })
_sym_db.RegisterMessage(FanReply)

RelayRequest = _reflection.GeneratedProtocolMessageType('RelayRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELAYREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.RelayRequest)
  })
_sym_db.RegisterMessage(RelayRequest)

RelayReply = _reflection.GeneratedProtocolMessageType('RelayReply', (_message.Message,), {
  'DESCRIPTOR' : _RELAYREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.RelayReply)
  })
_sym_db.RegisterMessage(RelayReply)

SensorRequest = _reflection.GeneratedProtocolMessageType('SensorRequest', (_message.Message,), {
  'DESCRIPTOR' : _SENSORREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.SensorRequest)
  })
_sym_db.RegisterMessage(SensorRequest)

SensorReply = _reflection.GeneratedProtocolMessageType('SensorReply', (_message.Message,), {

  'envdata' : _reflection.GeneratedProtocolMessageType('envdata', (_message.Message,), {
    'DESCRIPTOR' : _SENSORREPLY_ENVDATA,
    '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
    # @@protoc_insertion_point(class_scope:reaper_module_controller.SensorReply.envdata)
    })
  ,

  'imudata' : _reflection.GeneratedProtocolMessageType('imudata', (_message.Message,), {
    'DESCRIPTOR' : _SENSORREPLY_IMUDATA,
    '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
    # @@protoc_insertion_point(class_scope:reaper_module_controller.SensorReply.imudata)
    })
  ,

  'thermdata' : _reflection.GeneratedProtocolMessageType('thermdata', (_message.Message,), {
    'DESCRIPTOR' : _SENSORREPLY_THERMDATA,
    '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
    # @@protoc_insertion_point(class_scope:reaper_module_controller.SensorReply.thermdata)
    })
  ,

  'leakdata' : _reflection.GeneratedProtocolMessageType('leakdata', (_message.Message,), {
    'DESCRIPTOR' : _SENSORREPLY_LEAKDATA,
    '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
    # @@protoc_insertion_point(class_scope:reaper_module_controller.SensorReply.leakdata)
    })
  ,

  'pressdata' : _reflection.GeneratedProtocolMessageType('pressdata', (_message.Message,), {
    'DESCRIPTOR' : _SENSORREPLY_PRESSDATA,
    '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
    # @@protoc_insertion_point(class_scope:reaper_module_controller.SensorReply.pressdata)
    })
  ,
  'DESCRIPTOR' : _SENSORREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.SensorReply)
  })
_sym_db.RegisterMessage(SensorReply)
_sym_db.RegisterMessage(SensorReply.envdata)
_sym_db.RegisterMessage(SensorReply.imudata)
_sym_db.RegisterMessage(SensorReply.thermdata)
_sym_db.RegisterMessage(SensorReply.leakdata)
_sym_db.RegisterMessage(SensorReply.pressdata)

StatusRequest = _reflection.GeneratedProtocolMessageType('StatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _STATUSREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.StatusRequest)
  })
_sym_db.RegisterMessage(StatusRequest)

StatusReply = _reflection.GeneratedProtocolMessageType('StatusReply', (_message.Message,), {
  'DESCRIPTOR' : _STATUSREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.StatusReply)
  })
_sym_db.RegisterMessage(StatusReply)

CoreDumpStart = _reflection.GeneratedProtocolMessageType('CoreDumpStart', (_message.Message,), {
  'DESCRIPTOR' : _COREDUMPSTART,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.CoreDumpStart)
  })
_sym_db.RegisterMessage(CoreDumpStart)

CoreDumpEnd = _reflection.GeneratedProtocolMessageType('CoreDumpEnd', (_message.Message,), {
  'DESCRIPTOR' : _COREDUMPEND,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.CoreDumpEnd)
  })
_sym_db.RegisterMessage(CoreDumpEnd)

CoreDumpData = _reflection.GeneratedProtocolMessageType('CoreDumpData', (_message.Message,), {
  'DESCRIPTOR' : _COREDUMPDATA,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.CoreDumpData)
  })
_sym_db.RegisterMessage(CoreDumpData)

CoreDumpReply = _reflection.GeneratedProtocolMessageType('CoreDumpReply', (_message.Message,), {
  'DESCRIPTOR' : _COREDUMPREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.CoreDumpReply)
  })
_sym_db.RegisterMessage(CoreDumpReply)

UdpRequest = _reflection.GeneratedProtocolMessageType('UdpRequest', (_message.Message,), {
  'DESCRIPTOR' : _UDPREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.UdpRequest)
  })
_sym_db.RegisterMessage(UdpRequest)

UdpReply = _reflection.GeneratedProtocolMessageType('UdpReply', (_message.Message,), {
  'DESCRIPTOR' : _UDPREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.UdpReply)
  })
_sym_db.RegisterMessage(UdpReply)

OobRequest = _reflection.GeneratedProtocolMessageType('OobRequest', (_message.Message,), {
  'DESCRIPTOR' : _OOBREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.OobRequest)
  })
_sym_db.RegisterMessage(OobRequest)

OobReply = _reflection.GeneratedProtocolMessageType('OobReply', (_message.Message,), {
  'DESCRIPTOR' : _OOBREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2'
  # @@protoc_insertion_point(class_scope:reaper_module_controller.OobReply)
  })
_sym_db.RegisterMessage(OobReply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

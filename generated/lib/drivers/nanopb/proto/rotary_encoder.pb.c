/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "rotary_encoder.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(rotary_encoder_RotaryEncodersConfig_Request, rotary_encoder_RotaryEncodersConfig_Request, AUTO)


PB_BIND(rotary_encoder_RotaryEncodersConfig_Reply, rotary_encoder_RotaryEncodersConfig_Reply, AUTO)


PB_BIND(rotary_encoder_RotaryEncoder_Request, rotary_encoder_RotaryEncoder_Request, AUTO)


PB_BIND(rotary_encoder_RotaryEncoder_Reply, rotary_encoder_RotaryEncoder_Reply, 2)


PB_BIND(rotary_encoder_RotaryEncoderSnapshot_Request, rotary_encoder_RotaryEncoderSnapshot_Request, 2)


PB_BIND(rotary_encoder_RotaryEncoderSnapshot_Reply, rotary_encoder_RotaryEncoderSnapshot_Reply, 2)


PB_BIND(rotary_encoder_RotaryEncoderHistoryVerify_Request, rotary_encoder_RotaryEncoderHistoryVerify_Request, AUTO)


PB_BIND(rotary_encoder_RotaryEncoderHistoryVerify_Reply, rotary_encoder_RotaryEncoderHistoryVerify_Reply, AUTO)


PB_BIND(rotary_encoder_Request, rotary_encoder_Request, 2)


PB_BIND(rotary_encoder_Reply, rotary_encoder_Reply, 2)





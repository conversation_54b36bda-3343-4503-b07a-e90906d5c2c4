# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/carbon_tractor.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import ots_tractor_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ots__tractor__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/carbon_tractor.proto',
  package='carbon_tractor',
  syntax='proto3',
  serialized_options=b'Z\025nanopb/carbon_tractor',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n7generated/lib/drivers/nanopb/proto/carbon_tractor.proto\x12\x0e\x63\x61rbon_tractor\x1a\x34generated/lib/drivers/nanopb/proto/ots_tractor.proto\"\x07\n\x05\x45mpty\"7\n\x07HHState\x12,\n\x05state\x18\x01 \x01(\x0e\x32\x1d.carbon_tractor.HHStateStatus\"5\n\nBrakeState\x12\x12\n\nforce_left\x18\x01 \x01(\x05\x12\x13\n\x0b\x66orce_right\x18\x02 \x01(\x05\"\x84\x01\n\x12SafetySensorsState\x12\x1a\n\x12triggered_sensor_1\x18\x01 \x01(\x08\x12\x1a\n\x12triggered_sensor_2\x18\x02 \x01(\x08\x12\x1a\n\x12triggered_sensor_3\x18\x03 \x01(\x08\x12\x1a\n\x12triggered_sensor_4\x18\x04 \x01(\x08\")\n\x17SafetySensorBypassState\x12\x0e\n\x06\x62ypass\x18\x01 \x01(\x08\"\x92\x02\n\rTractorStatus\x12&\n\x05state\x18\x01 \x01(\x0b\x32\x17.carbon_tractor.HHState\x12\x12\n\nerror_flag\x18\x02 \x01(\x05\x12\x14\n\x0cground_speed\x18\x03 \x01(\x02\x12\x13\n\x0bwheel_angle\x18\x04 \x01(\x02\x12\x1d\n\x15hitch_lift_percentage\x18\x05 \x01(\x02\x12$\n\x04gear\x18\x06 \x01(\x0b\x32\x16.ots_tractor.GearState\x12\x18\n\x10safety_triggered\x18\x07 \x01(\x08\x12\x15\n\rsafety_bypass\x18\x08 \x01(\x08\x12\x16\n\x0eremote_lockout\x18\t \x01(\x08\x12\x0c\n\x04rpms\x18\n \x01(\x05\"\x0c\n\nPetRequest\"\x1e\n\rSteeringState\x12\r\n\x05\x61ngle\x18\x01 \x01(\x02\"\xae\x01\n\x10SteeringCfgState\x12\n\n\x02kp\x18\x01 \x01(\x02\x12\n\n\x02ki\x18\x02 \x01(\x02\x12\n\n\x02kd\x18\x03 \x01(\x02\x12\x16\n\x0eintegral_limit\x18\x04 \x01(\x02\x12\x16\n\x0eupdate_rate_hz\x18\x05 \x01(\x05\x12\"\n\x1amin_steering_valve_current\x18\x06 \x01(\r\x12\"\n\x1amax_steering_valve_current\x18\x07 \x01(\r\" \n\x0cPetLossState\x12\x10\n\x08use_stop\x18\x01 \x01(\x08\"*\n\x0fSpeedLimitState\x12\x17\n\x0fspeed_limit_mph\x18\x01 \x01(\x02\"\x87\x03\n\nSetRequest\x12+\n\x08hh_state\x18\x01 \x01(\x0b\x32\x17.carbon_tractor.HHStateH\x00\x12,\n\x06\x62rakes\x18\x02 \x01(\x0b\x32\x1a.carbon_tractor.BrakeStateH\x00\x12@\n\rsafety_bypass\x18\x03 \x01(\x0b\x32\'.carbon_tractor.SafetySensorBypassStateH\x00\x12\x31\n\x08steering\x18\x04 \x01(\x0b\x32\x1d.carbon_tractor.SteeringStateH\x00\x12\x38\n\x0csteering_cfg\x18\x05 \x01(\x0b\x32 .carbon_tractor.SteeringCfgStateH\x00\x12\x30\n\x08pet_loss\x18\x06 \x01(\x0b\x32\x1c.carbon_tractor.PetLossStateH\x00\x12\x36\n\x0bspeed_limit\x18\x07 \x01(\x0b\x32\x1f.carbon_tractor.SpeedLimitStateH\x00\x42\x05\n\x03set\"\x85\x03\n\x08SetReply\x12+\n\x08hh_state\x18\x01 \x01(\x0b\x32\x17.carbon_tractor.HHStateH\x00\x12,\n\x06\x62rakes\x18\x02 \x01(\x0b\x32\x1a.carbon_tractor.BrakeStateH\x00\x12@\n\rsafety_bypass\x18\x03 \x01(\x0b\x32\'.carbon_tractor.SafetySensorBypassStateH\x00\x12\x31\n\x08steering\x18\x04 \x01(\x0b\x32\x1d.carbon_tractor.SteeringStateH\x00\x12\x38\n\x0csteering_cfg\x18\x05 \x01(\x0b\x32 .carbon_tractor.SteeringCfgStateH\x00\x12\x30\n\x08pet_loss\x18\x06 \x01(\x0b\x32\x1c.carbon_tractor.PetLossStateH\x00\x12\x36\n\x0bspeed_limit\x18\x07 \x01(\x0b\x32\x1f.carbon_tractor.SpeedLimitStateH\x00\x42\x05\n\x03set\"\x9c\x03\n\nGetRequest\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x15.carbon_tractor.EmptyH\x00\x12)\n\x08hh_state\x18\x02 \x01(\x0b\x32\x15.carbon_tractor.EmptyH\x00\x12\'\n\x06\x62rakes\x18\x03 \x01(\x0b\x32\x15.carbon_tractor.EmptyH\x00\x12\'\n\x06safety\x18\x04 \x01(\x0b\x32\x15.carbon_tractor.EmptyH\x00\x12.\n\rsafety_bypass\x18\x05 \x01(\x0b\x32\x15.carbon_tractor.EmptyH\x00\x12)\n\x08steering\x18\x06 \x01(\x0b\x32\x15.carbon_tractor.EmptyH\x00\x12-\n\x0csteering_cfg\x18\x07 \x01(\x0b\x32\x15.carbon_tractor.EmptyH\x00\x12)\n\x08pet_loss\x18\x08 \x01(\x0b\x32\x15.carbon_tractor.EmptyH\x00\x12,\n\x0bspeed_limit\x18\t \x01(\x0b\x32\x15.carbon_tractor.EmptyH\x00\x42\x05\n\x03get\"\xec\x03\n\x08GetReply\x12/\n\x06status\x18\x01 \x01(\x0b\x32\x1d.carbon_tractor.TractorStatusH\x00\x12+\n\x08hh_state\x18\x02 \x01(\x0b\x32\x17.carbon_tractor.HHStateH\x00\x12,\n\x06\x62rakes\x18\x03 \x01(\x0b\x32\x1a.carbon_tractor.BrakeStateH\x00\x12\x34\n\x06safety\x18\x04 \x01(\x0b\x32\".carbon_tractor.SafetySensorsStateH\x00\x12@\n\rsafety_bypass\x18\x05 \x01(\x0b\x32\'.carbon_tractor.SafetySensorBypassStateH\x00\x12\x31\n\x08steering\x18\x06 \x01(\x0b\x32\x1d.carbon_tractor.SteeringStateH\x00\x12\x38\n\x0csteering_cfg\x18\x07 \x01(\x0b\x32 .carbon_tractor.SteeringCfgStateH\x00\x12\x30\n\x08pet_loss\x18\x08 \x01(\x0b\x32\x1c.carbon_tractor.PetLossStateH\x00\x12\x36\n\x0bspeed_limit\x18\t \x01(\x0b\x32\x1f.carbon_tractor.SpeedLimitStateH\x00\x42\x05\n\x03get\"\x0e\n\x0c\x44\x65\x62ugRequest\"\x0c\n\nDebugReply\"\xc4\x01\n\x07Request\x12)\n\x03set\x18\x01 \x01(\x0b\x32\x1a.carbon_tractor.SetRequestH\x00\x12)\n\x03get\x18\x02 \x01(\x0b\x32\x1a.carbon_tractor.GetRequestH\x00\x12)\n\x03pet\x18\x03 \x01(\x0b\x32\x1a.carbon_tractor.PetRequestH\x00\x12-\n\x05\x64\x65\x62ug\x18\x04 \x01(\x0b\x32\x1c.carbon_tractor.DebugRequestH\x00\x42\t\n\x07request\"\xbd\x01\n\x05Reply\x12\'\n\x03set\x18\x01 \x01(\x0b\x32\x18.carbon_tractor.SetReplyH\x00\x12\'\n\x03get\x18\x02 \x01(\x0b\x32\x18.carbon_tractor.GetReplyH\x00\x12,\n\x03pet\x18\x03 \x01(\x0b\x32\x1d.carbon_tractor.TractorStatusH\x00\x12+\n\x05\x64\x65\x62ug\x18\x04 \x01(\x0b\x32\x1a.carbon_tractor.DebugReplyH\x00\x42\x07\n\x05reply*o\n\rHHStateStatus\x12\x0e\n\nHH_UNKNOWN\x10\x00\x12\x0f\n\x0bHH_DISABLED\x10\x01\x12\x12\n\x0eHH_OPERATIONAL\x10\x02\x12\x0e\n\nHH_STOPPED\x10\x03\x12\x0b\n\x07HH_SAFE\x10\x04\x12\x0c\n\x08HH_ESTOP\x10\x05\x42\x17Z\x15nanopb/carbon_tractorb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ots__tractor__pb2.DESCRIPTOR,])

_HHSTATESTATUS = _descriptor.EnumDescriptor(
  name='HHStateStatus',
  full_name='carbon_tractor.HHStateStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='HH_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_DISABLED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_OPERATIONAL', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_STOPPED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_SAFE', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_ESTOP', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3123,
  serialized_end=3234,
)
_sym_db.RegisterEnumDescriptor(_HHSTATESTATUS)

HHStateStatus = enum_type_wrapper.EnumTypeWrapper(_HHSTATESTATUS)
HH_UNKNOWN = 0
HH_DISABLED = 1
HH_OPERATIONAL = 2
HH_STOPPED = 3
HH_SAFE = 4
HH_ESTOP = 5



_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='carbon_tractor.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=129,
  serialized_end=136,
)


_HHSTATE = _descriptor.Descriptor(
  name='HHState',
  full_name='carbon_tractor.HHState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon_tractor.HHState.state', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=138,
  serialized_end=193,
)


_BRAKESTATE = _descriptor.Descriptor(
  name='BrakeState',
  full_name='carbon_tractor.BrakeState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='force_left', full_name='carbon_tractor.BrakeState.force_left', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='force_right', full_name='carbon_tractor.BrakeState.force_right', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=195,
  serialized_end=248,
)


_SAFETYSENSORSSTATE = _descriptor.Descriptor(
  name='SafetySensorsState',
  full_name='carbon_tractor.SafetySensorsState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='triggered_sensor_1', full_name='carbon_tractor.SafetySensorsState.triggered_sensor_1', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='triggered_sensor_2', full_name='carbon_tractor.SafetySensorsState.triggered_sensor_2', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='triggered_sensor_3', full_name='carbon_tractor.SafetySensorsState.triggered_sensor_3', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='triggered_sensor_4', full_name='carbon_tractor.SafetySensorsState.triggered_sensor_4', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=251,
  serialized_end=383,
)


_SAFETYSENSORBYPASSSTATE = _descriptor.Descriptor(
  name='SafetySensorBypassState',
  full_name='carbon_tractor.SafetySensorBypassState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='bypass', full_name='carbon_tractor.SafetySensorBypassState.bypass', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=385,
  serialized_end=426,
)


_TRACTORSTATUS = _descriptor.Descriptor(
  name='TractorStatus',
  full_name='carbon_tractor.TractorStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon_tractor.TractorStatus.state', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_flag', full_name='carbon_tractor.TractorStatus.error_flag', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ground_speed', full_name='carbon_tractor.TractorStatus.ground_speed', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_angle', full_name='carbon_tractor.TractorStatus.wheel_angle', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hitch_lift_percentage', full_name='carbon_tractor.TractorStatus.hitch_lift_percentage', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gear', full_name='carbon_tractor.TractorStatus.gear', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_triggered', full_name='carbon_tractor.TractorStatus.safety_triggered', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_bypass', full_name='carbon_tractor.TractorStatus.safety_bypass', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='remote_lockout', full_name='carbon_tractor.TractorStatus.remote_lockout', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rpms', full_name='carbon_tractor.TractorStatus.rpms', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=429,
  serialized_end=703,
)


_PETREQUEST = _descriptor.Descriptor(
  name='PetRequest',
  full_name='carbon_tractor.PetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=705,
  serialized_end=717,
)


_STEERINGSTATE = _descriptor.Descriptor(
  name='SteeringState',
  full_name='carbon_tractor.SteeringState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='angle', full_name='carbon_tractor.SteeringState.angle', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=719,
  serialized_end=749,
)


_STEERINGCFGSTATE = _descriptor.Descriptor(
  name='SteeringCfgState',
  full_name='carbon_tractor.SteeringCfgState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='kp', full_name='carbon_tractor.SteeringCfgState.kp', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ki', full_name='carbon_tractor.SteeringCfgState.ki', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='kd', full_name='carbon_tractor.SteeringCfgState.kd', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='integral_limit', full_name='carbon_tractor.SteeringCfgState.integral_limit', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='update_rate_hz', full_name='carbon_tractor.SteeringCfgState.update_rate_hz', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_steering_valve_current', full_name='carbon_tractor.SteeringCfgState.min_steering_valve_current', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_steering_valve_current', full_name='carbon_tractor.SteeringCfgState.max_steering_valve_current', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=752,
  serialized_end=926,
)


_PETLOSSSTATE = _descriptor.Descriptor(
  name='PetLossState',
  full_name='carbon_tractor.PetLossState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='use_stop', full_name='carbon_tractor.PetLossState.use_stop', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=928,
  serialized_end=960,
)


_SPEEDLIMITSTATE = _descriptor.Descriptor(
  name='SpeedLimitState',
  full_name='carbon_tractor.SpeedLimitState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='speed_limit_mph', full_name='carbon_tractor.SpeedLimitState.speed_limit_mph', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=962,
  serialized_end=1004,
)


_SETREQUEST = _descriptor.Descriptor(
  name='SetRequest',
  full_name='carbon_tractor.SetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='hh_state', full_name='carbon_tractor.SetRequest.hh_state', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='brakes', full_name='carbon_tractor.SetRequest.brakes', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_bypass', full_name='carbon_tractor.SetRequest.safety_bypass', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steering', full_name='carbon_tractor.SetRequest.steering', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steering_cfg', full_name='carbon_tractor.SetRequest.steering_cfg', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pet_loss', full_name='carbon_tractor.SetRequest.pet_loss', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_limit', full_name='carbon_tractor.SetRequest.speed_limit', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='set', full_name='carbon_tractor.SetRequest.set',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1007,
  serialized_end=1398,
)


_SETREPLY = _descriptor.Descriptor(
  name='SetReply',
  full_name='carbon_tractor.SetReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='hh_state', full_name='carbon_tractor.SetReply.hh_state', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='brakes', full_name='carbon_tractor.SetReply.brakes', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_bypass', full_name='carbon_tractor.SetReply.safety_bypass', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steering', full_name='carbon_tractor.SetReply.steering', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steering_cfg', full_name='carbon_tractor.SetReply.steering_cfg', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pet_loss', full_name='carbon_tractor.SetReply.pet_loss', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_limit', full_name='carbon_tractor.SetReply.speed_limit', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='set', full_name='carbon_tractor.SetReply.set',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1401,
  serialized_end=1790,
)


_GETREQUEST = _descriptor.Descriptor(
  name='GetRequest',
  full_name='carbon_tractor.GetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='carbon_tractor.GetRequest.status', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hh_state', full_name='carbon_tractor.GetRequest.hh_state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='brakes', full_name='carbon_tractor.GetRequest.brakes', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety', full_name='carbon_tractor.GetRequest.safety', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_bypass', full_name='carbon_tractor.GetRequest.safety_bypass', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steering', full_name='carbon_tractor.GetRequest.steering', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steering_cfg', full_name='carbon_tractor.GetRequest.steering_cfg', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pet_loss', full_name='carbon_tractor.GetRequest.pet_loss', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_limit', full_name='carbon_tractor.GetRequest.speed_limit', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='get', full_name='carbon_tractor.GetRequest.get',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1793,
  serialized_end=2205,
)


_GETREPLY = _descriptor.Descriptor(
  name='GetReply',
  full_name='carbon_tractor.GetReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='carbon_tractor.GetReply.status', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hh_state', full_name='carbon_tractor.GetReply.hh_state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='brakes', full_name='carbon_tractor.GetReply.brakes', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety', full_name='carbon_tractor.GetReply.safety', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_bypass', full_name='carbon_tractor.GetReply.safety_bypass', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steering', full_name='carbon_tractor.GetReply.steering', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='steering_cfg', full_name='carbon_tractor.GetReply.steering_cfg', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pet_loss', full_name='carbon_tractor.GetReply.pet_loss', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_limit', full_name='carbon_tractor.GetReply.speed_limit', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='get', full_name='carbon_tractor.GetReply.get',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2208,
  serialized_end=2700,
)


_DEBUGREQUEST = _descriptor.Descriptor(
  name='DebugRequest',
  full_name='carbon_tractor.DebugRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2702,
  serialized_end=2716,
)


_DEBUGREPLY = _descriptor.Descriptor(
  name='DebugReply',
  full_name='carbon_tractor.DebugReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2718,
  serialized_end=2730,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='carbon_tractor.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='set', full_name='carbon_tractor.Request.set', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get', full_name='carbon_tractor.Request.get', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pet', full_name='carbon_tractor.Request.pet', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='debug', full_name='carbon_tractor.Request.debug', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='carbon_tractor.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2733,
  serialized_end=2929,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='carbon_tractor.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='set', full_name='carbon_tractor.Reply.set', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get', full_name='carbon_tractor.Reply.get', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pet', full_name='carbon_tractor.Reply.pet', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='debug', full_name='carbon_tractor.Reply.debug', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='carbon_tractor.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2932,
  serialized_end=3121,
)

_HHSTATE.fields_by_name['state'].enum_type = _HHSTATESTATUS
_TRACTORSTATUS.fields_by_name['state'].message_type = _HHSTATE
_TRACTORSTATUS.fields_by_name['gear'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ots__tractor__pb2._GEARSTATE
_SETREQUEST.fields_by_name['hh_state'].message_type = _HHSTATE
_SETREQUEST.fields_by_name['brakes'].message_type = _BRAKESTATE
_SETREQUEST.fields_by_name['safety_bypass'].message_type = _SAFETYSENSORBYPASSSTATE
_SETREQUEST.fields_by_name['steering'].message_type = _STEERINGSTATE
_SETREQUEST.fields_by_name['steering_cfg'].message_type = _STEERINGCFGSTATE
_SETREQUEST.fields_by_name['pet_loss'].message_type = _PETLOSSSTATE
_SETREQUEST.fields_by_name['speed_limit'].message_type = _SPEEDLIMITSTATE
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['hh_state'])
_SETREQUEST.fields_by_name['hh_state'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['brakes'])
_SETREQUEST.fields_by_name['brakes'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['safety_bypass'])
_SETREQUEST.fields_by_name['safety_bypass'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['steering'])
_SETREQUEST.fields_by_name['steering'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['steering_cfg'])
_SETREQUEST.fields_by_name['steering_cfg'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['pet_loss'])
_SETREQUEST.fields_by_name['pet_loss'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['speed_limit'])
_SETREQUEST.fields_by_name['speed_limit'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREPLY.fields_by_name['hh_state'].message_type = _HHSTATE
_SETREPLY.fields_by_name['brakes'].message_type = _BRAKESTATE
_SETREPLY.fields_by_name['safety_bypass'].message_type = _SAFETYSENSORBYPASSSTATE
_SETREPLY.fields_by_name['steering'].message_type = _STEERINGSTATE
_SETREPLY.fields_by_name['steering_cfg'].message_type = _STEERINGCFGSTATE
_SETREPLY.fields_by_name['pet_loss'].message_type = _PETLOSSSTATE
_SETREPLY.fields_by_name['speed_limit'].message_type = _SPEEDLIMITSTATE
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['hh_state'])
_SETREPLY.fields_by_name['hh_state'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['brakes'])
_SETREPLY.fields_by_name['brakes'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['safety_bypass'])
_SETREPLY.fields_by_name['safety_bypass'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['steering'])
_SETREPLY.fields_by_name['steering'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['steering_cfg'])
_SETREPLY.fields_by_name['steering_cfg'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['pet_loss'])
_SETREPLY.fields_by_name['pet_loss'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['speed_limit'])
_SETREPLY.fields_by_name['speed_limit'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_GETREQUEST.fields_by_name['status'].message_type = _EMPTY
_GETREQUEST.fields_by_name['hh_state'].message_type = _EMPTY
_GETREQUEST.fields_by_name['brakes'].message_type = _EMPTY
_GETREQUEST.fields_by_name['safety'].message_type = _EMPTY
_GETREQUEST.fields_by_name['safety_bypass'].message_type = _EMPTY
_GETREQUEST.fields_by_name['steering'].message_type = _EMPTY
_GETREQUEST.fields_by_name['steering_cfg'].message_type = _EMPTY
_GETREQUEST.fields_by_name['pet_loss'].message_type = _EMPTY
_GETREQUEST.fields_by_name['speed_limit'].message_type = _EMPTY
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['status'])
_GETREQUEST.fields_by_name['status'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['hh_state'])
_GETREQUEST.fields_by_name['hh_state'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['brakes'])
_GETREQUEST.fields_by_name['brakes'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['safety'])
_GETREQUEST.fields_by_name['safety'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['safety_bypass'])
_GETREQUEST.fields_by_name['safety_bypass'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['steering'])
_GETREQUEST.fields_by_name['steering'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['steering_cfg'])
_GETREQUEST.fields_by_name['steering_cfg'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['pet_loss'])
_GETREQUEST.fields_by_name['pet_loss'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['speed_limit'])
_GETREQUEST.fields_by_name['speed_limit'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREPLY.fields_by_name['status'].message_type = _TRACTORSTATUS
_GETREPLY.fields_by_name['hh_state'].message_type = _HHSTATE
_GETREPLY.fields_by_name['brakes'].message_type = _BRAKESTATE
_GETREPLY.fields_by_name['safety'].message_type = _SAFETYSENSORSSTATE
_GETREPLY.fields_by_name['safety_bypass'].message_type = _SAFETYSENSORBYPASSSTATE
_GETREPLY.fields_by_name['steering'].message_type = _STEERINGSTATE
_GETREPLY.fields_by_name['steering_cfg'].message_type = _STEERINGCFGSTATE
_GETREPLY.fields_by_name['pet_loss'].message_type = _PETLOSSSTATE
_GETREPLY.fields_by_name['speed_limit'].message_type = _SPEEDLIMITSTATE
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['status'])
_GETREPLY.fields_by_name['status'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['hh_state'])
_GETREPLY.fields_by_name['hh_state'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['brakes'])
_GETREPLY.fields_by_name['brakes'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['safety'])
_GETREPLY.fields_by_name['safety'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['safety_bypass'])
_GETREPLY.fields_by_name['safety_bypass'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['steering'])
_GETREPLY.fields_by_name['steering'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['steering_cfg'])
_GETREPLY.fields_by_name['steering_cfg'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['pet_loss'])
_GETREPLY.fields_by_name['pet_loss'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['speed_limit'])
_GETREPLY.fields_by_name['speed_limit'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_REQUEST.fields_by_name['set'].message_type = _SETREQUEST
_REQUEST.fields_by_name['get'].message_type = _GETREQUEST
_REQUEST.fields_by_name['pet'].message_type = _PETREQUEST
_REQUEST.fields_by_name['debug'].message_type = _DEBUGREQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set'])
_REQUEST.fields_by_name['set'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get'])
_REQUEST.fields_by_name['get'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['pet'])
_REQUEST.fields_by_name['pet'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['debug'])
_REQUEST.fields_by_name['debug'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['set'].message_type = _SETREPLY
_REPLY.fields_by_name['get'].message_type = _GETREPLY
_REPLY.fields_by_name['pet'].message_type = _TRACTORSTATUS
_REPLY.fields_by_name['debug'].message_type = _DEBUGREPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['set'])
_REPLY.fields_by_name['set'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['get'])
_REPLY.fields_by_name['get'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pet'])
_REPLY.fields_by_name['pet'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['debug'])
_REPLY.fields_by_name['debug'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['HHState'] = _HHSTATE
DESCRIPTOR.message_types_by_name['BrakeState'] = _BRAKESTATE
DESCRIPTOR.message_types_by_name['SafetySensorsState'] = _SAFETYSENSORSSTATE
DESCRIPTOR.message_types_by_name['SafetySensorBypassState'] = _SAFETYSENSORBYPASSSTATE
DESCRIPTOR.message_types_by_name['TractorStatus'] = _TRACTORSTATUS
DESCRIPTOR.message_types_by_name['PetRequest'] = _PETREQUEST
DESCRIPTOR.message_types_by_name['SteeringState'] = _STEERINGSTATE
DESCRIPTOR.message_types_by_name['SteeringCfgState'] = _STEERINGCFGSTATE
DESCRIPTOR.message_types_by_name['PetLossState'] = _PETLOSSSTATE
DESCRIPTOR.message_types_by_name['SpeedLimitState'] = _SPEEDLIMITSTATE
DESCRIPTOR.message_types_by_name['SetRequest'] = _SETREQUEST
DESCRIPTOR.message_types_by_name['SetReply'] = _SETREPLY
DESCRIPTOR.message_types_by_name['GetRequest'] = _GETREQUEST
DESCRIPTOR.message_types_by_name['GetReply'] = _GETREPLY
DESCRIPTOR.message_types_by_name['DebugRequest'] = _DEBUGREQUEST
DESCRIPTOR.message_types_by_name['DebugReply'] = _DEBUGREPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.enum_types_by_name['HHStateStatus'] = _HHSTATESTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.Empty)
  })
_sym_db.RegisterMessage(Empty)

HHState = _reflection.GeneratedProtocolMessageType('HHState', (_message.Message,), {
  'DESCRIPTOR' : _HHSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.HHState)
  })
_sym_db.RegisterMessage(HHState)

BrakeState = _reflection.GeneratedProtocolMessageType('BrakeState', (_message.Message,), {
  'DESCRIPTOR' : _BRAKESTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.BrakeState)
  })
_sym_db.RegisterMessage(BrakeState)

SafetySensorsState = _reflection.GeneratedProtocolMessageType('SafetySensorsState', (_message.Message,), {
  'DESCRIPTOR' : _SAFETYSENSORSSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.SafetySensorsState)
  })
_sym_db.RegisterMessage(SafetySensorsState)

SafetySensorBypassState = _reflection.GeneratedProtocolMessageType('SafetySensorBypassState', (_message.Message,), {
  'DESCRIPTOR' : _SAFETYSENSORBYPASSSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.SafetySensorBypassState)
  })
_sym_db.RegisterMessage(SafetySensorBypassState)

TractorStatus = _reflection.GeneratedProtocolMessageType('TractorStatus', (_message.Message,), {
  'DESCRIPTOR' : _TRACTORSTATUS,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.TractorStatus)
  })
_sym_db.RegisterMessage(TractorStatus)

PetRequest = _reflection.GeneratedProtocolMessageType('PetRequest', (_message.Message,), {
  'DESCRIPTOR' : _PETREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.PetRequest)
  })
_sym_db.RegisterMessage(PetRequest)

SteeringState = _reflection.GeneratedProtocolMessageType('SteeringState', (_message.Message,), {
  'DESCRIPTOR' : _STEERINGSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.SteeringState)
  })
_sym_db.RegisterMessage(SteeringState)

SteeringCfgState = _reflection.GeneratedProtocolMessageType('SteeringCfgState', (_message.Message,), {
  'DESCRIPTOR' : _STEERINGCFGSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.SteeringCfgState)
  })
_sym_db.RegisterMessage(SteeringCfgState)

PetLossState = _reflection.GeneratedProtocolMessageType('PetLossState', (_message.Message,), {
  'DESCRIPTOR' : _PETLOSSSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.PetLossState)
  })
_sym_db.RegisterMessage(PetLossState)

SpeedLimitState = _reflection.GeneratedProtocolMessageType('SpeedLimitState', (_message.Message,), {
  'DESCRIPTOR' : _SPEEDLIMITSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.SpeedLimitState)
  })
_sym_db.RegisterMessage(SpeedLimitState)

SetRequest = _reflection.GeneratedProtocolMessageType('SetRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.SetRequest)
  })
_sym_db.RegisterMessage(SetRequest)

SetReply = _reflection.GeneratedProtocolMessageType('SetReply', (_message.Message,), {
  'DESCRIPTOR' : _SETREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.SetReply)
  })
_sym_db.RegisterMessage(SetReply)

GetRequest = _reflection.GeneratedProtocolMessageType('GetRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.GetRequest)
  })
_sym_db.RegisterMessage(GetRequest)

GetReply = _reflection.GeneratedProtocolMessageType('GetReply', (_message.Message,), {
  'DESCRIPTOR' : _GETREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.GetReply)
  })
_sym_db.RegisterMessage(GetReply)

DebugRequest = _reflection.GeneratedProtocolMessageType('DebugRequest', (_message.Message,), {
  'DESCRIPTOR' : _DEBUGREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.DebugRequest)
  })
_sym_db.RegisterMessage(DebugRequest)

DebugReply = _reflection.GeneratedProtocolMessageType('DebugReply', (_message.Message,), {
  'DESCRIPTOR' : _DEBUGREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.DebugReply)
  })
_sym_db.RegisterMessage(DebugReply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.carbon_tractor_pb2'
  # @@protoc_insertion_point(class_scope:carbon_tractor.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

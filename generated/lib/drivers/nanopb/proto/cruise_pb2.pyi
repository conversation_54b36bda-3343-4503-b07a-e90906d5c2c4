"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.diagnostic_pb2 import (
    Ping as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping,
    Pong as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

TractorVariantTypeValue = typing___NewType('TractorVariantTypeValue', builtin___int)
type___TractorVariantTypeValue = TractorVariantTypeValue
TractorVariantType: _TractorVariantType
class _TractorVariantType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[TractorVariantTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    TV_UNKNOWN = typing___cast(TractorVariantTypeValue, 0)
    TV_JD_6LH = typing___cast(TractorVariantTypeValue, 1)
    TV_JD_6LHM = typing___cast(TractorVariantTypeValue, 2)
    TV_JD_6PRO = typing___cast(TractorVariantTypeValue, 3)
    TV_JD_7RH = typing___cast(TractorVariantTypeValue, 4)
    TV_JD_8PRO = typing___cast(TractorVariantTypeValue, 5)
TV_UNKNOWN = typing___cast(TractorVariantTypeValue, 0)
TV_JD_6LH = typing___cast(TractorVariantTypeValue, 1)
TV_JD_6LHM = typing___cast(TractorVariantTypeValue, 2)
TV_JD_6PRO = typing___cast(TractorVariantTypeValue, 3)
TV_JD_7RH = typing___cast(TractorVariantTypeValue, 4)
TV_JD_8PRO = typing___cast(TractorVariantTypeValue, 5)

class Throttle_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    change: builtin___int = ...

    def __init__(self,
        *,
        change : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"change",b"change"]) -> None: ...
type___Throttle_Request = Throttle_Request

class Throttle_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___Throttle_Reply = Throttle_Reply

class Status_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Status_Request = Status_Request

class Status_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...
    unavailable: builtin___bool = ...
    speed_ticks: builtin___int = ...
    speed_lever_position: builtin___int = ...
    speed_lever_s1: builtin___bool = ...
    speed_lever_s2: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        unavailable : typing___Optional[builtin___bool] = None,
        speed_ticks : typing___Optional[builtin___int] = None,
        speed_lever_position : typing___Optional[builtin___int] = None,
        speed_lever_s1 : typing___Optional[builtin___bool] = None,
        speed_lever_s2 : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"speed_lever_position",b"speed_lever_position",u"speed_lever_s1",b"speed_lever_s1",u"speed_lever_s2",b"speed_lever_s2",u"speed_ticks",b"speed_ticks",u"unavailable",b"unavailable"]) -> None: ...
type___Status_Reply = Status_Reply

class Enable_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___Enable_Request = Enable_Request

class Enable_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___Enable_Reply = Enable_Reply

class Gear_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    gear: builtin___int = ...

    def __init__(self,
        *,
        gear : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"gear",b"gear"]) -> None: ...
type___Gear_Request = Gear_Request

class Gear_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___Gear_Reply = Gear_Reply

class Variant_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    variant: type___TractorVariantTypeValue = ...

    def __init__(self,
        *,
        variant : typing___Optional[type___TractorVariantTypeValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"variant",b"variant"]) -> None: ...
type___Variant_Request = Variant_Request

class Variant_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    variant: type___TractorVariantTypeValue = ...

    def __init__(self,
        *,
        variant : typing___Optional[type___TractorVariantTypeValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"variant",b"variant"]) -> None: ...
type___Variant_Reply = Variant_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def throttle(self) -> type___Throttle_Request: ...

    @property
    def status(self) -> type___Status_Request: ...

    @property
    def enable(self) -> type___Enable_Request: ...

    @property
    def gear(self) -> type___Gear_Request: ...

    @property
    def variant(self) -> type___Variant_Request: ...

    def __init__(self,
        *,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        throttle : typing___Optional[type___Throttle_Request] = None,
        status : typing___Optional[type___Status_Request] = None,
        enable : typing___Optional[type___Enable_Request] = None,
        gear : typing___Optional[type___Gear_Request] = None,
        variant : typing___Optional[type___Variant_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"enable",b"enable",u"gear",b"gear",u"ping",b"ping",u"request",b"request",u"status",b"status",u"throttle",b"throttle",u"variant",b"variant"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enable",b"enable",u"gear",b"gear",u"ping",b"ping",u"request",b"request",u"status",b"status",u"throttle",b"throttle",u"variant",b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","throttle","status","enable","gear","variant"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def throttle(self) -> type___Throttle_Reply: ...

    @property
    def status(self) -> type___Status_Reply: ...

    @property
    def enable(self) -> type___Enable_Reply: ...

    @property
    def gear(self) -> type___Gear_Reply: ...

    @property
    def variant(self) -> type___Variant_Reply: ...

    def __init__(self,
        *,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        throttle : typing___Optional[type___Throttle_Reply] = None,
        status : typing___Optional[type___Status_Reply] = None,
        enable : typing___Optional[type___Enable_Reply] = None,
        gear : typing___Optional[type___Gear_Reply] = None,
        variant : typing___Optional[type___Variant_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"enable",b"enable",u"gear",b"gear",u"pong",b"pong",u"reply",b"reply",u"status",b"status",u"throttle",b"throttle",u"variant",b"variant"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enable",b"enable",u"gear",b"gear",u"pong",b"pong",u"reply",b"reply",u"status",b"status",u"throttle",b"throttle",u"variant",b"variant"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["pong","throttle","status","enable","gear","variant"]: ...
type___Reply = Reply

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/lens.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/lens.proto',
  package='lens',
  syntax='proto3',
  serialized_options=b'Z\013nanopb/lens',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n-generated/lib/drivers/nanopb/proto/lens.proto\x12\x04lens\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\"\x1c\n\x0bSet_Request\x12\r\n\x05value\x18\x01 \x01(\r\"\r\n\x0bGet_Request\"\x1a\n\tGet_Reply\x12\r\n\x05value\x18\x01 \x01(\r\"X\n\x07Request\x12 \n\x03set\x18\x01 \x01(\x0b\x32\x11.lens.Set_RequestH\x00\x12 \n\x03get\x18\x02 \x01(\x0b\x32\x11.lens.Get_RequestH\x00\x42\t\n\x07request\"h\n\x05Reply\x12\x1d\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x02 \x01(\x0b\x32\x08.ack.AckH\x00\x12\x1e\n\x03get\x18\x03 \x01(\x0b\x32\x0f.lens.Get_ReplyH\x00\x42\x07\n\x05replyB\rZ\x0bnanopb/lensb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,])




_SET_REQUEST = _descriptor.Descriptor(
  name='Set_Request',
  full_name='lens.Set_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='lens.Set_Request.value', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=149,
  serialized_end=177,
)


_GET_REQUEST = _descriptor.Descriptor(
  name='Get_Request',
  full_name='lens.Get_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=179,
  serialized_end=192,
)


_GET_REPLY = _descriptor.Descriptor(
  name='Get_Reply',
  full_name='lens.Get_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='lens.Get_Reply.value', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=194,
  serialized_end=220,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='lens.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='set', full_name='lens.Request.set', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get', full_name='lens.Request.get', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='lens.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=222,
  serialized_end=310,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='lens.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='lens.Reply.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='lens.Reply.ack', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get', full_name='lens.Reply.get', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='lens.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=312,
  serialized_end=416,
)

_REQUEST.fields_by_name['set'].message_type = _SET_REQUEST
_REQUEST.fields_by_name['get'].message_type = _GET_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set'])
_REQUEST.fields_by_name['set'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get'])
_REQUEST.fields_by_name['get'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['get'].message_type = _GET_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['get'])
_REPLY.fields_by_name['get'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Set_Request'] = _SET_REQUEST
DESCRIPTOR.message_types_by_name['Get_Request'] = _GET_REQUEST
DESCRIPTOR.message_types_by_name['Get_Reply'] = _GET_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Set_Request = _reflection.GeneratedProtocolMessageType('Set_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.lens_pb2'
  # @@protoc_insertion_point(class_scope:lens.Set_Request)
  })
_sym_db.RegisterMessage(Set_Request)

Get_Request = _reflection.GeneratedProtocolMessageType('Get_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.lens_pb2'
  # @@protoc_insertion_point(class_scope:lens.Get_Request)
  })
_sym_db.RegisterMessage(Get_Request)

Get_Reply = _reflection.GeneratedProtocolMessageType('Get_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GET_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.lens_pb2'
  # @@protoc_insertion_point(class_scope:lens.Get_Reply)
  })
_sym_db.RegisterMessage(Get_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.lens_pb2'
  # @@protoc_insertion_point(class_scope:lens.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.lens_pb2'
  # @@protoc_insertion_point(class_scope:lens.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/camera_power_control.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/camera_power_control.proto',
  package='camera_power_control',
  syntax='proto3',
  serialized_options=b'Z\033nanopb/camera_power_control',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n=generated/lib/drivers/nanopb/proto/camera_power_control.proto\x12\x14\x63\x61mera_power_control\".\n\x07Request\x12\x11\n\tcamera_id\x18\x01 \x01(\r\x12\x10\n\x08power_on\x18\x02 \x01(\x08\"\x13\n\x05Reply\x12\n\n\x02ok\x18\x01 \x01(\x08\x42\x1dZ\x1bnanopb/camera_power_controlb\x06proto3'
)




_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='camera_power_control.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='camera_id', full_name='camera_power_control.Request.camera_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_on', full_name='camera_power_control.Request.power_on', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=87,
  serialized_end=133,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='camera_power_control.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ok', full_name='camera_power_control.Reply.ok', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=135,
  serialized_end=154,
)

DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.camera_power_control_pb2'
  # @@protoc_insertion_point(class_scope:camera_power_control.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.camera_power_control_pb2'
  # @@protoc_insertion_point(class_scope:camera_power_control.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

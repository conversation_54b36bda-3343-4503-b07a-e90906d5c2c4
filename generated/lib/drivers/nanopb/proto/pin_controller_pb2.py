# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/pin_controller.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import diagnostic_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2
from generated.lib.drivers.nanopb.proto import request_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2
from generated.lib.drivers.nanopb.proto import version_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/pin_controller.proto',
  package='pin_controller',
  syntax='proto3',
  serialized_options=b'Z\025nanopb/pin_controller',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n7generated/lib/drivers/nanopb/proto/pin_controller.proto\x12\x0epin_controller\x1a\x33generated/lib/drivers/nanopb/proto/diagnostic.proto\x1a\x30generated/lib/drivers/nanopb/proto/request.proto\x1a\x30generated/lib/drivers/nanopb/proto/version.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\"4\n\x0f\x41nalogReadValue\x12\x12\n\npin_number\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\r\"V\n\x10\x44igitalReadValue\x12\x12\n\npin_number\x18\x01 \x01(\r\x12.\n\x05value\x18\x02 \x01(\x0e\x32\x1f.pin_controller.DigitalPinValue\"\x91\x01\n\x08PinReply\x12\x17\n\x03\x61\x63k\x18\x01 \x01(\x0b\x32\x08.ack.AckH\x00\x12\x30\n\x04read\x18\x02 \x01(\x0b\x32 .pin_controller.DigitalReadValueH\x00\x12\x31\n\x06\x61nalog\x18\x03 \x01(\x0b\x32\x1f.pin_controller.AnalogReadValueH\x00\x42\x07\n\x05reply\" \n\nAnalogRead\x12\x12\n\npin_number\x18\x01 \x01(\r\"!\n\x0b\x44igitalRead\x12\x12\n\npin_number\x18\x01 \x01(\r\"R\n\x0c\x44igitalWrite\x12\x12\n\npin_number\x18\x01 \x01(\r\x12.\n\x05value\x18\x02 \x01(\x0e\x32\x1f.pin_controller.DigitalPinValue\"Q\n\x10PinConfiguration\x12\x12\n\npin_number\x18\x01 \x01(\r\x12)\n\x08pin_mode\x18\x02 \x01(\x0e\x32\x17.pin_controller.PinMode\":\n\x10PWMConfiguration\x12\x12\n\npin_number\x18\x01 \x01(\r\x12\x12\n\nvalue_8bit\x18\x02 \x01(\r\"\x83\x02\n\nPinRequest\x12/\n\x03pin\x18\x01 \x01(\x0b\x32 .pin_controller.PinConfigurationH\x00\x12/\n\x03pwm\x18\x02 \x01(\x0b\x32 .pin_controller.PWMConfigurationH\x00\x12-\n\x05write\x18\x03 \x01(\x0b\x32\x1c.pin_controller.DigitalWriteH\x00\x12+\n\x04read\x18\x04 \x01(\x0b\x32\x1b.pin_controller.DigitalReadH\x00\x12,\n\x06\x61nalog\x18\x05 \x01(\x0b\x32\x1a.pin_controller.AnalogReadH\x00\x42\t\n\x07request\"\xc7\x01\n\x05Reply\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04pong\x18\x02 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12\x17\n\x03\x61\x63k\x18\x03 \x01(\x0b\x32\x08.ack.AckH\x00\x12)\n\x07version\x18\x04 \x01(\x0b\x32\x16.version.Version_ReplyH\x00\x12\'\n\x03pin\x18\x05 \x01(\x0b\x32\x18.pin_controller.PinReplyH\x00\x42\x07\n\x05reply\"\xb6\x01\n\x07Request\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12+\n\x07version\x18\x03 \x01(\x0b\x32\x18.version.Version_RequestH\x00\x12)\n\x03pin\x18\x04 \x01(\x0b\x32\x1a.pin_controller.PinRequestH\x00\x42\t\n\x07request*$\n\x0f\x44igitalPinValue\x12\x07\n\x03LOW\x10\x00\x12\x08\n\x04HIGH\x10\x01*3\n\x07PinMode\x12\t\n\x05INPUT\x10\x00\x12\n\n\x06OUTPUT\x10\x01\x12\x11\n\rINPUT_PULL_UP\x10\x02\x42\x17Z\x15nanopb/pin_controllerb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,])

_DIGITALPINVALUE = _descriptor.EnumDescriptor(
  name='DigitalPinValue',
  full_name='pin_controller.DigitalPinValue',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LOW', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HIGH', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1509,
  serialized_end=1545,
)
_sym_db.RegisterEnumDescriptor(_DIGITALPINVALUE)

DigitalPinValue = enum_type_wrapper.EnumTypeWrapper(_DIGITALPINVALUE)
_PINMODE = _descriptor.EnumDescriptor(
  name='PinMode',
  full_name='pin_controller.PinMode',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INPUT', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='OUTPUT', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INPUT_PULL_UP', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1547,
  serialized_end=1598,
)
_sym_db.RegisterEnumDescriptor(_PINMODE)

PinMode = enum_type_wrapper.EnumTypeWrapper(_PINMODE)
LOW = 0
HIGH = 1
INPUT = 0
OUTPUT = 1
INPUT_PULL_UP = 2



_ANALOGREADVALUE = _descriptor.Descriptor(
  name='AnalogReadValue',
  full_name='pin_controller.AnalogReadValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pin_number', full_name='pin_controller.AnalogReadValue.pin_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='pin_controller.AnalogReadValue.value', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=274,
  serialized_end=326,
)


_DIGITALREADVALUE = _descriptor.Descriptor(
  name='DigitalReadValue',
  full_name='pin_controller.DigitalReadValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pin_number', full_name='pin_controller.DigitalReadValue.pin_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='pin_controller.DigitalReadValue.value', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=328,
  serialized_end=414,
)


_PINREPLY = _descriptor.Descriptor(
  name='PinReply',
  full_name='pin_controller.PinReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ack', full_name='pin_controller.PinReply.ack', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='read', full_name='pin_controller.PinReply.read', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='analog', full_name='pin_controller.PinReply.analog', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='pin_controller.PinReply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=417,
  serialized_end=562,
)


_ANALOGREAD = _descriptor.Descriptor(
  name='AnalogRead',
  full_name='pin_controller.AnalogRead',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pin_number', full_name='pin_controller.AnalogRead.pin_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=564,
  serialized_end=596,
)


_DIGITALREAD = _descriptor.Descriptor(
  name='DigitalRead',
  full_name='pin_controller.DigitalRead',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pin_number', full_name='pin_controller.DigitalRead.pin_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=598,
  serialized_end=631,
)


_DIGITALWRITE = _descriptor.Descriptor(
  name='DigitalWrite',
  full_name='pin_controller.DigitalWrite',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pin_number', full_name='pin_controller.DigitalWrite.pin_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='pin_controller.DigitalWrite.value', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=633,
  serialized_end=715,
)


_PINCONFIGURATION = _descriptor.Descriptor(
  name='PinConfiguration',
  full_name='pin_controller.PinConfiguration',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pin_number', full_name='pin_controller.PinConfiguration.pin_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pin_mode', full_name='pin_controller.PinConfiguration.pin_mode', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=717,
  serialized_end=798,
)


_PWMCONFIGURATION = _descriptor.Descriptor(
  name='PWMConfiguration',
  full_name='pin_controller.PWMConfiguration',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pin_number', full_name='pin_controller.PWMConfiguration.pin_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value_8bit', full_name='pin_controller.PWMConfiguration.value_8bit', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=800,
  serialized_end=858,
)


_PINREQUEST = _descriptor.Descriptor(
  name='PinRequest',
  full_name='pin_controller.PinRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pin', full_name='pin_controller.PinRequest.pin', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pwm', full_name='pin_controller.PinRequest.pwm', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='write', full_name='pin_controller.PinRequest.write', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='read', full_name='pin_controller.PinRequest.read', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='analog', full_name='pin_controller.PinRequest.analog', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='pin_controller.PinRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=861,
  serialized_end=1120,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='pin_controller.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='pin_controller.Reply.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pong', full_name='pin_controller.Reply.pong', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='pin_controller.Reply.ack', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='pin_controller.Reply.version', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pin', full_name='pin_controller.Reply.pin', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='pin_controller.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1123,
  serialized_end=1322,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='pin_controller.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='pin_controller.Request.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ping', full_name='pin_controller.Request.ping', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='pin_controller.Request.version', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pin', full_name='pin_controller.Request.pin', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='pin_controller.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1325,
  serialized_end=1507,
)

_DIGITALREADVALUE.fields_by_name['value'].enum_type = _DIGITALPINVALUE
_PINREPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_PINREPLY.fields_by_name['read'].message_type = _DIGITALREADVALUE
_PINREPLY.fields_by_name['analog'].message_type = _ANALOGREADVALUE
_PINREPLY.oneofs_by_name['reply'].fields.append(
  _PINREPLY.fields_by_name['ack'])
_PINREPLY.fields_by_name['ack'].containing_oneof = _PINREPLY.oneofs_by_name['reply']
_PINREPLY.oneofs_by_name['reply'].fields.append(
  _PINREPLY.fields_by_name['read'])
_PINREPLY.fields_by_name['read'].containing_oneof = _PINREPLY.oneofs_by_name['reply']
_PINREPLY.oneofs_by_name['reply'].fields.append(
  _PINREPLY.fields_by_name['analog'])
_PINREPLY.fields_by_name['analog'].containing_oneof = _PINREPLY.oneofs_by_name['reply']
_DIGITALWRITE.fields_by_name['value'].enum_type = _DIGITALPINVALUE
_PINCONFIGURATION.fields_by_name['pin_mode'].enum_type = _PINMODE
_PINREQUEST.fields_by_name['pin'].message_type = _PINCONFIGURATION
_PINREQUEST.fields_by_name['pwm'].message_type = _PWMCONFIGURATION
_PINREQUEST.fields_by_name['write'].message_type = _DIGITALWRITE
_PINREQUEST.fields_by_name['read'].message_type = _DIGITALREAD
_PINREQUEST.fields_by_name['analog'].message_type = _ANALOGREAD
_PINREQUEST.oneofs_by_name['request'].fields.append(
  _PINREQUEST.fields_by_name['pin'])
_PINREQUEST.fields_by_name['pin'].containing_oneof = _PINREQUEST.oneofs_by_name['request']
_PINREQUEST.oneofs_by_name['request'].fields.append(
  _PINREQUEST.fields_by_name['pwm'])
_PINREQUEST.fields_by_name['pwm'].containing_oneof = _PINREQUEST.oneofs_by_name['request']
_PINREQUEST.oneofs_by_name['request'].fields.append(
  _PINREQUEST.fields_by_name['write'])
_PINREQUEST.fields_by_name['write'].containing_oneof = _PINREQUEST.oneofs_by_name['request']
_PINREQUEST.oneofs_by_name['request'].fields.append(
  _PINREQUEST.fields_by_name['read'])
_PINREQUEST.fields_by_name['read'].containing_oneof = _PINREQUEST.oneofs_by_name['request']
_PINREQUEST.oneofs_by_name['request'].fields.append(
  _PINREQUEST.fields_by_name['analog'])
_PINREQUEST.fields_by_name['analog'].containing_oneof = _PINREQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REPLY
_REPLY.fields_by_name['pin'].message_type = _PINREPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pong'])
_REPLY.fields_by_name['pong'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['version'])
_REPLY.fields_by_name['version'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pin'])
_REPLY.fields_by_name['pin'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REQUEST.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_REQUEST.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REQUEST
_REQUEST.fields_by_name['pin'].message_type = _PINREQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ping'])
_REQUEST.fields_by_name['ping'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['version'])
_REQUEST.fields_by_name['version'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['pin'])
_REQUEST.fields_by_name['pin'].containing_oneof = _REQUEST.oneofs_by_name['request']
DESCRIPTOR.message_types_by_name['AnalogReadValue'] = _ANALOGREADVALUE
DESCRIPTOR.message_types_by_name['DigitalReadValue'] = _DIGITALREADVALUE
DESCRIPTOR.message_types_by_name['PinReply'] = _PINREPLY
DESCRIPTOR.message_types_by_name['AnalogRead'] = _ANALOGREAD
DESCRIPTOR.message_types_by_name['DigitalRead'] = _DIGITALREAD
DESCRIPTOR.message_types_by_name['DigitalWrite'] = _DIGITALWRITE
DESCRIPTOR.message_types_by_name['PinConfiguration'] = _PINCONFIGURATION
DESCRIPTOR.message_types_by_name['PWMConfiguration'] = _PWMCONFIGURATION
DESCRIPTOR.message_types_by_name['PinRequest'] = _PINREQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.enum_types_by_name['DigitalPinValue'] = _DIGITALPINVALUE
DESCRIPTOR.enum_types_by_name['PinMode'] = _PINMODE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AnalogReadValue = _reflection.GeneratedProtocolMessageType('AnalogReadValue', (_message.Message,), {
  'DESCRIPTOR' : _ANALOGREADVALUE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.AnalogReadValue)
  })
_sym_db.RegisterMessage(AnalogReadValue)

DigitalReadValue = _reflection.GeneratedProtocolMessageType('DigitalReadValue', (_message.Message,), {
  'DESCRIPTOR' : _DIGITALREADVALUE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.DigitalReadValue)
  })
_sym_db.RegisterMessage(DigitalReadValue)

PinReply = _reflection.GeneratedProtocolMessageType('PinReply', (_message.Message,), {
  'DESCRIPTOR' : _PINREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.PinReply)
  })
_sym_db.RegisterMessage(PinReply)

AnalogRead = _reflection.GeneratedProtocolMessageType('AnalogRead', (_message.Message,), {
  'DESCRIPTOR' : _ANALOGREAD,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.AnalogRead)
  })
_sym_db.RegisterMessage(AnalogRead)

DigitalRead = _reflection.GeneratedProtocolMessageType('DigitalRead', (_message.Message,), {
  'DESCRIPTOR' : _DIGITALREAD,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.DigitalRead)
  })
_sym_db.RegisterMessage(DigitalRead)

DigitalWrite = _reflection.GeneratedProtocolMessageType('DigitalWrite', (_message.Message,), {
  'DESCRIPTOR' : _DIGITALWRITE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.DigitalWrite)
  })
_sym_db.RegisterMessage(DigitalWrite)

PinConfiguration = _reflection.GeneratedProtocolMessageType('PinConfiguration', (_message.Message,), {
  'DESCRIPTOR' : _PINCONFIGURATION,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.PinConfiguration)
  })
_sym_db.RegisterMessage(PinConfiguration)

PWMConfiguration = _reflection.GeneratedProtocolMessageType('PWMConfiguration', (_message.Message,), {
  'DESCRIPTOR' : _PWMCONFIGURATION,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.PWMConfiguration)
  })
_sym_db.RegisterMessage(PWMConfiguration)

PinRequest = _reflection.GeneratedProtocolMessageType('PinRequest', (_message.Message,), {
  'DESCRIPTOR' : _PINREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.PinRequest)
  })
_sym_db.RegisterMessage(PinRequest)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.Reply)
  })
_sym_db.RegisterMessage(Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pin_controller_pb2'
  # @@protoc_insertion_point(class_scope:pin_controller.Request)
  })
_sym_db.RegisterMessage(Request)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

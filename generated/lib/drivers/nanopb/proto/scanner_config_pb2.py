# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/scanner_config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/scanner_config.proto',
  package='scanner_config',
  syntax='proto3',
  serialized_options=b'Z\025nanopb/scanner_config',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n7generated/lib/drivers/nanopb/proto/scanner_config.proto\x12\x0escanner_config\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\":\n\x13\x44\x65lta_Target_Config\x12\x10\n\x08pan_skew\x18\x01 \x01(\x02\x12\x11\n\ttilt_skew\x18\x02 \x01(\x02\"8\n\x0c\x43olor_Config\x12\x0b\n\x03red\x18\x01 \x01(\x02\x12\r\n\x05green\x18\x02 \x01(\x02\x12\x0c\n\x04\x62lue\x18\x03 \x01(\x02\"4\n\x1b\x43\x61mera_Serial_Number_Config\x12\x15\n\rserial_number\x18\x01 \x01(\x0c\"-\n\x1aScanner_Barcode_Str_Config\x12\x0f\n\x07\x62\x61rcode\x18\x01 \x01(\x0c\"V\n\x1fSet_Delta_Target_Config_Request\x12\x33\n\x06\x63onfig\x18\x01 \x01(\x0b\x32#.scanner_config.Delta_Target_Config\"!\n\x1fGet_Delta_Target_Config_Request\"H\n\x18Set_Color_Config_Request\x12,\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x1c.scanner_config.Color_Config\"\x1a\n\x18Get_Color_Config_Request\"f\n\'Set_Camera_Serial_Number_Config_Request\x12;\n\x06\x63onfig\x18\x01 \x01(\x0b\x32+.scanner_config.Camera_Serial_Number_Config\")\n\'Get_Camera_Serial_Number_Config_Request\"+\n\x17Set_HW_Revision_Request\x12\x10\n\x08revision\x18\x01 \x01(\r\"\x19\n\x17Get_HW_Revision_Request\"]\n\x1fSet_Scanner_Barcode_Str_Request\x12:\n\x06\x63onfig\x18\x01 \x01(\x0b\x32*.scanner_config.Scanner_Barcode_Str_Config\"!\n\x1fGet_Scanner_Barcode_Str_Request\"T\n\x1dGet_Delta_Target_Config_Reply\x12\x33\n\x06\x63onfig\x18\x01 \x01(\x0b\x32#.scanner_config.Delta_Target_Config\"F\n\x16Get_Color_Config_Reply\x12,\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x1c.scanner_config.Color_Config\"d\n%Get_Camera_Serial_Number_Config_Reply\x12;\n\x06\x63onfig\x18\x01 \x01(\x0b\x32+.scanner_config.Camera_Serial_Number_Config\")\n\x15Get_HW_Revision_Reply\x12\x10\n\x08revision\x18\x01 \x01(\r\"[\n\x1dGet_Scanner_Barcode_Str_Reply\x12:\n\x06\x63onfig\x18\x01 \x01(\x0b\x32*.scanner_config.Scanner_Barcode_Str_Config\"\xaa\x05\n\x07Request\x12\x41\n\x06set_dt\x18\x01 \x01(\x0b\x32/.scanner_config.Set_Delta_Target_Config_RequestH\x00\x12\x41\n\x06get_dt\x18\x02 \x01(\x0b\x32/.scanner_config.Get_Delta_Target_Config_RequestH\x00\x12=\n\tset_color\x18\x03 \x01(\x0b\x32(.scanner_config.Set_Color_Config_RequestH\x00\x12=\n\tget_color\x18\x04 \x01(\x0b\x32(.scanner_config.Get_Color_Config_RequestH\x00\x12I\n\x06set_sn\x18\x05 \x01(\x0b\x32\x37.scanner_config.Set_Camera_Serial_Number_Config_RequestH\x00\x12I\n\x06get_sn\x18\x06 \x01(\x0b\x32\x37.scanner_config.Get_Camera_Serial_Number_Config_RequestH\x00\x12\x39\n\x06set_hw\x18\x07 \x01(\x0b\x32\'.scanner_config.Set_HW_Revision_RequestH\x00\x12\x39\n\x06get_hw\x18\x08 \x01(\x0b\x32\'.scanner_config.Get_HW_Revision_RequestH\x00\x12\x41\n\x06set_bc\x18\t \x01(\x0b\x32/.scanner_config.Set_Scanner_Barcode_Str_RequestH\x00\x12\x41\n\x06get_bc\x18\n \x01(\x0b\x32/.scanner_config.Get_Scanner_Barcode_Str_RequestH\x00\x42\t\n\x07request\"\xf5\x02\n\x05Reply\x12\x1d\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x02 \x01(\x0b\x32\x08.ack.AckH\x00\x12;\n\x02\x64t\x18\x03 \x01(\x0b\x32-.scanner_config.Get_Delta_Target_Config_ReplyH\x00\x12\x37\n\x05\x63olor\x18\x04 \x01(\x0b\x32&.scanner_config.Get_Color_Config_ReplyH\x00\x12\x43\n\x02sn\x18\x05 \x01(\x0b\x32\x35.scanner_config.Get_Camera_Serial_Number_Config_ReplyH\x00\x12\x33\n\x02hw\x18\x06 \x01(\x0b\x32%.scanner_config.Get_HW_Revision_ReplyH\x00\x12;\n\x02\x62\x63\x18\x07 \x01(\x0b\x32-.scanner_config.Get_Scanner_Barcode_Str_ReplyH\x00\x42\x07\n\x05replyB\x17Z\x15nanopb/scanner_configb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,])




_DELTA_TARGET_CONFIG = _descriptor.Descriptor(
  name='Delta_Target_Config',
  full_name='scanner_config.Delta_Target_Config',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan_skew', full_name='scanner_config.Delta_Target_Config.pan_skew', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt_skew', full_name='scanner_config.Delta_Target_Config.tilt_skew', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=169,
  serialized_end=227,
)


_COLOR_CONFIG = _descriptor.Descriptor(
  name='Color_Config',
  full_name='scanner_config.Color_Config',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='red', full_name='scanner_config.Color_Config.red', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='green', full_name='scanner_config.Color_Config.green', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='blue', full_name='scanner_config.Color_Config.blue', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=229,
  serialized_end=285,
)


_CAMERA_SERIAL_NUMBER_CONFIG = _descriptor.Descriptor(
  name='Camera_Serial_Number_Config',
  full_name='scanner_config.Camera_Serial_Number_Config',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='serial_number', full_name='scanner_config.Camera_Serial_Number_Config.serial_number', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=287,
  serialized_end=339,
)


_SCANNER_BARCODE_STR_CONFIG = _descriptor.Descriptor(
  name='Scanner_Barcode_Str_Config',
  full_name='scanner_config.Scanner_Barcode_Str_Config',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='barcode', full_name='scanner_config.Scanner_Barcode_Str_Config.barcode', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=341,
  serialized_end=386,
)


_SET_DELTA_TARGET_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Set_Delta_Target_Config_Request',
  full_name='scanner_config.Set_Delta_Target_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='scanner_config.Set_Delta_Target_Config_Request.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=388,
  serialized_end=474,
)


_GET_DELTA_TARGET_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Get_Delta_Target_Config_Request',
  full_name='scanner_config.Get_Delta_Target_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=476,
  serialized_end=509,
)


_SET_COLOR_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Set_Color_Config_Request',
  full_name='scanner_config.Set_Color_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='scanner_config.Set_Color_Config_Request.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=511,
  serialized_end=583,
)


_GET_COLOR_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Get_Color_Config_Request',
  full_name='scanner_config.Get_Color_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=585,
  serialized_end=611,
)


_SET_CAMERA_SERIAL_NUMBER_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Set_Camera_Serial_Number_Config_Request',
  full_name='scanner_config.Set_Camera_Serial_Number_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='scanner_config.Set_Camera_Serial_Number_Config_Request.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=613,
  serialized_end=715,
)


_GET_CAMERA_SERIAL_NUMBER_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Get_Camera_Serial_Number_Config_Request',
  full_name='scanner_config.Get_Camera_Serial_Number_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=717,
  serialized_end=758,
)


_SET_HW_REVISION_REQUEST = _descriptor.Descriptor(
  name='Set_HW_Revision_Request',
  full_name='scanner_config.Set_HW_Revision_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='revision', full_name='scanner_config.Set_HW_Revision_Request.revision', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=760,
  serialized_end=803,
)


_GET_HW_REVISION_REQUEST = _descriptor.Descriptor(
  name='Get_HW_Revision_Request',
  full_name='scanner_config.Get_HW_Revision_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=805,
  serialized_end=830,
)


_SET_SCANNER_BARCODE_STR_REQUEST = _descriptor.Descriptor(
  name='Set_Scanner_Barcode_Str_Request',
  full_name='scanner_config.Set_Scanner_Barcode_Str_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='scanner_config.Set_Scanner_Barcode_Str_Request.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=832,
  serialized_end=925,
)


_GET_SCANNER_BARCODE_STR_REQUEST = _descriptor.Descriptor(
  name='Get_Scanner_Barcode_Str_Request',
  full_name='scanner_config.Get_Scanner_Barcode_Str_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=927,
  serialized_end=960,
)


_GET_DELTA_TARGET_CONFIG_REPLY = _descriptor.Descriptor(
  name='Get_Delta_Target_Config_Reply',
  full_name='scanner_config.Get_Delta_Target_Config_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='scanner_config.Get_Delta_Target_Config_Reply.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=962,
  serialized_end=1046,
)


_GET_COLOR_CONFIG_REPLY = _descriptor.Descriptor(
  name='Get_Color_Config_Reply',
  full_name='scanner_config.Get_Color_Config_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='scanner_config.Get_Color_Config_Reply.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1048,
  serialized_end=1118,
)


_GET_CAMERA_SERIAL_NUMBER_CONFIG_REPLY = _descriptor.Descriptor(
  name='Get_Camera_Serial_Number_Config_Reply',
  full_name='scanner_config.Get_Camera_Serial_Number_Config_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='scanner_config.Get_Camera_Serial_Number_Config_Reply.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1120,
  serialized_end=1220,
)


_GET_HW_REVISION_REPLY = _descriptor.Descriptor(
  name='Get_HW_Revision_Reply',
  full_name='scanner_config.Get_HW_Revision_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='revision', full_name='scanner_config.Get_HW_Revision_Reply.revision', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1222,
  serialized_end=1263,
)


_GET_SCANNER_BARCODE_STR_REPLY = _descriptor.Descriptor(
  name='Get_Scanner_Barcode_Str_Reply',
  full_name='scanner_config.Get_Scanner_Barcode_Str_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='scanner_config.Get_Scanner_Barcode_Str_Reply.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1265,
  serialized_end=1356,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='scanner_config.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='set_dt', full_name='scanner_config.Request.set_dt', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_dt', full_name='scanner_config.Request.get_dt', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_color', full_name='scanner_config.Request.set_color', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_color', full_name='scanner_config.Request.get_color', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_sn', full_name='scanner_config.Request.set_sn', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_sn', full_name='scanner_config.Request.get_sn', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_hw', full_name='scanner_config.Request.set_hw', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_hw', full_name='scanner_config.Request.get_hw', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_bc', full_name='scanner_config.Request.set_bc', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_bc', full_name='scanner_config.Request.get_bc', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='scanner_config.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1359,
  serialized_end=2041,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='scanner_config.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='scanner_config.Reply.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='scanner_config.Reply.ack', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dt', full_name='scanner_config.Reply.dt', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='color', full_name='scanner_config.Reply.color', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sn', full_name='scanner_config.Reply.sn', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hw', full_name='scanner_config.Reply.hw', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bc', full_name='scanner_config.Reply.bc', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='scanner_config.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2044,
  serialized_end=2417,
)

_SET_DELTA_TARGET_CONFIG_REQUEST.fields_by_name['config'].message_type = _DELTA_TARGET_CONFIG
_SET_COLOR_CONFIG_REQUEST.fields_by_name['config'].message_type = _COLOR_CONFIG
_SET_CAMERA_SERIAL_NUMBER_CONFIG_REQUEST.fields_by_name['config'].message_type = _CAMERA_SERIAL_NUMBER_CONFIG
_SET_SCANNER_BARCODE_STR_REQUEST.fields_by_name['config'].message_type = _SCANNER_BARCODE_STR_CONFIG
_GET_DELTA_TARGET_CONFIG_REPLY.fields_by_name['config'].message_type = _DELTA_TARGET_CONFIG
_GET_COLOR_CONFIG_REPLY.fields_by_name['config'].message_type = _COLOR_CONFIG
_GET_CAMERA_SERIAL_NUMBER_CONFIG_REPLY.fields_by_name['config'].message_type = _CAMERA_SERIAL_NUMBER_CONFIG
_GET_SCANNER_BARCODE_STR_REPLY.fields_by_name['config'].message_type = _SCANNER_BARCODE_STR_CONFIG
_REQUEST.fields_by_name['set_dt'].message_type = _SET_DELTA_TARGET_CONFIG_REQUEST
_REQUEST.fields_by_name['get_dt'].message_type = _GET_DELTA_TARGET_CONFIG_REQUEST
_REQUEST.fields_by_name['set_color'].message_type = _SET_COLOR_CONFIG_REQUEST
_REQUEST.fields_by_name['get_color'].message_type = _GET_COLOR_CONFIG_REQUEST
_REQUEST.fields_by_name['set_sn'].message_type = _SET_CAMERA_SERIAL_NUMBER_CONFIG_REQUEST
_REQUEST.fields_by_name['get_sn'].message_type = _GET_CAMERA_SERIAL_NUMBER_CONFIG_REQUEST
_REQUEST.fields_by_name['set_hw'].message_type = _SET_HW_REVISION_REQUEST
_REQUEST.fields_by_name['get_hw'].message_type = _GET_HW_REVISION_REQUEST
_REQUEST.fields_by_name['set_bc'].message_type = _SET_SCANNER_BARCODE_STR_REQUEST
_REQUEST.fields_by_name['get_bc'].message_type = _GET_SCANNER_BARCODE_STR_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set_dt'])
_REQUEST.fields_by_name['set_dt'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get_dt'])
_REQUEST.fields_by_name['get_dt'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set_color'])
_REQUEST.fields_by_name['set_color'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get_color'])
_REQUEST.fields_by_name['get_color'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set_sn'])
_REQUEST.fields_by_name['set_sn'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get_sn'])
_REQUEST.fields_by_name['get_sn'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set_hw'])
_REQUEST.fields_by_name['set_hw'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get_hw'])
_REQUEST.fields_by_name['get_hw'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set_bc'])
_REQUEST.fields_by_name['set_bc'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get_bc'])
_REQUEST.fields_by_name['get_bc'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['dt'].message_type = _GET_DELTA_TARGET_CONFIG_REPLY
_REPLY.fields_by_name['color'].message_type = _GET_COLOR_CONFIG_REPLY
_REPLY.fields_by_name['sn'].message_type = _GET_CAMERA_SERIAL_NUMBER_CONFIG_REPLY
_REPLY.fields_by_name['hw'].message_type = _GET_HW_REVISION_REPLY
_REPLY.fields_by_name['bc'].message_type = _GET_SCANNER_BARCODE_STR_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['dt'])
_REPLY.fields_by_name['dt'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['color'])
_REPLY.fields_by_name['color'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['sn'])
_REPLY.fields_by_name['sn'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['hw'])
_REPLY.fields_by_name['hw'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['bc'])
_REPLY.fields_by_name['bc'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Delta_Target_Config'] = _DELTA_TARGET_CONFIG
DESCRIPTOR.message_types_by_name['Color_Config'] = _COLOR_CONFIG
DESCRIPTOR.message_types_by_name['Camera_Serial_Number_Config'] = _CAMERA_SERIAL_NUMBER_CONFIG
DESCRIPTOR.message_types_by_name['Scanner_Barcode_Str_Config'] = _SCANNER_BARCODE_STR_CONFIG
DESCRIPTOR.message_types_by_name['Set_Delta_Target_Config_Request'] = _SET_DELTA_TARGET_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Get_Delta_Target_Config_Request'] = _GET_DELTA_TARGET_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Set_Color_Config_Request'] = _SET_COLOR_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Get_Color_Config_Request'] = _GET_COLOR_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Set_Camera_Serial_Number_Config_Request'] = _SET_CAMERA_SERIAL_NUMBER_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Get_Camera_Serial_Number_Config_Request'] = _GET_CAMERA_SERIAL_NUMBER_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Set_HW_Revision_Request'] = _SET_HW_REVISION_REQUEST
DESCRIPTOR.message_types_by_name['Get_HW_Revision_Request'] = _GET_HW_REVISION_REQUEST
DESCRIPTOR.message_types_by_name['Set_Scanner_Barcode_Str_Request'] = _SET_SCANNER_BARCODE_STR_REQUEST
DESCRIPTOR.message_types_by_name['Get_Scanner_Barcode_Str_Request'] = _GET_SCANNER_BARCODE_STR_REQUEST
DESCRIPTOR.message_types_by_name['Get_Delta_Target_Config_Reply'] = _GET_DELTA_TARGET_CONFIG_REPLY
DESCRIPTOR.message_types_by_name['Get_Color_Config_Reply'] = _GET_COLOR_CONFIG_REPLY
DESCRIPTOR.message_types_by_name['Get_Camera_Serial_Number_Config_Reply'] = _GET_CAMERA_SERIAL_NUMBER_CONFIG_REPLY
DESCRIPTOR.message_types_by_name['Get_HW_Revision_Reply'] = _GET_HW_REVISION_REPLY
DESCRIPTOR.message_types_by_name['Get_Scanner_Barcode_Str_Reply'] = _GET_SCANNER_BARCODE_STR_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Delta_Target_Config = _reflection.GeneratedProtocolMessageType('Delta_Target_Config', (_message.Message,), {
  'DESCRIPTOR' : _DELTA_TARGET_CONFIG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Delta_Target_Config)
  })
_sym_db.RegisterMessage(Delta_Target_Config)

Color_Config = _reflection.GeneratedProtocolMessageType('Color_Config', (_message.Message,), {
  'DESCRIPTOR' : _COLOR_CONFIG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Color_Config)
  })
_sym_db.RegisterMessage(Color_Config)

Camera_Serial_Number_Config = _reflection.GeneratedProtocolMessageType('Camera_Serial_Number_Config', (_message.Message,), {
  'DESCRIPTOR' : _CAMERA_SERIAL_NUMBER_CONFIG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Camera_Serial_Number_Config)
  })
_sym_db.RegisterMessage(Camera_Serial_Number_Config)

Scanner_Barcode_Str_Config = _reflection.GeneratedProtocolMessageType('Scanner_Barcode_Str_Config', (_message.Message,), {
  'DESCRIPTOR' : _SCANNER_BARCODE_STR_CONFIG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Scanner_Barcode_Str_Config)
  })
_sym_db.RegisterMessage(Scanner_Barcode_Str_Config)

Set_Delta_Target_Config_Request = _reflection.GeneratedProtocolMessageType('Set_Delta_Target_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_DELTA_TARGET_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Set_Delta_Target_Config_Request)
  })
_sym_db.RegisterMessage(Set_Delta_Target_Config_Request)

Get_Delta_Target_Config_Request = _reflection.GeneratedProtocolMessageType('Get_Delta_Target_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_DELTA_TARGET_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_Delta_Target_Config_Request)
  })
_sym_db.RegisterMessage(Get_Delta_Target_Config_Request)

Set_Color_Config_Request = _reflection.GeneratedProtocolMessageType('Set_Color_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_COLOR_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Set_Color_Config_Request)
  })
_sym_db.RegisterMessage(Set_Color_Config_Request)

Get_Color_Config_Request = _reflection.GeneratedProtocolMessageType('Get_Color_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_COLOR_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_Color_Config_Request)
  })
_sym_db.RegisterMessage(Get_Color_Config_Request)

Set_Camera_Serial_Number_Config_Request = _reflection.GeneratedProtocolMessageType('Set_Camera_Serial_Number_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_CAMERA_SERIAL_NUMBER_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Set_Camera_Serial_Number_Config_Request)
  })
_sym_db.RegisterMessage(Set_Camera_Serial_Number_Config_Request)

Get_Camera_Serial_Number_Config_Request = _reflection.GeneratedProtocolMessageType('Get_Camera_Serial_Number_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_CAMERA_SERIAL_NUMBER_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_Camera_Serial_Number_Config_Request)
  })
_sym_db.RegisterMessage(Get_Camera_Serial_Number_Config_Request)

Set_HW_Revision_Request = _reflection.GeneratedProtocolMessageType('Set_HW_Revision_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_HW_REVISION_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Set_HW_Revision_Request)
  })
_sym_db.RegisterMessage(Set_HW_Revision_Request)

Get_HW_Revision_Request = _reflection.GeneratedProtocolMessageType('Get_HW_Revision_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_HW_REVISION_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_HW_Revision_Request)
  })
_sym_db.RegisterMessage(Get_HW_Revision_Request)

Set_Scanner_Barcode_Str_Request = _reflection.GeneratedProtocolMessageType('Set_Scanner_Barcode_Str_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_SCANNER_BARCODE_STR_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Set_Scanner_Barcode_Str_Request)
  })
_sym_db.RegisterMessage(Set_Scanner_Barcode_Str_Request)

Get_Scanner_Barcode_Str_Request = _reflection.GeneratedProtocolMessageType('Get_Scanner_Barcode_Str_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_SCANNER_BARCODE_STR_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_Scanner_Barcode_Str_Request)
  })
_sym_db.RegisterMessage(Get_Scanner_Barcode_Str_Request)

Get_Delta_Target_Config_Reply = _reflection.GeneratedProtocolMessageType('Get_Delta_Target_Config_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GET_DELTA_TARGET_CONFIG_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_Delta_Target_Config_Reply)
  })
_sym_db.RegisterMessage(Get_Delta_Target_Config_Reply)

Get_Color_Config_Reply = _reflection.GeneratedProtocolMessageType('Get_Color_Config_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GET_COLOR_CONFIG_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_Color_Config_Reply)
  })
_sym_db.RegisterMessage(Get_Color_Config_Reply)

Get_Camera_Serial_Number_Config_Reply = _reflection.GeneratedProtocolMessageType('Get_Camera_Serial_Number_Config_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GET_CAMERA_SERIAL_NUMBER_CONFIG_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_Camera_Serial_Number_Config_Reply)
  })
_sym_db.RegisterMessage(Get_Camera_Serial_Number_Config_Reply)

Get_HW_Revision_Reply = _reflection.GeneratedProtocolMessageType('Get_HW_Revision_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GET_HW_REVISION_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_HW_Revision_Reply)
  })
_sym_db.RegisterMessage(Get_HW_Revision_Reply)

Get_Scanner_Barcode_Str_Reply = _reflection.GeneratedProtocolMessageType('Get_Scanner_Barcode_Str_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GET_SCANNER_BARCODE_STR_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Get_Scanner_Barcode_Str_Reply)
  })
_sym_db.RegisterMessage(Get_Scanner_Barcode_Str_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_config_pb2'
  # @@protoc_insertion_point(class_scope:scanner_config.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

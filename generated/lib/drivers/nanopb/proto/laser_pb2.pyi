"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

LaserTypeValue = typing___NewType('LaserTypeValue', builtin___int)
type___LaserTypeValue = LaserTypeValue
LaserType: _LaserType
class _LaserType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[LaserTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    LASERTYPE_UNKNOWN = typing___cast(LaserTypeValue, 0)
    LASERTYPE_CO2 = typing___cast(LaserTypeValue, 1)
    LASERTYPE_DIODE_BWT = typing___cast(LaserTypeValue, 2)
    LASERTYPE_DIODE_JLIGHT = typing___cast(LaserTypeValue, 3)
LASERTYPE_UNKNOWN = typing___cast(LaserTypeValue, 0)
LASERTYPE_CO2 = typing___cast(LaserTypeValue, 1)
LASERTYPE_DIODE_BWT = typing___cast(LaserTypeValue, 2)
LASERTYPE_DIODE_JLIGHT = typing___cast(LaserTypeValue, 3)

Jlight_FaultValue = typing___NewType('Jlight_FaultValue', builtin___int)
type___Jlight_FaultValue = Jlight_FaultValue
Jlight_Fault: _Jlight_Fault
class _Jlight_Fault(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[Jlight_FaultValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    JLIGHT_FAULT_UNKNOWN = typing___cast(Jlight_FaultValue, 0)
    JLIGHT_FAULT_INTERLOCK = typing___cast(Jlight_FaultValue, 1)
    JLIGHT_FAULT_SYSTEM = typing___cast(Jlight_FaultValue, 2)
    JLIGHT_FAULT_CH1_OVERCURRENT = typing___cast(Jlight_FaultValue, 3)
    JLIGHT_FAULT_CH2_OVERCURRENT = typing___cast(Jlight_FaultValue, 4)
    JLIGHT_FAULT_CH1_OVERVOLTAGE = typing___cast(Jlight_FaultValue, 5)
    JLIGHT_FAULT_CH2_OVERVOLTAGE = typing___cast(Jlight_FaultValue, 6)
    JLIGHT_FAULT_CH1_OVERTEMP = typing___cast(Jlight_FaultValue, 7)
    JLIGHT_FAULT_CH2_OVERTEMP = typing___cast(Jlight_FaultValue, 8)
    JLIGHT_FAULT_DRIVER_OVERTEMP = typing___cast(Jlight_FaultValue, 9)
JLIGHT_FAULT_UNKNOWN = typing___cast(Jlight_FaultValue, 0)
JLIGHT_FAULT_INTERLOCK = typing___cast(Jlight_FaultValue, 1)
JLIGHT_FAULT_SYSTEM = typing___cast(Jlight_FaultValue, 2)
JLIGHT_FAULT_CH1_OVERCURRENT = typing___cast(Jlight_FaultValue, 3)
JLIGHT_FAULT_CH2_OVERCURRENT = typing___cast(Jlight_FaultValue, 4)
JLIGHT_FAULT_CH1_OVERVOLTAGE = typing___cast(Jlight_FaultValue, 5)
JLIGHT_FAULT_CH2_OVERVOLTAGE = typing___cast(Jlight_FaultValue, 6)
JLIGHT_FAULT_CH1_OVERTEMP = typing___cast(Jlight_FaultValue, 7)
JLIGHT_FAULT_CH2_OVERTEMP = typing___cast(Jlight_FaultValue, 8)
JLIGHT_FAULT_DRIVER_OVERTEMP = typing___cast(Jlight_FaultValue, 9)

class Raw_Data_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Raw_Data_Request = Raw_Data_Request

class Raw_Data_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    therm1_raw: builtin___int = ...
    therm2_raw: builtin___int = ...
    photodiode_raw: builtin___int = ...

    def __init__(self,
        *,
        therm1_raw : typing___Optional[builtin___int] = None,
        therm2_raw : typing___Optional[builtin___int] = None,
        photodiode_raw : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_photodiode_raw",b"_photodiode_raw",u"_therm1_raw",b"_therm1_raw",u"_therm2_raw",b"_therm2_raw",u"photodiode_raw",b"photodiode_raw",u"therm1_raw",b"therm1_raw",u"therm2_raw",b"therm2_raw"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_photodiode_raw",b"_photodiode_raw",u"_therm1_raw",b"_therm1_raw",u"_therm2_raw",b"_therm2_raw",u"photodiode_raw",b"photodiode_raw",u"therm1_raw",b"therm1_raw",u"therm2_raw",b"therm2_raw"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_photodiode_raw",b"_photodiode_raw"]) -> typing_extensions___Literal["photodiode_raw"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_therm1_raw",b"_therm1_raw"]) -> typing_extensions___Literal["therm1_raw"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_therm2_raw",b"_therm2_raw"]) -> typing_extensions___Literal["therm2_raw"]: ...
type___Raw_Data_Reply = Raw_Data_Reply

class Laser_Inventory_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Laser_Inventory_Request = Laser_Inventory_Request

class Laser_Inventory_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model: typing___Text = ...
    serial: typing___Text = ...
    power: builtin___int = ...
    type: type___LaserTypeValue = ...

    def __init__(self,
        *,
        model : typing___Optional[typing___Text] = None,
        serial : typing___Optional[typing___Text] = None,
        power : typing___Optional[builtin___int] = None,
        type : typing___Optional[type___LaserTypeValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"model",b"model",u"power",b"power",u"serial",b"serial",u"type",b"type"]) -> None: ...
type___Laser_Inventory_Reply = Laser_Inventory_Reply

class Laser_Set_Type_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type: type___LaserTypeValue = ...

    def __init__(self,
        *,
        type : typing___Optional[type___LaserTypeValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"type",b"type"]) -> None: ...
type___Laser_Set_Type_Request = Laser_Set_Type_Request

class Laser_Reset_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    transport: builtin___bool = ...
    faults: builtin___bool = ...

    def __init__(self,
        *,
        transport : typing___Optional[builtin___bool] = None,
        faults : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"faults",b"faults",u"transport",b"transport"]) -> None: ...
type___Laser_Reset_Request = Laser_Reset_Request

class Jlight_Status(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    psuTemp: builtin___float = ...
    psuInputVolts: builtin___float = ...
    psuOutputVolts: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    faults: google___protobuf___internal___containers___RepeatedScalarFieldContainer[type___Jlight_FaultValue] = ...

    def __init__(self,
        *,
        psuTemp : typing___Optional[builtin___float] = None,
        psuInputVolts : typing___Optional[builtin___float] = None,
        psuOutputVolts : typing___Optional[typing___Iterable[builtin___float]] = None,
        faults : typing___Optional[typing___Iterable[type___Jlight_FaultValue]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"faults",b"faults",u"psuInputVolts",b"psuInputVolts",u"psuOutputVolts",b"psuOutputVolts",u"psuTemp",b"psuTemp"]) -> None: ...
type___Jlight_Status = Jlight_Status

class Bwt_Status(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    faults: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...

    def __init__(self,
        *,
        faults : typing___Optional[typing___Iterable[builtin___int]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"faults",b"faults"]) -> None: ...
type___Bwt_Status = Bwt_Status

class Bwt_Passthrough_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    command: typing___Text = ...

    def __init__(self,
        *,
        command : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"command",b"command"]) -> None: ...
type___Bwt_Passthrough_Request = Bwt_Passthrough_Request

class Bwt_Passthrough_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    response: typing___Text = ...

    def __init__(self,
        *,
        response : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"response",b"response"]) -> None: ...
type___Bwt_Passthrough_Reply = Bwt_Passthrough_Reply

class Bwt_Transport_Get_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Bwt_Transport_Get_Config_Request = Bwt_Transport_Get_Config_Request

class Bwt_Transport_Config(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    log_messages: builtin___bool = ...
    intercommand_delay: builtin___bool = ...

    def __init__(self,
        *,
        log_messages : typing___Optional[builtin___bool] = None,
        intercommand_delay : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"intercommand_delay",b"intercommand_delay",u"log_messages",b"log_messages"]) -> None: ...
type___Bwt_Transport_Config = Bwt_Transport_Config

class Diode_Status_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Diode_Status_Request = Diode_Status_Request

class Diode_Status_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp: builtin___int = ...
    temp: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    humidity: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    current: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    faulted: builtin___bool = ...
    thermistors: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...

    @property
    def extra_bwt(self) -> type___Bwt_Status: ...

    @property
    def extra_jlight(self) -> type___Jlight_Status: ...

    def __init__(self,
        *,
        timestamp : typing___Optional[builtin___int] = None,
        temp : typing___Optional[typing___Iterable[builtin___float]] = None,
        humidity : typing___Optional[typing___Iterable[builtin___float]] = None,
        current : typing___Optional[typing___Iterable[builtin___float]] = None,
        faulted : typing___Optional[builtin___bool] = None,
        thermistors : typing___Optional[typing___Iterable[builtin___float]] = None,
        extra_bwt : typing___Optional[type___Bwt_Status] = None,
        extra_jlight : typing___Optional[type___Jlight_Status] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"extra",b"extra",u"extra_bwt",b"extra_bwt",u"extra_jlight",b"extra_jlight"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current",b"current",u"extra",b"extra",u"extra_bwt",b"extra_bwt",u"extra_jlight",b"extra_jlight",u"faulted",b"faulted",u"humidity",b"humidity",u"temp",b"temp",u"thermistors",b"thermistors",u"timestamp",b"timestamp"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"extra",b"extra"]) -> typing_extensions___Literal["extra_bwt","extra_jlight"]: ...
type___Diode_Status_Reply = Diode_Status_Reply

class Diode_Set_Current_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    commit: builtin___bool = ...

    def __init__(self,
        *,
        current : typing___Optional[typing___Iterable[builtin___int]] = None,
        commit : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"commit",b"commit",u"current",b"current"]) -> None: ...
type___Diode_Set_Current_Request = Diode_Set_Current_Request

class Laser_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    on: builtin___bool = ...

    def __init__(self,
        *,
        on : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"on",b"on"]) -> None: ...
type___Laser_Request = Laser_Request

class Get_Laser_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get_Laser_Request = Get_Laser_Request

class Intensity_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    intensity: builtin___int = ...

    def __init__(self,
        *,
        intensity : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"intensity",b"intensity"]) -> None: ...
type___Intensity_Request = Intensity_Request

class Laser_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    raw_therm1_reading_mv: builtin___int = ...
    raw_therm2_reading_mv: builtin___int = ...
    on: builtin___bool = ...
    lpsu_state: builtin___bool = ...
    fireable: builtin___bool = ...

    def __init__(self,
        *,
        raw_therm1_reading_mv : typing___Optional[builtin___int] = None,
        raw_therm2_reading_mv : typing___Optional[builtin___int] = None,
        on : typing___Optional[builtin___bool] = None,
        lpsu_state : typing___Optional[builtin___bool] = None,
        fireable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"fireable",b"fireable",u"lpsu_state",b"lpsu_state",u"on",b"on",u"raw_therm1_reading_mv",b"raw_therm1_reading_mv",u"raw_therm2_reading_mv",b"raw_therm2_reading_mv"]) -> None: ...
type___Laser_Reply = Laser_Reply

class Laser_State_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    on: builtin___bool = ...

    def __init__(self,
        *,
        on : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"on",b"on"]) -> None: ...
type___Laser_State_Reply = Laser_State_Reply

class Laser_Status_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lpsu_state: builtin___bool = ...
    lpsu_current: builtin___float = ...
    power: builtin___float = ...
    arc_detected: builtin___bool = ...

    def __init__(self,
        *,
        lpsu_state : typing___Optional[builtin___bool] = None,
        lpsu_current : typing___Optional[builtin___float] = None,
        power : typing___Optional[builtin___float] = None,
        arc_detected : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"arc_detected",b"arc_detected",u"lpsu_current",b"lpsu_current",u"lpsu_state",b"lpsu_state",u"power",b"power"]) -> None: ...
type___Laser_Status_Reply = Laser_Status_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def laser(self) -> type___Laser_Request: ...

    @property
    def get_laser(self) -> type___Get_Laser_Request: ...

    @property
    def intensity(self) -> type___Intensity_Request: ...

    @property
    def raw_data(self) -> type___Raw_Data_Request: ...

    @property
    def diode_status(self) -> type___Diode_Status_Request: ...

    @property
    def laser_inventory(self) -> type___Laser_Inventory_Request: ...

    @property
    def bwt_passthrough(self) -> type___Bwt_Passthrough_Request: ...

    @property
    def bwt_set_config(self) -> type___Bwt_Transport_Config: ...

    @property
    def bwt_get_config(self) -> type___Bwt_Transport_Get_Config_Request: ...

    @property
    def laser_reset(self) -> type___Laser_Reset_Request: ...

    @property
    def diode_set_current(self) -> type___Diode_Set_Current_Request: ...

    @property
    def set_type(self) -> type___Laser_Set_Type_Request: ...

    def __init__(self,
        *,
        laser : typing___Optional[type___Laser_Request] = None,
        get_laser : typing___Optional[type___Get_Laser_Request] = None,
        intensity : typing___Optional[type___Intensity_Request] = None,
        raw_data : typing___Optional[type___Raw_Data_Request] = None,
        diode_status : typing___Optional[type___Diode_Status_Request] = None,
        laser_inventory : typing___Optional[type___Laser_Inventory_Request] = None,
        bwt_passthrough : typing___Optional[type___Bwt_Passthrough_Request] = None,
        bwt_set_config : typing___Optional[type___Bwt_Transport_Config] = None,
        bwt_get_config : typing___Optional[type___Bwt_Transport_Get_Config_Request] = None,
        laser_reset : typing___Optional[type___Laser_Reset_Request] = None,
        diode_set_current : typing___Optional[type___Diode_Set_Current_Request] = None,
        set_type : typing___Optional[type___Laser_Set_Type_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bwt_get_config",b"bwt_get_config",u"bwt_passthrough",b"bwt_passthrough",u"bwt_set_config",b"bwt_set_config",u"diode_set_current",b"diode_set_current",u"diode_status",b"diode_status",u"get_laser",b"get_laser",u"intensity",b"intensity",u"laser",b"laser",u"laser_inventory",b"laser_inventory",u"laser_reset",b"laser_reset",u"raw_data",b"raw_data",u"request",b"request",u"set_type",b"set_type"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bwt_get_config",b"bwt_get_config",u"bwt_passthrough",b"bwt_passthrough",u"bwt_set_config",b"bwt_set_config",u"diode_set_current",b"diode_set_current",u"diode_status",b"diode_status",u"get_laser",b"get_laser",u"intensity",b"intensity",u"laser",b"laser",u"laser_inventory",b"laser_inventory",u"laser_reset",b"laser_reset",u"raw_data",b"raw_data",u"request",b"request",u"set_type",b"set_type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["laser","get_laser","intensity","raw_data","diode_status","laser_inventory","bwt_passthrough","bwt_set_config","bwt_get_config","laser_reset","diode_set_current","set_type"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def laser(self) -> type___Laser_State_Reply: ...

    @property
    def laser_reply(self) -> type___Laser_Reply: ...

    @property
    def raw_data(self) -> type___Raw_Data_Reply: ...

    @property
    def diode_status(self) -> type___Diode_Status_Reply: ...

    @property
    def laser_inventory(self) -> type___Laser_Inventory_Reply: ...

    @property
    def bwt_passthrough(self) -> type___Bwt_Passthrough_Reply: ...

    @property
    def bwt_config(self) -> type___Bwt_Transport_Config: ...

    def __init__(self,
        *,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        laser : typing___Optional[type___Laser_State_Reply] = None,
        laser_reply : typing___Optional[type___Laser_Reply] = None,
        raw_data : typing___Optional[type___Raw_Data_Reply] = None,
        diode_status : typing___Optional[type___Diode_Status_Reply] = None,
        laser_inventory : typing___Optional[type___Laser_Inventory_Reply] = None,
        bwt_passthrough : typing___Optional[type___Bwt_Passthrough_Reply] = None,
        bwt_config : typing___Optional[type___Bwt_Transport_Config] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"bwt_config",b"bwt_config",u"bwt_passthrough",b"bwt_passthrough",u"diode_status",b"diode_status",u"error",b"error",u"laser",b"laser",u"laser_inventory",b"laser_inventory",u"laser_reply",b"laser_reply",u"raw_data",b"raw_data",u"reply",b"reply"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"bwt_config",b"bwt_config",u"bwt_passthrough",b"bwt_passthrough",u"diode_status",b"diode_status",u"error",b"error",u"laser",b"laser",u"laser_inventory",b"laser_inventory",u"laser_reply",b"laser_reply",u"raw_data",b"raw_data",u"reply",b"reply"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","laser","laser_reply","raw_data","diode_status","laser_inventory","bwt_passthrough","bwt_config"]: ...
type___Reply = Reply

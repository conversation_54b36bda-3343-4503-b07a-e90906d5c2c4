# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/dawg.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/dawg.proto',
  package='dawg',
  syntax='proto3',
  serialized_options=b'Z\013nanopb/dawg',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n-generated/lib/drivers/nanopb/proto/dawg.proto\x12\x04\x64\x61wg\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\"$\n\x0e\x43onfig_Request\x12\x12\n\ntimeout_ms\x18\x01 \x01(\r\"\x1c\n\x0b\x41rm_Request\x12\r\n\x05\x61rmed\x18\x01 \x01(\x08\"\x1d\n\x0bPet_Request\x12\x0e\n\x06\x66iring\x18\x01 \x01(\x08\"\x13\n\x11Get_State_Request\",\n\x0bState_Reply\x12\r\n\x05\x61rmed\x18\x01 \x01(\x08\x12\x0e\n\x06petted\x18\x02 \x01(\x08\"\xaa\x01\n\x07Request\x12&\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x14.dawg.Config_RequestH\x00\x12 \n\x03\x61rm\x18\x02 \x01(\x0b\x32\x11.dawg.Arm_RequestH\x00\x12 \n\x03pet\x18\x03 \x01(\x0b\x32\x11.dawg.Pet_RequestH\x00\x12(\n\x05state\x18\x04 \x01(\x0b\x32\x17.dawg.Get_State_RequestH\x00\x42\t\n\x07request\"l\n\x05Reply\x12\x1d\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x02 \x01(\x0b\x32\x08.ack.AckH\x00\x12\"\n\x05state\x18\x03 \x01(\x0b\x32\x11.dawg.State_ReplyH\x00\x42\x07\n\x05replyB\rZ\x0bnanopb/dawgb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,])




_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Config_Request',
  full_name='dawg.Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timeout_ms', full_name='dawg.Config_Request.timeout_ms', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=149,
  serialized_end=185,
)


_ARM_REQUEST = _descriptor.Descriptor(
  name='Arm_Request',
  full_name='dawg.Arm_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='armed', full_name='dawg.Arm_Request.armed', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=187,
  serialized_end=215,
)


_PET_REQUEST = _descriptor.Descriptor(
  name='Pet_Request',
  full_name='dawg.Pet_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='firing', full_name='dawg.Pet_Request.firing', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=217,
  serialized_end=246,
)


_GET_STATE_REQUEST = _descriptor.Descriptor(
  name='Get_State_Request',
  full_name='dawg.Get_State_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=248,
  serialized_end=267,
)


_STATE_REPLY = _descriptor.Descriptor(
  name='State_Reply',
  full_name='dawg.State_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='armed', full_name='dawg.State_Reply.armed', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='petted', full_name='dawg.State_Reply.petted', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=269,
  serialized_end=313,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='dawg.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='dawg.Request.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='arm', full_name='dawg.Request.arm', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pet', full_name='dawg.Request.pet', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='dawg.Request.state', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='dawg.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=316,
  serialized_end=486,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='dawg.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='dawg.Reply.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='dawg.Reply.ack', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='dawg.Reply.state', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='dawg.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=488,
  serialized_end=596,
)

_REQUEST.fields_by_name['config'].message_type = _CONFIG_REQUEST
_REQUEST.fields_by_name['arm'].message_type = _ARM_REQUEST
_REQUEST.fields_by_name['pet'].message_type = _PET_REQUEST
_REQUEST.fields_by_name['state'].message_type = _GET_STATE_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['config'])
_REQUEST.fields_by_name['config'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['arm'])
_REQUEST.fields_by_name['arm'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['pet'])
_REQUEST.fields_by_name['pet'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['state'])
_REQUEST.fields_by_name['state'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['state'].message_type = _STATE_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['state'])
_REPLY.fields_by_name['state'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Config_Request'] = _CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Arm_Request'] = _ARM_REQUEST
DESCRIPTOR.message_types_by_name['Pet_Request'] = _PET_REQUEST
DESCRIPTOR.message_types_by_name['Get_State_Request'] = _GET_STATE_REQUEST
DESCRIPTOR.message_types_by_name['State_Reply'] = _STATE_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Config_Request = _reflection.GeneratedProtocolMessageType('Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.dawg_pb2'
  # @@protoc_insertion_point(class_scope:dawg.Config_Request)
  })
_sym_db.RegisterMessage(Config_Request)

Arm_Request = _reflection.GeneratedProtocolMessageType('Arm_Request', (_message.Message,), {
  'DESCRIPTOR' : _ARM_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.dawg_pb2'
  # @@protoc_insertion_point(class_scope:dawg.Arm_Request)
  })
_sym_db.RegisterMessage(Arm_Request)

Pet_Request = _reflection.GeneratedProtocolMessageType('Pet_Request', (_message.Message,), {
  'DESCRIPTOR' : _PET_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.dawg_pb2'
  # @@protoc_insertion_point(class_scope:dawg.Pet_Request)
  })
_sym_db.RegisterMessage(Pet_Request)

Get_State_Request = _reflection.GeneratedProtocolMessageType('Get_State_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_STATE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.dawg_pb2'
  # @@protoc_insertion_point(class_scope:dawg.Get_State_Request)
  })
_sym_db.RegisterMessage(Get_State_Request)

State_Reply = _reflection.GeneratedProtocolMessageType('State_Reply', (_message.Message,), {
  'DESCRIPTOR' : _STATE_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.dawg_pb2'
  # @@protoc_insertion_point(class_scope:dawg.State_Reply)
  })
_sym_db.RegisterMessage(State_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.dawg_pb2'
  # @@protoc_insertion_point(class_scope:dawg.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.dawg_pb2'
  # @@protoc_insertion_point(class_scope:dawg.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

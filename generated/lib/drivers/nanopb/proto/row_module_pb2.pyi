"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.dawg_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___dawg_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___dawg_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.scanner_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___scanner_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___scanner_pb2___Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Reset_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Reset_Request = Reset_Request

class Clear_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Clear_Config_Request = Clear_Config_Request

class Scanner_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_id: builtin___int = ...

    @property
    def request(self) -> generated___lib___drivers___nanopb___proto___scanner_pb2___Request: ...

    def __init__(self,
        *,
        scanner_id : typing___Optional[builtin___int] = None,
        request : typing___Optional[generated___lib___drivers___nanopb___proto___scanner_pb2___Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"request",b"request"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"request",b"request",u"scanner_id",b"scanner_id"]) -> None: ...
type___Scanner_Request = Scanner_Request

class ACK_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ACK_Reply = ACK_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def reset(self) -> type___Reset_Request: ...

    @property
    def clear(self) -> type___Clear_Config_Request: ...

    @property
    def scanner(self) -> type___Scanner_Request: ...

    @property
    def dawg(self) -> generated___lib___drivers___nanopb___proto___dawg_pb2___Request: ...

    def __init__(self,
        *,
        reset : typing___Optional[type___Reset_Request] = None,
        clear : typing___Optional[type___Clear_Config_Request] = None,
        scanner : typing___Optional[type___Scanner_Request] = None,
        dawg : typing___Optional[generated___lib___drivers___nanopb___proto___dawg_pb2___Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"clear",b"clear",u"dawg",b"dawg",u"request",b"request",u"reset",b"reset",u"scanner",b"scanner"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"clear",b"clear",u"dawg",b"dawg",u"request",b"request",u"reset",b"reset",u"scanner",b"scanner"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["reset","clear","scanner","dawg"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ack(self) -> type___ACK_Reply: ...

    @property
    def scanner(self) -> generated___lib___drivers___nanopb___proto___scanner_pb2___Reply: ...

    @property
    def dawg(self) -> generated___lib___drivers___nanopb___proto___dawg_pb2___Reply: ...

    def __init__(self,
        *,
        ack : typing___Optional[type___ACK_Reply] = None,
        scanner : typing___Optional[generated___lib___drivers___nanopb___proto___scanner_pb2___Reply] = None,
        dawg : typing___Optional[generated___lib___drivers___nanopb___proto___dawg_pb2___Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"dawg",b"dawg",u"reply",b"reply",u"scanner",b"scanner"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"dawg",b"dawg",u"reply",b"reply",u"scanner",b"scanner"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["ack","scanner","dawg"]: ...
type___Reply = Reply

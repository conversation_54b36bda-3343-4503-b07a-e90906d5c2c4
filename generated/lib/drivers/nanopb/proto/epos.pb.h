/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_EPOS_EPOS_PB_H_INCLUDED
#define PB_EPOS_EPOS_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/can_open.pb.h"
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _epos_PID_Request_Type {
    epos_PID_Request_Type_PID_REQUEST_FALLBACK = 0,
    epos_PID_Request_Type_PID_REQUEST_TEST = 1,
    epos_PID_Request_Type_PID_REQUEST_SAVE = 2
} epos_PID_Request_Type;

/* Struct definitions */
typedef struct _epos_Disable_Request {
    char dummy_field;
} epos_Disable_Request;

typedef struct _epos_Enable_Request {
    char dummy_field;
} epos_Enable_Request;

typedef struct _epos_Get_PID_Request {
    char dummy_field;
} epos_Get_PID_Request;

typedef struct _epos_Get_Pos_Vel_Request {
    char dummy_field;
} epos_Get_Pos_Vel_Request;

typedef struct _epos_Setup_PDOs_Request {
    char dummy_field;
} epos_Setup_PDOs_Request;

typedef struct _epos_Actual_Position_Home_Params {
    uint32_t range;
} epos_Actual_Position_Home_Params;

typedef struct _epos_Await_Settling_Request {
    int32_t target_position;
    uint32_t window;
    uint32_t timeout_ms;
} epos_Await_Settling_Request;

typedef struct _epos_Await_Status_Request {
    uint32_t timeout_ms;
    uint32_t expected;
    uint32_t expected_neg;
} epos_Await_Status_Request;

typedef struct _epos_EPOS_PID {
    uint32_t gain_p;
    uint32_t gain_i;
    uint32_t gain_d;
    uint32_t gain_ffv;
    uint32_t gain_ffa;
    uint32_t current_p;
    uint32_t current_i;
} epos_EPOS_PID;

typedef struct _epos_Go_To_Request {
    int32_t position;
    uint32_t velocity;
    uint32_t window;
    uint32_t timeout_ms;
} epos_Go_To_Request;

typedef struct _epos_Hard_Home_Params {
    int32_t step_size;
    uint32_t offset;
} epos_Hard_Home_Params;

typedef struct _epos_Homing_Limit_Reply {
    int32_t limit;
} epos_Homing_Limit_Reply;

typedef struct _epos_Pos_Vel_Reply {
    int32_t position;
    int32_t velocity;
} epos_Pos_Vel_Reply;

typedef struct _epos_Set_Positional_PID_Request {
    uint32_t gain_p;
    uint32_t gain_i;
    uint32_t gain_d;
    uint32_t gain_ffv;
    uint32_t gain_ffa;
} epos_Set_Positional_PID_Request;

typedef struct _epos_Settling_Time_Reply {
    uint32_t duration;
} epos_Settling_Time_Reply;

typedef struct _epos_Switch_Home_Params {
    int32_t step_size;
    uint32_t threshold_step;
} epos_Switch_Home_Params;

typedef struct _epos_Get_PID_Reply {
    bool valid;
    pb_size_t which_pid;
    union {
        epos_EPOS_PID epos;
    } pid;
} epos_Get_PID_Reply;

typedef struct _epos_Home_Params {
    int32_t min_position;
    int32_t max_position;
    uint32_t profile_velocity;
    pb_size_t which_params;
    union {
        epos_Hard_Home_Params hard_stop;
        epos_Switch_Home_Params limit_switch;
        epos_Actual_Position_Home_Params actual_position;
    } params;
    bool invert;
} epos_Home_Params;

typedef struct _epos_Set_PID_Request {
    uint32_t current_p;
    uint32_t current_i;
    bool has_positional_pid;
    epos_Set_Positional_PID_Request positional_pid;
} epos_Set_PID_Request;

typedef struct _epos_Set_PID_V2_Request {
    epos_PID_Request_Type type;
    pb_size_t which_pid;
    union {
        epos_EPOS_PID epos;
    } pid;
} epos_Set_PID_V2_Request;

typedef struct _epos_Home_Request {
    bool has_params;
    epos_Home_Params params;
} epos_Home_Request;

typedef struct _epos_Reply {
    pb_size_t which_reply;
    union {
        ack_Ack ack;
        can_open_Reply can;
        epos_Homing_Limit_Reply limit;
        epos_Settling_Time_Reply settle;
        epos_Pos_Vel_Reply pos_vel;
        error_Error error;
        epos_Get_PID_Reply pid;
    } reply;
} epos_Reply;

typedef struct _epos_Request {
    pb_size_t which_request;
    union {
        can_open_Request can;
        epos_Setup_PDOs_Request setup_pdos;
        epos_Enable_Request enable;
        epos_Disable_Request disable;
        epos_Home_Request home;
        epos_Await_Settling_Request settle;
        epos_Get_Pos_Vel_Request pos_vel;
        epos_Go_To_Request go_to;
        epos_Await_Status_Request status;
        epos_Set_Positional_PID_Request positional_pid;
        epos_Set_PID_Request pid;
        epos_Set_PID_V2_Request pid_v2;
        epos_Get_PID_Request get_pid;
    } request;
} epos_Request;


/* Helper constants for enums */
#define _epos_PID_Request_Type_MIN epos_PID_Request_Type_PID_REQUEST_FALLBACK
#define _epos_PID_Request_Type_MAX epos_PID_Request_Type_PID_REQUEST_SAVE
#define _epos_PID_Request_Type_ARRAYSIZE ((epos_PID_Request_Type)(epos_PID_Request_Type_PID_REQUEST_SAVE+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define epos_Setup_PDOs_Request_init_default     {0}
#define epos_Enable_Request_init_default         {0}
#define epos_Disable_Request_init_default        {0}
#define epos_Hard_Home_Params_init_default       {0, 0}
#define epos_Switch_Home_Params_init_default     {0, 0}
#define epos_Actual_Position_Home_Params_init_default {0}
#define epos_Home_Params_init_default            {0, 0, 0, 0, {epos_Hard_Home_Params_init_default}, 0}
#define epos_Home_Request_init_default           {false, epos_Home_Params_init_default}
#define epos_Await_Settling_Request_init_default {0, 0, 0}
#define epos_Get_Pos_Vel_Request_init_default    {0}
#define epos_Go_To_Request_init_default          {0, 0, 0, 0}
#define epos_Await_Status_Request_init_default   {0, 0, 0}
#define epos_Set_Positional_PID_Request_init_default {0, 0, 0, 0, 0}
#define epos_Set_PID_Request_init_default        {0, 0, false, epos_Set_Positional_PID_Request_init_default}
#define epos_EPOS_PID_init_default               {0, 0, 0, 0, 0, 0, 0}
#define epos_Set_PID_V2_Request_init_default     {_epos_PID_Request_Type_MIN, 0, {epos_EPOS_PID_init_default}}
#define epos_Get_PID_Request_init_default        {0}
#define epos_Homing_Limit_Reply_init_default     {0}
#define epos_Settling_Time_Reply_init_default    {0}
#define epos_Pos_Vel_Reply_init_default          {0, 0}
#define epos_Get_PID_Reply_init_default          {0, 0, {epos_EPOS_PID_init_default}}
#define epos_Request_init_default                {0, {can_open_Request_init_default}}
#define epos_Reply_init_default                  {0, {ack_Ack_init_default}}
#define epos_Setup_PDOs_Request_init_zero        {0}
#define epos_Enable_Request_init_zero            {0}
#define epos_Disable_Request_init_zero           {0}
#define epos_Hard_Home_Params_init_zero          {0, 0}
#define epos_Switch_Home_Params_init_zero        {0, 0}
#define epos_Actual_Position_Home_Params_init_zero {0}
#define epos_Home_Params_init_zero               {0, 0, 0, 0, {epos_Hard_Home_Params_init_zero}, 0}
#define epos_Home_Request_init_zero              {false, epos_Home_Params_init_zero}
#define epos_Await_Settling_Request_init_zero    {0, 0, 0}
#define epos_Get_Pos_Vel_Request_init_zero       {0}
#define epos_Go_To_Request_init_zero             {0, 0, 0, 0}
#define epos_Await_Status_Request_init_zero      {0, 0, 0}
#define epos_Set_Positional_PID_Request_init_zero {0, 0, 0, 0, 0}
#define epos_Set_PID_Request_init_zero           {0, 0, false, epos_Set_Positional_PID_Request_init_zero}
#define epos_EPOS_PID_init_zero                  {0, 0, 0, 0, 0, 0, 0}
#define epos_Set_PID_V2_Request_init_zero        {_epos_PID_Request_Type_MIN, 0, {epos_EPOS_PID_init_zero}}
#define epos_Get_PID_Request_init_zero           {0}
#define epos_Homing_Limit_Reply_init_zero        {0}
#define epos_Settling_Time_Reply_init_zero       {0}
#define epos_Pos_Vel_Reply_init_zero             {0, 0}
#define epos_Get_PID_Reply_init_zero             {0, 0, {epos_EPOS_PID_init_zero}}
#define epos_Request_init_zero                   {0, {can_open_Request_init_zero}}
#define epos_Reply_init_zero                     {0, {ack_Ack_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define epos_Actual_Position_Home_Params_range_tag 1
#define epos_Await_Settling_Request_target_position_tag 1
#define epos_Await_Settling_Request_window_tag   2
#define epos_Await_Settling_Request_timeout_ms_tag 3
#define epos_Await_Status_Request_timeout_ms_tag 1
#define epos_Await_Status_Request_expected_tag   2
#define epos_Await_Status_Request_expected_neg_tag 3
#define epos_EPOS_PID_gain_p_tag                 1
#define epos_EPOS_PID_gain_i_tag                 2
#define epos_EPOS_PID_gain_d_tag                 3
#define epos_EPOS_PID_gain_ffv_tag               4
#define epos_EPOS_PID_gain_ffa_tag               5
#define epos_EPOS_PID_current_p_tag              6
#define epos_EPOS_PID_current_i_tag              7
#define epos_Go_To_Request_position_tag          1
#define epos_Go_To_Request_velocity_tag          2
#define epos_Go_To_Request_window_tag            3
#define epos_Go_To_Request_timeout_ms_tag        4
#define epos_Hard_Home_Params_step_size_tag      1
#define epos_Hard_Home_Params_offset_tag         2
#define epos_Homing_Limit_Reply_limit_tag        1
#define epos_Pos_Vel_Reply_position_tag          1
#define epos_Pos_Vel_Reply_velocity_tag          2
#define epos_Set_Positional_PID_Request_gain_p_tag 1
#define epos_Set_Positional_PID_Request_gain_i_tag 2
#define epos_Set_Positional_PID_Request_gain_d_tag 3
#define epos_Set_Positional_PID_Request_gain_ffv_tag 4
#define epos_Set_Positional_PID_Request_gain_ffa_tag 5
#define epos_Settling_Time_Reply_duration_tag    1
#define epos_Switch_Home_Params_step_size_tag    1
#define epos_Switch_Home_Params_threshold_step_tag 2
#define epos_Get_PID_Reply_valid_tag             1
#define epos_Get_PID_Reply_epos_tag              2
#define epos_Home_Params_min_position_tag        1
#define epos_Home_Params_max_position_tag        2
#define epos_Home_Params_profile_velocity_tag    3
#define epos_Home_Params_hard_stop_tag           4
#define epos_Home_Params_limit_switch_tag        5
#define epos_Home_Params_actual_position_tag     6
#define epos_Home_Params_invert_tag              7
#define epos_Set_PID_Request_current_p_tag       1
#define epos_Set_PID_Request_current_i_tag       2
#define epos_Set_PID_Request_positional_pid_tag  3
#define epos_Set_PID_V2_Request_type_tag         1
#define epos_Set_PID_V2_Request_epos_tag         2
#define epos_Home_Request_params_tag             1
#define epos_Reply_ack_tag                       1
#define epos_Reply_can_tag                       2
#define epos_Reply_limit_tag                     3
#define epos_Reply_settle_tag                    4
#define epos_Reply_pos_vel_tag                   5
#define epos_Reply_error_tag                     6
#define epos_Reply_pid_tag                       7
#define epos_Request_can_tag                     1
#define epos_Request_setup_pdos_tag              2
#define epos_Request_enable_tag                  3
#define epos_Request_disable_tag                 4
#define epos_Request_home_tag                    5
#define epos_Request_settle_tag                  6
#define epos_Request_pos_vel_tag                 7
#define epos_Request_go_to_tag                   8
#define epos_Request_status_tag                  9
#define epos_Request_positional_pid_tag          10
#define epos_Request_pid_tag                     11
#define epos_Request_pid_v2_tag                  12
#define epos_Request_get_pid_tag                 13

/* Struct field encoding specification for nanopb */
#define epos_Setup_PDOs_Request_FIELDLIST(X, a) \

#define epos_Setup_PDOs_Request_CALLBACK NULL
#define epos_Setup_PDOs_Request_DEFAULT NULL

#define epos_Enable_Request_FIELDLIST(X, a) \

#define epos_Enable_Request_CALLBACK NULL
#define epos_Enable_Request_DEFAULT NULL

#define epos_Disable_Request_FIELDLIST(X, a) \

#define epos_Disable_Request_CALLBACK NULL
#define epos_Disable_Request_DEFAULT NULL

#define epos_Hard_Home_Params_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    step_size,         1) \
X(a, STATIC,   SINGULAR, UINT32,   offset,            2)
#define epos_Hard_Home_Params_CALLBACK NULL
#define epos_Hard_Home_Params_DEFAULT NULL

#define epos_Switch_Home_Params_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    step_size,         1) \
X(a, STATIC,   SINGULAR, UINT32,   threshold_step,    2)
#define epos_Switch_Home_Params_CALLBACK NULL
#define epos_Switch_Home_Params_DEFAULT NULL

#define epos_Actual_Position_Home_Params_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   range,             1)
#define epos_Actual_Position_Home_Params_CALLBACK NULL
#define epos_Actual_Position_Home_Params_DEFAULT NULL

#define epos_Home_Params_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    min_position,      1) \
X(a, STATIC,   SINGULAR, INT32,    max_position,      2) \
X(a, STATIC,   SINGULAR, UINT32,   profile_velocity,   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (params,hard_stop,params.hard_stop),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (params,limit_switch,params.limit_switch),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (params,actual_position,params.actual_position),   6) \
X(a, STATIC,   SINGULAR, BOOL,     invert,            7)
#define epos_Home_Params_CALLBACK NULL
#define epos_Home_Params_DEFAULT NULL
#define epos_Home_Params_params_hard_stop_MSGTYPE epos_Hard_Home_Params
#define epos_Home_Params_params_limit_switch_MSGTYPE epos_Switch_Home_Params
#define epos_Home_Params_params_actual_position_MSGTYPE epos_Actual_Position_Home_Params

#define epos_Home_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  params,            1)
#define epos_Home_Request_CALLBACK NULL
#define epos_Home_Request_DEFAULT NULL
#define epos_Home_Request_params_MSGTYPE epos_Home_Params

#define epos_Await_Settling_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    target_position,   1) \
X(a, STATIC,   SINGULAR, UINT32,   window,            2) \
X(a, STATIC,   SINGULAR, UINT32,   timeout_ms,        3)
#define epos_Await_Settling_Request_CALLBACK NULL
#define epos_Await_Settling_Request_DEFAULT NULL

#define epos_Get_Pos_Vel_Request_FIELDLIST(X, a) \

#define epos_Get_Pos_Vel_Request_CALLBACK NULL
#define epos_Get_Pos_Vel_Request_DEFAULT NULL

#define epos_Go_To_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    position,          1) \
X(a, STATIC,   SINGULAR, UINT32,   velocity,          2) \
X(a, STATIC,   SINGULAR, UINT32,   window,            3) \
X(a, STATIC,   SINGULAR, UINT32,   timeout_ms,        4)
#define epos_Go_To_Request_CALLBACK NULL
#define epos_Go_To_Request_DEFAULT NULL

#define epos_Await_Status_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   timeout_ms,        1) \
X(a, STATIC,   SINGULAR, UINT32,   expected,          2) \
X(a, STATIC,   SINGULAR, UINT32,   expected_neg,      3)
#define epos_Await_Status_Request_CALLBACK NULL
#define epos_Await_Status_Request_DEFAULT NULL

#define epos_Set_Positional_PID_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   gain_p,            1) \
X(a, STATIC,   SINGULAR, UINT32,   gain_i,            2) \
X(a, STATIC,   SINGULAR, UINT32,   gain_d,            3) \
X(a, STATIC,   SINGULAR, UINT32,   gain_ffv,          4) \
X(a, STATIC,   SINGULAR, UINT32,   gain_ffa,          5)
#define epos_Set_Positional_PID_Request_CALLBACK NULL
#define epos_Set_Positional_PID_Request_DEFAULT NULL

#define epos_Set_PID_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   current_p,         1) \
X(a, STATIC,   SINGULAR, UINT32,   current_i,         2) \
X(a, STATIC,   OPTIONAL, MESSAGE,  positional_pid,    3)
#define epos_Set_PID_Request_CALLBACK NULL
#define epos_Set_PID_Request_DEFAULT NULL
#define epos_Set_PID_Request_positional_pid_MSGTYPE epos_Set_Positional_PID_Request

#define epos_EPOS_PID_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   gain_p,            1) \
X(a, STATIC,   SINGULAR, UINT32,   gain_i,            2) \
X(a, STATIC,   SINGULAR, UINT32,   gain_d,            3) \
X(a, STATIC,   SINGULAR, UINT32,   gain_ffv,          4) \
X(a, STATIC,   SINGULAR, UINT32,   gain_ffa,          5) \
X(a, STATIC,   SINGULAR, UINT32,   current_p,         6) \
X(a, STATIC,   SINGULAR, UINT32,   current_i,         7)
#define epos_EPOS_PID_CALLBACK NULL
#define epos_EPOS_PID_DEFAULT NULL

#define epos_Set_PID_V2_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (pid,epos,pid.epos),   2)
#define epos_Set_PID_V2_Request_CALLBACK NULL
#define epos_Set_PID_V2_Request_DEFAULT NULL
#define epos_Set_PID_V2_Request_pid_epos_MSGTYPE epos_EPOS_PID

#define epos_Get_PID_Request_FIELDLIST(X, a) \

#define epos_Get_PID_Request_CALLBACK NULL
#define epos_Get_PID_Request_DEFAULT NULL

#define epos_Homing_Limit_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    limit,             1)
#define epos_Homing_Limit_Reply_CALLBACK NULL
#define epos_Homing_Limit_Reply_DEFAULT NULL

#define epos_Settling_Time_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   duration,          1)
#define epos_Settling_Time_Reply_CALLBACK NULL
#define epos_Settling_Time_Reply_DEFAULT NULL

#define epos_Pos_Vel_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    position,          1) \
X(a, STATIC,   SINGULAR, INT32,    velocity,          2)
#define epos_Pos_Vel_Reply_CALLBACK NULL
#define epos_Pos_Vel_Reply_DEFAULT NULL

#define epos_Get_PID_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     valid,             1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (pid,epos,pid.epos),   2)
#define epos_Get_PID_Reply_CALLBACK NULL
#define epos_Get_PID_Reply_DEFAULT NULL
#define epos_Get_PID_Reply_pid_epos_MSGTYPE epos_EPOS_PID

#define epos_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,can,request.can),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,setup_pdos,request.setup_pdos),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,enable,request.enable),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,disable,request.disable),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,home,request.home),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,settle,request.settle),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pos_vel,request.pos_vel),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,go_to,request.go_to),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,status,request.status),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,positional_pid,request.positional_pid),  10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pid,request.pid),  11) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pid_v2,request.pid_v2),  12) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_pid,request.get_pid),  13)
#define epos_Request_CALLBACK NULL
#define epos_Request_DEFAULT NULL
#define epos_Request_request_can_MSGTYPE can_open_Request
#define epos_Request_request_setup_pdos_MSGTYPE epos_Setup_PDOs_Request
#define epos_Request_request_enable_MSGTYPE epos_Enable_Request
#define epos_Request_request_disable_MSGTYPE epos_Disable_Request
#define epos_Request_request_home_MSGTYPE epos_Home_Request
#define epos_Request_request_settle_MSGTYPE epos_Await_Settling_Request
#define epos_Request_request_pos_vel_MSGTYPE epos_Get_Pos_Vel_Request
#define epos_Request_request_go_to_MSGTYPE epos_Go_To_Request
#define epos_Request_request_status_MSGTYPE epos_Await_Status_Request
#define epos_Request_request_positional_pid_MSGTYPE epos_Set_Positional_PID_Request
#define epos_Request_request_pid_MSGTYPE epos_Set_PID_Request
#define epos_Request_request_pid_v2_MSGTYPE epos_Set_PID_V2_Request
#define epos_Request_request_get_pid_MSGTYPE epos_Get_PID_Request

#define epos_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,can,reply.can),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,limit,reply.limit),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,settle,reply.settle),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pos_vel,reply.pos_vel),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pid,reply.pid),   7)
#define epos_Reply_CALLBACK NULL
#define epos_Reply_DEFAULT NULL
#define epos_Reply_reply_ack_MSGTYPE ack_Ack
#define epos_Reply_reply_can_MSGTYPE can_open_Reply
#define epos_Reply_reply_limit_MSGTYPE epos_Homing_Limit_Reply
#define epos_Reply_reply_settle_MSGTYPE epos_Settling_Time_Reply
#define epos_Reply_reply_pos_vel_MSGTYPE epos_Pos_Vel_Reply
#define epos_Reply_reply_error_MSGTYPE error_Error
#define epos_Reply_reply_pid_MSGTYPE epos_Get_PID_Reply

extern const pb_msgdesc_t epos_Setup_PDOs_Request_msg;
extern const pb_msgdesc_t epos_Enable_Request_msg;
extern const pb_msgdesc_t epos_Disable_Request_msg;
extern const pb_msgdesc_t epos_Hard_Home_Params_msg;
extern const pb_msgdesc_t epos_Switch_Home_Params_msg;
extern const pb_msgdesc_t epos_Actual_Position_Home_Params_msg;
extern const pb_msgdesc_t epos_Home_Params_msg;
extern const pb_msgdesc_t epos_Home_Request_msg;
extern const pb_msgdesc_t epos_Await_Settling_Request_msg;
extern const pb_msgdesc_t epos_Get_Pos_Vel_Request_msg;
extern const pb_msgdesc_t epos_Go_To_Request_msg;
extern const pb_msgdesc_t epos_Await_Status_Request_msg;
extern const pb_msgdesc_t epos_Set_Positional_PID_Request_msg;
extern const pb_msgdesc_t epos_Set_PID_Request_msg;
extern const pb_msgdesc_t epos_EPOS_PID_msg;
extern const pb_msgdesc_t epos_Set_PID_V2_Request_msg;
extern const pb_msgdesc_t epos_Get_PID_Request_msg;
extern const pb_msgdesc_t epos_Homing_Limit_Reply_msg;
extern const pb_msgdesc_t epos_Settling_Time_Reply_msg;
extern const pb_msgdesc_t epos_Pos_Vel_Reply_msg;
extern const pb_msgdesc_t epos_Get_PID_Reply_msg;
extern const pb_msgdesc_t epos_Request_msg;
extern const pb_msgdesc_t epos_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define epos_Setup_PDOs_Request_fields &epos_Setup_PDOs_Request_msg
#define epos_Enable_Request_fields &epos_Enable_Request_msg
#define epos_Disable_Request_fields &epos_Disable_Request_msg
#define epos_Hard_Home_Params_fields &epos_Hard_Home_Params_msg
#define epos_Switch_Home_Params_fields &epos_Switch_Home_Params_msg
#define epos_Actual_Position_Home_Params_fields &epos_Actual_Position_Home_Params_msg
#define epos_Home_Params_fields &epos_Home_Params_msg
#define epos_Home_Request_fields &epos_Home_Request_msg
#define epos_Await_Settling_Request_fields &epos_Await_Settling_Request_msg
#define epos_Get_Pos_Vel_Request_fields &epos_Get_Pos_Vel_Request_msg
#define epos_Go_To_Request_fields &epos_Go_To_Request_msg
#define epos_Await_Status_Request_fields &epos_Await_Status_Request_msg
#define epos_Set_Positional_PID_Request_fields &epos_Set_Positional_PID_Request_msg
#define epos_Set_PID_Request_fields &epos_Set_PID_Request_msg
#define epos_EPOS_PID_fields &epos_EPOS_PID_msg
#define epos_Set_PID_V2_Request_fields &epos_Set_PID_V2_Request_msg
#define epos_Get_PID_Request_fields &epos_Get_PID_Request_msg
#define epos_Homing_Limit_Reply_fields &epos_Homing_Limit_Reply_msg
#define epos_Settling_Time_Reply_fields &epos_Settling_Time_Reply_msg
#define epos_Pos_Vel_Reply_fields &epos_Pos_Vel_Reply_msg
#define epos_Get_PID_Reply_fields &epos_Get_PID_Reply_msg
#define epos_Request_fields &epos_Request_msg
#define epos_Reply_fields &epos_Reply_msg

/* Maximum encoded size of messages (where known) */
#define epos_Setup_PDOs_Request_size             0
#define epos_Enable_Request_size                 0
#define epos_Disable_Request_size                0
#define epos_Hard_Home_Params_size               17
#define epos_Switch_Home_Params_size             17
#define epos_Actual_Position_Home_Params_size    6
#define epos_Home_Params_size                    49
#define epos_Home_Request_size                   51
#define epos_Await_Settling_Request_size         23
#define epos_Get_Pos_Vel_Request_size            0
#define epos_Go_To_Request_size                  29
#define epos_Await_Status_Request_size           18
#define epos_Set_Positional_PID_Request_size     30
#define epos_Set_PID_Request_size                44
#define epos_EPOS_PID_size                       42
#define epos_Set_PID_V2_Request_size             46
#define epos_Get_PID_Request_size                0
#define epos_Homing_Limit_Reply_size             11
#define epos_Settling_Time_Reply_size            6
#define epos_Pos_Vel_Reply_size                  22
#define epos_Get_PID_Reply_size                  46
#if defined(can_open_Request_size)
typedef union epos_Request_request_size_union {char f1[(6 + can_open_Request_size)]; char f0[53];} epos_Request_request_size_union;
#define epos_Request_size                        (0 + sizeof(epos_Request_request_size_union))
#endif
#if defined(ack_Ack_size) && defined(can_open_Reply_size) && defined(error_Error_size)
typedef union epos_Reply_reply_size_union {char f1[(6 + ack_Ack_size)]; char f2[(6 + can_open_Reply_size)]; char f6[(6 + error_Error_size)]; char f0[48];} epos_Reply_reply_size_union;
#define epos_Reply_size                          (0 + sizeof(epos_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

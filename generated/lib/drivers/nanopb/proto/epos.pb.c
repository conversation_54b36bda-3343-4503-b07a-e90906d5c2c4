/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "epos.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(epos_Setup_PDOs_Request, epos_Setup_PDOs_Request, AUTO)


PB_BIND(epos_Enable_Request, epos_Enable_Request, AUTO)


PB_BIND(epos_Disable_Request, epos_Disable_Request, AUTO)


PB_BIND(epos_Hard_Home_Params, epos_Hard_Home_Params, AUTO)


PB_BIND(epos_Switch_Home_Params, epos_Switch_Home_Params, AUTO)


PB_BIND(epos_Actual_Position_Home_Params, epos_Actual_Position_Home_Params, AUTO)


PB_BIND(epos_Home_Params, epos_Home_Params, AUTO)


PB_BIND(epos_Home_Request, epos_Home_Request, AUTO)


PB_BIND(epos_Await_Settling_Request, epos_Await_Settling_Request, AUTO)


PB_BIND(epos_Get_Pos_Vel_Request, epos_Get_Pos_Vel_Request, AUTO)


PB_BIND(epos_Go_To_Request, epos_Go_To_Request, AUTO)


PB_BIND(epos_Await_Status_Request, epos_Await_Status_Request, AUTO)


PB_BIND(epos_Set_Positional_PID_Request, epos_Set_Positional_PID_Request, AUTO)


PB_BIND(epos_Set_PID_Request, epos_Set_PID_Request, AUTO)


PB_BIND(epos_EPOS_PID, epos_EPOS_PID, AUTO)


PB_BIND(epos_Set_PID_V2_Request, epos_Set_PID_V2_Request, AUTO)


PB_BIND(epos_Get_PID_Request, epos_Get_PID_Request, AUTO)


PB_BIND(epos_Homing_Limit_Reply, epos_Homing_Limit_Reply, AUTO)


PB_BIND(epos_Settling_Time_Reply, epos_Settling_Time_Reply, AUTO)


PB_BIND(epos_Pos_Vel_Reply, epos_Pos_Vel_Reply, AUTO)


PB_BIND(epos_Get_PID_Reply, epos_Get_PID_Reply, AUTO)


PB_BIND(epos_Request, epos_Request, 2)


PB_BIND(epos_Reply, epos_Reply, 2)





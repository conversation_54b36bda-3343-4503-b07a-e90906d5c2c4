# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/request.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/request.proto',
  package='request',
  syntax='proto3',
  serialized_options=b'Z\016nanopb/request',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n0generated/lib/drivers/nanopb/proto/request.proto\x12\x07request\"\"\n\rRequestHeader\x12\x11\n\trequestId\x18\x01 \x01(\x05\x42\x10Z\x0enanopb/requestb\x06proto3'
)




_REQUESTHEADER = _descriptor.Descriptor(
  name='RequestHeader',
  full_name='request.RequestHeader',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='requestId', full_name='request.RequestHeader.requestId', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=61,
  serialized_end=95,
)

DESCRIPTOR.message_types_by_name['RequestHeader'] = _REQUESTHEADER
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RequestHeader = _reflection.GeneratedProtocolMessageType('RequestHeader', (_message.Message,), {
  'DESCRIPTOR' : _REQUESTHEADER,
  '__module__' : 'generated.lib.drivers.nanopb.proto.request_pb2'
  # @@protoc_insertion_point(class_scope:request.RequestHeader)
  })
_sym_db.RegisterMessage(RequestHeader)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

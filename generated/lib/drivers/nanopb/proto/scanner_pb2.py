# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/scanner.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import servo_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2
from generated.lib.drivers.nanopb.proto import epos_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/scanner.proto',
  package='scanner',
  syntax='proto3',
  serialized_options=b'Z\016nanopb/scanner',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n0generated/lib/drivers/nanopb/proto/scanner.proto\x12\x07scanner\x1a.generated/lib/drivers/nanopb/proto/servo.proto\x1a-generated/lib/drivers/nanopb/proto/epos.proto\"\x1b\n\rLaser_Request\x12\n\n\x02on\x18\x01 \x01(\x08\"\x13\n\x11Get_Laser_Request\"&\n\x11Intensity_Request\x12\x11\n\tintensity\x18\x01 \x01(\x05\"]\n\x0c\x42oot_Request\x12%\n\npan_params\x18\x01 \x01(\x0b\x32\x11.epos.Home_Params\x12&\n\x0btilt_params\x18\x02 \x01(\x0b\x32\x11.epos.Home_Params\"\x0e\n\x0cStop_Request\"K\n\x0eGimbal_Request\x12\x1b\n\x03pan\x18\x01 \x01(\x0b\x32\x0e.servo.Request\x12\x1c\n\x04tilt\x18\x02 \x01(\x0b\x32\x0e.servo.Request\"\r\n\x0b\x45rror_Reply\"\x0b\n\tACK_Reply\"\x1f\n\x11Laser_State_Reply\x12\n\n\x02on\x18\x01 \x01(\x08\"E\n\x0cGimbal_Reply\x12\x19\n\x03pan\x18\x01 \x01(\x0b\x32\x0c.servo.Reply\x12\x1a\n\x04tilt\x18\x02 \x01(\x0b\x32\x0c.servo.Reply\"\x98\x02\n\x07Request\x12\'\n\x05laser\x18\x01 \x01(\x0b\x32\x16.scanner.Laser_RequestH\x00\x12/\n\tget_laser\x18\x02 \x01(\x0b\x32\x1a.scanner.Get_Laser_RequestH\x00\x12%\n\x04\x62oot\x18\x03 \x01(\x0b\x32\x15.scanner.Boot_RequestH\x00\x12%\n\x04stop\x18\x04 \x01(\x0b\x32\x15.scanner.Stop_RequestH\x00\x12)\n\x06gimbal\x18\x05 \x01(\x0b\x32\x17.scanner.Gimbal_RequestH\x00\x12/\n\tintensity\x18\x06 \x01(\x0b\x32\x1a.scanner.Intensity_RequestH\x00\x42\t\n\x07request\"\xb0\x01\n\x05Reply\x12%\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x14.scanner.Error_ReplyH\x00\x12!\n\x03\x61\x63k\x18\x02 \x01(\x0b\x32\x12.scanner.ACK_ReplyH\x00\x12+\n\x05laser\x18\x03 \x01(\x0b\x32\x1a.scanner.Laser_State_ReplyH\x00\x12\'\n\x06gimbal\x18\x04 \x01(\x0b\x32\x15.scanner.Gimbal_ReplyH\x00\x42\x07\n\x05replyB\x10Z\x0enanopb/scannerb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2.DESCRIPTOR,])




_LASER_REQUEST = _descriptor.Descriptor(
  name='Laser_Request',
  full_name='scanner.Laser_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='on', full_name='scanner.Laser_Request.on', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=156,
  serialized_end=183,
)


_GET_LASER_REQUEST = _descriptor.Descriptor(
  name='Get_Laser_Request',
  full_name='scanner.Get_Laser_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=185,
  serialized_end=204,
)


_INTENSITY_REQUEST = _descriptor.Descriptor(
  name='Intensity_Request',
  full_name='scanner.Intensity_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='intensity', full_name='scanner.Intensity_Request.intensity', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=206,
  serialized_end=244,
)


_BOOT_REQUEST = _descriptor.Descriptor(
  name='Boot_Request',
  full_name='scanner.Boot_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan_params', full_name='scanner.Boot_Request.pan_params', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt_params', full_name='scanner.Boot_Request.tilt_params', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=246,
  serialized_end=339,
)


_STOP_REQUEST = _descriptor.Descriptor(
  name='Stop_Request',
  full_name='scanner.Stop_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=341,
  serialized_end=355,
)


_GIMBAL_REQUEST = _descriptor.Descriptor(
  name='Gimbal_Request',
  full_name='scanner.Gimbal_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan', full_name='scanner.Gimbal_Request.pan', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt', full_name='scanner.Gimbal_Request.tilt', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=357,
  serialized_end=432,
)


_ERROR_REPLY = _descriptor.Descriptor(
  name='Error_Reply',
  full_name='scanner.Error_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=434,
  serialized_end=447,
)


_ACK_REPLY = _descriptor.Descriptor(
  name='ACK_Reply',
  full_name='scanner.ACK_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=449,
  serialized_end=460,
)


_LASER_STATE_REPLY = _descriptor.Descriptor(
  name='Laser_State_Reply',
  full_name='scanner.Laser_State_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='on', full_name='scanner.Laser_State_Reply.on', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=462,
  serialized_end=493,
)


_GIMBAL_REPLY = _descriptor.Descriptor(
  name='Gimbal_Reply',
  full_name='scanner.Gimbal_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='pan', full_name='scanner.Gimbal_Reply.pan', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt', full_name='scanner.Gimbal_Reply.tilt', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=495,
  serialized_end=564,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='scanner.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='laser', full_name='scanner.Request.laser', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_laser', full_name='scanner.Request.get_laser', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='boot', full_name='scanner.Request.boot', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stop', full_name='scanner.Request.stop', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gimbal', full_name='scanner.Request.gimbal', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intensity', full_name='scanner.Request.intensity', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='scanner.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=567,
  serialized_end=847,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='scanner.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='scanner.Reply.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='scanner.Reply.ack', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser', full_name='scanner.Reply.laser', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gimbal', full_name='scanner.Reply.gimbal', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='scanner.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=850,
  serialized_end=1026,
)

_BOOT_REQUEST.fields_by_name['pan_params'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2._HOME_PARAMS
_BOOT_REQUEST.fields_by_name['tilt_params'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_epos__pb2._HOME_PARAMS
_GIMBAL_REQUEST.fields_by_name['pan'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2._REQUEST
_GIMBAL_REQUEST.fields_by_name['tilt'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2._REQUEST
_GIMBAL_REPLY.fields_by_name['pan'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2._REPLY
_GIMBAL_REPLY.fields_by_name['tilt'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_servo__pb2._REPLY
_REQUEST.fields_by_name['laser'].message_type = _LASER_REQUEST
_REQUEST.fields_by_name['get_laser'].message_type = _GET_LASER_REQUEST
_REQUEST.fields_by_name['boot'].message_type = _BOOT_REQUEST
_REQUEST.fields_by_name['stop'].message_type = _STOP_REQUEST
_REQUEST.fields_by_name['gimbal'].message_type = _GIMBAL_REQUEST
_REQUEST.fields_by_name['intensity'].message_type = _INTENSITY_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['laser'])
_REQUEST.fields_by_name['laser'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get_laser'])
_REQUEST.fields_by_name['get_laser'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['boot'])
_REQUEST.fields_by_name['boot'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['stop'])
_REQUEST.fields_by_name['stop'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['gimbal'])
_REQUEST.fields_by_name['gimbal'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['intensity'])
_REQUEST.fields_by_name['intensity'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['error'].message_type = _ERROR_REPLY
_REPLY.fields_by_name['ack'].message_type = _ACK_REPLY
_REPLY.fields_by_name['laser'].message_type = _LASER_STATE_REPLY
_REPLY.fields_by_name['gimbal'].message_type = _GIMBAL_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['laser'])
_REPLY.fields_by_name['laser'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['gimbal'])
_REPLY.fields_by_name['gimbal'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Laser_Request'] = _LASER_REQUEST
DESCRIPTOR.message_types_by_name['Get_Laser_Request'] = _GET_LASER_REQUEST
DESCRIPTOR.message_types_by_name['Intensity_Request'] = _INTENSITY_REQUEST
DESCRIPTOR.message_types_by_name['Boot_Request'] = _BOOT_REQUEST
DESCRIPTOR.message_types_by_name['Stop_Request'] = _STOP_REQUEST
DESCRIPTOR.message_types_by_name['Gimbal_Request'] = _GIMBAL_REQUEST
DESCRIPTOR.message_types_by_name['Error_Reply'] = _ERROR_REPLY
DESCRIPTOR.message_types_by_name['ACK_Reply'] = _ACK_REPLY
DESCRIPTOR.message_types_by_name['Laser_State_Reply'] = _LASER_STATE_REPLY
DESCRIPTOR.message_types_by_name['Gimbal_Reply'] = _GIMBAL_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Laser_Request = _reflection.GeneratedProtocolMessageType('Laser_Request', (_message.Message,), {
  'DESCRIPTOR' : _LASER_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Laser_Request)
  })
_sym_db.RegisterMessage(Laser_Request)

Get_Laser_Request = _reflection.GeneratedProtocolMessageType('Get_Laser_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_LASER_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Get_Laser_Request)
  })
_sym_db.RegisterMessage(Get_Laser_Request)

Intensity_Request = _reflection.GeneratedProtocolMessageType('Intensity_Request', (_message.Message,), {
  'DESCRIPTOR' : _INTENSITY_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Intensity_Request)
  })
_sym_db.RegisterMessage(Intensity_Request)

Boot_Request = _reflection.GeneratedProtocolMessageType('Boot_Request', (_message.Message,), {
  'DESCRIPTOR' : _BOOT_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Boot_Request)
  })
_sym_db.RegisterMessage(Boot_Request)

Stop_Request = _reflection.GeneratedProtocolMessageType('Stop_Request', (_message.Message,), {
  'DESCRIPTOR' : _STOP_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Stop_Request)
  })
_sym_db.RegisterMessage(Stop_Request)

Gimbal_Request = _reflection.GeneratedProtocolMessageType('Gimbal_Request', (_message.Message,), {
  'DESCRIPTOR' : _GIMBAL_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Gimbal_Request)
  })
_sym_db.RegisterMessage(Gimbal_Request)

Error_Reply = _reflection.GeneratedProtocolMessageType('Error_Reply', (_message.Message,), {
  'DESCRIPTOR' : _ERROR_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Error_Reply)
  })
_sym_db.RegisterMessage(Error_Reply)

ACK_Reply = _reflection.GeneratedProtocolMessageType('ACK_Reply', (_message.Message,), {
  'DESCRIPTOR' : _ACK_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.ACK_Reply)
  })
_sym_db.RegisterMessage(ACK_Reply)

Laser_State_Reply = _reflection.GeneratedProtocolMessageType('Laser_State_Reply', (_message.Message,), {
  'DESCRIPTOR' : _LASER_STATE_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Laser_State_Reply)
  })
_sym_db.RegisterMessage(Laser_State_Reply)

Gimbal_Reply = _reflection.GeneratedProtocolMessageType('Gimbal_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GIMBAL_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Gimbal_Reply)
  })
_sym_db.RegisterMessage(Gimbal_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.scanner_pb2'
  # @@protoc_insertion_point(class_scope:scanner.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

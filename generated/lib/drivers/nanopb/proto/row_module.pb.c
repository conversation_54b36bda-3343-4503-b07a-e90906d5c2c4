/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "row_module.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(row_module_Reset_Request, row_module_Reset_Request, AUTO)


PB_BIND(row_module_Clear_Config_Request, row_module_Clear_Config_Request, AUTO)


PB_BIND(row_module_Scanner_Request, row_module_Scanner_Request, 2)


PB_BIND(row_module_ACK_Reply, row_module_ACK_Reply, AUTO)


PB_BIND(row_module_Request, row_module_Request, 2)


PB_BIND(row_module_Reply, row_module_Reply, 2)




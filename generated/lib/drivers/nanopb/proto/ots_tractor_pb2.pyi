"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

TractorVariantTypeValue = typing___NewType('TractorVariantTypeValue', builtin___int)
type___TractorVariantTypeValue = TractorVariantTypeValue
TractorVariantType: _TractorVariantType
class _TractorVariantType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[TractorVariantTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    TV_UNKNOWN = typing___cast(TractorVariantTypeValue, 0)
    TV_JD_6LH = typing___cast(TractorVariantTypeValue, 1)
    TV_JD_6LHM = typing___cast(TractorVariantTypeValue, 2)
    TV_JD_6PRO = typing___cast(TractorVariantTypeValue, 3)
    TV_JD_7LH = typing___cast(TractorVariantTypeValue, 4)
    TV_JD_7PRO = typing___cast(TractorVariantTypeValue, 5)
    TV_JD_8RH = typing___cast(TractorVariantTypeValue, 6)
TV_UNKNOWN = typing___cast(TractorVariantTypeValue, 0)
TV_JD_6LH = typing___cast(TractorVariantTypeValue, 1)
TV_JD_6LHM = typing___cast(TractorVariantTypeValue, 2)
TV_JD_6PRO = typing___cast(TractorVariantTypeValue, 3)
TV_JD_7LH = typing___cast(TractorVariantTypeValue, 4)
TV_JD_7PRO = typing___cast(TractorVariantTypeValue, 5)
TV_JD_8RH = typing___cast(TractorVariantTypeValue, 6)

GearValue = typing___NewType('GearValue', builtin___int)
type___GearValue = GearValue
Gear: _Gear
class _Gear(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[GearValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    GEAR_PARK = typing___cast(GearValue, 0)
    GEAR_REVERSE = typing___cast(GearValue, 1)
    GEAR_NEUTRAL = typing___cast(GearValue, 2)
    GEAR_FORWARD = typing___cast(GearValue, 3)
    GEAR_POWERZERO = typing___cast(GearValue, 4)
GEAR_PARK = typing___cast(GearValue, 0)
GEAR_REVERSE = typing___cast(GearValue, 1)
GEAR_NEUTRAL = typing___cast(GearValue, 2)
GEAR_FORWARD = typing___cast(GearValue, 3)
GEAR_POWERZERO = typing___cast(GearValue, 4)

LightsValue = typing___NewType('LightsValue', builtin___int)
type___LightsValue = LightsValue
Lights: _Lights
class _Lights(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[LightsValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    LIGHTS_OFF = typing___cast(LightsValue, 0)
    LIGHTS_LOW = typing___cast(LightsValue, 1)
    LIGHTS_HIGH = typing___cast(LightsValue, 2)
LIGHTS_OFF = typing___cast(LightsValue, 0)
LIGHTS_LOW = typing___cast(LightsValue, 1)
LIGHTS_HIGH = typing___cast(LightsValue, 2)

HitchCmdValue = typing___NewType('HitchCmdValue', builtin___int)
type___HitchCmdValue = HitchCmdValue
HitchCmd: _HitchCmd
class _HitchCmd(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[HitchCmdValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    LIFT = typing___cast(HitchCmdValue, 0)
    LOWER = typing___cast(HitchCmdValue, 1)
    PRECISE = typing___cast(HitchCmdValue, 2)
LIFT = typing___cast(HitchCmdValue, 0)
LOWER = typing___cast(HitchCmdValue, 1)
PRECISE = typing___cast(HitchCmdValue, 2)

class GearState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    gear: type___GearValue = ...

    def __init__(self,
        *,
        gear : typing___Optional[type___GearValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"gear",b"gear"]) -> None: ...
type___GearState = GearState

class LightsState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lights: type___LightsValue = ...

    def __init__(self,
        *,
        lights : typing___Optional[type___LightsValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lights",b"lights"]) -> None: ...
type___LightsState = LightsState

class SpeedControlState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    speed: builtin___float = ...

    def __init__(self,
        *,
        speed : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"speed",b"speed"]) -> None: ...
type___SpeedControlState = SpeedControlState

class EngineRpmState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    rpms: builtin___int = ...

    def __init__(self,
        *,
        rpms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"rpms",b"rpms"]) -> None: ...
type___EngineRpmState = EngineRpmState

class PtoState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___PtoState = PtoState

class HitchV2Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    hitch: type___HitchCmdValue = ...
    precise_hitch_percent: builtin___float = ...

    def __init__(self,
        *,
        hitch : typing___Optional[type___HitchCmdValue] = None,
        precise_hitch_percent : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"hitch",b"hitch",u"precise_hitch_percent",b"precise_hitch_percent"]) -> None: ...
type___HitchV2Request = HitchV2Request

class HitchRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    hitch_lift_force: builtin___int = ...

    def __init__(self,
        *,
        hitch_lift_force : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"hitch_lift_force",b"hitch_lift_force"]) -> None: ...
type___HitchRequest = HitchRequest

class HitchReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___HitchReply = HitchReply

class ScvRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scv_id: builtin___int = ...
    force: builtin___int = ...
    cmd_time_ms: builtin___int = ...

    def __init__(self,
        *,
        scv_id : typing___Optional[builtin___int] = None,
        force : typing___Optional[builtin___int] = None,
        cmd_time_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cmd_time_ms",b"cmd_time_ms",u"force",b"force",u"scv_id",b"scv_id"]) -> None: ...
type___ScvRequest = ScvRequest

class ScvReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ScvReply = ScvReply

class TractorVariantState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    variant: type___TractorVariantTypeValue = ...

    def __init__(self,
        *,
        variant : typing___Optional[type___TractorVariantTypeValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"variant",b"variant"]) -> None: ...
type___TractorVariantState = TractorVariantState

class WheelAngleCalState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    sensor_deg_per_bit: builtin___float = ...
    sensor_center_val: builtin___int = ...
    center_trim_deg: builtin___float = ...
    right_lock_deg: builtin___float = ...
    sensor_full_left_val: builtin___int = ...
    sensor_full_right_val: builtin___int = ...

    def __init__(self,
        *,
        sensor_deg_per_bit : typing___Optional[builtin___float] = None,
        sensor_center_val : typing___Optional[builtin___int] = None,
        center_trim_deg : typing___Optional[builtin___float] = None,
        right_lock_deg : typing___Optional[builtin___float] = None,
        sensor_full_left_val : typing___Optional[builtin___int] = None,
        sensor_full_right_val : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_trim_deg",b"center_trim_deg",u"right_lock_deg",b"right_lock_deg",u"sensor_center_val",b"sensor_center_val",u"sensor_deg_per_bit",b"sensor_deg_per_bit",u"sensor_full_left_val",b"sensor_full_left_val",u"sensor_full_right_val",b"sensor_full_right_val"]) -> None: ...
type___WheelAngleCalState = WheelAngleCalState

class FuelLevel(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    fuel_level: builtin___float = ...

    def __init__(self,
        *,
        fuel_level : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"fuel_level",b"fuel_level"]) -> None: ...
type___FuelLevel = FuelLevel

class EngineTemp(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    engine_temp: builtin___float = ...

    def __init__(self,
        *,
        engine_temp : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"engine_temp",b"engine_temp"]) -> None: ...
type___EngineTemp = EngineTemp

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

class SetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def gear(self) -> type___GearState: ...

    @property
    def lights(self) -> type___LightsState: ...

    @property
    def speed_control(self) -> type___SpeedControlState: ...

    @property
    def hitch(self) -> type___HitchRequest: ...

    @property
    def scv(self) -> type___ScvRequest: ...

    @property
    def rpms(self) -> type___EngineRpmState: ...

    @property
    def front_pto(self) -> type___PtoState: ...

    @property
    def rear_pto(self) -> type___PtoState: ...

    @property
    def variant(self) -> type___TractorVariantState: ...

    @property
    def wheel_cal(self) -> type___WheelAngleCalState: ...

    @property
    def ignition_off(self) -> type___Empty: ...

    @property
    def hitch_V2(self) -> type___HitchV2Request: ...

    def __init__(self,
        *,
        gear : typing___Optional[type___GearState] = None,
        lights : typing___Optional[type___LightsState] = None,
        speed_control : typing___Optional[type___SpeedControlState] = None,
        hitch : typing___Optional[type___HitchRequest] = None,
        scv : typing___Optional[type___ScvRequest] = None,
        rpms : typing___Optional[type___EngineRpmState] = None,
        front_pto : typing___Optional[type___PtoState] = None,
        rear_pto : typing___Optional[type___PtoState] = None,
        variant : typing___Optional[type___TractorVariantState] = None,
        wheel_cal : typing___Optional[type___WheelAngleCalState] = None,
        ignition_off : typing___Optional[type___Empty] = None,
        hitch_V2 : typing___Optional[type___HitchV2Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"front_pto",b"front_pto",u"gear",b"gear",u"hitch",b"hitch",u"hitch_V2",b"hitch_V2",u"ignition_off",b"ignition_off",u"lights",b"lights",u"rear_pto",b"rear_pto",u"rpms",b"rpms",u"scv",b"scv",u"set",b"set",u"speed_control",b"speed_control",u"variant",b"variant",u"wheel_cal",b"wheel_cal"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"front_pto",b"front_pto",u"gear",b"gear",u"hitch",b"hitch",u"hitch_V2",b"hitch_V2",u"ignition_off",b"ignition_off",u"lights",b"lights",u"rear_pto",b"rear_pto",u"rpms",b"rpms",u"scv",b"scv",u"set",b"set",u"speed_control",b"speed_control",u"variant",b"variant",u"wheel_cal",b"wheel_cal"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"set",b"set"]) -> typing_extensions___Literal["gear","lights","speed_control","hitch","scv","rpms","front_pto","rear_pto","variant","wheel_cal","ignition_off","hitch_V2"]: ...
type___SetRequest = SetRequest

class SetReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def gear(self) -> type___GearState: ...

    @property
    def lights(self) -> type___LightsState: ...

    @property
    def speed_control(self) -> type___SpeedControlState: ...

    @property
    def hitch(self) -> type___HitchReply: ...

    @property
    def scv(self) -> type___ScvReply: ...

    @property
    def rpms(self) -> type___EngineRpmState: ...

    @property
    def front_pto(self) -> type___PtoState: ...

    @property
    def rear_pto(self) -> type___PtoState: ...

    @property
    def variant(self) -> type___TractorVariantState: ...

    @property
    def wheel_cal(self) -> type___WheelAngleCalState: ...

    @property
    def ignition_off(self) -> type___Empty: ...

    @property
    def hitch_V2(self) -> type___HitchReply: ...

    def __init__(self,
        *,
        gear : typing___Optional[type___GearState] = None,
        lights : typing___Optional[type___LightsState] = None,
        speed_control : typing___Optional[type___SpeedControlState] = None,
        hitch : typing___Optional[type___HitchReply] = None,
        scv : typing___Optional[type___ScvReply] = None,
        rpms : typing___Optional[type___EngineRpmState] = None,
        front_pto : typing___Optional[type___PtoState] = None,
        rear_pto : typing___Optional[type___PtoState] = None,
        variant : typing___Optional[type___TractorVariantState] = None,
        wheel_cal : typing___Optional[type___WheelAngleCalState] = None,
        ignition_off : typing___Optional[type___Empty] = None,
        hitch_V2 : typing___Optional[type___HitchReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"front_pto",b"front_pto",u"gear",b"gear",u"hitch",b"hitch",u"hitch_V2",b"hitch_V2",u"ignition_off",b"ignition_off",u"lights",b"lights",u"rear_pto",b"rear_pto",u"rpms",b"rpms",u"scv",b"scv",u"set",b"set",u"speed_control",b"speed_control",u"variant",b"variant",u"wheel_cal",b"wheel_cal"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"front_pto",b"front_pto",u"gear",b"gear",u"hitch",b"hitch",u"hitch_V2",b"hitch_V2",u"ignition_off",b"ignition_off",u"lights",b"lights",u"rear_pto",b"rear_pto",u"rpms",b"rpms",u"scv",b"scv",u"set",b"set",u"speed_control",b"speed_control",u"variant",b"variant",u"wheel_cal",b"wheel_cal"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"set",b"set"]) -> typing_extensions___Literal["gear","lights","speed_control","hitch","scv","rpms","front_pto","rear_pto","variant","wheel_cal","ignition_off","hitch_V2"]: ...
type___SetReply = SetReply

class GetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def gear(self) -> type___Empty: ...

    @property
    def lights(self) -> type___Empty: ...

    @property
    def speed_control(self) -> type___Empty: ...

    @property
    def rpms(self) -> type___Empty: ...

    @property
    def front_pto(self) -> type___Empty: ...

    @property
    def rear_pto(self) -> type___Empty: ...

    @property
    def variant(self) -> type___Empty: ...

    @property
    def wheel_cal(self) -> type___Empty: ...

    @property
    def fuel_level(self) -> type___Empty: ...

    @property
    def engine_temp(self) -> type___Empty: ...

    def __init__(self,
        *,
        gear : typing___Optional[type___Empty] = None,
        lights : typing___Optional[type___Empty] = None,
        speed_control : typing___Optional[type___Empty] = None,
        rpms : typing___Optional[type___Empty] = None,
        front_pto : typing___Optional[type___Empty] = None,
        rear_pto : typing___Optional[type___Empty] = None,
        variant : typing___Optional[type___Empty] = None,
        wheel_cal : typing___Optional[type___Empty] = None,
        fuel_level : typing___Optional[type___Empty] = None,
        engine_temp : typing___Optional[type___Empty] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"engine_temp",b"engine_temp",u"front_pto",b"front_pto",u"fuel_level",b"fuel_level",u"gear",b"gear",u"get",b"get",u"lights",b"lights",u"rear_pto",b"rear_pto",u"rpms",b"rpms",u"speed_control",b"speed_control",u"variant",b"variant",u"wheel_cal",b"wheel_cal"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"engine_temp",b"engine_temp",u"front_pto",b"front_pto",u"fuel_level",b"fuel_level",u"gear",b"gear",u"get",b"get",u"lights",b"lights",u"rear_pto",b"rear_pto",u"rpms",b"rpms",u"speed_control",b"speed_control",u"variant",b"variant",u"wheel_cal",b"wheel_cal"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"get",b"get"]) -> typing_extensions___Literal["gear","lights","speed_control","rpms","front_pto","rear_pto","variant","wheel_cal","fuel_level","engine_temp"]: ...
type___GetRequest = GetRequest

class GetReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def gear(self) -> type___GearState: ...

    @property
    def lights(self) -> type___LightsState: ...

    @property
    def speed_control(self) -> type___SpeedControlState: ...

    @property
    def rpms(self) -> type___EngineRpmState: ...

    @property
    def front_pto(self) -> type___PtoState: ...

    @property
    def rear_pto(self) -> type___PtoState: ...

    @property
    def variant(self) -> type___TractorVariantState: ...

    @property
    def wheel_cal(self) -> type___WheelAngleCalState: ...

    @property
    def fuel_level(self) -> type___FuelLevel: ...

    @property
    def engine_temp(self) -> type___EngineTemp: ...

    def __init__(self,
        *,
        gear : typing___Optional[type___GearState] = None,
        lights : typing___Optional[type___LightsState] = None,
        speed_control : typing___Optional[type___SpeedControlState] = None,
        rpms : typing___Optional[type___EngineRpmState] = None,
        front_pto : typing___Optional[type___PtoState] = None,
        rear_pto : typing___Optional[type___PtoState] = None,
        variant : typing___Optional[type___TractorVariantState] = None,
        wheel_cal : typing___Optional[type___WheelAngleCalState] = None,
        fuel_level : typing___Optional[type___FuelLevel] = None,
        engine_temp : typing___Optional[type___EngineTemp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"engine_temp",b"engine_temp",u"front_pto",b"front_pto",u"fuel_level",b"fuel_level",u"gear",b"gear",u"get",b"get",u"lights",b"lights",u"rear_pto",b"rear_pto",u"rpms",b"rpms",u"speed_control",b"speed_control",u"variant",b"variant",u"wheel_cal",b"wheel_cal"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"engine_temp",b"engine_temp",u"front_pto",b"front_pto",u"fuel_level",b"fuel_level",u"gear",b"gear",u"get",b"get",u"lights",b"lights",u"rear_pto",b"rear_pto",u"rpms",b"rpms",u"speed_control",b"speed_control",u"variant",b"variant",u"wheel_cal",b"wheel_cal"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"get",b"get"]) -> typing_extensions___Literal["gear","lights","speed_control","rpms","front_pto","rear_pto","variant","wheel_cal","fuel_level","engine_temp"]: ...
type___GetReply = GetReply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def set(self) -> type___SetRequest: ...

    @property
    def get(self) -> type___GetRequest: ...

    def __init__(self,
        *,
        set : typing___Optional[type___SetRequest] = None,
        get : typing___Optional[type___GetRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"get",b"get",u"request",b"request",u"set",b"set"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"get",b"get",u"request",b"request",u"set",b"set"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["set","get"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def set(self) -> type___SetReply: ...

    @property
    def get(self) -> type___GetReply: ...

    def __init__(self,
        *,
        set : typing___Optional[type___SetReply] = None,
        get : typing___Optional[type___GetReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"get",b"get",u"reply",b"reply",u"set",b"set"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"get",b"get",u"reply",b"reply",u"set",b"set"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["set","get"]: ...
type___Reply = Reply

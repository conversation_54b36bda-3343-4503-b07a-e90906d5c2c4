/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_CRUISE_CRUISE_PB_H_INCLUDED
#define PB_CRUISE_CRUISE_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _cruise_TractorVariantType {
    cruise_TractorVariantType_TV_UNKNOWN = 0,
    cruise_TractorVariantType_TV_JD_6LH = 1,
    cruise_TractorVariantType_TV_JD_6LHM = 2,
    cruise_TractorVariantType_TV_JD_6PRO = 3,
    cruise_TractorVariantType_TV_JD_7RH = 4,
    cruise_TractorVariantType_TV_JD_8PRO = 5
} cruise_TractorVariantType;

/* Struct definitions */
typedef struct _cruise_Status_Request {
    char dummy_field;
} cruise_Status_Request;

typedef struct _cruise_Enable_Reply {
    bool enabled;
} cruise_Enable_Reply;

typedef struct _cruise_Enable_Request {
    bool enabled;
} cruise_Enable_Request;

typedef struct _cruise_Gear_Reply {
    bool success;
} cruise_Gear_Reply;

typedef struct _cruise_Gear_Request {
    int32_t gear;
} cruise_Gear_Request;

typedef struct _cruise_Status_Reply {
    bool enabled;
    bool unavailable;
    int32_t speed_ticks;
    int32_t speed_lever_position;
    bool speed_lever_s1;
    bool speed_lever_s2;
} cruise_Status_Reply;

typedef struct _cruise_Throttle_Reply {
    bool success;
} cruise_Throttle_Reply;

typedef struct _cruise_Throttle_Request {
    int32_t change;
} cruise_Throttle_Request;

typedef struct _cruise_Variant_Reply {
    cruise_TractorVariantType variant;
} cruise_Variant_Reply;

typedef struct _cruise_Variant_Request {
    cruise_TractorVariantType variant;
} cruise_Variant_Request;

typedef struct _cruise_Reply {
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        cruise_Throttle_Reply throttle;
        cruise_Status_Reply status;
        cruise_Enable_Reply enable;
        cruise_Gear_Reply gear;
        cruise_Variant_Reply variant;
    } reply;
} cruise_Reply;

typedef struct _cruise_Request {
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        cruise_Throttle_Request throttle;
        cruise_Status_Request status;
        cruise_Enable_Request enable;
        cruise_Gear_Request gear;
        cruise_Variant_Request variant;
    } request;
} cruise_Request;


/* Helper constants for enums */
#define _cruise_TractorVariantType_MIN cruise_TractorVariantType_TV_UNKNOWN
#define _cruise_TractorVariantType_MAX cruise_TractorVariantType_TV_JD_8PRO
#define _cruise_TractorVariantType_ARRAYSIZE ((cruise_TractorVariantType)(cruise_TractorVariantType_TV_JD_8PRO+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define cruise_Throttle_Request_init_default     {0}
#define cruise_Throttle_Reply_init_default       {0}
#define cruise_Status_Request_init_default       {0}
#define cruise_Status_Reply_init_default         {0, 0, 0, 0, 0, 0}
#define cruise_Enable_Request_init_default       {0}
#define cruise_Enable_Reply_init_default         {0}
#define cruise_Gear_Request_init_default         {0}
#define cruise_Gear_Reply_init_default           {0}
#define cruise_Variant_Request_init_default      {_cruise_TractorVariantType_MIN}
#define cruise_Variant_Reply_init_default        {_cruise_TractorVariantType_MIN}
#define cruise_Request_init_default              {0, {diagnostic_Ping_init_default}}
#define cruise_Reply_init_default                {0, {diagnostic_Pong_init_default}}
#define cruise_Throttle_Request_init_zero        {0}
#define cruise_Throttle_Reply_init_zero          {0}
#define cruise_Status_Request_init_zero          {0}
#define cruise_Status_Reply_init_zero            {0, 0, 0, 0, 0, 0}
#define cruise_Enable_Request_init_zero          {0}
#define cruise_Enable_Reply_init_zero            {0}
#define cruise_Gear_Request_init_zero            {0}
#define cruise_Gear_Reply_init_zero              {0}
#define cruise_Variant_Request_init_zero         {_cruise_TractorVariantType_MIN}
#define cruise_Variant_Reply_init_zero           {_cruise_TractorVariantType_MIN}
#define cruise_Request_init_zero                 {0, {diagnostic_Ping_init_zero}}
#define cruise_Reply_init_zero                   {0, {diagnostic_Pong_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define cruise_Enable_Reply_enabled_tag          1
#define cruise_Enable_Request_enabled_tag        1
#define cruise_Gear_Reply_success_tag            1
#define cruise_Gear_Request_gear_tag             1
#define cruise_Status_Reply_enabled_tag          1
#define cruise_Status_Reply_unavailable_tag      2
#define cruise_Status_Reply_speed_ticks_tag      3
#define cruise_Status_Reply_speed_lever_position_tag 4
#define cruise_Status_Reply_speed_lever_s1_tag   5
#define cruise_Status_Reply_speed_lever_s2_tag   6
#define cruise_Throttle_Reply_success_tag        1
#define cruise_Throttle_Request_change_tag       1
#define cruise_Variant_Reply_variant_tag         1
#define cruise_Variant_Request_variant_tag       1
#define cruise_Reply_pong_tag                    2
#define cruise_Reply_throttle_tag                3
#define cruise_Reply_status_tag                  4
#define cruise_Reply_enable_tag                  5
#define cruise_Reply_gear_tag                    6
#define cruise_Reply_variant_tag                 7
#define cruise_Request_ping_tag                  2
#define cruise_Request_throttle_tag              3
#define cruise_Request_status_tag                4
#define cruise_Request_enable_tag                5
#define cruise_Request_gear_tag                  6
#define cruise_Request_variant_tag               7

/* Struct field encoding specification for nanopb */
#define cruise_Throttle_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    change,            1)
#define cruise_Throttle_Request_CALLBACK NULL
#define cruise_Throttle_Request_DEFAULT NULL

#define cruise_Throttle_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     success,           1)
#define cruise_Throttle_Reply_CALLBACK NULL
#define cruise_Throttle_Reply_DEFAULT NULL

#define cruise_Status_Request_FIELDLIST(X, a) \

#define cruise_Status_Request_CALLBACK NULL
#define cruise_Status_Request_DEFAULT NULL

#define cruise_Status_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     enabled,           1) \
X(a, STATIC,   SINGULAR, BOOL,     unavailable,       2) \
X(a, STATIC,   SINGULAR, INT32,    speed_ticks,       3) \
X(a, STATIC,   SINGULAR, INT32,    speed_lever_position,   4) \
X(a, STATIC,   SINGULAR, BOOL,     speed_lever_s1,    5) \
X(a, STATIC,   SINGULAR, BOOL,     speed_lever_s2,    6)
#define cruise_Status_Reply_CALLBACK NULL
#define cruise_Status_Reply_DEFAULT NULL

#define cruise_Enable_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     enabled,           1)
#define cruise_Enable_Request_CALLBACK NULL
#define cruise_Enable_Request_DEFAULT NULL

#define cruise_Enable_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     enabled,           1)
#define cruise_Enable_Reply_CALLBACK NULL
#define cruise_Enable_Reply_DEFAULT NULL

#define cruise_Gear_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    gear,              1)
#define cruise_Gear_Request_CALLBACK NULL
#define cruise_Gear_Request_DEFAULT NULL

#define cruise_Gear_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     success,           1)
#define cruise_Gear_Reply_CALLBACK NULL
#define cruise_Gear_Reply_DEFAULT NULL

#define cruise_Variant_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    variant,           1)
#define cruise_Variant_Request_CALLBACK NULL
#define cruise_Variant_Request_DEFAULT NULL

#define cruise_Variant_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    variant,           1)
#define cruise_Variant_Reply_CALLBACK NULL
#define cruise_Variant_Reply_DEFAULT NULL

#define cruise_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,throttle,request.throttle),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,status,request.status),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,enable,request.enable),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,gear,request.gear),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,variant,request.variant),   7)
#define cruise_Request_CALLBACK NULL
#define cruise_Request_DEFAULT NULL
#define cruise_Request_request_ping_MSGTYPE diagnostic_Ping
#define cruise_Request_request_throttle_MSGTYPE cruise_Throttle_Request
#define cruise_Request_request_status_MSGTYPE cruise_Status_Request
#define cruise_Request_request_enable_MSGTYPE cruise_Enable_Request
#define cruise_Request_request_gear_MSGTYPE cruise_Gear_Request
#define cruise_Request_request_variant_MSGTYPE cruise_Variant_Request

#define cruise_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,throttle,reply.throttle),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,status,reply.status),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,enable,reply.enable),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,gear,reply.gear),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,variant,reply.variant),   7)
#define cruise_Reply_CALLBACK NULL
#define cruise_Reply_DEFAULT NULL
#define cruise_Reply_reply_pong_MSGTYPE diagnostic_Pong
#define cruise_Reply_reply_throttle_MSGTYPE cruise_Throttle_Reply
#define cruise_Reply_reply_status_MSGTYPE cruise_Status_Reply
#define cruise_Reply_reply_enable_MSGTYPE cruise_Enable_Reply
#define cruise_Reply_reply_gear_MSGTYPE cruise_Gear_Reply
#define cruise_Reply_reply_variant_MSGTYPE cruise_Variant_Reply

extern const pb_msgdesc_t cruise_Throttle_Request_msg;
extern const pb_msgdesc_t cruise_Throttle_Reply_msg;
extern const pb_msgdesc_t cruise_Status_Request_msg;
extern const pb_msgdesc_t cruise_Status_Reply_msg;
extern const pb_msgdesc_t cruise_Enable_Request_msg;
extern const pb_msgdesc_t cruise_Enable_Reply_msg;
extern const pb_msgdesc_t cruise_Gear_Request_msg;
extern const pb_msgdesc_t cruise_Gear_Reply_msg;
extern const pb_msgdesc_t cruise_Variant_Request_msg;
extern const pb_msgdesc_t cruise_Variant_Reply_msg;
extern const pb_msgdesc_t cruise_Request_msg;
extern const pb_msgdesc_t cruise_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define cruise_Throttle_Request_fields &cruise_Throttle_Request_msg
#define cruise_Throttle_Reply_fields &cruise_Throttle_Reply_msg
#define cruise_Status_Request_fields &cruise_Status_Request_msg
#define cruise_Status_Reply_fields &cruise_Status_Reply_msg
#define cruise_Enable_Request_fields &cruise_Enable_Request_msg
#define cruise_Enable_Reply_fields &cruise_Enable_Reply_msg
#define cruise_Gear_Request_fields &cruise_Gear_Request_msg
#define cruise_Gear_Reply_fields &cruise_Gear_Reply_msg
#define cruise_Variant_Request_fields &cruise_Variant_Request_msg
#define cruise_Variant_Reply_fields &cruise_Variant_Reply_msg
#define cruise_Request_fields &cruise_Request_msg
#define cruise_Reply_fields &cruise_Reply_msg

/* Maximum encoded size of messages (where known) */
#define cruise_Throttle_Request_size             11
#define cruise_Throttle_Reply_size               2
#define cruise_Status_Request_size               0
#define cruise_Status_Reply_size                 30
#define cruise_Enable_Request_size               2
#define cruise_Enable_Reply_size                 2
#define cruise_Gear_Request_size                 11
#define cruise_Gear_Reply_size                   2
#define cruise_Variant_Request_size              2
#define cruise_Variant_Reply_size                2
#if defined(diagnostic_Ping_size)
typedef union cruise_Request_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f0[13];} cruise_Request_request_size_union;
#define cruise_Request_size                      (0 + sizeof(cruise_Request_request_size_union))
#endif
#if defined(diagnostic_Pong_size)
typedef union cruise_Reply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f0[32];} cruise_Reply_reply_size_union;
#define cruise_Reply_size                        (0 + sizeof(cruise_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

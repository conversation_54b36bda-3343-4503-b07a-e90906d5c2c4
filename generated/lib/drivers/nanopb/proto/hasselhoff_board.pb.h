/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_HASSELHOFF_BOARD_HASSELHOFF_BOARD_PB_H_INCLUDED
#define PB_HASSELHOFF_BOARD_HASSELHOFF_BOARD_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"
#include "generated/lib/drivers/nanopb/proto/version.pb.h"
#include "generated/lib/drivers/nanopb/proto/time.pb.h"
#include "generated/lib/drivers/nanopb/proto/carbon_tractor.pb.h"
#include "generated/lib/drivers/nanopb/proto/ots_tractor.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _hasselhoff_board_Reply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        time_Reply time;
        version_Version_Reply version;
        ots_tractor_Reply ots_tractor;
        carbon_tractor_Reply carbon_tractor;
    } reply;
} hasselhoff_board_Reply;

typedef struct _hasselhoff_board_Request {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        time_Request time;
        version_Version_Request version;
        version_Reset_Request reset;
        ots_tractor_Request ots_tractor;
        carbon_tractor_Request carbon_tractor;
    } request;
} hasselhoff_board_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define hasselhoff_board_Reply_init_default      {false, request_RequestHeader_init_default, 0, {diagnostic_Pong_init_default}}
#define hasselhoff_board_Request_init_default    {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define hasselhoff_board_Reply_init_zero         {false, request_RequestHeader_init_zero, 0, {diagnostic_Pong_init_zero}}
#define hasselhoff_board_Request_init_zero       {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define hasselhoff_board_Reply_header_tag        1
#define hasselhoff_board_Reply_pong_tag          2
#define hasselhoff_board_Reply_time_tag          3
#define hasselhoff_board_Reply_version_tag       4
#define hasselhoff_board_Reply_ots_tractor_tag   5
#define hasselhoff_board_Reply_carbon_tractor_tag 6
#define hasselhoff_board_Request_header_tag      1
#define hasselhoff_board_Request_ping_tag        2
#define hasselhoff_board_Request_time_tag        3
#define hasselhoff_board_Request_version_tag     4
#define hasselhoff_board_Request_reset_tag       5
#define hasselhoff_board_Request_ots_tractor_tag 6
#define hasselhoff_board_Request_carbon_tractor_tag 7

/* Struct field encoding specification for nanopb */
#define hasselhoff_board_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,time,reply.time),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,version,reply.version),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ots_tractor,reply.ots_tractor),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,carbon_tractor,reply.carbon_tractor),   6)
#define hasselhoff_board_Reply_CALLBACK NULL
#define hasselhoff_board_Reply_DEFAULT NULL
#define hasselhoff_board_Reply_header_MSGTYPE request_RequestHeader
#define hasselhoff_board_Reply_reply_pong_MSGTYPE diagnostic_Pong
#define hasselhoff_board_Reply_reply_time_MSGTYPE time_Reply
#define hasselhoff_board_Reply_reply_version_MSGTYPE version_Version_Reply
#define hasselhoff_board_Reply_reply_ots_tractor_MSGTYPE ots_tractor_Reply
#define hasselhoff_board_Reply_reply_carbon_tractor_MSGTYPE carbon_tractor_Reply

#define hasselhoff_board_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,time,request.time),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,version,request.version),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ots_tractor,request.ots_tractor),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,carbon_tractor,request.carbon_tractor),   7)
#define hasselhoff_board_Request_CALLBACK NULL
#define hasselhoff_board_Request_DEFAULT NULL
#define hasselhoff_board_Request_header_MSGTYPE request_RequestHeader
#define hasselhoff_board_Request_request_ping_MSGTYPE diagnostic_Ping
#define hasselhoff_board_Request_request_time_MSGTYPE time_Request
#define hasselhoff_board_Request_request_version_MSGTYPE version_Version_Request
#define hasselhoff_board_Request_request_reset_MSGTYPE version_Reset_Request
#define hasselhoff_board_Request_request_ots_tractor_MSGTYPE ots_tractor_Request
#define hasselhoff_board_Request_request_carbon_tractor_MSGTYPE carbon_tractor_Request

extern const pb_msgdesc_t hasselhoff_board_Reply_msg;
extern const pb_msgdesc_t hasselhoff_board_Request_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define hasselhoff_board_Reply_fields &hasselhoff_board_Reply_msg
#define hasselhoff_board_Request_fields &hasselhoff_board_Request_msg

/* Maximum encoded size of messages (where known) */
#if defined(request_RequestHeader_size) && defined(diagnostic_Pong_size) && defined(time_Reply_size) && defined(version_Version_Reply_size) && defined(ots_tractor_Reply_size) && defined(carbon_tractor_Reply_size)
typedef union hasselhoff_board_Reply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f3[(6 + time_Reply_size)]; char f4[(6 + version_Version_Reply_size)]; char f5[(6 + ots_tractor_Reply_size)]; char f6[(6 + carbon_tractor_Reply_size)];} hasselhoff_board_Reply_reply_size_union;
#define hasselhoff_board_Reply_size              (6 + request_RequestHeader_size + sizeof(hasselhoff_board_Reply_reply_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size) && defined(time_Request_size) && defined(version_Version_Request_size) && defined(version_Reset_Request_size) && defined(ots_tractor_Request_size) && defined(carbon_tractor_Request_size)
typedef union hasselhoff_board_Request_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f3[(6 + time_Request_size)]; char f4[(6 + version_Version_Request_size)]; char f5[(6 + version_Reset_Request_size)]; char f6[(6 + ots_tractor_Request_size)]; char f7[(6 + carbon_tractor_Request_size)];} hasselhoff_board_Request_request_size_union;
#define hasselhoff_board_Request_size            (6 + request_RequestHeader_size + sizeof(hasselhoff_board_Request_request_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

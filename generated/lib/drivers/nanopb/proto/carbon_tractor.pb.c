/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "carbon_tractor.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(carbon_tractor_Empty, carbon_tractor_Empty, AUTO)


PB_BIND(carbon_tractor_HHState, carbon_tractor_HHState, AUTO)


PB_BIND(carbon_tractor_BrakeState, carbon_tractor_BrakeState, AUTO)


PB_BIND(carbon_tractor_SafetySensorsState, carbon_tractor_SafetySensorsState, AUTO)


PB_BIND(carbon_tractor_SafetySensorBypassState, carbon_tractor_SafetySensorBypassState, AUTO)


PB_BIND(carbon_tractor_TractorStatus, carbon_tractor_TractorStatus, 2)


PB_BIND(carbon_tractor_PetRequest, carbon_tractor_PetRequest, AUTO)


PB_BIND(carbon_tractor_SteeringState, carbon_tractor_SteeringState, AUTO)


PB_BIND(carbon_tractor_SteeringCfgState, carbon_tractor_SteeringCfgState, AUTO)


PB_BIND(carbon_tractor_PetLossState, carbon_tractor_PetLossState, AUTO)


PB_BIND(carbon_tractor_SpeedLimitState, carbon_tractor_SpeedLimitState, AUTO)


PB_BIND(carbon_tractor_SetRequest, carbon_tractor_SetRequest, AUTO)


PB_BIND(carbon_tractor_SetReply, carbon_tractor_SetReply, AUTO)


PB_BIND(carbon_tractor_GetRequest, carbon_tractor_GetRequest, AUTO)


PB_BIND(carbon_tractor_GetReply, carbon_tractor_GetReply, 2)


PB_BIND(carbon_tractor_DebugRequest, carbon_tractor_DebugRequest, AUTO)


PB_BIND(carbon_tractor_DebugReply, carbon_tractor_DebugReply, AUTO)


PB_BIND(carbon_tractor_Request, carbon_tractor_Request, AUTO)


PB_BIND(carbon_tractor_Reply, carbon_tractor_Reply, 2)





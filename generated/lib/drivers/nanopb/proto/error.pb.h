/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_ERROR_ERROR_PB_H_INCLUDED
#define PB_ERROR_ERROR_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _error_Error {
    uint32_t code;
} error_Error;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define error_Error_init_default                 {0}
#define error_Error_init_zero                    {0}

/* Field tags (for use in manual encoding/decoding) */
#define error_Error_code_tag                     1

/* Struct field encoding specification for nanopb */
#define error_Error_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   code,              1)
#define error_Error_CALLBACK NULL
#define error_Error_DEFAULT NULL

extern const pb_msgdesc_t error_Error_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define error_Error_fields &error_Error_msg

/* Maximum encoded size of messages (where known) */
#define error_Error_size                         6

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "drive_solenoids.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(drive_solenoids_Drive_Request, drive_solenoids_Drive_Request, AUTO)


PB_BIND(drive_solenoids_Drive_Reply, drive_solenoids_Drive_Reply, AUTO)


PB_BIND(drive_solenoids_Turn_Request, drive_solenoids_Turn_Request, AUTO)


PB_BIND(drive_solenoids_Turn_Reply, drive_solenoids_Turn_Reply, AUTO)


PB_BIND(drive_solenoids_Request, drive_solenoids_Request, AUTO)


PB_BIND(drive_solenoids_Reply, drive_solenoids_Reply, AUTO)






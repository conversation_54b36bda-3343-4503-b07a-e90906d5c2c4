# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/benjamin_gps_board.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2
from generated.lib.drivers.nanopb.proto import diagnostic_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2
from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import gps_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_gps__pb2
from generated.lib.drivers.nanopb.proto import heading_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_heading__pb2
from generated.lib.drivers.nanopb.proto import hwinfo_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2
from generated.lib.drivers.nanopb.proto import request_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/benjamin_gps_board.proto',
  package='benjamin_gps_board',
  syntax='proto3',
  serialized_options=b'Z\031nanopb/benjamin_gps_board',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n;generated/lib/drivers/nanopb/proto/benjamin_gps_board.proto\x12\x12\x62\x65njamin_gps_board\x1a,generated/lib/drivers/nanopb/proto/ack.proto\x1a\x33generated/lib/drivers/nanopb/proto/diagnostic.proto\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/gps.proto\x1a\x30generated/lib/drivers/nanopb/proto/heading.proto\x1a/generated/lib/drivers/nanopb/proto/hwinfo.proto\x1a\x30generated/lib/drivers/nanopb/proto/request.proto\"&\n\x11PtpConfig_Request\x12\x11\n\tis_master\x18\x01 \x01(\x08\"\xf1\x01\n\x05Reply\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04pong\x18\x02 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12\x19\n\x03gps\x18\x03 \x01(\x0b\x32\n.gps.ReplyH\x00\x12!\n\x07heading\x18\x04 \x01(\x0b\x32\x0e.heading.ReplyH\x00\x12\x1f\n\x06hwinfo\x18\x05 \x01(\x0b\x32\r.hwinfo.ReplyH\x00\x12\x1d\n\x05\x65rror\x18\x06 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x07 \x01(\x0b\x32\x08.ack.AckH\x00\x42\x07\n\x05reply\"\x80\x02\n\x07Request\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12\x1b\n\x03gps\x18\x03 \x01(\x0b\x32\x0c.gps.RequestH\x00\x12#\n\x07heading\x18\x04 \x01(\x0b\x32\x10.heading.RequestH\x00\x12!\n\x06hwinfo\x18\x05 \x01(\x0b\x32\x0f.hwinfo.RequestH\x00\x12;\n\nptp_config\x18\x06 \x01(\x0b\x32%.benjamin_gps_board.PtpConfig_RequestH\x00\x42\t\n\x07requestB\x1bZ\x19nanopb/benjamin_gps_boardb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_gps__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_heading__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2.DESCRIPTOR,])




_PTPCONFIG_REQUEST = _descriptor.Descriptor(
  name='PtpConfig_Request',
  full_name='benjamin_gps_board.PtpConfig_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='is_master', full_name='benjamin_gps_board.PtpConfig_Request.is_master', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=425,
  serialized_end=463,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='benjamin_gps_board.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='benjamin_gps_board.Reply.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pong', full_name='benjamin_gps_board.Reply.pong', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps', full_name='benjamin_gps_board.Reply.gps', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading', full_name='benjamin_gps_board.Reply.heading', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hwinfo', full_name='benjamin_gps_board.Reply.hwinfo', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error', full_name='benjamin_gps_board.Reply.error', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='benjamin_gps_board.Reply.ack', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='benjamin_gps_board.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=466,
  serialized_end=707,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='benjamin_gps_board.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='benjamin_gps_board.Request.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ping', full_name='benjamin_gps_board.Request.ping', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps', full_name='benjamin_gps_board.Request.gps', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading', full_name='benjamin_gps_board.Request.heading', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hwinfo', full_name='benjamin_gps_board.Request.hwinfo', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ptp_config', full_name='benjamin_gps_board.Request.ptp_config', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='benjamin_gps_board.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=710,
  serialized_end=966,
)

_REPLY.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_REPLY.fields_by_name['gps'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_gps__pb2._REPLY
_REPLY.fields_by_name['heading'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_heading__pb2._REPLY
_REPLY.fields_by_name['hwinfo'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2._REPLY
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pong'])
_REPLY.fields_by_name['pong'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['gps'])
_REPLY.fields_by_name['gps'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['heading'])
_REPLY.fields_by_name['heading'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['hwinfo'])
_REPLY.fields_by_name['hwinfo'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REQUEST.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_REQUEST.fields_by_name['gps'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_gps__pb2._REQUEST
_REQUEST.fields_by_name['heading'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_heading__pb2._REQUEST
_REQUEST.fields_by_name['hwinfo'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_hwinfo__pb2._REQUEST
_REQUEST.fields_by_name['ptp_config'].message_type = _PTPCONFIG_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ping'])
_REQUEST.fields_by_name['ping'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['gps'])
_REQUEST.fields_by_name['gps'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['heading'])
_REQUEST.fields_by_name['heading'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['hwinfo'])
_REQUEST.fields_by_name['hwinfo'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ptp_config'])
_REQUEST.fields_by_name['ptp_config'].containing_oneof = _REQUEST.oneofs_by_name['request']
DESCRIPTOR.message_types_by_name['PtpConfig_Request'] = _PTPCONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PtpConfig_Request = _reflection.GeneratedProtocolMessageType('PtpConfig_Request', (_message.Message,), {
  'DESCRIPTOR' : _PTPCONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.benjamin_gps_board_pb2'
  # @@protoc_insertion_point(class_scope:benjamin_gps_board.PtpConfig_Request)
  })
_sym_db.RegisterMessage(PtpConfig_Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.benjamin_gps_board_pb2'
  # @@protoc_insertion_point(class_scope:benjamin_gps_board.Reply)
  })
_sym_db.RegisterMessage(Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.benjamin_gps_board_pb2'
  # @@protoc_insertion_point(class_scope:benjamin_gps_board.Request)
  })
_sym_db.RegisterMessage(Request)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

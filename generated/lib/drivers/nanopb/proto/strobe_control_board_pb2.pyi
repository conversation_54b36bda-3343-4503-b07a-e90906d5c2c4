"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.camera_power_control_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___camera_power_control_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___camera_power_control_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.diagnostic_pb2 import (
    Ping as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping,
    Pong as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong,
)

from generated.lib.drivers.nanopb.proto.request_pb2 import (
    RequestHeader as generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader,
)

from generated.lib.drivers.nanopb.proto.strobe_control_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___strobe_control_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___strobe_control_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.time_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___time_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___time_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.version_pb2 import (
    Reset_Request as generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request,
    Version_Reply as generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply,
    Version_Request as generated___lib___drivers___nanopb___proto___version_pb2___Version_Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def strobe_control(self) -> generated___lib___drivers___nanopb___proto___strobe_control_pb2___Reply: ...

    @property
    def camera_power_control(self) -> generated___lib___drivers___nanopb___proto___camera_power_control_pb2___Reply: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply: ...

    @property
    def time(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Reply: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        strobe_control : typing___Optional[generated___lib___drivers___nanopb___proto___strobe_control_pb2___Reply] = None,
        camera_power_control : typing___Optional[generated___lib___drivers___nanopb___proto___camera_power_control_pb2___Reply] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply] = None,
        time : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"camera_power_control",b"camera_power_control",u"header",b"header",u"pong",b"pong",u"reply",b"reply",u"strobe_control",b"strobe_control",u"time",b"time",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"camera_power_control",b"camera_power_control",u"header",b"header",u"pong",b"pong",u"reply",b"reply",u"strobe_control",b"strobe_control",u"time",b"time",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["pong","strobe_control","camera_power_control","version","time"]: ...
type___Reply = Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def strobe_control(self) -> generated___lib___drivers___nanopb___proto___strobe_control_pb2___Request: ...

    @property
    def camera_power_control(self) -> generated___lib___drivers___nanopb___proto___camera_power_control_pb2___Request: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Request: ...

    @property
    def reset(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request: ...

    @property
    def time(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Request: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        strobe_control : typing___Optional[generated___lib___drivers___nanopb___proto___strobe_control_pb2___Request] = None,
        camera_power_control : typing___Optional[generated___lib___drivers___nanopb___proto___camera_power_control_pb2___Request] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Request] = None,
        reset : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request] = None,
        time : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"camera_power_control",b"camera_power_control",u"header",b"header",u"ping",b"ping",u"request",b"request",u"reset",b"reset",u"strobe_control",b"strobe_control",u"time",b"time",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"camera_power_control",b"camera_power_control",u"header",b"header",u"ping",b"ping",u"request",b"request",u"reset",b"reset",u"strobe_control",b"strobe_control",u"time",b"time",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","strobe_control","camera_power_control","version","reset","time"]: ...
type___Request = Request

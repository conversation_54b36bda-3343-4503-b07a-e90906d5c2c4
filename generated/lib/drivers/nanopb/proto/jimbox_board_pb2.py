# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/jimbox_board.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import diagnostic_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2
from generated.lib.drivers.nanopb.proto import request_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2
from generated.lib.drivers.nanopb.proto import cruise_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_cruise__pb2
from generated.lib.drivers.nanopb.proto import version_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2
from generated.lib.drivers.nanopb.proto import time_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/jimbox_board.proto',
  package='jimbox_board',
  syntax='proto3',
  serialized_options=b'Z\023nanopb/jimbox_board',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n5generated/lib/drivers/nanopb/proto/jimbox_board.proto\x12\x0cjimbox_board\x1a\x33generated/lib/drivers/nanopb/proto/diagnostic.proto\x1a\x30generated/lib/drivers/nanopb/proto/request.proto\x1a/generated/lib/drivers/nanopb/proto/cruise.proto\x1a\x30generated/lib/drivers/nanopb/proto/version.proto\x1a-generated/lib/drivers/nanopb/proto/time.proto\"\xc3\x01\n\x05Reply\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04pong\x18\x02 \x01(\x0b\x32\x10.diagnostic.PongH\x00\x12\x1f\n\x06\x63ruise\x18\x03 \x01(\x0b\x32\r.cruise.ReplyH\x00\x12)\n\x07version\x18\x04 \x01(\x0b\x32\x16.version.Version_ReplyH\x00\x12\x1b\n\x04time\x18\x05 \x01(\x0b\x32\x0b.time.ReplyH\x00\x42\x07\n\x05reply\"\xf6\x01\n\x07Request\x12&\n\x06header\x18\x01 \x01(\x0b\x32\x16.request.RequestHeader\x12 \n\x04ping\x18\x02 \x01(\x0b\x32\x10.diagnostic.PingH\x00\x12!\n\x06\x63ruise\x18\x03 \x01(\x0b\x32\x0f.cruise.RequestH\x00\x12+\n\x07version\x18\x04 \x01(\x0b\x32\x18.version.Version_RequestH\x00\x12\'\n\x05reset\x18\x05 \x01(\x0b\x32\x16.version.Reset_RequestH\x00\x12\x1d\n\x04time\x18\x06 \x01(\x0b\x32\r.time.RequestH\x00\x42\t\n\x07requestB\x15Z\x13nanopb/jimbox_boardb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_cruise__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2.DESCRIPTOR,])




_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='jimbox_board.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='jimbox_board.Reply.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pong', full_name='jimbox_board.Reply.pong', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cruise', full_name='jimbox_board.Reply.cruise', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='jimbox_board.Reply.version', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time', full_name='jimbox_board.Reply.time', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='jimbox_board.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=321,
  serialized_end=516,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='jimbox_board.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='header', full_name='jimbox_board.Request.header', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ping', full_name='jimbox_board.Request.ping', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cruise', full_name='jimbox_board.Request.cruise', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='jimbox_board.Request.version', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reset', full_name='jimbox_board.Request.reset', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time', full_name='jimbox_board.Request.time', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='jimbox_board.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=519,
  serialized_end=765,
)

_REPLY.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REPLY.fields_by_name['pong'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PONG
_REPLY.fields_by_name['cruise'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_cruise__pb2._REPLY
_REPLY.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REPLY
_REPLY.fields_by_name['time'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['pong'])
_REPLY.fields_by_name['pong'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['cruise'])
_REPLY.fields_by_name['cruise'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['version'])
_REPLY.fields_by_name['version'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['time'])
_REPLY.fields_by_name['time'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REQUEST.fields_by_name['header'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_request__pb2._REQUESTHEADER
_REQUEST.fields_by_name['ping'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_diagnostic__pb2._PING
_REQUEST.fields_by_name['cruise'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_cruise__pb2._REQUEST
_REQUEST.fields_by_name['version'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._VERSION_REQUEST
_REQUEST.fields_by_name['reset'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_version__pb2._RESET_REQUEST
_REQUEST.fields_by_name['time'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_time__pb2._REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ping'])
_REQUEST.fields_by_name['ping'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['cruise'])
_REQUEST.fields_by_name['cruise'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['version'])
_REQUEST.fields_by_name['version'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['reset'])
_REQUEST.fields_by_name['reset'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['time'])
_REQUEST.fields_by_name['time'].containing_oneof = _REQUEST.oneofs_by_name['request']
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.jimbox_board_pb2'
  # @@protoc_insertion_point(class_scope:jimbox_board.Reply)
  })
_sym_db.RegisterMessage(Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.jimbox_board_pb2'
  # @@protoc_insertion_point(class_scope:jimbox_board.Request)
  })
_sym_db.RegisterMessage(Request)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

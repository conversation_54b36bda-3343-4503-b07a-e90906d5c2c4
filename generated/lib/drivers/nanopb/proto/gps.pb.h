/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_GPS_GPS_PB_H_INCLUDED
#define PB_GPS_GPS_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _gps_CarrierPhaseSoln {
    gps_CarrierPhaseSoln_NONE = 0,
    gps_CarrierPhaseSoln_FLOATING = 1,
    gps_CarrierPhaseSoln_FIXED = 2
} gps_CarrierPhaseSoln;

/* Struct definitions */
typedef struct _gps_GetLastGga_Request {
    char dummy_field;
} gps_GetLastGga_Request;

typedef struct _gps_HeadingCorrection_Reply {
    char dummy_field;
} gps_HeadingCorrection_Reply;

typedef struct _gps_Position_Request {
    char dummy_field;
} gps_Position_Request;

typedef struct _gps_Rtcm_Reply {
    char dummy_field;
} gps_Rtcm_Reply;

typedef struct _gps_Spartn_Reply {
    char dummy_field;
} gps_Spartn_Reply;

typedef struct _gps_GetLastGga_Reply {
    char raw_sentence[100];
} gps_GetLastGga_Reply;

typedef struct _gps_HeadingCorrection_Request {
    float heading_offset;
} gps_HeadingCorrection_Request;

typedef PB_BYTES_ARRAY_T(64) gps_Rtcm_Request_data_t;
typedef struct _gps_Rtcm_Request {
    gps_Rtcm_Request_data_t data;
    bool end;
} gps_Rtcm_Request;

typedef PB_BYTES_ARRAY_T(64) gps_Spartn_Request_data_t;
typedef struct _gps_Spartn_Request {
    gps_Spartn_Request_data_t data;
    bool end;
} gps_Spartn_Request;

typedef struct _gps_ValueWithAccuracy {
    double value;
    double accuracy;
} gps_ValueWithAccuracy;

typedef struct _gps_DualGpsData {
    bool gnss_valid;
    bool diff_corrections;
    bool is_moving_base;
    gps_CarrierPhaseSoln carrier_phase;
    uint64_t timestamp_ms;
    pb_size_t which__north;
    union {
        gps_ValueWithAccuracy north;
    } _north;
    pb_size_t which__east;
    union {
        gps_ValueWithAccuracy east;
    } _east;
    pb_size_t which__down;
    union {
        gps_ValueWithAccuracy down;
    } _down;
    pb_size_t which__length;
    union {
        gps_ValueWithAccuracy length;
    } _length;
    pb_size_t which__heading;
    union {
        gps_ValueWithAccuracy heading;
    } _heading;
} gps_DualGpsData;

typedef struct _gps_Request {
    pb_size_t which_request;
    union {
        gps_Position_Request position;
        gps_Spartn_Request spartn;
        gps_HeadingCorrection_Request heading_correction;
        gps_Rtcm_Request rtcm;
        gps_GetLastGga_Request gga;
    } request;
} gps_Request;

typedef struct _gps_Position_Reply {
    bool have_fix;
    double latitude;
    double longitude;
    int32_t num_sats;
    float hdop;
    uint64_t timestamp_ms;
    int32_t height_mm;
    bool have_approx_fix;
    int32_t fix_type;
    int32_t fix_flags;
    pb_size_t which__dual;
    union {
        gps_DualGpsData dual;
    } _dual;
} gps_Position_Reply;

typedef struct _gps_Reply {
    pb_size_t which_reply;
    union {
        gps_Position_Reply position;
        gps_Spartn_Reply spartn;
        gps_HeadingCorrection_Reply heading_correction;
        gps_Rtcm_Reply rtcm;
        gps_GetLastGga_Reply gga;
    } reply;
} gps_Reply;


/* Helper constants for enums */
#define _gps_CarrierPhaseSoln_MIN gps_CarrierPhaseSoln_NONE
#define _gps_CarrierPhaseSoln_MAX gps_CarrierPhaseSoln_FIXED
#define _gps_CarrierPhaseSoln_ARRAYSIZE ((gps_CarrierPhaseSoln)(gps_CarrierPhaseSoln_FIXED+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define gps_Position_Request_init_default        {0}
#define gps_Spartn_Request_init_default          {{0, {0}}, 0}
#define gps_Spartn_Reply_init_default            {0}
#define gps_Rtcm_Request_init_default            {{0, {0}}, 0}
#define gps_Rtcm_Reply_init_default              {0}
#define gps_ValueWithAccuracy_init_default       {0, 0}
#define gps_DualGpsData_init_default             {0, 0, 0, _gps_CarrierPhaseSoln_MIN, 0, 0, {gps_ValueWithAccuracy_init_default}, 0, {gps_ValueWithAccuracy_init_default}, 0, {gps_ValueWithAccuracy_init_default}, 0, {gps_ValueWithAccuracy_init_default}, 0, {gps_ValueWithAccuracy_init_default}}
#define gps_Position_Reply_init_default          {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, {gps_DualGpsData_init_default}}
#define gps_HeadingCorrection_Request_init_default {0}
#define gps_HeadingCorrection_Reply_init_default {0}
#define gps_GetLastGga_Request_init_default      {0}
#define gps_GetLastGga_Reply_init_default        {""}
#define gps_Request_init_default                 {0, {gps_Position_Request_init_default}}
#define gps_Reply_init_default                   {0, {gps_Position_Reply_init_default}}
#define gps_Position_Request_init_zero           {0}
#define gps_Spartn_Request_init_zero             {{0, {0}}, 0}
#define gps_Spartn_Reply_init_zero               {0}
#define gps_Rtcm_Request_init_zero               {{0, {0}}, 0}
#define gps_Rtcm_Reply_init_zero                 {0}
#define gps_ValueWithAccuracy_init_zero          {0, 0}
#define gps_DualGpsData_init_zero                {0, 0, 0, _gps_CarrierPhaseSoln_MIN, 0, 0, {gps_ValueWithAccuracy_init_zero}, 0, {gps_ValueWithAccuracy_init_zero}, 0, {gps_ValueWithAccuracy_init_zero}, 0, {gps_ValueWithAccuracy_init_zero}, 0, {gps_ValueWithAccuracy_init_zero}}
#define gps_Position_Reply_init_zero             {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, {gps_DualGpsData_init_zero}}
#define gps_HeadingCorrection_Request_init_zero  {0}
#define gps_HeadingCorrection_Reply_init_zero    {0}
#define gps_GetLastGga_Request_init_zero         {0}
#define gps_GetLastGga_Reply_init_zero           {""}
#define gps_Request_init_zero                    {0, {gps_Position_Request_init_zero}}
#define gps_Reply_init_zero                      {0, {gps_Position_Reply_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define gps_GetLastGga_Reply_raw_sentence_tag    1
#define gps_HeadingCorrection_Request_heading_offset_tag 1
#define gps_Rtcm_Request_data_tag                1
#define gps_Rtcm_Request_end_tag                 2
#define gps_Spartn_Request_data_tag              1
#define gps_Spartn_Request_end_tag               2
#define gps_ValueWithAccuracy_value_tag          1
#define gps_ValueWithAccuracy_accuracy_tag       2
#define gps_DualGpsData_gnss_valid_tag           1
#define gps_DualGpsData_diff_corrections_tag     2
#define gps_DualGpsData_is_moving_base_tag       3
#define gps_DualGpsData_carrier_phase_tag        4
#define gps_DualGpsData_timestamp_ms_tag         5
#define gps_DualGpsData_north_tag                6
#define gps_DualGpsData_east_tag                 7
#define gps_DualGpsData_down_tag                 8
#define gps_DualGpsData_length_tag               9
#define gps_DualGpsData_heading_tag              10
#define gps_Request_position_tag                 1
#define gps_Request_spartn_tag                   2
#define gps_Request_heading_correction_tag       3
#define gps_Request_rtcm_tag                     4
#define gps_Request_gga_tag                      5
#define gps_Position_Reply_have_fix_tag          1
#define gps_Position_Reply_latitude_tag          2
#define gps_Position_Reply_longitude_tag         3
#define gps_Position_Reply_num_sats_tag          4
#define gps_Position_Reply_hdop_tag              5
#define gps_Position_Reply_timestamp_ms_tag      6
#define gps_Position_Reply_height_mm_tag         7
#define gps_Position_Reply_have_approx_fix_tag   8
#define gps_Position_Reply_fix_type_tag          9
#define gps_Position_Reply_fix_flags_tag         10
#define gps_Position_Reply_dual_tag              11
#define gps_Reply_position_tag                   1
#define gps_Reply_spartn_tag                     2
#define gps_Reply_heading_correction_tag         3
#define gps_Reply_rtcm_tag                       4
#define gps_Reply_gga_tag                        5

/* Struct field encoding specification for nanopb */
#define gps_Position_Request_FIELDLIST(X, a) \

#define gps_Position_Request_CALLBACK NULL
#define gps_Position_Request_DEFAULT NULL

#define gps_Spartn_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BYTES,    data,              1) \
X(a, STATIC,   SINGULAR, BOOL,     end,               2)
#define gps_Spartn_Request_CALLBACK NULL
#define gps_Spartn_Request_DEFAULT NULL

#define gps_Spartn_Reply_FIELDLIST(X, a) \

#define gps_Spartn_Reply_CALLBACK NULL
#define gps_Spartn_Reply_DEFAULT NULL

#define gps_Rtcm_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BYTES,    data,              1) \
X(a, STATIC,   SINGULAR, BOOL,     end,               2)
#define gps_Rtcm_Request_CALLBACK NULL
#define gps_Rtcm_Request_DEFAULT NULL

#define gps_Rtcm_Reply_FIELDLIST(X, a) \

#define gps_Rtcm_Reply_CALLBACK NULL
#define gps_Rtcm_Reply_DEFAULT NULL

#define gps_ValueWithAccuracy_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, DOUBLE,   value,             1) \
X(a, STATIC,   SINGULAR, DOUBLE,   accuracy,          2)
#define gps_ValueWithAccuracy_CALLBACK NULL
#define gps_ValueWithAccuracy_DEFAULT NULL

#define gps_DualGpsData_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     gnss_valid,        1) \
X(a, STATIC,   SINGULAR, BOOL,     diff_corrections,   2) \
X(a, STATIC,   SINGULAR, BOOL,     is_moving_base,    3) \
X(a, STATIC,   SINGULAR, UENUM,    carrier_phase,     4) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp_ms,      5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_north,north,_north.north),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_east,east,_east.east),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_down,down,_down.down),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_length,length,_length.length),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_heading,heading,_heading.heading),  10)
#define gps_DualGpsData_CALLBACK NULL
#define gps_DualGpsData_DEFAULT NULL
#define gps_DualGpsData__north_north_MSGTYPE gps_ValueWithAccuracy
#define gps_DualGpsData__east_east_MSGTYPE gps_ValueWithAccuracy
#define gps_DualGpsData__down_down_MSGTYPE gps_ValueWithAccuracy
#define gps_DualGpsData__length_length_MSGTYPE gps_ValueWithAccuracy
#define gps_DualGpsData__heading_heading_MSGTYPE gps_ValueWithAccuracy

#define gps_Position_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     have_fix,          1) \
X(a, STATIC,   SINGULAR, DOUBLE,   latitude,          2) \
X(a, STATIC,   SINGULAR, DOUBLE,   longitude,         3) \
X(a, STATIC,   SINGULAR, INT32,    num_sats,          4) \
X(a, STATIC,   SINGULAR, FLOAT,    hdop,              5) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp_ms,      6) \
X(a, STATIC,   SINGULAR, INT32,    height_mm,         7) \
X(a, STATIC,   SINGULAR, BOOL,     have_approx_fix,   8) \
X(a, STATIC,   SINGULAR, INT32,    fix_type,          9) \
X(a, STATIC,   SINGULAR, INT32,    fix_flags,        10) \
X(a, STATIC,   ONEOF,    MESSAGE,  (_dual,dual,_dual.dual),  11)
#define gps_Position_Reply_CALLBACK NULL
#define gps_Position_Reply_DEFAULT NULL
#define gps_Position_Reply__dual_dual_MSGTYPE gps_DualGpsData

#define gps_HeadingCorrection_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    heading_offset,    1)
#define gps_HeadingCorrection_Request_CALLBACK NULL
#define gps_HeadingCorrection_Request_DEFAULT NULL

#define gps_HeadingCorrection_Reply_FIELDLIST(X, a) \

#define gps_HeadingCorrection_Reply_CALLBACK NULL
#define gps_HeadingCorrection_Reply_DEFAULT NULL

#define gps_GetLastGga_Request_FIELDLIST(X, a) \

#define gps_GetLastGga_Request_CALLBACK NULL
#define gps_GetLastGga_Request_DEFAULT NULL

#define gps_GetLastGga_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, STRING,   raw_sentence,      1)
#define gps_GetLastGga_Reply_CALLBACK NULL
#define gps_GetLastGga_Reply_DEFAULT NULL

#define gps_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,position,request.position),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,spartn,request.spartn),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,heading_correction,request.heading_correction),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,rtcm,request.rtcm),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,gga,request.gga),   5)
#define gps_Request_CALLBACK NULL
#define gps_Request_DEFAULT NULL
#define gps_Request_request_position_MSGTYPE gps_Position_Request
#define gps_Request_request_spartn_MSGTYPE gps_Spartn_Request
#define gps_Request_request_heading_correction_MSGTYPE gps_HeadingCorrection_Request
#define gps_Request_request_rtcm_MSGTYPE gps_Rtcm_Request
#define gps_Request_request_gga_MSGTYPE gps_GetLastGga_Request

#define gps_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,position,reply.position),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,spartn,reply.spartn),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,heading_correction,reply.heading_correction),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,rtcm,reply.rtcm),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,gga,reply.gga),   5)
#define gps_Reply_CALLBACK NULL
#define gps_Reply_DEFAULT NULL
#define gps_Reply_reply_position_MSGTYPE gps_Position_Reply
#define gps_Reply_reply_spartn_MSGTYPE gps_Spartn_Reply
#define gps_Reply_reply_heading_correction_MSGTYPE gps_HeadingCorrection_Reply
#define gps_Reply_reply_rtcm_MSGTYPE gps_Rtcm_Reply
#define gps_Reply_reply_gga_MSGTYPE gps_GetLastGga_Reply

extern const pb_msgdesc_t gps_Position_Request_msg;
extern const pb_msgdesc_t gps_Spartn_Request_msg;
extern const pb_msgdesc_t gps_Spartn_Reply_msg;
extern const pb_msgdesc_t gps_Rtcm_Request_msg;
extern const pb_msgdesc_t gps_Rtcm_Reply_msg;
extern const pb_msgdesc_t gps_ValueWithAccuracy_msg;
extern const pb_msgdesc_t gps_DualGpsData_msg;
extern const pb_msgdesc_t gps_Position_Reply_msg;
extern const pb_msgdesc_t gps_HeadingCorrection_Request_msg;
extern const pb_msgdesc_t gps_HeadingCorrection_Reply_msg;
extern const pb_msgdesc_t gps_GetLastGga_Request_msg;
extern const pb_msgdesc_t gps_GetLastGga_Reply_msg;
extern const pb_msgdesc_t gps_Request_msg;
extern const pb_msgdesc_t gps_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define gps_Position_Request_fields &gps_Position_Request_msg
#define gps_Spartn_Request_fields &gps_Spartn_Request_msg
#define gps_Spartn_Reply_fields &gps_Spartn_Reply_msg
#define gps_Rtcm_Request_fields &gps_Rtcm_Request_msg
#define gps_Rtcm_Reply_fields &gps_Rtcm_Reply_msg
#define gps_ValueWithAccuracy_fields &gps_ValueWithAccuracy_msg
#define gps_DualGpsData_fields &gps_DualGpsData_msg
#define gps_Position_Reply_fields &gps_Position_Reply_msg
#define gps_HeadingCorrection_Request_fields &gps_HeadingCorrection_Request_msg
#define gps_HeadingCorrection_Reply_fields &gps_HeadingCorrection_Reply_msg
#define gps_GetLastGga_Request_fields &gps_GetLastGga_Request_msg
#define gps_GetLastGga_Reply_fields &gps_GetLastGga_Reply_msg
#define gps_Request_fields &gps_Request_msg
#define gps_Reply_fields &gps_Reply_msg

/* Maximum encoded size of messages (where known) */
#define gps_Position_Request_size                0
#define gps_Spartn_Request_size                  68
#define gps_Spartn_Reply_size                    0
#define gps_Rtcm_Request_size                    68
#define gps_Rtcm_Reply_size                      0
#define gps_ValueWithAccuracy_size               18
#define gps_DualGpsData_size                     119
#define gps_Position_Reply_size                  203
#define gps_HeadingCorrection_Request_size       5
#define gps_HeadingCorrection_Reply_size         0
#define gps_GetLastGga_Request_size              0
#define gps_GetLastGga_Reply_size                101
#define gps_Request_size                         70
#define gps_Reply_size                           206

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

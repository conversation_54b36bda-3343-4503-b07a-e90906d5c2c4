/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_EXPERIMENT_BOARD_EXPERIMENT_BOARD_PB_H_INCLUDED
#define PB_EXPERIMENT_BOARD_EXPERIMENT_BOARD_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"
#include "generated/lib/drivers/nanopb/proto/row_module.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _experiment_board_ExperimentRepeated {
    pb_size_t items_count;
    struct _experiment_board_ExperimentItem *items;
} experiment_board_ExperimentRepeated;

typedef struct _experiment_board_ExperimentItem {
    uint32_t x;
} experiment_board_ExperimentItem;

typedef struct _experiment_board_ExperimentRequest {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        experiment_board_ExperimentRepeated items;
        row_module_Request row_module;
    } request;
} experiment_board_ExperimentRequest;

typedef struct _experiment_board_ExperimentResult {
    uint32_t sum;
} experiment_board_ExperimentResult;

typedef struct _experiment_board_ExperimentReply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        experiment_board_ExperimentResult result;
        row_module_Reply row_module;
    } reply;
} experiment_board_ExperimentReply;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define experiment_board_ExperimentItem_init_default {0}
#define experiment_board_ExperimentRepeated_init_default {0, NULL}
#define experiment_board_ExperimentResult_init_default {0}
#define experiment_board_ExperimentReply_init_default {false, request_RequestHeader_init_default, 0, {diagnostic_Pong_init_default}}
#define experiment_board_ExperimentRequest_init_default {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define experiment_board_ExperimentItem_init_zero {0}
#define experiment_board_ExperimentRepeated_init_zero {0, NULL}
#define experiment_board_ExperimentResult_init_zero {0}
#define experiment_board_ExperimentReply_init_zero {false, request_RequestHeader_init_zero, 0, {diagnostic_Pong_init_zero}}
#define experiment_board_ExperimentRequest_init_zero {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define experiment_board_ExperimentRepeated_items_tag 1
#define experiment_board_ExperimentItem_x_tag    1
#define experiment_board_ExperimentRequest_header_tag 1
#define experiment_board_ExperimentRequest_ping_tag 2
#define experiment_board_ExperimentRequest_items_tag 3
#define experiment_board_ExperimentRequest_row_module_tag 4
#define experiment_board_ExperimentResult_sum_tag 1
#define experiment_board_ExperimentReply_header_tag 1
#define experiment_board_ExperimentReply_pong_tag 2
#define experiment_board_ExperimentReply_result_tag 3
#define experiment_board_ExperimentReply_row_module_tag 4

/* Struct field encoding specification for nanopb */
#define experiment_board_ExperimentItem_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   x,                 1)
#define experiment_board_ExperimentItem_CALLBACK NULL
#define experiment_board_ExperimentItem_DEFAULT NULL

#define experiment_board_ExperimentRepeated_FIELDLIST(X, a) \
X(a, POINTER,  REPEATED, MESSAGE,  items,             1)
#define experiment_board_ExperimentRepeated_CALLBACK NULL
#define experiment_board_ExperimentRepeated_DEFAULT NULL
#define experiment_board_ExperimentRepeated_items_MSGTYPE experiment_board_ExperimentItem

#define experiment_board_ExperimentResult_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   sum,               1)
#define experiment_board_ExperimentResult_CALLBACK NULL
#define experiment_board_ExperimentResult_DEFAULT NULL

#define experiment_board_ExperimentReply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,result,reply.result),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,row_module,reply.row_module),   4)
#define experiment_board_ExperimentReply_CALLBACK NULL
#define experiment_board_ExperimentReply_DEFAULT NULL
#define experiment_board_ExperimentReply_header_MSGTYPE request_RequestHeader
#define experiment_board_ExperimentReply_reply_pong_MSGTYPE diagnostic_Pong
#define experiment_board_ExperimentReply_reply_result_MSGTYPE experiment_board_ExperimentResult
#define experiment_board_ExperimentReply_reply_row_module_MSGTYPE row_module_Reply

#define experiment_board_ExperimentRequest_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,items,request.items),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,row_module,request.row_module),   4)
#define experiment_board_ExperimentRequest_CALLBACK NULL
#define experiment_board_ExperimentRequest_DEFAULT NULL
#define experiment_board_ExperimentRequest_header_MSGTYPE request_RequestHeader
#define experiment_board_ExperimentRequest_request_ping_MSGTYPE diagnostic_Ping
#define experiment_board_ExperimentRequest_request_items_MSGTYPE experiment_board_ExperimentRepeated
#define experiment_board_ExperimentRequest_request_row_module_MSGTYPE row_module_Request

extern const pb_msgdesc_t experiment_board_ExperimentItem_msg;
extern const pb_msgdesc_t experiment_board_ExperimentRepeated_msg;
extern const pb_msgdesc_t experiment_board_ExperimentResult_msg;
extern const pb_msgdesc_t experiment_board_ExperimentReply_msg;
extern const pb_msgdesc_t experiment_board_ExperimentRequest_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define experiment_board_ExperimentItem_fields &experiment_board_ExperimentItem_msg
#define experiment_board_ExperimentRepeated_fields &experiment_board_ExperimentRepeated_msg
#define experiment_board_ExperimentResult_fields &experiment_board_ExperimentResult_msg
#define experiment_board_ExperimentReply_fields &experiment_board_ExperimentReply_msg
#define experiment_board_ExperimentRequest_fields &experiment_board_ExperimentRequest_msg

/* Maximum encoded size of messages (where known) */
#define experiment_board_ExperimentItem_size     6
/* experiment_board_ExperimentRepeated_size depends on runtime parameters */
#define experiment_board_ExperimentResult_size   6
#if defined(request_RequestHeader_size) && defined(diagnostic_Pong_size) && defined(row_module_Reply_size)
typedef union experiment_board_ExperimentReply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f4[(6 + row_module_Reply_size)]; char f0[8];} experiment_board_ExperimentReply_reply_size_union;
#define experiment_board_ExperimentReply_size    (6 + request_RequestHeader_size + sizeof(experiment_board_ExperimentReply_reply_size_union))
#endif
/* experiment_board_ExperimentRequest_size depends on runtime parameters */

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.diagnostic_pb2 import (
    Ping as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping,
    Pong as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from generated.lib.drivers.nanopb.proto.hwinfo_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___hwinfo_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___hwinfo_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.request_pb2 import (
    RequestHeader as generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader,
)

from generated.lib.drivers.nanopb.proto.strobe_control_pb2 import (
    Request as generated___lib___drivers___nanopb___proto___strobe_control_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.time_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___time_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___time_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.version_pb2 import (
    Reset_Request as generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request,
    Version_Reply as generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply,
    Version_Request as generated___lib___drivers___nanopb___proto___version_pb2___Version_Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

NetworkAddressSourceValue = typing___NewType('NetworkAddressSourceValue', builtin___int)
type___NetworkAddressSourceValue = NetworkAddressSourceValue
NetworkAddressSource: _NetworkAddressSource
class _NetworkAddressSource(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[NetworkAddressSourceValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    MANUAL = typing___cast(NetworkAddressSourceValue, 0)
    STATIC = typing___cast(NetworkAddressSourceValue, 1)
    DHCP = typing___cast(NetworkAddressSourceValue, 2)
MANUAL = typing___cast(NetworkAddressSourceValue, 0)
STATIC = typing___cast(NetworkAddressSourceValue, 1)
DHCP = typing___cast(NetworkAddressSourceValue, 2)

ThermostatSourceValue = typing___NewType('ThermostatSourceValue', builtin___int)
type___ThermostatSourceValue = ThermostatSourceValue
ThermostatSource: _ThermostatSource
class _ThermostatSource(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ThermostatSourceValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    DEFAULT = typing___cast(ThermostatSourceValue, 0)
    ENVIRO_INTERNAL = typing___cast(ThermostatSourceValue, 1)
    ENVIRO_EXTERNAL = typing___cast(ThermostatSourceValue, 2)
DEFAULT = typing___cast(ThermostatSourceValue, 0)
ENVIRO_INTERNAL = typing___cast(ThermostatSourceValue, 1)
ENVIRO_EXTERNAL = typing___cast(ThermostatSourceValue, 2)

class NetworkConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___NetworkConfigRequest = NetworkConfigRequest

class NetworkResetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___NetworkResetRequest = NetworkResetRequest

class NetworkPowerCycleRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___NetworkPowerCycleRequest = NetworkPowerCycleRequest

class NetworkRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___NetworkConfigRequest: ...

    @property
    def reset(self) -> type___NetworkResetRequest: ...

    @property
    def powerCycle(self) -> type___NetworkPowerCycleRequest: ...

    def __init__(self,
        *,
        config : typing___Optional[type___NetworkConfigRequest] = None,
        reset : typing___Optional[type___NetworkResetRequest] = None,
        powerCycle : typing___Optional[type___NetworkPowerCycleRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config",u"powerCycle",b"powerCycle",u"request",b"request",u"reset",b"reset"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"powerCycle",b"powerCycle",u"request",b"request",u"reset",b"reset"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["config","reset","powerCycle"]: ...
type___NetworkRequest = NetworkRequest

class NetworkAddress(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    source: type___NetworkAddressSourceValue = ...
    unicast: builtin___bytes = ...
    subnet: builtin___bytes = ...
    gateway: builtin___bytes = ...

    def __init__(self,
        *,
        source : typing___Optional[type___NetworkAddressSourceValue] = None,
        unicast : typing___Optional[builtin___bytes] = None,
        subnet : typing___Optional[builtin___bytes] = None,
        gateway : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_gateway",b"_gateway",u"gateway",b"gateway"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_gateway",b"_gateway",u"gateway",b"gateway",u"source",b"source",u"subnet",b"subnet",u"unicast",b"unicast"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_gateway",b"_gateway"]) -> typing_extensions___Literal["gateway"]: ...
type___NetworkAddress = NetworkAddress

class NetworkConfigReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    linkUp: builtin___bool = ...
    mac: builtin___bytes = ...

    @property
    def addresses(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___NetworkAddress]: ...

    def __init__(self,
        *,
        linkUp : typing___Optional[builtin___bool] = None,
        mac : typing___Optional[builtin___bytes] = None,
        addresses : typing___Optional[typing___Iterable[type___NetworkAddress]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"addresses",b"addresses",u"linkUp",b"linkUp",u"mac",b"mac"]) -> None: ...
type___NetworkConfigReply = NetworkConfigReply

class NetworkReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def config(self) -> type___NetworkConfigReply: ...

    def __init__(self,
        *,
        config : typing___Optional[type___NetworkConfigReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config",u"reply",b"reply"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"reply",b"reply"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["config"]: ...
type___NetworkReply = NetworkReply

class ScannerStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    powerEnabled: builtin___bool = ...
    fuseBlown: builtin___bool = ...
    fuseTimestamp: builtin___int = ...
    current: builtin___float = ...
    currentTimestamp: builtin___int = ...

    def __init__(self,
        *,
        powerEnabled : typing___Optional[builtin___bool] = None,
        fuseBlown : typing___Optional[builtin___bool] = None,
        fuseTimestamp : typing___Optional[builtin___int] = None,
        current : typing___Optional[builtin___float] = None,
        currentTimestamp : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current",b"current",u"currentTimestamp",b"currentTimestamp",u"fuseBlown",b"fuseBlown",u"fuseTimestamp",b"fuseTimestamp",u"powerEnabled",b"powerEnabled"]) -> None: ...
type___ScannerStatus = ScannerStatus

class ScannerGetStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ScannerGetStatusRequest = ScannerGetStatusRequest

class ScannerSetPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scannerA: builtin___bool = ...
    scannerB: builtin___bool = ...

    def __init__(self,
        *,
        scannerA : typing___Optional[builtin___bool] = None,
        scannerB : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_scannerA",b"_scannerA",u"_scannerB",b"_scannerB",u"scannerA",b"scannerA",u"scannerB",b"scannerB"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_scannerA",b"_scannerA",u"_scannerB",b"_scannerB",u"scannerA",b"scannerA",u"scannerB",b"scannerB"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scannerA",b"_scannerA"]) -> typing_extensions___Literal["scannerA"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scannerB",b"_scannerB"]) -> typing_extensions___Literal["scannerB"]: ...
type___ScannerSetPowerRequest = ScannerSetPowerRequest

class ScannerResetOcpRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scannerA: builtin___bool = ...
    scannerB: builtin___bool = ...

    def __init__(self,
        *,
        scannerA : typing___Optional[builtin___bool] = None,
        scannerB : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_scannerA",b"_scannerA",u"_scannerB",b"_scannerB",u"scannerA",b"scannerA",u"scannerB",b"scannerB"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_scannerA",b"_scannerA",u"_scannerB",b"_scannerB",u"scannerA",b"scannerA",u"scannerB",b"scannerB"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scannerA",b"_scannerA"]) -> typing_extensions___Literal["scannerA"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scannerB",b"_scannerB"]) -> typing_extensions___Literal["scannerB"]: ...
type___ScannerResetOcpRequest = ScannerResetOcpRequest

class ScannerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def status(self) -> type___ScannerGetStatusRequest: ...

    @property
    def power(self) -> type___ScannerSetPowerRequest: ...

    @property
    def ocp(self) -> type___ScannerResetOcpRequest: ...

    def __init__(self,
        *,
        status : typing___Optional[type___ScannerGetStatusRequest] = None,
        power : typing___Optional[type___ScannerSetPowerRequest] = None,
        ocp : typing___Optional[type___ScannerResetOcpRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ocp",b"ocp",u"power",b"power",u"request",b"request",u"status",b"status"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ocp",b"ocp",u"power",b"power",u"request",b"request",u"status",b"status"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["status","power","ocp"]: ...
type___ScannerRequest = ScannerRequest

class ScannerStatusReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def data(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ScannerStatus]: ...

    def __init__(self,
        *,
        data : typing___Optional[typing___Iterable[type___ScannerStatus]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data"]) -> None: ...
type___ScannerStatusReply = ScannerStatusReply

class ScannerReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def status(self) -> type___ScannerStatusReply: ...

    def __init__(self,
        *,
        status : typing___Optional[type___ScannerStatusReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"reply",b"reply",u"status",b"status"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"reply",b"reply",u"status",b"status"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["status"]: ...
type___ScannerReply = ScannerReply

class PowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    relayBoard: builtin___bool = ...
    strobeBoard: builtin___bool = ...
    ethSwitch: builtin___bool = ...
    predictCam: builtin___bool = ...

    def __init__(self,
        *,
        relayBoard : typing___Optional[builtin___bool] = None,
        strobeBoard : typing___Optional[builtin___bool] = None,
        ethSwitch : typing___Optional[builtin___bool] = None,
        predictCam : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_ethSwitch",b"_ethSwitch",u"_predictCam",b"_predictCam",u"_relayBoard",b"_relayBoard",u"_strobeBoard",b"_strobeBoard",u"ethSwitch",b"ethSwitch",u"predictCam",b"predictCam",u"relayBoard",b"relayBoard",u"strobeBoard",b"strobeBoard"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_ethSwitch",b"_ethSwitch",u"_predictCam",b"_predictCam",u"_relayBoard",b"_relayBoard",u"_strobeBoard",b"_strobeBoard",u"ethSwitch",b"ethSwitch",u"predictCam",b"predictCam",u"relayBoard",b"relayBoard",u"strobeBoard",b"strobeBoard"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_ethSwitch",b"_ethSwitch"]) -> typing_extensions___Literal["ethSwitch"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_predictCam",b"_predictCam"]) -> typing_extensions___Literal["predictCam"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_relayBoard",b"_relayBoard"]) -> typing_extensions___Literal["relayBoard"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_strobeBoard",b"_strobeBoard"]) -> typing_extensions___Literal["strobeBoard"]: ...
type___PowerRequest = PowerRequest

class PowerReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    relayBoard: builtin___bool = ...
    strobeBoard: builtin___bool = ...
    ethSwitch: builtin___bool = ...
    predictCam: builtin___bool = ...

    def __init__(self,
        *,
        relayBoard : typing___Optional[builtin___bool] = None,
        strobeBoard : typing___Optional[builtin___bool] = None,
        ethSwitch : typing___Optional[builtin___bool] = None,
        predictCam : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ethSwitch",b"ethSwitch",u"predictCam",b"predictCam",u"relayBoard",b"relayBoard",u"strobeBoard",b"strobeBoard"]) -> None: ...
type___PowerReply = PowerReply

class StrobeStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StrobeStatusRequest = StrobeStatusRequest

class SetStrobeStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___SetStrobeStateRequest = SetStrobeStateRequest

class TimedStrobeDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    durationMsec: builtin___int = ...

    def __init__(self,
        *,
        durationMsec : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"durationMsec",b"durationMsec"]) -> None: ...
type___TimedStrobeDisableRequest = TimedStrobeDisableRequest

class StrobeStatusReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    voltage: builtin___float = ...
    voltageTimestamp: builtin___int = ...
    current: builtin___float = ...
    currentTimestamp: builtin___int = ...
    temperature: builtin___float = ...
    temperatureTimestamp: builtin___int = ...
    ready: builtin___bool = ...
    enabled: builtin___bool = ...
    firing: builtin___bool = ...
    exposureUs: builtin___int = ...
    periodUs: builtin___int = ...
    targetsPerPredict: builtin___int = ...

    def __init__(self,
        *,
        voltage : typing___Optional[builtin___float] = None,
        voltageTimestamp : typing___Optional[builtin___int] = None,
        current : typing___Optional[builtin___float] = None,
        currentTimestamp : typing___Optional[builtin___int] = None,
        temperature : typing___Optional[builtin___float] = None,
        temperatureTimestamp : typing___Optional[builtin___int] = None,
        ready : typing___Optional[builtin___bool] = None,
        enabled : typing___Optional[builtin___bool] = None,
        firing : typing___Optional[builtin___bool] = None,
        exposureUs : typing___Optional[builtin___int] = None,
        periodUs : typing___Optional[builtin___int] = None,
        targetsPerPredict : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_exposureUs",b"_exposureUs",u"_periodUs",b"_periodUs",u"_targetsPerPredict",b"_targetsPerPredict",u"exposureUs",b"exposureUs",u"periodUs",b"periodUs",u"targetsPerPredict",b"targetsPerPredict"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_exposureUs",b"_exposureUs",u"_periodUs",b"_periodUs",u"_targetsPerPredict",b"_targetsPerPredict",u"current",b"current",u"currentTimestamp",b"currentTimestamp",u"enabled",b"enabled",u"exposureUs",b"exposureUs",u"firing",b"firing",u"periodUs",b"periodUs",u"ready",b"ready",u"targetsPerPredict",b"targetsPerPredict",u"temperature",b"temperature",u"temperatureTimestamp",b"temperatureTimestamp",u"voltage",b"voltage",u"voltageTimestamp",b"voltageTimestamp"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_exposureUs",b"_exposureUs"]) -> typing_extensions___Literal["exposureUs"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_periodUs",b"_periodUs"]) -> typing_extensions___Literal["periodUs"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_targetsPerPredict",b"_targetsPerPredict"]) -> typing_extensions___Literal["targetsPerPredict"]: ...
type___StrobeStatusReply = StrobeStatusReply

class StrobeRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def setWaveform(self) -> generated___lib___drivers___nanopb___proto___strobe_control_pb2___Request: ...

    @property
    def getStatus(self) -> type___StrobeStatusRequest: ...

    @property
    def setState(self) -> type___SetStrobeStateRequest: ...

    @property
    def timedDisable(self) -> type___TimedStrobeDisableRequest: ...

    def __init__(self,
        *,
        setWaveform : typing___Optional[generated___lib___drivers___nanopb___proto___strobe_control_pb2___Request] = None,
        getStatus : typing___Optional[type___StrobeStatusRequest] = None,
        setState : typing___Optional[type___SetStrobeStateRequest] = None,
        timedDisable : typing___Optional[type___TimedStrobeDisableRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"getStatus",b"getStatus",u"request",b"request",u"setState",b"setState",u"setWaveform",b"setWaveform",u"timedDisable",b"timedDisable"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"getStatus",b"getStatus",u"request",b"request",u"setState",b"setState",u"setWaveform",b"setWaveform",u"timedDisable",b"timedDisable"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["setWaveform","getStatus","setState","timedDisable"]: ...
type___StrobeRequest = StrobeRequest

class StrobeReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def status(self) -> type___StrobeStatusReply: ...

    def __init__(self,
        *,
        status : typing___Optional[type___StrobeStatusReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"reply",b"reply",u"status",b"status"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"reply",b"reply",u"status",b"status"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["status"]: ...
type___StrobeReply = StrobeReply

class ModuleIdentity(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    number: builtin___int = ...

    def __init__(self,
        *,
        number : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"number",b"number"]) -> None: ...
type___ModuleIdentity = ModuleIdentity

class GetModuleIdentityRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetModuleIdentityRequest = GetModuleIdentityRequest

class SetOtpLockRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lock: builtin___bool = ...
    key: builtin___int = ...

    def __init__(self,
        *,
        lock : typing___Optional[builtin___bool] = None,
        key : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"lock",b"lock"]) -> None: ...
type___SetOtpLockRequest = SetOtpLockRequest

class SetBoardIdRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cbsn: typing___Text = ...
    assySn: typing___Text = ...

    def __init__(self,
        *,
        cbsn : typing___Optional[typing___Text] = None,
        assySn : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_assySn",b"_assySn",u"_cbsn",b"_cbsn",u"assySn",b"assySn",u"cbsn",b"cbsn"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_assySn",b"_assySn",u"_cbsn",b"_cbsn",u"assySn",b"assySn",u"cbsn",b"cbsn"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_assySn",b"_assySn"]) -> typing_extensions___Literal["assySn"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_cbsn",b"_cbsn"]) -> typing_extensions___Literal["cbsn"]: ...
type___SetBoardIdRequest = SetBoardIdRequest

class ConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def getIdentity(self) -> type___GetModuleIdentityRequest: ...

    @property
    def setIdentity(self) -> type___ModuleIdentity: ...

    @property
    def otpLock(self) -> type___SetOtpLockRequest: ...

    @property
    def setBoardIdentity(self) -> type___SetBoardIdRequest: ...

    def __init__(self,
        *,
        getIdentity : typing___Optional[type___GetModuleIdentityRequest] = None,
        setIdentity : typing___Optional[type___ModuleIdentity] = None,
        otpLock : typing___Optional[type___SetOtpLockRequest] = None,
        setBoardIdentity : typing___Optional[type___SetBoardIdRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"getIdentity",b"getIdentity",u"otpLock",b"otpLock",u"request",b"request",u"setBoardIdentity",b"setBoardIdentity",u"setIdentity",b"setIdentity"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"getIdentity",b"getIdentity",u"otpLock",b"otpLock",u"request",b"request",u"setBoardIdentity",b"setBoardIdentity",u"setIdentity",b"setIdentity"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["getIdentity","setIdentity","otpLock","setBoardIdentity"]: ...
type___ConfigRequest = ConfigRequest

class ConfigReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def identity(self) -> type___ModuleIdentity: ...

    def __init__(self,
        *,
        identity : typing___Optional[type___ModuleIdentity] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"identity",b"identity",u"reply",b"reply"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"identity",b"identity",u"reply",b"reply"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["identity"]: ...
type___ConfigReply = ConfigReply

class ThermostatConfig(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    setpoint: builtin___float = ...
    hysteresis: builtin___float = ...
    source: type___ThermostatSourceValue = ...

    def __init__(self,
        *,
        setpoint : typing___Optional[builtin___float] = None,
        hysteresis : typing___Optional[builtin___float] = None,
        source : typing___Optional[type___ThermostatSourceValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"hysteresis",b"hysteresis",u"setpoint",b"setpoint",u"source",b"source"]) -> None: ...
type___ThermostatConfig = ThermostatConfig

class FanSetRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    fan1: builtin___bool = ...
    fan2: builtin___bool = ...

    def __init__(self,
        *,
        fan1 : typing___Optional[builtin___bool] = None,
        fan2 : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_fan1",b"_fan1",u"_fan2",b"_fan2",u"fan1",b"fan1",u"fan2",b"fan2"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_fan1",b"_fan1",u"_fan2",b"_fan2",u"fan1",b"fan1",u"fan2",b"fan2"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_fan1",b"_fan1"]) -> typing_extensions___Literal["fan1"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_fan2",b"_fan2"]) -> typing_extensions___Literal["fan2"]: ...
type___FanSetRequest = FanSetRequest

class FanThermostatConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    @property
    def config(self) -> type___ThermostatConfig: ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        config : typing___Optional[type___ThermostatConfig] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_config",b"_config",u"_enabled",b"_enabled",u"config",b"config",u"enabled",b"enabled"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_config",b"_config",u"_enabled",b"_enabled",u"config",b"config",u"enabled",b"enabled"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_config",b"_config"]) -> typing_extensions___Literal["config"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_enabled",b"_enabled"]) -> typing_extensions___Literal["enabled"]: ...
type___FanThermostatConfigRequest = FanThermostatConfigRequest

class FanRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def set(self) -> type___FanSetRequest: ...

    @property
    def thermoConfig(self) -> type___FanThermostatConfigRequest: ...

    def __init__(self,
        *,
        set : typing___Optional[type___FanSetRequest] = None,
        thermoConfig : typing___Optional[type___FanThermostatConfigRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"request",b"request",u"set",b"set",u"thermoConfig",b"thermoConfig"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"request",b"request",u"set",b"set",u"thermoConfig",b"thermoConfig"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["set","thermoConfig"]: ...
type___FanRequest = FanRequest

class FanReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    fan1: builtin___bool = ...
    fan2: builtin___bool = ...
    thermostatEnabled: builtin___bool = ...
    thermostatActual: builtin___float = ...

    @property
    def thermostatConfig(self) -> type___ThermostatConfig: ...

    def __init__(self,
        *,
        fan1 : typing___Optional[builtin___bool] = None,
        fan2 : typing___Optional[builtin___bool] = None,
        thermostatEnabled : typing___Optional[builtin___bool] = None,
        thermostatConfig : typing___Optional[type___ThermostatConfig] = None,
        thermostatActual : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_thermostatActual",b"_thermostatActual",u"_thermostatConfig",b"_thermostatConfig",u"thermostatActual",b"thermostatActual",u"thermostatConfig",b"thermostatConfig"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_thermostatActual",b"_thermostatActual",u"_thermostatConfig",b"_thermostatConfig",u"fan1",b"fan1",u"fan2",b"fan2",u"thermostatActual",b"thermostatActual",u"thermostatConfig",b"thermostatConfig",u"thermostatEnabled",b"thermostatEnabled"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_thermostatActual",b"_thermostatActual"]) -> typing_extensions___Literal["thermostatActual"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_thermostatConfig",b"_thermostatConfig"]) -> typing_extensions___Literal["thermostatConfig"]: ...
type___FanReply = FanReply

class RelayRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pc: builtin___bool = ...
    btl: builtin___bool = ...
    laser: builtin___bool = ...

    def __init__(self,
        *,
        pc : typing___Optional[builtin___bool] = None,
        btl : typing___Optional[builtin___bool] = None,
        laser : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_btl",b"_btl",u"_laser",b"_laser",u"_pc",b"_pc",u"btl",b"btl",u"laser",b"laser",u"pc",b"pc"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_btl",b"_btl",u"_laser",b"_laser",u"_pc",b"_pc",u"btl",b"btl",u"laser",b"laser",u"pc",b"pc"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_btl",b"_btl"]) -> typing_extensions___Literal["btl"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_laser",b"_laser"]) -> typing_extensions___Literal["laser"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_pc",b"_pc"]) -> typing_extensions___Literal["pc"]: ...
type___RelayRequest = RelayRequest

class RelayReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pc: builtin___bool = ...
    btl: builtin___bool = ...
    laser: builtin___bool = ...

    def __init__(self,
        *,
        pc : typing___Optional[builtin___bool] = None,
        btl : typing___Optional[builtin___bool] = None,
        laser : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"btl",b"btl",u"laser",b"laser",u"pc",b"pc"]) -> None: ...
type___RelayReply = RelayReply

class SensorRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SensorRequest = SensorRequest

class SensorReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class envdata(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        timestamp: builtin___int = ...
        temp: builtin___float = ...
        humidity: builtin___float = ...
        pressure: builtin___float = ...

        def __init__(self,
            *,
            timestamp : typing___Optional[builtin___int] = None,
            temp : typing___Optional[builtin___float] = None,
            humidity : typing___Optional[builtin___float] = None,
            pressure : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"humidity",b"humidity",u"pressure",b"pressure",u"temp",b"temp",u"timestamp",b"timestamp"]) -> None: ...
    type___envdata = envdata

    class imudata(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        timestamp: builtin___int = ...
        accel: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
        gyro: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...

        def __init__(self,
            *,
            timestamp : typing___Optional[builtin___int] = None,
            accel : typing___Optional[typing___Iterable[builtin___float]] = None,
            gyro : typing___Optional[typing___Iterable[builtin___float]] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"accel",b"accel",u"gyro",b"gyro",u"timestamp",b"timestamp"]) -> None: ...
    type___imudata = imudata

    class thermdata(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        timestamp: builtin___int = ...
        temp: builtin___float = ...

        def __init__(self,
            *,
            timestamp : typing___Optional[builtin___int] = None,
            temp : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"temp",b"temp",u"timestamp",b"timestamp"]) -> None: ...
    type___thermdata = thermdata

    class leakdata(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        timestamp: builtin___int = ...
        active: builtin___bool = ...

        def __init__(self,
            *,
            timestamp : typing___Optional[builtin___int] = None,
            active : typing___Optional[builtin___bool] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"active",b"active",u"timestamp",b"timestamp"]) -> None: ...
    type___leakdata = leakdata

    class pressdata(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        timestamp: builtin___int = ...
        pressure: builtin___float = ...
        temperature: builtin___float = ...

        def __init__(self,
            *,
            timestamp : typing___Optional[builtin___int] = None,
            pressure : typing___Optional[builtin___float] = None,
            temperature : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"pressure",b"pressure",u"temperature",b"temperature",u"timestamp",b"timestamp"]) -> None: ...
    type___pressdata = pressdata

    hasExternalEnv: builtin___bool = ...

    @property
    def env(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___SensorReply.envdata]: ...

    @property
    def imu(self) -> type___SensorReply.imudata: ...

    @property
    def therm(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___SensorReply.thermdata]: ...

    @property
    def leak(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___SensorReply.leakdata]: ...

    @property
    def press(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___SensorReply.pressdata]: ...

    def __init__(self,
        *,
        env : typing___Optional[typing___Iterable[type___SensorReply.envdata]] = None,
        imu : typing___Optional[type___SensorReply.imudata] = None,
        therm : typing___Optional[typing___Iterable[type___SensorReply.thermdata]] = None,
        leak : typing___Optional[typing___Iterable[type___SensorReply.leakdata]] = None,
        press : typing___Optional[typing___Iterable[type___SensorReply.pressdata]] = None,
        hasExternalEnv : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"imu",b"imu"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"env",b"env",u"hasExternalEnv",b"hasExternalEnv",u"imu",b"imu",u"leak",b"leak",u"press",b"press",u"therm",b"therm"]) -> None: ...
type___SensorReply = SensorReply

class StatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StatusRequest = StatusRequest

class StatusReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def sensors(self) -> type___SensorReply: ...

    @property
    def relays(self) -> type___RelayReply: ...

    @property
    def power(self) -> type___PowerReply: ...

    @property
    def strobe(self) -> type___StrobeStatusReply: ...

    @property
    def scanners(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ScannerStatus]: ...

    def __init__(self,
        *,
        sensors : typing___Optional[type___SensorReply] = None,
        relays : typing___Optional[type___RelayReply] = None,
        power : typing___Optional[type___PowerReply] = None,
        strobe : typing___Optional[type___StrobeStatusReply] = None,
        scanners : typing___Optional[typing___Iterable[type___ScannerStatus]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"power",b"power",u"relays",b"relays",u"sensors",b"sensors",u"strobe",b"strobe"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"power",b"power",u"relays",b"relays",u"scanners",b"scanners",u"sensors",b"sensors",u"strobe",b"strobe"]) -> None: ...
type___StatusReply = StatusReply

class CoreDumpStart(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___CoreDumpStart = CoreDumpStart

class CoreDumpEnd(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___CoreDumpEnd = CoreDumpEnd

class CoreDumpData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    is_last: builtin___bool = ...
    offset: builtin___int = ...
    data: builtin___bytes = ...

    def __init__(self,
        *,
        is_last : typing___Optional[builtin___bool] = None,
        offset : typing___Optional[builtin___int] = None,
        data : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data",u"is_last",b"is_last",u"offset",b"offset"]) -> None: ...
type___CoreDumpData = CoreDumpData

class CoreDumpReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def start(self) -> type___CoreDumpStart: ...

    @property
    def end(self) -> type___CoreDumpEnd: ...

    @property
    def data(self) -> type___CoreDumpData: ...

    def __init__(self,
        *,
        start : typing___Optional[type___CoreDumpStart] = None,
        end : typing___Optional[type___CoreDumpEnd] = None,
        data : typing___Optional[type___CoreDumpData] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"data",b"data",u"end",b"end",u"payload",b"payload",u"start",b"start"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data",u"end",b"end",u"payload",b"payload",u"start",b"start"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"payload",b"payload"]) -> typing_extensions___Literal["start","end","data"]: ...
type___CoreDumpReply = CoreDumpReply

class UdpRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Request: ...

    @property
    def reset(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request: ...

    @property
    def time(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Request: ...

    @property
    def sensor(self) -> type___SensorRequest: ...

    @property
    def relay(self) -> type___RelayRequest: ...

    @property
    def fan(self) -> type___FanRequest: ...

    @property
    def config(self) -> type___ConfigRequest: ...

    @property
    def strobe(self) -> type___StrobeRequest: ...

    @property
    def power(self) -> type___PowerRequest: ...

    @property
    def scanner(self) -> type___ScannerRequest: ...

    @property
    def network(self) -> type___NetworkRequest: ...

    @property
    def hwinfo(self) -> generated___lib___drivers___nanopb___proto___hwinfo_pb2___Request: ...

    @property
    def status(self) -> type___StatusRequest: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Request] = None,
        reset : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request] = None,
        time : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Request] = None,
        sensor : typing___Optional[type___SensorRequest] = None,
        relay : typing___Optional[type___RelayRequest] = None,
        fan : typing___Optional[type___FanRequest] = None,
        config : typing___Optional[type___ConfigRequest] = None,
        strobe : typing___Optional[type___StrobeRequest] = None,
        power : typing___Optional[type___PowerRequest] = None,
        scanner : typing___Optional[type___ScannerRequest] = None,
        network : typing___Optional[type___NetworkRequest] = None,
        hwinfo : typing___Optional[generated___lib___drivers___nanopb___proto___hwinfo_pb2___Request] = None,
        status : typing___Optional[type___StatusRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config",u"fan",b"fan",u"header",b"header",u"hwinfo",b"hwinfo",u"network",b"network",u"ping",b"ping",u"power",b"power",u"relay",b"relay",u"request",b"request",u"reset",b"reset",u"scanner",b"scanner",u"sensor",b"sensor",u"status",b"status",u"strobe",b"strobe",u"time",b"time",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"fan",b"fan",u"header",b"header",u"hwinfo",b"hwinfo",u"network",b"network",u"ping",b"ping",u"power",b"power",u"relay",b"relay",u"request",b"request",u"reset",b"reset",u"scanner",b"scanner",u"sensor",b"sensor",u"status",b"status",u"strobe",b"strobe",u"time",b"time",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","version","reset","time","sensor","relay","fan","config","strobe","power","scanner","network","hwinfo","status"]: ...
type___UdpRequest = UdpRequest

class UdpReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply: ...

    @property
    def time(self) -> generated___lib___drivers___nanopb___proto___time_pb2___Reply: ...

    @property
    def sensor(self) -> type___SensorReply: ...

    @property
    def relay(self) -> type___RelayReply: ...

    @property
    def fan(self) -> type___FanReply: ...

    @property
    def config(self) -> type___ConfigReply: ...

    @property
    def strobe(self) -> type___StrobeReply: ...

    @property
    def power(self) -> type___PowerReply: ...

    @property
    def scanner(self) -> type___ScannerReply: ...

    @property
    def network(self) -> type___NetworkReply: ...

    @property
    def hwinfo(self) -> generated___lib___drivers___nanopb___proto___hwinfo_pb2___Reply: ...

    @property
    def status(self) -> type___StatusReply: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply] = None,
        time : typing___Optional[generated___lib___drivers___nanopb___proto___time_pb2___Reply] = None,
        sensor : typing___Optional[type___SensorReply] = None,
        relay : typing___Optional[type___RelayReply] = None,
        fan : typing___Optional[type___FanReply] = None,
        config : typing___Optional[type___ConfigReply] = None,
        strobe : typing___Optional[type___StrobeReply] = None,
        power : typing___Optional[type___PowerReply] = None,
        scanner : typing___Optional[type___ScannerReply] = None,
        network : typing___Optional[type___NetworkReply] = None,
        hwinfo : typing___Optional[generated___lib___drivers___nanopb___proto___hwinfo_pb2___Reply] = None,
        status : typing___Optional[type___StatusReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"config",b"config",u"error",b"error",u"fan",b"fan",u"header",b"header",u"hwinfo",b"hwinfo",u"network",b"network",u"pong",b"pong",u"power",b"power",u"relay",b"relay",u"reply",b"reply",u"scanner",b"scanner",u"sensor",b"sensor",u"status",b"status",u"strobe",b"strobe",u"time",b"time",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"config",b"config",u"error",b"error",u"fan",b"fan",u"header",b"header",u"hwinfo",b"hwinfo",u"network",b"network",u"pong",b"pong",u"power",b"power",u"relay",b"relay",u"reply",b"reply",u"scanner",b"scanner",u"sensor",b"sensor",u"status",b"status",u"strobe",b"strobe",u"time",b"time",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","pong","version","time","sensor","relay","fan","config","strobe","power","scanner","network","hwinfo","status"]: ...
type___UdpReply = UdpReply

class OobRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Request: ...

    @property
    def reset(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request: ...

    @property
    def config(self) -> type___ConfigRequest: ...

    @property
    def network(self) -> type___NetworkRequest: ...

    @property
    def hwinfo(self) -> generated___lib___drivers___nanopb___proto___hwinfo_pb2___Request: ...

    @property
    def strobe(self) -> type___StrobeRequest: ...

    @property
    def power(self) -> type___PowerRequest: ...

    @property
    def relay(self) -> type___RelayRequest: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Request] = None,
        reset : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Reset_Request] = None,
        config : typing___Optional[type___ConfigRequest] = None,
        network : typing___Optional[type___NetworkRequest] = None,
        hwinfo : typing___Optional[generated___lib___drivers___nanopb___proto___hwinfo_pb2___Request] = None,
        strobe : typing___Optional[type___StrobeRequest] = None,
        power : typing___Optional[type___PowerRequest] = None,
        relay : typing___Optional[type___RelayRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config",u"header",b"header",u"hwinfo",b"hwinfo",u"network",b"network",u"ping",b"ping",u"power",b"power",u"relay",b"relay",u"request",b"request",u"reset",b"reset",u"strobe",b"strobe",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"header",b"header",u"hwinfo",b"hwinfo",u"network",b"network",u"ping",b"ping",u"power",b"power",u"relay",b"relay",u"request",b"request",u"reset",b"reset",u"strobe",b"strobe",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","version","reset","config","network","hwinfo","strobe","power","relay"]: ...
type___OobRequest = OobRequest

class OobReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply: ...

    @property
    def config(self) -> type___ConfigReply: ...

    @property
    def network(self) -> type___NetworkReply: ...

    @property
    def hwinfo(self) -> generated___lib___drivers___nanopb___proto___hwinfo_pb2___Reply: ...

    @property
    def strobe(self) -> type___StrobeReply: ...

    @property
    def power(self) -> type___PowerReply: ...

    @property
    def relay(self) -> type___RelayReply: ...

    @property
    def coredump(self) -> type___CoreDumpReply: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply] = None,
        config : typing___Optional[type___ConfigReply] = None,
        network : typing___Optional[type___NetworkReply] = None,
        hwinfo : typing___Optional[generated___lib___drivers___nanopb___proto___hwinfo_pb2___Reply] = None,
        strobe : typing___Optional[type___StrobeReply] = None,
        power : typing___Optional[type___PowerReply] = None,
        relay : typing___Optional[type___RelayReply] = None,
        coredump : typing___Optional[type___CoreDumpReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"config",b"config",u"coredump",b"coredump",u"error",b"error",u"header",b"header",u"hwinfo",b"hwinfo",u"network",b"network",u"pong",b"pong",u"power",b"power",u"relay",b"relay",u"reply",b"reply",u"strobe",b"strobe",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"config",b"config",u"coredump",b"coredump",u"error",b"error",u"header",b"header",u"hwinfo",b"hwinfo",u"network",b"network",u"pong",b"pong",u"power",b"power",u"relay",b"relay",u"reply",b"reply",u"strobe",b"strobe",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["error","ack","pong","version","config","network","hwinfo","strobe","power","relay","coredump"]: ...
type___OobReply = OobReply

/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_SLAYERTB_SIMULATOR_SLAYERTB_SIMULATOR_PB_H_INCLUDED
#define PB_SLAYERTB_SIMULATOR_SLAYERTB_SIMULATOR_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _slayertb_simulator_Sensor {
    slayertb_simulator_Sensor_LIFTED = 0,
    slayertb_simulator_Sensor_LEFT_ESTOP = 1,
    slayertb_simulator_Sensor_RIGHT_ESTOP = 2,
    slayertb_simulator_Sensor_CAB_ESTOP = 3,
    slayertb_simulator_Sensor_LASER_KEY = 4,
    slayertb_simulator_Sensor_LASER_INTERLOCK = 5,
    slayertb_simulator_Sensor_WATER_PROTECT = 6,
    slayertb_simulator_Sensor_TRACTOR_POWER = 7,
    slayertb_simulator_Sensor_TEMP = 8,
    slayertb_simulator_Sensor_HUMIDITY = 9,
    slayertb_simulator_Sensor_BATTERY_VOLTAGE = 10,
    slayertb_simulator_Sensor_CHILLER_RUN_SIGNAL = 11,
    slayertb_simulator_Sensor_CAB_ESTOP_SIGNAL = 12,
    slayertb_simulator_Sensor_BEACON_LEFT_SIGNAL = 13,
    slayertb_simulator_Sensor_BEACON_RIGHT_SIGNAL = 14
} slayertb_simulator_Sensor;

/* Struct definitions */
typedef struct _slayertb_simulator_SetValueReply {
    char dummy_field;
} slayertb_simulator_SetValueReply;

typedef struct _slayertb_simulator_UseTempSensorReply {
    char dummy_field;
} slayertb_simulator_UseTempSensorReply;

typedef struct _slayertb_simulator_GetValueReply {
    pb_size_t which_value;
    union {
        bool boolean_value;
        int32_t int_value;
    } value;
} slayertb_simulator_GetValueReply;

typedef struct _slayertb_simulator_GetValueRequest {
    slayertb_simulator_Sensor sensor;
} slayertb_simulator_GetValueRequest;

typedef struct _slayertb_simulator_SetValueRequest {
    slayertb_simulator_Sensor sensor;
    pb_size_t which_value;
    union {
        bool boolean_value;
        int32_t int_value;
    } value;
} slayertb_simulator_SetValueRequest;

typedef struct _slayertb_simulator_UseTempSensorRequest {
    bool on;
} slayertb_simulator_UseTempSensorRequest;

typedef struct _slayertb_simulator_Reply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        slayertb_simulator_GetValueReply get_value;
        slayertb_simulator_SetValueReply set_value;
        slayertb_simulator_UseTempSensorReply use_temp_sensor;
    } reply;
} slayertb_simulator_Reply;

typedef struct _slayertb_simulator_Request {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        slayertb_simulator_GetValueRequest get_value;
        slayertb_simulator_SetValueRequest set_value;
        slayertb_simulator_UseTempSensorRequest use_temp_sensor;
    } request;
} slayertb_simulator_Request;


/* Helper constants for enums */
#define _slayertb_simulator_Sensor_MIN slayertb_simulator_Sensor_LIFTED
#define _slayertb_simulator_Sensor_MAX slayertb_simulator_Sensor_BEACON_RIGHT_SIGNAL
#define _slayertb_simulator_Sensor_ARRAYSIZE ((slayertb_simulator_Sensor)(slayertb_simulator_Sensor_BEACON_RIGHT_SIGNAL+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define slayertb_simulator_GetValueRequest_init_default {_slayertb_simulator_Sensor_MIN}
#define slayertb_simulator_GetValueReply_init_default {0, {0}}
#define slayertb_simulator_SetValueRequest_init_default {_slayertb_simulator_Sensor_MIN, 0, {0}}
#define slayertb_simulator_SetValueReply_init_default {0}
#define slayertb_simulator_UseTempSensorRequest_init_default {0}
#define slayertb_simulator_UseTempSensorReply_init_default {0}
#define slayertb_simulator_Reply_init_default    {false, request_RequestHeader_init_default, 0, {diagnostic_Pong_init_default}}
#define slayertb_simulator_Request_init_default  {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define slayertb_simulator_GetValueRequest_init_zero {_slayertb_simulator_Sensor_MIN}
#define slayertb_simulator_GetValueReply_init_zero {0, {0}}
#define slayertb_simulator_SetValueRequest_init_zero {_slayertb_simulator_Sensor_MIN, 0, {0}}
#define slayertb_simulator_SetValueReply_init_zero {0}
#define slayertb_simulator_UseTempSensorRequest_init_zero {0}
#define slayertb_simulator_UseTempSensorReply_init_zero {0}
#define slayertb_simulator_Reply_init_zero       {false, request_RequestHeader_init_zero, 0, {diagnostic_Pong_init_zero}}
#define slayertb_simulator_Request_init_zero     {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define slayertb_simulator_GetValueReply_boolean_value_tag 1
#define slayertb_simulator_GetValueReply_int_value_tag 2
#define slayertb_simulator_GetValueRequest_sensor_tag 1
#define slayertb_simulator_SetValueRequest_sensor_tag 1
#define slayertb_simulator_SetValueRequest_boolean_value_tag 2
#define slayertb_simulator_SetValueRequest_int_value_tag 3
#define slayertb_simulator_UseTempSensorRequest_on_tag 1
#define slayertb_simulator_Reply_header_tag      1
#define slayertb_simulator_Reply_pong_tag        2
#define slayertb_simulator_Reply_get_value_tag   3
#define slayertb_simulator_Reply_set_value_tag   4
#define slayertb_simulator_Reply_use_temp_sensor_tag 5
#define slayertb_simulator_Request_header_tag    1
#define slayertb_simulator_Request_ping_tag      2
#define slayertb_simulator_Request_get_value_tag 3
#define slayertb_simulator_Request_set_value_tag 4
#define slayertb_simulator_Request_use_temp_sensor_tag 5

/* Struct field encoding specification for nanopb */
#define slayertb_simulator_GetValueRequest_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    sensor,            1)
#define slayertb_simulator_GetValueRequest_CALLBACK NULL
#define slayertb_simulator_GetValueRequest_DEFAULT NULL

#define slayertb_simulator_GetValueReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    BOOL,     (value,boolean_value,value.boolean_value),   1) \
X(a, STATIC,   ONEOF,    INT32,    (value,int_value,value.int_value),   2)
#define slayertb_simulator_GetValueReply_CALLBACK NULL
#define slayertb_simulator_GetValueReply_DEFAULT NULL

#define slayertb_simulator_SetValueRequest_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    sensor,            1) \
X(a, STATIC,   ONEOF,    BOOL,     (value,boolean_value,value.boolean_value),   2) \
X(a, STATIC,   ONEOF,    INT32,    (value,int_value,value.int_value),   3)
#define slayertb_simulator_SetValueRequest_CALLBACK NULL
#define slayertb_simulator_SetValueRequest_DEFAULT NULL

#define slayertb_simulator_SetValueReply_FIELDLIST(X, a) \

#define slayertb_simulator_SetValueReply_CALLBACK NULL
#define slayertb_simulator_SetValueReply_DEFAULT NULL

#define slayertb_simulator_UseTempSensorRequest_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     on,                1)
#define slayertb_simulator_UseTempSensorRequest_CALLBACK NULL
#define slayertb_simulator_UseTempSensorRequest_DEFAULT NULL

#define slayertb_simulator_UseTempSensorReply_FIELDLIST(X, a) \

#define slayertb_simulator_UseTempSensorReply_CALLBACK NULL
#define slayertb_simulator_UseTempSensorReply_DEFAULT NULL

#define slayertb_simulator_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,get_value,reply.get_value),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,set_value,reply.set_value),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,use_temp_sensor,reply.use_temp_sensor),   5)
#define slayertb_simulator_Reply_CALLBACK NULL
#define slayertb_simulator_Reply_DEFAULT NULL
#define slayertb_simulator_Reply_header_MSGTYPE request_RequestHeader
#define slayertb_simulator_Reply_reply_pong_MSGTYPE diagnostic_Pong
#define slayertb_simulator_Reply_reply_get_value_MSGTYPE slayertb_simulator_GetValueReply
#define slayertb_simulator_Reply_reply_set_value_MSGTYPE slayertb_simulator_SetValueReply
#define slayertb_simulator_Reply_reply_use_temp_sensor_MSGTYPE slayertb_simulator_UseTempSensorReply

#define slayertb_simulator_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get_value,request.get_value),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set_value,request.set_value),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,use_temp_sensor,request.use_temp_sensor),   5)
#define slayertb_simulator_Request_CALLBACK NULL
#define slayertb_simulator_Request_DEFAULT NULL
#define slayertb_simulator_Request_header_MSGTYPE request_RequestHeader
#define slayertb_simulator_Request_request_ping_MSGTYPE diagnostic_Ping
#define slayertb_simulator_Request_request_get_value_MSGTYPE slayertb_simulator_GetValueRequest
#define slayertb_simulator_Request_request_set_value_MSGTYPE slayertb_simulator_SetValueRequest
#define slayertb_simulator_Request_request_use_temp_sensor_MSGTYPE slayertb_simulator_UseTempSensorRequest

extern const pb_msgdesc_t slayertb_simulator_GetValueRequest_msg;
extern const pb_msgdesc_t slayertb_simulator_GetValueReply_msg;
extern const pb_msgdesc_t slayertb_simulator_SetValueRequest_msg;
extern const pb_msgdesc_t slayertb_simulator_SetValueReply_msg;
extern const pb_msgdesc_t slayertb_simulator_UseTempSensorRequest_msg;
extern const pb_msgdesc_t slayertb_simulator_UseTempSensorReply_msg;
extern const pb_msgdesc_t slayertb_simulator_Reply_msg;
extern const pb_msgdesc_t slayertb_simulator_Request_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define slayertb_simulator_GetValueRequest_fields &slayertb_simulator_GetValueRequest_msg
#define slayertb_simulator_GetValueReply_fields &slayertb_simulator_GetValueReply_msg
#define slayertb_simulator_SetValueRequest_fields &slayertb_simulator_SetValueRequest_msg
#define slayertb_simulator_SetValueReply_fields &slayertb_simulator_SetValueReply_msg
#define slayertb_simulator_UseTempSensorRequest_fields &slayertb_simulator_UseTempSensorRequest_msg
#define slayertb_simulator_UseTempSensorReply_fields &slayertb_simulator_UseTempSensorReply_msg
#define slayertb_simulator_Reply_fields &slayertb_simulator_Reply_msg
#define slayertb_simulator_Request_fields &slayertb_simulator_Request_msg

/* Maximum encoded size of messages (where known) */
#define slayertb_simulator_GetValueRequest_size  2
#define slayertb_simulator_GetValueReply_size    11
#define slayertb_simulator_SetValueRequest_size  13
#define slayertb_simulator_SetValueReply_size    0
#define slayertb_simulator_UseTempSensorRequest_size 2
#define slayertb_simulator_UseTempSensorReply_size 0
#if defined(request_RequestHeader_size) && defined(diagnostic_Pong_size)
typedef union slayertb_simulator_Reply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f0[13];} slayertb_simulator_Reply_reply_size_union;
#define slayertb_simulator_Reply_size            (6 + request_RequestHeader_size + sizeof(slayertb_simulator_Reply_reply_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size)
typedef union slayertb_simulator_Request_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f0[15];} slayertb_simulator_Request_request_size_union;
#define slayertb_simulator_Request_size          (6 + request_RequestHeader_size + sizeof(slayertb_simulator_Request_request_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.diagnostic_pb2 import (
    Ping as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping,
    Pong as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong,
)

from generated.lib.drivers.nanopb.proto.error_pb2 import (
    Error as generated___lib___drivers___nanopb___proto___error_pb2___Error,
)

from generated.lib.drivers.nanopb.proto.gps_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___gps_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___gps_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.heading_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___heading_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___heading_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.hwinfo_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___hwinfo_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___hwinfo_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.request_pb2 import (
    RequestHeader as generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class PtpConfig_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    is_master: builtin___bool = ...

    def __init__(self,
        *,
        is_master : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"is_master",b"is_master"]) -> None: ...
type___PtpConfig_Request = PtpConfig_Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def gps(self) -> generated___lib___drivers___nanopb___proto___gps_pb2___Reply: ...

    @property
    def heading(self) -> generated___lib___drivers___nanopb___proto___heading_pb2___Reply: ...

    @property
    def hwinfo(self) -> generated___lib___drivers___nanopb___proto___hwinfo_pb2___Reply: ...

    @property
    def error(self) -> generated___lib___drivers___nanopb___proto___error_pb2___Error: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        gps : typing___Optional[generated___lib___drivers___nanopb___proto___gps_pb2___Reply] = None,
        heading : typing___Optional[generated___lib___drivers___nanopb___proto___heading_pb2___Reply] = None,
        hwinfo : typing___Optional[generated___lib___drivers___nanopb___proto___hwinfo_pb2___Reply] = None,
        error : typing___Optional[generated___lib___drivers___nanopb___proto___error_pb2___Error] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"gps",b"gps",u"header",b"header",u"heading",b"heading",u"hwinfo",b"hwinfo",u"pong",b"pong",u"reply",b"reply"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"error",b"error",u"gps",b"gps",u"header",b"header",u"heading",b"heading",u"hwinfo",b"hwinfo",u"pong",b"pong",u"reply",b"reply"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["pong","gps","heading","hwinfo","error","ack"]: ...
type___Reply = Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def gps(self) -> generated___lib___drivers___nanopb___proto___gps_pb2___Request: ...

    @property
    def heading(self) -> generated___lib___drivers___nanopb___proto___heading_pb2___Request: ...

    @property
    def hwinfo(self) -> generated___lib___drivers___nanopb___proto___hwinfo_pb2___Request: ...

    @property
    def ptp_config(self) -> type___PtpConfig_Request: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        gps : typing___Optional[generated___lib___drivers___nanopb___proto___gps_pb2___Request] = None,
        heading : typing___Optional[generated___lib___drivers___nanopb___proto___heading_pb2___Request] = None,
        hwinfo : typing___Optional[generated___lib___drivers___nanopb___proto___hwinfo_pb2___Request] = None,
        ptp_config : typing___Optional[type___PtpConfig_Request] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"gps",b"gps",u"header",b"header",u"heading",b"heading",u"hwinfo",b"hwinfo",u"ping",b"ping",u"ptp_config",b"ptp_config",u"request",b"request"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"gps",b"gps",u"header",b"header",u"heading",b"heading",u"hwinfo",b"hwinfo",u"ping",b"ping",u"ptp_config",b"ptp_config",u"request",b"request"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","gps","heading","hwinfo","ptp_config"]: ...
type___Request = Request

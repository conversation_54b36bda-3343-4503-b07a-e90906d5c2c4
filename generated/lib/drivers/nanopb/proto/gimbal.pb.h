/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_GIMBAL_GIMBAL_PB_H_INCLUDED
#define PB_GIMBAL_GIMBAL_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/servo.pb.h"
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"
#include "generated/lib/drivers/nanopb/proto/epos.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _gimbal_Stop_Request {
    char dummy_field;
} gimbal_Stop_Request;

typedef struct _gimbal_Boot_Request {
    bool has_pan_params;
    epos_Home_Params pan_params;
    bool has_tilt_params;
    epos_Home_Params tilt_params;
} gimbal_Boot_Request;

typedef struct _gimbal_GetPositionAtRequest {
    uint64_t timestamp_us;
} gimbal_GetPositionAtRequest;

typedef struct _gimbal_PositionAt {
    int32_t pan;
    int32_t tilt;
    uint64_t timestamp_us;
    bool valid;
} gimbal_PositionAt;

typedef struct _gimbal_Servos_Reply {
    bool has_pan;
    servo_Reply pan;
    bool has_tilt;
    servo_Reply tilt;
} gimbal_Servos_Reply;

typedef struct _gimbal_Servos_Request {
    bool has_pan;
    servo_Request pan;
    bool has_tilt;
    servo_Request tilt;
} gimbal_Servos_Request;

typedef struct _gimbal_GetPositionAtReply {
    bool has_requested;
    gimbal_PositionAt requested;
    bool has_current;
    gimbal_PositionAt current;
} gimbal_GetPositionAtReply;

typedef struct _gimbal_Request {
    pb_size_t which_request;
    union {
        gimbal_Boot_Request boot;
        gimbal_Stop_Request stop;
        gimbal_Servos_Request servos;
        gimbal_GetPositionAtRequest position;
    } request;
} gimbal_Request;

typedef struct _gimbal_Reply {
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        gimbal_Servos_Reply servos;
        gimbal_GetPositionAtReply position;
    } reply;
} gimbal_Reply;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define gimbal_Boot_Request_init_default         {false, epos_Home_Params_init_default, false, epos_Home_Params_init_default}
#define gimbal_Stop_Request_init_default         {0}
#define gimbal_Servos_Request_init_default       {false, servo_Request_init_default, false, servo_Request_init_default}
#define gimbal_GetPositionAtRequest_init_default {0}
#define gimbal_Servos_Reply_init_default         {false, servo_Reply_init_default, false, servo_Reply_init_default}
#define gimbal_PositionAt_init_default           {0, 0, 0, 0}
#define gimbal_GetPositionAtReply_init_default   {false, gimbal_PositionAt_init_default, false, gimbal_PositionAt_init_default}
#define gimbal_Request_init_default              {0, {gimbal_Boot_Request_init_default}}
#define gimbal_Reply_init_default                {0, {error_Error_init_default}}
#define gimbal_Boot_Request_init_zero            {false, epos_Home_Params_init_zero, false, epos_Home_Params_init_zero}
#define gimbal_Stop_Request_init_zero            {0}
#define gimbal_Servos_Request_init_zero          {false, servo_Request_init_zero, false, servo_Request_init_zero}
#define gimbal_GetPositionAtRequest_init_zero    {0}
#define gimbal_Servos_Reply_init_zero            {false, servo_Reply_init_zero, false, servo_Reply_init_zero}
#define gimbal_PositionAt_init_zero              {0, 0, 0, 0}
#define gimbal_GetPositionAtReply_init_zero      {false, gimbal_PositionAt_init_zero, false, gimbal_PositionAt_init_zero}
#define gimbal_Request_init_zero                 {0, {gimbal_Boot_Request_init_zero}}
#define gimbal_Reply_init_zero                   {0, {error_Error_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define gimbal_Boot_Request_pan_params_tag       1
#define gimbal_Boot_Request_tilt_params_tag      2
#define gimbal_GetPositionAtRequest_timestamp_us_tag 1
#define gimbal_PositionAt_pan_tag                1
#define gimbal_PositionAt_tilt_tag               2
#define gimbal_PositionAt_timestamp_us_tag       3
#define gimbal_PositionAt_valid_tag              4
#define gimbal_Servos_Reply_pan_tag              1
#define gimbal_Servos_Reply_tilt_tag             2
#define gimbal_Servos_Request_pan_tag            1
#define gimbal_Servos_Request_tilt_tag           2
#define gimbal_GetPositionAtReply_requested_tag  1
#define gimbal_GetPositionAtReply_current_tag    2
#define gimbal_Request_boot_tag                  1
#define gimbal_Request_stop_tag                  2
#define gimbal_Request_servos_tag                3
#define gimbal_Request_position_tag              4
#define gimbal_Reply_error_tag                   1
#define gimbal_Reply_ack_tag                     2
#define gimbal_Reply_servos_tag                  3
#define gimbal_Reply_position_tag                4

/* Struct field encoding specification for nanopb */
#define gimbal_Boot_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pan_params,        1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  tilt_params,       2)
#define gimbal_Boot_Request_CALLBACK NULL
#define gimbal_Boot_Request_DEFAULT NULL
#define gimbal_Boot_Request_pan_params_MSGTYPE epos_Home_Params
#define gimbal_Boot_Request_tilt_params_MSGTYPE epos_Home_Params

#define gimbal_Stop_Request_FIELDLIST(X, a) \

#define gimbal_Stop_Request_CALLBACK NULL
#define gimbal_Stop_Request_DEFAULT NULL

#define gimbal_Servos_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pan,               1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  tilt,              2)
#define gimbal_Servos_Request_CALLBACK NULL
#define gimbal_Servos_Request_DEFAULT NULL
#define gimbal_Servos_Request_pan_MSGTYPE servo_Request
#define gimbal_Servos_Request_tilt_MSGTYPE servo_Request

#define gimbal_GetPositionAtRequest_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp_us,      1)
#define gimbal_GetPositionAtRequest_CALLBACK NULL
#define gimbal_GetPositionAtRequest_DEFAULT NULL

#define gimbal_Servos_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  pan,               1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  tilt,              2)
#define gimbal_Servos_Reply_CALLBACK NULL
#define gimbal_Servos_Reply_DEFAULT NULL
#define gimbal_Servos_Reply_pan_MSGTYPE servo_Reply
#define gimbal_Servos_Reply_tilt_MSGTYPE servo_Reply

#define gimbal_PositionAt_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    pan,               1) \
X(a, STATIC,   SINGULAR, INT32,    tilt,              2) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp_us,      3) \
X(a, STATIC,   SINGULAR, BOOL,     valid,             4)
#define gimbal_PositionAt_CALLBACK NULL
#define gimbal_PositionAt_DEFAULT NULL

#define gimbal_GetPositionAtReply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  requested,         1) \
X(a, STATIC,   OPTIONAL, MESSAGE,  current,           2)
#define gimbal_GetPositionAtReply_CALLBACK NULL
#define gimbal_GetPositionAtReply_DEFAULT NULL
#define gimbal_GetPositionAtReply_requested_MSGTYPE gimbal_PositionAt
#define gimbal_GetPositionAtReply_current_MSGTYPE gimbal_PositionAt

#define gimbal_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,boot,request.boot),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,stop,request.stop),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,servos,request.servos),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,position,request.position),   4)
#define gimbal_Request_CALLBACK NULL
#define gimbal_Request_DEFAULT NULL
#define gimbal_Request_request_boot_MSGTYPE gimbal_Boot_Request
#define gimbal_Request_request_stop_MSGTYPE gimbal_Stop_Request
#define gimbal_Request_request_servos_MSGTYPE gimbal_Servos_Request
#define gimbal_Request_request_position_MSGTYPE gimbal_GetPositionAtRequest

#define gimbal_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,servos,reply.servos),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,position,reply.position),   4)
#define gimbal_Reply_CALLBACK NULL
#define gimbal_Reply_DEFAULT NULL
#define gimbal_Reply_reply_error_MSGTYPE error_Error
#define gimbal_Reply_reply_ack_MSGTYPE ack_Ack
#define gimbal_Reply_reply_servos_MSGTYPE gimbal_Servos_Reply
#define gimbal_Reply_reply_position_MSGTYPE gimbal_GetPositionAtReply

extern const pb_msgdesc_t gimbal_Boot_Request_msg;
extern const pb_msgdesc_t gimbal_Stop_Request_msg;
extern const pb_msgdesc_t gimbal_Servos_Request_msg;
extern const pb_msgdesc_t gimbal_GetPositionAtRequest_msg;
extern const pb_msgdesc_t gimbal_Servos_Reply_msg;
extern const pb_msgdesc_t gimbal_PositionAt_msg;
extern const pb_msgdesc_t gimbal_GetPositionAtReply_msg;
extern const pb_msgdesc_t gimbal_Request_msg;
extern const pb_msgdesc_t gimbal_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define gimbal_Boot_Request_fields &gimbal_Boot_Request_msg
#define gimbal_Stop_Request_fields &gimbal_Stop_Request_msg
#define gimbal_Servos_Request_fields &gimbal_Servos_Request_msg
#define gimbal_GetPositionAtRequest_fields &gimbal_GetPositionAtRequest_msg
#define gimbal_Servos_Reply_fields &gimbal_Servos_Reply_msg
#define gimbal_PositionAt_fields &gimbal_PositionAt_msg
#define gimbal_GetPositionAtReply_fields &gimbal_GetPositionAtReply_msg
#define gimbal_Request_fields &gimbal_Request_msg
#define gimbal_Reply_fields &gimbal_Reply_msg

/* Maximum encoded size of messages (where known) */
#if defined(epos_Home_Params_size) && defined(epos_Home_Params_size)
#define gimbal_Boot_Request_size                 (12 + epos_Home_Params_size + epos_Home_Params_size)
#endif
#define gimbal_Stop_Request_size                 0
#if defined(servo_Request_size) && defined(servo_Request_size)
#define gimbal_Servos_Request_size               (12 + servo_Request_size + servo_Request_size)
#endif
#define gimbal_GetPositionAtRequest_size         11
#if defined(servo_Reply_size) && defined(servo_Reply_size)
#define gimbal_Servos_Reply_size                 (12 + servo_Reply_size + servo_Reply_size)
#endif
#define gimbal_PositionAt_size                   35
#define gimbal_GetPositionAtReply_size           74
#if defined(epos_Home_Params_size) && defined(epos_Home_Params_size) && defined(servo_Request_size) && defined(servo_Request_size)
typedef union gimbal_Request_request_size_union {char f1[(18 + epos_Home_Params_size + epos_Home_Params_size)]; char f3[(18 + servo_Request_size + servo_Request_size)]; char f0[13];} gimbal_Request_request_size_union;
#define gimbal_Request_size                      (0 + sizeof(gimbal_Request_request_size_union))
#endif
#if defined(error_Error_size) && defined(ack_Ack_size) && defined(servo_Reply_size) && defined(servo_Reply_size)
typedef union gimbal_Reply_reply_size_union {char f1[(6 + error_Error_size)]; char f2[(6 + ack_Ack_size)]; char f3[(18 + servo_Reply_size + servo_Reply_size)]; char f0[76];} gimbal_Reply_reply_size_union;
#define gimbal_Reply_size                        (0 + sizeof(gimbal_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_HEADING_HEADING_PB_H_INCLUDED
#define PB_HEADING_HEADING_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _heading_Request {
    char dummy_field;
} heading_Request;

typedef struct _heading_Reply {
    bool have_fix;
    double heading_deg;
    double accuracy_deg;
    uint64_t timestamp_ms;
    bool have_approx_fix;
} heading_Reply;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define heading_Request_init_default             {0}
#define heading_Reply_init_default               {0, 0, 0, 0, 0}
#define heading_Request_init_zero                {0}
#define heading_Reply_init_zero                  {0, 0, 0, 0, 0}

/* Field tags (for use in manual encoding/decoding) */
#define heading_Reply_have_fix_tag               1
#define heading_Reply_heading_deg_tag            2
#define heading_Reply_accuracy_deg_tag           3
#define heading_Reply_timestamp_ms_tag           4
#define heading_Reply_have_approx_fix_tag        5

/* Struct field encoding specification for nanopb */
#define heading_Request_FIELDLIST(X, a) \

#define heading_Request_CALLBACK NULL
#define heading_Request_DEFAULT NULL

#define heading_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     have_fix,          1) \
X(a, STATIC,   SINGULAR, DOUBLE,   heading_deg,       2) \
X(a, STATIC,   SINGULAR, DOUBLE,   accuracy_deg,      3) \
X(a, STATIC,   SINGULAR, UINT64,   timestamp_ms,      4) \
X(a, STATIC,   SINGULAR, BOOL,     have_approx_fix,   5)
#define heading_Reply_CALLBACK NULL
#define heading_Reply_DEFAULT NULL

extern const pb_msgdesc_t heading_Request_msg;
extern const pb_msgdesc_t heading_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define heading_Request_fields &heading_Request_msg
#define heading_Reply_fields &heading_Reply_msg

/* Maximum encoded size of messages (where known) */
#define heading_Request_size                     0
#define heading_Reply_size                       33

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_JIMBOX_BOARD_JIMBOX_BOARD_PB_H_INCLUDED
#define PB_JIMBOX_BOARD_JIMBOX_BOARD_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"
#include "generated/lib/drivers/nanopb/proto/cruise.pb.h"
#include "generated/lib/drivers/nanopb/proto/version.pb.h"
#include "generated/lib/drivers/nanopb/proto/time.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _jimbox_board_Reply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        cruise_Reply cruise;
        version_Version_Reply version;
        time_Reply time;
    } reply;
} jimbox_board_Reply;

typedef struct _jimbox_board_Request {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        cruise_Request cruise;
        version_Version_Request version;
        version_Reset_Request reset;
        time_Request time;
    } request;
} jimbox_board_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define jimbox_board_Reply_init_default          {false, request_RequestHeader_init_default, 0, {diagnostic_Pong_init_default}}
#define jimbox_board_Request_init_default        {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define jimbox_board_Reply_init_zero             {false, request_RequestHeader_init_zero, 0, {diagnostic_Pong_init_zero}}
#define jimbox_board_Request_init_zero           {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define jimbox_board_Reply_header_tag            1
#define jimbox_board_Reply_pong_tag              2
#define jimbox_board_Reply_cruise_tag            3
#define jimbox_board_Reply_version_tag           4
#define jimbox_board_Reply_time_tag              5
#define jimbox_board_Request_header_tag          1
#define jimbox_board_Request_ping_tag            2
#define jimbox_board_Request_cruise_tag          3
#define jimbox_board_Request_version_tag         4
#define jimbox_board_Request_reset_tag           5
#define jimbox_board_Request_time_tag            6

/* Struct field encoding specification for nanopb */
#define jimbox_board_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,cruise,reply.cruise),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,version,reply.version),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,time,reply.time),   5)
#define jimbox_board_Reply_CALLBACK NULL
#define jimbox_board_Reply_DEFAULT NULL
#define jimbox_board_Reply_header_MSGTYPE request_RequestHeader
#define jimbox_board_Reply_reply_pong_MSGTYPE diagnostic_Pong
#define jimbox_board_Reply_reply_cruise_MSGTYPE cruise_Reply
#define jimbox_board_Reply_reply_version_MSGTYPE version_Version_Reply
#define jimbox_board_Reply_reply_time_MSGTYPE time_Reply

#define jimbox_board_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,cruise,request.cruise),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,version,request.version),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,time,request.time),   6)
#define jimbox_board_Request_CALLBACK NULL
#define jimbox_board_Request_DEFAULT NULL
#define jimbox_board_Request_header_MSGTYPE request_RequestHeader
#define jimbox_board_Request_request_ping_MSGTYPE diagnostic_Ping
#define jimbox_board_Request_request_cruise_MSGTYPE cruise_Request
#define jimbox_board_Request_request_version_MSGTYPE version_Version_Request
#define jimbox_board_Request_request_reset_MSGTYPE version_Reset_Request
#define jimbox_board_Request_request_time_MSGTYPE time_Request

extern const pb_msgdesc_t jimbox_board_Reply_msg;
extern const pb_msgdesc_t jimbox_board_Request_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define jimbox_board_Reply_fields &jimbox_board_Reply_msg
#define jimbox_board_Request_fields &jimbox_board_Request_msg

/* Maximum encoded size of messages (where known) */
#if defined(request_RequestHeader_size) && defined(diagnostic_Pong_size) && defined(cruise_Reply_size) && defined(version_Version_Reply_size) && defined(time_Reply_size)
typedef union jimbox_board_Reply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f3[(6 + cruise_Reply_size)]; char f4[(6 + version_Version_Reply_size)]; char f5[(6 + time_Reply_size)];} jimbox_board_Reply_reply_size_union;
#define jimbox_board_Reply_size                  (6 + request_RequestHeader_size + sizeof(jimbox_board_Reply_reply_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size) && defined(cruise_Request_size) && defined(version_Version_Request_size) && defined(version_Reset_Request_size) && defined(time_Request_size)
typedef union jimbox_board_Request_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f3[(6 + cruise_Request_size)]; char f4[(6 + version_Version_Request_size)]; char f5[(6 + version_Reset_Request_size)]; char f6[(6 + time_Request_size)];} jimbox_board_Request_request_size_union;
#define jimbox_board_Request_size                (6 + request_RequestHeader_size + sizeof(jimbox_board_Request_request_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

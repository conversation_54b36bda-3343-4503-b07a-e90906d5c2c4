/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_CAMERA_POWER_CONTROL_CAMERA_POWER_CONTROL_PB_H_INCLUDED
#define PB_CAMERA_POWER_CONTROL_CAMERA_POWER_CONTROL_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _camera_power_control_Reply {
    bool ok;
} camera_power_control_Reply;

typedef struct _camera_power_control_Request {
    uint32_t camera_id;
    bool power_on;
} camera_power_control_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define camera_power_control_Request_init_default {0, 0}
#define camera_power_control_Reply_init_default  {0}
#define camera_power_control_Request_init_zero   {0, 0}
#define camera_power_control_Reply_init_zero     {0}

/* Field tags (for use in manual encoding/decoding) */
#define camera_power_control_Reply_ok_tag        1
#define camera_power_control_Request_camera_id_tag 1
#define camera_power_control_Request_power_on_tag 2

/* Struct field encoding specification for nanopb */
#define camera_power_control_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   camera_id,         1) \
X(a, STATIC,   SINGULAR, BOOL,     power_on,          2)
#define camera_power_control_Request_CALLBACK NULL
#define camera_power_control_Request_DEFAULT NULL

#define camera_power_control_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     ok,                1)
#define camera_power_control_Reply_CALLBACK NULL
#define camera_power_control_Reply_DEFAULT NULL

extern const pb_msgdesc_t camera_power_control_Request_msg;
extern const pb_msgdesc_t camera_power_control_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define camera_power_control_Request_fields &camera_power_control_Request_msg
#define camera_power_control_Reply_fields &camera_power_control_Reply_msg

/* Maximum encoded size of messages (where known) */
#define camera_power_control_Request_size        8
#define camera_power_control_Reply_size          2

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

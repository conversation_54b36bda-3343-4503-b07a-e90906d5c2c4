# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/time.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2
from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/time.proto',
  package='time',
  syntax='proto3',
  serialized_options=b'Z\013nanopb/time',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n-generated/lib/drivers/nanopb/proto/time.proto\x12\x04time\x1a,generated/lib/drivers/nanopb/proto/ack.proto\x1a.generated/lib/drivers/nanopb/proto/error.proto\",\n\tTimestamp\x12\x0f\n\x07seconds\x18\x01 \x01(\r\x12\x0e\n\x06micros\x18\x02 \x01(\r\"<\n\x16Set_Epoch_Time_Request\x12\"\n\ttimestamp\x18\x01 \x01(\x0b\x32\x0f.time.Timestamp\"\x17\n\x15Get_Timestamp_Request\"\x1d\n\x1bGet_Debug_Timestamp_Request\"\xa6\x01\n\x19Get_Debug_Timestamp_Reply\x12\"\n\ttimestamp\x18\x01 \x01(\x0b\x32\x0f.time.Timestamp\x12\x15\n\rpps_timer_val\x18\x02 \x01(\r\x12\x11\n\tpps_ticks\x18\x03 \x01(\r\x12\x10\n\x08\x66req_mul\x18\x04 \x01(\x01\x12\x13\n\x0b\x65rror_ticks\x18\x05 \x01(\x05\x12\x14\n\x0c\x65rror_ticks2\x18\x06 \x01(\x05\"\xa1\x01\n\x07Request\x12+\n\x03set\x18\x01 \x01(\x0b\x32\x1c.time.Set_Epoch_Time_RequestH\x00\x12*\n\x03get\x18\x02 \x01(\x0b\x32\x1b.time.Get_Timestamp_RequestH\x00\x12\x32\n\x05\x64\x65\x62ug\x18\x03 \x01(\x0b\x32!.time.Get_Debug_Timestamp_RequestH\x00\x42\t\n\x07request\"\xa0\x01\n\x05Reply\x12\x1d\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x02 \x01(\x0b\x32\x08.ack.AckH\x00\x12$\n\ttimestamp\x18\x03 \x01(\x0b\x32\x0f.time.TimestampH\x00\x12\x30\n\x05\x64\x65\x62ug\x18\x04 \x01(\x0b\x32\x1f.time.Get_Debug_Timestamp_ReplyH\x00\x42\x07\n\x05replyB\rZ\x0bnanopb/timeb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,])




_TIMESTAMP = _descriptor.Descriptor(
  name='Timestamp',
  full_name='time.Timestamp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='seconds', full_name='time.Timestamp.seconds', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='micros', full_name='time.Timestamp.micros', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=149,
  serialized_end=193,
)


_SET_EPOCH_TIME_REQUEST = _descriptor.Descriptor(
  name='Set_Epoch_Time_Request',
  full_name='time.Set_Epoch_Time_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='time.Set_Epoch_Time_Request.timestamp', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=195,
  serialized_end=255,
)


_GET_TIMESTAMP_REQUEST = _descriptor.Descriptor(
  name='Get_Timestamp_Request',
  full_name='time.Get_Timestamp_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=257,
  serialized_end=280,
)


_GET_DEBUG_TIMESTAMP_REQUEST = _descriptor.Descriptor(
  name='Get_Debug_Timestamp_Request',
  full_name='time.Get_Debug_Timestamp_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=282,
  serialized_end=311,
)


_GET_DEBUG_TIMESTAMP_REPLY = _descriptor.Descriptor(
  name='Get_Debug_Timestamp_Reply',
  full_name='time.Get_Debug_Timestamp_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='time.Get_Debug_Timestamp_Reply.timestamp', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pps_timer_val', full_name='time.Get_Debug_Timestamp_Reply.pps_timer_val', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pps_ticks', full_name='time.Get_Debug_Timestamp_Reply.pps_ticks', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='freq_mul', full_name='time.Get_Debug_Timestamp_Reply.freq_mul', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_ticks', full_name='time.Get_Debug_Timestamp_Reply.error_ticks', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_ticks2', full_name='time.Get_Debug_Timestamp_Reply.error_ticks2', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=314,
  serialized_end=480,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='time.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='set', full_name='time.Request.set', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get', full_name='time.Request.get', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='debug', full_name='time.Request.debug', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='time.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=483,
  serialized_end=644,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='time.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='time.Reply.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='time.Reply.ack', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='time.Reply.timestamp', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='debug', full_name='time.Reply.debug', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='time.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=647,
  serialized_end=807,
)

_SET_EPOCH_TIME_REQUEST.fields_by_name['timestamp'].message_type = _TIMESTAMP
_GET_DEBUG_TIMESTAMP_REPLY.fields_by_name['timestamp'].message_type = _TIMESTAMP
_REQUEST.fields_by_name['set'].message_type = _SET_EPOCH_TIME_REQUEST
_REQUEST.fields_by_name['get'].message_type = _GET_TIMESTAMP_REQUEST
_REQUEST.fields_by_name['debug'].message_type = _GET_DEBUG_TIMESTAMP_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set'])
_REQUEST.fields_by_name['set'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get'])
_REQUEST.fields_by_name['get'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['debug'])
_REQUEST.fields_by_name['debug'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['timestamp'].message_type = _TIMESTAMP
_REPLY.fields_by_name['debug'].message_type = _GET_DEBUG_TIMESTAMP_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['timestamp'])
_REPLY.fields_by_name['timestamp'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['debug'])
_REPLY.fields_by_name['debug'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Timestamp'] = _TIMESTAMP
DESCRIPTOR.message_types_by_name['Set_Epoch_Time_Request'] = _SET_EPOCH_TIME_REQUEST
DESCRIPTOR.message_types_by_name['Get_Timestamp_Request'] = _GET_TIMESTAMP_REQUEST
DESCRIPTOR.message_types_by_name['Get_Debug_Timestamp_Request'] = _GET_DEBUG_TIMESTAMP_REQUEST
DESCRIPTOR.message_types_by_name['Get_Debug_Timestamp_Reply'] = _GET_DEBUG_TIMESTAMP_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Timestamp = _reflection.GeneratedProtocolMessageType('Timestamp', (_message.Message,), {
  'DESCRIPTOR' : _TIMESTAMP,
  '__module__' : 'generated.lib.drivers.nanopb.proto.time_pb2'
  # @@protoc_insertion_point(class_scope:time.Timestamp)
  })
_sym_db.RegisterMessage(Timestamp)

Set_Epoch_Time_Request = _reflection.GeneratedProtocolMessageType('Set_Epoch_Time_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_EPOCH_TIME_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.time_pb2'
  # @@protoc_insertion_point(class_scope:time.Set_Epoch_Time_Request)
  })
_sym_db.RegisterMessage(Set_Epoch_Time_Request)

Get_Timestamp_Request = _reflection.GeneratedProtocolMessageType('Get_Timestamp_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_TIMESTAMP_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.time_pb2'
  # @@protoc_insertion_point(class_scope:time.Get_Timestamp_Request)
  })
_sym_db.RegisterMessage(Get_Timestamp_Request)

Get_Debug_Timestamp_Request = _reflection.GeneratedProtocolMessageType('Get_Debug_Timestamp_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_DEBUG_TIMESTAMP_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.time_pb2'
  # @@protoc_insertion_point(class_scope:time.Get_Debug_Timestamp_Request)
  })
_sym_db.RegisterMessage(Get_Debug_Timestamp_Request)

Get_Debug_Timestamp_Reply = _reflection.GeneratedProtocolMessageType('Get_Debug_Timestamp_Reply', (_message.Message,), {
  'DESCRIPTOR' : _GET_DEBUG_TIMESTAMP_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.time_pb2'
  # @@protoc_insertion_point(class_scope:time.Get_Debug_Timestamp_Reply)
  })
_sym_db.RegisterMessage(Get_Debug_Timestamp_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.time_pb2'
  # @@protoc_insertion_point(class_scope:time.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.time_pb2'
  # @@protoc_insertion_point(class_scope:time.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

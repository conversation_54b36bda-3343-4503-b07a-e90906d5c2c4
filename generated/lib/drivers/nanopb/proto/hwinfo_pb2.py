# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/hwinfo.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/hwinfo.proto',
  package='hwinfo',
  syntax='proto3',
  serialized_options=b'Z\rnanopb/hwinfo',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n/generated/lib/drivers/nanopb/proto/hwinfo.proto\x12\x06hwinfo\"\x15\n\x13\x42oardVersionRequest\"\x16\n\x14\x42oardIdentityRequest\"v\n\x07Request\x12.\n\x07version\x18\x01 \x01(\x0b\x32\x1b.hwinfo.BoardVersionRequestH\x00\x12\x30\n\x08identity\x18\x02 \x01(\x0b\x32\x1c.hwinfo.BoardIdentityRequestH\x00\x42\t\n\x07request\"/\n\x11\x42oardVersionReply\x12\r\n\x05model\x18\x01 \x01(\t\x12\x0b\n\x03rev\x18\x02 \x01(\r\"P\n\x12\x42oardIdentityReply\x12\x11\n\x04\x63\x62sn\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x13\n\x06\x61ssySn\x18\x02 \x01(\tH\x01\x88\x01\x01\x42\x07\n\x05_cbsnB\t\n\x07_assySn\"n\n\x05Reply\x12,\n\x07version\x18\x01 \x01(\x0b\x32\x19.hwinfo.BoardVersionReplyH\x00\x12.\n\x08identity\x18\x02 \x01(\x0b\x32\x1a.hwinfo.BoardIdentityReplyH\x00\x42\x07\n\x05replyB\x0fZ\rnanopb/hwinfob\x06proto3'
)




_BOARDVERSIONREQUEST = _descriptor.Descriptor(
  name='BoardVersionRequest',
  full_name='hwinfo.BoardVersionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=59,
  serialized_end=80,
)


_BOARDIDENTITYREQUEST = _descriptor.Descriptor(
  name='BoardIdentityRequest',
  full_name='hwinfo.BoardIdentityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=82,
  serialized_end=104,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='hwinfo.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='version', full_name='hwinfo.Request.version', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='identity', full_name='hwinfo.Request.identity', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='hwinfo.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=106,
  serialized_end=224,
)


_BOARDVERSIONREPLY = _descriptor.Descriptor(
  name='BoardVersionReply',
  full_name='hwinfo.BoardVersionReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model', full_name='hwinfo.BoardVersionReply.model', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rev', full_name='hwinfo.BoardVersionReply.rev', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=226,
  serialized_end=273,
)


_BOARDIDENTITYREPLY = _descriptor.Descriptor(
  name='BoardIdentityReply',
  full_name='hwinfo.BoardIdentityReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cbsn', full_name='hwinfo.BoardIdentityReply.cbsn', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='assySn', full_name='hwinfo.BoardIdentityReply.assySn', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_cbsn', full_name='hwinfo.BoardIdentityReply._cbsn',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_assySn', full_name='hwinfo.BoardIdentityReply._assySn',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=275,
  serialized_end=355,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='hwinfo.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='version', full_name='hwinfo.Reply.version', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='identity', full_name='hwinfo.Reply.identity', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='hwinfo.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=357,
  serialized_end=467,
)

_REQUEST.fields_by_name['version'].message_type = _BOARDVERSIONREQUEST
_REQUEST.fields_by_name['identity'].message_type = _BOARDIDENTITYREQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['version'])
_REQUEST.fields_by_name['version'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['identity'])
_REQUEST.fields_by_name['identity'].containing_oneof = _REQUEST.oneofs_by_name['request']
_BOARDIDENTITYREPLY.oneofs_by_name['_cbsn'].fields.append(
  _BOARDIDENTITYREPLY.fields_by_name['cbsn'])
_BOARDIDENTITYREPLY.fields_by_name['cbsn'].containing_oneof = _BOARDIDENTITYREPLY.oneofs_by_name['_cbsn']
_BOARDIDENTITYREPLY.oneofs_by_name['_assySn'].fields.append(
  _BOARDIDENTITYREPLY.fields_by_name['assySn'])
_BOARDIDENTITYREPLY.fields_by_name['assySn'].containing_oneof = _BOARDIDENTITYREPLY.oneofs_by_name['_assySn']
_REPLY.fields_by_name['version'].message_type = _BOARDVERSIONREPLY
_REPLY.fields_by_name['identity'].message_type = _BOARDIDENTITYREPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['version'])
_REPLY.fields_by_name['version'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['identity'])
_REPLY.fields_by_name['identity'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['BoardVersionRequest'] = _BOARDVERSIONREQUEST
DESCRIPTOR.message_types_by_name['BoardIdentityRequest'] = _BOARDIDENTITYREQUEST
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['BoardVersionReply'] = _BOARDVERSIONREPLY
DESCRIPTOR.message_types_by_name['BoardIdentityReply'] = _BOARDIDENTITYREPLY
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

BoardVersionRequest = _reflection.GeneratedProtocolMessageType('BoardVersionRequest', (_message.Message,), {
  'DESCRIPTOR' : _BOARDVERSIONREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.hwinfo_pb2'
  # @@protoc_insertion_point(class_scope:hwinfo.BoardVersionRequest)
  })
_sym_db.RegisterMessage(BoardVersionRequest)

BoardIdentityRequest = _reflection.GeneratedProtocolMessageType('BoardIdentityRequest', (_message.Message,), {
  'DESCRIPTOR' : _BOARDIDENTITYREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.hwinfo_pb2'
  # @@protoc_insertion_point(class_scope:hwinfo.BoardIdentityRequest)
  })
_sym_db.RegisterMessage(BoardIdentityRequest)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.hwinfo_pb2'
  # @@protoc_insertion_point(class_scope:hwinfo.Request)
  })
_sym_db.RegisterMessage(Request)

BoardVersionReply = _reflection.GeneratedProtocolMessageType('BoardVersionReply', (_message.Message,), {
  'DESCRIPTOR' : _BOARDVERSIONREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.hwinfo_pb2'
  # @@protoc_insertion_point(class_scope:hwinfo.BoardVersionReply)
  })
_sym_db.RegisterMessage(BoardVersionReply)

BoardIdentityReply = _reflection.GeneratedProtocolMessageType('BoardIdentityReply', (_message.Message,), {
  'DESCRIPTOR' : _BOARDIDENTITYREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.hwinfo_pb2'
  # @@protoc_insertion_point(class_scope:hwinfo.BoardIdentityReply)
  })
_sym_db.RegisterMessage(BoardIdentityReply)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.hwinfo_pb2'
  # @@protoc_insertion_point(class_scope:hwinfo.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

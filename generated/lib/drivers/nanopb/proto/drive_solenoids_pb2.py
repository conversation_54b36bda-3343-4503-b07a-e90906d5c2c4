# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/drive_solenoids.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/drive_solenoids.proto',
  package='drive_solenoids',
  syntax='proto3',
  serialized_options=b'Z\026nanopb/drive_solenoids',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n8generated/lib/drivers/nanopb/proto/drive_solenoids.proto\x12\x0f\x64rive_solenoids\"\x8c\x01\n\rDrive_Request\x12\x35\n\x03\x64ir\x18\x01 \x01(\x0e\x32(.drive_solenoids.Drive_Request.Direction\x12\x12\n\nduty_cycle\x18\x02 \x01(\x02\"0\n\tDirection\x12\x0b\n\x07\x66orward\x10\x00\x12\x0c\n\x08\x62\x61\x63kward\x10\x01\x12\x08\n\x04stop\x10\x03\"\r\n\x0b\x44rive_Reply\"\x88\x01\n\x0cTurn_Request\x12\x34\n\x03\x64ir\x18\x01 \x01(\x0e\x32\'.drive_solenoids.Turn_Request.Direction\x12\x12\n\nduty_cycle\x18\x02 \x01(\x02\".\n\tDirection\x12\x08\n\x04left\x10\x00\x12\t\n\x05right\x10\x01\x12\x0c\n\x08straight\x10\x02\"\x0c\n\nTurn_Reply\"t\n\x07Request\x12/\n\x05\x64rive\x18\x01 \x01(\x0b\x32\x1e.drive_solenoids.Drive_RequestH\x00\x12-\n\x04turn\x18\x02 \x01(\x0b\x32\x1d.drive_solenoids.Turn_RequestH\x00\x42\t\n\x07request\"l\n\x05Reply\x12-\n\x05\x64rive\x18\x01 \x01(\x0b\x32\x1c.drive_solenoids.Drive_ReplyH\x00\x12+\n\x04turn\x18\x02 \x01(\x0b\x32\x1b.drive_solenoids.Turn_ReplyH\x00\x42\x07\n\x05replyB\x18Z\x16nanopb/drive_solenoidsb\x06proto3'
)



_DRIVE_REQUEST_DIRECTION = _descriptor.EnumDescriptor(
  name='Direction',
  full_name='drive_solenoids.Drive_Request.Direction',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='forward', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='backward', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='stop', index=2, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=170,
  serialized_end=218,
)
_sym_db.RegisterEnumDescriptor(_DRIVE_REQUEST_DIRECTION)

_TURN_REQUEST_DIRECTION = _descriptor.EnumDescriptor(
  name='Direction',
  full_name='drive_solenoids.Turn_Request.Direction',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='left', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='right', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='straight', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=326,
  serialized_end=372,
)
_sym_db.RegisterEnumDescriptor(_TURN_REQUEST_DIRECTION)


_DRIVE_REQUEST = _descriptor.Descriptor(
  name='Drive_Request',
  full_name='drive_solenoids.Drive_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='dir', full_name='drive_solenoids.Drive_Request.dir', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duty_cycle', full_name='drive_solenoids.Drive_Request.duty_cycle', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _DRIVE_REQUEST_DIRECTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=78,
  serialized_end=218,
)


_DRIVE_REPLY = _descriptor.Descriptor(
  name='Drive_Reply',
  full_name='drive_solenoids.Drive_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=220,
  serialized_end=233,
)


_TURN_REQUEST = _descriptor.Descriptor(
  name='Turn_Request',
  full_name='drive_solenoids.Turn_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='dir', full_name='drive_solenoids.Turn_Request.dir', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duty_cycle', full_name='drive_solenoids.Turn_Request.duty_cycle', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TURN_REQUEST_DIRECTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=236,
  serialized_end=372,
)


_TURN_REPLY = _descriptor.Descriptor(
  name='Turn_Reply',
  full_name='drive_solenoids.Turn_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=374,
  serialized_end=386,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='drive_solenoids.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='drive', full_name='drive_solenoids.Request.drive', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='turn', full_name='drive_solenoids.Request.turn', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='drive_solenoids.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=388,
  serialized_end=504,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='drive_solenoids.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='drive', full_name='drive_solenoids.Reply.drive', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='turn', full_name='drive_solenoids.Reply.turn', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='drive_solenoids.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=506,
  serialized_end=614,
)

_DRIVE_REQUEST.fields_by_name['dir'].enum_type = _DRIVE_REQUEST_DIRECTION
_DRIVE_REQUEST_DIRECTION.containing_type = _DRIVE_REQUEST
_TURN_REQUEST.fields_by_name['dir'].enum_type = _TURN_REQUEST_DIRECTION
_TURN_REQUEST_DIRECTION.containing_type = _TURN_REQUEST
_REQUEST.fields_by_name['drive'].message_type = _DRIVE_REQUEST
_REQUEST.fields_by_name['turn'].message_type = _TURN_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['drive'])
_REQUEST.fields_by_name['drive'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['turn'])
_REQUEST.fields_by_name['turn'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['drive'].message_type = _DRIVE_REPLY
_REPLY.fields_by_name['turn'].message_type = _TURN_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['drive'])
_REPLY.fields_by_name['drive'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['turn'])
_REPLY.fields_by_name['turn'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Drive_Request'] = _DRIVE_REQUEST
DESCRIPTOR.message_types_by_name['Drive_Reply'] = _DRIVE_REPLY
DESCRIPTOR.message_types_by_name['Turn_Request'] = _TURN_REQUEST
DESCRIPTOR.message_types_by_name['Turn_Reply'] = _TURN_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Drive_Request = _reflection.GeneratedProtocolMessageType('Drive_Request', (_message.Message,), {
  'DESCRIPTOR' : _DRIVE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.drive_solenoids_pb2'
  # @@protoc_insertion_point(class_scope:drive_solenoids.Drive_Request)
  })
_sym_db.RegisterMessage(Drive_Request)

Drive_Reply = _reflection.GeneratedProtocolMessageType('Drive_Reply', (_message.Message,), {
  'DESCRIPTOR' : _DRIVE_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.drive_solenoids_pb2'
  # @@protoc_insertion_point(class_scope:drive_solenoids.Drive_Reply)
  })
_sym_db.RegisterMessage(Drive_Reply)

Turn_Request = _reflection.GeneratedProtocolMessageType('Turn_Request', (_message.Message,), {
  'DESCRIPTOR' : _TURN_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.drive_solenoids_pb2'
  # @@protoc_insertion_point(class_scope:drive_solenoids.Turn_Request)
  })
_sym_db.RegisterMessage(Turn_Request)

Turn_Reply = _reflection.GeneratedProtocolMessageType('Turn_Reply', (_message.Message,), {
  'DESCRIPTOR' : _TURN_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.drive_solenoids_pb2'
  # @@protoc_insertion_point(class_scope:drive_solenoids.Turn_Reply)
  })
_sym_db.RegisterMessage(Turn_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.drive_solenoids_pb2'
  # @@protoc_insertion_point(class_scope:drive_solenoids.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.drive_solenoids_pb2'
  # @@protoc_insertion_point(class_scope:drive_solenoids.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

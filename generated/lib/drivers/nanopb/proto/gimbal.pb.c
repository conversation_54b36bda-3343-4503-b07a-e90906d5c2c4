/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "gimbal.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(gimbal_Boot_Request, gimbal_Boot_Request, 2)


PB_BIND(gimbal_Stop_Request, gimbal_Stop_Request, AUTO)


PB_BIND(gimbal_Servos_Request, gimbal_Servos_Request, 2)


PB_BIND(gimbal_GetPositionAtRequest, gimbal_GetPositionAtRequest, AUTO)


PB_BIND(gimbal_Servos_Reply, gimbal_Servos_Reply, 2)


PB_BIND(gimbal_PositionAt, gimbal_PositionAt, AUTO)


PB_BIND(gimbal_GetPositionAtReply, gimbal_GetPositionAtReply, AUTO)


PB_BIND(gimbal_Request, gimbal_Request, 2)


PB_BIND(gimbal_Reply, gimbal_Reply, 2)




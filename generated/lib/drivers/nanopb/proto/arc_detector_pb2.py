# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/arc_detector.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import error_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/arc_detector.proto',
  package='arc_detector',
  syntax='proto3',
  serialized_options=b'Z\023nanopb/arc_detector',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n5generated/lib/drivers/nanopb/proto/arc_detector.proto\x12\x0c\x61rc_detector\x1a.generated/lib/drivers/nanopb/proto/error.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\"\x9c\x01\n\x06\x43onfig\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x12\n\nupperLimit\x18\x02 \x01(\x02\x12\x12\n\nlowerLimit\x18\x03 \x01(\x02\x12\x13\n\x0b\x61larmPeriod\x18\x04 \x01(\r\x12\x16\n\x0e\x61larmThreshold\x18\x05 \x01(\r\x12\x14\n\x0cinitialDelay\x18\x06 \x01(\r\x12\x16\n\x0esampleInterval\x18\x07 \x01(\r\"\x15\n\x13Reset_Alarm_Request\"\x10\n\x0eStatus_Request\"V\n\x0cStatus_Reply\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\r\n\x05\x61larm\x18\x02 \x01(\x08\x12\x12\n\nminCurrent\x18\x03 \x01(\x02\x12\x12\n\nmaxCurrent\x18\x04 \x01(\x02\"=\n\x12Set_Config_Request\x12\'\n\tnewConfig\x18\x01 \x01(\x0b\x32\x14.arc_detector.Config\"\x14\n\x12Get_Config_Request\"2\n\x0c\x43onfig_Reply\x12\"\n\x04\x63onf\x18\x01 \x01(\x0b\x32\x14.arc_detector.Config\"\xe2\x01\n\x07Request\x12\x33\n\x07setConf\x18\x01 \x01(\x0b\x32 .arc_detector.Set_Config_RequestH\x00\x12\x33\n\x07getConf\x18\x02 \x01(\x0b\x32 .arc_detector.Get_Config_RequestH\x00\x12.\n\x06status\x18\x03 \x01(\x0b\x32\x1c.arc_detector.Status_RequestH\x00\x12\x32\n\x05reset\x18\x04 \x01(\x0b\x32!.arc_detector.Reset_Alarm_RequestH\x00\x42\t\n\x07request\"\xa2\x01\n\x05Reply\x12\x1d\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x0c.error.ErrorH\x00\x12\x17\n\x03\x61\x63k\x18\x02 \x01(\x0b\x32\x08.ack.AckH\x00\x12,\n\x06status\x18\x03 \x01(\x0b\x32\x1a.arc_detector.Status_ReplyH\x00\x12*\n\x04\x63onf\x18\x04 \x01(\x0b\x32\x1a.arc_detector.Config_ReplyH\x00\x42\x07\n\x05replyB\x15Z\x13nanopb/arc_detectorb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,])




_CONFIG = _descriptor.Descriptor(
  name='Config',
  full_name='arc_detector.Config',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='arc_detector.Config.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='upperLimit', full_name='arc_detector.Config.upperLimit', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lowerLimit', full_name='arc_detector.Config.lowerLimit', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alarmPeriod', full_name='arc_detector.Config.alarmPeriod', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alarmThreshold', full_name='arc_detector.Config.alarmThreshold', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='initialDelay', full_name='arc_detector.Config.initialDelay', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sampleInterval', full_name='arc_detector.Config.sampleInterval', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=166,
  serialized_end=322,
)


_RESET_ALARM_REQUEST = _descriptor.Descriptor(
  name='Reset_Alarm_Request',
  full_name='arc_detector.Reset_Alarm_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=324,
  serialized_end=345,
)


_STATUS_REQUEST = _descriptor.Descriptor(
  name='Status_Request',
  full_name='arc_detector.Status_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=347,
  serialized_end=363,
)


_STATUS_REPLY = _descriptor.Descriptor(
  name='Status_Reply',
  full_name='arc_detector.Status_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='arc_detector.Status_Reply.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alarm', full_name='arc_detector.Status_Reply.alarm', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='minCurrent', full_name='arc_detector.Status_Reply.minCurrent', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='maxCurrent', full_name='arc_detector.Status_Reply.maxCurrent', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=365,
  serialized_end=451,
)


_SET_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Set_Config_Request',
  full_name='arc_detector.Set_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='newConfig', full_name='arc_detector.Set_Config_Request.newConfig', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=453,
  serialized_end=514,
)


_GET_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Get_Config_Request',
  full_name='arc_detector.Get_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=516,
  serialized_end=536,
)


_CONFIG_REPLY = _descriptor.Descriptor(
  name='Config_Reply',
  full_name='arc_detector.Config_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='conf', full_name='arc_detector.Config_Reply.conf', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=538,
  serialized_end=588,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='arc_detector.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='setConf', full_name='arc_detector.Request.setConf', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='getConf', full_name='arc_detector.Request.getConf', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='arc_detector.Request.status', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reset', full_name='arc_detector.Request.reset', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='arc_detector.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=591,
  serialized_end=817,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='arc_detector.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='arc_detector.Reply.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ack', full_name='arc_detector.Reply.ack', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='arc_detector.Reply.status', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='conf', full_name='arc_detector.Reply.conf', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='arc_detector.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=820,
  serialized_end=982,
)

_SET_CONFIG_REQUEST.fields_by_name['newConfig'].message_type = _CONFIG
_CONFIG_REPLY.fields_by_name['conf'].message_type = _CONFIG
_REQUEST.fields_by_name['setConf'].message_type = _SET_CONFIG_REQUEST
_REQUEST.fields_by_name['getConf'].message_type = _GET_CONFIG_REQUEST
_REQUEST.fields_by_name['status'].message_type = _STATUS_REQUEST
_REQUEST.fields_by_name['reset'].message_type = _RESET_ALARM_REQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['setConf'])
_REQUEST.fields_by_name['setConf'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['getConf'])
_REQUEST.fields_by_name['getConf'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['status'])
_REQUEST.fields_by_name['status'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['reset'])
_REQUEST.fields_by_name['reset'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['error'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_error__pb2._ERROR
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['status'].message_type = _STATUS_REPLY
_REPLY.fields_by_name['conf'].message_type = _CONFIG_REPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['error'])
_REPLY.fields_by_name['error'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['status'])
_REPLY.fields_by_name['status'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['conf'])
_REPLY.fields_by_name['conf'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['Config'] = _CONFIG
DESCRIPTOR.message_types_by_name['Reset_Alarm_Request'] = _RESET_ALARM_REQUEST
DESCRIPTOR.message_types_by_name['Status_Request'] = _STATUS_REQUEST
DESCRIPTOR.message_types_by_name['Status_Reply'] = _STATUS_REPLY
DESCRIPTOR.message_types_by_name['Set_Config_Request'] = _SET_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Get_Config_Request'] = _GET_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Config_Reply'] = _CONFIG_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Config = _reflection.GeneratedProtocolMessageType('Config', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.arc_detector_pb2'
  # @@protoc_insertion_point(class_scope:arc_detector.Config)
  })
_sym_db.RegisterMessage(Config)

Reset_Alarm_Request = _reflection.GeneratedProtocolMessageType('Reset_Alarm_Request', (_message.Message,), {
  'DESCRIPTOR' : _RESET_ALARM_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.arc_detector_pb2'
  # @@protoc_insertion_point(class_scope:arc_detector.Reset_Alarm_Request)
  })
_sym_db.RegisterMessage(Reset_Alarm_Request)

Status_Request = _reflection.GeneratedProtocolMessageType('Status_Request', (_message.Message,), {
  'DESCRIPTOR' : _STATUS_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.arc_detector_pb2'
  # @@protoc_insertion_point(class_scope:arc_detector.Status_Request)
  })
_sym_db.RegisterMessage(Status_Request)

Status_Reply = _reflection.GeneratedProtocolMessageType('Status_Reply', (_message.Message,), {
  'DESCRIPTOR' : _STATUS_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.arc_detector_pb2'
  # @@protoc_insertion_point(class_scope:arc_detector.Status_Reply)
  })
_sym_db.RegisterMessage(Status_Reply)

Set_Config_Request = _reflection.GeneratedProtocolMessageType('Set_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _SET_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.arc_detector_pb2'
  # @@protoc_insertion_point(class_scope:arc_detector.Set_Config_Request)
  })
_sym_db.RegisterMessage(Set_Config_Request)

Get_Config_Request = _reflection.GeneratedProtocolMessageType('Get_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _GET_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.arc_detector_pb2'
  # @@protoc_insertion_point(class_scope:arc_detector.Get_Config_Request)
  })
_sym_db.RegisterMessage(Get_Config_Request)

Config_Reply = _reflection.GeneratedProtocolMessageType('Config_Reply', (_message.Message,), {
  'DESCRIPTOR' : _CONFIG_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.arc_detector_pb2'
  # @@protoc_insertion_point(class_scope:arc_detector.Config_Reply)
  })
_sym_db.RegisterMessage(Config_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.arc_detector_pb2'
  # @@protoc_insertion_point(class_scope:arc_detector.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.arc_detector_pb2'
  # @@protoc_insertion_point(class_scope:arc_detector.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_BENJAMIN_GPS_BOARD_BENJAMIN_GPS_BOARD_PB_H_INCLUDED
#define PB_BENJAMIN_GPS_BOARD_BENJAMIN_GPS_BOARD_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"
#include "generated/lib/drivers/nanopb/proto/diagnostic.pb.h"
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/gps.pb.h"
#include "generated/lib/drivers/nanopb/proto/heading.pb.h"
#include "generated/lib/drivers/nanopb/proto/hwinfo.pb.h"
#include "generated/lib/drivers/nanopb/proto/request.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _benjamin_gps_board_PtpConfig_Request {
    bool is_master;
} benjamin_gps_board_PtpConfig_Request;

typedef struct _benjamin_gps_board_Reply {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_reply;
    union {
        diagnostic_Pong pong;
        gps_Reply gps;
        heading_Reply heading;
        hwinfo_Reply hwinfo;
        error_Error error;
        ack_Ack ack;
    } reply;
} benjamin_gps_board_Reply;

typedef struct _benjamin_gps_board_Request {
    bool has_header;
    request_RequestHeader header;
    pb_size_t which_request;
    union {
        diagnostic_Ping ping;
        gps_Request gps;
        heading_Request heading;
        hwinfo_Request hwinfo;
        benjamin_gps_board_PtpConfig_Request ptp_config;
    } request;
} benjamin_gps_board_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define benjamin_gps_board_PtpConfig_Request_init_default {0}
#define benjamin_gps_board_Reply_init_default    {false, request_RequestHeader_init_default, 0, {diagnostic_Pong_init_default}}
#define benjamin_gps_board_Request_init_default  {false, request_RequestHeader_init_default, 0, {diagnostic_Ping_init_default}}
#define benjamin_gps_board_PtpConfig_Request_init_zero {0}
#define benjamin_gps_board_Reply_init_zero       {false, request_RequestHeader_init_zero, 0, {diagnostic_Pong_init_zero}}
#define benjamin_gps_board_Request_init_zero     {false, request_RequestHeader_init_zero, 0, {diagnostic_Ping_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define benjamin_gps_board_PtpConfig_Request_is_master_tag 1
#define benjamin_gps_board_Reply_header_tag      1
#define benjamin_gps_board_Reply_pong_tag        2
#define benjamin_gps_board_Reply_gps_tag         3
#define benjamin_gps_board_Reply_heading_tag     4
#define benjamin_gps_board_Reply_hwinfo_tag      5
#define benjamin_gps_board_Reply_error_tag       6
#define benjamin_gps_board_Reply_ack_tag         7
#define benjamin_gps_board_Request_header_tag    1
#define benjamin_gps_board_Request_ping_tag      2
#define benjamin_gps_board_Request_gps_tag       3
#define benjamin_gps_board_Request_heading_tag   4
#define benjamin_gps_board_Request_hwinfo_tag    5
#define benjamin_gps_board_Request_ptp_config_tag 6

/* Struct field encoding specification for nanopb */
#define benjamin_gps_board_PtpConfig_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     is_master,         1)
#define benjamin_gps_board_PtpConfig_Request_CALLBACK NULL
#define benjamin_gps_board_PtpConfig_Request_DEFAULT NULL

#define benjamin_gps_board_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pong,reply.pong),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,gps,reply.gps),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,heading,reply.heading),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,hwinfo,reply.hwinfo),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   7)
#define benjamin_gps_board_Reply_CALLBACK NULL
#define benjamin_gps_board_Reply_DEFAULT NULL
#define benjamin_gps_board_Reply_header_MSGTYPE request_RequestHeader
#define benjamin_gps_board_Reply_reply_pong_MSGTYPE diagnostic_Pong
#define benjamin_gps_board_Reply_reply_gps_MSGTYPE gps_Reply
#define benjamin_gps_board_Reply_reply_heading_MSGTYPE heading_Reply
#define benjamin_gps_board_Reply_reply_hwinfo_MSGTYPE hwinfo_Reply
#define benjamin_gps_board_Reply_reply_error_MSGTYPE error_Error
#define benjamin_gps_board_Reply_reply_ack_MSGTYPE ack_Ack

#define benjamin_gps_board_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  header,            1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ping,request.ping),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,gps,request.gps),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,heading,request.heading),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,hwinfo,request.hwinfo),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,ptp_config,request.ptp_config),   6)
#define benjamin_gps_board_Request_CALLBACK NULL
#define benjamin_gps_board_Request_DEFAULT NULL
#define benjamin_gps_board_Request_header_MSGTYPE request_RequestHeader
#define benjamin_gps_board_Request_request_ping_MSGTYPE diagnostic_Ping
#define benjamin_gps_board_Request_request_gps_MSGTYPE gps_Request
#define benjamin_gps_board_Request_request_heading_MSGTYPE heading_Request
#define benjamin_gps_board_Request_request_hwinfo_MSGTYPE hwinfo_Request
#define benjamin_gps_board_Request_request_ptp_config_MSGTYPE benjamin_gps_board_PtpConfig_Request

extern const pb_msgdesc_t benjamin_gps_board_PtpConfig_Request_msg;
extern const pb_msgdesc_t benjamin_gps_board_Reply_msg;
extern const pb_msgdesc_t benjamin_gps_board_Request_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define benjamin_gps_board_PtpConfig_Request_fields &benjamin_gps_board_PtpConfig_Request_msg
#define benjamin_gps_board_Reply_fields &benjamin_gps_board_Reply_msg
#define benjamin_gps_board_Request_fields &benjamin_gps_board_Request_msg

/* Maximum encoded size of messages (where known) */
#define benjamin_gps_board_PtpConfig_Request_size 2
#if defined(request_RequestHeader_size) && defined(diagnostic_Pong_size) && defined(gps_Reply_size) && defined(heading_Reply_size) && defined(hwinfo_Reply_size) && defined(error_Error_size) && defined(ack_Ack_size)
typedef union benjamin_gps_board_Reply_reply_size_union {char f2[(6 + diagnostic_Pong_size)]; char f3[(6 + gps_Reply_size)]; char f4[(6 + heading_Reply_size)]; char f5[(6 + hwinfo_Reply_size)]; char f6[(6 + error_Error_size)]; char f7[(6 + ack_Ack_size)];} benjamin_gps_board_Reply_reply_size_union;
#define benjamin_gps_board_Reply_size            (6 + request_RequestHeader_size + sizeof(benjamin_gps_board_Reply_reply_size_union))
#endif
#if defined(request_RequestHeader_size) && defined(diagnostic_Ping_size) && defined(gps_Request_size) && defined(heading_Request_size) && defined(hwinfo_Request_size)
typedef union benjamin_gps_board_Request_request_size_union {char f2[(6 + diagnostic_Ping_size)]; char f3[(6 + gps_Request_size)]; char f4[(6 + heading_Request_size)]; char f5[(6 + hwinfo_Request_size)]; char f0[4];} benjamin_gps_board_Request_request_size_union;
#define benjamin_gps_board_Request_size          (6 + request_RequestHeader_size + sizeof(benjamin_gps_board_Request_request_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

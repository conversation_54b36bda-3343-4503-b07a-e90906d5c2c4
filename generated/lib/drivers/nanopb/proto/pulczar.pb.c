/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "pulczar.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(pulczar_AmbientTempConfig, pulczar_AmbientTempConfig, AUTO)


PB_BIND(pulczar_AmbientTempGetStateRequest, pulczar_AmbientTempGetStateRequest, AUTO)


PB_BIND(pulczar_AmbientTempRequest, pulczar_AmbientTempRequest, AUTO)


PB_BIND(pulczar_AmbientTempReply, pulczar_AmbientTempReply, AUTO)


PB_BIND(pulczar_HwStatus_Request, pulczar_HwStatus_Request, AUTO)


PB_BIND(pulczar_HwStatus_Servo, pulczar_HwStatus_Servo, AUTO)


PB_BIND(pulczar_HwStatus_Slayer_Reply, pulczar_HwStatus_Slayer_Reply, AUTO)


PB_BIND(pulczar_HwStatus_Reaper_Reply, pulczar_HwStatus_Reaper_Reply, 2)


PB_BIND(pulczar_HwStatus_Reply, pulczar_HwStatus_Reply, 2)


PB_BIND(pulczar_Reset_Request, pulczar_Reset_Request, AUTO)


PB_BIND(pulczar_Clear_Config_Request, pulczar_Clear_Config_Request, AUTO)


PB_BIND(pulczar_Status_Request, pulczar_Status_Request, AUTO)


PB_BIND(pulczar_Override_Request, pulczar_Override_Request, AUTO)


PB_BIND(pulczar_Power_Request, pulczar_Power_Request, AUTO)


PB_BIND(pulczar_Status_Reply, pulczar_Status_Reply, 2)


PB_BIND(pulczar_Power_Reply, pulczar_Power_Reply, AUTO)


PB_BIND(pulczar_Request, pulczar_Request, 2)


PB_BIND(pulczar_Reply, pulczar_Reply, 2)




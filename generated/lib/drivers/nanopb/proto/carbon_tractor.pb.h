/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_CARBON_TRACTOR_CARBON_TRACTOR_PB_H_INCLUDED
#define PB_CARBON_TRACTOR_CARBON_TRACTOR_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/ots_tractor.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _carbon_tractor_HHStateStatus {
    carbon_tractor_HHStateStatus_HH_UNKNOWN = 0,
    carbon_tractor_HHStateStatus_HH_DISABLED = 1,
    carbon_tractor_HHStateStatus_HH_OPERATIONAL = 2,
    carbon_tractor_HHStateStatus_HH_STOPPED = 3,
    carbon_tractor_HHStateStatus_HH_SAFE = 4,
    carbon_tractor_HHStateStatus_HH_ESTOP = 5
} carbon_tractor_HHStateStatus;

/* Struct definitions */
typedef struct _carbon_tractor_DebugReply {
    char dummy_field;
} carbon_tractor_DebugReply;

typedef struct _carbon_tractor_DebugRequest {
    char dummy_field;
} carbon_tractor_DebugRequest;

typedef struct _carbon_tractor_Empty {
    char dummy_field;
} carbon_tractor_Empty;

typedef struct _carbon_tractor_PetRequest {
    char dummy_field;
} carbon_tractor_PetRequest;

typedef struct _carbon_tractor_BrakeState {
    int32_t force_left;
    int32_t force_right;
} carbon_tractor_BrakeState;

typedef struct _carbon_tractor_GetRequest {
    pb_size_t which_get;
    union {
        carbon_tractor_Empty status;
        carbon_tractor_Empty hh_state;
        carbon_tractor_Empty brakes;
        carbon_tractor_Empty safety;
        carbon_tractor_Empty safety_bypass;
        carbon_tractor_Empty steering;
        carbon_tractor_Empty steering_cfg;
        carbon_tractor_Empty pet_loss;
        carbon_tractor_Empty speed_limit;
    } get;
} carbon_tractor_GetRequest;

typedef struct _carbon_tractor_HHState {
    carbon_tractor_HHStateStatus state;
} carbon_tractor_HHState;

typedef struct _carbon_tractor_PetLossState {
    bool use_stop;
} carbon_tractor_PetLossState;

typedef struct _carbon_tractor_SafetySensorBypassState {
    bool bypass;
} carbon_tractor_SafetySensorBypassState;

typedef struct _carbon_tractor_SafetySensorsState {
    bool triggered_sensor_1;
    bool triggered_sensor_2;
    bool triggered_sensor_3;
    bool triggered_sensor_4;
} carbon_tractor_SafetySensorsState;

typedef struct _carbon_tractor_SpeedLimitState {
    float speed_limit_mph;
} carbon_tractor_SpeedLimitState;

typedef struct _carbon_tractor_SteeringCfgState {
    float kp;
    float ki;
    float kd;
    float integral_limit;
    int32_t update_rate_hz;
    uint32_t min_steering_valve_current;
    uint32_t max_steering_valve_current;
} carbon_tractor_SteeringCfgState;

typedef struct _carbon_tractor_SteeringState {
    float angle;
} carbon_tractor_SteeringState;

typedef struct _carbon_tractor_SetReply {
    pb_size_t which_set;
    union {
        carbon_tractor_HHState hh_state;
        carbon_tractor_BrakeState brakes;
        carbon_tractor_SafetySensorBypassState safety_bypass;
        carbon_tractor_SteeringState steering;
        carbon_tractor_SteeringCfgState steering_cfg;
        carbon_tractor_PetLossState pet_loss;
        carbon_tractor_SpeedLimitState speed_limit;
    } set;
} carbon_tractor_SetReply;

typedef struct _carbon_tractor_SetRequest {
    pb_size_t which_set;
    union {
        carbon_tractor_HHState hh_state;
        carbon_tractor_BrakeState brakes;
        carbon_tractor_SafetySensorBypassState safety_bypass;
        carbon_tractor_SteeringState steering;
        carbon_tractor_SteeringCfgState steering_cfg;
        carbon_tractor_PetLossState pet_loss;
        carbon_tractor_SpeedLimitState speed_limit;
    } set;
} carbon_tractor_SetRequest;

typedef struct _carbon_tractor_TractorStatus {
    bool has_state;
    carbon_tractor_HHState state;
    int32_t error_flag;
    float ground_speed;
    float wheel_angle;
    float hitch_lift_percentage;
    bool has_gear;
    ots_tractor_GearState gear;
    bool safety_triggered;
    bool safety_bypass;
    bool remote_lockout;
    int32_t rpms;
} carbon_tractor_TractorStatus;

typedef struct _carbon_tractor_GetReply {
    pb_size_t which_get;
    union {
        carbon_tractor_TractorStatus status;
        carbon_tractor_HHState hh_state;
        carbon_tractor_BrakeState brakes;
        carbon_tractor_SafetySensorsState safety;
        carbon_tractor_SafetySensorBypassState safety_bypass;
        carbon_tractor_SteeringState steering;
        carbon_tractor_SteeringCfgState steering_cfg;
        carbon_tractor_PetLossState pet_loss;
        carbon_tractor_SpeedLimitState speed_limit;
    } get;
} carbon_tractor_GetReply;

typedef struct _carbon_tractor_Request {
    pb_size_t which_request;
    union {
        carbon_tractor_SetRequest set;
        carbon_tractor_GetRequest get;
        carbon_tractor_PetRequest pet;
        carbon_tractor_DebugRequest debug;
    } request;
} carbon_tractor_Request;

typedef struct _carbon_tractor_Reply {
    pb_size_t which_reply;
    union {
        carbon_tractor_SetReply set;
        carbon_tractor_GetReply get;
        carbon_tractor_TractorStatus pet;
        carbon_tractor_DebugReply debug;
    } reply;
} carbon_tractor_Reply;


/* Helper constants for enums */
#define _carbon_tractor_HHStateStatus_MIN carbon_tractor_HHStateStatus_HH_UNKNOWN
#define _carbon_tractor_HHStateStatus_MAX carbon_tractor_HHStateStatus_HH_ESTOP
#define _carbon_tractor_HHStateStatus_ARRAYSIZE ((carbon_tractor_HHStateStatus)(carbon_tractor_HHStateStatus_HH_ESTOP+1))


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define carbon_tractor_Empty_init_default        {0}
#define carbon_tractor_HHState_init_default      {_carbon_tractor_HHStateStatus_MIN}
#define carbon_tractor_BrakeState_init_default   {0, 0}
#define carbon_tractor_SafetySensorsState_init_default {0, 0, 0, 0}
#define carbon_tractor_SafetySensorBypassState_init_default {0}
#define carbon_tractor_TractorStatus_init_default {false, carbon_tractor_HHState_init_default, 0, 0, 0, 0, false, ots_tractor_GearState_init_default, 0, 0, 0, 0}
#define carbon_tractor_PetRequest_init_default   {0}
#define carbon_tractor_SteeringState_init_default {0}
#define carbon_tractor_SteeringCfgState_init_default {0, 0, 0, 0, 0, 0, 0}
#define carbon_tractor_PetLossState_init_default {0}
#define carbon_tractor_SpeedLimitState_init_default {0}
#define carbon_tractor_SetRequest_init_default   {0, {carbon_tractor_HHState_init_default}}
#define carbon_tractor_SetReply_init_default     {0, {carbon_tractor_HHState_init_default}}
#define carbon_tractor_GetRequest_init_default   {0, {carbon_tractor_Empty_init_default}}
#define carbon_tractor_GetReply_init_default     {0, {carbon_tractor_TractorStatus_init_default}}
#define carbon_tractor_DebugRequest_init_default {0}
#define carbon_tractor_DebugReply_init_default   {0}
#define carbon_tractor_Request_init_default      {0, {carbon_tractor_SetRequest_init_default}}
#define carbon_tractor_Reply_init_default        {0, {carbon_tractor_SetReply_init_default}}
#define carbon_tractor_Empty_init_zero           {0}
#define carbon_tractor_HHState_init_zero         {_carbon_tractor_HHStateStatus_MIN}
#define carbon_tractor_BrakeState_init_zero      {0, 0}
#define carbon_tractor_SafetySensorsState_init_zero {0, 0, 0, 0}
#define carbon_tractor_SafetySensorBypassState_init_zero {0}
#define carbon_tractor_TractorStatus_init_zero   {false, carbon_tractor_HHState_init_zero, 0, 0, 0, 0, false, ots_tractor_GearState_init_zero, 0, 0, 0, 0}
#define carbon_tractor_PetRequest_init_zero      {0}
#define carbon_tractor_SteeringState_init_zero   {0}
#define carbon_tractor_SteeringCfgState_init_zero {0, 0, 0, 0, 0, 0, 0}
#define carbon_tractor_PetLossState_init_zero    {0}
#define carbon_tractor_SpeedLimitState_init_zero {0}
#define carbon_tractor_SetRequest_init_zero      {0, {carbon_tractor_HHState_init_zero}}
#define carbon_tractor_SetReply_init_zero        {0, {carbon_tractor_HHState_init_zero}}
#define carbon_tractor_GetRequest_init_zero      {0, {carbon_tractor_Empty_init_zero}}
#define carbon_tractor_GetReply_init_zero        {0, {carbon_tractor_TractorStatus_init_zero}}
#define carbon_tractor_DebugRequest_init_zero    {0}
#define carbon_tractor_DebugReply_init_zero      {0}
#define carbon_tractor_Request_init_zero         {0, {carbon_tractor_SetRequest_init_zero}}
#define carbon_tractor_Reply_init_zero           {0, {carbon_tractor_SetReply_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define carbon_tractor_BrakeState_force_left_tag 1
#define carbon_tractor_BrakeState_force_right_tag 2
#define carbon_tractor_GetRequest_status_tag     1
#define carbon_tractor_GetRequest_hh_state_tag   2
#define carbon_tractor_GetRequest_brakes_tag     3
#define carbon_tractor_GetRequest_safety_tag     4
#define carbon_tractor_GetRequest_safety_bypass_tag 5
#define carbon_tractor_GetRequest_steering_tag   6
#define carbon_tractor_GetRequest_steering_cfg_tag 7
#define carbon_tractor_GetRequest_pet_loss_tag   8
#define carbon_tractor_GetRequest_speed_limit_tag 9
#define carbon_tractor_HHState_state_tag         1
#define carbon_tractor_PetLossState_use_stop_tag 1
#define carbon_tractor_SafetySensorBypassState_bypass_tag 1
#define carbon_tractor_SafetySensorsState_triggered_sensor_1_tag 1
#define carbon_tractor_SafetySensorsState_triggered_sensor_2_tag 2
#define carbon_tractor_SafetySensorsState_triggered_sensor_3_tag 3
#define carbon_tractor_SafetySensorsState_triggered_sensor_4_tag 4
#define carbon_tractor_SpeedLimitState_speed_limit_mph_tag 1
#define carbon_tractor_SteeringCfgState_kp_tag   1
#define carbon_tractor_SteeringCfgState_ki_tag   2
#define carbon_tractor_SteeringCfgState_kd_tag   3
#define carbon_tractor_SteeringCfgState_integral_limit_tag 4
#define carbon_tractor_SteeringCfgState_update_rate_hz_tag 5
#define carbon_tractor_SteeringCfgState_min_steering_valve_current_tag 6
#define carbon_tractor_SteeringCfgState_max_steering_valve_current_tag 7
#define carbon_tractor_SteeringState_angle_tag   1
#define carbon_tractor_SetReply_hh_state_tag     1
#define carbon_tractor_SetReply_brakes_tag       2
#define carbon_tractor_SetReply_safety_bypass_tag 3
#define carbon_tractor_SetReply_steering_tag     4
#define carbon_tractor_SetReply_steering_cfg_tag 5
#define carbon_tractor_SetReply_pet_loss_tag     6
#define carbon_tractor_SetReply_speed_limit_tag  7
#define carbon_tractor_SetRequest_hh_state_tag   1
#define carbon_tractor_SetRequest_brakes_tag     2
#define carbon_tractor_SetRequest_safety_bypass_tag 3
#define carbon_tractor_SetRequest_steering_tag   4
#define carbon_tractor_SetRequest_steering_cfg_tag 5
#define carbon_tractor_SetRequest_pet_loss_tag   6
#define carbon_tractor_SetRequest_speed_limit_tag 7
#define carbon_tractor_TractorStatus_state_tag   1
#define carbon_tractor_TractorStatus_error_flag_tag 2
#define carbon_tractor_TractorStatus_ground_speed_tag 3
#define carbon_tractor_TractorStatus_wheel_angle_tag 4
#define carbon_tractor_TractorStatus_hitch_lift_percentage_tag 5
#define carbon_tractor_TractorStatus_gear_tag    6
#define carbon_tractor_TractorStatus_safety_triggered_tag 7
#define carbon_tractor_TractorStatus_safety_bypass_tag 8
#define carbon_tractor_TractorStatus_remote_lockout_tag 9
#define carbon_tractor_TractorStatus_rpms_tag    10
#define carbon_tractor_GetReply_status_tag       1
#define carbon_tractor_GetReply_hh_state_tag     2
#define carbon_tractor_GetReply_brakes_tag       3
#define carbon_tractor_GetReply_safety_tag       4
#define carbon_tractor_GetReply_safety_bypass_tag 5
#define carbon_tractor_GetReply_steering_tag     6
#define carbon_tractor_GetReply_steering_cfg_tag 7
#define carbon_tractor_GetReply_pet_loss_tag     8
#define carbon_tractor_GetReply_speed_limit_tag  9
#define carbon_tractor_Request_set_tag           1
#define carbon_tractor_Request_get_tag           2
#define carbon_tractor_Request_pet_tag           3
#define carbon_tractor_Request_debug_tag         4
#define carbon_tractor_Reply_set_tag             1
#define carbon_tractor_Reply_get_tag             2
#define carbon_tractor_Reply_pet_tag             3
#define carbon_tractor_Reply_debug_tag           4

/* Struct field encoding specification for nanopb */
#define carbon_tractor_Empty_FIELDLIST(X, a) \

#define carbon_tractor_Empty_CALLBACK NULL
#define carbon_tractor_Empty_DEFAULT NULL

#define carbon_tractor_HHState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    state,             1)
#define carbon_tractor_HHState_CALLBACK NULL
#define carbon_tractor_HHState_DEFAULT NULL

#define carbon_tractor_BrakeState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    force_left,        1) \
X(a, STATIC,   SINGULAR, INT32,    force_right,       2)
#define carbon_tractor_BrakeState_CALLBACK NULL
#define carbon_tractor_BrakeState_DEFAULT NULL

#define carbon_tractor_SafetySensorsState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     triggered_sensor_1,   1) \
X(a, STATIC,   SINGULAR, BOOL,     triggered_sensor_2,   2) \
X(a, STATIC,   SINGULAR, BOOL,     triggered_sensor_3,   3) \
X(a, STATIC,   SINGULAR, BOOL,     triggered_sensor_4,   4)
#define carbon_tractor_SafetySensorsState_CALLBACK NULL
#define carbon_tractor_SafetySensorsState_DEFAULT NULL

#define carbon_tractor_SafetySensorBypassState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     bypass,            1)
#define carbon_tractor_SafetySensorBypassState_CALLBACK NULL
#define carbon_tractor_SafetySensorBypassState_DEFAULT NULL

#define carbon_tractor_TractorStatus_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  state,             1) \
X(a, STATIC,   SINGULAR, INT32,    error_flag,        2) \
X(a, STATIC,   SINGULAR, FLOAT,    ground_speed,      3) \
X(a, STATIC,   SINGULAR, FLOAT,    wheel_angle,       4) \
X(a, STATIC,   SINGULAR, FLOAT,    hitch_lift_percentage,   5) \
X(a, STATIC,   OPTIONAL, MESSAGE,  gear,              6) \
X(a, STATIC,   SINGULAR, BOOL,     safety_triggered,   7) \
X(a, STATIC,   SINGULAR, BOOL,     safety_bypass,     8) \
X(a, STATIC,   SINGULAR, BOOL,     remote_lockout,    9) \
X(a, STATIC,   SINGULAR, INT32,    rpms,             10)
#define carbon_tractor_TractorStatus_CALLBACK NULL
#define carbon_tractor_TractorStatus_DEFAULT NULL
#define carbon_tractor_TractorStatus_state_MSGTYPE carbon_tractor_HHState
#define carbon_tractor_TractorStatus_gear_MSGTYPE ots_tractor_GearState

#define carbon_tractor_PetRequest_FIELDLIST(X, a) \

#define carbon_tractor_PetRequest_CALLBACK NULL
#define carbon_tractor_PetRequest_DEFAULT NULL

#define carbon_tractor_SteeringState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    angle,             1)
#define carbon_tractor_SteeringState_CALLBACK NULL
#define carbon_tractor_SteeringState_DEFAULT NULL

#define carbon_tractor_SteeringCfgState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    kp,                1) \
X(a, STATIC,   SINGULAR, FLOAT,    ki,                2) \
X(a, STATIC,   SINGULAR, FLOAT,    kd,                3) \
X(a, STATIC,   SINGULAR, FLOAT,    integral_limit,    4) \
X(a, STATIC,   SINGULAR, INT32,    update_rate_hz,    5) \
X(a, STATIC,   SINGULAR, UINT32,   min_steering_valve_current,   6) \
X(a, STATIC,   SINGULAR, UINT32,   max_steering_valve_current,   7)
#define carbon_tractor_SteeringCfgState_CALLBACK NULL
#define carbon_tractor_SteeringCfgState_DEFAULT NULL

#define carbon_tractor_PetLossState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     use_stop,          1)
#define carbon_tractor_PetLossState_CALLBACK NULL
#define carbon_tractor_PetLossState_DEFAULT NULL

#define carbon_tractor_SpeedLimitState_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FLOAT,    speed_limit_mph,   1)
#define carbon_tractor_SpeedLimitState_CALLBACK NULL
#define carbon_tractor_SpeedLimitState_DEFAULT NULL

#define carbon_tractor_SetRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,hh_state,set.hh_state),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,brakes,set.brakes),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,safety_bypass,set.safety_bypass),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,steering,set.steering),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,steering_cfg,set.steering_cfg),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,pet_loss,set.pet_loss),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,speed_limit,set.speed_limit),   7)
#define carbon_tractor_SetRequest_CALLBACK NULL
#define carbon_tractor_SetRequest_DEFAULT NULL
#define carbon_tractor_SetRequest_set_hh_state_MSGTYPE carbon_tractor_HHState
#define carbon_tractor_SetRequest_set_brakes_MSGTYPE carbon_tractor_BrakeState
#define carbon_tractor_SetRequest_set_safety_bypass_MSGTYPE carbon_tractor_SafetySensorBypassState
#define carbon_tractor_SetRequest_set_steering_MSGTYPE carbon_tractor_SteeringState
#define carbon_tractor_SetRequest_set_steering_cfg_MSGTYPE carbon_tractor_SteeringCfgState
#define carbon_tractor_SetRequest_set_pet_loss_MSGTYPE carbon_tractor_PetLossState
#define carbon_tractor_SetRequest_set_speed_limit_MSGTYPE carbon_tractor_SpeedLimitState

#define carbon_tractor_SetReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,hh_state,set.hh_state),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,brakes,set.brakes),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,safety_bypass,set.safety_bypass),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,steering,set.steering),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,steering_cfg,set.steering_cfg),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,pet_loss,set.pet_loss),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (set,speed_limit,set.speed_limit),   7)
#define carbon_tractor_SetReply_CALLBACK NULL
#define carbon_tractor_SetReply_DEFAULT NULL
#define carbon_tractor_SetReply_set_hh_state_MSGTYPE carbon_tractor_HHState
#define carbon_tractor_SetReply_set_brakes_MSGTYPE carbon_tractor_BrakeState
#define carbon_tractor_SetReply_set_safety_bypass_MSGTYPE carbon_tractor_SafetySensorBypassState
#define carbon_tractor_SetReply_set_steering_MSGTYPE carbon_tractor_SteeringState
#define carbon_tractor_SetReply_set_steering_cfg_MSGTYPE carbon_tractor_SteeringCfgState
#define carbon_tractor_SetReply_set_pet_loss_MSGTYPE carbon_tractor_PetLossState
#define carbon_tractor_SetReply_set_speed_limit_MSGTYPE carbon_tractor_SpeedLimitState

#define carbon_tractor_GetRequest_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,status,get.status),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,hh_state,get.hh_state),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,brakes,get.brakes),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,safety,get.safety),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,safety_bypass,get.safety_bypass),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,steering,get.steering),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,steering_cfg,get.steering_cfg),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,pet_loss,get.pet_loss),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,speed_limit,get.speed_limit),   9)
#define carbon_tractor_GetRequest_CALLBACK NULL
#define carbon_tractor_GetRequest_DEFAULT NULL
#define carbon_tractor_GetRequest_get_status_MSGTYPE carbon_tractor_Empty
#define carbon_tractor_GetRequest_get_hh_state_MSGTYPE carbon_tractor_Empty
#define carbon_tractor_GetRequest_get_brakes_MSGTYPE carbon_tractor_Empty
#define carbon_tractor_GetRequest_get_safety_MSGTYPE carbon_tractor_Empty
#define carbon_tractor_GetRequest_get_safety_bypass_MSGTYPE carbon_tractor_Empty
#define carbon_tractor_GetRequest_get_steering_MSGTYPE carbon_tractor_Empty
#define carbon_tractor_GetRequest_get_steering_cfg_MSGTYPE carbon_tractor_Empty
#define carbon_tractor_GetRequest_get_pet_loss_MSGTYPE carbon_tractor_Empty
#define carbon_tractor_GetRequest_get_speed_limit_MSGTYPE carbon_tractor_Empty

#define carbon_tractor_GetReply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,status,get.status),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,hh_state,get.hh_state),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,brakes,get.brakes),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,safety,get.safety),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,safety_bypass,get.safety_bypass),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,steering,get.steering),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,steering_cfg,get.steering_cfg),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,pet_loss,get.pet_loss),   8) \
X(a, STATIC,   ONEOF,    MESSAGE,  (get,speed_limit,get.speed_limit),   9)
#define carbon_tractor_GetReply_CALLBACK NULL
#define carbon_tractor_GetReply_DEFAULT NULL
#define carbon_tractor_GetReply_get_status_MSGTYPE carbon_tractor_TractorStatus
#define carbon_tractor_GetReply_get_hh_state_MSGTYPE carbon_tractor_HHState
#define carbon_tractor_GetReply_get_brakes_MSGTYPE carbon_tractor_BrakeState
#define carbon_tractor_GetReply_get_safety_MSGTYPE carbon_tractor_SafetySensorsState
#define carbon_tractor_GetReply_get_safety_bypass_MSGTYPE carbon_tractor_SafetySensorBypassState
#define carbon_tractor_GetReply_get_steering_MSGTYPE carbon_tractor_SteeringState
#define carbon_tractor_GetReply_get_steering_cfg_MSGTYPE carbon_tractor_SteeringCfgState
#define carbon_tractor_GetReply_get_pet_loss_MSGTYPE carbon_tractor_PetLossState
#define carbon_tractor_GetReply_get_speed_limit_MSGTYPE carbon_tractor_SpeedLimitState

#define carbon_tractor_DebugRequest_FIELDLIST(X, a) \

#define carbon_tractor_DebugRequest_CALLBACK NULL
#define carbon_tractor_DebugRequest_DEFAULT NULL

#define carbon_tractor_DebugReply_FIELDLIST(X, a) \

#define carbon_tractor_DebugReply_CALLBACK NULL
#define carbon_tractor_DebugReply_DEFAULT NULL

#define carbon_tractor_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,set,request.set),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,get,request.get),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,pet,request.pet),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,debug,request.debug),   4)
#define carbon_tractor_Request_CALLBACK NULL
#define carbon_tractor_Request_DEFAULT NULL
#define carbon_tractor_Request_request_set_MSGTYPE carbon_tractor_SetRequest
#define carbon_tractor_Request_request_get_MSGTYPE carbon_tractor_GetRequest
#define carbon_tractor_Request_request_pet_MSGTYPE carbon_tractor_PetRequest
#define carbon_tractor_Request_request_debug_MSGTYPE carbon_tractor_DebugRequest

#define carbon_tractor_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,set,reply.set),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,get,reply.get),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,pet,reply.pet),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,debug,reply.debug),   4)
#define carbon_tractor_Reply_CALLBACK NULL
#define carbon_tractor_Reply_DEFAULT NULL
#define carbon_tractor_Reply_reply_set_MSGTYPE carbon_tractor_SetReply
#define carbon_tractor_Reply_reply_get_MSGTYPE carbon_tractor_GetReply
#define carbon_tractor_Reply_reply_pet_MSGTYPE carbon_tractor_TractorStatus
#define carbon_tractor_Reply_reply_debug_MSGTYPE carbon_tractor_DebugReply

extern const pb_msgdesc_t carbon_tractor_Empty_msg;
extern const pb_msgdesc_t carbon_tractor_HHState_msg;
extern const pb_msgdesc_t carbon_tractor_BrakeState_msg;
extern const pb_msgdesc_t carbon_tractor_SafetySensorsState_msg;
extern const pb_msgdesc_t carbon_tractor_SafetySensorBypassState_msg;
extern const pb_msgdesc_t carbon_tractor_TractorStatus_msg;
extern const pb_msgdesc_t carbon_tractor_PetRequest_msg;
extern const pb_msgdesc_t carbon_tractor_SteeringState_msg;
extern const pb_msgdesc_t carbon_tractor_SteeringCfgState_msg;
extern const pb_msgdesc_t carbon_tractor_PetLossState_msg;
extern const pb_msgdesc_t carbon_tractor_SpeedLimitState_msg;
extern const pb_msgdesc_t carbon_tractor_SetRequest_msg;
extern const pb_msgdesc_t carbon_tractor_SetReply_msg;
extern const pb_msgdesc_t carbon_tractor_GetRequest_msg;
extern const pb_msgdesc_t carbon_tractor_GetReply_msg;
extern const pb_msgdesc_t carbon_tractor_DebugRequest_msg;
extern const pb_msgdesc_t carbon_tractor_DebugReply_msg;
extern const pb_msgdesc_t carbon_tractor_Request_msg;
extern const pb_msgdesc_t carbon_tractor_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define carbon_tractor_Empty_fields &carbon_tractor_Empty_msg
#define carbon_tractor_HHState_fields &carbon_tractor_HHState_msg
#define carbon_tractor_BrakeState_fields &carbon_tractor_BrakeState_msg
#define carbon_tractor_SafetySensorsState_fields &carbon_tractor_SafetySensorsState_msg
#define carbon_tractor_SafetySensorBypassState_fields &carbon_tractor_SafetySensorBypassState_msg
#define carbon_tractor_TractorStatus_fields &carbon_tractor_TractorStatus_msg
#define carbon_tractor_PetRequest_fields &carbon_tractor_PetRequest_msg
#define carbon_tractor_SteeringState_fields &carbon_tractor_SteeringState_msg
#define carbon_tractor_SteeringCfgState_fields &carbon_tractor_SteeringCfgState_msg
#define carbon_tractor_PetLossState_fields &carbon_tractor_PetLossState_msg
#define carbon_tractor_SpeedLimitState_fields &carbon_tractor_SpeedLimitState_msg
#define carbon_tractor_SetRequest_fields &carbon_tractor_SetRequest_msg
#define carbon_tractor_SetReply_fields &carbon_tractor_SetReply_msg
#define carbon_tractor_GetRequest_fields &carbon_tractor_GetRequest_msg
#define carbon_tractor_GetReply_fields &carbon_tractor_GetReply_msg
#define carbon_tractor_DebugRequest_fields &carbon_tractor_DebugRequest_msg
#define carbon_tractor_DebugReply_fields &carbon_tractor_DebugReply_msg
#define carbon_tractor_Request_fields &carbon_tractor_Request_msg
#define carbon_tractor_Reply_fields &carbon_tractor_Reply_msg

/* Maximum encoded size of messages (where known) */
#define carbon_tractor_Empty_size                0
#define carbon_tractor_HHState_size              2
#define carbon_tractor_BrakeState_size           22
#define carbon_tractor_SafetySensorsState_size   8
#define carbon_tractor_SafetySensorBypassState_size 2
#if defined(ots_tractor_GearState_size)
#define carbon_tractor_TractorStatus_size        (53 + ots_tractor_GearState_size)
#endif
#define carbon_tractor_PetRequest_size           0
#define carbon_tractor_SteeringState_size        5
#define carbon_tractor_SteeringCfgState_size     43
#define carbon_tractor_PetLossState_size         2
#define carbon_tractor_SpeedLimitState_size      5
#define carbon_tractor_SetRequest_size           45
#define carbon_tractor_SetReply_size             45
#define carbon_tractor_GetRequest_size           2
#if defined(ots_tractor_GearState_size)
typedef union carbon_tractor_GetReply_get_size_union {char f1[(59 + ots_tractor_GearState_size)]; char f0[45];} carbon_tractor_GetReply_get_size_union;
#define carbon_tractor_GetReply_size             (0 + sizeof(carbon_tractor_GetReply_get_size_union))
#endif
#define carbon_tractor_DebugRequest_size         0
#define carbon_tractor_DebugReply_size           0
#define carbon_tractor_Request_size              47
#if defined(ots_tractor_GearState_size) && defined(ots_tractor_GearState_size)
typedef union carbon_tractor_Reply_reply_size_union {char f2[(6 + sizeof(carbon_tractor_GetReply_get_size_union))]; char f3[(59 + ots_tractor_GearState_size)]; char f0[47];} carbon_tractor_Reply_reply_size_union;
#define carbon_tractor_Reply_size                (0 + sizeof(carbon_tractor_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

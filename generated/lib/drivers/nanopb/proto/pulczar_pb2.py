# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/pulczar.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.drivers.nanopb.proto import gimbal_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_gimbal__pb2
from generated.lib.drivers.nanopb.proto import laser_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_laser__pb2
from generated.lib.drivers.nanopb.proto import lens_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_lens__pb2
from generated.lib.drivers.nanopb.proto import dawg_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_dawg__pb2
from generated.lib.drivers.nanopb.proto import ack_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2
from generated.lib.drivers.nanopb.proto import scanner_config_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_scanner__config__pb2
from generated.lib.drivers.nanopb.proto import arc_detector_pb2 as generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_arc__detector__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/pulczar.proto',
  package='pulczar',
  syntax='proto3',
  serialized_options=b'Z\016nanopb/pulczar',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n0generated/lib/drivers/nanopb/proto/pulczar.proto\x12\x07pulczar\x1a/generated/lib/drivers/nanopb/proto/gimbal.proto\x1a.generated/lib/drivers/nanopb/proto/laser.proto\x1a-generated/lib/drivers/nanopb/proto/lens.proto\x1a-generated/lib/drivers/nanopb/proto/dawg.proto\x1a,generated/lib/drivers/nanopb/proto/ack.proto\x1a\x37generated/lib/drivers/nanopb/proto/scanner_config.proto\x1a\x35generated/lib/drivers/nanopb/proto/arc_detector.proto\"X\n\x11\x41mbientTempConfig\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x16\n\x0euse_thermistor\x18\x02 \x01(\x08\x12\x11\n\x04temp\x18\x03 \x01(\x02H\x00\x88\x01\x01\x42\x07\n\x05_temp\"\x1c\n\x1a\x41mbientTempGetStateRequest\"\x8c\x01\n\x12\x41mbientTempRequest\x12\x30\n\nset_config\x18\x01 \x01(\x0b\x32\x1a.pulczar.AmbientTempConfigH\x00\x12\x39\n\nget_config\x18\x02 \x01(\x0b\x32#.pulczar.AmbientTempGetStateRequestH\x00\x42\t\n\x07request\"R\n\x10\x41mbientTempReply\x12\x12\n\nsense_temp\x18\x01 \x01(\x02\x12*\n\x06\x63onfig\x18\x02 \x01(\x0b\x32\x1a.pulczar.AmbientTempConfig\"\x12\n\x10HwStatus_Request\"\xd0\x01\n\x0eHwStatus_Servo\x12\x11\n\tconnected\x18\x01 \x01(\x08\x12\x15\n\rcontroller_sn\x18\x02 \x01(\r\x12\x16\n\x0esensor_time_ms\x18\x03 \x01(\x04\x12\x1b\n\x13output_stage_temp_c\x18\x04 \x01(\x02\x12\x16\n\x0emotor_supply_v\x18\x05 \x01(\x02\x12\x17\n\x0fmotor_current_a\x18\x06 \x01(\x02\x12\x17\n\x0f\x65ncoder_time_ms\x18\x07 \x01(\x04\x12\x15\n\rencoder_ticks\x18\x08 \x01(\x03\"\xf8\x02\n\x15HwStatus_Slayer_Reply\x12\"\n\x1alpm_thermistor_beam_raw_mv\x18\x01 \x01(\x02\x12\"\n\x1alpm_thermistor_beam_temp_c\x18\x02 \x01(\x02\x12%\n\x1dlpm_thermistor_ambient_raw_mv\x18\x03 \x01(\x02\x12%\n\x1dlpm_thermistor_ambient_temp_c\x18\x04 \x01(\x02\x12\x13\n\x0blpsu_status\x18\x05 \x01(\x08\x12\x17\n\x0flpsu_current_ma\x18\x06 \x01(\x02\x12/\n\tservo_pan\x18\x07 \x01(\x0b\x32\x17.pulczar.HwStatus_ServoH\x00\x88\x01\x01\x12\x30\n\nservo_tilt\x18\x08 \x01(\x0b\x32\x17.pulczar.HwStatus_ServoH\x01\x88\x01\x01\x12\x1b\n\x13target_cam_power_on\x18\t \x01(\x08\x42\x0c\n\n_servo_panB\r\n\x0b_servo_tilt\"\x84\x03\n\x15HwStatus_Reaper_Reply\x12\x17\n\x0flaser_connected\x18\x01 \x01(\x08\x12\x35\n\x0flaser_inventory\x18\x02 \x01(\x0b\x32\x1c.laser.Laser_Inventory_Reply\x12/\n\x0claser_status\x18\x03 \x01(\x0b\x32\x19.laser.Diode_Status_Reply\x12\x1a\n\x12laser_therm_temp_c\x18\x04 \x03(\x02\x12\x15\n\rlaser_power_w\x18\x05 \x01(\x02\x12\x1a\n\x12laser_power_raw_mv\x18\x06 \x01(\x02\x12/\n\tservo_pan\x18\x07 \x01(\x0b\x32\x17.pulczar.HwStatus_ServoH\x00\x88\x01\x01\x12\x30\n\nservo_tilt\x18\x08 \x01(\x0b\x32\x17.pulczar.HwStatus_ServoH\x01\x88\x01\x01\x12\x1b\n\x13target_cam_power_on\x18\t \x01(\x08\x42\x0c\n\n_servo_panB\r\n\x0b_servo_tilt\"}\n\x0eHwStatus_Reply\x12\x30\n\x06slayer\x18\x01 \x01(\x0b\x32\x1e.pulczar.HwStatus_Slayer_ReplyH\x00\x12\x30\n\x06reaper\x18\x02 \x01(\x0b\x32\x1e.pulczar.HwStatus_Reaper_ReplyH\x00\x42\x07\n\x05reply\"\x0f\n\rReset_Request\"\x16\n\x14\x43lear_Config_Request\"\x10\n\x0eStatus_Request\"$\n\x10Override_Request\x12\x10\n\x08override\x18\x01 \x01(\r\"_\n\rPower_Request\x12\x16\n\ttargetCam\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x18\n\x0b\x66iringBoard\x18\x02 \x01(\x08H\x01\x88\x01\x01\x42\x0c\n\n_targetCamB\x0e\n\x0c_firingBoard\"O\n\x0cStatus_Reply\x12\x0e\n\x06status\x18\x01 \x01(\r\x12/\n\x0claser_status\x18\x02 \x01(\x0b\x32\x19.laser.Laser_Status_Reply\"5\n\x0bPower_Reply\x12\x11\n\ttargetCam\x18\x01 \x01(\x08\x12\x13\n\x0b\x66iringBoard\x18\x02 \x01(\x08\"\xa6\x04\n\x07Request\x12\'\n\x05reset\x18\x01 \x01(\x0b\x32\x16.pulczar.Reset_RequestH\x00\x12.\n\x05\x63lear\x18\x02 \x01(\x0b\x32\x1d.pulczar.Clear_Config_RequestH\x00\x12!\n\x06gimbal\x18\x03 \x01(\x0b\x32\x0f.gimbal.RequestH\x00\x12\x1d\n\x04\x64\x61wg\x18\x04 \x01(\x0b\x32\r.dawg.RequestH\x00\x12\x1f\n\x05laser\x18\x05 \x01(\x0b\x32\x0e.laser.RequestH\x00\x12)\n\x06status\x18\x06 \x01(\x0b\x32\x17.pulczar.Status_RequestH\x00\x12\x1d\n\x04lens\x18\x07 \x01(\x0b\x32\r.lens.RequestH\x00\x12-\n\x08override\x18\x08 \x01(\x0b\x32\x19.pulczar.Override_RequestH\x00\x12\'\n\x04\x63onf\x18\t \x01(\x0b\x32\x17.scanner_config.RequestH\x00\x12$\n\x03\x61rc\x18\n \x01(\x0b\x32\x15.arc_detector.RequestH\x00\x12\'\n\x05power\x18\x0b \x01(\x0b\x32\x16.pulczar.Power_RequestH\x00\x12.\n\thw_status\x18\x0c \x01(\x0b\x32\x19.pulczar.HwStatus_RequestH\x00\x12\x33\n\x0c\x61mbient_temp\x18\r \x01(\x0b\x32\x1b.pulczar.AmbientTempRequestH\x00\x42\t\n\x07request\"\x9f\x03\n\x05Reply\x12\x17\n\x03\x61\x63k\x18\x01 \x01(\x0b\x32\x08.ack.AckH\x00\x12\x1f\n\x06gimbal\x18\x02 \x01(\x0b\x32\r.gimbal.ReplyH\x00\x12\x1b\n\x04\x64\x61wg\x18\x03 \x01(\x0b\x32\x0b.dawg.ReplyH\x00\x12\x1d\n\x05laser\x18\x04 \x01(\x0b\x32\x0c.laser.ReplyH\x00\x12\'\n\x06status\x18\x05 \x01(\x0b\x32\x15.pulczar.Status_ReplyH\x00\x12\x1b\n\x04lens\x18\x06 \x01(\x0b\x32\x0b.lens.ReplyH\x00\x12%\n\x04\x63onf\x18\x07 \x01(\x0b\x32\x15.scanner_config.ReplyH\x00\x12\"\n\x03\x61rc\x18\x08 \x01(\x0b\x32\x13.arc_detector.ReplyH\x00\x12%\n\x05power\x18\t \x01(\x0b\x32\x14.pulczar.Power_ReplyH\x00\x12,\n\thw_status\x18\n \x01(\x0b\x32\x17.pulczar.HwStatus_ReplyH\x00\x12\x31\n\x0c\x61mbient_temp\x18\x0b \x01(\x0b\x32\x19.pulczar.AmbientTempReplyH\x00\x42\x07\n\x05replyB\x10Z\x0enanopb/pulczarb\x06proto3'
  ,
  dependencies=[generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_gimbal__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_laser__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_lens__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_dawg__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_scanner__config__pb2.DESCRIPTOR,generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_arc__detector__pb2.DESCRIPTOR,])




_AMBIENTTEMPCONFIG = _descriptor.Descriptor(
  name='AmbientTempConfig',
  full_name='pulczar.AmbientTempConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='pulczar.AmbientTempConfig.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_thermistor', full_name='pulczar.AmbientTempConfig.use_thermistor', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp', full_name='pulczar.AmbientTempConfig.temp', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_temp', full_name='pulczar.AmbientTempConfig._temp',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=410,
  serialized_end=498,
)


_AMBIENTTEMPGETSTATEREQUEST = _descriptor.Descriptor(
  name='AmbientTempGetStateRequest',
  full_name='pulczar.AmbientTempGetStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=500,
  serialized_end=528,
)


_AMBIENTTEMPREQUEST = _descriptor.Descriptor(
  name='AmbientTempRequest',
  full_name='pulczar.AmbientTempRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='set_config', full_name='pulczar.AmbientTempRequest.set_config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_config', full_name='pulczar.AmbientTempRequest.get_config', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='pulczar.AmbientTempRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=531,
  serialized_end=671,
)


_AMBIENTTEMPREPLY = _descriptor.Descriptor(
  name='AmbientTempReply',
  full_name='pulczar.AmbientTempReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sense_temp', full_name='pulczar.AmbientTempReply.sense_temp', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='config', full_name='pulczar.AmbientTempReply.config', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=673,
  serialized_end=755,
)


_HWSTATUS_REQUEST = _descriptor.Descriptor(
  name='HwStatus_Request',
  full_name='pulczar.HwStatus_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=757,
  serialized_end=775,
)


_HWSTATUS_SERVO = _descriptor.Descriptor(
  name='HwStatus_Servo',
  full_name='pulczar.HwStatus_Servo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='connected', full_name='pulczar.HwStatus_Servo.connected', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='controller_sn', full_name='pulczar.HwStatus_Servo.controller_sn', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor_time_ms', full_name='pulczar.HwStatus_Servo.sensor_time_ms', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='output_stage_temp_c', full_name='pulczar.HwStatus_Servo.output_stage_temp_c', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_supply_v', full_name='pulczar.HwStatus_Servo.motor_supply_v', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_current_a', full_name='pulczar.HwStatus_Servo.motor_current_a', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='encoder_time_ms', full_name='pulczar.HwStatus_Servo.encoder_time_ms', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='encoder_ticks', full_name='pulczar.HwStatus_Servo.encoder_ticks', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=778,
  serialized_end=986,
)


_HWSTATUS_SLAYER_REPLY = _descriptor.Descriptor(
  name='HwStatus_Slayer_Reply',
  full_name='pulczar.HwStatus_Slayer_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lpm_thermistor_beam_raw_mv', full_name='pulczar.HwStatus_Slayer_Reply.lpm_thermistor_beam_raw_mv', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lpm_thermistor_beam_temp_c', full_name='pulczar.HwStatus_Slayer_Reply.lpm_thermistor_beam_temp_c', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lpm_thermistor_ambient_raw_mv', full_name='pulczar.HwStatus_Slayer_Reply.lpm_thermistor_ambient_raw_mv', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lpm_thermistor_ambient_temp_c', full_name='pulczar.HwStatus_Slayer_Reply.lpm_thermistor_ambient_temp_c', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lpsu_status', full_name='pulczar.HwStatus_Slayer_Reply.lpsu_status', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lpsu_current_ma', full_name='pulczar.HwStatus_Slayer_Reply.lpsu_current_ma', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='servo_pan', full_name='pulczar.HwStatus_Slayer_Reply.servo_pan', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='servo_tilt', full_name='pulczar.HwStatus_Slayer_Reply.servo_tilt', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_cam_power_on', full_name='pulczar.HwStatus_Slayer_Reply.target_cam_power_on', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_servo_pan', full_name='pulczar.HwStatus_Slayer_Reply._servo_pan',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_servo_tilt', full_name='pulczar.HwStatus_Slayer_Reply._servo_tilt',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=989,
  serialized_end=1365,
)


_HWSTATUS_REAPER_REPLY = _descriptor.Descriptor(
  name='HwStatus_Reaper_Reply',
  full_name='pulczar.HwStatus_Reaper_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='laser_connected', full_name='pulczar.HwStatus_Reaper_Reply.laser_connected', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_inventory', full_name='pulczar.HwStatus_Reaper_Reply.laser_inventory', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_status', full_name='pulczar.HwStatus_Reaper_Reply.laser_status', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_therm_temp_c', full_name='pulczar.HwStatus_Reaper_Reply.laser_therm_temp_c', index=3,
      number=4, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_power_w', full_name='pulczar.HwStatus_Reaper_Reply.laser_power_w', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_power_raw_mv', full_name='pulczar.HwStatus_Reaper_Reply.laser_power_raw_mv', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='servo_pan', full_name='pulczar.HwStatus_Reaper_Reply.servo_pan', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='servo_tilt', full_name='pulczar.HwStatus_Reaper_Reply.servo_tilt', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_cam_power_on', full_name='pulczar.HwStatus_Reaper_Reply.target_cam_power_on', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_servo_pan', full_name='pulczar.HwStatus_Reaper_Reply._servo_pan',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_servo_tilt', full_name='pulczar.HwStatus_Reaper_Reply._servo_tilt',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1368,
  serialized_end=1756,
)


_HWSTATUS_REPLY = _descriptor.Descriptor(
  name='HwStatus_Reply',
  full_name='pulczar.HwStatus_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='slayer', full_name='pulczar.HwStatus_Reply.slayer', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reaper', full_name='pulczar.HwStatus_Reply.reaper', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='pulczar.HwStatus_Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1758,
  serialized_end=1883,
)


_RESET_REQUEST = _descriptor.Descriptor(
  name='Reset_Request',
  full_name='pulczar.Reset_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1885,
  serialized_end=1900,
)


_CLEAR_CONFIG_REQUEST = _descriptor.Descriptor(
  name='Clear_Config_Request',
  full_name='pulczar.Clear_Config_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1902,
  serialized_end=1924,
)


_STATUS_REQUEST = _descriptor.Descriptor(
  name='Status_Request',
  full_name='pulczar.Status_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1926,
  serialized_end=1942,
)


_OVERRIDE_REQUEST = _descriptor.Descriptor(
  name='Override_Request',
  full_name='pulczar.Override_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='override', full_name='pulczar.Override_Request.override', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1944,
  serialized_end=1980,
)


_POWER_REQUEST = _descriptor.Descriptor(
  name='Power_Request',
  full_name='pulczar.Power_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='targetCam', full_name='pulczar.Power_Request.targetCam', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='firingBoard', full_name='pulczar.Power_Request.firingBoard', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_targetCam', full_name='pulczar.Power_Request._targetCam',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_firingBoard', full_name='pulczar.Power_Request._firingBoard',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1982,
  serialized_end=2077,
)


_STATUS_REPLY = _descriptor.Descriptor(
  name='Status_Reply',
  full_name='pulczar.Status_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='pulczar.Status_Reply.status', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_status', full_name='pulczar.Status_Reply.laser_status', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2079,
  serialized_end=2158,
)


_POWER_REPLY = _descriptor.Descriptor(
  name='Power_Reply',
  full_name='pulczar.Power_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='targetCam', full_name='pulczar.Power_Reply.targetCam', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='firingBoard', full_name='pulczar.Power_Reply.firingBoard', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2160,
  serialized_end=2213,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='pulczar.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='reset', full_name='pulczar.Request.reset', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='clear', full_name='pulczar.Request.clear', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gimbal', full_name='pulczar.Request.gimbal', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dawg', full_name='pulczar.Request.dawg', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser', full_name='pulczar.Request.laser', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='pulczar.Request.status', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lens', full_name='pulczar.Request.lens', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='override', full_name='pulczar.Request.override', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='conf', full_name='pulczar.Request.conf', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='arc', full_name='pulczar.Request.arc', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='pulczar.Request.power', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hw_status', full_name='pulczar.Request.hw_status', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ambient_temp', full_name='pulczar.Request.ambient_temp', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='pulczar.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2216,
  serialized_end=2766,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='pulczar.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ack', full_name='pulczar.Reply.ack', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gimbal', full_name='pulczar.Reply.gimbal', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dawg', full_name='pulczar.Reply.dawg', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser', full_name='pulczar.Reply.laser', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='pulczar.Reply.status', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lens', full_name='pulczar.Reply.lens', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='conf', full_name='pulczar.Reply.conf', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='arc', full_name='pulczar.Reply.arc', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power', full_name='pulczar.Reply.power', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hw_status', full_name='pulczar.Reply.hw_status', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ambient_temp', full_name='pulczar.Reply.ambient_temp', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='pulczar.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2769,
  serialized_end=3184,
)

_AMBIENTTEMPCONFIG.oneofs_by_name['_temp'].fields.append(
  _AMBIENTTEMPCONFIG.fields_by_name['temp'])
_AMBIENTTEMPCONFIG.fields_by_name['temp'].containing_oneof = _AMBIENTTEMPCONFIG.oneofs_by_name['_temp']
_AMBIENTTEMPREQUEST.fields_by_name['set_config'].message_type = _AMBIENTTEMPCONFIG
_AMBIENTTEMPREQUEST.fields_by_name['get_config'].message_type = _AMBIENTTEMPGETSTATEREQUEST
_AMBIENTTEMPREQUEST.oneofs_by_name['request'].fields.append(
  _AMBIENTTEMPREQUEST.fields_by_name['set_config'])
_AMBIENTTEMPREQUEST.fields_by_name['set_config'].containing_oneof = _AMBIENTTEMPREQUEST.oneofs_by_name['request']
_AMBIENTTEMPREQUEST.oneofs_by_name['request'].fields.append(
  _AMBIENTTEMPREQUEST.fields_by_name['get_config'])
_AMBIENTTEMPREQUEST.fields_by_name['get_config'].containing_oneof = _AMBIENTTEMPREQUEST.oneofs_by_name['request']
_AMBIENTTEMPREPLY.fields_by_name['config'].message_type = _AMBIENTTEMPCONFIG
_HWSTATUS_SLAYER_REPLY.fields_by_name['servo_pan'].message_type = _HWSTATUS_SERVO
_HWSTATUS_SLAYER_REPLY.fields_by_name['servo_tilt'].message_type = _HWSTATUS_SERVO
_HWSTATUS_SLAYER_REPLY.oneofs_by_name['_servo_pan'].fields.append(
  _HWSTATUS_SLAYER_REPLY.fields_by_name['servo_pan'])
_HWSTATUS_SLAYER_REPLY.fields_by_name['servo_pan'].containing_oneof = _HWSTATUS_SLAYER_REPLY.oneofs_by_name['_servo_pan']
_HWSTATUS_SLAYER_REPLY.oneofs_by_name['_servo_tilt'].fields.append(
  _HWSTATUS_SLAYER_REPLY.fields_by_name['servo_tilt'])
_HWSTATUS_SLAYER_REPLY.fields_by_name['servo_tilt'].containing_oneof = _HWSTATUS_SLAYER_REPLY.oneofs_by_name['_servo_tilt']
_HWSTATUS_REAPER_REPLY.fields_by_name['laser_inventory'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_laser__pb2._LASER_INVENTORY_REPLY
_HWSTATUS_REAPER_REPLY.fields_by_name['laser_status'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_laser__pb2._DIODE_STATUS_REPLY
_HWSTATUS_REAPER_REPLY.fields_by_name['servo_pan'].message_type = _HWSTATUS_SERVO
_HWSTATUS_REAPER_REPLY.fields_by_name['servo_tilt'].message_type = _HWSTATUS_SERVO
_HWSTATUS_REAPER_REPLY.oneofs_by_name['_servo_pan'].fields.append(
  _HWSTATUS_REAPER_REPLY.fields_by_name['servo_pan'])
_HWSTATUS_REAPER_REPLY.fields_by_name['servo_pan'].containing_oneof = _HWSTATUS_REAPER_REPLY.oneofs_by_name['_servo_pan']
_HWSTATUS_REAPER_REPLY.oneofs_by_name['_servo_tilt'].fields.append(
  _HWSTATUS_REAPER_REPLY.fields_by_name['servo_tilt'])
_HWSTATUS_REAPER_REPLY.fields_by_name['servo_tilt'].containing_oneof = _HWSTATUS_REAPER_REPLY.oneofs_by_name['_servo_tilt']
_HWSTATUS_REPLY.fields_by_name['slayer'].message_type = _HWSTATUS_SLAYER_REPLY
_HWSTATUS_REPLY.fields_by_name['reaper'].message_type = _HWSTATUS_REAPER_REPLY
_HWSTATUS_REPLY.oneofs_by_name['reply'].fields.append(
  _HWSTATUS_REPLY.fields_by_name['slayer'])
_HWSTATUS_REPLY.fields_by_name['slayer'].containing_oneof = _HWSTATUS_REPLY.oneofs_by_name['reply']
_HWSTATUS_REPLY.oneofs_by_name['reply'].fields.append(
  _HWSTATUS_REPLY.fields_by_name['reaper'])
_HWSTATUS_REPLY.fields_by_name['reaper'].containing_oneof = _HWSTATUS_REPLY.oneofs_by_name['reply']
_POWER_REQUEST.oneofs_by_name['_targetCam'].fields.append(
  _POWER_REQUEST.fields_by_name['targetCam'])
_POWER_REQUEST.fields_by_name['targetCam'].containing_oneof = _POWER_REQUEST.oneofs_by_name['_targetCam']
_POWER_REQUEST.oneofs_by_name['_firingBoard'].fields.append(
  _POWER_REQUEST.fields_by_name['firingBoard'])
_POWER_REQUEST.fields_by_name['firingBoard'].containing_oneof = _POWER_REQUEST.oneofs_by_name['_firingBoard']
_STATUS_REPLY.fields_by_name['laser_status'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_laser__pb2._LASER_STATUS_REPLY
_REQUEST.fields_by_name['reset'].message_type = _RESET_REQUEST
_REQUEST.fields_by_name['clear'].message_type = _CLEAR_CONFIG_REQUEST
_REQUEST.fields_by_name['gimbal'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_gimbal__pb2._REQUEST
_REQUEST.fields_by_name['dawg'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_dawg__pb2._REQUEST
_REQUEST.fields_by_name['laser'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_laser__pb2._REQUEST
_REQUEST.fields_by_name['status'].message_type = _STATUS_REQUEST
_REQUEST.fields_by_name['lens'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_lens__pb2._REQUEST
_REQUEST.fields_by_name['override'].message_type = _OVERRIDE_REQUEST
_REQUEST.fields_by_name['conf'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_scanner__config__pb2._REQUEST
_REQUEST.fields_by_name['arc'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_arc__detector__pb2._REQUEST
_REQUEST.fields_by_name['power'].message_type = _POWER_REQUEST
_REQUEST.fields_by_name['hw_status'].message_type = _HWSTATUS_REQUEST
_REQUEST.fields_by_name['ambient_temp'].message_type = _AMBIENTTEMPREQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['reset'])
_REQUEST.fields_by_name['reset'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['clear'])
_REQUEST.fields_by_name['clear'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['gimbal'])
_REQUEST.fields_by_name['gimbal'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['dawg'])
_REQUEST.fields_by_name['dawg'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['laser'])
_REQUEST.fields_by_name['laser'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['status'])
_REQUEST.fields_by_name['status'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['lens'])
_REQUEST.fields_by_name['lens'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['override'])
_REQUEST.fields_by_name['override'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['conf'])
_REQUEST.fields_by_name['conf'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['arc'])
_REQUEST.fields_by_name['arc'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['power'])
_REQUEST.fields_by_name['power'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['hw_status'])
_REQUEST.fields_by_name['hw_status'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['ambient_temp'])
_REQUEST.fields_by_name['ambient_temp'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['ack'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_ack__pb2._ACK
_REPLY.fields_by_name['gimbal'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_gimbal__pb2._REPLY
_REPLY.fields_by_name['dawg'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_dawg__pb2._REPLY
_REPLY.fields_by_name['laser'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_laser__pb2._REPLY
_REPLY.fields_by_name['status'].message_type = _STATUS_REPLY
_REPLY.fields_by_name['lens'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_lens__pb2._REPLY
_REPLY.fields_by_name['conf'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_scanner__config__pb2._REPLY
_REPLY.fields_by_name['arc'].message_type = generated_dot_lib_dot_drivers_dot_nanopb_dot_proto_dot_arc__detector__pb2._REPLY
_REPLY.fields_by_name['power'].message_type = _POWER_REPLY
_REPLY.fields_by_name['hw_status'].message_type = _HWSTATUS_REPLY
_REPLY.fields_by_name['ambient_temp'].message_type = _AMBIENTTEMPREPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ack'])
_REPLY.fields_by_name['ack'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['gimbal'])
_REPLY.fields_by_name['gimbal'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['dawg'])
_REPLY.fields_by_name['dawg'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['laser'])
_REPLY.fields_by_name['laser'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['status'])
_REPLY.fields_by_name['status'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['lens'])
_REPLY.fields_by_name['lens'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['conf'])
_REPLY.fields_by_name['conf'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['arc'])
_REPLY.fields_by_name['arc'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['power'])
_REPLY.fields_by_name['power'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['hw_status'])
_REPLY.fields_by_name['hw_status'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['ambient_temp'])
_REPLY.fields_by_name['ambient_temp'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['AmbientTempConfig'] = _AMBIENTTEMPCONFIG
DESCRIPTOR.message_types_by_name['AmbientTempGetStateRequest'] = _AMBIENTTEMPGETSTATEREQUEST
DESCRIPTOR.message_types_by_name['AmbientTempRequest'] = _AMBIENTTEMPREQUEST
DESCRIPTOR.message_types_by_name['AmbientTempReply'] = _AMBIENTTEMPREPLY
DESCRIPTOR.message_types_by_name['HwStatus_Request'] = _HWSTATUS_REQUEST
DESCRIPTOR.message_types_by_name['HwStatus_Servo'] = _HWSTATUS_SERVO
DESCRIPTOR.message_types_by_name['HwStatus_Slayer_Reply'] = _HWSTATUS_SLAYER_REPLY
DESCRIPTOR.message_types_by_name['HwStatus_Reaper_Reply'] = _HWSTATUS_REAPER_REPLY
DESCRIPTOR.message_types_by_name['HwStatus_Reply'] = _HWSTATUS_REPLY
DESCRIPTOR.message_types_by_name['Reset_Request'] = _RESET_REQUEST
DESCRIPTOR.message_types_by_name['Clear_Config_Request'] = _CLEAR_CONFIG_REQUEST
DESCRIPTOR.message_types_by_name['Status_Request'] = _STATUS_REQUEST
DESCRIPTOR.message_types_by_name['Override_Request'] = _OVERRIDE_REQUEST
DESCRIPTOR.message_types_by_name['Power_Request'] = _POWER_REQUEST
DESCRIPTOR.message_types_by_name['Status_Reply'] = _STATUS_REPLY
DESCRIPTOR.message_types_by_name['Power_Reply'] = _POWER_REPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AmbientTempConfig = _reflection.GeneratedProtocolMessageType('AmbientTempConfig', (_message.Message,), {
  'DESCRIPTOR' : _AMBIENTTEMPCONFIG,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.AmbientTempConfig)
  })
_sym_db.RegisterMessage(AmbientTempConfig)

AmbientTempGetStateRequest = _reflection.GeneratedProtocolMessageType('AmbientTempGetStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _AMBIENTTEMPGETSTATEREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.AmbientTempGetStateRequest)
  })
_sym_db.RegisterMessage(AmbientTempGetStateRequest)

AmbientTempRequest = _reflection.GeneratedProtocolMessageType('AmbientTempRequest', (_message.Message,), {
  'DESCRIPTOR' : _AMBIENTTEMPREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.AmbientTempRequest)
  })
_sym_db.RegisterMessage(AmbientTempRequest)

AmbientTempReply = _reflection.GeneratedProtocolMessageType('AmbientTempReply', (_message.Message,), {
  'DESCRIPTOR' : _AMBIENTTEMPREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.AmbientTempReply)
  })
_sym_db.RegisterMessage(AmbientTempReply)

HwStatus_Request = _reflection.GeneratedProtocolMessageType('HwStatus_Request', (_message.Message,), {
  'DESCRIPTOR' : _HWSTATUS_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.HwStatus_Request)
  })
_sym_db.RegisterMessage(HwStatus_Request)

HwStatus_Servo = _reflection.GeneratedProtocolMessageType('HwStatus_Servo', (_message.Message,), {
  'DESCRIPTOR' : _HWSTATUS_SERVO,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.HwStatus_Servo)
  })
_sym_db.RegisterMessage(HwStatus_Servo)

HwStatus_Slayer_Reply = _reflection.GeneratedProtocolMessageType('HwStatus_Slayer_Reply', (_message.Message,), {
  'DESCRIPTOR' : _HWSTATUS_SLAYER_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.HwStatus_Slayer_Reply)
  })
_sym_db.RegisterMessage(HwStatus_Slayer_Reply)

HwStatus_Reaper_Reply = _reflection.GeneratedProtocolMessageType('HwStatus_Reaper_Reply', (_message.Message,), {
  'DESCRIPTOR' : _HWSTATUS_REAPER_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.HwStatus_Reaper_Reply)
  })
_sym_db.RegisterMessage(HwStatus_Reaper_Reply)

HwStatus_Reply = _reflection.GeneratedProtocolMessageType('HwStatus_Reply', (_message.Message,), {
  'DESCRIPTOR' : _HWSTATUS_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.HwStatus_Reply)
  })
_sym_db.RegisterMessage(HwStatus_Reply)

Reset_Request = _reflection.GeneratedProtocolMessageType('Reset_Request', (_message.Message,), {
  'DESCRIPTOR' : _RESET_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.Reset_Request)
  })
_sym_db.RegisterMessage(Reset_Request)

Clear_Config_Request = _reflection.GeneratedProtocolMessageType('Clear_Config_Request', (_message.Message,), {
  'DESCRIPTOR' : _CLEAR_CONFIG_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.Clear_Config_Request)
  })
_sym_db.RegisterMessage(Clear_Config_Request)

Status_Request = _reflection.GeneratedProtocolMessageType('Status_Request', (_message.Message,), {
  'DESCRIPTOR' : _STATUS_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.Status_Request)
  })
_sym_db.RegisterMessage(Status_Request)

Override_Request = _reflection.GeneratedProtocolMessageType('Override_Request', (_message.Message,), {
  'DESCRIPTOR' : _OVERRIDE_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.Override_Request)
  })
_sym_db.RegisterMessage(Override_Request)

Power_Request = _reflection.GeneratedProtocolMessageType('Power_Request', (_message.Message,), {
  'DESCRIPTOR' : _POWER_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.Power_Request)
  })
_sym_db.RegisterMessage(Power_Request)

Status_Reply = _reflection.GeneratedProtocolMessageType('Status_Reply', (_message.Message,), {
  'DESCRIPTOR' : _STATUS_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.Status_Reply)
  })
_sym_db.RegisterMessage(Status_Reply)

Power_Reply = _reflection.GeneratedProtocolMessageType('Power_Reply', (_message.Message,), {
  'DESCRIPTOR' : _POWER_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.Power_Reply)
  })
_sym_db.RegisterMessage(Power_Reply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.pulczar_pb2'
  # @@protoc_insertion_point(class_scope:pulczar.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

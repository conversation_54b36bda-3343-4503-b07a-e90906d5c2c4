/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "version.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(version_Reset_Request, version_Reset_Request, AUTO)


PB_BIND(version_Version_Request, version_Version_Request, AUTO)


PB_BIND(version_Version_Reply, version_Version_Reply, AUTO)




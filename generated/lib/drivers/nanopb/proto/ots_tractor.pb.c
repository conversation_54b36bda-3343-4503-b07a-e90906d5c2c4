/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "ots_tractor.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(ots_tractor_GearState, ots_tractor_GearState, AUTO)


PB_BIND(ots_tractor_LightsState, ots_tractor_LightsState, AUTO)


PB_BIND(ots_tractor_SpeedControlState, ots_tractor_SpeedControlState, AUTO)


PB_BIND(ots_tractor_EngineRpmState, ots_tractor_EngineRpmState, AUTO)


PB_BIND(ots_tractor_PtoState, ots_tractor_PtoState, AUTO)


PB_BIND(ots_tractor_HitchV2Request, ots_tractor_HitchV2Request, AUTO)


PB_BIND(ots_tractor_HitchRequest, ots_tractor_HitchRequest, AUTO)


PB_BIND(ots_tractor_HitchReply, ots_tractor_HitchReply, AUTO)


PB_BIND(ots_tractor_ScvRequest, ots_tractor_ScvRequest, AUTO)


PB_BIND(ots_tractor_ScvReply, ots_tractor_ScvReply, AUTO)


PB_BIND(ots_tractor_TractorVariantState, ots_tractor_TractorVariantState, AUTO)


PB_BIND(ots_tractor_WheelAngleCalState, ots_tractor_WheelAngleCalState, AUTO)


PB_BIND(ots_tractor_FuelLevel, ots_tractor_FuelLevel, AUTO)


PB_BIND(ots_tractor_EngineTemp, ots_tractor_EngineTemp, AUTO)


PB_BIND(ots_tractor_Empty, ots_tractor_Empty, AUTO)


PB_BIND(ots_tractor_SetRequest, ots_tractor_SetRequest, AUTO)


PB_BIND(ots_tractor_SetReply, ots_tractor_SetReply, AUTO)


PB_BIND(ots_tractor_GetRequest, ots_tractor_GetRequest, AUTO)


PB_BIND(ots_tractor_GetReply, ots_tractor_GetReply, AUTO)


PB_BIND(ots_tractor_Request, ots_tractor_Request, AUTO)


PB_BIND(ots_tractor_Reply, ots_tractor_Reply, AUTO)








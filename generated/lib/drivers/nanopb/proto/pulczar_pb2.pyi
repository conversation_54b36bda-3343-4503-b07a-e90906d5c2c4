"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.arc_detector_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___arc_detector_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___arc_detector_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.dawg_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___dawg_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___dawg_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.gimbal_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___gimbal_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___gimbal_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.laser_pb2 import (
    Diode_Status_Reply as generated___lib___drivers___nanopb___proto___laser_pb2___Diode_Status_Reply,
    Laser_Inventory_Reply as generated___lib___drivers___nanopb___proto___laser_pb2___Laser_Inventory_Reply,
    Laser_Status_Reply as generated___lib___drivers___nanopb___proto___laser_pb2___Laser_Status_Reply,
    Reply as generated___lib___drivers___nanopb___proto___laser_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___laser_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.lens_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___lens_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___lens_pb2___Request,
)

from generated.lib.drivers.nanopb.proto.scanner_config_pb2 import (
    Reply as generated___lib___drivers___nanopb___proto___scanner_config_pb2___Reply,
    Request as generated___lib___drivers___nanopb___proto___scanner_config_pb2___Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class AmbientTempConfig(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...
    use_thermistor: builtin___bool = ...
    temp: builtin___float = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        use_thermistor : typing___Optional[builtin___bool] = None,
        temp : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_temp",b"_temp",u"temp",b"temp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_temp",b"_temp",u"enabled",b"enabled",u"temp",b"temp",u"use_thermistor",b"use_thermistor"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_temp",b"_temp"]) -> typing_extensions___Literal["temp"]: ...
type___AmbientTempConfig = AmbientTempConfig

class AmbientTempGetStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___AmbientTempGetStateRequest = AmbientTempGetStateRequest

class AmbientTempRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def set_config(self) -> type___AmbientTempConfig: ...

    @property
    def get_config(self) -> type___AmbientTempGetStateRequest: ...

    def __init__(self,
        *,
        set_config : typing___Optional[type___AmbientTempConfig] = None,
        get_config : typing___Optional[type___AmbientTempGetStateRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"get_config",b"get_config",u"request",b"request",u"set_config",b"set_config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"get_config",b"get_config",u"request",b"request",u"set_config",b"set_config"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["set_config","get_config"]: ...
type___AmbientTempRequest = AmbientTempRequest

class AmbientTempReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    sense_temp: builtin___float = ...

    @property
    def config(self) -> type___AmbientTempConfig: ...

    def __init__(self,
        *,
        sense_temp : typing___Optional[builtin___float] = None,
        config : typing___Optional[type___AmbientTempConfig] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"config",b"config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"config",b"config",u"sense_temp",b"sense_temp"]) -> None: ...
type___AmbientTempReply = AmbientTempReply

class HwStatus_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___HwStatus_Request = HwStatus_Request

class HwStatus_Servo(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    connected: builtin___bool = ...
    controller_sn: builtin___int = ...
    sensor_time_ms: builtin___int = ...
    output_stage_temp_c: builtin___float = ...
    motor_supply_v: builtin___float = ...
    motor_current_a: builtin___float = ...
    encoder_time_ms: builtin___int = ...
    encoder_ticks: builtin___int = ...

    def __init__(self,
        *,
        connected : typing___Optional[builtin___bool] = None,
        controller_sn : typing___Optional[builtin___int] = None,
        sensor_time_ms : typing___Optional[builtin___int] = None,
        output_stage_temp_c : typing___Optional[builtin___float] = None,
        motor_supply_v : typing___Optional[builtin___float] = None,
        motor_current_a : typing___Optional[builtin___float] = None,
        encoder_time_ms : typing___Optional[builtin___int] = None,
        encoder_ticks : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"connected",b"connected",u"controller_sn",b"controller_sn",u"encoder_ticks",b"encoder_ticks",u"encoder_time_ms",b"encoder_time_ms",u"motor_current_a",b"motor_current_a",u"motor_supply_v",b"motor_supply_v",u"output_stage_temp_c",b"output_stage_temp_c",u"sensor_time_ms",b"sensor_time_ms"]) -> None: ...
type___HwStatus_Servo = HwStatus_Servo

class HwStatus_Slayer_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lpm_thermistor_beam_raw_mv: builtin___float = ...
    lpm_thermistor_beam_temp_c: builtin___float = ...
    lpm_thermistor_ambient_raw_mv: builtin___float = ...
    lpm_thermistor_ambient_temp_c: builtin___float = ...
    lpsu_status: builtin___bool = ...
    lpsu_current_ma: builtin___float = ...
    target_cam_power_on: builtin___bool = ...

    @property
    def servo_pan(self) -> type___HwStatus_Servo: ...

    @property
    def servo_tilt(self) -> type___HwStatus_Servo: ...

    def __init__(self,
        *,
        lpm_thermistor_beam_raw_mv : typing___Optional[builtin___float] = None,
        lpm_thermistor_beam_temp_c : typing___Optional[builtin___float] = None,
        lpm_thermistor_ambient_raw_mv : typing___Optional[builtin___float] = None,
        lpm_thermistor_ambient_temp_c : typing___Optional[builtin___float] = None,
        lpsu_status : typing___Optional[builtin___bool] = None,
        lpsu_current_ma : typing___Optional[builtin___float] = None,
        servo_pan : typing___Optional[type___HwStatus_Servo] = None,
        servo_tilt : typing___Optional[type___HwStatus_Servo] = None,
        target_cam_power_on : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_servo_pan",b"_servo_pan",u"_servo_tilt",b"_servo_tilt",u"servo_pan",b"servo_pan",u"servo_tilt",b"servo_tilt"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_servo_pan",b"_servo_pan",u"_servo_tilt",b"_servo_tilt",u"lpm_thermistor_ambient_raw_mv",b"lpm_thermistor_ambient_raw_mv",u"lpm_thermistor_ambient_temp_c",b"lpm_thermistor_ambient_temp_c",u"lpm_thermistor_beam_raw_mv",b"lpm_thermistor_beam_raw_mv",u"lpm_thermistor_beam_temp_c",b"lpm_thermistor_beam_temp_c",u"lpsu_current_ma",b"lpsu_current_ma",u"lpsu_status",b"lpsu_status",u"servo_pan",b"servo_pan",u"servo_tilt",b"servo_tilt",u"target_cam_power_on",b"target_cam_power_on"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_servo_pan",b"_servo_pan"]) -> typing_extensions___Literal["servo_pan"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_servo_tilt",b"_servo_tilt"]) -> typing_extensions___Literal["servo_tilt"]: ...
type___HwStatus_Slayer_Reply = HwStatus_Slayer_Reply

class HwStatus_Reaper_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    laser_connected: builtin___bool = ...
    laser_therm_temp_c: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    laser_power_w: builtin___float = ...
    laser_power_raw_mv: builtin___float = ...
    target_cam_power_on: builtin___bool = ...

    @property
    def laser_inventory(self) -> generated___lib___drivers___nanopb___proto___laser_pb2___Laser_Inventory_Reply: ...

    @property
    def laser_status(self) -> generated___lib___drivers___nanopb___proto___laser_pb2___Diode_Status_Reply: ...

    @property
    def servo_pan(self) -> type___HwStatus_Servo: ...

    @property
    def servo_tilt(self) -> type___HwStatus_Servo: ...

    def __init__(self,
        *,
        laser_connected : typing___Optional[builtin___bool] = None,
        laser_inventory : typing___Optional[generated___lib___drivers___nanopb___proto___laser_pb2___Laser_Inventory_Reply] = None,
        laser_status : typing___Optional[generated___lib___drivers___nanopb___proto___laser_pb2___Diode_Status_Reply] = None,
        laser_therm_temp_c : typing___Optional[typing___Iterable[builtin___float]] = None,
        laser_power_w : typing___Optional[builtin___float] = None,
        laser_power_raw_mv : typing___Optional[builtin___float] = None,
        servo_pan : typing___Optional[type___HwStatus_Servo] = None,
        servo_tilt : typing___Optional[type___HwStatus_Servo] = None,
        target_cam_power_on : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_servo_pan",b"_servo_pan",u"_servo_tilt",b"_servo_tilt",u"laser_inventory",b"laser_inventory",u"laser_status",b"laser_status",u"servo_pan",b"servo_pan",u"servo_tilt",b"servo_tilt"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_servo_pan",b"_servo_pan",u"_servo_tilt",b"_servo_tilt",u"laser_connected",b"laser_connected",u"laser_inventory",b"laser_inventory",u"laser_power_raw_mv",b"laser_power_raw_mv",u"laser_power_w",b"laser_power_w",u"laser_status",b"laser_status",u"laser_therm_temp_c",b"laser_therm_temp_c",u"servo_pan",b"servo_pan",u"servo_tilt",b"servo_tilt",u"target_cam_power_on",b"target_cam_power_on"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_servo_pan",b"_servo_pan"]) -> typing_extensions___Literal["servo_pan"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_servo_tilt",b"_servo_tilt"]) -> typing_extensions___Literal["servo_tilt"]: ...
type___HwStatus_Reaper_Reply = HwStatus_Reaper_Reply

class HwStatus_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def slayer(self) -> type___HwStatus_Slayer_Reply: ...

    @property
    def reaper(self) -> type___HwStatus_Reaper_Reply: ...

    def __init__(self,
        *,
        slayer : typing___Optional[type___HwStatus_Slayer_Reply] = None,
        reaper : typing___Optional[type___HwStatus_Reaper_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"reaper",b"reaper",u"reply",b"reply",u"slayer",b"slayer"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"reaper",b"reaper",u"reply",b"reply",u"slayer",b"slayer"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["slayer","reaper"]: ...
type___HwStatus_Reply = HwStatus_Reply

class Reset_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Reset_Request = Reset_Request

class Clear_Config_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Clear_Config_Request = Clear_Config_Request

class Status_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Status_Request = Status_Request

class Override_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    override: builtin___int = ...

    def __init__(self,
        *,
        override : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"override",b"override"]) -> None: ...
type___Override_Request = Override_Request

class Power_Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    targetCam: builtin___bool = ...
    firingBoard: builtin___bool = ...

    def __init__(self,
        *,
        targetCam : typing___Optional[builtin___bool] = None,
        firingBoard : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_firingBoard",b"_firingBoard",u"_targetCam",b"_targetCam",u"firingBoard",b"firingBoard",u"targetCam",b"targetCam"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_firingBoard",b"_firingBoard",u"_targetCam",b"_targetCam",u"firingBoard",b"firingBoard",u"targetCam",b"targetCam"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_firingBoard",b"_firingBoard"]) -> typing_extensions___Literal["firingBoard"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_targetCam",b"_targetCam"]) -> typing_extensions___Literal["targetCam"]: ...
type___Power_Request = Power_Request

class Status_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    status: builtin___int = ...

    @property
    def laser_status(self) -> generated___lib___drivers___nanopb___proto___laser_pb2___Laser_Status_Reply: ...

    def __init__(self,
        *,
        status : typing___Optional[builtin___int] = None,
        laser_status : typing___Optional[generated___lib___drivers___nanopb___proto___laser_pb2___Laser_Status_Reply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"laser_status",b"laser_status"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"laser_status",b"laser_status",u"status",b"status"]) -> None: ...
type___Status_Reply = Status_Reply

class Power_Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    targetCam: builtin___bool = ...
    firingBoard: builtin___bool = ...

    def __init__(self,
        *,
        targetCam : typing___Optional[builtin___bool] = None,
        firingBoard : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"firingBoard",b"firingBoard",u"targetCam",b"targetCam"]) -> None: ...
type___Power_Reply = Power_Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def reset(self) -> type___Reset_Request: ...

    @property
    def clear(self) -> type___Clear_Config_Request: ...

    @property
    def gimbal(self) -> generated___lib___drivers___nanopb___proto___gimbal_pb2___Request: ...

    @property
    def dawg(self) -> generated___lib___drivers___nanopb___proto___dawg_pb2___Request: ...

    @property
    def laser(self) -> generated___lib___drivers___nanopb___proto___laser_pb2___Request: ...

    @property
    def status(self) -> type___Status_Request: ...

    @property
    def lens(self) -> generated___lib___drivers___nanopb___proto___lens_pb2___Request: ...

    @property
    def override(self) -> type___Override_Request: ...

    @property
    def conf(self) -> generated___lib___drivers___nanopb___proto___scanner_config_pb2___Request: ...

    @property
    def arc(self) -> generated___lib___drivers___nanopb___proto___arc_detector_pb2___Request: ...

    @property
    def power(self) -> type___Power_Request: ...

    @property
    def hw_status(self) -> type___HwStatus_Request: ...

    @property
    def ambient_temp(self) -> type___AmbientTempRequest: ...

    def __init__(self,
        *,
        reset : typing___Optional[type___Reset_Request] = None,
        clear : typing___Optional[type___Clear_Config_Request] = None,
        gimbal : typing___Optional[generated___lib___drivers___nanopb___proto___gimbal_pb2___Request] = None,
        dawg : typing___Optional[generated___lib___drivers___nanopb___proto___dawg_pb2___Request] = None,
        laser : typing___Optional[generated___lib___drivers___nanopb___proto___laser_pb2___Request] = None,
        status : typing___Optional[type___Status_Request] = None,
        lens : typing___Optional[generated___lib___drivers___nanopb___proto___lens_pb2___Request] = None,
        override : typing___Optional[type___Override_Request] = None,
        conf : typing___Optional[generated___lib___drivers___nanopb___proto___scanner_config_pb2___Request] = None,
        arc : typing___Optional[generated___lib___drivers___nanopb___proto___arc_detector_pb2___Request] = None,
        power : typing___Optional[type___Power_Request] = None,
        hw_status : typing___Optional[type___HwStatus_Request] = None,
        ambient_temp : typing___Optional[type___AmbientTempRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ambient_temp",b"ambient_temp",u"arc",b"arc",u"clear",b"clear",u"conf",b"conf",u"dawg",b"dawg",u"gimbal",b"gimbal",u"hw_status",b"hw_status",u"laser",b"laser",u"lens",b"lens",u"override",b"override",u"power",b"power",u"request",b"request",u"reset",b"reset",u"status",b"status"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ambient_temp",b"ambient_temp",u"arc",b"arc",u"clear",b"clear",u"conf",b"conf",u"dawg",b"dawg",u"gimbal",b"gimbal",u"hw_status",b"hw_status",u"laser",b"laser",u"lens",b"lens",u"override",b"override",u"power",b"power",u"request",b"request",u"reset",b"reset",u"status",b"status"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["reset","clear","gimbal","dawg","laser","status","lens","override","conf","arc","power","hw_status","ambient_temp"]: ...
type___Request = Request

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def gimbal(self) -> generated___lib___drivers___nanopb___proto___gimbal_pb2___Reply: ...

    @property
    def dawg(self) -> generated___lib___drivers___nanopb___proto___dawg_pb2___Reply: ...

    @property
    def laser(self) -> generated___lib___drivers___nanopb___proto___laser_pb2___Reply: ...

    @property
    def status(self) -> type___Status_Reply: ...

    @property
    def lens(self) -> generated___lib___drivers___nanopb___proto___lens_pb2___Reply: ...

    @property
    def conf(self) -> generated___lib___drivers___nanopb___proto___scanner_config_pb2___Reply: ...

    @property
    def arc(self) -> generated___lib___drivers___nanopb___proto___arc_detector_pb2___Reply: ...

    @property
    def power(self) -> type___Power_Reply: ...

    @property
    def hw_status(self) -> type___HwStatus_Reply: ...

    @property
    def ambient_temp(self) -> type___AmbientTempReply: ...

    def __init__(self,
        *,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        gimbal : typing___Optional[generated___lib___drivers___nanopb___proto___gimbal_pb2___Reply] = None,
        dawg : typing___Optional[generated___lib___drivers___nanopb___proto___dawg_pb2___Reply] = None,
        laser : typing___Optional[generated___lib___drivers___nanopb___proto___laser_pb2___Reply] = None,
        status : typing___Optional[type___Status_Reply] = None,
        lens : typing___Optional[generated___lib___drivers___nanopb___proto___lens_pb2___Reply] = None,
        conf : typing___Optional[generated___lib___drivers___nanopb___proto___scanner_config_pb2___Reply] = None,
        arc : typing___Optional[generated___lib___drivers___nanopb___proto___arc_detector_pb2___Reply] = None,
        power : typing___Optional[type___Power_Reply] = None,
        hw_status : typing___Optional[type___HwStatus_Reply] = None,
        ambient_temp : typing___Optional[type___AmbientTempReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"ambient_temp",b"ambient_temp",u"arc",b"arc",u"conf",b"conf",u"dawg",b"dawg",u"gimbal",b"gimbal",u"hw_status",b"hw_status",u"laser",b"laser",u"lens",b"lens",u"power",b"power",u"reply",b"reply",u"status",b"status"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"ambient_temp",b"ambient_temp",u"arc",b"arc",u"conf",b"conf",u"dawg",b"dawg",u"gimbal",b"gimbal",u"hw_status",b"hw_status",u"laser",b"laser",u"lens",b"lens",u"power",b"power",u"reply",b"reply",u"status",b"status"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["ack","gimbal","dawg","laser","status","lens","conf","arc","power","hw_status","ambient_temp"]: ...
type___Reply = Reply

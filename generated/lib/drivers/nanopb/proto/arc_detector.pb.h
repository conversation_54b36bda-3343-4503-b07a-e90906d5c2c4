/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_ARC_DETECTOR_ARC_DETECTOR_PB_H_INCLUDED
#define PB_ARC_DETECTOR_ARC_DETECTOR_PB_H_INCLUDED
#include <pb.h>
#include "generated/lib/drivers/nanopb/proto/error.pb.h"
#include "generated/lib/drivers/nanopb/proto/ack.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _arc_detector_Get_Config_Request {
    char dummy_field;
} arc_detector_Get_Config_Request;

typedef struct _arc_detector_Reset_Alarm_Request {
    char dummy_field;
} arc_detector_Reset_Alarm_Request;

typedef struct _arc_detector_Status_Request {
    char dummy_field;
} arc_detector_Status_Request;

typedef struct _arc_detector_Config {
    bool enabled;
    float upperLimit;
    float lowerLimit;
    uint32_t alarmPeriod;
    uint32_t alarmThreshold;
    uint32_t initialDelay;
    uint32_t sampleInterval;
} arc_detector_Config;

typedef struct _arc_detector_Status_Reply {
    bool enabled;
    bool alarm;
    float minCurrent;
    float maxCurrent;
} arc_detector_Status_Reply;

typedef struct _arc_detector_Config_Reply {
    bool has_conf;
    arc_detector_Config conf;
} arc_detector_Config_Reply;

typedef struct _arc_detector_Set_Config_Request {
    bool has_newConfig;
    arc_detector_Config newConfig;
} arc_detector_Set_Config_Request;

typedef struct _arc_detector_Reply {
    pb_size_t which_reply;
    union {
        error_Error error;
        ack_Ack ack;
        arc_detector_Status_Reply status;
        arc_detector_Config_Reply conf;
    } reply;
} arc_detector_Reply;

typedef struct _arc_detector_Request {
    pb_size_t which_request;
    union {
        arc_detector_Set_Config_Request setConf;
        arc_detector_Get_Config_Request getConf;
        arc_detector_Status_Request status;
        arc_detector_Reset_Alarm_Request reset;
    } request;
} arc_detector_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define arc_detector_Config_init_default         {0, 0, 0, 0, 0, 0, 0}
#define arc_detector_Reset_Alarm_Request_init_default {0}
#define arc_detector_Status_Request_init_default {0}
#define arc_detector_Status_Reply_init_default   {0, 0, 0, 0}
#define arc_detector_Set_Config_Request_init_default {false, arc_detector_Config_init_default}
#define arc_detector_Get_Config_Request_init_default {0}
#define arc_detector_Config_Reply_init_default   {false, arc_detector_Config_init_default}
#define arc_detector_Request_init_default        {0, {arc_detector_Set_Config_Request_init_default}}
#define arc_detector_Reply_init_default          {0, {error_Error_init_default}}
#define arc_detector_Config_init_zero            {0, 0, 0, 0, 0, 0, 0}
#define arc_detector_Reset_Alarm_Request_init_zero {0}
#define arc_detector_Status_Request_init_zero    {0}
#define arc_detector_Status_Reply_init_zero      {0, 0, 0, 0}
#define arc_detector_Set_Config_Request_init_zero {false, arc_detector_Config_init_zero}
#define arc_detector_Get_Config_Request_init_zero {0}
#define arc_detector_Config_Reply_init_zero      {false, arc_detector_Config_init_zero}
#define arc_detector_Request_init_zero           {0, {arc_detector_Set_Config_Request_init_zero}}
#define arc_detector_Reply_init_zero             {0, {error_Error_init_zero}}

/* Field tags (for use in manual encoding/decoding) */
#define arc_detector_Config_enabled_tag          1
#define arc_detector_Config_upperLimit_tag       2
#define arc_detector_Config_lowerLimit_tag       3
#define arc_detector_Config_alarmPeriod_tag      4
#define arc_detector_Config_alarmThreshold_tag   5
#define arc_detector_Config_initialDelay_tag     6
#define arc_detector_Config_sampleInterval_tag   7
#define arc_detector_Status_Reply_enabled_tag    1
#define arc_detector_Status_Reply_alarm_tag      2
#define arc_detector_Status_Reply_minCurrent_tag 3
#define arc_detector_Status_Reply_maxCurrent_tag 4
#define arc_detector_Config_Reply_conf_tag       1
#define arc_detector_Set_Config_Request_newConfig_tag 1
#define arc_detector_Reply_error_tag             1
#define arc_detector_Reply_ack_tag               2
#define arc_detector_Reply_status_tag            3
#define arc_detector_Reply_conf_tag              4
#define arc_detector_Request_setConf_tag         1
#define arc_detector_Request_getConf_tag         2
#define arc_detector_Request_status_tag          3
#define arc_detector_Request_reset_tag           4

/* Struct field encoding specification for nanopb */
#define arc_detector_Config_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     enabled,           1) \
X(a, STATIC,   SINGULAR, FLOAT,    upperLimit,        2) \
X(a, STATIC,   SINGULAR, FLOAT,    lowerLimit,        3) \
X(a, STATIC,   SINGULAR, UINT32,   alarmPeriod,       4) \
X(a, STATIC,   SINGULAR, UINT32,   alarmThreshold,    5) \
X(a, STATIC,   SINGULAR, UINT32,   initialDelay,      6) \
X(a, STATIC,   SINGULAR, UINT32,   sampleInterval,    7)
#define arc_detector_Config_CALLBACK NULL
#define arc_detector_Config_DEFAULT NULL

#define arc_detector_Reset_Alarm_Request_FIELDLIST(X, a) \

#define arc_detector_Reset_Alarm_Request_CALLBACK NULL
#define arc_detector_Reset_Alarm_Request_DEFAULT NULL

#define arc_detector_Status_Request_FIELDLIST(X, a) \

#define arc_detector_Status_Request_CALLBACK NULL
#define arc_detector_Status_Request_DEFAULT NULL

#define arc_detector_Status_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     enabled,           1) \
X(a, STATIC,   SINGULAR, BOOL,     alarm,             2) \
X(a, STATIC,   SINGULAR, FLOAT,    minCurrent,        3) \
X(a, STATIC,   SINGULAR, FLOAT,    maxCurrent,        4)
#define arc_detector_Status_Reply_CALLBACK NULL
#define arc_detector_Status_Reply_DEFAULT NULL

#define arc_detector_Set_Config_Request_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  newConfig,         1)
#define arc_detector_Set_Config_Request_CALLBACK NULL
#define arc_detector_Set_Config_Request_DEFAULT NULL
#define arc_detector_Set_Config_Request_newConfig_MSGTYPE arc_detector_Config

#define arc_detector_Get_Config_Request_FIELDLIST(X, a) \

#define arc_detector_Get_Config_Request_CALLBACK NULL
#define arc_detector_Get_Config_Request_DEFAULT NULL

#define arc_detector_Config_Reply_FIELDLIST(X, a) \
X(a, STATIC,   OPTIONAL, MESSAGE,  conf,              1)
#define arc_detector_Config_Reply_CALLBACK NULL
#define arc_detector_Config_Reply_DEFAULT NULL
#define arc_detector_Config_Reply_conf_MSGTYPE arc_detector_Config

#define arc_detector_Request_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,setConf,request.setConf),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,getConf,request.getConf),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,status,request.status),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (request,reset,request.reset),   4)
#define arc_detector_Request_CALLBACK NULL
#define arc_detector_Request_DEFAULT NULL
#define arc_detector_Request_request_setConf_MSGTYPE arc_detector_Set_Config_Request
#define arc_detector_Request_request_getConf_MSGTYPE arc_detector_Get_Config_Request
#define arc_detector_Request_request_status_MSGTYPE arc_detector_Status_Request
#define arc_detector_Request_request_reset_MSGTYPE arc_detector_Reset_Alarm_Request

#define arc_detector_Reply_FIELDLIST(X, a) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,error,reply.error),   1) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,ack,reply.ack),   2) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,status,reply.status),   3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (reply,conf,reply.conf),   4)
#define arc_detector_Reply_CALLBACK NULL
#define arc_detector_Reply_DEFAULT NULL
#define arc_detector_Reply_reply_error_MSGTYPE error_Error
#define arc_detector_Reply_reply_ack_MSGTYPE ack_Ack
#define arc_detector_Reply_reply_status_MSGTYPE arc_detector_Status_Reply
#define arc_detector_Reply_reply_conf_MSGTYPE arc_detector_Config_Reply

extern const pb_msgdesc_t arc_detector_Config_msg;
extern const pb_msgdesc_t arc_detector_Reset_Alarm_Request_msg;
extern const pb_msgdesc_t arc_detector_Status_Request_msg;
extern const pb_msgdesc_t arc_detector_Status_Reply_msg;
extern const pb_msgdesc_t arc_detector_Set_Config_Request_msg;
extern const pb_msgdesc_t arc_detector_Get_Config_Request_msg;
extern const pb_msgdesc_t arc_detector_Config_Reply_msg;
extern const pb_msgdesc_t arc_detector_Request_msg;
extern const pb_msgdesc_t arc_detector_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define arc_detector_Config_fields &arc_detector_Config_msg
#define arc_detector_Reset_Alarm_Request_fields &arc_detector_Reset_Alarm_Request_msg
#define arc_detector_Status_Request_fields &arc_detector_Status_Request_msg
#define arc_detector_Status_Reply_fields &arc_detector_Status_Reply_msg
#define arc_detector_Set_Config_Request_fields &arc_detector_Set_Config_Request_msg
#define arc_detector_Get_Config_Request_fields &arc_detector_Get_Config_Request_msg
#define arc_detector_Config_Reply_fields &arc_detector_Config_Reply_msg
#define arc_detector_Request_fields &arc_detector_Request_msg
#define arc_detector_Reply_fields &arc_detector_Reply_msg

/* Maximum encoded size of messages (where known) */
#define arc_detector_Config_size                 36
#define arc_detector_Reset_Alarm_Request_size    0
#define arc_detector_Status_Request_size         0
#define arc_detector_Status_Reply_size           14
#define arc_detector_Set_Config_Request_size     38
#define arc_detector_Get_Config_Request_size     0
#define arc_detector_Config_Reply_size           38
#define arc_detector_Request_size                40
#if defined(error_Error_size) && defined(ack_Ack_size)
typedef union arc_detector_Reply_reply_size_union {char f1[(6 + error_Error_size)]; char f2[(6 + ack_Ack_size)]; char f0[40];} arc_detector_Reply_reply_size_union;
#define arc_detector_Reply_size                  (0 + sizeof(arc_detector_Reply_reply_size_union))
#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

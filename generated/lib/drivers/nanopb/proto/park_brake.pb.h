/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.3 */

#ifndef PB_PARK_BRAKE_PARK_BRAKE_PB_H_INCLUDED
#define PB_PARK_BRAKE_PARK_BRAKE_PB_H_INCLUDED
#include <pb.h>

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Struct definitions */
typedef struct _park_brake_Query_Request {
    char dummy_field;
} park_brake_Query_Request;

typedef struct _park_brake_Reply {
    char dummy_field;
} park_brake_Reply;

typedef struct _park_brake_Query_Reply {
    bool onoff;
} park_brake_Query_Reply;

typedef struct _park_brake_Request {
    bool onoff;
} park_brake_Request;


#ifdef __cplusplus
extern "C" {
#endif

/* Initializer values for message structs */
#define park_brake_Request_init_default          {0}
#define park_brake_Reply_init_default            {0}
#define park_brake_Query_Request_init_default    {0}
#define park_brake_Query_Reply_init_default      {0}
#define park_brake_Request_init_zero             {0}
#define park_brake_Reply_init_zero               {0}
#define park_brake_Query_Request_init_zero       {0}
#define park_brake_Query_Reply_init_zero         {0}

/* Field tags (for use in manual encoding/decoding) */
#define park_brake_Query_Reply_onoff_tag         1
#define park_brake_Request_onoff_tag             1

/* Struct field encoding specification for nanopb */
#define park_brake_Request_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     onoff,             1)
#define park_brake_Request_CALLBACK NULL
#define park_brake_Request_DEFAULT NULL

#define park_brake_Reply_FIELDLIST(X, a) \

#define park_brake_Reply_CALLBACK NULL
#define park_brake_Reply_DEFAULT NULL

#define park_brake_Query_Request_FIELDLIST(X, a) \

#define park_brake_Query_Request_CALLBACK NULL
#define park_brake_Query_Request_DEFAULT NULL

#define park_brake_Query_Reply_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, BOOL,     onoff,             1)
#define park_brake_Query_Reply_CALLBACK NULL
#define park_brake_Query_Reply_DEFAULT NULL

extern const pb_msgdesc_t park_brake_Request_msg;
extern const pb_msgdesc_t park_brake_Reply_msg;
extern const pb_msgdesc_t park_brake_Query_Request_msg;
extern const pb_msgdesc_t park_brake_Query_Reply_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define park_brake_Request_fields &park_brake_Request_msg
#define park_brake_Reply_fields &park_brake_Reply_msg
#define park_brake_Query_Request_fields &park_brake_Query_Request_msg
#define park_brake_Query_Reply_fields &park_brake_Query_Reply_msg

/* Maximum encoded size of messages (where known) */
#define park_brake_Request_size                  2
#define park_brake_Reply_size                    0
#define park_brake_Query_Request_size            0
#define park_brake_Query_Reply_size              2

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

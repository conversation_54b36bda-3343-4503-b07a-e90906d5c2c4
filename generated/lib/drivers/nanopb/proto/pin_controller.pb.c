/* Automatically generated nanopb constant definitions */
/* Generated by nanopb-0.4.3 */

#include "pin_controller.pb.h"
#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

PB_BIND(pin_controller_AnalogReadValue, pin_controller_AnalogReadValue, AUTO)


PB_BIND(pin_controller_DigitalReadValue, pin_controller_DigitalReadValue, AUTO)


PB_BIND(pin_controller_PinReply, pin_controller_PinReply, 2)


PB_BIND(pin_controller_AnalogRead, pin_controller_AnalogRead, AUTO)


PB_BIND(pin_controller_DigitalRead, pin_controller_DigitalRead, AUTO)


PB_BIND(pin_controller_DigitalWrite, pin_controller_DigitalWrite, AUTO)


PB_BIND(pin_controller_PinConfiguration, pin_controller_PinConfiguration, AUTO)


PB_BIND(pin_controller_PWMConfiguration, pin_controller_PWMConfiguration, AUTO)


PB_BIND(pin_controller_PinRequest, pin_controller_PinRequest, AUTO)


PB_BIND(pin_controller_Reply, pin_controller_Reply, 2)


PB_BIND(pin_controller_Request, pin_controller_Request, 2)






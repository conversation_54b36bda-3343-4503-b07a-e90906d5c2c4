# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/version.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/version.proto',
  package='version',
  syntax='proto3',
  serialized_options=b'Z\016nanopb/version',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n0generated/lib/drivers/nanopb/proto/version.proto\x12\x07version\"\x0f\n\rReset_Request\"\x11\n\x0fVersion_Request\"-\n\rVersion_Reply\x12\r\n\x05major\x18\x01 \x01(\r\x12\r\n\x05minor\x18\x02 \x01(\rB\x10Z\x0enanopb/versionb\x06proto3'
)




_RESET_REQUEST = _descriptor.Descriptor(
  name='Reset_Request',
  full_name='version.Reset_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=61,
  serialized_end=76,
)


_VERSION_REQUEST = _descriptor.Descriptor(
  name='Version_Request',
  full_name='version.Version_Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=78,
  serialized_end=95,
)


_VERSION_REPLY = _descriptor.Descriptor(
  name='Version_Reply',
  full_name='version.Version_Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='major', full_name='version.Version_Reply.major', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='minor', full_name='version.Version_Reply.minor', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=97,
  serialized_end=142,
)

DESCRIPTOR.message_types_by_name['Reset_Request'] = _RESET_REQUEST
DESCRIPTOR.message_types_by_name['Version_Request'] = _VERSION_REQUEST
DESCRIPTOR.message_types_by_name['Version_Reply'] = _VERSION_REPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Reset_Request = _reflection.GeneratedProtocolMessageType('Reset_Request', (_message.Message,), {
  'DESCRIPTOR' : _RESET_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.version_pb2'
  # @@protoc_insertion_point(class_scope:version.Reset_Request)
  })
_sym_db.RegisterMessage(Reset_Request)

Version_Request = _reflection.GeneratedProtocolMessageType('Version_Request', (_message.Message,), {
  'DESCRIPTOR' : _VERSION_REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.version_pb2'
  # @@protoc_insertion_point(class_scope:version.Version_Request)
  })
_sym_db.RegisterMessage(Version_Request)

Version_Reply = _reflection.GeneratedProtocolMessageType('Version_Reply', (_message.Message,), {
  'DESCRIPTOR' : _VERSION_REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.version_pb2'
  # @@protoc_insertion_point(class_scope:version.Version_Reply)
  })
_sym_db.RegisterMessage(Version_Reply)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

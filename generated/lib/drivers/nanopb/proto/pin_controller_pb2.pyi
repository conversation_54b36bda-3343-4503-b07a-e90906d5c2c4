"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.lib.drivers.nanopb.proto.ack_pb2 import (
    Ack as generated___lib___drivers___nanopb___proto___ack_pb2___Ack,
)

from generated.lib.drivers.nanopb.proto.diagnostic_pb2 import (
    Ping as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping,
    Pong as generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong,
)

from generated.lib.drivers.nanopb.proto.request_pb2 import (
    RequestHeader as generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader,
)

from generated.lib.drivers.nanopb.proto.version_pb2 import (
    Version_Reply as generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply,
    Version_Request as generated___lib___drivers___nanopb___proto___version_pb2___Version_Request,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

DigitalPinValueValue = typing___NewType('DigitalPinValueValue', builtin___int)
type___DigitalPinValueValue = DigitalPinValueValue
DigitalPinValue: _DigitalPinValue
class _DigitalPinValue(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[DigitalPinValueValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    LOW = typing___cast(DigitalPinValueValue, 0)
    HIGH = typing___cast(DigitalPinValueValue, 1)
LOW = typing___cast(DigitalPinValueValue, 0)
HIGH = typing___cast(DigitalPinValueValue, 1)

PinModeValue = typing___NewType('PinModeValue', builtin___int)
type___PinModeValue = PinModeValue
PinMode: _PinMode
class _PinMode(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[PinModeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    INPUT = typing___cast(PinModeValue, 0)
    OUTPUT = typing___cast(PinModeValue, 1)
    INPUT_PULL_UP = typing___cast(PinModeValue, 2)
INPUT = typing___cast(PinModeValue, 0)
OUTPUT = typing___cast(PinModeValue, 1)
INPUT_PULL_UP = typing___cast(PinModeValue, 2)

class AnalogReadValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pin_number: builtin___int = ...
    value: builtin___int = ...

    def __init__(self,
        *,
        pin_number : typing___Optional[builtin___int] = None,
        value : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pin_number",b"pin_number",u"value",b"value"]) -> None: ...
type___AnalogReadValue = AnalogReadValue

class DigitalReadValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pin_number: builtin___int = ...
    value: type___DigitalPinValueValue = ...

    def __init__(self,
        *,
        pin_number : typing___Optional[builtin___int] = None,
        value : typing___Optional[type___DigitalPinValueValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pin_number",b"pin_number",u"value",b"value"]) -> None: ...
type___DigitalReadValue = DigitalReadValue

class PinReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def read(self) -> type___DigitalReadValue: ...

    @property
    def analog(self) -> type___AnalogReadValue: ...

    def __init__(self,
        *,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        read : typing___Optional[type___DigitalReadValue] = None,
        analog : typing___Optional[type___AnalogReadValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"analog",b"analog",u"read",b"read",u"reply",b"reply"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"analog",b"analog",u"read",b"read",u"reply",b"reply"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["ack","read","analog"]: ...
type___PinReply = PinReply

class AnalogRead(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pin_number: builtin___int = ...

    def __init__(self,
        *,
        pin_number : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pin_number",b"pin_number"]) -> None: ...
type___AnalogRead = AnalogRead

class DigitalRead(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pin_number: builtin___int = ...

    def __init__(self,
        *,
        pin_number : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pin_number",b"pin_number"]) -> None: ...
type___DigitalRead = DigitalRead

class DigitalWrite(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pin_number: builtin___int = ...
    value: type___DigitalPinValueValue = ...

    def __init__(self,
        *,
        pin_number : typing___Optional[builtin___int] = None,
        value : typing___Optional[type___DigitalPinValueValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pin_number",b"pin_number",u"value",b"value"]) -> None: ...
type___DigitalWrite = DigitalWrite

class PinConfiguration(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pin_number: builtin___int = ...
    pin_mode: type___PinModeValue = ...

    def __init__(self,
        *,
        pin_number : typing___Optional[builtin___int] = None,
        pin_mode : typing___Optional[type___PinModeValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pin_mode",b"pin_mode",u"pin_number",b"pin_number"]) -> None: ...
type___PinConfiguration = PinConfiguration

class PWMConfiguration(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    pin_number: builtin___int = ...
    value_8bit: builtin___int = ...

    def __init__(self,
        *,
        pin_number : typing___Optional[builtin___int] = None,
        value_8bit : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pin_number",b"pin_number",u"value_8bit",b"value_8bit"]) -> None: ...
type___PWMConfiguration = PWMConfiguration

class PinRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def pin(self) -> type___PinConfiguration: ...

    @property
    def pwm(self) -> type___PWMConfiguration: ...

    @property
    def write(self) -> type___DigitalWrite: ...

    @property
    def read(self) -> type___DigitalRead: ...

    @property
    def analog(self) -> type___AnalogRead: ...

    def __init__(self,
        *,
        pin : typing___Optional[type___PinConfiguration] = None,
        pwm : typing___Optional[type___PWMConfiguration] = None,
        write : typing___Optional[type___DigitalWrite] = None,
        read : typing___Optional[type___DigitalRead] = None,
        analog : typing___Optional[type___AnalogRead] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"analog",b"analog",u"pin",b"pin",u"pwm",b"pwm",u"read",b"read",u"request",b"request",u"write",b"write"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"analog",b"analog",u"pin",b"pin",u"pwm",b"pwm",u"read",b"read",u"request",b"request",u"write",b"write"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["pin","pwm","write","read","analog"]: ...
type___PinRequest = PinRequest

class Reply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def pong(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong: ...

    @property
    def ack(self) -> generated___lib___drivers___nanopb___proto___ack_pb2___Ack: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply: ...

    @property
    def pin(self) -> type___PinReply: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        pong : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Pong] = None,
        ack : typing___Optional[generated___lib___drivers___nanopb___proto___ack_pb2___Ack] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Reply] = None,
        pin : typing___Optional[type___PinReply] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"header",b"header",u"pin",b"pin",u"pong",b"pong",u"reply",b"reply",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ack",b"ack",u"header",b"header",u"pin",b"pin",u"pong",b"pong",u"reply",b"reply",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"reply",b"reply"]) -> typing_extensions___Literal["pong","ack","version","pin"]: ...
type___Reply = Reply

class Request(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def header(self) -> generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader: ...

    @property
    def ping(self) -> generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping: ...

    @property
    def version(self) -> generated___lib___drivers___nanopb___proto___version_pb2___Version_Request: ...

    @property
    def pin(self) -> type___PinRequest: ...

    def __init__(self,
        *,
        header : typing___Optional[generated___lib___drivers___nanopb___proto___request_pb2___RequestHeader] = None,
        ping : typing___Optional[generated___lib___drivers___nanopb___proto___diagnostic_pb2___Ping] = None,
        version : typing___Optional[generated___lib___drivers___nanopb___proto___version_pb2___Version_Request] = None,
        pin : typing___Optional[type___PinRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"header",b"header",u"pin",b"pin",u"ping",b"ping",u"request",b"request",u"version",b"version"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"header",b"header",u"pin",b"pin",u"ping",b"ping",u"request",b"request",u"version",b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["ping","version","pin"]: ...
type___Request = Request

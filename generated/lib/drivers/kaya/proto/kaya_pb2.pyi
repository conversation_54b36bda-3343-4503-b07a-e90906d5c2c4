"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

PixelFormatValue = typing___NewType('PixelFormatValue', builtin___int)
type___PixelFormatValue = PixelFormatValue
PixelFormat: _PixelFormat
class _PixelFormat(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[PixelFormatValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    PF_UNKNOWN = typing___cast(PixelFormatValue, 0)
    BGR8 = typing___cast(PixelFormatValue, 1)
    BayerRG8 = typing___cast(PixelFormatValue, 2)
PF_UNKNOWN = typing___cast(PixelFormatValue, 0)
BGR8 = typing___cast(PixelFormatValue, 1)
BayerRG8 = typing___cast(PixelFormatValue, 2)

class GrabRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    pixel_format: type___PixelFormatValue = ...
    exposure_us: builtin___int = ...
    analog_gain_level: builtin___float = ...
    analog_black_level: builtin___float = ...
    focus: builtin___float = ...
    wb_ratio_red: builtin___float = ...
    wb_ratio_green: builtin___float = ...
    wb_ratio_blue: builtin___float = ...
    wb_b_gain_db: builtin___float = ...
    focusing_mode: builtin___bool = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        pixel_format : typing___Optional[type___PixelFormatValue] = None,
        exposure_us : typing___Optional[builtin___int] = None,
        analog_gain_level : typing___Optional[builtin___float] = None,
        analog_black_level : typing___Optional[builtin___float] = None,
        focus : typing___Optional[builtin___float] = None,
        wb_ratio_red : typing___Optional[builtin___float] = None,
        wb_ratio_green : typing___Optional[builtin___float] = None,
        wb_ratio_blue : typing___Optional[builtin___float] = None,
        wb_b_gain_db : typing___Optional[builtin___float] = None,
        focusing_mode : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"analog_black_level",b"analog_black_level",u"analog_gain_level",b"analog_gain_level",u"exposure_us",b"exposure_us",u"focus",b"focus",u"focusing_mode",b"focusing_mode",u"pixel_format",b"pixel_format",u"timestamp_ms",b"timestamp_ms",u"wb_b_gain_db",b"wb_b_gain_db",u"wb_ratio_blue",b"wb_ratio_blue",u"wb_ratio_green",b"wb_ratio_green",u"wb_ratio_red",b"wb_ratio_red"]) -> None: ...
type___GrabRequest = GrabRequest

class GrabResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    height: builtin___int = ...
    width: builtin___int = ...
    bits_per_pixel: builtin___int = ...
    data: builtin___bytes = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        height : typing___Optional[builtin___int] = None,
        width : typing___Optional[builtin___int] = None,
        bits_per_pixel : typing___Optional[builtin___int] = None,
        data : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bits_per_pixel",b"bits_per_pixel",u"data",b"data",u"height",b"height",u"timestamp_ms",b"timestamp_ms",u"width",b"width"]) -> None: ...
type___GrabResponse = GrabResponse

class GetSensorTempRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetSensorTempRequest = GetSensorTempRequest

class GetSensorTempResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    sensor_temp_c: builtin___int = ...

    def __init__(self,
        *,
        sensor_temp_c : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"sensor_temp_c",b"sensor_temp_c"]) -> None: ...
type___GetSensorTempResponse = GetSensorTempResponse

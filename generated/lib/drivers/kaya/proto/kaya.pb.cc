// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: generated/lib/drivers/kaya/proto/kaya.proto

#include "generated/lib/drivers/kaya/proto/kaya.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace kaya {
constexpr GrabRequest::GrabRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timestamp_ms_(int64_t{0})
  , pixel_format_(0)

  , exposure_us_(0)
  , analog_gain_level_(0)
  , analog_black_level_(0)
  , focus_(0)
  , wb_ratio_red_(0)
  , wb_ratio_green_(0)
  , wb_ratio_blue_(0)
  , wb_b_gain_db_(0)
  , focusing_mode_(false){}
struct GrabRequestDefaultTypeInternal {
  constexpr GrabRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GrabRequestDefaultTypeInternal() {}
  union {
    GrabRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GrabRequestDefaultTypeInternal _GrabRequest_default_instance_;
constexpr GrabResponse::GrabResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : data_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0})
  , height_(0)
  , width_(0)
  , bits_per_pixel_(0){}
struct GrabResponseDefaultTypeInternal {
  constexpr GrabResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GrabResponseDefaultTypeInternal() {}
  union {
    GrabResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GrabResponseDefaultTypeInternal _GrabResponse_default_instance_;
constexpr GetSensorTempRequest::GetSensorTempRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetSensorTempRequestDefaultTypeInternal {
  constexpr GetSensorTempRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetSensorTempRequestDefaultTypeInternal() {}
  union {
    GetSensorTempRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetSensorTempRequestDefaultTypeInternal _GetSensorTempRequest_default_instance_;
constexpr GetSensorTempResponse::GetSensorTempResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : sensor_temp_c_(0){}
struct GetSensorTempResponseDefaultTypeInternal {
  constexpr GetSensorTempResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetSensorTempResponseDefaultTypeInternal() {}
  union {
    GetSensorTempResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetSensorTempResponseDefaultTypeInternal _GetSensorTempResponse_default_instance_;
}  // namespace kaya
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto[4];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto = nullptr;

const uint32_t TableStruct_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, pixel_format_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, exposure_us_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, analog_gain_level_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, analog_black_level_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, focus_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, wb_ratio_red_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, wb_ratio_green_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, wb_ratio_blue_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, wb_b_gain_db_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabRequest, focusing_mode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::kaya::GrabResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::kaya::GrabResponse, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabResponse, height_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabResponse, width_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabResponse, bits_per_pixel_),
  PROTOBUF_FIELD_OFFSET(::kaya::GrabResponse, data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::kaya::GetSensorTempRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::kaya::GetSensorTempResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::kaya::GetSensorTempResponse, sensor_temp_c_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::kaya::GrabRequest)},
  { 17, -1, -1, sizeof(::kaya::GrabResponse)},
  { 28, -1, -1, sizeof(::kaya::GetSensorTempRequest)},
  { 34, -1, -1, sizeof(::kaya::GetSensorTempResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::kaya::_GrabRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::kaya::_GrabResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::kaya::_GetSensorTempRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::kaya::_GetSensorTempResponse_default_instance_),
};

const char descriptor_table_protodef_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n+generated/lib/drivers/kaya/proto/kaya."
  "proto\022\004kaya\"\231\002\n\013GrabRequest\022\024\n\014timestamp"
  "_ms\030\001 \001(\003\022\'\n\014pixel_format\030\002 \001(\0162\021.kaya.P"
  "ixelFormat\022\023\n\013exposure_us\030\003 \001(\005\022\031\n\021analo"
  "g_gain_level\030\004 \001(\002\022\032\n\022analog_black_level"
  "\030\005 \001(\002\022\r\n\005focus\030\006 \001(\002\022\024\n\014wb_ratio_red\030\007 "
  "\001(\002\022\026\n\016wb_ratio_green\030\010 \001(\002\022\025\n\rwb_ratio_"
  "blue\030\t \001(\002\022\024\n\014wb_b_gain_db\030\n \001(\002\022\025\n\rfocu"
  "sing_mode\030\013 \001(\010\"i\n\014GrabResponse\022\024\n\014times"
  "tamp_ms\030\001 \001(\003\022\016\n\006height\030\002 \001(\005\022\r\n\005width\030\003"
  " \001(\005\022\026\n\016bits_per_pixel\030\004 \001(\005\022\014\n\004data\030\005 \001"
  "(\014\"\026\n\024GetSensorTempRequest\".\n\025GetSensorT"
  "empResponse\022\025\n\rsensor_temp_c\030\001 \001(\005*5\n\013Pi"
  "xelFormat\022\016\n\nPF_UNKNOWN\020\000\022\010\n\004BGR8\020\001\022\014\n\010B"
  "ayerRG8\020\0022\212\001\n\013KayaService\022/\n\004Grab\022\021.kaya"
  ".GrabRequest\032\022.kaya.GrabResponse\"\000\022J\n\rGe"
  "tSensorTemp\022\032.kaya.GetSensorTempRequest\032"
  "\033.kaya.GetSensorTempResponse\"\000b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto = {
  false, false, 718, descriptor_table_protodef_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto, "generated/lib/drivers/kaya/proto/kaya.proto", 
  &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_once, nullptr, 0, 4,
  schemas, file_default_instances, TableStruct_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto::offsets,
  file_level_metadata_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto, file_level_enum_descriptors_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto, file_level_service_descriptors_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_getter() {
  return &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto(&descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto);
namespace kaya {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PixelFormat_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto);
  return file_level_enum_descriptors_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto[0];
}
bool PixelFormat_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class GrabRequest::_Internal {
 public:
};

GrabRequest::GrabRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:kaya.GrabRequest)
}
GrabRequest::GrabRequest(const GrabRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&focusing_mode_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(focusing_mode_));
  // @@protoc_insertion_point(copy_constructor:kaya.GrabRequest)
}

inline void GrabRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&focusing_mode_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(focusing_mode_));
}

GrabRequest::~GrabRequest() {
  // @@protoc_insertion_point(destructor:kaya.GrabRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GrabRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GrabRequest::ArenaDtor(void* object) {
  GrabRequest* _this = reinterpret_cast< GrabRequest* >(object);
  (void)_this;
}
void GrabRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GrabRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GrabRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:kaya.GrabRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&focusing_mode_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(focusing_mode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GrabRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .kaya.PixelFormat pixel_format = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_pixel_format(static_cast<::kaya::PixelFormat>(val));
        } else
          goto handle_unusual;
        continue;
      // int32 exposure_us = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          exposure_us_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float analog_gain_level = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          analog_gain_level_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float analog_black_level = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          analog_black_level_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float focus = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          focus_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float wb_ratio_red = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          wb_ratio_red_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float wb_ratio_green = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          wb_ratio_green_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float wb_ratio_blue = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          wb_ratio_blue_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float wb_b_gain_db = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 85)) {
          wb_b_gain_db_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // bool focusing_mode = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          focusing_mode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GrabRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:kaya.GrabRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // .kaya.PixelFormat pixel_format = 2;
  if (this->_internal_pixel_format() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_pixel_format(), target);
  }

  // int32 exposure_us = 3;
  if (this->_internal_exposure_us() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_exposure_us(), target);
  }

  // float analog_gain_level = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_analog_gain_level = this->_internal_analog_gain_level();
  uint32_t raw_analog_gain_level;
  memcpy(&raw_analog_gain_level, &tmp_analog_gain_level, sizeof(tmp_analog_gain_level));
  if (raw_analog_gain_level != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_analog_gain_level(), target);
  }

  // float analog_black_level = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_analog_black_level = this->_internal_analog_black_level();
  uint32_t raw_analog_black_level;
  memcpy(&raw_analog_black_level, &tmp_analog_black_level, sizeof(tmp_analog_black_level));
  if (raw_analog_black_level != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_analog_black_level(), target);
  }

  // float focus = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focus = this->_internal_focus();
  uint32_t raw_focus;
  memcpy(&raw_focus, &tmp_focus, sizeof(tmp_focus));
  if (raw_focus != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_focus(), target);
  }

  // float wb_ratio_red = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_ratio_red = this->_internal_wb_ratio_red();
  uint32_t raw_wb_ratio_red;
  memcpy(&raw_wb_ratio_red, &tmp_wb_ratio_red, sizeof(tmp_wb_ratio_red));
  if (raw_wb_ratio_red != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_wb_ratio_red(), target);
  }

  // float wb_ratio_green = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_ratio_green = this->_internal_wb_ratio_green();
  uint32_t raw_wb_ratio_green;
  memcpy(&raw_wb_ratio_green, &tmp_wb_ratio_green, sizeof(tmp_wb_ratio_green));
  if (raw_wb_ratio_green != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_wb_ratio_green(), target);
  }

  // float wb_ratio_blue = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_ratio_blue = this->_internal_wb_ratio_blue();
  uint32_t raw_wb_ratio_blue;
  memcpy(&raw_wb_ratio_blue, &tmp_wb_ratio_blue, sizeof(tmp_wb_ratio_blue));
  if (raw_wb_ratio_blue != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_wb_ratio_blue(), target);
  }

  // float wb_b_gain_db = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_b_gain_db = this->_internal_wb_b_gain_db();
  uint32_t raw_wb_b_gain_db;
  memcpy(&raw_wb_b_gain_db, &tmp_wb_b_gain_db, sizeof(tmp_wb_b_gain_db));
  if (raw_wb_b_gain_db != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_wb_b_gain_db(), target);
  }

  // bool focusing_mode = 11;
  if (this->_internal_focusing_mode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(11, this->_internal_focusing_mode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:kaya.GrabRequest)
  return target;
}

size_t GrabRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:kaya.GrabRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // .kaya.PixelFormat pixel_format = 2;
  if (this->_internal_pixel_format() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_pixel_format());
  }

  // int32 exposure_us = 3;
  if (this->_internal_exposure_us() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_exposure_us());
  }

  // float analog_gain_level = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_analog_gain_level = this->_internal_analog_gain_level();
  uint32_t raw_analog_gain_level;
  memcpy(&raw_analog_gain_level, &tmp_analog_gain_level, sizeof(tmp_analog_gain_level));
  if (raw_analog_gain_level != 0) {
    total_size += 1 + 4;
  }

  // float analog_black_level = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_analog_black_level = this->_internal_analog_black_level();
  uint32_t raw_analog_black_level;
  memcpy(&raw_analog_black_level, &tmp_analog_black_level, sizeof(tmp_analog_black_level));
  if (raw_analog_black_level != 0) {
    total_size += 1 + 4;
  }

  // float focus = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focus = this->_internal_focus();
  uint32_t raw_focus;
  memcpy(&raw_focus, &tmp_focus, sizeof(tmp_focus));
  if (raw_focus != 0) {
    total_size += 1 + 4;
  }

  // float wb_ratio_red = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_ratio_red = this->_internal_wb_ratio_red();
  uint32_t raw_wb_ratio_red;
  memcpy(&raw_wb_ratio_red, &tmp_wb_ratio_red, sizeof(tmp_wb_ratio_red));
  if (raw_wb_ratio_red != 0) {
    total_size += 1 + 4;
  }

  // float wb_ratio_green = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_ratio_green = this->_internal_wb_ratio_green();
  uint32_t raw_wb_ratio_green;
  memcpy(&raw_wb_ratio_green, &tmp_wb_ratio_green, sizeof(tmp_wb_ratio_green));
  if (raw_wb_ratio_green != 0) {
    total_size += 1 + 4;
  }

  // float wb_ratio_blue = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_ratio_blue = this->_internal_wb_ratio_blue();
  uint32_t raw_wb_ratio_blue;
  memcpy(&raw_wb_ratio_blue, &tmp_wb_ratio_blue, sizeof(tmp_wb_ratio_blue));
  if (raw_wb_ratio_blue != 0) {
    total_size += 1 + 4;
  }

  // float wb_b_gain_db = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_b_gain_db = this->_internal_wb_b_gain_db();
  uint32_t raw_wb_b_gain_db;
  memcpy(&raw_wb_b_gain_db, &tmp_wb_b_gain_db, sizeof(tmp_wb_b_gain_db));
  if (raw_wb_b_gain_db != 0) {
    total_size += 1 + 4;
  }

  // bool focusing_mode = 11;
  if (this->_internal_focusing_mode() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GrabRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GrabRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GrabRequest::GetClassData() const { return &_class_data_; }

void GrabRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GrabRequest *>(to)->MergeFrom(
      static_cast<const GrabRequest &>(from));
}


void GrabRequest::MergeFrom(const GrabRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:kaya.GrabRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_pixel_format() != 0) {
    _internal_set_pixel_format(from._internal_pixel_format());
  }
  if (from._internal_exposure_us() != 0) {
    _internal_set_exposure_us(from._internal_exposure_us());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_analog_gain_level = from._internal_analog_gain_level();
  uint32_t raw_analog_gain_level;
  memcpy(&raw_analog_gain_level, &tmp_analog_gain_level, sizeof(tmp_analog_gain_level));
  if (raw_analog_gain_level != 0) {
    _internal_set_analog_gain_level(from._internal_analog_gain_level());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_analog_black_level = from._internal_analog_black_level();
  uint32_t raw_analog_black_level;
  memcpy(&raw_analog_black_level, &tmp_analog_black_level, sizeof(tmp_analog_black_level));
  if (raw_analog_black_level != 0) {
    _internal_set_analog_black_level(from._internal_analog_black_level());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focus = from._internal_focus();
  uint32_t raw_focus;
  memcpy(&raw_focus, &tmp_focus, sizeof(tmp_focus));
  if (raw_focus != 0) {
    _internal_set_focus(from._internal_focus());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_ratio_red = from._internal_wb_ratio_red();
  uint32_t raw_wb_ratio_red;
  memcpy(&raw_wb_ratio_red, &tmp_wb_ratio_red, sizeof(tmp_wb_ratio_red));
  if (raw_wb_ratio_red != 0) {
    _internal_set_wb_ratio_red(from._internal_wb_ratio_red());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_ratio_green = from._internal_wb_ratio_green();
  uint32_t raw_wb_ratio_green;
  memcpy(&raw_wb_ratio_green, &tmp_wb_ratio_green, sizeof(tmp_wb_ratio_green));
  if (raw_wb_ratio_green != 0) {
    _internal_set_wb_ratio_green(from._internal_wb_ratio_green());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_ratio_blue = from._internal_wb_ratio_blue();
  uint32_t raw_wb_ratio_blue;
  memcpy(&raw_wb_ratio_blue, &tmp_wb_ratio_blue, sizeof(tmp_wb_ratio_blue));
  if (raw_wb_ratio_blue != 0) {
    _internal_set_wb_ratio_blue(from._internal_wb_ratio_blue());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_wb_b_gain_db = from._internal_wb_b_gain_db();
  uint32_t raw_wb_b_gain_db;
  memcpy(&raw_wb_b_gain_db, &tmp_wb_b_gain_db, sizeof(tmp_wb_b_gain_db));
  if (raw_wb_b_gain_db != 0) {
    _internal_set_wb_b_gain_db(from._internal_wb_b_gain_db());
  }
  if (from._internal_focusing_mode() != 0) {
    _internal_set_focusing_mode(from._internal_focusing_mode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GrabRequest::CopyFrom(const GrabRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:kaya.GrabRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GrabRequest::IsInitialized() const {
  return true;
}

void GrabRequest::InternalSwap(GrabRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GrabRequest, focusing_mode_)
      + sizeof(GrabRequest::focusing_mode_)
      - PROTOBUF_FIELD_OFFSET(GrabRequest, timestamp_ms_)>(
          reinterpret_cast<char*>(&timestamp_ms_),
          reinterpret_cast<char*>(&other->timestamp_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GrabRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_getter, &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_once,
      file_level_metadata_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto[0]);
}

// ===================================================================

class GrabResponse::_Internal {
 public:
};

GrabResponse::GrabResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:kaya.GrabResponse)
}
GrabResponse::GrabResponse(const GrabResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArenaForAllocation());
  }
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&bits_per_pixel_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(bits_per_pixel_));
  // @@protoc_insertion_point(copy_constructor:kaya.GrabResponse)
}

inline void GrabResponse::SharedCtor() {
data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bits_per_pixel_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(bits_per_pixel_));
}

GrabResponse::~GrabResponse() {
  // @@protoc_insertion_point(destructor:kaya.GrabResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GrabResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GrabResponse::ArenaDtor(void* object) {
  GrabResponse* _this = reinterpret_cast< GrabResponse* >(object);
  (void)_this;
}
void GrabResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GrabResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GrabResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:kaya.GrabResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  data_.ClearToEmpty();
  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&bits_per_pixel_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(bits_per_pixel_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GrabResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 width = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 bits_per_pixel = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          bits_per_pixel_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes data = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GrabResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:kaya.GrabResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_height(), target);
  }

  // int32 width = 3;
  if (this->_internal_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_width(), target);
  }

  // int32 bits_per_pixel = 4;
  if (this->_internal_bits_per_pixel() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_bits_per_pixel(), target);
  }

  // bytes data = 5;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        5, this->_internal_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:kaya.GrabResponse)
  return target;
}

size_t GrabResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:kaya.GrabResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes data = 5;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_height());
  }

  // int32 width = 3;
  if (this->_internal_width() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_width());
  }

  // int32 bits_per_pixel = 4;
  if (this->_internal_bits_per_pixel() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_bits_per_pixel());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GrabResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GrabResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GrabResponse::GetClassData() const { return &_class_data_; }

void GrabResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GrabResponse *>(to)->MergeFrom(
      static_cast<const GrabResponse &>(from));
}


void GrabResponse::MergeFrom(const GrabResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:kaya.GrabResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _internal_set_data(from._internal_data());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_height() != 0) {
    _internal_set_height(from._internal_height());
  }
  if (from._internal_width() != 0) {
    _internal_set_width(from._internal_width());
  }
  if (from._internal_bits_per_pixel() != 0) {
    _internal_set_bits_per_pixel(from._internal_bits_per_pixel());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GrabResponse::CopyFrom(const GrabResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:kaya.GrabResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GrabResponse::IsInitialized() const {
  return true;
}

void GrabResponse::InternalSwap(GrabResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &data_, lhs_arena,
      &other->data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GrabResponse, bits_per_pixel_)
      + sizeof(GrabResponse::bits_per_pixel_)
      - PROTOBUF_FIELD_OFFSET(GrabResponse, timestamp_ms_)>(
          reinterpret_cast<char*>(&timestamp_ms_),
          reinterpret_cast<char*>(&other->timestamp_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GrabResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_getter, &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_once,
      file_level_metadata_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto[1]);
}

// ===================================================================

class GetSensorTempRequest::_Internal {
 public:
};

GetSensorTempRequest::GetSensorTempRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:kaya.GetSensorTempRequest)
}
GetSensorTempRequest::GetSensorTempRequest(const GetSensorTempRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:kaya.GetSensorTempRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetSensorTempRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetSensorTempRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata GetSensorTempRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_getter, &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_once,
      file_level_metadata_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto[2]);
}

// ===================================================================

class GetSensorTempResponse::_Internal {
 public:
};

GetSensorTempResponse::GetSensorTempResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:kaya.GetSensorTempResponse)
}
GetSensorTempResponse::GetSensorTempResponse(const GetSensorTempResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  sensor_temp_c_ = from.sensor_temp_c_;
  // @@protoc_insertion_point(copy_constructor:kaya.GetSensorTempResponse)
}

inline void GetSensorTempResponse::SharedCtor() {
sensor_temp_c_ = 0;
}

GetSensorTempResponse::~GetSensorTempResponse() {
  // @@protoc_insertion_point(destructor:kaya.GetSensorTempResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetSensorTempResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetSensorTempResponse::ArenaDtor(void* object) {
  GetSensorTempResponse* _this = reinterpret_cast< GetSensorTempResponse* >(object);
  (void)_this;
}
void GetSensorTempResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetSensorTempResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetSensorTempResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:kaya.GetSensorTempResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  sensor_temp_c_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetSensorTempResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 sensor_temp_c = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          sensor_temp_c_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetSensorTempResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:kaya.GetSensorTempResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 sensor_temp_c = 1;
  if (this->_internal_sensor_temp_c() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_sensor_temp_c(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:kaya.GetSensorTempResponse)
  return target;
}

size_t GetSensorTempResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:kaya.GetSensorTempResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 sensor_temp_c = 1;
  if (this->_internal_sensor_temp_c() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_sensor_temp_c());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetSensorTempResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetSensorTempResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetSensorTempResponse::GetClassData() const { return &_class_data_; }

void GetSensorTempResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetSensorTempResponse *>(to)->MergeFrom(
      static_cast<const GetSensorTempResponse &>(from));
}


void GetSensorTempResponse::MergeFrom(const GetSensorTempResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:kaya.GetSensorTempResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_sensor_temp_c() != 0) {
    _internal_set_sensor_temp_c(from._internal_sensor_temp_c());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetSensorTempResponse::CopyFrom(const GetSensorTempResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:kaya.GetSensorTempResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetSensorTempResponse::IsInitialized() const {
  return true;
}

void GetSensorTempResponse::InternalSwap(GetSensorTempResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(sensor_temp_c_, other->sensor_temp_c_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetSensorTempResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_getter, &descriptor_table_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto_once,
      file_level_metadata_generated_2flib_2fdrivers_2fkaya_2fproto_2fkaya_2eproto[3]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace kaya
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::kaya::GrabRequest* Arena::CreateMaybeMessage< ::kaya::GrabRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::kaya::GrabRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::kaya::GrabResponse* Arena::CreateMaybeMessage< ::kaya::GrabResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::kaya::GrabResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::kaya::GetSensorTempRequest* Arena::CreateMaybeMessage< ::kaya::GetSensorTempRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::kaya::GetSensorTempRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::kaya::GetSensorTempResponse* Arena::CreateMaybeMessage< ::kaya::GetSensorTempResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::kaya::GetSensorTempResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

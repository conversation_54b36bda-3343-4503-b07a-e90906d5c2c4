# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.lib.drivers.kaya.proto import kaya_pb2 as generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2


class KayaServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Grab = channel.unary_unary(
                '/kaya.KayaService/Grab',
                request_serializer=generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GrabRequest.SerializeToString,
                response_deserializer=generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GrabResponse.FromString,
                )
        self.GetSensorTemp = channel.unary_unary(
                '/kaya.KayaService/GetSensorTemp',
                request_serializer=generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GetSensorTempRequest.SerializeToString,
                response_deserializer=generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GetSensorTempResponse.FromString,
                )


class KayaServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Grab(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSensorTemp(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_KayaServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Grab': grpc.unary_unary_rpc_method_handler(
                    servicer.Grab,
                    request_deserializer=generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GrabRequest.FromString,
                    response_serializer=generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GrabResponse.SerializeToString,
            ),
            'GetSensorTemp': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSensorTemp,
                    request_deserializer=generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GetSensorTempRequest.FromString,
                    response_serializer=generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GetSensorTempResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'kaya.KayaService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class KayaService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Grab(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/kaya.KayaService/Grab',
            generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GrabRequest.SerializeToString,
            generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GrabResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSensorTemp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/kaya.KayaService/GetSensorTemp',
            generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GetSensorTempRequest.SerializeToString,
            generated_dot_lib_dot_drivers_dot_kaya_dot_proto_dot_kaya__pb2.GetSensorTempResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/kaya/proto/kaya.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/kaya/proto/kaya.proto',
  package='kaya',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n+generated/lib/drivers/kaya/proto/kaya.proto\x12\x04kaya\"\x99\x02\n\x0bGrabRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\'\n\x0cpixel_format\x18\x02 \x01(\x0e\x32\x11.kaya.PixelFormat\x12\x13\n\x0b\x65xposure_us\x18\x03 \x01(\x05\x12\x19\n\x11\x61nalog_gain_level\x18\x04 \x01(\x02\x12\x1a\n\x12\x61nalog_black_level\x18\x05 \x01(\x02\x12\r\n\x05\x66ocus\x18\x06 \x01(\x02\x12\x14\n\x0cwb_ratio_red\x18\x07 \x01(\x02\x12\x16\n\x0ewb_ratio_green\x18\x08 \x01(\x02\x12\x15\n\rwb_ratio_blue\x18\t \x01(\x02\x12\x14\n\x0cwb_b_gain_db\x18\n \x01(\x02\x12\x15\n\rfocusing_mode\x18\x0b \x01(\x08\"i\n\x0cGrabResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x0e\n\x06height\x18\x02 \x01(\x05\x12\r\n\x05width\x18\x03 \x01(\x05\x12\x16\n\x0e\x62its_per_pixel\x18\x04 \x01(\x05\x12\x0c\n\x04\x64\x61ta\x18\x05 \x01(\x0c\"\x16\n\x14GetSensorTempRequest\".\n\x15GetSensorTempResponse\x12\x15\n\rsensor_temp_c\x18\x01 \x01(\x05*5\n\x0bPixelFormat\x12\x0e\n\nPF_UNKNOWN\x10\x00\x12\x08\n\x04\x42GR8\x10\x01\x12\x0c\n\x08\x42\x61yerRG8\x10\x02\x32\x8a\x01\n\x0bKayaService\x12/\n\x04Grab\x12\x11.kaya.GrabRequest\x1a\x12.kaya.GrabResponse\"\x00\x12J\n\rGetSensorTemp\x12\x1a.kaya.GetSensorTempRequest\x1a\x1b.kaya.GetSensorTempResponse\"\x00\x62\x06proto3'
)

_PIXELFORMAT = _descriptor.EnumDescriptor(
  name='PixelFormat',
  full_name='kaya.PixelFormat',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PF_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BGR8', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BayerRG8', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=516,
  serialized_end=569,
)
_sym_db.RegisterEnumDescriptor(_PIXELFORMAT)

PixelFormat = enum_type_wrapper.EnumTypeWrapper(_PIXELFORMAT)
PF_UNKNOWN = 0
BGR8 = 1
BayerRG8 = 2



_GRABREQUEST = _descriptor.Descriptor(
  name='GrabRequest',
  full_name='kaya.GrabRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='kaya.GrabRequest.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pixel_format', full_name='kaya.GrabRequest.pixel_format', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exposure_us', full_name='kaya.GrabRequest.exposure_us', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='analog_gain_level', full_name='kaya.GrabRequest.analog_gain_level', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='analog_black_level', full_name='kaya.GrabRequest.analog_black_level', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focus', full_name='kaya.GrabRequest.focus', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_ratio_red', full_name='kaya.GrabRequest.wb_ratio_red', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_ratio_green', full_name='kaya.GrabRequest.wb_ratio_green', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_ratio_blue', full_name='kaya.GrabRequest.wb_ratio_blue', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wb_b_gain_db', full_name='kaya.GrabRequest.wb_b_gain_db', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focusing_mode', full_name='kaya.GrabRequest.focusing_mode', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=54,
  serialized_end=335,
)


_GRABRESPONSE = _descriptor.Descriptor(
  name='GrabResponse',
  full_name='kaya.GrabResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='kaya.GrabResponse.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='kaya.GrabResponse.height', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width', full_name='kaya.GrabResponse.width', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bits_per_pixel', full_name='kaya.GrabResponse.bits_per_pixel', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='kaya.GrabResponse.data', index=4,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=337,
  serialized_end=442,
)


_GETSENSORTEMPREQUEST = _descriptor.Descriptor(
  name='GetSensorTempRequest',
  full_name='kaya.GetSensorTempRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=444,
  serialized_end=466,
)


_GETSENSORTEMPRESPONSE = _descriptor.Descriptor(
  name='GetSensorTempResponse',
  full_name='kaya.GetSensorTempResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor_temp_c', full_name='kaya.GetSensorTempResponse.sensor_temp_c', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=468,
  serialized_end=514,
)

_GRABREQUEST.fields_by_name['pixel_format'].enum_type = _PIXELFORMAT
DESCRIPTOR.message_types_by_name['GrabRequest'] = _GRABREQUEST
DESCRIPTOR.message_types_by_name['GrabResponse'] = _GRABRESPONSE
DESCRIPTOR.message_types_by_name['GetSensorTempRequest'] = _GETSENSORTEMPREQUEST
DESCRIPTOR.message_types_by_name['GetSensorTempResponse'] = _GETSENSORTEMPRESPONSE
DESCRIPTOR.enum_types_by_name['PixelFormat'] = _PIXELFORMAT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GrabRequest = _reflection.GeneratedProtocolMessageType('GrabRequest', (_message.Message,), {
  'DESCRIPTOR' : _GRABREQUEST,
  '__module__' : 'generated.lib.drivers.kaya.proto.kaya_pb2'
  # @@protoc_insertion_point(class_scope:kaya.GrabRequest)
  })
_sym_db.RegisterMessage(GrabRequest)

GrabResponse = _reflection.GeneratedProtocolMessageType('GrabResponse', (_message.Message,), {
  'DESCRIPTOR' : _GRABRESPONSE,
  '__module__' : 'generated.lib.drivers.kaya.proto.kaya_pb2'
  # @@protoc_insertion_point(class_scope:kaya.GrabResponse)
  })
_sym_db.RegisterMessage(GrabResponse)

GetSensorTempRequest = _reflection.GeneratedProtocolMessageType('GetSensorTempRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSENSORTEMPREQUEST,
  '__module__' : 'generated.lib.drivers.kaya.proto.kaya_pb2'
  # @@protoc_insertion_point(class_scope:kaya.GetSensorTempRequest)
  })
_sym_db.RegisterMessage(GetSensorTempRequest)

GetSensorTempResponse = _reflection.GeneratedProtocolMessageType('GetSensorTempResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSENSORTEMPRESPONSE,
  '__module__' : 'generated.lib.drivers.kaya.proto.kaya_pb2'
  # @@protoc_insertion_point(class_scope:kaya.GetSensorTempResponse)
  })
_sym_db.RegisterMessage(GetSensorTempResponse)



_KAYASERVICE = _descriptor.ServiceDescriptor(
  name='KayaService',
  full_name='kaya.KayaService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=572,
  serialized_end=710,
  methods=[
  _descriptor.MethodDescriptor(
    name='Grab',
    full_name='kaya.KayaService.Grab',
    index=0,
    containing_service=None,
    input_type=_GRABREQUEST,
    output_type=_GRABRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSensorTemp',
    full_name='kaya.KayaService.GetSensorTemp',
    index=1,
    containing_service=None,
    input_type=_GETSENSORTEMPREQUEST,
    output_type=_GETSENSORTEMPRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_KAYASERVICE)

DESCRIPTOR.services_by_name['KayaService'] = _KAYASERVICE

# @@protoc_insertion_point(module_scope)

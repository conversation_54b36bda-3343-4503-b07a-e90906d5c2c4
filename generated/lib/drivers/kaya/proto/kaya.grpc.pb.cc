// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: generated/lib/drivers/kaya/proto/kaya.proto

#include "generated/lib/drivers/kaya/proto/kaya.pb.h"
#include "generated/lib/drivers/kaya/proto/kaya.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace kaya {

static const char* KayaService_method_names[] = {
  "/kaya.KayaService/Grab",
  "/kaya.KayaService/GetSensorTemp",
};

std::unique_ptr< KayaService::Stub> KayaService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< KayaService::Stub> stub(new KayaService::Stub(channel, options));
  return stub;
}

KayaService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Grab_(KayaService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSensorTemp_(KayaService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status KayaService::Stub::Grab(::grpc::ClientContext* context, const ::kaya::GrabRequest& request, ::kaya::GrabResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::kaya::GrabRequest, ::kaya::GrabResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Grab_, context, request, response);
}

void KayaService::Stub::async::Grab(::grpc::ClientContext* context, const ::kaya::GrabRequest* request, ::kaya::GrabResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::kaya::GrabRequest, ::kaya::GrabResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Grab_, context, request, response, std::move(f));
}

void KayaService::Stub::async::Grab(::grpc::ClientContext* context, const ::kaya::GrabRequest* request, ::kaya::GrabResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Grab_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::kaya::GrabResponse>* KayaService::Stub::PrepareAsyncGrabRaw(::grpc::ClientContext* context, const ::kaya::GrabRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::kaya::GrabResponse, ::kaya::GrabRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Grab_, context, request);
}

::grpc::ClientAsyncResponseReader< ::kaya::GrabResponse>* KayaService::Stub::AsyncGrabRaw(::grpc::ClientContext* context, const ::kaya::GrabRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGrabRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status KayaService::Stub::GetSensorTemp(::grpc::ClientContext* context, const ::kaya::GetSensorTempRequest& request, ::kaya::GetSensorTempResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::kaya::GetSensorTempRequest, ::kaya::GetSensorTempResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSensorTemp_, context, request, response);
}

void KayaService::Stub::async::GetSensorTemp(::grpc::ClientContext* context, const ::kaya::GetSensorTempRequest* request, ::kaya::GetSensorTempResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::kaya::GetSensorTempRequest, ::kaya::GetSensorTempResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSensorTemp_, context, request, response, std::move(f));
}

void KayaService::Stub::async::GetSensorTemp(::grpc::ClientContext* context, const ::kaya::GetSensorTempRequest* request, ::kaya::GetSensorTempResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSensorTemp_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::kaya::GetSensorTempResponse>* KayaService::Stub::PrepareAsyncGetSensorTempRaw(::grpc::ClientContext* context, const ::kaya::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::kaya::GetSensorTempResponse, ::kaya::GetSensorTempRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSensorTemp_, context, request);
}

::grpc::ClientAsyncResponseReader< ::kaya::GetSensorTempResponse>* KayaService::Stub::AsyncGetSensorTempRaw(::grpc::ClientContext* context, const ::kaya::GetSensorTempRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSensorTempRaw(context, request, cq);
  result->StartCall();
  return result;
}

KayaService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      KayaService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< KayaService::Service, ::kaya::GrabRequest, ::kaya::GrabResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](KayaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::kaya::GrabRequest* req,
             ::kaya::GrabResponse* resp) {
               return service->Grab(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      KayaService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< KayaService::Service, ::kaya::GetSensorTempRequest, ::kaya::GetSensorTempResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](KayaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::kaya::GetSensorTempRequest* req,
             ::kaya::GetSensorTempResponse* resp) {
               return service->GetSensorTemp(ctx, req, resp);
             }, this)));
}

KayaService::Service::~Service() {
}

::grpc::Status KayaService::Service::Grab(::grpc::ServerContext* context, const ::kaya::GrabRequest* request, ::kaya::GrabResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status KayaService::Service::GetSensorTemp(::grpc::ServerContext* context, const ::kaya::GetSensorTempRequest* request, ::kaya::GetSensorTempResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace kaya


// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: lib/drivers/zedcam/proto/zed.proto

#include "lib/drivers/zedcam/proto/zed.pb.h"
#include "lib/drivers/zedcam/proto/zed.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace zed {

static const char* ZED_method_names[] = {
  "/zed.ZED/Grab",
};

std::unique_ptr< ZED::Stub> ZED::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ZED::Stub> stub(new ZED::Stub(channel, options));
  return stub;
}

ZED::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Grab_(ZED_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ZED::Stub::Grab(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::zed::GrabResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::zed::GrabRequest, ::zed::GrabResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Grab_, context, request, response);
}

void ZED::Stub::async::Grab(::grpc::ClientContext* context, const ::zed::GrabRequest* request, ::zed::GrabResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::zed::GrabRequest, ::zed::GrabResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Grab_, context, request, response, std::move(f));
}

void ZED::Stub::async::Grab(::grpc::ClientContext* context, const ::zed::GrabRequest* request, ::zed::GrabResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Grab_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::zed::GrabResponse>* ZED::Stub::PrepareAsyncGrabRaw(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::zed::GrabResponse, ::zed::GrabRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Grab_, context, request);
}

::grpc::ClientAsyncResponseReader< ::zed::GrabResponse>* ZED::Stub::AsyncGrabRaw(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGrabRaw(context, request, cq);
  result->StartCall();
  return result;
}

ZED::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ZED_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ZED::Service, ::zed::GrabRequest, ::zed::GrabResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ZED::Service* service,
             ::grpc::ServerContext* ctx,
             const ::zed::GrabRequest* req,
             ::zed::GrabResponse* resp) {
               return service->Grab(ctx, req, resp);
             }, this)));
}

ZED::Service::~Service() {
}

::grpc::Status ZED::Service::Grab(::grpc::ServerContext* context, const ::zed::GrabRequest* request, ::zed::GrabResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace zed


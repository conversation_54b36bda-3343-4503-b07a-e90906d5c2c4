// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/drivers/zedcam/proto/zed.proto

#include "lib/drivers/zedcam/proto/zed.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace zed {
constexpr GrabRequest::GrabRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timestamp_ms_(int64_t{0}){}
struct GrabRequestDefaultTypeInternal {
  constexpr GrabRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GrabRequestDefaultTypeInternal() {}
  union {
    GrabRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GrabRequestDefaultTypeInternal _GrabRequest_default_instance_;
constexpr GrabResponse::GrabResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : view_image_bytes_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , view_depth_bytes_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0})
  , height_(0)
  , width_(0){}
struct GrabResponseDefaultTypeInternal {
  constexpr GrabResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GrabResponseDefaultTypeInternal() {}
  union {
    GrabResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GrabResponseDefaultTypeInternal _GrabResponse_default_instance_;
}  // namespace zed
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto = nullptr;

const uint32_t TableStruct_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::zed::GrabRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::zed::GrabRequest, timestamp_ms_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::zed::GrabResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::zed::GrabResponse, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::zed::GrabResponse, height_),
  PROTOBUF_FIELD_OFFSET(::zed::GrabResponse, width_),
  PROTOBUF_FIELD_OFFSET(::zed::GrabResponse, view_image_bytes_),
  PROTOBUF_FIELD_OFFSET(::zed::GrabResponse, view_depth_bytes_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::zed::GrabRequest)},
  { 7, -1, -1, sizeof(::zed::GrabResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::zed::_GrabRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::zed::_GrabResponse_default_instance_),
};

const char descriptor_table_protodef_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\"lib/drivers/zedcam/proto/zed.proto\022\003ze"
  "d\"#\n\013GrabRequest\022\024\n\014timestamp_ms\030\001 \001(\003\"w"
  "\n\014GrabResponse\022\024\n\014timestamp_ms\030\001 \001(\003\022\016\n\006"
  "height\030\002 \001(\005\022\r\n\005width\030\003 \001(\005\022\030\n\020view_imag"
  "e_bytes\030\004 \001(\014\022\030\n\020view_depth_bytes\030\005 \001(\0142"
  "4\n\003ZED\022-\n\004Grab\022\020.zed.GrabRequest\032\021.zed.G"
  "rabResponse\"\000b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto = {
  false, false, 261, descriptor_table_protodef_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto, "lib/drivers/zedcam/proto/zed.proto", 
  &descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto_once, nullptr, 0, 2,
  schemas, file_default_instances, TableStruct_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto::offsets,
  file_level_metadata_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto, file_level_enum_descriptors_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto, file_level_service_descriptors_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto_getter() {
  return &descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto(&descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto);
namespace zed {

// ===================================================================

class GrabRequest::_Internal {
 public:
};

GrabRequest::GrabRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:zed.GrabRequest)
}
GrabRequest::GrabRequest(const GrabRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:zed.GrabRequest)
}

inline void GrabRequest::SharedCtor() {
timestamp_ms_ = int64_t{0};
}

GrabRequest::~GrabRequest() {
  // @@protoc_insertion_point(destructor:zed.GrabRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GrabRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GrabRequest::ArenaDtor(void* object) {
  GrabRequest* _this = reinterpret_cast< GrabRequest* >(object);
  (void)_this;
}
void GrabRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GrabRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GrabRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:zed.GrabRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  timestamp_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GrabRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GrabRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:zed.GrabRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:zed.GrabRequest)
  return target;
}

size_t GrabRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:zed.GrabRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GrabRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GrabRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GrabRequest::GetClassData() const { return &_class_data_; }

void GrabRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GrabRequest *>(to)->MergeFrom(
      static_cast<const GrabRequest &>(from));
}


void GrabRequest::MergeFrom(const GrabRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:zed.GrabRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GrabRequest::CopyFrom(const GrabRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:zed.GrabRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GrabRequest::IsInitialized() const {
  return true;
}

void GrabRequest::InternalSwap(GrabRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(timestamp_ms_, other->timestamp_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GrabRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto_getter, &descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto_once,
      file_level_metadata_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto[0]);
}

// ===================================================================

class GrabResponse::_Internal {
 public:
};

GrabResponse::GrabResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:zed.GrabResponse)
}
GrabResponse::GrabResponse(const GrabResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  view_image_bytes_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    view_image_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_view_image_bytes().empty()) {
    view_image_bytes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_view_image_bytes(), 
      GetArenaForAllocation());
  }
  view_depth_bytes_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    view_depth_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_view_depth_bytes().empty()) {
    view_depth_bytes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_view_depth_bytes(), 
      GetArenaForAllocation());
  }
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&width_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(width_));
  // @@protoc_insertion_point(copy_constructor:zed.GrabResponse)
}

inline void GrabResponse::SharedCtor() {
view_image_bytes_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  view_image_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
view_depth_bytes_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  view_depth_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&width_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(width_));
}

GrabResponse::~GrabResponse() {
  // @@protoc_insertion_point(destructor:zed.GrabResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GrabResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  view_image_bytes_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  view_depth_bytes_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GrabResponse::ArenaDtor(void* object) {
  GrabResponse* _this = reinterpret_cast< GrabResponse* >(object);
  (void)_this;
}
void GrabResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GrabResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GrabResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:zed.GrabResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  view_image_bytes_.ClearToEmpty();
  view_depth_bytes_.ClearToEmpty();
  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&width_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(width_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GrabResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 width = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes view_image_bytes = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_view_image_bytes();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes view_depth_bytes = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_view_depth_bytes();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GrabResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:zed.GrabResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_height(), target);
  }

  // int32 width = 3;
  if (this->_internal_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_width(), target);
  }

  // bytes view_image_bytes = 4;
  if (!this->_internal_view_image_bytes().empty()) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_view_image_bytes(), target);
  }

  // bytes view_depth_bytes = 5;
  if (!this->_internal_view_depth_bytes().empty()) {
    target = stream->WriteBytesMaybeAliased(
        5, this->_internal_view_depth_bytes(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:zed.GrabResponse)
  return target;
}

size_t GrabResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:zed.GrabResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes view_image_bytes = 4;
  if (!this->_internal_view_image_bytes().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_view_image_bytes());
  }

  // bytes view_depth_bytes = 5;
  if (!this->_internal_view_depth_bytes().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_view_depth_bytes());
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_height());
  }

  // int32 width = 3;
  if (this->_internal_width() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_width());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GrabResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GrabResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GrabResponse::GetClassData() const { return &_class_data_; }

void GrabResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GrabResponse *>(to)->MergeFrom(
      static_cast<const GrabResponse &>(from));
}


void GrabResponse::MergeFrom(const GrabResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:zed.GrabResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_view_image_bytes().empty()) {
    _internal_set_view_image_bytes(from._internal_view_image_bytes());
  }
  if (!from._internal_view_depth_bytes().empty()) {
    _internal_set_view_depth_bytes(from._internal_view_depth_bytes());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_height() != 0) {
    _internal_set_height(from._internal_height());
  }
  if (from._internal_width() != 0) {
    _internal_set_width(from._internal_width());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GrabResponse::CopyFrom(const GrabResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:zed.GrabResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GrabResponse::IsInitialized() const {
  return true;
}

void GrabResponse::InternalSwap(GrabResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &view_image_bytes_, lhs_arena,
      &other->view_image_bytes_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &view_depth_bytes_, lhs_arena,
      &other->view_depth_bytes_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GrabResponse, width_)
      + sizeof(GrabResponse::width_)
      - PROTOBUF_FIELD_OFFSET(GrabResponse, timestamp_ms_)>(
          reinterpret_cast<char*>(&timestamp_ms_),
          reinterpret_cast<char*>(&other->timestamp_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GrabResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto_getter, &descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto_once,
      file_level_metadata_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace zed
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::zed::GrabRequest* Arena::CreateMaybeMessage< ::zed::GrabRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::zed::GrabRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::zed::GrabResponse* Arena::CreateMaybeMessage< ::zed::GrabResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::zed::GrabResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: lib/drivers/zedcam/proto/zed.proto
#ifndef GRPC_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto__INCLUDED
#define GRPC_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto__INCLUDED

#include "lib/drivers/zedcam/proto/zed.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace zed {

class ZED final {
 public:
  static constexpr char const* service_full_name() {
    return "zed.ZED";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Grab(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::zed::GrabResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::zed::GrabResponse>> AsyncGrab(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::zed::GrabResponse>>(AsyncGrabRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::zed::GrabResponse>> PrepareAsyncGrab(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::zed::GrabResponse>>(PrepareAsyncGrabRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Grab(::grpc::ClientContext* context, const ::zed::GrabRequest* request, ::zed::GrabResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Grab(::grpc::ClientContext* context, const ::zed::GrabRequest* request, ::zed::GrabResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::zed::GrabResponse>* AsyncGrabRaw(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::zed::GrabResponse>* PrepareAsyncGrabRaw(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Grab(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::zed::GrabResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::zed::GrabResponse>> AsyncGrab(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::zed::GrabResponse>>(AsyncGrabRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::zed::GrabResponse>> PrepareAsyncGrab(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::zed::GrabResponse>>(PrepareAsyncGrabRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Grab(::grpc::ClientContext* context, const ::zed::GrabRequest* request, ::zed::GrabResponse* response, std::function<void(::grpc::Status)>) override;
      void Grab(::grpc::ClientContext* context, const ::zed::GrabRequest* request, ::zed::GrabResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::zed::GrabResponse>* AsyncGrabRaw(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::zed::GrabResponse>* PrepareAsyncGrabRaw(::grpc::ClientContext* context, const ::zed::GrabRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Grab_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Grab(::grpc::ServerContext* context, const ::zed::GrabRequest* request, ::zed::GrabResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Grab() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::zed::GrabRequest* /*request*/, ::zed::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGrab(::grpc::ServerContext* context, ::zed::GrabRequest* request, ::grpc::ServerAsyncResponseWriter< ::zed::GrabResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Grab<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Grab() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::zed::GrabRequest, ::zed::GrabResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::zed::GrabRequest* request, ::zed::GrabResponse* response) { return this->Grab(context, request, response); }));}
    void SetMessageAllocatorFor_Grab(
        ::grpc::MessageAllocator< ::zed::GrabRequest, ::zed::GrabResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::zed::GrabRequest, ::zed::GrabResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::zed::GrabRequest* /*request*/, ::zed::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Grab(
      ::grpc::CallbackServerContext* /*context*/, const ::zed::GrabRequest* /*request*/, ::zed::GrabResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Grab<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Grab() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::zed::GrabRequest* /*request*/, ::zed::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Grab() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::zed::GrabRequest* /*request*/, ::zed::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGrab(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Grab() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Grab(context, request, response); }));
    }
    ~WithRawCallbackMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::zed::GrabRequest* /*request*/, ::zed::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Grab(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Grab : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Grab() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::zed::GrabRequest, ::zed::GrabResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::zed::GrabRequest, ::zed::GrabResponse>* streamer) {
                       return this->StreamedGrab(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Grab() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Grab(::grpc::ServerContext* /*context*/, const ::zed::GrabRequest* /*request*/, ::zed::GrabResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGrab(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::zed::GrabRequest,::zed::GrabResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Grab<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Grab<Service > StreamedService;
};

}  // namespace zed


#endif  // GRPC_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto__INCLUDED

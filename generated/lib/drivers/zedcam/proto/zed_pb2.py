# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: lib/drivers/zedcam/proto/zed.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='lib/drivers/zedcam/proto/zed.proto',
  package='zed',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\"lib/drivers/zedcam/proto/zed.proto\x12\x03zed\"#\n\x0bGrabRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\"w\n\x0cGrabResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x0e\n\x06height\x18\x02 \x01(\x05\x12\r\n\x05width\x18\x03 \x01(\x05\x12\x18\n\x10view_image_bytes\x18\x04 \x01(\x0c\x12\x18\n\x10view_depth_bytes\x18\x05 \x01(\x0c\x32\x34\n\x03ZED\x12-\n\x04Grab\x12\x10.zed.GrabRequest\x1a\x11.zed.GrabResponse\"\x00\x62\x06proto3'
)




_GRABREQUEST = _descriptor.Descriptor(
  name='GrabRequest',
  full_name='zed.GrabRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='zed.GrabRequest.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=43,
  serialized_end=78,
)


_GRABRESPONSE = _descriptor.Descriptor(
  name='GrabResponse',
  full_name='zed.GrabResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='zed.GrabResponse.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='zed.GrabResponse.height', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width', full_name='zed.GrabResponse.width', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='view_image_bytes', full_name='zed.GrabResponse.view_image_bytes', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='view_depth_bytes', full_name='zed.GrabResponse.view_depth_bytes', index=4,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=80,
  serialized_end=199,
)

DESCRIPTOR.message_types_by_name['GrabRequest'] = _GRABREQUEST
DESCRIPTOR.message_types_by_name['GrabResponse'] = _GRABRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GrabRequest = _reflection.GeneratedProtocolMessageType('GrabRequest', (_message.Message,), {
  'DESCRIPTOR' : _GRABREQUEST,
  '__module__' : 'lib.drivers.zedcam.proto.zed_pb2'
  # @@protoc_insertion_point(class_scope:zed.GrabRequest)
  })
_sym_db.RegisterMessage(GrabRequest)

GrabResponse = _reflection.GeneratedProtocolMessageType('GrabResponse', (_message.Message,), {
  'DESCRIPTOR' : _GRABRESPONSE,
  '__module__' : 'lib.drivers.zedcam.proto.zed_pb2'
  # @@protoc_insertion_point(class_scope:zed.GrabResponse)
  })
_sym_db.RegisterMessage(GrabResponse)



_ZED = _descriptor.ServiceDescriptor(
  name='ZED',
  full_name='zed.ZED',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=201,
  serialized_end=253,
  methods=[
  _descriptor.MethodDescriptor(
    name='Grab',
    full_name='zed.ZED.Grab',
    index=0,
    containing_service=None,
    input_type=_GRABREQUEST,
    output_type=_GRABRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_ZED)

DESCRIPTOR.services_by_name['ZED'] = _ZED

# @@protoc_insertion_point(module_scope)

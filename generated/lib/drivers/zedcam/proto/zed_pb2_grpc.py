# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.lib.drivers.zedcam.proto import zed_pb2 as lib_dot_drivers_dot_zedcam_dot_proto_dot_zed__pb2


class ZEDStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Grab = channel.unary_unary(
                '/zed.ZED/Grab',
                request_serializer=lib_dot_drivers_dot_zedcam_dot_proto_dot_zed__pb2.GrabRequest.SerializeToString,
                response_deserializer=lib_dot_drivers_dot_zedcam_dot_proto_dot_zed__pb2.GrabResponse.FromString,
                )


class ZEDServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Grab(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ZEDServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Grab': grpc.unary_unary_rpc_method_handler(
                    servicer.Grab,
                    request_deserializer=lib_dot_drivers_dot_zedcam_dot_proto_dot_zed__pb2.GrabRequest.FromString,
                    response_serializer=lib_dot_drivers_dot_zedcam_dot_proto_dot_zed__pb2.GrabResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'zed.ZED', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ZED(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Grab(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/zed.ZED/Grab',
            lib_dot_drivers_dot_zedcam_dot_proto_dot_zed__pb2.GrabRequest.SerializeToString,
            lib_dot_drivers_dot_zedcam_dot_proto_dot_zed__pb2.GrabResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lib/drivers/zedcam/proto/zed.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto;
namespace zed {
class GrabRequest;
struct GrabRequestDefaultTypeInternal;
extern GrabRequestDefaultTypeInternal _GrabRequest_default_instance_;
class GrabResponse;
struct GrabResponseDefaultTypeInternal;
extern GrabResponseDefaultTypeInternal _GrabResponse_default_instance_;
}  // namespace zed
PROTOBUF_NAMESPACE_OPEN
template<> ::zed::GrabRequest* Arena::CreateMaybeMessage<::zed::GrabRequest>(Arena*);
template<> ::zed::GrabResponse* Arena::CreateMaybeMessage<::zed::GrabResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace zed {

// ===================================================================

class GrabRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:zed.GrabRequest) */ {
 public:
  inline GrabRequest() : GrabRequest(nullptr) {}
  ~GrabRequest() override;
  explicit constexpr GrabRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GrabRequest(const GrabRequest& from);
  GrabRequest(GrabRequest&& from) noexcept
    : GrabRequest() {
    *this = ::std::move(from);
  }

  inline GrabRequest& operator=(const GrabRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GrabRequest& operator=(GrabRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GrabRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GrabRequest* internal_default_instance() {
    return reinterpret_cast<const GrabRequest*>(
               &_GrabRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GrabRequest& a, GrabRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GrabRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GrabRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GrabRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GrabRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GrabRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GrabRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GrabRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "zed.GrabRequest";
  }
  protected:
  explicit GrabRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimestampMsFieldNumber = 1,
  };
  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:zed.GrabRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int64_t timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto;
};
// -------------------------------------------------------------------

class GrabResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:zed.GrabResponse) */ {
 public:
  inline GrabResponse() : GrabResponse(nullptr) {}
  ~GrabResponse() override;
  explicit constexpr GrabResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GrabResponse(const GrabResponse& from);
  GrabResponse(GrabResponse&& from) noexcept
    : GrabResponse() {
    *this = ::std::move(from);
  }

  inline GrabResponse& operator=(const GrabResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GrabResponse& operator=(GrabResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GrabResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GrabResponse* internal_default_instance() {
    return reinterpret_cast<const GrabResponse*>(
               &_GrabResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GrabResponse& a, GrabResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GrabResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GrabResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GrabResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GrabResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GrabResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GrabResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GrabResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "zed.GrabResponse";
  }
  protected:
  explicit GrabResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kViewImageBytesFieldNumber = 4,
    kViewDepthBytesFieldNumber = 5,
    kTimestampMsFieldNumber = 1,
    kHeightFieldNumber = 2,
    kWidthFieldNumber = 3,
  };
  // bytes view_image_bytes = 4;
  void clear_view_image_bytes();
  const std::string& view_image_bytes() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_view_image_bytes(ArgT0&& arg0, ArgT... args);
  std::string* mutable_view_image_bytes();
  PROTOBUF_NODISCARD std::string* release_view_image_bytes();
  void set_allocated_view_image_bytes(std::string* view_image_bytes);
  private:
  const std::string& _internal_view_image_bytes() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_view_image_bytes(const std::string& value);
  std::string* _internal_mutable_view_image_bytes();
  public:

  // bytes view_depth_bytes = 5;
  void clear_view_depth_bytes();
  const std::string& view_depth_bytes() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_view_depth_bytes(ArgT0&& arg0, ArgT... args);
  std::string* mutable_view_depth_bytes();
  PROTOBUF_NODISCARD std::string* release_view_depth_bytes();
  void set_allocated_view_depth_bytes(std::string* view_depth_bytes);
  private:
  const std::string& _internal_view_depth_bytes() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_view_depth_bytes(const std::string& value);
  std::string* _internal_mutable_view_depth_bytes();
  public:

  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // int32 height = 2;
  void clear_height();
  int32_t height() const;
  void set_height(int32_t value);
  private:
  int32_t _internal_height() const;
  void _internal_set_height(int32_t value);
  public:

  // int32 width = 3;
  void clear_width();
  int32_t width() const;
  void set_width(int32_t value);
  private:
  int32_t _internal_width() const;
  void _internal_set_width(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:zed.GrabResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr view_image_bytes_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr view_depth_bytes_;
  int64_t timestamp_ms_;
  int32_t height_;
  int32_t width_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GrabRequest

// int64 timestamp_ms = 1;
inline void GrabRequest::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t GrabRequest::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t GrabRequest::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:zed.GrabRequest.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void GrabRequest::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void GrabRequest::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:zed.GrabRequest.timestamp_ms)
}

// -------------------------------------------------------------------

// GrabResponse

// int64 timestamp_ms = 1;
inline void GrabResponse::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t GrabResponse::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t GrabResponse::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:zed.GrabResponse.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void GrabResponse::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void GrabResponse::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:zed.GrabResponse.timestamp_ms)
}

// int32 height = 2;
inline void GrabResponse::clear_height() {
  height_ = 0;
}
inline int32_t GrabResponse::_internal_height() const {
  return height_;
}
inline int32_t GrabResponse::height() const {
  // @@protoc_insertion_point(field_get:zed.GrabResponse.height)
  return _internal_height();
}
inline void GrabResponse::_internal_set_height(int32_t value) {
  
  height_ = value;
}
inline void GrabResponse::set_height(int32_t value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:zed.GrabResponse.height)
}

// int32 width = 3;
inline void GrabResponse::clear_width() {
  width_ = 0;
}
inline int32_t GrabResponse::_internal_width() const {
  return width_;
}
inline int32_t GrabResponse::width() const {
  // @@protoc_insertion_point(field_get:zed.GrabResponse.width)
  return _internal_width();
}
inline void GrabResponse::_internal_set_width(int32_t value) {
  
  width_ = value;
}
inline void GrabResponse::set_width(int32_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:zed.GrabResponse.width)
}

// bytes view_image_bytes = 4;
inline void GrabResponse::clear_view_image_bytes() {
  view_image_bytes_.ClearToEmpty();
}
inline const std::string& GrabResponse::view_image_bytes() const {
  // @@protoc_insertion_point(field_get:zed.GrabResponse.view_image_bytes)
  return _internal_view_image_bytes();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GrabResponse::set_view_image_bytes(ArgT0&& arg0, ArgT... args) {
 
 view_image_bytes_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:zed.GrabResponse.view_image_bytes)
}
inline std::string* GrabResponse::mutable_view_image_bytes() {
  std::string* _s = _internal_mutable_view_image_bytes();
  // @@protoc_insertion_point(field_mutable:zed.GrabResponse.view_image_bytes)
  return _s;
}
inline const std::string& GrabResponse::_internal_view_image_bytes() const {
  return view_image_bytes_.Get();
}
inline void GrabResponse::_internal_set_view_image_bytes(const std::string& value) {
  
  view_image_bytes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GrabResponse::_internal_mutable_view_image_bytes() {
  
  return view_image_bytes_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GrabResponse::release_view_image_bytes() {
  // @@protoc_insertion_point(field_release:zed.GrabResponse.view_image_bytes)
  return view_image_bytes_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GrabResponse::set_allocated_view_image_bytes(std::string* view_image_bytes) {
  if (view_image_bytes != nullptr) {
    
  } else {
    
  }
  view_image_bytes_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), view_image_bytes,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (view_image_bytes_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    view_image_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:zed.GrabResponse.view_image_bytes)
}

// bytes view_depth_bytes = 5;
inline void GrabResponse::clear_view_depth_bytes() {
  view_depth_bytes_.ClearToEmpty();
}
inline const std::string& GrabResponse::view_depth_bytes() const {
  // @@protoc_insertion_point(field_get:zed.GrabResponse.view_depth_bytes)
  return _internal_view_depth_bytes();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GrabResponse::set_view_depth_bytes(ArgT0&& arg0, ArgT... args) {
 
 view_depth_bytes_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:zed.GrabResponse.view_depth_bytes)
}
inline std::string* GrabResponse::mutable_view_depth_bytes() {
  std::string* _s = _internal_mutable_view_depth_bytes();
  // @@protoc_insertion_point(field_mutable:zed.GrabResponse.view_depth_bytes)
  return _s;
}
inline const std::string& GrabResponse::_internal_view_depth_bytes() const {
  return view_depth_bytes_.Get();
}
inline void GrabResponse::_internal_set_view_depth_bytes(const std::string& value) {
  
  view_depth_bytes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GrabResponse::_internal_mutable_view_depth_bytes() {
  
  return view_depth_bytes_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GrabResponse::release_view_depth_bytes() {
  // @@protoc_insertion_point(field_release:zed.GrabResponse.view_depth_bytes)
  return view_depth_bytes_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GrabResponse::set_allocated_view_depth_bytes(std::string* view_depth_bytes) {
  if (view_depth_bytes != nullptr) {
    
  } else {
    
  }
  view_depth_bytes_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), view_depth_bytes,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (view_depth_bytes_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    view_depth_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:zed.GrabResponse.view_depth_bytes)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace zed

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_lib_2fdrivers_2fzedcam_2fproto_2fzed_2eproto

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

CarrierPhaseSolnValue = typing___NewType('CarrierPhaseSolnValue', builtin___int)
type___CarrierPhaseSolnValue = CarrierPhaseSolnValue
CarrierPhaseSoln: _CarrierPhaseSoln
class _CarrierPhaseSoln(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[CarrierPhaseSolnValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NONE = typing___cast(CarrierPhaseSolnValue, 0)
    FLOATING = typing___cast(CarrierPhaseSolnValue, 1)
    FIXED = typing___cast(CarrierPhaseSolnValue, 2)
NONE = typing___cast(CarrierPhaseSolnValue, 0)
FLOATING = typing___cast(CarrierPhaseSolnValue, 1)
FIXED = typing___cast(CarrierPhaseSolnValue, 2)

FixTypeValue = typing___NewType('FixTypeValue', builtin___int)
type___FixTypeValue = FixTypeValue
FixType: _FixType
class _FixType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[FixTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NO_FIX = typing___cast(FixTypeValue, 0)
    DEAD_RECKONING_ONLY = typing___cast(FixTypeValue, 1)
    FIX_2D = typing___cast(FixTypeValue, 2)
    FIX_3D = typing___cast(FixTypeValue, 3)
    GNSS_DR = typing___cast(FixTypeValue, 4)
    TIME_ONLY = typing___cast(FixTypeValue, 5)
NO_FIX = typing___cast(FixTypeValue, 0)
DEAD_RECKONING_ONLY = typing___cast(FixTypeValue, 1)
FIX_2D = typing___cast(FixTypeValue, 2)
FIX_3D = typing___cast(FixTypeValue, 3)
GNSS_DR = typing___cast(FixTypeValue, 4)
TIME_ONLY = typing___cast(FixTypeValue, 5)

NetworkLinkSpeedValue = typing___NewType('NetworkLinkSpeedValue', builtin___int)
type___NetworkLinkSpeedValue = NetworkLinkSpeedValue
NetworkLinkSpeed: _NetworkLinkSpeed
class _NetworkLinkSpeed(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[NetworkLinkSpeedValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    UNKNOWN = typing___cast(NetworkLinkSpeedValue, 0)
    SPEED_10M_HALF = typing___cast(NetworkLinkSpeedValue, 1)
    SPEED_10M_FULL = typing___cast(NetworkLinkSpeedValue, 2)
    SPEED_100M_HALF = typing___cast(NetworkLinkSpeedValue, 3)
    SPEED_100M_FULL = typing___cast(NetworkLinkSpeedValue, 4)
    SPEED_1G_FULL = typing___cast(NetworkLinkSpeedValue, 5)
    SPEED_2G5_FULL = typing___cast(NetworkLinkSpeedValue, 6)
    SPEED_5G_FULL = typing___cast(NetworkLinkSpeedValue, 7)
    SPEED_10G_FULL = typing___cast(NetworkLinkSpeedValue, 8)
UNKNOWN = typing___cast(NetworkLinkSpeedValue, 0)
SPEED_10M_HALF = typing___cast(NetworkLinkSpeedValue, 1)
SPEED_10M_FULL = typing___cast(NetworkLinkSpeedValue, 2)
SPEED_100M_HALF = typing___cast(NetworkLinkSpeedValue, 3)
SPEED_100M_FULL = typing___cast(NetworkLinkSpeedValue, 4)
SPEED_1G_FULL = typing___cast(NetworkLinkSpeedValue, 5)
SPEED_2G5_FULL = typing___cast(NetworkLinkSpeedValue, 6)
SPEED_5G_FULL = typing___cast(NetworkLinkSpeedValue, 7)
SPEED_10G_FULL = typing___cast(NetworkLinkSpeedValue, 8)

class PingRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PingRequest = PingRequest

class PingResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PingResponse = PingResponse

class GetRotaryTicksRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetRotaryTicksRequest = GetRotaryTicksRequest

class GetRotaryTicksResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    front_left_ticks: builtin___int = ...
    front_right_ticks: builtin___int = ...
    back_left_ticks: builtin___int = ...
    back_right_ticks: builtin___int = ...
    front_left_enabled: builtin___bool = ...
    front_right_enabled: builtin___bool = ...
    back_left_enabled: builtin___bool = ...
    back_right_enabled: builtin___bool = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        front_left_ticks : typing___Optional[builtin___int] = None,
        front_right_ticks : typing___Optional[builtin___int] = None,
        back_left_ticks : typing___Optional[builtin___int] = None,
        back_right_ticks : typing___Optional[builtin___int] = None,
        front_left_enabled : typing___Optional[builtin___bool] = None,
        front_right_enabled : typing___Optional[builtin___bool] = None,
        back_left_enabled : typing___Optional[builtin___bool] = None,
        back_right_enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"back_left_enabled",b"back_left_enabled",u"back_left_ticks",b"back_left_ticks",u"back_right_enabled",b"back_right_enabled",u"back_right_ticks",b"back_right_ticks",u"front_left_enabled",b"front_left_enabled",u"front_left_ticks",b"front_left_ticks",u"front_right_enabled",b"front_right_enabled",u"front_right_ticks",b"front_right_ticks",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetRotaryTicksResponse = GetRotaryTicksResponse

class GetNextDistanceRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextDistanceRequest = GetNextDistanceRequest

class GetNextDistanceResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    distance: builtin___float = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        distance : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"distance",b"distance",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextDistanceResponse = GetNextDistanceResponse

class GetNextVelocityRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextVelocityRequest = GetNextVelocityRequest

class GetNextVelocityResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    mm_per_ms: builtin___float = ...
    lifted: builtin___bool = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        mm_per_ms : typing___Optional[builtin___float] = None,
        lifted : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lifted",b"lifted",u"mm_per_ms",b"mm_per_ms",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextVelocityResponse = GetNextVelocityResponse

class SetJimboxSpeedRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    target_speed: builtin___float = ...
    actual_ground_speed: builtin___float = ...

    def __init__(self,
        *,
        target_speed : typing___Optional[builtin___float] = None,
        actual_ground_speed : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"actual_ground_speed",b"actual_ground_speed",u"target_speed",b"target_speed"]) -> None: ...
type___SetJimboxSpeedRequest = SetJimboxSpeedRequest

class SetJimboxSpeedResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    speed_setpoint: builtin___float = ...

    def __init__(self,
        *,
        speed_setpoint : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"speed_setpoint",b"speed_setpoint"]) -> None: ...
type___SetJimboxSpeedResponse = SetJimboxSpeedResponse

class SetCruiseEnabledRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___SetCruiseEnabledRequest = SetCruiseEnabledRequest

class SetCruiseEnabledResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetCruiseEnabledResponse = SetCruiseEnabledResponse

class GetCruiseStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetCruiseStatusRequest = GetCruiseStatusRequest

class GetCruiseStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...
    installed: builtin___bool = ...
    speed: builtin___float = ...
    allow_enable: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        installed : typing___Optional[builtin___bool] = None,
        speed : typing___Optional[builtin___float] = None,
        allow_enable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"allow_enable",b"allow_enable",u"enabled",b"enabled",u"installed",b"installed",u"speed",b"speed"]) -> None: ...
type___GetCruiseStatusResponse = GetCruiseStatusResponse

class SetImplementStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    active: builtin___bool = ...
    error: builtin___bool = ...
    error_message: typing___Text = ...

    def __init__(self,
        *,
        active : typing___Optional[builtin___bool] = None,
        error : typing___Optional[builtin___bool] = None,
        error_message : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_error_message",b"_error_message",u"error_message",b"error_message"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_error_message",b"_error_message",u"active",b"active",u"error",b"error",u"error_message",b"error_message"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_error_message",b"_error_message"]) -> typing_extensions___Literal["error_message"]: ...
type___SetImplementStateRequest = SetImplementStateRequest

class SetImplementStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetImplementStateResponse = SetImplementStateResponse

class SetSafeStateEnforcementRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enforced: builtin___bool = ...

    def __init__(self,
        *,
        enforced : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enforced",b"enforced"]) -> None: ...
type___SetSafeStateEnforcementRequest = SetSafeStateEnforcementRequest

class SetSafeStateEnforcementResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetSafeStateEnforcementResponse = SetSafeStateEnforcementResponse

class GetTractorSafetyStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetTractorSafetyStateRequest = GetTractorSafetyStateRequest

class GetTractorSafetyStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    is_safe: builtin___bool = ...
    enforced: builtin___bool = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        is_safe : typing___Optional[builtin___bool] = None,
        enforced : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enforced",b"enforced",u"is_safe",b"is_safe",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetTractorSafetyStateResponse = GetTractorSafetyStateResponse

class GetTractorIFStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetTractorIFStateRequest = GetTractorIFStateRequest

class GetTractorIFStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    expected: builtin___bool = ...
    connected: builtin___bool = ...

    def __init__(self,
        *,
        expected : typing___Optional[builtin___bool] = None,
        connected : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"connected",b"connected",u"expected",b"expected"]) -> None: ...
type___GetTractorIFStateResponse = GetTractorIFStateResponse

class GetSafetyStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetSafetyStatusRequest = GetSafetyStatusRequest

class GetSafetyStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lifted: builtin___bool = ...
    estopped: builtin___bool = ...
    in_cab_estopped: builtin___bool = ...
    left_estopped: builtin___bool = ...
    right_estopped: builtin___bool = ...
    laser_key: builtin___bool = ...
    interlock: builtin___bool = ...
    water_protect: builtin___bool = ...
    reset_required: builtin___bool = ...
    center_estop: builtin___bool = ...
    power_button_estop: builtin___bool = ...
    left_lpsu_interlock: builtin___bool = ...
    right_lpsu_interlock: builtin___bool = ...
    debug_mode: builtin___bool = ...

    def __init__(self,
        *,
        lifted : typing___Optional[builtin___bool] = None,
        estopped : typing___Optional[builtin___bool] = None,
        in_cab_estopped : typing___Optional[builtin___bool] = None,
        left_estopped : typing___Optional[builtin___bool] = None,
        right_estopped : typing___Optional[builtin___bool] = None,
        laser_key : typing___Optional[builtin___bool] = None,
        interlock : typing___Optional[builtin___bool] = None,
        water_protect : typing___Optional[builtin___bool] = None,
        reset_required : typing___Optional[builtin___bool] = None,
        center_estop : typing___Optional[builtin___bool] = None,
        power_button_estop : typing___Optional[builtin___bool] = None,
        left_lpsu_interlock : typing___Optional[builtin___bool] = None,
        right_lpsu_interlock : typing___Optional[builtin___bool] = None,
        debug_mode : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_estop",b"center_estop",u"debug_mode",b"debug_mode",u"estopped",b"estopped",u"in_cab_estopped",b"in_cab_estopped",u"interlock",b"interlock",u"laser_key",b"laser_key",u"left_estopped",b"left_estopped",u"left_lpsu_interlock",b"left_lpsu_interlock",u"lifted",b"lifted",u"power_button_estop",b"power_button_estop",u"reset_required",b"reset_required",u"right_estopped",b"right_estopped",u"right_lpsu_interlock",b"right_lpsu_interlock",u"water_protect",b"water_protect"]) -> None: ...
type___GetSafetyStatusResponse = GetSafetyStatusResponse

class GeoLLA(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lat: builtin___float = ...
    lng: builtin___float = ...
    alt: builtin___float = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        lat : typing___Optional[builtin___float] = None,
        lng : typing___Optional[builtin___float] = None,
        alt : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"alt",b"alt",u"lat",b"lat",u"lng",b"lng",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GeoLLA = GeoLLA

class GeoECEF(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___float = ...
    y: builtin___float = ...
    z: builtin___float = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        z : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms",u"x",b"x",u"y",b"y",u"z",b"z"]) -> None: ...
type___GeoECEF = GeoECEF

class GetGPSDataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    validate: builtin___bool = ...

    def __init__(self,
        *,
        validate : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"validate",b"validate"]) -> None: ...
type___GetGPSDataRequest = GetGPSDataRequest

class GetGPSDataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def lla(self) -> type___GeoLLA: ...

    @property
    def ecef(self) -> type___GeoECEF: ...

    def __init__(self,
        *,
        lla : typing___Optional[type___GeoLLA] = None,
        ecef : typing___Optional[type___GeoECEF] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ecef",b"ecef",u"lla",b"lla"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ecef",b"ecef",u"lla",b"lla"]) -> None: ...
type___GetGPSDataResponse = GetGPSDataResponse

class GetNextGPSDataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextGPSDataRequest = GetNextGPSDataRequest

class GetNextGPSDataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def lla(self) -> type___GeoLLA: ...

    def __init__(self,
        *,
        lla : typing___Optional[type___GeoLLA] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"lla",b"lla"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lla",b"lla"]) -> None: ...
type___GetNextGPSDataResponse = GetNextGPSDataResponse

class GetNextRawGPSDataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetNextRawGPSDataRequest = GetNextRawGPSDataRequest

class ValueWithAccuracy(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    value: builtin___float = ...
    accuracy: builtin___float = ...

    def __init__(self,
        *,
        value : typing___Optional[builtin___float] = None,
        accuracy : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"accuracy",b"accuracy",u"value",b"value"]) -> None: ...
type___ValueWithAccuracy = ValueWithAccuracy

class DualGpsData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    gnss_valid: builtin___bool = ...
    diff_corrections: builtin___bool = ...
    is_moving_base: builtin___bool = ...
    carrier_phase: type___CarrierPhaseSolnValue = ...
    timestamp_ms: builtin___int = ...

    @property
    def north(self) -> type___ValueWithAccuracy: ...

    @property
    def east(self) -> type___ValueWithAccuracy: ...

    @property
    def down(self) -> type___ValueWithAccuracy: ...

    @property
    def length(self) -> type___ValueWithAccuracy: ...

    @property
    def heading(self) -> type___ValueWithAccuracy: ...

    def __init__(self,
        *,
        gnss_valid : typing___Optional[builtin___bool] = None,
        diff_corrections : typing___Optional[builtin___bool] = None,
        is_moving_base : typing___Optional[builtin___bool] = None,
        carrier_phase : typing___Optional[type___CarrierPhaseSolnValue] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        north : typing___Optional[type___ValueWithAccuracy] = None,
        east : typing___Optional[type___ValueWithAccuracy] = None,
        down : typing___Optional[type___ValueWithAccuracy] = None,
        length : typing___Optional[type___ValueWithAccuracy] = None,
        heading : typing___Optional[type___ValueWithAccuracy] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_down",b"_down",u"_east",b"_east",u"_heading",b"_heading",u"_length",b"_length",u"_north",b"_north",u"down",b"down",u"east",b"east",u"heading",b"heading",u"length",b"length",u"north",b"north"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_down",b"_down",u"_east",b"_east",u"_heading",b"_heading",u"_length",b"_length",u"_north",b"_north",u"carrier_phase",b"carrier_phase",u"diff_corrections",b"diff_corrections",u"down",b"down",u"east",b"east",u"gnss_valid",b"gnss_valid",u"heading",b"heading",u"is_moving_base",b"is_moving_base",u"length",b"length",u"north",b"north",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_down",b"_down"]) -> typing_extensions___Literal["down"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_east",b"_east"]) -> typing_extensions___Literal["east"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_heading",b"_heading"]) -> typing_extensions___Literal["heading"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_length",b"_length"]) -> typing_extensions___Literal["length"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_north",b"_north"]) -> typing_extensions___Literal["north"]: ...
type___DualGpsData = DualGpsData

class GetNextRawGPSDataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    have_fix: builtin___bool = ...
    latitude: builtin___float = ...
    longitude: builtin___float = ...
    num_sats: builtin___int = ...
    hdop: builtin___float = ...
    timestamp_ms: builtin___int = ...
    height_mm: builtin___int = ...
    have_approx_fix: builtin___bool = ...
    fix_type: type___FixTypeValue = ...
    gnss_valid: builtin___bool = ...
    diff_corrections: builtin___bool = ...
    carrier_phase: type___CarrierPhaseSolnValue = ...

    @property
    def dual(self) -> type___DualGpsData: ...

    def __init__(self,
        *,
        have_fix : typing___Optional[builtin___bool] = None,
        latitude : typing___Optional[builtin___float] = None,
        longitude : typing___Optional[builtin___float] = None,
        num_sats : typing___Optional[builtin___int] = None,
        hdop : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        height_mm : typing___Optional[builtin___int] = None,
        have_approx_fix : typing___Optional[builtin___bool] = None,
        fix_type : typing___Optional[type___FixTypeValue] = None,
        gnss_valid : typing___Optional[builtin___bool] = None,
        diff_corrections : typing___Optional[builtin___bool] = None,
        carrier_phase : typing___Optional[type___CarrierPhaseSolnValue] = None,
        dual : typing___Optional[type___DualGpsData] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_dual",b"_dual",u"dual",b"dual"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_dual",b"_dual",u"carrier_phase",b"carrier_phase",u"diff_corrections",b"diff_corrections",u"dual",b"dual",u"fix_type",b"fix_type",u"gnss_valid",b"gnss_valid",u"have_approx_fix",b"have_approx_fix",u"have_fix",b"have_fix",u"hdop",b"hdop",u"height_mm",b"height_mm",u"latitude",b"latitude",u"longitude",b"longitude",u"num_sats",b"num_sats",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_dual",b"_dual"]) -> typing_extensions___Literal["dual"]: ...
type___GetNextRawGPSDataResponse = GetNextRawGPSDataResponse

class GetGPSFixedPosRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetGPSFixedPosRequest = GetGPSFixedPosRequest

class GetGPSFixedPosResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x_mm: builtin___float = ...
    y_mm: builtin___float = ...

    def __init__(self,
        *,
        x_mm : typing___Optional[builtin___float] = None,
        y_mm : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x_mm",b"x_mm",u"y_mm",b"y_mm"]) -> None: ...
type___GetGPSFixedPosResponse = GetGPSFixedPosResponse

class GetManagedBoardErrorsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetManagedBoardErrorsRequest = GetManagedBoardErrorsRequest

class GetManagedBoardErrorsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    board: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    encoder_error_flag: builtin___bool = ...
    encoder_error_msg: typing___Text = ...
    gps_has_fix: builtin___bool = ...

    def __init__(self,
        *,
        board : typing___Optional[typing___Iterable[typing___Text]] = None,
        encoder_error_flag : typing___Optional[builtin___bool] = None,
        encoder_error_msg : typing___Optional[typing___Text] = None,
        gps_has_fix : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"board",b"board",u"encoder_error_flag",b"encoder_error_flag",u"encoder_error_msg",b"encoder_error_msg",u"gps_has_fix",b"gps_has_fix"]) -> None: ...
type___GetManagedBoardErrorsResponse = GetManagedBoardErrorsResponse

class GetSupervisoryStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetSupervisoryStatusRequest = GetSupervisoryStatusRequest

class GetReaperSupervisoryStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetReaperSupervisoryStatusRequest = GetReaperSupervisoryStatusRequest

class ChillerAlarms(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    low_level_in_tank: builtin___bool = ...
    high_circulating_fluid_discharge_temp: builtin___bool = ...
    circulating_fluid_discharge_temp_rise: builtin___bool = ...
    circulating_fluid_discharge_temp_drop: builtin___bool = ...
    high_circulating_fluid_return_temp: builtin___bool = ...
    circulating_fluid_discharge_pressure_rise: builtin___bool = ...
    circulating_fluid_discharge_pressure_drop: builtin___bool = ...
    high_compressor_suction_temp: builtin___bool = ...
    low_compressor_suction_temp: builtin___bool = ...
    low_super_heat_temp: builtin___bool = ...
    high_compressor_discharge_pressure: builtin___bool = ...
    refrigerant_circut_pressure_high_drop: builtin___bool = ...
    refrigerant_circut_pressure_low_rise: builtin___bool = ...
    refrigerant_circut_pressure_low_drop: builtin___bool = ...
    compressor_running_failure: builtin___bool = ...
    communication_error: builtin___bool = ...
    memory_error: builtin___bool = ...
    dc_line_fuse_cut: builtin___bool = ...
    circulating_fluid_discharge_temp_sensor_failure: builtin___bool = ...
    circulating_fluid_return_temp_sensor_failure: builtin___bool = ...
    circulating_fluid_suction_temp_sensor_failure: builtin___bool = ...
    circulating_fluid_discharge_pressure_sensor_failure: builtin___bool = ...
    compressor_discharge_pressure_sensor_failure: builtin___bool = ...
    compressor_suction_pressure_sensor_failure: builtin___bool = ...
    pump_maintenance: builtin___bool = ...
    fan_maintenance: builtin___bool = ...
    compressor_maintenance: builtin___bool = ...
    contact_input_1_signal_detection: builtin___bool = ...
    contact_input_2_signal_detection: builtin___bool = ...
    compressor_discharge_temp_sensor_failure: builtin___bool = ...
    compressor_discharge_temp_rise: builtin___bool = ...
    dustproof_filter_maintenance: builtin___bool = ...
    power_stoppage: builtin___bool = ...
    compressor_waiting: builtin___bool = ...
    fan_failure: builtin___bool = ...
    compressor_over_current: builtin___bool = ...
    pump_over_current: builtin___bool = ...
    air_exhaust_fan_stoppage: builtin___bool = ...
    incorrect_phase_error: builtin___bool = ...
    phase_board_over_current: builtin___bool = ...

    def __init__(self,
        *,
        low_level_in_tank : typing___Optional[builtin___bool] = None,
        high_circulating_fluid_discharge_temp : typing___Optional[builtin___bool] = None,
        circulating_fluid_discharge_temp_rise : typing___Optional[builtin___bool] = None,
        circulating_fluid_discharge_temp_drop : typing___Optional[builtin___bool] = None,
        high_circulating_fluid_return_temp : typing___Optional[builtin___bool] = None,
        circulating_fluid_discharge_pressure_rise : typing___Optional[builtin___bool] = None,
        circulating_fluid_discharge_pressure_drop : typing___Optional[builtin___bool] = None,
        high_compressor_suction_temp : typing___Optional[builtin___bool] = None,
        low_compressor_suction_temp : typing___Optional[builtin___bool] = None,
        low_super_heat_temp : typing___Optional[builtin___bool] = None,
        high_compressor_discharge_pressure : typing___Optional[builtin___bool] = None,
        refrigerant_circut_pressure_high_drop : typing___Optional[builtin___bool] = None,
        refrigerant_circut_pressure_low_rise : typing___Optional[builtin___bool] = None,
        refrigerant_circut_pressure_low_drop : typing___Optional[builtin___bool] = None,
        compressor_running_failure : typing___Optional[builtin___bool] = None,
        communication_error : typing___Optional[builtin___bool] = None,
        memory_error : typing___Optional[builtin___bool] = None,
        dc_line_fuse_cut : typing___Optional[builtin___bool] = None,
        circulating_fluid_discharge_temp_sensor_failure : typing___Optional[builtin___bool] = None,
        circulating_fluid_return_temp_sensor_failure : typing___Optional[builtin___bool] = None,
        circulating_fluid_suction_temp_sensor_failure : typing___Optional[builtin___bool] = None,
        circulating_fluid_discharge_pressure_sensor_failure : typing___Optional[builtin___bool] = None,
        compressor_discharge_pressure_sensor_failure : typing___Optional[builtin___bool] = None,
        compressor_suction_pressure_sensor_failure : typing___Optional[builtin___bool] = None,
        pump_maintenance : typing___Optional[builtin___bool] = None,
        fan_maintenance : typing___Optional[builtin___bool] = None,
        compressor_maintenance : typing___Optional[builtin___bool] = None,
        contact_input_1_signal_detection : typing___Optional[builtin___bool] = None,
        contact_input_2_signal_detection : typing___Optional[builtin___bool] = None,
        compressor_discharge_temp_sensor_failure : typing___Optional[builtin___bool] = None,
        compressor_discharge_temp_rise : typing___Optional[builtin___bool] = None,
        dustproof_filter_maintenance : typing___Optional[builtin___bool] = None,
        power_stoppage : typing___Optional[builtin___bool] = None,
        compressor_waiting : typing___Optional[builtin___bool] = None,
        fan_failure : typing___Optional[builtin___bool] = None,
        compressor_over_current : typing___Optional[builtin___bool] = None,
        pump_over_current : typing___Optional[builtin___bool] = None,
        air_exhaust_fan_stoppage : typing___Optional[builtin___bool] = None,
        incorrect_phase_error : typing___Optional[builtin___bool] = None,
        phase_board_over_current : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"air_exhaust_fan_stoppage",b"air_exhaust_fan_stoppage",u"circulating_fluid_discharge_pressure_drop",b"circulating_fluid_discharge_pressure_drop",u"circulating_fluid_discharge_pressure_rise",b"circulating_fluid_discharge_pressure_rise",u"circulating_fluid_discharge_pressure_sensor_failure",b"circulating_fluid_discharge_pressure_sensor_failure",u"circulating_fluid_discharge_temp_drop",b"circulating_fluid_discharge_temp_drop",u"circulating_fluid_discharge_temp_rise",b"circulating_fluid_discharge_temp_rise",u"circulating_fluid_discharge_temp_sensor_failure",b"circulating_fluid_discharge_temp_sensor_failure",u"circulating_fluid_return_temp_sensor_failure",b"circulating_fluid_return_temp_sensor_failure",u"circulating_fluid_suction_temp_sensor_failure",b"circulating_fluid_suction_temp_sensor_failure",u"communication_error",b"communication_error",u"compressor_discharge_pressure_sensor_failure",b"compressor_discharge_pressure_sensor_failure",u"compressor_discharge_temp_rise",b"compressor_discharge_temp_rise",u"compressor_discharge_temp_sensor_failure",b"compressor_discharge_temp_sensor_failure",u"compressor_maintenance",b"compressor_maintenance",u"compressor_over_current",b"compressor_over_current",u"compressor_running_failure",b"compressor_running_failure",u"compressor_suction_pressure_sensor_failure",b"compressor_suction_pressure_sensor_failure",u"compressor_waiting",b"compressor_waiting",u"contact_input_1_signal_detection",b"contact_input_1_signal_detection",u"contact_input_2_signal_detection",b"contact_input_2_signal_detection",u"dc_line_fuse_cut",b"dc_line_fuse_cut",u"dustproof_filter_maintenance",b"dustproof_filter_maintenance",u"fan_failure",b"fan_failure",u"fan_maintenance",b"fan_maintenance",u"high_circulating_fluid_discharge_temp",b"high_circulating_fluid_discharge_temp",u"high_circulating_fluid_return_temp",b"high_circulating_fluid_return_temp",u"high_compressor_discharge_pressure",b"high_compressor_discharge_pressure",u"high_compressor_suction_temp",b"high_compressor_suction_temp",u"incorrect_phase_error",b"incorrect_phase_error",u"low_compressor_suction_temp",b"low_compressor_suction_temp",u"low_level_in_tank",b"low_level_in_tank",u"low_super_heat_temp",b"low_super_heat_temp",u"memory_error",b"memory_error",u"phase_board_over_current",b"phase_board_over_current",u"power_stoppage",b"power_stoppage",u"pump_maintenance",b"pump_maintenance",u"pump_over_current",b"pump_over_current",u"refrigerant_circut_pressure_high_drop",b"refrigerant_circut_pressure_high_drop",u"refrigerant_circut_pressure_low_drop",b"refrigerant_circut_pressure_low_drop",u"refrigerant_circut_pressure_low_rise",b"refrigerant_circut_pressure_low_rise"]) -> None: ...
type___ChillerAlarms = ChillerAlarms

class GetSupervisoryStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    water_protect_status: builtin___bool = ...
    main_contactor_status_fb: builtin___bool = ...
    power_good: builtin___bool = ...
    power_bad: builtin___bool = ...
    power_very_bad: builtin___bool = ...
    lifted_status: builtin___bool = ...
    temp_humidity_status: builtin___bool = ...
    tractor_power: builtin___bool = ...
    ac_frequency: builtin___float = ...
    ac_voltage_a_b: builtin___float = ...
    ac_voltage_b_c: builtin___float = ...
    ac_voltage_a_c: builtin___float = ...
    ac_voltage_a: builtin___float = ...
    ac_voltage_b: builtin___float = ...
    ac_voltage_c: builtin___float = ...
    phase_power_w_3: builtin___int = ...
    phase_power_va_3: builtin___int = ...
    power_factor: builtin___float = ...
    server_cabinet_temp: builtin___float = ...
    server_cabinet_humidity: builtin___float = ...
    battery_voltage_12v: builtin___float = ...
    temp_humidity_bypass_status: builtin___bool = ...
    temp_bypass_status: builtin___bool = ...
    humidity_bypass_status: builtin___bool = ...
    temp_status: builtin___bool = ...
    humidity_status: builtin___bool = ...
    btl_disabled: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    server_disabled: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    scanners_disabled: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    wheel_encoder_disabled: builtin___bool = ...
    strobe_disabled: builtin___bool = ...
    gps_disabled: builtin___bool = ...
    main_contactor_disabled: builtin___bool = ...
    air_conditioner_disabled: builtin___bool = ...
    chiller_disabled: builtin___bool = ...
    chiller_temp: builtin___float = ...
    chiller_flow: builtin___float = ...
    chiller_pressure: builtin___float = ...
    chiller_conductivity: builtin___float = ...
    chiller_set_temp: builtin___float = ...

    @property
    def chiller_alarms(self) -> type___ChillerAlarms: ...

    def __init__(self,
        *,
        water_protect_status : typing___Optional[builtin___bool] = None,
        main_contactor_status_fb : typing___Optional[builtin___bool] = None,
        power_good : typing___Optional[builtin___bool] = None,
        power_bad : typing___Optional[builtin___bool] = None,
        power_very_bad : typing___Optional[builtin___bool] = None,
        lifted_status : typing___Optional[builtin___bool] = None,
        temp_humidity_status : typing___Optional[builtin___bool] = None,
        tractor_power : typing___Optional[builtin___bool] = None,
        ac_frequency : typing___Optional[builtin___float] = None,
        ac_voltage_a_b : typing___Optional[builtin___float] = None,
        ac_voltage_b_c : typing___Optional[builtin___float] = None,
        ac_voltage_a_c : typing___Optional[builtin___float] = None,
        ac_voltage_a : typing___Optional[builtin___float] = None,
        ac_voltage_b : typing___Optional[builtin___float] = None,
        ac_voltage_c : typing___Optional[builtin___float] = None,
        phase_power_w_3 : typing___Optional[builtin___int] = None,
        phase_power_va_3 : typing___Optional[builtin___int] = None,
        power_factor : typing___Optional[builtin___float] = None,
        server_cabinet_temp : typing___Optional[builtin___float] = None,
        server_cabinet_humidity : typing___Optional[builtin___float] = None,
        battery_voltage_12v : typing___Optional[builtin___float] = None,
        temp_humidity_bypass_status : typing___Optional[builtin___bool] = None,
        temp_bypass_status : typing___Optional[builtin___bool] = None,
        humidity_bypass_status : typing___Optional[builtin___bool] = None,
        temp_status : typing___Optional[builtin___bool] = None,
        humidity_status : typing___Optional[builtin___bool] = None,
        btl_disabled : typing___Optional[typing___Iterable[builtin___bool]] = None,
        server_disabled : typing___Optional[typing___Iterable[builtin___bool]] = None,
        scanners_disabled : typing___Optional[typing___Iterable[builtin___bool]] = None,
        wheel_encoder_disabled : typing___Optional[builtin___bool] = None,
        strobe_disabled : typing___Optional[builtin___bool] = None,
        gps_disabled : typing___Optional[builtin___bool] = None,
        main_contactor_disabled : typing___Optional[builtin___bool] = None,
        air_conditioner_disabled : typing___Optional[builtin___bool] = None,
        chiller_disabled : typing___Optional[builtin___bool] = None,
        chiller_temp : typing___Optional[builtin___float] = None,
        chiller_flow : typing___Optional[builtin___float] = None,
        chiller_pressure : typing___Optional[builtin___float] = None,
        chiller_conductivity : typing___Optional[builtin___float] = None,
        chiller_set_temp : typing___Optional[builtin___float] = None,
        chiller_alarms : typing___Optional[type___ChillerAlarms] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"chiller_alarms",b"chiller_alarms"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ac_frequency",b"ac_frequency",u"ac_voltage_a",b"ac_voltage_a",u"ac_voltage_a_b",b"ac_voltage_a_b",u"ac_voltage_a_c",b"ac_voltage_a_c",u"ac_voltage_b",b"ac_voltage_b",u"ac_voltage_b_c",b"ac_voltage_b_c",u"ac_voltage_c",b"ac_voltage_c",u"air_conditioner_disabled",b"air_conditioner_disabled",u"battery_voltage_12v",b"battery_voltage_12v",u"btl_disabled",b"btl_disabled",u"chiller_alarms",b"chiller_alarms",u"chiller_conductivity",b"chiller_conductivity",u"chiller_disabled",b"chiller_disabled",u"chiller_flow",b"chiller_flow",u"chiller_pressure",b"chiller_pressure",u"chiller_set_temp",b"chiller_set_temp",u"chiller_temp",b"chiller_temp",u"gps_disabled",b"gps_disabled",u"humidity_bypass_status",b"humidity_bypass_status",u"humidity_status",b"humidity_status",u"lifted_status",b"lifted_status",u"main_contactor_disabled",b"main_contactor_disabled",u"main_contactor_status_fb",b"main_contactor_status_fb",u"phase_power_va_3",b"phase_power_va_3",u"phase_power_w_3",b"phase_power_w_3",u"power_bad",b"power_bad",u"power_factor",b"power_factor",u"power_good",b"power_good",u"power_very_bad",b"power_very_bad",u"scanners_disabled",b"scanners_disabled",u"server_cabinet_humidity",b"server_cabinet_humidity",u"server_cabinet_temp",b"server_cabinet_temp",u"server_disabled",b"server_disabled",u"strobe_disabled",b"strobe_disabled",u"temp_bypass_status",b"temp_bypass_status",u"temp_humidity_bypass_status",b"temp_humidity_bypass_status",u"temp_humidity_status",b"temp_humidity_status",u"temp_status",b"temp_status",u"tractor_power",b"tractor_power",u"water_protect_status",b"water_protect_status",u"wheel_encoder_disabled",b"wheel_encoder_disabled"]) -> None: ...
type___GetSupervisoryStatusResponse = GetSupervisoryStatusResponse

class SetServerDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable",u"row_id",b"row_id"]) -> None: ...
type___SetServerDisableRequest = SetServerDisableRequest

class SetServerDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetServerDisableResponse = SetServerDisableResponse

class SetBTLDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable",u"row_id",b"row_id"]) -> None: ...
type___SetBTLDisableRequest = SetBTLDisableRequest

class SetBTLDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetBTLDisableResponse = SetBTLDisableResponse

class SetScannersDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable",u"row_id",b"row_id"]) -> None: ...
type___SetScannersDisableRequest = SetScannersDisableRequest

class SetScannersDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetScannersDisableResponse = SetScannersDisableResponse

class SetWheelEncoderBoardDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable"]) -> None: ...
type___SetWheelEncoderBoardDisableRequest = SetWheelEncoderBoardDisableRequest

class SetWheelEncoderBoardDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetWheelEncoderBoardDisableResponse = SetWheelEncoderBoardDisableResponse

class SetWheelEncoderDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disable: builtin___bool = ...
    front: builtin___bool = ...
    left: builtin___bool = ...

    def __init__(self,
        *,
        disable : typing___Optional[builtin___bool] = None,
        front : typing___Optional[builtin___bool] = None,
        left : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable",u"front",b"front",u"left",b"left"]) -> None: ...
type___SetWheelEncoderDisableRequest = SetWheelEncoderDisableRequest

class SetWheelEncoderDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetWheelEncoderDisableResponse = SetWheelEncoderDisableResponse

class SetStrobeDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable"]) -> None: ...
type___SetStrobeDisableRequest = SetStrobeDisableRequest

class SetStrobeDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetStrobeDisableResponse = SetStrobeDisableResponse

class SetGPSDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable"]) -> None: ...
type___SetGPSDisableRequest = SetGPSDisableRequest

class SetGPSDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetGPSDisableResponse = SetGPSDisableResponse

class CommandComputerPowerCycleRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___CommandComputerPowerCycleRequest = CommandComputerPowerCycleRequest

class CommandComputerPowerCycleResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___CommandComputerPowerCycleResponse = CommandComputerPowerCycleResponse

class SuicideSwitchRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SuicideSwitchRequest = SuicideSwitchRequest

class SuicideSwitchResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SuicideSwitchResponse = SuicideSwitchResponse

class SetMainContactorDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable"]) -> None: ...
type___SetMainContactorDisableRequest = SetMainContactorDisableRequest

class SetMainContactorDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetMainContactorDisableResponse = SetMainContactorDisableResponse

class SetAirConditionerDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable"]) -> None: ...
type___SetAirConditionerDisableRequest = SetAirConditionerDisableRequest

class SetAirConditionerDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetAirConditionerDisableResponse = SetAirConditionerDisableResponse

class SetChillerDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable"]) -> None: ...
type___SetChillerDisableRequest = SetChillerDisableRequest

class SetChillerDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetChillerDisableResponse = SetChillerDisableResponse

class SetTempBypassDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable"]) -> None: ...
type___SetTempBypassDisableRequest = SetTempBypassDisableRequest

class SetTempBypassDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetTempBypassDisableResponse = SetTempBypassDisableResponse

class SetHumidityBypassDisableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disable: builtin___bool = ...

    def __init__(self,
        *,
        disable : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disable",b"disable"]) -> None: ...
type___SetHumidityBypassDisableRequest = SetHumidityBypassDisableRequest

class SetHumidityBypassDisableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetHumidityBypassDisableResponse = SetHumidityBypassDisableResponse

class GetAvailableUSBStorageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetAvailableUSBStorageRequest = GetAvailableUSBStorageRequest

class GetAvailableUSBStorageResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    used: builtin___float = ...
    success: builtin___bool = ...
    usb_available: builtin___bool = ...

    def __init__(self,
        *,
        used : typing___Optional[builtin___float] = None,
        success : typing___Optional[builtin___bool] = None,
        usb_available : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success",u"usb_available",b"usb_available",u"used",b"used"]) -> None: ...
type___GetAvailableUSBStorageResponse = GetAvailableUSBStorageResponse

class GetReadyRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetReadyRequest = GetReadyRequest

class GetReadyResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ready: builtin___bool = ...

    def __init__(self,
        *,
        ready : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ready",b"ready"]) -> None: ...
type___GetReadyResponse = GetReadyResponse

class Get240vUptimeRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Get240vUptimeRequest = Get240vUptimeRequest

class Get240vUptimeResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    uptime_s: builtin___int = ...

    def __init__(self,
        *,
        uptime_s : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"uptime_s",b"uptime_s"]) -> None: ...
type___Get240vUptimeResponse = Get240vUptimeResponse

class GetDeltaTravelMMRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___GetDeltaTravelMMRequest = GetDeltaTravelMMRequest

class GetDeltaTravelMMResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    delta_mm: builtin___float = ...

    def __init__(self,
        *,
        delta_mm : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"delta_mm",b"delta_mm"]) -> None: ...
type___GetDeltaTravelMMResponse = GetDeltaTravelMMResponse

class GetRuntimeRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetRuntimeRequest = GetRuntimeRequest

class GetRuntimeResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    runtime_240v: builtin___int = ...

    def __init__(self,
        *,
        runtime_240v : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"runtime_240v",b"runtime_240v"]) -> None: ...
type___GetRuntimeResponse = GetRuntimeResponse

class GetWheelEncoderResolutionRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetWheelEncoderResolutionRequest = GetWheelEncoderResolutionRequest

class GetWheelEncoderResolutionResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    resolution: builtin___int = ...

    def __init__(self,
        *,
        resolution : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"resolution",b"resolution"]) -> None: ...
type___GetWheelEncoderResolutionResponse = GetWheelEncoderResolutionResponse

class StrobeSettings(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    exposure_us: builtin___int = ...
    period_us: builtin___int = ...
    targets_per_predict_ratio: builtin___int = ...

    def __init__(self,
        *,
        exposure_us : typing___Optional[builtin___int] = None,
        period_us : typing___Optional[builtin___int] = None,
        targets_per_predict_ratio : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_exposure_us",b"_exposure_us",u"_period_us",b"_period_us",u"_targets_per_predict_ratio",b"_targets_per_predict_ratio",u"exposure_us",b"exposure_us",u"period_us",b"period_us",u"targets_per_predict_ratio",b"targets_per_predict_ratio"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_exposure_us",b"_exposure_us",u"_period_us",b"_period_us",u"_targets_per_predict_ratio",b"_targets_per_predict_ratio",u"exposure_us",b"exposure_us",u"period_us",b"period_us",u"targets_per_predict_ratio",b"targets_per_predict_ratio"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_exposure_us",b"_exposure_us"]) -> typing_extensions___Literal["exposure_us"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_period_us",b"_period_us"]) -> typing_extensions___Literal["period_us"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_targets_per_predict_ratio",b"_targets_per_predict_ratio"]) -> typing_extensions___Literal["targets_per_predict_ratio"]: ...
type___StrobeSettings = StrobeSettings

class SetStrobeSettingsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetStrobeSettingsResponse = SetStrobeSettingsResponse

class GetStrobeSettingsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetStrobeSettingsRequest = GetStrobeSettingsRequest

class EnvironmentalSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    temperature_c: builtin___float = ...
    humidity_rh: builtin___float = ...
    pressure_hpa: builtin___float = ...

    def __init__(self,
        *,
        temperature_c : typing___Optional[builtin___float] = None,
        humidity_rh : typing___Optional[builtin___float] = None,
        pressure_hpa : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"humidity_rh",b"humidity_rh",u"pressure_hpa",b"pressure_hpa",u"temperature_c",b"temperature_c"]) -> None: ...
type___EnvironmentalSensorData = EnvironmentalSensorData

class CoolantSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    temperature_c: builtin___float = ...
    pressure_kpa: builtin___float = ...

    def __init__(self,
        *,
        temperature_c : typing___Optional[builtin___float] = None,
        pressure_kpa : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pressure_kpa",b"pressure_kpa",u"temperature_c",b"temperature_c"]) -> None: ...
type___CoolantSensorData = CoolantSensorData

class NetworkPortState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    link_up: builtin___bool = ...
    actual_link_speed: type___NetworkLinkSpeedValue = ...
    expected_link_speed: type___NetworkLinkSpeedValue = ...

    def __init__(self,
        *,
        link_up : typing___Optional[builtin___bool] = None,
        actual_link_speed : typing___Optional[type___NetworkLinkSpeedValue] = None,
        expected_link_speed : typing___Optional[type___NetworkLinkSpeedValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"actual_link_speed",b"actual_link_speed",u"expected_link_speed",b"expected_link_speed",u"link_up",b"link_up"]) -> None: ...
type___NetworkPortState = NetworkPortState

class ReaperPcSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    temperature_cpu_core_c: builtin___float = ...
    temperature_system_c: builtin___float = ...
    temperature_gpu_1_c: builtin___float = ...
    temperature_gpu_2_c: builtin___float = ...
    psu_12v: builtin___float = ...
    psu_5v: builtin___float = ...
    psu_3v3: builtin___float = ...
    load: builtin___float = ...
    uptime: builtin___int = ...
    ram_usage_percent: builtin___float = ...
    disk_usage_percent: builtin___float = ...

    @property
    def scanner_link(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___NetworkPortState]: ...

    @property
    def target_cam_link(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___NetworkPortState]: ...

    @property
    def predict_cam_link(self) -> type___NetworkPortState: ...

    @property
    def ipmi_link(self) -> type___NetworkPortState: ...

    @property
    def global_link(self) -> type___NetworkPortState: ...

    @property
    def ext_link(self) -> type___NetworkPortState: ...

    def __init__(self,
        *,
        temperature_cpu_core_c : typing___Optional[builtin___float] = None,
        temperature_system_c : typing___Optional[builtin___float] = None,
        temperature_gpu_1_c : typing___Optional[builtin___float] = None,
        temperature_gpu_2_c : typing___Optional[builtin___float] = None,
        psu_12v : typing___Optional[builtin___float] = None,
        psu_5v : typing___Optional[builtin___float] = None,
        psu_3v3 : typing___Optional[builtin___float] = None,
        load : typing___Optional[builtin___float] = None,
        uptime : typing___Optional[builtin___int] = None,
        ram_usage_percent : typing___Optional[builtin___float] = None,
        disk_usage_percent : typing___Optional[builtin___float] = None,
        scanner_link : typing___Optional[typing___Iterable[type___NetworkPortState]] = None,
        target_cam_link : typing___Optional[typing___Iterable[type___NetworkPortState]] = None,
        predict_cam_link : typing___Optional[type___NetworkPortState] = None,
        ipmi_link : typing___Optional[type___NetworkPortState] = None,
        global_link : typing___Optional[type___NetworkPortState] = None,
        ext_link : typing___Optional[type___NetworkPortState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_temperature_gpu_1_c",b"_temperature_gpu_1_c",u"_temperature_gpu_2_c",b"_temperature_gpu_2_c",u"ext_link",b"ext_link",u"global_link",b"global_link",u"ipmi_link",b"ipmi_link",u"predict_cam_link",b"predict_cam_link",u"temperature_gpu_1_c",b"temperature_gpu_1_c",u"temperature_gpu_2_c",b"temperature_gpu_2_c"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_temperature_gpu_1_c",b"_temperature_gpu_1_c",u"_temperature_gpu_2_c",b"_temperature_gpu_2_c",u"disk_usage_percent",b"disk_usage_percent",u"ext_link",b"ext_link",u"global_link",b"global_link",u"ipmi_link",b"ipmi_link",u"load",b"load",u"predict_cam_link",b"predict_cam_link",u"psu_12v",b"psu_12v",u"psu_3v3",b"psu_3v3",u"psu_5v",b"psu_5v",u"ram_usage_percent",b"ram_usage_percent",u"scanner_link",b"scanner_link",u"target_cam_link",b"target_cam_link",u"temperature_cpu_core_c",b"temperature_cpu_core_c",u"temperature_gpu_1_c",b"temperature_gpu_1_c",u"temperature_gpu_2_c",b"temperature_gpu_2_c",u"temperature_system_c",b"temperature_system_c",u"uptime",b"uptime"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_temperature_gpu_1_c",b"_temperature_gpu_1_c"]) -> typing_extensions___Literal["temperature_gpu_1_c"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_temperature_gpu_2_c",b"_temperature_gpu_2_c"]) -> typing_extensions___Literal["temperature_gpu_2_c"]: ...
type___ReaperPcSensorData = ReaperPcSensorData

class ReaperScannerLaserStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model: typing___Text = ...
    sn: typing___Text = ...
    rated_power: builtin___int = ...
    temperature_c: builtin___float = ...
    humidity: builtin___float = ...
    laser_current_ma: builtin___float = ...
    faults: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        model : typing___Optional[typing___Text] = None,
        sn : typing___Optional[typing___Text] = None,
        rated_power : typing___Optional[builtin___int] = None,
        temperature_c : typing___Optional[builtin___float] = None,
        humidity : typing___Optional[builtin___float] = None,
        laser_current_ma : typing___Optional[builtin___float] = None,
        faults : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"faults",b"faults",u"humidity",b"humidity",u"laser_current_ma",b"laser_current_ma",u"model",b"model",u"rated_power",b"rated_power",u"sn",b"sn",u"temperature_c",b"temperature_c"]) -> None: ...
type___ReaperScannerLaserStatus = ReaperScannerLaserStatus

class ReaperScannerMotorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    controller_sn: typing___Text = ...
    temperature_output_c: builtin___float = ...
    motor_supply_v: builtin___float = ...
    motor_current_a: builtin___float = ...
    encoder_position: builtin___int = ...

    def __init__(self,
        *,
        controller_sn : typing___Optional[typing___Text] = None,
        temperature_output_c : typing___Optional[builtin___float] = None,
        motor_supply_v : typing___Optional[builtin___float] = None,
        motor_current_a : typing___Optional[builtin___float] = None,
        encoder_position : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"controller_sn",b"controller_sn",u"encoder_position",b"encoder_position",u"motor_current_a",b"motor_current_a",u"motor_supply_v",b"motor_supply_v",u"temperature_output_c",b"temperature_output_c"]) -> None: ...
type___ReaperScannerMotorData = ReaperScannerMotorData

class ReaperScannerSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_sn: typing___Text = ...
    power_on: builtin___bool = ...
    current_a: builtin___float = ...
    fuse_tripped: builtin___bool = ...
    temperature_collimator_c: builtin___float = ...
    temperature_fiber_c: builtin___float = ...
    laser_power_w: builtin___float = ...
    laser_power_raw_mv: builtin___float = ...
    laser_connected: builtin___bool = ...
    target_connected: builtin___bool = ...
    target_sn: typing___Text = ...
    temperature_target_c: builtin___float = ...
    target_cam_power_enabled: builtin___bool = ...

    @property
    def laser_status(self) -> type___ReaperScannerLaserStatus: ...

    @property
    def motor_pan(self) -> type___ReaperScannerMotorData: ...

    @property
    def motor_tilt(self) -> type___ReaperScannerMotorData: ...

    def __init__(self,
        *,
        scanner_sn : typing___Optional[typing___Text] = None,
        power_on : typing___Optional[builtin___bool] = None,
        current_a : typing___Optional[builtin___float] = None,
        fuse_tripped : typing___Optional[builtin___bool] = None,
        temperature_collimator_c : typing___Optional[builtin___float] = None,
        temperature_fiber_c : typing___Optional[builtin___float] = None,
        laser_power_w : typing___Optional[builtin___float] = None,
        laser_power_raw_mv : typing___Optional[builtin___float] = None,
        laser_connected : typing___Optional[builtin___bool] = None,
        laser_status : typing___Optional[type___ReaperScannerLaserStatus] = None,
        target_connected : typing___Optional[builtin___bool] = None,
        target_sn : typing___Optional[typing___Text] = None,
        temperature_target_c : typing___Optional[builtin___float] = None,
        motor_pan : typing___Optional[type___ReaperScannerMotorData] = None,
        motor_tilt : typing___Optional[type___ReaperScannerMotorData] = None,
        target_cam_power_enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_laser_status",b"_laser_status",u"_motor_pan",b"_motor_pan",u"_motor_tilt",b"_motor_tilt",u"_target_sn",b"_target_sn",u"_temperature_target_c",b"_temperature_target_c",u"laser_status",b"laser_status",u"motor_pan",b"motor_pan",u"motor_tilt",b"motor_tilt",u"target_sn",b"target_sn",u"temperature_target_c",b"temperature_target_c"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_laser_status",b"_laser_status",u"_motor_pan",b"_motor_pan",u"_motor_tilt",b"_motor_tilt",u"_target_sn",b"_target_sn",u"_temperature_target_c",b"_temperature_target_c",u"current_a",b"current_a",u"fuse_tripped",b"fuse_tripped",u"laser_connected",b"laser_connected",u"laser_power_raw_mv",b"laser_power_raw_mv",u"laser_power_w",b"laser_power_w",u"laser_status",b"laser_status",u"motor_pan",b"motor_pan",u"motor_tilt",b"motor_tilt",u"power_on",b"power_on",u"scanner_sn",b"scanner_sn",u"target_cam_power_enabled",b"target_cam_power_enabled",u"target_connected",b"target_connected",u"target_sn",b"target_sn",u"temperature_collimator_c",b"temperature_collimator_c",u"temperature_fiber_c",b"temperature_fiber_c",u"temperature_target_c",b"temperature_target_c"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_laser_status",b"_laser_status"]) -> typing_extensions___Literal["laser_status"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_motor_pan",b"_motor_pan"]) -> typing_extensions___Literal["motor_pan"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_motor_tilt",b"_motor_tilt"]) -> typing_extensions___Literal["motor_tilt"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_target_sn",b"_target_sn"]) -> typing_extensions___Literal["target_sn"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_temperature_target_c",b"_temperature_target_c"]) -> typing_extensions___Literal["temperature_target_c"]: ...
type___ReaperScannerSensorData = ReaperScannerSensorData

class ReaperCenterEnclosureData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    water_protect_status: builtin___bool = ...
    main_contactor_status_fb: builtin___bool = ...
    power_good: builtin___bool = ...
    power_bad: builtin___bool = ...
    power_very_bad: builtin___bool = ...
    lifted_status: builtin___bool = ...
    tractor_power: builtin___bool = ...
    ac_frequency: builtin___float = ...
    ac_voltage_a_b: builtin___float = ...
    ac_voltage_b_c: builtin___float = ...
    ac_voltage_a_c: builtin___float = ...
    ac_voltage_a: builtin___float = ...
    ac_voltage_b: builtin___float = ...
    ac_voltage_c: builtin___float = ...
    phase_power_w_3: builtin___int = ...
    phase_power_va_3: builtin___int = ...
    power_factor: builtin___float = ...
    server_cabinet_temp: builtin___float = ...
    server_cabinet_humidity: builtin___float = ...
    battery_voltage_12v: builtin___float = ...
    wheel_encoder_disabled: builtin___bool = ...
    strobe_disabled: builtin___bool = ...
    gps_disabled: builtin___bool = ...
    main_contactor_disabled: builtin___bool = ...
    air_conditioner_disabled: builtin___bool = ...
    chiller_disabled: builtin___bool = ...
    chiller_temp: builtin___float = ...
    chiller_flow: builtin___float = ...
    chiller_pressure: builtin___float = ...
    chiller_conductivity: builtin___float = ...
    chiller_set_temp: builtin___float = ...
    chiller_heat_transfer: builtin___float = ...
    chiller_fluid_delta_temp: builtin___float = ...

    @property
    def chiller_alarms(self) -> type___ChillerAlarms: ...

    def __init__(self,
        *,
        water_protect_status : typing___Optional[builtin___bool] = None,
        main_contactor_status_fb : typing___Optional[builtin___bool] = None,
        power_good : typing___Optional[builtin___bool] = None,
        power_bad : typing___Optional[builtin___bool] = None,
        power_very_bad : typing___Optional[builtin___bool] = None,
        lifted_status : typing___Optional[builtin___bool] = None,
        tractor_power : typing___Optional[builtin___bool] = None,
        ac_frequency : typing___Optional[builtin___float] = None,
        ac_voltage_a_b : typing___Optional[builtin___float] = None,
        ac_voltage_b_c : typing___Optional[builtin___float] = None,
        ac_voltage_a_c : typing___Optional[builtin___float] = None,
        ac_voltage_a : typing___Optional[builtin___float] = None,
        ac_voltage_b : typing___Optional[builtin___float] = None,
        ac_voltage_c : typing___Optional[builtin___float] = None,
        phase_power_w_3 : typing___Optional[builtin___int] = None,
        phase_power_va_3 : typing___Optional[builtin___int] = None,
        power_factor : typing___Optional[builtin___float] = None,
        server_cabinet_temp : typing___Optional[builtin___float] = None,
        server_cabinet_humidity : typing___Optional[builtin___float] = None,
        battery_voltage_12v : typing___Optional[builtin___float] = None,
        wheel_encoder_disabled : typing___Optional[builtin___bool] = None,
        strobe_disabled : typing___Optional[builtin___bool] = None,
        gps_disabled : typing___Optional[builtin___bool] = None,
        main_contactor_disabled : typing___Optional[builtin___bool] = None,
        air_conditioner_disabled : typing___Optional[builtin___bool] = None,
        chiller_disabled : typing___Optional[builtin___bool] = None,
        chiller_temp : typing___Optional[builtin___float] = None,
        chiller_flow : typing___Optional[builtin___float] = None,
        chiller_pressure : typing___Optional[builtin___float] = None,
        chiller_conductivity : typing___Optional[builtin___float] = None,
        chiller_set_temp : typing___Optional[builtin___float] = None,
        chiller_heat_transfer : typing___Optional[builtin___float] = None,
        chiller_fluid_delta_temp : typing___Optional[builtin___float] = None,
        chiller_alarms : typing___Optional[type___ChillerAlarms] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"chiller_alarms",b"chiller_alarms"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ac_frequency",b"ac_frequency",u"ac_voltage_a",b"ac_voltage_a",u"ac_voltage_a_b",b"ac_voltage_a_b",u"ac_voltage_a_c",b"ac_voltage_a_c",u"ac_voltage_b",b"ac_voltage_b",u"ac_voltage_b_c",b"ac_voltage_b_c",u"ac_voltage_c",b"ac_voltage_c",u"air_conditioner_disabled",b"air_conditioner_disabled",u"battery_voltage_12v",b"battery_voltage_12v",u"chiller_alarms",b"chiller_alarms",u"chiller_conductivity",b"chiller_conductivity",u"chiller_disabled",b"chiller_disabled",u"chiller_flow",b"chiller_flow",u"chiller_fluid_delta_temp",b"chiller_fluid_delta_temp",u"chiller_heat_transfer",b"chiller_heat_transfer",u"chiller_pressure",b"chiller_pressure",u"chiller_set_temp",b"chiller_set_temp",u"chiller_temp",b"chiller_temp",u"gps_disabled",b"gps_disabled",u"lifted_status",b"lifted_status",u"main_contactor_disabled",b"main_contactor_disabled",u"main_contactor_status_fb",b"main_contactor_status_fb",u"phase_power_va_3",b"phase_power_va_3",u"phase_power_w_3",b"phase_power_w_3",u"power_bad",b"power_bad",u"power_factor",b"power_factor",u"power_good",b"power_good",u"power_very_bad",b"power_very_bad",u"server_cabinet_humidity",b"server_cabinet_humidity",u"server_cabinet_temp",b"server_cabinet_temp",u"strobe_disabled",b"strobe_disabled",u"tractor_power",b"tractor_power",u"water_protect_status",b"water_protect_status",u"wheel_encoder_disabled",b"wheel_encoder_disabled"]) -> None: ...
type___ReaperCenterEnclosureData = ReaperCenterEnclosureData

class ReaperModuleSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    module_sn: typing___Text = ...
    strobe_temperature_c: builtin___float = ...
    strobe_cap_voltage: builtin___float = ...
    strobe_current: builtin___float = ...
    pc_power_enabled: builtin___bool = ...
    lasers_power_enabled: builtin___bool = ...
    predict_cam_power_enabled: builtin___bool = ...
    strobe_power_enabled: builtin___bool = ...
    strobe_enabled: builtin___bool = ...

    @property
    def enviro_enclosure(self) -> type___EnvironmentalSensorData: ...

    @property
    def enviro_pc(self) -> type___EnvironmentalSensorData: ...

    @property
    def coolant_inlet(self) -> type___CoolantSensorData: ...

    @property
    def coolant_outlet(self) -> type___CoolantSensorData: ...

    @property
    def pc(self) -> type___ReaperPcSensorData: ...

    @property
    def scanner_a(self) -> type___ReaperScannerSensorData: ...

    @property
    def scanner_b(self) -> type___ReaperScannerSensorData: ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        module_sn : typing___Optional[typing___Text] = None,
        enviro_enclosure : typing___Optional[type___EnvironmentalSensorData] = None,
        enviro_pc : typing___Optional[type___EnvironmentalSensorData] = None,
        coolant_inlet : typing___Optional[type___CoolantSensorData] = None,
        coolant_outlet : typing___Optional[type___CoolantSensorData] = None,
        strobe_temperature_c : typing___Optional[builtin___float] = None,
        strobe_cap_voltage : typing___Optional[builtin___float] = None,
        strobe_current : typing___Optional[builtin___float] = None,
        pc : typing___Optional[type___ReaperPcSensorData] = None,
        scanner_a : typing___Optional[type___ReaperScannerSensorData] = None,
        scanner_b : typing___Optional[type___ReaperScannerSensorData] = None,
        pc_power_enabled : typing___Optional[builtin___bool] = None,
        lasers_power_enabled : typing___Optional[builtin___bool] = None,
        predict_cam_power_enabled : typing___Optional[builtin___bool] = None,
        strobe_power_enabled : typing___Optional[builtin___bool] = None,
        strobe_enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_module_sn",b"_module_sn",u"_pc",b"_pc",u"_scanner_a",b"_scanner_a",u"_scanner_b",b"_scanner_b",u"coolant_inlet",b"coolant_inlet",u"coolant_outlet",b"coolant_outlet",u"enviro_enclosure",b"enviro_enclosure",u"enviro_pc",b"enviro_pc",u"module_sn",b"module_sn",u"pc",b"pc",u"scanner_a",b"scanner_a",u"scanner_b",b"scanner_b"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_module_sn",b"_module_sn",u"_pc",b"_pc",u"_scanner_a",b"_scanner_a",u"_scanner_b",b"_scanner_b",u"coolant_inlet",b"coolant_inlet",u"coolant_outlet",b"coolant_outlet",u"enviro_enclosure",b"enviro_enclosure",u"enviro_pc",b"enviro_pc",u"lasers_power_enabled",b"lasers_power_enabled",u"module_id",b"module_id",u"module_sn",b"module_sn",u"pc",b"pc",u"pc_power_enabled",b"pc_power_enabled",u"predict_cam_power_enabled",b"predict_cam_power_enabled",u"scanner_a",b"scanner_a",u"scanner_b",b"scanner_b",u"strobe_cap_voltage",b"strobe_cap_voltage",u"strobe_current",b"strobe_current",u"strobe_enabled",b"strobe_enabled",u"strobe_power_enabled",b"strobe_power_enabled",u"strobe_temperature_c",b"strobe_temperature_c"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_module_sn",b"_module_sn"]) -> typing_extensions___Literal["module_sn"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_pc",b"_pc"]) -> typing_extensions___Literal["pc"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scanner_a",b"_scanner_a"]) -> typing_extensions___Literal["scanner_a"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scanner_b",b"_scanner_b"]) -> typing_extensions___Literal["scanner_b"]: ...
type___ReaperModuleSensorData = ReaperModuleSensorData

class GetReaperEnclosureSensorsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetReaperEnclosureSensorsRequest = GetReaperEnclosureSensorsRequest

class GetReaperEnclosureSensorsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def sensors(self) -> type___ReaperCenterEnclosureData: ...

    def __init__(self,
        *,
        sensors : typing___Optional[type___ReaperCenterEnclosureData] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"sensors",b"sensors"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"sensors",b"sensors"]) -> None: ...
type___GetReaperEnclosureSensorsResponse = GetReaperEnclosureSensorsResponse

class GetReaperModuleSensorsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...

    def __init__(self,
        *,
        module_ids : typing___Optional[typing___Iterable[builtin___int]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"module_ids",b"module_ids"]) -> None: ...
type___GetReaperModuleSensorsRequest = GetReaperModuleSensorsRequest

class GetReaperModuleSensorsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def module_sensors(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ReaperModuleSensorData]: ...

    def __init__(self,
        *,
        module_sensors : typing___Optional[typing___Iterable[type___ReaperModuleSensorData]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"module_sensors",b"module_sensors"]) -> None: ...
type___GetReaperModuleSensorsResponse = GetReaperModuleSensorsResponse

class SetReaperScannerPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    scanner_a_power: builtin___bool = ...
    scanner_b_power: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        scanner_a_power : typing___Optional[builtin___bool] = None,
        scanner_b_power : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_scanner_a_power",b"_scanner_a_power",u"_scanner_b_power",b"_scanner_b_power",u"scanner_a_power",b"scanner_a_power",u"scanner_b_power",b"scanner_b_power"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_scanner_a_power",b"_scanner_a_power",u"_scanner_b_power",b"_scanner_b_power",u"module_id",b"module_id",u"scanner_a_power",b"scanner_a_power",u"scanner_b_power",b"scanner_b_power"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scanner_a_power",b"_scanner_a_power"]) -> typing_extensions___Literal["scanner_a_power"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scanner_b_power",b"_scanner_b_power"]) -> typing_extensions___Literal["scanner_b_power"]: ...
type___SetReaperScannerPowerRequest = SetReaperScannerPowerRequest

class SetReaperScannerPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperScannerPowerResponse = SetReaperScannerPowerResponse

class SetReaperTargetPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    target_a_power: builtin___bool = ...
    target_b_power: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        target_a_power : typing___Optional[builtin___bool] = None,
        target_b_power : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_target_a_power",b"_target_a_power",u"_target_b_power",b"_target_b_power",u"target_a_power",b"target_a_power",u"target_b_power",b"target_b_power"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_target_a_power",b"_target_a_power",u"_target_b_power",b"_target_b_power",u"module_id",b"module_id",u"target_a_power",b"target_a_power",u"target_b_power",b"target_b_power"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_target_a_power",b"_target_a_power"]) -> typing_extensions___Literal["target_a_power"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_target_b_power",b"_target_b_power"]) -> typing_extensions___Literal["target_b_power"]: ...
type___SetReaperTargetPowerRequest = SetReaperTargetPowerRequest

class SetReaperTargetPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperTargetPowerResponse = SetReaperTargetPowerResponse

class SetReaperPredictCamPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"module_id",b"module_id"]) -> None: ...
type___SetReaperPredictCamPowerRequest = SetReaperPredictCamPowerRequest

class SetReaperPredictCamPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperPredictCamPowerResponse = SetReaperPredictCamPowerResponse

class SetReaperStrobeConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...

    @property
    def settings(self) -> type___StrobeSettings: ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        settings : typing___Optional[type___StrobeSettings] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_module_id",b"_module_id",u"module_id",b"module_id",u"settings",b"settings"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_module_id",b"_module_id",u"module_id",b"module_id",u"settings",b"settings"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_module_id",b"_module_id"]) -> typing_extensions___Literal["module_id"]: ...
type___SetReaperStrobeConfigRequest = SetReaperStrobeConfigRequest

class SetReaperStrobeConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperStrobeConfigResponse = SetReaperStrobeConfigResponse

class SetReaperStrobeEnableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    enabled: builtin___bool = ...
    duration_ms: builtin___int = ...

    def __init__(self,
        *,
        module_ids : typing___Optional[typing___Iterable[builtin___int]] = None,
        enabled : typing___Optional[builtin___bool] = None,
        duration_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_duration_ms",b"_duration_ms",u"duration_ms",b"duration_ms"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_duration_ms",b"_duration_ms",u"duration_ms",b"duration_ms",u"enabled",b"enabled",u"module_ids",b"module_ids"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_duration_ms",b"_duration_ms"]) -> typing_extensions___Literal["duration_ms"]: ...
type___SetReaperStrobeEnableRequest = SetReaperStrobeEnableRequest

class SetReaperStrobeEnableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperStrobeEnableResponse = SetReaperStrobeEnableResponse

class SetReaperModulePcPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"module_id",b"module_id"]) -> None: ...
type___SetReaperModulePcPowerRequest = SetReaperModulePcPowerRequest

class SetReaperModulePcPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperModulePcPowerResponse = SetReaperModulePcPowerResponse

class SetReaperModuleLaserPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"module_id",b"module_id"]) -> None: ...
type___SetReaperModuleLaserPowerRequest = SetReaperModuleLaserPowerRequest

class SetReaperModuleLaserPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperModuleLaserPowerResponse = SetReaperModuleLaserPowerResponse

class SetReaperModuleStrobePowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"module_id",b"module_id"]) -> None: ...
type___SetReaperModuleStrobePowerRequest = SetReaperModuleStrobePowerRequest

class SetReaperModuleStrobePowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperModuleStrobePowerResponse = SetReaperModuleStrobePowerResponse

class IdentifyModuleRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    to_id_ip: typing___Text = ...
    turn_off_ips: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        to_id_ip : typing___Optional[typing___Text] = None,
        turn_off_ips : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"to_id_ip",b"to_id_ip",u"turn_off_ips",b"turn_off_ips"]) -> None: ...
type___IdentifyModuleRequest = IdentifyModuleRequest

class IdentifyModuleResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___IdentifyModuleResponse = IdentifyModuleResponse

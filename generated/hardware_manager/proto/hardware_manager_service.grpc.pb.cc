// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: hardware_manager/proto/hardware_manager_service.proto

#include "hardware_manager/proto/hardware_manager_service.pb.h"
#include "hardware_manager/proto/hardware_manager_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace hardware_manager {

static const char* HardwareManagerService_method_names[] = {
  "/hardware_manager.HardwareManagerService/Ping",
  "/hardware_manager.HardwareManagerService/GetReady",
  "/hardware_manager.HardwareManagerService/GetNextDistance",
  "/hardware_manager.HardwareManagerService/GetNextVelocity",
  "/hardware_manager.HardwareManagerService/GetRotaryTicks",
  "/hardware_manager.HardwareManagerService/GetDeltaTravelMM",
  "/hardware_manager.HardwareManagerService/GetWheelEncoderResolution",
  "/hardware_manager.HardwareManagerService/GetSafetyStatus",
  "/hardware_manager.HardwareManagerService/GetGPSData",
  "/hardware_manager.HardwareManagerService/GetNextGPSData",
  "/hardware_manager.HardwareManagerService/GetNextRawGPSData",
  "/hardware_manager.HardwareManagerService/GetGPSFixedPos",
  "/hardware_manager.HardwareManagerService/SetStrobeSettings",
  "/hardware_manager.HardwareManagerService/GetStrobeSettings",
  "/hardware_manager.HardwareManagerService/GetManagedBoardErrors",
  "/hardware_manager.HardwareManagerService/GetSupervisoryStatus",
  "/hardware_manager.HardwareManagerService/GetReaperSupervisoryStatus",
  "/hardware_manager.HardwareManagerService/SetServerDisable",
  "/hardware_manager.HardwareManagerService/SetBTLDisable",
  "/hardware_manager.HardwareManagerService/SetScannersDisable",
  "/hardware_manager.HardwareManagerService/SetWheelEncoderBoardDisable",
  "/hardware_manager.HardwareManagerService/SetWheelEncoderDisable",
  "/hardware_manager.HardwareManagerService/SetGPSDisable",
  "/hardware_manager.HardwareManagerService/SuicideSwitch",
  "/hardware_manager.HardwareManagerService/CommandComputerPowerCycle",
  "/hardware_manager.HardwareManagerService/SetMainContactorDisable",
  "/hardware_manager.HardwareManagerService/SetStrobeDisable",
  "/hardware_manager.HardwareManagerService/SetAirConditionerDisable",
  "/hardware_manager.HardwareManagerService/SetChillerDisable",
  "/hardware_manager.HardwareManagerService/SetTempBypassDisable",
  "/hardware_manager.HardwareManagerService/SetHumidityBypassDisable",
  "/hardware_manager.HardwareManagerService/Get240vUptime",
  "/hardware_manager.HardwareManagerService/GetRuntime",
  "/hardware_manager.HardwareManagerService/GetAvailableUSBStorage",
  "/hardware_manager.HardwareManagerService/SetJimboxSpeed",
  "/hardware_manager.HardwareManagerService/SetCruiseEnabled",
  "/hardware_manager.HardwareManagerService/GetCruiseStatus",
  "/hardware_manager.HardwareManagerService/SetImplementStateOnTractor",
  "/hardware_manager.HardwareManagerService/SetSafeStateEnforcement",
  "/hardware_manager.HardwareManagerService/GetTractorSafetyState",
  "/hardware_manager.HardwareManagerService/GetTractorIFState",
  "/hardware_manager.HardwareManagerService/GetReaperEnclosureSensors",
  "/hardware_manager.HardwareManagerService/GetReaperModuleSensors",
  "/hardware_manager.HardwareManagerService/SetReaperScannerPower",
  "/hardware_manager.HardwareManagerService/SetReaperTargetPower",
  "/hardware_manager.HardwareManagerService/SetReaperPredictCamPower",
  "/hardware_manager.HardwareManagerService/SetReaperStrobeConfig",
  "/hardware_manager.HardwareManagerService/SetReaperStrobeEnable",
  "/hardware_manager.HardwareManagerService/SetReaperModulePcPower",
  "/hardware_manager.HardwareManagerService/SetReaperModuleLaserPower",
  "/hardware_manager.HardwareManagerService/SetReaperModuleStrobePower",
  "/hardware_manager.HardwareManagerService/IdentifyModule",
};

std::unique_ptr< HardwareManagerService::Stub> HardwareManagerService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< HardwareManagerService::Stub> stub(new HardwareManagerService::Stub(channel, options));
  return stub;
}

HardwareManagerService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Ping_(HardwareManagerService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetReady_(HardwareManagerService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextDistance_(HardwareManagerService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextVelocity_(HardwareManagerService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetRotaryTicks_(HardwareManagerService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDeltaTravelMM_(HardwareManagerService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetWheelEncoderResolution_(HardwareManagerService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSafetyStatus_(HardwareManagerService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetGPSData_(HardwareManagerService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextGPSData_(HardwareManagerService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextRawGPSData_(HardwareManagerService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetGPSFixedPos_(HardwareManagerService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetStrobeSettings_(HardwareManagerService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetStrobeSettings_(HardwareManagerService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetManagedBoardErrors_(HardwareManagerService_method_names[14], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSupervisoryStatus_(HardwareManagerService_method_names[15], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetReaperSupervisoryStatus_(HardwareManagerService_method_names[16], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetServerDisable_(HardwareManagerService_method_names[17], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetBTLDisable_(HardwareManagerService_method_names[18], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetScannersDisable_(HardwareManagerService_method_names[19], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetWheelEncoderBoardDisable_(HardwareManagerService_method_names[20], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetWheelEncoderDisable_(HardwareManagerService_method_names[21], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetGPSDisable_(HardwareManagerService_method_names[22], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SuicideSwitch_(HardwareManagerService_method_names[23], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CommandComputerPowerCycle_(HardwareManagerService_method_names[24], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetMainContactorDisable_(HardwareManagerService_method_names[25], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetStrobeDisable_(HardwareManagerService_method_names[26], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetAirConditionerDisable_(HardwareManagerService_method_names[27], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetChillerDisable_(HardwareManagerService_method_names[28], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetTempBypassDisable_(HardwareManagerService_method_names[29], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetHumidityBypassDisable_(HardwareManagerService_method_names[30], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Get240vUptime_(HardwareManagerService_method_names[31], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetRuntime_(HardwareManagerService_method_names[32], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetAvailableUSBStorage_(HardwareManagerService_method_names[33], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetJimboxSpeed_(HardwareManagerService_method_names[34], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetCruiseEnabled_(HardwareManagerService_method_names[35], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCruiseStatus_(HardwareManagerService_method_names[36], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetImplementStateOnTractor_(HardwareManagerService_method_names[37], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetSafeStateEnforcement_(HardwareManagerService_method_names[38], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTractorSafetyState_(HardwareManagerService_method_names[39], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTractorIFState_(HardwareManagerService_method_names[40], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetReaperEnclosureSensors_(HardwareManagerService_method_names[41], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetReaperModuleSensors_(HardwareManagerService_method_names[42], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperScannerPower_(HardwareManagerService_method_names[43], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperTargetPower_(HardwareManagerService_method_names[44], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperPredictCamPower_(HardwareManagerService_method_names[45], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperStrobeConfig_(HardwareManagerService_method_names[46], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperStrobeEnable_(HardwareManagerService_method_names[47], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperModulePcPower_(HardwareManagerService_method_names[48], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperModuleLaserPower_(HardwareManagerService_method_names[49], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetReaperModuleStrobePower_(HardwareManagerService_method_names[50], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_IdentifyModule_(HardwareManagerService_method_names[51], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status HardwareManagerService::Stub::Ping(::grpc::ClientContext* context, const ::hardware_manager::PingRequest& request, ::hardware_manager::PingResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::PingRequest, ::hardware_manager::PingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Ping_, context, request, response);
}

void HardwareManagerService::Stub::async::Ping(::grpc::ClientContext* context, const ::hardware_manager::PingRequest* request, ::hardware_manager::PingResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::PingRequest, ::hardware_manager::PingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::Ping(::grpc::ClientContext* context, const ::hardware_manager::PingRequest* request, ::hardware_manager::PingResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::PingResponse>* HardwareManagerService::Stub::PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::hardware_manager::PingRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::PingResponse, ::hardware_manager::PingRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Ping_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::PingResponse>* HardwareManagerService::Stub::AsyncPingRaw(::grpc::ClientContext* context, const ::hardware_manager::PingRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetReady(::grpc::ClientContext* context, const ::hardware_manager::GetReadyRequest& request, ::hardware_manager::GetReadyResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetReadyRequest, ::hardware_manager::GetReadyResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetReady_, context, request, response);
}

void HardwareManagerService::Stub::async::GetReady(::grpc::ClientContext* context, const ::hardware_manager::GetReadyRequest* request, ::hardware_manager::GetReadyResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetReadyRequest, ::hardware_manager::GetReadyResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReady_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetReady(::grpc::ClientContext* context, const ::hardware_manager::GetReadyRequest* request, ::hardware_manager::GetReadyResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReady_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetReadyResponse>* HardwareManagerService::Stub::PrepareAsyncGetReadyRaw(::grpc::ClientContext* context, const ::hardware_manager::GetReadyRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetReadyResponse, ::hardware_manager::GetReadyRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetReady_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetReadyResponse>* HardwareManagerService::Stub::AsyncGetReadyRaw(::grpc::ClientContext* context, const ::hardware_manager::GetReadyRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetReadyRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetNextDistance(::grpc::ClientContext* context, const ::hardware_manager::GetNextDistanceRequest& request, ::hardware_manager::GetNextDistanceResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetNextDistanceRequest, ::hardware_manager::GetNextDistanceResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextDistance_, context, request, response);
}

void HardwareManagerService::Stub::async::GetNextDistance(::grpc::ClientContext* context, const ::hardware_manager::GetNextDistanceRequest* request, ::hardware_manager::GetNextDistanceResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetNextDistanceRequest, ::hardware_manager::GetNextDistanceResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDistance_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetNextDistance(::grpc::ClientContext* context, const ::hardware_manager::GetNextDistanceRequest* request, ::hardware_manager::GetNextDistanceResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDistance_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetNextDistanceResponse>* HardwareManagerService::Stub::PrepareAsyncGetNextDistanceRaw(::grpc::ClientContext* context, const ::hardware_manager::GetNextDistanceRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetNextDistanceResponse, ::hardware_manager::GetNextDistanceRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextDistance_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetNextDistanceResponse>* HardwareManagerService::Stub::AsyncGetNextDistanceRaw(::grpc::ClientContext* context, const ::hardware_manager::GetNextDistanceRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextDistanceRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetNextVelocity(::grpc::ClientContext* context, const ::hardware_manager::GetNextVelocityRequest& request, ::hardware_manager::GetNextVelocityResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetNextVelocityRequest, ::hardware_manager::GetNextVelocityResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextVelocity_, context, request, response);
}

void HardwareManagerService::Stub::async::GetNextVelocity(::grpc::ClientContext* context, const ::hardware_manager::GetNextVelocityRequest* request, ::hardware_manager::GetNextVelocityResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetNextVelocityRequest, ::hardware_manager::GetNextVelocityResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextVelocity_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetNextVelocity(::grpc::ClientContext* context, const ::hardware_manager::GetNextVelocityRequest* request, ::hardware_manager::GetNextVelocityResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextVelocity_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetNextVelocityResponse>* HardwareManagerService::Stub::PrepareAsyncGetNextVelocityRaw(::grpc::ClientContext* context, const ::hardware_manager::GetNextVelocityRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetNextVelocityResponse, ::hardware_manager::GetNextVelocityRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextVelocity_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetNextVelocityResponse>* HardwareManagerService::Stub::AsyncGetNextVelocityRaw(::grpc::ClientContext* context, const ::hardware_manager::GetNextVelocityRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextVelocityRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetRotaryTicks(::grpc::ClientContext* context, const ::hardware_manager::GetRotaryTicksRequest& request, ::hardware_manager::GetRotaryTicksResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetRotaryTicksRequest, ::hardware_manager::GetRotaryTicksResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetRotaryTicks_, context, request, response);
}

void HardwareManagerService::Stub::async::GetRotaryTicks(::grpc::ClientContext* context, const ::hardware_manager::GetRotaryTicksRequest* request, ::hardware_manager::GetRotaryTicksResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetRotaryTicksRequest, ::hardware_manager::GetRotaryTicksResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRotaryTicks_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetRotaryTicks(::grpc::ClientContext* context, const ::hardware_manager::GetRotaryTicksRequest* request, ::hardware_manager::GetRotaryTicksResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRotaryTicks_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetRotaryTicksResponse>* HardwareManagerService::Stub::PrepareAsyncGetRotaryTicksRaw(::grpc::ClientContext* context, const ::hardware_manager::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetRotaryTicksResponse, ::hardware_manager::GetRotaryTicksRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetRotaryTicks_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetRotaryTicksResponse>* HardwareManagerService::Stub::AsyncGetRotaryTicksRaw(::grpc::ClientContext* context, const ::hardware_manager::GetRotaryTicksRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetRotaryTicksRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetDeltaTravelMM(::grpc::ClientContext* context, const ::hardware_manager::GetDeltaTravelMMRequest& request, ::hardware_manager::GetDeltaTravelMMResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetDeltaTravelMMRequest, ::hardware_manager::GetDeltaTravelMMResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDeltaTravelMM_, context, request, response);
}

void HardwareManagerService::Stub::async::GetDeltaTravelMM(::grpc::ClientContext* context, const ::hardware_manager::GetDeltaTravelMMRequest* request, ::hardware_manager::GetDeltaTravelMMResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetDeltaTravelMMRequest, ::hardware_manager::GetDeltaTravelMMResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeltaTravelMM_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetDeltaTravelMM(::grpc::ClientContext* context, const ::hardware_manager::GetDeltaTravelMMRequest* request, ::hardware_manager::GetDeltaTravelMMResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDeltaTravelMM_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetDeltaTravelMMResponse>* HardwareManagerService::Stub::PrepareAsyncGetDeltaTravelMMRaw(::grpc::ClientContext* context, const ::hardware_manager::GetDeltaTravelMMRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetDeltaTravelMMResponse, ::hardware_manager::GetDeltaTravelMMRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDeltaTravelMM_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetDeltaTravelMMResponse>* HardwareManagerService::Stub::AsyncGetDeltaTravelMMRaw(::grpc::ClientContext* context, const ::hardware_manager::GetDeltaTravelMMRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDeltaTravelMMRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetWheelEncoderResolution(::grpc::ClientContext* context, const ::hardware_manager::GetWheelEncoderResolutionRequest& request, ::hardware_manager::GetWheelEncoderResolutionResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetWheelEncoderResolutionRequest, ::hardware_manager::GetWheelEncoderResolutionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetWheelEncoderResolution_, context, request, response);
}

void HardwareManagerService::Stub::async::GetWheelEncoderResolution(::grpc::ClientContext* context, const ::hardware_manager::GetWheelEncoderResolutionRequest* request, ::hardware_manager::GetWheelEncoderResolutionResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetWheelEncoderResolutionRequest, ::hardware_manager::GetWheelEncoderResolutionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetWheelEncoderResolution_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetWheelEncoderResolution(::grpc::ClientContext* context, const ::hardware_manager::GetWheelEncoderResolutionRequest* request, ::hardware_manager::GetWheelEncoderResolutionResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetWheelEncoderResolution_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetWheelEncoderResolutionResponse>* HardwareManagerService::Stub::PrepareAsyncGetWheelEncoderResolutionRaw(::grpc::ClientContext* context, const ::hardware_manager::GetWheelEncoderResolutionRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetWheelEncoderResolutionResponse, ::hardware_manager::GetWheelEncoderResolutionRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetWheelEncoderResolution_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetWheelEncoderResolutionResponse>* HardwareManagerService::Stub::AsyncGetWheelEncoderResolutionRaw(::grpc::ClientContext* context, const ::hardware_manager::GetWheelEncoderResolutionRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetWheelEncoderResolutionRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetSafetyStatus(::grpc::ClientContext* context, const ::hardware_manager::GetSafetyStatusRequest& request, ::hardware_manager::GetSafetyStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetSafetyStatusRequest, ::hardware_manager::GetSafetyStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSafetyStatus_, context, request, response);
}

void HardwareManagerService::Stub::async::GetSafetyStatus(::grpc::ClientContext* context, const ::hardware_manager::GetSafetyStatusRequest* request, ::hardware_manager::GetSafetyStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetSafetyStatusRequest, ::hardware_manager::GetSafetyStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSafetyStatus_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetSafetyStatus(::grpc::ClientContext* context, const ::hardware_manager::GetSafetyStatusRequest* request, ::hardware_manager::GetSafetyStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSafetyStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetSafetyStatusResponse>* HardwareManagerService::Stub::PrepareAsyncGetSafetyStatusRaw(::grpc::ClientContext* context, const ::hardware_manager::GetSafetyStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetSafetyStatusResponse, ::hardware_manager::GetSafetyStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSafetyStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetSafetyStatusResponse>* HardwareManagerService::Stub::AsyncGetSafetyStatusRaw(::grpc::ClientContext* context, const ::hardware_manager::GetSafetyStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSafetyStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetGPSData(::grpc::ClientContext* context, const ::hardware_manager::GetGPSDataRequest& request, ::hardware_manager::GetGPSDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetGPSDataRequest, ::hardware_manager::GetGPSDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetGPSData_, context, request, response);
}

void HardwareManagerService::Stub::async::GetGPSData(::grpc::ClientContext* context, const ::hardware_manager::GetGPSDataRequest* request, ::hardware_manager::GetGPSDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetGPSDataRequest, ::hardware_manager::GetGPSDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetGPSData_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetGPSData(::grpc::ClientContext* context, const ::hardware_manager::GetGPSDataRequest* request, ::hardware_manager::GetGPSDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetGPSData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetGPSDataResponse>* HardwareManagerService::Stub::PrepareAsyncGetGPSDataRaw(::grpc::ClientContext* context, const ::hardware_manager::GetGPSDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetGPSDataResponse, ::hardware_manager::GetGPSDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetGPSData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetGPSDataResponse>* HardwareManagerService::Stub::AsyncGetGPSDataRaw(::grpc::ClientContext* context, const ::hardware_manager::GetGPSDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetGPSDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetNextGPSData(::grpc::ClientContext* context, const ::hardware_manager::GetNextGPSDataRequest& request, ::hardware_manager::GetNextGPSDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetNextGPSDataRequest, ::hardware_manager::GetNextGPSDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextGPSData_, context, request, response);
}

void HardwareManagerService::Stub::async::GetNextGPSData(::grpc::ClientContext* context, const ::hardware_manager::GetNextGPSDataRequest* request, ::hardware_manager::GetNextGPSDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetNextGPSDataRequest, ::hardware_manager::GetNextGPSDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextGPSData_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetNextGPSData(::grpc::ClientContext* context, const ::hardware_manager::GetNextGPSDataRequest* request, ::hardware_manager::GetNextGPSDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextGPSData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetNextGPSDataResponse>* HardwareManagerService::Stub::PrepareAsyncGetNextGPSDataRaw(::grpc::ClientContext* context, const ::hardware_manager::GetNextGPSDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetNextGPSDataResponse, ::hardware_manager::GetNextGPSDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextGPSData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetNextGPSDataResponse>* HardwareManagerService::Stub::AsyncGetNextGPSDataRaw(::grpc::ClientContext* context, const ::hardware_manager::GetNextGPSDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextGPSDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetNextRawGPSData(::grpc::ClientContext* context, const ::hardware_manager::GetNextRawGPSDataRequest& request, ::hardware_manager::GetNextRawGPSDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetNextRawGPSDataRequest, ::hardware_manager::GetNextRawGPSDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextRawGPSData_, context, request, response);
}

void HardwareManagerService::Stub::async::GetNextRawGPSData(::grpc::ClientContext* context, const ::hardware_manager::GetNextRawGPSDataRequest* request, ::hardware_manager::GetNextRawGPSDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetNextRawGPSDataRequest, ::hardware_manager::GetNextRawGPSDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextRawGPSData_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetNextRawGPSData(::grpc::ClientContext* context, const ::hardware_manager::GetNextRawGPSDataRequest* request, ::hardware_manager::GetNextRawGPSDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextRawGPSData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetNextRawGPSDataResponse>* HardwareManagerService::Stub::PrepareAsyncGetNextRawGPSDataRaw(::grpc::ClientContext* context, const ::hardware_manager::GetNextRawGPSDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetNextRawGPSDataResponse, ::hardware_manager::GetNextRawGPSDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextRawGPSData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetNextRawGPSDataResponse>* HardwareManagerService::Stub::AsyncGetNextRawGPSDataRaw(::grpc::ClientContext* context, const ::hardware_manager::GetNextRawGPSDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextRawGPSDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetGPSFixedPos(::grpc::ClientContext* context, const ::hardware_manager::GetGPSFixedPosRequest& request, ::hardware_manager::GetGPSFixedPosResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetGPSFixedPosRequest, ::hardware_manager::GetGPSFixedPosResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetGPSFixedPos_, context, request, response);
}

void HardwareManagerService::Stub::async::GetGPSFixedPos(::grpc::ClientContext* context, const ::hardware_manager::GetGPSFixedPosRequest* request, ::hardware_manager::GetGPSFixedPosResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetGPSFixedPosRequest, ::hardware_manager::GetGPSFixedPosResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetGPSFixedPos_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetGPSFixedPos(::grpc::ClientContext* context, const ::hardware_manager::GetGPSFixedPosRequest* request, ::hardware_manager::GetGPSFixedPosResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetGPSFixedPos_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetGPSFixedPosResponse>* HardwareManagerService::Stub::PrepareAsyncGetGPSFixedPosRaw(::grpc::ClientContext* context, const ::hardware_manager::GetGPSFixedPosRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetGPSFixedPosResponse, ::hardware_manager::GetGPSFixedPosRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetGPSFixedPos_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetGPSFixedPosResponse>* HardwareManagerService::Stub::AsyncGetGPSFixedPosRaw(::grpc::ClientContext* context, const ::hardware_manager::GetGPSFixedPosRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetGPSFixedPosRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetStrobeSettings(::grpc::ClientContext* context, const ::hardware_manager::StrobeSettings& request, ::hardware_manager::SetStrobeSettingsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::StrobeSettings, ::hardware_manager::SetStrobeSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetStrobeSettings_, context, request, response);
}

void HardwareManagerService::Stub::async::SetStrobeSettings(::grpc::ClientContext* context, const ::hardware_manager::StrobeSettings* request, ::hardware_manager::SetStrobeSettingsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::StrobeSettings, ::hardware_manager::SetStrobeSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetStrobeSettings_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetStrobeSettings(::grpc::ClientContext* context, const ::hardware_manager::StrobeSettings* request, ::hardware_manager::SetStrobeSettingsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetStrobeSettings_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetStrobeSettingsResponse>* HardwareManagerService::Stub::PrepareAsyncSetStrobeSettingsRaw(::grpc::ClientContext* context, const ::hardware_manager::StrobeSettings& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetStrobeSettingsResponse, ::hardware_manager::StrobeSettings, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetStrobeSettings_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetStrobeSettingsResponse>* HardwareManagerService::Stub::AsyncSetStrobeSettingsRaw(::grpc::ClientContext* context, const ::hardware_manager::StrobeSettings& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetStrobeSettingsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetStrobeSettings(::grpc::ClientContext* context, const ::hardware_manager::GetStrobeSettingsRequest& request, ::hardware_manager::StrobeSettings* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetStrobeSettingsRequest, ::hardware_manager::StrobeSettings, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetStrobeSettings_, context, request, response);
}

void HardwareManagerService::Stub::async::GetStrobeSettings(::grpc::ClientContext* context, const ::hardware_manager::GetStrobeSettingsRequest* request, ::hardware_manager::StrobeSettings* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetStrobeSettingsRequest, ::hardware_manager::StrobeSettings, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetStrobeSettings_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetStrobeSettings(::grpc::ClientContext* context, const ::hardware_manager::GetStrobeSettingsRequest* request, ::hardware_manager::StrobeSettings* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetStrobeSettings_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::StrobeSettings>* HardwareManagerService::Stub::PrepareAsyncGetStrobeSettingsRaw(::grpc::ClientContext* context, const ::hardware_manager::GetStrobeSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::StrobeSettings, ::hardware_manager::GetStrobeSettingsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetStrobeSettings_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::StrobeSettings>* HardwareManagerService::Stub::AsyncGetStrobeSettingsRaw(::grpc::ClientContext* context, const ::hardware_manager::GetStrobeSettingsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetStrobeSettingsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetManagedBoardErrors(::grpc::ClientContext* context, const ::hardware_manager::GetManagedBoardErrorsRequest& request, ::hardware_manager::GetManagedBoardErrorsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetManagedBoardErrorsRequest, ::hardware_manager::GetManagedBoardErrorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetManagedBoardErrors_, context, request, response);
}

void HardwareManagerService::Stub::async::GetManagedBoardErrors(::grpc::ClientContext* context, const ::hardware_manager::GetManagedBoardErrorsRequest* request, ::hardware_manager::GetManagedBoardErrorsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetManagedBoardErrorsRequest, ::hardware_manager::GetManagedBoardErrorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetManagedBoardErrors_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetManagedBoardErrors(::grpc::ClientContext* context, const ::hardware_manager::GetManagedBoardErrorsRequest* request, ::hardware_manager::GetManagedBoardErrorsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetManagedBoardErrors_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetManagedBoardErrorsResponse>* HardwareManagerService::Stub::PrepareAsyncGetManagedBoardErrorsRaw(::grpc::ClientContext* context, const ::hardware_manager::GetManagedBoardErrorsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetManagedBoardErrorsResponse, ::hardware_manager::GetManagedBoardErrorsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetManagedBoardErrors_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetManagedBoardErrorsResponse>* HardwareManagerService::Stub::AsyncGetManagedBoardErrorsRaw(::grpc::ClientContext* context, const ::hardware_manager::GetManagedBoardErrorsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetManagedBoardErrorsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetSupervisoryStatus(::grpc::ClientContext* context, const ::hardware_manager::GetSupervisoryStatusRequest& request, ::hardware_manager::GetSupervisoryStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetSupervisoryStatusRequest, ::hardware_manager::GetSupervisoryStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSupervisoryStatus_, context, request, response);
}

void HardwareManagerService::Stub::async::GetSupervisoryStatus(::grpc::ClientContext* context, const ::hardware_manager::GetSupervisoryStatusRequest* request, ::hardware_manager::GetSupervisoryStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetSupervisoryStatusRequest, ::hardware_manager::GetSupervisoryStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSupervisoryStatus_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetSupervisoryStatus(::grpc::ClientContext* context, const ::hardware_manager::GetSupervisoryStatusRequest* request, ::hardware_manager::GetSupervisoryStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSupervisoryStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetSupervisoryStatusResponse>* HardwareManagerService::Stub::PrepareAsyncGetSupervisoryStatusRaw(::grpc::ClientContext* context, const ::hardware_manager::GetSupervisoryStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetSupervisoryStatusResponse, ::hardware_manager::GetSupervisoryStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSupervisoryStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetSupervisoryStatusResponse>* HardwareManagerService::Stub::AsyncGetSupervisoryStatusRaw(::grpc::ClientContext* context, const ::hardware_manager::GetSupervisoryStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSupervisoryStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetReaperSupervisoryStatus(::grpc::ClientContext* context, const ::hardware_manager::GetReaperSupervisoryStatusRequest& request, ::hardware_manager::ReaperCenterEnclosureData* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetReaperSupervisoryStatusRequest, ::hardware_manager::ReaperCenterEnclosureData, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetReaperSupervisoryStatus_, context, request, response);
}

void HardwareManagerService::Stub::async::GetReaperSupervisoryStatus(::grpc::ClientContext* context, const ::hardware_manager::GetReaperSupervisoryStatusRequest* request, ::hardware_manager::ReaperCenterEnclosureData* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetReaperSupervisoryStatusRequest, ::hardware_manager::ReaperCenterEnclosureData, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReaperSupervisoryStatus_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetReaperSupervisoryStatus(::grpc::ClientContext* context, const ::hardware_manager::GetReaperSupervisoryStatusRequest* request, ::hardware_manager::ReaperCenterEnclosureData* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReaperSupervisoryStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperCenterEnclosureData>* HardwareManagerService::Stub::PrepareAsyncGetReaperSupervisoryStatusRaw(::grpc::ClientContext* context, const ::hardware_manager::GetReaperSupervisoryStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::ReaperCenterEnclosureData, ::hardware_manager::GetReaperSupervisoryStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetReaperSupervisoryStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::ReaperCenterEnclosureData>* HardwareManagerService::Stub::AsyncGetReaperSupervisoryStatusRaw(::grpc::ClientContext* context, const ::hardware_manager::GetReaperSupervisoryStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetReaperSupervisoryStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetServerDisable(::grpc::ClientContext* context, const ::hardware_manager::SetServerDisableRequest& request, ::hardware_manager::SetServerDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetServerDisableRequest, ::hardware_manager::SetServerDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetServerDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetServerDisable(::grpc::ClientContext* context, const ::hardware_manager::SetServerDisableRequest* request, ::hardware_manager::SetServerDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetServerDisableRequest, ::hardware_manager::SetServerDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetServerDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetServerDisable(::grpc::ClientContext* context, const ::hardware_manager::SetServerDisableRequest* request, ::hardware_manager::SetServerDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetServerDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetServerDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetServerDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetServerDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetServerDisableResponse, ::hardware_manager::SetServerDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetServerDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetServerDisableResponse>* HardwareManagerService::Stub::AsyncSetServerDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetServerDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetServerDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetBTLDisable(::grpc::ClientContext* context, const ::hardware_manager::SetBTLDisableRequest& request, ::hardware_manager::SetBTLDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetBTLDisableRequest, ::hardware_manager::SetBTLDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetBTLDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetBTLDisable(::grpc::ClientContext* context, const ::hardware_manager::SetBTLDisableRequest* request, ::hardware_manager::SetBTLDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetBTLDisableRequest, ::hardware_manager::SetBTLDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetBTLDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetBTLDisable(::grpc::ClientContext* context, const ::hardware_manager::SetBTLDisableRequest* request, ::hardware_manager::SetBTLDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetBTLDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetBTLDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetBTLDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetBTLDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetBTLDisableResponse, ::hardware_manager::SetBTLDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetBTLDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetBTLDisableResponse>* HardwareManagerService::Stub::AsyncSetBTLDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetBTLDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetBTLDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetScannersDisable(::grpc::ClientContext* context, const ::hardware_manager::SetScannersDisableRequest& request, ::hardware_manager::SetScannersDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetScannersDisableRequest, ::hardware_manager::SetScannersDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetScannersDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetScannersDisable(::grpc::ClientContext* context, const ::hardware_manager::SetScannersDisableRequest* request, ::hardware_manager::SetScannersDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetScannersDisableRequest, ::hardware_manager::SetScannersDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetScannersDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetScannersDisable(::grpc::ClientContext* context, const ::hardware_manager::SetScannersDisableRequest* request, ::hardware_manager::SetScannersDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetScannersDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetScannersDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetScannersDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetScannersDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetScannersDisableResponse, ::hardware_manager::SetScannersDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetScannersDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetScannersDisableResponse>* HardwareManagerService::Stub::AsyncSetScannersDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetScannersDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetScannersDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetWheelEncoderBoardDisable(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderBoardDisableRequest& request, ::hardware_manager::SetWheelEncoderBoardDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetWheelEncoderBoardDisableRequest, ::hardware_manager::SetWheelEncoderBoardDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetWheelEncoderBoardDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetWheelEncoderBoardDisable(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderBoardDisableRequest* request, ::hardware_manager::SetWheelEncoderBoardDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetWheelEncoderBoardDisableRequest, ::hardware_manager::SetWheelEncoderBoardDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetWheelEncoderBoardDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetWheelEncoderBoardDisable(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderBoardDisableRequest* request, ::hardware_manager::SetWheelEncoderBoardDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetWheelEncoderBoardDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetWheelEncoderBoardDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetWheelEncoderBoardDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderBoardDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetWheelEncoderBoardDisableResponse, ::hardware_manager::SetWheelEncoderBoardDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetWheelEncoderBoardDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetWheelEncoderBoardDisableResponse>* HardwareManagerService::Stub::AsyncSetWheelEncoderBoardDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderBoardDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetWheelEncoderBoardDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetWheelEncoderDisable(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderDisableRequest& request, ::hardware_manager::SetWheelEncoderDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetWheelEncoderDisableRequest, ::hardware_manager::SetWheelEncoderDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetWheelEncoderDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetWheelEncoderDisable(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderDisableRequest* request, ::hardware_manager::SetWheelEncoderDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetWheelEncoderDisableRequest, ::hardware_manager::SetWheelEncoderDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetWheelEncoderDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetWheelEncoderDisable(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderDisableRequest* request, ::hardware_manager::SetWheelEncoderDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetWheelEncoderDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetWheelEncoderDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetWheelEncoderDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetWheelEncoderDisableResponse, ::hardware_manager::SetWheelEncoderDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetWheelEncoderDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetWheelEncoderDisableResponse>* HardwareManagerService::Stub::AsyncSetWheelEncoderDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetWheelEncoderDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetWheelEncoderDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetGPSDisable(::grpc::ClientContext* context, const ::hardware_manager::SetGPSDisableRequest& request, ::hardware_manager::SetGPSDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetGPSDisableRequest, ::hardware_manager::SetGPSDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetGPSDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetGPSDisable(::grpc::ClientContext* context, const ::hardware_manager::SetGPSDisableRequest* request, ::hardware_manager::SetGPSDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetGPSDisableRequest, ::hardware_manager::SetGPSDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetGPSDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetGPSDisable(::grpc::ClientContext* context, const ::hardware_manager::SetGPSDisableRequest* request, ::hardware_manager::SetGPSDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetGPSDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetGPSDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetGPSDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetGPSDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetGPSDisableResponse, ::hardware_manager::SetGPSDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetGPSDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetGPSDisableResponse>* HardwareManagerService::Stub::AsyncSetGPSDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetGPSDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetGPSDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SuicideSwitch(::grpc::ClientContext* context, const ::hardware_manager::SuicideSwitchRequest& request, ::hardware_manager::SuicideSwitchResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SuicideSwitchRequest, ::hardware_manager::SuicideSwitchResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SuicideSwitch_, context, request, response);
}

void HardwareManagerService::Stub::async::SuicideSwitch(::grpc::ClientContext* context, const ::hardware_manager::SuicideSwitchRequest* request, ::hardware_manager::SuicideSwitchResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SuicideSwitchRequest, ::hardware_manager::SuicideSwitchResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SuicideSwitch_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SuicideSwitch(::grpc::ClientContext* context, const ::hardware_manager::SuicideSwitchRequest* request, ::hardware_manager::SuicideSwitchResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SuicideSwitch_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SuicideSwitchResponse>* HardwareManagerService::Stub::PrepareAsyncSuicideSwitchRaw(::grpc::ClientContext* context, const ::hardware_manager::SuicideSwitchRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SuicideSwitchResponse, ::hardware_manager::SuicideSwitchRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SuicideSwitch_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SuicideSwitchResponse>* HardwareManagerService::Stub::AsyncSuicideSwitchRaw(::grpc::ClientContext* context, const ::hardware_manager::SuicideSwitchRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSuicideSwitchRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::CommandComputerPowerCycle(::grpc::ClientContext* context, const ::hardware_manager::CommandComputerPowerCycleRequest& request, ::hardware_manager::CommandComputerPowerCycleResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::CommandComputerPowerCycleRequest, ::hardware_manager::CommandComputerPowerCycleResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CommandComputerPowerCycle_, context, request, response);
}

void HardwareManagerService::Stub::async::CommandComputerPowerCycle(::grpc::ClientContext* context, const ::hardware_manager::CommandComputerPowerCycleRequest* request, ::hardware_manager::CommandComputerPowerCycleResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::CommandComputerPowerCycleRequest, ::hardware_manager::CommandComputerPowerCycleResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CommandComputerPowerCycle_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::CommandComputerPowerCycle(::grpc::ClientContext* context, const ::hardware_manager::CommandComputerPowerCycleRequest* request, ::hardware_manager::CommandComputerPowerCycleResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CommandComputerPowerCycle_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::CommandComputerPowerCycleResponse>* HardwareManagerService::Stub::PrepareAsyncCommandComputerPowerCycleRaw(::grpc::ClientContext* context, const ::hardware_manager::CommandComputerPowerCycleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::CommandComputerPowerCycleResponse, ::hardware_manager::CommandComputerPowerCycleRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CommandComputerPowerCycle_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::CommandComputerPowerCycleResponse>* HardwareManagerService::Stub::AsyncCommandComputerPowerCycleRaw(::grpc::ClientContext* context, const ::hardware_manager::CommandComputerPowerCycleRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCommandComputerPowerCycleRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetMainContactorDisable(::grpc::ClientContext* context, const ::hardware_manager::SetMainContactorDisableRequest& request, ::hardware_manager::SetMainContactorDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetMainContactorDisableRequest, ::hardware_manager::SetMainContactorDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetMainContactorDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetMainContactorDisable(::grpc::ClientContext* context, const ::hardware_manager::SetMainContactorDisableRequest* request, ::hardware_manager::SetMainContactorDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetMainContactorDisableRequest, ::hardware_manager::SetMainContactorDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetMainContactorDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetMainContactorDisable(::grpc::ClientContext* context, const ::hardware_manager::SetMainContactorDisableRequest* request, ::hardware_manager::SetMainContactorDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetMainContactorDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetMainContactorDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetMainContactorDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetMainContactorDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetMainContactorDisableResponse, ::hardware_manager::SetMainContactorDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetMainContactorDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetMainContactorDisableResponse>* HardwareManagerService::Stub::AsyncSetMainContactorDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetMainContactorDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetMainContactorDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetStrobeDisable(::grpc::ClientContext* context, const ::hardware_manager::SetStrobeDisableRequest& request, ::hardware_manager::SetStrobeDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetStrobeDisableRequest, ::hardware_manager::SetStrobeDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetStrobeDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetStrobeDisable(::grpc::ClientContext* context, const ::hardware_manager::SetStrobeDisableRequest* request, ::hardware_manager::SetStrobeDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetStrobeDisableRequest, ::hardware_manager::SetStrobeDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetStrobeDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetStrobeDisable(::grpc::ClientContext* context, const ::hardware_manager::SetStrobeDisableRequest* request, ::hardware_manager::SetStrobeDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetStrobeDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetStrobeDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetStrobeDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetStrobeDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetStrobeDisableResponse, ::hardware_manager::SetStrobeDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetStrobeDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetStrobeDisableResponse>* HardwareManagerService::Stub::AsyncSetStrobeDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetStrobeDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetStrobeDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetAirConditionerDisable(::grpc::ClientContext* context, const ::hardware_manager::SetAirConditionerDisableRequest& request, ::hardware_manager::SetAirConditionerDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetAirConditionerDisableRequest, ::hardware_manager::SetAirConditionerDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetAirConditionerDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetAirConditionerDisable(::grpc::ClientContext* context, const ::hardware_manager::SetAirConditionerDisableRequest* request, ::hardware_manager::SetAirConditionerDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetAirConditionerDisableRequest, ::hardware_manager::SetAirConditionerDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetAirConditionerDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetAirConditionerDisable(::grpc::ClientContext* context, const ::hardware_manager::SetAirConditionerDisableRequest* request, ::hardware_manager::SetAirConditionerDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetAirConditionerDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetAirConditionerDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetAirConditionerDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetAirConditionerDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetAirConditionerDisableResponse, ::hardware_manager::SetAirConditionerDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetAirConditionerDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetAirConditionerDisableResponse>* HardwareManagerService::Stub::AsyncSetAirConditionerDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetAirConditionerDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetAirConditionerDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetChillerDisable(::grpc::ClientContext* context, const ::hardware_manager::SetChillerDisableRequest& request, ::hardware_manager::SetChillerDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetChillerDisableRequest, ::hardware_manager::SetChillerDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetChillerDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetChillerDisable(::grpc::ClientContext* context, const ::hardware_manager::SetChillerDisableRequest* request, ::hardware_manager::SetChillerDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetChillerDisableRequest, ::hardware_manager::SetChillerDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetChillerDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetChillerDisable(::grpc::ClientContext* context, const ::hardware_manager::SetChillerDisableRequest* request, ::hardware_manager::SetChillerDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetChillerDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetChillerDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetChillerDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetChillerDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetChillerDisableResponse, ::hardware_manager::SetChillerDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetChillerDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetChillerDisableResponse>* HardwareManagerService::Stub::AsyncSetChillerDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetChillerDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetChillerDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetTempBypassDisable(::grpc::ClientContext* context, const ::hardware_manager::SetTempBypassDisableRequest& request, ::hardware_manager::SetTempBypassDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetTempBypassDisableRequest, ::hardware_manager::SetTempBypassDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetTempBypassDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetTempBypassDisable(::grpc::ClientContext* context, const ::hardware_manager::SetTempBypassDisableRequest* request, ::hardware_manager::SetTempBypassDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetTempBypassDisableRequest, ::hardware_manager::SetTempBypassDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTempBypassDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetTempBypassDisable(::grpc::ClientContext* context, const ::hardware_manager::SetTempBypassDisableRequest* request, ::hardware_manager::SetTempBypassDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTempBypassDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetTempBypassDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetTempBypassDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetTempBypassDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetTempBypassDisableResponse, ::hardware_manager::SetTempBypassDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetTempBypassDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetTempBypassDisableResponse>* HardwareManagerService::Stub::AsyncSetTempBypassDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetTempBypassDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetTempBypassDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetHumidityBypassDisable(::grpc::ClientContext* context, const ::hardware_manager::SetHumidityBypassDisableRequest& request, ::hardware_manager::SetHumidityBypassDisableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetHumidityBypassDisableRequest, ::hardware_manager::SetHumidityBypassDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetHumidityBypassDisable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetHumidityBypassDisable(::grpc::ClientContext* context, const ::hardware_manager::SetHumidityBypassDisableRequest* request, ::hardware_manager::SetHumidityBypassDisableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetHumidityBypassDisableRequest, ::hardware_manager::SetHumidityBypassDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetHumidityBypassDisable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetHumidityBypassDisable(::grpc::ClientContext* context, const ::hardware_manager::SetHumidityBypassDisableRequest* request, ::hardware_manager::SetHumidityBypassDisableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetHumidityBypassDisable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetHumidityBypassDisableResponse>* HardwareManagerService::Stub::PrepareAsyncSetHumidityBypassDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetHumidityBypassDisableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetHumidityBypassDisableResponse, ::hardware_manager::SetHumidityBypassDisableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetHumidityBypassDisable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetHumidityBypassDisableResponse>* HardwareManagerService::Stub::AsyncSetHumidityBypassDisableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetHumidityBypassDisableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetHumidityBypassDisableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::Get240vUptime(::grpc::ClientContext* context, const ::hardware_manager::Get240vUptimeRequest& request, ::hardware_manager::Get240vUptimeResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::Get240vUptimeRequest, ::hardware_manager::Get240vUptimeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Get240vUptime_, context, request, response);
}

void HardwareManagerService::Stub::async::Get240vUptime(::grpc::ClientContext* context, const ::hardware_manager::Get240vUptimeRequest* request, ::hardware_manager::Get240vUptimeResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::Get240vUptimeRequest, ::hardware_manager::Get240vUptimeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Get240vUptime_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::Get240vUptime(::grpc::ClientContext* context, const ::hardware_manager::Get240vUptimeRequest* request, ::hardware_manager::Get240vUptimeResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Get240vUptime_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::Get240vUptimeResponse>* HardwareManagerService::Stub::PrepareAsyncGet240vUptimeRaw(::grpc::ClientContext* context, const ::hardware_manager::Get240vUptimeRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::Get240vUptimeResponse, ::hardware_manager::Get240vUptimeRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Get240vUptime_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::Get240vUptimeResponse>* HardwareManagerService::Stub::AsyncGet240vUptimeRaw(::grpc::ClientContext* context, const ::hardware_manager::Get240vUptimeRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGet240vUptimeRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetRuntime(::grpc::ClientContext* context, const ::hardware_manager::GetRuntimeRequest& request, ::hardware_manager::GetRuntimeResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetRuntimeRequest, ::hardware_manager::GetRuntimeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetRuntime_, context, request, response);
}

void HardwareManagerService::Stub::async::GetRuntime(::grpc::ClientContext* context, const ::hardware_manager::GetRuntimeRequest* request, ::hardware_manager::GetRuntimeResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetRuntimeRequest, ::hardware_manager::GetRuntimeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRuntime_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetRuntime(::grpc::ClientContext* context, const ::hardware_manager::GetRuntimeRequest* request, ::hardware_manager::GetRuntimeResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRuntime_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetRuntimeResponse>* HardwareManagerService::Stub::PrepareAsyncGetRuntimeRaw(::grpc::ClientContext* context, const ::hardware_manager::GetRuntimeRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetRuntimeResponse, ::hardware_manager::GetRuntimeRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetRuntime_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetRuntimeResponse>* HardwareManagerService::Stub::AsyncGetRuntimeRaw(::grpc::ClientContext* context, const ::hardware_manager::GetRuntimeRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetRuntimeRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetAvailableUSBStorage(::grpc::ClientContext* context, const ::hardware_manager::GetAvailableUSBStorageRequest& request, ::hardware_manager::GetAvailableUSBStorageResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetAvailableUSBStorageRequest, ::hardware_manager::GetAvailableUSBStorageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetAvailableUSBStorage_, context, request, response);
}

void HardwareManagerService::Stub::async::GetAvailableUSBStorage(::grpc::ClientContext* context, const ::hardware_manager::GetAvailableUSBStorageRequest* request, ::hardware_manager::GetAvailableUSBStorageResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetAvailableUSBStorageRequest, ::hardware_manager::GetAvailableUSBStorageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetAvailableUSBStorage_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetAvailableUSBStorage(::grpc::ClientContext* context, const ::hardware_manager::GetAvailableUSBStorageRequest* request, ::hardware_manager::GetAvailableUSBStorageResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetAvailableUSBStorage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetAvailableUSBStorageResponse>* HardwareManagerService::Stub::PrepareAsyncGetAvailableUSBStorageRaw(::grpc::ClientContext* context, const ::hardware_manager::GetAvailableUSBStorageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetAvailableUSBStorageResponse, ::hardware_manager::GetAvailableUSBStorageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetAvailableUSBStorage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetAvailableUSBStorageResponse>* HardwareManagerService::Stub::AsyncGetAvailableUSBStorageRaw(::grpc::ClientContext* context, const ::hardware_manager::GetAvailableUSBStorageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetAvailableUSBStorageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetJimboxSpeed(::grpc::ClientContext* context, const ::hardware_manager::SetJimboxSpeedRequest& request, ::hardware_manager::SetJimboxSpeedResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetJimboxSpeedRequest, ::hardware_manager::SetJimboxSpeedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetJimboxSpeed_, context, request, response);
}

void HardwareManagerService::Stub::async::SetJimboxSpeed(::grpc::ClientContext* context, const ::hardware_manager::SetJimboxSpeedRequest* request, ::hardware_manager::SetJimboxSpeedResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetJimboxSpeedRequest, ::hardware_manager::SetJimboxSpeedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetJimboxSpeed_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetJimboxSpeed(::grpc::ClientContext* context, const ::hardware_manager::SetJimboxSpeedRequest* request, ::hardware_manager::SetJimboxSpeedResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetJimboxSpeed_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetJimboxSpeedResponse>* HardwareManagerService::Stub::PrepareAsyncSetJimboxSpeedRaw(::grpc::ClientContext* context, const ::hardware_manager::SetJimboxSpeedRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetJimboxSpeedResponse, ::hardware_manager::SetJimboxSpeedRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetJimboxSpeed_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetJimboxSpeedResponse>* HardwareManagerService::Stub::AsyncSetJimboxSpeedRaw(::grpc::ClientContext* context, const ::hardware_manager::SetJimboxSpeedRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetJimboxSpeedRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetCruiseEnabled(::grpc::ClientContext* context, const ::hardware_manager::SetCruiseEnabledRequest& request, ::hardware_manager::SetCruiseEnabledResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetCruiseEnabledRequest, ::hardware_manager::SetCruiseEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetCruiseEnabled_, context, request, response);
}

void HardwareManagerService::Stub::async::SetCruiseEnabled(::grpc::ClientContext* context, const ::hardware_manager::SetCruiseEnabledRequest* request, ::hardware_manager::SetCruiseEnabledResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetCruiseEnabledRequest, ::hardware_manager::SetCruiseEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCruiseEnabled_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetCruiseEnabled(::grpc::ClientContext* context, const ::hardware_manager::SetCruiseEnabledRequest* request, ::hardware_manager::SetCruiseEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCruiseEnabled_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetCruiseEnabledResponse>* HardwareManagerService::Stub::PrepareAsyncSetCruiseEnabledRaw(::grpc::ClientContext* context, const ::hardware_manager::SetCruiseEnabledRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetCruiseEnabledResponse, ::hardware_manager::SetCruiseEnabledRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetCruiseEnabled_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetCruiseEnabledResponse>* HardwareManagerService::Stub::AsyncSetCruiseEnabledRaw(::grpc::ClientContext* context, const ::hardware_manager::SetCruiseEnabledRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetCruiseEnabledRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetCruiseStatus(::grpc::ClientContext* context, const ::hardware_manager::GetCruiseStatusRequest& request, ::hardware_manager::GetCruiseStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetCruiseStatusRequest, ::hardware_manager::GetCruiseStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCruiseStatus_, context, request, response);
}

void HardwareManagerService::Stub::async::GetCruiseStatus(::grpc::ClientContext* context, const ::hardware_manager::GetCruiseStatusRequest* request, ::hardware_manager::GetCruiseStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetCruiseStatusRequest, ::hardware_manager::GetCruiseStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCruiseStatus_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetCruiseStatus(::grpc::ClientContext* context, const ::hardware_manager::GetCruiseStatusRequest* request, ::hardware_manager::GetCruiseStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCruiseStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetCruiseStatusResponse>* HardwareManagerService::Stub::PrepareAsyncGetCruiseStatusRaw(::grpc::ClientContext* context, const ::hardware_manager::GetCruiseStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetCruiseStatusResponse, ::hardware_manager::GetCruiseStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCruiseStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetCruiseStatusResponse>* HardwareManagerService::Stub::AsyncGetCruiseStatusRaw(::grpc::ClientContext* context, const ::hardware_manager::GetCruiseStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCruiseStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetImplementStateOnTractor(::grpc::ClientContext* context, const ::hardware_manager::SetImplementStateRequest& request, ::hardware_manager::SetImplementStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetImplementStateRequest, ::hardware_manager::SetImplementStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetImplementStateOnTractor_, context, request, response);
}

void HardwareManagerService::Stub::async::SetImplementStateOnTractor(::grpc::ClientContext* context, const ::hardware_manager::SetImplementStateRequest* request, ::hardware_manager::SetImplementStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetImplementStateRequest, ::hardware_manager::SetImplementStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetImplementStateOnTractor_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetImplementStateOnTractor(::grpc::ClientContext* context, const ::hardware_manager::SetImplementStateRequest* request, ::hardware_manager::SetImplementStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetImplementStateOnTractor_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetImplementStateResponse>* HardwareManagerService::Stub::PrepareAsyncSetImplementStateOnTractorRaw(::grpc::ClientContext* context, const ::hardware_manager::SetImplementStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetImplementStateResponse, ::hardware_manager::SetImplementStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetImplementStateOnTractor_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetImplementStateResponse>* HardwareManagerService::Stub::AsyncSetImplementStateOnTractorRaw(::grpc::ClientContext* context, const ::hardware_manager::SetImplementStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetImplementStateOnTractorRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetSafeStateEnforcement(::grpc::ClientContext* context, const ::hardware_manager::SetSafeStateEnforcementRequest& request, ::hardware_manager::SetSafeStateEnforcementResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetSafeStateEnforcementRequest, ::hardware_manager::SetSafeStateEnforcementResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetSafeStateEnforcement_, context, request, response);
}

void HardwareManagerService::Stub::async::SetSafeStateEnforcement(::grpc::ClientContext* context, const ::hardware_manager::SetSafeStateEnforcementRequest* request, ::hardware_manager::SetSafeStateEnforcementResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetSafeStateEnforcementRequest, ::hardware_manager::SetSafeStateEnforcementResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetSafeStateEnforcement_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetSafeStateEnforcement(::grpc::ClientContext* context, const ::hardware_manager::SetSafeStateEnforcementRequest* request, ::hardware_manager::SetSafeStateEnforcementResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetSafeStateEnforcement_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetSafeStateEnforcementResponse>* HardwareManagerService::Stub::PrepareAsyncSetSafeStateEnforcementRaw(::grpc::ClientContext* context, const ::hardware_manager::SetSafeStateEnforcementRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetSafeStateEnforcementResponse, ::hardware_manager::SetSafeStateEnforcementRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetSafeStateEnforcement_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetSafeStateEnforcementResponse>* HardwareManagerService::Stub::AsyncSetSafeStateEnforcementRaw(::grpc::ClientContext* context, const ::hardware_manager::SetSafeStateEnforcementRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetSafeStateEnforcementRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetTractorSafetyState(::grpc::ClientContext* context, const ::hardware_manager::GetTractorSafetyStateRequest& request, ::hardware_manager::GetTractorSafetyStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetTractorSafetyStateRequest, ::hardware_manager::GetTractorSafetyStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTractorSafetyState_, context, request, response);
}

void HardwareManagerService::Stub::async::GetTractorSafetyState(::grpc::ClientContext* context, const ::hardware_manager::GetTractorSafetyStateRequest* request, ::hardware_manager::GetTractorSafetyStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetTractorSafetyStateRequest, ::hardware_manager::GetTractorSafetyStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTractorSafetyState_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetTractorSafetyState(::grpc::ClientContext* context, const ::hardware_manager::GetTractorSafetyStateRequest* request, ::hardware_manager::GetTractorSafetyStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTractorSafetyState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetTractorSafetyStateResponse>* HardwareManagerService::Stub::PrepareAsyncGetTractorSafetyStateRaw(::grpc::ClientContext* context, const ::hardware_manager::GetTractorSafetyStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetTractorSafetyStateResponse, ::hardware_manager::GetTractorSafetyStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTractorSafetyState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetTractorSafetyStateResponse>* HardwareManagerService::Stub::AsyncGetTractorSafetyStateRaw(::grpc::ClientContext* context, const ::hardware_manager::GetTractorSafetyStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTractorSafetyStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetTractorIFState(::grpc::ClientContext* context, const ::hardware_manager::GetTractorIFStateRequest& request, ::hardware_manager::GetTractorIFStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetTractorIFStateRequest, ::hardware_manager::GetTractorIFStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTractorIFState_, context, request, response);
}

void HardwareManagerService::Stub::async::GetTractorIFState(::grpc::ClientContext* context, const ::hardware_manager::GetTractorIFStateRequest* request, ::hardware_manager::GetTractorIFStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetTractorIFStateRequest, ::hardware_manager::GetTractorIFStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTractorIFState_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetTractorIFState(::grpc::ClientContext* context, const ::hardware_manager::GetTractorIFStateRequest* request, ::hardware_manager::GetTractorIFStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTractorIFState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetTractorIFStateResponse>* HardwareManagerService::Stub::PrepareAsyncGetTractorIFStateRaw(::grpc::ClientContext* context, const ::hardware_manager::GetTractorIFStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetTractorIFStateResponse, ::hardware_manager::GetTractorIFStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTractorIFState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetTractorIFStateResponse>* HardwareManagerService::Stub::AsyncGetTractorIFStateRaw(::grpc::ClientContext* context, const ::hardware_manager::GetTractorIFStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTractorIFStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetReaperEnclosureSensors(::grpc::ClientContext* context, const ::hardware_manager::GetReaperEnclosureSensorsRequest& request, ::hardware_manager::GetReaperEnclosureSensorsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetReaperEnclosureSensorsRequest, ::hardware_manager::GetReaperEnclosureSensorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetReaperEnclosureSensors_, context, request, response);
}

void HardwareManagerService::Stub::async::GetReaperEnclosureSensors(::grpc::ClientContext* context, const ::hardware_manager::GetReaperEnclosureSensorsRequest* request, ::hardware_manager::GetReaperEnclosureSensorsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetReaperEnclosureSensorsRequest, ::hardware_manager::GetReaperEnclosureSensorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReaperEnclosureSensors_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetReaperEnclosureSensors(::grpc::ClientContext* context, const ::hardware_manager::GetReaperEnclosureSensorsRequest* request, ::hardware_manager::GetReaperEnclosureSensorsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReaperEnclosureSensors_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetReaperEnclosureSensorsResponse>* HardwareManagerService::Stub::PrepareAsyncGetReaperEnclosureSensorsRaw(::grpc::ClientContext* context, const ::hardware_manager::GetReaperEnclosureSensorsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetReaperEnclosureSensorsResponse, ::hardware_manager::GetReaperEnclosureSensorsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetReaperEnclosureSensors_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetReaperEnclosureSensorsResponse>* HardwareManagerService::Stub::AsyncGetReaperEnclosureSensorsRaw(::grpc::ClientContext* context, const ::hardware_manager::GetReaperEnclosureSensorsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetReaperEnclosureSensorsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::GetReaperModuleSensors(::grpc::ClientContext* context, const ::hardware_manager::GetReaperModuleSensorsRequest& request, ::hardware_manager::GetReaperModuleSensorsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::GetReaperModuleSensorsRequest, ::hardware_manager::GetReaperModuleSensorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetReaperModuleSensors_, context, request, response);
}

void HardwareManagerService::Stub::async::GetReaperModuleSensors(::grpc::ClientContext* context, const ::hardware_manager::GetReaperModuleSensorsRequest* request, ::hardware_manager::GetReaperModuleSensorsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::GetReaperModuleSensorsRequest, ::hardware_manager::GetReaperModuleSensorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReaperModuleSensors_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::GetReaperModuleSensors(::grpc::ClientContext* context, const ::hardware_manager::GetReaperModuleSensorsRequest* request, ::hardware_manager::GetReaperModuleSensorsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetReaperModuleSensors_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetReaperModuleSensorsResponse>* HardwareManagerService::Stub::PrepareAsyncGetReaperModuleSensorsRaw(::grpc::ClientContext* context, const ::hardware_manager::GetReaperModuleSensorsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::GetReaperModuleSensorsResponse, ::hardware_manager::GetReaperModuleSensorsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetReaperModuleSensors_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::GetReaperModuleSensorsResponse>* HardwareManagerService::Stub::AsyncGetReaperModuleSensorsRaw(::grpc::ClientContext* context, const ::hardware_manager::GetReaperModuleSensorsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetReaperModuleSensorsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetReaperScannerPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperScannerPowerRequest& request, ::hardware_manager::SetReaperScannerPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetReaperScannerPowerRequest, ::hardware_manager::SetReaperScannerPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperScannerPower_, context, request, response);
}

void HardwareManagerService::Stub::async::SetReaperScannerPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperScannerPowerRequest* request, ::hardware_manager::SetReaperScannerPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetReaperScannerPowerRequest, ::hardware_manager::SetReaperScannerPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperScannerPower_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetReaperScannerPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperScannerPowerRequest* request, ::hardware_manager::SetReaperScannerPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperScannerPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperScannerPowerResponse>* HardwareManagerService::Stub::PrepareAsyncSetReaperScannerPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetReaperScannerPowerResponse, ::hardware_manager::SetReaperScannerPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperScannerPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperScannerPowerResponse>* HardwareManagerService::Stub::AsyncSetReaperScannerPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperScannerPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperScannerPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetReaperTargetPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperTargetPowerRequest& request, ::hardware_manager::SetReaperTargetPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetReaperTargetPowerRequest, ::hardware_manager::SetReaperTargetPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperTargetPower_, context, request, response);
}

void HardwareManagerService::Stub::async::SetReaperTargetPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperTargetPowerRequest* request, ::hardware_manager::SetReaperTargetPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetReaperTargetPowerRequest, ::hardware_manager::SetReaperTargetPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperTargetPower_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetReaperTargetPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperTargetPowerRequest* request, ::hardware_manager::SetReaperTargetPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperTargetPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperTargetPowerResponse>* HardwareManagerService::Stub::PrepareAsyncSetReaperTargetPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetReaperTargetPowerResponse, ::hardware_manager::SetReaperTargetPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperTargetPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperTargetPowerResponse>* HardwareManagerService::Stub::AsyncSetReaperTargetPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperTargetPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperTargetPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetReaperPredictCamPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperPredictCamPowerRequest& request, ::hardware_manager::SetReaperPredictCamPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetReaperPredictCamPowerRequest, ::hardware_manager::SetReaperPredictCamPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperPredictCamPower_, context, request, response);
}

void HardwareManagerService::Stub::async::SetReaperPredictCamPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperPredictCamPowerRequest* request, ::hardware_manager::SetReaperPredictCamPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetReaperPredictCamPowerRequest, ::hardware_manager::SetReaperPredictCamPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperPredictCamPower_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetReaperPredictCamPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperPredictCamPowerRequest* request, ::hardware_manager::SetReaperPredictCamPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperPredictCamPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperPredictCamPowerResponse>* HardwareManagerService::Stub::PrepareAsyncSetReaperPredictCamPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetReaperPredictCamPowerResponse, ::hardware_manager::SetReaperPredictCamPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperPredictCamPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperPredictCamPowerResponse>* HardwareManagerService::Stub::AsyncSetReaperPredictCamPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperPredictCamPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperPredictCamPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetReaperStrobeConfig(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeConfigRequest& request, ::hardware_manager::SetReaperStrobeConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetReaperStrobeConfigRequest, ::hardware_manager::SetReaperStrobeConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperStrobeConfig_, context, request, response);
}

void HardwareManagerService::Stub::async::SetReaperStrobeConfig(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeConfigRequest* request, ::hardware_manager::SetReaperStrobeConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetReaperStrobeConfigRequest, ::hardware_manager::SetReaperStrobeConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperStrobeConfig_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetReaperStrobeConfig(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeConfigRequest* request, ::hardware_manager::SetReaperStrobeConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperStrobeConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperStrobeConfigResponse>* HardwareManagerService::Stub::PrepareAsyncSetReaperStrobeConfigRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetReaperStrobeConfigResponse, ::hardware_manager::SetReaperStrobeConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperStrobeConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperStrobeConfigResponse>* HardwareManagerService::Stub::AsyncSetReaperStrobeConfigRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperStrobeConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetReaperStrobeEnable(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeEnableRequest& request, ::hardware_manager::SetReaperStrobeEnableResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetReaperStrobeEnableRequest, ::hardware_manager::SetReaperStrobeEnableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperStrobeEnable_, context, request, response);
}

void HardwareManagerService::Stub::async::SetReaperStrobeEnable(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeEnableRequest* request, ::hardware_manager::SetReaperStrobeEnableResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetReaperStrobeEnableRequest, ::hardware_manager::SetReaperStrobeEnableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperStrobeEnable_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetReaperStrobeEnable(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeEnableRequest* request, ::hardware_manager::SetReaperStrobeEnableResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperStrobeEnable_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperStrobeEnableResponse>* HardwareManagerService::Stub::PrepareAsyncSetReaperStrobeEnableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetReaperStrobeEnableResponse, ::hardware_manager::SetReaperStrobeEnableRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperStrobeEnable_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperStrobeEnableResponse>* HardwareManagerService::Stub::AsyncSetReaperStrobeEnableRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperStrobeEnableRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperStrobeEnableRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetReaperModulePcPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModulePcPowerRequest& request, ::hardware_manager::SetReaperModulePcPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetReaperModulePcPowerRequest, ::hardware_manager::SetReaperModulePcPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperModulePcPower_, context, request, response);
}

void HardwareManagerService::Stub::async::SetReaperModulePcPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModulePcPowerRequest* request, ::hardware_manager::SetReaperModulePcPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetReaperModulePcPowerRequest, ::hardware_manager::SetReaperModulePcPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModulePcPower_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetReaperModulePcPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModulePcPowerRequest* request, ::hardware_manager::SetReaperModulePcPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModulePcPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperModulePcPowerResponse>* HardwareManagerService::Stub::PrepareAsyncSetReaperModulePcPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetReaperModulePcPowerResponse, ::hardware_manager::SetReaperModulePcPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperModulePcPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperModulePcPowerResponse>* HardwareManagerService::Stub::AsyncSetReaperModulePcPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModulePcPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperModulePcPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleLaserPowerRequest& request, ::hardware_manager::SetReaperModuleLaserPowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetReaperModuleLaserPowerRequest, ::hardware_manager::SetReaperModuleLaserPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperModuleLaserPower_, context, request, response);
}

void HardwareManagerService::Stub::async::SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleLaserPowerRequest* request, ::hardware_manager::SetReaperModuleLaserPowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetReaperModuleLaserPowerRequest, ::hardware_manager::SetReaperModuleLaserPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModuleLaserPower_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetReaperModuleLaserPower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleLaserPowerRequest* request, ::hardware_manager::SetReaperModuleLaserPowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModuleLaserPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperModuleLaserPowerResponse>* HardwareManagerService::Stub::PrepareAsyncSetReaperModuleLaserPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetReaperModuleLaserPowerResponse, ::hardware_manager::SetReaperModuleLaserPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperModuleLaserPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperModuleLaserPowerResponse>* HardwareManagerService::Stub::AsyncSetReaperModuleLaserPowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperModuleLaserPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::SetReaperModuleStrobePower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleStrobePowerRequest& request, ::hardware_manager::SetReaperModuleStrobePowerResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::SetReaperModuleStrobePowerRequest, ::hardware_manager::SetReaperModuleStrobePowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetReaperModuleStrobePower_, context, request, response);
}

void HardwareManagerService::Stub::async::SetReaperModuleStrobePower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleStrobePowerRequest* request, ::hardware_manager::SetReaperModuleStrobePowerResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::SetReaperModuleStrobePowerRequest, ::hardware_manager::SetReaperModuleStrobePowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModuleStrobePower_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::SetReaperModuleStrobePower(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleStrobePowerRequest* request, ::hardware_manager::SetReaperModuleStrobePowerResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetReaperModuleStrobePower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperModuleStrobePowerResponse>* HardwareManagerService::Stub::PrepareAsyncSetReaperModuleStrobePowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleStrobePowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::SetReaperModuleStrobePowerResponse, ::hardware_manager::SetReaperModuleStrobePowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetReaperModuleStrobePower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::SetReaperModuleStrobePowerResponse>* HardwareManagerService::Stub::AsyncSetReaperModuleStrobePowerRaw(::grpc::ClientContext* context, const ::hardware_manager::SetReaperModuleStrobePowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetReaperModuleStrobePowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status HardwareManagerService::Stub::IdentifyModule(::grpc::ClientContext* context, const ::hardware_manager::IdentifyModuleRequest& request, ::hardware_manager::IdentifyModuleResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::hardware_manager::IdentifyModuleRequest, ::hardware_manager::IdentifyModuleResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_IdentifyModule_, context, request, response);
}

void HardwareManagerService::Stub::async::IdentifyModule(::grpc::ClientContext* context, const ::hardware_manager::IdentifyModuleRequest* request, ::hardware_manager::IdentifyModuleResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::hardware_manager::IdentifyModuleRequest, ::hardware_manager::IdentifyModuleResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_IdentifyModule_, context, request, response, std::move(f));
}

void HardwareManagerService::Stub::async::IdentifyModule(::grpc::ClientContext* context, const ::hardware_manager::IdentifyModuleRequest* request, ::hardware_manager::IdentifyModuleResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_IdentifyModule_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::IdentifyModuleResponse>* HardwareManagerService::Stub::PrepareAsyncIdentifyModuleRaw(::grpc::ClientContext* context, const ::hardware_manager::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::hardware_manager::IdentifyModuleResponse, ::hardware_manager::IdentifyModuleRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_IdentifyModule_, context, request);
}

::grpc::ClientAsyncResponseReader< ::hardware_manager::IdentifyModuleResponse>* HardwareManagerService::Stub::AsyncIdentifyModuleRaw(::grpc::ClientContext* context, const ::hardware_manager::IdentifyModuleRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncIdentifyModuleRaw(context, request, cq);
  result->StartCall();
  return result;
}

HardwareManagerService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::PingRequest, ::hardware_manager::PingResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::PingRequest* req,
             ::hardware_manager::PingResponse* resp) {
               return service->Ping(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetReadyRequest, ::hardware_manager::GetReadyResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetReadyRequest* req,
             ::hardware_manager::GetReadyResponse* resp) {
               return service->GetReady(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetNextDistanceRequest, ::hardware_manager::GetNextDistanceResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetNextDistanceRequest* req,
             ::hardware_manager::GetNextDistanceResponse* resp) {
               return service->GetNextDistance(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetNextVelocityRequest, ::hardware_manager::GetNextVelocityResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetNextVelocityRequest* req,
             ::hardware_manager::GetNextVelocityResponse* resp) {
               return service->GetNextVelocity(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetRotaryTicksRequest, ::hardware_manager::GetRotaryTicksResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetRotaryTicksRequest* req,
             ::hardware_manager::GetRotaryTicksResponse* resp) {
               return service->GetRotaryTicks(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetDeltaTravelMMRequest, ::hardware_manager::GetDeltaTravelMMResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetDeltaTravelMMRequest* req,
             ::hardware_manager::GetDeltaTravelMMResponse* resp) {
               return service->GetDeltaTravelMM(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetWheelEncoderResolutionRequest, ::hardware_manager::GetWheelEncoderResolutionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetWheelEncoderResolutionRequest* req,
             ::hardware_manager::GetWheelEncoderResolutionResponse* resp) {
               return service->GetWheelEncoderResolution(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetSafetyStatusRequest, ::hardware_manager::GetSafetyStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetSafetyStatusRequest* req,
             ::hardware_manager::GetSafetyStatusResponse* resp) {
               return service->GetSafetyStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetGPSDataRequest, ::hardware_manager::GetGPSDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetGPSDataRequest* req,
             ::hardware_manager::GetGPSDataResponse* resp) {
               return service->GetGPSData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetNextGPSDataRequest, ::hardware_manager::GetNextGPSDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetNextGPSDataRequest* req,
             ::hardware_manager::GetNextGPSDataResponse* resp) {
               return service->GetNextGPSData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetNextRawGPSDataRequest, ::hardware_manager::GetNextRawGPSDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetNextRawGPSDataRequest* req,
             ::hardware_manager::GetNextRawGPSDataResponse* resp) {
               return service->GetNextRawGPSData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetGPSFixedPosRequest, ::hardware_manager::GetGPSFixedPosResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetGPSFixedPosRequest* req,
             ::hardware_manager::GetGPSFixedPosResponse* resp) {
               return service->GetGPSFixedPos(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::StrobeSettings, ::hardware_manager::SetStrobeSettingsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::StrobeSettings* req,
             ::hardware_manager::SetStrobeSettingsResponse* resp) {
               return service->SetStrobeSettings(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetStrobeSettingsRequest, ::hardware_manager::StrobeSettings, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetStrobeSettingsRequest* req,
             ::hardware_manager::StrobeSettings* resp) {
               return service->GetStrobeSettings(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetManagedBoardErrorsRequest, ::hardware_manager::GetManagedBoardErrorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetManagedBoardErrorsRequest* req,
             ::hardware_manager::GetManagedBoardErrorsResponse* resp) {
               return service->GetManagedBoardErrors(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[15],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetSupervisoryStatusRequest, ::hardware_manager::GetSupervisoryStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetSupervisoryStatusRequest* req,
             ::hardware_manager::GetSupervisoryStatusResponse* resp) {
               return service->GetSupervisoryStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[16],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetReaperSupervisoryStatusRequest, ::hardware_manager::ReaperCenterEnclosureData, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetReaperSupervisoryStatusRequest* req,
             ::hardware_manager::ReaperCenterEnclosureData* resp) {
               return service->GetReaperSupervisoryStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[17],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetServerDisableRequest, ::hardware_manager::SetServerDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetServerDisableRequest* req,
             ::hardware_manager::SetServerDisableResponse* resp) {
               return service->SetServerDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[18],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetBTLDisableRequest, ::hardware_manager::SetBTLDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetBTLDisableRequest* req,
             ::hardware_manager::SetBTLDisableResponse* resp) {
               return service->SetBTLDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[19],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetScannersDisableRequest, ::hardware_manager::SetScannersDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetScannersDisableRequest* req,
             ::hardware_manager::SetScannersDisableResponse* resp) {
               return service->SetScannersDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[20],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetWheelEncoderBoardDisableRequest, ::hardware_manager::SetWheelEncoderBoardDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetWheelEncoderBoardDisableRequest* req,
             ::hardware_manager::SetWheelEncoderBoardDisableResponse* resp) {
               return service->SetWheelEncoderBoardDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[21],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetWheelEncoderDisableRequest, ::hardware_manager::SetWheelEncoderDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetWheelEncoderDisableRequest* req,
             ::hardware_manager::SetWheelEncoderDisableResponse* resp) {
               return service->SetWheelEncoderDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[22],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetGPSDisableRequest, ::hardware_manager::SetGPSDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetGPSDisableRequest* req,
             ::hardware_manager::SetGPSDisableResponse* resp) {
               return service->SetGPSDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[23],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SuicideSwitchRequest, ::hardware_manager::SuicideSwitchResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SuicideSwitchRequest* req,
             ::hardware_manager::SuicideSwitchResponse* resp) {
               return service->SuicideSwitch(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[24],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::CommandComputerPowerCycleRequest, ::hardware_manager::CommandComputerPowerCycleResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::CommandComputerPowerCycleRequest* req,
             ::hardware_manager::CommandComputerPowerCycleResponse* resp) {
               return service->CommandComputerPowerCycle(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[25],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetMainContactorDisableRequest, ::hardware_manager::SetMainContactorDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetMainContactorDisableRequest* req,
             ::hardware_manager::SetMainContactorDisableResponse* resp) {
               return service->SetMainContactorDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[26],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetStrobeDisableRequest, ::hardware_manager::SetStrobeDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetStrobeDisableRequest* req,
             ::hardware_manager::SetStrobeDisableResponse* resp) {
               return service->SetStrobeDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[27],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetAirConditionerDisableRequest, ::hardware_manager::SetAirConditionerDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetAirConditionerDisableRequest* req,
             ::hardware_manager::SetAirConditionerDisableResponse* resp) {
               return service->SetAirConditionerDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[28],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetChillerDisableRequest, ::hardware_manager::SetChillerDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetChillerDisableRequest* req,
             ::hardware_manager::SetChillerDisableResponse* resp) {
               return service->SetChillerDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[29],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetTempBypassDisableRequest, ::hardware_manager::SetTempBypassDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetTempBypassDisableRequest* req,
             ::hardware_manager::SetTempBypassDisableResponse* resp) {
               return service->SetTempBypassDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[30],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetHumidityBypassDisableRequest, ::hardware_manager::SetHumidityBypassDisableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetHumidityBypassDisableRequest* req,
             ::hardware_manager::SetHumidityBypassDisableResponse* resp) {
               return service->SetHumidityBypassDisable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[31],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::Get240vUptimeRequest, ::hardware_manager::Get240vUptimeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::Get240vUptimeRequest* req,
             ::hardware_manager::Get240vUptimeResponse* resp) {
               return service->Get240vUptime(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[32],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetRuntimeRequest, ::hardware_manager::GetRuntimeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetRuntimeRequest* req,
             ::hardware_manager::GetRuntimeResponse* resp) {
               return service->GetRuntime(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[33],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetAvailableUSBStorageRequest, ::hardware_manager::GetAvailableUSBStorageResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetAvailableUSBStorageRequest* req,
             ::hardware_manager::GetAvailableUSBStorageResponse* resp) {
               return service->GetAvailableUSBStorage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[34],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetJimboxSpeedRequest, ::hardware_manager::SetJimboxSpeedResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetJimboxSpeedRequest* req,
             ::hardware_manager::SetJimboxSpeedResponse* resp) {
               return service->SetJimboxSpeed(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[35],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetCruiseEnabledRequest, ::hardware_manager::SetCruiseEnabledResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetCruiseEnabledRequest* req,
             ::hardware_manager::SetCruiseEnabledResponse* resp) {
               return service->SetCruiseEnabled(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[36],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetCruiseStatusRequest, ::hardware_manager::GetCruiseStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetCruiseStatusRequest* req,
             ::hardware_manager::GetCruiseStatusResponse* resp) {
               return service->GetCruiseStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[37],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetImplementStateRequest, ::hardware_manager::SetImplementStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetImplementStateRequest* req,
             ::hardware_manager::SetImplementStateResponse* resp) {
               return service->SetImplementStateOnTractor(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[38],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetSafeStateEnforcementRequest, ::hardware_manager::SetSafeStateEnforcementResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetSafeStateEnforcementRequest* req,
             ::hardware_manager::SetSafeStateEnforcementResponse* resp) {
               return service->SetSafeStateEnforcement(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[39],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetTractorSafetyStateRequest, ::hardware_manager::GetTractorSafetyStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetTractorSafetyStateRequest* req,
             ::hardware_manager::GetTractorSafetyStateResponse* resp) {
               return service->GetTractorSafetyState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[40],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetTractorIFStateRequest, ::hardware_manager::GetTractorIFStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetTractorIFStateRequest* req,
             ::hardware_manager::GetTractorIFStateResponse* resp) {
               return service->GetTractorIFState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[41],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetReaperEnclosureSensorsRequest, ::hardware_manager::GetReaperEnclosureSensorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetReaperEnclosureSensorsRequest* req,
             ::hardware_manager::GetReaperEnclosureSensorsResponse* resp) {
               return service->GetReaperEnclosureSensors(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[42],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::GetReaperModuleSensorsRequest, ::hardware_manager::GetReaperModuleSensorsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::GetReaperModuleSensorsRequest* req,
             ::hardware_manager::GetReaperModuleSensorsResponse* resp) {
               return service->GetReaperModuleSensors(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[43],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetReaperScannerPowerRequest, ::hardware_manager::SetReaperScannerPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetReaperScannerPowerRequest* req,
             ::hardware_manager::SetReaperScannerPowerResponse* resp) {
               return service->SetReaperScannerPower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[44],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetReaperTargetPowerRequest, ::hardware_manager::SetReaperTargetPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetReaperTargetPowerRequest* req,
             ::hardware_manager::SetReaperTargetPowerResponse* resp) {
               return service->SetReaperTargetPower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[45],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetReaperPredictCamPowerRequest, ::hardware_manager::SetReaperPredictCamPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetReaperPredictCamPowerRequest* req,
             ::hardware_manager::SetReaperPredictCamPowerResponse* resp) {
               return service->SetReaperPredictCamPower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[46],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetReaperStrobeConfigRequest, ::hardware_manager::SetReaperStrobeConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetReaperStrobeConfigRequest* req,
             ::hardware_manager::SetReaperStrobeConfigResponse* resp) {
               return service->SetReaperStrobeConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[47],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetReaperStrobeEnableRequest, ::hardware_manager::SetReaperStrobeEnableResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetReaperStrobeEnableRequest* req,
             ::hardware_manager::SetReaperStrobeEnableResponse* resp) {
               return service->SetReaperStrobeEnable(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[48],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetReaperModulePcPowerRequest, ::hardware_manager::SetReaperModulePcPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetReaperModulePcPowerRequest* req,
             ::hardware_manager::SetReaperModulePcPowerResponse* resp) {
               return service->SetReaperModulePcPower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[49],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetReaperModuleLaserPowerRequest, ::hardware_manager::SetReaperModuleLaserPowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetReaperModuleLaserPowerRequest* req,
             ::hardware_manager::SetReaperModuleLaserPowerResponse* resp) {
               return service->SetReaperModuleLaserPower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[50],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::SetReaperModuleStrobePowerRequest, ::hardware_manager::SetReaperModuleStrobePowerResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::SetReaperModuleStrobePowerRequest* req,
             ::hardware_manager::SetReaperModuleStrobePowerResponse* resp) {
               return service->SetReaperModuleStrobePower(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      HardwareManagerService_method_names[51],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< HardwareManagerService::Service, ::hardware_manager::IdentifyModuleRequest, ::hardware_manager::IdentifyModuleResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](HardwareManagerService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::hardware_manager::IdentifyModuleRequest* req,
             ::hardware_manager::IdentifyModuleResponse* resp) {
               return service->IdentifyModule(ctx, req, resp);
             }, this)));
}

HardwareManagerService::Service::~Service() {
}

::grpc::Status HardwareManagerService::Service::Ping(::grpc::ServerContext* context, const ::hardware_manager::PingRequest* request, ::hardware_manager::PingResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetReady(::grpc::ServerContext* context, const ::hardware_manager::GetReadyRequest* request, ::hardware_manager::GetReadyResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetNextDistance(::grpc::ServerContext* context, const ::hardware_manager::GetNextDistanceRequest* request, ::hardware_manager::GetNextDistanceResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetNextVelocity(::grpc::ServerContext* context, const ::hardware_manager::GetNextVelocityRequest* request, ::hardware_manager::GetNextVelocityResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetRotaryTicks(::grpc::ServerContext* context, const ::hardware_manager::GetRotaryTicksRequest* request, ::hardware_manager::GetRotaryTicksResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetDeltaTravelMM(::grpc::ServerContext* context, const ::hardware_manager::GetDeltaTravelMMRequest* request, ::hardware_manager::GetDeltaTravelMMResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetWheelEncoderResolution(::grpc::ServerContext* context, const ::hardware_manager::GetWheelEncoderResolutionRequest* request, ::hardware_manager::GetWheelEncoderResolutionResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetSafetyStatus(::grpc::ServerContext* context, const ::hardware_manager::GetSafetyStatusRequest* request, ::hardware_manager::GetSafetyStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetGPSData(::grpc::ServerContext* context, const ::hardware_manager::GetGPSDataRequest* request, ::hardware_manager::GetGPSDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetNextGPSData(::grpc::ServerContext* context, const ::hardware_manager::GetNextGPSDataRequest* request, ::hardware_manager::GetNextGPSDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetNextRawGPSData(::grpc::ServerContext* context, const ::hardware_manager::GetNextRawGPSDataRequest* request, ::hardware_manager::GetNextRawGPSDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetGPSFixedPos(::grpc::ServerContext* context, const ::hardware_manager::GetGPSFixedPosRequest* request, ::hardware_manager::GetGPSFixedPosResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetStrobeSettings(::grpc::ServerContext* context, const ::hardware_manager::StrobeSettings* request, ::hardware_manager::SetStrobeSettingsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetStrobeSettings(::grpc::ServerContext* context, const ::hardware_manager::GetStrobeSettingsRequest* request, ::hardware_manager::StrobeSettings* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetManagedBoardErrors(::grpc::ServerContext* context, const ::hardware_manager::GetManagedBoardErrorsRequest* request, ::hardware_manager::GetManagedBoardErrorsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetSupervisoryStatus(::grpc::ServerContext* context, const ::hardware_manager::GetSupervisoryStatusRequest* request, ::hardware_manager::GetSupervisoryStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetReaperSupervisoryStatus(::grpc::ServerContext* context, const ::hardware_manager::GetReaperSupervisoryStatusRequest* request, ::hardware_manager::ReaperCenterEnclosureData* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetServerDisable(::grpc::ServerContext* context, const ::hardware_manager::SetServerDisableRequest* request, ::hardware_manager::SetServerDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetBTLDisable(::grpc::ServerContext* context, const ::hardware_manager::SetBTLDisableRequest* request, ::hardware_manager::SetBTLDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetScannersDisable(::grpc::ServerContext* context, const ::hardware_manager::SetScannersDisableRequest* request, ::hardware_manager::SetScannersDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetWheelEncoderBoardDisable(::grpc::ServerContext* context, const ::hardware_manager::SetWheelEncoderBoardDisableRequest* request, ::hardware_manager::SetWheelEncoderBoardDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetWheelEncoderDisable(::grpc::ServerContext* context, const ::hardware_manager::SetWheelEncoderDisableRequest* request, ::hardware_manager::SetWheelEncoderDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetGPSDisable(::grpc::ServerContext* context, const ::hardware_manager::SetGPSDisableRequest* request, ::hardware_manager::SetGPSDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SuicideSwitch(::grpc::ServerContext* context, const ::hardware_manager::SuicideSwitchRequest* request, ::hardware_manager::SuicideSwitchResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::CommandComputerPowerCycle(::grpc::ServerContext* context, const ::hardware_manager::CommandComputerPowerCycleRequest* request, ::hardware_manager::CommandComputerPowerCycleResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetMainContactorDisable(::grpc::ServerContext* context, const ::hardware_manager::SetMainContactorDisableRequest* request, ::hardware_manager::SetMainContactorDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetStrobeDisable(::grpc::ServerContext* context, const ::hardware_manager::SetStrobeDisableRequest* request, ::hardware_manager::SetStrobeDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetAirConditionerDisable(::grpc::ServerContext* context, const ::hardware_manager::SetAirConditionerDisableRequest* request, ::hardware_manager::SetAirConditionerDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetChillerDisable(::grpc::ServerContext* context, const ::hardware_manager::SetChillerDisableRequest* request, ::hardware_manager::SetChillerDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetTempBypassDisable(::grpc::ServerContext* context, const ::hardware_manager::SetTempBypassDisableRequest* request, ::hardware_manager::SetTempBypassDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetHumidityBypassDisable(::grpc::ServerContext* context, const ::hardware_manager::SetHumidityBypassDisableRequest* request, ::hardware_manager::SetHumidityBypassDisableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::Get240vUptime(::grpc::ServerContext* context, const ::hardware_manager::Get240vUptimeRequest* request, ::hardware_manager::Get240vUptimeResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetRuntime(::grpc::ServerContext* context, const ::hardware_manager::GetRuntimeRequest* request, ::hardware_manager::GetRuntimeResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetAvailableUSBStorage(::grpc::ServerContext* context, const ::hardware_manager::GetAvailableUSBStorageRequest* request, ::hardware_manager::GetAvailableUSBStorageResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetJimboxSpeed(::grpc::ServerContext* context, const ::hardware_manager::SetJimboxSpeedRequest* request, ::hardware_manager::SetJimboxSpeedResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetCruiseEnabled(::grpc::ServerContext* context, const ::hardware_manager::SetCruiseEnabledRequest* request, ::hardware_manager::SetCruiseEnabledResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetCruiseStatus(::grpc::ServerContext* context, const ::hardware_manager::GetCruiseStatusRequest* request, ::hardware_manager::GetCruiseStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetImplementStateOnTractor(::grpc::ServerContext* context, const ::hardware_manager::SetImplementStateRequest* request, ::hardware_manager::SetImplementStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetSafeStateEnforcement(::grpc::ServerContext* context, const ::hardware_manager::SetSafeStateEnforcementRequest* request, ::hardware_manager::SetSafeStateEnforcementResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetTractorSafetyState(::grpc::ServerContext* context, const ::hardware_manager::GetTractorSafetyStateRequest* request, ::hardware_manager::GetTractorSafetyStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetTractorIFState(::grpc::ServerContext* context, const ::hardware_manager::GetTractorIFStateRequest* request, ::hardware_manager::GetTractorIFStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetReaperEnclosureSensors(::grpc::ServerContext* context, const ::hardware_manager::GetReaperEnclosureSensorsRequest* request, ::hardware_manager::GetReaperEnclosureSensorsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::GetReaperModuleSensors(::grpc::ServerContext* context, const ::hardware_manager::GetReaperModuleSensorsRequest* request, ::hardware_manager::GetReaperModuleSensorsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetReaperScannerPower(::grpc::ServerContext* context, const ::hardware_manager::SetReaperScannerPowerRequest* request, ::hardware_manager::SetReaperScannerPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetReaperTargetPower(::grpc::ServerContext* context, const ::hardware_manager::SetReaperTargetPowerRequest* request, ::hardware_manager::SetReaperTargetPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetReaperPredictCamPower(::grpc::ServerContext* context, const ::hardware_manager::SetReaperPredictCamPowerRequest* request, ::hardware_manager::SetReaperPredictCamPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetReaperStrobeConfig(::grpc::ServerContext* context, const ::hardware_manager::SetReaperStrobeConfigRequest* request, ::hardware_manager::SetReaperStrobeConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetReaperStrobeEnable(::grpc::ServerContext* context, const ::hardware_manager::SetReaperStrobeEnableRequest* request, ::hardware_manager::SetReaperStrobeEnableResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetReaperModulePcPower(::grpc::ServerContext* context, const ::hardware_manager::SetReaperModulePcPowerRequest* request, ::hardware_manager::SetReaperModulePcPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetReaperModuleLaserPower(::grpc::ServerContext* context, const ::hardware_manager::SetReaperModuleLaserPowerRequest* request, ::hardware_manager::SetReaperModuleLaserPowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::SetReaperModuleStrobePower(::grpc::ServerContext* context, const ::hardware_manager::SetReaperModuleStrobePowerRequest* request, ::hardware_manager::SetReaperModuleStrobePowerResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status HardwareManagerService::Service::IdentifyModule(::grpc::ServerContext* context, const ::hardware_manager::IdentifyModuleRequest* request, ::hardware_manager::IdentifyModuleResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace hardware_manager


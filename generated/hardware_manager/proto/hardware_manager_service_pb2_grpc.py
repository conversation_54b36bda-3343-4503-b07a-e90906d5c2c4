# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.hardware_manager.proto import hardware_manager_service_pb2 as hardware__manager_dot_proto_dot_hardware__manager__service__pb2


class HardwareManagerServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/Ping',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.PingRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.PingResponse.FromString,
                )
        self.GetReady = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetReady',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReadyRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReadyResponse.FromString,
                )
        self.GetNextDistance = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetNextDistance',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextDistanceRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextDistanceResponse.FromString,
                )
        self.GetNextVelocity = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetNextVelocity',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextVelocityRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextVelocityResponse.FromString,
                )
        self.GetRotaryTicks = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetRotaryTicks',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRotaryTicksRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRotaryTicksResponse.FromString,
                )
        self.GetDeltaTravelMM = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetDeltaTravelMM',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetDeltaTravelMMRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetDeltaTravelMMResponse.FromString,
                )
        self.GetWheelEncoderResolution = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetWheelEncoderResolution',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionResponse.FromString,
                )
        self.GetSafetyStatus = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetSafetyStatus',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSafetyStatusRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSafetyStatusResponse.FromString,
                )
        self.GetGPSData = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetGPSData',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSDataRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSDataResponse.FromString,
                )
        self.GetNextGPSData = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetNextGPSData',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextGPSDataRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextGPSDataResponse.FromString,
                )
        self.GetNextRawGPSData = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetNextRawGPSData',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextRawGPSDataRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextRawGPSDataResponse.FromString,
                )
        self.GetGPSFixedPos = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetGPSFixedPos',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSFixedPosRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSFixedPosResponse.FromString,
                )
        self.SetStrobeSettings = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetStrobeSettings',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.StrobeSettings.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetStrobeSettingsResponse.FromString,
                )
        self.GetStrobeSettings = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetStrobeSettings',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetStrobeSettingsRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.StrobeSettings.FromString,
                )
        self.GetManagedBoardErrors = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetManagedBoardErrors',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetManagedBoardErrorsRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetManagedBoardErrorsResponse.FromString,
                )
        self.GetSupervisoryStatus = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetSupervisoryStatus',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSupervisoryStatusRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSupervisoryStatusResponse.FromString,
                )
        self.GetReaperSupervisoryStatus = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetReaperSupervisoryStatus',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperSupervisoryStatusRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.ReaperCenterEnclosureData.FromString,
                )
        self.SetServerDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetServerDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetServerDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetServerDisableResponse.FromString,
                )
        self.SetBTLDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetBTLDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetBTLDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetBTLDisableResponse.FromString,
                )
        self.SetScannersDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetScannersDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetScannersDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetScannersDisableResponse.FromString,
                )
        self.SetWheelEncoderBoardDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetWheelEncoderBoardDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableResponse.FromString,
                )
        self.SetWheelEncoderDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetWheelEncoderDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderDisableResponse.FromString,
                )
        self.SetGPSDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetGPSDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetGPSDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetGPSDisableResponse.FromString,
                )
        self.SuicideSwitch = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SuicideSwitch',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SuicideSwitchRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SuicideSwitchResponse.FromString,
                )
        self.CommandComputerPowerCycle = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/CommandComputerPowerCycle',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.CommandComputerPowerCycleRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.CommandComputerPowerCycleResponse.FromString,
                )
        self.SetMainContactorDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetMainContactorDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetMainContactorDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetMainContactorDisableResponse.FromString,
                )
        self.SetStrobeDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetStrobeDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetStrobeDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetStrobeDisableResponse.FromString,
                )
        self.SetAirConditionerDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetAirConditionerDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetAirConditionerDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetAirConditionerDisableResponse.FromString,
                )
        self.SetChillerDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetChillerDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetChillerDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetChillerDisableResponse.FromString,
                )
        self.SetTempBypassDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetTempBypassDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetTempBypassDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetTempBypassDisableResponse.FromString,
                )
        self.SetHumidityBypassDisable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetHumidityBypassDisable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetHumidityBypassDisableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetHumidityBypassDisableResponse.FromString,
                )
        self.Get240vUptime = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/Get240vUptime',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.Get240vUptimeRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.Get240vUptimeResponse.FromString,
                )
        self.GetRuntime = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetRuntime',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRuntimeRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRuntimeResponse.FromString,
                )
        self.GetAvailableUSBStorage = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetAvailableUSBStorage',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetAvailableUSBStorageRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetAvailableUSBStorageResponse.FromString,
                )
        self.SetJimboxSpeed = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetJimboxSpeed',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetJimboxSpeedRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetJimboxSpeedResponse.FromString,
                )
        self.SetCruiseEnabled = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetCruiseEnabled',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetCruiseEnabledRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetCruiseEnabledResponse.FromString,
                )
        self.GetCruiseStatus = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetCruiseStatus',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetCruiseStatusRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetCruiseStatusResponse.FromString,
                )
        self.SetImplementStateOnTractor = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetImplementStateOnTractor',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetImplementStateRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetImplementStateResponse.FromString,
                )
        self.SetSafeStateEnforcement = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetSafeStateEnforcement',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetSafeStateEnforcementRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetSafeStateEnforcementResponse.FromString,
                )
        self.GetTractorSafetyState = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetTractorSafetyState',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorSafetyStateRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorSafetyStateResponse.FromString,
                )
        self.GetTractorIFState = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetTractorIFState',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorIFStateRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorIFStateResponse.FromString,
                )
        self.GetReaperEnclosureSensors = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetReaperEnclosureSensors',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperEnclosureSensorsRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperEnclosureSensorsResponse.FromString,
                )
        self.GetReaperModuleSensors = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/GetReaperModuleSensors',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperModuleSensorsRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperModuleSensorsResponse.FromString,
                )
        self.SetReaperScannerPower = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetReaperScannerPower',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperScannerPowerRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperScannerPowerResponse.FromString,
                )
        self.SetReaperTargetPower = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetReaperTargetPower',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperTargetPowerRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperTargetPowerResponse.FromString,
                )
        self.SetReaperPredictCamPower = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetReaperPredictCamPower',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperPredictCamPowerRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperPredictCamPowerResponse.FromString,
                )
        self.SetReaperStrobeConfig = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetReaperStrobeConfig',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeConfigRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeConfigResponse.FromString,
                )
        self.SetReaperStrobeEnable = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetReaperStrobeEnable',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeEnableRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeEnableResponse.FromString,
                )
        self.SetReaperModulePcPower = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetReaperModulePcPower',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModulePcPowerRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModulePcPowerResponse.FromString,
                )
        self.SetReaperModuleLaserPower = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetReaperModuleLaserPower',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleLaserPowerRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleLaserPowerResponse.FromString,
                )
        self.SetReaperModuleStrobePower = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/SetReaperModuleStrobePower',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleStrobePowerRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleStrobePowerResponse.FromString,
                )
        self.IdentifyModule = channel.unary_unary(
                '/hardware_manager.HardwareManagerService/IdentifyModule',
                request_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.IdentifyModuleRequest.SerializeToString,
                response_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.IdentifyModuleResponse.FromString,
                )


class HardwareManagerServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetReady(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextDistance(self, request, context):
        """Rotary
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextVelocity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRotaryTicks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeltaTravelMM(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetWheelEncoderResolution(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSafetyStatus(self, request, context):
        """Safety
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetGPSData(self, request, context):
        """GPS
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextGPSData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextRawGPSData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetGPSFixedPos(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetStrobeSettings(self, request, context):
        """Strobe
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStrobeSettings(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetManagedBoardErrors(self, request, context):
        """Managed Boards
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSupervisoryStatus(self, request, context):
        """Supervisory PLC
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetReaperSupervisoryStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetServerDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetBTLDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetScannersDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetWheelEncoderBoardDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetWheelEncoderDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetGPSDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SuicideSwitch(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CommandComputerPowerCycle(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetMainContactorDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetStrobeDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetAirConditionerDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetChillerDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetTempBypassDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetHumidityBypassDisable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Get240vUptime(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRuntime(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAvailableUSBStorage(self, request, context):
        """USB
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetJimboxSpeed(self, request, context):
        """Jimbox
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCruiseEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCruiseStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetImplementStateOnTractor(self, request, context):
        """Tractor
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetSafeStateEnforcement(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTractorSafetyState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTractorIFState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetReaperEnclosureSensors(self, request, context):
        """Hardware/board status
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetReaperModuleSensors(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperScannerPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperTargetPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperPredictCamPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperStrobeConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperStrobeEnable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperModulePcPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperModuleLaserPower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetReaperModuleStrobePower(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IdentifyModule(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_HardwareManagerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.PingRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.PingResponse.SerializeToString,
            ),
            'GetReady': grpc.unary_unary_rpc_method_handler(
                    servicer.GetReady,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReadyRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReadyResponse.SerializeToString,
            ),
            'GetNextDistance': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextDistance,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextDistanceRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextDistanceResponse.SerializeToString,
            ),
            'GetNextVelocity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextVelocity,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextVelocityRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextVelocityResponse.SerializeToString,
            ),
            'GetRotaryTicks': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRotaryTicks,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRotaryTicksRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRotaryTicksResponse.SerializeToString,
            ),
            'GetDeltaTravelMM': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeltaTravelMM,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetDeltaTravelMMRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetDeltaTravelMMResponse.SerializeToString,
            ),
            'GetWheelEncoderResolution': grpc.unary_unary_rpc_method_handler(
                    servicer.GetWheelEncoderResolution,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionResponse.SerializeToString,
            ),
            'GetSafetyStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSafetyStatus,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSafetyStatusRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSafetyStatusResponse.SerializeToString,
            ),
            'GetGPSData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetGPSData,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSDataRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSDataResponse.SerializeToString,
            ),
            'GetNextGPSData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextGPSData,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextGPSDataRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextGPSDataResponse.SerializeToString,
            ),
            'GetNextRawGPSData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextRawGPSData,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextRawGPSDataRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextRawGPSDataResponse.SerializeToString,
            ),
            'GetGPSFixedPos': grpc.unary_unary_rpc_method_handler(
                    servicer.GetGPSFixedPos,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSFixedPosRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSFixedPosResponse.SerializeToString,
            ),
            'SetStrobeSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.SetStrobeSettings,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.StrobeSettings.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetStrobeSettingsResponse.SerializeToString,
            ),
            'GetStrobeSettings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStrobeSettings,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetStrobeSettingsRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.StrobeSettings.SerializeToString,
            ),
            'GetManagedBoardErrors': grpc.unary_unary_rpc_method_handler(
                    servicer.GetManagedBoardErrors,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetManagedBoardErrorsRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetManagedBoardErrorsResponse.SerializeToString,
            ),
            'GetSupervisoryStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSupervisoryStatus,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSupervisoryStatusRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSupervisoryStatusResponse.SerializeToString,
            ),
            'GetReaperSupervisoryStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetReaperSupervisoryStatus,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperSupervisoryStatusRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.ReaperCenterEnclosureData.SerializeToString,
            ),
            'SetServerDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetServerDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetServerDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetServerDisableResponse.SerializeToString,
            ),
            'SetBTLDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetBTLDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetBTLDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetBTLDisableResponse.SerializeToString,
            ),
            'SetScannersDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetScannersDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetScannersDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetScannersDisableResponse.SerializeToString,
            ),
            'SetWheelEncoderBoardDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetWheelEncoderBoardDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableResponse.SerializeToString,
            ),
            'SetWheelEncoderDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetWheelEncoderDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderDisableResponse.SerializeToString,
            ),
            'SetGPSDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetGPSDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetGPSDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetGPSDisableResponse.SerializeToString,
            ),
            'SuicideSwitch': grpc.unary_unary_rpc_method_handler(
                    servicer.SuicideSwitch,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SuicideSwitchRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SuicideSwitchResponse.SerializeToString,
            ),
            'CommandComputerPowerCycle': grpc.unary_unary_rpc_method_handler(
                    servicer.CommandComputerPowerCycle,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.CommandComputerPowerCycleRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.CommandComputerPowerCycleResponse.SerializeToString,
            ),
            'SetMainContactorDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetMainContactorDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetMainContactorDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetMainContactorDisableResponse.SerializeToString,
            ),
            'SetStrobeDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetStrobeDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetStrobeDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetStrobeDisableResponse.SerializeToString,
            ),
            'SetAirConditionerDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetAirConditionerDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetAirConditionerDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetAirConditionerDisableResponse.SerializeToString,
            ),
            'SetChillerDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetChillerDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetChillerDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetChillerDisableResponse.SerializeToString,
            ),
            'SetTempBypassDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetTempBypassDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetTempBypassDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetTempBypassDisableResponse.SerializeToString,
            ),
            'SetHumidityBypassDisable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetHumidityBypassDisable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetHumidityBypassDisableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetHumidityBypassDisableResponse.SerializeToString,
            ),
            'Get240vUptime': grpc.unary_unary_rpc_method_handler(
                    servicer.Get240vUptime,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.Get240vUptimeRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.Get240vUptimeResponse.SerializeToString,
            ),
            'GetRuntime': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRuntime,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRuntimeRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRuntimeResponse.SerializeToString,
            ),
            'GetAvailableUSBStorage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAvailableUSBStorage,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetAvailableUSBStorageRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetAvailableUSBStorageResponse.SerializeToString,
            ),
            'SetJimboxSpeed': grpc.unary_unary_rpc_method_handler(
                    servicer.SetJimboxSpeed,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetJimboxSpeedRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetJimboxSpeedResponse.SerializeToString,
            ),
            'SetCruiseEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCruiseEnabled,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetCruiseEnabledRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetCruiseEnabledResponse.SerializeToString,
            ),
            'GetCruiseStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCruiseStatus,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetCruiseStatusRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetCruiseStatusResponse.SerializeToString,
            ),
            'SetImplementStateOnTractor': grpc.unary_unary_rpc_method_handler(
                    servicer.SetImplementStateOnTractor,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetImplementStateRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetImplementStateResponse.SerializeToString,
            ),
            'SetSafeStateEnforcement': grpc.unary_unary_rpc_method_handler(
                    servicer.SetSafeStateEnforcement,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetSafeStateEnforcementRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetSafeStateEnforcementResponse.SerializeToString,
            ),
            'GetTractorSafetyState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTractorSafetyState,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorSafetyStateRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorSafetyStateResponse.SerializeToString,
            ),
            'GetTractorIFState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTractorIFState,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorIFStateRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorIFStateResponse.SerializeToString,
            ),
            'GetReaperEnclosureSensors': grpc.unary_unary_rpc_method_handler(
                    servicer.GetReaperEnclosureSensors,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperEnclosureSensorsRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperEnclosureSensorsResponse.SerializeToString,
            ),
            'GetReaperModuleSensors': grpc.unary_unary_rpc_method_handler(
                    servicer.GetReaperModuleSensors,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperModuleSensorsRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperModuleSensorsResponse.SerializeToString,
            ),
            'SetReaperScannerPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperScannerPower,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperScannerPowerRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperScannerPowerResponse.SerializeToString,
            ),
            'SetReaperTargetPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperTargetPower,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperTargetPowerRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperTargetPowerResponse.SerializeToString,
            ),
            'SetReaperPredictCamPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperPredictCamPower,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperPredictCamPowerRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperPredictCamPowerResponse.SerializeToString,
            ),
            'SetReaperStrobeConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperStrobeConfig,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeConfigRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeConfigResponse.SerializeToString,
            ),
            'SetReaperStrobeEnable': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperStrobeEnable,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeEnableRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeEnableResponse.SerializeToString,
            ),
            'SetReaperModulePcPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperModulePcPower,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModulePcPowerRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModulePcPowerResponse.SerializeToString,
            ),
            'SetReaperModuleLaserPower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperModuleLaserPower,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleLaserPowerRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleLaserPowerResponse.SerializeToString,
            ),
            'SetReaperModuleStrobePower': grpc.unary_unary_rpc_method_handler(
                    servicer.SetReaperModuleStrobePower,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleStrobePowerRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleStrobePowerResponse.SerializeToString,
            ),
            'IdentifyModule': grpc.unary_unary_rpc_method_handler(
                    servicer.IdentifyModule,
                    request_deserializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.IdentifyModuleRequest.FromString,
                    response_serializer=hardware__manager_dot_proto_dot_hardware__manager__service__pb2.IdentifyModuleResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'hardware_manager.HardwareManagerService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class HardwareManagerService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/Ping',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.PingRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.PingResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetReady(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetReady',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReadyRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReadyResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextDistance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetNextDistance',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextDistanceRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextDistanceResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextVelocity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetNextVelocity',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextVelocityRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextVelocityResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetRotaryTicks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetRotaryTicks',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRotaryTicksRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRotaryTicksResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDeltaTravelMM(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetDeltaTravelMM',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetDeltaTravelMMRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetDeltaTravelMMResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetWheelEncoderResolution(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetWheelEncoderResolution',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetWheelEncoderResolutionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSafetyStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetSafetyStatus',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSafetyStatusRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSafetyStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetGPSData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetGPSData',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSDataRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextGPSData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetNextGPSData',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextGPSDataRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextGPSDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextRawGPSData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetNextRawGPSData',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextRawGPSDataRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetNextRawGPSDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetGPSFixedPos(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetGPSFixedPos',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSFixedPosRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetGPSFixedPosResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetStrobeSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetStrobeSettings',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.StrobeSettings.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetStrobeSettingsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetStrobeSettings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetStrobeSettings',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetStrobeSettingsRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.StrobeSettings.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetManagedBoardErrors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetManagedBoardErrors',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetManagedBoardErrorsRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetManagedBoardErrorsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSupervisoryStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetSupervisoryStatus',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSupervisoryStatusRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetSupervisoryStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetReaperSupervisoryStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetReaperSupervisoryStatus',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperSupervisoryStatusRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.ReaperCenterEnclosureData.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetServerDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetServerDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetServerDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetServerDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetBTLDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetBTLDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetBTLDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetBTLDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetScannersDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetScannersDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetScannersDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetScannersDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetWheelEncoderBoardDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetWheelEncoderBoardDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderBoardDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetWheelEncoderDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetWheelEncoderDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetWheelEncoderDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetGPSDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetGPSDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetGPSDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetGPSDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SuicideSwitch(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SuicideSwitch',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SuicideSwitchRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SuicideSwitchResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CommandComputerPowerCycle(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/CommandComputerPowerCycle',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.CommandComputerPowerCycleRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.CommandComputerPowerCycleResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetMainContactorDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetMainContactorDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetMainContactorDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetMainContactorDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetStrobeDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetStrobeDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetStrobeDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetStrobeDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetAirConditionerDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetAirConditionerDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetAirConditionerDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetAirConditionerDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetChillerDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetChillerDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetChillerDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetChillerDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetTempBypassDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetTempBypassDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetTempBypassDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetTempBypassDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetHumidityBypassDisable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetHumidityBypassDisable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetHumidityBypassDisableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetHumidityBypassDisableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Get240vUptime(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/Get240vUptime',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.Get240vUptimeRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.Get240vUptimeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetRuntime(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetRuntime',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRuntimeRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetRuntimeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetAvailableUSBStorage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetAvailableUSBStorage',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetAvailableUSBStorageRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetAvailableUSBStorageResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetJimboxSpeed(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetJimboxSpeed',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetJimboxSpeedRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetJimboxSpeedResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetCruiseEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetCruiseEnabled',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetCruiseEnabledRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetCruiseEnabledResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCruiseStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetCruiseStatus',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetCruiseStatusRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetCruiseStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetImplementStateOnTractor(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetImplementStateOnTractor',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetImplementStateRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetImplementStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetSafeStateEnforcement(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetSafeStateEnforcement',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetSafeStateEnforcementRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetSafeStateEnforcementResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTractorSafetyState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetTractorSafetyState',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorSafetyStateRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorSafetyStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTractorIFState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetTractorIFState',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorIFStateRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetTractorIFStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetReaperEnclosureSensors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetReaperEnclosureSensors',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperEnclosureSensorsRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperEnclosureSensorsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetReaperModuleSensors(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/GetReaperModuleSensors',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperModuleSensorsRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.GetReaperModuleSensorsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetReaperScannerPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetReaperScannerPower',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperScannerPowerRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperScannerPowerResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetReaperTargetPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetReaperTargetPower',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperTargetPowerRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperTargetPowerResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetReaperPredictCamPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetReaperPredictCamPower',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperPredictCamPowerRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperPredictCamPowerResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetReaperStrobeConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetReaperStrobeConfig',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeConfigRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetReaperStrobeEnable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetReaperStrobeEnable',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeEnableRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperStrobeEnableResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetReaperModulePcPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetReaperModulePcPower',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModulePcPowerRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModulePcPowerResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetReaperModuleLaserPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetReaperModuleLaserPower',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleLaserPowerRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleLaserPowerResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetReaperModuleStrobePower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/SetReaperModuleStrobePower',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleStrobePowerRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.SetReaperModuleStrobePowerResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def IdentifyModule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/hardware_manager.HardwareManagerService/IdentifyModule',
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.IdentifyModuleRequest.SerializeToString,
            hardware__manager_dot_proto_dot_hardware__manager__service__pb2.IdentifyModuleResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

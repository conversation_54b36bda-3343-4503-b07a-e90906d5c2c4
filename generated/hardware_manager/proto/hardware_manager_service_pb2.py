# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hardware_manager/proto/hardware_manager_service.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='hardware_manager/proto/hardware_manager_service.proto',
  package='hardware_manager',
  syntax='proto3',
  serialized_options=b'Z\026proto/hardware_manager',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n5hardware_manager/proto/hardware_manager_service.proto\x12\x10hardware_manager\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\r\"\x19\n\x0cPingResponse\x12\t\n\x01x\x18\x01 \x01(\r\"\x17\n\x15GetRotaryTicksRequest\"\x86\x02\n\x16GetRotaryTicksResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x18\n\x10\x66ront_left_ticks\x18\x02 \x01(\x03\x12\x19\n\x11\x66ront_right_ticks\x18\x03 \x01(\x03\x12\x17\n\x0f\x62\x61\x63k_left_ticks\x18\x04 \x01(\x03\x12\x18\n\x10\x62\x61\x63k_right_ticks\x18\x05 \x01(\x03\x12\x1a\n\x12\x66ront_left_enabled\x18\x06 \x01(\x08\x12\x1b\n\x13\x66ront_right_enabled\x18\x07 \x01(\x08\x12\x19\n\x11\x62\x61\x63k_left_enabled\x18\x08 \x01(\x08\x12\x1a\n\x12\x62\x61\x63k_right_enabled\x18\t \x01(\x08\".\n\x16GetNextDistanceRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\"A\n\x17GetNextDistanceResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x10\n\x08\x64istance\x18\x02 \x01(\x01\".\n\x16GetNextVelocityRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\"R\n\x17GetNextVelocityResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x11\n\tmm_per_ms\x18\x02 \x01(\x01\x12\x0e\n\x06lifted\x18\x03 \x01(\x08\"J\n\x15SetJimboxSpeedRequest\x12\x14\n\x0ctarget_speed\x18\x01 \x01(\x01\x12\x1b\n\x13\x61\x63tual_ground_speed\x18\x02 \x01(\x01\"0\n\x16SetJimboxSpeedResponse\x12\x16\n\x0espeed_setpoint\x18\x01 \x01(\x01\"*\n\x17SetCruiseEnabledRequest\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"+\n\x18SetCruiseEnabledResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x18\n\x16GetCruiseStatusRequest\"b\n\x17GetCruiseStatusResponse\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x11\n\tinstalled\x18\x02 \x01(\x08\x12\r\n\x05speed\x18\x03 \x01(\x01\x12\x14\n\x0c\x61llow_enable\x18\x04 \x01(\x08\"g\n\x18SetImplementStateRequest\x12\x0e\n\x06\x61\x63tive\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\x08\x12\x1a\n\rerror_message\x18\x03 \x01(\tH\x00\x88\x01\x01\x42\x10\n\x0e_error_message\"\x1b\n\x19SetImplementStateResponse\"2\n\x1eSetSafeStateEnforcementRequest\x12\x10\n\x08\x65nforced\x18\x01 \x01(\x08\"!\n\x1fSetSafeStateEnforcementResponse\"4\n\x1cGetTractorSafetyStateRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\"X\n\x1dGetTractorSafetyStateResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x0f\n\x07is_safe\x18\x02 \x01(\x08\x12\x10\n\x08\x65nforced\x18\x03 \x01(\x08\"\x1a\n\x18GetTractorIFStateRequest\"@\n\x19GetTractorIFStateResponse\x12\x10\n\x08\x65xpected\x18\x01 \x01(\x08\x12\x11\n\tconnected\x18\x02 \x01(\x08\"\x18\n\x16GetSafetyStatusRequest\"\xd9\x02\n\x17GetSafetyStatusResponse\x12\x0e\n\x06lifted\x18\x01 \x01(\x08\x12\x10\n\x08\x65stopped\x18\x02 \x01(\x08\x12\x17\n\x0fin_cab_estopped\x18\x03 \x01(\x08\x12\x15\n\rleft_estopped\x18\x04 \x01(\x08\x12\x16\n\x0eright_estopped\x18\x05 \x01(\x08\x12\x11\n\tlaser_key\x18\x06 \x01(\x08\x12\x11\n\tinterlock\x18\x07 \x01(\x08\x12\x15\n\rwater_protect\x18\x08 \x01(\x08\x12\x16\n\x0ereset_required\x18\t \x01(\x08\x12\x14\n\x0c\x63\x65nter_estop\x18\n \x01(\x08\x12\x1a\n\x12power_button_estop\x18\x0b \x01(\x08\x12\x1b\n\x13left_lpsu_interlock\x18\x0c \x01(\x08\x12\x1c\n\x14right_lpsu_interlock\x18\r \x01(\x08\x12\x12\n\ndebug_mode\x18\x0e \x01(\x08\"E\n\x06GeoLLA\x12\x0b\n\x03lat\x18\x01 \x01(\x01\x12\x0b\n\x03lng\x18\x02 \x01(\x01\x12\x0b\n\x03\x61lt\x18\x03 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x04 \x01(\x03\"@\n\x07GeoECEF\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x04 \x01(\x03\"%\n\x11GetGPSDataRequest\x12\x10\n\x08validate\x18\x01 \x01(\x08\"d\n\x12GetGPSDataResponse\x12%\n\x03lla\x18\x01 \x01(\x0b\x32\x18.hardware_manager.GeoLLA\x12\'\n\x04\x65\x63\x65\x66\x18\x02 \x01(\x0b\x32\x19.hardware_manager.GeoECEF\"-\n\x15GetNextGPSDataRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\"?\n\x16GetNextGPSDataResponse\x12%\n\x03lla\x18\x01 \x01(\x0b\x32\x18.hardware_manager.GeoLLA\"0\n\x18GetNextRawGPSDataRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\"4\n\x11ValueWithAccuracy\x12\r\n\x05value\x18\x01 \x01(\x01\x12\x10\n\x08\x61\x63\x63uracy\x18\x02 \x01(\x01\"\xf5\x03\n\x0b\x44ualGpsData\x12\x12\n\ngnss_valid\x18\x01 \x01(\x08\x12\x18\n\x10\x64iff_corrections\x18\x02 \x01(\x08\x12\x16\n\x0eis_moving_base\x18\x03 \x01(\x08\x12\x39\n\rcarrier_phase\x18\x04 \x01(\x0e\x32\".hardware_manager.CarrierPhaseSoln\x12\x14\n\x0ctimestamp_ms\x18\x05 \x01(\x04\x12\x37\n\x05north\x18\x06 \x01(\x0b\x32#.hardware_manager.ValueWithAccuracyH\x00\x88\x01\x01\x12\x36\n\x04\x65\x61st\x18\x07 \x01(\x0b\x32#.hardware_manager.ValueWithAccuracyH\x01\x88\x01\x01\x12\x36\n\x04\x64own\x18\x08 \x01(\x0b\x32#.hardware_manager.ValueWithAccuracyH\x02\x88\x01\x01\x12\x38\n\x06length\x18\t \x01(\x0b\x32#.hardware_manager.ValueWithAccuracyH\x03\x88\x01\x01\x12\x39\n\x07heading\x18\n \x01(\x0b\x32#.hardware_manager.ValueWithAccuracyH\x04\x88\x01\x01\x42\x08\n\x06_northB\x07\n\x05_eastB\x07\n\x05_downB\t\n\x07_lengthB\n\n\x08_heading\"\x85\x03\n\x19GetNextRawGPSDataResponse\x12\x10\n\x08have_fix\x18\x01 \x01(\x08\x12\x10\n\x08latitude\x18\x02 \x01(\x01\x12\x11\n\tlongitude\x18\x03 \x01(\x01\x12\x10\n\x08num_sats\x18\x04 \x01(\x05\x12\x0c\n\x04hdop\x18\x05 \x01(\x02\x12\x14\n\x0ctimestamp_ms\x18\x06 \x01(\x04\x12\x11\n\theight_mm\x18\x07 \x01(\x05\x12\x17\n\x0fhave_approx_fix\x18\x08 \x01(\x08\x12+\n\x08\x66ix_type\x18\t \x01(\x0e\x32\x19.hardware_manager.FixType\x12\x12\n\ngnss_valid\x18\n \x01(\x08\x12\x18\n\x10\x64iff_corrections\x18\x0b \x01(\x08\x12\x39\n\rcarrier_phase\x18\x0c \x01(\x0e\x32\".hardware_manager.CarrierPhaseSoln\x12\x30\n\x04\x64ual\x18\r \x01(\x0b\x32\x1d.hardware_manager.DualGpsDataH\x00\x88\x01\x01\x42\x07\n\x05_dual\"\x17\n\x15GetGPSFixedPosRequest\"4\n\x16GetGPSFixedPosResponse\x12\x0c\n\x04x_mm\x18\x01 \x01(\x02\x12\x0c\n\x04y_mm\x18\x02 \x01(\x02\"\x1e\n\x1cGetManagedBoardErrorsRequest\"z\n\x1dGetManagedBoardErrorsResponse\x12\r\n\x05\x62oard\x18\x01 \x03(\t\x12\x1a\n\x12\x65ncoder_error_flag\x18\x02 \x01(\x08\x12\x19\n\x11\x65ncoder_error_msg\x18\x03 \x01(\t\x12\x13\n\x0bgps_has_fix\x18\x04 \x01(\x08\"\x1d\n\x1bGetSupervisoryStatusRequest\"#\n!GetReaperSupervisoryStatusRequest\"\xb5\x0c\n\rChillerAlarms\x12\x19\n\x11low_level_in_tank\x18\x01 \x01(\x08\x12-\n%high_circulating_fluid_discharge_temp\x18\x02 \x01(\x08\x12-\n%circulating_fluid_discharge_temp_rise\x18\x03 \x01(\x08\x12-\n%circulating_fluid_discharge_temp_drop\x18\x04 \x01(\x08\x12*\n\"high_circulating_fluid_return_temp\x18\x05 \x01(\x08\x12\x31\n)circulating_fluid_discharge_pressure_rise\x18\x06 \x01(\x08\x12\x31\n)circulating_fluid_discharge_pressure_drop\x18\x07 \x01(\x08\x12$\n\x1chigh_compressor_suction_temp\x18\x08 \x01(\x08\x12#\n\x1blow_compressor_suction_temp\x18\t \x01(\x08\x12\x1b\n\x13low_super_heat_temp\x18\n \x01(\x08\x12*\n\"high_compressor_discharge_pressure\x18\x0b \x01(\x08\x12-\n%refrigerant_circut_pressure_high_drop\x18\x0c \x01(\x08\x12,\n$refrigerant_circut_pressure_low_rise\x18\r \x01(\x08\x12,\n$refrigerant_circut_pressure_low_drop\x18\x0e \x01(\x08\x12\"\n\x1a\x63ompressor_running_failure\x18\x0f \x01(\x08\x12\x1b\n\x13\x63ommunication_error\x18\x10 \x01(\x08\x12\x14\n\x0cmemory_error\x18\x11 \x01(\x08\x12\x18\n\x10\x64\x63_line_fuse_cut\x18\x12 \x01(\x08\x12\x37\n/circulating_fluid_discharge_temp_sensor_failure\x18\x13 \x01(\x08\x12\x34\n,circulating_fluid_return_temp_sensor_failure\x18\x14 \x01(\x08\x12\x35\n-circulating_fluid_suction_temp_sensor_failure\x18\x15 \x01(\x08\x12;\n3circulating_fluid_discharge_pressure_sensor_failure\x18\x16 \x01(\x08\x12\x34\n,compressor_discharge_pressure_sensor_failure\x18\x17 \x01(\x08\x12\x32\n*compressor_suction_pressure_sensor_failure\x18\x18 \x01(\x08\x12\x18\n\x10pump_maintenance\x18\x19 \x01(\x08\x12\x17\n\x0f\x66\x61n_maintenance\x18\x1a \x01(\x08\x12\x1e\n\x16\x63ompressor_maintenance\x18\x1b \x01(\x08\x12(\n contact_input_1_signal_detection\x18\x1c \x01(\x08\x12(\n contact_input_2_signal_detection\x18\x1d \x01(\x08\x12\x30\n(compressor_discharge_temp_sensor_failure\x18\x1e \x01(\x08\x12&\n\x1e\x63ompressor_discharge_temp_rise\x18\x1f \x01(\x08\x12$\n\x1c\x64ustproof_filter_maintenance\x18  \x01(\x08\x12\x16\n\x0epower_stoppage\x18! \x01(\x08\x12\x1a\n\x12\x63ompressor_waiting\x18\" \x01(\x08\x12\x13\n\x0b\x66\x61n_failure\x18# \x01(\x08\x12\x1f\n\x17\x63ompressor_over_current\x18$ \x01(\x08\x12\x19\n\x11pump_over_current\x18% \x01(\x08\x12 \n\x18\x61ir_exhaust_fan_stoppage\x18& \x01(\x08\x12\x1d\n\x15incorrect_phase_error\x18\' \x01(\x08\x12 \n\x18phase_board_over_current\x18( \x01(\x08\"\xed\x08\n\x1cGetSupervisoryStatusResponse\x12\x1c\n\x14water_protect_status\x18\x01 \x01(\x08\x12 \n\x18main_contactor_status_fb\x18\x02 \x01(\x08\x12\x12\n\npower_good\x18\x03 \x01(\x08\x12\x11\n\tpower_bad\x18\x04 \x01(\x08\x12\x16\n\x0epower_very_bad\x18\x05 \x01(\x08\x12\x15\n\rlifted_status\x18\x06 \x01(\x08\x12\x1c\n\x14temp_humidity_status\x18\x07 \x01(\x08\x12\x15\n\rtractor_power\x18\x08 \x01(\x08\x12\x14\n\x0c\x61\x63_frequency\x18\t \x01(\x01\x12\x16\n\x0e\x61\x63_voltage_a_b\x18\n \x01(\x01\x12\x16\n\x0e\x61\x63_voltage_b_c\x18\x0b \x01(\x01\x12\x16\n\x0e\x61\x63_voltage_a_c\x18\x0c \x01(\x01\x12\x14\n\x0c\x61\x63_voltage_a\x18\r \x01(\x01\x12\x14\n\x0c\x61\x63_voltage_b\x18\x0e \x01(\x01\x12\x14\n\x0c\x61\x63_voltage_c\x18\x0f \x01(\x01\x12\x17\n\x0fphase_power_w_3\x18\x10 \x01(\x03\x12\x18\n\x10phase_power_va_3\x18\x11 \x01(\x03\x12\x14\n\x0cpower_factor\x18\x12 \x01(\x01\x12\x1b\n\x13server_cabinet_temp\x18\x13 \x01(\x01\x12\x1f\n\x17server_cabinet_humidity\x18\x14 \x01(\x01\x12\x1b\n\x13\x62\x61ttery_voltage_12v\x18\x15 \x01(\x01\x12\'\n\x1btemp_humidity_bypass_status\x18\x16 \x01(\x08\x42\x02\x18\x01\x12\x1a\n\x12temp_bypass_status\x18\x17 \x01(\x08\x12\x1e\n\x16humidity_bypass_status\x18\x18 \x01(\x08\x12\x13\n\x0btemp_status\x18\x19 \x01(\x08\x12\x17\n\x0fhumidity_status\x18\x1a \x01(\x08\x12\x14\n\x0c\x62tl_disabled\x18\x1b \x03(\x08\x12\x17\n\x0fserver_disabled\x18\x1c \x03(\x08\x12\x19\n\x11scanners_disabled\x18\x1d \x03(\x08\x12\x1e\n\x16wheel_encoder_disabled\x18\x1e \x01(\x08\x12\x17\n\x0fstrobe_disabled\x18\x1f \x01(\x08\x12\x14\n\x0cgps_disabled\x18  \x01(\x08\x12\x1f\n\x17main_contactor_disabled\x18! \x01(\x08\x12 \n\x18\x61ir_conditioner_disabled\x18\" \x01(\x08\x12\x18\n\x10\x63hiller_disabled\x18# \x01(\x08\x12\x14\n\x0c\x63hiller_temp\x18$ \x01(\x01\x12\x14\n\x0c\x63hiller_flow\x18% \x01(\x01\x12\x18\n\x10\x63hiller_pressure\x18& \x01(\x01\x12\x1c\n\x14\x63hiller_conductivity\x18\' \x01(\x01\x12\x18\n\x10\x63hiller_set_temp\x18( \x01(\x01\x12\x37\n\x0e\x63hiller_alarms\x18) \x01(\x0b\x32\x1f.hardware_manager.ChillerAlarms\":\n\x17SetServerDisableRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\x03\x12\x0f\n\x07\x64isable\x18\x02 \x01(\x08\"+\n\x18SetServerDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"7\n\x14SetBTLDisableRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\x03\x12\x0f\n\x07\x64isable\x18\x02 \x01(\x08\"(\n\x15SetBTLDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"<\n\x19SetScannersDisableRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\x03\x12\x0f\n\x07\x64isable\x18\x02 \x01(\x08\"-\n\x1aSetScannersDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"5\n\"SetWheelEncoderBoardDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"6\n#SetWheelEncoderBoardDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"M\n\x1dSetWheelEncoderDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\x12\r\n\x05\x66ront\x18\x02 \x01(\x08\x12\x0c\n\x04left\x18\x03 \x01(\x08\"1\n\x1eSetWheelEncoderDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"*\n\x17SetStrobeDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"+\n\x18SetStrobeDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\'\n\x14SetGPSDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"(\n\x15SetGPSDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\"\n CommandComputerPowerCycleRequest\"4\n!CommandComputerPowerCycleResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x16\n\x14SuicideSwitchRequest\"(\n\x15SuicideSwitchResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"1\n\x1eSetMainContactorDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"2\n\x1fSetMainContactorDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"2\n\x1fSetAirConditionerDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"3\n SetAirConditionerDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"+\n\x18SetChillerDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\",\n\x19SetChillerDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\".\n\x1bSetTempBypassDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"/\n\x1cSetTempBypassDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"2\n\x1fSetHumidityBypassDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"3\n SetHumidityBypassDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x1f\n\x1dGetAvailableUSBStorageRequest\"V\n\x1eGetAvailableUSBStorageResponse\x12\x0c\n\x04used\x18\x01 \x01(\x02\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rusb_available\x18\x03 \x01(\x08\"\x11\n\x0fGetReadyRequest\"!\n\x10GetReadyResponse\x12\r\n\x05ready\x18\x01 \x01(\x08\"\x16\n\x14Get240vUptimeRequest\")\n\x15Get240vUptimeResponse\x12\x10\n\x08uptime_s\x18\x01 \x01(\x03\"%\n\x17GetDeltaTravelMMRequest\x12\n\n\x02id\x18\x01 \x01(\t\",\n\x18GetDeltaTravelMMResponse\x12\x10\n\x08\x64\x65lta_mm\x18\x01 \x01(\x01\"\x13\n\x11GetRuntimeRequest\"*\n\x12GetRuntimeResponse\x12\x14\n\x0cruntime_240v\x18\x01 \x01(\r\"\"\n GetWheelEncoderResolutionRequest\"7\n!GetWheelEncoderResolutionResponse\x12\x12\n\nresolution\x18\x01 \x01(\r\"\xa6\x01\n\x0eStrobeSettings\x12\x18\n\x0b\x65xposure_us\x18\x01 \x01(\rH\x00\x88\x01\x01\x12\x16\n\tperiod_us\x18\x02 \x01(\rH\x01\x88\x01\x01\x12&\n\x19targets_per_predict_ratio\x18\x03 \x01(\rH\x02\x88\x01\x01\x42\x0e\n\x0c_exposure_usB\x0c\n\n_period_usB\x1c\n\x1a_targets_per_predict_ratio\",\n\x19SetStrobeSettingsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x1a\n\x18GetStrobeSettingsRequest\"[\n\x17\x45nvironmentalSensorData\x12\x15\n\rtemperature_c\x18\x01 \x01(\x01\x12\x13\n\x0bhumidity_rh\x18\x02 \x01(\x01\x12\x14\n\x0cpressure_hpa\x18\x03 \x01(\x01\"@\n\x11\x43oolantSensorData\x12\x15\n\rtemperature_c\x18\x01 \x01(\x01\x12\x14\n\x0cpressure_kpa\x18\x02 \x01(\x01\"\xa3\x01\n\x10NetworkPortState\x12\x0f\n\x07link_up\x18\x01 \x01(\x08\x12=\n\x11\x61\x63tual_link_speed\x18\x02 \x01(\x0e\x32\".hardware_manager.NetworkLinkSpeed\x12?\n\x13\x65xpected_link_speed\x18\x03 \x01(\x0e\x32\".hardware_manager.NetworkLinkSpeed\"\xa8\x05\n\x12ReaperPcSensorData\x12\x1e\n\x16temperature_cpu_core_c\x18\x01 \x01(\x01\x12\x1c\n\x14temperature_system_c\x18\x02 \x01(\x01\x12 \n\x13temperature_gpu_1_c\x18\x03 \x01(\x01H\x00\x88\x01\x01\x12 \n\x13temperature_gpu_2_c\x18\x04 \x01(\x01H\x01\x88\x01\x01\x12\x0f\n\x07psu_12v\x18\x05 \x01(\x01\x12\x0e\n\x06psu_5v\x18\x06 \x01(\x01\x12\x0f\n\x07psu_3v3\x18\x07 \x01(\x01\x12\x0c\n\x04load\x18\x08 \x01(\x01\x12\x0e\n\x06uptime\x18\t \x01(\r\x12\x19\n\x11ram_usage_percent\x18\n \x01(\x01\x12\x1a\n\x12\x64isk_usage_percent\x18\x0b \x01(\x01\x12\x38\n\x0cscanner_link\x18\x0c \x03(\x0b\x32\".hardware_manager.NetworkPortState\x12;\n\x0ftarget_cam_link\x18\r \x03(\x0b\x32\".hardware_manager.NetworkPortState\x12<\n\x10predict_cam_link\x18\x0e \x01(\x0b\x32\".hardware_manager.NetworkPortState\x12\x35\n\tipmi_link\x18\x0f \x01(\x0b\x32\".hardware_manager.NetworkPortState\x12\x37\n\x0bglobal_link\x18\x10 \x01(\x0b\x32\".hardware_manager.NetworkPortState\x12\x34\n\x08\x65xt_link\x18\x11 \x01(\x0b\x32\".hardware_manager.NetworkPortStateB\x16\n\x14_temperature_gpu_1_cB\x16\n\x14_temperature_gpu_2_c\"\x9d\x01\n\x18ReaperScannerLaserStatus\x12\r\n\x05model\x18\x01 \x01(\t\x12\n\n\x02sn\x18\x02 \x01(\t\x12\x13\n\x0brated_power\x18\x03 \x01(\r\x12\x15\n\rtemperature_c\x18\x04 \x01(\x01\x12\x10\n\x08humidity\x18\x05 \x01(\x01\x12\x18\n\x10laser_current_ma\x18\x06 \x01(\x01\x12\x0e\n\x06\x66\x61ults\x18\x07 \x03(\t\"\x98\x01\n\x16ReaperScannerMotorData\x12\x15\n\rcontroller_sn\x18\x01 \x01(\t\x12\x1c\n\x14temperature_output_c\x18\x02 \x01(\x01\x12\x16\n\x0emotor_supply_v\x18\x03 \x01(\x01\x12\x17\n\x0fmotor_current_a\x18\x04 \x01(\x01\x12\x18\n\x10\x65ncoder_position\x18\x05 \x01(\x03\"\x8b\x05\n\x17ReaperScannerSensorData\x12\x12\n\nscanner_sn\x18\x01 \x01(\t\x12\x10\n\x08power_on\x18\x02 \x01(\x08\x12\x11\n\tcurrent_a\x18\x03 \x01(\x01\x12\x14\n\x0c\x66use_tripped\x18\x04 \x01(\x08\x12 \n\x18temperature_collimator_c\x18\x05 \x01(\x01\x12\x1b\n\x13temperature_fiber_c\x18\x06 \x01(\x01\x12\x15\n\rlaser_power_w\x18\x07 \x01(\x01\x12\x1a\n\x12laser_power_raw_mv\x18\x10 \x01(\x01\x12\x17\n\x0flaser_connected\x18\x08 \x01(\x08\x12\x45\n\x0claser_status\x18\t \x01(\x0b\x32*.hardware_manager.ReaperScannerLaserStatusH\x00\x88\x01\x01\x12\x18\n\x10target_connected\x18\n \x01(\x08\x12\x16\n\ttarget_sn\x18\x0b \x01(\tH\x01\x88\x01\x01\x12!\n\x14temperature_target_c\x18\x0c \x01(\x01H\x02\x88\x01\x01\x12@\n\tmotor_pan\x18\r \x01(\x0b\x32(.hardware_manager.ReaperScannerMotorDataH\x03\x88\x01\x01\x12\x41\n\nmotor_tilt\x18\x0e \x01(\x0b\x32(.hardware_manager.ReaperScannerMotorDataH\x04\x88\x01\x01\x12 \n\x18target_cam_power_enabled\x18\x0f \x01(\x08\x42\x0f\n\r_laser_statusB\x0c\n\n_target_snB\x17\n\x15_temperature_target_cB\x0c\n\n_motor_panB\r\n\x0b_motor_tilt\"\xb0\x07\n\x19ReaperCenterEnclosureData\x12\x1c\n\x14water_protect_status\x18\x01 \x01(\x08\x12 \n\x18main_contactor_status_fb\x18\x02 \x01(\x08\x12\x12\n\npower_good\x18\x03 \x01(\x08\x12\x11\n\tpower_bad\x18\x04 \x01(\x08\x12\x16\n\x0epower_very_bad\x18\x05 \x01(\x08\x12\x15\n\rlifted_status\x18\x06 \x01(\x08\x12\x15\n\rtractor_power\x18\x07 \x01(\x08\x12\x14\n\x0c\x61\x63_frequency\x18\x08 \x01(\x01\x12\x16\n\x0e\x61\x63_voltage_a_b\x18\t \x01(\x01\x12\x16\n\x0e\x61\x63_voltage_b_c\x18\n \x01(\x01\x12\x16\n\x0e\x61\x63_voltage_a_c\x18\x0b \x01(\x01\x12\x14\n\x0c\x61\x63_voltage_a\x18\x0c \x01(\x01\x12\x14\n\x0c\x61\x63_voltage_b\x18\r \x01(\x01\x12\x14\n\x0c\x61\x63_voltage_c\x18\x0e \x01(\x01\x12\x17\n\x0fphase_power_w_3\x18\x0f \x01(\x03\x12\x18\n\x10phase_power_va_3\x18\x10 \x01(\x03\x12\x14\n\x0cpower_factor\x18\x11 \x01(\x01\x12\x1b\n\x13server_cabinet_temp\x18\x12 \x01(\x01\x12\x1f\n\x17server_cabinet_humidity\x18\x13 \x01(\x01\x12\x1b\n\x13\x62\x61ttery_voltage_12v\x18\x14 \x01(\x01\x12\x1e\n\x16wheel_encoder_disabled\x18\x15 \x01(\x08\x12\x17\n\x0fstrobe_disabled\x18\x16 \x01(\x08\x12\x14\n\x0cgps_disabled\x18\x17 \x01(\x08\x12\x1f\n\x17main_contactor_disabled\x18\x18 \x01(\x08\x12 \n\x18\x61ir_conditioner_disabled\x18\x19 \x01(\x08\x12\x18\n\x10\x63hiller_disabled\x18\x1a \x01(\x08\x12\x14\n\x0c\x63hiller_temp\x18\x1b \x01(\x01\x12\x14\n\x0c\x63hiller_flow\x18\x1c \x01(\x01\x12\x18\n\x10\x63hiller_pressure\x18\x1d \x01(\x01\x12\x1c\n\x14\x63hiller_conductivity\x18\x1e \x01(\x01\x12\x18\n\x10\x63hiller_set_temp\x18\x1f \x01(\x01\x12\x1d\n\x15\x63hiller_heat_transfer\x18  \x01(\x01\x12 \n\x18\x63hiller_fluid_delta_temp\x18! \x01(\x01\x12\x37\n\x0e\x63hiller_alarms\x18\" \x01(\x0b\x32\x1f.hardware_manager.ChillerAlarms\"\x90\x06\n\x16ReaperModuleSensorData\x12\x11\n\tmodule_id\x18\x01 \x01(\x05\x12\x16\n\tmodule_sn\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x43\n\x10\x65nviro_enclosure\x18\x03 \x01(\x0b\x32).hardware_manager.EnvironmentalSensorData\x12<\n\tenviro_pc\x18\x04 \x01(\x0b\x32).hardware_manager.EnvironmentalSensorData\x12:\n\rcoolant_inlet\x18\x05 \x01(\x0b\x32#.hardware_manager.CoolantSensorData\x12;\n\x0e\x63oolant_outlet\x18\x06 \x01(\x0b\x32#.hardware_manager.CoolantSensorData\x12\x1c\n\x14strobe_temperature_c\x18\x07 \x01(\x01\x12\x1a\n\x12strobe_cap_voltage\x18\x08 \x01(\x01\x12\x16\n\x0estrobe_current\x18\t \x01(\x01\x12\x35\n\x02pc\x18\n \x01(\x0b\x32$.hardware_manager.ReaperPcSensorDataH\x01\x88\x01\x01\x12\x41\n\tscanner_a\x18\x0b \x01(\x0b\x32).hardware_manager.ReaperScannerSensorDataH\x02\x88\x01\x01\x12\x41\n\tscanner_b\x18\x0c \x01(\x0b\x32).hardware_manager.ReaperScannerSensorDataH\x03\x88\x01\x01\x12\x18\n\x10pc_power_enabled\x18\r \x01(\x08\x12\x1c\n\x14lasers_power_enabled\x18\x0e \x01(\x08\x12!\n\x19predict_cam_power_enabled\x18\x0f \x01(\x08\x12\x1c\n\x14strobe_power_enabled\x18\x10 \x01(\x08\x12\x16\n\x0estrobe_enabled\x18\x11 \x01(\x08\x42\x0c\n\n_module_snB\x05\n\x03_pcB\x0c\n\n_scanner_aB\x0c\n\n_scanner_b\"\"\n GetReaperEnclosureSensorsRequest\"a\n!GetReaperEnclosureSensorsResponse\x12<\n\x07sensors\x18\x01 \x01(\x0b\x32+.hardware_manager.ReaperCenterEnclosureData\"3\n\x1dGetReaperModuleSensorsRequest\x12\x12\n\nmodule_ids\x18\x01 \x03(\r\"b\n\x1eGetReaperModuleSensorsResponse\x12@\n\x0emodule_sensors\x18\x01 \x03(\x0b\x32(.hardware_manager.ReaperModuleSensorData\"\x95\x01\n\x1cSetReaperScannerPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x1c\n\x0fscanner_a_power\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x1c\n\x0fscanner_b_power\x18\x03 \x01(\x08H\x01\x88\x01\x01\x42\x12\n\x10_scanner_a_powerB\x12\n\x10_scanner_b_power\"0\n\x1dSetReaperScannerPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x90\x01\n\x1bSetReaperTargetPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x1b\n\x0etarget_a_power\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x1b\n\x0etarget_b_power\x18\x03 \x01(\x08H\x01\x88\x01\x01\x42\x11\n\x0f_target_a_powerB\x11\n\x0f_target_b_power\"/\n\x1cSetReaperTargetPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"E\n\x1fSetReaperPredictCamPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"3\n SetReaperPredictCamPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"x\n\x1cSetReaperStrobeConfigRequest\x12\x16\n\tmodule_id\x18\x01 \x01(\rH\x00\x88\x01\x01\x12\x32\n\x08settings\x18\x02 \x01(\x0b\x32 .hardware_manager.StrobeSettingsB\x0c\n\n_module_id\"0\n\x1dSetReaperStrobeConfigResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"m\n\x1cSetReaperStrobeEnableRequest\x12\x12\n\nmodule_ids\x18\x01 \x03(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\x12\x18\n\x0b\x64uration_ms\x18\x03 \x01(\rH\x00\x88\x01\x01\x42\x0e\n\x0c_duration_ms\"0\n\x1dSetReaperStrobeEnableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"C\n\x1dSetReaperModulePcPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"1\n\x1eSetReaperModulePcPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"F\n SetReaperModuleLaserPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"4\n!SetReaperModuleLaserPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"G\n!SetReaperModuleStrobePowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"5\n\"SetReaperModuleStrobePowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"?\n\x15IdentifyModuleRequest\x12\x10\n\x08to_id_ip\x18\x01 \x01(\t\x12\x14\n\x0cturn_off_ips\x18\x02 \x03(\t\")\n\x16IdentifyModuleResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08*5\n\x10\x43\x61rrierPhaseSoln\x12\x08\n\x04NONE\x10\x00\x12\x0c\n\x08\x46LOATING\x10\x01\x12\t\n\x05\x46IXED\x10\x02*b\n\x07\x46ixType\x12\n\n\x06NO_FIX\x10\x00\x12\x17\n\x13\x44\x45\x41\x44_RECKONING_ONLY\x10\x01\x12\n\n\x06\x46IX_2D\x10\x02\x12\n\n\x06\x46IX_3D\x10\x03\x12\x0b\n\x07GNSS_DR\x10\x04\x12\r\n\tTIME_ONLY\x10\x05*\xbf\x01\n\x10NetworkLinkSpeed\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x12\n\x0eSPEED_10M_HALF\x10\x01\x12\x12\n\x0eSPEED_10M_FULL\x10\x02\x12\x13\n\x0fSPEED_100M_HALF\x10\x03\x12\x13\n\x0fSPEED_100M_FULL\x10\x04\x12\x11\n\rSPEED_1G_FULL\x10\x05\x12\x12\n\x0eSPEED_2G5_FULL\x10\x06\x12\x11\n\rSPEED_5G_FULL\x10\x07\x12\x12\n\x0eSPEED_10G_FULL\x10\x08\x32\x8f/\n\x16HardwareManagerService\x12G\n\x04Ping\x12\x1d.hardware_manager.PingRequest\x1a\x1e.hardware_manager.PingResponse\"\x00\x12S\n\x08GetReady\x12!.hardware_manager.GetReadyRequest\x1a\".hardware_manager.GetReadyResponse\"\x00\x12h\n\x0fGetNextDistance\x12(.hardware_manager.GetNextDistanceRequest\x1a).hardware_manager.GetNextDistanceResponse\"\x00\x12h\n\x0fGetNextVelocity\x12(.hardware_manager.GetNextVelocityRequest\x1a).hardware_manager.GetNextVelocityResponse\"\x00\x12\x65\n\x0eGetRotaryTicks\x12\'.hardware_manager.GetRotaryTicksRequest\x1a(.hardware_manager.GetRotaryTicksResponse\"\x00\x12k\n\x10GetDeltaTravelMM\x12).hardware_manager.GetDeltaTravelMMRequest\x1a*.hardware_manager.GetDeltaTravelMMResponse\"\x00\x12\x86\x01\n\x19GetWheelEncoderResolution\x12\x32.hardware_manager.GetWheelEncoderResolutionRequest\x1a\x33.hardware_manager.GetWheelEncoderResolutionResponse\"\x00\x12h\n\x0fGetSafetyStatus\x12(.hardware_manager.GetSafetyStatusRequest\x1a).hardware_manager.GetSafetyStatusResponse\"\x00\x12Y\n\nGetGPSData\x12#.hardware_manager.GetGPSDataRequest\x1a$.hardware_manager.GetGPSDataResponse\"\x00\x12\x65\n\x0eGetNextGPSData\x12\'.hardware_manager.GetNextGPSDataRequest\x1a(.hardware_manager.GetNextGPSDataResponse\"\x00\x12n\n\x11GetNextRawGPSData\x12*.hardware_manager.GetNextRawGPSDataRequest\x1a+.hardware_manager.GetNextRawGPSDataResponse\"\x00\x12\x65\n\x0eGetGPSFixedPos\x12\'.hardware_manager.GetGPSFixedPosRequest\x1a(.hardware_manager.GetGPSFixedPosResponse\"\x00\x12\x64\n\x11SetStrobeSettings\x12 .hardware_manager.StrobeSettings\x1a+.hardware_manager.SetStrobeSettingsResponse\"\x00\x12\x63\n\x11GetStrobeSettings\x12*.hardware_manager.GetStrobeSettingsRequest\x1a .hardware_manager.StrobeSettings\"\x00\x12z\n\x15GetManagedBoardErrors\x12..hardware_manager.GetManagedBoardErrorsRequest\x1a/.hardware_manager.GetManagedBoardErrorsResponse\"\x00\x12w\n\x14GetSupervisoryStatus\x12-.hardware_manager.GetSupervisoryStatusRequest\x1a..hardware_manager.GetSupervisoryStatusResponse\"\x00\x12\x80\x01\n\x1aGetReaperSupervisoryStatus\x12\x33.hardware_manager.GetReaperSupervisoryStatusRequest\x1a+.hardware_manager.ReaperCenterEnclosureData\"\x00\x12k\n\x10SetServerDisable\x12).hardware_manager.SetServerDisableRequest\x1a*.hardware_manager.SetServerDisableResponse\"\x00\x12\x62\n\rSetBTLDisable\x12&.hardware_manager.SetBTLDisableRequest\x1a\'.hardware_manager.SetBTLDisableResponse\"\x00\x12q\n\x12SetScannersDisable\x12+.hardware_manager.SetScannersDisableRequest\x1a,.hardware_manager.SetScannersDisableResponse\"\x00\x12\x8c\x01\n\x1bSetWheelEncoderBoardDisable\x12\x34.hardware_manager.SetWheelEncoderBoardDisableRequest\x1a\x35.hardware_manager.SetWheelEncoderBoardDisableResponse\"\x00\x12}\n\x16SetWheelEncoderDisable\x12/.hardware_manager.SetWheelEncoderDisableRequest\x1a\x30.hardware_manager.SetWheelEncoderDisableResponse\"\x00\x12\x62\n\rSetGPSDisable\x12&.hardware_manager.SetGPSDisableRequest\x1a\'.hardware_manager.SetGPSDisableResponse\"\x00\x12\x62\n\rSuicideSwitch\x12&.hardware_manager.SuicideSwitchRequest\x1a\'.hardware_manager.SuicideSwitchResponse\"\x00\x12\x86\x01\n\x19\x43ommandComputerPowerCycle\x12\x32.hardware_manager.CommandComputerPowerCycleRequest\x1a\x33.hardware_manager.CommandComputerPowerCycleResponse\"\x00\x12\x80\x01\n\x17SetMainContactorDisable\x12\x30.hardware_manager.SetMainContactorDisableRequest\x1a\x31.hardware_manager.SetMainContactorDisableResponse\"\x00\x12k\n\x10SetStrobeDisable\x12).hardware_manager.SetStrobeDisableRequest\x1a*.hardware_manager.SetStrobeDisableResponse\"\x00\x12\x83\x01\n\x18SetAirConditionerDisable\x12\x31.hardware_manager.SetAirConditionerDisableRequest\x1a\x32.hardware_manager.SetAirConditionerDisableResponse\"\x00\x12n\n\x11SetChillerDisable\x12*.hardware_manager.SetChillerDisableRequest\x1a+.hardware_manager.SetChillerDisableResponse\"\x00\x12w\n\x14SetTempBypassDisable\x12-.hardware_manager.SetTempBypassDisableRequest\x1a..hardware_manager.SetTempBypassDisableResponse\"\x00\x12\x83\x01\n\x18SetHumidityBypassDisable\x12\x31.hardware_manager.SetHumidityBypassDisableRequest\x1a\x32.hardware_manager.SetHumidityBypassDisableResponse\"\x00\x12\x62\n\rGet240vUptime\x12&.hardware_manager.Get240vUptimeRequest\x1a\'.hardware_manager.Get240vUptimeResponse\"\x00\x12Y\n\nGetRuntime\x12#.hardware_manager.GetRuntimeRequest\x1a$.hardware_manager.GetRuntimeResponse\"\x00\x12}\n\x16GetAvailableUSBStorage\x12/.hardware_manager.GetAvailableUSBStorageRequest\x1a\x30.hardware_manager.GetAvailableUSBStorageResponse\"\x00\x12\x65\n\x0eSetJimboxSpeed\x12\'.hardware_manager.SetJimboxSpeedRequest\x1a(.hardware_manager.SetJimboxSpeedResponse\"\x00\x12k\n\x10SetCruiseEnabled\x12).hardware_manager.SetCruiseEnabledRequest\x1a*.hardware_manager.SetCruiseEnabledResponse\"\x00\x12h\n\x0fGetCruiseStatus\x12(.hardware_manager.GetCruiseStatusRequest\x1a).hardware_manager.GetCruiseStatusResponse\"\x00\x12w\n\x1aSetImplementStateOnTractor\x12*.hardware_manager.SetImplementStateRequest\x1a+.hardware_manager.SetImplementStateResponse\"\x00\x12\x80\x01\n\x17SetSafeStateEnforcement\x12\x30.hardware_manager.SetSafeStateEnforcementRequest\x1a\x31.hardware_manager.SetSafeStateEnforcementResponse\"\x00\x12z\n\x15GetTractorSafetyState\x12..hardware_manager.GetTractorSafetyStateRequest\x1a/.hardware_manager.GetTractorSafetyStateResponse\"\x00\x12n\n\x11GetTractorIFState\x12*.hardware_manager.GetTractorIFStateRequest\x1a+.hardware_manager.GetTractorIFStateResponse\"\x00\x12\x86\x01\n\x19GetReaperEnclosureSensors\x12\x32.hardware_manager.GetReaperEnclosureSensorsRequest\x1a\x33.hardware_manager.GetReaperEnclosureSensorsResponse\"\x00\x12}\n\x16GetReaperModuleSensors\x12/.hardware_manager.GetReaperModuleSensorsRequest\x1a\x30.hardware_manager.GetReaperModuleSensorsResponse\"\x00\x12z\n\x15SetReaperScannerPower\x12..hardware_manager.SetReaperScannerPowerRequest\x1a/.hardware_manager.SetReaperScannerPowerResponse\"\x00\x12w\n\x14SetReaperTargetPower\x12-.hardware_manager.SetReaperTargetPowerRequest\x1a..hardware_manager.SetReaperTargetPowerResponse\"\x00\x12\x83\x01\n\x18SetReaperPredictCamPower\x12\x31.hardware_manager.SetReaperPredictCamPowerRequest\x1a\x32.hardware_manager.SetReaperPredictCamPowerResponse\"\x00\x12z\n\x15SetReaperStrobeConfig\x12..hardware_manager.SetReaperStrobeConfigRequest\x1a/.hardware_manager.SetReaperStrobeConfigResponse\"\x00\x12z\n\x15SetReaperStrobeEnable\x12..hardware_manager.SetReaperStrobeEnableRequest\x1a/.hardware_manager.SetReaperStrobeEnableResponse\"\x00\x12}\n\x16SetReaperModulePcPower\x12/.hardware_manager.SetReaperModulePcPowerRequest\x1a\x30.hardware_manager.SetReaperModulePcPowerResponse\"\x00\x12\x86\x01\n\x19SetReaperModuleLaserPower\x12\x32.hardware_manager.SetReaperModuleLaserPowerRequest\x1a\x33.hardware_manager.SetReaperModuleLaserPowerResponse\"\x00\x12\x89\x01\n\x1aSetReaperModuleStrobePower\x12\x33.hardware_manager.SetReaperModuleStrobePowerRequest\x1a\x34.hardware_manager.SetReaperModuleStrobePowerResponse\"\x00\x12\x65\n\x0eIdentifyModule\x12\'.hardware_manager.IdentifyModuleRequest\x1a(.hardware_manager.IdentifyModuleResponse\"\x00\x42\x18Z\x16proto/hardware_managerb\x06proto3'
)

_CARRIERPHASESOLN = _descriptor.EnumDescriptor(
  name='CarrierPhaseSoln',
  full_name='hardware_manager.CarrierPhaseSoln',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FLOATING', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FIXED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13707,
  serialized_end=13760,
)
_sym_db.RegisterEnumDescriptor(_CARRIERPHASESOLN)

CarrierPhaseSoln = enum_type_wrapper.EnumTypeWrapper(_CARRIERPHASESOLN)
_FIXTYPE = _descriptor.EnumDescriptor(
  name='FixType',
  full_name='hardware_manager.FixType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NO_FIX', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DEAD_RECKONING_ONLY', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FIX_2D', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FIX_3D', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GNSS_DR', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TIME_ONLY', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13762,
  serialized_end=13860,
)
_sym_db.RegisterEnumDescriptor(_FIXTYPE)

FixType = enum_type_wrapper.EnumTypeWrapper(_FIXTYPE)
_NETWORKLINKSPEED = _descriptor.EnumDescriptor(
  name='NetworkLinkSpeed',
  full_name='hardware_manager.NetworkLinkSpeed',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_10M_HALF', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_10M_FULL', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_100M_HALF', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_100M_FULL', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_1G_FULL', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_2G5_FULL', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_5G_FULL', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_10G_FULL', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13863,
  serialized_end=14054,
)
_sym_db.RegisterEnumDescriptor(_NETWORKLINKSPEED)

NetworkLinkSpeed = enum_type_wrapper.EnumTypeWrapper(_NETWORKLINKSPEED)
NONE = 0
FLOATING = 1
FIXED = 2
NO_FIX = 0
DEAD_RECKONING_ONLY = 1
FIX_2D = 2
FIX_3D = 3
GNSS_DR = 4
TIME_ONLY = 5
UNKNOWN = 0
SPEED_10M_HALF = 1
SPEED_10M_FULL = 2
SPEED_100M_HALF = 3
SPEED_100M_FULL = 4
SPEED_1G_FULL = 5
SPEED_2G5_FULL = 6
SPEED_5G_FULL = 7
SPEED_10G_FULL = 8



_PINGREQUEST = _descriptor.Descriptor(
  name='PingRequest',
  full_name='hardware_manager.PingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='hardware_manager.PingRequest.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=75,
  serialized_end=99,
)


_PINGRESPONSE = _descriptor.Descriptor(
  name='PingResponse',
  full_name='hardware_manager.PingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='hardware_manager.PingResponse.x', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=101,
  serialized_end=126,
)


_GETROTARYTICKSREQUEST = _descriptor.Descriptor(
  name='GetRotaryTicksRequest',
  full_name='hardware_manager.GetRotaryTicksRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=128,
  serialized_end=151,
)


_GETROTARYTICKSRESPONSE = _descriptor.Descriptor(
  name='GetRotaryTicksResponse',
  full_name='hardware_manager.GetRotaryTicksResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetRotaryTicksResponse.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_left_ticks', full_name='hardware_manager.GetRotaryTicksResponse.front_left_ticks', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_right_ticks', full_name='hardware_manager.GetRotaryTicksResponse.front_right_ticks', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='back_left_ticks', full_name='hardware_manager.GetRotaryTicksResponse.back_left_ticks', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='back_right_ticks', full_name='hardware_manager.GetRotaryTicksResponse.back_right_ticks', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_left_enabled', full_name='hardware_manager.GetRotaryTicksResponse.front_left_enabled', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_right_enabled', full_name='hardware_manager.GetRotaryTicksResponse.front_right_enabled', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='back_left_enabled', full_name='hardware_manager.GetRotaryTicksResponse.back_left_enabled', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='back_right_enabled', full_name='hardware_manager.GetRotaryTicksResponse.back_right_enabled', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=154,
  serialized_end=416,
)


_GETNEXTDISTANCEREQUEST = _descriptor.Descriptor(
  name='GetNextDistanceRequest',
  full_name='hardware_manager.GetNextDistanceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetNextDistanceRequest.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=418,
  serialized_end=464,
)


_GETNEXTDISTANCERESPONSE = _descriptor.Descriptor(
  name='GetNextDistanceResponse',
  full_name='hardware_manager.GetNextDistanceResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetNextDistanceResponse.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='distance', full_name='hardware_manager.GetNextDistanceResponse.distance', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=466,
  serialized_end=531,
)


_GETNEXTVELOCITYREQUEST = _descriptor.Descriptor(
  name='GetNextVelocityRequest',
  full_name='hardware_manager.GetNextVelocityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetNextVelocityRequest.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=533,
  serialized_end=579,
)


_GETNEXTVELOCITYRESPONSE = _descriptor.Descriptor(
  name='GetNextVelocityResponse',
  full_name='hardware_manager.GetNextVelocityResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetNextVelocityResponse.timestamp_ms', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mm_per_ms', full_name='hardware_manager.GetNextVelocityResponse.mm_per_ms', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifted', full_name='hardware_manager.GetNextVelocityResponse.lifted', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=581,
  serialized_end=663,
)


_SETJIMBOXSPEEDREQUEST = _descriptor.Descriptor(
  name='SetJimboxSpeedRequest',
  full_name='hardware_manager.SetJimboxSpeedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_speed', full_name='hardware_manager.SetJimboxSpeedRequest.target_speed', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='actual_ground_speed', full_name='hardware_manager.SetJimboxSpeedRequest.actual_ground_speed', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=665,
  serialized_end=739,
)


_SETJIMBOXSPEEDRESPONSE = _descriptor.Descriptor(
  name='SetJimboxSpeedResponse',
  full_name='hardware_manager.SetJimboxSpeedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='speed_setpoint', full_name='hardware_manager.SetJimboxSpeedResponse.speed_setpoint', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=741,
  serialized_end=789,
)


_SETCRUISEENABLEDREQUEST = _descriptor.Descriptor(
  name='SetCruiseEnabledRequest',
  full_name='hardware_manager.SetCruiseEnabledRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='hardware_manager.SetCruiseEnabledRequest.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=791,
  serialized_end=833,
)


_SETCRUISEENABLEDRESPONSE = _descriptor.Descriptor(
  name='SetCruiseEnabledResponse',
  full_name='hardware_manager.SetCruiseEnabledResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetCruiseEnabledResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=835,
  serialized_end=878,
)


_GETCRUISESTATUSREQUEST = _descriptor.Descriptor(
  name='GetCruiseStatusRequest',
  full_name='hardware_manager.GetCruiseStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=880,
  serialized_end=904,
)


_GETCRUISESTATUSRESPONSE = _descriptor.Descriptor(
  name='GetCruiseStatusResponse',
  full_name='hardware_manager.GetCruiseStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='hardware_manager.GetCruiseStatusResponse.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='installed', full_name='hardware_manager.GetCruiseStatusResponse.installed', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed', full_name='hardware_manager.GetCruiseStatusResponse.speed', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='allow_enable', full_name='hardware_manager.GetCruiseStatusResponse.allow_enable', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=906,
  serialized_end=1004,
)


_SETIMPLEMENTSTATEREQUEST = _descriptor.Descriptor(
  name='SetImplementStateRequest',
  full_name='hardware_manager.SetImplementStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='active', full_name='hardware_manager.SetImplementStateRequest.active', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error', full_name='hardware_manager.SetImplementStateRequest.error', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_message', full_name='hardware_manager.SetImplementStateRequest.error_message', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_error_message', full_name='hardware_manager.SetImplementStateRequest._error_message',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1006,
  serialized_end=1109,
)


_SETIMPLEMENTSTATERESPONSE = _descriptor.Descriptor(
  name='SetImplementStateResponse',
  full_name='hardware_manager.SetImplementStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1111,
  serialized_end=1138,
)


_SETSAFESTATEENFORCEMENTREQUEST = _descriptor.Descriptor(
  name='SetSafeStateEnforcementRequest',
  full_name='hardware_manager.SetSafeStateEnforcementRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enforced', full_name='hardware_manager.SetSafeStateEnforcementRequest.enforced', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1140,
  serialized_end=1190,
)


_SETSAFESTATEENFORCEMENTRESPONSE = _descriptor.Descriptor(
  name='SetSafeStateEnforcementResponse',
  full_name='hardware_manager.SetSafeStateEnforcementResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1192,
  serialized_end=1225,
)


_GETTRACTORSAFETYSTATEREQUEST = _descriptor.Descriptor(
  name='GetTractorSafetyStateRequest',
  full_name='hardware_manager.GetTractorSafetyStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetTractorSafetyStateRequest.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1227,
  serialized_end=1279,
)


_GETTRACTORSAFETYSTATERESPONSE = _descriptor.Descriptor(
  name='GetTractorSafetyStateResponse',
  full_name='hardware_manager.GetTractorSafetyStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetTractorSafetyStateResponse.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_safe', full_name='hardware_manager.GetTractorSafetyStateResponse.is_safe', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enforced', full_name='hardware_manager.GetTractorSafetyStateResponse.enforced', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1281,
  serialized_end=1369,
)


_GETTRACTORIFSTATEREQUEST = _descriptor.Descriptor(
  name='GetTractorIFStateRequest',
  full_name='hardware_manager.GetTractorIFStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1371,
  serialized_end=1397,
)


_GETTRACTORIFSTATERESPONSE = _descriptor.Descriptor(
  name='GetTractorIFStateResponse',
  full_name='hardware_manager.GetTractorIFStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='expected', full_name='hardware_manager.GetTractorIFStateResponse.expected', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='connected', full_name='hardware_manager.GetTractorIFStateResponse.connected', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1399,
  serialized_end=1463,
)


_GETSAFETYSTATUSREQUEST = _descriptor.Descriptor(
  name='GetSafetyStatusRequest',
  full_name='hardware_manager.GetSafetyStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1465,
  serialized_end=1489,
)


_GETSAFETYSTATUSRESPONSE = _descriptor.Descriptor(
  name='GetSafetyStatusResponse',
  full_name='hardware_manager.GetSafetyStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lifted', full_name='hardware_manager.GetSafetyStatusResponse.lifted', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='estopped', full_name='hardware_manager.GetSafetyStatusResponse.estopped', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='in_cab_estopped', full_name='hardware_manager.GetSafetyStatusResponse.in_cab_estopped', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='left_estopped', full_name='hardware_manager.GetSafetyStatusResponse.left_estopped', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='right_estopped', full_name='hardware_manager.GetSafetyStatusResponse.right_estopped', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_key', full_name='hardware_manager.GetSafetyStatusResponse.laser_key', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='interlock', full_name='hardware_manager.GetSafetyStatusResponse.interlock', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='water_protect', full_name='hardware_manager.GetSafetyStatusResponse.water_protect', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reset_required', full_name='hardware_manager.GetSafetyStatusResponse.reset_required', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_estop', full_name='hardware_manager.GetSafetyStatusResponse.center_estop', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_button_estop', full_name='hardware_manager.GetSafetyStatusResponse.power_button_estop', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='left_lpsu_interlock', full_name='hardware_manager.GetSafetyStatusResponse.left_lpsu_interlock', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='right_lpsu_interlock', full_name='hardware_manager.GetSafetyStatusResponse.right_lpsu_interlock', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='debug_mode', full_name='hardware_manager.GetSafetyStatusResponse.debug_mode', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1492,
  serialized_end=1837,
)


_GEOLLA = _descriptor.Descriptor(
  name='GeoLLA',
  full_name='hardware_manager.GeoLLA',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lat', full_name='hardware_manager.GeoLLA.lat', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lng', full_name='hardware_manager.GeoLLA.lng', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alt', full_name='hardware_manager.GeoLLA.alt', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GeoLLA.timestamp_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1839,
  serialized_end=1908,
)


_GEOECEF = _descriptor.Descriptor(
  name='GeoECEF',
  full_name='hardware_manager.GeoECEF',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='hardware_manager.GeoECEF.x', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='hardware_manager.GeoECEF.y', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z', full_name='hardware_manager.GeoECEF.z', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GeoECEF.timestamp_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1910,
  serialized_end=1974,
)


_GETGPSDATAREQUEST = _descriptor.Descriptor(
  name='GetGPSDataRequest',
  full_name='hardware_manager.GetGPSDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='validate', full_name='hardware_manager.GetGPSDataRequest.validate', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1976,
  serialized_end=2013,
)


_GETGPSDATARESPONSE = _descriptor.Descriptor(
  name='GetGPSDataResponse',
  full_name='hardware_manager.GetGPSDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lla', full_name='hardware_manager.GetGPSDataResponse.lla', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef', full_name='hardware_manager.GetGPSDataResponse.ecef', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2015,
  serialized_end=2115,
)


_GETNEXTGPSDATAREQUEST = _descriptor.Descriptor(
  name='GetNextGPSDataRequest',
  full_name='hardware_manager.GetNextGPSDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetNextGPSDataRequest.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2117,
  serialized_end=2162,
)


_GETNEXTGPSDATARESPONSE = _descriptor.Descriptor(
  name='GetNextGPSDataResponse',
  full_name='hardware_manager.GetNextGPSDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lla', full_name='hardware_manager.GetNextGPSDataResponse.lla', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2164,
  serialized_end=2227,
)


_GETNEXTRAWGPSDATAREQUEST = _descriptor.Descriptor(
  name='GetNextRawGPSDataRequest',
  full_name='hardware_manager.GetNextRawGPSDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetNextRawGPSDataRequest.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2229,
  serialized_end=2277,
)


_VALUEWITHACCURACY = _descriptor.Descriptor(
  name='ValueWithAccuracy',
  full_name='hardware_manager.ValueWithAccuracy',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='hardware_manager.ValueWithAccuracy.value', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='accuracy', full_name='hardware_manager.ValueWithAccuracy.accuracy', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2279,
  serialized_end=2331,
)


_DUALGPSDATA = _descriptor.Descriptor(
  name='DualGpsData',
  full_name='hardware_manager.DualGpsData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gnss_valid', full_name='hardware_manager.DualGpsData.gnss_valid', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='diff_corrections', full_name='hardware_manager.DualGpsData.diff_corrections', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_moving_base', full_name='hardware_manager.DualGpsData.is_moving_base', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='carrier_phase', full_name='hardware_manager.DualGpsData.carrier_phase', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.DualGpsData.timestamp_ms', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='north', full_name='hardware_manager.DualGpsData.north', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='east', full_name='hardware_manager.DualGpsData.east', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='down', full_name='hardware_manager.DualGpsData.down', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='length', full_name='hardware_manager.DualGpsData.length', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading', full_name='hardware_manager.DualGpsData.heading', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_north', full_name='hardware_manager.DualGpsData._north',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_east', full_name='hardware_manager.DualGpsData._east',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_down', full_name='hardware_manager.DualGpsData._down',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_length', full_name='hardware_manager.DualGpsData._length',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_heading', full_name='hardware_manager.DualGpsData._heading',
      index=4, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2334,
  serialized_end=2835,
)


_GETNEXTRAWGPSDATARESPONSE = _descriptor.Descriptor(
  name='GetNextRawGPSDataResponse',
  full_name='hardware_manager.GetNextRawGPSDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='have_fix', full_name='hardware_manager.GetNextRawGPSDataResponse.have_fix', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latitude', full_name='hardware_manager.GetNextRawGPSDataResponse.latitude', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='longitude', full_name='hardware_manager.GetNextRawGPSDataResponse.longitude', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_sats', full_name='hardware_manager.GetNextRawGPSDataResponse.num_sats', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hdop', full_name='hardware_manager.GetNextRawGPSDataResponse.hdop', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='hardware_manager.GetNextRawGPSDataResponse.timestamp_ms', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height_mm', full_name='hardware_manager.GetNextRawGPSDataResponse.height_mm', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='have_approx_fix', full_name='hardware_manager.GetNextRawGPSDataResponse.have_approx_fix', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fix_type', full_name='hardware_manager.GetNextRawGPSDataResponse.fix_type', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gnss_valid', full_name='hardware_manager.GetNextRawGPSDataResponse.gnss_valid', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='diff_corrections', full_name='hardware_manager.GetNextRawGPSDataResponse.diff_corrections', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='carrier_phase', full_name='hardware_manager.GetNextRawGPSDataResponse.carrier_phase', index=11,
      number=12, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dual', full_name='hardware_manager.GetNextRawGPSDataResponse.dual', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_dual', full_name='hardware_manager.GetNextRawGPSDataResponse._dual',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2838,
  serialized_end=3227,
)


_GETGPSFIXEDPOSREQUEST = _descriptor.Descriptor(
  name='GetGPSFixedPosRequest',
  full_name='hardware_manager.GetGPSFixedPosRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3229,
  serialized_end=3252,
)


_GETGPSFIXEDPOSRESPONSE = _descriptor.Descriptor(
  name='GetGPSFixedPosResponse',
  full_name='hardware_manager.GetGPSFixedPosResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x_mm', full_name='hardware_manager.GetGPSFixedPosResponse.x_mm', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_mm', full_name='hardware_manager.GetGPSFixedPosResponse.y_mm', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3254,
  serialized_end=3306,
)


_GETMANAGEDBOARDERRORSREQUEST = _descriptor.Descriptor(
  name='GetManagedBoardErrorsRequest',
  full_name='hardware_manager.GetManagedBoardErrorsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3308,
  serialized_end=3338,
)


_GETMANAGEDBOARDERRORSRESPONSE = _descriptor.Descriptor(
  name='GetManagedBoardErrorsResponse',
  full_name='hardware_manager.GetManagedBoardErrorsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='board', full_name='hardware_manager.GetManagedBoardErrorsResponse.board', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='encoder_error_flag', full_name='hardware_manager.GetManagedBoardErrorsResponse.encoder_error_flag', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='encoder_error_msg', full_name='hardware_manager.GetManagedBoardErrorsResponse.encoder_error_msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps_has_fix', full_name='hardware_manager.GetManagedBoardErrorsResponse.gps_has_fix', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3340,
  serialized_end=3462,
)


_GETSUPERVISORYSTATUSREQUEST = _descriptor.Descriptor(
  name='GetSupervisoryStatusRequest',
  full_name='hardware_manager.GetSupervisoryStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3464,
  serialized_end=3493,
)


_GETREAPERSUPERVISORYSTATUSREQUEST = _descriptor.Descriptor(
  name='GetReaperSupervisoryStatusRequest',
  full_name='hardware_manager.GetReaperSupervisoryStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3495,
  serialized_end=3530,
)


_CHILLERALARMS = _descriptor.Descriptor(
  name='ChillerAlarms',
  full_name='hardware_manager.ChillerAlarms',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='low_level_in_tank', full_name='hardware_manager.ChillerAlarms.low_level_in_tank', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='high_circulating_fluid_discharge_temp', full_name='hardware_manager.ChillerAlarms.high_circulating_fluid_discharge_temp', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='circulating_fluid_discharge_temp_rise', full_name='hardware_manager.ChillerAlarms.circulating_fluid_discharge_temp_rise', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='circulating_fluid_discharge_temp_drop', full_name='hardware_manager.ChillerAlarms.circulating_fluid_discharge_temp_drop', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='high_circulating_fluid_return_temp', full_name='hardware_manager.ChillerAlarms.high_circulating_fluid_return_temp', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='circulating_fluid_discharge_pressure_rise', full_name='hardware_manager.ChillerAlarms.circulating_fluid_discharge_pressure_rise', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='circulating_fluid_discharge_pressure_drop', full_name='hardware_manager.ChillerAlarms.circulating_fluid_discharge_pressure_drop', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='high_compressor_suction_temp', full_name='hardware_manager.ChillerAlarms.high_compressor_suction_temp', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='low_compressor_suction_temp', full_name='hardware_manager.ChillerAlarms.low_compressor_suction_temp', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='low_super_heat_temp', full_name='hardware_manager.ChillerAlarms.low_super_heat_temp', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='high_compressor_discharge_pressure', full_name='hardware_manager.ChillerAlarms.high_compressor_discharge_pressure', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='refrigerant_circut_pressure_high_drop', full_name='hardware_manager.ChillerAlarms.refrigerant_circut_pressure_high_drop', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='refrigerant_circut_pressure_low_rise', full_name='hardware_manager.ChillerAlarms.refrigerant_circut_pressure_low_rise', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='refrigerant_circut_pressure_low_drop', full_name='hardware_manager.ChillerAlarms.refrigerant_circut_pressure_low_drop', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='compressor_running_failure', full_name='hardware_manager.ChillerAlarms.compressor_running_failure', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='communication_error', full_name='hardware_manager.ChillerAlarms.communication_error', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='memory_error', full_name='hardware_manager.ChillerAlarms.memory_error', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dc_line_fuse_cut', full_name='hardware_manager.ChillerAlarms.dc_line_fuse_cut', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='circulating_fluid_discharge_temp_sensor_failure', full_name='hardware_manager.ChillerAlarms.circulating_fluid_discharge_temp_sensor_failure', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='circulating_fluid_return_temp_sensor_failure', full_name='hardware_manager.ChillerAlarms.circulating_fluid_return_temp_sensor_failure', index=19,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='circulating_fluid_suction_temp_sensor_failure', full_name='hardware_manager.ChillerAlarms.circulating_fluid_suction_temp_sensor_failure', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='circulating_fluid_discharge_pressure_sensor_failure', full_name='hardware_manager.ChillerAlarms.circulating_fluid_discharge_pressure_sensor_failure', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='compressor_discharge_pressure_sensor_failure', full_name='hardware_manager.ChillerAlarms.compressor_discharge_pressure_sensor_failure', index=22,
      number=23, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='compressor_suction_pressure_sensor_failure', full_name='hardware_manager.ChillerAlarms.compressor_suction_pressure_sensor_failure', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pump_maintenance', full_name='hardware_manager.ChillerAlarms.pump_maintenance', index=24,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fan_maintenance', full_name='hardware_manager.ChillerAlarms.fan_maintenance', index=25,
      number=26, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='compressor_maintenance', full_name='hardware_manager.ChillerAlarms.compressor_maintenance', index=26,
      number=27, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='contact_input_1_signal_detection', full_name='hardware_manager.ChillerAlarms.contact_input_1_signal_detection', index=27,
      number=28, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='contact_input_2_signal_detection', full_name='hardware_manager.ChillerAlarms.contact_input_2_signal_detection', index=28,
      number=29, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='compressor_discharge_temp_sensor_failure', full_name='hardware_manager.ChillerAlarms.compressor_discharge_temp_sensor_failure', index=29,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='compressor_discharge_temp_rise', full_name='hardware_manager.ChillerAlarms.compressor_discharge_temp_rise', index=30,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dustproof_filter_maintenance', full_name='hardware_manager.ChillerAlarms.dustproof_filter_maintenance', index=31,
      number=32, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_stoppage', full_name='hardware_manager.ChillerAlarms.power_stoppage', index=32,
      number=33, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='compressor_waiting', full_name='hardware_manager.ChillerAlarms.compressor_waiting', index=33,
      number=34, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fan_failure', full_name='hardware_manager.ChillerAlarms.fan_failure', index=34,
      number=35, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='compressor_over_current', full_name='hardware_manager.ChillerAlarms.compressor_over_current', index=35,
      number=36, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pump_over_current', full_name='hardware_manager.ChillerAlarms.pump_over_current', index=36,
      number=37, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='air_exhaust_fan_stoppage', full_name='hardware_manager.ChillerAlarms.air_exhaust_fan_stoppage', index=37,
      number=38, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='incorrect_phase_error', full_name='hardware_manager.ChillerAlarms.incorrect_phase_error', index=38,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phase_board_over_current', full_name='hardware_manager.ChillerAlarms.phase_board_over_current', index=39,
      number=40, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3533,
  serialized_end=5122,
)


_GETSUPERVISORYSTATUSRESPONSE = _descriptor.Descriptor(
  name='GetSupervisoryStatusResponse',
  full_name='hardware_manager.GetSupervisoryStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='water_protect_status', full_name='hardware_manager.GetSupervisoryStatusResponse.water_protect_status', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_contactor_status_fb', full_name='hardware_manager.GetSupervisoryStatusResponse.main_contactor_status_fb', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_good', full_name='hardware_manager.GetSupervisoryStatusResponse.power_good', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_bad', full_name='hardware_manager.GetSupervisoryStatusResponse.power_bad', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_very_bad', full_name='hardware_manager.GetSupervisoryStatusResponse.power_very_bad', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifted_status', full_name='hardware_manager.GetSupervisoryStatusResponse.lifted_status', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp_humidity_status', full_name='hardware_manager.GetSupervisoryStatusResponse.temp_humidity_status', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tractor_power', full_name='hardware_manager.GetSupervisoryStatusResponse.tractor_power', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_frequency', full_name='hardware_manager.GetSupervisoryStatusResponse.ac_frequency', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a_b', full_name='hardware_manager.GetSupervisoryStatusResponse.ac_voltage_a_b', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_b_c', full_name='hardware_manager.GetSupervisoryStatusResponse.ac_voltage_b_c', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a_c', full_name='hardware_manager.GetSupervisoryStatusResponse.ac_voltage_a_c', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a', full_name='hardware_manager.GetSupervisoryStatusResponse.ac_voltage_a', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_b', full_name='hardware_manager.GetSupervisoryStatusResponse.ac_voltage_b', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_c', full_name='hardware_manager.GetSupervisoryStatusResponse.ac_voltage_c', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phase_power_w_3', full_name='hardware_manager.GetSupervisoryStatusResponse.phase_power_w_3', index=15,
      number=16, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phase_power_va_3', full_name='hardware_manager.GetSupervisoryStatusResponse.phase_power_va_3', index=16,
      number=17, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_factor', full_name='hardware_manager.GetSupervisoryStatusResponse.power_factor', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_cabinet_temp', full_name='hardware_manager.GetSupervisoryStatusResponse.server_cabinet_temp', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_cabinet_humidity', full_name='hardware_manager.GetSupervisoryStatusResponse.server_cabinet_humidity', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='battery_voltage_12v', full_name='hardware_manager.GetSupervisoryStatusResponse.battery_voltage_12v', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp_humidity_bypass_status', full_name='hardware_manager.GetSupervisoryStatusResponse.temp_humidity_bypass_status', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp_bypass_status', full_name='hardware_manager.GetSupervisoryStatusResponse.temp_bypass_status', index=22,
      number=23, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity_bypass_status', full_name='hardware_manager.GetSupervisoryStatusResponse.humidity_bypass_status', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp_status', full_name='hardware_manager.GetSupervisoryStatusResponse.temp_status', index=24,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity_status', full_name='hardware_manager.GetSupervisoryStatusResponse.humidity_status', index=25,
      number=26, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='btl_disabled', full_name='hardware_manager.GetSupervisoryStatusResponse.btl_disabled', index=26,
      number=27, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_disabled', full_name='hardware_manager.GetSupervisoryStatusResponse.server_disabled', index=27,
      number=28, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanners_disabled', full_name='hardware_manager.GetSupervisoryStatusResponse.scanners_disabled', index=28,
      number=29, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_encoder_disabled', full_name='hardware_manager.GetSupervisoryStatusResponse.wheel_encoder_disabled', index=29,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_disabled', full_name='hardware_manager.GetSupervisoryStatusResponse.strobe_disabled', index=30,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps_disabled', full_name='hardware_manager.GetSupervisoryStatusResponse.gps_disabled', index=31,
      number=32, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_contactor_disabled', full_name='hardware_manager.GetSupervisoryStatusResponse.main_contactor_disabled', index=32,
      number=33, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='air_conditioner_disabled', full_name='hardware_manager.GetSupervisoryStatusResponse.air_conditioner_disabled', index=33,
      number=34, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_disabled', full_name='hardware_manager.GetSupervisoryStatusResponse.chiller_disabled', index=34,
      number=35, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_temp', full_name='hardware_manager.GetSupervisoryStatusResponse.chiller_temp', index=35,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_flow', full_name='hardware_manager.GetSupervisoryStatusResponse.chiller_flow', index=36,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_pressure', full_name='hardware_manager.GetSupervisoryStatusResponse.chiller_pressure', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_conductivity', full_name='hardware_manager.GetSupervisoryStatusResponse.chiller_conductivity', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_set_temp', full_name='hardware_manager.GetSupervisoryStatusResponse.chiller_set_temp', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_alarms', full_name='hardware_manager.GetSupervisoryStatusResponse.chiller_alarms', index=40,
      number=41, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5125,
  serialized_end=6258,
)


_SETSERVERDISABLEREQUEST = _descriptor.Descriptor(
  name='SetServerDisableRequest',
  full_name='hardware_manager.SetServerDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='hardware_manager.SetServerDisableRequest.row_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetServerDisableRequest.disable', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6260,
  serialized_end=6318,
)


_SETSERVERDISABLERESPONSE = _descriptor.Descriptor(
  name='SetServerDisableResponse',
  full_name='hardware_manager.SetServerDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetServerDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6320,
  serialized_end=6363,
)


_SETBTLDISABLEREQUEST = _descriptor.Descriptor(
  name='SetBTLDisableRequest',
  full_name='hardware_manager.SetBTLDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='hardware_manager.SetBTLDisableRequest.row_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetBTLDisableRequest.disable', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6365,
  serialized_end=6420,
)


_SETBTLDISABLERESPONSE = _descriptor.Descriptor(
  name='SetBTLDisableResponse',
  full_name='hardware_manager.SetBTLDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetBTLDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6422,
  serialized_end=6462,
)


_SETSCANNERSDISABLEREQUEST = _descriptor.Descriptor(
  name='SetScannersDisableRequest',
  full_name='hardware_manager.SetScannersDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='hardware_manager.SetScannersDisableRequest.row_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetScannersDisableRequest.disable', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6464,
  serialized_end=6524,
)


_SETSCANNERSDISABLERESPONSE = _descriptor.Descriptor(
  name='SetScannersDisableResponse',
  full_name='hardware_manager.SetScannersDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetScannersDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6526,
  serialized_end=6571,
)


_SETWHEELENCODERBOARDDISABLEREQUEST = _descriptor.Descriptor(
  name='SetWheelEncoderBoardDisableRequest',
  full_name='hardware_manager.SetWheelEncoderBoardDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetWheelEncoderBoardDisableRequest.disable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6573,
  serialized_end=6626,
)


_SETWHEELENCODERBOARDDISABLERESPONSE = _descriptor.Descriptor(
  name='SetWheelEncoderBoardDisableResponse',
  full_name='hardware_manager.SetWheelEncoderBoardDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetWheelEncoderBoardDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6628,
  serialized_end=6682,
)


_SETWHEELENCODERDISABLEREQUEST = _descriptor.Descriptor(
  name='SetWheelEncoderDisableRequest',
  full_name='hardware_manager.SetWheelEncoderDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetWheelEncoderDisableRequest.disable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front', full_name='hardware_manager.SetWheelEncoderDisableRequest.front', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='left', full_name='hardware_manager.SetWheelEncoderDisableRequest.left', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6684,
  serialized_end=6761,
)


_SETWHEELENCODERDISABLERESPONSE = _descriptor.Descriptor(
  name='SetWheelEncoderDisableResponse',
  full_name='hardware_manager.SetWheelEncoderDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetWheelEncoderDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6763,
  serialized_end=6812,
)


_SETSTROBEDISABLEREQUEST = _descriptor.Descriptor(
  name='SetStrobeDisableRequest',
  full_name='hardware_manager.SetStrobeDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetStrobeDisableRequest.disable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6814,
  serialized_end=6856,
)


_SETSTROBEDISABLERESPONSE = _descriptor.Descriptor(
  name='SetStrobeDisableResponse',
  full_name='hardware_manager.SetStrobeDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetStrobeDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6858,
  serialized_end=6901,
)


_SETGPSDISABLEREQUEST = _descriptor.Descriptor(
  name='SetGPSDisableRequest',
  full_name='hardware_manager.SetGPSDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetGPSDisableRequest.disable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6903,
  serialized_end=6942,
)


_SETGPSDISABLERESPONSE = _descriptor.Descriptor(
  name='SetGPSDisableResponse',
  full_name='hardware_manager.SetGPSDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetGPSDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6944,
  serialized_end=6984,
)


_COMMANDCOMPUTERPOWERCYCLEREQUEST = _descriptor.Descriptor(
  name='CommandComputerPowerCycleRequest',
  full_name='hardware_manager.CommandComputerPowerCycleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6986,
  serialized_end=7020,
)


_COMMANDCOMPUTERPOWERCYCLERESPONSE = _descriptor.Descriptor(
  name='CommandComputerPowerCycleResponse',
  full_name='hardware_manager.CommandComputerPowerCycleResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.CommandComputerPowerCycleResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7022,
  serialized_end=7074,
)


_SUICIDESWITCHREQUEST = _descriptor.Descriptor(
  name='SuicideSwitchRequest',
  full_name='hardware_manager.SuicideSwitchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7076,
  serialized_end=7098,
)


_SUICIDESWITCHRESPONSE = _descriptor.Descriptor(
  name='SuicideSwitchResponse',
  full_name='hardware_manager.SuicideSwitchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SuicideSwitchResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7100,
  serialized_end=7140,
)


_SETMAINCONTACTORDISABLEREQUEST = _descriptor.Descriptor(
  name='SetMainContactorDisableRequest',
  full_name='hardware_manager.SetMainContactorDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetMainContactorDisableRequest.disable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7142,
  serialized_end=7191,
)


_SETMAINCONTACTORDISABLERESPONSE = _descriptor.Descriptor(
  name='SetMainContactorDisableResponse',
  full_name='hardware_manager.SetMainContactorDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetMainContactorDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7193,
  serialized_end=7243,
)


_SETAIRCONDITIONERDISABLEREQUEST = _descriptor.Descriptor(
  name='SetAirConditionerDisableRequest',
  full_name='hardware_manager.SetAirConditionerDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetAirConditionerDisableRequest.disable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7245,
  serialized_end=7295,
)


_SETAIRCONDITIONERDISABLERESPONSE = _descriptor.Descriptor(
  name='SetAirConditionerDisableResponse',
  full_name='hardware_manager.SetAirConditionerDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetAirConditionerDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7297,
  serialized_end=7348,
)


_SETCHILLERDISABLEREQUEST = _descriptor.Descriptor(
  name='SetChillerDisableRequest',
  full_name='hardware_manager.SetChillerDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetChillerDisableRequest.disable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7350,
  serialized_end=7393,
)


_SETCHILLERDISABLERESPONSE = _descriptor.Descriptor(
  name='SetChillerDisableResponse',
  full_name='hardware_manager.SetChillerDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetChillerDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7395,
  serialized_end=7439,
)


_SETTEMPBYPASSDISABLEREQUEST = _descriptor.Descriptor(
  name='SetTempBypassDisableRequest',
  full_name='hardware_manager.SetTempBypassDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetTempBypassDisableRequest.disable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7441,
  serialized_end=7487,
)


_SETTEMPBYPASSDISABLERESPONSE = _descriptor.Descriptor(
  name='SetTempBypassDisableResponse',
  full_name='hardware_manager.SetTempBypassDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetTempBypassDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7489,
  serialized_end=7536,
)


_SETHUMIDITYBYPASSDISABLEREQUEST = _descriptor.Descriptor(
  name='SetHumidityBypassDisableRequest',
  full_name='hardware_manager.SetHumidityBypassDisableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disable', full_name='hardware_manager.SetHumidityBypassDisableRequest.disable', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7538,
  serialized_end=7588,
)


_SETHUMIDITYBYPASSDISABLERESPONSE = _descriptor.Descriptor(
  name='SetHumidityBypassDisableResponse',
  full_name='hardware_manager.SetHumidityBypassDisableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetHumidityBypassDisableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7590,
  serialized_end=7641,
)


_GETAVAILABLEUSBSTORAGEREQUEST = _descriptor.Descriptor(
  name='GetAvailableUSBStorageRequest',
  full_name='hardware_manager.GetAvailableUSBStorageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7643,
  serialized_end=7674,
)


_GETAVAILABLEUSBSTORAGERESPONSE = _descriptor.Descriptor(
  name='GetAvailableUSBStorageResponse',
  full_name='hardware_manager.GetAvailableUSBStorageResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='used', full_name='hardware_manager.GetAvailableUSBStorageResponse.used', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.GetAvailableUSBStorageResponse.success', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='usb_available', full_name='hardware_manager.GetAvailableUSBStorageResponse.usb_available', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7676,
  serialized_end=7762,
)


_GETREADYREQUEST = _descriptor.Descriptor(
  name='GetReadyRequest',
  full_name='hardware_manager.GetReadyRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7764,
  serialized_end=7781,
)


_GETREADYRESPONSE = _descriptor.Descriptor(
  name='GetReadyResponse',
  full_name='hardware_manager.GetReadyResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ready', full_name='hardware_manager.GetReadyResponse.ready', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7783,
  serialized_end=7816,
)


_GET240VUPTIMEREQUEST = _descriptor.Descriptor(
  name='Get240vUptimeRequest',
  full_name='hardware_manager.Get240vUptimeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7818,
  serialized_end=7840,
)


_GET240VUPTIMERESPONSE = _descriptor.Descriptor(
  name='Get240vUptimeResponse',
  full_name='hardware_manager.Get240vUptimeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uptime_s', full_name='hardware_manager.Get240vUptimeResponse.uptime_s', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7842,
  serialized_end=7883,
)


_GETDELTATRAVELMMREQUEST = _descriptor.Descriptor(
  name='GetDeltaTravelMMRequest',
  full_name='hardware_manager.GetDeltaTravelMMRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='hardware_manager.GetDeltaTravelMMRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7885,
  serialized_end=7922,
)


_GETDELTATRAVELMMRESPONSE = _descriptor.Descriptor(
  name='GetDeltaTravelMMResponse',
  full_name='hardware_manager.GetDeltaTravelMMResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='delta_mm', full_name='hardware_manager.GetDeltaTravelMMResponse.delta_mm', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7924,
  serialized_end=7968,
)


_GETRUNTIMEREQUEST = _descriptor.Descriptor(
  name='GetRuntimeRequest',
  full_name='hardware_manager.GetRuntimeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7970,
  serialized_end=7989,
)


_GETRUNTIMERESPONSE = _descriptor.Descriptor(
  name='GetRuntimeResponse',
  full_name='hardware_manager.GetRuntimeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='runtime_240v', full_name='hardware_manager.GetRuntimeResponse.runtime_240v', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7991,
  serialized_end=8033,
)


_GETWHEELENCODERRESOLUTIONREQUEST = _descriptor.Descriptor(
  name='GetWheelEncoderResolutionRequest',
  full_name='hardware_manager.GetWheelEncoderResolutionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8035,
  serialized_end=8069,
)


_GETWHEELENCODERRESOLUTIONRESPONSE = _descriptor.Descriptor(
  name='GetWheelEncoderResolutionResponse',
  full_name='hardware_manager.GetWheelEncoderResolutionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='resolution', full_name='hardware_manager.GetWheelEncoderResolutionResponse.resolution', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8071,
  serialized_end=8126,
)


_STROBESETTINGS = _descriptor.Descriptor(
  name='StrobeSettings',
  full_name='hardware_manager.StrobeSettings',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='exposure_us', full_name='hardware_manager.StrobeSettings.exposure_us', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='period_us', full_name='hardware_manager.StrobeSettings.period_us', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='targets_per_predict_ratio', full_name='hardware_manager.StrobeSettings.targets_per_predict_ratio', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_exposure_us', full_name='hardware_manager.StrobeSettings._exposure_us',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_period_us', full_name='hardware_manager.StrobeSettings._period_us',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_targets_per_predict_ratio', full_name='hardware_manager.StrobeSettings._targets_per_predict_ratio',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=8129,
  serialized_end=8295,
)


_SETSTROBESETTINGSRESPONSE = _descriptor.Descriptor(
  name='SetStrobeSettingsResponse',
  full_name='hardware_manager.SetStrobeSettingsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetStrobeSettingsResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8297,
  serialized_end=8341,
)


_GETSTROBESETTINGSREQUEST = _descriptor.Descriptor(
  name='GetStrobeSettingsRequest',
  full_name='hardware_manager.GetStrobeSettingsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8343,
  serialized_end=8369,
)


_ENVIRONMENTALSENSORDATA = _descriptor.Descriptor(
  name='EnvironmentalSensorData',
  full_name='hardware_manager.EnvironmentalSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='temperature_c', full_name='hardware_manager.EnvironmentalSensorData.temperature_c', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity_rh', full_name='hardware_manager.EnvironmentalSensorData.humidity_rh', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pressure_hpa', full_name='hardware_manager.EnvironmentalSensorData.pressure_hpa', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8371,
  serialized_end=8462,
)


_COOLANTSENSORDATA = _descriptor.Descriptor(
  name='CoolantSensorData',
  full_name='hardware_manager.CoolantSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='temperature_c', full_name='hardware_manager.CoolantSensorData.temperature_c', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pressure_kpa', full_name='hardware_manager.CoolantSensorData.pressure_kpa', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8464,
  serialized_end=8528,
)


_NETWORKPORTSTATE = _descriptor.Descriptor(
  name='NetworkPortState',
  full_name='hardware_manager.NetworkPortState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='link_up', full_name='hardware_manager.NetworkPortState.link_up', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='actual_link_speed', full_name='hardware_manager.NetworkPortState.actual_link_speed', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expected_link_speed', full_name='hardware_manager.NetworkPortState.expected_link_speed', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8531,
  serialized_end=8694,
)


_REAPERPCSENSORDATA = _descriptor.Descriptor(
  name='ReaperPcSensorData',
  full_name='hardware_manager.ReaperPcSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='temperature_cpu_core_c', full_name='hardware_manager.ReaperPcSensorData.temperature_cpu_core_c', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_system_c', full_name='hardware_manager.ReaperPcSensorData.temperature_system_c', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_gpu_1_c', full_name='hardware_manager.ReaperPcSensorData.temperature_gpu_1_c', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_gpu_2_c', full_name='hardware_manager.ReaperPcSensorData.temperature_gpu_2_c', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='psu_12v', full_name='hardware_manager.ReaperPcSensorData.psu_12v', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='psu_5v', full_name='hardware_manager.ReaperPcSensorData.psu_5v', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='psu_3v3', full_name='hardware_manager.ReaperPcSensorData.psu_3v3', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='load', full_name='hardware_manager.ReaperPcSensorData.load', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='uptime', full_name='hardware_manager.ReaperPcSensorData.uptime', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ram_usage_percent', full_name='hardware_manager.ReaperPcSensorData.ram_usage_percent', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='disk_usage_percent', full_name='hardware_manager.ReaperPcSensorData.disk_usage_percent', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_link', full_name='hardware_manager.ReaperPcSensorData.scanner_link', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_cam_link', full_name='hardware_manager.ReaperPcSensorData.target_cam_link', index=12,
      number=13, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_cam_link', full_name='hardware_manager.ReaperPcSensorData.predict_cam_link', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ipmi_link', full_name='hardware_manager.ReaperPcSensorData.ipmi_link', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='global_link', full_name='hardware_manager.ReaperPcSensorData.global_link', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ext_link', full_name='hardware_manager.ReaperPcSensorData.ext_link', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_temperature_gpu_1_c', full_name='hardware_manager.ReaperPcSensorData._temperature_gpu_1_c',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_temperature_gpu_2_c', full_name='hardware_manager.ReaperPcSensorData._temperature_gpu_2_c',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=8697,
  serialized_end=9377,
)


_REAPERSCANNERLASERSTATUS = _descriptor.Descriptor(
  name='ReaperScannerLaserStatus',
  full_name='hardware_manager.ReaperScannerLaserStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model', full_name='hardware_manager.ReaperScannerLaserStatus.model', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sn', full_name='hardware_manager.ReaperScannerLaserStatus.sn', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rated_power', full_name='hardware_manager.ReaperScannerLaserStatus.rated_power', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_c', full_name='hardware_manager.ReaperScannerLaserStatus.temperature_c', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity', full_name='hardware_manager.ReaperScannerLaserStatus.humidity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_current_ma', full_name='hardware_manager.ReaperScannerLaserStatus.laser_current_ma', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='faults', full_name='hardware_manager.ReaperScannerLaserStatus.faults', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9380,
  serialized_end=9537,
)


_REAPERSCANNERMOTORDATA = _descriptor.Descriptor(
  name='ReaperScannerMotorData',
  full_name='hardware_manager.ReaperScannerMotorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='controller_sn', full_name='hardware_manager.ReaperScannerMotorData.controller_sn', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_output_c', full_name='hardware_manager.ReaperScannerMotorData.temperature_output_c', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_supply_v', full_name='hardware_manager.ReaperScannerMotorData.motor_supply_v', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_current_a', full_name='hardware_manager.ReaperScannerMotorData.motor_current_a', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='encoder_position', full_name='hardware_manager.ReaperScannerMotorData.encoder_position', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9540,
  serialized_end=9692,
)


_REAPERSCANNERSENSORDATA = _descriptor.Descriptor(
  name='ReaperScannerSensorData',
  full_name='hardware_manager.ReaperScannerSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_sn', full_name='hardware_manager.ReaperScannerSensorData.scanner_sn', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_on', full_name='hardware_manager.ReaperScannerSensorData.power_on', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_a', full_name='hardware_manager.ReaperScannerSensorData.current_a', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fuse_tripped', full_name='hardware_manager.ReaperScannerSensorData.fuse_tripped', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_collimator_c', full_name='hardware_manager.ReaperScannerSensorData.temperature_collimator_c', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_fiber_c', full_name='hardware_manager.ReaperScannerSensorData.temperature_fiber_c', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_power_w', full_name='hardware_manager.ReaperScannerSensorData.laser_power_w', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_power_raw_mv', full_name='hardware_manager.ReaperScannerSensorData.laser_power_raw_mv', index=7,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_connected', full_name='hardware_manager.ReaperScannerSensorData.laser_connected', index=8,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_status', full_name='hardware_manager.ReaperScannerSensorData.laser_status', index=9,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_connected', full_name='hardware_manager.ReaperScannerSensorData.target_connected', index=10,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_sn', full_name='hardware_manager.ReaperScannerSensorData.target_sn', index=11,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_target_c', full_name='hardware_manager.ReaperScannerSensorData.temperature_target_c', index=12,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_pan', full_name='hardware_manager.ReaperScannerSensorData.motor_pan', index=13,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_tilt', full_name='hardware_manager.ReaperScannerSensorData.motor_tilt', index=14,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_cam_power_enabled', full_name='hardware_manager.ReaperScannerSensorData.target_cam_power_enabled', index=15,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_laser_status', full_name='hardware_manager.ReaperScannerSensorData._laser_status',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_target_sn', full_name='hardware_manager.ReaperScannerSensorData._target_sn',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_temperature_target_c', full_name='hardware_manager.ReaperScannerSensorData._temperature_target_c',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_motor_pan', full_name='hardware_manager.ReaperScannerSensorData._motor_pan',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_motor_tilt', full_name='hardware_manager.ReaperScannerSensorData._motor_tilt',
      index=4, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=9695,
  serialized_end=10346,
)


_REAPERCENTERENCLOSUREDATA = _descriptor.Descriptor(
  name='ReaperCenterEnclosureData',
  full_name='hardware_manager.ReaperCenterEnclosureData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='water_protect_status', full_name='hardware_manager.ReaperCenterEnclosureData.water_protect_status', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_contactor_status_fb', full_name='hardware_manager.ReaperCenterEnclosureData.main_contactor_status_fb', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_good', full_name='hardware_manager.ReaperCenterEnclosureData.power_good', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_bad', full_name='hardware_manager.ReaperCenterEnclosureData.power_bad', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_very_bad', full_name='hardware_manager.ReaperCenterEnclosureData.power_very_bad', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifted_status', full_name='hardware_manager.ReaperCenterEnclosureData.lifted_status', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tractor_power', full_name='hardware_manager.ReaperCenterEnclosureData.tractor_power', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_frequency', full_name='hardware_manager.ReaperCenterEnclosureData.ac_frequency', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a_b', full_name='hardware_manager.ReaperCenterEnclosureData.ac_voltage_a_b', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_b_c', full_name='hardware_manager.ReaperCenterEnclosureData.ac_voltage_b_c', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a_c', full_name='hardware_manager.ReaperCenterEnclosureData.ac_voltage_a_c', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a', full_name='hardware_manager.ReaperCenterEnclosureData.ac_voltage_a', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_b', full_name='hardware_manager.ReaperCenterEnclosureData.ac_voltage_b', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_c', full_name='hardware_manager.ReaperCenterEnclosureData.ac_voltage_c', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phase_power_w_3', full_name='hardware_manager.ReaperCenterEnclosureData.phase_power_w_3', index=14,
      number=15, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phase_power_va_3', full_name='hardware_manager.ReaperCenterEnclosureData.phase_power_va_3', index=15,
      number=16, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_factor', full_name='hardware_manager.ReaperCenterEnclosureData.power_factor', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_cabinet_temp', full_name='hardware_manager.ReaperCenterEnclosureData.server_cabinet_temp', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_cabinet_humidity', full_name='hardware_manager.ReaperCenterEnclosureData.server_cabinet_humidity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='battery_voltage_12v', full_name='hardware_manager.ReaperCenterEnclosureData.battery_voltage_12v', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_encoder_disabled', full_name='hardware_manager.ReaperCenterEnclosureData.wheel_encoder_disabled', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_disabled', full_name='hardware_manager.ReaperCenterEnclosureData.strobe_disabled', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps_disabled', full_name='hardware_manager.ReaperCenterEnclosureData.gps_disabled', index=22,
      number=23, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_contactor_disabled', full_name='hardware_manager.ReaperCenterEnclosureData.main_contactor_disabled', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='air_conditioner_disabled', full_name='hardware_manager.ReaperCenterEnclosureData.air_conditioner_disabled', index=24,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_disabled', full_name='hardware_manager.ReaperCenterEnclosureData.chiller_disabled', index=25,
      number=26, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_temp', full_name='hardware_manager.ReaperCenterEnclosureData.chiller_temp', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_flow', full_name='hardware_manager.ReaperCenterEnclosureData.chiller_flow', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_pressure', full_name='hardware_manager.ReaperCenterEnclosureData.chiller_pressure', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_conductivity', full_name='hardware_manager.ReaperCenterEnclosureData.chiller_conductivity', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_set_temp', full_name='hardware_manager.ReaperCenterEnclosureData.chiller_set_temp', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_heat_transfer', full_name='hardware_manager.ReaperCenterEnclosureData.chiller_heat_transfer', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_fluid_delta_temp', full_name='hardware_manager.ReaperCenterEnclosureData.chiller_fluid_delta_temp', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_alarms', full_name='hardware_manager.ReaperCenterEnclosureData.chiller_alarms', index=33,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10349,
  serialized_end=11293,
)


_REAPERMODULESENSORDATA = _descriptor.Descriptor(
  name='ReaperModuleSensorData',
  full_name='hardware_manager.ReaperModuleSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='hardware_manager.ReaperModuleSensorData.module_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='module_sn', full_name='hardware_manager.ReaperModuleSensorData.module_sn', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enviro_enclosure', full_name='hardware_manager.ReaperModuleSensorData.enviro_enclosure', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enviro_pc', full_name='hardware_manager.ReaperModuleSensorData.enviro_pc', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='coolant_inlet', full_name='hardware_manager.ReaperModuleSensorData.coolant_inlet', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='coolant_outlet', full_name='hardware_manager.ReaperModuleSensorData.coolant_outlet', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_temperature_c', full_name='hardware_manager.ReaperModuleSensorData.strobe_temperature_c', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_cap_voltage', full_name='hardware_manager.ReaperModuleSensorData.strobe_cap_voltage', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_current', full_name='hardware_manager.ReaperModuleSensorData.strobe_current', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pc', full_name='hardware_manager.ReaperModuleSensorData.pc', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_a', full_name='hardware_manager.ReaperModuleSensorData.scanner_a', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_b', full_name='hardware_manager.ReaperModuleSensorData.scanner_b', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pc_power_enabled', full_name='hardware_manager.ReaperModuleSensorData.pc_power_enabled', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lasers_power_enabled', full_name='hardware_manager.ReaperModuleSensorData.lasers_power_enabled', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_cam_power_enabled', full_name='hardware_manager.ReaperModuleSensorData.predict_cam_power_enabled', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_power_enabled', full_name='hardware_manager.ReaperModuleSensorData.strobe_power_enabled', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_enabled', full_name='hardware_manager.ReaperModuleSensorData.strobe_enabled', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_module_sn', full_name='hardware_manager.ReaperModuleSensorData._module_sn',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_pc', full_name='hardware_manager.ReaperModuleSensorData._pc',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_scanner_a', full_name='hardware_manager.ReaperModuleSensorData._scanner_a',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_scanner_b', full_name='hardware_manager.ReaperModuleSensorData._scanner_b',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=11296,
  serialized_end=12080,
)


_GETREAPERENCLOSURESENSORSREQUEST = _descriptor.Descriptor(
  name='GetReaperEnclosureSensorsRequest',
  full_name='hardware_manager.GetReaperEnclosureSensorsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12082,
  serialized_end=12116,
)


_GETREAPERENCLOSURESENSORSRESPONSE = _descriptor.Descriptor(
  name='GetReaperEnclosureSensorsResponse',
  full_name='hardware_manager.GetReaperEnclosureSensorsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensors', full_name='hardware_manager.GetReaperEnclosureSensorsResponse.sensors', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12118,
  serialized_end=12215,
)


_GETREAPERMODULESENSORSREQUEST = _descriptor.Descriptor(
  name='GetReaperModuleSensorsRequest',
  full_name='hardware_manager.GetReaperModuleSensorsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_ids', full_name='hardware_manager.GetReaperModuleSensorsRequest.module_ids', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12217,
  serialized_end=12268,
)


_GETREAPERMODULESENSORSRESPONSE = _descriptor.Descriptor(
  name='GetReaperModuleSensorsResponse',
  full_name='hardware_manager.GetReaperModuleSensorsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_sensors', full_name='hardware_manager.GetReaperModuleSensorsResponse.module_sensors', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12270,
  serialized_end=12368,
)


_SETREAPERSCANNERPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperScannerPowerRequest',
  full_name='hardware_manager.SetReaperScannerPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='hardware_manager.SetReaperScannerPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_a_power', full_name='hardware_manager.SetReaperScannerPowerRequest.scanner_a_power', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_b_power', full_name='hardware_manager.SetReaperScannerPowerRequest.scanner_b_power', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_scanner_a_power', full_name='hardware_manager.SetReaperScannerPowerRequest._scanner_a_power',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_scanner_b_power', full_name='hardware_manager.SetReaperScannerPowerRequest._scanner_b_power',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=12371,
  serialized_end=12520,
)


_SETREAPERSCANNERPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperScannerPowerResponse',
  full_name='hardware_manager.SetReaperScannerPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetReaperScannerPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12522,
  serialized_end=12570,
)


_SETREAPERTARGETPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperTargetPowerRequest',
  full_name='hardware_manager.SetReaperTargetPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='hardware_manager.SetReaperTargetPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_a_power', full_name='hardware_manager.SetReaperTargetPowerRequest.target_a_power', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_b_power', full_name='hardware_manager.SetReaperTargetPowerRequest.target_b_power', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_target_a_power', full_name='hardware_manager.SetReaperTargetPowerRequest._target_a_power',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_target_b_power', full_name='hardware_manager.SetReaperTargetPowerRequest._target_b_power',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=12573,
  serialized_end=12717,
)


_SETREAPERTARGETPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperTargetPowerResponse',
  full_name='hardware_manager.SetReaperTargetPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetReaperTargetPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12719,
  serialized_end=12766,
)


_SETREAPERPREDICTCAMPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperPredictCamPowerRequest',
  full_name='hardware_manager.SetReaperPredictCamPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='hardware_manager.SetReaperPredictCamPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='hardware_manager.SetReaperPredictCamPowerRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12768,
  serialized_end=12837,
)


_SETREAPERPREDICTCAMPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperPredictCamPowerResponse',
  full_name='hardware_manager.SetReaperPredictCamPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetReaperPredictCamPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12839,
  serialized_end=12890,
)


_SETREAPERSTROBECONFIGREQUEST = _descriptor.Descriptor(
  name='SetReaperStrobeConfigRequest',
  full_name='hardware_manager.SetReaperStrobeConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='hardware_manager.SetReaperStrobeConfigRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='settings', full_name='hardware_manager.SetReaperStrobeConfigRequest.settings', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_module_id', full_name='hardware_manager.SetReaperStrobeConfigRequest._module_id',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=12892,
  serialized_end=13012,
)


_SETREAPERSTROBECONFIGRESPONSE = _descriptor.Descriptor(
  name='SetReaperStrobeConfigResponse',
  full_name='hardware_manager.SetReaperStrobeConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetReaperStrobeConfigResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13014,
  serialized_end=13062,
)


_SETREAPERSTROBEENABLEREQUEST = _descriptor.Descriptor(
  name='SetReaperStrobeEnableRequest',
  full_name='hardware_manager.SetReaperStrobeEnableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_ids', full_name='hardware_manager.SetReaperStrobeEnableRequest.module_ids', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='hardware_manager.SetReaperStrobeEnableRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_ms', full_name='hardware_manager.SetReaperStrobeEnableRequest.duration_ms', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_duration_ms', full_name='hardware_manager.SetReaperStrobeEnableRequest._duration_ms',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=13064,
  serialized_end=13173,
)


_SETREAPERSTROBEENABLERESPONSE = _descriptor.Descriptor(
  name='SetReaperStrobeEnableResponse',
  full_name='hardware_manager.SetReaperStrobeEnableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetReaperStrobeEnableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13175,
  serialized_end=13223,
)


_SETREAPERMODULEPCPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperModulePcPowerRequest',
  full_name='hardware_manager.SetReaperModulePcPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='hardware_manager.SetReaperModulePcPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='hardware_manager.SetReaperModulePcPowerRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13225,
  serialized_end=13292,
)


_SETREAPERMODULEPCPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperModulePcPowerResponse',
  full_name='hardware_manager.SetReaperModulePcPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetReaperModulePcPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13294,
  serialized_end=13343,
)


_SETREAPERMODULELASERPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperModuleLaserPowerRequest',
  full_name='hardware_manager.SetReaperModuleLaserPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='hardware_manager.SetReaperModuleLaserPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='hardware_manager.SetReaperModuleLaserPowerRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13345,
  serialized_end=13415,
)


_SETREAPERMODULELASERPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperModuleLaserPowerResponse',
  full_name='hardware_manager.SetReaperModuleLaserPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetReaperModuleLaserPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13417,
  serialized_end=13469,
)


_SETREAPERMODULESTROBEPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperModuleStrobePowerRequest',
  full_name='hardware_manager.SetReaperModuleStrobePowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='hardware_manager.SetReaperModuleStrobePowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='hardware_manager.SetReaperModuleStrobePowerRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13471,
  serialized_end=13542,
)


_SETREAPERMODULESTROBEPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperModuleStrobePowerResponse',
  full_name='hardware_manager.SetReaperModuleStrobePowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.SetReaperModuleStrobePowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13544,
  serialized_end=13597,
)


_IDENTIFYMODULEREQUEST = _descriptor.Descriptor(
  name='IdentifyModuleRequest',
  full_name='hardware_manager.IdentifyModuleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='to_id_ip', full_name='hardware_manager.IdentifyModuleRequest.to_id_ip', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='turn_off_ips', full_name='hardware_manager.IdentifyModuleRequest.turn_off_ips', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13599,
  serialized_end=13662,
)


_IDENTIFYMODULERESPONSE = _descriptor.Descriptor(
  name='IdentifyModuleResponse',
  full_name='hardware_manager.IdentifyModuleResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='hardware_manager.IdentifyModuleResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13664,
  serialized_end=13705,
)

_SETIMPLEMENTSTATEREQUEST.oneofs_by_name['_error_message'].fields.append(
  _SETIMPLEMENTSTATEREQUEST.fields_by_name['error_message'])
_SETIMPLEMENTSTATEREQUEST.fields_by_name['error_message'].containing_oneof = _SETIMPLEMENTSTATEREQUEST.oneofs_by_name['_error_message']
_GETGPSDATARESPONSE.fields_by_name['lla'].message_type = _GEOLLA
_GETGPSDATARESPONSE.fields_by_name['ecef'].message_type = _GEOECEF
_GETNEXTGPSDATARESPONSE.fields_by_name['lla'].message_type = _GEOLLA
_DUALGPSDATA.fields_by_name['carrier_phase'].enum_type = _CARRIERPHASESOLN
_DUALGPSDATA.fields_by_name['north'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.fields_by_name['east'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.fields_by_name['down'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.fields_by_name['length'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.fields_by_name['heading'].message_type = _VALUEWITHACCURACY
_DUALGPSDATA.oneofs_by_name['_north'].fields.append(
  _DUALGPSDATA.fields_by_name['north'])
_DUALGPSDATA.fields_by_name['north'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_north']
_DUALGPSDATA.oneofs_by_name['_east'].fields.append(
  _DUALGPSDATA.fields_by_name['east'])
_DUALGPSDATA.fields_by_name['east'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_east']
_DUALGPSDATA.oneofs_by_name['_down'].fields.append(
  _DUALGPSDATA.fields_by_name['down'])
_DUALGPSDATA.fields_by_name['down'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_down']
_DUALGPSDATA.oneofs_by_name['_length'].fields.append(
  _DUALGPSDATA.fields_by_name['length'])
_DUALGPSDATA.fields_by_name['length'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_length']
_DUALGPSDATA.oneofs_by_name['_heading'].fields.append(
  _DUALGPSDATA.fields_by_name['heading'])
_DUALGPSDATA.fields_by_name['heading'].containing_oneof = _DUALGPSDATA.oneofs_by_name['_heading']
_GETNEXTRAWGPSDATARESPONSE.fields_by_name['fix_type'].enum_type = _FIXTYPE
_GETNEXTRAWGPSDATARESPONSE.fields_by_name['carrier_phase'].enum_type = _CARRIERPHASESOLN
_GETNEXTRAWGPSDATARESPONSE.fields_by_name['dual'].message_type = _DUALGPSDATA
_GETNEXTRAWGPSDATARESPONSE.oneofs_by_name['_dual'].fields.append(
  _GETNEXTRAWGPSDATARESPONSE.fields_by_name['dual'])
_GETNEXTRAWGPSDATARESPONSE.fields_by_name['dual'].containing_oneof = _GETNEXTRAWGPSDATARESPONSE.oneofs_by_name['_dual']
_GETSUPERVISORYSTATUSRESPONSE.fields_by_name['chiller_alarms'].message_type = _CHILLERALARMS
_STROBESETTINGS.oneofs_by_name['_exposure_us'].fields.append(
  _STROBESETTINGS.fields_by_name['exposure_us'])
_STROBESETTINGS.fields_by_name['exposure_us'].containing_oneof = _STROBESETTINGS.oneofs_by_name['_exposure_us']
_STROBESETTINGS.oneofs_by_name['_period_us'].fields.append(
  _STROBESETTINGS.fields_by_name['period_us'])
_STROBESETTINGS.fields_by_name['period_us'].containing_oneof = _STROBESETTINGS.oneofs_by_name['_period_us']
_STROBESETTINGS.oneofs_by_name['_targets_per_predict_ratio'].fields.append(
  _STROBESETTINGS.fields_by_name['targets_per_predict_ratio'])
_STROBESETTINGS.fields_by_name['targets_per_predict_ratio'].containing_oneof = _STROBESETTINGS.oneofs_by_name['_targets_per_predict_ratio']
_NETWORKPORTSTATE.fields_by_name['actual_link_speed'].enum_type = _NETWORKLINKSPEED
_NETWORKPORTSTATE.fields_by_name['expected_link_speed'].enum_type = _NETWORKLINKSPEED
_REAPERPCSENSORDATA.fields_by_name['scanner_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['target_cam_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['predict_cam_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['ipmi_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['global_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['ext_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.oneofs_by_name['_temperature_gpu_1_c'].fields.append(
  _REAPERPCSENSORDATA.fields_by_name['temperature_gpu_1_c'])
_REAPERPCSENSORDATA.fields_by_name['temperature_gpu_1_c'].containing_oneof = _REAPERPCSENSORDATA.oneofs_by_name['_temperature_gpu_1_c']
_REAPERPCSENSORDATA.oneofs_by_name['_temperature_gpu_2_c'].fields.append(
  _REAPERPCSENSORDATA.fields_by_name['temperature_gpu_2_c'])
_REAPERPCSENSORDATA.fields_by_name['temperature_gpu_2_c'].containing_oneof = _REAPERPCSENSORDATA.oneofs_by_name['_temperature_gpu_2_c']
_REAPERSCANNERSENSORDATA.fields_by_name['laser_status'].message_type = _REAPERSCANNERLASERSTATUS
_REAPERSCANNERSENSORDATA.fields_by_name['motor_pan'].message_type = _REAPERSCANNERMOTORDATA
_REAPERSCANNERSENSORDATA.fields_by_name['motor_tilt'].message_type = _REAPERSCANNERMOTORDATA
_REAPERSCANNERSENSORDATA.oneofs_by_name['_laser_status'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['laser_status'])
_REAPERSCANNERSENSORDATA.fields_by_name['laser_status'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_laser_status']
_REAPERSCANNERSENSORDATA.oneofs_by_name['_target_sn'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['target_sn'])
_REAPERSCANNERSENSORDATA.fields_by_name['target_sn'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_target_sn']
_REAPERSCANNERSENSORDATA.oneofs_by_name['_temperature_target_c'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['temperature_target_c'])
_REAPERSCANNERSENSORDATA.fields_by_name['temperature_target_c'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_temperature_target_c']
_REAPERSCANNERSENSORDATA.oneofs_by_name['_motor_pan'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['motor_pan'])
_REAPERSCANNERSENSORDATA.fields_by_name['motor_pan'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_motor_pan']
_REAPERSCANNERSENSORDATA.oneofs_by_name['_motor_tilt'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['motor_tilt'])
_REAPERSCANNERSENSORDATA.fields_by_name['motor_tilt'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_motor_tilt']
_REAPERCENTERENCLOSUREDATA.fields_by_name['chiller_alarms'].message_type = _CHILLERALARMS
_REAPERMODULESENSORDATA.fields_by_name['enviro_enclosure'].message_type = _ENVIRONMENTALSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['enviro_pc'].message_type = _ENVIRONMENTALSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['coolant_inlet'].message_type = _COOLANTSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['coolant_outlet'].message_type = _COOLANTSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['pc'].message_type = _REAPERPCSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['scanner_a'].message_type = _REAPERSCANNERSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['scanner_b'].message_type = _REAPERSCANNERSENSORDATA
_REAPERMODULESENSORDATA.oneofs_by_name['_module_sn'].fields.append(
  _REAPERMODULESENSORDATA.fields_by_name['module_sn'])
_REAPERMODULESENSORDATA.fields_by_name['module_sn'].containing_oneof = _REAPERMODULESENSORDATA.oneofs_by_name['_module_sn']
_REAPERMODULESENSORDATA.oneofs_by_name['_pc'].fields.append(
  _REAPERMODULESENSORDATA.fields_by_name['pc'])
_REAPERMODULESENSORDATA.fields_by_name['pc'].containing_oneof = _REAPERMODULESENSORDATA.oneofs_by_name['_pc']
_REAPERMODULESENSORDATA.oneofs_by_name['_scanner_a'].fields.append(
  _REAPERMODULESENSORDATA.fields_by_name['scanner_a'])
_REAPERMODULESENSORDATA.fields_by_name['scanner_a'].containing_oneof = _REAPERMODULESENSORDATA.oneofs_by_name['_scanner_a']
_REAPERMODULESENSORDATA.oneofs_by_name['_scanner_b'].fields.append(
  _REAPERMODULESENSORDATA.fields_by_name['scanner_b'])
_REAPERMODULESENSORDATA.fields_by_name['scanner_b'].containing_oneof = _REAPERMODULESENSORDATA.oneofs_by_name['_scanner_b']
_GETREAPERENCLOSURESENSORSRESPONSE.fields_by_name['sensors'].message_type = _REAPERCENTERENCLOSUREDATA
_GETREAPERMODULESENSORSRESPONSE.fields_by_name['module_sensors'].message_type = _REAPERMODULESENSORDATA
_SETREAPERSCANNERPOWERREQUEST.oneofs_by_name['_scanner_a_power'].fields.append(
  _SETREAPERSCANNERPOWERREQUEST.fields_by_name['scanner_a_power'])
_SETREAPERSCANNERPOWERREQUEST.fields_by_name['scanner_a_power'].containing_oneof = _SETREAPERSCANNERPOWERREQUEST.oneofs_by_name['_scanner_a_power']
_SETREAPERSCANNERPOWERREQUEST.oneofs_by_name['_scanner_b_power'].fields.append(
  _SETREAPERSCANNERPOWERREQUEST.fields_by_name['scanner_b_power'])
_SETREAPERSCANNERPOWERREQUEST.fields_by_name['scanner_b_power'].containing_oneof = _SETREAPERSCANNERPOWERREQUEST.oneofs_by_name['_scanner_b_power']
_SETREAPERTARGETPOWERREQUEST.oneofs_by_name['_target_a_power'].fields.append(
  _SETREAPERTARGETPOWERREQUEST.fields_by_name['target_a_power'])
_SETREAPERTARGETPOWERREQUEST.fields_by_name['target_a_power'].containing_oneof = _SETREAPERTARGETPOWERREQUEST.oneofs_by_name['_target_a_power']
_SETREAPERTARGETPOWERREQUEST.oneofs_by_name['_target_b_power'].fields.append(
  _SETREAPERTARGETPOWERREQUEST.fields_by_name['target_b_power'])
_SETREAPERTARGETPOWERREQUEST.fields_by_name['target_b_power'].containing_oneof = _SETREAPERTARGETPOWERREQUEST.oneofs_by_name['_target_b_power']
_SETREAPERSTROBECONFIGREQUEST.fields_by_name['settings'].message_type = _STROBESETTINGS
_SETREAPERSTROBECONFIGREQUEST.oneofs_by_name['_module_id'].fields.append(
  _SETREAPERSTROBECONFIGREQUEST.fields_by_name['module_id'])
_SETREAPERSTROBECONFIGREQUEST.fields_by_name['module_id'].containing_oneof = _SETREAPERSTROBECONFIGREQUEST.oneofs_by_name['_module_id']
_SETREAPERSTROBEENABLEREQUEST.oneofs_by_name['_duration_ms'].fields.append(
  _SETREAPERSTROBEENABLEREQUEST.fields_by_name['duration_ms'])
_SETREAPERSTROBEENABLEREQUEST.fields_by_name['duration_ms'].containing_oneof = _SETREAPERSTROBEENABLEREQUEST.oneofs_by_name['_duration_ms']
DESCRIPTOR.message_types_by_name['PingRequest'] = _PINGREQUEST
DESCRIPTOR.message_types_by_name['PingResponse'] = _PINGRESPONSE
DESCRIPTOR.message_types_by_name['GetRotaryTicksRequest'] = _GETROTARYTICKSREQUEST
DESCRIPTOR.message_types_by_name['GetRotaryTicksResponse'] = _GETROTARYTICKSRESPONSE
DESCRIPTOR.message_types_by_name['GetNextDistanceRequest'] = _GETNEXTDISTANCEREQUEST
DESCRIPTOR.message_types_by_name['GetNextDistanceResponse'] = _GETNEXTDISTANCERESPONSE
DESCRIPTOR.message_types_by_name['GetNextVelocityRequest'] = _GETNEXTVELOCITYREQUEST
DESCRIPTOR.message_types_by_name['GetNextVelocityResponse'] = _GETNEXTVELOCITYRESPONSE
DESCRIPTOR.message_types_by_name['SetJimboxSpeedRequest'] = _SETJIMBOXSPEEDREQUEST
DESCRIPTOR.message_types_by_name['SetJimboxSpeedResponse'] = _SETJIMBOXSPEEDRESPONSE
DESCRIPTOR.message_types_by_name['SetCruiseEnabledRequest'] = _SETCRUISEENABLEDREQUEST
DESCRIPTOR.message_types_by_name['SetCruiseEnabledResponse'] = _SETCRUISEENABLEDRESPONSE
DESCRIPTOR.message_types_by_name['GetCruiseStatusRequest'] = _GETCRUISESTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetCruiseStatusResponse'] = _GETCRUISESTATUSRESPONSE
DESCRIPTOR.message_types_by_name['SetImplementStateRequest'] = _SETIMPLEMENTSTATEREQUEST
DESCRIPTOR.message_types_by_name['SetImplementStateResponse'] = _SETIMPLEMENTSTATERESPONSE
DESCRIPTOR.message_types_by_name['SetSafeStateEnforcementRequest'] = _SETSAFESTATEENFORCEMENTREQUEST
DESCRIPTOR.message_types_by_name['SetSafeStateEnforcementResponse'] = _SETSAFESTATEENFORCEMENTRESPONSE
DESCRIPTOR.message_types_by_name['GetTractorSafetyStateRequest'] = _GETTRACTORSAFETYSTATEREQUEST
DESCRIPTOR.message_types_by_name['GetTractorSafetyStateResponse'] = _GETTRACTORSAFETYSTATERESPONSE
DESCRIPTOR.message_types_by_name['GetTractorIFStateRequest'] = _GETTRACTORIFSTATEREQUEST
DESCRIPTOR.message_types_by_name['GetTractorIFStateResponse'] = _GETTRACTORIFSTATERESPONSE
DESCRIPTOR.message_types_by_name['GetSafetyStatusRequest'] = _GETSAFETYSTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetSafetyStatusResponse'] = _GETSAFETYSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['GeoLLA'] = _GEOLLA
DESCRIPTOR.message_types_by_name['GeoECEF'] = _GEOECEF
DESCRIPTOR.message_types_by_name['GetGPSDataRequest'] = _GETGPSDATAREQUEST
DESCRIPTOR.message_types_by_name['GetGPSDataResponse'] = _GETGPSDATARESPONSE
DESCRIPTOR.message_types_by_name['GetNextGPSDataRequest'] = _GETNEXTGPSDATAREQUEST
DESCRIPTOR.message_types_by_name['GetNextGPSDataResponse'] = _GETNEXTGPSDATARESPONSE
DESCRIPTOR.message_types_by_name['GetNextRawGPSDataRequest'] = _GETNEXTRAWGPSDATAREQUEST
DESCRIPTOR.message_types_by_name['ValueWithAccuracy'] = _VALUEWITHACCURACY
DESCRIPTOR.message_types_by_name['DualGpsData'] = _DUALGPSDATA
DESCRIPTOR.message_types_by_name['GetNextRawGPSDataResponse'] = _GETNEXTRAWGPSDATARESPONSE
DESCRIPTOR.message_types_by_name['GetGPSFixedPosRequest'] = _GETGPSFIXEDPOSREQUEST
DESCRIPTOR.message_types_by_name['GetGPSFixedPosResponse'] = _GETGPSFIXEDPOSRESPONSE
DESCRIPTOR.message_types_by_name['GetManagedBoardErrorsRequest'] = _GETMANAGEDBOARDERRORSREQUEST
DESCRIPTOR.message_types_by_name['GetManagedBoardErrorsResponse'] = _GETMANAGEDBOARDERRORSRESPONSE
DESCRIPTOR.message_types_by_name['GetSupervisoryStatusRequest'] = _GETSUPERVISORYSTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetReaperSupervisoryStatusRequest'] = _GETREAPERSUPERVISORYSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ChillerAlarms'] = _CHILLERALARMS
DESCRIPTOR.message_types_by_name['GetSupervisoryStatusResponse'] = _GETSUPERVISORYSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['SetServerDisableRequest'] = _SETSERVERDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetServerDisableResponse'] = _SETSERVERDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetBTLDisableRequest'] = _SETBTLDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetBTLDisableResponse'] = _SETBTLDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetScannersDisableRequest'] = _SETSCANNERSDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetScannersDisableResponse'] = _SETSCANNERSDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetWheelEncoderBoardDisableRequest'] = _SETWHEELENCODERBOARDDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetWheelEncoderBoardDisableResponse'] = _SETWHEELENCODERBOARDDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetWheelEncoderDisableRequest'] = _SETWHEELENCODERDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetWheelEncoderDisableResponse'] = _SETWHEELENCODERDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetStrobeDisableRequest'] = _SETSTROBEDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetStrobeDisableResponse'] = _SETSTROBEDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetGPSDisableRequest'] = _SETGPSDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetGPSDisableResponse'] = _SETGPSDISABLERESPONSE
DESCRIPTOR.message_types_by_name['CommandComputerPowerCycleRequest'] = _COMMANDCOMPUTERPOWERCYCLEREQUEST
DESCRIPTOR.message_types_by_name['CommandComputerPowerCycleResponse'] = _COMMANDCOMPUTERPOWERCYCLERESPONSE
DESCRIPTOR.message_types_by_name['SuicideSwitchRequest'] = _SUICIDESWITCHREQUEST
DESCRIPTOR.message_types_by_name['SuicideSwitchResponse'] = _SUICIDESWITCHRESPONSE
DESCRIPTOR.message_types_by_name['SetMainContactorDisableRequest'] = _SETMAINCONTACTORDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetMainContactorDisableResponse'] = _SETMAINCONTACTORDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetAirConditionerDisableRequest'] = _SETAIRCONDITIONERDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetAirConditionerDisableResponse'] = _SETAIRCONDITIONERDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetChillerDisableRequest'] = _SETCHILLERDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetChillerDisableResponse'] = _SETCHILLERDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetTempBypassDisableRequest'] = _SETTEMPBYPASSDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetTempBypassDisableResponse'] = _SETTEMPBYPASSDISABLERESPONSE
DESCRIPTOR.message_types_by_name['SetHumidityBypassDisableRequest'] = _SETHUMIDITYBYPASSDISABLEREQUEST
DESCRIPTOR.message_types_by_name['SetHumidityBypassDisableResponse'] = _SETHUMIDITYBYPASSDISABLERESPONSE
DESCRIPTOR.message_types_by_name['GetAvailableUSBStorageRequest'] = _GETAVAILABLEUSBSTORAGEREQUEST
DESCRIPTOR.message_types_by_name['GetAvailableUSBStorageResponse'] = _GETAVAILABLEUSBSTORAGERESPONSE
DESCRIPTOR.message_types_by_name['GetReadyRequest'] = _GETREADYREQUEST
DESCRIPTOR.message_types_by_name['GetReadyResponse'] = _GETREADYRESPONSE
DESCRIPTOR.message_types_by_name['Get240vUptimeRequest'] = _GET240VUPTIMEREQUEST
DESCRIPTOR.message_types_by_name['Get240vUptimeResponse'] = _GET240VUPTIMERESPONSE
DESCRIPTOR.message_types_by_name['GetDeltaTravelMMRequest'] = _GETDELTATRAVELMMREQUEST
DESCRIPTOR.message_types_by_name['GetDeltaTravelMMResponse'] = _GETDELTATRAVELMMRESPONSE
DESCRIPTOR.message_types_by_name['GetRuntimeRequest'] = _GETRUNTIMEREQUEST
DESCRIPTOR.message_types_by_name['GetRuntimeResponse'] = _GETRUNTIMERESPONSE
DESCRIPTOR.message_types_by_name['GetWheelEncoderResolutionRequest'] = _GETWHEELENCODERRESOLUTIONREQUEST
DESCRIPTOR.message_types_by_name['GetWheelEncoderResolutionResponse'] = _GETWHEELENCODERRESOLUTIONRESPONSE
DESCRIPTOR.message_types_by_name['StrobeSettings'] = _STROBESETTINGS
DESCRIPTOR.message_types_by_name['SetStrobeSettingsResponse'] = _SETSTROBESETTINGSRESPONSE
DESCRIPTOR.message_types_by_name['GetStrobeSettingsRequest'] = _GETSTROBESETTINGSREQUEST
DESCRIPTOR.message_types_by_name['EnvironmentalSensorData'] = _ENVIRONMENTALSENSORDATA
DESCRIPTOR.message_types_by_name['CoolantSensorData'] = _COOLANTSENSORDATA
DESCRIPTOR.message_types_by_name['NetworkPortState'] = _NETWORKPORTSTATE
DESCRIPTOR.message_types_by_name['ReaperPcSensorData'] = _REAPERPCSENSORDATA
DESCRIPTOR.message_types_by_name['ReaperScannerLaserStatus'] = _REAPERSCANNERLASERSTATUS
DESCRIPTOR.message_types_by_name['ReaperScannerMotorData'] = _REAPERSCANNERMOTORDATA
DESCRIPTOR.message_types_by_name['ReaperScannerSensorData'] = _REAPERSCANNERSENSORDATA
DESCRIPTOR.message_types_by_name['ReaperCenterEnclosureData'] = _REAPERCENTERENCLOSUREDATA
DESCRIPTOR.message_types_by_name['ReaperModuleSensorData'] = _REAPERMODULESENSORDATA
DESCRIPTOR.message_types_by_name['GetReaperEnclosureSensorsRequest'] = _GETREAPERENCLOSURESENSORSREQUEST
DESCRIPTOR.message_types_by_name['GetReaperEnclosureSensorsResponse'] = _GETREAPERENCLOSURESENSORSRESPONSE
DESCRIPTOR.message_types_by_name['GetReaperModuleSensorsRequest'] = _GETREAPERMODULESENSORSREQUEST
DESCRIPTOR.message_types_by_name['GetReaperModuleSensorsResponse'] = _GETREAPERMODULESENSORSRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperScannerPowerRequest'] = _SETREAPERSCANNERPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperScannerPowerResponse'] = _SETREAPERSCANNERPOWERRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperTargetPowerRequest'] = _SETREAPERTARGETPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperTargetPowerResponse'] = _SETREAPERTARGETPOWERRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperPredictCamPowerRequest'] = _SETREAPERPREDICTCAMPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperPredictCamPowerResponse'] = _SETREAPERPREDICTCAMPOWERRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperStrobeConfigRequest'] = _SETREAPERSTROBECONFIGREQUEST
DESCRIPTOR.message_types_by_name['SetReaperStrobeConfigResponse'] = _SETREAPERSTROBECONFIGRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperStrobeEnableRequest'] = _SETREAPERSTROBEENABLEREQUEST
DESCRIPTOR.message_types_by_name['SetReaperStrobeEnableResponse'] = _SETREAPERSTROBEENABLERESPONSE
DESCRIPTOR.message_types_by_name['SetReaperModulePcPowerRequest'] = _SETREAPERMODULEPCPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperModulePcPowerResponse'] = _SETREAPERMODULEPCPOWERRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperModuleLaserPowerRequest'] = _SETREAPERMODULELASERPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperModuleLaserPowerResponse'] = _SETREAPERMODULELASERPOWERRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperModuleStrobePowerRequest'] = _SETREAPERMODULESTROBEPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperModuleStrobePowerResponse'] = _SETREAPERMODULESTROBEPOWERRESPONSE
DESCRIPTOR.message_types_by_name['IdentifyModuleRequest'] = _IDENTIFYMODULEREQUEST
DESCRIPTOR.message_types_by_name['IdentifyModuleResponse'] = _IDENTIFYMODULERESPONSE
DESCRIPTOR.enum_types_by_name['CarrierPhaseSoln'] = _CARRIERPHASESOLN
DESCRIPTOR.enum_types_by_name['FixType'] = _FIXTYPE
DESCRIPTOR.enum_types_by_name['NetworkLinkSpeed'] = _NETWORKLINKSPEED
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PingRequest = _reflection.GeneratedProtocolMessageType('PingRequest', (_message.Message,), {
  'DESCRIPTOR' : _PINGREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.PingRequest)
  })
_sym_db.RegisterMessage(PingRequest)

PingResponse = _reflection.GeneratedProtocolMessageType('PingResponse', (_message.Message,), {
  'DESCRIPTOR' : _PINGRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.PingResponse)
  })
_sym_db.RegisterMessage(PingResponse)

GetRotaryTicksRequest = _reflection.GeneratedProtocolMessageType('GetRotaryTicksRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETROTARYTICKSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetRotaryTicksRequest)
  })
_sym_db.RegisterMessage(GetRotaryTicksRequest)

GetRotaryTicksResponse = _reflection.GeneratedProtocolMessageType('GetRotaryTicksResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETROTARYTICKSRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetRotaryTicksResponse)
  })
_sym_db.RegisterMessage(GetRotaryTicksResponse)

GetNextDistanceRequest = _reflection.GeneratedProtocolMessageType('GetNextDistanceRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTDISTANCEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetNextDistanceRequest)
  })
_sym_db.RegisterMessage(GetNextDistanceRequest)

GetNextDistanceResponse = _reflection.GeneratedProtocolMessageType('GetNextDistanceResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTDISTANCERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetNextDistanceResponse)
  })
_sym_db.RegisterMessage(GetNextDistanceResponse)

GetNextVelocityRequest = _reflection.GeneratedProtocolMessageType('GetNextVelocityRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTVELOCITYREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetNextVelocityRequest)
  })
_sym_db.RegisterMessage(GetNextVelocityRequest)

GetNextVelocityResponse = _reflection.GeneratedProtocolMessageType('GetNextVelocityResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTVELOCITYRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetNextVelocityResponse)
  })
_sym_db.RegisterMessage(GetNextVelocityResponse)

SetJimboxSpeedRequest = _reflection.GeneratedProtocolMessageType('SetJimboxSpeedRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETJIMBOXSPEEDREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetJimboxSpeedRequest)
  })
_sym_db.RegisterMessage(SetJimboxSpeedRequest)

SetJimboxSpeedResponse = _reflection.GeneratedProtocolMessageType('SetJimboxSpeedResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETJIMBOXSPEEDRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetJimboxSpeedResponse)
  })
_sym_db.RegisterMessage(SetJimboxSpeedResponse)

SetCruiseEnabledRequest = _reflection.GeneratedProtocolMessageType('SetCruiseEnabledRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETCRUISEENABLEDREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetCruiseEnabledRequest)
  })
_sym_db.RegisterMessage(SetCruiseEnabledRequest)

SetCruiseEnabledResponse = _reflection.GeneratedProtocolMessageType('SetCruiseEnabledResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETCRUISEENABLEDRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetCruiseEnabledResponse)
  })
_sym_db.RegisterMessage(SetCruiseEnabledResponse)

GetCruiseStatusRequest = _reflection.GeneratedProtocolMessageType('GetCruiseStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCRUISESTATUSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetCruiseStatusRequest)
  })
_sym_db.RegisterMessage(GetCruiseStatusRequest)

GetCruiseStatusResponse = _reflection.GeneratedProtocolMessageType('GetCruiseStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCRUISESTATUSRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetCruiseStatusResponse)
  })
_sym_db.RegisterMessage(GetCruiseStatusResponse)

SetImplementStateRequest = _reflection.GeneratedProtocolMessageType('SetImplementStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETIMPLEMENTSTATEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetImplementStateRequest)
  })
_sym_db.RegisterMessage(SetImplementStateRequest)

SetImplementStateResponse = _reflection.GeneratedProtocolMessageType('SetImplementStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETIMPLEMENTSTATERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetImplementStateResponse)
  })
_sym_db.RegisterMessage(SetImplementStateResponse)

SetSafeStateEnforcementRequest = _reflection.GeneratedProtocolMessageType('SetSafeStateEnforcementRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETSAFESTATEENFORCEMENTREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetSafeStateEnforcementRequest)
  })
_sym_db.RegisterMessage(SetSafeStateEnforcementRequest)

SetSafeStateEnforcementResponse = _reflection.GeneratedProtocolMessageType('SetSafeStateEnforcementResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETSAFESTATEENFORCEMENTRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetSafeStateEnforcementResponse)
  })
_sym_db.RegisterMessage(SetSafeStateEnforcementResponse)

GetTractorSafetyStateRequest = _reflection.GeneratedProtocolMessageType('GetTractorSafetyStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTRACTORSAFETYSTATEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetTractorSafetyStateRequest)
  })
_sym_db.RegisterMessage(GetTractorSafetyStateRequest)

GetTractorSafetyStateResponse = _reflection.GeneratedProtocolMessageType('GetTractorSafetyStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETTRACTORSAFETYSTATERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetTractorSafetyStateResponse)
  })
_sym_db.RegisterMessage(GetTractorSafetyStateResponse)

GetTractorIFStateRequest = _reflection.GeneratedProtocolMessageType('GetTractorIFStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTRACTORIFSTATEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetTractorIFStateRequest)
  })
_sym_db.RegisterMessage(GetTractorIFStateRequest)

GetTractorIFStateResponse = _reflection.GeneratedProtocolMessageType('GetTractorIFStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETTRACTORIFSTATERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetTractorIFStateResponse)
  })
_sym_db.RegisterMessage(GetTractorIFStateResponse)

GetSafetyStatusRequest = _reflection.GeneratedProtocolMessageType('GetSafetyStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSAFETYSTATUSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetSafetyStatusRequest)
  })
_sym_db.RegisterMessage(GetSafetyStatusRequest)

GetSafetyStatusResponse = _reflection.GeneratedProtocolMessageType('GetSafetyStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSAFETYSTATUSRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetSafetyStatusResponse)
  })
_sym_db.RegisterMessage(GetSafetyStatusResponse)

GeoLLA = _reflection.GeneratedProtocolMessageType('GeoLLA', (_message.Message,), {
  'DESCRIPTOR' : _GEOLLA,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GeoLLA)
  })
_sym_db.RegisterMessage(GeoLLA)

GeoECEF = _reflection.GeneratedProtocolMessageType('GeoECEF', (_message.Message,), {
  'DESCRIPTOR' : _GEOECEF,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GeoECEF)
  })
_sym_db.RegisterMessage(GeoECEF)

GetGPSDataRequest = _reflection.GeneratedProtocolMessageType('GetGPSDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETGPSDATAREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetGPSDataRequest)
  })
_sym_db.RegisterMessage(GetGPSDataRequest)

GetGPSDataResponse = _reflection.GeneratedProtocolMessageType('GetGPSDataResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETGPSDATARESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetGPSDataResponse)
  })
_sym_db.RegisterMessage(GetGPSDataResponse)

GetNextGPSDataRequest = _reflection.GeneratedProtocolMessageType('GetNextGPSDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTGPSDATAREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetNextGPSDataRequest)
  })
_sym_db.RegisterMessage(GetNextGPSDataRequest)

GetNextGPSDataResponse = _reflection.GeneratedProtocolMessageType('GetNextGPSDataResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTGPSDATARESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetNextGPSDataResponse)
  })
_sym_db.RegisterMessage(GetNextGPSDataResponse)

GetNextRawGPSDataRequest = _reflection.GeneratedProtocolMessageType('GetNextRawGPSDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTRAWGPSDATAREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetNextRawGPSDataRequest)
  })
_sym_db.RegisterMessage(GetNextRawGPSDataRequest)

ValueWithAccuracy = _reflection.GeneratedProtocolMessageType('ValueWithAccuracy', (_message.Message,), {
  'DESCRIPTOR' : _VALUEWITHACCURACY,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.ValueWithAccuracy)
  })
_sym_db.RegisterMessage(ValueWithAccuracy)

DualGpsData = _reflection.GeneratedProtocolMessageType('DualGpsData', (_message.Message,), {
  'DESCRIPTOR' : _DUALGPSDATA,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.DualGpsData)
  })
_sym_db.RegisterMessage(DualGpsData)

GetNextRawGPSDataResponse = _reflection.GeneratedProtocolMessageType('GetNextRawGPSDataResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTRAWGPSDATARESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetNextRawGPSDataResponse)
  })
_sym_db.RegisterMessage(GetNextRawGPSDataResponse)

GetGPSFixedPosRequest = _reflection.GeneratedProtocolMessageType('GetGPSFixedPosRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETGPSFIXEDPOSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetGPSFixedPosRequest)
  })
_sym_db.RegisterMessage(GetGPSFixedPosRequest)

GetGPSFixedPosResponse = _reflection.GeneratedProtocolMessageType('GetGPSFixedPosResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETGPSFIXEDPOSRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetGPSFixedPosResponse)
  })
_sym_db.RegisterMessage(GetGPSFixedPosResponse)

GetManagedBoardErrorsRequest = _reflection.GeneratedProtocolMessageType('GetManagedBoardErrorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETMANAGEDBOARDERRORSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetManagedBoardErrorsRequest)
  })
_sym_db.RegisterMessage(GetManagedBoardErrorsRequest)

GetManagedBoardErrorsResponse = _reflection.GeneratedProtocolMessageType('GetManagedBoardErrorsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETMANAGEDBOARDERRORSRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetManagedBoardErrorsResponse)
  })
_sym_db.RegisterMessage(GetManagedBoardErrorsResponse)

GetSupervisoryStatusRequest = _reflection.GeneratedProtocolMessageType('GetSupervisoryStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSUPERVISORYSTATUSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetSupervisoryStatusRequest)
  })
_sym_db.RegisterMessage(GetSupervisoryStatusRequest)

GetReaperSupervisoryStatusRequest = _reflection.GeneratedProtocolMessageType('GetReaperSupervisoryStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETREAPERSUPERVISORYSTATUSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetReaperSupervisoryStatusRequest)
  })
_sym_db.RegisterMessage(GetReaperSupervisoryStatusRequest)

ChillerAlarms = _reflection.GeneratedProtocolMessageType('ChillerAlarms', (_message.Message,), {
  'DESCRIPTOR' : _CHILLERALARMS,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.ChillerAlarms)
  })
_sym_db.RegisterMessage(ChillerAlarms)

GetSupervisoryStatusResponse = _reflection.GeneratedProtocolMessageType('GetSupervisoryStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETSUPERVISORYSTATUSRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetSupervisoryStatusResponse)
  })
_sym_db.RegisterMessage(GetSupervisoryStatusResponse)

SetServerDisableRequest = _reflection.GeneratedProtocolMessageType('SetServerDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETSERVERDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetServerDisableRequest)
  })
_sym_db.RegisterMessage(SetServerDisableRequest)

SetServerDisableResponse = _reflection.GeneratedProtocolMessageType('SetServerDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETSERVERDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetServerDisableResponse)
  })
_sym_db.RegisterMessage(SetServerDisableResponse)

SetBTLDisableRequest = _reflection.GeneratedProtocolMessageType('SetBTLDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETBTLDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetBTLDisableRequest)
  })
_sym_db.RegisterMessage(SetBTLDisableRequest)

SetBTLDisableResponse = _reflection.GeneratedProtocolMessageType('SetBTLDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETBTLDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetBTLDisableResponse)
  })
_sym_db.RegisterMessage(SetBTLDisableResponse)

SetScannersDisableRequest = _reflection.GeneratedProtocolMessageType('SetScannersDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETSCANNERSDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetScannersDisableRequest)
  })
_sym_db.RegisterMessage(SetScannersDisableRequest)

SetScannersDisableResponse = _reflection.GeneratedProtocolMessageType('SetScannersDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETSCANNERSDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetScannersDisableResponse)
  })
_sym_db.RegisterMessage(SetScannersDisableResponse)

SetWheelEncoderBoardDisableRequest = _reflection.GeneratedProtocolMessageType('SetWheelEncoderBoardDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETWHEELENCODERBOARDDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetWheelEncoderBoardDisableRequest)
  })
_sym_db.RegisterMessage(SetWheelEncoderBoardDisableRequest)

SetWheelEncoderBoardDisableResponse = _reflection.GeneratedProtocolMessageType('SetWheelEncoderBoardDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETWHEELENCODERBOARDDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetWheelEncoderBoardDisableResponse)
  })
_sym_db.RegisterMessage(SetWheelEncoderBoardDisableResponse)

SetWheelEncoderDisableRequest = _reflection.GeneratedProtocolMessageType('SetWheelEncoderDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETWHEELENCODERDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetWheelEncoderDisableRequest)
  })
_sym_db.RegisterMessage(SetWheelEncoderDisableRequest)

SetWheelEncoderDisableResponse = _reflection.GeneratedProtocolMessageType('SetWheelEncoderDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETWHEELENCODERDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetWheelEncoderDisableResponse)
  })
_sym_db.RegisterMessage(SetWheelEncoderDisableResponse)

SetStrobeDisableRequest = _reflection.GeneratedProtocolMessageType('SetStrobeDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETSTROBEDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetStrobeDisableRequest)
  })
_sym_db.RegisterMessage(SetStrobeDisableRequest)

SetStrobeDisableResponse = _reflection.GeneratedProtocolMessageType('SetStrobeDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETSTROBEDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetStrobeDisableResponse)
  })
_sym_db.RegisterMessage(SetStrobeDisableResponse)

SetGPSDisableRequest = _reflection.GeneratedProtocolMessageType('SetGPSDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETGPSDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetGPSDisableRequest)
  })
_sym_db.RegisterMessage(SetGPSDisableRequest)

SetGPSDisableResponse = _reflection.GeneratedProtocolMessageType('SetGPSDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETGPSDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetGPSDisableResponse)
  })
_sym_db.RegisterMessage(SetGPSDisableResponse)

CommandComputerPowerCycleRequest = _reflection.GeneratedProtocolMessageType('CommandComputerPowerCycleRequest', (_message.Message,), {
  'DESCRIPTOR' : _COMMANDCOMPUTERPOWERCYCLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.CommandComputerPowerCycleRequest)
  })
_sym_db.RegisterMessage(CommandComputerPowerCycleRequest)

CommandComputerPowerCycleResponse = _reflection.GeneratedProtocolMessageType('CommandComputerPowerCycleResponse', (_message.Message,), {
  'DESCRIPTOR' : _COMMANDCOMPUTERPOWERCYCLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.CommandComputerPowerCycleResponse)
  })
_sym_db.RegisterMessage(CommandComputerPowerCycleResponse)

SuicideSwitchRequest = _reflection.GeneratedProtocolMessageType('SuicideSwitchRequest', (_message.Message,), {
  'DESCRIPTOR' : _SUICIDESWITCHREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SuicideSwitchRequest)
  })
_sym_db.RegisterMessage(SuicideSwitchRequest)

SuicideSwitchResponse = _reflection.GeneratedProtocolMessageType('SuicideSwitchResponse', (_message.Message,), {
  'DESCRIPTOR' : _SUICIDESWITCHRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SuicideSwitchResponse)
  })
_sym_db.RegisterMessage(SuicideSwitchResponse)

SetMainContactorDisableRequest = _reflection.GeneratedProtocolMessageType('SetMainContactorDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETMAINCONTACTORDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetMainContactorDisableRequest)
  })
_sym_db.RegisterMessage(SetMainContactorDisableRequest)

SetMainContactorDisableResponse = _reflection.GeneratedProtocolMessageType('SetMainContactorDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETMAINCONTACTORDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetMainContactorDisableResponse)
  })
_sym_db.RegisterMessage(SetMainContactorDisableResponse)

SetAirConditionerDisableRequest = _reflection.GeneratedProtocolMessageType('SetAirConditionerDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETAIRCONDITIONERDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetAirConditionerDisableRequest)
  })
_sym_db.RegisterMessage(SetAirConditionerDisableRequest)

SetAirConditionerDisableResponse = _reflection.GeneratedProtocolMessageType('SetAirConditionerDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETAIRCONDITIONERDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetAirConditionerDisableResponse)
  })
_sym_db.RegisterMessage(SetAirConditionerDisableResponse)

SetChillerDisableRequest = _reflection.GeneratedProtocolMessageType('SetChillerDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETCHILLERDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetChillerDisableRequest)
  })
_sym_db.RegisterMessage(SetChillerDisableRequest)

SetChillerDisableResponse = _reflection.GeneratedProtocolMessageType('SetChillerDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETCHILLERDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetChillerDisableResponse)
  })
_sym_db.RegisterMessage(SetChillerDisableResponse)

SetTempBypassDisableRequest = _reflection.GeneratedProtocolMessageType('SetTempBypassDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETTEMPBYPASSDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetTempBypassDisableRequest)
  })
_sym_db.RegisterMessage(SetTempBypassDisableRequest)

SetTempBypassDisableResponse = _reflection.GeneratedProtocolMessageType('SetTempBypassDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETTEMPBYPASSDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetTempBypassDisableResponse)
  })
_sym_db.RegisterMessage(SetTempBypassDisableResponse)

SetHumidityBypassDisableRequest = _reflection.GeneratedProtocolMessageType('SetHumidityBypassDisableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETHUMIDITYBYPASSDISABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetHumidityBypassDisableRequest)
  })
_sym_db.RegisterMessage(SetHumidityBypassDisableRequest)

SetHumidityBypassDisableResponse = _reflection.GeneratedProtocolMessageType('SetHumidityBypassDisableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETHUMIDITYBYPASSDISABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetHumidityBypassDisableResponse)
  })
_sym_db.RegisterMessage(SetHumidityBypassDisableResponse)

GetAvailableUSBStorageRequest = _reflection.GeneratedProtocolMessageType('GetAvailableUSBStorageRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETAVAILABLEUSBSTORAGEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetAvailableUSBStorageRequest)
  })
_sym_db.RegisterMessage(GetAvailableUSBStorageRequest)

GetAvailableUSBStorageResponse = _reflection.GeneratedProtocolMessageType('GetAvailableUSBStorageResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETAVAILABLEUSBSTORAGERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetAvailableUSBStorageResponse)
  })
_sym_db.RegisterMessage(GetAvailableUSBStorageResponse)

GetReadyRequest = _reflection.GeneratedProtocolMessageType('GetReadyRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETREADYREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetReadyRequest)
  })
_sym_db.RegisterMessage(GetReadyRequest)

GetReadyResponse = _reflection.GeneratedProtocolMessageType('GetReadyResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETREADYRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetReadyResponse)
  })
_sym_db.RegisterMessage(GetReadyResponse)

Get240vUptimeRequest = _reflection.GeneratedProtocolMessageType('Get240vUptimeRequest', (_message.Message,), {
  'DESCRIPTOR' : _GET240VUPTIMEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.Get240vUptimeRequest)
  })
_sym_db.RegisterMessage(Get240vUptimeRequest)

Get240vUptimeResponse = _reflection.GeneratedProtocolMessageType('Get240vUptimeResponse', (_message.Message,), {
  'DESCRIPTOR' : _GET240VUPTIMERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.Get240vUptimeResponse)
  })
_sym_db.RegisterMessage(Get240vUptimeResponse)

GetDeltaTravelMMRequest = _reflection.GeneratedProtocolMessageType('GetDeltaTravelMMRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDELTATRAVELMMREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetDeltaTravelMMRequest)
  })
_sym_db.RegisterMessage(GetDeltaTravelMMRequest)

GetDeltaTravelMMResponse = _reflection.GeneratedProtocolMessageType('GetDeltaTravelMMResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETDELTATRAVELMMRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetDeltaTravelMMResponse)
  })
_sym_db.RegisterMessage(GetDeltaTravelMMResponse)

GetRuntimeRequest = _reflection.GeneratedProtocolMessageType('GetRuntimeRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETRUNTIMEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetRuntimeRequest)
  })
_sym_db.RegisterMessage(GetRuntimeRequest)

GetRuntimeResponse = _reflection.GeneratedProtocolMessageType('GetRuntimeResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETRUNTIMERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetRuntimeResponse)
  })
_sym_db.RegisterMessage(GetRuntimeResponse)

GetWheelEncoderResolutionRequest = _reflection.GeneratedProtocolMessageType('GetWheelEncoderResolutionRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETWHEELENCODERRESOLUTIONREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetWheelEncoderResolutionRequest)
  })
_sym_db.RegisterMessage(GetWheelEncoderResolutionRequest)

GetWheelEncoderResolutionResponse = _reflection.GeneratedProtocolMessageType('GetWheelEncoderResolutionResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETWHEELENCODERRESOLUTIONRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetWheelEncoderResolutionResponse)
  })
_sym_db.RegisterMessage(GetWheelEncoderResolutionResponse)

StrobeSettings = _reflection.GeneratedProtocolMessageType('StrobeSettings', (_message.Message,), {
  'DESCRIPTOR' : _STROBESETTINGS,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.StrobeSettings)
  })
_sym_db.RegisterMessage(StrobeSettings)

SetStrobeSettingsResponse = _reflection.GeneratedProtocolMessageType('SetStrobeSettingsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETSTROBESETTINGSRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetStrobeSettingsResponse)
  })
_sym_db.RegisterMessage(SetStrobeSettingsResponse)

GetStrobeSettingsRequest = _reflection.GeneratedProtocolMessageType('GetStrobeSettingsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETSTROBESETTINGSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetStrobeSettingsRequest)
  })
_sym_db.RegisterMessage(GetStrobeSettingsRequest)

EnvironmentalSensorData = _reflection.GeneratedProtocolMessageType('EnvironmentalSensorData', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTALSENSORDATA,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.EnvironmentalSensorData)
  })
_sym_db.RegisterMessage(EnvironmentalSensorData)

CoolantSensorData = _reflection.GeneratedProtocolMessageType('CoolantSensorData', (_message.Message,), {
  'DESCRIPTOR' : _COOLANTSENSORDATA,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.CoolantSensorData)
  })
_sym_db.RegisterMessage(CoolantSensorData)

NetworkPortState = _reflection.GeneratedProtocolMessageType('NetworkPortState', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKPORTSTATE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.NetworkPortState)
  })
_sym_db.RegisterMessage(NetworkPortState)

ReaperPcSensorData = _reflection.GeneratedProtocolMessageType('ReaperPcSensorData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERPCSENSORDATA,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.ReaperPcSensorData)
  })
_sym_db.RegisterMessage(ReaperPcSensorData)

ReaperScannerLaserStatus = _reflection.GeneratedProtocolMessageType('ReaperScannerLaserStatus', (_message.Message,), {
  'DESCRIPTOR' : _REAPERSCANNERLASERSTATUS,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.ReaperScannerLaserStatus)
  })
_sym_db.RegisterMessage(ReaperScannerLaserStatus)

ReaperScannerMotorData = _reflection.GeneratedProtocolMessageType('ReaperScannerMotorData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERSCANNERMOTORDATA,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.ReaperScannerMotorData)
  })
_sym_db.RegisterMessage(ReaperScannerMotorData)

ReaperScannerSensorData = _reflection.GeneratedProtocolMessageType('ReaperScannerSensorData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERSCANNERSENSORDATA,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.ReaperScannerSensorData)
  })
_sym_db.RegisterMessage(ReaperScannerSensorData)

ReaperCenterEnclosureData = _reflection.GeneratedProtocolMessageType('ReaperCenterEnclosureData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERCENTERENCLOSUREDATA,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.ReaperCenterEnclosureData)
  })
_sym_db.RegisterMessage(ReaperCenterEnclosureData)

ReaperModuleSensorData = _reflection.GeneratedProtocolMessageType('ReaperModuleSensorData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERMODULESENSORDATA,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.ReaperModuleSensorData)
  })
_sym_db.RegisterMessage(ReaperModuleSensorData)

GetReaperEnclosureSensorsRequest = _reflection.GeneratedProtocolMessageType('GetReaperEnclosureSensorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETREAPERENCLOSURESENSORSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetReaperEnclosureSensorsRequest)
  })
_sym_db.RegisterMessage(GetReaperEnclosureSensorsRequest)

GetReaperEnclosureSensorsResponse = _reflection.GeneratedProtocolMessageType('GetReaperEnclosureSensorsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETREAPERENCLOSURESENSORSRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetReaperEnclosureSensorsResponse)
  })
_sym_db.RegisterMessage(GetReaperEnclosureSensorsResponse)

GetReaperModuleSensorsRequest = _reflection.GeneratedProtocolMessageType('GetReaperModuleSensorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETREAPERMODULESENSORSREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetReaperModuleSensorsRequest)
  })
_sym_db.RegisterMessage(GetReaperModuleSensorsRequest)

GetReaperModuleSensorsResponse = _reflection.GeneratedProtocolMessageType('GetReaperModuleSensorsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETREAPERMODULESENSORSRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.GetReaperModuleSensorsResponse)
  })
_sym_db.RegisterMessage(GetReaperModuleSensorsResponse)

SetReaperScannerPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperScannerPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSCANNERPOWERREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperScannerPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperScannerPowerRequest)

SetReaperScannerPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperScannerPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSCANNERPOWERRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperScannerPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperScannerPowerResponse)

SetReaperTargetPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperTargetPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERTARGETPOWERREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperTargetPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperTargetPowerRequest)

SetReaperTargetPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperTargetPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERTARGETPOWERRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperTargetPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperTargetPowerResponse)

SetReaperPredictCamPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperPredictCamPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERPREDICTCAMPOWERREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperPredictCamPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperPredictCamPowerRequest)

SetReaperPredictCamPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperPredictCamPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERPREDICTCAMPOWERRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperPredictCamPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperPredictCamPowerResponse)

SetReaperStrobeConfigRequest = _reflection.GeneratedProtocolMessageType('SetReaperStrobeConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSTROBECONFIGREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperStrobeConfigRequest)
  })
_sym_db.RegisterMessage(SetReaperStrobeConfigRequest)

SetReaperStrobeConfigResponse = _reflection.GeneratedProtocolMessageType('SetReaperStrobeConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSTROBECONFIGRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperStrobeConfigResponse)
  })
_sym_db.RegisterMessage(SetReaperStrobeConfigResponse)

SetReaperStrobeEnableRequest = _reflection.GeneratedProtocolMessageType('SetReaperStrobeEnableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSTROBEENABLEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperStrobeEnableRequest)
  })
_sym_db.RegisterMessage(SetReaperStrobeEnableRequest)

SetReaperStrobeEnableResponse = _reflection.GeneratedProtocolMessageType('SetReaperStrobeEnableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSTROBEENABLERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperStrobeEnableResponse)
  })
_sym_db.RegisterMessage(SetReaperStrobeEnableResponse)

SetReaperModulePcPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperModulePcPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULEPCPOWERREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperModulePcPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperModulePcPowerRequest)

SetReaperModulePcPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperModulePcPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULEPCPOWERRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperModulePcPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperModulePcPowerResponse)

SetReaperModuleLaserPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperModuleLaserPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULELASERPOWERREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperModuleLaserPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperModuleLaserPowerRequest)

SetReaperModuleLaserPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperModuleLaserPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULELASERPOWERRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperModuleLaserPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperModuleLaserPowerResponse)

SetReaperModuleStrobePowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperModuleStrobePowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULESTROBEPOWERREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperModuleStrobePowerRequest)
  })
_sym_db.RegisterMessage(SetReaperModuleStrobePowerRequest)

SetReaperModuleStrobePowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperModuleStrobePowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULESTROBEPOWERRESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.SetReaperModuleStrobePowerResponse)
  })
_sym_db.RegisterMessage(SetReaperModuleStrobePowerResponse)

IdentifyModuleRequest = _reflection.GeneratedProtocolMessageType('IdentifyModuleRequest', (_message.Message,), {
  'DESCRIPTOR' : _IDENTIFYMODULEREQUEST,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.IdentifyModuleRequest)
  })
_sym_db.RegisterMessage(IdentifyModuleRequest)

IdentifyModuleResponse = _reflection.GeneratedProtocolMessageType('IdentifyModuleResponse', (_message.Message,), {
  'DESCRIPTOR' : _IDENTIFYMODULERESPONSE,
  '__module__' : 'hardware_manager.proto.hardware_manager_service_pb2'
  # @@protoc_insertion_point(class_scope:hardware_manager.IdentifyModuleResponse)
  })
_sym_db.RegisterMessage(IdentifyModuleResponse)


DESCRIPTOR._options = None
_GETSUPERVISORYSTATUSRESPONSE.fields_by_name['temp_humidity_bypass_status']._options = None

_HARDWAREMANAGERSERVICE = _descriptor.ServiceDescriptor(
  name='HardwareManagerService',
  full_name='hardware_manager.HardwareManagerService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=14057,
  serialized_end=20088,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='hardware_manager.HardwareManagerService.Ping',
    index=0,
    containing_service=None,
    input_type=_PINGREQUEST,
    output_type=_PINGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetReady',
    full_name='hardware_manager.HardwareManagerService.GetReady',
    index=1,
    containing_service=None,
    input_type=_GETREADYREQUEST,
    output_type=_GETREADYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextDistance',
    full_name='hardware_manager.HardwareManagerService.GetNextDistance',
    index=2,
    containing_service=None,
    input_type=_GETNEXTDISTANCEREQUEST,
    output_type=_GETNEXTDISTANCERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextVelocity',
    full_name='hardware_manager.HardwareManagerService.GetNextVelocity',
    index=3,
    containing_service=None,
    input_type=_GETNEXTVELOCITYREQUEST,
    output_type=_GETNEXTVELOCITYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRotaryTicks',
    full_name='hardware_manager.HardwareManagerService.GetRotaryTicks',
    index=4,
    containing_service=None,
    input_type=_GETROTARYTICKSREQUEST,
    output_type=_GETROTARYTICKSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDeltaTravelMM',
    full_name='hardware_manager.HardwareManagerService.GetDeltaTravelMM',
    index=5,
    containing_service=None,
    input_type=_GETDELTATRAVELMMREQUEST,
    output_type=_GETDELTATRAVELMMRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetWheelEncoderResolution',
    full_name='hardware_manager.HardwareManagerService.GetWheelEncoderResolution',
    index=6,
    containing_service=None,
    input_type=_GETWHEELENCODERRESOLUTIONREQUEST,
    output_type=_GETWHEELENCODERRESOLUTIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSafetyStatus',
    full_name='hardware_manager.HardwareManagerService.GetSafetyStatus',
    index=7,
    containing_service=None,
    input_type=_GETSAFETYSTATUSREQUEST,
    output_type=_GETSAFETYSTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetGPSData',
    full_name='hardware_manager.HardwareManagerService.GetGPSData',
    index=8,
    containing_service=None,
    input_type=_GETGPSDATAREQUEST,
    output_type=_GETGPSDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextGPSData',
    full_name='hardware_manager.HardwareManagerService.GetNextGPSData',
    index=9,
    containing_service=None,
    input_type=_GETNEXTGPSDATAREQUEST,
    output_type=_GETNEXTGPSDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextRawGPSData',
    full_name='hardware_manager.HardwareManagerService.GetNextRawGPSData',
    index=10,
    containing_service=None,
    input_type=_GETNEXTRAWGPSDATAREQUEST,
    output_type=_GETNEXTRAWGPSDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetGPSFixedPos',
    full_name='hardware_manager.HardwareManagerService.GetGPSFixedPos',
    index=11,
    containing_service=None,
    input_type=_GETGPSFIXEDPOSREQUEST,
    output_type=_GETGPSFIXEDPOSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetStrobeSettings',
    full_name='hardware_manager.HardwareManagerService.SetStrobeSettings',
    index=12,
    containing_service=None,
    input_type=_STROBESETTINGS,
    output_type=_SETSTROBESETTINGSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetStrobeSettings',
    full_name='hardware_manager.HardwareManagerService.GetStrobeSettings',
    index=13,
    containing_service=None,
    input_type=_GETSTROBESETTINGSREQUEST,
    output_type=_STROBESETTINGS,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetManagedBoardErrors',
    full_name='hardware_manager.HardwareManagerService.GetManagedBoardErrors',
    index=14,
    containing_service=None,
    input_type=_GETMANAGEDBOARDERRORSREQUEST,
    output_type=_GETMANAGEDBOARDERRORSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSupervisoryStatus',
    full_name='hardware_manager.HardwareManagerService.GetSupervisoryStatus',
    index=15,
    containing_service=None,
    input_type=_GETSUPERVISORYSTATUSREQUEST,
    output_type=_GETSUPERVISORYSTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetReaperSupervisoryStatus',
    full_name='hardware_manager.HardwareManagerService.GetReaperSupervisoryStatus',
    index=16,
    containing_service=None,
    input_type=_GETREAPERSUPERVISORYSTATUSREQUEST,
    output_type=_REAPERCENTERENCLOSUREDATA,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetServerDisable',
    full_name='hardware_manager.HardwareManagerService.SetServerDisable',
    index=17,
    containing_service=None,
    input_type=_SETSERVERDISABLEREQUEST,
    output_type=_SETSERVERDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetBTLDisable',
    full_name='hardware_manager.HardwareManagerService.SetBTLDisable',
    index=18,
    containing_service=None,
    input_type=_SETBTLDISABLEREQUEST,
    output_type=_SETBTLDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetScannersDisable',
    full_name='hardware_manager.HardwareManagerService.SetScannersDisable',
    index=19,
    containing_service=None,
    input_type=_SETSCANNERSDISABLEREQUEST,
    output_type=_SETSCANNERSDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetWheelEncoderBoardDisable',
    full_name='hardware_manager.HardwareManagerService.SetWheelEncoderBoardDisable',
    index=20,
    containing_service=None,
    input_type=_SETWHEELENCODERBOARDDISABLEREQUEST,
    output_type=_SETWHEELENCODERBOARDDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetWheelEncoderDisable',
    full_name='hardware_manager.HardwareManagerService.SetWheelEncoderDisable',
    index=21,
    containing_service=None,
    input_type=_SETWHEELENCODERDISABLEREQUEST,
    output_type=_SETWHEELENCODERDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetGPSDisable',
    full_name='hardware_manager.HardwareManagerService.SetGPSDisable',
    index=22,
    containing_service=None,
    input_type=_SETGPSDISABLEREQUEST,
    output_type=_SETGPSDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SuicideSwitch',
    full_name='hardware_manager.HardwareManagerService.SuicideSwitch',
    index=23,
    containing_service=None,
    input_type=_SUICIDESWITCHREQUEST,
    output_type=_SUICIDESWITCHRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CommandComputerPowerCycle',
    full_name='hardware_manager.HardwareManagerService.CommandComputerPowerCycle',
    index=24,
    containing_service=None,
    input_type=_COMMANDCOMPUTERPOWERCYCLEREQUEST,
    output_type=_COMMANDCOMPUTERPOWERCYCLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetMainContactorDisable',
    full_name='hardware_manager.HardwareManagerService.SetMainContactorDisable',
    index=25,
    containing_service=None,
    input_type=_SETMAINCONTACTORDISABLEREQUEST,
    output_type=_SETMAINCONTACTORDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetStrobeDisable',
    full_name='hardware_manager.HardwareManagerService.SetStrobeDisable',
    index=26,
    containing_service=None,
    input_type=_SETSTROBEDISABLEREQUEST,
    output_type=_SETSTROBEDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetAirConditionerDisable',
    full_name='hardware_manager.HardwareManagerService.SetAirConditionerDisable',
    index=27,
    containing_service=None,
    input_type=_SETAIRCONDITIONERDISABLEREQUEST,
    output_type=_SETAIRCONDITIONERDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetChillerDisable',
    full_name='hardware_manager.HardwareManagerService.SetChillerDisable',
    index=28,
    containing_service=None,
    input_type=_SETCHILLERDISABLEREQUEST,
    output_type=_SETCHILLERDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetTempBypassDisable',
    full_name='hardware_manager.HardwareManagerService.SetTempBypassDisable',
    index=29,
    containing_service=None,
    input_type=_SETTEMPBYPASSDISABLEREQUEST,
    output_type=_SETTEMPBYPASSDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetHumidityBypassDisable',
    full_name='hardware_manager.HardwareManagerService.SetHumidityBypassDisable',
    index=30,
    containing_service=None,
    input_type=_SETHUMIDITYBYPASSDISABLEREQUEST,
    output_type=_SETHUMIDITYBYPASSDISABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Get240vUptime',
    full_name='hardware_manager.HardwareManagerService.Get240vUptime',
    index=31,
    containing_service=None,
    input_type=_GET240VUPTIMEREQUEST,
    output_type=_GET240VUPTIMERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetRuntime',
    full_name='hardware_manager.HardwareManagerService.GetRuntime',
    index=32,
    containing_service=None,
    input_type=_GETRUNTIMEREQUEST,
    output_type=_GETRUNTIMERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetAvailableUSBStorage',
    full_name='hardware_manager.HardwareManagerService.GetAvailableUSBStorage',
    index=33,
    containing_service=None,
    input_type=_GETAVAILABLEUSBSTORAGEREQUEST,
    output_type=_GETAVAILABLEUSBSTORAGERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetJimboxSpeed',
    full_name='hardware_manager.HardwareManagerService.SetJimboxSpeed',
    index=34,
    containing_service=None,
    input_type=_SETJIMBOXSPEEDREQUEST,
    output_type=_SETJIMBOXSPEEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetCruiseEnabled',
    full_name='hardware_manager.HardwareManagerService.SetCruiseEnabled',
    index=35,
    containing_service=None,
    input_type=_SETCRUISEENABLEDREQUEST,
    output_type=_SETCRUISEENABLEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCruiseStatus',
    full_name='hardware_manager.HardwareManagerService.GetCruiseStatus',
    index=36,
    containing_service=None,
    input_type=_GETCRUISESTATUSREQUEST,
    output_type=_GETCRUISESTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetImplementStateOnTractor',
    full_name='hardware_manager.HardwareManagerService.SetImplementStateOnTractor',
    index=37,
    containing_service=None,
    input_type=_SETIMPLEMENTSTATEREQUEST,
    output_type=_SETIMPLEMENTSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetSafeStateEnforcement',
    full_name='hardware_manager.HardwareManagerService.SetSafeStateEnforcement',
    index=38,
    containing_service=None,
    input_type=_SETSAFESTATEENFORCEMENTREQUEST,
    output_type=_SETSAFESTATEENFORCEMENTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTractorSafetyState',
    full_name='hardware_manager.HardwareManagerService.GetTractorSafetyState',
    index=39,
    containing_service=None,
    input_type=_GETTRACTORSAFETYSTATEREQUEST,
    output_type=_GETTRACTORSAFETYSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTractorIFState',
    full_name='hardware_manager.HardwareManagerService.GetTractorIFState',
    index=40,
    containing_service=None,
    input_type=_GETTRACTORIFSTATEREQUEST,
    output_type=_GETTRACTORIFSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetReaperEnclosureSensors',
    full_name='hardware_manager.HardwareManagerService.GetReaperEnclosureSensors',
    index=41,
    containing_service=None,
    input_type=_GETREAPERENCLOSURESENSORSREQUEST,
    output_type=_GETREAPERENCLOSURESENSORSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetReaperModuleSensors',
    full_name='hardware_manager.HardwareManagerService.GetReaperModuleSensors',
    index=42,
    containing_service=None,
    input_type=_GETREAPERMODULESENSORSREQUEST,
    output_type=_GETREAPERMODULESENSORSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperScannerPower',
    full_name='hardware_manager.HardwareManagerService.SetReaperScannerPower',
    index=43,
    containing_service=None,
    input_type=_SETREAPERSCANNERPOWERREQUEST,
    output_type=_SETREAPERSCANNERPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperTargetPower',
    full_name='hardware_manager.HardwareManagerService.SetReaperTargetPower',
    index=44,
    containing_service=None,
    input_type=_SETREAPERTARGETPOWERREQUEST,
    output_type=_SETREAPERTARGETPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperPredictCamPower',
    full_name='hardware_manager.HardwareManagerService.SetReaperPredictCamPower',
    index=45,
    containing_service=None,
    input_type=_SETREAPERPREDICTCAMPOWERREQUEST,
    output_type=_SETREAPERPREDICTCAMPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperStrobeConfig',
    full_name='hardware_manager.HardwareManagerService.SetReaperStrobeConfig',
    index=46,
    containing_service=None,
    input_type=_SETREAPERSTROBECONFIGREQUEST,
    output_type=_SETREAPERSTROBECONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperStrobeEnable',
    full_name='hardware_manager.HardwareManagerService.SetReaperStrobeEnable',
    index=47,
    containing_service=None,
    input_type=_SETREAPERSTROBEENABLEREQUEST,
    output_type=_SETREAPERSTROBEENABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperModulePcPower',
    full_name='hardware_manager.HardwareManagerService.SetReaperModulePcPower',
    index=48,
    containing_service=None,
    input_type=_SETREAPERMODULEPCPOWERREQUEST,
    output_type=_SETREAPERMODULEPCPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperModuleLaserPower',
    full_name='hardware_manager.HardwareManagerService.SetReaperModuleLaserPower',
    index=49,
    containing_service=None,
    input_type=_SETREAPERMODULELASERPOWERREQUEST,
    output_type=_SETREAPERMODULELASERPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperModuleStrobePower',
    full_name='hardware_manager.HardwareManagerService.SetReaperModuleStrobePower',
    index=50,
    containing_service=None,
    input_type=_SETREAPERMODULESTROBEPOWERREQUEST,
    output_type=_SETREAPERMODULESTROBEPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='IdentifyModule',
    full_name='hardware_manager.HardwareManagerService.IdentifyModule',
    index=51,
    containing_service=None,
    input_type=_IDENTIFYMODULEREQUEST,
    output_type=_IDENTIFYMODULERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_HARDWAREMANAGERSERVICE)

DESCRIPTOR.services_by_name['HardwareManagerService'] = _HARDWAREMANAGERSERVICE

# @@protoc_insertion_point(module_scope)

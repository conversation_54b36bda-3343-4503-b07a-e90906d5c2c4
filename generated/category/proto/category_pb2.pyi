"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class CategoryCollection(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    customer_id: typing___Text = ...
    name: typing___Text = ...
    category_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    protected: builtin___bool = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        customer_id : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        category_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        protected : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"category_ids",b"category_ids",u"customer_id",b"customer_id",u"id",b"id",u"name",b"name",u"protected",b"protected"]) -> None: ...
type___CategoryCollection = CategoryCollection

class Category(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    customer_id: typing___Text = ...
    name: typing___Text = ...
    chip_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    protected: builtin___bool = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        customer_id : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        chip_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        protected : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"chip_ids",b"chip_ids",u"customer_id",b"customer_id",u"id",b"id",u"name",b"name",u"protected",b"protected"]) -> None: ...
type___Category = Category

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: category/proto/category.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_category_2fproto_2fcategory_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_category_2fproto_2fcategory_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_category_2fproto_2fcategory_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_category_2fproto_2fcategory_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_category_2fproto_2fcategory_2eproto;
namespace carbon {
namespace category {
class Category;
struct CategoryDefaultTypeInternal;
extern CategoryDefaultTypeInternal _Category_default_instance_;
class CategoryCollection;
struct CategoryCollectionDefaultTypeInternal;
extern CategoryCollectionDefaultTypeInternal _CategoryCollection_default_instance_;
}  // namespace category
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::category::Category* Arena::CreateMaybeMessage<::carbon::category::Category>(Arena*);
template<> ::carbon::category::CategoryCollection* Arena::CreateMaybeMessage<::carbon::category::CategoryCollection>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace category {

// ===================================================================

class CategoryCollection final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.category.CategoryCollection) */ {
 public:
  inline CategoryCollection() : CategoryCollection(nullptr) {}
  ~CategoryCollection() override;
  explicit constexpr CategoryCollection(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CategoryCollection(const CategoryCollection& from);
  CategoryCollection(CategoryCollection&& from) noexcept
    : CategoryCollection() {
    *this = ::std::move(from);
  }

  inline CategoryCollection& operator=(const CategoryCollection& from) {
    CopyFrom(from);
    return *this;
  }
  inline CategoryCollection& operator=(CategoryCollection&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CategoryCollection& default_instance() {
    return *internal_default_instance();
  }
  static inline const CategoryCollection* internal_default_instance() {
    return reinterpret_cast<const CategoryCollection*>(
               &_CategoryCollection_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CategoryCollection& a, CategoryCollection& b) {
    a.Swap(&b);
  }
  inline void Swap(CategoryCollection* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CategoryCollection* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CategoryCollection* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CategoryCollection>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CategoryCollection& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CategoryCollection& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CategoryCollection* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.category.CategoryCollection";
  }
  protected:
  explicit CategoryCollection(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCategoryIdsFieldNumber = 4,
    kIdFieldNumber = 1,
    kCustomerIdFieldNumber = 2,
    kNameFieldNumber = 3,
    kProtectedFieldNumber = 5,
  };
  // repeated string category_ids = 4;
  int category_ids_size() const;
  private:
  int _internal_category_ids_size() const;
  public:
  void clear_category_ids();
  const std::string& category_ids(int index) const;
  std::string* mutable_category_ids(int index);
  void set_category_ids(int index, const std::string& value);
  void set_category_ids(int index, std::string&& value);
  void set_category_ids(int index, const char* value);
  void set_category_ids(int index, const char* value, size_t size);
  std::string* add_category_ids();
  void add_category_ids(const std::string& value);
  void add_category_ids(std::string&& value);
  void add_category_ids(const char* value);
  void add_category_ids(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& category_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_category_ids();
  private:
  const std::string& _internal_category_ids(int index) const;
  std::string* _internal_add_category_ids();
  public:

  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string customer_id = 2;
  void clear_customer_id();
  const std::string& customer_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_customer_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_customer_id();
  PROTOBUF_NODISCARD std::string* release_customer_id();
  void set_allocated_customer_id(std::string* customer_id);
  private:
  const std::string& _internal_customer_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_customer_id(const std::string& value);
  std::string* _internal_mutable_customer_id();
  public:

  // string name = 3;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bool protected = 5;
  void clear_protected_();
  bool protected_() const;
  void set_protected_(bool value);
  private:
  bool _internal_protected_() const;
  void _internal_set_protected_(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.category.CategoryCollection)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> category_ids_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr customer_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  bool protected__;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_category_2fproto_2fcategory_2eproto;
};
// -------------------------------------------------------------------

class Category final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.category.Category) */ {
 public:
  inline Category() : Category(nullptr) {}
  ~Category() override;
  explicit constexpr Category(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Category(const Category& from);
  Category(Category&& from) noexcept
    : Category() {
    *this = ::std::move(from);
  }

  inline Category& operator=(const Category& from) {
    CopyFrom(from);
    return *this;
  }
  inline Category& operator=(Category&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Category& default_instance() {
    return *internal_default_instance();
  }
  static inline const Category* internal_default_instance() {
    return reinterpret_cast<const Category*>(
               &_Category_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Category& a, Category& b) {
    a.Swap(&b);
  }
  inline void Swap(Category* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Category* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Category* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Category>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Category& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Category& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Category* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.category.Category";
  }
  protected:
  explicit Category(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChipIdsFieldNumber = 4,
    kIdFieldNumber = 1,
    kCustomerIdFieldNumber = 2,
    kNameFieldNumber = 3,
    kProtectedFieldNumber = 5,
  };
  // repeated string chip_ids = 4;
  int chip_ids_size() const;
  private:
  int _internal_chip_ids_size() const;
  public:
  void clear_chip_ids();
  const std::string& chip_ids(int index) const;
  std::string* mutable_chip_ids(int index);
  void set_chip_ids(int index, const std::string& value);
  void set_chip_ids(int index, std::string&& value);
  void set_chip_ids(int index, const char* value);
  void set_chip_ids(int index, const char* value, size_t size);
  std::string* add_chip_ids();
  void add_chip_ids(const std::string& value);
  void add_chip_ids(std::string&& value);
  void add_chip_ids(const char* value);
  void add_chip_ids(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& chip_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_chip_ids();
  private:
  const std::string& _internal_chip_ids(int index) const;
  std::string* _internal_add_chip_ids();
  public:

  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string customer_id = 2;
  void clear_customer_id();
  const std::string& customer_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_customer_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_customer_id();
  PROTOBUF_NODISCARD std::string* release_customer_id();
  void set_allocated_customer_id(std::string* customer_id);
  private:
  const std::string& _internal_customer_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_customer_id(const std::string& value);
  std::string* _internal_mutable_customer_id();
  public:

  // string name = 3;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bool protected = 5;
  void clear_protected_();
  bool protected_() const;
  void set_protected_(bool value);
  private:
  bool _internal_protected_() const;
  void _internal_set_protected_(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.category.Category)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> chip_ids_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr customer_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  bool protected__;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_category_2fproto_2fcategory_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CategoryCollection

// string id = 1;
inline void CategoryCollection::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& CategoryCollection::id() const {
  // @@protoc_insertion_point(field_get:carbon.category.CategoryCollection.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CategoryCollection::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.category.CategoryCollection.id)
}
inline std::string* CategoryCollection::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.category.CategoryCollection.id)
  return _s;
}
inline const std::string& CategoryCollection::_internal_id() const {
  return id_.Get();
}
inline void CategoryCollection::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CategoryCollection::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CategoryCollection::release_id() {
  // @@protoc_insertion_point(field_release:carbon.category.CategoryCollection.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CategoryCollection::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.category.CategoryCollection.id)
}

// string customer_id = 2;
inline void CategoryCollection::clear_customer_id() {
  customer_id_.ClearToEmpty();
}
inline const std::string& CategoryCollection::customer_id() const {
  // @@protoc_insertion_point(field_get:carbon.category.CategoryCollection.customer_id)
  return _internal_customer_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CategoryCollection::set_customer_id(ArgT0&& arg0, ArgT... args) {
 
 customer_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.category.CategoryCollection.customer_id)
}
inline std::string* CategoryCollection::mutable_customer_id() {
  std::string* _s = _internal_mutable_customer_id();
  // @@protoc_insertion_point(field_mutable:carbon.category.CategoryCollection.customer_id)
  return _s;
}
inline const std::string& CategoryCollection::_internal_customer_id() const {
  return customer_id_.Get();
}
inline void CategoryCollection::_internal_set_customer_id(const std::string& value) {
  
  customer_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CategoryCollection::_internal_mutable_customer_id() {
  
  return customer_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CategoryCollection::release_customer_id() {
  // @@protoc_insertion_point(field_release:carbon.category.CategoryCollection.customer_id)
  return customer_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CategoryCollection::set_allocated_customer_id(std::string* customer_id) {
  if (customer_id != nullptr) {
    
  } else {
    
  }
  customer_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), customer_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (customer_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    customer_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.category.CategoryCollection.customer_id)
}

// string name = 3;
inline void CategoryCollection::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& CategoryCollection::name() const {
  // @@protoc_insertion_point(field_get:carbon.category.CategoryCollection.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CategoryCollection::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.category.CategoryCollection.name)
}
inline std::string* CategoryCollection::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.category.CategoryCollection.name)
  return _s;
}
inline const std::string& CategoryCollection::_internal_name() const {
  return name_.Get();
}
inline void CategoryCollection::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CategoryCollection::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CategoryCollection::release_name() {
  // @@protoc_insertion_point(field_release:carbon.category.CategoryCollection.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CategoryCollection::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.category.CategoryCollection.name)
}

// repeated string category_ids = 4;
inline int CategoryCollection::_internal_category_ids_size() const {
  return category_ids_.size();
}
inline int CategoryCollection::category_ids_size() const {
  return _internal_category_ids_size();
}
inline void CategoryCollection::clear_category_ids() {
  category_ids_.Clear();
}
inline std::string* CategoryCollection::add_category_ids() {
  std::string* _s = _internal_add_category_ids();
  // @@protoc_insertion_point(field_add_mutable:carbon.category.CategoryCollection.category_ids)
  return _s;
}
inline const std::string& CategoryCollection::_internal_category_ids(int index) const {
  return category_ids_.Get(index);
}
inline const std::string& CategoryCollection::category_ids(int index) const {
  // @@protoc_insertion_point(field_get:carbon.category.CategoryCollection.category_ids)
  return _internal_category_ids(index);
}
inline std::string* CategoryCollection::mutable_category_ids(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.category.CategoryCollection.category_ids)
  return category_ids_.Mutable(index);
}
inline void CategoryCollection::set_category_ids(int index, const std::string& value) {
  category_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.category.CategoryCollection.category_ids)
}
inline void CategoryCollection::set_category_ids(int index, std::string&& value) {
  category_ids_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.category.CategoryCollection.category_ids)
}
inline void CategoryCollection::set_category_ids(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  category_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.category.CategoryCollection.category_ids)
}
inline void CategoryCollection::set_category_ids(int index, const char* value, size_t size) {
  category_ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.category.CategoryCollection.category_ids)
}
inline std::string* CategoryCollection::_internal_add_category_ids() {
  return category_ids_.Add();
}
inline void CategoryCollection::add_category_ids(const std::string& value) {
  category_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.category.CategoryCollection.category_ids)
}
inline void CategoryCollection::add_category_ids(std::string&& value) {
  category_ids_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.category.CategoryCollection.category_ids)
}
inline void CategoryCollection::add_category_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  category_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.category.CategoryCollection.category_ids)
}
inline void CategoryCollection::add_category_ids(const char* value, size_t size) {
  category_ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.category.CategoryCollection.category_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CategoryCollection::category_ids() const {
  // @@protoc_insertion_point(field_list:carbon.category.CategoryCollection.category_ids)
  return category_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CategoryCollection::mutable_category_ids() {
  // @@protoc_insertion_point(field_mutable_list:carbon.category.CategoryCollection.category_ids)
  return &category_ids_;
}

// bool protected = 5;
inline void CategoryCollection::clear_protected_() {
  protected__ = false;
}
inline bool CategoryCollection::_internal_protected_() const {
  return protected__;
}
inline bool CategoryCollection::protected_() const {
  // @@protoc_insertion_point(field_get:carbon.category.CategoryCollection.protected)
  return _internal_protected_();
}
inline void CategoryCollection::_internal_set_protected_(bool value) {
  
  protected__ = value;
}
inline void CategoryCollection::set_protected_(bool value) {
  _internal_set_protected_(value);
  // @@protoc_insertion_point(field_set:carbon.category.CategoryCollection.protected)
}

// -------------------------------------------------------------------

// Category

// string id = 1;
inline void Category::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& Category::id() const {
  // @@protoc_insertion_point(field_get:carbon.category.Category.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Category::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.category.Category.id)
}
inline std::string* Category::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.category.Category.id)
  return _s;
}
inline const std::string& Category::_internal_id() const {
  return id_.Get();
}
inline void Category::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Category::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Category::release_id() {
  // @@protoc_insertion_point(field_release:carbon.category.Category.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Category::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.category.Category.id)
}

// string customer_id = 2;
inline void Category::clear_customer_id() {
  customer_id_.ClearToEmpty();
}
inline const std::string& Category::customer_id() const {
  // @@protoc_insertion_point(field_get:carbon.category.Category.customer_id)
  return _internal_customer_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Category::set_customer_id(ArgT0&& arg0, ArgT... args) {
 
 customer_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.category.Category.customer_id)
}
inline std::string* Category::mutable_customer_id() {
  std::string* _s = _internal_mutable_customer_id();
  // @@protoc_insertion_point(field_mutable:carbon.category.Category.customer_id)
  return _s;
}
inline const std::string& Category::_internal_customer_id() const {
  return customer_id_.Get();
}
inline void Category::_internal_set_customer_id(const std::string& value) {
  
  customer_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Category::_internal_mutable_customer_id() {
  
  return customer_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Category::release_customer_id() {
  // @@protoc_insertion_point(field_release:carbon.category.Category.customer_id)
  return customer_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Category::set_allocated_customer_id(std::string* customer_id) {
  if (customer_id != nullptr) {
    
  } else {
    
  }
  customer_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), customer_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (customer_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    customer_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.category.Category.customer_id)
}

// string name = 3;
inline void Category::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& Category::name() const {
  // @@protoc_insertion_point(field_get:carbon.category.Category.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Category::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.category.Category.name)
}
inline std::string* Category::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.category.Category.name)
  return _s;
}
inline const std::string& Category::_internal_name() const {
  return name_.Get();
}
inline void Category::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Category::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Category::release_name() {
  // @@protoc_insertion_point(field_release:carbon.category.Category.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Category::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.category.Category.name)
}

// repeated string chip_ids = 4;
inline int Category::_internal_chip_ids_size() const {
  return chip_ids_.size();
}
inline int Category::chip_ids_size() const {
  return _internal_chip_ids_size();
}
inline void Category::clear_chip_ids() {
  chip_ids_.Clear();
}
inline std::string* Category::add_chip_ids() {
  std::string* _s = _internal_add_chip_ids();
  // @@protoc_insertion_point(field_add_mutable:carbon.category.Category.chip_ids)
  return _s;
}
inline const std::string& Category::_internal_chip_ids(int index) const {
  return chip_ids_.Get(index);
}
inline const std::string& Category::chip_ids(int index) const {
  // @@protoc_insertion_point(field_get:carbon.category.Category.chip_ids)
  return _internal_chip_ids(index);
}
inline std::string* Category::mutable_chip_ids(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.category.Category.chip_ids)
  return chip_ids_.Mutable(index);
}
inline void Category::set_chip_ids(int index, const std::string& value) {
  chip_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.category.Category.chip_ids)
}
inline void Category::set_chip_ids(int index, std::string&& value) {
  chip_ids_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.category.Category.chip_ids)
}
inline void Category::set_chip_ids(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  chip_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.category.Category.chip_ids)
}
inline void Category::set_chip_ids(int index, const char* value, size_t size) {
  chip_ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.category.Category.chip_ids)
}
inline std::string* Category::_internal_add_chip_ids() {
  return chip_ids_.Add();
}
inline void Category::add_chip_ids(const std::string& value) {
  chip_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.category.Category.chip_ids)
}
inline void Category::add_chip_ids(std::string&& value) {
  chip_ids_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.category.Category.chip_ids)
}
inline void Category::add_chip_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  chip_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.category.Category.chip_ids)
}
inline void Category::add_chip_ids(const char* value, size_t size) {
  chip_ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.category.Category.chip_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Category::chip_ids() const {
  // @@protoc_insertion_point(field_list:carbon.category.Category.chip_ids)
  return chip_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Category::mutable_chip_ids() {
  // @@protoc_insertion_point(field_mutable_list:carbon.category.Category.chip_ids)
  return &chip_ids_;
}

// bool protected = 5;
inline void Category::clear_protected_() {
  protected__ = false;
}
inline bool Category::_internal_protected_() const {
  return protected__;
}
inline bool Category::protected_() const {
  // @@protoc_insertion_point(field_get:carbon.category.Category.protected)
  return _internal_protected_();
}
inline void Category::_internal_set_protected_(bool value) {
  
  protected__ = value;
}
inline void Category::set_protected_(bool value) {
  _internal_set_protected_(value);
  // @@protoc_insertion_point(field_set:carbon.category.Category.protected)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace category
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_category_2fproto_2fcategory_2eproto

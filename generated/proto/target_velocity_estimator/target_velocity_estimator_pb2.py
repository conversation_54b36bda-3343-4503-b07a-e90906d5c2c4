# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/target_velocity_estimator/target_velocity_estimator.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/target_velocity_estimator/target_velocity_estimator.proto',
  package='carbon.aimbot.target_velocity_estimator',
  syntax='proto3',
  serialized_options=b'Z\037proto/target_velocity_estimator',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n?proto/target_velocity_estimator/target_velocity_estimator.proto\x12\'carbon.aimbot.target_velocity_estimator\"G\n\rTVERowProfile\x12\x19\n\x11primary_kill_rate\x18\x01 \x01(\x02\x12\x1b\n\x13secondary_kill_rate\x18\x02 \x01(\x02\"\x84\x05\n\nTVEProfile\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tupdate_ts\x18\x03 \x01(\x03\x12\x11\n\tprotected\x18\x04 \x01(\x08\x12\x1d\n\x15\x63ruise_offset_percent\x18\x05 \x01(\x02\x12\x15\n\rprimary_range\x18\x06 \x01(\x02\x12\x17\n\x0fsecondary_range\x18\x07 \x01(\x02\x12\x1a\n\x12increase_smoothing\x18\x08 \x01(\x02\x12\x1a\n\x12\x64\x65\x63rease_smoothing\x18\t \x01(\x02\x12\x45\n\x05row_1\x18\n \x01(\x0b\x32\x36.carbon.aimbot.target_velocity_estimator.TVERowProfile\x12\x45\n\x05row_2\x18\x0b \x01(\x0b\x32\x36.carbon.aimbot.target_velocity_estimator.TVERowProfile\x12\x45\n\x05row_3\x18\x0c \x01(\x0b\x32\x36.carbon.aimbot.target_velocity_estimator.TVERowProfile\x12K\n\x04rows\x18\r \x03(\x0b\x32=.carbon.aimbot.target_velocity_estimator.TVEProfile.RowsEntry\x12\x13\n\x0bmin_vel_mph\x18\x0e \x01(\x02\x12\x13\n\x0bmax_vel_mph\x18\x0f \x01(\x02\x1a\x63\n\tRowsEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x45\n\x05value\x18\x02 \x01(\x0b\x32\x36.carbon.aimbot.target_velocity_estimator.TVERowProfile:\x02\x38\x01\"*\n\x0eProfileDetails\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\tB!Z\x1fproto/target_velocity_estimatorb\x06proto3'
)




_TVEROWPROFILE = _descriptor.Descriptor(
  name='TVERowProfile',
  full_name='carbon.aimbot.target_velocity_estimator.TVERowProfile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='primary_kill_rate', full_name='carbon.aimbot.target_velocity_estimator.TVERowProfile.primary_kill_rate', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='secondary_kill_rate', full_name='carbon.aimbot.target_velocity_estimator.TVERowProfile.secondary_kill_rate', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=108,
  serialized_end=179,
)


_TVEPROFILE_ROWSENTRY = _descriptor.Descriptor(
  name='RowsEntry',
  full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.RowsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.RowsEntry.key', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.RowsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=727,
  serialized_end=826,
)

_TVEPROFILE = _descriptor.Descriptor(
  name='TVEProfile',
  full_name='carbon.aimbot.target_velocity_estimator.TVEProfile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='update_ts', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.update_ts', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='protected', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.protected', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cruise_offset_percent', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.cruise_offset_percent', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='primary_range', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.primary_range', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='secondary_range', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.secondary_range', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='increase_smoothing', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.increase_smoothing', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='decrease_smoothing', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.decrease_smoothing', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_1', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.row_1', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_2', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.row_2', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_3', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.row_3', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rows', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.rows', index=12,
      number=13, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_vel_mph', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.min_vel_mph', index=13,
      number=14, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_vel_mph', full_name='carbon.aimbot.target_velocity_estimator.TVEProfile.max_vel_mph', index=14,
      number=15, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_TVEPROFILE_ROWSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=182,
  serialized_end=826,
)


_PROFILEDETAILS = _descriptor.Descriptor(
  name='ProfileDetails',
  full_name='carbon.aimbot.target_velocity_estimator.ProfileDetails',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.aimbot.target_velocity_estimator.ProfileDetails.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.aimbot.target_velocity_estimator.ProfileDetails.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=828,
  serialized_end=870,
)

_TVEPROFILE_ROWSENTRY.fields_by_name['value'].message_type = _TVEROWPROFILE
_TVEPROFILE_ROWSENTRY.containing_type = _TVEPROFILE
_TVEPROFILE.fields_by_name['row_1'].message_type = _TVEROWPROFILE
_TVEPROFILE.fields_by_name['row_2'].message_type = _TVEROWPROFILE
_TVEPROFILE.fields_by_name['row_3'].message_type = _TVEROWPROFILE
_TVEPROFILE.fields_by_name['rows'].message_type = _TVEPROFILE_ROWSENTRY
DESCRIPTOR.message_types_by_name['TVERowProfile'] = _TVEROWPROFILE
DESCRIPTOR.message_types_by_name['TVEProfile'] = _TVEPROFILE
DESCRIPTOR.message_types_by_name['ProfileDetails'] = _PROFILEDETAILS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TVERowProfile = _reflection.GeneratedProtocolMessageType('TVERowProfile', (_message.Message,), {
  'DESCRIPTOR' : _TVEROWPROFILE,
  '__module__' : 'proto.target_velocity_estimator.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.target_velocity_estimator.TVERowProfile)
  })
_sym_db.RegisterMessage(TVERowProfile)

TVEProfile = _reflection.GeneratedProtocolMessageType('TVEProfile', (_message.Message,), {

  'RowsEntry' : _reflection.GeneratedProtocolMessageType('RowsEntry', (_message.Message,), {
    'DESCRIPTOR' : _TVEPROFILE_ROWSENTRY,
    '__module__' : 'proto.target_velocity_estimator.target_velocity_estimator_pb2'
    # @@protoc_insertion_point(class_scope:carbon.aimbot.target_velocity_estimator.TVEProfile.RowsEntry)
    })
  ,
  'DESCRIPTOR' : _TVEPROFILE,
  '__module__' : 'proto.target_velocity_estimator.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.target_velocity_estimator.TVEProfile)
  })
_sym_db.RegisterMessage(TVEProfile)
_sym_db.RegisterMessage(TVEProfile.RowsEntry)

ProfileDetails = _reflection.GeneratedProtocolMessageType('ProfileDetails', (_message.Message,), {
  'DESCRIPTOR' : _PROFILEDETAILS,
  '__module__' : 'proto.target_velocity_estimator.target_velocity_estimator_pb2'
  # @@protoc_insertion_point(class_scope:carbon.aimbot.target_velocity_estimator.ProfileDetails)
  })
_sym_db.RegisterMessage(ProfileDetails)


DESCRIPTOR._options = None
_TVEPROFILE_ROWSENTRY._options = None
# @@protoc_insertion_point(module_scope)

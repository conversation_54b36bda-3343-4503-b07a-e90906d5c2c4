// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/target_velocity_estimator/target_velocity_estimator.proto

#include "proto/target_velocity_estimator/target_velocity_estimator.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace aimbot {
namespace target_velocity_estimator {
constexpr TVERowProfile::TVERowProfile(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : primary_kill_rate_(0)
  , secondary_kill_rate_(0){}
struct TVERowProfileDefaultTypeInternal {
  constexpr TVERowProfileDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TVERowProfileDefaultTypeInternal() {}
  union {
    TVERowProfile _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TVERowProfileDefaultTypeInternal _TVERowProfile_default_instance_;
constexpr TVEProfile_RowsEntry_DoNotUse::TVEProfile_RowsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TVEProfile_RowsEntry_DoNotUseDefaultTypeInternal {
  constexpr TVEProfile_RowsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TVEProfile_RowsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TVEProfile_RowsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TVEProfile_RowsEntry_DoNotUseDefaultTypeInternal _TVEProfile_RowsEntry_DoNotUse_default_instance_;
constexpr TVEProfile::TVEProfile(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : rows_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , row_1_(nullptr)
  , row_2_(nullptr)
  , row_3_(nullptr)
  , update_ts_(int64_t{0})
  , protected__(false)
  , cruise_offset_percent_(0)
  , primary_range_(0)
  , secondary_range_(0)
  , increase_smoothing_(0)
  , decrease_smoothing_(0)
  , min_vel_mph_(0)
  , max_vel_mph_(0){}
struct TVEProfileDefaultTypeInternal {
  constexpr TVEProfileDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TVEProfileDefaultTypeInternal() {}
  union {
    TVEProfile _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TVEProfileDefaultTypeInternal _TVEProfile_default_instance_;
constexpr ProfileDetails::ProfileDetails(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ProfileDetailsDefaultTypeInternal {
  constexpr ProfileDetailsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ProfileDetailsDefaultTypeInternal() {}
  union {
    ProfileDetails _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ProfileDetailsDefaultTypeInternal _ProfileDetails_default_instance_;
}  // namespace target_velocity_estimator
}  // namespace aimbot
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto[4];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto = nullptr;

const uint32_t TableStruct_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVERowProfile, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVERowProfile, primary_kill_rate_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVERowProfile, secondary_kill_rate_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, update_ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, protected__),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, cruise_offset_percent_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, primary_range_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, secondary_range_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, increase_smoothing_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, decrease_smoothing_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, row_1_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, row_2_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, row_3_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, rows_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, min_vel_mph_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::TVEProfile, max_vel_mph_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::ProfileDetails, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::ProfileDetails, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::aimbot::target_velocity_estimator::ProfileDetails, name_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::aimbot::target_velocity_estimator::TVERowProfile)},
  { 8, 16, -1, sizeof(::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse)},
  { 18, -1, -1, sizeof(::carbon::aimbot::target_velocity_estimator::TVEProfile)},
  { 39, -1, -1, sizeof(::carbon::aimbot::target_velocity_estimator::ProfileDetails)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::target_velocity_estimator::_TVERowProfile_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::target_velocity_estimator::_TVEProfile_RowsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::target_velocity_estimator::_TVEProfile_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::aimbot::target_velocity_estimator::_ProfileDetails_default_instance_),
};

const char descriptor_table_protodef_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\?proto/target_velocity_estimator/target"
  "_velocity_estimator.proto\022\'carbon.aimbot"
  ".target_velocity_estimator\"G\n\rTVERowProf"
  "ile\022\031\n\021primary_kill_rate\030\001 \001(\002\022\033\n\023second"
  "ary_kill_rate\030\002 \001(\002\"\204\005\n\nTVEProfile\022\n\n\002id"
  "\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\021\n\tupdate_ts\030\003 \001(\003\022"
  "\021\n\tprotected\030\004 \001(\010\022\035\n\025cruise_offset_perc"
  "ent\030\005 \001(\002\022\025\n\rprimary_range\030\006 \001(\002\022\027\n\017seco"
  "ndary_range\030\007 \001(\002\022\032\n\022increase_smoothing\030"
  "\010 \001(\002\022\032\n\022decrease_smoothing\030\t \001(\002\022E\n\005row"
  "_1\030\n \001(\01326.carbon.aimbot.target_velocity"
  "_estimator.TVERowProfile\022E\n\005row_2\030\013 \001(\0132"
  "6.carbon.aimbot.target_velocity_estimato"
  "r.TVERowProfile\022E\n\005row_3\030\014 \001(\01326.carbon."
  "aimbot.target_velocity_estimator.TVERowP"
  "rofile\022K\n\004rows\030\r \003(\0132=.carbon.aimbot.tar"
  "get_velocity_estimator.TVEProfile.RowsEn"
  "try\022\023\n\013min_vel_mph\030\016 \001(\002\022\023\n\013max_vel_mph\030"
  "\017 \001(\002\032c\n\tRowsEntry\022\013\n\003key\030\001 \001(\r\022E\n\005value"
  "\030\002 \001(\01326.carbon.aimbot.target_velocity_e"
  "stimator.TVERowProfile:\0028\001\"*\n\016ProfileDet"
  "ails\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\tB!Z\037proto/"
  "target_velocity_estimatorb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto = {
  false, false, 913, descriptor_table_protodef_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto, "proto/target_velocity_estimator/target_velocity_estimator.proto", 
  &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_once, nullptr, 0, 4,
  schemas, file_default_instances, TableStruct_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto::offsets,
  file_level_metadata_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto, file_level_enum_descriptors_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto, file_level_service_descriptors_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_getter() {
  return &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto(&descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto);
namespace carbon {
namespace aimbot {
namespace target_velocity_estimator {

// ===================================================================

class TVERowProfile::_Internal {
 public:
};

TVERowProfile::TVERowProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.target_velocity_estimator.TVERowProfile)
}
TVERowProfile::TVERowProfile(const TVERowProfile& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&primary_kill_rate_, &from.primary_kill_rate_,
    static_cast<size_t>(reinterpret_cast<char*>(&secondary_kill_rate_) -
    reinterpret_cast<char*>(&primary_kill_rate_)) + sizeof(secondary_kill_rate_));
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.target_velocity_estimator.TVERowProfile)
}

inline void TVERowProfile::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&primary_kill_rate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&secondary_kill_rate_) -
    reinterpret_cast<char*>(&primary_kill_rate_)) + sizeof(secondary_kill_rate_));
}

TVERowProfile::~TVERowProfile() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.target_velocity_estimator.TVERowProfile)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TVERowProfile::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TVERowProfile::ArenaDtor(void* object) {
  TVERowProfile* _this = reinterpret_cast< TVERowProfile* >(object);
  (void)_this;
}
void TVERowProfile::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TVERowProfile::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TVERowProfile::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.target_velocity_estimator.TVERowProfile)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&primary_kill_rate_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&secondary_kill_rate_) -
      reinterpret_cast<char*>(&primary_kill_rate_)) + sizeof(secondary_kill_rate_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TVERowProfile::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float primary_kill_rate = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          primary_kill_rate_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float secondary_kill_rate = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          secondary_kill_rate_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TVERowProfile::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.target_velocity_estimator.TVERowProfile)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float primary_kill_rate = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_primary_kill_rate = this->_internal_primary_kill_rate();
  uint32_t raw_primary_kill_rate;
  memcpy(&raw_primary_kill_rate, &tmp_primary_kill_rate, sizeof(tmp_primary_kill_rate));
  if (raw_primary_kill_rate != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_primary_kill_rate(), target);
  }

  // float secondary_kill_rate = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_secondary_kill_rate = this->_internal_secondary_kill_rate();
  uint32_t raw_secondary_kill_rate;
  memcpy(&raw_secondary_kill_rate, &tmp_secondary_kill_rate, sizeof(tmp_secondary_kill_rate));
  if (raw_secondary_kill_rate != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_secondary_kill_rate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.target_velocity_estimator.TVERowProfile)
  return target;
}

size_t TVERowProfile::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.target_velocity_estimator.TVERowProfile)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float primary_kill_rate = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_primary_kill_rate = this->_internal_primary_kill_rate();
  uint32_t raw_primary_kill_rate;
  memcpy(&raw_primary_kill_rate, &tmp_primary_kill_rate, sizeof(tmp_primary_kill_rate));
  if (raw_primary_kill_rate != 0) {
    total_size += 1 + 4;
  }

  // float secondary_kill_rate = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_secondary_kill_rate = this->_internal_secondary_kill_rate();
  uint32_t raw_secondary_kill_rate;
  memcpy(&raw_secondary_kill_rate, &tmp_secondary_kill_rate, sizeof(tmp_secondary_kill_rate));
  if (raw_secondary_kill_rate != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TVERowProfile::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TVERowProfile::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TVERowProfile::GetClassData() const { return &_class_data_; }

void TVERowProfile::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TVERowProfile *>(to)->MergeFrom(
      static_cast<const TVERowProfile &>(from));
}


void TVERowProfile::MergeFrom(const TVERowProfile& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.target_velocity_estimator.TVERowProfile)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_primary_kill_rate = from._internal_primary_kill_rate();
  uint32_t raw_primary_kill_rate;
  memcpy(&raw_primary_kill_rate, &tmp_primary_kill_rate, sizeof(tmp_primary_kill_rate));
  if (raw_primary_kill_rate != 0) {
    _internal_set_primary_kill_rate(from._internal_primary_kill_rate());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_secondary_kill_rate = from._internal_secondary_kill_rate();
  uint32_t raw_secondary_kill_rate;
  memcpy(&raw_secondary_kill_rate, &tmp_secondary_kill_rate, sizeof(tmp_secondary_kill_rate));
  if (raw_secondary_kill_rate != 0) {
    _internal_set_secondary_kill_rate(from._internal_secondary_kill_rate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TVERowProfile::CopyFrom(const TVERowProfile& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.target_velocity_estimator.TVERowProfile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TVERowProfile::IsInitialized() const {
  return true;
}

void TVERowProfile::InternalSwap(TVERowProfile* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TVERowProfile, secondary_kill_rate_)
      + sizeof(TVERowProfile::secondary_kill_rate_)
      - PROTOBUF_FIELD_OFFSET(TVERowProfile, primary_kill_rate_)>(
          reinterpret_cast<char*>(&primary_kill_rate_),
          reinterpret_cast<char*>(&other->primary_kill_rate_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TVERowProfile::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto[0]);
}

// ===================================================================

TVEProfile_RowsEntry_DoNotUse::TVEProfile_RowsEntry_DoNotUse() {}
TVEProfile_RowsEntry_DoNotUse::TVEProfile_RowsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TVEProfile_RowsEntry_DoNotUse::MergeFrom(const TVEProfile_RowsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TVEProfile_RowsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto[1]);
}

// ===================================================================

class TVEProfile::_Internal {
 public:
  static const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& row_1(const TVEProfile* msg);
  static const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& row_2(const TVEProfile* msg);
  static const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& row_3(const TVEProfile* msg);
};

const ::carbon::aimbot::target_velocity_estimator::TVERowProfile&
TVEProfile::_Internal::row_1(const TVEProfile* msg) {
  return *msg->row_1_;
}
const ::carbon::aimbot::target_velocity_estimator::TVERowProfile&
TVEProfile::_Internal::row_2(const TVEProfile* msg) {
  return *msg->row_2_;
}
const ::carbon::aimbot::target_velocity_estimator::TVERowProfile&
TVEProfile::_Internal::row_3(const TVEProfile* msg) {
  return *msg->row_3_;
}
TVEProfile::TVEProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  rows_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.target_velocity_estimator.TVEProfile)
}
TVEProfile::TVEProfile(const TVEProfile& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  rows_.MergeFrom(from.rows_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_row_1()) {
    row_1_ = new ::carbon::aimbot::target_velocity_estimator::TVERowProfile(*from.row_1_);
  } else {
    row_1_ = nullptr;
  }
  if (from._internal_has_row_2()) {
    row_2_ = new ::carbon::aimbot::target_velocity_estimator::TVERowProfile(*from.row_2_);
  } else {
    row_2_ = nullptr;
  }
  if (from._internal_has_row_3()) {
    row_3_ = new ::carbon::aimbot::target_velocity_estimator::TVERowProfile(*from.row_3_);
  } else {
    row_3_ = nullptr;
  }
  ::memcpy(&update_ts_, &from.update_ts_,
    static_cast<size_t>(reinterpret_cast<char*>(&max_vel_mph_) -
    reinterpret_cast<char*>(&update_ts_)) + sizeof(max_vel_mph_));
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.target_velocity_estimator.TVEProfile)
}

inline void TVEProfile::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&row_1_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&max_vel_mph_) -
    reinterpret_cast<char*>(&row_1_)) + sizeof(max_vel_mph_));
}

TVEProfile::~TVEProfile() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.target_velocity_estimator.TVEProfile)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TVEProfile::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete row_1_;
  if (this != internal_default_instance()) delete row_2_;
  if (this != internal_default_instance()) delete row_3_;
}

void TVEProfile::ArenaDtor(void* object) {
  TVEProfile* _this = reinterpret_cast< TVEProfile* >(object);
  (void)_this;
  _this->rows_. ~MapField();
}
inline void TVEProfile::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &TVEProfile::ArenaDtor);
  }
}
void TVEProfile::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TVEProfile::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.target_velocity_estimator.TVEProfile)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rows_.Clear();
  id_.ClearToEmpty();
  name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && row_1_ != nullptr) {
    delete row_1_;
  }
  row_1_ = nullptr;
  if (GetArenaForAllocation() == nullptr && row_2_ != nullptr) {
    delete row_2_;
  }
  row_2_ = nullptr;
  if (GetArenaForAllocation() == nullptr && row_3_ != nullptr) {
    delete row_3_;
  }
  row_3_ = nullptr;
  ::memset(&update_ts_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&max_vel_mph_) -
      reinterpret_cast<char*>(&update_ts_)) + sizeof(max_vel_mph_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TVEProfile::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.target_velocity_estimator.TVEProfile.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.target_velocity_estimator.TVEProfile.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 update_ts = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          update_ts_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool protected = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          protected__ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float cruise_offset_percent = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          cruise_offset_percent_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float primary_range = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          primary_range_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float secondary_range = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          secondary_range_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float increase_smoothing = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          increase_smoothing_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float decrease_smoothing = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          decrease_smoothing_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_1 = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_row_1(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_2 = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_row_2(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_3 = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr = ctx->ParseMessage(_internal_mutable_row_3(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<uint32, .carbon.aimbot.target_velocity_estimator.TVERowProfile> rows = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&rows_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<106>(ptr));
        } else
          goto handle_unusual;
        continue;
      // float min_vel_mph = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 117)) {
          min_vel_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float max_vel_mph = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 125)) {
          max_vel_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TVEProfile::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.target_velocity_estimator.TVEProfile)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.target_velocity_estimator.TVEProfile.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.target_velocity_estimator.TVEProfile.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // int64 update_ts = 3;
  if (this->_internal_update_ts() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_update_ts(), target);
  }

  // bool protected = 4;
  if (this->_internal_protected_() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_protected_(), target);
  }

  // float cruise_offset_percent = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cruise_offset_percent = this->_internal_cruise_offset_percent();
  uint32_t raw_cruise_offset_percent;
  memcpy(&raw_cruise_offset_percent, &tmp_cruise_offset_percent, sizeof(tmp_cruise_offset_percent));
  if (raw_cruise_offset_percent != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_cruise_offset_percent(), target);
  }

  // float primary_range = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_primary_range = this->_internal_primary_range();
  uint32_t raw_primary_range;
  memcpy(&raw_primary_range, &tmp_primary_range, sizeof(tmp_primary_range));
  if (raw_primary_range != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_primary_range(), target);
  }

  // float secondary_range = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_secondary_range = this->_internal_secondary_range();
  uint32_t raw_secondary_range;
  memcpy(&raw_secondary_range, &tmp_secondary_range, sizeof(tmp_secondary_range));
  if (raw_secondary_range != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_secondary_range(), target);
  }

  // float increase_smoothing = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_increase_smoothing = this->_internal_increase_smoothing();
  uint32_t raw_increase_smoothing;
  memcpy(&raw_increase_smoothing, &tmp_increase_smoothing, sizeof(tmp_increase_smoothing));
  if (raw_increase_smoothing != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_increase_smoothing(), target);
  }

  // float decrease_smoothing = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_decrease_smoothing = this->_internal_decrease_smoothing();
  uint32_t raw_decrease_smoothing;
  memcpy(&raw_decrease_smoothing, &tmp_decrease_smoothing, sizeof(tmp_decrease_smoothing));
  if (raw_decrease_smoothing != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_decrease_smoothing(), target);
  }

  // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_1 = 10;
  if (this->_internal_has_row_1()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::row_1(this), target, stream);
  }

  // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_2 = 11;
  if (this->_internal_has_row_2()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::row_2(this), target, stream);
  }

  // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_3 = 12;
  if (this->_internal_has_row_3()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        12, _Internal::row_3(this), target, stream);
  }

  // map<uint32, .carbon.aimbot.target_velocity_estimator.TVERowProfile> rows = 13;
  if (!this->_internal_rows().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< uint32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_rows().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_rows().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >::const_iterator
          it = this->_internal_rows().begin();
          it != this->_internal_rows().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TVEProfile_RowsEntry_DoNotUse::Funcs::InternalSerialize(13, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >::const_iterator
          it = this->_internal_rows().begin();
          it != this->_internal_rows().end(); ++it) {
        target = TVEProfile_RowsEntry_DoNotUse::Funcs::InternalSerialize(13, it->first, it->second, target, stream);
      }
    }
  }

  // float min_vel_mph = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_vel_mph = this->_internal_min_vel_mph();
  uint32_t raw_min_vel_mph;
  memcpy(&raw_min_vel_mph, &tmp_min_vel_mph, sizeof(tmp_min_vel_mph));
  if (raw_min_vel_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(14, this->_internal_min_vel_mph(), target);
  }

  // float max_vel_mph = 15;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_vel_mph = this->_internal_max_vel_mph();
  uint32_t raw_max_vel_mph;
  memcpy(&raw_max_vel_mph, &tmp_max_vel_mph, sizeof(tmp_max_vel_mph));
  if (raw_max_vel_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(15, this->_internal_max_vel_mph(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.target_velocity_estimator.TVEProfile)
  return target;
}

size_t TVEProfile::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.target_velocity_estimator.TVEProfile)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<uint32, .carbon.aimbot.target_velocity_estimator.TVERowProfile> rows = 13;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_rows_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >::const_iterator
      it = this->_internal_rows().begin();
      it != this->_internal_rows().end(); ++it) {
    total_size += TVEProfile_RowsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_1 = 10;
  if (this->_internal_has_row_1()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *row_1_);
  }

  // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_2 = 11;
  if (this->_internal_has_row_2()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *row_2_);
  }

  // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_3 = 12;
  if (this->_internal_has_row_3()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *row_3_);
  }

  // int64 update_ts = 3;
  if (this->_internal_update_ts() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_update_ts());
  }

  // bool protected = 4;
  if (this->_internal_protected_() != 0) {
    total_size += 1 + 1;
  }

  // float cruise_offset_percent = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cruise_offset_percent = this->_internal_cruise_offset_percent();
  uint32_t raw_cruise_offset_percent;
  memcpy(&raw_cruise_offset_percent, &tmp_cruise_offset_percent, sizeof(tmp_cruise_offset_percent));
  if (raw_cruise_offset_percent != 0) {
    total_size += 1 + 4;
  }

  // float primary_range = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_primary_range = this->_internal_primary_range();
  uint32_t raw_primary_range;
  memcpy(&raw_primary_range, &tmp_primary_range, sizeof(tmp_primary_range));
  if (raw_primary_range != 0) {
    total_size += 1 + 4;
  }

  // float secondary_range = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_secondary_range = this->_internal_secondary_range();
  uint32_t raw_secondary_range;
  memcpy(&raw_secondary_range, &tmp_secondary_range, sizeof(tmp_secondary_range));
  if (raw_secondary_range != 0) {
    total_size += 1 + 4;
  }

  // float increase_smoothing = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_increase_smoothing = this->_internal_increase_smoothing();
  uint32_t raw_increase_smoothing;
  memcpy(&raw_increase_smoothing, &tmp_increase_smoothing, sizeof(tmp_increase_smoothing));
  if (raw_increase_smoothing != 0) {
    total_size += 1 + 4;
  }

  // float decrease_smoothing = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_decrease_smoothing = this->_internal_decrease_smoothing();
  uint32_t raw_decrease_smoothing;
  memcpy(&raw_decrease_smoothing, &tmp_decrease_smoothing, sizeof(tmp_decrease_smoothing));
  if (raw_decrease_smoothing != 0) {
    total_size += 1 + 4;
  }

  // float min_vel_mph = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_vel_mph = this->_internal_min_vel_mph();
  uint32_t raw_min_vel_mph;
  memcpy(&raw_min_vel_mph, &tmp_min_vel_mph, sizeof(tmp_min_vel_mph));
  if (raw_min_vel_mph != 0) {
    total_size += 1 + 4;
  }

  // float max_vel_mph = 15;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_vel_mph = this->_internal_max_vel_mph();
  uint32_t raw_max_vel_mph;
  memcpy(&raw_max_vel_mph, &tmp_max_vel_mph, sizeof(tmp_max_vel_mph));
  if (raw_max_vel_mph != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TVEProfile::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TVEProfile::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TVEProfile::GetClassData() const { return &_class_data_; }

void TVEProfile::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TVEProfile *>(to)->MergeFrom(
      static_cast<const TVEProfile &>(from));
}


void TVEProfile::MergeFrom(const TVEProfile& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.target_velocity_estimator.TVEProfile)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  rows_.MergeFrom(from.rows_);
  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_has_row_1()) {
    _internal_mutable_row_1()->::carbon::aimbot::target_velocity_estimator::TVERowProfile::MergeFrom(from._internal_row_1());
  }
  if (from._internal_has_row_2()) {
    _internal_mutable_row_2()->::carbon::aimbot::target_velocity_estimator::TVERowProfile::MergeFrom(from._internal_row_2());
  }
  if (from._internal_has_row_3()) {
    _internal_mutable_row_3()->::carbon::aimbot::target_velocity_estimator::TVERowProfile::MergeFrom(from._internal_row_3());
  }
  if (from._internal_update_ts() != 0) {
    _internal_set_update_ts(from._internal_update_ts());
  }
  if (from._internal_protected_() != 0) {
    _internal_set_protected_(from._internal_protected_());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cruise_offset_percent = from._internal_cruise_offset_percent();
  uint32_t raw_cruise_offset_percent;
  memcpy(&raw_cruise_offset_percent, &tmp_cruise_offset_percent, sizeof(tmp_cruise_offset_percent));
  if (raw_cruise_offset_percent != 0) {
    _internal_set_cruise_offset_percent(from._internal_cruise_offset_percent());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_primary_range = from._internal_primary_range();
  uint32_t raw_primary_range;
  memcpy(&raw_primary_range, &tmp_primary_range, sizeof(tmp_primary_range));
  if (raw_primary_range != 0) {
    _internal_set_primary_range(from._internal_primary_range());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_secondary_range = from._internal_secondary_range();
  uint32_t raw_secondary_range;
  memcpy(&raw_secondary_range, &tmp_secondary_range, sizeof(tmp_secondary_range));
  if (raw_secondary_range != 0) {
    _internal_set_secondary_range(from._internal_secondary_range());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_increase_smoothing = from._internal_increase_smoothing();
  uint32_t raw_increase_smoothing;
  memcpy(&raw_increase_smoothing, &tmp_increase_smoothing, sizeof(tmp_increase_smoothing));
  if (raw_increase_smoothing != 0) {
    _internal_set_increase_smoothing(from._internal_increase_smoothing());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_decrease_smoothing = from._internal_decrease_smoothing();
  uint32_t raw_decrease_smoothing;
  memcpy(&raw_decrease_smoothing, &tmp_decrease_smoothing, sizeof(tmp_decrease_smoothing));
  if (raw_decrease_smoothing != 0) {
    _internal_set_decrease_smoothing(from._internal_decrease_smoothing());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_vel_mph = from._internal_min_vel_mph();
  uint32_t raw_min_vel_mph;
  memcpy(&raw_min_vel_mph, &tmp_min_vel_mph, sizeof(tmp_min_vel_mph));
  if (raw_min_vel_mph != 0) {
    _internal_set_min_vel_mph(from._internal_min_vel_mph());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_vel_mph = from._internal_max_vel_mph();
  uint32_t raw_max_vel_mph;
  memcpy(&raw_max_vel_mph, &tmp_max_vel_mph, sizeof(tmp_max_vel_mph));
  if (raw_max_vel_mph != 0) {
    _internal_set_max_vel_mph(from._internal_max_vel_mph());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TVEProfile::CopyFrom(const TVEProfile& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.target_velocity_estimator.TVEProfile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TVEProfile::IsInitialized() const {
  return true;
}

void TVEProfile::InternalSwap(TVEProfile* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  rows_.InternalSwap(&other->rows_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TVEProfile, max_vel_mph_)
      + sizeof(TVEProfile::max_vel_mph_)
      - PROTOBUF_FIELD_OFFSET(TVEProfile, row_1_)>(
          reinterpret_cast<char*>(&row_1_),
          reinterpret_cast<char*>(&other->row_1_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TVEProfile::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto[2]);
}

// ===================================================================

class ProfileDetails::_Internal {
 public:
};

ProfileDetails::ProfileDetails(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.aimbot.target_velocity_estimator.ProfileDetails)
}
ProfileDetails::ProfileDetails(const ProfileDetails& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.aimbot.target_velocity_estimator.ProfileDetails)
}

inline void ProfileDetails::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ProfileDetails::~ProfileDetails() {
  // @@protoc_insertion_point(destructor:carbon.aimbot.target_velocity_estimator.ProfileDetails)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ProfileDetails::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ProfileDetails::ArenaDtor(void* object) {
  ProfileDetails* _this = reinterpret_cast< ProfileDetails* >(object);
  (void)_this;
}
void ProfileDetails::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ProfileDetails::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ProfileDetails::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.aimbot.target_velocity_estimator.ProfileDetails)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ProfileDetails::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.target_velocity_estimator.ProfileDetails.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.aimbot.target_velocity_estimator.ProfileDetails.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ProfileDetails::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.aimbot.target_velocity_estimator.ProfileDetails)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.target_velocity_estimator.ProfileDetails.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.aimbot.target_velocity_estimator.ProfileDetails.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.aimbot.target_velocity_estimator.ProfileDetails)
  return target;
}

size_t ProfileDetails::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.aimbot.target_velocity_estimator.ProfileDetails)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ProfileDetails::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ProfileDetails::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ProfileDetails::GetClassData() const { return &_class_data_; }

void ProfileDetails::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ProfileDetails *>(to)->MergeFrom(
      static_cast<const ProfileDetails &>(from));
}


void ProfileDetails::MergeFrom(const ProfileDetails& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.aimbot.target_velocity_estimator.ProfileDetails)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ProfileDetails::CopyFrom(const ProfileDetails& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.aimbot.target_velocity_estimator.ProfileDetails)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProfileDetails::IsInitialized() const {
  return true;
}

void ProfileDetails::InternalSwap(ProfileDetails* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ProfileDetails::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_getter, &descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto_once,
      file_level_metadata_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto[3]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace target_velocity_estimator
}  // namespace aimbot
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::aimbot::target_velocity_estimator::TVERowProfile* Arena::CreateMaybeMessage< ::carbon::aimbot::target_velocity_estimator::TVERowProfile >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::target_velocity_estimator::TVERowProfile >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::target_velocity_estimator::TVEProfile* Arena::CreateMaybeMessage< ::carbon::aimbot::target_velocity_estimator::TVEProfile >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::target_velocity_estimator::TVEProfile >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::aimbot::target_velocity_estimator::ProfileDetails* Arena::CreateMaybeMessage< ::carbon::aimbot::target_velocity_estimator::ProfileDetails >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::aimbot::target_velocity_estimator::ProfileDetails >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

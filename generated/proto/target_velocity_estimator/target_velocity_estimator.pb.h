// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/target_velocity_estimator/target_velocity_estimator.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[4]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto;
namespace carbon {
namespace aimbot {
namespace target_velocity_estimator {
class ProfileDetails;
struct ProfileDetailsDefaultTypeInternal;
extern ProfileDetailsDefaultTypeInternal _ProfileDetails_default_instance_;
class TVEProfile;
struct TVEProfileDefaultTypeInternal;
extern TVEProfileDefaultTypeInternal _TVEProfile_default_instance_;
class TVEProfile_RowsEntry_DoNotUse;
struct TVEProfile_RowsEntry_DoNotUseDefaultTypeInternal;
extern TVEProfile_RowsEntry_DoNotUseDefaultTypeInternal _TVEProfile_RowsEntry_DoNotUse_default_instance_;
class TVERowProfile;
struct TVERowProfileDefaultTypeInternal;
extern TVERowProfileDefaultTypeInternal _TVERowProfile_default_instance_;
}  // namespace target_velocity_estimator
}  // namespace aimbot
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::aimbot::target_velocity_estimator::ProfileDetails* Arena::CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::ProfileDetails>(Arena*);
template<> ::carbon::aimbot::target_velocity_estimator::TVEProfile* Arena::CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::TVEProfile>(Arena*);
template<> ::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::TVEProfile_RowsEntry_DoNotUse>(Arena*);
template<> ::carbon::aimbot::target_velocity_estimator::TVERowProfile* Arena::CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::TVERowProfile>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace aimbot {
namespace target_velocity_estimator {

// ===================================================================

class TVERowProfile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.target_velocity_estimator.TVERowProfile) */ {
 public:
  inline TVERowProfile() : TVERowProfile(nullptr) {}
  ~TVERowProfile() override;
  explicit constexpr TVERowProfile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TVERowProfile(const TVERowProfile& from);
  TVERowProfile(TVERowProfile&& from) noexcept
    : TVERowProfile() {
    *this = ::std::move(from);
  }

  inline TVERowProfile& operator=(const TVERowProfile& from) {
    CopyFrom(from);
    return *this;
  }
  inline TVERowProfile& operator=(TVERowProfile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TVERowProfile& default_instance() {
    return *internal_default_instance();
  }
  static inline const TVERowProfile* internal_default_instance() {
    return reinterpret_cast<const TVERowProfile*>(
               &_TVERowProfile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TVERowProfile& a, TVERowProfile& b) {
    a.Swap(&b);
  }
  inline void Swap(TVERowProfile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TVERowProfile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TVERowProfile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TVERowProfile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TVERowProfile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TVERowProfile& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TVERowProfile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.target_velocity_estimator.TVERowProfile";
  }
  protected:
  explicit TVERowProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPrimaryKillRateFieldNumber = 1,
    kSecondaryKillRateFieldNumber = 2,
  };
  // float primary_kill_rate = 1;
  void clear_primary_kill_rate();
  float primary_kill_rate() const;
  void set_primary_kill_rate(float value);
  private:
  float _internal_primary_kill_rate() const;
  void _internal_set_primary_kill_rate(float value);
  public:

  // float secondary_kill_rate = 2;
  void clear_secondary_kill_rate();
  float secondary_kill_rate() const;
  void set_secondary_kill_rate(float value);
  private:
  float _internal_secondary_kill_rate() const;
  void _internal_set_secondary_kill_rate(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.target_velocity_estimator.TVERowProfile)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float primary_kill_rate_;
  float secondary_kill_rate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class TVEProfile_RowsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TVEProfile_RowsEntry_DoNotUse, 
    uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TVEProfile_RowsEntry_DoNotUse, 
    uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  TVEProfile_RowsEntry_DoNotUse();
  explicit constexpr TVEProfile_RowsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TVEProfile_RowsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TVEProfile_RowsEntry_DoNotUse& other);
  static const TVEProfile_RowsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TVEProfile_RowsEntry_DoNotUse*>(&_TVEProfile_RowsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TVEProfile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.target_velocity_estimator.TVEProfile) */ {
 public:
  inline TVEProfile() : TVEProfile(nullptr) {}
  ~TVEProfile() override;
  explicit constexpr TVEProfile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TVEProfile(const TVEProfile& from);
  TVEProfile(TVEProfile&& from) noexcept
    : TVEProfile() {
    *this = ::std::move(from);
  }

  inline TVEProfile& operator=(const TVEProfile& from) {
    CopyFrom(from);
    return *this;
  }
  inline TVEProfile& operator=(TVEProfile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TVEProfile& default_instance() {
    return *internal_default_instance();
  }
  static inline const TVEProfile* internal_default_instance() {
    return reinterpret_cast<const TVEProfile*>(
               &_TVEProfile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TVEProfile& a, TVEProfile& b) {
    a.Swap(&b);
  }
  inline void Swap(TVEProfile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TVEProfile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TVEProfile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TVEProfile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TVEProfile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TVEProfile& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TVEProfile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.target_velocity_estimator.TVEProfile";
  }
  protected:
  explicit TVEProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kRowsFieldNumber = 13,
    kIdFieldNumber = 1,
    kNameFieldNumber = 2,
    kRow1FieldNumber = 10,
    kRow2FieldNumber = 11,
    kRow3FieldNumber = 12,
    kUpdateTsFieldNumber = 3,
    kProtectedFieldNumber = 4,
    kCruiseOffsetPercentFieldNumber = 5,
    kPrimaryRangeFieldNumber = 6,
    kSecondaryRangeFieldNumber = 7,
    kIncreaseSmoothingFieldNumber = 8,
    kDecreaseSmoothingFieldNumber = 9,
    kMinVelMphFieldNumber = 14,
    kMaxVelMphFieldNumber = 15,
  };
  // map<uint32, .carbon.aimbot.target_velocity_estimator.TVERowProfile> rows = 13;
  int rows_size() const;
  private:
  int _internal_rows_size() const;
  public:
  void clear_rows();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >&
      _internal_rows() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >*
      _internal_mutable_rows();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >&
      rows() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >*
      mutable_rows();

  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_1 = 10;
  bool has_row_1() const;
  private:
  bool _internal_has_row_1() const;
  public:
  void clear_row_1();
  const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& row_1() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVERowProfile* release_row_1();
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* mutable_row_1();
  void set_allocated_row_1(::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_1);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& _internal_row_1() const;
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* _internal_mutable_row_1();
  public:
  void unsafe_arena_set_allocated_row_1(
      ::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_1);
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* unsafe_arena_release_row_1();

  // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_2 = 11;
  bool has_row_2() const;
  private:
  bool _internal_has_row_2() const;
  public:
  void clear_row_2();
  const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& row_2() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVERowProfile* release_row_2();
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* mutable_row_2();
  void set_allocated_row_2(::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_2);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& _internal_row_2() const;
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* _internal_mutable_row_2();
  public:
  void unsafe_arena_set_allocated_row_2(
      ::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_2);
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* unsafe_arena_release_row_2();

  // .carbon.aimbot.target_velocity_estimator.TVERowProfile row_3 = 12;
  bool has_row_3() const;
  private:
  bool _internal_has_row_3() const;
  public:
  void clear_row_3();
  const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& row_3() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVERowProfile* release_row_3();
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* mutable_row_3();
  void set_allocated_row_3(::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_3);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& _internal_row_3() const;
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* _internal_mutable_row_3();
  public:
  void unsafe_arena_set_allocated_row_3(
      ::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_3);
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* unsafe_arena_release_row_3();

  // int64 update_ts = 3;
  void clear_update_ts();
  int64_t update_ts() const;
  void set_update_ts(int64_t value);
  private:
  int64_t _internal_update_ts() const;
  void _internal_set_update_ts(int64_t value);
  public:

  // bool protected = 4;
  void clear_protected_();
  bool protected_() const;
  void set_protected_(bool value);
  private:
  bool _internal_protected_() const;
  void _internal_set_protected_(bool value);
  public:

  // float cruise_offset_percent = 5;
  void clear_cruise_offset_percent();
  float cruise_offset_percent() const;
  void set_cruise_offset_percent(float value);
  private:
  float _internal_cruise_offset_percent() const;
  void _internal_set_cruise_offset_percent(float value);
  public:

  // float primary_range = 6;
  void clear_primary_range();
  float primary_range() const;
  void set_primary_range(float value);
  private:
  float _internal_primary_range() const;
  void _internal_set_primary_range(float value);
  public:

  // float secondary_range = 7;
  void clear_secondary_range();
  float secondary_range() const;
  void set_secondary_range(float value);
  private:
  float _internal_secondary_range() const;
  void _internal_set_secondary_range(float value);
  public:

  // float increase_smoothing = 8;
  void clear_increase_smoothing();
  float increase_smoothing() const;
  void set_increase_smoothing(float value);
  private:
  float _internal_increase_smoothing() const;
  void _internal_set_increase_smoothing(float value);
  public:

  // float decrease_smoothing = 9;
  void clear_decrease_smoothing();
  float decrease_smoothing() const;
  void set_decrease_smoothing(float value);
  private:
  float _internal_decrease_smoothing() const;
  void _internal_set_decrease_smoothing(float value);
  public:

  // float min_vel_mph = 14;
  void clear_min_vel_mph();
  float min_vel_mph() const;
  void set_min_vel_mph(float value);
  private:
  float _internal_min_vel_mph() const;
  void _internal_set_min_vel_mph(float value);
  public:

  // float max_vel_mph = 15;
  void clear_max_vel_mph();
  float max_vel_mph() const;
  void set_max_vel_mph(float value);
  private:
  float _internal_max_vel_mph() const;
  void _internal_set_max_vel_mph(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.target_velocity_estimator.TVEProfile)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TVEProfile_RowsEntry_DoNotUse,
      uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> rows_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_1_;
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_2_;
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_3_;
  int64_t update_ts_;
  bool protected__;
  float cruise_offset_percent_;
  float primary_range_;
  float secondary_range_;
  float increase_smoothing_;
  float decrease_smoothing_;
  float min_vel_mph_;
  float max_vel_mph_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto;
};
// -------------------------------------------------------------------

class ProfileDetails final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.aimbot.target_velocity_estimator.ProfileDetails) */ {
 public:
  inline ProfileDetails() : ProfileDetails(nullptr) {}
  ~ProfileDetails() override;
  explicit constexpr ProfileDetails(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileDetails(const ProfileDetails& from);
  ProfileDetails(ProfileDetails&& from) noexcept
    : ProfileDetails() {
    *this = ::std::move(from);
  }

  inline ProfileDetails& operator=(const ProfileDetails& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileDetails& operator=(ProfileDetails&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileDetails& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileDetails* internal_default_instance() {
    return reinterpret_cast<const ProfileDetails*>(
               &_ProfileDetails_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ProfileDetails& a, ProfileDetails& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileDetails* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileDetails* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileDetails* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileDetails>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileDetails& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ProfileDetails& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileDetails* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.aimbot.target_velocity_estimator.ProfileDetails";
  }
  protected:
  explicit ProfileDetails(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kNameFieldNumber = 2,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.aimbot.target_velocity_estimator.ProfileDetails)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TVERowProfile

// float primary_kill_rate = 1;
inline void TVERowProfile::clear_primary_kill_rate() {
  primary_kill_rate_ = 0;
}
inline float TVERowProfile::_internal_primary_kill_rate() const {
  return primary_kill_rate_;
}
inline float TVERowProfile::primary_kill_rate() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVERowProfile.primary_kill_rate)
  return _internal_primary_kill_rate();
}
inline void TVERowProfile::_internal_set_primary_kill_rate(float value) {
  
  primary_kill_rate_ = value;
}
inline void TVERowProfile::set_primary_kill_rate(float value) {
  _internal_set_primary_kill_rate(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVERowProfile.primary_kill_rate)
}

// float secondary_kill_rate = 2;
inline void TVERowProfile::clear_secondary_kill_rate() {
  secondary_kill_rate_ = 0;
}
inline float TVERowProfile::_internal_secondary_kill_rate() const {
  return secondary_kill_rate_;
}
inline float TVERowProfile::secondary_kill_rate() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVERowProfile.secondary_kill_rate)
  return _internal_secondary_kill_rate();
}
inline void TVERowProfile::_internal_set_secondary_kill_rate(float value) {
  
  secondary_kill_rate_ = value;
}
inline void TVERowProfile::set_secondary_kill_rate(float value) {
  _internal_set_secondary_kill_rate(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVERowProfile.secondary_kill_rate)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TVEProfile

// string id = 1;
inline void TVEProfile::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& TVEProfile::id() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TVEProfile::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.id)
}
inline std::string* TVEProfile::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.target_velocity_estimator.TVEProfile.id)
  return _s;
}
inline const std::string& TVEProfile::_internal_id() const {
  return id_.Get();
}
inline void TVEProfile::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TVEProfile::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TVEProfile::release_id() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.target_velocity_estimator.TVEProfile.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TVEProfile::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.target_velocity_estimator.TVEProfile.id)
}

// string name = 2;
inline void TVEProfile::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& TVEProfile::name() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TVEProfile::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.name)
}
inline std::string* TVEProfile::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.target_velocity_estimator.TVEProfile.name)
  return _s;
}
inline const std::string& TVEProfile::_internal_name() const {
  return name_.Get();
}
inline void TVEProfile::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TVEProfile::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TVEProfile::release_name() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.target_velocity_estimator.TVEProfile.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TVEProfile::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.target_velocity_estimator.TVEProfile.name)
}

// int64 update_ts = 3;
inline void TVEProfile::clear_update_ts() {
  update_ts_ = int64_t{0};
}
inline int64_t TVEProfile::_internal_update_ts() const {
  return update_ts_;
}
inline int64_t TVEProfile::update_ts() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.update_ts)
  return _internal_update_ts();
}
inline void TVEProfile::_internal_set_update_ts(int64_t value) {
  
  update_ts_ = value;
}
inline void TVEProfile::set_update_ts(int64_t value) {
  _internal_set_update_ts(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.update_ts)
}

// bool protected = 4;
inline void TVEProfile::clear_protected_() {
  protected__ = false;
}
inline bool TVEProfile::_internal_protected_() const {
  return protected__;
}
inline bool TVEProfile::protected_() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.protected)
  return _internal_protected_();
}
inline void TVEProfile::_internal_set_protected_(bool value) {
  
  protected__ = value;
}
inline void TVEProfile::set_protected_(bool value) {
  _internal_set_protected_(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.protected)
}

// float cruise_offset_percent = 5;
inline void TVEProfile::clear_cruise_offset_percent() {
  cruise_offset_percent_ = 0;
}
inline float TVEProfile::_internal_cruise_offset_percent() const {
  return cruise_offset_percent_;
}
inline float TVEProfile::cruise_offset_percent() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.cruise_offset_percent)
  return _internal_cruise_offset_percent();
}
inline void TVEProfile::_internal_set_cruise_offset_percent(float value) {
  
  cruise_offset_percent_ = value;
}
inline void TVEProfile::set_cruise_offset_percent(float value) {
  _internal_set_cruise_offset_percent(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.cruise_offset_percent)
}

// float primary_range = 6;
inline void TVEProfile::clear_primary_range() {
  primary_range_ = 0;
}
inline float TVEProfile::_internal_primary_range() const {
  return primary_range_;
}
inline float TVEProfile::primary_range() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.primary_range)
  return _internal_primary_range();
}
inline void TVEProfile::_internal_set_primary_range(float value) {
  
  primary_range_ = value;
}
inline void TVEProfile::set_primary_range(float value) {
  _internal_set_primary_range(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.primary_range)
}

// float secondary_range = 7;
inline void TVEProfile::clear_secondary_range() {
  secondary_range_ = 0;
}
inline float TVEProfile::_internal_secondary_range() const {
  return secondary_range_;
}
inline float TVEProfile::secondary_range() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.secondary_range)
  return _internal_secondary_range();
}
inline void TVEProfile::_internal_set_secondary_range(float value) {
  
  secondary_range_ = value;
}
inline void TVEProfile::set_secondary_range(float value) {
  _internal_set_secondary_range(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.secondary_range)
}

// float increase_smoothing = 8;
inline void TVEProfile::clear_increase_smoothing() {
  increase_smoothing_ = 0;
}
inline float TVEProfile::_internal_increase_smoothing() const {
  return increase_smoothing_;
}
inline float TVEProfile::increase_smoothing() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.increase_smoothing)
  return _internal_increase_smoothing();
}
inline void TVEProfile::_internal_set_increase_smoothing(float value) {
  
  increase_smoothing_ = value;
}
inline void TVEProfile::set_increase_smoothing(float value) {
  _internal_set_increase_smoothing(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.increase_smoothing)
}

// float decrease_smoothing = 9;
inline void TVEProfile::clear_decrease_smoothing() {
  decrease_smoothing_ = 0;
}
inline float TVEProfile::_internal_decrease_smoothing() const {
  return decrease_smoothing_;
}
inline float TVEProfile::decrease_smoothing() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.decrease_smoothing)
  return _internal_decrease_smoothing();
}
inline void TVEProfile::_internal_set_decrease_smoothing(float value) {
  
  decrease_smoothing_ = value;
}
inline void TVEProfile::set_decrease_smoothing(float value) {
  _internal_set_decrease_smoothing(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.decrease_smoothing)
}

// .carbon.aimbot.target_velocity_estimator.TVERowProfile row_1 = 10;
inline bool TVEProfile::_internal_has_row_1() const {
  return this != internal_default_instance() && row_1_ != nullptr;
}
inline bool TVEProfile::has_row_1() const {
  return _internal_has_row_1();
}
inline void TVEProfile::clear_row_1() {
  if (GetArenaForAllocation() == nullptr && row_1_ != nullptr) {
    delete row_1_;
  }
  row_1_ = nullptr;
}
inline const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& TVEProfile::_internal_row_1() const {
  const ::carbon::aimbot::target_velocity_estimator::TVERowProfile* p = row_1_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::target_velocity_estimator::TVERowProfile&>(
      ::carbon::aimbot::target_velocity_estimator::_TVERowProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& TVEProfile::row_1() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.row_1)
  return _internal_row_1();
}
inline void TVEProfile::unsafe_arena_set_allocated_row_1(
    ::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_1) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(row_1_);
  }
  row_1_ = row_1;
  if (row_1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.aimbot.target_velocity_estimator.TVEProfile.row_1)
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::release_row_1() {
  
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* temp = row_1_;
  row_1_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::unsafe_arena_release_row_1() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.target_velocity_estimator.TVEProfile.row_1)
  
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* temp = row_1_;
  row_1_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::_internal_mutable_row_1() {
  
  if (row_1_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::TVERowProfile>(GetArenaForAllocation());
    row_1_ = p;
  }
  return row_1_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::mutable_row_1() {
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* _msg = _internal_mutable_row_1();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.target_velocity_estimator.TVEProfile.row_1)
  return _msg;
}
inline void TVEProfile::set_allocated_row_1(::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_1) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete row_1_;
  }
  if (row_1) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::aimbot::target_velocity_estimator::TVERowProfile>::GetOwningArena(row_1);
    if (message_arena != submessage_arena) {
      row_1 = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, row_1, submessage_arena);
    }
    
  } else {
    
  }
  row_1_ = row_1;
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.target_velocity_estimator.TVEProfile.row_1)
}

// .carbon.aimbot.target_velocity_estimator.TVERowProfile row_2 = 11;
inline bool TVEProfile::_internal_has_row_2() const {
  return this != internal_default_instance() && row_2_ != nullptr;
}
inline bool TVEProfile::has_row_2() const {
  return _internal_has_row_2();
}
inline void TVEProfile::clear_row_2() {
  if (GetArenaForAllocation() == nullptr && row_2_ != nullptr) {
    delete row_2_;
  }
  row_2_ = nullptr;
}
inline const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& TVEProfile::_internal_row_2() const {
  const ::carbon::aimbot::target_velocity_estimator::TVERowProfile* p = row_2_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::target_velocity_estimator::TVERowProfile&>(
      ::carbon::aimbot::target_velocity_estimator::_TVERowProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& TVEProfile::row_2() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.row_2)
  return _internal_row_2();
}
inline void TVEProfile::unsafe_arena_set_allocated_row_2(
    ::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_2) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(row_2_);
  }
  row_2_ = row_2;
  if (row_2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.aimbot.target_velocity_estimator.TVEProfile.row_2)
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::release_row_2() {
  
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* temp = row_2_;
  row_2_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::unsafe_arena_release_row_2() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.target_velocity_estimator.TVEProfile.row_2)
  
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* temp = row_2_;
  row_2_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::_internal_mutable_row_2() {
  
  if (row_2_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::TVERowProfile>(GetArenaForAllocation());
    row_2_ = p;
  }
  return row_2_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::mutable_row_2() {
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* _msg = _internal_mutable_row_2();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.target_velocity_estimator.TVEProfile.row_2)
  return _msg;
}
inline void TVEProfile::set_allocated_row_2(::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_2) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete row_2_;
  }
  if (row_2) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::aimbot::target_velocity_estimator::TVERowProfile>::GetOwningArena(row_2);
    if (message_arena != submessage_arena) {
      row_2 = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, row_2, submessage_arena);
    }
    
  } else {
    
  }
  row_2_ = row_2;
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.target_velocity_estimator.TVEProfile.row_2)
}

// .carbon.aimbot.target_velocity_estimator.TVERowProfile row_3 = 12;
inline bool TVEProfile::_internal_has_row_3() const {
  return this != internal_default_instance() && row_3_ != nullptr;
}
inline bool TVEProfile::has_row_3() const {
  return _internal_has_row_3();
}
inline void TVEProfile::clear_row_3() {
  if (GetArenaForAllocation() == nullptr && row_3_ != nullptr) {
    delete row_3_;
  }
  row_3_ = nullptr;
}
inline const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& TVEProfile::_internal_row_3() const {
  const ::carbon::aimbot::target_velocity_estimator::TVERowProfile* p = row_3_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::target_velocity_estimator::TVERowProfile&>(
      ::carbon::aimbot::target_velocity_estimator::_TVERowProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVERowProfile& TVEProfile::row_3() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.row_3)
  return _internal_row_3();
}
inline void TVEProfile::unsafe_arena_set_allocated_row_3(
    ::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_3) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(row_3_);
  }
  row_3_ = row_3;
  if (row_3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.aimbot.target_velocity_estimator.TVEProfile.row_3)
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::release_row_3() {
  
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* temp = row_3_;
  row_3_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::unsafe_arena_release_row_3() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.target_velocity_estimator.TVEProfile.row_3)
  
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* temp = row_3_;
  row_3_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::_internal_mutable_row_3() {
  
  if (row_3_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::target_velocity_estimator::TVERowProfile>(GetArenaForAllocation());
    row_3_ = p;
  }
  return row_3_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVERowProfile* TVEProfile::mutable_row_3() {
  ::carbon::aimbot::target_velocity_estimator::TVERowProfile* _msg = _internal_mutable_row_3();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.target_velocity_estimator.TVEProfile.row_3)
  return _msg;
}
inline void TVEProfile::set_allocated_row_3(::carbon::aimbot::target_velocity_estimator::TVERowProfile* row_3) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete row_3_;
  }
  if (row_3) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::aimbot::target_velocity_estimator::TVERowProfile>::GetOwningArena(row_3);
    if (message_arena != submessage_arena) {
      row_3 = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, row_3, submessage_arena);
    }
    
  } else {
    
  }
  row_3_ = row_3;
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.target_velocity_estimator.TVEProfile.row_3)
}

// map<uint32, .carbon.aimbot.target_velocity_estimator.TVERowProfile> rows = 13;
inline int TVEProfile::_internal_rows_size() const {
  return rows_.size();
}
inline int TVEProfile::rows_size() const {
  return _internal_rows_size();
}
inline void TVEProfile::clear_rows() {
  rows_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >&
TVEProfile::_internal_rows() const {
  return rows_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >&
TVEProfile::rows() const {
  // @@protoc_insertion_point(field_map:carbon.aimbot.target_velocity_estimator.TVEProfile.rows)
  return _internal_rows();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >*
TVEProfile::_internal_mutable_rows() {
  return rows_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, ::carbon::aimbot::target_velocity_estimator::TVERowProfile >*
TVEProfile::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_map:carbon.aimbot.target_velocity_estimator.TVEProfile.rows)
  return _internal_mutable_rows();
}

// float min_vel_mph = 14;
inline void TVEProfile::clear_min_vel_mph() {
  min_vel_mph_ = 0;
}
inline float TVEProfile::_internal_min_vel_mph() const {
  return min_vel_mph_;
}
inline float TVEProfile::min_vel_mph() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.min_vel_mph)
  return _internal_min_vel_mph();
}
inline void TVEProfile::_internal_set_min_vel_mph(float value) {
  
  min_vel_mph_ = value;
}
inline void TVEProfile::set_min_vel_mph(float value) {
  _internal_set_min_vel_mph(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.min_vel_mph)
}

// float max_vel_mph = 15;
inline void TVEProfile::clear_max_vel_mph() {
  max_vel_mph_ = 0;
}
inline float TVEProfile::_internal_max_vel_mph() const {
  return max_vel_mph_;
}
inline float TVEProfile::max_vel_mph() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.TVEProfile.max_vel_mph)
  return _internal_max_vel_mph();
}
inline void TVEProfile::_internal_set_max_vel_mph(float value) {
  
  max_vel_mph_ = value;
}
inline void TVEProfile::set_max_vel_mph(float value) {
  _internal_set_max_vel_mph(value);
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.TVEProfile.max_vel_mph)
}

// -------------------------------------------------------------------

// ProfileDetails

// string id = 1;
inline void ProfileDetails::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& ProfileDetails::id() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.ProfileDetails.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileDetails::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.ProfileDetails.id)
}
inline std::string* ProfileDetails::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.target_velocity_estimator.ProfileDetails.id)
  return _s;
}
inline const std::string& ProfileDetails::_internal_id() const {
  return id_.Get();
}
inline void ProfileDetails::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ProfileDetails::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ProfileDetails::release_id() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.target_velocity_estimator.ProfileDetails.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ProfileDetails::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.target_velocity_estimator.ProfileDetails.id)
}

// string name = 2;
inline void ProfileDetails::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& ProfileDetails::name() const {
  // @@protoc_insertion_point(field_get:carbon.aimbot.target_velocity_estimator.ProfileDetails.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileDetails::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.aimbot.target_velocity_estimator.ProfileDetails.name)
}
inline std::string* ProfileDetails::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.aimbot.target_velocity_estimator.ProfileDetails.name)
  return _s;
}
inline const std::string& ProfileDetails::_internal_name() const {
  return name_.Get();
}
inline void ProfileDetails::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ProfileDetails::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ProfileDetails::release_name() {
  // @@protoc_insertion_point(field_release:carbon.aimbot.target_velocity_estimator.ProfileDetails.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ProfileDetails::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.aimbot.target_velocity_estimator.ProfileDetails.name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace target_velocity_estimator
}  // namespace aimbot
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2ftarget_5fvelocity_5festimator_2ftarget_5fvelocity_5festimator_2eproto

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class TVERowProfile(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    primary_kill_rate: builtin___float = ...
    secondary_kill_rate: builtin___float = ...

    def __init__(self,
        *,
        primary_kill_rate : typing___Optional[builtin___float] = None,
        secondary_kill_rate : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"primary_kill_rate",b"primary_kill_rate",u"secondary_kill_rate",b"secondary_kill_rate"]) -> None: ...
type___TVERowProfile = TVERowProfile

class TVEProfile(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class RowsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___TVERowProfile: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___TVERowProfile] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___RowsEntry = RowsEntry

    id: typing___Text = ...
    name: typing___Text = ...
    update_ts: builtin___int = ...
    protected: builtin___bool = ...
    cruise_offset_percent: builtin___float = ...
    primary_range: builtin___float = ...
    secondary_range: builtin___float = ...
    increase_smoothing: builtin___float = ...
    decrease_smoothing: builtin___float = ...
    min_vel_mph: builtin___float = ...
    max_vel_mph: builtin___float = ...

    @property
    def row_1(self) -> type___TVERowProfile: ...

    @property
    def row_2(self) -> type___TVERowProfile: ...

    @property
    def row_3(self) -> type___TVERowProfile: ...

    @property
    def rows(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___TVERowProfile]: ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        update_ts : typing___Optional[builtin___int] = None,
        protected : typing___Optional[builtin___bool] = None,
        cruise_offset_percent : typing___Optional[builtin___float] = None,
        primary_range : typing___Optional[builtin___float] = None,
        secondary_range : typing___Optional[builtin___float] = None,
        increase_smoothing : typing___Optional[builtin___float] = None,
        decrease_smoothing : typing___Optional[builtin___float] = None,
        row_1 : typing___Optional[type___TVERowProfile] = None,
        row_2 : typing___Optional[type___TVERowProfile] = None,
        row_3 : typing___Optional[type___TVERowProfile] = None,
        rows : typing___Optional[typing___Mapping[builtin___int, type___TVERowProfile]] = None,
        min_vel_mph : typing___Optional[builtin___float] = None,
        max_vel_mph : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"row_1",b"row_1",u"row_2",b"row_2",u"row_3",b"row_3"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cruise_offset_percent",b"cruise_offset_percent",u"decrease_smoothing",b"decrease_smoothing",u"id",b"id",u"increase_smoothing",b"increase_smoothing",u"max_vel_mph",b"max_vel_mph",u"min_vel_mph",b"min_vel_mph",u"name",b"name",u"primary_range",b"primary_range",u"protected",b"protected",u"row_1",b"row_1",u"row_2",b"row_2",u"row_3",b"row_3",u"rows",b"rows",u"secondary_range",b"secondary_range",u"update_ts",b"update_ts"]) -> None: ...
type___TVEProfile = TVEProfile

class ProfileDetails(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"name",b"name"]) -> None: ...
type___ProfileDetails = ProfileDetails

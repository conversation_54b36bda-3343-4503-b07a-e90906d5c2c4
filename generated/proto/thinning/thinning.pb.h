// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/thinning/thinning.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2fthinning_2fthinning_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2fthinning_2fthinning_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2fthinning_2fthinning_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2fthinning_2fthinning_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[7]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fthinning_2fthinning_2eproto;
namespace carbon {
namespace thinning {
class Bounds;
struct BoundsDefaultTypeInternal;
extern BoundsDefaultTypeInternal _Bounds_default_instance_;
class Box;
struct BoxDefaultTypeInternal;
extern BoxDefaultTypeInternal _Box_default_instance_;
class ConfigDefinition;
struct ConfigDefinitionDefaultTypeInternal;
extern ConfigDefinitionDefaultTypeInternal _ConfigDefinition_default_instance_;
class ConfigDefinition_RowsEntry_DoNotUse;
struct ConfigDefinition_RowsEntry_DoNotUseDefaultTypeInternal;
extern ConfigDefinition_RowsEntry_DoNotUseDefaultTypeInternal _ConfigDefinition_RowsEntry_DoNotUse_default_instance_;
class DoubleBox;
struct DoubleBoxDefaultTypeInternal;
extern DoubleBoxDefaultTypeInternal _DoubleBox_default_instance_;
class SizeFilter;
struct SizeFilterDefaultTypeInternal;
extern SizeFilterDefaultTypeInternal _SizeFilter_default_instance_;
class SizedNotSoGreedyCfg;
struct SizedNotSoGreedyCfgDefaultTypeInternal;
extern SizedNotSoGreedyCfgDefaultTypeInternal _SizedNotSoGreedyCfg_default_instance_;
}  // namespace thinning
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::thinning::Bounds* Arena::CreateMaybeMessage<::carbon::thinning::Bounds>(Arena*);
template<> ::carbon::thinning::Box* Arena::CreateMaybeMessage<::carbon::thinning::Box>(Arena*);
template<> ::carbon::thinning::ConfigDefinition* Arena::CreateMaybeMessage<::carbon::thinning::ConfigDefinition>(Arena*);
template<> ::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse>(Arena*);
template<> ::carbon::thinning::DoubleBox* Arena::CreateMaybeMessage<::carbon::thinning::DoubleBox>(Arena*);
template<> ::carbon::thinning::SizeFilter* Arena::CreateMaybeMessage<::carbon::thinning::SizeFilter>(Arena*);
template<> ::carbon::thinning::SizedNotSoGreedyCfg* Arena::CreateMaybeMessage<::carbon::thinning::SizedNotSoGreedyCfg>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace thinning {

// ===================================================================

class Box final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.thinning.Box) */ {
 public:
  inline Box() : Box(nullptr) {}
  ~Box() override;
  explicit constexpr Box(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Box(const Box& from);
  Box(Box&& from) noexcept
    : Box() {
    *this = ::std::move(from);
  }

  inline Box& operator=(const Box& from) {
    CopyFrom(from);
    return *this;
  }
  inline Box& operator=(Box&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Box& default_instance() {
    return *internal_default_instance();
  }
  static inline const Box* internal_default_instance() {
    return reinterpret_cast<const Box*>(
               &_Box_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Box& a, Box& b) {
    a.Swap(&b);
  }
  inline void Swap(Box* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Box* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Box* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Box>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Box& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Box& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Box* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.thinning.Box";
  }
  protected:
  explicit Box(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWidthFieldNumber = 1,
    kHeightFieldNumber = 2,
  };
  // double width = 1;
  void clear_width();
  double width() const;
  void set_width(double value);
  private:
  double _internal_width() const;
  void _internal_set_width(double value);
  public:

  // double height = 2;
  void clear_height();
  double height() const;
  void set_height(double value);
  private:
  double _internal_height() const;
  void _internal_set_height(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.thinning.Box)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double width_;
  double height_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fthinning_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class DoubleBox final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.thinning.DoubleBox) */ {
 public:
  inline DoubleBox() : DoubleBox(nullptr) {}
  ~DoubleBox() override;
  explicit constexpr DoubleBox(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DoubleBox(const DoubleBox& from);
  DoubleBox(DoubleBox&& from) noexcept
    : DoubleBox() {
    *this = ::std::move(from);
  }

  inline DoubleBox& operator=(const DoubleBox& from) {
    CopyFrom(from);
    return *this;
  }
  inline DoubleBox& operator=(DoubleBox&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DoubleBox& default_instance() {
    return *internal_default_instance();
  }
  static inline const DoubleBox* internal_default_instance() {
    return reinterpret_cast<const DoubleBox*>(
               &_DoubleBox_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DoubleBox& a, DoubleBox& b) {
    a.Swap(&b);
  }
  inline void Swap(DoubleBox* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DoubleBox* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DoubleBox* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DoubleBox>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DoubleBox& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DoubleBox& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DoubleBox* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.thinning.DoubleBox";
  }
  protected:
  explicit DoubleBox(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBox1FieldNumber = 1,
    kBox2FieldNumber = 2,
    kIdealYDistFieldNumber = 3,
  };
  // .carbon.thinning.Box box_1 = 1;
  bool has_box_1() const;
  private:
  bool _internal_has_box_1() const;
  public:
  void clear_box_1();
  const ::carbon::thinning::Box& box_1() const;
  PROTOBUF_NODISCARD ::carbon::thinning::Box* release_box_1();
  ::carbon::thinning::Box* mutable_box_1();
  void set_allocated_box_1(::carbon::thinning::Box* box_1);
  private:
  const ::carbon::thinning::Box& _internal_box_1() const;
  ::carbon::thinning::Box* _internal_mutable_box_1();
  public:
  void unsafe_arena_set_allocated_box_1(
      ::carbon::thinning::Box* box_1);
  ::carbon::thinning::Box* unsafe_arena_release_box_1();

  // .carbon.thinning.Box box_2 = 2;
  bool has_box_2() const;
  private:
  bool _internal_has_box_2() const;
  public:
  void clear_box_2();
  const ::carbon::thinning::Box& box_2() const;
  PROTOBUF_NODISCARD ::carbon::thinning::Box* release_box_2();
  ::carbon::thinning::Box* mutable_box_2();
  void set_allocated_box_2(::carbon::thinning::Box* box_2);
  private:
  const ::carbon::thinning::Box& _internal_box_2() const;
  ::carbon::thinning::Box* _internal_mutable_box_2();
  public:
  void unsafe_arena_set_allocated_box_2(
      ::carbon::thinning::Box* box_2);
  ::carbon::thinning::Box* unsafe_arena_release_box_2();

  // double ideal_y_dist = 3;
  void clear_ideal_y_dist();
  double ideal_y_dist() const;
  void set_ideal_y_dist(double value);
  private:
  double _internal_ideal_y_dist() const;
  void _internal_set_ideal_y_dist(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.thinning.DoubleBox)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::thinning::Box* box_1_;
  ::carbon::thinning::Box* box_2_;
  double ideal_y_dist_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fthinning_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class SizedNotSoGreedyCfg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.thinning.SizedNotSoGreedyCfg) */ {
 public:
  inline SizedNotSoGreedyCfg() : SizedNotSoGreedyCfg(nullptr) {}
  ~SizedNotSoGreedyCfg() override;
  explicit constexpr SizedNotSoGreedyCfg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SizedNotSoGreedyCfg(const SizedNotSoGreedyCfg& from);
  SizedNotSoGreedyCfg(SizedNotSoGreedyCfg&& from) noexcept
    : SizedNotSoGreedyCfg() {
    *this = ::std::move(from);
  }

  inline SizedNotSoGreedyCfg& operator=(const SizedNotSoGreedyCfg& from) {
    CopyFrom(from);
    return *this;
  }
  inline SizedNotSoGreedyCfg& operator=(SizedNotSoGreedyCfg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SizedNotSoGreedyCfg& default_instance() {
    return *internal_default_instance();
  }
  static inline const SizedNotSoGreedyCfg* internal_default_instance() {
    return reinterpret_cast<const SizedNotSoGreedyCfg*>(
               &_SizedNotSoGreedyCfg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SizedNotSoGreedyCfg& a, SizedNotSoGreedyCfg& b) {
    a.Swap(&b);
  }
  inline void Swap(SizedNotSoGreedyCfg* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SizedNotSoGreedyCfg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SizedNotSoGreedyCfg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SizedNotSoGreedyCfg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SizedNotSoGreedyCfg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SizedNotSoGreedyCfg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SizedNotSoGreedyCfg* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.thinning.SizedNotSoGreedyCfg";
  }
  protected:
  explicit SizedNotSoGreedyCfg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMinKeepoutFieldNumber = 1,
    kMaxYSearchRadiusFieldNumber = 2,
    kIdealYDistFieldNumber = 3,
    kSizeWeightFieldNumber = 4,
    kDistWeightFieldNumber = 5,
  };
  // .carbon.thinning.Box min_keepout = 1;
  bool has_min_keepout() const;
  private:
  bool _internal_has_min_keepout() const;
  public:
  void clear_min_keepout();
  const ::carbon::thinning::Box& min_keepout() const;
  PROTOBUF_NODISCARD ::carbon::thinning::Box* release_min_keepout();
  ::carbon::thinning::Box* mutable_min_keepout();
  void set_allocated_min_keepout(::carbon::thinning::Box* min_keepout);
  private:
  const ::carbon::thinning::Box& _internal_min_keepout() const;
  ::carbon::thinning::Box* _internal_mutable_min_keepout();
  public:
  void unsafe_arena_set_allocated_min_keepout(
      ::carbon::thinning::Box* min_keepout);
  ::carbon::thinning::Box* unsafe_arena_release_min_keepout();

  // double max_y_search_radius = 2;
  void clear_max_y_search_radius();
  double max_y_search_radius() const;
  void set_max_y_search_radius(double value);
  private:
  double _internal_max_y_search_radius() const;
  void _internal_set_max_y_search_radius(double value);
  public:

  // double ideal_y_dist = 3;
  void clear_ideal_y_dist();
  double ideal_y_dist() const;
  void set_ideal_y_dist(double value);
  private:
  double _internal_ideal_y_dist() const;
  void _internal_set_ideal_y_dist(double value);
  public:

  // double size_weight = 4;
  void clear_size_weight();
  double size_weight() const;
  void set_size_weight(double value);
  private:
  double _internal_size_weight() const;
  void _internal_set_size_weight(double value);
  public:

  // double dist_weight = 5;
  void clear_dist_weight();
  double dist_weight() const;
  void set_dist_weight(double value);
  private:
  double _internal_dist_weight() const;
  void _internal_set_dist_weight(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.thinning.SizedNotSoGreedyCfg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::thinning::Box* min_keepout_;
  double max_y_search_radius_;
  double ideal_y_dist_;
  double size_weight_;
  double dist_weight_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fthinning_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class Bounds final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.thinning.Bounds) */ {
 public:
  inline Bounds() : Bounds(nullptr) {}
  ~Bounds() override;
  explicit constexpr Bounds(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Bounds(const Bounds& from);
  Bounds(Bounds&& from) noexcept
    : Bounds() {
    *this = ::std::move(from);
  }

  inline Bounds& operator=(const Bounds& from) {
    CopyFrom(from);
    return *this;
  }
  inline Bounds& operator=(Bounds&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Bounds& default_instance() {
    return *internal_default_instance();
  }
  enum BoundsCase {
    kBox = 1,
    kDoubleBox = 2,
    kSizedCfg = 3,
    BOUNDS_NOT_SET = 0,
  };

  static inline const Bounds* internal_default_instance() {
    return reinterpret_cast<const Bounds*>(
               &_Bounds_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Bounds& a, Bounds& b) {
    a.Swap(&b);
  }
  inline void Swap(Bounds* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Bounds* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Bounds* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Bounds>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Bounds& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Bounds& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Bounds* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.thinning.Bounds";
  }
  protected:
  explicit Bounds(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBoxFieldNumber = 1,
    kDoubleBoxFieldNumber = 2,
    kSizedCfgFieldNumber = 3,
  };
  // .carbon.thinning.Box box = 1;
  bool has_box() const;
  private:
  bool _internal_has_box() const;
  public:
  void clear_box();
  const ::carbon::thinning::Box& box() const;
  PROTOBUF_NODISCARD ::carbon::thinning::Box* release_box();
  ::carbon::thinning::Box* mutable_box();
  void set_allocated_box(::carbon::thinning::Box* box);
  private:
  const ::carbon::thinning::Box& _internal_box() const;
  ::carbon::thinning::Box* _internal_mutable_box();
  public:
  void unsafe_arena_set_allocated_box(
      ::carbon::thinning::Box* box);
  ::carbon::thinning::Box* unsafe_arena_release_box();

  // .carbon.thinning.DoubleBox double_box = 2 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_double_box() const;
  private:
  bool _internal_has_double_box() const;
  public:
  PROTOBUF_DEPRECATED void clear_double_box();
  PROTOBUF_DEPRECATED const ::carbon::thinning::DoubleBox& double_box() const;
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED ::carbon::thinning::DoubleBox* release_double_box();
  PROTOBUF_DEPRECATED ::carbon::thinning::DoubleBox* mutable_double_box();
  PROTOBUF_DEPRECATED void set_allocated_double_box(::carbon::thinning::DoubleBox* double_box);
  private:
  const ::carbon::thinning::DoubleBox& _internal_double_box() const;
  ::carbon::thinning::DoubleBox* _internal_mutable_double_box();
  public:
  PROTOBUF_DEPRECATED void unsafe_arena_set_allocated_double_box(
      ::carbon::thinning::DoubleBox* double_box);
  PROTOBUF_DEPRECATED ::carbon::thinning::DoubleBox* unsafe_arena_release_double_box();

  // .carbon.thinning.SizedNotSoGreedyCfg sized_cfg = 3;
  bool has_sized_cfg() const;
  private:
  bool _internal_has_sized_cfg() const;
  public:
  void clear_sized_cfg();
  const ::carbon::thinning::SizedNotSoGreedyCfg& sized_cfg() const;
  PROTOBUF_NODISCARD ::carbon::thinning::SizedNotSoGreedyCfg* release_sized_cfg();
  ::carbon::thinning::SizedNotSoGreedyCfg* mutable_sized_cfg();
  void set_allocated_sized_cfg(::carbon::thinning::SizedNotSoGreedyCfg* sized_cfg);
  private:
  const ::carbon::thinning::SizedNotSoGreedyCfg& _internal_sized_cfg() const;
  ::carbon::thinning::SizedNotSoGreedyCfg* _internal_mutable_sized_cfg();
  public:
  void unsafe_arena_set_allocated_sized_cfg(
      ::carbon::thinning::SizedNotSoGreedyCfg* sized_cfg);
  ::carbon::thinning::SizedNotSoGreedyCfg* unsafe_arena_release_sized_cfg();

  void clear_bounds();
  BoundsCase bounds_case() const;
  // @@protoc_insertion_point(class_scope:carbon.thinning.Bounds)
 private:
  class _Internal;
  void set_has_box();
  void set_has_double_box();
  void set_has_sized_cfg();

  inline bool has_bounds() const;
  inline void clear_has_bounds();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union BoundsUnion {
    constexpr BoundsUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::thinning::Box* box_;
    ::carbon::thinning::DoubleBox* double_box_;
    ::carbon::thinning::SizedNotSoGreedyCfg* sized_cfg_;
  } bounds_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_proto_2fthinning_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class SizeFilter final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.thinning.SizeFilter) */ {
 public:
  inline SizeFilter() : SizeFilter(nullptr) {}
  ~SizeFilter() override;
  explicit constexpr SizeFilter(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SizeFilter(const SizeFilter& from);
  SizeFilter(SizeFilter&& from) noexcept
    : SizeFilter() {
    *this = ::std::move(from);
  }

  inline SizeFilter& operator=(const SizeFilter& from) {
    CopyFrom(from);
    return *this;
  }
  inline SizeFilter& operator=(SizeFilter&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SizeFilter& default_instance() {
    return *internal_default_instance();
  }
  static inline const SizeFilter* internal_default_instance() {
    return reinterpret_cast<const SizeFilter*>(
               &_SizeFilter_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SizeFilter& a, SizeFilter& b) {
    a.Swap(&b);
  }
  inline void Swap(SizeFilter* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SizeFilter* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SizeFilter* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SizeFilter>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SizeFilter& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SizeFilter& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SizeFilter* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.thinning.SizeFilter";
  }
  protected:
  explicit SizeFilter(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSamplesSizeFieldNumber = 2,
    kAcceptableVarianceFieldNumber = 3,
    kEnabledFieldNumber = 1,
  };
  // uint64 samples_size = 2;
  void clear_samples_size();
  uint64_t samples_size() const;
  void set_samples_size(uint64_t value);
  private:
  uint64_t _internal_samples_size() const;
  void _internal_set_samples_size(uint64_t value);
  public:

  // double acceptable_variance = 3;
  void clear_acceptable_variance();
  double acceptable_variance() const;
  void set_acceptable_variance(double value);
  private:
  double _internal_acceptable_variance() const;
  void _internal_set_acceptable_variance(double value);
  public:

  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.thinning.SizeFilter)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t samples_size_;
  double acceptable_variance_;
  bool enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fthinning_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class ConfigDefinition_RowsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ConfigDefinition_RowsEntry_DoNotUse, 
    int32_t, ::carbon::thinning::Bounds,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ConfigDefinition_RowsEntry_DoNotUse, 
    int32_t, ::carbon::thinning::Bounds,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ConfigDefinition_RowsEntry_DoNotUse();
  explicit constexpr ConfigDefinition_RowsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ConfigDefinition_RowsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ConfigDefinition_RowsEntry_DoNotUse& other);
  static const ConfigDefinition_RowsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ConfigDefinition_RowsEntry_DoNotUse*>(&_ConfigDefinition_RowsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class ConfigDefinition final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.thinning.ConfigDefinition) */ {
 public:
  inline ConfigDefinition() : ConfigDefinition(nullptr) {}
  ~ConfigDefinition() override;
  explicit constexpr ConfigDefinition(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigDefinition(const ConfigDefinition& from);
  ConfigDefinition(ConfigDefinition&& from) noexcept
    : ConfigDefinition() {
    *this = ::std::move(from);
  }

  inline ConfigDefinition& operator=(const ConfigDefinition& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigDefinition& operator=(ConfigDefinition&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigDefinition& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigDefinition* internal_default_instance() {
    return reinterpret_cast<const ConfigDefinition*>(
               &_ConfigDefinition_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(ConfigDefinition& a, ConfigDefinition& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigDefinition* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigDefinition* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigDefinition* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigDefinition>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigDefinition& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ConfigDefinition& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigDefinition* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.thinning.ConfigDefinition";
  }
  protected:
  explicit ConfigDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kRowsFieldNumber = 8,
    kNameFieldNumber = 1,
    kIdFieldNumber = 7,
    kRow1FieldNumber = 3,
    kRow2FieldNumber = 4,
    kRow3FieldNumber = 5,
    kSizeFilterFieldNumber = 6,
    kActiveFieldNumber = 2,
  };
  // map<int32, .carbon.thinning.Bounds> rows = 8;
  int rows_size() const;
  private:
  int _internal_rows_size() const;
  public:
  void clear_rows();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >&
      _internal_rows() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >*
      _internal_mutable_rows();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >&
      rows() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >*
      mutable_rows();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string id = 7;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // .carbon.thinning.Bounds row_1 = 3;
  bool has_row_1() const;
  private:
  bool _internal_has_row_1() const;
  public:
  void clear_row_1();
  const ::carbon::thinning::Bounds& row_1() const;
  PROTOBUF_NODISCARD ::carbon::thinning::Bounds* release_row_1();
  ::carbon::thinning::Bounds* mutable_row_1();
  void set_allocated_row_1(::carbon::thinning::Bounds* row_1);
  private:
  const ::carbon::thinning::Bounds& _internal_row_1() const;
  ::carbon::thinning::Bounds* _internal_mutable_row_1();
  public:
  void unsafe_arena_set_allocated_row_1(
      ::carbon::thinning::Bounds* row_1);
  ::carbon::thinning::Bounds* unsafe_arena_release_row_1();

  // .carbon.thinning.Bounds row_2 = 4;
  bool has_row_2() const;
  private:
  bool _internal_has_row_2() const;
  public:
  void clear_row_2();
  const ::carbon::thinning::Bounds& row_2() const;
  PROTOBUF_NODISCARD ::carbon::thinning::Bounds* release_row_2();
  ::carbon::thinning::Bounds* mutable_row_2();
  void set_allocated_row_2(::carbon::thinning::Bounds* row_2);
  private:
  const ::carbon::thinning::Bounds& _internal_row_2() const;
  ::carbon::thinning::Bounds* _internal_mutable_row_2();
  public:
  void unsafe_arena_set_allocated_row_2(
      ::carbon::thinning::Bounds* row_2);
  ::carbon::thinning::Bounds* unsafe_arena_release_row_2();

  // .carbon.thinning.Bounds row_3 = 5;
  bool has_row_3() const;
  private:
  bool _internal_has_row_3() const;
  public:
  void clear_row_3();
  const ::carbon::thinning::Bounds& row_3() const;
  PROTOBUF_NODISCARD ::carbon::thinning::Bounds* release_row_3();
  ::carbon::thinning::Bounds* mutable_row_3();
  void set_allocated_row_3(::carbon::thinning::Bounds* row_3);
  private:
  const ::carbon::thinning::Bounds& _internal_row_3() const;
  ::carbon::thinning::Bounds* _internal_mutable_row_3();
  public:
  void unsafe_arena_set_allocated_row_3(
      ::carbon::thinning::Bounds* row_3);
  ::carbon::thinning::Bounds* unsafe_arena_release_row_3();

  // .carbon.thinning.SizeFilter size_filter = 6 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_size_filter() const;
  private:
  bool _internal_has_size_filter() const;
  public:
  PROTOBUF_DEPRECATED void clear_size_filter();
  PROTOBUF_DEPRECATED const ::carbon::thinning::SizeFilter& size_filter() const;
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED ::carbon::thinning::SizeFilter* release_size_filter();
  PROTOBUF_DEPRECATED ::carbon::thinning::SizeFilter* mutable_size_filter();
  PROTOBUF_DEPRECATED void set_allocated_size_filter(::carbon::thinning::SizeFilter* size_filter);
  private:
  const ::carbon::thinning::SizeFilter& _internal_size_filter() const;
  ::carbon::thinning::SizeFilter* _internal_mutable_size_filter();
  public:
  PROTOBUF_DEPRECATED void unsafe_arena_set_allocated_size_filter(
      ::carbon::thinning::SizeFilter* size_filter);
  PROTOBUF_DEPRECATED ::carbon::thinning::SizeFilter* unsafe_arena_release_size_filter();

  // bool active = 2 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_active();
  PROTOBUF_DEPRECATED bool active() const;
  PROTOBUF_DEPRECATED void set_active(bool value);
  private:
  bool _internal_active() const;
  void _internal_set_active(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.thinning.ConfigDefinition)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ConfigDefinition_RowsEntry_DoNotUse,
      int32_t, ::carbon::thinning::Bounds,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> rows_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::carbon::thinning::Bounds* row_1_;
  ::carbon::thinning::Bounds* row_2_;
  ::carbon::thinning::Bounds* row_3_;
  ::carbon::thinning::SizeFilter* size_filter_;
  bool active_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fthinning_2fthinning_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Box

// double width = 1;
inline void Box::clear_width() {
  width_ = 0;
}
inline double Box::_internal_width() const {
  return width_;
}
inline double Box::width() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.Box.width)
  return _internal_width();
}
inline void Box::_internal_set_width(double value) {
  
  width_ = value;
}
inline void Box::set_width(double value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.Box.width)
}

// double height = 2;
inline void Box::clear_height() {
  height_ = 0;
}
inline double Box::_internal_height() const {
  return height_;
}
inline double Box::height() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.Box.height)
  return _internal_height();
}
inline void Box::_internal_set_height(double value) {
  
  height_ = value;
}
inline void Box::set_height(double value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.Box.height)
}

// -------------------------------------------------------------------

// DoubleBox

// .carbon.thinning.Box box_1 = 1;
inline bool DoubleBox::_internal_has_box_1() const {
  return this != internal_default_instance() && box_1_ != nullptr;
}
inline bool DoubleBox::has_box_1() const {
  return _internal_has_box_1();
}
inline void DoubleBox::clear_box_1() {
  if (GetArenaForAllocation() == nullptr && box_1_ != nullptr) {
    delete box_1_;
  }
  box_1_ = nullptr;
}
inline const ::carbon::thinning::Box& DoubleBox::_internal_box_1() const {
  const ::carbon::thinning::Box* p = box_1_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::thinning::Box&>(
      ::carbon::thinning::_Box_default_instance_);
}
inline const ::carbon::thinning::Box& DoubleBox::box_1() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.DoubleBox.box_1)
  return _internal_box_1();
}
inline void DoubleBox::unsafe_arena_set_allocated_box_1(
    ::carbon::thinning::Box* box_1) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(box_1_);
  }
  box_1_ = box_1;
  if (box_1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.DoubleBox.box_1)
}
inline ::carbon::thinning::Box* DoubleBox::release_box_1() {
  
  ::carbon::thinning::Box* temp = box_1_;
  box_1_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::thinning::Box* DoubleBox::unsafe_arena_release_box_1() {
  // @@protoc_insertion_point(field_release:carbon.thinning.DoubleBox.box_1)
  
  ::carbon::thinning::Box* temp = box_1_;
  box_1_ = nullptr;
  return temp;
}
inline ::carbon::thinning::Box* DoubleBox::_internal_mutable_box_1() {
  
  if (box_1_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::thinning::Box>(GetArenaForAllocation());
    box_1_ = p;
  }
  return box_1_;
}
inline ::carbon::thinning::Box* DoubleBox::mutable_box_1() {
  ::carbon::thinning::Box* _msg = _internal_mutable_box_1();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.DoubleBox.box_1)
  return _msg;
}
inline void DoubleBox::set_allocated_box_1(::carbon::thinning::Box* box_1) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete box_1_;
  }
  if (box_1) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::Box>::GetOwningArena(box_1);
    if (message_arena != submessage_arena) {
      box_1 = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, box_1, submessage_arena);
    }
    
  } else {
    
  }
  box_1_ = box_1;
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.DoubleBox.box_1)
}

// .carbon.thinning.Box box_2 = 2;
inline bool DoubleBox::_internal_has_box_2() const {
  return this != internal_default_instance() && box_2_ != nullptr;
}
inline bool DoubleBox::has_box_2() const {
  return _internal_has_box_2();
}
inline void DoubleBox::clear_box_2() {
  if (GetArenaForAllocation() == nullptr && box_2_ != nullptr) {
    delete box_2_;
  }
  box_2_ = nullptr;
}
inline const ::carbon::thinning::Box& DoubleBox::_internal_box_2() const {
  const ::carbon::thinning::Box* p = box_2_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::thinning::Box&>(
      ::carbon::thinning::_Box_default_instance_);
}
inline const ::carbon::thinning::Box& DoubleBox::box_2() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.DoubleBox.box_2)
  return _internal_box_2();
}
inline void DoubleBox::unsafe_arena_set_allocated_box_2(
    ::carbon::thinning::Box* box_2) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(box_2_);
  }
  box_2_ = box_2;
  if (box_2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.DoubleBox.box_2)
}
inline ::carbon::thinning::Box* DoubleBox::release_box_2() {
  
  ::carbon::thinning::Box* temp = box_2_;
  box_2_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::thinning::Box* DoubleBox::unsafe_arena_release_box_2() {
  // @@protoc_insertion_point(field_release:carbon.thinning.DoubleBox.box_2)
  
  ::carbon::thinning::Box* temp = box_2_;
  box_2_ = nullptr;
  return temp;
}
inline ::carbon::thinning::Box* DoubleBox::_internal_mutable_box_2() {
  
  if (box_2_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::thinning::Box>(GetArenaForAllocation());
    box_2_ = p;
  }
  return box_2_;
}
inline ::carbon::thinning::Box* DoubleBox::mutable_box_2() {
  ::carbon::thinning::Box* _msg = _internal_mutable_box_2();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.DoubleBox.box_2)
  return _msg;
}
inline void DoubleBox::set_allocated_box_2(::carbon::thinning::Box* box_2) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete box_2_;
  }
  if (box_2) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::Box>::GetOwningArena(box_2);
    if (message_arena != submessage_arena) {
      box_2 = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, box_2, submessage_arena);
    }
    
  } else {
    
  }
  box_2_ = box_2;
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.DoubleBox.box_2)
}

// double ideal_y_dist = 3;
inline void DoubleBox::clear_ideal_y_dist() {
  ideal_y_dist_ = 0;
}
inline double DoubleBox::_internal_ideal_y_dist() const {
  return ideal_y_dist_;
}
inline double DoubleBox::ideal_y_dist() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.DoubleBox.ideal_y_dist)
  return _internal_ideal_y_dist();
}
inline void DoubleBox::_internal_set_ideal_y_dist(double value) {
  
  ideal_y_dist_ = value;
}
inline void DoubleBox::set_ideal_y_dist(double value) {
  _internal_set_ideal_y_dist(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.DoubleBox.ideal_y_dist)
}

// -------------------------------------------------------------------

// SizedNotSoGreedyCfg

// .carbon.thinning.Box min_keepout = 1;
inline bool SizedNotSoGreedyCfg::_internal_has_min_keepout() const {
  return this != internal_default_instance() && min_keepout_ != nullptr;
}
inline bool SizedNotSoGreedyCfg::has_min_keepout() const {
  return _internal_has_min_keepout();
}
inline void SizedNotSoGreedyCfg::clear_min_keepout() {
  if (GetArenaForAllocation() == nullptr && min_keepout_ != nullptr) {
    delete min_keepout_;
  }
  min_keepout_ = nullptr;
}
inline const ::carbon::thinning::Box& SizedNotSoGreedyCfg::_internal_min_keepout() const {
  const ::carbon::thinning::Box* p = min_keepout_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::thinning::Box&>(
      ::carbon::thinning::_Box_default_instance_);
}
inline const ::carbon::thinning::Box& SizedNotSoGreedyCfg::min_keepout() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.SizedNotSoGreedyCfg.min_keepout)
  return _internal_min_keepout();
}
inline void SizedNotSoGreedyCfg::unsafe_arena_set_allocated_min_keepout(
    ::carbon::thinning::Box* min_keepout) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(min_keepout_);
  }
  min_keepout_ = min_keepout;
  if (min_keepout) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.SizedNotSoGreedyCfg.min_keepout)
}
inline ::carbon::thinning::Box* SizedNotSoGreedyCfg::release_min_keepout() {
  
  ::carbon::thinning::Box* temp = min_keepout_;
  min_keepout_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::thinning::Box* SizedNotSoGreedyCfg::unsafe_arena_release_min_keepout() {
  // @@protoc_insertion_point(field_release:carbon.thinning.SizedNotSoGreedyCfg.min_keepout)
  
  ::carbon::thinning::Box* temp = min_keepout_;
  min_keepout_ = nullptr;
  return temp;
}
inline ::carbon::thinning::Box* SizedNotSoGreedyCfg::_internal_mutable_min_keepout() {
  
  if (min_keepout_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::thinning::Box>(GetArenaForAllocation());
    min_keepout_ = p;
  }
  return min_keepout_;
}
inline ::carbon::thinning::Box* SizedNotSoGreedyCfg::mutable_min_keepout() {
  ::carbon::thinning::Box* _msg = _internal_mutable_min_keepout();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.SizedNotSoGreedyCfg.min_keepout)
  return _msg;
}
inline void SizedNotSoGreedyCfg::set_allocated_min_keepout(::carbon::thinning::Box* min_keepout) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete min_keepout_;
  }
  if (min_keepout) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::Box>::GetOwningArena(min_keepout);
    if (message_arena != submessage_arena) {
      min_keepout = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, min_keepout, submessage_arena);
    }
    
  } else {
    
  }
  min_keepout_ = min_keepout;
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.SizedNotSoGreedyCfg.min_keepout)
}

// double max_y_search_radius = 2;
inline void SizedNotSoGreedyCfg::clear_max_y_search_radius() {
  max_y_search_radius_ = 0;
}
inline double SizedNotSoGreedyCfg::_internal_max_y_search_radius() const {
  return max_y_search_radius_;
}
inline double SizedNotSoGreedyCfg::max_y_search_radius() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.SizedNotSoGreedyCfg.max_y_search_radius)
  return _internal_max_y_search_radius();
}
inline void SizedNotSoGreedyCfg::_internal_set_max_y_search_radius(double value) {
  
  max_y_search_radius_ = value;
}
inline void SizedNotSoGreedyCfg::set_max_y_search_radius(double value) {
  _internal_set_max_y_search_radius(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.SizedNotSoGreedyCfg.max_y_search_radius)
}

// double ideal_y_dist = 3;
inline void SizedNotSoGreedyCfg::clear_ideal_y_dist() {
  ideal_y_dist_ = 0;
}
inline double SizedNotSoGreedyCfg::_internal_ideal_y_dist() const {
  return ideal_y_dist_;
}
inline double SizedNotSoGreedyCfg::ideal_y_dist() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.SizedNotSoGreedyCfg.ideal_y_dist)
  return _internal_ideal_y_dist();
}
inline void SizedNotSoGreedyCfg::_internal_set_ideal_y_dist(double value) {
  
  ideal_y_dist_ = value;
}
inline void SizedNotSoGreedyCfg::set_ideal_y_dist(double value) {
  _internal_set_ideal_y_dist(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.SizedNotSoGreedyCfg.ideal_y_dist)
}

// double size_weight = 4;
inline void SizedNotSoGreedyCfg::clear_size_weight() {
  size_weight_ = 0;
}
inline double SizedNotSoGreedyCfg::_internal_size_weight() const {
  return size_weight_;
}
inline double SizedNotSoGreedyCfg::size_weight() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.SizedNotSoGreedyCfg.size_weight)
  return _internal_size_weight();
}
inline void SizedNotSoGreedyCfg::_internal_set_size_weight(double value) {
  
  size_weight_ = value;
}
inline void SizedNotSoGreedyCfg::set_size_weight(double value) {
  _internal_set_size_weight(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.SizedNotSoGreedyCfg.size_weight)
}

// double dist_weight = 5;
inline void SizedNotSoGreedyCfg::clear_dist_weight() {
  dist_weight_ = 0;
}
inline double SizedNotSoGreedyCfg::_internal_dist_weight() const {
  return dist_weight_;
}
inline double SizedNotSoGreedyCfg::dist_weight() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.SizedNotSoGreedyCfg.dist_weight)
  return _internal_dist_weight();
}
inline void SizedNotSoGreedyCfg::_internal_set_dist_weight(double value) {
  
  dist_weight_ = value;
}
inline void SizedNotSoGreedyCfg::set_dist_weight(double value) {
  _internal_set_dist_weight(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.SizedNotSoGreedyCfg.dist_weight)
}

// -------------------------------------------------------------------

// Bounds

// .carbon.thinning.Box box = 1;
inline bool Bounds::_internal_has_box() const {
  return bounds_case() == kBox;
}
inline bool Bounds::has_box() const {
  return _internal_has_box();
}
inline void Bounds::set_has_box() {
  _oneof_case_[0] = kBox;
}
inline void Bounds::clear_box() {
  if (_internal_has_box()) {
    if (GetArenaForAllocation() == nullptr) {
      delete bounds_.box_;
    }
    clear_has_bounds();
  }
}
inline ::carbon::thinning::Box* Bounds::release_box() {
  // @@protoc_insertion_point(field_release:carbon.thinning.Bounds.box)
  if (_internal_has_box()) {
    clear_has_bounds();
      ::carbon::thinning::Box* temp = bounds_.box_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    bounds_.box_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::thinning::Box& Bounds::_internal_box() const {
  return _internal_has_box()
      ? *bounds_.box_
      : reinterpret_cast< ::carbon::thinning::Box&>(::carbon::thinning::_Box_default_instance_);
}
inline const ::carbon::thinning::Box& Bounds::box() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.Bounds.box)
  return _internal_box();
}
inline ::carbon::thinning::Box* Bounds::unsafe_arena_release_box() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.thinning.Bounds.box)
  if (_internal_has_box()) {
    clear_has_bounds();
    ::carbon::thinning::Box* temp = bounds_.box_;
    bounds_.box_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Bounds::unsafe_arena_set_allocated_box(::carbon::thinning::Box* box) {
  clear_bounds();
  if (box) {
    set_has_box();
    bounds_.box_ = box;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.Bounds.box)
}
inline ::carbon::thinning::Box* Bounds::_internal_mutable_box() {
  if (!_internal_has_box()) {
    clear_bounds();
    set_has_box();
    bounds_.box_ = CreateMaybeMessage< ::carbon::thinning::Box >(GetArenaForAllocation());
  }
  return bounds_.box_;
}
inline ::carbon::thinning::Box* Bounds::mutable_box() {
  ::carbon::thinning::Box* _msg = _internal_mutable_box();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.Bounds.box)
  return _msg;
}

// .carbon.thinning.DoubleBox double_box = 2 [deprecated = true];
inline bool Bounds::_internal_has_double_box() const {
  return bounds_case() == kDoubleBox;
}
inline bool Bounds::has_double_box() const {
  return _internal_has_double_box();
}
inline void Bounds::set_has_double_box() {
  _oneof_case_[0] = kDoubleBox;
}
inline void Bounds::clear_double_box() {
  if (_internal_has_double_box()) {
    if (GetArenaForAllocation() == nullptr) {
      delete bounds_.double_box_;
    }
    clear_has_bounds();
  }
}
inline ::carbon::thinning::DoubleBox* Bounds::release_double_box() {
  // @@protoc_insertion_point(field_release:carbon.thinning.Bounds.double_box)
  if (_internal_has_double_box()) {
    clear_has_bounds();
      ::carbon::thinning::DoubleBox* temp = bounds_.double_box_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    bounds_.double_box_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::thinning::DoubleBox& Bounds::_internal_double_box() const {
  return _internal_has_double_box()
      ? *bounds_.double_box_
      : reinterpret_cast< ::carbon::thinning::DoubleBox&>(::carbon::thinning::_DoubleBox_default_instance_);
}
inline const ::carbon::thinning::DoubleBox& Bounds::double_box() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.Bounds.double_box)
  return _internal_double_box();
}
inline ::carbon::thinning::DoubleBox* Bounds::unsafe_arena_release_double_box() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.thinning.Bounds.double_box)
  if (_internal_has_double_box()) {
    clear_has_bounds();
    ::carbon::thinning::DoubleBox* temp = bounds_.double_box_;
    bounds_.double_box_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Bounds::unsafe_arena_set_allocated_double_box(::carbon::thinning::DoubleBox* double_box) {
  clear_bounds();
  if (double_box) {
    set_has_double_box();
    bounds_.double_box_ = double_box;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.Bounds.double_box)
}
inline ::carbon::thinning::DoubleBox* Bounds::_internal_mutable_double_box() {
  if (!_internal_has_double_box()) {
    clear_bounds();
    set_has_double_box();
    bounds_.double_box_ = CreateMaybeMessage< ::carbon::thinning::DoubleBox >(GetArenaForAllocation());
  }
  return bounds_.double_box_;
}
inline ::carbon::thinning::DoubleBox* Bounds::mutable_double_box() {
  ::carbon::thinning::DoubleBox* _msg = _internal_mutable_double_box();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.Bounds.double_box)
  return _msg;
}

// .carbon.thinning.SizedNotSoGreedyCfg sized_cfg = 3;
inline bool Bounds::_internal_has_sized_cfg() const {
  return bounds_case() == kSizedCfg;
}
inline bool Bounds::has_sized_cfg() const {
  return _internal_has_sized_cfg();
}
inline void Bounds::set_has_sized_cfg() {
  _oneof_case_[0] = kSizedCfg;
}
inline void Bounds::clear_sized_cfg() {
  if (_internal_has_sized_cfg()) {
    if (GetArenaForAllocation() == nullptr) {
      delete bounds_.sized_cfg_;
    }
    clear_has_bounds();
  }
}
inline ::carbon::thinning::SizedNotSoGreedyCfg* Bounds::release_sized_cfg() {
  // @@protoc_insertion_point(field_release:carbon.thinning.Bounds.sized_cfg)
  if (_internal_has_sized_cfg()) {
    clear_has_bounds();
      ::carbon::thinning::SizedNotSoGreedyCfg* temp = bounds_.sized_cfg_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    bounds_.sized_cfg_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::thinning::SizedNotSoGreedyCfg& Bounds::_internal_sized_cfg() const {
  return _internal_has_sized_cfg()
      ? *bounds_.sized_cfg_
      : reinterpret_cast< ::carbon::thinning::SizedNotSoGreedyCfg&>(::carbon::thinning::_SizedNotSoGreedyCfg_default_instance_);
}
inline const ::carbon::thinning::SizedNotSoGreedyCfg& Bounds::sized_cfg() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.Bounds.sized_cfg)
  return _internal_sized_cfg();
}
inline ::carbon::thinning::SizedNotSoGreedyCfg* Bounds::unsafe_arena_release_sized_cfg() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.thinning.Bounds.sized_cfg)
  if (_internal_has_sized_cfg()) {
    clear_has_bounds();
    ::carbon::thinning::SizedNotSoGreedyCfg* temp = bounds_.sized_cfg_;
    bounds_.sized_cfg_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Bounds::unsafe_arena_set_allocated_sized_cfg(::carbon::thinning::SizedNotSoGreedyCfg* sized_cfg) {
  clear_bounds();
  if (sized_cfg) {
    set_has_sized_cfg();
    bounds_.sized_cfg_ = sized_cfg;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.Bounds.sized_cfg)
}
inline ::carbon::thinning::SizedNotSoGreedyCfg* Bounds::_internal_mutable_sized_cfg() {
  if (!_internal_has_sized_cfg()) {
    clear_bounds();
    set_has_sized_cfg();
    bounds_.sized_cfg_ = CreateMaybeMessage< ::carbon::thinning::SizedNotSoGreedyCfg >(GetArenaForAllocation());
  }
  return bounds_.sized_cfg_;
}
inline ::carbon::thinning::SizedNotSoGreedyCfg* Bounds::mutable_sized_cfg() {
  ::carbon::thinning::SizedNotSoGreedyCfg* _msg = _internal_mutable_sized_cfg();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.Bounds.sized_cfg)
  return _msg;
}

inline bool Bounds::has_bounds() const {
  return bounds_case() != BOUNDS_NOT_SET;
}
inline void Bounds::clear_has_bounds() {
  _oneof_case_[0] = BOUNDS_NOT_SET;
}
inline Bounds::BoundsCase Bounds::bounds_case() const {
  return Bounds::BoundsCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// SizeFilter

// bool enabled = 1;
inline void SizeFilter::clear_enabled() {
  enabled_ = false;
}
inline bool SizeFilter::_internal_enabled() const {
  return enabled_;
}
inline bool SizeFilter::enabled() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.SizeFilter.enabled)
  return _internal_enabled();
}
inline void SizeFilter::_internal_set_enabled(bool value) {
  
  enabled_ = value;
}
inline void SizeFilter::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.SizeFilter.enabled)
}

// uint64 samples_size = 2;
inline void SizeFilter::clear_samples_size() {
  samples_size_ = uint64_t{0u};
}
inline uint64_t SizeFilter::_internal_samples_size() const {
  return samples_size_;
}
inline uint64_t SizeFilter::samples_size() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.SizeFilter.samples_size)
  return _internal_samples_size();
}
inline void SizeFilter::_internal_set_samples_size(uint64_t value) {
  
  samples_size_ = value;
}
inline void SizeFilter::set_samples_size(uint64_t value) {
  _internal_set_samples_size(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.SizeFilter.samples_size)
}

// double acceptable_variance = 3;
inline void SizeFilter::clear_acceptable_variance() {
  acceptable_variance_ = 0;
}
inline double SizeFilter::_internal_acceptable_variance() const {
  return acceptable_variance_;
}
inline double SizeFilter::acceptable_variance() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.SizeFilter.acceptable_variance)
  return _internal_acceptable_variance();
}
inline void SizeFilter::_internal_set_acceptable_variance(double value) {
  
  acceptable_variance_ = value;
}
inline void SizeFilter::set_acceptable_variance(double value) {
  _internal_set_acceptable_variance(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.SizeFilter.acceptable_variance)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ConfigDefinition

// string name = 1;
inline void ConfigDefinition::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& ConfigDefinition::name() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.ConfigDefinition.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigDefinition::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.thinning.ConfigDefinition.name)
}
inline std::string* ConfigDefinition::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.ConfigDefinition.name)
  return _s;
}
inline const std::string& ConfigDefinition::_internal_name() const {
  return name_.Get();
}
inline void ConfigDefinition::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigDefinition::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigDefinition::release_name() {
  // @@protoc_insertion_point(field_release:carbon.thinning.ConfigDefinition.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigDefinition::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.ConfigDefinition.name)
}

// bool active = 2 [deprecated = true];
inline void ConfigDefinition::clear_active() {
  active_ = false;
}
inline bool ConfigDefinition::_internal_active() const {
  return active_;
}
inline bool ConfigDefinition::active() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.ConfigDefinition.active)
  return _internal_active();
}
inline void ConfigDefinition::_internal_set_active(bool value) {
  
  active_ = value;
}
inline void ConfigDefinition::set_active(bool value) {
  _internal_set_active(value);
  // @@protoc_insertion_point(field_set:carbon.thinning.ConfigDefinition.active)
}

// .carbon.thinning.Bounds row_1 = 3;
inline bool ConfigDefinition::_internal_has_row_1() const {
  return this != internal_default_instance() && row_1_ != nullptr;
}
inline bool ConfigDefinition::has_row_1() const {
  return _internal_has_row_1();
}
inline void ConfigDefinition::clear_row_1() {
  if (GetArenaForAllocation() == nullptr && row_1_ != nullptr) {
    delete row_1_;
  }
  row_1_ = nullptr;
}
inline const ::carbon::thinning::Bounds& ConfigDefinition::_internal_row_1() const {
  const ::carbon::thinning::Bounds* p = row_1_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::thinning::Bounds&>(
      ::carbon::thinning::_Bounds_default_instance_);
}
inline const ::carbon::thinning::Bounds& ConfigDefinition::row_1() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.ConfigDefinition.row_1)
  return _internal_row_1();
}
inline void ConfigDefinition::unsafe_arena_set_allocated_row_1(
    ::carbon::thinning::Bounds* row_1) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(row_1_);
  }
  row_1_ = row_1;
  if (row_1) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.ConfigDefinition.row_1)
}
inline ::carbon::thinning::Bounds* ConfigDefinition::release_row_1() {
  
  ::carbon::thinning::Bounds* temp = row_1_;
  row_1_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::thinning::Bounds* ConfigDefinition::unsafe_arena_release_row_1() {
  // @@protoc_insertion_point(field_release:carbon.thinning.ConfigDefinition.row_1)
  
  ::carbon::thinning::Bounds* temp = row_1_;
  row_1_ = nullptr;
  return temp;
}
inline ::carbon::thinning::Bounds* ConfigDefinition::_internal_mutable_row_1() {
  
  if (row_1_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::thinning::Bounds>(GetArenaForAllocation());
    row_1_ = p;
  }
  return row_1_;
}
inline ::carbon::thinning::Bounds* ConfigDefinition::mutable_row_1() {
  ::carbon::thinning::Bounds* _msg = _internal_mutable_row_1();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.ConfigDefinition.row_1)
  return _msg;
}
inline void ConfigDefinition::set_allocated_row_1(::carbon::thinning::Bounds* row_1) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete row_1_;
  }
  if (row_1) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::Bounds>::GetOwningArena(row_1);
    if (message_arena != submessage_arena) {
      row_1 = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, row_1, submessage_arena);
    }
    
  } else {
    
  }
  row_1_ = row_1;
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.ConfigDefinition.row_1)
}

// .carbon.thinning.Bounds row_2 = 4;
inline bool ConfigDefinition::_internal_has_row_2() const {
  return this != internal_default_instance() && row_2_ != nullptr;
}
inline bool ConfigDefinition::has_row_2() const {
  return _internal_has_row_2();
}
inline void ConfigDefinition::clear_row_2() {
  if (GetArenaForAllocation() == nullptr && row_2_ != nullptr) {
    delete row_2_;
  }
  row_2_ = nullptr;
}
inline const ::carbon::thinning::Bounds& ConfigDefinition::_internal_row_2() const {
  const ::carbon::thinning::Bounds* p = row_2_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::thinning::Bounds&>(
      ::carbon::thinning::_Bounds_default_instance_);
}
inline const ::carbon::thinning::Bounds& ConfigDefinition::row_2() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.ConfigDefinition.row_2)
  return _internal_row_2();
}
inline void ConfigDefinition::unsafe_arena_set_allocated_row_2(
    ::carbon::thinning::Bounds* row_2) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(row_2_);
  }
  row_2_ = row_2;
  if (row_2) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.ConfigDefinition.row_2)
}
inline ::carbon::thinning::Bounds* ConfigDefinition::release_row_2() {
  
  ::carbon::thinning::Bounds* temp = row_2_;
  row_2_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::thinning::Bounds* ConfigDefinition::unsafe_arena_release_row_2() {
  // @@protoc_insertion_point(field_release:carbon.thinning.ConfigDefinition.row_2)
  
  ::carbon::thinning::Bounds* temp = row_2_;
  row_2_ = nullptr;
  return temp;
}
inline ::carbon::thinning::Bounds* ConfigDefinition::_internal_mutable_row_2() {
  
  if (row_2_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::thinning::Bounds>(GetArenaForAllocation());
    row_2_ = p;
  }
  return row_2_;
}
inline ::carbon::thinning::Bounds* ConfigDefinition::mutable_row_2() {
  ::carbon::thinning::Bounds* _msg = _internal_mutable_row_2();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.ConfigDefinition.row_2)
  return _msg;
}
inline void ConfigDefinition::set_allocated_row_2(::carbon::thinning::Bounds* row_2) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete row_2_;
  }
  if (row_2) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::Bounds>::GetOwningArena(row_2);
    if (message_arena != submessage_arena) {
      row_2 = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, row_2, submessage_arena);
    }
    
  } else {
    
  }
  row_2_ = row_2;
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.ConfigDefinition.row_2)
}

// .carbon.thinning.Bounds row_3 = 5;
inline bool ConfigDefinition::_internal_has_row_3() const {
  return this != internal_default_instance() && row_3_ != nullptr;
}
inline bool ConfigDefinition::has_row_3() const {
  return _internal_has_row_3();
}
inline void ConfigDefinition::clear_row_3() {
  if (GetArenaForAllocation() == nullptr && row_3_ != nullptr) {
    delete row_3_;
  }
  row_3_ = nullptr;
}
inline const ::carbon::thinning::Bounds& ConfigDefinition::_internal_row_3() const {
  const ::carbon::thinning::Bounds* p = row_3_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::thinning::Bounds&>(
      ::carbon::thinning::_Bounds_default_instance_);
}
inline const ::carbon::thinning::Bounds& ConfigDefinition::row_3() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.ConfigDefinition.row_3)
  return _internal_row_3();
}
inline void ConfigDefinition::unsafe_arena_set_allocated_row_3(
    ::carbon::thinning::Bounds* row_3) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(row_3_);
  }
  row_3_ = row_3;
  if (row_3) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.ConfigDefinition.row_3)
}
inline ::carbon::thinning::Bounds* ConfigDefinition::release_row_3() {
  
  ::carbon::thinning::Bounds* temp = row_3_;
  row_3_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::thinning::Bounds* ConfigDefinition::unsafe_arena_release_row_3() {
  // @@protoc_insertion_point(field_release:carbon.thinning.ConfigDefinition.row_3)
  
  ::carbon::thinning::Bounds* temp = row_3_;
  row_3_ = nullptr;
  return temp;
}
inline ::carbon::thinning::Bounds* ConfigDefinition::_internal_mutable_row_3() {
  
  if (row_3_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::thinning::Bounds>(GetArenaForAllocation());
    row_3_ = p;
  }
  return row_3_;
}
inline ::carbon::thinning::Bounds* ConfigDefinition::mutable_row_3() {
  ::carbon::thinning::Bounds* _msg = _internal_mutable_row_3();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.ConfigDefinition.row_3)
  return _msg;
}
inline void ConfigDefinition::set_allocated_row_3(::carbon::thinning::Bounds* row_3) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete row_3_;
  }
  if (row_3) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::Bounds>::GetOwningArena(row_3);
    if (message_arena != submessage_arena) {
      row_3 = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, row_3, submessage_arena);
    }
    
  } else {
    
  }
  row_3_ = row_3;
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.ConfigDefinition.row_3)
}

// .carbon.thinning.SizeFilter size_filter = 6 [deprecated = true];
inline bool ConfigDefinition::_internal_has_size_filter() const {
  return this != internal_default_instance() && size_filter_ != nullptr;
}
inline bool ConfigDefinition::has_size_filter() const {
  return _internal_has_size_filter();
}
inline void ConfigDefinition::clear_size_filter() {
  if (GetArenaForAllocation() == nullptr && size_filter_ != nullptr) {
    delete size_filter_;
  }
  size_filter_ = nullptr;
}
inline const ::carbon::thinning::SizeFilter& ConfigDefinition::_internal_size_filter() const {
  const ::carbon::thinning::SizeFilter* p = size_filter_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::thinning::SizeFilter&>(
      ::carbon::thinning::_SizeFilter_default_instance_);
}
inline const ::carbon::thinning::SizeFilter& ConfigDefinition::size_filter() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.ConfigDefinition.size_filter)
  return _internal_size_filter();
}
inline void ConfigDefinition::unsafe_arena_set_allocated_size_filter(
    ::carbon::thinning::SizeFilter* size_filter) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(size_filter_);
  }
  size_filter_ = size_filter;
  if (size_filter) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.thinning.ConfigDefinition.size_filter)
}
inline ::carbon::thinning::SizeFilter* ConfigDefinition::release_size_filter() {
  
  ::carbon::thinning::SizeFilter* temp = size_filter_;
  size_filter_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::thinning::SizeFilter* ConfigDefinition::unsafe_arena_release_size_filter() {
  // @@protoc_insertion_point(field_release:carbon.thinning.ConfigDefinition.size_filter)
  
  ::carbon::thinning::SizeFilter* temp = size_filter_;
  size_filter_ = nullptr;
  return temp;
}
inline ::carbon::thinning::SizeFilter* ConfigDefinition::_internal_mutable_size_filter() {
  
  if (size_filter_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::thinning::SizeFilter>(GetArenaForAllocation());
    size_filter_ = p;
  }
  return size_filter_;
}
inline ::carbon::thinning::SizeFilter* ConfigDefinition::mutable_size_filter() {
  ::carbon::thinning::SizeFilter* _msg = _internal_mutable_size_filter();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.ConfigDefinition.size_filter)
  return _msg;
}
inline void ConfigDefinition::set_allocated_size_filter(::carbon::thinning::SizeFilter* size_filter) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete size_filter_;
  }
  if (size_filter) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::SizeFilter>::GetOwningArena(size_filter);
    if (message_arena != submessage_arena) {
      size_filter = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, size_filter, submessage_arena);
    }
    
  } else {
    
  }
  size_filter_ = size_filter;
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.ConfigDefinition.size_filter)
}

// string id = 7;
inline void ConfigDefinition::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& ConfigDefinition::id() const {
  // @@protoc_insertion_point(field_get:carbon.thinning.ConfigDefinition.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigDefinition::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.thinning.ConfigDefinition.id)
}
inline std::string* ConfigDefinition::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.thinning.ConfigDefinition.id)
  return _s;
}
inline const std::string& ConfigDefinition::_internal_id() const {
  return id_.Get();
}
inline void ConfigDefinition::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigDefinition::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigDefinition::release_id() {
  // @@protoc_insertion_point(field_release:carbon.thinning.ConfigDefinition.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigDefinition::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.ConfigDefinition.id)
}

// map<int32, .carbon.thinning.Bounds> rows = 8;
inline int ConfigDefinition::_internal_rows_size() const {
  return rows_.size();
}
inline int ConfigDefinition::rows_size() const {
  return _internal_rows_size();
}
inline void ConfigDefinition::clear_rows() {
  rows_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >&
ConfigDefinition::_internal_rows() const {
  return rows_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >&
ConfigDefinition::rows() const {
  // @@protoc_insertion_point(field_map:carbon.thinning.ConfigDefinition.rows)
  return _internal_rows();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >*
ConfigDefinition::_internal_mutable_rows() {
  return rows_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >*
ConfigDefinition::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_map:carbon.thinning.ConfigDefinition.rows)
  return _internal_mutable_rows();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace thinning
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2fthinning_2fthinning_2eproto

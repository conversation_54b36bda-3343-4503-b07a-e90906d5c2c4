// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/thinning/thinning.proto

#include "proto/thinning/thinning.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace thinning {
constexpr Box::Box(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : width_(0)
  , height_(0){}
struct BoxDefaultTypeInternal {
  constexpr BoxDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BoxDefaultTypeInternal() {}
  union {
    Box _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BoxDefaultTypeInternal _Box_default_instance_;
constexpr DoubleBox::DoubleBox(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : box_1_(nullptr)
  , box_2_(nullptr)
  , ideal_y_dist_(0){}
struct DoubleBoxDefaultTypeInternal {
  constexpr DoubleBoxDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DoubleBoxDefaultTypeInternal() {}
  union {
    DoubleBox _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DoubleBoxDefaultTypeInternal _DoubleBox_default_instance_;
constexpr SizedNotSoGreedyCfg::SizedNotSoGreedyCfg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : min_keepout_(nullptr)
  , max_y_search_radius_(0)
  , ideal_y_dist_(0)
  , size_weight_(0)
  , dist_weight_(0){}
struct SizedNotSoGreedyCfgDefaultTypeInternal {
  constexpr SizedNotSoGreedyCfgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SizedNotSoGreedyCfgDefaultTypeInternal() {}
  union {
    SizedNotSoGreedyCfg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SizedNotSoGreedyCfgDefaultTypeInternal _SizedNotSoGreedyCfg_default_instance_;
constexpr Bounds::Bounds(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct BoundsDefaultTypeInternal {
  constexpr BoundsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BoundsDefaultTypeInternal() {}
  union {
    Bounds _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BoundsDefaultTypeInternal _Bounds_default_instance_;
constexpr SizeFilter::SizeFilter(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : samples_size_(uint64_t{0u})
  , acceptable_variance_(0)
  , enabled_(false){}
struct SizeFilterDefaultTypeInternal {
  constexpr SizeFilterDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SizeFilterDefaultTypeInternal() {}
  union {
    SizeFilter _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SizeFilterDefaultTypeInternal _SizeFilter_default_instance_;
constexpr ConfigDefinition_RowsEntry_DoNotUse::ConfigDefinition_RowsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct ConfigDefinition_RowsEntry_DoNotUseDefaultTypeInternal {
  constexpr ConfigDefinition_RowsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigDefinition_RowsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    ConfigDefinition_RowsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigDefinition_RowsEntry_DoNotUseDefaultTypeInternal _ConfigDefinition_RowsEntry_DoNotUse_default_instance_;
constexpr ConfigDefinition::ConfigDefinition(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : rows_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , row_1_(nullptr)
  , row_2_(nullptr)
  , row_3_(nullptr)
  , size_filter_(nullptr)
  , active_(false){}
struct ConfigDefinitionDefaultTypeInternal {
  constexpr ConfigDefinitionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigDefinitionDefaultTypeInternal() {}
  union {
    ConfigDefinition _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigDefinitionDefaultTypeInternal _ConfigDefinition_default_instance_;
}  // namespace thinning
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2fthinning_2fthinning_2eproto[7];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2fthinning_2fthinning_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2fthinning_2fthinning_2eproto = nullptr;

const uint32_t TableStruct_proto_2fthinning_2fthinning_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::Box, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::Box, width_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::Box, height_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::DoubleBox, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::DoubleBox, box_1_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::DoubleBox, box_2_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::DoubleBox, ideal_y_dist_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizedNotSoGreedyCfg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizedNotSoGreedyCfg, min_keepout_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizedNotSoGreedyCfg, max_y_search_radius_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizedNotSoGreedyCfg, ideal_y_dist_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizedNotSoGreedyCfg, size_weight_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizedNotSoGreedyCfg, dist_weight_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::Bounds, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::Bounds, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::Bounds, bounds_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizeFilter, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizeFilter, enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizeFilter, samples_size_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::SizeFilter, acceptable_variance_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition, active_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition, row_1_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition, row_2_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition, row_3_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition, size_filter_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::thinning::ConfigDefinition, rows_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::thinning::Box)},
  { 8, -1, -1, sizeof(::carbon::thinning::DoubleBox)},
  { 17, -1, -1, sizeof(::carbon::thinning::SizedNotSoGreedyCfg)},
  { 28, -1, -1, sizeof(::carbon::thinning::Bounds)},
  { 38, -1, -1, sizeof(::carbon::thinning::SizeFilter)},
  { 47, 55, -1, sizeof(::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse)},
  { 57, -1, -1, sizeof(::carbon::thinning::ConfigDefinition)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::thinning::_Box_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::thinning::_DoubleBox_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::thinning::_SizedNotSoGreedyCfg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::thinning::_Bounds_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::thinning::_SizeFilter_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::thinning::_ConfigDefinition_RowsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::thinning::_ConfigDefinition_default_instance_),
};

const char descriptor_table_protodef_proto_2fthinning_2fthinning_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035proto/thinning/thinning.proto\022\017carbon."
  "thinning\"$\n\003Box\022\r\n\005width\030\001 \001(\001\022\016\n\006height"
  "\030\002 \001(\001\"o\n\tDoubleBox\022#\n\005box_1\030\001 \001(\0132\024.car"
  "bon.thinning.Box\022#\n\005box_2\030\002 \001(\0132\024.carbon"
  ".thinning.Box\022\024\n\014ideal_y_dist\030\003 \001(\001:\002\030\001\""
  "\235\001\n\023SizedNotSoGreedyCfg\022)\n\013min_keepout\030\001"
  " \001(\0132\024.carbon.thinning.Box\022\033\n\023max_y_sear"
  "ch_radius\030\002 \001(\001\022\024\n\014ideal_y_dist\030\003 \001(\001\022\023\n"
  "\013size_weight\030\004 \001(\001\022\023\n\013dist_weight\030\005 \001(\001\""
  "\250\001\n\006Bounds\022#\n\003box\030\001 \001(\0132\024.carbon.thinnin"
  "g.BoxH\000\0224\n\ndouble_box\030\002 \001(\0132\032.carbon.thi"
  "nning.DoubleBoxB\002\030\001H\000\0229\n\tsized_cfg\030\003 \001(\013"
  "2$.carbon.thinning.SizedNotSoGreedyCfgH\000"
  "B\010\n\006bounds\"T\n\nSizeFilter\022\017\n\007enabled\030\001 \001("
  "\010\022\024\n\014samples_size\030\002 \001(\004\022\033\n\023acceptable_va"
  "riance\030\003 \001(\001:\002\030\001\"\357\002\n\020ConfigDefinition\022\014\n"
  "\004name\030\001 \001(\t\022\022\n\006active\030\002 \001(\010B\002\030\001\022&\n\005row_1"
  "\030\003 \001(\0132\027.carbon.thinning.Bounds\022&\n\005row_2"
  "\030\004 \001(\0132\027.carbon.thinning.Bounds\022&\n\005row_3"
  "\030\005 \001(\0132\027.carbon.thinning.Bounds\0224\n\013size_"
  "filter\030\006 \001(\0132\033.carbon.thinning.SizeFilte"
  "rB\002\030\001\022\n\n\002id\030\007 \001(\t\0229\n\004rows\030\010 \003(\0132+.carbon"
  ".thinning.ConfigDefinition.RowsEntry\032D\n\t"
  "RowsEntry\022\013\n\003key\030\001 \001(\005\022&\n\005value\030\002 \001(\0132\027."
  "carbon.thinning.Bounds:\0028\001B\020Z\016proto/thin"
  "ningb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2fthinning_2fthinning_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fthinning_2fthinning_2eproto = {
  false, false, 1012, descriptor_table_protodef_proto_2fthinning_2fthinning_2eproto, "proto/thinning/thinning.proto", 
  &descriptor_table_proto_2fthinning_2fthinning_2eproto_once, nullptr, 0, 7,
  schemas, file_default_instances, TableStruct_proto_2fthinning_2fthinning_2eproto::offsets,
  file_level_metadata_proto_2fthinning_2fthinning_2eproto, file_level_enum_descriptors_proto_2fthinning_2fthinning_2eproto, file_level_service_descriptors_proto_2fthinning_2fthinning_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2fthinning_2fthinning_2eproto_getter() {
  return &descriptor_table_proto_2fthinning_2fthinning_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2fthinning_2fthinning_2eproto(&descriptor_table_proto_2fthinning_2fthinning_2eproto);
namespace carbon {
namespace thinning {

// ===================================================================

class Box::_Internal {
 public:
};

Box::Box(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.thinning.Box)
}
Box::Box(const Box& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&width_, &from.width_,
    static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&width_)) + sizeof(height_));
  // @@protoc_insertion_point(copy_constructor:carbon.thinning.Box)
}

inline void Box::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&width_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&width_)) + sizeof(height_));
}

Box::~Box() {
  // @@protoc_insertion_point(destructor:carbon.thinning.Box)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Box::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Box::ArenaDtor(void* object) {
  Box* _this = reinterpret_cast< Box* >(object);
  (void)_this;
}
void Box::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Box::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Box::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.thinning.Box)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&width_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&height_) -
      reinterpret_cast<char*>(&width_)) + sizeof(height_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Box::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double width = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Box::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.thinning.Box)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double width = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_width = this->_internal_width();
  uint64_t raw_width;
  memcpy(&raw_width, &tmp_width, sizeof(tmp_width));
  if (raw_width != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_width(), target);
  }

  // double height = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_height = this->_internal_height();
  uint64_t raw_height;
  memcpy(&raw_height, &tmp_height, sizeof(tmp_height));
  if (raw_height != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_height(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.thinning.Box)
  return target;
}

size_t Box::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.thinning.Box)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double width = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_width = this->_internal_width();
  uint64_t raw_width;
  memcpy(&raw_width, &tmp_width, sizeof(tmp_width));
  if (raw_width != 0) {
    total_size += 1 + 8;
  }

  // double height = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_height = this->_internal_height();
  uint64_t raw_height;
  memcpy(&raw_height, &tmp_height, sizeof(tmp_height));
  if (raw_height != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Box::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Box::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Box::GetClassData() const { return &_class_data_; }

void Box::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Box *>(to)->MergeFrom(
      static_cast<const Box &>(from));
}


void Box::MergeFrom(const Box& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.thinning.Box)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_width = from._internal_width();
  uint64_t raw_width;
  memcpy(&raw_width, &tmp_width, sizeof(tmp_width));
  if (raw_width != 0) {
    _internal_set_width(from._internal_width());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_height = from._internal_height();
  uint64_t raw_height;
  memcpy(&raw_height, &tmp_height, sizeof(tmp_height));
  if (raw_height != 0) {
    _internal_set_height(from._internal_height());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Box::CopyFrom(const Box& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.thinning.Box)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Box::IsInitialized() const {
  return true;
}

void Box::InternalSwap(Box* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Box, height_)
      + sizeof(Box::height_)
      - PROTOBUF_FIELD_OFFSET(Box, width_)>(
          reinterpret_cast<char*>(&width_),
          reinterpret_cast<char*>(&other->width_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Box::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fthinning_2fthinning_2eproto_getter, &descriptor_table_proto_2fthinning_2fthinning_2eproto_once,
      file_level_metadata_proto_2fthinning_2fthinning_2eproto[0]);
}

// ===================================================================

class DoubleBox::_Internal {
 public:
  static const ::carbon::thinning::Box& box_1(const DoubleBox* msg);
  static const ::carbon::thinning::Box& box_2(const DoubleBox* msg);
};

const ::carbon::thinning::Box&
DoubleBox::_Internal::box_1(const DoubleBox* msg) {
  return *msg->box_1_;
}
const ::carbon::thinning::Box&
DoubleBox::_Internal::box_2(const DoubleBox* msg) {
  return *msg->box_2_;
}
DoubleBox::DoubleBox(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.thinning.DoubleBox)
}
DoubleBox::DoubleBox(const DoubleBox& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_box_1()) {
    box_1_ = new ::carbon::thinning::Box(*from.box_1_);
  } else {
    box_1_ = nullptr;
  }
  if (from._internal_has_box_2()) {
    box_2_ = new ::carbon::thinning::Box(*from.box_2_);
  } else {
    box_2_ = nullptr;
  }
  ideal_y_dist_ = from.ideal_y_dist_;
  // @@protoc_insertion_point(copy_constructor:carbon.thinning.DoubleBox)
}

inline void DoubleBox::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&box_1_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&ideal_y_dist_) -
    reinterpret_cast<char*>(&box_1_)) + sizeof(ideal_y_dist_));
}

DoubleBox::~DoubleBox() {
  // @@protoc_insertion_point(destructor:carbon.thinning.DoubleBox)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DoubleBox::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete box_1_;
  if (this != internal_default_instance()) delete box_2_;
}

void DoubleBox::ArenaDtor(void* object) {
  DoubleBox* _this = reinterpret_cast< DoubleBox* >(object);
  (void)_this;
}
void DoubleBox::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DoubleBox::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DoubleBox::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.thinning.DoubleBox)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && box_1_ != nullptr) {
    delete box_1_;
  }
  box_1_ = nullptr;
  if (GetArenaForAllocation() == nullptr && box_2_ != nullptr) {
    delete box_2_;
  }
  box_2_ = nullptr;
  ideal_y_dist_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DoubleBox::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.thinning.Box box_1 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_box_1(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.Box box_2 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_box_2(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double ideal_y_dist = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          ideal_y_dist_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DoubleBox::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.thinning.DoubleBox)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.thinning.Box box_1 = 1;
  if (this->_internal_has_box_1()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::box_1(this), target, stream);
  }

  // .carbon.thinning.Box box_2 = 2;
  if (this->_internal_has_box_2()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::box_2(this), target, stream);
  }

  // double ideal_y_dist = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ideal_y_dist = this->_internal_ideal_y_dist();
  uint64_t raw_ideal_y_dist;
  memcpy(&raw_ideal_y_dist, &tmp_ideal_y_dist, sizeof(tmp_ideal_y_dist));
  if (raw_ideal_y_dist != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_ideal_y_dist(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.thinning.DoubleBox)
  return target;
}

size_t DoubleBox::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.thinning.DoubleBox)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.thinning.Box box_1 = 1;
  if (this->_internal_has_box_1()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *box_1_);
  }

  // .carbon.thinning.Box box_2 = 2;
  if (this->_internal_has_box_2()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *box_2_);
  }

  // double ideal_y_dist = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ideal_y_dist = this->_internal_ideal_y_dist();
  uint64_t raw_ideal_y_dist;
  memcpy(&raw_ideal_y_dist, &tmp_ideal_y_dist, sizeof(tmp_ideal_y_dist));
  if (raw_ideal_y_dist != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DoubleBox::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DoubleBox::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DoubleBox::GetClassData() const { return &_class_data_; }

void DoubleBox::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DoubleBox *>(to)->MergeFrom(
      static_cast<const DoubleBox &>(from));
}


void DoubleBox::MergeFrom(const DoubleBox& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.thinning.DoubleBox)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_box_1()) {
    _internal_mutable_box_1()->::carbon::thinning::Box::MergeFrom(from._internal_box_1());
  }
  if (from._internal_has_box_2()) {
    _internal_mutable_box_2()->::carbon::thinning::Box::MergeFrom(from._internal_box_2());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ideal_y_dist = from._internal_ideal_y_dist();
  uint64_t raw_ideal_y_dist;
  memcpy(&raw_ideal_y_dist, &tmp_ideal_y_dist, sizeof(tmp_ideal_y_dist));
  if (raw_ideal_y_dist != 0) {
    _internal_set_ideal_y_dist(from._internal_ideal_y_dist());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DoubleBox::CopyFrom(const DoubleBox& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.thinning.DoubleBox)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoubleBox::IsInitialized() const {
  return true;
}

void DoubleBox::InternalSwap(DoubleBox* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DoubleBox, ideal_y_dist_)
      + sizeof(DoubleBox::ideal_y_dist_)
      - PROTOBUF_FIELD_OFFSET(DoubleBox, box_1_)>(
          reinterpret_cast<char*>(&box_1_),
          reinterpret_cast<char*>(&other->box_1_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DoubleBox::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fthinning_2fthinning_2eproto_getter, &descriptor_table_proto_2fthinning_2fthinning_2eproto_once,
      file_level_metadata_proto_2fthinning_2fthinning_2eproto[1]);
}

// ===================================================================

class SizedNotSoGreedyCfg::_Internal {
 public:
  static const ::carbon::thinning::Box& min_keepout(const SizedNotSoGreedyCfg* msg);
};

const ::carbon::thinning::Box&
SizedNotSoGreedyCfg::_Internal::min_keepout(const SizedNotSoGreedyCfg* msg) {
  return *msg->min_keepout_;
}
SizedNotSoGreedyCfg::SizedNotSoGreedyCfg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.thinning.SizedNotSoGreedyCfg)
}
SizedNotSoGreedyCfg::SizedNotSoGreedyCfg(const SizedNotSoGreedyCfg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_min_keepout()) {
    min_keepout_ = new ::carbon::thinning::Box(*from.min_keepout_);
  } else {
    min_keepout_ = nullptr;
  }
  ::memcpy(&max_y_search_radius_, &from.max_y_search_radius_,
    static_cast<size_t>(reinterpret_cast<char*>(&dist_weight_) -
    reinterpret_cast<char*>(&max_y_search_radius_)) + sizeof(dist_weight_));
  // @@protoc_insertion_point(copy_constructor:carbon.thinning.SizedNotSoGreedyCfg)
}

inline void SizedNotSoGreedyCfg::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&min_keepout_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&dist_weight_) -
    reinterpret_cast<char*>(&min_keepout_)) + sizeof(dist_weight_));
}

SizedNotSoGreedyCfg::~SizedNotSoGreedyCfg() {
  // @@protoc_insertion_point(destructor:carbon.thinning.SizedNotSoGreedyCfg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SizedNotSoGreedyCfg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete min_keepout_;
}

void SizedNotSoGreedyCfg::ArenaDtor(void* object) {
  SizedNotSoGreedyCfg* _this = reinterpret_cast< SizedNotSoGreedyCfg* >(object);
  (void)_this;
}
void SizedNotSoGreedyCfg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SizedNotSoGreedyCfg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SizedNotSoGreedyCfg::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.thinning.SizedNotSoGreedyCfg)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && min_keepout_ != nullptr) {
    delete min_keepout_;
  }
  min_keepout_ = nullptr;
  ::memset(&max_y_search_radius_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&dist_weight_) -
      reinterpret_cast<char*>(&max_y_search_radius_)) + sizeof(dist_weight_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SizedNotSoGreedyCfg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.thinning.Box min_keepout = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_min_keepout(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double max_y_search_radius = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          max_y_search_radius_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double ideal_y_dist = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          ideal_y_dist_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double size_weight = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          size_weight_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double dist_weight = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 41)) {
          dist_weight_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SizedNotSoGreedyCfg::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.thinning.SizedNotSoGreedyCfg)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.thinning.Box min_keepout = 1;
  if (this->_internal_has_min_keepout()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::min_keepout(this), target, stream);
  }

  // double max_y_search_radius = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_max_y_search_radius = this->_internal_max_y_search_radius();
  uint64_t raw_max_y_search_radius;
  memcpy(&raw_max_y_search_radius, &tmp_max_y_search_radius, sizeof(tmp_max_y_search_radius));
  if (raw_max_y_search_radius != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_max_y_search_radius(), target);
  }

  // double ideal_y_dist = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ideal_y_dist = this->_internal_ideal_y_dist();
  uint64_t raw_ideal_y_dist;
  memcpy(&raw_ideal_y_dist, &tmp_ideal_y_dist, sizeof(tmp_ideal_y_dist));
  if (raw_ideal_y_dist != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_ideal_y_dist(), target);
  }

  // double size_weight = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_size_weight = this->_internal_size_weight();
  uint64_t raw_size_weight;
  memcpy(&raw_size_weight, &tmp_size_weight, sizeof(tmp_size_weight));
  if (raw_size_weight != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_size_weight(), target);
  }

  // double dist_weight = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_dist_weight = this->_internal_dist_weight();
  uint64_t raw_dist_weight;
  memcpy(&raw_dist_weight, &tmp_dist_weight, sizeof(tmp_dist_weight));
  if (raw_dist_weight != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(5, this->_internal_dist_weight(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.thinning.SizedNotSoGreedyCfg)
  return target;
}

size_t SizedNotSoGreedyCfg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.thinning.SizedNotSoGreedyCfg)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.thinning.Box min_keepout = 1;
  if (this->_internal_has_min_keepout()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *min_keepout_);
  }

  // double max_y_search_radius = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_max_y_search_radius = this->_internal_max_y_search_radius();
  uint64_t raw_max_y_search_radius;
  memcpy(&raw_max_y_search_radius, &tmp_max_y_search_radius, sizeof(tmp_max_y_search_radius));
  if (raw_max_y_search_radius != 0) {
    total_size += 1 + 8;
  }

  // double ideal_y_dist = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ideal_y_dist = this->_internal_ideal_y_dist();
  uint64_t raw_ideal_y_dist;
  memcpy(&raw_ideal_y_dist, &tmp_ideal_y_dist, sizeof(tmp_ideal_y_dist));
  if (raw_ideal_y_dist != 0) {
    total_size += 1 + 8;
  }

  // double size_weight = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_size_weight = this->_internal_size_weight();
  uint64_t raw_size_weight;
  memcpy(&raw_size_weight, &tmp_size_weight, sizeof(tmp_size_weight));
  if (raw_size_weight != 0) {
    total_size += 1 + 8;
  }

  // double dist_weight = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_dist_weight = this->_internal_dist_weight();
  uint64_t raw_dist_weight;
  memcpy(&raw_dist_weight, &tmp_dist_weight, sizeof(tmp_dist_weight));
  if (raw_dist_weight != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SizedNotSoGreedyCfg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SizedNotSoGreedyCfg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SizedNotSoGreedyCfg::GetClassData() const { return &_class_data_; }

void SizedNotSoGreedyCfg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SizedNotSoGreedyCfg *>(to)->MergeFrom(
      static_cast<const SizedNotSoGreedyCfg &>(from));
}


void SizedNotSoGreedyCfg::MergeFrom(const SizedNotSoGreedyCfg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.thinning.SizedNotSoGreedyCfg)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_min_keepout()) {
    _internal_mutable_min_keepout()->::carbon::thinning::Box::MergeFrom(from._internal_min_keepout());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_max_y_search_radius = from._internal_max_y_search_radius();
  uint64_t raw_max_y_search_radius;
  memcpy(&raw_max_y_search_radius, &tmp_max_y_search_radius, sizeof(tmp_max_y_search_radius));
  if (raw_max_y_search_radius != 0) {
    _internal_set_max_y_search_radius(from._internal_max_y_search_radius());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ideal_y_dist = from._internal_ideal_y_dist();
  uint64_t raw_ideal_y_dist;
  memcpy(&raw_ideal_y_dist, &tmp_ideal_y_dist, sizeof(tmp_ideal_y_dist));
  if (raw_ideal_y_dist != 0) {
    _internal_set_ideal_y_dist(from._internal_ideal_y_dist());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_size_weight = from._internal_size_weight();
  uint64_t raw_size_weight;
  memcpy(&raw_size_weight, &tmp_size_weight, sizeof(tmp_size_weight));
  if (raw_size_weight != 0) {
    _internal_set_size_weight(from._internal_size_weight());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_dist_weight = from._internal_dist_weight();
  uint64_t raw_dist_weight;
  memcpy(&raw_dist_weight, &tmp_dist_weight, sizeof(tmp_dist_weight));
  if (raw_dist_weight != 0) {
    _internal_set_dist_weight(from._internal_dist_weight());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SizedNotSoGreedyCfg::CopyFrom(const SizedNotSoGreedyCfg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.thinning.SizedNotSoGreedyCfg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SizedNotSoGreedyCfg::IsInitialized() const {
  return true;
}

void SizedNotSoGreedyCfg::InternalSwap(SizedNotSoGreedyCfg* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SizedNotSoGreedyCfg, dist_weight_)
      + sizeof(SizedNotSoGreedyCfg::dist_weight_)
      - PROTOBUF_FIELD_OFFSET(SizedNotSoGreedyCfg, min_keepout_)>(
          reinterpret_cast<char*>(&min_keepout_),
          reinterpret_cast<char*>(&other->min_keepout_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SizedNotSoGreedyCfg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fthinning_2fthinning_2eproto_getter, &descriptor_table_proto_2fthinning_2fthinning_2eproto_once,
      file_level_metadata_proto_2fthinning_2fthinning_2eproto[2]);
}

// ===================================================================

class Bounds::_Internal {
 public:
  static const ::carbon::thinning::Box& box(const Bounds* msg);
  static const ::carbon::thinning::DoubleBox& double_box(const Bounds* msg);
  static const ::carbon::thinning::SizedNotSoGreedyCfg& sized_cfg(const Bounds* msg);
};

const ::carbon::thinning::Box&
Bounds::_Internal::box(const Bounds* msg) {
  return *msg->bounds_.box_;
}
const ::carbon::thinning::DoubleBox&
Bounds::_Internal::double_box(const Bounds* msg) {
  return *msg->bounds_.double_box_;
}
const ::carbon::thinning::SizedNotSoGreedyCfg&
Bounds::_Internal::sized_cfg(const Bounds* msg) {
  return *msg->bounds_.sized_cfg_;
}
void Bounds::set_allocated_box(::carbon::thinning::Box* box) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_bounds();
  if (box) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::Box>::GetOwningArena(box);
    if (message_arena != submessage_arena) {
      box = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, box, submessage_arena);
    }
    set_has_box();
    bounds_.box_ = box;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.Bounds.box)
}
void Bounds::set_allocated_double_box(::carbon::thinning::DoubleBox* double_box) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_bounds();
  if (double_box) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::DoubleBox>::GetOwningArena(double_box);
    if (message_arena != submessage_arena) {
      double_box = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, double_box, submessage_arena);
    }
    set_has_double_box();
    bounds_.double_box_ = double_box;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.Bounds.double_box)
}
void Bounds::set_allocated_sized_cfg(::carbon::thinning::SizedNotSoGreedyCfg* sized_cfg) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_bounds();
  if (sized_cfg) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::thinning::SizedNotSoGreedyCfg>::GetOwningArena(sized_cfg);
    if (message_arena != submessage_arena) {
      sized_cfg = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sized_cfg, submessage_arena);
    }
    set_has_sized_cfg();
    bounds_.sized_cfg_ = sized_cfg;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.thinning.Bounds.sized_cfg)
}
Bounds::Bounds(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.thinning.Bounds)
}
Bounds::Bounds(const Bounds& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_bounds();
  switch (from.bounds_case()) {
    case kBox: {
      _internal_mutable_box()->::carbon::thinning::Box::MergeFrom(from._internal_box());
      break;
    }
    case kDoubleBox: {
      _internal_mutable_double_box()->::carbon::thinning::DoubleBox::MergeFrom(from._internal_double_box());
      break;
    }
    case kSizedCfg: {
      _internal_mutable_sized_cfg()->::carbon::thinning::SizedNotSoGreedyCfg::MergeFrom(from._internal_sized_cfg());
      break;
    }
    case BOUNDS_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.thinning.Bounds)
}

inline void Bounds::SharedCtor() {
clear_has_bounds();
}

Bounds::~Bounds() {
  // @@protoc_insertion_point(destructor:carbon.thinning.Bounds)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Bounds::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_bounds()) {
    clear_bounds();
  }
}

void Bounds::ArenaDtor(void* object) {
  Bounds* _this = reinterpret_cast< Bounds* >(object);
  (void)_this;
}
void Bounds::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Bounds::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Bounds::clear_bounds() {
// @@protoc_insertion_point(one_of_clear_start:carbon.thinning.Bounds)
  switch (bounds_case()) {
    case kBox: {
      if (GetArenaForAllocation() == nullptr) {
        delete bounds_.box_;
      }
      break;
    }
    case kDoubleBox: {
      if (GetArenaForAllocation() == nullptr) {
        delete bounds_.double_box_;
      }
      break;
    }
    case kSizedCfg: {
      if (GetArenaForAllocation() == nullptr) {
        delete bounds_.sized_cfg_;
      }
      break;
    }
    case BOUNDS_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = BOUNDS_NOT_SET;
}


void Bounds::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.thinning.Bounds)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_bounds();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Bounds::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.thinning.Box box = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_box(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.DoubleBox double_box = 2 [deprecated = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_double_box(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.SizedNotSoGreedyCfg sized_cfg = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_sized_cfg(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Bounds::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.thinning.Bounds)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.thinning.Box box = 1;
  if (_internal_has_box()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::box(this), target, stream);
  }

  // .carbon.thinning.DoubleBox double_box = 2 [deprecated = true];
  if (_internal_has_double_box()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::double_box(this), target, stream);
  }

  // .carbon.thinning.SizedNotSoGreedyCfg sized_cfg = 3;
  if (_internal_has_sized_cfg()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::sized_cfg(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.thinning.Bounds)
  return target;
}

size_t Bounds::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.thinning.Bounds)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (bounds_case()) {
    // .carbon.thinning.Box box = 1;
    case kBox: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *bounds_.box_);
      break;
    }
    // .carbon.thinning.DoubleBox double_box = 2 [deprecated = true];
    case kDoubleBox: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *bounds_.double_box_);
      break;
    }
    // .carbon.thinning.SizedNotSoGreedyCfg sized_cfg = 3;
    case kSizedCfg: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *bounds_.sized_cfg_);
      break;
    }
    case BOUNDS_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Bounds::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Bounds::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Bounds::GetClassData() const { return &_class_data_; }

void Bounds::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Bounds *>(to)->MergeFrom(
      static_cast<const Bounds &>(from));
}


void Bounds::MergeFrom(const Bounds& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.thinning.Bounds)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.bounds_case()) {
    case kBox: {
      _internal_mutable_box()->::carbon::thinning::Box::MergeFrom(from._internal_box());
      break;
    }
    case kDoubleBox: {
      _internal_mutable_double_box()->::carbon::thinning::DoubleBox::MergeFrom(from._internal_double_box());
      break;
    }
    case kSizedCfg: {
      _internal_mutable_sized_cfg()->::carbon::thinning::SizedNotSoGreedyCfg::MergeFrom(from._internal_sized_cfg());
      break;
    }
    case BOUNDS_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Bounds::CopyFrom(const Bounds& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.thinning.Bounds)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Bounds::IsInitialized() const {
  return true;
}

void Bounds::InternalSwap(Bounds* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(bounds_, other->bounds_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata Bounds::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fthinning_2fthinning_2eproto_getter, &descriptor_table_proto_2fthinning_2fthinning_2eproto_once,
      file_level_metadata_proto_2fthinning_2fthinning_2eproto[3]);
}

// ===================================================================

class SizeFilter::_Internal {
 public:
};

SizeFilter::SizeFilter(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.thinning.SizeFilter)
}
SizeFilter::SizeFilter(const SizeFilter& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&samples_size_, &from.samples_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&enabled_) -
    reinterpret_cast<char*>(&samples_size_)) + sizeof(enabled_));
  // @@protoc_insertion_point(copy_constructor:carbon.thinning.SizeFilter)
}

inline void SizeFilter::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&samples_size_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&enabled_) -
    reinterpret_cast<char*>(&samples_size_)) + sizeof(enabled_));
}

SizeFilter::~SizeFilter() {
  // @@protoc_insertion_point(destructor:carbon.thinning.SizeFilter)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SizeFilter::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SizeFilter::ArenaDtor(void* object) {
  SizeFilter* _this = reinterpret_cast< SizeFilter* >(object);
  (void)_this;
}
void SizeFilter::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SizeFilter::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SizeFilter::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.thinning.SizeFilter)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&samples_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&enabled_) -
      reinterpret_cast<char*>(&samples_size_)) + sizeof(enabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SizeFilter::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 samples_size = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          samples_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double acceptable_variance = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          acceptable_variance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SizeFilter::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.thinning.SizeFilter)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enabled(), target);
  }

  // uint64 samples_size = 2;
  if (this->_internal_samples_size() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_samples_size(), target);
  }

  // double acceptable_variance = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_acceptable_variance = this->_internal_acceptable_variance();
  uint64_t raw_acceptable_variance;
  memcpy(&raw_acceptable_variance, &tmp_acceptable_variance, sizeof(tmp_acceptable_variance));
  if (raw_acceptable_variance != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_acceptable_variance(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.thinning.SizeFilter)
  return target;
}

size_t SizeFilter::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.thinning.SizeFilter)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 samples_size = 2;
  if (this->_internal_samples_size() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_samples_size());
  }

  // double acceptable_variance = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_acceptable_variance = this->_internal_acceptable_variance();
  uint64_t raw_acceptable_variance;
  memcpy(&raw_acceptable_variance, &tmp_acceptable_variance, sizeof(tmp_acceptable_variance));
  if (raw_acceptable_variance != 0) {
    total_size += 1 + 8;
  }

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SizeFilter::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SizeFilter::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SizeFilter::GetClassData() const { return &_class_data_; }

void SizeFilter::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SizeFilter *>(to)->MergeFrom(
      static_cast<const SizeFilter &>(from));
}


void SizeFilter::MergeFrom(const SizeFilter& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.thinning.SizeFilter)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_samples_size() != 0) {
    _internal_set_samples_size(from._internal_samples_size());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_acceptable_variance = from._internal_acceptable_variance();
  uint64_t raw_acceptable_variance;
  memcpy(&raw_acceptable_variance, &tmp_acceptable_variance, sizeof(tmp_acceptable_variance));
  if (raw_acceptable_variance != 0) {
    _internal_set_acceptable_variance(from._internal_acceptable_variance());
  }
  if (from._internal_enabled() != 0) {
    _internal_set_enabled(from._internal_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SizeFilter::CopyFrom(const SizeFilter& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.thinning.SizeFilter)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SizeFilter::IsInitialized() const {
  return true;
}

void SizeFilter::InternalSwap(SizeFilter* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SizeFilter, enabled_)
      + sizeof(SizeFilter::enabled_)
      - PROTOBUF_FIELD_OFFSET(SizeFilter, samples_size_)>(
          reinterpret_cast<char*>(&samples_size_),
          reinterpret_cast<char*>(&other->samples_size_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SizeFilter::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fthinning_2fthinning_2eproto_getter, &descriptor_table_proto_2fthinning_2fthinning_2eproto_once,
      file_level_metadata_proto_2fthinning_2fthinning_2eproto[4]);
}

// ===================================================================

ConfigDefinition_RowsEntry_DoNotUse::ConfigDefinition_RowsEntry_DoNotUse() {}
ConfigDefinition_RowsEntry_DoNotUse::ConfigDefinition_RowsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void ConfigDefinition_RowsEntry_DoNotUse::MergeFrom(const ConfigDefinition_RowsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata ConfigDefinition_RowsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fthinning_2fthinning_2eproto_getter, &descriptor_table_proto_2fthinning_2fthinning_2eproto_once,
      file_level_metadata_proto_2fthinning_2fthinning_2eproto[5]);
}

// ===================================================================

class ConfigDefinition::_Internal {
 public:
  static const ::carbon::thinning::Bounds& row_1(const ConfigDefinition* msg);
  static const ::carbon::thinning::Bounds& row_2(const ConfigDefinition* msg);
  static const ::carbon::thinning::Bounds& row_3(const ConfigDefinition* msg);
  static const ::carbon::thinning::SizeFilter& size_filter(const ConfigDefinition* msg);
};

const ::carbon::thinning::Bounds&
ConfigDefinition::_Internal::row_1(const ConfigDefinition* msg) {
  return *msg->row_1_;
}
const ::carbon::thinning::Bounds&
ConfigDefinition::_Internal::row_2(const ConfigDefinition* msg) {
  return *msg->row_2_;
}
const ::carbon::thinning::Bounds&
ConfigDefinition::_Internal::row_3(const ConfigDefinition* msg) {
  return *msg->row_3_;
}
const ::carbon::thinning::SizeFilter&
ConfigDefinition::_Internal::size_filter(const ConfigDefinition* msg) {
  return *msg->size_filter_;
}
ConfigDefinition::ConfigDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  rows_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.thinning.ConfigDefinition)
}
ConfigDefinition::ConfigDefinition(const ConfigDefinition& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  rows_.MergeFrom(from.rows_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_row_1()) {
    row_1_ = new ::carbon::thinning::Bounds(*from.row_1_);
  } else {
    row_1_ = nullptr;
  }
  if (from._internal_has_row_2()) {
    row_2_ = new ::carbon::thinning::Bounds(*from.row_2_);
  } else {
    row_2_ = nullptr;
  }
  if (from._internal_has_row_3()) {
    row_3_ = new ::carbon::thinning::Bounds(*from.row_3_);
  } else {
    row_3_ = nullptr;
  }
  if (from._internal_has_size_filter()) {
    size_filter_ = new ::carbon::thinning::SizeFilter(*from.size_filter_);
  } else {
    size_filter_ = nullptr;
  }
  active_ = from.active_;
  // @@protoc_insertion_point(copy_constructor:carbon.thinning.ConfigDefinition)
}

inline void ConfigDefinition::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&row_1_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&active_) -
    reinterpret_cast<char*>(&row_1_)) + sizeof(active_));
}

ConfigDefinition::~ConfigDefinition() {
  // @@protoc_insertion_point(destructor:carbon.thinning.ConfigDefinition)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ConfigDefinition::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete row_1_;
  if (this != internal_default_instance()) delete row_2_;
  if (this != internal_default_instance()) delete row_3_;
  if (this != internal_default_instance()) delete size_filter_;
}

void ConfigDefinition::ArenaDtor(void* object) {
  ConfigDefinition* _this = reinterpret_cast< ConfigDefinition* >(object);
  (void)_this;
  _this->rows_. ~MapField();
}
inline void ConfigDefinition::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &ConfigDefinition::ArenaDtor);
  }
}
void ConfigDefinition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ConfigDefinition::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.thinning.ConfigDefinition)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rows_.Clear();
  name_.ClearToEmpty();
  id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && row_1_ != nullptr) {
    delete row_1_;
  }
  row_1_ = nullptr;
  if (GetArenaForAllocation() == nullptr && row_2_ != nullptr) {
    delete row_2_;
  }
  row_2_ = nullptr;
  if (GetArenaForAllocation() == nullptr && row_3_ != nullptr) {
    delete row_3_;
  }
  row_3_ = nullptr;
  if (GetArenaForAllocation() == nullptr && size_filter_ != nullptr) {
    delete size_filter_;
  }
  size_filter_ = nullptr;
  active_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigDefinition::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.thinning.ConfigDefinition.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool active = 2 [deprecated = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          active_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.Bounds row_1 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_row_1(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.Bounds row_2 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_row_2(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.Bounds row_3 = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_row_3(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.thinning.SizeFilter size_filter = 6 [deprecated = true];
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_size_filter(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string id = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.thinning.ConfigDefinition.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<int32, .carbon.thinning.Bounds> rows = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&rows_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigDefinition::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.thinning.ConfigDefinition)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.thinning.ConfigDefinition.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // bool active = 2 [deprecated = true];
  if (this->_internal_active() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_active(), target);
  }

  // .carbon.thinning.Bounds row_1 = 3;
  if (this->_internal_has_row_1()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::row_1(this), target, stream);
  }

  // .carbon.thinning.Bounds row_2 = 4;
  if (this->_internal_has_row_2()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::row_2(this), target, stream);
  }

  // .carbon.thinning.Bounds row_3 = 5;
  if (this->_internal_has_row_3()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::row_3(this), target, stream);
  }

  // .carbon.thinning.SizeFilter size_filter = 6 [deprecated = true];
  if (this->_internal_has_size_filter()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::size_filter(this), target, stream);
  }

  // string id = 7;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.thinning.ConfigDefinition.id");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_id(), target);
  }

  // map<int32, .carbon.thinning.Bounds> rows = 8;
  if (!this->_internal_rows().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_rows().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_rows().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >::const_iterator
          it = this->_internal_rows().begin();
          it != this->_internal_rows().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = ConfigDefinition_RowsEntry_DoNotUse::Funcs::InternalSerialize(8, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >::const_iterator
          it = this->_internal_rows().begin();
          it != this->_internal_rows().end(); ++it) {
        target = ConfigDefinition_RowsEntry_DoNotUse::Funcs::InternalSerialize(8, it->first, it->second, target, stream);
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.thinning.ConfigDefinition)
  return target;
}

size_t ConfigDefinition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.thinning.ConfigDefinition)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<int32, .carbon.thinning.Bounds> rows = 8;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_rows_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::thinning::Bounds >::const_iterator
      it = this->_internal_rows().begin();
      it != this->_internal_rows().end(); ++it) {
    total_size += ConfigDefinition_RowsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string id = 7;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // .carbon.thinning.Bounds row_1 = 3;
  if (this->_internal_has_row_1()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *row_1_);
  }

  // .carbon.thinning.Bounds row_2 = 4;
  if (this->_internal_has_row_2()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *row_2_);
  }

  // .carbon.thinning.Bounds row_3 = 5;
  if (this->_internal_has_row_3()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *row_3_);
  }

  // .carbon.thinning.SizeFilter size_filter = 6 [deprecated = true];
  if (this->_internal_has_size_filter()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *size_filter_);
  }

  // bool active = 2 [deprecated = true];
  if (this->_internal_active() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigDefinition::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ConfigDefinition::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigDefinition::GetClassData() const { return &_class_data_; }

void ConfigDefinition::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ConfigDefinition *>(to)->MergeFrom(
      static_cast<const ConfigDefinition &>(from));
}


void ConfigDefinition::MergeFrom(const ConfigDefinition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.thinning.ConfigDefinition)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  rows_.MergeFrom(from.rows_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_has_row_1()) {
    _internal_mutable_row_1()->::carbon::thinning::Bounds::MergeFrom(from._internal_row_1());
  }
  if (from._internal_has_row_2()) {
    _internal_mutable_row_2()->::carbon::thinning::Bounds::MergeFrom(from._internal_row_2());
  }
  if (from._internal_has_row_3()) {
    _internal_mutable_row_3()->::carbon::thinning::Bounds::MergeFrom(from._internal_row_3());
  }
  if (from._internal_has_size_filter()) {
    _internal_mutable_size_filter()->::carbon::thinning::SizeFilter::MergeFrom(from._internal_size_filter());
  }
  if (from._internal_active() != 0) {
    _internal_set_active(from._internal_active());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigDefinition::CopyFrom(const ConfigDefinition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.thinning.ConfigDefinition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigDefinition::IsInitialized() const {
  return true;
}

void ConfigDefinition::InternalSwap(ConfigDefinition* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  rows_.InternalSwap(&other->rows_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ConfigDefinition, active_)
      + sizeof(ConfigDefinition::active_)
      - PROTOBUF_FIELD_OFFSET(ConfigDefinition, row_1_)>(
          reinterpret_cast<char*>(&row_1_),
          reinterpret_cast<char*>(&other->row_1_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigDefinition::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fthinning_2fthinning_2eproto_getter, &descriptor_table_proto_2fthinning_2fthinning_2eproto_once,
      file_level_metadata_proto_2fthinning_2fthinning_2eproto[6]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace thinning
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::thinning::Box* Arena::CreateMaybeMessage< ::carbon::thinning::Box >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::thinning::Box >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::thinning::DoubleBox* Arena::CreateMaybeMessage< ::carbon::thinning::DoubleBox >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::thinning::DoubleBox >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::thinning::SizedNotSoGreedyCfg* Arena::CreateMaybeMessage< ::carbon::thinning::SizedNotSoGreedyCfg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::thinning::SizedNotSoGreedyCfg >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::thinning::Bounds* Arena::CreateMaybeMessage< ::carbon::thinning::Bounds >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::thinning::Bounds >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::thinning::SizeFilter* Arena::CreateMaybeMessage< ::carbon::thinning::SizeFilter >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::thinning::SizeFilter >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::thinning::ConfigDefinition_RowsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::thinning::ConfigDefinition* Arena::CreateMaybeMessage< ::carbon::thinning::ConfigDefinition >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::thinning::ConfigDefinition >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Box(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    width: builtin___float = ...
    height: builtin___float = ...

    def __init__(self,
        *,
        width : typing___Optional[builtin___float] = None,
        height : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"height",b"height",u"width",b"width"]) -> None: ...
type___Box = Box

class DoubleBox(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ideal_y_dist: builtin___float = ...

    @property
    def box_1(self) -> type___Box: ...

    @property
    def box_2(self) -> type___Box: ...

    def __init__(self,
        *,
        box_1 : typing___Optional[type___Box] = None,
        box_2 : typing___Optional[type___Box] = None,
        ideal_y_dist : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"box_1",b"box_1",u"box_2",b"box_2"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"box_1",b"box_1",u"box_2",b"box_2",u"ideal_y_dist",b"ideal_y_dist"]) -> None: ...
type___DoubleBox = DoubleBox

class SizedNotSoGreedyCfg(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    max_y_search_radius: builtin___float = ...
    ideal_y_dist: builtin___float = ...
    size_weight: builtin___float = ...
    dist_weight: builtin___float = ...

    @property
    def min_keepout(self) -> type___Box: ...

    def __init__(self,
        *,
        min_keepout : typing___Optional[type___Box] = None,
        max_y_search_radius : typing___Optional[builtin___float] = None,
        ideal_y_dist : typing___Optional[builtin___float] = None,
        size_weight : typing___Optional[builtin___float] = None,
        dist_weight : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"min_keepout",b"min_keepout"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"dist_weight",b"dist_weight",u"ideal_y_dist",b"ideal_y_dist",u"max_y_search_radius",b"max_y_search_radius",u"min_keepout",b"min_keepout",u"size_weight",b"size_weight"]) -> None: ...
type___SizedNotSoGreedyCfg = SizedNotSoGreedyCfg

class Bounds(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def box(self) -> type___Box: ...

    @property
    def double_box(self) -> type___DoubleBox: ...

    @property
    def sized_cfg(self) -> type___SizedNotSoGreedyCfg: ...

    def __init__(self,
        *,
        box : typing___Optional[type___Box] = None,
        double_box : typing___Optional[type___DoubleBox] = None,
        sized_cfg : typing___Optional[type___SizedNotSoGreedyCfg] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bounds",b"bounds",u"box",b"box",u"double_box",b"double_box",u"sized_cfg",b"sized_cfg"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bounds",b"bounds",u"box",b"box",u"double_box",b"double_box",u"sized_cfg",b"sized_cfg"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"bounds",b"bounds"]) -> typing_extensions___Literal["box","double_box","sized_cfg"]: ...
type___Bounds = Bounds

class SizeFilter(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...
    samples_size: builtin___int = ...
    acceptable_variance: builtin___float = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        samples_size : typing___Optional[builtin___int] = None,
        acceptable_variance : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"acceptable_variance",b"acceptable_variance",u"enabled",b"enabled",u"samples_size",b"samples_size"]) -> None: ...
type___SizeFilter = SizeFilter

class ConfigDefinition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class RowsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___Bounds: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___Bounds] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___RowsEntry = RowsEntry

    name: typing___Text = ...
    active: builtin___bool = ...
    id: typing___Text = ...

    @property
    def row_1(self) -> type___Bounds: ...

    @property
    def row_2(self) -> type___Bounds: ...

    @property
    def row_3(self) -> type___Bounds: ...

    @property
    def size_filter(self) -> type___SizeFilter: ...

    @property
    def rows(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___Bounds]: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        active : typing___Optional[builtin___bool] = None,
        row_1 : typing___Optional[type___Bounds] = None,
        row_2 : typing___Optional[type___Bounds] = None,
        row_3 : typing___Optional[type___Bounds] = None,
        size_filter : typing___Optional[type___SizeFilter] = None,
        id : typing___Optional[typing___Text] = None,
        rows : typing___Optional[typing___Mapping[builtin___int, type___Bounds]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"row_1",b"row_1",u"row_2",b"row_2",u"row_3",b"row_3",u"size_filter",b"size_filter"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active",b"active",u"id",b"id",u"name",b"name",u"row_1",b"row_1",u"row_2",b"row_2",u"row_3",b"row_3",u"rows",b"rows",u"size_filter",b"size_filter"]) -> None: ...
type___ConfigDefinition = ConfigDefinition

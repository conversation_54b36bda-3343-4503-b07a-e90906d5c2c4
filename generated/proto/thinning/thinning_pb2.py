# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/thinning/thinning.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/thinning/thinning.proto',
  package='carbon.thinning',
  syntax='proto3',
  serialized_options=b'Z\016proto/thinning',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1dproto/thinning/thinning.proto\x12\x0f\x63\x61rbon.thinning\"$\n\x03\x42ox\x12\r\n\x05width\x18\x01 \x01(\x01\x12\x0e\n\x06height\x18\x02 \x01(\x01\"o\n\tDoubleBox\x12#\n\x05\x62ox_1\x18\x01 \x01(\x0b\x32\x14.carbon.thinning.Box\x12#\n\x05\x62ox_2\x18\x02 \x01(\x0b\x32\x14.carbon.thinning.Box\x12\x14\n\x0cideal_y_dist\x18\x03 \x01(\x01:\x02\x18\x01\"\x9d\x01\n\x13SizedNotSoGreedyCfg\x12)\n\x0bmin_keepout\x18\x01 \x01(\x0b\x32\x14.carbon.thinning.Box\x12\x1b\n\x13max_y_search_radius\x18\x02 \x01(\x01\x12\x14\n\x0cideal_y_dist\x18\x03 \x01(\x01\x12\x13\n\x0bsize_weight\x18\x04 \x01(\x01\x12\x13\n\x0b\x64ist_weight\x18\x05 \x01(\x01\"\xa8\x01\n\x06\x42ounds\x12#\n\x03\x62ox\x18\x01 \x01(\x0b\x32\x14.carbon.thinning.BoxH\x00\x12\x34\n\ndouble_box\x18\x02 \x01(\x0b\x32\x1a.carbon.thinning.DoubleBoxB\x02\x18\x01H\x00\x12\x39\n\tsized_cfg\x18\x03 \x01(\x0b\x32$.carbon.thinning.SizedNotSoGreedyCfgH\x00\x42\x08\n\x06\x62ounds\"T\n\nSizeFilter\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x14\n\x0csamples_size\x18\x02 \x01(\x04\x12\x1b\n\x13\x61\x63\x63\x65ptable_variance\x18\x03 \x01(\x01:\x02\x18\x01\"\xef\x02\n\x10\x43onfigDefinition\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\x06\x61\x63tive\x18\x02 \x01(\x08\x42\x02\x18\x01\x12&\n\x05row_1\x18\x03 \x01(\x0b\x32\x17.carbon.thinning.Bounds\x12&\n\x05row_2\x18\x04 \x01(\x0b\x32\x17.carbon.thinning.Bounds\x12&\n\x05row_3\x18\x05 \x01(\x0b\x32\x17.carbon.thinning.Bounds\x12\x34\n\x0bsize_filter\x18\x06 \x01(\x0b\x32\x1b.carbon.thinning.SizeFilterB\x02\x18\x01\x12\n\n\x02id\x18\x07 \x01(\t\x12\x39\n\x04rows\x18\x08 \x03(\x0b\x32+.carbon.thinning.ConfigDefinition.RowsEntry\x1a\x44\n\tRowsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12&\n\x05value\x18\x02 \x01(\x0b\x32\x17.carbon.thinning.Bounds:\x02\x38\x01\x42\x10Z\x0eproto/thinningb\x06proto3'
)




_BOX = _descriptor.Descriptor(
  name='Box',
  full_name='carbon.thinning.Box',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='width', full_name='carbon.thinning.Box.width', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='carbon.thinning.Box.height', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=50,
  serialized_end=86,
)


_DOUBLEBOX = _descriptor.Descriptor(
  name='DoubleBox',
  full_name='carbon.thinning.DoubleBox',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='box_1', full_name='carbon.thinning.DoubleBox.box_1', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='box_2', full_name='carbon.thinning.DoubleBox.box_2', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ideal_y_dist', full_name='carbon.thinning.DoubleBox.ideal_y_dist', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=88,
  serialized_end=199,
)


_SIZEDNOTSOGREEDYCFG = _descriptor.Descriptor(
  name='SizedNotSoGreedyCfg',
  full_name='carbon.thinning.SizedNotSoGreedyCfg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min_keepout', full_name='carbon.thinning.SizedNotSoGreedyCfg.min_keepout', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_y_search_radius', full_name='carbon.thinning.SizedNotSoGreedyCfg.max_y_search_radius', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ideal_y_dist', full_name='carbon.thinning.SizedNotSoGreedyCfg.ideal_y_dist', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size_weight', full_name='carbon.thinning.SizedNotSoGreedyCfg.size_weight', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dist_weight', full_name='carbon.thinning.SizedNotSoGreedyCfg.dist_weight', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=202,
  serialized_end=359,
)


_BOUNDS = _descriptor.Descriptor(
  name='Bounds',
  full_name='carbon.thinning.Bounds',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='box', full_name='carbon.thinning.Bounds.box', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='double_box', full_name='carbon.thinning.Bounds.double_box', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sized_cfg', full_name='carbon.thinning.Bounds.sized_cfg', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='bounds', full_name='carbon.thinning.Bounds.bounds',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=362,
  serialized_end=530,
)


_SIZEFILTER = _descriptor.Descriptor(
  name='SizeFilter',
  full_name='carbon.thinning.SizeFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.thinning.SizeFilter.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='samples_size', full_name='carbon.thinning.SizeFilter.samples_size', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='acceptable_variance', full_name='carbon.thinning.SizeFilter.acceptable_variance', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'\030\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=532,
  serialized_end=616,
)


_CONFIGDEFINITION_ROWSENTRY = _descriptor.Descriptor(
  name='RowsEntry',
  full_name='carbon.thinning.ConfigDefinition.RowsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.thinning.ConfigDefinition.RowsEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.thinning.ConfigDefinition.RowsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=918,
  serialized_end=986,
)

_CONFIGDEFINITION = _descriptor.Descriptor(
  name='ConfigDefinition',
  full_name='carbon.thinning.ConfigDefinition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.thinning.ConfigDefinition.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active', full_name='carbon.thinning.ConfigDefinition.active', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_1', full_name='carbon.thinning.ConfigDefinition.row_1', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_2', full_name='carbon.thinning.ConfigDefinition.row_2', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_3', full_name='carbon.thinning.ConfigDefinition.row_3', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='size_filter', full_name='carbon.thinning.ConfigDefinition.size_filter', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.thinning.ConfigDefinition.id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rows', full_name='carbon.thinning.ConfigDefinition.rows', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_CONFIGDEFINITION_ROWSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=619,
  serialized_end=986,
)

_DOUBLEBOX.fields_by_name['box_1'].message_type = _BOX
_DOUBLEBOX.fields_by_name['box_2'].message_type = _BOX
_SIZEDNOTSOGREEDYCFG.fields_by_name['min_keepout'].message_type = _BOX
_BOUNDS.fields_by_name['box'].message_type = _BOX
_BOUNDS.fields_by_name['double_box'].message_type = _DOUBLEBOX
_BOUNDS.fields_by_name['sized_cfg'].message_type = _SIZEDNOTSOGREEDYCFG
_BOUNDS.oneofs_by_name['bounds'].fields.append(
  _BOUNDS.fields_by_name['box'])
_BOUNDS.fields_by_name['box'].containing_oneof = _BOUNDS.oneofs_by_name['bounds']
_BOUNDS.oneofs_by_name['bounds'].fields.append(
  _BOUNDS.fields_by_name['double_box'])
_BOUNDS.fields_by_name['double_box'].containing_oneof = _BOUNDS.oneofs_by_name['bounds']
_BOUNDS.oneofs_by_name['bounds'].fields.append(
  _BOUNDS.fields_by_name['sized_cfg'])
_BOUNDS.fields_by_name['sized_cfg'].containing_oneof = _BOUNDS.oneofs_by_name['bounds']
_CONFIGDEFINITION_ROWSENTRY.fields_by_name['value'].message_type = _BOUNDS
_CONFIGDEFINITION_ROWSENTRY.containing_type = _CONFIGDEFINITION
_CONFIGDEFINITION.fields_by_name['row_1'].message_type = _BOUNDS
_CONFIGDEFINITION.fields_by_name['row_2'].message_type = _BOUNDS
_CONFIGDEFINITION.fields_by_name['row_3'].message_type = _BOUNDS
_CONFIGDEFINITION.fields_by_name['size_filter'].message_type = _SIZEFILTER
_CONFIGDEFINITION.fields_by_name['rows'].message_type = _CONFIGDEFINITION_ROWSENTRY
DESCRIPTOR.message_types_by_name['Box'] = _BOX
DESCRIPTOR.message_types_by_name['DoubleBox'] = _DOUBLEBOX
DESCRIPTOR.message_types_by_name['SizedNotSoGreedyCfg'] = _SIZEDNOTSOGREEDYCFG
DESCRIPTOR.message_types_by_name['Bounds'] = _BOUNDS
DESCRIPTOR.message_types_by_name['SizeFilter'] = _SIZEFILTER
DESCRIPTOR.message_types_by_name['ConfigDefinition'] = _CONFIGDEFINITION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Box = _reflection.GeneratedProtocolMessageType('Box', (_message.Message,), {
  'DESCRIPTOR' : _BOX,
  '__module__' : 'proto.thinning.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.thinning.Box)
  })
_sym_db.RegisterMessage(Box)

DoubleBox = _reflection.GeneratedProtocolMessageType('DoubleBox', (_message.Message,), {
  'DESCRIPTOR' : _DOUBLEBOX,
  '__module__' : 'proto.thinning.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.thinning.DoubleBox)
  })
_sym_db.RegisterMessage(DoubleBox)

SizedNotSoGreedyCfg = _reflection.GeneratedProtocolMessageType('SizedNotSoGreedyCfg', (_message.Message,), {
  'DESCRIPTOR' : _SIZEDNOTSOGREEDYCFG,
  '__module__' : 'proto.thinning.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.thinning.SizedNotSoGreedyCfg)
  })
_sym_db.RegisterMessage(SizedNotSoGreedyCfg)

Bounds = _reflection.GeneratedProtocolMessageType('Bounds', (_message.Message,), {
  'DESCRIPTOR' : _BOUNDS,
  '__module__' : 'proto.thinning.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.thinning.Bounds)
  })
_sym_db.RegisterMessage(Bounds)

SizeFilter = _reflection.GeneratedProtocolMessageType('SizeFilter', (_message.Message,), {
  'DESCRIPTOR' : _SIZEFILTER,
  '__module__' : 'proto.thinning.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.thinning.SizeFilter)
  })
_sym_db.RegisterMessage(SizeFilter)

ConfigDefinition = _reflection.GeneratedProtocolMessageType('ConfigDefinition', (_message.Message,), {

  'RowsEntry' : _reflection.GeneratedProtocolMessageType('RowsEntry', (_message.Message,), {
    'DESCRIPTOR' : _CONFIGDEFINITION_ROWSENTRY,
    '__module__' : 'proto.thinning.thinning_pb2'
    # @@protoc_insertion_point(class_scope:carbon.thinning.ConfigDefinition.RowsEntry)
    })
  ,
  'DESCRIPTOR' : _CONFIGDEFINITION,
  '__module__' : 'proto.thinning.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.thinning.ConfigDefinition)
  })
_sym_db.RegisterMessage(ConfigDefinition)
_sym_db.RegisterMessage(ConfigDefinition.RowsEntry)


DESCRIPTOR._options = None
_DOUBLEBOX._options = None
_BOUNDS.fields_by_name['double_box']._options = None
_SIZEFILTER._options = None
_CONFIGDEFINITION_ROWSENTRY._options = None
_CONFIGDEFINITION.fields_by_name['active']._options = None
_CONFIGDEFINITION.fields_by_name['size_filter']._options = None
# @@protoc_insertion_point(module_scope)

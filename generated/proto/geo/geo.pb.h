// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/geo/geo.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2fgeo_2fgeo_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2fgeo_2fgeo_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/timestamp.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2fgeo_2fgeo_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2fgeo_2fgeo_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fgeo_2fgeo_2eproto;
namespace carbon {
namespace geo {
class AbLine;
struct AbLineDefaultTypeInternal;
extern AbLineDefaultTypeInternal _AbLine_default_instance_;
class CaptureInfo;
struct CaptureInfoDefaultTypeInternal;
extern CaptureInfoDefaultTypeInternal _CaptureInfo_default_instance_;
class Id;
struct IdDefaultTypeInternal;
extern IdDefaultTypeInternal _Id_default_instance_;
class LineString;
struct LineStringDefaultTypeInternal;
extern LineStringDefaultTypeInternal _LineString_default_instance_;
class MultiPolygon;
struct MultiPolygonDefaultTypeInternal;
extern MultiPolygonDefaultTypeInternal _MultiPolygon_default_instance_;
class Point;
struct PointDefaultTypeInternal;
extern PointDefaultTypeInternal _Point_default_instance_;
class Polygon;
struct PolygonDefaultTypeInternal;
extern PolygonDefaultTypeInternal _Polygon_default_instance_;
class PolygonRing;
struct PolygonRingDefaultTypeInternal;
extern PolygonRingDefaultTypeInternal _PolygonRing_default_instance_;
}  // namespace geo
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::geo::AbLine* Arena::CreateMaybeMessage<::carbon::geo::AbLine>(Arena*);
template<> ::carbon::geo::CaptureInfo* Arena::CreateMaybeMessage<::carbon::geo::CaptureInfo>(Arena*);
template<> ::carbon::geo::Id* Arena::CreateMaybeMessage<::carbon::geo::Id>(Arena*);
template<> ::carbon::geo::LineString* Arena::CreateMaybeMessage<::carbon::geo::LineString>(Arena*);
template<> ::carbon::geo::MultiPolygon* Arena::CreateMaybeMessage<::carbon::geo::MultiPolygon>(Arena*);
template<> ::carbon::geo::Point* Arena::CreateMaybeMessage<::carbon::geo::Point>(Arena*);
template<> ::carbon::geo::Polygon* Arena::CreateMaybeMessage<::carbon::geo::Polygon>(Arena*);
template<> ::carbon::geo::PolygonRing* Arena::CreateMaybeMessage<::carbon::geo::PolygonRing>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace geo {

enum FixType : int {
  FIX_TYPE_UNSPECIFIED = 0,
  NO_FIX = 1,
  GNSS = 2,
  DIFFERENTIAL_GNSS = 3,
  RTK_FIXED = 4,
  RTK_FLOAT = 5,
  DEAD_RECKONING = 6,
  FixType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  FixType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool FixType_IsValid(int value);
constexpr FixType FixType_MIN = FIX_TYPE_UNSPECIFIED;
constexpr FixType FixType_MAX = DEAD_RECKONING;
constexpr int FixType_ARRAYSIZE = FixType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FixType_descriptor();
template<typename T>
inline const std::string& FixType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FixType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FixType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FixType_descriptor(), enum_t_value);
}
inline bool FixType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, FixType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FixType>(
    FixType_descriptor(), name, value);
}
// ===================================================================

class CaptureInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.geo.CaptureInfo) */ {
 public:
  inline CaptureInfo() : CaptureInfo(nullptr) {}
  ~CaptureInfo() override;
  explicit constexpr CaptureInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CaptureInfo(const CaptureInfo& from);
  CaptureInfo(CaptureInfo&& from) noexcept
    : CaptureInfo() {
    *this = ::std::move(from);
  }

  inline CaptureInfo& operator=(const CaptureInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CaptureInfo& operator=(CaptureInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CaptureInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const CaptureInfo* internal_default_instance() {
    return reinterpret_cast<const CaptureInfo*>(
               &_CaptureInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CaptureInfo& a, CaptureInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CaptureInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CaptureInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CaptureInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CaptureInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CaptureInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CaptureInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CaptureInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.geo.CaptureInfo";
  }
  protected:
  explicit CaptureInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCaptureTimeFieldNumber = 2,
    kFixTypeFieldNumber = 1,
  };
  // .google.protobuf.Timestamp capture_time = 2;
  bool has_capture_time() const;
  private:
  bool _internal_has_capture_time() const;
  public:
  void clear_capture_time();
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& capture_time() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Timestamp* release_capture_time();
  ::PROTOBUF_NAMESPACE_ID::Timestamp* mutable_capture_time();
  void set_allocated_capture_time(::PROTOBUF_NAMESPACE_ID::Timestamp* capture_time);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& _internal_capture_time() const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _internal_mutable_capture_time();
  public:
  void unsafe_arena_set_allocated_capture_time(
      ::PROTOBUF_NAMESPACE_ID::Timestamp* capture_time);
  ::PROTOBUF_NAMESPACE_ID::Timestamp* unsafe_arena_release_capture_time();

  // .carbon.geo.FixType fix_type = 1;
  void clear_fix_type();
  ::carbon::geo::FixType fix_type() const;
  void set_fix_type(::carbon::geo::FixType value);
  private:
  ::carbon::geo::FixType _internal_fix_type() const;
  void _internal_set_fix_type(::carbon::geo::FixType value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.geo.CaptureInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* capture_time_;
  int fix_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fgeo_2fgeo_2eproto;
};
// -------------------------------------------------------------------

class Id final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.geo.Id) */ {
 public:
  inline Id() : Id(nullptr) {}
  ~Id() override;
  explicit constexpr Id(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Id(const Id& from);
  Id(Id&& from) noexcept
    : Id() {
    *this = ::std::move(from);
  }

  inline Id& operator=(const Id& from) {
    CopyFrom(from);
    return *this;
  }
  inline Id& operator=(Id&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Id& default_instance() {
    return *internal_default_instance();
  }
  static inline const Id* internal_default_instance() {
    return reinterpret_cast<const Id*>(
               &_Id_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Id& a, Id& b) {
    a.Swap(&b);
  }
  inline void Swap(Id* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Id* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Id* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Id>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Id& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Id& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Id* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.geo.Id";
  }
  protected:
  explicit Id(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.geo.Id)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fgeo_2fgeo_2eproto;
};
// -------------------------------------------------------------------

class Point final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.geo.Point) */ {
 public:
  inline Point() : Point(nullptr) {}
  ~Point() override;
  explicit constexpr Point(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Point(const Point& from);
  Point(Point&& from) noexcept
    : Point() {
    *this = ::std::move(from);
  }

  inline Point& operator=(const Point& from) {
    CopyFrom(from);
    return *this;
  }
  inline Point& operator=(Point&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Point& default_instance() {
    return *internal_default_instance();
  }
  static inline const Point* internal_default_instance() {
    return reinterpret_cast<const Point*>(
               &_Point_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Point& a, Point& b) {
    a.Swap(&b);
  }
  inline void Swap(Point* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Point* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Point* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Point>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Point& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Point& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Point* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.geo.Point";
  }
  protected:
  explicit Point(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 6,
    kCaptureInfoFieldNumber = 4,
    kIdFieldNumber = 5,
    kLngFieldNumber = 1,
    kLatFieldNumber = 2,
    kAltFieldNumber = 3,
  };
  // string name = 6;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .carbon.geo.CaptureInfo capture_info = 4;
  bool has_capture_info() const;
  private:
  bool _internal_has_capture_info() const;
  public:
  void clear_capture_info();
  const ::carbon::geo::CaptureInfo& capture_info() const;
  PROTOBUF_NODISCARD ::carbon::geo::CaptureInfo* release_capture_info();
  ::carbon::geo::CaptureInfo* mutable_capture_info();
  void set_allocated_capture_info(::carbon::geo::CaptureInfo* capture_info);
  private:
  const ::carbon::geo::CaptureInfo& _internal_capture_info() const;
  ::carbon::geo::CaptureInfo* _internal_mutable_capture_info();
  public:
  void unsafe_arena_set_allocated_capture_info(
      ::carbon::geo::CaptureInfo* capture_info);
  ::carbon::geo::CaptureInfo* unsafe_arena_release_capture_info();

  // .carbon.geo.Id id = 5;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  const ::carbon::geo::Id& id() const;
  PROTOBUF_NODISCARD ::carbon::geo::Id* release_id();
  ::carbon::geo::Id* mutable_id();
  void set_allocated_id(::carbon::geo::Id* id);
  private:
  const ::carbon::geo::Id& _internal_id() const;
  ::carbon::geo::Id* _internal_mutable_id();
  public:
  void unsafe_arena_set_allocated_id(
      ::carbon::geo::Id* id);
  ::carbon::geo::Id* unsafe_arena_release_id();

  // double lng = 1;
  void clear_lng();
  double lng() const;
  void set_lng(double value);
  private:
  double _internal_lng() const;
  void _internal_set_lng(double value);
  public:

  // double lat = 2;
  void clear_lat();
  double lat() const;
  void set_lat(double value);
  private:
  double _internal_lat() const;
  void _internal_set_lat(double value);
  public:

  // double alt = 3;
  void clear_alt();
  double alt() const;
  void set_alt(double value);
  private:
  double _internal_alt() const;
  void _internal_set_alt(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.geo.Point)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::carbon::geo::CaptureInfo* capture_info_;
  ::carbon::geo::Id* id_;
  double lng_;
  double lat_;
  double alt_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fgeo_2fgeo_2eproto;
};
// -------------------------------------------------------------------

class LineString final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.geo.LineString) */ {
 public:
  inline LineString() : LineString(nullptr) {}
  ~LineString() override;
  explicit constexpr LineString(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LineString(const LineString& from);
  LineString(LineString&& from) noexcept
    : LineString() {
    *this = ::std::move(from);
  }

  inline LineString& operator=(const LineString& from) {
    CopyFrom(from);
    return *this;
  }
  inline LineString& operator=(LineString&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LineString& default_instance() {
    return *internal_default_instance();
  }
  static inline const LineString* internal_default_instance() {
    return reinterpret_cast<const LineString*>(
               &_LineString_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(LineString& a, LineString& b) {
    a.Swap(&b);
  }
  inline void Swap(LineString* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LineString* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LineString* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LineString>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LineString& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LineString& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LineString* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.geo.LineString";
  }
  protected:
  explicit LineString(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 1,
  };
  // repeated .carbon.geo.Point points = 1;
  int points_size() const;
  private:
  int _internal_points_size() const;
  public:
  void clear_points();
  ::carbon::geo::Point* mutable_points(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point >*
      mutable_points();
  private:
  const ::carbon::geo::Point& _internal_points(int index) const;
  ::carbon::geo::Point* _internal_add_points();
  public:
  const ::carbon::geo::Point& points(int index) const;
  ::carbon::geo::Point* add_points();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point >&
      points() const;

  // @@protoc_insertion_point(class_scope:carbon.geo.LineString)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point > points_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fgeo_2fgeo_2eproto;
};
// -------------------------------------------------------------------

class AbLine final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.geo.AbLine) */ {
 public:
  inline AbLine() : AbLine(nullptr) {}
  ~AbLine() override;
  explicit constexpr AbLine(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AbLine(const AbLine& from);
  AbLine(AbLine&& from) noexcept
    : AbLine() {
    *this = ::std::move(from);
  }

  inline AbLine& operator=(const AbLine& from) {
    CopyFrom(from);
    return *this;
  }
  inline AbLine& operator=(AbLine&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AbLine& default_instance() {
    return *internal_default_instance();
  }
  static inline const AbLine* internal_default_instance() {
    return reinterpret_cast<const AbLine*>(
               &_AbLine_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(AbLine& a, AbLine& b) {
    a.Swap(&b);
  }
  inline void Swap(AbLine* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AbLine* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AbLine* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AbLine>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AbLine& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AbLine& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AbLine* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.geo.AbLine";
  }
  protected:
  explicit AbLine(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAFieldNumber = 1,
    kBFieldNumber = 2,
  };
  // .carbon.geo.Point a = 1;
  bool has_a() const;
  private:
  bool _internal_has_a() const;
  public:
  void clear_a();
  const ::carbon::geo::Point& a() const;
  PROTOBUF_NODISCARD ::carbon::geo::Point* release_a();
  ::carbon::geo::Point* mutable_a();
  void set_allocated_a(::carbon::geo::Point* a);
  private:
  const ::carbon::geo::Point& _internal_a() const;
  ::carbon::geo::Point* _internal_mutable_a();
  public:
  void unsafe_arena_set_allocated_a(
      ::carbon::geo::Point* a);
  ::carbon::geo::Point* unsafe_arena_release_a();

  // .carbon.geo.Point b = 2;
  bool has_b() const;
  private:
  bool _internal_has_b() const;
  public:
  void clear_b();
  const ::carbon::geo::Point& b() const;
  PROTOBUF_NODISCARD ::carbon::geo::Point* release_b();
  ::carbon::geo::Point* mutable_b();
  void set_allocated_b(::carbon::geo::Point* b);
  private:
  const ::carbon::geo::Point& _internal_b() const;
  ::carbon::geo::Point* _internal_mutable_b();
  public:
  void unsafe_arena_set_allocated_b(
      ::carbon::geo::Point* b);
  ::carbon::geo::Point* unsafe_arena_release_b();

  // @@protoc_insertion_point(class_scope:carbon.geo.AbLine)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::geo::Point* a_;
  ::carbon::geo::Point* b_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fgeo_2fgeo_2eproto;
};
// -------------------------------------------------------------------

class Polygon final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.geo.Polygon) */ {
 public:
  inline Polygon() : Polygon(nullptr) {}
  ~Polygon() override;
  explicit constexpr Polygon(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Polygon(const Polygon& from);
  Polygon(Polygon&& from) noexcept
    : Polygon() {
    *this = ::std::move(from);
  }

  inline Polygon& operator=(const Polygon& from) {
    CopyFrom(from);
    return *this;
  }
  inline Polygon& operator=(Polygon&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Polygon& default_instance() {
    return *internal_default_instance();
  }
  static inline const Polygon* internal_default_instance() {
    return reinterpret_cast<const Polygon*>(
               &_Polygon_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Polygon& a, Polygon& b) {
    a.Swap(&b);
  }
  inline void Swap(Polygon* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Polygon* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Polygon* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Polygon>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Polygon& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Polygon& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Polygon* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.geo.Polygon";
  }
  protected:
  explicit Polygon(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHolesFieldNumber = 2,
    kBoundaryFieldNumber = 1,
  };
  // repeated .carbon.geo.PolygonRing holes = 2;
  int holes_size() const;
  private:
  int _internal_holes_size() const;
  public:
  void clear_holes();
  ::carbon::geo::PolygonRing* mutable_holes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::PolygonRing >*
      mutable_holes();
  private:
  const ::carbon::geo::PolygonRing& _internal_holes(int index) const;
  ::carbon::geo::PolygonRing* _internal_add_holes();
  public:
  const ::carbon::geo::PolygonRing& holes(int index) const;
  ::carbon::geo::PolygonRing* add_holes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::PolygonRing >&
      holes() const;

  // .carbon.geo.PolygonRing boundary = 1;
  bool has_boundary() const;
  private:
  bool _internal_has_boundary() const;
  public:
  void clear_boundary();
  const ::carbon::geo::PolygonRing& boundary() const;
  PROTOBUF_NODISCARD ::carbon::geo::PolygonRing* release_boundary();
  ::carbon::geo::PolygonRing* mutable_boundary();
  void set_allocated_boundary(::carbon::geo::PolygonRing* boundary);
  private:
  const ::carbon::geo::PolygonRing& _internal_boundary() const;
  ::carbon::geo::PolygonRing* _internal_mutable_boundary();
  public:
  void unsafe_arena_set_allocated_boundary(
      ::carbon::geo::PolygonRing* boundary);
  ::carbon::geo::PolygonRing* unsafe_arena_release_boundary();

  // @@protoc_insertion_point(class_scope:carbon.geo.Polygon)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::PolygonRing > holes_;
  ::carbon::geo::PolygonRing* boundary_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fgeo_2fgeo_2eproto;
};
// -------------------------------------------------------------------

class PolygonRing final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.geo.PolygonRing) */ {
 public:
  inline PolygonRing() : PolygonRing(nullptr) {}
  ~PolygonRing() override;
  explicit constexpr PolygonRing(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PolygonRing(const PolygonRing& from);
  PolygonRing(PolygonRing&& from) noexcept
    : PolygonRing() {
    *this = ::std::move(from);
  }

  inline PolygonRing& operator=(const PolygonRing& from) {
    CopyFrom(from);
    return *this;
  }
  inline PolygonRing& operator=(PolygonRing&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PolygonRing& default_instance() {
    return *internal_default_instance();
  }
  static inline const PolygonRing* internal_default_instance() {
    return reinterpret_cast<const PolygonRing*>(
               &_PolygonRing_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(PolygonRing& a, PolygonRing& b) {
    a.Swap(&b);
  }
  inline void Swap(PolygonRing* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PolygonRing* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PolygonRing* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PolygonRing>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PolygonRing& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PolygonRing& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PolygonRing* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.geo.PolygonRing";
  }
  protected:
  explicit PolygonRing(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 1,
  };
  // repeated .carbon.geo.Point points = 1;
  int points_size() const;
  private:
  int _internal_points_size() const;
  public:
  void clear_points();
  ::carbon::geo::Point* mutable_points(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point >*
      mutable_points();
  private:
  const ::carbon::geo::Point& _internal_points(int index) const;
  ::carbon::geo::Point* _internal_add_points();
  public:
  const ::carbon::geo::Point& points(int index) const;
  ::carbon::geo::Point* add_points();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point >&
      points() const;

  // @@protoc_insertion_point(class_scope:carbon.geo.PolygonRing)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point > points_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fgeo_2fgeo_2eproto;
};
// -------------------------------------------------------------------

class MultiPolygon final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.geo.MultiPolygon) */ {
 public:
  inline MultiPolygon() : MultiPolygon(nullptr) {}
  ~MultiPolygon() override;
  explicit constexpr MultiPolygon(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MultiPolygon(const MultiPolygon& from);
  MultiPolygon(MultiPolygon&& from) noexcept
    : MultiPolygon() {
    *this = ::std::move(from);
  }

  inline MultiPolygon& operator=(const MultiPolygon& from) {
    CopyFrom(from);
    return *this;
  }
  inline MultiPolygon& operator=(MultiPolygon&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MultiPolygon& default_instance() {
    return *internal_default_instance();
  }
  static inline const MultiPolygon* internal_default_instance() {
    return reinterpret_cast<const MultiPolygon*>(
               &_MultiPolygon_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(MultiPolygon& a, MultiPolygon& b) {
    a.Swap(&b);
  }
  inline void Swap(MultiPolygon* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MultiPolygon* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MultiPolygon* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MultiPolygon>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MultiPolygon& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MultiPolygon& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MultiPolygon* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.geo.MultiPolygon";
  }
  protected:
  explicit MultiPolygon(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPolygonsFieldNumber = 1,
  };
  // repeated .carbon.geo.Polygon polygons = 1;
  int polygons_size() const;
  private:
  int _internal_polygons_size() const;
  public:
  void clear_polygons();
  ::carbon::geo::Polygon* mutable_polygons(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Polygon >*
      mutable_polygons();
  private:
  const ::carbon::geo::Polygon& _internal_polygons(int index) const;
  ::carbon::geo::Polygon* _internal_add_polygons();
  public:
  const ::carbon::geo::Polygon& polygons(int index) const;
  ::carbon::geo::Polygon* add_polygons();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Polygon >&
      polygons() const;

  // @@protoc_insertion_point(class_scope:carbon.geo.MultiPolygon)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Polygon > polygons_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fgeo_2fgeo_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CaptureInfo

// .carbon.geo.FixType fix_type = 1;
inline void CaptureInfo::clear_fix_type() {
  fix_type_ = 0;
}
inline ::carbon::geo::FixType CaptureInfo::_internal_fix_type() const {
  return static_cast< ::carbon::geo::FixType >(fix_type_);
}
inline ::carbon::geo::FixType CaptureInfo::fix_type() const {
  // @@protoc_insertion_point(field_get:carbon.geo.CaptureInfo.fix_type)
  return _internal_fix_type();
}
inline void CaptureInfo::_internal_set_fix_type(::carbon::geo::FixType value) {
  
  fix_type_ = value;
}
inline void CaptureInfo::set_fix_type(::carbon::geo::FixType value) {
  _internal_set_fix_type(value);
  // @@protoc_insertion_point(field_set:carbon.geo.CaptureInfo.fix_type)
}

// .google.protobuf.Timestamp capture_time = 2;
inline bool CaptureInfo::_internal_has_capture_time() const {
  return this != internal_default_instance() && capture_time_ != nullptr;
}
inline bool CaptureInfo::has_capture_time() const {
  return _internal_has_capture_time();
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& CaptureInfo::_internal_capture_time() const {
  const ::PROTOBUF_NAMESPACE_ID::Timestamp* p = capture_time_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Timestamp&>(
      ::PROTOBUF_NAMESPACE_ID::_Timestamp_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& CaptureInfo::capture_time() const {
  // @@protoc_insertion_point(field_get:carbon.geo.CaptureInfo.capture_time)
  return _internal_capture_time();
}
inline void CaptureInfo::unsafe_arena_set_allocated_capture_time(
    ::PROTOBUF_NAMESPACE_ID::Timestamp* capture_time) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(capture_time_);
  }
  capture_time_ = capture_time;
  if (capture_time) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.geo.CaptureInfo.capture_time)
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* CaptureInfo::release_capture_time() {
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = capture_time_;
  capture_time_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* CaptureInfo::unsafe_arena_release_capture_time() {
  // @@protoc_insertion_point(field_release:carbon.geo.CaptureInfo.capture_time)
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = capture_time_;
  capture_time_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* CaptureInfo::_internal_mutable_capture_time() {
  
  if (capture_time_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Timestamp>(GetArenaForAllocation());
    capture_time_ = p;
  }
  return capture_time_;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* CaptureInfo::mutable_capture_time() {
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _msg = _internal_mutable_capture_time();
  // @@protoc_insertion_point(field_mutable:carbon.geo.CaptureInfo.capture_time)
  return _msg;
}
inline void CaptureInfo::set_allocated_capture_time(::PROTOBUF_NAMESPACE_ID::Timestamp* capture_time) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(capture_time_);
  }
  if (capture_time) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(capture_time));
    if (message_arena != submessage_arena) {
      capture_time = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, capture_time, submessage_arena);
    }
    
  } else {
    
  }
  capture_time_ = capture_time;
  // @@protoc_insertion_point(field_set_allocated:carbon.geo.CaptureInfo.capture_time)
}

// -------------------------------------------------------------------

// Id

// string id = 1;
inline void Id::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& Id::id() const {
  // @@protoc_insertion_point(field_get:carbon.geo.Id.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Id::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.geo.Id.id)
}
inline std::string* Id::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.geo.Id.id)
  return _s;
}
inline const std::string& Id::_internal_id() const {
  return id_.Get();
}
inline void Id::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Id::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Id::release_id() {
  // @@protoc_insertion_point(field_release:carbon.geo.Id.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Id::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.geo.Id.id)
}

// -------------------------------------------------------------------

// Point

// double lng = 1;
inline void Point::clear_lng() {
  lng_ = 0;
}
inline double Point::_internal_lng() const {
  return lng_;
}
inline double Point::lng() const {
  // @@protoc_insertion_point(field_get:carbon.geo.Point.lng)
  return _internal_lng();
}
inline void Point::_internal_set_lng(double value) {
  
  lng_ = value;
}
inline void Point::set_lng(double value) {
  _internal_set_lng(value);
  // @@protoc_insertion_point(field_set:carbon.geo.Point.lng)
}

// double lat = 2;
inline void Point::clear_lat() {
  lat_ = 0;
}
inline double Point::_internal_lat() const {
  return lat_;
}
inline double Point::lat() const {
  // @@protoc_insertion_point(field_get:carbon.geo.Point.lat)
  return _internal_lat();
}
inline void Point::_internal_set_lat(double value) {
  
  lat_ = value;
}
inline void Point::set_lat(double value) {
  _internal_set_lat(value);
  // @@protoc_insertion_point(field_set:carbon.geo.Point.lat)
}

// double alt = 3;
inline void Point::clear_alt() {
  alt_ = 0;
}
inline double Point::_internal_alt() const {
  return alt_;
}
inline double Point::alt() const {
  // @@protoc_insertion_point(field_get:carbon.geo.Point.alt)
  return _internal_alt();
}
inline void Point::_internal_set_alt(double value) {
  
  alt_ = value;
}
inline void Point::set_alt(double value) {
  _internal_set_alt(value);
  // @@protoc_insertion_point(field_set:carbon.geo.Point.alt)
}

// .carbon.geo.CaptureInfo capture_info = 4;
inline bool Point::_internal_has_capture_info() const {
  return this != internal_default_instance() && capture_info_ != nullptr;
}
inline bool Point::has_capture_info() const {
  return _internal_has_capture_info();
}
inline void Point::clear_capture_info() {
  if (GetArenaForAllocation() == nullptr && capture_info_ != nullptr) {
    delete capture_info_;
  }
  capture_info_ = nullptr;
}
inline const ::carbon::geo::CaptureInfo& Point::_internal_capture_info() const {
  const ::carbon::geo::CaptureInfo* p = capture_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::CaptureInfo&>(
      ::carbon::geo::_CaptureInfo_default_instance_);
}
inline const ::carbon::geo::CaptureInfo& Point::capture_info() const {
  // @@protoc_insertion_point(field_get:carbon.geo.Point.capture_info)
  return _internal_capture_info();
}
inline void Point::unsafe_arena_set_allocated_capture_info(
    ::carbon::geo::CaptureInfo* capture_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(capture_info_);
  }
  capture_info_ = capture_info;
  if (capture_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.geo.Point.capture_info)
}
inline ::carbon::geo::CaptureInfo* Point::release_capture_info() {
  
  ::carbon::geo::CaptureInfo* temp = capture_info_;
  capture_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::CaptureInfo* Point::unsafe_arena_release_capture_info() {
  // @@protoc_insertion_point(field_release:carbon.geo.Point.capture_info)
  
  ::carbon::geo::CaptureInfo* temp = capture_info_;
  capture_info_ = nullptr;
  return temp;
}
inline ::carbon::geo::CaptureInfo* Point::_internal_mutable_capture_info() {
  
  if (capture_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::CaptureInfo>(GetArenaForAllocation());
    capture_info_ = p;
  }
  return capture_info_;
}
inline ::carbon::geo::CaptureInfo* Point::mutable_capture_info() {
  ::carbon::geo::CaptureInfo* _msg = _internal_mutable_capture_info();
  // @@protoc_insertion_point(field_mutable:carbon.geo.Point.capture_info)
  return _msg;
}
inline void Point::set_allocated_capture_info(::carbon::geo::CaptureInfo* capture_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete capture_info_;
  }
  if (capture_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::geo::CaptureInfo>::GetOwningArena(capture_info);
    if (message_arena != submessage_arena) {
      capture_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, capture_info, submessage_arena);
    }
    
  } else {
    
  }
  capture_info_ = capture_info;
  // @@protoc_insertion_point(field_set_allocated:carbon.geo.Point.capture_info)
}

// .carbon.geo.Id id = 5;
inline bool Point::_internal_has_id() const {
  return this != internal_default_instance() && id_ != nullptr;
}
inline bool Point::has_id() const {
  return _internal_has_id();
}
inline void Point::clear_id() {
  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
}
inline const ::carbon::geo::Id& Point::_internal_id() const {
  const ::carbon::geo::Id* p = id_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::Id&>(
      ::carbon::geo::_Id_default_instance_);
}
inline const ::carbon::geo::Id& Point::id() const {
  // @@protoc_insertion_point(field_get:carbon.geo.Point.id)
  return _internal_id();
}
inline void Point::unsafe_arena_set_allocated_id(
    ::carbon::geo::Id* id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(id_);
  }
  id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.geo.Point.id)
}
inline ::carbon::geo::Id* Point::release_id() {
  
  ::carbon::geo::Id* temp = id_;
  id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::Id* Point::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_release:carbon.geo.Point.id)
  
  ::carbon::geo::Id* temp = id_;
  id_ = nullptr;
  return temp;
}
inline ::carbon::geo::Id* Point::_internal_mutable_id() {
  
  if (id_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::Id>(GetArenaForAllocation());
    id_ = p;
  }
  return id_;
}
inline ::carbon::geo::Id* Point::mutable_id() {
  ::carbon::geo::Id* _msg = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.geo.Point.id)
  return _msg;
}
inline void Point::set_allocated_id(::carbon::geo::Id* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete id_;
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::geo::Id>::GetOwningArena(id);
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:carbon.geo.Point.id)
}

// string name = 6;
inline void Point::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& Point::name() const {
  // @@protoc_insertion_point(field_get:carbon.geo.Point.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Point::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.geo.Point.name)
}
inline std::string* Point::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.geo.Point.name)
  return _s;
}
inline const std::string& Point::_internal_name() const {
  return name_.Get();
}
inline void Point::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Point::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Point::release_name() {
  // @@protoc_insertion_point(field_release:carbon.geo.Point.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Point::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.geo.Point.name)
}

// -------------------------------------------------------------------

// LineString

// repeated .carbon.geo.Point points = 1;
inline int LineString::_internal_points_size() const {
  return points_.size();
}
inline int LineString::points_size() const {
  return _internal_points_size();
}
inline void LineString::clear_points() {
  points_.Clear();
}
inline ::carbon::geo::Point* LineString::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.geo.LineString.points)
  return points_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point >*
LineString::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:carbon.geo.LineString.points)
  return &points_;
}
inline const ::carbon::geo::Point& LineString::_internal_points(int index) const {
  return points_.Get(index);
}
inline const ::carbon::geo::Point& LineString::points(int index) const {
  // @@protoc_insertion_point(field_get:carbon.geo.LineString.points)
  return _internal_points(index);
}
inline ::carbon::geo::Point* LineString::_internal_add_points() {
  return points_.Add();
}
inline ::carbon::geo::Point* LineString::add_points() {
  ::carbon::geo::Point* _add = _internal_add_points();
  // @@protoc_insertion_point(field_add:carbon.geo.LineString.points)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point >&
LineString::points() const {
  // @@protoc_insertion_point(field_list:carbon.geo.LineString.points)
  return points_;
}

// -------------------------------------------------------------------

// AbLine

// .carbon.geo.Point a = 1;
inline bool AbLine::_internal_has_a() const {
  return this != internal_default_instance() && a_ != nullptr;
}
inline bool AbLine::has_a() const {
  return _internal_has_a();
}
inline void AbLine::clear_a() {
  if (GetArenaForAllocation() == nullptr && a_ != nullptr) {
    delete a_;
  }
  a_ = nullptr;
}
inline const ::carbon::geo::Point& AbLine::_internal_a() const {
  const ::carbon::geo::Point* p = a_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::Point&>(
      ::carbon::geo::_Point_default_instance_);
}
inline const ::carbon::geo::Point& AbLine::a() const {
  // @@protoc_insertion_point(field_get:carbon.geo.AbLine.a)
  return _internal_a();
}
inline void AbLine::unsafe_arena_set_allocated_a(
    ::carbon::geo::Point* a) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(a_);
  }
  a_ = a;
  if (a) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.geo.AbLine.a)
}
inline ::carbon::geo::Point* AbLine::release_a() {
  
  ::carbon::geo::Point* temp = a_;
  a_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::Point* AbLine::unsafe_arena_release_a() {
  // @@protoc_insertion_point(field_release:carbon.geo.AbLine.a)
  
  ::carbon::geo::Point* temp = a_;
  a_ = nullptr;
  return temp;
}
inline ::carbon::geo::Point* AbLine::_internal_mutable_a() {
  
  if (a_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::Point>(GetArenaForAllocation());
    a_ = p;
  }
  return a_;
}
inline ::carbon::geo::Point* AbLine::mutable_a() {
  ::carbon::geo::Point* _msg = _internal_mutable_a();
  // @@protoc_insertion_point(field_mutable:carbon.geo.AbLine.a)
  return _msg;
}
inline void AbLine::set_allocated_a(::carbon::geo::Point* a) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete a_;
  }
  if (a) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::geo::Point>::GetOwningArena(a);
    if (message_arena != submessage_arena) {
      a = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, a, submessage_arena);
    }
    
  } else {
    
  }
  a_ = a;
  // @@protoc_insertion_point(field_set_allocated:carbon.geo.AbLine.a)
}

// .carbon.geo.Point b = 2;
inline bool AbLine::_internal_has_b() const {
  return this != internal_default_instance() && b_ != nullptr;
}
inline bool AbLine::has_b() const {
  return _internal_has_b();
}
inline void AbLine::clear_b() {
  if (GetArenaForAllocation() == nullptr && b_ != nullptr) {
    delete b_;
  }
  b_ = nullptr;
}
inline const ::carbon::geo::Point& AbLine::_internal_b() const {
  const ::carbon::geo::Point* p = b_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::Point&>(
      ::carbon::geo::_Point_default_instance_);
}
inline const ::carbon::geo::Point& AbLine::b() const {
  // @@protoc_insertion_point(field_get:carbon.geo.AbLine.b)
  return _internal_b();
}
inline void AbLine::unsafe_arena_set_allocated_b(
    ::carbon::geo::Point* b) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(b_);
  }
  b_ = b;
  if (b) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.geo.AbLine.b)
}
inline ::carbon::geo::Point* AbLine::release_b() {
  
  ::carbon::geo::Point* temp = b_;
  b_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::Point* AbLine::unsafe_arena_release_b() {
  // @@protoc_insertion_point(field_release:carbon.geo.AbLine.b)
  
  ::carbon::geo::Point* temp = b_;
  b_ = nullptr;
  return temp;
}
inline ::carbon::geo::Point* AbLine::_internal_mutable_b() {
  
  if (b_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::Point>(GetArenaForAllocation());
    b_ = p;
  }
  return b_;
}
inline ::carbon::geo::Point* AbLine::mutable_b() {
  ::carbon::geo::Point* _msg = _internal_mutable_b();
  // @@protoc_insertion_point(field_mutable:carbon.geo.AbLine.b)
  return _msg;
}
inline void AbLine::set_allocated_b(::carbon::geo::Point* b) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete b_;
  }
  if (b) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::geo::Point>::GetOwningArena(b);
    if (message_arena != submessage_arena) {
      b = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, b, submessage_arena);
    }
    
  } else {
    
  }
  b_ = b;
  // @@protoc_insertion_point(field_set_allocated:carbon.geo.AbLine.b)
}

// -------------------------------------------------------------------

// Polygon

// .carbon.geo.PolygonRing boundary = 1;
inline bool Polygon::_internal_has_boundary() const {
  return this != internal_default_instance() && boundary_ != nullptr;
}
inline bool Polygon::has_boundary() const {
  return _internal_has_boundary();
}
inline void Polygon::clear_boundary() {
  if (GetArenaForAllocation() == nullptr && boundary_ != nullptr) {
    delete boundary_;
  }
  boundary_ = nullptr;
}
inline const ::carbon::geo::PolygonRing& Polygon::_internal_boundary() const {
  const ::carbon::geo::PolygonRing* p = boundary_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::PolygonRing&>(
      ::carbon::geo::_PolygonRing_default_instance_);
}
inline const ::carbon::geo::PolygonRing& Polygon::boundary() const {
  // @@protoc_insertion_point(field_get:carbon.geo.Polygon.boundary)
  return _internal_boundary();
}
inline void Polygon::unsafe_arena_set_allocated_boundary(
    ::carbon::geo::PolygonRing* boundary) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(boundary_);
  }
  boundary_ = boundary;
  if (boundary) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.geo.Polygon.boundary)
}
inline ::carbon::geo::PolygonRing* Polygon::release_boundary() {
  
  ::carbon::geo::PolygonRing* temp = boundary_;
  boundary_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::PolygonRing* Polygon::unsafe_arena_release_boundary() {
  // @@protoc_insertion_point(field_release:carbon.geo.Polygon.boundary)
  
  ::carbon::geo::PolygonRing* temp = boundary_;
  boundary_ = nullptr;
  return temp;
}
inline ::carbon::geo::PolygonRing* Polygon::_internal_mutable_boundary() {
  
  if (boundary_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::PolygonRing>(GetArenaForAllocation());
    boundary_ = p;
  }
  return boundary_;
}
inline ::carbon::geo::PolygonRing* Polygon::mutable_boundary() {
  ::carbon::geo::PolygonRing* _msg = _internal_mutable_boundary();
  // @@protoc_insertion_point(field_mutable:carbon.geo.Polygon.boundary)
  return _msg;
}
inline void Polygon::set_allocated_boundary(::carbon::geo::PolygonRing* boundary) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete boundary_;
  }
  if (boundary) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::geo::PolygonRing>::GetOwningArena(boundary);
    if (message_arena != submessage_arena) {
      boundary = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, boundary, submessage_arena);
    }
    
  } else {
    
  }
  boundary_ = boundary;
  // @@protoc_insertion_point(field_set_allocated:carbon.geo.Polygon.boundary)
}

// repeated .carbon.geo.PolygonRing holes = 2;
inline int Polygon::_internal_holes_size() const {
  return holes_.size();
}
inline int Polygon::holes_size() const {
  return _internal_holes_size();
}
inline void Polygon::clear_holes() {
  holes_.Clear();
}
inline ::carbon::geo::PolygonRing* Polygon::mutable_holes(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.geo.Polygon.holes)
  return holes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::PolygonRing >*
Polygon::mutable_holes() {
  // @@protoc_insertion_point(field_mutable_list:carbon.geo.Polygon.holes)
  return &holes_;
}
inline const ::carbon::geo::PolygonRing& Polygon::_internal_holes(int index) const {
  return holes_.Get(index);
}
inline const ::carbon::geo::PolygonRing& Polygon::holes(int index) const {
  // @@protoc_insertion_point(field_get:carbon.geo.Polygon.holes)
  return _internal_holes(index);
}
inline ::carbon::geo::PolygonRing* Polygon::_internal_add_holes() {
  return holes_.Add();
}
inline ::carbon::geo::PolygonRing* Polygon::add_holes() {
  ::carbon::geo::PolygonRing* _add = _internal_add_holes();
  // @@protoc_insertion_point(field_add:carbon.geo.Polygon.holes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::PolygonRing >&
Polygon::holes() const {
  // @@protoc_insertion_point(field_list:carbon.geo.Polygon.holes)
  return holes_;
}

// -------------------------------------------------------------------

// PolygonRing

// repeated .carbon.geo.Point points = 1;
inline int PolygonRing::_internal_points_size() const {
  return points_.size();
}
inline int PolygonRing::points_size() const {
  return _internal_points_size();
}
inline void PolygonRing::clear_points() {
  points_.Clear();
}
inline ::carbon::geo::Point* PolygonRing::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.geo.PolygonRing.points)
  return points_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point >*
PolygonRing::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:carbon.geo.PolygonRing.points)
  return &points_;
}
inline const ::carbon::geo::Point& PolygonRing::_internal_points(int index) const {
  return points_.Get(index);
}
inline const ::carbon::geo::Point& PolygonRing::points(int index) const {
  // @@protoc_insertion_point(field_get:carbon.geo.PolygonRing.points)
  return _internal_points(index);
}
inline ::carbon::geo::Point* PolygonRing::_internal_add_points() {
  return points_.Add();
}
inline ::carbon::geo::Point* PolygonRing::add_points() {
  ::carbon::geo::Point* _add = _internal_add_points();
  // @@protoc_insertion_point(field_add:carbon.geo.PolygonRing.points)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Point >&
PolygonRing::points() const {
  // @@protoc_insertion_point(field_list:carbon.geo.PolygonRing.points)
  return points_;
}

// -------------------------------------------------------------------

// MultiPolygon

// repeated .carbon.geo.Polygon polygons = 1;
inline int MultiPolygon::_internal_polygons_size() const {
  return polygons_.size();
}
inline int MultiPolygon::polygons_size() const {
  return _internal_polygons_size();
}
inline void MultiPolygon::clear_polygons() {
  polygons_.Clear();
}
inline ::carbon::geo::Polygon* MultiPolygon::mutable_polygons(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.geo.MultiPolygon.polygons)
  return polygons_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Polygon >*
MultiPolygon::mutable_polygons() {
  // @@protoc_insertion_point(field_mutable_list:carbon.geo.MultiPolygon.polygons)
  return &polygons_;
}
inline const ::carbon::geo::Polygon& MultiPolygon::_internal_polygons(int index) const {
  return polygons_.Get(index);
}
inline const ::carbon::geo::Polygon& MultiPolygon::polygons(int index) const {
  // @@protoc_insertion_point(field_get:carbon.geo.MultiPolygon.polygons)
  return _internal_polygons(index);
}
inline ::carbon::geo::Polygon* MultiPolygon::_internal_add_polygons() {
  return polygons_.Add();
}
inline ::carbon::geo::Polygon* MultiPolygon::add_polygons() {
  ::carbon::geo::Polygon* _add = _internal_add_polygons();
  // @@protoc_insertion_point(field_add:carbon.geo.MultiPolygon.polygons)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::geo::Polygon >&
MultiPolygon::polygons() const {
  // @@protoc_insertion_point(field_list:carbon.geo.MultiPolygon.polygons)
  return polygons_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace geo
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::geo::FixType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::geo::FixType>() {
  return ::carbon::geo::FixType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2fgeo_2fgeo_2eproto

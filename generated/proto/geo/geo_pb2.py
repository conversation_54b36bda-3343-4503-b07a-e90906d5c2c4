# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/geo/geo.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/geo/geo.proto',
  package='carbon.geo',
  syntax='proto3',
  serialized_options=b'Z\tproto/geo',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x13proto/geo/geo.proto\x12\ncarbon.geo\x1a\x1fgoogle/protobuf/timestamp.proto\"f\n\x0b\x43\x61ptureInfo\x12%\n\x08\x66ix_type\x18\x01 \x01(\x0e\x32\x13.carbon.geo.FixType\x12\x30\n\x0c\x63\x61pture_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x10\n\x02Id\x12\n\n\x02id\x18\x01 \x01(\t\"\x87\x01\n\x05Point\x12\x0b\n\x03lng\x18\x01 \x01(\x01\x12\x0b\n\x03lat\x18\x02 \x01(\x01\x12\x0b\n\x03\x61lt\x18\x03 \x01(\x01\x12-\n\x0c\x63\x61pture_info\x18\x04 \x01(\x0b\x32\x17.carbon.geo.CaptureInfo\x12\x1a\n\x02id\x18\x05 \x01(\x0b\x32\x0e.carbon.geo.Id\x12\x0c\n\x04name\x18\x06 \x01(\t\"/\n\nLineString\x12!\n\x06points\x18\x01 \x03(\x0b\x32\x11.carbon.geo.Point\"D\n\x06\x41\x62Line\x12\x1c\n\x01\x61\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x1c\n\x01\x62\x18\x02 \x01(\x0b\x32\x11.carbon.geo.Point\"\\\n\x07Polygon\x12)\n\x08\x62oundary\x18\x01 \x01(\x0b\x32\x17.carbon.geo.PolygonRing\x12&\n\x05holes\x18\x02 \x03(\x0b\x32\x17.carbon.geo.PolygonRing\"0\n\x0bPolygonRing\x12!\n\x06points\x18\x01 \x03(\x0b\x32\x11.carbon.geo.Point\"5\n\x0cMultiPolygon\x12%\n\x08polygons\x18\x01 \x03(\x0b\x32\x13.carbon.geo.Polygon*\x82\x01\n\x07\x46ixType\x12\x18\n\x14\x46IX_TYPE_UNSPECIFIED\x10\x00\x12\n\n\x06NO_FIX\x10\x01\x12\x08\n\x04GNSS\x10\x02\x12\x15\n\x11\x44IFFERENTIAL_GNSS\x10\x03\x12\r\n\tRTK_FIXED\x10\x04\x12\r\n\tRTK_FLOAT\x10\x05\x12\x12\n\x0e\x44\x45\x41\x44_RECKONING\x10\x06\x42\x0bZ\tproto/geob\x06proto3'
  ,
  dependencies=[google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_FIXTYPE = _descriptor.EnumDescriptor(
  name='FixType',
  full_name='carbon.geo.FixType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FIX_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NO_FIX', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GNSS', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DIFFERENTIAL_GNSS', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RTK_FIXED', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RTK_FLOAT', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DEAD_RECKONING', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=647,
  serialized_end=777,
)
_sym_db.RegisterEnumDescriptor(_FIXTYPE)

FixType = enum_type_wrapper.EnumTypeWrapper(_FIXTYPE)
FIX_TYPE_UNSPECIFIED = 0
NO_FIX = 1
GNSS = 2
DIFFERENTIAL_GNSS = 3
RTK_FIXED = 4
RTK_FLOAT = 5
DEAD_RECKONING = 6



_CAPTUREINFO = _descriptor.Descriptor(
  name='CaptureInfo',
  full_name='carbon.geo.CaptureInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fix_type', full_name='carbon.geo.CaptureInfo.fix_type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='capture_time', full_name='carbon.geo.CaptureInfo.capture_time', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=68,
  serialized_end=170,
)


_ID = _descriptor.Descriptor(
  name='Id',
  full_name='carbon.geo.Id',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.geo.Id.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=172,
  serialized_end=188,
)


_POINT = _descriptor.Descriptor(
  name='Point',
  full_name='carbon.geo.Point',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lng', full_name='carbon.geo.Point.lng', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lat', full_name='carbon.geo.Point.lat', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alt', full_name='carbon.geo.Point.alt', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='capture_info', full_name='carbon.geo.Point.capture_info', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.geo.Point.id', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.geo.Point.name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=191,
  serialized_end=326,
)


_LINESTRING = _descriptor.Descriptor(
  name='LineString',
  full_name='carbon.geo.LineString',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='points', full_name='carbon.geo.LineString.points', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=328,
  serialized_end=375,
)


_ABLINE = _descriptor.Descriptor(
  name='AbLine',
  full_name='carbon.geo.AbLine',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='a', full_name='carbon.geo.AbLine.a', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='b', full_name='carbon.geo.AbLine.b', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=377,
  serialized_end=445,
)


_POLYGON = _descriptor.Descriptor(
  name='Polygon',
  full_name='carbon.geo.Polygon',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='boundary', full_name='carbon.geo.Polygon.boundary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='holes', full_name='carbon.geo.Polygon.holes', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=447,
  serialized_end=539,
)


_POLYGONRING = _descriptor.Descriptor(
  name='PolygonRing',
  full_name='carbon.geo.PolygonRing',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='points', full_name='carbon.geo.PolygonRing.points', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=541,
  serialized_end=589,
)


_MULTIPOLYGON = _descriptor.Descriptor(
  name='MultiPolygon',
  full_name='carbon.geo.MultiPolygon',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='polygons', full_name='carbon.geo.MultiPolygon.polygons', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=591,
  serialized_end=644,
)

_CAPTUREINFO.fields_by_name['fix_type'].enum_type = _FIXTYPE
_CAPTUREINFO.fields_by_name['capture_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_POINT.fields_by_name['capture_info'].message_type = _CAPTUREINFO
_POINT.fields_by_name['id'].message_type = _ID
_LINESTRING.fields_by_name['points'].message_type = _POINT
_ABLINE.fields_by_name['a'].message_type = _POINT
_ABLINE.fields_by_name['b'].message_type = _POINT
_POLYGON.fields_by_name['boundary'].message_type = _POLYGONRING
_POLYGON.fields_by_name['holes'].message_type = _POLYGONRING
_POLYGONRING.fields_by_name['points'].message_type = _POINT
_MULTIPOLYGON.fields_by_name['polygons'].message_type = _POLYGON
DESCRIPTOR.message_types_by_name['CaptureInfo'] = _CAPTUREINFO
DESCRIPTOR.message_types_by_name['Id'] = _ID
DESCRIPTOR.message_types_by_name['Point'] = _POINT
DESCRIPTOR.message_types_by_name['LineString'] = _LINESTRING
DESCRIPTOR.message_types_by_name['AbLine'] = _ABLINE
DESCRIPTOR.message_types_by_name['Polygon'] = _POLYGON
DESCRIPTOR.message_types_by_name['PolygonRing'] = _POLYGONRING
DESCRIPTOR.message_types_by_name['MultiPolygon'] = _MULTIPOLYGON
DESCRIPTOR.enum_types_by_name['FixType'] = _FIXTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CaptureInfo = _reflection.GeneratedProtocolMessageType('CaptureInfo', (_message.Message,), {
  'DESCRIPTOR' : _CAPTUREINFO,
  '__module__' : 'proto.geo.geo_pb2'
  # @@protoc_insertion_point(class_scope:carbon.geo.CaptureInfo)
  })
_sym_db.RegisterMessage(CaptureInfo)

Id = _reflection.GeneratedProtocolMessageType('Id', (_message.Message,), {
  'DESCRIPTOR' : _ID,
  '__module__' : 'proto.geo.geo_pb2'
  # @@protoc_insertion_point(class_scope:carbon.geo.Id)
  })
_sym_db.RegisterMessage(Id)

Point = _reflection.GeneratedProtocolMessageType('Point', (_message.Message,), {
  'DESCRIPTOR' : _POINT,
  '__module__' : 'proto.geo.geo_pb2'
  # @@protoc_insertion_point(class_scope:carbon.geo.Point)
  })
_sym_db.RegisterMessage(Point)

LineString = _reflection.GeneratedProtocolMessageType('LineString', (_message.Message,), {
  'DESCRIPTOR' : _LINESTRING,
  '__module__' : 'proto.geo.geo_pb2'
  # @@protoc_insertion_point(class_scope:carbon.geo.LineString)
  })
_sym_db.RegisterMessage(LineString)

AbLine = _reflection.GeneratedProtocolMessageType('AbLine', (_message.Message,), {
  'DESCRIPTOR' : _ABLINE,
  '__module__' : 'proto.geo.geo_pb2'
  # @@protoc_insertion_point(class_scope:carbon.geo.AbLine)
  })
_sym_db.RegisterMessage(AbLine)

Polygon = _reflection.GeneratedProtocolMessageType('Polygon', (_message.Message,), {
  'DESCRIPTOR' : _POLYGON,
  '__module__' : 'proto.geo.geo_pb2'
  # @@protoc_insertion_point(class_scope:carbon.geo.Polygon)
  })
_sym_db.RegisterMessage(Polygon)

PolygonRing = _reflection.GeneratedProtocolMessageType('PolygonRing', (_message.Message,), {
  'DESCRIPTOR' : _POLYGONRING,
  '__module__' : 'proto.geo.geo_pb2'
  # @@protoc_insertion_point(class_scope:carbon.geo.PolygonRing)
  })
_sym_db.RegisterMessage(PolygonRing)

MultiPolygon = _reflection.GeneratedProtocolMessageType('MultiPolygon', (_message.Message,), {
  'DESCRIPTOR' : _MULTIPOLYGON,
  '__module__' : 'proto.geo.geo_pb2'
  # @@protoc_insertion_point(class_scope:carbon.geo.MultiPolygon)
  })
_sym_db.RegisterMessage(MultiPolygon)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from google.protobuf.timestamp_pb2 import (
    Timestamp as google___protobuf___timestamp_pb2___Timestamp,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

FixTypeValue = typing___NewType('FixTypeValue', builtin___int)
type___FixTypeValue = FixTypeValue
FixType: _FixType
class _FixType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[FixTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    FIX_TYPE_UNSPECIFIED = typing___cast(FixTypeValue, 0)
    NO_FIX = typing___cast(FixTypeValue, 1)
    GNSS = typing___cast(FixTypeValue, 2)
    DIFFERENTIAL_GNSS = typing___cast(FixTypeValue, 3)
    RTK_FIXED = typing___cast(FixTypeValue, 4)
    RTK_FLOAT = typing___cast(FixTypeValue, 5)
    DEAD_RECKONING = typing___cast(FixTypeValue, 6)
FIX_TYPE_UNSPECIFIED = typing___cast(FixTypeValue, 0)
NO_FIX = typing___cast(FixTypeValue, 1)
GNSS = typing___cast(FixTypeValue, 2)
DIFFERENTIAL_GNSS = typing___cast(FixTypeValue, 3)
RTK_FIXED = typing___cast(FixTypeValue, 4)
RTK_FLOAT = typing___cast(FixTypeValue, 5)
DEAD_RECKONING = typing___cast(FixTypeValue, 6)

class CaptureInfo(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    fix_type: type___FixTypeValue = ...

    @property
    def capture_time(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    def __init__(self,
        *,
        fix_type : typing___Optional[type___FixTypeValue] = None,
        capture_time : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"capture_time",b"capture_time"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"capture_time",b"capture_time",u"fix_type",b"fix_type"]) -> None: ...
type___CaptureInfo = CaptureInfo

class Id(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___Id = Id

class Point(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lng: builtin___float = ...
    lat: builtin___float = ...
    alt: builtin___float = ...
    name: typing___Text = ...

    @property
    def capture_info(self) -> type___CaptureInfo: ...

    @property
    def id(self) -> type___Id: ...

    def __init__(self,
        *,
        lng : typing___Optional[builtin___float] = None,
        lat : typing___Optional[builtin___float] = None,
        alt : typing___Optional[builtin___float] = None,
        capture_info : typing___Optional[type___CaptureInfo] = None,
        id : typing___Optional[type___Id] = None,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"capture_info",b"capture_info",u"id",b"id"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"alt",b"alt",u"capture_info",b"capture_info",u"id",b"id",u"lat",b"lat",u"lng",b"lng",u"name",b"name"]) -> None: ...
type___Point = Point

class LineString(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def points(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Point]: ...

    def __init__(self,
        *,
        points : typing___Optional[typing___Iterable[type___Point]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"points",b"points"]) -> None: ...
type___LineString = LineString

class AbLine(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def a(self) -> type___Point: ...

    @property
    def b(self) -> type___Point: ...

    def __init__(self,
        *,
        a : typing___Optional[type___Point] = None,
        b : typing___Optional[type___Point] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"a",b"a",u"b",b"b"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"a",b"a",u"b",b"b"]) -> None: ...
type___AbLine = AbLine

class Polygon(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def boundary(self) -> type___PolygonRing: ...

    @property
    def holes(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PolygonRing]: ...

    def __init__(self,
        *,
        boundary : typing___Optional[type___PolygonRing] = None,
        holes : typing___Optional[typing___Iterable[type___PolygonRing]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"boundary",b"boundary"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"boundary",b"boundary",u"holes",b"holes"]) -> None: ...
type___Polygon = Polygon

class PolygonRing(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def points(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Point]: ...

    def __init__(self,
        *,
        points : typing___Optional[typing___Iterable[type___Point]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"points",b"points"]) -> None: ...
type___PolygonRing = PolygonRing

class MultiPolygon(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def polygons(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Polygon]: ...

    def __init__(self,
        *,
        polygons : typing___Optional[typing___Iterable[type___Polygon]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"polygons",b"polygons"]) -> None: ...
type___MultiPolygon = MultiPolygon

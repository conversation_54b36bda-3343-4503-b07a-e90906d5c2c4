// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/geo/geo.proto

#include "proto/geo/geo.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace geo {
constexpr CaptureInfo::CaptureInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : capture_time_(nullptr)
  , fix_type_(0)
{}
struct CaptureInfoDefaultTypeInternal {
  constexpr CaptureInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CaptureInfoDefaultTypeInternal() {}
  union {
    CaptureInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CaptureInfoDefaultTypeInternal _CaptureInfo_default_instance_;
constexpr Id::Id(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct IdDefaultTypeInternal {
  constexpr IdDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~IdDefaultTypeInternal() {}
  union {
    Id _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT IdDefaultTypeInternal _Id_default_instance_;
constexpr Point::Point(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , capture_info_(nullptr)
  , id_(nullptr)
  , lng_(0)
  , lat_(0)
  , alt_(0){}
struct PointDefaultTypeInternal {
  constexpr PointDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PointDefaultTypeInternal() {}
  union {
    Point _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PointDefaultTypeInternal _Point_default_instance_;
constexpr LineString::LineString(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : points_(){}
struct LineStringDefaultTypeInternal {
  constexpr LineStringDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LineStringDefaultTypeInternal() {}
  union {
    LineString _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LineStringDefaultTypeInternal _LineString_default_instance_;
constexpr AbLine::AbLine(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : a_(nullptr)
  , b_(nullptr){}
struct AbLineDefaultTypeInternal {
  constexpr AbLineDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AbLineDefaultTypeInternal() {}
  union {
    AbLine _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AbLineDefaultTypeInternal _AbLine_default_instance_;
constexpr Polygon::Polygon(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : holes_()
  , boundary_(nullptr){}
struct PolygonDefaultTypeInternal {
  constexpr PolygonDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PolygonDefaultTypeInternal() {}
  union {
    Polygon _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PolygonDefaultTypeInternal _Polygon_default_instance_;
constexpr PolygonRing::PolygonRing(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : points_(){}
struct PolygonRingDefaultTypeInternal {
  constexpr PolygonRingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PolygonRingDefaultTypeInternal() {}
  union {
    PolygonRing _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PolygonRingDefaultTypeInternal _PolygonRing_default_instance_;
constexpr MultiPolygon::MultiPolygon(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : polygons_(){}
struct MultiPolygonDefaultTypeInternal {
  constexpr MultiPolygonDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MultiPolygonDefaultTypeInternal() {}
  union {
    MultiPolygon _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MultiPolygonDefaultTypeInternal _MultiPolygon_default_instance_;
}  // namespace geo
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2fgeo_2fgeo_2eproto[8];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_2fgeo_2fgeo_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2fgeo_2fgeo_2eproto = nullptr;

const uint32_t TableStruct_proto_2fgeo_2fgeo_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::CaptureInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::CaptureInfo, fix_type_),
  PROTOBUF_FIELD_OFFSET(::carbon::geo::CaptureInfo, capture_time_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Id, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Id, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Point, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Point, lng_),
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Point, lat_),
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Point, alt_),
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Point, capture_info_),
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Point, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Point, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::LineString, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::LineString, points_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::AbLine, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::AbLine, a_),
  PROTOBUF_FIELD_OFFSET(::carbon::geo::AbLine, b_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Polygon, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Polygon, boundary_),
  PROTOBUF_FIELD_OFFSET(::carbon::geo::Polygon, holes_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::PolygonRing, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::PolygonRing, points_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::MultiPolygon, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::geo::MultiPolygon, polygons_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::geo::CaptureInfo)},
  { 8, -1, -1, sizeof(::carbon::geo::Id)},
  { 15, -1, -1, sizeof(::carbon::geo::Point)},
  { 27, -1, -1, sizeof(::carbon::geo::LineString)},
  { 34, -1, -1, sizeof(::carbon::geo::AbLine)},
  { 42, -1, -1, sizeof(::carbon::geo::Polygon)},
  { 50, -1, -1, sizeof(::carbon::geo::PolygonRing)},
  { 57, -1, -1, sizeof(::carbon::geo::MultiPolygon)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::geo::_CaptureInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::geo::_Id_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::geo::_Point_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::geo::_LineString_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::geo::_AbLine_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::geo::_Polygon_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::geo::_PolygonRing_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::geo::_MultiPolygon_default_instance_),
};

const char descriptor_table_protodef_proto_2fgeo_2fgeo_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\023proto/geo/geo.proto\022\ncarbon.geo\032\037googl"
  "e/protobuf/timestamp.proto\"f\n\013CaptureInf"
  "o\022%\n\010fix_type\030\001 \001(\0162\023.carbon.geo.FixType"
  "\0220\n\014capture_time\030\002 \001(\0132\032.google.protobuf"
  ".Timestamp\"\020\n\002Id\022\n\n\002id\030\001 \001(\t\"\207\001\n\005Point\022\013"
  "\n\003lng\030\001 \001(\001\022\013\n\003lat\030\002 \001(\001\022\013\n\003alt\030\003 \001(\001\022-\n"
  "\014capture_info\030\004 \001(\0132\027.carbon.geo.Capture"
  "Info\022\032\n\002id\030\005 \001(\0132\016.carbon.geo.Id\022\014\n\004name"
  "\030\006 \001(\t\"/\n\nLineString\022!\n\006points\030\001 \003(\0132\021.c"
  "arbon.geo.Point\"D\n\006AbLine\022\034\n\001a\030\001 \001(\0132\021.c"
  "arbon.geo.Point\022\034\n\001b\030\002 \001(\0132\021.carbon.geo."
  "Point\"\\\n\007Polygon\022)\n\010boundary\030\001 \001(\0132\027.car"
  "bon.geo.PolygonRing\022&\n\005holes\030\002 \003(\0132\027.car"
  "bon.geo.PolygonRing\"0\n\013PolygonRing\022!\n\006po"
  "ints\030\001 \003(\0132\021.carbon.geo.Point\"5\n\014MultiPo"
  "lygon\022%\n\010polygons\030\001 \003(\0132\023.carbon.geo.Pol"
  "ygon*\202\001\n\007FixType\022\030\n\024FIX_TYPE_UNSPECIFIED"
  "\020\000\022\n\n\006NO_FIX\020\001\022\010\n\004GNSS\020\002\022\025\n\021DIFFERENTIAL"
  "_GNSS\020\003\022\r\n\tRTK_FIXED\020\004\022\r\n\tRTK_FLOAT\020\005\022\022\n"
  "\016DEAD_RECKONING\020\006B\013Z\tproto/geob\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2fgeo_2fgeo_2eproto_deps[1] = {
  &::descriptor_table_google_2fprotobuf_2ftimestamp_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2fgeo_2fgeo_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fgeo_2fgeo_2eproto = {
  false, false, 798, descriptor_table_protodef_proto_2fgeo_2fgeo_2eproto, "proto/geo/geo.proto", 
  &descriptor_table_proto_2fgeo_2fgeo_2eproto_once, descriptor_table_proto_2fgeo_2fgeo_2eproto_deps, 1, 8,
  schemas, file_default_instances, TableStruct_proto_2fgeo_2fgeo_2eproto::offsets,
  file_level_metadata_proto_2fgeo_2fgeo_2eproto, file_level_enum_descriptors_proto_2fgeo_2fgeo_2eproto, file_level_service_descriptors_proto_2fgeo_2fgeo_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2fgeo_2fgeo_2eproto_getter() {
  return &descriptor_table_proto_2fgeo_2fgeo_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2fgeo_2fgeo_2eproto(&descriptor_table_proto_2fgeo_2fgeo_2eproto);
namespace carbon {
namespace geo {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FixType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2fgeo_2fgeo_2eproto);
  return file_level_enum_descriptors_proto_2fgeo_2fgeo_2eproto[0];
}
bool FixType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class CaptureInfo::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& capture_time(const CaptureInfo* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Timestamp&
CaptureInfo::_Internal::capture_time(const CaptureInfo* msg) {
  return *msg->capture_time_;
}
void CaptureInfo::clear_capture_time() {
  if (GetArenaForAllocation() == nullptr && capture_time_ != nullptr) {
    delete capture_time_;
  }
  capture_time_ = nullptr;
}
CaptureInfo::CaptureInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.geo.CaptureInfo)
}
CaptureInfo::CaptureInfo(const CaptureInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_capture_time()) {
    capture_time_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.capture_time_);
  } else {
    capture_time_ = nullptr;
  }
  fix_type_ = from.fix_type_;
  // @@protoc_insertion_point(copy_constructor:carbon.geo.CaptureInfo)
}

inline void CaptureInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&capture_time_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&fix_type_) -
    reinterpret_cast<char*>(&capture_time_)) + sizeof(fix_type_));
}

CaptureInfo::~CaptureInfo() {
  // @@protoc_insertion_point(destructor:carbon.geo.CaptureInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CaptureInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete capture_time_;
}

void CaptureInfo::ArenaDtor(void* object) {
  CaptureInfo* _this = reinterpret_cast< CaptureInfo* >(object);
  (void)_this;
}
void CaptureInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CaptureInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CaptureInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.geo.CaptureInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && capture_time_ != nullptr) {
    delete capture_time_;
  }
  capture_time_ = nullptr;
  fix_type_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CaptureInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.FixType fix_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_fix_type(static_cast<::carbon::geo::FixType>(val));
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp capture_time = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_capture_time(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CaptureInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.geo.CaptureInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.FixType fix_type = 1;
  if (this->_internal_fix_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_fix_type(), target);
  }

  // .google.protobuf.Timestamp capture_time = 2;
  if (this->_internal_has_capture_time()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::capture_time(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.geo.CaptureInfo)
  return target;
}

size_t CaptureInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.geo.CaptureInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .google.protobuf.Timestamp capture_time = 2;
  if (this->_internal_has_capture_time()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *capture_time_);
  }

  // .carbon.geo.FixType fix_type = 1;
  if (this->_internal_fix_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_fix_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CaptureInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CaptureInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CaptureInfo::GetClassData() const { return &_class_data_; }

void CaptureInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CaptureInfo *>(to)->MergeFrom(
      static_cast<const CaptureInfo &>(from));
}


void CaptureInfo::MergeFrom(const CaptureInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.geo.CaptureInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_capture_time()) {
    _internal_mutable_capture_time()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_capture_time());
  }
  if (from._internal_fix_type() != 0) {
    _internal_set_fix_type(from._internal_fix_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CaptureInfo::CopyFrom(const CaptureInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.geo.CaptureInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CaptureInfo::IsInitialized() const {
  return true;
}

void CaptureInfo::InternalSwap(CaptureInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CaptureInfo, fix_type_)
      + sizeof(CaptureInfo::fix_type_)
      - PROTOBUF_FIELD_OFFSET(CaptureInfo, capture_time_)>(
          reinterpret_cast<char*>(&capture_time_),
          reinterpret_cast<char*>(&other->capture_time_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CaptureInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fgeo_2fgeo_2eproto_getter, &descriptor_table_proto_2fgeo_2fgeo_2eproto_once,
      file_level_metadata_proto_2fgeo_2fgeo_2eproto[0]);
}

// ===================================================================

class Id::_Internal {
 public:
};

Id::Id(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.geo.Id)
}
Id::Id(const Id& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.geo.Id)
}

inline void Id::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Id::~Id() {
  // @@protoc_insertion_point(destructor:carbon.geo.Id)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Id::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void Id::ArenaDtor(void* object) {
  Id* _this = reinterpret_cast< Id* >(object);
  (void)_this;
}
void Id::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Id::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Id::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.geo.Id)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Id::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.geo.Id.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Id::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.geo.Id)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.geo.Id.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.geo.Id)
  return target;
}

size_t Id::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.geo.Id)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Id::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Id::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Id::GetClassData() const { return &_class_data_; }

void Id::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Id *>(to)->MergeFrom(
      static_cast<const Id &>(from));
}


void Id::MergeFrom(const Id& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.geo.Id)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Id::CopyFrom(const Id& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.geo.Id)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Id::IsInitialized() const {
  return true;
}

void Id::InternalSwap(Id* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata Id::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fgeo_2fgeo_2eproto_getter, &descriptor_table_proto_2fgeo_2fgeo_2eproto_once,
      file_level_metadata_proto_2fgeo_2fgeo_2eproto[1]);
}

// ===================================================================

class Point::_Internal {
 public:
  static const ::carbon::geo::CaptureInfo& capture_info(const Point* msg);
  static const ::carbon::geo::Id& id(const Point* msg);
};

const ::carbon::geo::CaptureInfo&
Point::_Internal::capture_info(const Point* msg) {
  return *msg->capture_info_;
}
const ::carbon::geo::Id&
Point::_Internal::id(const Point* msg) {
  return *msg->id_;
}
Point::Point(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.geo.Point)
}
Point::Point(const Point& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_capture_info()) {
    capture_info_ = new ::carbon::geo::CaptureInfo(*from.capture_info_);
  } else {
    capture_info_ = nullptr;
  }
  if (from._internal_has_id()) {
    id_ = new ::carbon::geo::Id(*from.id_);
  } else {
    id_ = nullptr;
  }
  ::memcpy(&lng_, &from.lng_,
    static_cast<size_t>(reinterpret_cast<char*>(&alt_) -
    reinterpret_cast<char*>(&lng_)) + sizeof(alt_));
  // @@protoc_insertion_point(copy_constructor:carbon.geo.Point)
}

inline void Point::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&capture_info_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&alt_) -
    reinterpret_cast<char*>(&capture_info_)) + sizeof(alt_));
}

Point::~Point() {
  // @@protoc_insertion_point(destructor:carbon.geo.Point)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Point::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete capture_info_;
  if (this != internal_default_instance()) delete id_;
}

void Point::ArenaDtor(void* object) {
  Point* _this = reinterpret_cast< Point* >(object);
  (void)_this;
}
void Point::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Point::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Point::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.geo.Point)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && capture_info_ != nullptr) {
    delete capture_info_;
  }
  capture_info_ = nullptr;
  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
  ::memset(&lng_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&alt_) -
      reinterpret_cast<char*>(&lng_)) + sizeof(alt_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Point::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double lng = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          lng_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double lat = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          lat_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double alt = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          alt_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // .carbon.geo.CaptureInfo capture_info = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_capture_info(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.geo.Id id = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_id(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.geo.Point.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Point::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.geo.Point)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double lng = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lng = this->_internal_lng();
  uint64_t raw_lng;
  memcpy(&raw_lng, &tmp_lng, sizeof(tmp_lng));
  if (raw_lng != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_lng(), target);
  }

  // double lat = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lat = this->_internal_lat();
  uint64_t raw_lat;
  memcpy(&raw_lat, &tmp_lat, sizeof(tmp_lat));
  if (raw_lat != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_lat(), target);
  }

  // double alt = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_alt = this->_internal_alt();
  uint64_t raw_alt;
  memcpy(&raw_alt, &tmp_alt, sizeof(tmp_alt));
  if (raw_alt != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_alt(), target);
  }

  // .carbon.geo.CaptureInfo capture_info = 4;
  if (this->_internal_has_capture_info()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::capture_info(this), target, stream);
  }

  // .carbon.geo.Id id = 5;
  if (this->_internal_has_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::id(this), target, stream);
  }

  // string name = 6;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.geo.Point.name");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.geo.Point)
  return target;
}

size_t Point::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.geo.Point)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 6;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .carbon.geo.CaptureInfo capture_info = 4;
  if (this->_internal_has_capture_info()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *capture_info_);
  }

  // .carbon.geo.Id id = 5;
  if (this->_internal_has_id()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *id_);
  }

  // double lng = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lng = this->_internal_lng();
  uint64_t raw_lng;
  memcpy(&raw_lng, &tmp_lng, sizeof(tmp_lng));
  if (raw_lng != 0) {
    total_size += 1 + 8;
  }

  // double lat = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lat = this->_internal_lat();
  uint64_t raw_lat;
  memcpy(&raw_lat, &tmp_lat, sizeof(tmp_lat));
  if (raw_lat != 0) {
    total_size += 1 + 8;
  }

  // double alt = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_alt = this->_internal_alt();
  uint64_t raw_alt;
  memcpy(&raw_alt, &tmp_alt, sizeof(tmp_alt));
  if (raw_alt != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Point::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Point::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Point::GetClassData() const { return &_class_data_; }

void Point::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Point *>(to)->MergeFrom(
      static_cast<const Point &>(from));
}


void Point::MergeFrom(const Point& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.geo.Point)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_has_capture_info()) {
    _internal_mutable_capture_info()->::carbon::geo::CaptureInfo::MergeFrom(from._internal_capture_info());
  }
  if (from._internal_has_id()) {
    _internal_mutable_id()->::carbon::geo::Id::MergeFrom(from._internal_id());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lng = from._internal_lng();
  uint64_t raw_lng;
  memcpy(&raw_lng, &tmp_lng, sizeof(tmp_lng));
  if (raw_lng != 0) {
    _internal_set_lng(from._internal_lng());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_lat = from._internal_lat();
  uint64_t raw_lat;
  memcpy(&raw_lat, &tmp_lat, sizeof(tmp_lat));
  if (raw_lat != 0) {
    _internal_set_lat(from._internal_lat());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_alt = from._internal_alt();
  uint64_t raw_alt;
  memcpy(&raw_alt, &tmp_alt, sizeof(tmp_alt));
  if (raw_alt != 0) {
    _internal_set_alt(from._internal_alt());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Point::CopyFrom(const Point& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.geo.Point)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Point::IsInitialized() const {
  return true;
}

void Point::InternalSwap(Point* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Point, alt_)
      + sizeof(Point::alt_)
      - PROTOBUF_FIELD_OFFSET(Point, capture_info_)>(
          reinterpret_cast<char*>(&capture_info_),
          reinterpret_cast<char*>(&other->capture_info_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Point::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fgeo_2fgeo_2eproto_getter, &descriptor_table_proto_2fgeo_2fgeo_2eproto_once,
      file_level_metadata_proto_2fgeo_2fgeo_2eproto[2]);
}

// ===================================================================

class LineString::_Internal {
 public:
};

LineString::LineString(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  points_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.geo.LineString)
}
LineString::LineString(const LineString& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      points_(from.points_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.geo.LineString)
}

inline void LineString::SharedCtor() {
}

LineString::~LineString() {
  // @@protoc_insertion_point(destructor:carbon.geo.LineString)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LineString::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LineString::ArenaDtor(void* object) {
  LineString* _this = reinterpret_cast< LineString* >(object);
  (void)_this;
}
void LineString::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LineString::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LineString::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.geo.LineString)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LineString::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.geo.Point points = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LineString::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.geo.LineString)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.geo.Point points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_points_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_points(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.geo.LineString)
  return target;
}

size_t LineString::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.geo.LineString)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.geo.Point points = 1;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LineString::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LineString::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LineString::GetClassData() const { return &_class_data_; }

void LineString::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LineString *>(to)->MergeFrom(
      static_cast<const LineString &>(from));
}


void LineString::MergeFrom(const LineString& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.geo.LineString)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LineString::CopyFrom(const LineString& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.geo.LineString)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LineString::IsInitialized() const {
  return true;
}

void LineString::InternalSwap(LineString* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  points_.InternalSwap(&other->points_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LineString::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fgeo_2fgeo_2eproto_getter, &descriptor_table_proto_2fgeo_2fgeo_2eproto_once,
      file_level_metadata_proto_2fgeo_2fgeo_2eproto[3]);
}

// ===================================================================

class AbLine::_Internal {
 public:
  static const ::carbon::geo::Point& a(const AbLine* msg);
  static const ::carbon::geo::Point& b(const AbLine* msg);
};

const ::carbon::geo::Point&
AbLine::_Internal::a(const AbLine* msg) {
  return *msg->a_;
}
const ::carbon::geo::Point&
AbLine::_Internal::b(const AbLine* msg) {
  return *msg->b_;
}
AbLine::AbLine(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.geo.AbLine)
}
AbLine::AbLine(const AbLine& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_a()) {
    a_ = new ::carbon::geo::Point(*from.a_);
  } else {
    a_ = nullptr;
  }
  if (from._internal_has_b()) {
    b_ = new ::carbon::geo::Point(*from.b_);
  } else {
    b_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.geo.AbLine)
}

inline void AbLine::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&a_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&b_) -
    reinterpret_cast<char*>(&a_)) + sizeof(b_));
}

AbLine::~AbLine() {
  // @@protoc_insertion_point(destructor:carbon.geo.AbLine)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AbLine::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete a_;
  if (this != internal_default_instance()) delete b_;
}

void AbLine::ArenaDtor(void* object) {
  AbLine* _this = reinterpret_cast< AbLine* >(object);
  (void)_this;
}
void AbLine::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AbLine::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AbLine::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.geo.AbLine)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && a_ != nullptr) {
    delete a_;
  }
  a_ = nullptr;
  if (GetArenaForAllocation() == nullptr && b_ != nullptr) {
    delete b_;
  }
  b_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AbLine::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.Point a = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_a(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.geo.Point b = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_b(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AbLine::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.geo.AbLine)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.Point a = 1;
  if (this->_internal_has_a()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::a(this), target, stream);
  }

  // .carbon.geo.Point b = 2;
  if (this->_internal_has_b()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::b(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.geo.AbLine)
  return target;
}

size_t AbLine::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.geo.AbLine)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.Point a = 1;
  if (this->_internal_has_a()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *a_);
  }

  // .carbon.geo.Point b = 2;
  if (this->_internal_has_b()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *b_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AbLine::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AbLine::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AbLine::GetClassData() const { return &_class_data_; }

void AbLine::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AbLine *>(to)->MergeFrom(
      static_cast<const AbLine &>(from));
}


void AbLine::MergeFrom(const AbLine& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.geo.AbLine)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_a()) {
    _internal_mutable_a()->::carbon::geo::Point::MergeFrom(from._internal_a());
  }
  if (from._internal_has_b()) {
    _internal_mutable_b()->::carbon::geo::Point::MergeFrom(from._internal_b());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AbLine::CopyFrom(const AbLine& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.geo.AbLine)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AbLine::IsInitialized() const {
  return true;
}

void AbLine::InternalSwap(AbLine* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AbLine, b_)
      + sizeof(AbLine::b_)
      - PROTOBUF_FIELD_OFFSET(AbLine, a_)>(
          reinterpret_cast<char*>(&a_),
          reinterpret_cast<char*>(&other->a_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AbLine::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fgeo_2fgeo_2eproto_getter, &descriptor_table_proto_2fgeo_2fgeo_2eproto_once,
      file_level_metadata_proto_2fgeo_2fgeo_2eproto[4]);
}

// ===================================================================

class Polygon::_Internal {
 public:
  static const ::carbon::geo::PolygonRing& boundary(const Polygon* msg);
};

const ::carbon::geo::PolygonRing&
Polygon::_Internal::boundary(const Polygon* msg) {
  return *msg->boundary_;
}
Polygon::Polygon(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  holes_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.geo.Polygon)
}
Polygon::Polygon(const Polygon& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      holes_(from.holes_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_boundary()) {
    boundary_ = new ::carbon::geo::PolygonRing(*from.boundary_);
  } else {
    boundary_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.geo.Polygon)
}

inline void Polygon::SharedCtor() {
boundary_ = nullptr;
}

Polygon::~Polygon() {
  // @@protoc_insertion_point(destructor:carbon.geo.Polygon)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Polygon::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete boundary_;
}

void Polygon::ArenaDtor(void* object) {
  Polygon* _this = reinterpret_cast< Polygon* >(object);
  (void)_this;
}
void Polygon::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Polygon::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Polygon::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.geo.Polygon)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  holes_.Clear();
  if (GetArenaForAllocation() == nullptr && boundary_ != nullptr) {
    delete boundary_;
  }
  boundary_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Polygon::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.PolygonRing boundary = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_boundary(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.geo.PolygonRing holes = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_holes(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Polygon::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.geo.Polygon)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.PolygonRing boundary = 1;
  if (this->_internal_has_boundary()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::boundary(this), target, stream);
  }

  // repeated .carbon.geo.PolygonRing holes = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_holes_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_holes(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.geo.Polygon)
  return target;
}

size_t Polygon::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.geo.Polygon)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.geo.PolygonRing holes = 2;
  total_size += 1UL * this->_internal_holes_size();
  for (const auto& msg : this->holes_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.geo.PolygonRing boundary = 1;
  if (this->_internal_has_boundary()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *boundary_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Polygon::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Polygon::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Polygon::GetClassData() const { return &_class_data_; }

void Polygon::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Polygon *>(to)->MergeFrom(
      static_cast<const Polygon &>(from));
}


void Polygon::MergeFrom(const Polygon& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.geo.Polygon)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  holes_.MergeFrom(from.holes_);
  if (from._internal_has_boundary()) {
    _internal_mutable_boundary()->::carbon::geo::PolygonRing::MergeFrom(from._internal_boundary());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Polygon::CopyFrom(const Polygon& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.geo.Polygon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polygon::IsInitialized() const {
  return true;
}

void Polygon::InternalSwap(Polygon* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  holes_.InternalSwap(&other->holes_);
  swap(boundary_, other->boundary_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Polygon::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fgeo_2fgeo_2eproto_getter, &descriptor_table_proto_2fgeo_2fgeo_2eproto_once,
      file_level_metadata_proto_2fgeo_2fgeo_2eproto[5]);
}

// ===================================================================

class PolygonRing::_Internal {
 public:
};

PolygonRing::PolygonRing(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  points_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.geo.PolygonRing)
}
PolygonRing::PolygonRing(const PolygonRing& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      points_(from.points_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.geo.PolygonRing)
}

inline void PolygonRing::SharedCtor() {
}

PolygonRing::~PolygonRing() {
  // @@protoc_insertion_point(destructor:carbon.geo.PolygonRing)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PolygonRing::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PolygonRing::ArenaDtor(void* object) {
  PolygonRing* _this = reinterpret_cast< PolygonRing* >(object);
  (void)_this;
}
void PolygonRing::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PolygonRing::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PolygonRing::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.geo.PolygonRing)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PolygonRing::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.geo.Point points = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PolygonRing::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.geo.PolygonRing)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.geo.Point points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_points_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_points(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.geo.PolygonRing)
  return target;
}

size_t PolygonRing::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.geo.PolygonRing)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.geo.Point points = 1;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PolygonRing::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PolygonRing::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PolygonRing::GetClassData() const { return &_class_data_; }

void PolygonRing::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PolygonRing *>(to)->MergeFrom(
      static_cast<const PolygonRing &>(from));
}


void PolygonRing::MergeFrom(const PolygonRing& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.geo.PolygonRing)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PolygonRing::CopyFrom(const PolygonRing& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.geo.PolygonRing)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PolygonRing::IsInitialized() const {
  return true;
}

void PolygonRing::InternalSwap(PolygonRing* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  points_.InternalSwap(&other->points_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PolygonRing::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fgeo_2fgeo_2eproto_getter, &descriptor_table_proto_2fgeo_2fgeo_2eproto_once,
      file_level_metadata_proto_2fgeo_2fgeo_2eproto[6]);
}

// ===================================================================

class MultiPolygon::_Internal {
 public:
};

MultiPolygon::MultiPolygon(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  polygons_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.geo.MultiPolygon)
}
MultiPolygon::MultiPolygon(const MultiPolygon& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      polygons_(from.polygons_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.geo.MultiPolygon)
}

inline void MultiPolygon::SharedCtor() {
}

MultiPolygon::~MultiPolygon() {
  // @@protoc_insertion_point(destructor:carbon.geo.MultiPolygon)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MultiPolygon::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void MultiPolygon::ArenaDtor(void* object) {
  MultiPolygon* _this = reinterpret_cast< MultiPolygon* >(object);
  (void)_this;
}
void MultiPolygon::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MultiPolygon::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MultiPolygon::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.geo.MultiPolygon)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  polygons_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MultiPolygon::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.geo.Polygon polygons = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_polygons(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MultiPolygon::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.geo.MultiPolygon)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.geo.Polygon polygons = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_polygons_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_polygons(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.geo.MultiPolygon)
  return target;
}

size_t MultiPolygon::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.geo.MultiPolygon)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.geo.Polygon polygons = 1;
  total_size += 1UL * this->_internal_polygons_size();
  for (const auto& msg : this->polygons_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MultiPolygon::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MultiPolygon::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MultiPolygon::GetClassData() const { return &_class_data_; }

void MultiPolygon::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MultiPolygon *>(to)->MergeFrom(
      static_cast<const MultiPolygon &>(from));
}


void MultiPolygon::MergeFrom(const MultiPolygon& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.geo.MultiPolygon)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  polygons_.MergeFrom(from.polygons_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MultiPolygon::CopyFrom(const MultiPolygon& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.geo.MultiPolygon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MultiPolygon::IsInitialized() const {
  return true;
}

void MultiPolygon::InternalSwap(MultiPolygon* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  polygons_.InternalSwap(&other->polygons_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MultiPolygon::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fgeo_2fgeo_2eproto_getter, &descriptor_table_proto_2fgeo_2fgeo_2eproto_once,
      file_level_metadata_proto_2fgeo_2fgeo_2eproto[7]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace geo
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::geo::CaptureInfo* Arena::CreateMaybeMessage< ::carbon::geo::CaptureInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::geo::CaptureInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::geo::Id* Arena::CreateMaybeMessage< ::carbon::geo::Id >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::geo::Id >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::geo::Point* Arena::CreateMaybeMessage< ::carbon::geo::Point >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::geo::Point >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::geo::LineString* Arena::CreateMaybeMessage< ::carbon::geo::LineString >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::geo::LineString >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::geo::AbLine* Arena::CreateMaybeMessage< ::carbon::geo::AbLine >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::geo::AbLine >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::geo::Polygon* Arena::CreateMaybeMessage< ::carbon::geo::Polygon >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::geo::Polygon >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::geo::PolygonRing* Arena::CreateMaybeMessage< ::carbon::geo::PolygonRing >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::geo::PolygonRing >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::geo::MultiPolygon* Arena::CreateMaybeMessage< ::carbon::geo::MultiPolygon >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::geo::MultiPolygon >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedComposite<PERSON>ieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class LaserPosition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row: builtin___int = ...
    slot: builtin___int = ...

    def __init__(self,
        *,
        row : typing___Optional[builtin___int] = None,
        slot : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row",b"row",u"slot",b"slot"]) -> None: ...
type___LaserPosition = LaserPosition

class LaserIdentifier(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    serial: typing___Text = ...

    @property
    def position(self) -> type___LaserPosition: ...

    def __init__(self,
        *,
        position : typing___Optional[type___LaserPosition] = None,
        serial : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"position",b"position"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"position",b"position",u"serial",b"serial"]) -> None: ...
type___LaserIdentifier = LaserIdentifier

class LaserLifeTime(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lifetime_sec: builtin___int = ...

    @property
    def id(self) -> type___LaserIdentifier: ...

    def __init__(self,
        *,
        id : typing___Optional[type___LaserIdentifier] = None,
        lifetime_sec : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"lifetime_sec",b"lifetime_sec"]) -> None: ...
type___LaserLifeTime = LaserLifeTime

class LaserEventTime(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_sec: builtin___int = ...

    @property
    def id(self) -> type___LaserIdentifier: ...

    def __init__(self,
        *,
        id : typing___Optional[type___LaserIdentifier] = None,
        timestamp_sec : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"timestamp_sec",b"timestamp_sec"]) -> None: ...
type___LaserEventTime = LaserEventTime

class LaserLifeTimes(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def lifetimes(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___LaserLifeTime]: ...

    def __init__(self,
        *,
        lifetimes : typing___Optional[typing___Iterable[type___LaserLifeTime]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lifetimes",b"lifetimes"]) -> None: ...
type___LaserLifeTimes = LaserLifeTimes

class LaserChangeTimes(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def installs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___LaserEventTime]: ...

    @property
    def removals(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___LaserEventTime]: ...

    def __init__(self,
        *,
        installs : typing___Optional[typing___Iterable[type___LaserEventTime]] = None,
        removals : typing___Optional[typing___Iterable[type___LaserEventTime]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"installs",b"installs",u"removals",b"removals"]) -> None: ...
type___LaserChangeTimes = LaserChangeTimes

class CountsByConclusionType(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    disarmed_weed: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    armed_weed: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    disarmed_crop: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    armed_crop: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...

    def __init__(self,
        *,
        disarmed_weed : typing___Optional[typing___Iterable[builtin___int]] = None,
        armed_weed : typing___Optional[typing___Iterable[builtin___int]] = None,
        disarmed_crop : typing___Optional[typing___Iterable[builtin___int]] = None,
        armed_crop : typing___Optional[typing___Iterable[builtin___int]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"armed_crop",b"armed_crop",u"armed_weed",b"armed_weed",u"disarmed_crop",b"disarmed_crop",u"disarmed_weed",b"disarmed_weed"]) -> None: ...
type___CountsByConclusionType = CountsByConclusionType

class TargetSizeData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cumulative_size: builtin___float = ...
    count: builtin___int = ...

    def __init__(self,
        *,
        cumulative_size : typing___Optional[builtin___float] = None,
        count : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"count",b"count",u"cumulative_size",b"cumulative_size"]) -> None: ...
type___TargetSizeData = TargetSizeData

class RequiredLaserTimeData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cumulative_time: builtin___int = ...
    count: builtin___int = ...

    def __init__(self,
        *,
        cumulative_time : typing___Optional[builtin___int] = None,
        count : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"count",b"count",u"cumulative_time",b"cumulative_time"]) -> None: ...
type___RequiredLaserTimeData = RequiredLaserTimeData

class SpatialPosition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    latitude: builtin___float = ...
    longitude: builtin___float = ...
    height_mm: builtin___float = ...
    timestamp_ms: builtin___int = ...
    ecef_x: builtin___float = ...
    ecef_y: builtin___float = ...
    ecef_z: builtin___float = ...

    def __init__(self,
        *,
        latitude : typing___Optional[builtin___float] = None,
        longitude : typing___Optional[builtin___float] = None,
        height_mm : typing___Optional[builtin___float] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ecef_x : typing___Optional[builtin___float] = None,
        ecef_y : typing___Optional[builtin___float] = None,
        ecef_z : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ecef_x",b"ecef_x",u"ecef_y",b"ecef_y",u"ecef_z",b"ecef_z",u"height_mm",b"height_mm",u"latitude",b"latitude",u"longitude",b"longitude",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___SpatialPosition = SpatialPosition

class WeedCounterChunk(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class CountsByCategoryEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___int = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___int] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___CountsByCategoryEntry = CountsByCategoryEntry

    valid_crop_count: builtin___int = ...

    @property
    def conclusion_counts(self) -> type___CountsByConclusionType: ...

    @property
    def weed_size_data(self) -> type___TargetSizeData: ...

    @property
    def crop_size_data(self) -> type___TargetSizeData: ...

    @property
    def counts_by_category(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___int]: ...

    @property
    def targeted_laser_time_data(self) -> type___RequiredLaserTimeData: ...

    @property
    def untargeted_laser_time_data(self) -> type___RequiredLaserTimeData: ...

    def __init__(self,
        *,
        conclusion_counts : typing___Optional[type___CountsByConclusionType] = None,
        weed_size_data : typing___Optional[type___TargetSizeData] = None,
        crop_size_data : typing___Optional[type___TargetSizeData] = None,
        counts_by_category : typing___Optional[typing___Mapping[typing___Text, builtin___int]] = None,
        targeted_laser_time_data : typing___Optional[type___RequiredLaserTimeData] = None,
        untargeted_laser_time_data : typing___Optional[type___RequiredLaserTimeData] = None,
        valid_crop_count : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"conclusion_counts",b"conclusion_counts",u"crop_size_data",b"crop_size_data",u"targeted_laser_time_data",b"targeted_laser_time_data",u"untargeted_laser_time_data",b"untargeted_laser_time_data",u"weed_size_data",b"weed_size_data"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"conclusion_counts",b"conclusion_counts",u"counts_by_category",b"counts_by_category",u"crop_size_data",b"crop_size_data",u"targeted_laser_time_data",b"targeted_laser_time_data",u"untargeted_laser_time_data",b"untargeted_laser_time_data",u"valid_crop_count",b"valid_crop_count",u"weed_size_data",b"weed_size_data"]) -> None: ...
type___WeedCounterChunk = WeedCounterChunk

class WheelEncoderSpatialData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    start_pos_m: builtin___float = ...
    end_pos_m: builtin___float = ...

    def __init__(self,
        *,
        start_pos_m : typing___Optional[builtin___float] = None,
        end_pos_m : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"end_pos_m",b"end_pos_m",u"start_pos_m",b"start_pos_m"]) -> None: ...
type___WheelEncoderSpatialData = WheelEncoderSpatialData

class BandingSpatialData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    percent_banded: builtin___float = ...

    def __init__(self,
        *,
        percent_banded : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"percent_banded",b"percent_banded"]) -> None: ...
type___BandingSpatialData = BandingSpatialData

class ImplementWidthSpatialData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    width_mm: builtin___float = ...

    def __init__(self,
        *,
        width_mm : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"width_mm",b"width_mm"]) -> None: ...
type___ImplementWidthSpatialData = ImplementWidthSpatialData

class VelocitySpatialMetric(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class AvgTargetVelEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___AvgTargetVelEntry = AvgTargetVelEntry


    @property
    def avg_target_vel(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___float]: ...

    def __init__(self,
        *,
        avg_target_vel : typing___Optional[typing___Mapping[typing___Text, builtin___float]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"avg_target_vel",b"avg_target_vel"]) -> None: ...
type___VelocitySpatialMetric = VelocitySpatialMetric

class JobMetric(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    job_id: typing___Text = ...

    def __init__(self,
        *,
        job_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"job_id",b"job_id"]) -> None: ...
type___JobMetric = JobMetric

class HWMetric(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lifted: builtin___bool = ...
    estopped: builtin___bool = ...
    laser_key: builtin___bool = ...
    interlock: builtin___bool = ...
    water_protect: builtin___bool = ...
    debug_mode: builtin___bool = ...

    def __init__(self,
        *,
        lifted : typing___Optional[builtin___bool] = None,
        estopped : typing___Optional[builtin___bool] = None,
        laser_key : typing___Optional[builtin___bool] = None,
        interlock : typing___Optional[builtin___bool] = None,
        water_protect : typing___Optional[builtin___bool] = None,
        debug_mode : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"debug_mode",b"debug_mode",u"estopped",b"estopped",u"interlock",b"interlock",u"laser_key",b"laser_key",u"lifted",b"lifted",u"water_protect",b"water_protect"]) -> None: ...
type___HWMetric = HWMetric

class SpatialMetricBlock(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    suspicious: builtin___bool = ...

    @property
    def start(self) -> type___SpatialPosition: ...

    @property
    def end(self) -> type___SpatialPosition: ...

    @property
    def weed_count(self) -> type___WeedCounterChunk: ...

    @property
    def we_data(self) -> type___WheelEncoderSpatialData: ...

    @property
    def banding_data(self) -> type___BandingSpatialData: ...

    @property
    def implement_width_data(self) -> type___ImplementWidthSpatialData: ...

    @property
    def vel_data(self) -> type___VelocitySpatialMetric: ...

    @property
    def start_left(self) -> type___SpatialPosition: ...

    @property
    def start_right(self) -> type___SpatialPosition: ...

    @property
    def end_left(self) -> type___SpatialPosition: ...

    @property
    def end_right(self) -> type___SpatialPosition: ...

    @property
    def job_metric(self) -> type___JobMetric: ...

    @property
    def hw_metric(self) -> type___HWMetric: ...

    def __init__(self,
        *,
        start : typing___Optional[type___SpatialPosition] = None,
        end : typing___Optional[type___SpatialPosition] = None,
        weed_count : typing___Optional[type___WeedCounterChunk] = None,
        we_data : typing___Optional[type___WheelEncoderSpatialData] = None,
        banding_data : typing___Optional[type___BandingSpatialData] = None,
        implement_width_data : typing___Optional[type___ImplementWidthSpatialData] = None,
        vel_data : typing___Optional[type___VelocitySpatialMetric] = None,
        start_left : typing___Optional[type___SpatialPosition] = None,
        start_right : typing___Optional[type___SpatialPosition] = None,
        end_left : typing___Optional[type___SpatialPosition] = None,
        end_right : typing___Optional[type___SpatialPosition] = None,
        job_metric : typing___Optional[type___JobMetric] = None,
        suspicious : typing___Optional[builtin___bool] = None,
        hw_metric : typing___Optional[type___HWMetric] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"banding_data",b"banding_data",u"end",b"end",u"end_left",b"end_left",u"end_right",b"end_right",u"hw_metric",b"hw_metric",u"implement_width_data",b"implement_width_data",u"job_metric",b"job_metric",u"start",b"start",u"start_left",b"start_left",u"start_right",b"start_right",u"vel_data",b"vel_data",u"we_data",b"we_data",u"weed_count",b"weed_count"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"banding_data",b"banding_data",u"end",b"end",u"end_left",b"end_left",u"end_right",b"end_right",u"hw_metric",b"hw_metric",u"implement_width_data",b"implement_width_data",u"job_metric",b"job_metric",u"start",b"start",u"start_left",b"start_left",u"start_right",b"start_right",u"suspicious",b"suspicious",u"vel_data",b"vel_data",u"we_data",b"we_data",u"weed_count",b"weed_count"]) -> None: ...
type___SpatialMetricBlock = SpatialMetricBlock

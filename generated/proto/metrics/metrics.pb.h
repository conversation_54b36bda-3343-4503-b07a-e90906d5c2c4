// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/metrics/metrics.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2fmetrics_2fmetrics_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2fmetrics_2fmetrics_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2fmetrics_2fmetrics_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2fmetrics_2fmetrics_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[20]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fmetrics_2fmetrics_2eproto;
namespace carbon {
namespace metrics {
class BandingSpatialData;
struct BandingSpatialDataDefaultTypeInternal;
extern BandingSpatialDataDefaultTypeInternal _BandingSpatialData_default_instance_;
class CountsByConclusionType;
struct CountsByConclusionTypeDefaultTypeInternal;
extern CountsByConclusionTypeDefaultTypeInternal _CountsByConclusionType_default_instance_;
class HWMetric;
struct HWMetricDefaultTypeInternal;
extern HWMetricDefaultTypeInternal _HWMetric_default_instance_;
class ImplementWidthSpatialData;
struct ImplementWidthSpatialDataDefaultTypeInternal;
extern ImplementWidthSpatialDataDefaultTypeInternal _ImplementWidthSpatialData_default_instance_;
class JobMetric;
struct JobMetricDefaultTypeInternal;
extern JobMetricDefaultTypeInternal _JobMetric_default_instance_;
class LaserChangeTimes;
struct LaserChangeTimesDefaultTypeInternal;
extern LaserChangeTimesDefaultTypeInternal _LaserChangeTimes_default_instance_;
class LaserEventTime;
struct LaserEventTimeDefaultTypeInternal;
extern LaserEventTimeDefaultTypeInternal _LaserEventTime_default_instance_;
class LaserIdentifier;
struct LaserIdentifierDefaultTypeInternal;
extern LaserIdentifierDefaultTypeInternal _LaserIdentifier_default_instance_;
class LaserLifeTime;
struct LaserLifeTimeDefaultTypeInternal;
extern LaserLifeTimeDefaultTypeInternal _LaserLifeTime_default_instance_;
class LaserLifeTimes;
struct LaserLifeTimesDefaultTypeInternal;
extern LaserLifeTimesDefaultTypeInternal _LaserLifeTimes_default_instance_;
class LaserPosition;
struct LaserPositionDefaultTypeInternal;
extern LaserPositionDefaultTypeInternal _LaserPosition_default_instance_;
class RequiredLaserTimeData;
struct RequiredLaserTimeDataDefaultTypeInternal;
extern RequiredLaserTimeDataDefaultTypeInternal _RequiredLaserTimeData_default_instance_;
class SpatialMetricBlock;
struct SpatialMetricBlockDefaultTypeInternal;
extern SpatialMetricBlockDefaultTypeInternal _SpatialMetricBlock_default_instance_;
class SpatialPosition;
struct SpatialPositionDefaultTypeInternal;
extern SpatialPositionDefaultTypeInternal _SpatialPosition_default_instance_;
class TargetSizeData;
struct TargetSizeDataDefaultTypeInternal;
extern TargetSizeDataDefaultTypeInternal _TargetSizeData_default_instance_;
class VelocitySpatialMetric;
struct VelocitySpatialMetricDefaultTypeInternal;
extern VelocitySpatialMetricDefaultTypeInternal _VelocitySpatialMetric_default_instance_;
class VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse;
struct VelocitySpatialMetric_AvgTargetVelEntry_DoNotUseDefaultTypeInternal;
extern VelocitySpatialMetric_AvgTargetVelEntry_DoNotUseDefaultTypeInternal _VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse_default_instance_;
class WeedCounterChunk;
struct WeedCounterChunkDefaultTypeInternal;
extern WeedCounterChunkDefaultTypeInternal _WeedCounterChunk_default_instance_;
class WeedCounterChunk_CountsByCategoryEntry_DoNotUse;
struct WeedCounterChunk_CountsByCategoryEntry_DoNotUseDefaultTypeInternal;
extern WeedCounterChunk_CountsByCategoryEntry_DoNotUseDefaultTypeInternal _WeedCounterChunk_CountsByCategoryEntry_DoNotUse_default_instance_;
class WheelEncoderSpatialData;
struct WheelEncoderSpatialDataDefaultTypeInternal;
extern WheelEncoderSpatialDataDefaultTypeInternal _WheelEncoderSpatialData_default_instance_;
}  // namespace metrics
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::metrics::BandingSpatialData* Arena::CreateMaybeMessage<::carbon::metrics::BandingSpatialData>(Arena*);
template<> ::carbon::metrics::CountsByConclusionType* Arena::CreateMaybeMessage<::carbon::metrics::CountsByConclusionType>(Arena*);
template<> ::carbon::metrics::HWMetric* Arena::CreateMaybeMessage<::carbon::metrics::HWMetric>(Arena*);
template<> ::carbon::metrics::ImplementWidthSpatialData* Arena::CreateMaybeMessage<::carbon::metrics::ImplementWidthSpatialData>(Arena*);
template<> ::carbon::metrics::JobMetric* Arena::CreateMaybeMessage<::carbon::metrics::JobMetric>(Arena*);
template<> ::carbon::metrics::LaserChangeTimes* Arena::CreateMaybeMessage<::carbon::metrics::LaserChangeTimes>(Arena*);
template<> ::carbon::metrics::LaserEventTime* Arena::CreateMaybeMessage<::carbon::metrics::LaserEventTime>(Arena*);
template<> ::carbon::metrics::LaserIdentifier* Arena::CreateMaybeMessage<::carbon::metrics::LaserIdentifier>(Arena*);
template<> ::carbon::metrics::LaserLifeTime* Arena::CreateMaybeMessage<::carbon::metrics::LaserLifeTime>(Arena*);
template<> ::carbon::metrics::LaserLifeTimes* Arena::CreateMaybeMessage<::carbon::metrics::LaserLifeTimes>(Arena*);
template<> ::carbon::metrics::LaserPosition* Arena::CreateMaybeMessage<::carbon::metrics::LaserPosition>(Arena*);
template<> ::carbon::metrics::RequiredLaserTimeData* Arena::CreateMaybeMessage<::carbon::metrics::RequiredLaserTimeData>(Arena*);
template<> ::carbon::metrics::SpatialMetricBlock* Arena::CreateMaybeMessage<::carbon::metrics::SpatialMetricBlock>(Arena*);
template<> ::carbon::metrics::SpatialPosition* Arena::CreateMaybeMessage<::carbon::metrics::SpatialPosition>(Arena*);
template<> ::carbon::metrics::TargetSizeData* Arena::CreateMaybeMessage<::carbon::metrics::TargetSizeData>(Arena*);
template<> ::carbon::metrics::VelocitySpatialMetric* Arena::CreateMaybeMessage<::carbon::metrics::VelocitySpatialMetric>(Arena*);
template<> ::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse>(Arena*);
template<> ::carbon::metrics::WeedCounterChunk* Arena::CreateMaybeMessage<::carbon::metrics::WeedCounterChunk>(Arena*);
template<> ::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse>(Arena*);
template<> ::carbon::metrics::WheelEncoderSpatialData* Arena::CreateMaybeMessage<::carbon::metrics::WheelEncoderSpatialData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace metrics {

// ===================================================================

class LaserPosition final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.LaserPosition) */ {
 public:
  inline LaserPosition() : LaserPosition(nullptr) {}
  ~LaserPosition() override;
  explicit constexpr LaserPosition(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaserPosition(const LaserPosition& from);
  LaserPosition(LaserPosition&& from) noexcept
    : LaserPosition() {
    *this = ::std::move(from);
  }

  inline LaserPosition& operator=(const LaserPosition& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaserPosition& operator=(LaserPosition&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaserPosition& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaserPosition* internal_default_instance() {
    return reinterpret_cast<const LaserPosition*>(
               &_LaserPosition_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(LaserPosition& a, LaserPosition& b) {
    a.Swap(&b);
  }
  inline void Swap(LaserPosition* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaserPosition* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaserPosition* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaserPosition>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaserPosition& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaserPosition& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaserPosition* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.LaserPosition";
  }
  protected:
  explicit LaserPosition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowFieldNumber = 1,
    kSlotFieldNumber = 2,
  };
  // uint32 row = 1;
  void clear_row();
  uint32_t row() const;
  void set_row(uint32_t value);
  private:
  uint32_t _internal_row() const;
  void _internal_set_row(uint32_t value);
  public:

  // uint32 slot = 2;
  void clear_slot();
  uint32_t slot() const;
  void set_slot(uint32_t value);
  private:
  uint32_t _internal_slot() const;
  void _internal_set_slot(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.LaserPosition)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t row_;
  uint32_t slot_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class LaserIdentifier final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.LaserIdentifier) */ {
 public:
  inline LaserIdentifier() : LaserIdentifier(nullptr) {}
  ~LaserIdentifier() override;
  explicit constexpr LaserIdentifier(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaserIdentifier(const LaserIdentifier& from);
  LaserIdentifier(LaserIdentifier&& from) noexcept
    : LaserIdentifier() {
    *this = ::std::move(from);
  }

  inline LaserIdentifier& operator=(const LaserIdentifier& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaserIdentifier& operator=(LaserIdentifier&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaserIdentifier& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaserIdentifier* internal_default_instance() {
    return reinterpret_cast<const LaserIdentifier*>(
               &_LaserIdentifier_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(LaserIdentifier& a, LaserIdentifier& b) {
    a.Swap(&b);
  }
  inline void Swap(LaserIdentifier* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaserIdentifier* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaserIdentifier* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaserIdentifier>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaserIdentifier& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaserIdentifier& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaserIdentifier* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.LaserIdentifier";
  }
  protected:
  explicit LaserIdentifier(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSerialFieldNumber = 2,
    kPositionFieldNumber = 1,
  };
  // string serial = 2;
  void clear_serial();
  const std::string& serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serial();
  PROTOBUF_NODISCARD std::string* release_serial();
  void set_allocated_serial(std::string* serial);
  private:
  const std::string& _internal_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serial(const std::string& value);
  std::string* _internal_mutable_serial();
  public:

  // .carbon.metrics.LaserPosition position = 1;
  bool has_position() const;
  private:
  bool _internal_has_position() const;
  public:
  void clear_position();
  const ::carbon::metrics::LaserPosition& position() const;
  PROTOBUF_NODISCARD ::carbon::metrics::LaserPosition* release_position();
  ::carbon::metrics::LaserPosition* mutable_position();
  void set_allocated_position(::carbon::metrics::LaserPosition* position);
  private:
  const ::carbon::metrics::LaserPosition& _internal_position() const;
  ::carbon::metrics::LaserPosition* _internal_mutable_position();
  public:
  void unsafe_arena_set_allocated_position(
      ::carbon::metrics::LaserPosition* position);
  ::carbon::metrics::LaserPosition* unsafe_arena_release_position();

  // @@protoc_insertion_point(class_scope:carbon.metrics.LaserIdentifier)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_;
  ::carbon::metrics::LaserPosition* position_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class LaserLifeTime final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.LaserLifeTime) */ {
 public:
  inline LaserLifeTime() : LaserLifeTime(nullptr) {}
  ~LaserLifeTime() override;
  explicit constexpr LaserLifeTime(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaserLifeTime(const LaserLifeTime& from);
  LaserLifeTime(LaserLifeTime&& from) noexcept
    : LaserLifeTime() {
    *this = ::std::move(from);
  }

  inline LaserLifeTime& operator=(const LaserLifeTime& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaserLifeTime& operator=(LaserLifeTime&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaserLifeTime& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaserLifeTime* internal_default_instance() {
    return reinterpret_cast<const LaserLifeTime*>(
               &_LaserLifeTime_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(LaserLifeTime& a, LaserLifeTime& b) {
    a.Swap(&b);
  }
  inline void Swap(LaserLifeTime* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaserLifeTime* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaserLifeTime* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaserLifeTime>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaserLifeTime& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaserLifeTime& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaserLifeTime* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.LaserLifeTime";
  }
  protected:
  explicit LaserLifeTime(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kLifetimeSecFieldNumber = 2,
  };
  // .carbon.metrics.LaserIdentifier id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  const ::carbon::metrics::LaserIdentifier& id() const;
  PROTOBUF_NODISCARD ::carbon::metrics::LaserIdentifier* release_id();
  ::carbon::metrics::LaserIdentifier* mutable_id();
  void set_allocated_id(::carbon::metrics::LaserIdentifier* id);
  private:
  const ::carbon::metrics::LaserIdentifier& _internal_id() const;
  ::carbon::metrics::LaserIdentifier* _internal_mutable_id();
  public:
  void unsafe_arena_set_allocated_id(
      ::carbon::metrics::LaserIdentifier* id);
  ::carbon::metrics::LaserIdentifier* unsafe_arena_release_id();

  // uint64 lifetime_sec = 2;
  void clear_lifetime_sec();
  uint64_t lifetime_sec() const;
  void set_lifetime_sec(uint64_t value);
  private:
  uint64_t _internal_lifetime_sec() const;
  void _internal_set_lifetime_sec(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.LaserLifeTime)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::metrics::LaserIdentifier* id_;
  uint64_t lifetime_sec_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class LaserEventTime final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.LaserEventTime) */ {
 public:
  inline LaserEventTime() : LaserEventTime(nullptr) {}
  ~LaserEventTime() override;
  explicit constexpr LaserEventTime(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaserEventTime(const LaserEventTime& from);
  LaserEventTime(LaserEventTime&& from) noexcept
    : LaserEventTime() {
    *this = ::std::move(from);
  }

  inline LaserEventTime& operator=(const LaserEventTime& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaserEventTime& operator=(LaserEventTime&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaserEventTime& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaserEventTime* internal_default_instance() {
    return reinterpret_cast<const LaserEventTime*>(
               &_LaserEventTime_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(LaserEventTime& a, LaserEventTime& b) {
    a.Swap(&b);
  }
  inline void Swap(LaserEventTime* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaserEventTime* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaserEventTime* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaserEventTime>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaserEventTime& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaserEventTime& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaserEventTime* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.LaserEventTime";
  }
  protected:
  explicit LaserEventTime(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kTimestampSecFieldNumber = 2,
  };
  // .carbon.metrics.LaserIdentifier id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  const ::carbon::metrics::LaserIdentifier& id() const;
  PROTOBUF_NODISCARD ::carbon::metrics::LaserIdentifier* release_id();
  ::carbon::metrics::LaserIdentifier* mutable_id();
  void set_allocated_id(::carbon::metrics::LaserIdentifier* id);
  private:
  const ::carbon::metrics::LaserIdentifier& _internal_id() const;
  ::carbon::metrics::LaserIdentifier* _internal_mutable_id();
  public:
  void unsafe_arena_set_allocated_id(
      ::carbon::metrics::LaserIdentifier* id);
  ::carbon::metrics::LaserIdentifier* unsafe_arena_release_id();

  // int64 timestamp_sec = 2;
  void clear_timestamp_sec();
  int64_t timestamp_sec() const;
  void set_timestamp_sec(int64_t value);
  private:
  int64_t _internal_timestamp_sec() const;
  void _internal_set_timestamp_sec(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.LaserEventTime)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::metrics::LaserIdentifier* id_;
  int64_t timestamp_sec_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class LaserLifeTimes final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.LaserLifeTimes) */ {
 public:
  inline LaserLifeTimes() : LaserLifeTimes(nullptr) {}
  ~LaserLifeTimes() override;
  explicit constexpr LaserLifeTimes(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaserLifeTimes(const LaserLifeTimes& from);
  LaserLifeTimes(LaserLifeTimes&& from) noexcept
    : LaserLifeTimes() {
    *this = ::std::move(from);
  }

  inline LaserLifeTimes& operator=(const LaserLifeTimes& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaserLifeTimes& operator=(LaserLifeTimes&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaserLifeTimes& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaserLifeTimes* internal_default_instance() {
    return reinterpret_cast<const LaserLifeTimes*>(
               &_LaserLifeTimes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(LaserLifeTimes& a, LaserLifeTimes& b) {
    a.Swap(&b);
  }
  inline void Swap(LaserLifeTimes* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaserLifeTimes* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaserLifeTimes* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaserLifeTimes>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaserLifeTimes& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaserLifeTimes& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaserLifeTimes* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.LaserLifeTimes";
  }
  protected:
  explicit LaserLifeTimes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLifetimesFieldNumber = 1,
  };
  // repeated .carbon.metrics.LaserLifeTime lifetimes = 1;
  int lifetimes_size() const;
  private:
  int _internal_lifetimes_size() const;
  public:
  void clear_lifetimes();
  ::carbon::metrics::LaserLifeTime* mutable_lifetimes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserLifeTime >*
      mutable_lifetimes();
  private:
  const ::carbon::metrics::LaserLifeTime& _internal_lifetimes(int index) const;
  ::carbon::metrics::LaserLifeTime* _internal_add_lifetimes();
  public:
  const ::carbon::metrics::LaserLifeTime& lifetimes(int index) const;
  ::carbon::metrics::LaserLifeTime* add_lifetimes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserLifeTime >&
      lifetimes() const;

  // @@protoc_insertion_point(class_scope:carbon.metrics.LaserLifeTimes)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserLifeTime > lifetimes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class LaserChangeTimes final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.LaserChangeTimes) */ {
 public:
  inline LaserChangeTimes() : LaserChangeTimes(nullptr) {}
  ~LaserChangeTimes() override;
  explicit constexpr LaserChangeTimes(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaserChangeTimes(const LaserChangeTimes& from);
  LaserChangeTimes(LaserChangeTimes&& from) noexcept
    : LaserChangeTimes() {
    *this = ::std::move(from);
  }

  inline LaserChangeTimes& operator=(const LaserChangeTimes& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaserChangeTimes& operator=(LaserChangeTimes&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaserChangeTimes& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaserChangeTimes* internal_default_instance() {
    return reinterpret_cast<const LaserChangeTimes*>(
               &_LaserChangeTimes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(LaserChangeTimes& a, LaserChangeTimes& b) {
    a.Swap(&b);
  }
  inline void Swap(LaserChangeTimes* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaserChangeTimes* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaserChangeTimes* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaserChangeTimes>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaserChangeTimes& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaserChangeTimes& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaserChangeTimes* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.LaserChangeTimes";
  }
  protected:
  explicit LaserChangeTimes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstallsFieldNumber = 1,
    kRemovalsFieldNumber = 2,
  };
  // repeated .carbon.metrics.LaserEventTime installs = 1;
  int installs_size() const;
  private:
  int _internal_installs_size() const;
  public:
  void clear_installs();
  ::carbon::metrics::LaserEventTime* mutable_installs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime >*
      mutable_installs();
  private:
  const ::carbon::metrics::LaserEventTime& _internal_installs(int index) const;
  ::carbon::metrics::LaserEventTime* _internal_add_installs();
  public:
  const ::carbon::metrics::LaserEventTime& installs(int index) const;
  ::carbon::metrics::LaserEventTime* add_installs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime >&
      installs() const;

  // repeated .carbon.metrics.LaserEventTime removals = 2;
  int removals_size() const;
  private:
  int _internal_removals_size() const;
  public:
  void clear_removals();
  ::carbon::metrics::LaserEventTime* mutable_removals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime >*
      mutable_removals();
  private:
  const ::carbon::metrics::LaserEventTime& _internal_removals(int index) const;
  ::carbon::metrics::LaserEventTime* _internal_add_removals();
  public:
  const ::carbon::metrics::LaserEventTime& removals(int index) const;
  ::carbon::metrics::LaserEventTime* add_removals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime >&
      removals() const;

  // @@protoc_insertion_point(class_scope:carbon.metrics.LaserChangeTimes)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime > installs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime > removals_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class CountsByConclusionType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.CountsByConclusionType) */ {
 public:
  inline CountsByConclusionType() : CountsByConclusionType(nullptr) {}
  ~CountsByConclusionType() override;
  explicit constexpr CountsByConclusionType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CountsByConclusionType(const CountsByConclusionType& from);
  CountsByConclusionType(CountsByConclusionType&& from) noexcept
    : CountsByConclusionType() {
    *this = ::std::move(from);
  }

  inline CountsByConclusionType& operator=(const CountsByConclusionType& from) {
    CopyFrom(from);
    return *this;
  }
  inline CountsByConclusionType& operator=(CountsByConclusionType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CountsByConclusionType& default_instance() {
    return *internal_default_instance();
  }
  static inline const CountsByConclusionType* internal_default_instance() {
    return reinterpret_cast<const CountsByConclusionType*>(
               &_CountsByConclusionType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(CountsByConclusionType& a, CountsByConclusionType& b) {
    a.Swap(&b);
  }
  inline void Swap(CountsByConclusionType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CountsByConclusionType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CountsByConclusionType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CountsByConclusionType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CountsByConclusionType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CountsByConclusionType& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CountsByConclusionType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.CountsByConclusionType";
  }
  protected:
  explicit CountsByConclusionType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDisarmedWeedFieldNumber = 1,
    kArmedWeedFieldNumber = 2,
    kDisarmedCropFieldNumber = 3,
    kArmedCropFieldNumber = 4,
  };
  // repeated uint32 disarmed_weed = 1;
  int disarmed_weed_size() const;
  private:
  int _internal_disarmed_weed_size() const;
  public:
  void clear_disarmed_weed();
  private:
  uint32_t _internal_disarmed_weed(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      _internal_disarmed_weed() const;
  void _internal_add_disarmed_weed(uint32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      _internal_mutable_disarmed_weed();
  public:
  uint32_t disarmed_weed(int index) const;
  void set_disarmed_weed(int index, uint32_t value);
  void add_disarmed_weed(uint32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      disarmed_weed() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      mutable_disarmed_weed();

  // repeated uint32 armed_weed = 2;
  int armed_weed_size() const;
  private:
  int _internal_armed_weed_size() const;
  public:
  void clear_armed_weed();
  private:
  uint32_t _internal_armed_weed(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      _internal_armed_weed() const;
  void _internal_add_armed_weed(uint32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      _internal_mutable_armed_weed();
  public:
  uint32_t armed_weed(int index) const;
  void set_armed_weed(int index, uint32_t value);
  void add_armed_weed(uint32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      armed_weed() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      mutable_armed_weed();

  // repeated uint32 disarmed_crop = 3;
  int disarmed_crop_size() const;
  private:
  int _internal_disarmed_crop_size() const;
  public:
  void clear_disarmed_crop();
  private:
  uint32_t _internal_disarmed_crop(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      _internal_disarmed_crop() const;
  void _internal_add_disarmed_crop(uint32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      _internal_mutable_disarmed_crop();
  public:
  uint32_t disarmed_crop(int index) const;
  void set_disarmed_crop(int index, uint32_t value);
  void add_disarmed_crop(uint32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      disarmed_crop() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      mutable_disarmed_crop();

  // repeated uint32 armed_crop = 4;
  int armed_crop_size() const;
  private:
  int _internal_armed_crop_size() const;
  public:
  void clear_armed_crop();
  private:
  uint32_t _internal_armed_crop(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      _internal_armed_crop() const;
  void _internal_add_armed_crop(uint32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      _internal_mutable_armed_crop();
  public:
  uint32_t armed_crop(int index) const;
  void set_armed_crop(int index, uint32_t value);
  void add_armed_crop(uint32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      armed_crop() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      mutable_armed_crop();

  // @@protoc_insertion_point(class_scope:carbon.metrics.CountsByConclusionType)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t > disarmed_weed_;
  mutable std::atomic<int> _disarmed_weed_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t > armed_weed_;
  mutable std::atomic<int> _armed_weed_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t > disarmed_crop_;
  mutable std::atomic<int> _disarmed_crop_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t > armed_crop_;
  mutable std::atomic<int> _armed_crop_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class TargetSizeData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.TargetSizeData) */ {
 public:
  inline TargetSizeData() : TargetSizeData(nullptr) {}
  ~TargetSizeData() override;
  explicit constexpr TargetSizeData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TargetSizeData(const TargetSizeData& from);
  TargetSizeData(TargetSizeData&& from) noexcept
    : TargetSizeData() {
    *this = ::std::move(from);
  }

  inline TargetSizeData& operator=(const TargetSizeData& from) {
    CopyFrom(from);
    return *this;
  }
  inline TargetSizeData& operator=(TargetSizeData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TargetSizeData& default_instance() {
    return *internal_default_instance();
  }
  static inline const TargetSizeData* internal_default_instance() {
    return reinterpret_cast<const TargetSizeData*>(
               &_TargetSizeData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(TargetSizeData& a, TargetSizeData& b) {
    a.Swap(&b);
  }
  inline void Swap(TargetSizeData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TargetSizeData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TargetSizeData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TargetSizeData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TargetSizeData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TargetSizeData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TargetSizeData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.TargetSizeData";
  }
  protected:
  explicit TargetSizeData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCumulativeSizeFieldNumber = 1,
    kCountFieldNumber = 2,
  };
  // double cumulative_size = 1;
  void clear_cumulative_size();
  double cumulative_size() const;
  void set_cumulative_size(double value);
  private:
  double _internal_cumulative_size() const;
  void _internal_set_cumulative_size(double value);
  public:

  // uint64 count = 2;
  void clear_count();
  uint64_t count() const;
  void set_count(uint64_t value);
  private:
  uint64_t _internal_count() const;
  void _internal_set_count(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.TargetSizeData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double cumulative_size_;
  uint64_t count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class RequiredLaserTimeData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.RequiredLaserTimeData) */ {
 public:
  inline RequiredLaserTimeData() : RequiredLaserTimeData(nullptr) {}
  ~RequiredLaserTimeData() override;
  explicit constexpr RequiredLaserTimeData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RequiredLaserTimeData(const RequiredLaserTimeData& from);
  RequiredLaserTimeData(RequiredLaserTimeData&& from) noexcept
    : RequiredLaserTimeData() {
    *this = ::std::move(from);
  }

  inline RequiredLaserTimeData& operator=(const RequiredLaserTimeData& from) {
    CopyFrom(from);
    return *this;
  }
  inline RequiredLaserTimeData& operator=(RequiredLaserTimeData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RequiredLaserTimeData& default_instance() {
    return *internal_default_instance();
  }
  static inline const RequiredLaserTimeData* internal_default_instance() {
    return reinterpret_cast<const RequiredLaserTimeData*>(
               &_RequiredLaserTimeData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(RequiredLaserTimeData& a, RequiredLaserTimeData& b) {
    a.Swap(&b);
  }
  inline void Swap(RequiredLaserTimeData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RequiredLaserTimeData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RequiredLaserTimeData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RequiredLaserTimeData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RequiredLaserTimeData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RequiredLaserTimeData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RequiredLaserTimeData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.RequiredLaserTimeData";
  }
  protected:
  explicit RequiredLaserTimeData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCumulativeTimeFieldNumber = 1,
    kCountFieldNumber = 2,
  };
  // uint64 cumulative_time = 1;
  void clear_cumulative_time();
  uint64_t cumulative_time() const;
  void set_cumulative_time(uint64_t value);
  private:
  uint64_t _internal_cumulative_time() const;
  void _internal_set_cumulative_time(uint64_t value);
  public:

  // uint64 count = 2;
  void clear_count();
  uint64_t count() const;
  void set_count(uint64_t value);
  private:
  uint64_t _internal_count() const;
  void _internal_set_count(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.RequiredLaserTimeData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t cumulative_time_;
  uint64_t count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class SpatialPosition final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.SpatialPosition) */ {
 public:
  inline SpatialPosition() : SpatialPosition(nullptr) {}
  ~SpatialPosition() override;
  explicit constexpr SpatialPosition(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SpatialPosition(const SpatialPosition& from);
  SpatialPosition(SpatialPosition&& from) noexcept
    : SpatialPosition() {
    *this = ::std::move(from);
  }

  inline SpatialPosition& operator=(const SpatialPosition& from) {
    CopyFrom(from);
    return *this;
  }
  inline SpatialPosition& operator=(SpatialPosition&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SpatialPosition& default_instance() {
    return *internal_default_instance();
  }
  static inline const SpatialPosition* internal_default_instance() {
    return reinterpret_cast<const SpatialPosition*>(
               &_SpatialPosition_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(SpatialPosition& a, SpatialPosition& b) {
    a.Swap(&b);
  }
  inline void Swap(SpatialPosition* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SpatialPosition* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SpatialPosition* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SpatialPosition>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SpatialPosition& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SpatialPosition& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SpatialPosition* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.SpatialPosition";
  }
  protected:
  explicit SpatialPosition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLatitudeFieldNumber = 1,
    kLongitudeFieldNumber = 2,
    kHeightMmFieldNumber = 3,
    kTimestampMsFieldNumber = 4,
    kEcefXFieldNumber = 5,
    kEcefYFieldNumber = 6,
    kEcefZFieldNumber = 7,
  };
  // double latitude = 1;
  void clear_latitude();
  double latitude() const;
  void set_latitude(double value);
  private:
  double _internal_latitude() const;
  void _internal_set_latitude(double value);
  public:

  // double longitude = 2;
  void clear_longitude();
  double longitude() const;
  void set_longitude(double value);
  private:
  double _internal_longitude() const;
  void _internal_set_longitude(double value);
  public:

  // double height_mm = 3;
  void clear_height_mm();
  double height_mm() const;
  void set_height_mm(double value);
  private:
  double _internal_height_mm() const;
  void _internal_set_height_mm(double value);
  public:

  // uint64 timestamp_ms = 4;
  void clear_timestamp_ms();
  uint64_t timestamp_ms() const;
  void set_timestamp_ms(uint64_t value);
  private:
  uint64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(uint64_t value);
  public:

  // double ecef_x = 5;
  void clear_ecef_x();
  double ecef_x() const;
  void set_ecef_x(double value);
  private:
  double _internal_ecef_x() const;
  void _internal_set_ecef_x(double value);
  public:

  // double ecef_y = 6;
  void clear_ecef_y();
  double ecef_y() const;
  void set_ecef_y(double value);
  private:
  double _internal_ecef_y() const;
  void _internal_set_ecef_y(double value);
  public:

  // double ecef_z = 7;
  void clear_ecef_z();
  double ecef_z() const;
  void set_ecef_z(double value);
  private:
  double _internal_ecef_z() const;
  void _internal_set_ecef_z(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.SpatialPosition)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double latitude_;
  double longitude_;
  double height_mm_;
  uint64_t timestamp_ms_;
  double ecef_x_;
  double ecef_y_;
  double ecef_z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class WeedCounterChunk_CountsByCategoryEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<WeedCounterChunk_CountsByCategoryEntry_DoNotUse, 
    std::string, uint32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<WeedCounterChunk_CountsByCategoryEntry_DoNotUse, 
    std::string, uint32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32> SuperType;
  WeedCounterChunk_CountsByCategoryEntry_DoNotUse();
  explicit constexpr WeedCounterChunk_CountsByCategoryEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit WeedCounterChunk_CountsByCategoryEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const WeedCounterChunk_CountsByCategoryEntry_DoNotUse& other);
  static const WeedCounterChunk_CountsByCategoryEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const WeedCounterChunk_CountsByCategoryEntry_DoNotUse*>(&_WeedCounterChunk_CountsByCategoryEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.metrics.WeedCounterChunk.CountsByCategoryEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class WeedCounterChunk final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.WeedCounterChunk) */ {
 public:
  inline WeedCounterChunk() : WeedCounterChunk(nullptr) {}
  ~WeedCounterChunk() override;
  explicit constexpr WeedCounterChunk(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WeedCounterChunk(const WeedCounterChunk& from);
  WeedCounterChunk(WeedCounterChunk&& from) noexcept
    : WeedCounterChunk() {
    *this = ::std::move(from);
  }

  inline WeedCounterChunk& operator=(const WeedCounterChunk& from) {
    CopyFrom(from);
    return *this;
  }
  inline WeedCounterChunk& operator=(WeedCounterChunk&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WeedCounterChunk& default_instance() {
    return *internal_default_instance();
  }
  static inline const WeedCounterChunk* internal_default_instance() {
    return reinterpret_cast<const WeedCounterChunk*>(
               &_WeedCounterChunk_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(WeedCounterChunk& a, WeedCounterChunk& b) {
    a.Swap(&b);
  }
  inline void Swap(WeedCounterChunk* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WeedCounterChunk* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WeedCounterChunk* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WeedCounterChunk>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WeedCounterChunk& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const WeedCounterChunk& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WeedCounterChunk* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.WeedCounterChunk";
  }
  protected:
  explicit WeedCounterChunk(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kCountsByCategoryFieldNumber = 4,
    kConclusionCountsFieldNumber = 1,
    kWeedSizeDataFieldNumber = 2,
    kCropSizeDataFieldNumber = 3,
    kTargetedLaserTimeDataFieldNumber = 5,
    kUntargetedLaserTimeDataFieldNumber = 6,
    kValidCropCountFieldNumber = 7,
  };
  // map<string, uint32> counts_by_category = 4;
  int counts_by_category_size() const;
  private:
  int _internal_counts_by_category_size() const;
  public:
  void clear_counts_by_category();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >&
      _internal_counts_by_category() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >*
      _internal_mutable_counts_by_category();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >&
      counts_by_category() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >*
      mutable_counts_by_category();

  // .carbon.metrics.CountsByConclusionType conclusion_counts = 1;
  bool has_conclusion_counts() const;
  private:
  bool _internal_has_conclusion_counts() const;
  public:
  void clear_conclusion_counts();
  const ::carbon::metrics::CountsByConclusionType& conclusion_counts() const;
  PROTOBUF_NODISCARD ::carbon::metrics::CountsByConclusionType* release_conclusion_counts();
  ::carbon::metrics::CountsByConclusionType* mutable_conclusion_counts();
  void set_allocated_conclusion_counts(::carbon::metrics::CountsByConclusionType* conclusion_counts);
  private:
  const ::carbon::metrics::CountsByConclusionType& _internal_conclusion_counts() const;
  ::carbon::metrics::CountsByConclusionType* _internal_mutable_conclusion_counts();
  public:
  void unsafe_arena_set_allocated_conclusion_counts(
      ::carbon::metrics::CountsByConclusionType* conclusion_counts);
  ::carbon::metrics::CountsByConclusionType* unsafe_arena_release_conclusion_counts();

  // .carbon.metrics.TargetSizeData weed_size_data = 2;
  bool has_weed_size_data() const;
  private:
  bool _internal_has_weed_size_data() const;
  public:
  void clear_weed_size_data();
  const ::carbon::metrics::TargetSizeData& weed_size_data() const;
  PROTOBUF_NODISCARD ::carbon::metrics::TargetSizeData* release_weed_size_data();
  ::carbon::metrics::TargetSizeData* mutable_weed_size_data();
  void set_allocated_weed_size_data(::carbon::metrics::TargetSizeData* weed_size_data);
  private:
  const ::carbon::metrics::TargetSizeData& _internal_weed_size_data() const;
  ::carbon::metrics::TargetSizeData* _internal_mutable_weed_size_data();
  public:
  void unsafe_arena_set_allocated_weed_size_data(
      ::carbon::metrics::TargetSizeData* weed_size_data);
  ::carbon::metrics::TargetSizeData* unsafe_arena_release_weed_size_data();

  // .carbon.metrics.TargetSizeData crop_size_data = 3;
  bool has_crop_size_data() const;
  private:
  bool _internal_has_crop_size_data() const;
  public:
  void clear_crop_size_data();
  const ::carbon::metrics::TargetSizeData& crop_size_data() const;
  PROTOBUF_NODISCARD ::carbon::metrics::TargetSizeData* release_crop_size_data();
  ::carbon::metrics::TargetSizeData* mutable_crop_size_data();
  void set_allocated_crop_size_data(::carbon::metrics::TargetSizeData* crop_size_data);
  private:
  const ::carbon::metrics::TargetSizeData& _internal_crop_size_data() const;
  ::carbon::metrics::TargetSizeData* _internal_mutable_crop_size_data();
  public:
  void unsafe_arena_set_allocated_crop_size_data(
      ::carbon::metrics::TargetSizeData* crop_size_data);
  ::carbon::metrics::TargetSizeData* unsafe_arena_release_crop_size_data();

  // .carbon.metrics.RequiredLaserTimeData targeted_laser_time_data = 5;
  bool has_targeted_laser_time_data() const;
  private:
  bool _internal_has_targeted_laser_time_data() const;
  public:
  void clear_targeted_laser_time_data();
  const ::carbon::metrics::RequiredLaserTimeData& targeted_laser_time_data() const;
  PROTOBUF_NODISCARD ::carbon::metrics::RequiredLaserTimeData* release_targeted_laser_time_data();
  ::carbon::metrics::RequiredLaserTimeData* mutable_targeted_laser_time_data();
  void set_allocated_targeted_laser_time_data(::carbon::metrics::RequiredLaserTimeData* targeted_laser_time_data);
  private:
  const ::carbon::metrics::RequiredLaserTimeData& _internal_targeted_laser_time_data() const;
  ::carbon::metrics::RequiredLaserTimeData* _internal_mutable_targeted_laser_time_data();
  public:
  void unsafe_arena_set_allocated_targeted_laser_time_data(
      ::carbon::metrics::RequiredLaserTimeData* targeted_laser_time_data);
  ::carbon::metrics::RequiredLaserTimeData* unsafe_arena_release_targeted_laser_time_data();

  // .carbon.metrics.RequiredLaserTimeData untargeted_laser_time_data = 6;
  bool has_untargeted_laser_time_data() const;
  private:
  bool _internal_has_untargeted_laser_time_data() const;
  public:
  void clear_untargeted_laser_time_data();
  const ::carbon::metrics::RequiredLaserTimeData& untargeted_laser_time_data() const;
  PROTOBUF_NODISCARD ::carbon::metrics::RequiredLaserTimeData* release_untargeted_laser_time_data();
  ::carbon::metrics::RequiredLaserTimeData* mutable_untargeted_laser_time_data();
  void set_allocated_untargeted_laser_time_data(::carbon::metrics::RequiredLaserTimeData* untargeted_laser_time_data);
  private:
  const ::carbon::metrics::RequiredLaserTimeData& _internal_untargeted_laser_time_data() const;
  ::carbon::metrics::RequiredLaserTimeData* _internal_mutable_untargeted_laser_time_data();
  public:
  void unsafe_arena_set_allocated_untargeted_laser_time_data(
      ::carbon::metrics::RequiredLaserTimeData* untargeted_laser_time_data);
  ::carbon::metrics::RequiredLaserTimeData* unsafe_arena_release_untargeted_laser_time_data();

  // uint64 valid_crop_count = 7;
  void clear_valid_crop_count();
  uint64_t valid_crop_count() const;
  void set_valid_crop_count(uint64_t value);
  private:
  uint64_t _internal_valid_crop_count() const;
  void _internal_set_valid_crop_count(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.WeedCounterChunk)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      WeedCounterChunk_CountsByCategoryEntry_DoNotUse,
      std::string, uint32_t,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32> counts_by_category_;
  ::carbon::metrics::CountsByConclusionType* conclusion_counts_;
  ::carbon::metrics::TargetSizeData* weed_size_data_;
  ::carbon::metrics::TargetSizeData* crop_size_data_;
  ::carbon::metrics::RequiredLaserTimeData* targeted_laser_time_data_;
  ::carbon::metrics::RequiredLaserTimeData* untargeted_laser_time_data_;
  uint64_t valid_crop_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class WheelEncoderSpatialData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.WheelEncoderSpatialData) */ {
 public:
  inline WheelEncoderSpatialData() : WheelEncoderSpatialData(nullptr) {}
  ~WheelEncoderSpatialData() override;
  explicit constexpr WheelEncoderSpatialData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WheelEncoderSpatialData(const WheelEncoderSpatialData& from);
  WheelEncoderSpatialData(WheelEncoderSpatialData&& from) noexcept
    : WheelEncoderSpatialData() {
    *this = ::std::move(from);
  }

  inline WheelEncoderSpatialData& operator=(const WheelEncoderSpatialData& from) {
    CopyFrom(from);
    return *this;
  }
  inline WheelEncoderSpatialData& operator=(WheelEncoderSpatialData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WheelEncoderSpatialData& default_instance() {
    return *internal_default_instance();
  }
  static inline const WheelEncoderSpatialData* internal_default_instance() {
    return reinterpret_cast<const WheelEncoderSpatialData*>(
               &_WheelEncoderSpatialData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(WheelEncoderSpatialData& a, WheelEncoderSpatialData& b) {
    a.Swap(&b);
  }
  inline void Swap(WheelEncoderSpatialData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WheelEncoderSpatialData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WheelEncoderSpatialData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WheelEncoderSpatialData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WheelEncoderSpatialData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const WheelEncoderSpatialData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WheelEncoderSpatialData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.WheelEncoderSpatialData";
  }
  protected:
  explicit WheelEncoderSpatialData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartPosMFieldNumber = 1,
    kEndPosMFieldNumber = 2,
  };
  // float start_pos_m = 1;
  void clear_start_pos_m();
  float start_pos_m() const;
  void set_start_pos_m(float value);
  private:
  float _internal_start_pos_m() const;
  void _internal_set_start_pos_m(float value);
  public:

  // float end_pos_m = 2;
  void clear_end_pos_m();
  float end_pos_m() const;
  void set_end_pos_m(float value);
  private:
  float _internal_end_pos_m() const;
  void _internal_set_end_pos_m(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.WheelEncoderSpatialData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float start_pos_m_;
  float end_pos_m_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class BandingSpatialData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.BandingSpatialData) */ {
 public:
  inline BandingSpatialData() : BandingSpatialData(nullptr) {}
  ~BandingSpatialData() override;
  explicit constexpr BandingSpatialData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BandingSpatialData(const BandingSpatialData& from);
  BandingSpatialData(BandingSpatialData&& from) noexcept
    : BandingSpatialData() {
    *this = ::std::move(from);
  }

  inline BandingSpatialData& operator=(const BandingSpatialData& from) {
    CopyFrom(from);
    return *this;
  }
  inline BandingSpatialData& operator=(BandingSpatialData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BandingSpatialData& default_instance() {
    return *internal_default_instance();
  }
  static inline const BandingSpatialData* internal_default_instance() {
    return reinterpret_cast<const BandingSpatialData*>(
               &_BandingSpatialData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(BandingSpatialData& a, BandingSpatialData& b) {
    a.Swap(&b);
  }
  inline void Swap(BandingSpatialData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BandingSpatialData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BandingSpatialData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BandingSpatialData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BandingSpatialData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BandingSpatialData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BandingSpatialData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.BandingSpatialData";
  }
  protected:
  explicit BandingSpatialData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPercentBandedFieldNumber = 1,
  };
  // float percent_banded = 1;
  void clear_percent_banded();
  float percent_banded() const;
  void set_percent_banded(float value);
  private:
  float _internal_percent_banded() const;
  void _internal_set_percent_banded(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.BandingSpatialData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float percent_banded_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class ImplementWidthSpatialData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.ImplementWidthSpatialData) */ {
 public:
  inline ImplementWidthSpatialData() : ImplementWidthSpatialData(nullptr) {}
  ~ImplementWidthSpatialData() override;
  explicit constexpr ImplementWidthSpatialData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ImplementWidthSpatialData(const ImplementWidthSpatialData& from);
  ImplementWidthSpatialData(ImplementWidthSpatialData&& from) noexcept
    : ImplementWidthSpatialData() {
    *this = ::std::move(from);
  }

  inline ImplementWidthSpatialData& operator=(const ImplementWidthSpatialData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ImplementWidthSpatialData& operator=(ImplementWidthSpatialData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ImplementWidthSpatialData& default_instance() {
    return *internal_default_instance();
  }
  static inline const ImplementWidthSpatialData* internal_default_instance() {
    return reinterpret_cast<const ImplementWidthSpatialData*>(
               &_ImplementWidthSpatialData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(ImplementWidthSpatialData& a, ImplementWidthSpatialData& b) {
    a.Swap(&b);
  }
  inline void Swap(ImplementWidthSpatialData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ImplementWidthSpatialData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ImplementWidthSpatialData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ImplementWidthSpatialData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ImplementWidthSpatialData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ImplementWidthSpatialData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ImplementWidthSpatialData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.ImplementWidthSpatialData";
  }
  protected:
  explicit ImplementWidthSpatialData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWidthMmFieldNumber = 1,
  };
  // float width_mm = 1;
  void clear_width_mm();
  float width_mm() const;
  void set_width_mm(float value);
  private:
  float _internal_width_mm() const;
  void _internal_set_width_mm(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.ImplementWidthSpatialData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float width_mm_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse, 
    std::string, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse, 
    std::string, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> SuperType;
  VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse();
  explicit constexpr VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse& other);
  static const VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse*>(&_VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class VelocitySpatialMetric final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.VelocitySpatialMetric) */ {
 public:
  inline VelocitySpatialMetric() : VelocitySpatialMetric(nullptr) {}
  ~VelocitySpatialMetric() override;
  explicit constexpr VelocitySpatialMetric(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VelocitySpatialMetric(const VelocitySpatialMetric& from);
  VelocitySpatialMetric(VelocitySpatialMetric&& from) noexcept
    : VelocitySpatialMetric() {
    *this = ::std::move(from);
  }

  inline VelocitySpatialMetric& operator=(const VelocitySpatialMetric& from) {
    CopyFrom(from);
    return *this;
  }
  inline VelocitySpatialMetric& operator=(VelocitySpatialMetric&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VelocitySpatialMetric& default_instance() {
    return *internal_default_instance();
  }
  static inline const VelocitySpatialMetric* internal_default_instance() {
    return reinterpret_cast<const VelocitySpatialMetric*>(
               &_VelocitySpatialMetric_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(VelocitySpatialMetric& a, VelocitySpatialMetric& b) {
    a.Swap(&b);
  }
  inline void Swap(VelocitySpatialMetric* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VelocitySpatialMetric* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VelocitySpatialMetric* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VelocitySpatialMetric>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VelocitySpatialMetric& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VelocitySpatialMetric& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VelocitySpatialMetric* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.VelocitySpatialMetric";
  }
  protected:
  explicit VelocitySpatialMetric(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAvgTargetVelFieldNumber = 1,
  };
  // map<string, float> avg_target_vel = 1;
  int avg_target_vel_size() const;
  private:
  int _internal_avg_target_vel_size() const;
  public:
  void clear_avg_target_vel();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
      _internal_avg_target_vel() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
      _internal_mutable_avg_target_vel();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
      avg_target_vel() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
      mutable_avg_target_vel();

  // @@protoc_insertion_point(class_scope:carbon.metrics.VelocitySpatialMetric)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse,
      std::string, float,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> avg_target_vel_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class JobMetric final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.JobMetric) */ {
 public:
  inline JobMetric() : JobMetric(nullptr) {}
  ~JobMetric() override;
  explicit constexpr JobMetric(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  JobMetric(const JobMetric& from);
  JobMetric(JobMetric&& from) noexcept
    : JobMetric() {
    *this = ::std::move(from);
  }

  inline JobMetric& operator=(const JobMetric& from) {
    CopyFrom(from);
    return *this;
  }
  inline JobMetric& operator=(JobMetric&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const JobMetric& default_instance() {
    return *internal_default_instance();
  }
  static inline const JobMetric* internal_default_instance() {
    return reinterpret_cast<const JobMetric*>(
               &_JobMetric_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(JobMetric& a, JobMetric& b) {
    a.Swap(&b);
  }
  inline void Swap(JobMetric* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(JobMetric* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  JobMetric* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<JobMetric>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const JobMetric& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const JobMetric& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(JobMetric* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.JobMetric";
  }
  protected:
  explicit JobMetric(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobIdFieldNumber = 1,
  };
  // string job_id = 1;
  void clear_job_id();
  const std::string& job_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_job_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_job_id();
  PROTOBUF_NODISCARD std::string* release_job_id();
  void set_allocated_job_id(std::string* job_id);
  private:
  const std::string& _internal_job_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_job_id(const std::string& value);
  std::string* _internal_mutable_job_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.JobMetric)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr job_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class HWMetric final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.HWMetric) */ {
 public:
  inline HWMetric() : HWMetric(nullptr) {}
  ~HWMetric() override;
  explicit constexpr HWMetric(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HWMetric(const HWMetric& from);
  HWMetric(HWMetric&& from) noexcept
    : HWMetric() {
    *this = ::std::move(from);
  }

  inline HWMetric& operator=(const HWMetric& from) {
    CopyFrom(from);
    return *this;
  }
  inline HWMetric& operator=(HWMetric&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HWMetric& default_instance() {
    return *internal_default_instance();
  }
  static inline const HWMetric* internal_default_instance() {
    return reinterpret_cast<const HWMetric*>(
               &_HWMetric_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(HWMetric& a, HWMetric& b) {
    a.Swap(&b);
  }
  inline void Swap(HWMetric* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HWMetric* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HWMetric* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HWMetric>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HWMetric& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HWMetric& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HWMetric* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.HWMetric";
  }
  protected:
  explicit HWMetric(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLiftedFieldNumber = 1,
    kEstoppedFieldNumber = 2,
    kLaserKeyFieldNumber = 3,
    kInterlockFieldNumber = 4,
    kWaterProtectFieldNumber = 5,
    kDebugModeFieldNumber = 6,
  };
  // bool lifted = 1;
  void clear_lifted();
  bool lifted() const;
  void set_lifted(bool value);
  private:
  bool _internal_lifted() const;
  void _internal_set_lifted(bool value);
  public:

  // bool estopped = 2;
  void clear_estopped();
  bool estopped() const;
  void set_estopped(bool value);
  private:
  bool _internal_estopped() const;
  void _internal_set_estopped(bool value);
  public:

  // bool laser_key = 3;
  void clear_laser_key();
  bool laser_key() const;
  void set_laser_key(bool value);
  private:
  bool _internal_laser_key() const;
  void _internal_set_laser_key(bool value);
  public:

  // bool interlock = 4;
  void clear_interlock();
  bool interlock() const;
  void set_interlock(bool value);
  private:
  bool _internal_interlock() const;
  void _internal_set_interlock(bool value);
  public:

  // bool water_protect = 5;
  void clear_water_protect();
  bool water_protect() const;
  void set_water_protect(bool value);
  private:
  bool _internal_water_protect() const;
  void _internal_set_water_protect(bool value);
  public:

  // bool debug_mode = 6;
  void clear_debug_mode();
  bool debug_mode() const;
  void set_debug_mode(bool value);
  private:
  bool _internal_debug_mode() const;
  void _internal_set_debug_mode(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.HWMetric)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool lifted_;
  bool estopped_;
  bool laser_key_;
  bool interlock_;
  bool water_protect_;
  bool debug_mode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// -------------------------------------------------------------------

class SpatialMetricBlock final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.metrics.SpatialMetricBlock) */ {
 public:
  inline SpatialMetricBlock() : SpatialMetricBlock(nullptr) {}
  ~SpatialMetricBlock() override;
  explicit constexpr SpatialMetricBlock(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SpatialMetricBlock(const SpatialMetricBlock& from);
  SpatialMetricBlock(SpatialMetricBlock&& from) noexcept
    : SpatialMetricBlock() {
    *this = ::std::move(from);
  }

  inline SpatialMetricBlock& operator=(const SpatialMetricBlock& from) {
    CopyFrom(from);
    return *this;
  }
  inline SpatialMetricBlock& operator=(SpatialMetricBlock&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SpatialMetricBlock& default_instance() {
    return *internal_default_instance();
  }
  static inline const SpatialMetricBlock* internal_default_instance() {
    return reinterpret_cast<const SpatialMetricBlock*>(
               &_SpatialMetricBlock_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(SpatialMetricBlock& a, SpatialMetricBlock& b) {
    a.Swap(&b);
  }
  inline void Swap(SpatialMetricBlock* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SpatialMetricBlock* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SpatialMetricBlock* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SpatialMetricBlock>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SpatialMetricBlock& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SpatialMetricBlock& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SpatialMetricBlock* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.metrics.SpatialMetricBlock";
  }
  protected:
  explicit SpatialMetricBlock(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartFieldNumber = 1,
    kEndFieldNumber = 2,
    kWeedCountFieldNumber = 3,
    kWeDataFieldNumber = 4,
    kBandingDataFieldNumber = 5,
    kImplementWidthDataFieldNumber = 6,
    kVelDataFieldNumber = 7,
    kStartLeftFieldNumber = 8,
    kStartRightFieldNumber = 9,
    kEndLeftFieldNumber = 10,
    kEndRightFieldNumber = 11,
    kJobMetricFieldNumber = 12,
    kHwMetricFieldNumber = 14,
    kSuspiciousFieldNumber = 13,
  };
  // .carbon.metrics.SpatialPosition start = 1;
  bool has_start() const;
  private:
  bool _internal_has_start() const;
  public:
  void clear_start();
  const ::carbon::metrics::SpatialPosition& start() const;
  PROTOBUF_NODISCARD ::carbon::metrics::SpatialPosition* release_start();
  ::carbon::metrics::SpatialPosition* mutable_start();
  void set_allocated_start(::carbon::metrics::SpatialPosition* start);
  private:
  const ::carbon::metrics::SpatialPosition& _internal_start() const;
  ::carbon::metrics::SpatialPosition* _internal_mutable_start();
  public:
  void unsafe_arena_set_allocated_start(
      ::carbon::metrics::SpatialPosition* start);
  ::carbon::metrics::SpatialPosition* unsafe_arena_release_start();

  // .carbon.metrics.SpatialPosition end = 2;
  bool has_end() const;
  private:
  bool _internal_has_end() const;
  public:
  void clear_end();
  const ::carbon::metrics::SpatialPosition& end() const;
  PROTOBUF_NODISCARD ::carbon::metrics::SpatialPosition* release_end();
  ::carbon::metrics::SpatialPosition* mutable_end();
  void set_allocated_end(::carbon::metrics::SpatialPosition* end);
  private:
  const ::carbon::metrics::SpatialPosition& _internal_end() const;
  ::carbon::metrics::SpatialPosition* _internal_mutable_end();
  public:
  void unsafe_arena_set_allocated_end(
      ::carbon::metrics::SpatialPosition* end);
  ::carbon::metrics::SpatialPosition* unsafe_arena_release_end();

  // .carbon.metrics.WeedCounterChunk weed_count = 3;
  bool has_weed_count() const;
  private:
  bool _internal_has_weed_count() const;
  public:
  void clear_weed_count();
  const ::carbon::metrics::WeedCounterChunk& weed_count() const;
  PROTOBUF_NODISCARD ::carbon::metrics::WeedCounterChunk* release_weed_count();
  ::carbon::metrics::WeedCounterChunk* mutable_weed_count();
  void set_allocated_weed_count(::carbon::metrics::WeedCounterChunk* weed_count);
  private:
  const ::carbon::metrics::WeedCounterChunk& _internal_weed_count() const;
  ::carbon::metrics::WeedCounterChunk* _internal_mutable_weed_count();
  public:
  void unsafe_arena_set_allocated_weed_count(
      ::carbon::metrics::WeedCounterChunk* weed_count);
  ::carbon::metrics::WeedCounterChunk* unsafe_arena_release_weed_count();

  // .carbon.metrics.WheelEncoderSpatialData we_data = 4;
  bool has_we_data() const;
  private:
  bool _internal_has_we_data() const;
  public:
  void clear_we_data();
  const ::carbon::metrics::WheelEncoderSpatialData& we_data() const;
  PROTOBUF_NODISCARD ::carbon::metrics::WheelEncoderSpatialData* release_we_data();
  ::carbon::metrics::WheelEncoderSpatialData* mutable_we_data();
  void set_allocated_we_data(::carbon::metrics::WheelEncoderSpatialData* we_data);
  private:
  const ::carbon::metrics::WheelEncoderSpatialData& _internal_we_data() const;
  ::carbon::metrics::WheelEncoderSpatialData* _internal_mutable_we_data();
  public:
  void unsafe_arena_set_allocated_we_data(
      ::carbon::metrics::WheelEncoderSpatialData* we_data);
  ::carbon::metrics::WheelEncoderSpatialData* unsafe_arena_release_we_data();

  // .carbon.metrics.BandingSpatialData banding_data = 5;
  bool has_banding_data() const;
  private:
  bool _internal_has_banding_data() const;
  public:
  void clear_banding_data();
  const ::carbon::metrics::BandingSpatialData& banding_data() const;
  PROTOBUF_NODISCARD ::carbon::metrics::BandingSpatialData* release_banding_data();
  ::carbon::metrics::BandingSpatialData* mutable_banding_data();
  void set_allocated_banding_data(::carbon::metrics::BandingSpatialData* banding_data);
  private:
  const ::carbon::metrics::BandingSpatialData& _internal_banding_data() const;
  ::carbon::metrics::BandingSpatialData* _internal_mutable_banding_data();
  public:
  void unsafe_arena_set_allocated_banding_data(
      ::carbon::metrics::BandingSpatialData* banding_data);
  ::carbon::metrics::BandingSpatialData* unsafe_arena_release_banding_data();

  // .carbon.metrics.ImplementWidthSpatialData implement_width_data = 6;
  bool has_implement_width_data() const;
  private:
  bool _internal_has_implement_width_data() const;
  public:
  void clear_implement_width_data();
  const ::carbon::metrics::ImplementWidthSpatialData& implement_width_data() const;
  PROTOBUF_NODISCARD ::carbon::metrics::ImplementWidthSpatialData* release_implement_width_data();
  ::carbon::metrics::ImplementWidthSpatialData* mutable_implement_width_data();
  void set_allocated_implement_width_data(::carbon::metrics::ImplementWidthSpatialData* implement_width_data);
  private:
  const ::carbon::metrics::ImplementWidthSpatialData& _internal_implement_width_data() const;
  ::carbon::metrics::ImplementWidthSpatialData* _internal_mutable_implement_width_data();
  public:
  void unsafe_arena_set_allocated_implement_width_data(
      ::carbon::metrics::ImplementWidthSpatialData* implement_width_data);
  ::carbon::metrics::ImplementWidthSpatialData* unsafe_arena_release_implement_width_data();

  // .carbon.metrics.VelocitySpatialMetric vel_data = 7;
  bool has_vel_data() const;
  private:
  bool _internal_has_vel_data() const;
  public:
  void clear_vel_data();
  const ::carbon::metrics::VelocitySpatialMetric& vel_data() const;
  PROTOBUF_NODISCARD ::carbon::metrics::VelocitySpatialMetric* release_vel_data();
  ::carbon::metrics::VelocitySpatialMetric* mutable_vel_data();
  void set_allocated_vel_data(::carbon::metrics::VelocitySpatialMetric* vel_data);
  private:
  const ::carbon::metrics::VelocitySpatialMetric& _internal_vel_data() const;
  ::carbon::metrics::VelocitySpatialMetric* _internal_mutable_vel_data();
  public:
  void unsafe_arena_set_allocated_vel_data(
      ::carbon::metrics::VelocitySpatialMetric* vel_data);
  ::carbon::metrics::VelocitySpatialMetric* unsafe_arena_release_vel_data();

  // .carbon.metrics.SpatialPosition start_left = 8;
  bool has_start_left() const;
  private:
  bool _internal_has_start_left() const;
  public:
  void clear_start_left();
  const ::carbon::metrics::SpatialPosition& start_left() const;
  PROTOBUF_NODISCARD ::carbon::metrics::SpatialPosition* release_start_left();
  ::carbon::metrics::SpatialPosition* mutable_start_left();
  void set_allocated_start_left(::carbon::metrics::SpatialPosition* start_left);
  private:
  const ::carbon::metrics::SpatialPosition& _internal_start_left() const;
  ::carbon::metrics::SpatialPosition* _internal_mutable_start_left();
  public:
  void unsafe_arena_set_allocated_start_left(
      ::carbon::metrics::SpatialPosition* start_left);
  ::carbon::metrics::SpatialPosition* unsafe_arena_release_start_left();

  // .carbon.metrics.SpatialPosition start_right = 9;
  bool has_start_right() const;
  private:
  bool _internal_has_start_right() const;
  public:
  void clear_start_right();
  const ::carbon::metrics::SpatialPosition& start_right() const;
  PROTOBUF_NODISCARD ::carbon::metrics::SpatialPosition* release_start_right();
  ::carbon::metrics::SpatialPosition* mutable_start_right();
  void set_allocated_start_right(::carbon::metrics::SpatialPosition* start_right);
  private:
  const ::carbon::metrics::SpatialPosition& _internal_start_right() const;
  ::carbon::metrics::SpatialPosition* _internal_mutable_start_right();
  public:
  void unsafe_arena_set_allocated_start_right(
      ::carbon::metrics::SpatialPosition* start_right);
  ::carbon::metrics::SpatialPosition* unsafe_arena_release_start_right();

  // .carbon.metrics.SpatialPosition end_left = 10;
  bool has_end_left() const;
  private:
  bool _internal_has_end_left() const;
  public:
  void clear_end_left();
  const ::carbon::metrics::SpatialPosition& end_left() const;
  PROTOBUF_NODISCARD ::carbon::metrics::SpatialPosition* release_end_left();
  ::carbon::metrics::SpatialPosition* mutable_end_left();
  void set_allocated_end_left(::carbon::metrics::SpatialPosition* end_left);
  private:
  const ::carbon::metrics::SpatialPosition& _internal_end_left() const;
  ::carbon::metrics::SpatialPosition* _internal_mutable_end_left();
  public:
  void unsafe_arena_set_allocated_end_left(
      ::carbon::metrics::SpatialPosition* end_left);
  ::carbon::metrics::SpatialPosition* unsafe_arena_release_end_left();

  // .carbon.metrics.SpatialPosition end_right = 11;
  bool has_end_right() const;
  private:
  bool _internal_has_end_right() const;
  public:
  void clear_end_right();
  const ::carbon::metrics::SpatialPosition& end_right() const;
  PROTOBUF_NODISCARD ::carbon::metrics::SpatialPosition* release_end_right();
  ::carbon::metrics::SpatialPosition* mutable_end_right();
  void set_allocated_end_right(::carbon::metrics::SpatialPosition* end_right);
  private:
  const ::carbon::metrics::SpatialPosition& _internal_end_right() const;
  ::carbon::metrics::SpatialPosition* _internal_mutable_end_right();
  public:
  void unsafe_arena_set_allocated_end_right(
      ::carbon::metrics::SpatialPosition* end_right);
  ::carbon::metrics::SpatialPosition* unsafe_arena_release_end_right();

  // .carbon.metrics.JobMetric job_metric = 12;
  bool has_job_metric() const;
  private:
  bool _internal_has_job_metric() const;
  public:
  void clear_job_metric();
  const ::carbon::metrics::JobMetric& job_metric() const;
  PROTOBUF_NODISCARD ::carbon::metrics::JobMetric* release_job_metric();
  ::carbon::metrics::JobMetric* mutable_job_metric();
  void set_allocated_job_metric(::carbon::metrics::JobMetric* job_metric);
  private:
  const ::carbon::metrics::JobMetric& _internal_job_metric() const;
  ::carbon::metrics::JobMetric* _internal_mutable_job_metric();
  public:
  void unsafe_arena_set_allocated_job_metric(
      ::carbon::metrics::JobMetric* job_metric);
  ::carbon::metrics::JobMetric* unsafe_arena_release_job_metric();

  // .carbon.metrics.HWMetric hw_metric = 14;
  bool has_hw_metric() const;
  private:
  bool _internal_has_hw_metric() const;
  public:
  void clear_hw_metric();
  const ::carbon::metrics::HWMetric& hw_metric() const;
  PROTOBUF_NODISCARD ::carbon::metrics::HWMetric* release_hw_metric();
  ::carbon::metrics::HWMetric* mutable_hw_metric();
  void set_allocated_hw_metric(::carbon::metrics::HWMetric* hw_metric);
  private:
  const ::carbon::metrics::HWMetric& _internal_hw_metric() const;
  ::carbon::metrics::HWMetric* _internal_mutable_hw_metric();
  public:
  void unsafe_arena_set_allocated_hw_metric(
      ::carbon::metrics::HWMetric* hw_metric);
  ::carbon::metrics::HWMetric* unsafe_arena_release_hw_metric();

  // bool suspicious = 13;
  void clear_suspicious();
  bool suspicious() const;
  void set_suspicious(bool value);
  private:
  bool _internal_suspicious() const;
  void _internal_set_suspicious(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.metrics.SpatialMetricBlock)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::metrics::SpatialPosition* start_;
  ::carbon::metrics::SpatialPosition* end_;
  ::carbon::metrics::WeedCounterChunk* weed_count_;
  ::carbon::metrics::WheelEncoderSpatialData* we_data_;
  ::carbon::metrics::BandingSpatialData* banding_data_;
  ::carbon::metrics::ImplementWidthSpatialData* implement_width_data_;
  ::carbon::metrics::VelocitySpatialMetric* vel_data_;
  ::carbon::metrics::SpatialPosition* start_left_;
  ::carbon::metrics::SpatialPosition* start_right_;
  ::carbon::metrics::SpatialPosition* end_left_;
  ::carbon::metrics::SpatialPosition* end_right_;
  ::carbon::metrics::JobMetric* job_metric_;
  ::carbon::metrics::HWMetric* hw_metric_;
  bool suspicious_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2fmetrics_2fmetrics_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// LaserPosition

// uint32 row = 1;
inline void LaserPosition::clear_row() {
  row_ = 0u;
}
inline uint32_t LaserPosition::_internal_row() const {
  return row_;
}
inline uint32_t LaserPosition::row() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserPosition.row)
  return _internal_row();
}
inline void LaserPosition::_internal_set_row(uint32_t value) {
  
  row_ = value;
}
inline void LaserPosition::set_row(uint32_t value) {
  _internal_set_row(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.LaserPosition.row)
}

// uint32 slot = 2;
inline void LaserPosition::clear_slot() {
  slot_ = 0u;
}
inline uint32_t LaserPosition::_internal_slot() const {
  return slot_;
}
inline uint32_t LaserPosition::slot() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserPosition.slot)
  return _internal_slot();
}
inline void LaserPosition::_internal_set_slot(uint32_t value) {
  
  slot_ = value;
}
inline void LaserPosition::set_slot(uint32_t value) {
  _internal_set_slot(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.LaserPosition.slot)
}

// -------------------------------------------------------------------

// LaserIdentifier

// .carbon.metrics.LaserPosition position = 1;
inline bool LaserIdentifier::_internal_has_position() const {
  return this != internal_default_instance() && position_ != nullptr;
}
inline bool LaserIdentifier::has_position() const {
  return _internal_has_position();
}
inline void LaserIdentifier::clear_position() {
  if (GetArenaForAllocation() == nullptr && position_ != nullptr) {
    delete position_;
  }
  position_ = nullptr;
}
inline const ::carbon::metrics::LaserPosition& LaserIdentifier::_internal_position() const {
  const ::carbon::metrics::LaserPosition* p = position_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::LaserPosition&>(
      ::carbon::metrics::_LaserPosition_default_instance_);
}
inline const ::carbon::metrics::LaserPosition& LaserIdentifier::position() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserIdentifier.position)
  return _internal_position();
}
inline void LaserIdentifier::unsafe_arena_set_allocated_position(
    ::carbon::metrics::LaserPosition* position) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(position_);
  }
  position_ = position;
  if (position) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.LaserIdentifier.position)
}
inline ::carbon::metrics::LaserPosition* LaserIdentifier::release_position() {
  
  ::carbon::metrics::LaserPosition* temp = position_;
  position_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::LaserPosition* LaserIdentifier::unsafe_arena_release_position() {
  // @@protoc_insertion_point(field_release:carbon.metrics.LaserIdentifier.position)
  
  ::carbon::metrics::LaserPosition* temp = position_;
  position_ = nullptr;
  return temp;
}
inline ::carbon::metrics::LaserPosition* LaserIdentifier::_internal_mutable_position() {
  
  if (position_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::LaserPosition>(GetArenaForAllocation());
    position_ = p;
  }
  return position_;
}
inline ::carbon::metrics::LaserPosition* LaserIdentifier::mutable_position() {
  ::carbon::metrics::LaserPosition* _msg = _internal_mutable_position();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.LaserIdentifier.position)
  return _msg;
}
inline void LaserIdentifier::set_allocated_position(::carbon::metrics::LaserPosition* position) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete position_;
  }
  if (position) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::LaserPosition>::GetOwningArena(position);
    if (message_arena != submessage_arena) {
      position = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, position, submessage_arena);
    }
    
  } else {
    
  }
  position_ = position;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.LaserIdentifier.position)
}

// string serial = 2;
inline void LaserIdentifier::clear_serial() {
  serial_.ClearToEmpty();
}
inline const std::string& LaserIdentifier::serial() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserIdentifier.serial)
  return _internal_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LaserIdentifier::set_serial(ArgT0&& arg0, ArgT... args) {
 
 serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.metrics.LaserIdentifier.serial)
}
inline std::string* LaserIdentifier::mutable_serial() {
  std::string* _s = _internal_mutable_serial();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.LaserIdentifier.serial)
  return _s;
}
inline const std::string& LaserIdentifier::_internal_serial() const {
  return serial_.Get();
}
inline void LaserIdentifier::_internal_set_serial(const std::string& value) {
  
  serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LaserIdentifier::_internal_mutable_serial() {
  
  return serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LaserIdentifier::release_serial() {
  // @@protoc_insertion_point(field_release:carbon.metrics.LaserIdentifier.serial)
  return serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LaserIdentifier::set_allocated_serial(std::string* serial) {
  if (serial != nullptr) {
    
  } else {
    
  }
  serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.LaserIdentifier.serial)
}

// -------------------------------------------------------------------

// LaserLifeTime

// .carbon.metrics.LaserIdentifier id = 1;
inline bool LaserLifeTime::_internal_has_id() const {
  return this != internal_default_instance() && id_ != nullptr;
}
inline bool LaserLifeTime::has_id() const {
  return _internal_has_id();
}
inline void LaserLifeTime::clear_id() {
  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
}
inline const ::carbon::metrics::LaserIdentifier& LaserLifeTime::_internal_id() const {
  const ::carbon::metrics::LaserIdentifier* p = id_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::LaserIdentifier&>(
      ::carbon::metrics::_LaserIdentifier_default_instance_);
}
inline const ::carbon::metrics::LaserIdentifier& LaserLifeTime::id() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserLifeTime.id)
  return _internal_id();
}
inline void LaserLifeTime::unsafe_arena_set_allocated_id(
    ::carbon::metrics::LaserIdentifier* id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(id_);
  }
  id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.LaserLifeTime.id)
}
inline ::carbon::metrics::LaserIdentifier* LaserLifeTime::release_id() {
  
  ::carbon::metrics::LaserIdentifier* temp = id_;
  id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::LaserIdentifier* LaserLifeTime::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_release:carbon.metrics.LaserLifeTime.id)
  
  ::carbon::metrics::LaserIdentifier* temp = id_;
  id_ = nullptr;
  return temp;
}
inline ::carbon::metrics::LaserIdentifier* LaserLifeTime::_internal_mutable_id() {
  
  if (id_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::LaserIdentifier>(GetArenaForAllocation());
    id_ = p;
  }
  return id_;
}
inline ::carbon::metrics::LaserIdentifier* LaserLifeTime::mutable_id() {
  ::carbon::metrics::LaserIdentifier* _msg = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.LaserLifeTime.id)
  return _msg;
}
inline void LaserLifeTime::set_allocated_id(::carbon::metrics::LaserIdentifier* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete id_;
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::LaserIdentifier>::GetOwningArena(id);
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.LaserLifeTime.id)
}

// uint64 lifetime_sec = 2;
inline void LaserLifeTime::clear_lifetime_sec() {
  lifetime_sec_ = uint64_t{0u};
}
inline uint64_t LaserLifeTime::_internal_lifetime_sec() const {
  return lifetime_sec_;
}
inline uint64_t LaserLifeTime::lifetime_sec() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserLifeTime.lifetime_sec)
  return _internal_lifetime_sec();
}
inline void LaserLifeTime::_internal_set_lifetime_sec(uint64_t value) {
  
  lifetime_sec_ = value;
}
inline void LaserLifeTime::set_lifetime_sec(uint64_t value) {
  _internal_set_lifetime_sec(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.LaserLifeTime.lifetime_sec)
}

// -------------------------------------------------------------------

// LaserEventTime

// .carbon.metrics.LaserIdentifier id = 1;
inline bool LaserEventTime::_internal_has_id() const {
  return this != internal_default_instance() && id_ != nullptr;
}
inline bool LaserEventTime::has_id() const {
  return _internal_has_id();
}
inline void LaserEventTime::clear_id() {
  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
}
inline const ::carbon::metrics::LaserIdentifier& LaserEventTime::_internal_id() const {
  const ::carbon::metrics::LaserIdentifier* p = id_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::LaserIdentifier&>(
      ::carbon::metrics::_LaserIdentifier_default_instance_);
}
inline const ::carbon::metrics::LaserIdentifier& LaserEventTime::id() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserEventTime.id)
  return _internal_id();
}
inline void LaserEventTime::unsafe_arena_set_allocated_id(
    ::carbon::metrics::LaserIdentifier* id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(id_);
  }
  id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.LaserEventTime.id)
}
inline ::carbon::metrics::LaserIdentifier* LaserEventTime::release_id() {
  
  ::carbon::metrics::LaserIdentifier* temp = id_;
  id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::LaserIdentifier* LaserEventTime::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_release:carbon.metrics.LaserEventTime.id)
  
  ::carbon::metrics::LaserIdentifier* temp = id_;
  id_ = nullptr;
  return temp;
}
inline ::carbon::metrics::LaserIdentifier* LaserEventTime::_internal_mutable_id() {
  
  if (id_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::LaserIdentifier>(GetArenaForAllocation());
    id_ = p;
  }
  return id_;
}
inline ::carbon::metrics::LaserIdentifier* LaserEventTime::mutable_id() {
  ::carbon::metrics::LaserIdentifier* _msg = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.LaserEventTime.id)
  return _msg;
}
inline void LaserEventTime::set_allocated_id(::carbon::metrics::LaserIdentifier* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete id_;
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::LaserIdentifier>::GetOwningArena(id);
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  id_ = id;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.LaserEventTime.id)
}

// int64 timestamp_sec = 2;
inline void LaserEventTime::clear_timestamp_sec() {
  timestamp_sec_ = int64_t{0};
}
inline int64_t LaserEventTime::_internal_timestamp_sec() const {
  return timestamp_sec_;
}
inline int64_t LaserEventTime::timestamp_sec() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserEventTime.timestamp_sec)
  return _internal_timestamp_sec();
}
inline void LaserEventTime::_internal_set_timestamp_sec(int64_t value) {
  
  timestamp_sec_ = value;
}
inline void LaserEventTime::set_timestamp_sec(int64_t value) {
  _internal_set_timestamp_sec(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.LaserEventTime.timestamp_sec)
}

// -------------------------------------------------------------------

// LaserLifeTimes

// repeated .carbon.metrics.LaserLifeTime lifetimes = 1;
inline int LaserLifeTimes::_internal_lifetimes_size() const {
  return lifetimes_.size();
}
inline int LaserLifeTimes::lifetimes_size() const {
  return _internal_lifetimes_size();
}
inline void LaserLifeTimes::clear_lifetimes() {
  lifetimes_.Clear();
}
inline ::carbon::metrics::LaserLifeTime* LaserLifeTimes::mutable_lifetimes(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.metrics.LaserLifeTimes.lifetimes)
  return lifetimes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserLifeTime >*
LaserLifeTimes::mutable_lifetimes() {
  // @@protoc_insertion_point(field_mutable_list:carbon.metrics.LaserLifeTimes.lifetimes)
  return &lifetimes_;
}
inline const ::carbon::metrics::LaserLifeTime& LaserLifeTimes::_internal_lifetimes(int index) const {
  return lifetimes_.Get(index);
}
inline const ::carbon::metrics::LaserLifeTime& LaserLifeTimes::lifetimes(int index) const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserLifeTimes.lifetimes)
  return _internal_lifetimes(index);
}
inline ::carbon::metrics::LaserLifeTime* LaserLifeTimes::_internal_add_lifetimes() {
  return lifetimes_.Add();
}
inline ::carbon::metrics::LaserLifeTime* LaserLifeTimes::add_lifetimes() {
  ::carbon::metrics::LaserLifeTime* _add = _internal_add_lifetimes();
  // @@protoc_insertion_point(field_add:carbon.metrics.LaserLifeTimes.lifetimes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserLifeTime >&
LaserLifeTimes::lifetimes() const {
  // @@protoc_insertion_point(field_list:carbon.metrics.LaserLifeTimes.lifetimes)
  return lifetimes_;
}

// -------------------------------------------------------------------

// LaserChangeTimes

// repeated .carbon.metrics.LaserEventTime installs = 1;
inline int LaserChangeTimes::_internal_installs_size() const {
  return installs_.size();
}
inline int LaserChangeTimes::installs_size() const {
  return _internal_installs_size();
}
inline void LaserChangeTimes::clear_installs() {
  installs_.Clear();
}
inline ::carbon::metrics::LaserEventTime* LaserChangeTimes::mutable_installs(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.metrics.LaserChangeTimes.installs)
  return installs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime >*
LaserChangeTimes::mutable_installs() {
  // @@protoc_insertion_point(field_mutable_list:carbon.metrics.LaserChangeTimes.installs)
  return &installs_;
}
inline const ::carbon::metrics::LaserEventTime& LaserChangeTimes::_internal_installs(int index) const {
  return installs_.Get(index);
}
inline const ::carbon::metrics::LaserEventTime& LaserChangeTimes::installs(int index) const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserChangeTimes.installs)
  return _internal_installs(index);
}
inline ::carbon::metrics::LaserEventTime* LaserChangeTimes::_internal_add_installs() {
  return installs_.Add();
}
inline ::carbon::metrics::LaserEventTime* LaserChangeTimes::add_installs() {
  ::carbon::metrics::LaserEventTime* _add = _internal_add_installs();
  // @@protoc_insertion_point(field_add:carbon.metrics.LaserChangeTimes.installs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime >&
LaserChangeTimes::installs() const {
  // @@protoc_insertion_point(field_list:carbon.metrics.LaserChangeTimes.installs)
  return installs_;
}

// repeated .carbon.metrics.LaserEventTime removals = 2;
inline int LaserChangeTimes::_internal_removals_size() const {
  return removals_.size();
}
inline int LaserChangeTimes::removals_size() const {
  return _internal_removals_size();
}
inline void LaserChangeTimes::clear_removals() {
  removals_.Clear();
}
inline ::carbon::metrics::LaserEventTime* LaserChangeTimes::mutable_removals(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.metrics.LaserChangeTimes.removals)
  return removals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime >*
LaserChangeTimes::mutable_removals() {
  // @@protoc_insertion_point(field_mutable_list:carbon.metrics.LaserChangeTimes.removals)
  return &removals_;
}
inline const ::carbon::metrics::LaserEventTime& LaserChangeTimes::_internal_removals(int index) const {
  return removals_.Get(index);
}
inline const ::carbon::metrics::LaserEventTime& LaserChangeTimes::removals(int index) const {
  // @@protoc_insertion_point(field_get:carbon.metrics.LaserChangeTimes.removals)
  return _internal_removals(index);
}
inline ::carbon::metrics::LaserEventTime* LaserChangeTimes::_internal_add_removals() {
  return removals_.Add();
}
inline ::carbon::metrics::LaserEventTime* LaserChangeTimes::add_removals() {
  ::carbon::metrics::LaserEventTime* _add = _internal_add_removals();
  // @@protoc_insertion_point(field_add:carbon.metrics.LaserChangeTimes.removals)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::metrics::LaserEventTime >&
LaserChangeTimes::removals() const {
  // @@protoc_insertion_point(field_list:carbon.metrics.LaserChangeTimes.removals)
  return removals_;
}

// -------------------------------------------------------------------

// CountsByConclusionType

// repeated uint32 disarmed_weed = 1;
inline int CountsByConclusionType::_internal_disarmed_weed_size() const {
  return disarmed_weed_.size();
}
inline int CountsByConclusionType::disarmed_weed_size() const {
  return _internal_disarmed_weed_size();
}
inline void CountsByConclusionType::clear_disarmed_weed() {
  disarmed_weed_.Clear();
}
inline uint32_t CountsByConclusionType::_internal_disarmed_weed(int index) const {
  return disarmed_weed_.Get(index);
}
inline uint32_t CountsByConclusionType::disarmed_weed(int index) const {
  // @@protoc_insertion_point(field_get:carbon.metrics.CountsByConclusionType.disarmed_weed)
  return _internal_disarmed_weed(index);
}
inline void CountsByConclusionType::set_disarmed_weed(int index, uint32_t value) {
  disarmed_weed_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.metrics.CountsByConclusionType.disarmed_weed)
}
inline void CountsByConclusionType::_internal_add_disarmed_weed(uint32_t value) {
  disarmed_weed_.Add(value);
}
inline void CountsByConclusionType::add_disarmed_weed(uint32_t value) {
  _internal_add_disarmed_weed(value);
  // @@protoc_insertion_point(field_add:carbon.metrics.CountsByConclusionType.disarmed_weed)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
CountsByConclusionType::_internal_disarmed_weed() const {
  return disarmed_weed_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
CountsByConclusionType::disarmed_weed() const {
  // @@protoc_insertion_point(field_list:carbon.metrics.CountsByConclusionType.disarmed_weed)
  return _internal_disarmed_weed();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
CountsByConclusionType::_internal_mutable_disarmed_weed() {
  return &disarmed_weed_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
CountsByConclusionType::mutable_disarmed_weed() {
  // @@protoc_insertion_point(field_mutable_list:carbon.metrics.CountsByConclusionType.disarmed_weed)
  return _internal_mutable_disarmed_weed();
}

// repeated uint32 armed_weed = 2;
inline int CountsByConclusionType::_internal_armed_weed_size() const {
  return armed_weed_.size();
}
inline int CountsByConclusionType::armed_weed_size() const {
  return _internal_armed_weed_size();
}
inline void CountsByConclusionType::clear_armed_weed() {
  armed_weed_.Clear();
}
inline uint32_t CountsByConclusionType::_internal_armed_weed(int index) const {
  return armed_weed_.Get(index);
}
inline uint32_t CountsByConclusionType::armed_weed(int index) const {
  // @@protoc_insertion_point(field_get:carbon.metrics.CountsByConclusionType.armed_weed)
  return _internal_armed_weed(index);
}
inline void CountsByConclusionType::set_armed_weed(int index, uint32_t value) {
  armed_weed_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.metrics.CountsByConclusionType.armed_weed)
}
inline void CountsByConclusionType::_internal_add_armed_weed(uint32_t value) {
  armed_weed_.Add(value);
}
inline void CountsByConclusionType::add_armed_weed(uint32_t value) {
  _internal_add_armed_weed(value);
  // @@protoc_insertion_point(field_add:carbon.metrics.CountsByConclusionType.armed_weed)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
CountsByConclusionType::_internal_armed_weed() const {
  return armed_weed_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
CountsByConclusionType::armed_weed() const {
  // @@protoc_insertion_point(field_list:carbon.metrics.CountsByConclusionType.armed_weed)
  return _internal_armed_weed();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
CountsByConclusionType::_internal_mutable_armed_weed() {
  return &armed_weed_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
CountsByConclusionType::mutable_armed_weed() {
  // @@protoc_insertion_point(field_mutable_list:carbon.metrics.CountsByConclusionType.armed_weed)
  return _internal_mutable_armed_weed();
}

// repeated uint32 disarmed_crop = 3;
inline int CountsByConclusionType::_internal_disarmed_crop_size() const {
  return disarmed_crop_.size();
}
inline int CountsByConclusionType::disarmed_crop_size() const {
  return _internal_disarmed_crop_size();
}
inline void CountsByConclusionType::clear_disarmed_crop() {
  disarmed_crop_.Clear();
}
inline uint32_t CountsByConclusionType::_internal_disarmed_crop(int index) const {
  return disarmed_crop_.Get(index);
}
inline uint32_t CountsByConclusionType::disarmed_crop(int index) const {
  // @@protoc_insertion_point(field_get:carbon.metrics.CountsByConclusionType.disarmed_crop)
  return _internal_disarmed_crop(index);
}
inline void CountsByConclusionType::set_disarmed_crop(int index, uint32_t value) {
  disarmed_crop_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.metrics.CountsByConclusionType.disarmed_crop)
}
inline void CountsByConclusionType::_internal_add_disarmed_crop(uint32_t value) {
  disarmed_crop_.Add(value);
}
inline void CountsByConclusionType::add_disarmed_crop(uint32_t value) {
  _internal_add_disarmed_crop(value);
  // @@protoc_insertion_point(field_add:carbon.metrics.CountsByConclusionType.disarmed_crop)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
CountsByConclusionType::_internal_disarmed_crop() const {
  return disarmed_crop_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
CountsByConclusionType::disarmed_crop() const {
  // @@protoc_insertion_point(field_list:carbon.metrics.CountsByConclusionType.disarmed_crop)
  return _internal_disarmed_crop();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
CountsByConclusionType::_internal_mutable_disarmed_crop() {
  return &disarmed_crop_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
CountsByConclusionType::mutable_disarmed_crop() {
  // @@protoc_insertion_point(field_mutable_list:carbon.metrics.CountsByConclusionType.disarmed_crop)
  return _internal_mutable_disarmed_crop();
}

// repeated uint32 armed_crop = 4;
inline int CountsByConclusionType::_internal_armed_crop_size() const {
  return armed_crop_.size();
}
inline int CountsByConclusionType::armed_crop_size() const {
  return _internal_armed_crop_size();
}
inline void CountsByConclusionType::clear_armed_crop() {
  armed_crop_.Clear();
}
inline uint32_t CountsByConclusionType::_internal_armed_crop(int index) const {
  return armed_crop_.Get(index);
}
inline uint32_t CountsByConclusionType::armed_crop(int index) const {
  // @@protoc_insertion_point(field_get:carbon.metrics.CountsByConclusionType.armed_crop)
  return _internal_armed_crop(index);
}
inline void CountsByConclusionType::set_armed_crop(int index, uint32_t value) {
  armed_crop_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.metrics.CountsByConclusionType.armed_crop)
}
inline void CountsByConclusionType::_internal_add_armed_crop(uint32_t value) {
  armed_crop_.Add(value);
}
inline void CountsByConclusionType::add_armed_crop(uint32_t value) {
  _internal_add_armed_crop(value);
  // @@protoc_insertion_point(field_add:carbon.metrics.CountsByConclusionType.armed_crop)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
CountsByConclusionType::_internal_armed_crop() const {
  return armed_crop_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
CountsByConclusionType::armed_crop() const {
  // @@protoc_insertion_point(field_list:carbon.metrics.CountsByConclusionType.armed_crop)
  return _internal_armed_crop();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
CountsByConclusionType::_internal_mutable_armed_crop() {
  return &armed_crop_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
CountsByConclusionType::mutable_armed_crop() {
  // @@protoc_insertion_point(field_mutable_list:carbon.metrics.CountsByConclusionType.armed_crop)
  return _internal_mutable_armed_crop();
}

// -------------------------------------------------------------------

// TargetSizeData

// double cumulative_size = 1;
inline void TargetSizeData::clear_cumulative_size() {
  cumulative_size_ = 0;
}
inline double TargetSizeData::_internal_cumulative_size() const {
  return cumulative_size_;
}
inline double TargetSizeData::cumulative_size() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.TargetSizeData.cumulative_size)
  return _internal_cumulative_size();
}
inline void TargetSizeData::_internal_set_cumulative_size(double value) {
  
  cumulative_size_ = value;
}
inline void TargetSizeData::set_cumulative_size(double value) {
  _internal_set_cumulative_size(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.TargetSizeData.cumulative_size)
}

// uint64 count = 2;
inline void TargetSizeData::clear_count() {
  count_ = uint64_t{0u};
}
inline uint64_t TargetSizeData::_internal_count() const {
  return count_;
}
inline uint64_t TargetSizeData::count() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.TargetSizeData.count)
  return _internal_count();
}
inline void TargetSizeData::_internal_set_count(uint64_t value) {
  
  count_ = value;
}
inline void TargetSizeData::set_count(uint64_t value) {
  _internal_set_count(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.TargetSizeData.count)
}

// -------------------------------------------------------------------

// RequiredLaserTimeData

// uint64 cumulative_time = 1;
inline void RequiredLaserTimeData::clear_cumulative_time() {
  cumulative_time_ = uint64_t{0u};
}
inline uint64_t RequiredLaserTimeData::_internal_cumulative_time() const {
  return cumulative_time_;
}
inline uint64_t RequiredLaserTimeData::cumulative_time() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.RequiredLaserTimeData.cumulative_time)
  return _internal_cumulative_time();
}
inline void RequiredLaserTimeData::_internal_set_cumulative_time(uint64_t value) {
  
  cumulative_time_ = value;
}
inline void RequiredLaserTimeData::set_cumulative_time(uint64_t value) {
  _internal_set_cumulative_time(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.RequiredLaserTimeData.cumulative_time)
}

// uint64 count = 2;
inline void RequiredLaserTimeData::clear_count() {
  count_ = uint64_t{0u};
}
inline uint64_t RequiredLaserTimeData::_internal_count() const {
  return count_;
}
inline uint64_t RequiredLaserTimeData::count() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.RequiredLaserTimeData.count)
  return _internal_count();
}
inline void RequiredLaserTimeData::_internal_set_count(uint64_t value) {
  
  count_ = value;
}
inline void RequiredLaserTimeData::set_count(uint64_t value) {
  _internal_set_count(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.RequiredLaserTimeData.count)
}

// -------------------------------------------------------------------

// SpatialPosition

// double latitude = 1;
inline void SpatialPosition::clear_latitude() {
  latitude_ = 0;
}
inline double SpatialPosition::_internal_latitude() const {
  return latitude_;
}
inline double SpatialPosition::latitude() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialPosition.latitude)
  return _internal_latitude();
}
inline void SpatialPosition::_internal_set_latitude(double value) {
  
  latitude_ = value;
}
inline void SpatialPosition::set_latitude(double value) {
  _internal_set_latitude(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.SpatialPosition.latitude)
}

// double longitude = 2;
inline void SpatialPosition::clear_longitude() {
  longitude_ = 0;
}
inline double SpatialPosition::_internal_longitude() const {
  return longitude_;
}
inline double SpatialPosition::longitude() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialPosition.longitude)
  return _internal_longitude();
}
inline void SpatialPosition::_internal_set_longitude(double value) {
  
  longitude_ = value;
}
inline void SpatialPosition::set_longitude(double value) {
  _internal_set_longitude(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.SpatialPosition.longitude)
}

// double height_mm = 3;
inline void SpatialPosition::clear_height_mm() {
  height_mm_ = 0;
}
inline double SpatialPosition::_internal_height_mm() const {
  return height_mm_;
}
inline double SpatialPosition::height_mm() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialPosition.height_mm)
  return _internal_height_mm();
}
inline void SpatialPosition::_internal_set_height_mm(double value) {
  
  height_mm_ = value;
}
inline void SpatialPosition::set_height_mm(double value) {
  _internal_set_height_mm(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.SpatialPosition.height_mm)
}

// uint64 timestamp_ms = 4;
inline void SpatialPosition::clear_timestamp_ms() {
  timestamp_ms_ = uint64_t{0u};
}
inline uint64_t SpatialPosition::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline uint64_t SpatialPosition::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialPosition.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void SpatialPosition::_internal_set_timestamp_ms(uint64_t value) {
  
  timestamp_ms_ = value;
}
inline void SpatialPosition::set_timestamp_ms(uint64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.SpatialPosition.timestamp_ms)
}

// double ecef_x = 5;
inline void SpatialPosition::clear_ecef_x() {
  ecef_x_ = 0;
}
inline double SpatialPosition::_internal_ecef_x() const {
  return ecef_x_;
}
inline double SpatialPosition::ecef_x() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialPosition.ecef_x)
  return _internal_ecef_x();
}
inline void SpatialPosition::_internal_set_ecef_x(double value) {
  
  ecef_x_ = value;
}
inline void SpatialPosition::set_ecef_x(double value) {
  _internal_set_ecef_x(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.SpatialPosition.ecef_x)
}

// double ecef_y = 6;
inline void SpatialPosition::clear_ecef_y() {
  ecef_y_ = 0;
}
inline double SpatialPosition::_internal_ecef_y() const {
  return ecef_y_;
}
inline double SpatialPosition::ecef_y() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialPosition.ecef_y)
  return _internal_ecef_y();
}
inline void SpatialPosition::_internal_set_ecef_y(double value) {
  
  ecef_y_ = value;
}
inline void SpatialPosition::set_ecef_y(double value) {
  _internal_set_ecef_y(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.SpatialPosition.ecef_y)
}

// double ecef_z = 7;
inline void SpatialPosition::clear_ecef_z() {
  ecef_z_ = 0;
}
inline double SpatialPosition::_internal_ecef_z() const {
  return ecef_z_;
}
inline double SpatialPosition::ecef_z() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialPosition.ecef_z)
  return _internal_ecef_z();
}
inline void SpatialPosition::_internal_set_ecef_z(double value) {
  
  ecef_z_ = value;
}
inline void SpatialPosition::set_ecef_z(double value) {
  _internal_set_ecef_z(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.SpatialPosition.ecef_z)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// WeedCounterChunk

// .carbon.metrics.CountsByConclusionType conclusion_counts = 1;
inline bool WeedCounterChunk::_internal_has_conclusion_counts() const {
  return this != internal_default_instance() && conclusion_counts_ != nullptr;
}
inline bool WeedCounterChunk::has_conclusion_counts() const {
  return _internal_has_conclusion_counts();
}
inline void WeedCounterChunk::clear_conclusion_counts() {
  if (GetArenaForAllocation() == nullptr && conclusion_counts_ != nullptr) {
    delete conclusion_counts_;
  }
  conclusion_counts_ = nullptr;
}
inline const ::carbon::metrics::CountsByConclusionType& WeedCounterChunk::_internal_conclusion_counts() const {
  const ::carbon::metrics::CountsByConclusionType* p = conclusion_counts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::CountsByConclusionType&>(
      ::carbon::metrics::_CountsByConclusionType_default_instance_);
}
inline const ::carbon::metrics::CountsByConclusionType& WeedCounterChunk::conclusion_counts() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.WeedCounterChunk.conclusion_counts)
  return _internal_conclusion_counts();
}
inline void WeedCounterChunk::unsafe_arena_set_allocated_conclusion_counts(
    ::carbon::metrics::CountsByConclusionType* conclusion_counts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(conclusion_counts_);
  }
  conclusion_counts_ = conclusion_counts;
  if (conclusion_counts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.WeedCounterChunk.conclusion_counts)
}
inline ::carbon::metrics::CountsByConclusionType* WeedCounterChunk::release_conclusion_counts() {
  
  ::carbon::metrics::CountsByConclusionType* temp = conclusion_counts_;
  conclusion_counts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::CountsByConclusionType* WeedCounterChunk::unsafe_arena_release_conclusion_counts() {
  // @@protoc_insertion_point(field_release:carbon.metrics.WeedCounterChunk.conclusion_counts)
  
  ::carbon::metrics::CountsByConclusionType* temp = conclusion_counts_;
  conclusion_counts_ = nullptr;
  return temp;
}
inline ::carbon::metrics::CountsByConclusionType* WeedCounterChunk::_internal_mutable_conclusion_counts() {
  
  if (conclusion_counts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::CountsByConclusionType>(GetArenaForAllocation());
    conclusion_counts_ = p;
  }
  return conclusion_counts_;
}
inline ::carbon::metrics::CountsByConclusionType* WeedCounterChunk::mutable_conclusion_counts() {
  ::carbon::metrics::CountsByConclusionType* _msg = _internal_mutable_conclusion_counts();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.WeedCounterChunk.conclusion_counts)
  return _msg;
}
inline void WeedCounterChunk::set_allocated_conclusion_counts(::carbon::metrics::CountsByConclusionType* conclusion_counts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete conclusion_counts_;
  }
  if (conclusion_counts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::CountsByConclusionType>::GetOwningArena(conclusion_counts);
    if (message_arena != submessage_arena) {
      conclusion_counts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, conclusion_counts, submessage_arena);
    }
    
  } else {
    
  }
  conclusion_counts_ = conclusion_counts;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.WeedCounterChunk.conclusion_counts)
}

// .carbon.metrics.TargetSizeData weed_size_data = 2;
inline bool WeedCounterChunk::_internal_has_weed_size_data() const {
  return this != internal_default_instance() && weed_size_data_ != nullptr;
}
inline bool WeedCounterChunk::has_weed_size_data() const {
  return _internal_has_weed_size_data();
}
inline void WeedCounterChunk::clear_weed_size_data() {
  if (GetArenaForAllocation() == nullptr && weed_size_data_ != nullptr) {
    delete weed_size_data_;
  }
  weed_size_data_ = nullptr;
}
inline const ::carbon::metrics::TargetSizeData& WeedCounterChunk::_internal_weed_size_data() const {
  const ::carbon::metrics::TargetSizeData* p = weed_size_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::TargetSizeData&>(
      ::carbon::metrics::_TargetSizeData_default_instance_);
}
inline const ::carbon::metrics::TargetSizeData& WeedCounterChunk::weed_size_data() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.WeedCounterChunk.weed_size_data)
  return _internal_weed_size_data();
}
inline void WeedCounterChunk::unsafe_arena_set_allocated_weed_size_data(
    ::carbon::metrics::TargetSizeData* weed_size_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(weed_size_data_);
  }
  weed_size_data_ = weed_size_data;
  if (weed_size_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.WeedCounterChunk.weed_size_data)
}
inline ::carbon::metrics::TargetSizeData* WeedCounterChunk::release_weed_size_data() {
  
  ::carbon::metrics::TargetSizeData* temp = weed_size_data_;
  weed_size_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::TargetSizeData* WeedCounterChunk::unsafe_arena_release_weed_size_data() {
  // @@protoc_insertion_point(field_release:carbon.metrics.WeedCounterChunk.weed_size_data)
  
  ::carbon::metrics::TargetSizeData* temp = weed_size_data_;
  weed_size_data_ = nullptr;
  return temp;
}
inline ::carbon::metrics::TargetSizeData* WeedCounterChunk::_internal_mutable_weed_size_data() {
  
  if (weed_size_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::TargetSizeData>(GetArenaForAllocation());
    weed_size_data_ = p;
  }
  return weed_size_data_;
}
inline ::carbon::metrics::TargetSizeData* WeedCounterChunk::mutable_weed_size_data() {
  ::carbon::metrics::TargetSizeData* _msg = _internal_mutable_weed_size_data();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.WeedCounterChunk.weed_size_data)
  return _msg;
}
inline void WeedCounterChunk::set_allocated_weed_size_data(::carbon::metrics::TargetSizeData* weed_size_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete weed_size_data_;
  }
  if (weed_size_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::TargetSizeData>::GetOwningArena(weed_size_data);
    if (message_arena != submessage_arena) {
      weed_size_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, weed_size_data, submessage_arena);
    }
    
  } else {
    
  }
  weed_size_data_ = weed_size_data;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.WeedCounterChunk.weed_size_data)
}

// .carbon.metrics.TargetSizeData crop_size_data = 3;
inline bool WeedCounterChunk::_internal_has_crop_size_data() const {
  return this != internal_default_instance() && crop_size_data_ != nullptr;
}
inline bool WeedCounterChunk::has_crop_size_data() const {
  return _internal_has_crop_size_data();
}
inline void WeedCounterChunk::clear_crop_size_data() {
  if (GetArenaForAllocation() == nullptr && crop_size_data_ != nullptr) {
    delete crop_size_data_;
  }
  crop_size_data_ = nullptr;
}
inline const ::carbon::metrics::TargetSizeData& WeedCounterChunk::_internal_crop_size_data() const {
  const ::carbon::metrics::TargetSizeData* p = crop_size_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::TargetSizeData&>(
      ::carbon::metrics::_TargetSizeData_default_instance_);
}
inline const ::carbon::metrics::TargetSizeData& WeedCounterChunk::crop_size_data() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.WeedCounterChunk.crop_size_data)
  return _internal_crop_size_data();
}
inline void WeedCounterChunk::unsafe_arena_set_allocated_crop_size_data(
    ::carbon::metrics::TargetSizeData* crop_size_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(crop_size_data_);
  }
  crop_size_data_ = crop_size_data;
  if (crop_size_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.WeedCounterChunk.crop_size_data)
}
inline ::carbon::metrics::TargetSizeData* WeedCounterChunk::release_crop_size_data() {
  
  ::carbon::metrics::TargetSizeData* temp = crop_size_data_;
  crop_size_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::TargetSizeData* WeedCounterChunk::unsafe_arena_release_crop_size_data() {
  // @@protoc_insertion_point(field_release:carbon.metrics.WeedCounterChunk.crop_size_data)
  
  ::carbon::metrics::TargetSizeData* temp = crop_size_data_;
  crop_size_data_ = nullptr;
  return temp;
}
inline ::carbon::metrics::TargetSizeData* WeedCounterChunk::_internal_mutable_crop_size_data() {
  
  if (crop_size_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::TargetSizeData>(GetArenaForAllocation());
    crop_size_data_ = p;
  }
  return crop_size_data_;
}
inline ::carbon::metrics::TargetSizeData* WeedCounterChunk::mutable_crop_size_data() {
  ::carbon::metrics::TargetSizeData* _msg = _internal_mutable_crop_size_data();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.WeedCounterChunk.crop_size_data)
  return _msg;
}
inline void WeedCounterChunk::set_allocated_crop_size_data(::carbon::metrics::TargetSizeData* crop_size_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete crop_size_data_;
  }
  if (crop_size_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::TargetSizeData>::GetOwningArena(crop_size_data);
    if (message_arena != submessage_arena) {
      crop_size_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, crop_size_data, submessage_arena);
    }
    
  } else {
    
  }
  crop_size_data_ = crop_size_data;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.WeedCounterChunk.crop_size_data)
}

// map<string, uint32> counts_by_category = 4;
inline int WeedCounterChunk::_internal_counts_by_category_size() const {
  return counts_by_category_.size();
}
inline int WeedCounterChunk::counts_by_category_size() const {
  return _internal_counts_by_category_size();
}
inline void WeedCounterChunk::clear_counts_by_category() {
  counts_by_category_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >&
WeedCounterChunk::_internal_counts_by_category() const {
  return counts_by_category_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >&
WeedCounterChunk::counts_by_category() const {
  // @@protoc_insertion_point(field_map:carbon.metrics.WeedCounterChunk.counts_by_category)
  return _internal_counts_by_category();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >*
WeedCounterChunk::_internal_mutable_counts_by_category() {
  return counts_by_category_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >*
WeedCounterChunk::mutable_counts_by_category() {
  // @@protoc_insertion_point(field_mutable_map:carbon.metrics.WeedCounterChunk.counts_by_category)
  return _internal_mutable_counts_by_category();
}

// .carbon.metrics.RequiredLaserTimeData targeted_laser_time_data = 5;
inline bool WeedCounterChunk::_internal_has_targeted_laser_time_data() const {
  return this != internal_default_instance() && targeted_laser_time_data_ != nullptr;
}
inline bool WeedCounterChunk::has_targeted_laser_time_data() const {
  return _internal_has_targeted_laser_time_data();
}
inline void WeedCounterChunk::clear_targeted_laser_time_data() {
  if (GetArenaForAllocation() == nullptr && targeted_laser_time_data_ != nullptr) {
    delete targeted_laser_time_data_;
  }
  targeted_laser_time_data_ = nullptr;
}
inline const ::carbon::metrics::RequiredLaserTimeData& WeedCounterChunk::_internal_targeted_laser_time_data() const {
  const ::carbon::metrics::RequiredLaserTimeData* p = targeted_laser_time_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::RequiredLaserTimeData&>(
      ::carbon::metrics::_RequiredLaserTimeData_default_instance_);
}
inline const ::carbon::metrics::RequiredLaserTimeData& WeedCounterChunk::targeted_laser_time_data() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.WeedCounterChunk.targeted_laser_time_data)
  return _internal_targeted_laser_time_data();
}
inline void WeedCounterChunk::unsafe_arena_set_allocated_targeted_laser_time_data(
    ::carbon::metrics::RequiredLaserTimeData* targeted_laser_time_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(targeted_laser_time_data_);
  }
  targeted_laser_time_data_ = targeted_laser_time_data;
  if (targeted_laser_time_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.WeedCounterChunk.targeted_laser_time_data)
}
inline ::carbon::metrics::RequiredLaserTimeData* WeedCounterChunk::release_targeted_laser_time_data() {
  
  ::carbon::metrics::RequiredLaserTimeData* temp = targeted_laser_time_data_;
  targeted_laser_time_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::RequiredLaserTimeData* WeedCounterChunk::unsafe_arena_release_targeted_laser_time_data() {
  // @@protoc_insertion_point(field_release:carbon.metrics.WeedCounterChunk.targeted_laser_time_data)
  
  ::carbon::metrics::RequiredLaserTimeData* temp = targeted_laser_time_data_;
  targeted_laser_time_data_ = nullptr;
  return temp;
}
inline ::carbon::metrics::RequiredLaserTimeData* WeedCounterChunk::_internal_mutable_targeted_laser_time_data() {
  
  if (targeted_laser_time_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::RequiredLaserTimeData>(GetArenaForAllocation());
    targeted_laser_time_data_ = p;
  }
  return targeted_laser_time_data_;
}
inline ::carbon::metrics::RequiredLaserTimeData* WeedCounterChunk::mutable_targeted_laser_time_data() {
  ::carbon::metrics::RequiredLaserTimeData* _msg = _internal_mutable_targeted_laser_time_data();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.WeedCounterChunk.targeted_laser_time_data)
  return _msg;
}
inline void WeedCounterChunk::set_allocated_targeted_laser_time_data(::carbon::metrics::RequiredLaserTimeData* targeted_laser_time_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete targeted_laser_time_data_;
  }
  if (targeted_laser_time_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::RequiredLaserTimeData>::GetOwningArena(targeted_laser_time_data);
    if (message_arena != submessage_arena) {
      targeted_laser_time_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, targeted_laser_time_data, submessage_arena);
    }
    
  } else {
    
  }
  targeted_laser_time_data_ = targeted_laser_time_data;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.WeedCounterChunk.targeted_laser_time_data)
}

// .carbon.metrics.RequiredLaserTimeData untargeted_laser_time_data = 6;
inline bool WeedCounterChunk::_internal_has_untargeted_laser_time_data() const {
  return this != internal_default_instance() && untargeted_laser_time_data_ != nullptr;
}
inline bool WeedCounterChunk::has_untargeted_laser_time_data() const {
  return _internal_has_untargeted_laser_time_data();
}
inline void WeedCounterChunk::clear_untargeted_laser_time_data() {
  if (GetArenaForAllocation() == nullptr && untargeted_laser_time_data_ != nullptr) {
    delete untargeted_laser_time_data_;
  }
  untargeted_laser_time_data_ = nullptr;
}
inline const ::carbon::metrics::RequiredLaserTimeData& WeedCounterChunk::_internal_untargeted_laser_time_data() const {
  const ::carbon::metrics::RequiredLaserTimeData* p = untargeted_laser_time_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::RequiredLaserTimeData&>(
      ::carbon::metrics::_RequiredLaserTimeData_default_instance_);
}
inline const ::carbon::metrics::RequiredLaserTimeData& WeedCounterChunk::untargeted_laser_time_data() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.WeedCounterChunk.untargeted_laser_time_data)
  return _internal_untargeted_laser_time_data();
}
inline void WeedCounterChunk::unsafe_arena_set_allocated_untargeted_laser_time_data(
    ::carbon::metrics::RequiredLaserTimeData* untargeted_laser_time_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(untargeted_laser_time_data_);
  }
  untargeted_laser_time_data_ = untargeted_laser_time_data;
  if (untargeted_laser_time_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.WeedCounterChunk.untargeted_laser_time_data)
}
inline ::carbon::metrics::RequiredLaserTimeData* WeedCounterChunk::release_untargeted_laser_time_data() {
  
  ::carbon::metrics::RequiredLaserTimeData* temp = untargeted_laser_time_data_;
  untargeted_laser_time_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::RequiredLaserTimeData* WeedCounterChunk::unsafe_arena_release_untargeted_laser_time_data() {
  // @@protoc_insertion_point(field_release:carbon.metrics.WeedCounterChunk.untargeted_laser_time_data)
  
  ::carbon::metrics::RequiredLaserTimeData* temp = untargeted_laser_time_data_;
  untargeted_laser_time_data_ = nullptr;
  return temp;
}
inline ::carbon::metrics::RequiredLaserTimeData* WeedCounterChunk::_internal_mutable_untargeted_laser_time_data() {
  
  if (untargeted_laser_time_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::RequiredLaserTimeData>(GetArenaForAllocation());
    untargeted_laser_time_data_ = p;
  }
  return untargeted_laser_time_data_;
}
inline ::carbon::metrics::RequiredLaserTimeData* WeedCounterChunk::mutable_untargeted_laser_time_data() {
  ::carbon::metrics::RequiredLaserTimeData* _msg = _internal_mutable_untargeted_laser_time_data();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.WeedCounterChunk.untargeted_laser_time_data)
  return _msg;
}
inline void WeedCounterChunk::set_allocated_untargeted_laser_time_data(::carbon::metrics::RequiredLaserTimeData* untargeted_laser_time_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete untargeted_laser_time_data_;
  }
  if (untargeted_laser_time_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::RequiredLaserTimeData>::GetOwningArena(untargeted_laser_time_data);
    if (message_arena != submessage_arena) {
      untargeted_laser_time_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, untargeted_laser_time_data, submessage_arena);
    }
    
  } else {
    
  }
  untargeted_laser_time_data_ = untargeted_laser_time_data;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.WeedCounterChunk.untargeted_laser_time_data)
}

// uint64 valid_crop_count = 7;
inline void WeedCounterChunk::clear_valid_crop_count() {
  valid_crop_count_ = uint64_t{0u};
}
inline uint64_t WeedCounterChunk::_internal_valid_crop_count() const {
  return valid_crop_count_;
}
inline uint64_t WeedCounterChunk::valid_crop_count() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.WeedCounterChunk.valid_crop_count)
  return _internal_valid_crop_count();
}
inline void WeedCounterChunk::_internal_set_valid_crop_count(uint64_t value) {
  
  valid_crop_count_ = value;
}
inline void WeedCounterChunk::set_valid_crop_count(uint64_t value) {
  _internal_set_valid_crop_count(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.WeedCounterChunk.valid_crop_count)
}

// -------------------------------------------------------------------

// WheelEncoderSpatialData

// float start_pos_m = 1;
inline void WheelEncoderSpatialData::clear_start_pos_m() {
  start_pos_m_ = 0;
}
inline float WheelEncoderSpatialData::_internal_start_pos_m() const {
  return start_pos_m_;
}
inline float WheelEncoderSpatialData::start_pos_m() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.WheelEncoderSpatialData.start_pos_m)
  return _internal_start_pos_m();
}
inline void WheelEncoderSpatialData::_internal_set_start_pos_m(float value) {
  
  start_pos_m_ = value;
}
inline void WheelEncoderSpatialData::set_start_pos_m(float value) {
  _internal_set_start_pos_m(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.WheelEncoderSpatialData.start_pos_m)
}

// float end_pos_m = 2;
inline void WheelEncoderSpatialData::clear_end_pos_m() {
  end_pos_m_ = 0;
}
inline float WheelEncoderSpatialData::_internal_end_pos_m() const {
  return end_pos_m_;
}
inline float WheelEncoderSpatialData::end_pos_m() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.WheelEncoderSpatialData.end_pos_m)
  return _internal_end_pos_m();
}
inline void WheelEncoderSpatialData::_internal_set_end_pos_m(float value) {
  
  end_pos_m_ = value;
}
inline void WheelEncoderSpatialData::set_end_pos_m(float value) {
  _internal_set_end_pos_m(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.WheelEncoderSpatialData.end_pos_m)
}

// -------------------------------------------------------------------

// BandingSpatialData

// float percent_banded = 1;
inline void BandingSpatialData::clear_percent_banded() {
  percent_banded_ = 0;
}
inline float BandingSpatialData::_internal_percent_banded() const {
  return percent_banded_;
}
inline float BandingSpatialData::percent_banded() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.BandingSpatialData.percent_banded)
  return _internal_percent_banded();
}
inline void BandingSpatialData::_internal_set_percent_banded(float value) {
  
  percent_banded_ = value;
}
inline void BandingSpatialData::set_percent_banded(float value) {
  _internal_set_percent_banded(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.BandingSpatialData.percent_banded)
}

// -------------------------------------------------------------------

// ImplementWidthSpatialData

// float width_mm = 1;
inline void ImplementWidthSpatialData::clear_width_mm() {
  width_mm_ = 0;
}
inline float ImplementWidthSpatialData::_internal_width_mm() const {
  return width_mm_;
}
inline float ImplementWidthSpatialData::width_mm() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.ImplementWidthSpatialData.width_mm)
  return _internal_width_mm();
}
inline void ImplementWidthSpatialData::_internal_set_width_mm(float value) {
  
  width_mm_ = value;
}
inline void ImplementWidthSpatialData::set_width_mm(float value) {
  _internal_set_width_mm(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.ImplementWidthSpatialData.width_mm)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// VelocitySpatialMetric

// map<string, float> avg_target_vel = 1;
inline int VelocitySpatialMetric::_internal_avg_target_vel_size() const {
  return avg_target_vel_.size();
}
inline int VelocitySpatialMetric::avg_target_vel_size() const {
  return _internal_avg_target_vel_size();
}
inline void VelocitySpatialMetric::clear_avg_target_vel() {
  avg_target_vel_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
VelocitySpatialMetric::_internal_avg_target_vel() const {
  return avg_target_vel_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >&
VelocitySpatialMetric::avg_target_vel() const {
  // @@protoc_insertion_point(field_map:carbon.metrics.VelocitySpatialMetric.avg_target_vel)
  return _internal_avg_target_vel();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
VelocitySpatialMetric::_internal_mutable_avg_target_vel() {
  return avg_target_vel_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >*
VelocitySpatialMetric::mutable_avg_target_vel() {
  // @@protoc_insertion_point(field_mutable_map:carbon.metrics.VelocitySpatialMetric.avg_target_vel)
  return _internal_mutable_avg_target_vel();
}

// -------------------------------------------------------------------

// JobMetric

// string job_id = 1;
inline void JobMetric::clear_job_id() {
  job_id_.ClearToEmpty();
}
inline const std::string& JobMetric::job_id() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.JobMetric.job_id)
  return _internal_job_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void JobMetric::set_job_id(ArgT0&& arg0, ArgT... args) {
 
 job_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.metrics.JobMetric.job_id)
}
inline std::string* JobMetric::mutable_job_id() {
  std::string* _s = _internal_mutable_job_id();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.JobMetric.job_id)
  return _s;
}
inline const std::string& JobMetric::_internal_job_id() const {
  return job_id_.Get();
}
inline void JobMetric::_internal_set_job_id(const std::string& value) {
  
  job_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* JobMetric::_internal_mutable_job_id() {
  
  return job_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* JobMetric::release_job_id() {
  // @@protoc_insertion_point(field_release:carbon.metrics.JobMetric.job_id)
  return job_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void JobMetric::set_allocated_job_id(std::string* job_id) {
  if (job_id != nullptr) {
    
  } else {
    
  }
  job_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), job_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (job_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    job_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.JobMetric.job_id)
}

// -------------------------------------------------------------------

// HWMetric

// bool lifted = 1;
inline void HWMetric::clear_lifted() {
  lifted_ = false;
}
inline bool HWMetric::_internal_lifted() const {
  return lifted_;
}
inline bool HWMetric::lifted() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.HWMetric.lifted)
  return _internal_lifted();
}
inline void HWMetric::_internal_set_lifted(bool value) {
  
  lifted_ = value;
}
inline void HWMetric::set_lifted(bool value) {
  _internal_set_lifted(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.HWMetric.lifted)
}

// bool estopped = 2;
inline void HWMetric::clear_estopped() {
  estopped_ = false;
}
inline bool HWMetric::_internal_estopped() const {
  return estopped_;
}
inline bool HWMetric::estopped() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.HWMetric.estopped)
  return _internal_estopped();
}
inline void HWMetric::_internal_set_estopped(bool value) {
  
  estopped_ = value;
}
inline void HWMetric::set_estopped(bool value) {
  _internal_set_estopped(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.HWMetric.estopped)
}

// bool laser_key = 3;
inline void HWMetric::clear_laser_key() {
  laser_key_ = false;
}
inline bool HWMetric::_internal_laser_key() const {
  return laser_key_;
}
inline bool HWMetric::laser_key() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.HWMetric.laser_key)
  return _internal_laser_key();
}
inline void HWMetric::_internal_set_laser_key(bool value) {
  
  laser_key_ = value;
}
inline void HWMetric::set_laser_key(bool value) {
  _internal_set_laser_key(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.HWMetric.laser_key)
}

// bool interlock = 4;
inline void HWMetric::clear_interlock() {
  interlock_ = false;
}
inline bool HWMetric::_internal_interlock() const {
  return interlock_;
}
inline bool HWMetric::interlock() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.HWMetric.interlock)
  return _internal_interlock();
}
inline void HWMetric::_internal_set_interlock(bool value) {
  
  interlock_ = value;
}
inline void HWMetric::set_interlock(bool value) {
  _internal_set_interlock(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.HWMetric.interlock)
}

// bool water_protect = 5;
inline void HWMetric::clear_water_protect() {
  water_protect_ = false;
}
inline bool HWMetric::_internal_water_protect() const {
  return water_protect_;
}
inline bool HWMetric::water_protect() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.HWMetric.water_protect)
  return _internal_water_protect();
}
inline void HWMetric::_internal_set_water_protect(bool value) {
  
  water_protect_ = value;
}
inline void HWMetric::set_water_protect(bool value) {
  _internal_set_water_protect(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.HWMetric.water_protect)
}

// bool debug_mode = 6;
inline void HWMetric::clear_debug_mode() {
  debug_mode_ = false;
}
inline bool HWMetric::_internal_debug_mode() const {
  return debug_mode_;
}
inline bool HWMetric::debug_mode() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.HWMetric.debug_mode)
  return _internal_debug_mode();
}
inline void HWMetric::_internal_set_debug_mode(bool value) {
  
  debug_mode_ = value;
}
inline void HWMetric::set_debug_mode(bool value) {
  _internal_set_debug_mode(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.HWMetric.debug_mode)
}

// -------------------------------------------------------------------

// SpatialMetricBlock

// .carbon.metrics.SpatialPosition start = 1;
inline bool SpatialMetricBlock::_internal_has_start() const {
  return this != internal_default_instance() && start_ != nullptr;
}
inline bool SpatialMetricBlock::has_start() const {
  return _internal_has_start();
}
inline void SpatialMetricBlock::clear_start() {
  if (GetArenaForAllocation() == nullptr && start_ != nullptr) {
    delete start_;
  }
  start_ = nullptr;
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::_internal_start() const {
  const ::carbon::metrics::SpatialPosition* p = start_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::SpatialPosition&>(
      ::carbon::metrics::_SpatialPosition_default_instance_);
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::start() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.start)
  return _internal_start();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_start(
    ::carbon::metrics::SpatialPosition* start) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_);
  }
  start_ = start;
  if (start) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.start)
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::release_start() {
  
  ::carbon::metrics::SpatialPosition* temp = start_;
  start_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::unsafe_arena_release_start() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.start)
  
  ::carbon::metrics::SpatialPosition* temp = start_;
  start_ = nullptr;
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::_internal_mutable_start() {
  
  if (start_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::SpatialPosition>(GetArenaForAllocation());
    start_ = p;
  }
  return start_;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::mutable_start() {
  ::carbon::metrics::SpatialPosition* _msg = _internal_mutable_start();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.start)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_start(::carbon::metrics::SpatialPosition* start) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete start_;
  }
  if (start) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::SpatialPosition>::GetOwningArena(start);
    if (message_arena != submessage_arena) {
      start = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start, submessage_arena);
    }
    
  } else {
    
  }
  start_ = start;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.start)
}

// .carbon.metrics.SpatialPosition end = 2;
inline bool SpatialMetricBlock::_internal_has_end() const {
  return this != internal_default_instance() && end_ != nullptr;
}
inline bool SpatialMetricBlock::has_end() const {
  return _internal_has_end();
}
inline void SpatialMetricBlock::clear_end() {
  if (GetArenaForAllocation() == nullptr && end_ != nullptr) {
    delete end_;
  }
  end_ = nullptr;
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::_internal_end() const {
  const ::carbon::metrics::SpatialPosition* p = end_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::SpatialPosition&>(
      ::carbon::metrics::_SpatialPosition_default_instance_);
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::end() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.end)
  return _internal_end();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_end(
    ::carbon::metrics::SpatialPosition* end) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_);
  }
  end_ = end;
  if (end) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.end)
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::release_end() {
  
  ::carbon::metrics::SpatialPosition* temp = end_;
  end_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::unsafe_arena_release_end() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.end)
  
  ::carbon::metrics::SpatialPosition* temp = end_;
  end_ = nullptr;
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::_internal_mutable_end() {
  
  if (end_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::SpatialPosition>(GetArenaForAllocation());
    end_ = p;
  }
  return end_;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::mutable_end() {
  ::carbon::metrics::SpatialPosition* _msg = _internal_mutable_end();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.end)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_end(::carbon::metrics::SpatialPosition* end) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete end_;
  }
  if (end) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::SpatialPosition>::GetOwningArena(end);
    if (message_arena != submessage_arena) {
      end = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end, submessage_arena);
    }
    
  } else {
    
  }
  end_ = end;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.end)
}

// .carbon.metrics.WeedCounterChunk weed_count = 3;
inline bool SpatialMetricBlock::_internal_has_weed_count() const {
  return this != internal_default_instance() && weed_count_ != nullptr;
}
inline bool SpatialMetricBlock::has_weed_count() const {
  return _internal_has_weed_count();
}
inline void SpatialMetricBlock::clear_weed_count() {
  if (GetArenaForAllocation() == nullptr && weed_count_ != nullptr) {
    delete weed_count_;
  }
  weed_count_ = nullptr;
}
inline const ::carbon::metrics::WeedCounterChunk& SpatialMetricBlock::_internal_weed_count() const {
  const ::carbon::metrics::WeedCounterChunk* p = weed_count_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::WeedCounterChunk&>(
      ::carbon::metrics::_WeedCounterChunk_default_instance_);
}
inline const ::carbon::metrics::WeedCounterChunk& SpatialMetricBlock::weed_count() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.weed_count)
  return _internal_weed_count();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_weed_count(
    ::carbon::metrics::WeedCounterChunk* weed_count) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(weed_count_);
  }
  weed_count_ = weed_count;
  if (weed_count) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.weed_count)
}
inline ::carbon::metrics::WeedCounterChunk* SpatialMetricBlock::release_weed_count() {
  
  ::carbon::metrics::WeedCounterChunk* temp = weed_count_;
  weed_count_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::WeedCounterChunk* SpatialMetricBlock::unsafe_arena_release_weed_count() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.weed_count)
  
  ::carbon::metrics::WeedCounterChunk* temp = weed_count_;
  weed_count_ = nullptr;
  return temp;
}
inline ::carbon::metrics::WeedCounterChunk* SpatialMetricBlock::_internal_mutable_weed_count() {
  
  if (weed_count_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::WeedCounterChunk>(GetArenaForAllocation());
    weed_count_ = p;
  }
  return weed_count_;
}
inline ::carbon::metrics::WeedCounterChunk* SpatialMetricBlock::mutable_weed_count() {
  ::carbon::metrics::WeedCounterChunk* _msg = _internal_mutable_weed_count();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.weed_count)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_weed_count(::carbon::metrics::WeedCounterChunk* weed_count) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete weed_count_;
  }
  if (weed_count) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::WeedCounterChunk>::GetOwningArena(weed_count);
    if (message_arena != submessage_arena) {
      weed_count = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, weed_count, submessage_arena);
    }
    
  } else {
    
  }
  weed_count_ = weed_count;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.weed_count)
}

// .carbon.metrics.WheelEncoderSpatialData we_data = 4;
inline bool SpatialMetricBlock::_internal_has_we_data() const {
  return this != internal_default_instance() && we_data_ != nullptr;
}
inline bool SpatialMetricBlock::has_we_data() const {
  return _internal_has_we_data();
}
inline void SpatialMetricBlock::clear_we_data() {
  if (GetArenaForAllocation() == nullptr && we_data_ != nullptr) {
    delete we_data_;
  }
  we_data_ = nullptr;
}
inline const ::carbon::metrics::WheelEncoderSpatialData& SpatialMetricBlock::_internal_we_data() const {
  const ::carbon::metrics::WheelEncoderSpatialData* p = we_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::WheelEncoderSpatialData&>(
      ::carbon::metrics::_WheelEncoderSpatialData_default_instance_);
}
inline const ::carbon::metrics::WheelEncoderSpatialData& SpatialMetricBlock::we_data() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.we_data)
  return _internal_we_data();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_we_data(
    ::carbon::metrics::WheelEncoderSpatialData* we_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(we_data_);
  }
  we_data_ = we_data;
  if (we_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.we_data)
}
inline ::carbon::metrics::WheelEncoderSpatialData* SpatialMetricBlock::release_we_data() {
  
  ::carbon::metrics::WheelEncoderSpatialData* temp = we_data_;
  we_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::WheelEncoderSpatialData* SpatialMetricBlock::unsafe_arena_release_we_data() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.we_data)
  
  ::carbon::metrics::WheelEncoderSpatialData* temp = we_data_;
  we_data_ = nullptr;
  return temp;
}
inline ::carbon::metrics::WheelEncoderSpatialData* SpatialMetricBlock::_internal_mutable_we_data() {
  
  if (we_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::WheelEncoderSpatialData>(GetArenaForAllocation());
    we_data_ = p;
  }
  return we_data_;
}
inline ::carbon::metrics::WheelEncoderSpatialData* SpatialMetricBlock::mutable_we_data() {
  ::carbon::metrics::WheelEncoderSpatialData* _msg = _internal_mutable_we_data();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.we_data)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_we_data(::carbon::metrics::WheelEncoderSpatialData* we_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete we_data_;
  }
  if (we_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::WheelEncoderSpatialData>::GetOwningArena(we_data);
    if (message_arena != submessage_arena) {
      we_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, we_data, submessage_arena);
    }
    
  } else {
    
  }
  we_data_ = we_data;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.we_data)
}

// .carbon.metrics.BandingSpatialData banding_data = 5;
inline bool SpatialMetricBlock::_internal_has_banding_data() const {
  return this != internal_default_instance() && banding_data_ != nullptr;
}
inline bool SpatialMetricBlock::has_banding_data() const {
  return _internal_has_banding_data();
}
inline void SpatialMetricBlock::clear_banding_data() {
  if (GetArenaForAllocation() == nullptr && banding_data_ != nullptr) {
    delete banding_data_;
  }
  banding_data_ = nullptr;
}
inline const ::carbon::metrics::BandingSpatialData& SpatialMetricBlock::_internal_banding_data() const {
  const ::carbon::metrics::BandingSpatialData* p = banding_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::BandingSpatialData&>(
      ::carbon::metrics::_BandingSpatialData_default_instance_);
}
inline const ::carbon::metrics::BandingSpatialData& SpatialMetricBlock::banding_data() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.banding_data)
  return _internal_banding_data();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_banding_data(
    ::carbon::metrics::BandingSpatialData* banding_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(banding_data_);
  }
  banding_data_ = banding_data;
  if (banding_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.banding_data)
}
inline ::carbon::metrics::BandingSpatialData* SpatialMetricBlock::release_banding_data() {
  
  ::carbon::metrics::BandingSpatialData* temp = banding_data_;
  banding_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::BandingSpatialData* SpatialMetricBlock::unsafe_arena_release_banding_data() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.banding_data)
  
  ::carbon::metrics::BandingSpatialData* temp = banding_data_;
  banding_data_ = nullptr;
  return temp;
}
inline ::carbon::metrics::BandingSpatialData* SpatialMetricBlock::_internal_mutable_banding_data() {
  
  if (banding_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::BandingSpatialData>(GetArenaForAllocation());
    banding_data_ = p;
  }
  return banding_data_;
}
inline ::carbon::metrics::BandingSpatialData* SpatialMetricBlock::mutable_banding_data() {
  ::carbon::metrics::BandingSpatialData* _msg = _internal_mutable_banding_data();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.banding_data)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_banding_data(::carbon::metrics::BandingSpatialData* banding_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete banding_data_;
  }
  if (banding_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::BandingSpatialData>::GetOwningArena(banding_data);
    if (message_arena != submessage_arena) {
      banding_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, banding_data, submessage_arena);
    }
    
  } else {
    
  }
  banding_data_ = banding_data;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.banding_data)
}

// .carbon.metrics.ImplementWidthSpatialData implement_width_data = 6;
inline bool SpatialMetricBlock::_internal_has_implement_width_data() const {
  return this != internal_default_instance() && implement_width_data_ != nullptr;
}
inline bool SpatialMetricBlock::has_implement_width_data() const {
  return _internal_has_implement_width_data();
}
inline void SpatialMetricBlock::clear_implement_width_data() {
  if (GetArenaForAllocation() == nullptr && implement_width_data_ != nullptr) {
    delete implement_width_data_;
  }
  implement_width_data_ = nullptr;
}
inline const ::carbon::metrics::ImplementWidthSpatialData& SpatialMetricBlock::_internal_implement_width_data() const {
  const ::carbon::metrics::ImplementWidthSpatialData* p = implement_width_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::ImplementWidthSpatialData&>(
      ::carbon::metrics::_ImplementWidthSpatialData_default_instance_);
}
inline const ::carbon::metrics::ImplementWidthSpatialData& SpatialMetricBlock::implement_width_data() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.implement_width_data)
  return _internal_implement_width_data();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_implement_width_data(
    ::carbon::metrics::ImplementWidthSpatialData* implement_width_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(implement_width_data_);
  }
  implement_width_data_ = implement_width_data;
  if (implement_width_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.implement_width_data)
}
inline ::carbon::metrics::ImplementWidthSpatialData* SpatialMetricBlock::release_implement_width_data() {
  
  ::carbon::metrics::ImplementWidthSpatialData* temp = implement_width_data_;
  implement_width_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::ImplementWidthSpatialData* SpatialMetricBlock::unsafe_arena_release_implement_width_data() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.implement_width_data)
  
  ::carbon::metrics::ImplementWidthSpatialData* temp = implement_width_data_;
  implement_width_data_ = nullptr;
  return temp;
}
inline ::carbon::metrics::ImplementWidthSpatialData* SpatialMetricBlock::_internal_mutable_implement_width_data() {
  
  if (implement_width_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::ImplementWidthSpatialData>(GetArenaForAllocation());
    implement_width_data_ = p;
  }
  return implement_width_data_;
}
inline ::carbon::metrics::ImplementWidthSpatialData* SpatialMetricBlock::mutable_implement_width_data() {
  ::carbon::metrics::ImplementWidthSpatialData* _msg = _internal_mutable_implement_width_data();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.implement_width_data)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_implement_width_data(::carbon::metrics::ImplementWidthSpatialData* implement_width_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete implement_width_data_;
  }
  if (implement_width_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::ImplementWidthSpatialData>::GetOwningArena(implement_width_data);
    if (message_arena != submessage_arena) {
      implement_width_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, implement_width_data, submessage_arena);
    }
    
  } else {
    
  }
  implement_width_data_ = implement_width_data;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.implement_width_data)
}

// .carbon.metrics.VelocitySpatialMetric vel_data = 7;
inline bool SpatialMetricBlock::_internal_has_vel_data() const {
  return this != internal_default_instance() && vel_data_ != nullptr;
}
inline bool SpatialMetricBlock::has_vel_data() const {
  return _internal_has_vel_data();
}
inline void SpatialMetricBlock::clear_vel_data() {
  if (GetArenaForAllocation() == nullptr && vel_data_ != nullptr) {
    delete vel_data_;
  }
  vel_data_ = nullptr;
}
inline const ::carbon::metrics::VelocitySpatialMetric& SpatialMetricBlock::_internal_vel_data() const {
  const ::carbon::metrics::VelocitySpatialMetric* p = vel_data_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::VelocitySpatialMetric&>(
      ::carbon::metrics::_VelocitySpatialMetric_default_instance_);
}
inline const ::carbon::metrics::VelocitySpatialMetric& SpatialMetricBlock::vel_data() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.vel_data)
  return _internal_vel_data();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_vel_data(
    ::carbon::metrics::VelocitySpatialMetric* vel_data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(vel_data_);
  }
  vel_data_ = vel_data;
  if (vel_data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.vel_data)
}
inline ::carbon::metrics::VelocitySpatialMetric* SpatialMetricBlock::release_vel_data() {
  
  ::carbon::metrics::VelocitySpatialMetric* temp = vel_data_;
  vel_data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::VelocitySpatialMetric* SpatialMetricBlock::unsafe_arena_release_vel_data() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.vel_data)
  
  ::carbon::metrics::VelocitySpatialMetric* temp = vel_data_;
  vel_data_ = nullptr;
  return temp;
}
inline ::carbon::metrics::VelocitySpatialMetric* SpatialMetricBlock::_internal_mutable_vel_data() {
  
  if (vel_data_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::VelocitySpatialMetric>(GetArenaForAllocation());
    vel_data_ = p;
  }
  return vel_data_;
}
inline ::carbon::metrics::VelocitySpatialMetric* SpatialMetricBlock::mutable_vel_data() {
  ::carbon::metrics::VelocitySpatialMetric* _msg = _internal_mutable_vel_data();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.vel_data)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_vel_data(::carbon::metrics::VelocitySpatialMetric* vel_data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete vel_data_;
  }
  if (vel_data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::VelocitySpatialMetric>::GetOwningArena(vel_data);
    if (message_arena != submessage_arena) {
      vel_data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, vel_data, submessage_arena);
    }
    
  } else {
    
  }
  vel_data_ = vel_data;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.vel_data)
}

// .carbon.metrics.SpatialPosition start_left = 8;
inline bool SpatialMetricBlock::_internal_has_start_left() const {
  return this != internal_default_instance() && start_left_ != nullptr;
}
inline bool SpatialMetricBlock::has_start_left() const {
  return _internal_has_start_left();
}
inline void SpatialMetricBlock::clear_start_left() {
  if (GetArenaForAllocation() == nullptr && start_left_ != nullptr) {
    delete start_left_;
  }
  start_left_ = nullptr;
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::_internal_start_left() const {
  const ::carbon::metrics::SpatialPosition* p = start_left_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::SpatialPosition&>(
      ::carbon::metrics::_SpatialPosition_default_instance_);
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::start_left() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.start_left)
  return _internal_start_left();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_start_left(
    ::carbon::metrics::SpatialPosition* start_left) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_left_);
  }
  start_left_ = start_left;
  if (start_left) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.start_left)
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::release_start_left() {
  
  ::carbon::metrics::SpatialPosition* temp = start_left_;
  start_left_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::unsafe_arena_release_start_left() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.start_left)
  
  ::carbon::metrics::SpatialPosition* temp = start_left_;
  start_left_ = nullptr;
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::_internal_mutable_start_left() {
  
  if (start_left_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::SpatialPosition>(GetArenaForAllocation());
    start_left_ = p;
  }
  return start_left_;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::mutable_start_left() {
  ::carbon::metrics::SpatialPosition* _msg = _internal_mutable_start_left();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.start_left)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_start_left(::carbon::metrics::SpatialPosition* start_left) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete start_left_;
  }
  if (start_left) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::SpatialPosition>::GetOwningArena(start_left);
    if (message_arena != submessage_arena) {
      start_left = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start_left, submessage_arena);
    }
    
  } else {
    
  }
  start_left_ = start_left;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.start_left)
}

// .carbon.metrics.SpatialPosition start_right = 9;
inline bool SpatialMetricBlock::_internal_has_start_right() const {
  return this != internal_default_instance() && start_right_ != nullptr;
}
inline bool SpatialMetricBlock::has_start_right() const {
  return _internal_has_start_right();
}
inline void SpatialMetricBlock::clear_start_right() {
  if (GetArenaForAllocation() == nullptr && start_right_ != nullptr) {
    delete start_right_;
  }
  start_right_ = nullptr;
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::_internal_start_right() const {
  const ::carbon::metrics::SpatialPosition* p = start_right_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::SpatialPosition&>(
      ::carbon::metrics::_SpatialPosition_default_instance_);
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::start_right() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.start_right)
  return _internal_start_right();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_start_right(
    ::carbon::metrics::SpatialPosition* start_right) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_right_);
  }
  start_right_ = start_right;
  if (start_right) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.start_right)
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::release_start_right() {
  
  ::carbon::metrics::SpatialPosition* temp = start_right_;
  start_right_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::unsafe_arena_release_start_right() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.start_right)
  
  ::carbon::metrics::SpatialPosition* temp = start_right_;
  start_right_ = nullptr;
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::_internal_mutable_start_right() {
  
  if (start_right_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::SpatialPosition>(GetArenaForAllocation());
    start_right_ = p;
  }
  return start_right_;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::mutable_start_right() {
  ::carbon::metrics::SpatialPosition* _msg = _internal_mutable_start_right();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.start_right)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_start_right(::carbon::metrics::SpatialPosition* start_right) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete start_right_;
  }
  if (start_right) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::SpatialPosition>::GetOwningArena(start_right);
    if (message_arena != submessage_arena) {
      start_right = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start_right, submessage_arena);
    }
    
  } else {
    
  }
  start_right_ = start_right;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.start_right)
}

// .carbon.metrics.SpatialPosition end_left = 10;
inline bool SpatialMetricBlock::_internal_has_end_left() const {
  return this != internal_default_instance() && end_left_ != nullptr;
}
inline bool SpatialMetricBlock::has_end_left() const {
  return _internal_has_end_left();
}
inline void SpatialMetricBlock::clear_end_left() {
  if (GetArenaForAllocation() == nullptr && end_left_ != nullptr) {
    delete end_left_;
  }
  end_left_ = nullptr;
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::_internal_end_left() const {
  const ::carbon::metrics::SpatialPosition* p = end_left_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::SpatialPosition&>(
      ::carbon::metrics::_SpatialPosition_default_instance_);
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::end_left() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.end_left)
  return _internal_end_left();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_end_left(
    ::carbon::metrics::SpatialPosition* end_left) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_left_);
  }
  end_left_ = end_left;
  if (end_left) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.end_left)
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::release_end_left() {
  
  ::carbon::metrics::SpatialPosition* temp = end_left_;
  end_left_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::unsafe_arena_release_end_left() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.end_left)
  
  ::carbon::metrics::SpatialPosition* temp = end_left_;
  end_left_ = nullptr;
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::_internal_mutable_end_left() {
  
  if (end_left_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::SpatialPosition>(GetArenaForAllocation());
    end_left_ = p;
  }
  return end_left_;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::mutable_end_left() {
  ::carbon::metrics::SpatialPosition* _msg = _internal_mutable_end_left();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.end_left)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_end_left(::carbon::metrics::SpatialPosition* end_left) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete end_left_;
  }
  if (end_left) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::SpatialPosition>::GetOwningArena(end_left);
    if (message_arena != submessage_arena) {
      end_left = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end_left, submessage_arena);
    }
    
  } else {
    
  }
  end_left_ = end_left;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.end_left)
}

// .carbon.metrics.SpatialPosition end_right = 11;
inline bool SpatialMetricBlock::_internal_has_end_right() const {
  return this != internal_default_instance() && end_right_ != nullptr;
}
inline bool SpatialMetricBlock::has_end_right() const {
  return _internal_has_end_right();
}
inline void SpatialMetricBlock::clear_end_right() {
  if (GetArenaForAllocation() == nullptr && end_right_ != nullptr) {
    delete end_right_;
  }
  end_right_ = nullptr;
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::_internal_end_right() const {
  const ::carbon::metrics::SpatialPosition* p = end_right_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::SpatialPosition&>(
      ::carbon::metrics::_SpatialPosition_default_instance_);
}
inline const ::carbon::metrics::SpatialPosition& SpatialMetricBlock::end_right() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.end_right)
  return _internal_end_right();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_end_right(
    ::carbon::metrics::SpatialPosition* end_right) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_right_);
  }
  end_right_ = end_right;
  if (end_right) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.end_right)
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::release_end_right() {
  
  ::carbon::metrics::SpatialPosition* temp = end_right_;
  end_right_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::unsafe_arena_release_end_right() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.end_right)
  
  ::carbon::metrics::SpatialPosition* temp = end_right_;
  end_right_ = nullptr;
  return temp;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::_internal_mutable_end_right() {
  
  if (end_right_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::SpatialPosition>(GetArenaForAllocation());
    end_right_ = p;
  }
  return end_right_;
}
inline ::carbon::metrics::SpatialPosition* SpatialMetricBlock::mutable_end_right() {
  ::carbon::metrics::SpatialPosition* _msg = _internal_mutable_end_right();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.end_right)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_end_right(::carbon::metrics::SpatialPosition* end_right) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete end_right_;
  }
  if (end_right) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::SpatialPosition>::GetOwningArena(end_right);
    if (message_arena != submessage_arena) {
      end_right = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end_right, submessage_arena);
    }
    
  } else {
    
  }
  end_right_ = end_right;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.end_right)
}

// .carbon.metrics.JobMetric job_metric = 12;
inline bool SpatialMetricBlock::_internal_has_job_metric() const {
  return this != internal_default_instance() && job_metric_ != nullptr;
}
inline bool SpatialMetricBlock::has_job_metric() const {
  return _internal_has_job_metric();
}
inline void SpatialMetricBlock::clear_job_metric() {
  if (GetArenaForAllocation() == nullptr && job_metric_ != nullptr) {
    delete job_metric_;
  }
  job_metric_ = nullptr;
}
inline const ::carbon::metrics::JobMetric& SpatialMetricBlock::_internal_job_metric() const {
  const ::carbon::metrics::JobMetric* p = job_metric_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::JobMetric&>(
      ::carbon::metrics::_JobMetric_default_instance_);
}
inline const ::carbon::metrics::JobMetric& SpatialMetricBlock::job_metric() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.job_metric)
  return _internal_job_metric();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_job_metric(
    ::carbon::metrics::JobMetric* job_metric) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(job_metric_);
  }
  job_metric_ = job_metric;
  if (job_metric) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.job_metric)
}
inline ::carbon::metrics::JobMetric* SpatialMetricBlock::release_job_metric() {
  
  ::carbon::metrics::JobMetric* temp = job_metric_;
  job_metric_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::JobMetric* SpatialMetricBlock::unsafe_arena_release_job_metric() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.job_metric)
  
  ::carbon::metrics::JobMetric* temp = job_metric_;
  job_metric_ = nullptr;
  return temp;
}
inline ::carbon::metrics::JobMetric* SpatialMetricBlock::_internal_mutable_job_metric() {
  
  if (job_metric_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::JobMetric>(GetArenaForAllocation());
    job_metric_ = p;
  }
  return job_metric_;
}
inline ::carbon::metrics::JobMetric* SpatialMetricBlock::mutable_job_metric() {
  ::carbon::metrics::JobMetric* _msg = _internal_mutable_job_metric();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.job_metric)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_job_metric(::carbon::metrics::JobMetric* job_metric) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete job_metric_;
  }
  if (job_metric) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::JobMetric>::GetOwningArena(job_metric);
    if (message_arena != submessage_arena) {
      job_metric = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, job_metric, submessage_arena);
    }
    
  } else {
    
  }
  job_metric_ = job_metric;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.job_metric)
}

// bool suspicious = 13;
inline void SpatialMetricBlock::clear_suspicious() {
  suspicious_ = false;
}
inline bool SpatialMetricBlock::_internal_suspicious() const {
  return suspicious_;
}
inline bool SpatialMetricBlock::suspicious() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.suspicious)
  return _internal_suspicious();
}
inline void SpatialMetricBlock::_internal_set_suspicious(bool value) {
  
  suspicious_ = value;
}
inline void SpatialMetricBlock::set_suspicious(bool value) {
  _internal_set_suspicious(value);
  // @@protoc_insertion_point(field_set:carbon.metrics.SpatialMetricBlock.suspicious)
}

// .carbon.metrics.HWMetric hw_metric = 14;
inline bool SpatialMetricBlock::_internal_has_hw_metric() const {
  return this != internal_default_instance() && hw_metric_ != nullptr;
}
inline bool SpatialMetricBlock::has_hw_metric() const {
  return _internal_has_hw_metric();
}
inline void SpatialMetricBlock::clear_hw_metric() {
  if (GetArenaForAllocation() == nullptr && hw_metric_ != nullptr) {
    delete hw_metric_;
  }
  hw_metric_ = nullptr;
}
inline const ::carbon::metrics::HWMetric& SpatialMetricBlock::_internal_hw_metric() const {
  const ::carbon::metrics::HWMetric* p = hw_metric_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::metrics::HWMetric&>(
      ::carbon::metrics::_HWMetric_default_instance_);
}
inline const ::carbon::metrics::HWMetric& SpatialMetricBlock::hw_metric() const {
  // @@protoc_insertion_point(field_get:carbon.metrics.SpatialMetricBlock.hw_metric)
  return _internal_hw_metric();
}
inline void SpatialMetricBlock::unsafe_arena_set_allocated_hw_metric(
    ::carbon::metrics::HWMetric* hw_metric) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(hw_metric_);
  }
  hw_metric_ = hw_metric;
  if (hw_metric) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.metrics.SpatialMetricBlock.hw_metric)
}
inline ::carbon::metrics::HWMetric* SpatialMetricBlock::release_hw_metric() {
  
  ::carbon::metrics::HWMetric* temp = hw_metric_;
  hw_metric_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::metrics::HWMetric* SpatialMetricBlock::unsafe_arena_release_hw_metric() {
  // @@protoc_insertion_point(field_release:carbon.metrics.SpatialMetricBlock.hw_metric)
  
  ::carbon::metrics::HWMetric* temp = hw_metric_;
  hw_metric_ = nullptr;
  return temp;
}
inline ::carbon::metrics::HWMetric* SpatialMetricBlock::_internal_mutable_hw_metric() {
  
  if (hw_metric_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::metrics::HWMetric>(GetArenaForAllocation());
    hw_metric_ = p;
  }
  return hw_metric_;
}
inline ::carbon::metrics::HWMetric* SpatialMetricBlock::mutable_hw_metric() {
  ::carbon::metrics::HWMetric* _msg = _internal_mutable_hw_metric();
  // @@protoc_insertion_point(field_mutable:carbon.metrics.SpatialMetricBlock.hw_metric)
  return _msg;
}
inline void SpatialMetricBlock::set_allocated_hw_metric(::carbon::metrics::HWMetric* hw_metric) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete hw_metric_;
  }
  if (hw_metric) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::metrics::HWMetric>::GetOwningArena(hw_metric);
    if (message_arena != submessage_arena) {
      hw_metric = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hw_metric, submessage_arena);
    }
    
  } else {
    
  }
  hw_metric_ = hw_metric;
  // @@protoc_insertion_point(field_set_allocated:carbon.metrics.SpatialMetricBlock.hw_metric)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace metrics
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2fmetrics_2fmetrics_2eproto

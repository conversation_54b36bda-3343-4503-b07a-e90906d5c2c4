# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/metrics/metrics.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/metrics/metrics.proto',
  package='carbon.metrics',
  syntax='proto3',
  serialized_options=b'Z\rproto/metrics',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1bproto/metrics/metrics.proto\x12\x0e\x63\x61rbon.metrics\"*\n\rLaserPosition\x12\x0b\n\x03row\x18\x01 \x01(\r\x12\x0c\n\x04slot\x18\x02 \x01(\r\"R\n\x0fLaserIdentifier\x12/\n\x08position\x18\x01 \x01(\x0b\x32\x1d.carbon.metrics.LaserPosition\x12\x0e\n\x06serial\x18\x02 \x01(\t\"R\n\rLaserLifeTime\x12+\n\x02id\x18\x01 \x01(\x0b\x32\x1f.carbon.metrics.LaserIdentifier\x12\x14\n\x0clifetime_sec\x18\x02 \x01(\x04\"T\n\x0eLaserEventTime\x12+\n\x02id\x18\x01 \x01(\x0b\x32\x1f.carbon.metrics.LaserIdentifier\x12\x15\n\rtimestamp_sec\x18\x02 \x01(\x03\"B\n\x0eLaserLifeTimes\x12\x30\n\tlifetimes\x18\x01 \x03(\x0b\x32\x1d.carbon.metrics.LaserLifeTime\"v\n\x10LaserChangeTimes\x12\x30\n\x08installs\x18\x01 \x03(\x0b\x32\x1e.carbon.metrics.LaserEventTime\x12\x30\n\x08removals\x18\x02 \x03(\x0b\x32\x1e.carbon.metrics.LaserEventTime\"n\n\x16\x43ountsByConclusionType\x12\x15\n\rdisarmed_weed\x18\x01 \x03(\r\x12\x12\n\narmed_weed\x18\x02 \x03(\r\x12\x15\n\rdisarmed_crop\x18\x03 \x03(\r\x12\x12\n\narmed_crop\x18\x04 \x03(\r\"8\n\x0eTargetSizeData\x12\x17\n\x0f\x63umulative_size\x18\x01 \x01(\x01\x12\r\n\x05\x63ount\x18\x02 \x01(\x04\"?\n\x15RequiredLaserTimeData\x12\x17\n\x0f\x63umulative_time\x18\x01 \x01(\x04\x12\r\n\x05\x63ount\x18\x02 \x01(\x04\"\x8f\x01\n\x0fSpatialPosition\x12\x10\n\x08latitude\x18\x01 \x01(\x01\x12\x11\n\tlongitude\x18\x02 \x01(\x01\x12\x11\n\theight_mm\x18\x03 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x04 \x01(\x04\x12\x0e\n\x06\x65\x63\x65\x66_x\x18\x05 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_y\x18\x06 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_z\x18\x07 \x01(\x01\"\x80\x04\n\x10WeedCounterChunk\x12\x41\n\x11\x63onclusion_counts\x18\x01 \x01(\x0b\x32&.carbon.metrics.CountsByConclusionType\x12\x36\n\x0eweed_size_data\x18\x02 \x01(\x0b\x32\x1e.carbon.metrics.TargetSizeData\x12\x36\n\x0e\x63rop_size_data\x18\x03 \x01(\x0b\x32\x1e.carbon.metrics.TargetSizeData\x12R\n\x12\x63ounts_by_category\x18\x04 \x03(\x0b\x32\x36.carbon.metrics.WeedCounterChunk.CountsByCategoryEntry\x12G\n\x18targeted_laser_time_data\x18\x05 \x01(\x0b\x32%.carbon.metrics.RequiredLaserTimeData\x12I\n\x1auntargeted_laser_time_data\x18\x06 \x01(\x0b\x32%.carbon.metrics.RequiredLaserTimeData\x12\x18\n\x10valid_crop_count\x18\x07 \x01(\x04\x1a\x37\n\x15\x43ountsByCategoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\r:\x02\x38\x01\"A\n\x17WheelEncoderSpatialData\x12\x13\n\x0bstart_pos_m\x18\x01 \x01(\x02\x12\x11\n\tend_pos_m\x18\x02 \x01(\x02\",\n\x12\x42\x61ndingSpatialData\x12\x16\n\x0epercent_banded\x18\x01 \x01(\x02\"-\n\x19ImplementWidthSpatialData\x12\x10\n\x08width_mm\x18\x01 \x01(\x02\"\x9d\x01\n\x15VelocitySpatialMetric\x12O\n\x0e\x61vg_target_vel\x18\x01 \x03(\x0b\x32\x37.carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry\x1a\x33\n\x11\x41vgTargetVelEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\x1b\n\tJobMetric\x12\x0e\n\x06job_id\x18\x01 \x01(\t\"}\n\x08HWMetric\x12\x0e\n\x06lifted\x18\x01 \x01(\x08\x12\x10\n\x08\x65stopped\x18\x02 \x01(\x08\x12\x11\n\tlaser_key\x18\x03 \x01(\x08\x12\x11\n\tinterlock\x18\x04 \x01(\x08\x12\x15\n\rwater_protect\x18\x05 \x01(\x08\x12\x12\n\ndebug_mode\x18\x06 \x01(\x08\"\xe0\x05\n\x12SpatialMetricBlock\x12.\n\x05start\x18\x01 \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12,\n\x03\x65nd\x18\x02 \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12\x34\n\nweed_count\x18\x03 \x01(\x0b\x32 .carbon.metrics.WeedCounterChunk\x12\x38\n\x07we_data\x18\x04 \x01(\x0b\x32\'.carbon.metrics.WheelEncoderSpatialData\x12\x38\n\x0c\x62\x61nding_data\x18\x05 \x01(\x0b\x32\".carbon.metrics.BandingSpatialData\x12G\n\x14implement_width_data\x18\x06 \x01(\x0b\x32).carbon.metrics.ImplementWidthSpatialData\x12\x37\n\x08vel_data\x18\x07 \x01(\x0b\x32%.carbon.metrics.VelocitySpatialMetric\x12\x33\n\nstart_left\x18\x08 \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12\x34\n\x0bstart_right\x18\t \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12\x31\n\x08\x65nd_left\x18\n \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12\x32\n\tend_right\x18\x0b \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12-\n\njob_metric\x18\x0c \x01(\x0b\x32\x19.carbon.metrics.JobMetric\x12\x12\n\nsuspicious\x18\r \x01(\x08\x12+\n\thw_metric\x18\x0e \x01(\x0b\x32\x18.carbon.metrics.HWMetricB\x0fZ\rproto/metricsb\x06proto3'
)




_LASERPOSITION = _descriptor.Descriptor(
  name='LaserPosition',
  full_name='carbon.metrics.LaserPosition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row', full_name='carbon.metrics.LaserPosition.row', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='slot', full_name='carbon.metrics.LaserPosition.slot', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=47,
  serialized_end=89,
)


_LASERIDENTIFIER = _descriptor.Descriptor(
  name='LaserIdentifier',
  full_name='carbon.metrics.LaserIdentifier',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='position', full_name='carbon.metrics.LaserIdentifier.position', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='serial', full_name='carbon.metrics.LaserIdentifier.serial', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=91,
  serialized_end=173,
)


_LASERLIFETIME = _descriptor.Descriptor(
  name='LaserLifeTime',
  full_name='carbon.metrics.LaserLifeTime',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.metrics.LaserLifeTime.id', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifetime_sec', full_name='carbon.metrics.LaserLifeTime.lifetime_sec', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=175,
  serialized_end=257,
)


_LASEREVENTTIME = _descriptor.Descriptor(
  name='LaserEventTime',
  full_name='carbon.metrics.LaserEventTime',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.metrics.LaserEventTime.id', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_sec', full_name='carbon.metrics.LaserEventTime.timestamp_sec', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=259,
  serialized_end=343,
)


_LASERLIFETIMES = _descriptor.Descriptor(
  name='LaserLifeTimes',
  full_name='carbon.metrics.LaserLifeTimes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lifetimes', full_name='carbon.metrics.LaserLifeTimes.lifetimes', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=345,
  serialized_end=411,
)


_LASERCHANGETIMES = _descriptor.Descriptor(
  name='LaserChangeTimes',
  full_name='carbon.metrics.LaserChangeTimes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='installs', full_name='carbon.metrics.LaserChangeTimes.installs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='removals', full_name='carbon.metrics.LaserChangeTimes.removals', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=413,
  serialized_end=531,
)


_COUNTSBYCONCLUSIONTYPE = _descriptor.Descriptor(
  name='CountsByConclusionType',
  full_name='carbon.metrics.CountsByConclusionType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disarmed_weed', full_name='carbon.metrics.CountsByConclusionType.disarmed_weed', index=0,
      number=1, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='armed_weed', full_name='carbon.metrics.CountsByConclusionType.armed_weed', index=1,
      number=2, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='disarmed_crop', full_name='carbon.metrics.CountsByConclusionType.disarmed_crop', index=2,
      number=3, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='armed_crop', full_name='carbon.metrics.CountsByConclusionType.armed_crop', index=3,
      number=4, type=13, cpp_type=3, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=533,
  serialized_end=643,
)


_TARGETSIZEDATA = _descriptor.Descriptor(
  name='TargetSizeData',
  full_name='carbon.metrics.TargetSizeData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cumulative_size', full_name='carbon.metrics.TargetSizeData.cumulative_size', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='count', full_name='carbon.metrics.TargetSizeData.count', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=645,
  serialized_end=701,
)


_REQUIREDLASERTIMEDATA = _descriptor.Descriptor(
  name='RequiredLaserTimeData',
  full_name='carbon.metrics.RequiredLaserTimeData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cumulative_time', full_name='carbon.metrics.RequiredLaserTimeData.cumulative_time', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='count', full_name='carbon.metrics.RequiredLaserTimeData.count', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=703,
  serialized_end=766,
)


_SPATIALPOSITION = _descriptor.Descriptor(
  name='SpatialPosition',
  full_name='carbon.metrics.SpatialPosition',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='latitude', full_name='carbon.metrics.SpatialPosition.latitude', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='longitude', full_name='carbon.metrics.SpatialPosition.longitude', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height_mm', full_name='carbon.metrics.SpatialPosition.height_mm', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.metrics.SpatialPosition.timestamp_ms', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_x', full_name='carbon.metrics.SpatialPosition.ecef_x', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_y', full_name='carbon.metrics.SpatialPosition.ecef_y', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_z', full_name='carbon.metrics.SpatialPosition.ecef_z', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=769,
  serialized_end=912,
)


_WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY = _descriptor.Descriptor(
  name='CountsByCategoryEntry',
  full_name='carbon.metrics.WeedCounterChunk.CountsByCategoryEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.metrics.WeedCounterChunk.CountsByCategoryEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.metrics.WeedCounterChunk.CountsByCategoryEntry.value', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1372,
  serialized_end=1427,
)

_WEEDCOUNTERCHUNK = _descriptor.Descriptor(
  name='WeedCounterChunk',
  full_name='carbon.metrics.WeedCounterChunk',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='conclusion_counts', full_name='carbon.metrics.WeedCounterChunk.conclusion_counts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_size_data', full_name='carbon.metrics.WeedCounterChunk.weed_size_data', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_size_data', full_name='carbon.metrics.WeedCounterChunk.crop_size_data', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='counts_by_category', full_name='carbon.metrics.WeedCounterChunk.counts_by_category', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='targeted_laser_time_data', full_name='carbon.metrics.WeedCounterChunk.targeted_laser_time_data', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='untargeted_laser_time_data', full_name='carbon.metrics.WeedCounterChunk.untargeted_laser_time_data', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='valid_crop_count', full_name='carbon.metrics.WeedCounterChunk.valid_crop_count', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=915,
  serialized_end=1427,
)


_WHEELENCODERSPATIALDATA = _descriptor.Descriptor(
  name='WheelEncoderSpatialData',
  full_name='carbon.metrics.WheelEncoderSpatialData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_pos_m', full_name='carbon.metrics.WheelEncoderSpatialData.start_pos_m', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_pos_m', full_name='carbon.metrics.WheelEncoderSpatialData.end_pos_m', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1429,
  serialized_end=1494,
)


_BANDINGSPATIALDATA = _descriptor.Descriptor(
  name='BandingSpatialData',
  full_name='carbon.metrics.BandingSpatialData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='percent_banded', full_name='carbon.metrics.BandingSpatialData.percent_banded', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1496,
  serialized_end=1540,
)


_IMPLEMENTWIDTHSPATIALDATA = _descriptor.Descriptor(
  name='ImplementWidthSpatialData',
  full_name='carbon.metrics.ImplementWidthSpatialData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='width_mm', full_name='carbon.metrics.ImplementWidthSpatialData.width_mm', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1542,
  serialized_end=1587,
)


_VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY = _descriptor.Descriptor(
  name='AvgTargetVelEntry',
  full_name='carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1696,
  serialized_end=1747,
)

_VELOCITYSPATIALMETRIC = _descriptor.Descriptor(
  name='VelocitySpatialMetric',
  full_name='carbon.metrics.VelocitySpatialMetric',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='avg_target_vel', full_name='carbon.metrics.VelocitySpatialMetric.avg_target_vel', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1590,
  serialized_end=1747,
)


_JOBMETRIC = _descriptor.Descriptor(
  name='JobMetric',
  full_name='carbon.metrics.JobMetric',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='job_id', full_name='carbon.metrics.JobMetric.job_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1749,
  serialized_end=1776,
)


_HWMETRIC = _descriptor.Descriptor(
  name='HWMetric',
  full_name='carbon.metrics.HWMetric',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lifted', full_name='carbon.metrics.HWMetric.lifted', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='estopped', full_name='carbon.metrics.HWMetric.estopped', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_key', full_name='carbon.metrics.HWMetric.laser_key', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='interlock', full_name='carbon.metrics.HWMetric.interlock', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='water_protect', full_name='carbon.metrics.HWMetric.water_protect', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='debug_mode', full_name='carbon.metrics.HWMetric.debug_mode', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1778,
  serialized_end=1903,
)


_SPATIALMETRICBLOCK = _descriptor.Descriptor(
  name='SpatialMetricBlock',
  full_name='carbon.metrics.SpatialMetricBlock',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start', full_name='carbon.metrics.SpatialMetricBlock.start', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end', full_name='carbon.metrics.SpatialMetricBlock.end', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_count', full_name='carbon.metrics.SpatialMetricBlock.weed_count', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='we_data', full_name='carbon.metrics.SpatialMetricBlock.we_data', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding_data', full_name='carbon.metrics.SpatialMetricBlock.banding_data', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='implement_width_data', full_name='carbon.metrics.SpatialMetricBlock.implement_width_data', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='vel_data', full_name='carbon.metrics.SpatialMetricBlock.vel_data', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_left', full_name='carbon.metrics.SpatialMetricBlock.start_left', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_right', full_name='carbon.metrics.SpatialMetricBlock.start_right', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_left', full_name='carbon.metrics.SpatialMetricBlock.end_left', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_right', full_name='carbon.metrics.SpatialMetricBlock.end_right', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='job_metric', full_name='carbon.metrics.SpatialMetricBlock.job_metric', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='suspicious', full_name='carbon.metrics.SpatialMetricBlock.suspicious', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hw_metric', full_name='carbon.metrics.SpatialMetricBlock.hw_metric', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1906,
  serialized_end=2642,
)

_LASERIDENTIFIER.fields_by_name['position'].message_type = _LASERPOSITION
_LASERLIFETIME.fields_by_name['id'].message_type = _LASERIDENTIFIER
_LASEREVENTTIME.fields_by_name['id'].message_type = _LASERIDENTIFIER
_LASERLIFETIMES.fields_by_name['lifetimes'].message_type = _LASERLIFETIME
_LASERCHANGETIMES.fields_by_name['installs'].message_type = _LASEREVENTTIME
_LASERCHANGETIMES.fields_by_name['removals'].message_type = _LASEREVENTTIME
_WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY.containing_type = _WEEDCOUNTERCHUNK
_WEEDCOUNTERCHUNK.fields_by_name['conclusion_counts'].message_type = _COUNTSBYCONCLUSIONTYPE
_WEEDCOUNTERCHUNK.fields_by_name['weed_size_data'].message_type = _TARGETSIZEDATA
_WEEDCOUNTERCHUNK.fields_by_name['crop_size_data'].message_type = _TARGETSIZEDATA
_WEEDCOUNTERCHUNK.fields_by_name['counts_by_category'].message_type = _WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY
_WEEDCOUNTERCHUNK.fields_by_name['targeted_laser_time_data'].message_type = _REQUIREDLASERTIMEDATA
_WEEDCOUNTERCHUNK.fields_by_name['untargeted_laser_time_data'].message_type = _REQUIREDLASERTIMEDATA
_VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY.containing_type = _VELOCITYSPATIALMETRIC
_VELOCITYSPATIALMETRIC.fields_by_name['avg_target_vel'].message_type = _VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY
_SPATIALMETRICBLOCK.fields_by_name['start'].message_type = _SPATIALPOSITION
_SPATIALMETRICBLOCK.fields_by_name['end'].message_type = _SPATIALPOSITION
_SPATIALMETRICBLOCK.fields_by_name['weed_count'].message_type = _WEEDCOUNTERCHUNK
_SPATIALMETRICBLOCK.fields_by_name['we_data'].message_type = _WHEELENCODERSPATIALDATA
_SPATIALMETRICBLOCK.fields_by_name['banding_data'].message_type = _BANDINGSPATIALDATA
_SPATIALMETRICBLOCK.fields_by_name['implement_width_data'].message_type = _IMPLEMENTWIDTHSPATIALDATA
_SPATIALMETRICBLOCK.fields_by_name['vel_data'].message_type = _VELOCITYSPATIALMETRIC
_SPATIALMETRICBLOCK.fields_by_name['start_left'].message_type = _SPATIALPOSITION
_SPATIALMETRICBLOCK.fields_by_name['start_right'].message_type = _SPATIALPOSITION
_SPATIALMETRICBLOCK.fields_by_name['end_left'].message_type = _SPATIALPOSITION
_SPATIALMETRICBLOCK.fields_by_name['end_right'].message_type = _SPATIALPOSITION
_SPATIALMETRICBLOCK.fields_by_name['job_metric'].message_type = _JOBMETRIC
_SPATIALMETRICBLOCK.fields_by_name['hw_metric'].message_type = _HWMETRIC
DESCRIPTOR.message_types_by_name['LaserPosition'] = _LASERPOSITION
DESCRIPTOR.message_types_by_name['LaserIdentifier'] = _LASERIDENTIFIER
DESCRIPTOR.message_types_by_name['LaserLifeTime'] = _LASERLIFETIME
DESCRIPTOR.message_types_by_name['LaserEventTime'] = _LASEREVENTTIME
DESCRIPTOR.message_types_by_name['LaserLifeTimes'] = _LASERLIFETIMES
DESCRIPTOR.message_types_by_name['LaserChangeTimes'] = _LASERCHANGETIMES
DESCRIPTOR.message_types_by_name['CountsByConclusionType'] = _COUNTSBYCONCLUSIONTYPE
DESCRIPTOR.message_types_by_name['TargetSizeData'] = _TARGETSIZEDATA
DESCRIPTOR.message_types_by_name['RequiredLaserTimeData'] = _REQUIREDLASERTIMEDATA
DESCRIPTOR.message_types_by_name['SpatialPosition'] = _SPATIALPOSITION
DESCRIPTOR.message_types_by_name['WeedCounterChunk'] = _WEEDCOUNTERCHUNK
DESCRIPTOR.message_types_by_name['WheelEncoderSpatialData'] = _WHEELENCODERSPATIALDATA
DESCRIPTOR.message_types_by_name['BandingSpatialData'] = _BANDINGSPATIALDATA
DESCRIPTOR.message_types_by_name['ImplementWidthSpatialData'] = _IMPLEMENTWIDTHSPATIALDATA
DESCRIPTOR.message_types_by_name['VelocitySpatialMetric'] = _VELOCITYSPATIALMETRIC
DESCRIPTOR.message_types_by_name['JobMetric'] = _JOBMETRIC
DESCRIPTOR.message_types_by_name['HWMetric'] = _HWMETRIC
DESCRIPTOR.message_types_by_name['SpatialMetricBlock'] = _SPATIALMETRICBLOCK
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

LaserPosition = _reflection.GeneratedProtocolMessageType('LaserPosition', (_message.Message,), {
  'DESCRIPTOR' : _LASERPOSITION,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.LaserPosition)
  })
_sym_db.RegisterMessage(LaserPosition)

LaserIdentifier = _reflection.GeneratedProtocolMessageType('LaserIdentifier', (_message.Message,), {
  'DESCRIPTOR' : _LASERIDENTIFIER,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.LaserIdentifier)
  })
_sym_db.RegisterMessage(LaserIdentifier)

LaserLifeTime = _reflection.GeneratedProtocolMessageType('LaserLifeTime', (_message.Message,), {
  'DESCRIPTOR' : _LASERLIFETIME,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.LaserLifeTime)
  })
_sym_db.RegisterMessage(LaserLifeTime)

LaserEventTime = _reflection.GeneratedProtocolMessageType('LaserEventTime', (_message.Message,), {
  'DESCRIPTOR' : _LASEREVENTTIME,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.LaserEventTime)
  })
_sym_db.RegisterMessage(LaserEventTime)

LaserLifeTimes = _reflection.GeneratedProtocolMessageType('LaserLifeTimes', (_message.Message,), {
  'DESCRIPTOR' : _LASERLIFETIMES,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.LaserLifeTimes)
  })
_sym_db.RegisterMessage(LaserLifeTimes)

LaserChangeTimes = _reflection.GeneratedProtocolMessageType('LaserChangeTimes', (_message.Message,), {
  'DESCRIPTOR' : _LASERCHANGETIMES,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.LaserChangeTimes)
  })
_sym_db.RegisterMessage(LaserChangeTimes)

CountsByConclusionType = _reflection.GeneratedProtocolMessageType('CountsByConclusionType', (_message.Message,), {
  'DESCRIPTOR' : _COUNTSBYCONCLUSIONTYPE,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.CountsByConclusionType)
  })
_sym_db.RegisterMessage(CountsByConclusionType)

TargetSizeData = _reflection.GeneratedProtocolMessageType('TargetSizeData', (_message.Message,), {
  'DESCRIPTOR' : _TARGETSIZEDATA,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.TargetSizeData)
  })
_sym_db.RegisterMessage(TargetSizeData)

RequiredLaserTimeData = _reflection.GeneratedProtocolMessageType('RequiredLaserTimeData', (_message.Message,), {
  'DESCRIPTOR' : _REQUIREDLASERTIMEDATA,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.RequiredLaserTimeData)
  })
_sym_db.RegisterMessage(RequiredLaserTimeData)

SpatialPosition = _reflection.GeneratedProtocolMessageType('SpatialPosition', (_message.Message,), {
  'DESCRIPTOR' : _SPATIALPOSITION,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.SpatialPosition)
  })
_sym_db.RegisterMessage(SpatialPosition)

WeedCounterChunk = _reflection.GeneratedProtocolMessageType('WeedCounterChunk', (_message.Message,), {

  'CountsByCategoryEntry' : _reflection.GeneratedProtocolMessageType('CountsByCategoryEntry', (_message.Message,), {
    'DESCRIPTOR' : _WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY,
    '__module__' : 'proto.metrics.metrics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.metrics.WeedCounterChunk.CountsByCategoryEntry)
    })
  ,
  'DESCRIPTOR' : _WEEDCOUNTERCHUNK,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.WeedCounterChunk)
  })
_sym_db.RegisterMessage(WeedCounterChunk)
_sym_db.RegisterMessage(WeedCounterChunk.CountsByCategoryEntry)

WheelEncoderSpatialData = _reflection.GeneratedProtocolMessageType('WheelEncoderSpatialData', (_message.Message,), {
  'DESCRIPTOR' : _WHEELENCODERSPATIALDATA,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.WheelEncoderSpatialData)
  })
_sym_db.RegisterMessage(WheelEncoderSpatialData)

BandingSpatialData = _reflection.GeneratedProtocolMessageType('BandingSpatialData', (_message.Message,), {
  'DESCRIPTOR' : _BANDINGSPATIALDATA,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.BandingSpatialData)
  })
_sym_db.RegisterMessage(BandingSpatialData)

ImplementWidthSpatialData = _reflection.GeneratedProtocolMessageType('ImplementWidthSpatialData', (_message.Message,), {
  'DESCRIPTOR' : _IMPLEMENTWIDTHSPATIALDATA,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.ImplementWidthSpatialData)
  })
_sym_db.RegisterMessage(ImplementWidthSpatialData)

VelocitySpatialMetric = _reflection.GeneratedProtocolMessageType('VelocitySpatialMetric', (_message.Message,), {

  'AvgTargetVelEntry' : _reflection.GeneratedProtocolMessageType('AvgTargetVelEntry', (_message.Message,), {
    'DESCRIPTOR' : _VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY,
    '__module__' : 'proto.metrics.metrics_pb2'
    # @@protoc_insertion_point(class_scope:carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry)
    })
  ,
  'DESCRIPTOR' : _VELOCITYSPATIALMETRIC,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.VelocitySpatialMetric)
  })
_sym_db.RegisterMessage(VelocitySpatialMetric)
_sym_db.RegisterMessage(VelocitySpatialMetric.AvgTargetVelEntry)

JobMetric = _reflection.GeneratedProtocolMessageType('JobMetric', (_message.Message,), {
  'DESCRIPTOR' : _JOBMETRIC,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.JobMetric)
  })
_sym_db.RegisterMessage(JobMetric)

HWMetric = _reflection.GeneratedProtocolMessageType('HWMetric', (_message.Message,), {
  'DESCRIPTOR' : _HWMETRIC,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.HWMetric)
  })
_sym_db.RegisterMessage(HWMetric)

SpatialMetricBlock = _reflection.GeneratedProtocolMessageType('SpatialMetricBlock', (_message.Message,), {
  'DESCRIPTOR' : _SPATIALMETRICBLOCK,
  '__module__' : 'proto.metrics.metrics_pb2'
  # @@protoc_insertion_point(class_scope:carbon.metrics.SpatialMetricBlock)
  })
_sym_db.RegisterMessage(SpatialMetricBlock)


DESCRIPTOR._options = None
_WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY._options = None
_VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY._options = None
# @@protoc_insertion_point(module_scope)

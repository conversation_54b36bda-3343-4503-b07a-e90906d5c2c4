// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/metrics/metrics.proto

#include "proto/metrics/metrics.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace metrics {
constexpr LaserPosition::LaserPosition(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_(0u)
  , slot_(0u){}
struct LaserPositionDefaultTypeInternal {
  constexpr LaserPositionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserPositionDefaultTypeInternal() {}
  union {
    LaserPosition _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserPositionDefaultTypeInternal _LaserPosition_default_instance_;
constexpr LaserIdentifier::LaserIdentifier(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , position_(nullptr){}
struct LaserIdentifierDefaultTypeInternal {
  constexpr LaserIdentifierDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserIdentifierDefaultTypeInternal() {}
  union {
    LaserIdentifier _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserIdentifierDefaultTypeInternal _LaserIdentifier_default_instance_;
constexpr LaserLifeTime::LaserLifeTime(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(nullptr)
  , lifetime_sec_(uint64_t{0u}){}
struct LaserLifeTimeDefaultTypeInternal {
  constexpr LaserLifeTimeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserLifeTimeDefaultTypeInternal() {}
  union {
    LaserLifeTime _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserLifeTimeDefaultTypeInternal _LaserLifeTime_default_instance_;
constexpr LaserEventTime::LaserEventTime(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(nullptr)
  , timestamp_sec_(int64_t{0}){}
struct LaserEventTimeDefaultTypeInternal {
  constexpr LaserEventTimeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserEventTimeDefaultTypeInternal() {}
  union {
    LaserEventTime _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserEventTimeDefaultTypeInternal _LaserEventTime_default_instance_;
constexpr LaserLifeTimes::LaserLifeTimes(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lifetimes_(){}
struct LaserLifeTimesDefaultTypeInternal {
  constexpr LaserLifeTimesDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserLifeTimesDefaultTypeInternal() {}
  union {
    LaserLifeTimes _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserLifeTimesDefaultTypeInternal _LaserLifeTimes_default_instance_;
constexpr LaserChangeTimes::LaserChangeTimes(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : installs_()
  , removals_(){}
struct LaserChangeTimesDefaultTypeInternal {
  constexpr LaserChangeTimesDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserChangeTimesDefaultTypeInternal() {}
  union {
    LaserChangeTimes _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserChangeTimesDefaultTypeInternal _LaserChangeTimes_default_instance_;
constexpr CountsByConclusionType::CountsByConclusionType(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : disarmed_weed_()
  , _disarmed_weed_cached_byte_size_(0)
  , armed_weed_()
  , _armed_weed_cached_byte_size_(0)
  , disarmed_crop_()
  , _disarmed_crop_cached_byte_size_(0)
  , armed_crop_()
  , _armed_crop_cached_byte_size_(0){}
struct CountsByConclusionTypeDefaultTypeInternal {
  constexpr CountsByConclusionTypeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CountsByConclusionTypeDefaultTypeInternal() {}
  union {
    CountsByConclusionType _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CountsByConclusionTypeDefaultTypeInternal _CountsByConclusionType_default_instance_;
constexpr TargetSizeData::TargetSizeData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cumulative_size_(0)
  , count_(uint64_t{0u}){}
struct TargetSizeDataDefaultTypeInternal {
  constexpr TargetSizeDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TargetSizeDataDefaultTypeInternal() {}
  union {
    TargetSizeData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TargetSizeDataDefaultTypeInternal _TargetSizeData_default_instance_;
constexpr RequiredLaserTimeData::RequiredLaserTimeData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cumulative_time_(uint64_t{0u})
  , count_(uint64_t{0u}){}
struct RequiredLaserTimeDataDefaultTypeInternal {
  constexpr RequiredLaserTimeDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RequiredLaserTimeDataDefaultTypeInternal() {}
  union {
    RequiredLaserTimeData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RequiredLaserTimeDataDefaultTypeInternal _RequiredLaserTimeData_default_instance_;
constexpr SpatialPosition::SpatialPosition(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : latitude_(0)
  , longitude_(0)
  , height_mm_(0)
  , timestamp_ms_(uint64_t{0u})
  , ecef_x_(0)
  , ecef_y_(0)
  , ecef_z_(0){}
struct SpatialPositionDefaultTypeInternal {
  constexpr SpatialPositionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SpatialPositionDefaultTypeInternal() {}
  union {
    SpatialPosition _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SpatialPositionDefaultTypeInternal _SpatialPosition_default_instance_;
constexpr WeedCounterChunk_CountsByCategoryEntry_DoNotUse::WeedCounterChunk_CountsByCategoryEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct WeedCounterChunk_CountsByCategoryEntry_DoNotUseDefaultTypeInternal {
  constexpr WeedCounterChunk_CountsByCategoryEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~WeedCounterChunk_CountsByCategoryEntry_DoNotUseDefaultTypeInternal() {}
  union {
    WeedCounterChunk_CountsByCategoryEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT WeedCounterChunk_CountsByCategoryEntry_DoNotUseDefaultTypeInternal _WeedCounterChunk_CountsByCategoryEntry_DoNotUse_default_instance_;
constexpr WeedCounterChunk::WeedCounterChunk(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : counts_by_category_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , conclusion_counts_(nullptr)
  , weed_size_data_(nullptr)
  , crop_size_data_(nullptr)
  , targeted_laser_time_data_(nullptr)
  , untargeted_laser_time_data_(nullptr)
  , valid_crop_count_(uint64_t{0u}){}
struct WeedCounterChunkDefaultTypeInternal {
  constexpr WeedCounterChunkDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~WeedCounterChunkDefaultTypeInternal() {}
  union {
    WeedCounterChunk _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT WeedCounterChunkDefaultTypeInternal _WeedCounterChunk_default_instance_;
constexpr WheelEncoderSpatialData::WheelEncoderSpatialData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : start_pos_m_(0)
  , end_pos_m_(0){}
struct WheelEncoderSpatialDataDefaultTypeInternal {
  constexpr WheelEncoderSpatialDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~WheelEncoderSpatialDataDefaultTypeInternal() {}
  union {
    WheelEncoderSpatialData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT WheelEncoderSpatialDataDefaultTypeInternal _WheelEncoderSpatialData_default_instance_;
constexpr BandingSpatialData::BandingSpatialData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : percent_banded_(0){}
struct BandingSpatialDataDefaultTypeInternal {
  constexpr BandingSpatialDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BandingSpatialDataDefaultTypeInternal() {}
  union {
    BandingSpatialData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BandingSpatialDataDefaultTypeInternal _BandingSpatialData_default_instance_;
constexpr ImplementWidthSpatialData::ImplementWidthSpatialData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : width_mm_(0){}
struct ImplementWidthSpatialDataDefaultTypeInternal {
  constexpr ImplementWidthSpatialDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ImplementWidthSpatialDataDefaultTypeInternal() {}
  union {
    ImplementWidthSpatialData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ImplementWidthSpatialDataDefaultTypeInternal _ImplementWidthSpatialData_default_instance_;
constexpr VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct VelocitySpatialMetric_AvgTargetVelEntry_DoNotUseDefaultTypeInternal {
  constexpr VelocitySpatialMetric_AvgTargetVelEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VelocitySpatialMetric_AvgTargetVelEntry_DoNotUseDefaultTypeInternal() {}
  union {
    VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VelocitySpatialMetric_AvgTargetVelEntry_DoNotUseDefaultTypeInternal _VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse_default_instance_;
constexpr VelocitySpatialMetric::VelocitySpatialMetric(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : avg_target_vel_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct VelocitySpatialMetricDefaultTypeInternal {
  constexpr VelocitySpatialMetricDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VelocitySpatialMetricDefaultTypeInternal() {}
  union {
    VelocitySpatialMetric _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VelocitySpatialMetricDefaultTypeInternal _VelocitySpatialMetric_default_instance_;
constexpr JobMetric::JobMetric(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : job_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct JobMetricDefaultTypeInternal {
  constexpr JobMetricDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~JobMetricDefaultTypeInternal() {}
  union {
    JobMetric _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT JobMetricDefaultTypeInternal _JobMetric_default_instance_;
constexpr HWMetric::HWMetric(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lifted_(false)
  , estopped_(false)
  , laser_key_(false)
  , interlock_(false)
  , water_protect_(false)
  , debug_mode_(false){}
struct HWMetricDefaultTypeInternal {
  constexpr HWMetricDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HWMetricDefaultTypeInternal() {}
  union {
    HWMetric _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HWMetricDefaultTypeInternal _HWMetric_default_instance_;
constexpr SpatialMetricBlock::SpatialMetricBlock(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : start_(nullptr)
  , end_(nullptr)
  , weed_count_(nullptr)
  , we_data_(nullptr)
  , banding_data_(nullptr)
  , implement_width_data_(nullptr)
  , vel_data_(nullptr)
  , start_left_(nullptr)
  , start_right_(nullptr)
  , end_left_(nullptr)
  , end_right_(nullptr)
  , job_metric_(nullptr)
  , hw_metric_(nullptr)
  , suspicious_(false){}
struct SpatialMetricBlockDefaultTypeInternal {
  constexpr SpatialMetricBlockDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SpatialMetricBlockDefaultTypeInternal() {}
  union {
    SpatialMetricBlock _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SpatialMetricBlockDefaultTypeInternal _SpatialMetricBlock_default_instance_;
}  // namespace metrics
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[20];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2fmetrics_2fmetrics_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2fmetrics_2fmetrics_2eproto = nullptr;

const uint32_t TableStruct_proto_2fmetrics_2fmetrics_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserPosition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserPosition, row_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserPosition, slot_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserIdentifier, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserIdentifier, position_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserIdentifier, serial_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserLifeTime, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserLifeTime, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserLifeTime, lifetime_sec_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserEventTime, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserEventTime, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserEventTime, timestamp_sec_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserLifeTimes, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserLifeTimes, lifetimes_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserChangeTimes, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserChangeTimes, installs_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::LaserChangeTimes, removals_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::CountsByConclusionType, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::CountsByConclusionType, disarmed_weed_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::CountsByConclusionType, armed_weed_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::CountsByConclusionType, disarmed_crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::CountsByConclusionType, armed_crop_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::TargetSizeData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::TargetSizeData, cumulative_size_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::TargetSizeData, count_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::RequiredLaserTimeData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::RequiredLaserTimeData, cumulative_time_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::RequiredLaserTimeData, count_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialPosition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialPosition, latitude_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialPosition, longitude_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialPosition, height_mm_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialPosition, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialPosition, ecef_x_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialPosition, ecef_y_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialPosition, ecef_z_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk, conclusion_counts_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk, weed_size_data_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk, crop_size_data_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk, counts_by_category_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk, targeted_laser_time_data_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk, untargeted_laser_time_data_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WeedCounterChunk, valid_crop_count_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WheelEncoderSpatialData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WheelEncoderSpatialData, start_pos_m_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::WheelEncoderSpatialData, end_pos_m_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::BandingSpatialData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::BandingSpatialData, percent_banded_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::ImplementWidthSpatialData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::ImplementWidthSpatialData, width_mm_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::VelocitySpatialMetric, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::VelocitySpatialMetric, avg_target_vel_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::JobMetric, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::JobMetric, job_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::HWMetric, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::HWMetric, lifted_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::HWMetric, estopped_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::HWMetric, laser_key_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::HWMetric, interlock_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::HWMetric, water_protect_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::HWMetric, debug_mode_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, start_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, end_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, weed_count_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, we_data_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, banding_data_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, implement_width_data_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, vel_data_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, start_left_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, start_right_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, end_left_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, end_right_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, job_metric_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, suspicious_),
  PROTOBUF_FIELD_OFFSET(::carbon::metrics::SpatialMetricBlock, hw_metric_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::metrics::LaserPosition)},
  { 8, -1, -1, sizeof(::carbon::metrics::LaserIdentifier)},
  { 16, -1, -1, sizeof(::carbon::metrics::LaserLifeTime)},
  { 24, -1, -1, sizeof(::carbon::metrics::LaserEventTime)},
  { 32, -1, -1, sizeof(::carbon::metrics::LaserLifeTimes)},
  { 39, -1, -1, sizeof(::carbon::metrics::LaserChangeTimes)},
  { 47, -1, -1, sizeof(::carbon::metrics::CountsByConclusionType)},
  { 57, -1, -1, sizeof(::carbon::metrics::TargetSizeData)},
  { 65, -1, -1, sizeof(::carbon::metrics::RequiredLaserTimeData)},
  { 73, -1, -1, sizeof(::carbon::metrics::SpatialPosition)},
  { 86, 94, -1, sizeof(::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse)},
  { 96, -1, -1, sizeof(::carbon::metrics::WeedCounterChunk)},
  { 109, -1, -1, sizeof(::carbon::metrics::WheelEncoderSpatialData)},
  { 117, -1, -1, sizeof(::carbon::metrics::BandingSpatialData)},
  { 124, -1, -1, sizeof(::carbon::metrics::ImplementWidthSpatialData)},
  { 131, 139, -1, sizeof(::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse)},
  { 141, -1, -1, sizeof(::carbon::metrics::VelocitySpatialMetric)},
  { 148, -1, -1, sizeof(::carbon::metrics::JobMetric)},
  { 155, -1, -1, sizeof(::carbon::metrics::HWMetric)},
  { 167, -1, -1, sizeof(::carbon::metrics::SpatialMetricBlock)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_LaserPosition_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_LaserIdentifier_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_LaserLifeTime_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_LaserEventTime_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_LaserLifeTimes_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_LaserChangeTimes_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_CountsByConclusionType_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_TargetSizeData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_RequiredLaserTimeData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_SpatialPosition_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_WeedCounterChunk_CountsByCategoryEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_WeedCounterChunk_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_WheelEncoderSpatialData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_BandingSpatialData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_ImplementWidthSpatialData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_VelocitySpatialMetric_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_JobMetric_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_HWMetric_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::metrics::_SpatialMetricBlock_default_instance_),
};

const char descriptor_table_protodef_proto_2fmetrics_2fmetrics_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033proto/metrics/metrics.proto\022\016carbon.me"
  "trics\"*\n\rLaserPosition\022\013\n\003row\030\001 \001(\r\022\014\n\004s"
  "lot\030\002 \001(\r\"R\n\017LaserIdentifier\022/\n\010position"
  "\030\001 \001(\0132\035.carbon.metrics.LaserPosition\022\016\n"
  "\006serial\030\002 \001(\t\"R\n\rLaserLifeTime\022+\n\002id\030\001 \001"
  "(\0132\037.carbon.metrics.LaserIdentifier\022\024\n\014l"
  "ifetime_sec\030\002 \001(\004\"T\n\016LaserEventTime\022+\n\002i"
  "d\030\001 \001(\0132\037.carbon.metrics.LaserIdentifier"
  "\022\025\n\rtimestamp_sec\030\002 \001(\003\"B\n\016LaserLifeTime"
  "s\0220\n\tlifetimes\030\001 \003(\0132\035.carbon.metrics.La"
  "serLifeTime\"v\n\020LaserChangeTimes\0220\n\010insta"
  "lls\030\001 \003(\0132\036.carbon.metrics.LaserEventTim"
  "e\0220\n\010removals\030\002 \003(\0132\036.carbon.metrics.Las"
  "erEventTime\"n\n\026CountsByConclusionType\022\025\n"
  "\rdisarmed_weed\030\001 \003(\r\022\022\n\narmed_weed\030\002 \003(\r"
  "\022\025\n\rdisarmed_crop\030\003 \003(\r\022\022\n\narmed_crop\030\004 "
  "\003(\r\"8\n\016TargetSizeData\022\027\n\017cumulative_size"
  "\030\001 \001(\001\022\r\n\005count\030\002 \001(\004\"\?\n\025RequiredLaserTi"
  "meData\022\027\n\017cumulative_time\030\001 \001(\004\022\r\n\005count"
  "\030\002 \001(\004\"\217\001\n\017SpatialPosition\022\020\n\010latitude\030\001"
  " \001(\001\022\021\n\tlongitude\030\002 \001(\001\022\021\n\theight_mm\030\003 \001"
  "(\001\022\024\n\014timestamp_ms\030\004 \001(\004\022\016\n\006ecef_x\030\005 \001(\001"
  "\022\016\n\006ecef_y\030\006 \001(\001\022\016\n\006ecef_z\030\007 \001(\001\"\200\004\n\020Wee"
  "dCounterChunk\022A\n\021conclusion_counts\030\001 \001(\013"
  "2&.carbon.metrics.CountsByConclusionType"
  "\0226\n\016weed_size_data\030\002 \001(\0132\036.carbon.metric"
  "s.TargetSizeData\0226\n\016crop_size_data\030\003 \001(\013"
  "2\036.carbon.metrics.TargetSizeData\022R\n\022coun"
  "ts_by_category\030\004 \003(\01326.carbon.metrics.We"
  "edCounterChunk.CountsByCategoryEntry\022G\n\030"
  "targeted_laser_time_data\030\005 \001(\0132%.carbon."
  "metrics.RequiredLaserTimeData\022I\n\032untarge"
  "ted_laser_time_data\030\006 \001(\0132%.carbon.metri"
  "cs.RequiredLaserTimeData\022\030\n\020valid_crop_c"
  "ount\030\007 \001(\004\0327\n\025CountsByCategoryEntry\022\013\n\003k"
  "ey\030\001 \001(\t\022\r\n\005value\030\002 \001(\r:\0028\001\"A\n\027WheelEnco"
  "derSpatialData\022\023\n\013start_pos_m\030\001 \001(\002\022\021\n\te"
  "nd_pos_m\030\002 \001(\002\",\n\022BandingSpatialData\022\026\n\016"
  "percent_banded\030\001 \001(\002\"-\n\031ImplementWidthSp"
  "atialData\022\020\n\010width_mm\030\001 \001(\002\"\235\001\n\025Velocity"
  "SpatialMetric\022O\n\016avg_target_vel\030\001 \003(\01327."
  "carbon.metrics.VelocitySpatialMetric.Avg"
  "TargetVelEntry\0323\n\021AvgTargetVelEntry\022\013\n\003k"
  "ey\030\001 \001(\t\022\r\n\005value\030\002 \001(\002:\0028\001\"\033\n\tJobMetric"
  "\022\016\n\006job_id\030\001 \001(\t\"}\n\010HWMetric\022\016\n\006lifted\030\001"
  " \001(\010\022\020\n\010estopped\030\002 \001(\010\022\021\n\tlaser_key\030\003 \001("
  "\010\022\021\n\tinterlock\030\004 \001(\010\022\025\n\rwater_protect\030\005 "
  "\001(\010\022\022\n\ndebug_mode\030\006 \001(\010\"\340\005\n\022SpatialMetri"
  "cBlock\022.\n\005start\030\001 \001(\0132\037.carbon.metrics.S"
  "patialPosition\022,\n\003end\030\002 \001(\0132\037.carbon.met"
  "rics.SpatialPosition\0224\n\nweed_count\030\003 \001(\013"
  "2 .carbon.metrics.WeedCounterChunk\0228\n\007we"
  "_data\030\004 \001(\0132\'.carbon.metrics.WheelEncode"
  "rSpatialData\0228\n\014banding_data\030\005 \001(\0132\".car"
  "bon.metrics.BandingSpatialData\022G\n\024implem"
  "ent_width_data\030\006 \001(\0132).carbon.metrics.Im"
  "plementWidthSpatialData\0227\n\010vel_data\030\007 \001("
  "\0132%.carbon.metrics.VelocitySpatialMetric"
  "\0223\n\nstart_left\030\010 \001(\0132\037.carbon.metrics.Sp"
  "atialPosition\0224\n\013start_right\030\t \001(\0132\037.car"
  "bon.metrics.SpatialPosition\0221\n\010end_left\030"
  "\n \001(\0132\037.carbon.metrics.SpatialPosition\0222"
  "\n\tend_right\030\013 \001(\0132\037.carbon.metrics.Spati"
  "alPosition\022-\n\njob_metric\030\014 \001(\0132\031.carbon."
  "metrics.JobMetric\022\022\n\nsuspicious\030\r \001(\010\022+\n"
  "\thw_metric\030\016 \001(\0132\030.carbon.metrics.HWMetr"
  "icB\017Z\rproto/metricsb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fmetrics_2fmetrics_2eproto = {
  false, false, 2667, descriptor_table_protodef_proto_2fmetrics_2fmetrics_2eproto, "proto/metrics/metrics.proto", 
  &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once, nullptr, 0, 20,
  schemas, file_default_instances, TableStruct_proto_2fmetrics_2fmetrics_2eproto::offsets,
  file_level_metadata_proto_2fmetrics_2fmetrics_2eproto, file_level_enum_descriptors_proto_2fmetrics_2fmetrics_2eproto, file_level_service_descriptors_proto_2fmetrics_2fmetrics_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter() {
  return &descriptor_table_proto_2fmetrics_2fmetrics_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2fmetrics_2fmetrics_2eproto(&descriptor_table_proto_2fmetrics_2fmetrics_2eproto);
namespace carbon {
namespace metrics {

// ===================================================================

class LaserPosition::_Internal {
 public:
};

LaserPosition::LaserPosition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.LaserPosition)
}
LaserPosition::LaserPosition(const LaserPosition& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&row_, &from.row_,
    static_cast<size_t>(reinterpret_cast<char*>(&slot_) -
    reinterpret_cast<char*>(&row_)) + sizeof(slot_));
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.LaserPosition)
}

inline void LaserPosition::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&row_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&slot_) -
    reinterpret_cast<char*>(&row_)) + sizeof(slot_));
}

LaserPosition::~LaserPosition() {
  // @@protoc_insertion_point(destructor:carbon.metrics.LaserPosition)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserPosition::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LaserPosition::ArenaDtor(void* object) {
  LaserPosition* _this = reinterpret_cast< LaserPosition* >(object);
  (void)_this;
}
void LaserPosition::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserPosition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserPosition::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.LaserPosition)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&row_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&slot_) -
      reinterpret_cast<char*>(&row_)) + sizeof(slot_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserPosition::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 row = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 slot = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          slot_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserPosition::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.LaserPosition)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 row = 1;
  if (this->_internal_row() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_row(), target);
  }

  // uint32 slot = 2;
  if (this->_internal_slot() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_slot(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.LaserPosition)
  return target;
}

size_t LaserPosition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.LaserPosition)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 row = 1;
  if (this->_internal_row() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_row());
  }

  // uint32 slot = 2;
  if (this->_internal_slot() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_slot());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserPosition::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserPosition::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserPosition::GetClassData() const { return &_class_data_; }

void LaserPosition::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserPosition *>(to)->MergeFrom(
      static_cast<const LaserPosition &>(from));
}


void LaserPosition::MergeFrom(const LaserPosition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.LaserPosition)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_row() != 0) {
    _internal_set_row(from._internal_row());
  }
  if (from._internal_slot() != 0) {
    _internal_set_slot(from._internal_slot());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserPosition::CopyFrom(const LaserPosition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.LaserPosition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserPosition::IsInitialized() const {
  return true;
}

void LaserPosition::InternalSwap(LaserPosition* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaserPosition, slot_)
      + sizeof(LaserPosition::slot_)
      - PROTOBUF_FIELD_OFFSET(LaserPosition, row_)>(
          reinterpret_cast<char*>(&row_),
          reinterpret_cast<char*>(&other->row_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserPosition::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[0]);
}

// ===================================================================

class LaserIdentifier::_Internal {
 public:
  static const ::carbon::metrics::LaserPosition& position(const LaserIdentifier* msg);
};

const ::carbon::metrics::LaserPosition&
LaserIdentifier::_Internal::position(const LaserIdentifier* msg) {
  return *msg->position_;
}
LaserIdentifier::LaserIdentifier(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.LaserIdentifier)
}
LaserIdentifier::LaserIdentifier(const LaserIdentifier& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_serial().empty()) {
    serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serial(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_position()) {
    position_ = new ::carbon::metrics::LaserPosition(*from.position_);
  } else {
    position_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.LaserIdentifier)
}

inline void LaserIdentifier::SharedCtor() {
serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
position_ = nullptr;
}

LaserIdentifier::~LaserIdentifier() {
  // @@protoc_insertion_point(destructor:carbon.metrics.LaserIdentifier)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserIdentifier::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete position_;
}

void LaserIdentifier::ArenaDtor(void* object) {
  LaserIdentifier* _this = reinterpret_cast< LaserIdentifier* >(object);
  (void)_this;
}
void LaserIdentifier::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserIdentifier::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserIdentifier::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.LaserIdentifier)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  serial_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && position_ != nullptr) {
    delete position_;
  }
  position_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserIdentifier::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.metrics.LaserPosition position = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_position(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string serial = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.metrics.LaserIdentifier.serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserIdentifier::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.LaserIdentifier)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.metrics.LaserPosition position = 1;
  if (this->_internal_has_position()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::position(this), target, stream);
  }

  // string serial = 2;
  if (!this->_internal_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_serial().data(), static_cast<int>(this->_internal_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.metrics.LaserIdentifier.serial");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_serial(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.LaserIdentifier)
  return target;
}

size_t LaserIdentifier::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.LaserIdentifier)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string serial = 2;
  if (!this->_internal_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_serial());
  }

  // .carbon.metrics.LaserPosition position = 1;
  if (this->_internal_has_position()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *position_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserIdentifier::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserIdentifier::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserIdentifier::GetClassData() const { return &_class_data_; }

void LaserIdentifier::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserIdentifier *>(to)->MergeFrom(
      static_cast<const LaserIdentifier &>(from));
}


void LaserIdentifier::MergeFrom(const LaserIdentifier& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.LaserIdentifier)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_serial().empty()) {
    _internal_set_serial(from._internal_serial());
  }
  if (from._internal_has_position()) {
    _internal_mutable_position()->::carbon::metrics::LaserPosition::MergeFrom(from._internal_position());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserIdentifier::CopyFrom(const LaserIdentifier& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.LaserIdentifier)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserIdentifier::IsInitialized() const {
  return true;
}

void LaserIdentifier::InternalSwap(LaserIdentifier* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serial_, lhs_arena,
      &other->serial_, rhs_arena
  );
  swap(position_, other->position_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserIdentifier::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[1]);
}

// ===================================================================

class LaserLifeTime::_Internal {
 public:
  static const ::carbon::metrics::LaserIdentifier& id(const LaserLifeTime* msg);
};

const ::carbon::metrics::LaserIdentifier&
LaserLifeTime::_Internal::id(const LaserLifeTime* msg) {
  return *msg->id_;
}
LaserLifeTime::LaserLifeTime(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.LaserLifeTime)
}
LaserLifeTime::LaserLifeTime(const LaserLifeTime& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_id()) {
    id_ = new ::carbon::metrics::LaserIdentifier(*from.id_);
  } else {
    id_ = nullptr;
  }
  lifetime_sec_ = from.lifetime_sec_;
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.LaserLifeTime)
}

inline void LaserLifeTime::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&lifetime_sec_) -
    reinterpret_cast<char*>(&id_)) + sizeof(lifetime_sec_));
}

LaserLifeTime::~LaserLifeTime() {
  // @@protoc_insertion_point(destructor:carbon.metrics.LaserLifeTime)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserLifeTime::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete id_;
}

void LaserLifeTime::ArenaDtor(void* object) {
  LaserLifeTime* _this = reinterpret_cast< LaserLifeTime* >(object);
  (void)_this;
}
void LaserLifeTime::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserLifeTime::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserLifeTime::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.LaserLifeTime)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
  lifetime_sec_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserLifeTime::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.metrics.LaserIdentifier id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_id(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 lifetime_sec = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          lifetime_sec_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserLifeTime::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.LaserLifeTime)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.metrics.LaserIdentifier id = 1;
  if (this->_internal_has_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::id(this), target, stream);
  }

  // uint64 lifetime_sec = 2;
  if (this->_internal_lifetime_sec() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_lifetime_sec(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.LaserLifeTime)
  return target;
}

size_t LaserLifeTime::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.LaserLifeTime)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.metrics.LaserIdentifier id = 1;
  if (this->_internal_has_id()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *id_);
  }

  // uint64 lifetime_sec = 2;
  if (this->_internal_lifetime_sec() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_lifetime_sec());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserLifeTime::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserLifeTime::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserLifeTime::GetClassData() const { return &_class_data_; }

void LaserLifeTime::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserLifeTime *>(to)->MergeFrom(
      static_cast<const LaserLifeTime &>(from));
}


void LaserLifeTime::MergeFrom(const LaserLifeTime& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.LaserLifeTime)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_id()) {
    _internal_mutable_id()->::carbon::metrics::LaserIdentifier::MergeFrom(from._internal_id());
  }
  if (from._internal_lifetime_sec() != 0) {
    _internal_set_lifetime_sec(from._internal_lifetime_sec());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserLifeTime::CopyFrom(const LaserLifeTime& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.LaserLifeTime)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserLifeTime::IsInitialized() const {
  return true;
}

void LaserLifeTime::InternalSwap(LaserLifeTime* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaserLifeTime, lifetime_sec_)
      + sizeof(LaserLifeTime::lifetime_sec_)
      - PROTOBUF_FIELD_OFFSET(LaserLifeTime, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserLifeTime::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[2]);
}

// ===================================================================

class LaserEventTime::_Internal {
 public:
  static const ::carbon::metrics::LaserIdentifier& id(const LaserEventTime* msg);
};

const ::carbon::metrics::LaserIdentifier&
LaserEventTime::_Internal::id(const LaserEventTime* msg) {
  return *msg->id_;
}
LaserEventTime::LaserEventTime(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.LaserEventTime)
}
LaserEventTime::LaserEventTime(const LaserEventTime& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_id()) {
    id_ = new ::carbon::metrics::LaserIdentifier(*from.id_);
  } else {
    id_ = nullptr;
  }
  timestamp_sec_ = from.timestamp_sec_;
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.LaserEventTime)
}

inline void LaserEventTime::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&timestamp_sec_) -
    reinterpret_cast<char*>(&id_)) + sizeof(timestamp_sec_));
}

LaserEventTime::~LaserEventTime() {
  // @@protoc_insertion_point(destructor:carbon.metrics.LaserEventTime)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserEventTime::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete id_;
}

void LaserEventTime::ArenaDtor(void* object) {
  LaserEventTime* _this = reinterpret_cast< LaserEventTime* >(object);
  (void)_this;
}
void LaserEventTime::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserEventTime::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserEventTime::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.LaserEventTime)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && id_ != nullptr) {
    delete id_;
  }
  id_ = nullptr;
  timestamp_sec_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserEventTime::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.metrics.LaserIdentifier id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_id(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 timestamp_sec = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          timestamp_sec_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserEventTime::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.LaserEventTime)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.metrics.LaserIdentifier id = 1;
  if (this->_internal_has_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::id(this), target, stream);
  }

  // int64 timestamp_sec = 2;
  if (this->_internal_timestamp_sec() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_timestamp_sec(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.LaserEventTime)
  return target;
}

size_t LaserEventTime::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.LaserEventTime)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.metrics.LaserIdentifier id = 1;
  if (this->_internal_has_id()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *id_);
  }

  // int64 timestamp_sec = 2;
  if (this->_internal_timestamp_sec() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_sec());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserEventTime::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserEventTime::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserEventTime::GetClassData() const { return &_class_data_; }

void LaserEventTime::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserEventTime *>(to)->MergeFrom(
      static_cast<const LaserEventTime &>(from));
}


void LaserEventTime::MergeFrom(const LaserEventTime& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.LaserEventTime)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_id()) {
    _internal_mutable_id()->::carbon::metrics::LaserIdentifier::MergeFrom(from._internal_id());
  }
  if (from._internal_timestamp_sec() != 0) {
    _internal_set_timestamp_sec(from._internal_timestamp_sec());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserEventTime::CopyFrom(const LaserEventTime& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.LaserEventTime)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserEventTime::IsInitialized() const {
  return true;
}

void LaserEventTime::InternalSwap(LaserEventTime* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaserEventTime, timestamp_sec_)
      + sizeof(LaserEventTime::timestamp_sec_)
      - PROTOBUF_FIELD_OFFSET(LaserEventTime, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserEventTime::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[3]);
}

// ===================================================================

class LaserLifeTimes::_Internal {
 public:
};

LaserLifeTimes::LaserLifeTimes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  lifetimes_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.LaserLifeTimes)
}
LaserLifeTimes::LaserLifeTimes(const LaserLifeTimes& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      lifetimes_(from.lifetimes_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.LaserLifeTimes)
}

inline void LaserLifeTimes::SharedCtor() {
}

LaserLifeTimes::~LaserLifeTimes() {
  // @@protoc_insertion_point(destructor:carbon.metrics.LaserLifeTimes)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserLifeTimes::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LaserLifeTimes::ArenaDtor(void* object) {
  LaserLifeTimes* _this = reinterpret_cast< LaserLifeTimes* >(object);
  (void)_this;
}
void LaserLifeTimes::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserLifeTimes::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserLifeTimes::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.LaserLifeTimes)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lifetimes_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserLifeTimes::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.metrics.LaserLifeTime lifetimes = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lifetimes(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserLifeTimes::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.LaserLifeTimes)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.metrics.LaserLifeTime lifetimes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_lifetimes_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_lifetimes(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.LaserLifeTimes)
  return target;
}

size_t LaserLifeTimes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.LaserLifeTimes)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.metrics.LaserLifeTime lifetimes = 1;
  total_size += 1UL * this->_internal_lifetimes_size();
  for (const auto& msg : this->lifetimes_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserLifeTimes::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserLifeTimes::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserLifeTimes::GetClassData() const { return &_class_data_; }

void LaserLifeTimes::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserLifeTimes *>(to)->MergeFrom(
      static_cast<const LaserLifeTimes &>(from));
}


void LaserLifeTimes::MergeFrom(const LaserLifeTimes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.LaserLifeTimes)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  lifetimes_.MergeFrom(from.lifetimes_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserLifeTimes::CopyFrom(const LaserLifeTimes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.LaserLifeTimes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserLifeTimes::IsInitialized() const {
  return true;
}

void LaserLifeTimes::InternalSwap(LaserLifeTimes* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  lifetimes_.InternalSwap(&other->lifetimes_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserLifeTimes::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[4]);
}

// ===================================================================

class LaserChangeTimes::_Internal {
 public:
};

LaserChangeTimes::LaserChangeTimes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  installs_(arena),
  removals_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.LaserChangeTimes)
}
LaserChangeTimes::LaserChangeTimes(const LaserChangeTimes& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      installs_(from.installs_),
      removals_(from.removals_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.LaserChangeTimes)
}

inline void LaserChangeTimes::SharedCtor() {
}

LaserChangeTimes::~LaserChangeTimes() {
  // @@protoc_insertion_point(destructor:carbon.metrics.LaserChangeTimes)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserChangeTimes::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LaserChangeTimes::ArenaDtor(void* object) {
  LaserChangeTimes* _this = reinterpret_cast< LaserChangeTimes* >(object);
  (void)_this;
}
void LaserChangeTimes::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserChangeTimes::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserChangeTimes::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.LaserChangeTimes)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  installs_.Clear();
  removals_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserChangeTimes::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.metrics.LaserEventTime installs = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_installs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.metrics.LaserEventTime removals = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_removals(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserChangeTimes::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.LaserChangeTimes)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.metrics.LaserEventTime installs = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_installs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_installs(i), target, stream);
  }

  // repeated .carbon.metrics.LaserEventTime removals = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_removals_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_removals(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.LaserChangeTimes)
  return target;
}

size_t LaserChangeTimes::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.LaserChangeTimes)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.metrics.LaserEventTime installs = 1;
  total_size += 1UL * this->_internal_installs_size();
  for (const auto& msg : this->installs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .carbon.metrics.LaserEventTime removals = 2;
  total_size += 1UL * this->_internal_removals_size();
  for (const auto& msg : this->removals_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserChangeTimes::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserChangeTimes::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserChangeTimes::GetClassData() const { return &_class_data_; }

void LaserChangeTimes::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserChangeTimes *>(to)->MergeFrom(
      static_cast<const LaserChangeTimes &>(from));
}


void LaserChangeTimes::MergeFrom(const LaserChangeTimes& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.LaserChangeTimes)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  installs_.MergeFrom(from.installs_);
  removals_.MergeFrom(from.removals_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserChangeTimes::CopyFrom(const LaserChangeTimes& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.LaserChangeTimes)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserChangeTimes::IsInitialized() const {
  return true;
}

void LaserChangeTimes::InternalSwap(LaserChangeTimes* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  installs_.InternalSwap(&other->installs_);
  removals_.InternalSwap(&other->removals_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserChangeTimes::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[5]);
}

// ===================================================================

class CountsByConclusionType::_Internal {
 public:
};

CountsByConclusionType::CountsByConclusionType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  disarmed_weed_(arena),
  armed_weed_(arena),
  disarmed_crop_(arena),
  armed_crop_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.CountsByConclusionType)
}
CountsByConclusionType::CountsByConclusionType(const CountsByConclusionType& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      disarmed_weed_(from.disarmed_weed_),
      armed_weed_(from.armed_weed_),
      disarmed_crop_(from.disarmed_crop_),
      armed_crop_(from.armed_crop_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.CountsByConclusionType)
}

inline void CountsByConclusionType::SharedCtor() {
}

CountsByConclusionType::~CountsByConclusionType() {
  // @@protoc_insertion_point(destructor:carbon.metrics.CountsByConclusionType)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CountsByConclusionType::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CountsByConclusionType::ArenaDtor(void* object) {
  CountsByConclusionType* _this = reinterpret_cast< CountsByConclusionType* >(object);
  (void)_this;
}
void CountsByConclusionType::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CountsByConclusionType::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CountsByConclusionType::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.CountsByConclusionType)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  disarmed_weed_.Clear();
  armed_weed_.Clear();
  disarmed_crop_.Clear();
  armed_crop_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CountsByConclusionType::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated uint32 disarmed_weed = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_disarmed_weed(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 8) {
          _internal_add_disarmed_weed(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated uint32 armed_weed = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_armed_weed(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 16) {
          _internal_add_armed_weed(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated uint32 disarmed_crop = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_disarmed_crop(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 24) {
          _internal_add_disarmed_crop(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated uint32 armed_crop = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_armed_crop(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 32) {
          _internal_add_armed_crop(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CountsByConclusionType::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.CountsByConclusionType)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated uint32 disarmed_weed = 1;
  {
    int byte_size = _disarmed_weed_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt32Packed(
          1, _internal_disarmed_weed(), byte_size, target);
    }
  }

  // repeated uint32 armed_weed = 2;
  {
    int byte_size = _armed_weed_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt32Packed(
          2, _internal_armed_weed(), byte_size, target);
    }
  }

  // repeated uint32 disarmed_crop = 3;
  {
    int byte_size = _disarmed_crop_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt32Packed(
          3, _internal_disarmed_crop(), byte_size, target);
    }
  }

  // repeated uint32 armed_crop = 4;
  {
    int byte_size = _armed_crop_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt32Packed(
          4, _internal_armed_crop(), byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.CountsByConclusionType)
  return target;
}

size_t CountsByConclusionType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.CountsByConclusionType)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated uint32 disarmed_weed = 1;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt32Size(this->disarmed_weed_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _disarmed_weed_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated uint32 armed_weed = 2;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt32Size(this->armed_weed_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _armed_weed_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated uint32 disarmed_crop = 3;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt32Size(this->disarmed_crop_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _disarmed_crop_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated uint32 armed_crop = 4;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt32Size(this->armed_crop_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _armed_crop_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CountsByConclusionType::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CountsByConclusionType::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CountsByConclusionType::GetClassData() const { return &_class_data_; }

void CountsByConclusionType::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CountsByConclusionType *>(to)->MergeFrom(
      static_cast<const CountsByConclusionType &>(from));
}


void CountsByConclusionType::MergeFrom(const CountsByConclusionType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.CountsByConclusionType)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  disarmed_weed_.MergeFrom(from.disarmed_weed_);
  armed_weed_.MergeFrom(from.armed_weed_);
  disarmed_crop_.MergeFrom(from.disarmed_crop_);
  armed_crop_.MergeFrom(from.armed_crop_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CountsByConclusionType::CopyFrom(const CountsByConclusionType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.CountsByConclusionType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CountsByConclusionType::IsInitialized() const {
  return true;
}

void CountsByConclusionType::InternalSwap(CountsByConclusionType* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  disarmed_weed_.InternalSwap(&other->disarmed_weed_);
  armed_weed_.InternalSwap(&other->armed_weed_);
  disarmed_crop_.InternalSwap(&other->disarmed_crop_);
  armed_crop_.InternalSwap(&other->armed_crop_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CountsByConclusionType::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[6]);
}

// ===================================================================

class TargetSizeData::_Internal {
 public:
};

TargetSizeData::TargetSizeData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.TargetSizeData)
}
TargetSizeData::TargetSizeData(const TargetSizeData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&cumulative_size_, &from.cumulative_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&cumulative_size_)) + sizeof(count_));
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.TargetSizeData)
}

inline void TargetSizeData::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&cumulative_size_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&cumulative_size_)) + sizeof(count_));
}

TargetSizeData::~TargetSizeData() {
  // @@protoc_insertion_point(destructor:carbon.metrics.TargetSizeData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TargetSizeData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TargetSizeData::ArenaDtor(void* object) {
  TargetSizeData* _this = reinterpret_cast< TargetSizeData* >(object);
  (void)_this;
}
void TargetSizeData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TargetSizeData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TargetSizeData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.TargetSizeData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&cumulative_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&count_) -
      reinterpret_cast<char*>(&cumulative_size_)) + sizeof(count_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TargetSizeData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double cumulative_size = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          cumulative_size_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // uint64 count = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TargetSizeData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.TargetSizeData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double cumulative_size = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cumulative_size = this->_internal_cumulative_size();
  uint64_t raw_cumulative_size;
  memcpy(&raw_cumulative_size, &tmp_cumulative_size, sizeof(tmp_cumulative_size));
  if (raw_cumulative_size != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_cumulative_size(), target);
  }

  // uint64 count = 2;
  if (this->_internal_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_count(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.TargetSizeData)
  return target;
}

size_t TargetSizeData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.TargetSizeData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double cumulative_size = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cumulative_size = this->_internal_cumulative_size();
  uint64_t raw_cumulative_size;
  memcpy(&raw_cumulative_size, &tmp_cumulative_size, sizeof(tmp_cumulative_size));
  if (raw_cumulative_size != 0) {
    total_size += 1 + 8;
  }

  // uint64 count = 2;
  if (this->_internal_count() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_count());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TargetSizeData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TargetSizeData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TargetSizeData::GetClassData() const { return &_class_data_; }

void TargetSizeData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TargetSizeData *>(to)->MergeFrom(
      static_cast<const TargetSizeData &>(from));
}


void TargetSizeData::MergeFrom(const TargetSizeData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.TargetSizeData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cumulative_size = from._internal_cumulative_size();
  uint64_t raw_cumulative_size;
  memcpy(&raw_cumulative_size, &tmp_cumulative_size, sizeof(tmp_cumulative_size));
  if (raw_cumulative_size != 0) {
    _internal_set_cumulative_size(from._internal_cumulative_size());
  }
  if (from._internal_count() != 0) {
    _internal_set_count(from._internal_count());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TargetSizeData::CopyFrom(const TargetSizeData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.TargetSizeData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TargetSizeData::IsInitialized() const {
  return true;
}

void TargetSizeData::InternalSwap(TargetSizeData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TargetSizeData, count_)
      + sizeof(TargetSizeData::count_)
      - PROTOBUF_FIELD_OFFSET(TargetSizeData, cumulative_size_)>(
          reinterpret_cast<char*>(&cumulative_size_),
          reinterpret_cast<char*>(&other->cumulative_size_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TargetSizeData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[7]);
}

// ===================================================================

class RequiredLaserTimeData::_Internal {
 public:
};

RequiredLaserTimeData::RequiredLaserTimeData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.RequiredLaserTimeData)
}
RequiredLaserTimeData::RequiredLaserTimeData(const RequiredLaserTimeData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&cumulative_time_, &from.cumulative_time_,
    static_cast<size_t>(reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&cumulative_time_)) + sizeof(count_));
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.RequiredLaserTimeData)
}

inline void RequiredLaserTimeData::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&cumulative_time_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&cumulative_time_)) + sizeof(count_));
}

RequiredLaserTimeData::~RequiredLaserTimeData() {
  // @@protoc_insertion_point(destructor:carbon.metrics.RequiredLaserTimeData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RequiredLaserTimeData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RequiredLaserTimeData::ArenaDtor(void* object) {
  RequiredLaserTimeData* _this = reinterpret_cast< RequiredLaserTimeData* >(object);
  (void)_this;
}
void RequiredLaserTimeData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RequiredLaserTimeData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RequiredLaserTimeData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.RequiredLaserTimeData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&cumulative_time_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&count_) -
      reinterpret_cast<char*>(&cumulative_time_)) + sizeof(count_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RequiredLaserTimeData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 cumulative_time = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          cumulative_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 count = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RequiredLaserTimeData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.RequiredLaserTimeData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 cumulative_time = 1;
  if (this->_internal_cumulative_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_cumulative_time(), target);
  }

  // uint64 count = 2;
  if (this->_internal_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_count(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.RequiredLaserTimeData)
  return target;
}

size_t RequiredLaserTimeData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.RequiredLaserTimeData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 cumulative_time = 1;
  if (this->_internal_cumulative_time() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_cumulative_time());
  }

  // uint64 count = 2;
  if (this->_internal_count() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_count());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RequiredLaserTimeData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RequiredLaserTimeData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RequiredLaserTimeData::GetClassData() const { return &_class_data_; }

void RequiredLaserTimeData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RequiredLaserTimeData *>(to)->MergeFrom(
      static_cast<const RequiredLaserTimeData &>(from));
}


void RequiredLaserTimeData::MergeFrom(const RequiredLaserTimeData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.RequiredLaserTimeData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_cumulative_time() != 0) {
    _internal_set_cumulative_time(from._internal_cumulative_time());
  }
  if (from._internal_count() != 0) {
    _internal_set_count(from._internal_count());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RequiredLaserTimeData::CopyFrom(const RequiredLaserTimeData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.RequiredLaserTimeData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RequiredLaserTimeData::IsInitialized() const {
  return true;
}

void RequiredLaserTimeData::InternalSwap(RequiredLaserTimeData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RequiredLaserTimeData, count_)
      + sizeof(RequiredLaserTimeData::count_)
      - PROTOBUF_FIELD_OFFSET(RequiredLaserTimeData, cumulative_time_)>(
          reinterpret_cast<char*>(&cumulative_time_),
          reinterpret_cast<char*>(&other->cumulative_time_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RequiredLaserTimeData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[8]);
}

// ===================================================================

class SpatialPosition::_Internal {
 public:
};

SpatialPosition::SpatialPosition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.SpatialPosition)
}
SpatialPosition::SpatialPosition(const SpatialPosition& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&latitude_, &from.latitude_,
    static_cast<size_t>(reinterpret_cast<char*>(&ecef_z_) -
    reinterpret_cast<char*>(&latitude_)) + sizeof(ecef_z_));
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.SpatialPosition)
}

inline void SpatialPosition::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&latitude_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&ecef_z_) -
    reinterpret_cast<char*>(&latitude_)) + sizeof(ecef_z_));
}

SpatialPosition::~SpatialPosition() {
  // @@protoc_insertion_point(destructor:carbon.metrics.SpatialPosition)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SpatialPosition::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SpatialPosition::ArenaDtor(void* object) {
  SpatialPosition* _this = reinterpret_cast< SpatialPosition* >(object);
  (void)_this;
}
void SpatialPosition::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SpatialPosition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SpatialPosition::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.SpatialPosition)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&latitude_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ecef_z_) -
      reinterpret_cast<char*>(&latitude_)) + sizeof(ecef_z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SpatialPosition::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double latitude = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          latitude_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double longitude = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          longitude_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double height_mm = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          height_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // uint64 timestamp_ms = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double ecef_x = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 41)) {
          ecef_x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double ecef_y = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 49)) {
          ecef_y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double ecef_z = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 57)) {
          ecef_z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SpatialPosition::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.SpatialPosition)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double latitude = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_latitude = this->_internal_latitude();
  uint64_t raw_latitude;
  memcpy(&raw_latitude, &tmp_latitude, sizeof(tmp_latitude));
  if (raw_latitude != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_latitude(), target);
  }

  // double longitude = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_longitude = this->_internal_longitude();
  uint64_t raw_longitude;
  memcpy(&raw_longitude, &tmp_longitude, sizeof(tmp_longitude));
  if (raw_longitude != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_longitude(), target);
  }

  // double height_mm = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_height_mm = this->_internal_height_mm();
  uint64_t raw_height_mm;
  memcpy(&raw_height_mm, &tmp_height_mm, sizeof(tmp_height_mm));
  if (raw_height_mm != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_height_mm(), target);
  }

  // uint64 timestamp_ms = 4;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_timestamp_ms(), target);
  }

  // double ecef_x = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ecef_x = this->_internal_ecef_x();
  uint64_t raw_ecef_x;
  memcpy(&raw_ecef_x, &tmp_ecef_x, sizeof(tmp_ecef_x));
  if (raw_ecef_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(5, this->_internal_ecef_x(), target);
  }

  // double ecef_y = 6;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ecef_y = this->_internal_ecef_y();
  uint64_t raw_ecef_y;
  memcpy(&raw_ecef_y, &tmp_ecef_y, sizeof(tmp_ecef_y));
  if (raw_ecef_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(6, this->_internal_ecef_y(), target);
  }

  // double ecef_z = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ecef_z = this->_internal_ecef_z();
  uint64_t raw_ecef_z;
  memcpy(&raw_ecef_z, &tmp_ecef_z, sizeof(tmp_ecef_z));
  if (raw_ecef_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(7, this->_internal_ecef_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.SpatialPosition)
  return target;
}

size_t SpatialPosition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.SpatialPosition)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double latitude = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_latitude = this->_internal_latitude();
  uint64_t raw_latitude;
  memcpy(&raw_latitude, &tmp_latitude, sizeof(tmp_latitude));
  if (raw_latitude != 0) {
    total_size += 1 + 8;
  }

  // double longitude = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_longitude = this->_internal_longitude();
  uint64_t raw_longitude;
  memcpy(&raw_longitude, &tmp_longitude, sizeof(tmp_longitude));
  if (raw_longitude != 0) {
    total_size += 1 + 8;
  }

  // double height_mm = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_height_mm = this->_internal_height_mm();
  uint64_t raw_height_mm;
  memcpy(&raw_height_mm, &tmp_height_mm, sizeof(tmp_height_mm));
  if (raw_height_mm != 0) {
    total_size += 1 + 8;
  }

  // uint64 timestamp_ms = 4;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_timestamp_ms());
  }

  // double ecef_x = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ecef_x = this->_internal_ecef_x();
  uint64_t raw_ecef_x;
  memcpy(&raw_ecef_x, &tmp_ecef_x, sizeof(tmp_ecef_x));
  if (raw_ecef_x != 0) {
    total_size += 1 + 8;
  }

  // double ecef_y = 6;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ecef_y = this->_internal_ecef_y();
  uint64_t raw_ecef_y;
  memcpy(&raw_ecef_y, &tmp_ecef_y, sizeof(tmp_ecef_y));
  if (raw_ecef_y != 0) {
    total_size += 1 + 8;
  }

  // double ecef_z = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ecef_z = this->_internal_ecef_z();
  uint64_t raw_ecef_z;
  memcpy(&raw_ecef_z, &tmp_ecef_z, sizeof(tmp_ecef_z));
  if (raw_ecef_z != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SpatialPosition::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SpatialPosition::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SpatialPosition::GetClassData() const { return &_class_data_; }

void SpatialPosition::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SpatialPosition *>(to)->MergeFrom(
      static_cast<const SpatialPosition &>(from));
}


void SpatialPosition::MergeFrom(const SpatialPosition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.SpatialPosition)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_latitude = from._internal_latitude();
  uint64_t raw_latitude;
  memcpy(&raw_latitude, &tmp_latitude, sizeof(tmp_latitude));
  if (raw_latitude != 0) {
    _internal_set_latitude(from._internal_latitude());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_longitude = from._internal_longitude();
  uint64_t raw_longitude;
  memcpy(&raw_longitude, &tmp_longitude, sizeof(tmp_longitude));
  if (raw_longitude != 0) {
    _internal_set_longitude(from._internal_longitude());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_height_mm = from._internal_height_mm();
  uint64_t raw_height_mm;
  memcpy(&raw_height_mm, &tmp_height_mm, sizeof(tmp_height_mm));
  if (raw_height_mm != 0) {
    _internal_set_height_mm(from._internal_height_mm());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ecef_x = from._internal_ecef_x();
  uint64_t raw_ecef_x;
  memcpy(&raw_ecef_x, &tmp_ecef_x, sizeof(tmp_ecef_x));
  if (raw_ecef_x != 0) {
    _internal_set_ecef_x(from._internal_ecef_x());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ecef_y = from._internal_ecef_y();
  uint64_t raw_ecef_y;
  memcpy(&raw_ecef_y, &tmp_ecef_y, sizeof(tmp_ecef_y));
  if (raw_ecef_y != 0) {
    _internal_set_ecef_y(from._internal_ecef_y());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_ecef_z = from._internal_ecef_z();
  uint64_t raw_ecef_z;
  memcpy(&raw_ecef_z, &tmp_ecef_z, sizeof(tmp_ecef_z));
  if (raw_ecef_z != 0) {
    _internal_set_ecef_z(from._internal_ecef_z());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SpatialPosition::CopyFrom(const SpatialPosition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.SpatialPosition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpatialPosition::IsInitialized() const {
  return true;
}

void SpatialPosition::InternalSwap(SpatialPosition* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SpatialPosition, ecef_z_)
      + sizeof(SpatialPosition::ecef_z_)
      - PROTOBUF_FIELD_OFFSET(SpatialPosition, latitude_)>(
          reinterpret_cast<char*>(&latitude_),
          reinterpret_cast<char*>(&other->latitude_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SpatialPosition::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[9]);
}

// ===================================================================

WeedCounterChunk_CountsByCategoryEntry_DoNotUse::WeedCounterChunk_CountsByCategoryEntry_DoNotUse() {}
WeedCounterChunk_CountsByCategoryEntry_DoNotUse::WeedCounterChunk_CountsByCategoryEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void WeedCounterChunk_CountsByCategoryEntry_DoNotUse::MergeFrom(const WeedCounterChunk_CountsByCategoryEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata WeedCounterChunk_CountsByCategoryEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[10]);
}

// ===================================================================

class WeedCounterChunk::_Internal {
 public:
  static const ::carbon::metrics::CountsByConclusionType& conclusion_counts(const WeedCounterChunk* msg);
  static const ::carbon::metrics::TargetSizeData& weed_size_data(const WeedCounterChunk* msg);
  static const ::carbon::metrics::TargetSizeData& crop_size_data(const WeedCounterChunk* msg);
  static const ::carbon::metrics::RequiredLaserTimeData& targeted_laser_time_data(const WeedCounterChunk* msg);
  static const ::carbon::metrics::RequiredLaserTimeData& untargeted_laser_time_data(const WeedCounterChunk* msg);
};

const ::carbon::metrics::CountsByConclusionType&
WeedCounterChunk::_Internal::conclusion_counts(const WeedCounterChunk* msg) {
  return *msg->conclusion_counts_;
}
const ::carbon::metrics::TargetSizeData&
WeedCounterChunk::_Internal::weed_size_data(const WeedCounterChunk* msg) {
  return *msg->weed_size_data_;
}
const ::carbon::metrics::TargetSizeData&
WeedCounterChunk::_Internal::crop_size_data(const WeedCounterChunk* msg) {
  return *msg->crop_size_data_;
}
const ::carbon::metrics::RequiredLaserTimeData&
WeedCounterChunk::_Internal::targeted_laser_time_data(const WeedCounterChunk* msg) {
  return *msg->targeted_laser_time_data_;
}
const ::carbon::metrics::RequiredLaserTimeData&
WeedCounterChunk::_Internal::untargeted_laser_time_data(const WeedCounterChunk* msg) {
  return *msg->untargeted_laser_time_data_;
}
WeedCounterChunk::WeedCounterChunk(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  counts_by_category_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.WeedCounterChunk)
}
WeedCounterChunk::WeedCounterChunk(const WeedCounterChunk& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  counts_by_category_.MergeFrom(from.counts_by_category_);
  if (from._internal_has_conclusion_counts()) {
    conclusion_counts_ = new ::carbon::metrics::CountsByConclusionType(*from.conclusion_counts_);
  } else {
    conclusion_counts_ = nullptr;
  }
  if (from._internal_has_weed_size_data()) {
    weed_size_data_ = new ::carbon::metrics::TargetSizeData(*from.weed_size_data_);
  } else {
    weed_size_data_ = nullptr;
  }
  if (from._internal_has_crop_size_data()) {
    crop_size_data_ = new ::carbon::metrics::TargetSizeData(*from.crop_size_data_);
  } else {
    crop_size_data_ = nullptr;
  }
  if (from._internal_has_targeted_laser_time_data()) {
    targeted_laser_time_data_ = new ::carbon::metrics::RequiredLaserTimeData(*from.targeted_laser_time_data_);
  } else {
    targeted_laser_time_data_ = nullptr;
  }
  if (from._internal_has_untargeted_laser_time_data()) {
    untargeted_laser_time_data_ = new ::carbon::metrics::RequiredLaserTimeData(*from.untargeted_laser_time_data_);
  } else {
    untargeted_laser_time_data_ = nullptr;
  }
  valid_crop_count_ = from.valid_crop_count_;
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.WeedCounterChunk)
}

inline void WeedCounterChunk::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&conclusion_counts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&valid_crop_count_) -
    reinterpret_cast<char*>(&conclusion_counts_)) + sizeof(valid_crop_count_));
}

WeedCounterChunk::~WeedCounterChunk() {
  // @@protoc_insertion_point(destructor:carbon.metrics.WeedCounterChunk)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void WeedCounterChunk::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete conclusion_counts_;
  if (this != internal_default_instance()) delete weed_size_data_;
  if (this != internal_default_instance()) delete crop_size_data_;
  if (this != internal_default_instance()) delete targeted_laser_time_data_;
  if (this != internal_default_instance()) delete untargeted_laser_time_data_;
}

void WeedCounterChunk::ArenaDtor(void* object) {
  WeedCounterChunk* _this = reinterpret_cast< WeedCounterChunk* >(object);
  (void)_this;
  _this->counts_by_category_. ~MapField();
}
inline void WeedCounterChunk::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &WeedCounterChunk::ArenaDtor);
  }
}
void WeedCounterChunk::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void WeedCounterChunk::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.WeedCounterChunk)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  counts_by_category_.Clear();
  if (GetArenaForAllocation() == nullptr && conclusion_counts_ != nullptr) {
    delete conclusion_counts_;
  }
  conclusion_counts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && weed_size_data_ != nullptr) {
    delete weed_size_data_;
  }
  weed_size_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && crop_size_data_ != nullptr) {
    delete crop_size_data_;
  }
  crop_size_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && targeted_laser_time_data_ != nullptr) {
    delete targeted_laser_time_data_;
  }
  targeted_laser_time_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && untargeted_laser_time_data_ != nullptr) {
    delete untargeted_laser_time_data_;
  }
  untargeted_laser_time_data_ = nullptr;
  valid_crop_count_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WeedCounterChunk::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.metrics.CountsByConclusionType conclusion_counts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_conclusion_counts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.TargetSizeData weed_size_data = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_weed_size_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.TargetSizeData crop_size_data = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_crop_size_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, uint32> counts_by_category = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&counts_by_category_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.RequiredLaserTimeData targeted_laser_time_data = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_targeted_laser_time_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.RequiredLaserTimeData untargeted_laser_time_data = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_untargeted_laser_time_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 valid_crop_count = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          valid_crop_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WeedCounterChunk::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.WeedCounterChunk)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.metrics.CountsByConclusionType conclusion_counts = 1;
  if (this->_internal_has_conclusion_counts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::conclusion_counts(this), target, stream);
  }

  // .carbon.metrics.TargetSizeData weed_size_data = 2;
  if (this->_internal_has_weed_size_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::weed_size_data(this), target, stream);
  }

  // .carbon.metrics.TargetSizeData crop_size_data = 3;
  if (this->_internal_has_crop_size_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::crop_size_data(this), target, stream);
  }

  // map<string, uint32> counts_by_category = 4;
  if (!this->_internal_counts_by_category().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.metrics.WeedCounterChunk.CountsByCategoryEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_counts_by_category().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_counts_by_category().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >::const_iterator
          it = this->_internal_counts_by_category().begin();
          it != this->_internal_counts_by_category().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = WeedCounterChunk_CountsByCategoryEntry_DoNotUse::Funcs::InternalSerialize(4, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >::const_iterator
          it = this->_internal_counts_by_category().begin();
          it != this->_internal_counts_by_category().end(); ++it) {
        target = WeedCounterChunk_CountsByCategoryEntry_DoNotUse::Funcs::InternalSerialize(4, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // .carbon.metrics.RequiredLaserTimeData targeted_laser_time_data = 5;
  if (this->_internal_has_targeted_laser_time_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::targeted_laser_time_data(this), target, stream);
  }

  // .carbon.metrics.RequiredLaserTimeData untargeted_laser_time_data = 6;
  if (this->_internal_has_untargeted_laser_time_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::untargeted_laser_time_data(this), target, stream);
  }

  // uint64 valid_crop_count = 7;
  if (this->_internal_valid_crop_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(7, this->_internal_valid_crop_count(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.WeedCounterChunk)
  return target;
}

size_t WeedCounterChunk::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.WeedCounterChunk)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, uint32> counts_by_category = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_counts_by_category_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, uint32_t >::const_iterator
      it = this->_internal_counts_by_category().begin();
      it != this->_internal_counts_by_category().end(); ++it) {
    total_size += WeedCounterChunk_CountsByCategoryEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // .carbon.metrics.CountsByConclusionType conclusion_counts = 1;
  if (this->_internal_has_conclusion_counts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *conclusion_counts_);
  }

  // .carbon.metrics.TargetSizeData weed_size_data = 2;
  if (this->_internal_has_weed_size_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *weed_size_data_);
  }

  // .carbon.metrics.TargetSizeData crop_size_data = 3;
  if (this->_internal_has_crop_size_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *crop_size_data_);
  }

  // .carbon.metrics.RequiredLaserTimeData targeted_laser_time_data = 5;
  if (this->_internal_has_targeted_laser_time_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *targeted_laser_time_data_);
  }

  // .carbon.metrics.RequiredLaserTimeData untargeted_laser_time_data = 6;
  if (this->_internal_has_untargeted_laser_time_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *untargeted_laser_time_data_);
  }

  // uint64 valid_crop_count = 7;
  if (this->_internal_valid_crop_count() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_valid_crop_count());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WeedCounterChunk::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    WeedCounterChunk::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WeedCounterChunk::GetClassData() const { return &_class_data_; }

void WeedCounterChunk::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<WeedCounterChunk *>(to)->MergeFrom(
      static_cast<const WeedCounterChunk &>(from));
}


void WeedCounterChunk::MergeFrom(const WeedCounterChunk& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.WeedCounterChunk)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  counts_by_category_.MergeFrom(from.counts_by_category_);
  if (from._internal_has_conclusion_counts()) {
    _internal_mutable_conclusion_counts()->::carbon::metrics::CountsByConclusionType::MergeFrom(from._internal_conclusion_counts());
  }
  if (from._internal_has_weed_size_data()) {
    _internal_mutable_weed_size_data()->::carbon::metrics::TargetSizeData::MergeFrom(from._internal_weed_size_data());
  }
  if (from._internal_has_crop_size_data()) {
    _internal_mutable_crop_size_data()->::carbon::metrics::TargetSizeData::MergeFrom(from._internal_crop_size_data());
  }
  if (from._internal_has_targeted_laser_time_data()) {
    _internal_mutable_targeted_laser_time_data()->::carbon::metrics::RequiredLaserTimeData::MergeFrom(from._internal_targeted_laser_time_data());
  }
  if (from._internal_has_untargeted_laser_time_data()) {
    _internal_mutable_untargeted_laser_time_data()->::carbon::metrics::RequiredLaserTimeData::MergeFrom(from._internal_untargeted_laser_time_data());
  }
  if (from._internal_valid_crop_count() != 0) {
    _internal_set_valid_crop_count(from._internal_valid_crop_count());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WeedCounterChunk::CopyFrom(const WeedCounterChunk& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.WeedCounterChunk)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WeedCounterChunk::IsInitialized() const {
  return true;
}

void WeedCounterChunk::InternalSwap(WeedCounterChunk* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  counts_by_category_.InternalSwap(&other->counts_by_category_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(WeedCounterChunk, valid_crop_count_)
      + sizeof(WeedCounterChunk::valid_crop_count_)
      - PROTOBUF_FIELD_OFFSET(WeedCounterChunk, conclusion_counts_)>(
          reinterpret_cast<char*>(&conclusion_counts_),
          reinterpret_cast<char*>(&other->conclusion_counts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata WeedCounterChunk::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[11]);
}

// ===================================================================

class WheelEncoderSpatialData::_Internal {
 public:
};

WheelEncoderSpatialData::WheelEncoderSpatialData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.WheelEncoderSpatialData)
}
WheelEncoderSpatialData::WheelEncoderSpatialData(const WheelEncoderSpatialData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&start_pos_m_, &from.start_pos_m_,
    static_cast<size_t>(reinterpret_cast<char*>(&end_pos_m_) -
    reinterpret_cast<char*>(&start_pos_m_)) + sizeof(end_pos_m_));
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.WheelEncoderSpatialData)
}

inline void WheelEncoderSpatialData::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&start_pos_m_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&end_pos_m_) -
    reinterpret_cast<char*>(&start_pos_m_)) + sizeof(end_pos_m_));
}

WheelEncoderSpatialData::~WheelEncoderSpatialData() {
  // @@protoc_insertion_point(destructor:carbon.metrics.WheelEncoderSpatialData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void WheelEncoderSpatialData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void WheelEncoderSpatialData::ArenaDtor(void* object) {
  WheelEncoderSpatialData* _this = reinterpret_cast< WheelEncoderSpatialData* >(object);
  (void)_this;
}
void WheelEncoderSpatialData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WheelEncoderSpatialData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void WheelEncoderSpatialData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.WheelEncoderSpatialData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&start_pos_m_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&end_pos_m_) -
      reinterpret_cast<char*>(&start_pos_m_)) + sizeof(end_pos_m_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WheelEncoderSpatialData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float start_pos_m = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          start_pos_m_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float end_pos_m = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          end_pos_m_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WheelEncoderSpatialData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.WheelEncoderSpatialData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float start_pos_m = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_start_pos_m = this->_internal_start_pos_m();
  uint32_t raw_start_pos_m;
  memcpy(&raw_start_pos_m, &tmp_start_pos_m, sizeof(tmp_start_pos_m));
  if (raw_start_pos_m != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_start_pos_m(), target);
  }

  // float end_pos_m = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_end_pos_m = this->_internal_end_pos_m();
  uint32_t raw_end_pos_m;
  memcpy(&raw_end_pos_m, &tmp_end_pos_m, sizeof(tmp_end_pos_m));
  if (raw_end_pos_m != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_end_pos_m(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.WheelEncoderSpatialData)
  return target;
}

size_t WheelEncoderSpatialData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.WheelEncoderSpatialData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float start_pos_m = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_start_pos_m = this->_internal_start_pos_m();
  uint32_t raw_start_pos_m;
  memcpy(&raw_start_pos_m, &tmp_start_pos_m, sizeof(tmp_start_pos_m));
  if (raw_start_pos_m != 0) {
    total_size += 1 + 4;
  }

  // float end_pos_m = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_end_pos_m = this->_internal_end_pos_m();
  uint32_t raw_end_pos_m;
  memcpy(&raw_end_pos_m, &tmp_end_pos_m, sizeof(tmp_end_pos_m));
  if (raw_end_pos_m != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WheelEncoderSpatialData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    WheelEncoderSpatialData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WheelEncoderSpatialData::GetClassData() const { return &_class_data_; }

void WheelEncoderSpatialData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<WheelEncoderSpatialData *>(to)->MergeFrom(
      static_cast<const WheelEncoderSpatialData &>(from));
}


void WheelEncoderSpatialData::MergeFrom(const WheelEncoderSpatialData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.WheelEncoderSpatialData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_start_pos_m = from._internal_start_pos_m();
  uint32_t raw_start_pos_m;
  memcpy(&raw_start_pos_m, &tmp_start_pos_m, sizeof(tmp_start_pos_m));
  if (raw_start_pos_m != 0) {
    _internal_set_start_pos_m(from._internal_start_pos_m());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_end_pos_m = from._internal_end_pos_m();
  uint32_t raw_end_pos_m;
  memcpy(&raw_end_pos_m, &tmp_end_pos_m, sizeof(tmp_end_pos_m));
  if (raw_end_pos_m != 0) {
    _internal_set_end_pos_m(from._internal_end_pos_m());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WheelEncoderSpatialData::CopyFrom(const WheelEncoderSpatialData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.WheelEncoderSpatialData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WheelEncoderSpatialData::IsInitialized() const {
  return true;
}

void WheelEncoderSpatialData::InternalSwap(WheelEncoderSpatialData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(WheelEncoderSpatialData, end_pos_m_)
      + sizeof(WheelEncoderSpatialData::end_pos_m_)
      - PROTOBUF_FIELD_OFFSET(WheelEncoderSpatialData, start_pos_m_)>(
          reinterpret_cast<char*>(&start_pos_m_),
          reinterpret_cast<char*>(&other->start_pos_m_));
}

::PROTOBUF_NAMESPACE_ID::Metadata WheelEncoderSpatialData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[12]);
}

// ===================================================================

class BandingSpatialData::_Internal {
 public:
};

BandingSpatialData::BandingSpatialData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.BandingSpatialData)
}
BandingSpatialData::BandingSpatialData(const BandingSpatialData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  percent_banded_ = from.percent_banded_;
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.BandingSpatialData)
}

inline void BandingSpatialData::SharedCtor() {
percent_banded_ = 0;
}

BandingSpatialData::~BandingSpatialData() {
  // @@protoc_insertion_point(destructor:carbon.metrics.BandingSpatialData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BandingSpatialData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BandingSpatialData::ArenaDtor(void* object) {
  BandingSpatialData* _this = reinterpret_cast< BandingSpatialData* >(object);
  (void)_this;
}
void BandingSpatialData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BandingSpatialData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BandingSpatialData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.BandingSpatialData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  percent_banded_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BandingSpatialData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float percent_banded = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          percent_banded_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* BandingSpatialData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.BandingSpatialData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float percent_banded = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_percent_banded = this->_internal_percent_banded();
  uint32_t raw_percent_banded;
  memcpy(&raw_percent_banded, &tmp_percent_banded, sizeof(tmp_percent_banded));
  if (raw_percent_banded != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_percent_banded(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.BandingSpatialData)
  return target;
}

size_t BandingSpatialData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.BandingSpatialData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float percent_banded = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_percent_banded = this->_internal_percent_banded();
  uint32_t raw_percent_banded;
  memcpy(&raw_percent_banded, &tmp_percent_banded, sizeof(tmp_percent_banded));
  if (raw_percent_banded != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BandingSpatialData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BandingSpatialData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BandingSpatialData::GetClassData() const { return &_class_data_; }

void BandingSpatialData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<BandingSpatialData *>(to)->MergeFrom(
      static_cast<const BandingSpatialData &>(from));
}


void BandingSpatialData::MergeFrom(const BandingSpatialData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.BandingSpatialData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_percent_banded = from._internal_percent_banded();
  uint32_t raw_percent_banded;
  memcpy(&raw_percent_banded, &tmp_percent_banded, sizeof(tmp_percent_banded));
  if (raw_percent_banded != 0) {
    _internal_set_percent_banded(from._internal_percent_banded());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BandingSpatialData::CopyFrom(const BandingSpatialData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.BandingSpatialData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BandingSpatialData::IsInitialized() const {
  return true;
}

void BandingSpatialData::InternalSwap(BandingSpatialData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(percent_banded_, other->percent_banded_);
}

::PROTOBUF_NAMESPACE_ID::Metadata BandingSpatialData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[13]);
}

// ===================================================================

class ImplementWidthSpatialData::_Internal {
 public:
};

ImplementWidthSpatialData::ImplementWidthSpatialData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.ImplementWidthSpatialData)
}
ImplementWidthSpatialData::ImplementWidthSpatialData(const ImplementWidthSpatialData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  width_mm_ = from.width_mm_;
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.ImplementWidthSpatialData)
}

inline void ImplementWidthSpatialData::SharedCtor() {
width_mm_ = 0;
}

ImplementWidthSpatialData::~ImplementWidthSpatialData() {
  // @@protoc_insertion_point(destructor:carbon.metrics.ImplementWidthSpatialData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ImplementWidthSpatialData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ImplementWidthSpatialData::ArenaDtor(void* object) {
  ImplementWidthSpatialData* _this = reinterpret_cast< ImplementWidthSpatialData* >(object);
  (void)_this;
}
void ImplementWidthSpatialData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ImplementWidthSpatialData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ImplementWidthSpatialData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.ImplementWidthSpatialData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  width_mm_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ImplementWidthSpatialData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float width_mm = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          width_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ImplementWidthSpatialData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.ImplementWidthSpatialData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float width_mm = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_width_mm = this->_internal_width_mm();
  uint32_t raw_width_mm;
  memcpy(&raw_width_mm, &tmp_width_mm, sizeof(tmp_width_mm));
  if (raw_width_mm != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_width_mm(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.ImplementWidthSpatialData)
  return target;
}

size_t ImplementWidthSpatialData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.ImplementWidthSpatialData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float width_mm = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_width_mm = this->_internal_width_mm();
  uint32_t raw_width_mm;
  memcpy(&raw_width_mm, &tmp_width_mm, sizeof(tmp_width_mm));
  if (raw_width_mm != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ImplementWidthSpatialData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ImplementWidthSpatialData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ImplementWidthSpatialData::GetClassData() const { return &_class_data_; }

void ImplementWidthSpatialData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ImplementWidthSpatialData *>(to)->MergeFrom(
      static_cast<const ImplementWidthSpatialData &>(from));
}


void ImplementWidthSpatialData::MergeFrom(const ImplementWidthSpatialData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.ImplementWidthSpatialData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_width_mm = from._internal_width_mm();
  uint32_t raw_width_mm;
  memcpy(&raw_width_mm, &tmp_width_mm, sizeof(tmp_width_mm));
  if (raw_width_mm != 0) {
    _internal_set_width_mm(from._internal_width_mm());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ImplementWidthSpatialData::CopyFrom(const ImplementWidthSpatialData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.ImplementWidthSpatialData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ImplementWidthSpatialData::IsInitialized() const {
  return true;
}

void ImplementWidthSpatialData::InternalSwap(ImplementWidthSpatialData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(width_mm_, other->width_mm_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ImplementWidthSpatialData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[14]);
}

// ===================================================================

VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse() {}
VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse::MergeFrom(const VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[15]);
}

// ===================================================================

class VelocitySpatialMetric::_Internal {
 public:
};

VelocitySpatialMetric::VelocitySpatialMetric(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  avg_target_vel_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.VelocitySpatialMetric)
}
VelocitySpatialMetric::VelocitySpatialMetric(const VelocitySpatialMetric& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  avg_target_vel_.MergeFrom(from.avg_target_vel_);
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.VelocitySpatialMetric)
}

inline void VelocitySpatialMetric::SharedCtor() {
}

VelocitySpatialMetric::~VelocitySpatialMetric() {
  // @@protoc_insertion_point(destructor:carbon.metrics.VelocitySpatialMetric)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VelocitySpatialMetric::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VelocitySpatialMetric::ArenaDtor(void* object) {
  VelocitySpatialMetric* _this = reinterpret_cast< VelocitySpatialMetric* >(object);
  (void)_this;
  _this->avg_target_vel_. ~MapField();
}
inline void VelocitySpatialMetric::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &VelocitySpatialMetric::ArenaDtor);
  }
}
void VelocitySpatialMetric::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VelocitySpatialMetric::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.VelocitySpatialMetric)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  avg_target_vel_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VelocitySpatialMetric::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, float> avg_target_vel = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&avg_target_vel_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VelocitySpatialMetric::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.VelocitySpatialMetric)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, float> avg_target_vel = 1;
  if (!this->_internal_avg_target_vel().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_avg_target_vel().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_avg_target_vel().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
          it = this->_internal_avg_target_vel().begin();
          it != this->_internal_avg_target_vel().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
          it = this->_internal_avg_target_vel().begin();
          it != this->_internal_avg_target_vel().end(); ++it) {
        target = VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.VelocitySpatialMetric)
  return target;
}

size_t VelocitySpatialMetric::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.VelocitySpatialMetric)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, float> avg_target_vel = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_avg_target_vel_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, float >::const_iterator
      it = this->_internal_avg_target_vel().begin();
      it != this->_internal_avg_target_vel().end(); ++it) {
    total_size += VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VelocitySpatialMetric::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VelocitySpatialMetric::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VelocitySpatialMetric::GetClassData() const { return &_class_data_; }

void VelocitySpatialMetric::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<VelocitySpatialMetric *>(to)->MergeFrom(
      static_cast<const VelocitySpatialMetric &>(from));
}


void VelocitySpatialMetric::MergeFrom(const VelocitySpatialMetric& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.VelocitySpatialMetric)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  avg_target_vel_.MergeFrom(from.avg_target_vel_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VelocitySpatialMetric::CopyFrom(const VelocitySpatialMetric& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.VelocitySpatialMetric)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VelocitySpatialMetric::IsInitialized() const {
  return true;
}

void VelocitySpatialMetric::InternalSwap(VelocitySpatialMetric* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  avg_target_vel_.InternalSwap(&other->avg_target_vel_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VelocitySpatialMetric::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[16]);
}

// ===================================================================

class JobMetric::_Internal {
 public:
};

JobMetric::JobMetric(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.JobMetric)
}
JobMetric::JobMetric(const JobMetric& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  job_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    job_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_job_id().empty()) {
    job_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_job_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.JobMetric)
}

inline void JobMetric::SharedCtor() {
job_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  job_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

JobMetric::~JobMetric() {
  // @@protoc_insertion_point(destructor:carbon.metrics.JobMetric)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void JobMetric::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  job_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void JobMetric::ArenaDtor(void* object) {
  JobMetric* _this = reinterpret_cast< JobMetric* >(object);
  (void)_this;
}
void JobMetric::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void JobMetric::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void JobMetric::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.JobMetric)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  job_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* JobMetric::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string job_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_job_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.metrics.JobMetric.job_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* JobMetric::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.JobMetric)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string job_id = 1;
  if (!this->_internal_job_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_job_id().data(), static_cast<int>(this->_internal_job_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.metrics.JobMetric.job_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_job_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.JobMetric)
  return target;
}

size_t JobMetric::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.JobMetric)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string job_id = 1;
  if (!this->_internal_job_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_job_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData JobMetric::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    JobMetric::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*JobMetric::GetClassData() const { return &_class_data_; }

void JobMetric::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<JobMetric *>(to)->MergeFrom(
      static_cast<const JobMetric &>(from));
}


void JobMetric::MergeFrom(const JobMetric& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.JobMetric)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_job_id().empty()) {
    _internal_set_job_id(from._internal_job_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void JobMetric::CopyFrom(const JobMetric& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.JobMetric)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JobMetric::IsInitialized() const {
  return true;
}

void JobMetric::InternalSwap(JobMetric* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &job_id_, lhs_arena,
      &other->job_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata JobMetric::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[17]);
}

// ===================================================================

class HWMetric::_Internal {
 public:
};

HWMetric::HWMetric(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.HWMetric)
}
HWMetric::HWMetric(const HWMetric& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&lifted_, &from.lifted_,
    static_cast<size_t>(reinterpret_cast<char*>(&debug_mode_) -
    reinterpret_cast<char*>(&lifted_)) + sizeof(debug_mode_));
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.HWMetric)
}

inline void HWMetric::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&lifted_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&debug_mode_) -
    reinterpret_cast<char*>(&lifted_)) + sizeof(debug_mode_));
}

HWMetric::~HWMetric() {
  // @@protoc_insertion_point(destructor:carbon.metrics.HWMetric)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HWMetric::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void HWMetric::ArenaDtor(void* object) {
  HWMetric* _this = reinterpret_cast< HWMetric* >(object);
  (void)_this;
}
void HWMetric::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HWMetric::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HWMetric::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.HWMetric)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&lifted_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&debug_mode_) -
      reinterpret_cast<char*>(&lifted_)) + sizeof(debug_mode_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HWMetric::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool lifted = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          lifted_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool estopped = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          estopped_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool laser_key = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          laser_key_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool interlock = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          interlock_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool water_protect = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          water_protect_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool debug_mode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          debug_mode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HWMetric::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.HWMetric)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool lifted = 1;
  if (this->_internal_lifted() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_lifted(), target);
  }

  // bool estopped = 2;
  if (this->_internal_estopped() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_estopped(), target);
  }

  // bool laser_key = 3;
  if (this->_internal_laser_key() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_laser_key(), target);
  }

  // bool interlock = 4;
  if (this->_internal_interlock() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_interlock(), target);
  }

  // bool water_protect = 5;
  if (this->_internal_water_protect() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_water_protect(), target);
  }

  // bool debug_mode = 6;
  if (this->_internal_debug_mode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_debug_mode(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.HWMetric)
  return target;
}

size_t HWMetric::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.HWMetric)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool lifted = 1;
  if (this->_internal_lifted() != 0) {
    total_size += 1 + 1;
  }

  // bool estopped = 2;
  if (this->_internal_estopped() != 0) {
    total_size += 1 + 1;
  }

  // bool laser_key = 3;
  if (this->_internal_laser_key() != 0) {
    total_size += 1 + 1;
  }

  // bool interlock = 4;
  if (this->_internal_interlock() != 0) {
    total_size += 1 + 1;
  }

  // bool water_protect = 5;
  if (this->_internal_water_protect() != 0) {
    total_size += 1 + 1;
  }

  // bool debug_mode = 6;
  if (this->_internal_debug_mode() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HWMetric::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HWMetric::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HWMetric::GetClassData() const { return &_class_data_; }

void HWMetric::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HWMetric *>(to)->MergeFrom(
      static_cast<const HWMetric &>(from));
}


void HWMetric::MergeFrom(const HWMetric& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.HWMetric)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_lifted() != 0) {
    _internal_set_lifted(from._internal_lifted());
  }
  if (from._internal_estopped() != 0) {
    _internal_set_estopped(from._internal_estopped());
  }
  if (from._internal_laser_key() != 0) {
    _internal_set_laser_key(from._internal_laser_key());
  }
  if (from._internal_interlock() != 0) {
    _internal_set_interlock(from._internal_interlock());
  }
  if (from._internal_water_protect() != 0) {
    _internal_set_water_protect(from._internal_water_protect());
  }
  if (from._internal_debug_mode() != 0) {
    _internal_set_debug_mode(from._internal_debug_mode());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HWMetric::CopyFrom(const HWMetric& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.HWMetric)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HWMetric::IsInitialized() const {
  return true;
}

void HWMetric::InternalSwap(HWMetric* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HWMetric, debug_mode_)
      + sizeof(HWMetric::debug_mode_)
      - PROTOBUF_FIELD_OFFSET(HWMetric, lifted_)>(
          reinterpret_cast<char*>(&lifted_),
          reinterpret_cast<char*>(&other->lifted_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HWMetric::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[18]);
}

// ===================================================================

class SpatialMetricBlock::_Internal {
 public:
  static const ::carbon::metrics::SpatialPosition& start(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::SpatialPosition& end(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::WeedCounterChunk& weed_count(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::WheelEncoderSpatialData& we_data(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::BandingSpatialData& banding_data(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::ImplementWidthSpatialData& implement_width_data(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::VelocitySpatialMetric& vel_data(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::SpatialPosition& start_left(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::SpatialPosition& start_right(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::SpatialPosition& end_left(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::SpatialPosition& end_right(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::JobMetric& job_metric(const SpatialMetricBlock* msg);
  static const ::carbon::metrics::HWMetric& hw_metric(const SpatialMetricBlock* msg);
};

const ::carbon::metrics::SpatialPosition&
SpatialMetricBlock::_Internal::start(const SpatialMetricBlock* msg) {
  return *msg->start_;
}
const ::carbon::metrics::SpatialPosition&
SpatialMetricBlock::_Internal::end(const SpatialMetricBlock* msg) {
  return *msg->end_;
}
const ::carbon::metrics::WeedCounterChunk&
SpatialMetricBlock::_Internal::weed_count(const SpatialMetricBlock* msg) {
  return *msg->weed_count_;
}
const ::carbon::metrics::WheelEncoderSpatialData&
SpatialMetricBlock::_Internal::we_data(const SpatialMetricBlock* msg) {
  return *msg->we_data_;
}
const ::carbon::metrics::BandingSpatialData&
SpatialMetricBlock::_Internal::banding_data(const SpatialMetricBlock* msg) {
  return *msg->banding_data_;
}
const ::carbon::metrics::ImplementWidthSpatialData&
SpatialMetricBlock::_Internal::implement_width_data(const SpatialMetricBlock* msg) {
  return *msg->implement_width_data_;
}
const ::carbon::metrics::VelocitySpatialMetric&
SpatialMetricBlock::_Internal::vel_data(const SpatialMetricBlock* msg) {
  return *msg->vel_data_;
}
const ::carbon::metrics::SpatialPosition&
SpatialMetricBlock::_Internal::start_left(const SpatialMetricBlock* msg) {
  return *msg->start_left_;
}
const ::carbon::metrics::SpatialPosition&
SpatialMetricBlock::_Internal::start_right(const SpatialMetricBlock* msg) {
  return *msg->start_right_;
}
const ::carbon::metrics::SpatialPosition&
SpatialMetricBlock::_Internal::end_left(const SpatialMetricBlock* msg) {
  return *msg->end_left_;
}
const ::carbon::metrics::SpatialPosition&
SpatialMetricBlock::_Internal::end_right(const SpatialMetricBlock* msg) {
  return *msg->end_right_;
}
const ::carbon::metrics::JobMetric&
SpatialMetricBlock::_Internal::job_metric(const SpatialMetricBlock* msg) {
  return *msg->job_metric_;
}
const ::carbon::metrics::HWMetric&
SpatialMetricBlock::_Internal::hw_metric(const SpatialMetricBlock* msg) {
  return *msg->hw_metric_;
}
SpatialMetricBlock::SpatialMetricBlock(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.metrics.SpatialMetricBlock)
}
SpatialMetricBlock::SpatialMetricBlock(const SpatialMetricBlock& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_start()) {
    start_ = new ::carbon::metrics::SpatialPosition(*from.start_);
  } else {
    start_ = nullptr;
  }
  if (from._internal_has_end()) {
    end_ = new ::carbon::metrics::SpatialPosition(*from.end_);
  } else {
    end_ = nullptr;
  }
  if (from._internal_has_weed_count()) {
    weed_count_ = new ::carbon::metrics::WeedCounterChunk(*from.weed_count_);
  } else {
    weed_count_ = nullptr;
  }
  if (from._internal_has_we_data()) {
    we_data_ = new ::carbon::metrics::WheelEncoderSpatialData(*from.we_data_);
  } else {
    we_data_ = nullptr;
  }
  if (from._internal_has_banding_data()) {
    banding_data_ = new ::carbon::metrics::BandingSpatialData(*from.banding_data_);
  } else {
    banding_data_ = nullptr;
  }
  if (from._internal_has_implement_width_data()) {
    implement_width_data_ = new ::carbon::metrics::ImplementWidthSpatialData(*from.implement_width_data_);
  } else {
    implement_width_data_ = nullptr;
  }
  if (from._internal_has_vel_data()) {
    vel_data_ = new ::carbon::metrics::VelocitySpatialMetric(*from.vel_data_);
  } else {
    vel_data_ = nullptr;
  }
  if (from._internal_has_start_left()) {
    start_left_ = new ::carbon::metrics::SpatialPosition(*from.start_left_);
  } else {
    start_left_ = nullptr;
  }
  if (from._internal_has_start_right()) {
    start_right_ = new ::carbon::metrics::SpatialPosition(*from.start_right_);
  } else {
    start_right_ = nullptr;
  }
  if (from._internal_has_end_left()) {
    end_left_ = new ::carbon::metrics::SpatialPosition(*from.end_left_);
  } else {
    end_left_ = nullptr;
  }
  if (from._internal_has_end_right()) {
    end_right_ = new ::carbon::metrics::SpatialPosition(*from.end_right_);
  } else {
    end_right_ = nullptr;
  }
  if (from._internal_has_job_metric()) {
    job_metric_ = new ::carbon::metrics::JobMetric(*from.job_metric_);
  } else {
    job_metric_ = nullptr;
  }
  if (from._internal_has_hw_metric()) {
    hw_metric_ = new ::carbon::metrics::HWMetric(*from.hw_metric_);
  } else {
    hw_metric_ = nullptr;
  }
  suspicious_ = from.suspicious_;
  // @@protoc_insertion_point(copy_constructor:carbon.metrics.SpatialMetricBlock)
}

inline void SpatialMetricBlock::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&start_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&suspicious_) -
    reinterpret_cast<char*>(&start_)) + sizeof(suspicious_));
}

SpatialMetricBlock::~SpatialMetricBlock() {
  // @@protoc_insertion_point(destructor:carbon.metrics.SpatialMetricBlock)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SpatialMetricBlock::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete start_;
  if (this != internal_default_instance()) delete end_;
  if (this != internal_default_instance()) delete weed_count_;
  if (this != internal_default_instance()) delete we_data_;
  if (this != internal_default_instance()) delete banding_data_;
  if (this != internal_default_instance()) delete implement_width_data_;
  if (this != internal_default_instance()) delete vel_data_;
  if (this != internal_default_instance()) delete start_left_;
  if (this != internal_default_instance()) delete start_right_;
  if (this != internal_default_instance()) delete end_left_;
  if (this != internal_default_instance()) delete end_right_;
  if (this != internal_default_instance()) delete job_metric_;
  if (this != internal_default_instance()) delete hw_metric_;
}

void SpatialMetricBlock::ArenaDtor(void* object) {
  SpatialMetricBlock* _this = reinterpret_cast< SpatialMetricBlock* >(object);
  (void)_this;
}
void SpatialMetricBlock::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SpatialMetricBlock::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SpatialMetricBlock::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.metrics.SpatialMetricBlock)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && start_ != nullptr) {
    delete start_;
  }
  start_ = nullptr;
  if (GetArenaForAllocation() == nullptr && end_ != nullptr) {
    delete end_;
  }
  end_ = nullptr;
  if (GetArenaForAllocation() == nullptr && weed_count_ != nullptr) {
    delete weed_count_;
  }
  weed_count_ = nullptr;
  if (GetArenaForAllocation() == nullptr && we_data_ != nullptr) {
    delete we_data_;
  }
  we_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && banding_data_ != nullptr) {
    delete banding_data_;
  }
  banding_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && implement_width_data_ != nullptr) {
    delete implement_width_data_;
  }
  implement_width_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && vel_data_ != nullptr) {
    delete vel_data_;
  }
  vel_data_ = nullptr;
  if (GetArenaForAllocation() == nullptr && start_left_ != nullptr) {
    delete start_left_;
  }
  start_left_ = nullptr;
  if (GetArenaForAllocation() == nullptr && start_right_ != nullptr) {
    delete start_right_;
  }
  start_right_ = nullptr;
  if (GetArenaForAllocation() == nullptr && end_left_ != nullptr) {
    delete end_left_;
  }
  end_left_ = nullptr;
  if (GetArenaForAllocation() == nullptr && end_right_ != nullptr) {
    delete end_right_;
  }
  end_right_ = nullptr;
  if (GetArenaForAllocation() == nullptr && job_metric_ != nullptr) {
    delete job_metric_;
  }
  job_metric_ = nullptr;
  if (GetArenaForAllocation() == nullptr && hw_metric_ != nullptr) {
    delete hw_metric_;
  }
  hw_metric_ = nullptr;
  suspicious_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SpatialMetricBlock::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.metrics.SpatialPosition start = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_start(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.SpatialPosition end = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_end(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.WeedCounterChunk weed_count = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_weed_count(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.WheelEncoderSpatialData we_data = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_we_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.BandingSpatialData banding_data = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_banding_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.ImplementWidthSpatialData implement_width_data = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_implement_width_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.VelocitySpatialMetric vel_data = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_vel_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.SpatialPosition start_left = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_start_left(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.SpatialPosition start_right = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_start_right(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.SpatialPosition end_left = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_end_left(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.SpatialPosition end_right = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_end_right(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.JobMetric job_metric = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr = ctx->ParseMessage(_internal_mutable_job_metric(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool suspicious = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          suspicious_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.metrics.HWMetric hw_metric = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          ptr = ctx->ParseMessage(_internal_mutable_hw_metric(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SpatialMetricBlock::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.metrics.SpatialMetricBlock)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.metrics.SpatialPosition start = 1;
  if (this->_internal_has_start()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::start(this), target, stream);
  }

  // .carbon.metrics.SpatialPosition end = 2;
  if (this->_internal_has_end()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::end(this), target, stream);
  }

  // .carbon.metrics.WeedCounterChunk weed_count = 3;
  if (this->_internal_has_weed_count()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::weed_count(this), target, stream);
  }

  // .carbon.metrics.WheelEncoderSpatialData we_data = 4;
  if (this->_internal_has_we_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::we_data(this), target, stream);
  }

  // .carbon.metrics.BandingSpatialData banding_data = 5;
  if (this->_internal_has_banding_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::banding_data(this), target, stream);
  }

  // .carbon.metrics.ImplementWidthSpatialData implement_width_data = 6;
  if (this->_internal_has_implement_width_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::implement_width_data(this), target, stream);
  }

  // .carbon.metrics.VelocitySpatialMetric vel_data = 7;
  if (this->_internal_has_vel_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::vel_data(this), target, stream);
  }

  // .carbon.metrics.SpatialPosition start_left = 8;
  if (this->_internal_has_start_left()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::start_left(this), target, stream);
  }

  // .carbon.metrics.SpatialPosition start_right = 9;
  if (this->_internal_has_start_right()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::start_right(this), target, stream);
  }

  // .carbon.metrics.SpatialPosition end_left = 10;
  if (this->_internal_has_end_left()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::end_left(this), target, stream);
  }

  // .carbon.metrics.SpatialPosition end_right = 11;
  if (this->_internal_has_end_right()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::end_right(this), target, stream);
  }

  // .carbon.metrics.JobMetric job_metric = 12;
  if (this->_internal_has_job_metric()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        12, _Internal::job_metric(this), target, stream);
  }

  // bool suspicious = 13;
  if (this->_internal_suspicious() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(13, this->_internal_suspicious(), target);
  }

  // .carbon.metrics.HWMetric hw_metric = 14;
  if (this->_internal_has_hw_metric()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        14, _Internal::hw_metric(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.metrics.SpatialMetricBlock)
  return target;
}

size_t SpatialMetricBlock::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.metrics.SpatialMetricBlock)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.metrics.SpatialPosition start = 1;
  if (this->_internal_has_start()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_);
  }

  // .carbon.metrics.SpatialPosition end = 2;
  if (this->_internal_has_end()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *end_);
  }

  // .carbon.metrics.WeedCounterChunk weed_count = 3;
  if (this->_internal_has_weed_count()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *weed_count_);
  }

  // .carbon.metrics.WheelEncoderSpatialData we_data = 4;
  if (this->_internal_has_we_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *we_data_);
  }

  // .carbon.metrics.BandingSpatialData banding_data = 5;
  if (this->_internal_has_banding_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *banding_data_);
  }

  // .carbon.metrics.ImplementWidthSpatialData implement_width_data = 6;
  if (this->_internal_has_implement_width_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *implement_width_data_);
  }

  // .carbon.metrics.VelocitySpatialMetric vel_data = 7;
  if (this->_internal_has_vel_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *vel_data_);
  }

  // .carbon.metrics.SpatialPosition start_left = 8;
  if (this->_internal_has_start_left()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_left_);
  }

  // .carbon.metrics.SpatialPosition start_right = 9;
  if (this->_internal_has_start_right()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_right_);
  }

  // .carbon.metrics.SpatialPosition end_left = 10;
  if (this->_internal_has_end_left()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *end_left_);
  }

  // .carbon.metrics.SpatialPosition end_right = 11;
  if (this->_internal_has_end_right()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *end_right_);
  }

  // .carbon.metrics.JobMetric job_metric = 12;
  if (this->_internal_has_job_metric()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *job_metric_);
  }

  // .carbon.metrics.HWMetric hw_metric = 14;
  if (this->_internal_has_hw_metric()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *hw_metric_);
  }

  // bool suspicious = 13;
  if (this->_internal_suspicious() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SpatialMetricBlock::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SpatialMetricBlock::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SpatialMetricBlock::GetClassData() const { return &_class_data_; }

void SpatialMetricBlock::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SpatialMetricBlock *>(to)->MergeFrom(
      static_cast<const SpatialMetricBlock &>(from));
}


void SpatialMetricBlock::MergeFrom(const SpatialMetricBlock& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.metrics.SpatialMetricBlock)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_start()) {
    _internal_mutable_start()->::carbon::metrics::SpatialPosition::MergeFrom(from._internal_start());
  }
  if (from._internal_has_end()) {
    _internal_mutable_end()->::carbon::metrics::SpatialPosition::MergeFrom(from._internal_end());
  }
  if (from._internal_has_weed_count()) {
    _internal_mutable_weed_count()->::carbon::metrics::WeedCounterChunk::MergeFrom(from._internal_weed_count());
  }
  if (from._internal_has_we_data()) {
    _internal_mutable_we_data()->::carbon::metrics::WheelEncoderSpatialData::MergeFrom(from._internal_we_data());
  }
  if (from._internal_has_banding_data()) {
    _internal_mutable_banding_data()->::carbon::metrics::BandingSpatialData::MergeFrom(from._internal_banding_data());
  }
  if (from._internal_has_implement_width_data()) {
    _internal_mutable_implement_width_data()->::carbon::metrics::ImplementWidthSpatialData::MergeFrom(from._internal_implement_width_data());
  }
  if (from._internal_has_vel_data()) {
    _internal_mutable_vel_data()->::carbon::metrics::VelocitySpatialMetric::MergeFrom(from._internal_vel_data());
  }
  if (from._internal_has_start_left()) {
    _internal_mutable_start_left()->::carbon::metrics::SpatialPosition::MergeFrom(from._internal_start_left());
  }
  if (from._internal_has_start_right()) {
    _internal_mutable_start_right()->::carbon::metrics::SpatialPosition::MergeFrom(from._internal_start_right());
  }
  if (from._internal_has_end_left()) {
    _internal_mutable_end_left()->::carbon::metrics::SpatialPosition::MergeFrom(from._internal_end_left());
  }
  if (from._internal_has_end_right()) {
    _internal_mutable_end_right()->::carbon::metrics::SpatialPosition::MergeFrom(from._internal_end_right());
  }
  if (from._internal_has_job_metric()) {
    _internal_mutable_job_metric()->::carbon::metrics::JobMetric::MergeFrom(from._internal_job_metric());
  }
  if (from._internal_has_hw_metric()) {
    _internal_mutable_hw_metric()->::carbon::metrics::HWMetric::MergeFrom(from._internal_hw_metric());
  }
  if (from._internal_suspicious() != 0) {
    _internal_set_suspicious(from._internal_suspicious());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SpatialMetricBlock::CopyFrom(const SpatialMetricBlock& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.metrics.SpatialMetricBlock)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpatialMetricBlock::IsInitialized() const {
  return true;
}

void SpatialMetricBlock::InternalSwap(SpatialMetricBlock* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SpatialMetricBlock, suspicious_)
      + sizeof(SpatialMetricBlock::suspicious_)
      - PROTOBUF_FIELD_OFFSET(SpatialMetricBlock, start_)>(
          reinterpret_cast<char*>(&start_),
          reinterpret_cast<char*>(&other->start_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SpatialMetricBlock::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_getter, &descriptor_table_proto_2fmetrics_2fmetrics_2eproto_once,
      file_level_metadata_proto_2fmetrics_2fmetrics_2eproto[19]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace metrics
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::metrics::LaserPosition* Arena::CreateMaybeMessage< ::carbon::metrics::LaserPosition >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::LaserPosition >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::LaserIdentifier* Arena::CreateMaybeMessage< ::carbon::metrics::LaserIdentifier >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::LaserIdentifier >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::LaserLifeTime* Arena::CreateMaybeMessage< ::carbon::metrics::LaserLifeTime >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::LaserLifeTime >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::LaserEventTime* Arena::CreateMaybeMessage< ::carbon::metrics::LaserEventTime >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::LaserEventTime >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::LaserLifeTimes* Arena::CreateMaybeMessage< ::carbon::metrics::LaserLifeTimes >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::LaserLifeTimes >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::LaserChangeTimes* Arena::CreateMaybeMessage< ::carbon::metrics::LaserChangeTimes >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::LaserChangeTimes >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::CountsByConclusionType* Arena::CreateMaybeMessage< ::carbon::metrics::CountsByConclusionType >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::CountsByConclusionType >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::TargetSizeData* Arena::CreateMaybeMessage< ::carbon::metrics::TargetSizeData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::TargetSizeData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::RequiredLaserTimeData* Arena::CreateMaybeMessage< ::carbon::metrics::RequiredLaserTimeData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::RequiredLaserTimeData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::SpatialPosition* Arena::CreateMaybeMessage< ::carbon::metrics::SpatialPosition >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::SpatialPosition >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::WeedCounterChunk_CountsByCategoryEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::WeedCounterChunk* Arena::CreateMaybeMessage< ::carbon::metrics::WeedCounterChunk >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::WeedCounterChunk >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::WheelEncoderSpatialData* Arena::CreateMaybeMessage< ::carbon::metrics::WheelEncoderSpatialData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::WheelEncoderSpatialData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::BandingSpatialData* Arena::CreateMaybeMessage< ::carbon::metrics::BandingSpatialData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::BandingSpatialData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::ImplementWidthSpatialData* Arena::CreateMaybeMessage< ::carbon::metrics::ImplementWidthSpatialData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::ImplementWidthSpatialData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::VelocitySpatialMetric_AvgTargetVelEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::VelocitySpatialMetric* Arena::CreateMaybeMessage< ::carbon::metrics::VelocitySpatialMetric >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::VelocitySpatialMetric >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::JobMetric* Arena::CreateMaybeMessage< ::carbon::metrics::JobMetric >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::JobMetric >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::HWMetric* Arena::CreateMaybeMessage< ::carbon::metrics::HWMetric >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::HWMetric >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::metrics::SpatialMetricBlock* Arena::CreateMaybeMessage< ::carbon::metrics::SpatialMetricBlock >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::metrics::SpatialMetricBlock >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

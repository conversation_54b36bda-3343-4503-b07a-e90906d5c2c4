// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/cv/cv.proto

#include "proto/cv/cv.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace aimbot {
namespace cv {
}  // namespace cv
}  // namespace aimbot
}  // namespace carbon
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_2fcv_2fcv_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2fcv_2fcv_2eproto = nullptr;
const uint32_t TableStruct_proto_2fcv_2fcv_2eproto::offsets[1] = {};
static constexpr ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema* schemas = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::Message* const* file_default_instances = nullptr;

const char descriptor_table_protodef_proto_2fcv_2fcv_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\021proto/cv/cv.proto\022\020carbon.aimbot.cv*\206\001"
  "\n\020P2PCaptureReason\022\031\n\025P2PCaptureReason_M"
  "ISS\020\000\022\034\n\030P2PCaptureReason_SUCCESS\020\001\022\031\n\025P"
  "2PCaptureReason_JUMP\020\002\022\036\n\032P2PCaptureReas"
  "on_First_P2P\020\003B\nZ\010proto/cvb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2fcv_2fcv_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fcv_2fcv_2eproto = {
  false, false, 194, descriptor_table_protodef_proto_2fcv_2fcv_2eproto, "proto/cv/cv.proto", 
  &descriptor_table_proto_2fcv_2fcv_2eproto_once, nullptr, 0, 0,
  schemas, file_default_instances, TableStruct_proto_2fcv_2fcv_2eproto::offsets,
  nullptr, file_level_enum_descriptors_proto_2fcv_2fcv_2eproto, file_level_service_descriptors_proto_2fcv_2fcv_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2fcv_2fcv_2eproto_getter() {
  return &descriptor_table_proto_2fcv_2fcv_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2fcv_2fcv_2eproto(&descriptor_table_proto_2fcv_2fcv_2eproto);
namespace carbon {
namespace aimbot {
namespace cv {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* P2PCaptureReason_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2fcv_2fcv_2eproto);
  return file_level_enum_descriptors_proto_2fcv_2fcv_2eproto[0];
}
bool P2PCaptureReason_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace cv
}  // namespace aimbot
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

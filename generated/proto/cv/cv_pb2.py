# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/cv/cv.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/cv/cv.proto',
  package='carbon.aimbot.cv',
  syntax='proto3',
  serialized_options=b'Z\010proto/cv',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x11proto/cv/cv.proto\x12\x10\x63\x61rbon.aimbot.cv*\x86\x01\n\x10P2PCaptureReason\x12\x19\n\x15P2PCaptureReason_MISS\x10\x00\x12\x1c\n\x18P2PCaptureReason_SUCCESS\x10\x01\x12\x19\n\x15P2PCaptureReason_JUMP\x10\x02\x12\x1e\n\x1aP2PCaptureReason_First_P2P\x10\x03\x42\nZ\x08proto/cvb\x06proto3'
)

_P2PCAPTUREREASON = _descriptor.EnumDescriptor(
  name='P2PCaptureReason',
  full_name='carbon.aimbot.cv.P2PCaptureReason',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='P2PCaptureReason_MISS', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='P2PCaptureReason_SUCCESS', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='P2PCaptureReason_JUMP', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='P2PCaptureReason_First_P2P', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=40,
  serialized_end=174,
)
_sym_db.RegisterEnumDescriptor(_P2PCAPTUREREASON)

P2PCaptureReason = enum_type_wrapper.EnumTypeWrapper(_P2PCAPTUREREASON)
P2PCaptureReason_MISS = 0
P2PCaptureReason_SUCCESS = 1
P2PCaptureReason_JUMP = 2
P2PCaptureReason_First_P2P = 3


DESCRIPTOR.enum_types_by_name['P2PCaptureReason'] = _P2PCAPTUREREASON
_sym_db.RegisterFileDescriptor(DESCRIPTOR)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

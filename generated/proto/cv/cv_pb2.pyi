"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    cast as typing___cast,
)


builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

P2PCaptureReasonValue = typing___NewType('P2PCaptureReasonValue', builtin___int)
type___P2PCaptureReasonValue = P2PCaptureReasonValue
P2PCaptureReason: _P2PCaptureReason
class _P2PCaptureReason(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[P2PCaptureReasonValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    P2PCaptureReason_MISS = typing___cast(P2PCaptureReasonValue, 0)
    P2PCaptureReason_SUCCESS = typing___cast(P2PCaptureReasonValue, 1)
    P2PCaptureReason_JUMP = typing___cast(P2PCaptureReasonValue, 2)
    P2PCaptureReason_First_P2P = typing___cast(P2PCaptureReasonValue, 3)
P2PCaptureReason_MISS = typing___cast(P2PCaptureReasonValue, 0)
P2PCaptureReason_SUCCESS = typing___cast(P2PCaptureReasonValue, 1)
P2PCaptureReason_JUMP = typing___cast(P2PCaptureReasonValue, 2)
P2PCaptureReason_First_P2P = typing___cast(P2PCaptureReasonValue, 3)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

LogLevelValue = typing___NewType('LogLevelValue', builtin___int)
type___LogLevelValue = LogLevelValue
LogLevel: _LogLevel
class _LogLevel(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[LogLevelValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    TRACE = typing___cast(LogLevelValue, 0)
    DEBUG = typing___cast(LogLevelValue, 1)
    INFO = typing___cast(LogLevelValue, 2)
    WARNING = typing___cast(LogLevelValue, 3)
    ERROR = typing___cast(LogLevelValue, 4)
    FATAL = typing___cast(LogLevelValue, 5)
    PANIC = typing___cast(LogLevelValue, 6)
TRACE = typing___cast(LogLevelValue, 0)
DEBUG = typing___cast(LogLevelValue, 1)
INFO = typing___cast(LogLevelValue, 2)
WARNING = typing___cast(LogLevelValue, 3)
ERROR = typing___cast(LogLevelValue, 4)
FATAL = typing___cast(LogLevelValue, 5)
PANIC = typing___cast(LogLevelValue, 6)

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

class SetLevelRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    logLevel: type___LogLevelValue = ...

    def __init__(self,
        *,
        logLevel : typing___Optional[type___LogLevelValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"logLevel",b"logLevel"]) -> None: ...
type___SetLevelRequest = SetLevelRequest

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/logging/logging.proto

#include "proto/logging/logging.pb.h"
#include "proto/logging/logging.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace logging {

static const char* LoggingService_method_names[] = {
  "/carbon.logging.LoggingService/SetLevel",
};

std::unique_ptr< LoggingService::Stub> LoggingService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< LoggingService::Stub> stub(new LoggingService::Stub(channel, options));
  return stub;
}

LoggingService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_SetLevel_(LoggingService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status LoggingService::Stub::SetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::carbon::logging::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::logging::SetLevelRequest, ::carbon::logging::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetLevel_, context, request, response);
}

void LoggingService::Stub::async::SetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest* request, ::carbon::logging::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::logging::SetLevelRequest, ::carbon::logging::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLevel_, context, request, response, std::move(f));
}

void LoggingService::Stub::async::SetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest* request, ::carbon::logging::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLevel_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::logging::Empty>* LoggingService::Stub::PrepareAsyncSetLevelRaw(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::logging::Empty, ::carbon::logging::SetLevelRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetLevel_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::logging::Empty>* LoggingService::Stub::AsyncSetLevelRaw(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetLevelRaw(context, request, cq);
  result->StartCall();
  return result;
}

LoggingService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LoggingService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LoggingService::Service, ::carbon::logging::SetLevelRequest, ::carbon::logging::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LoggingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::logging::SetLevelRequest* req,
             ::carbon::logging::Empty* resp) {
               return service->SetLevel(ctx, req, resp);
             }, this)));
}

LoggingService::Service::~Service() {
}

::grpc::Status LoggingService::Service::SetLevel(::grpc::ServerContext* context, const ::carbon::logging::SetLevelRequest* request, ::carbon::logging::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace logging


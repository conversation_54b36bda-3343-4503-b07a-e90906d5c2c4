# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/logging/logging.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/logging/logging.proto',
  package='carbon.logging',
  syntax='proto3',
  serialized_options=b'H\003Z\rproto/logging',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1bproto/logging/logging.proto\x12\x0e\x63\x61rbon.logging\"\x07\n\x05\x45mpty\"=\n\x0fSetLevelRequest\x12*\n\x08logLevel\x18\x01 \x01(\x0e\x32\x18.carbon.logging.LogLevel*X\n\x08LogLevel\x12\t\n\x05TRACE\x10\x00\x12\t\n\x05\x44\x45\x42UG\x10\x01\x12\x08\n\x04INFO\x10\x02\x12\x0b\n\x07WARNING\x10\x03\x12\t\n\x05\x45RROR\x10\x04\x12\t\n\x05\x46\x41TAL\x10\x05\x12\t\n\x05PANIC\x10\x06\x32T\n\x0eLoggingService\x12\x42\n\x08SetLevel\x12\x1f.carbon.logging.SetLevelRequest\x1a\x15.carbon.logging.EmptyB\x11H\x03Z\rproto/loggingb\x06proto3'
)

_LOGLEVEL = _descriptor.EnumDescriptor(
  name='LogLevel',
  full_name='carbon.logging.LogLevel',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TRACE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DEBUG', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INFO', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='WARNING', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ERROR', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FATAL', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PANIC', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=119,
  serialized_end=207,
)
_sym_db.RegisterEnumDescriptor(_LOGLEVEL)

LogLevel = enum_type_wrapper.EnumTypeWrapper(_LOGLEVEL)
TRACE = 0
DEBUG = 1
INFO = 2
WARNING = 3
ERROR = 4
FATAL = 5
PANIC = 6



_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='carbon.logging.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=47,
  serialized_end=54,
)


_SETLEVELREQUEST = _descriptor.Descriptor(
  name='SetLevelRequest',
  full_name='carbon.logging.SetLevelRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='logLevel', full_name='carbon.logging.SetLevelRequest.logLevel', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=56,
  serialized_end=117,
)

_SETLEVELREQUEST.fields_by_name['logLevel'].enum_type = _LOGLEVEL
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['SetLevelRequest'] = _SETLEVELREQUEST
DESCRIPTOR.enum_types_by_name['LogLevel'] = _LOGLEVEL
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'proto.logging.logging_pb2'
  # @@protoc_insertion_point(class_scope:carbon.logging.Empty)
  })
_sym_db.RegisterMessage(Empty)

SetLevelRequest = _reflection.GeneratedProtocolMessageType('SetLevelRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETLEVELREQUEST,
  '__module__' : 'proto.logging.logging_pb2'
  # @@protoc_insertion_point(class_scope:carbon.logging.SetLevelRequest)
  })
_sym_db.RegisterMessage(SetLevelRequest)


DESCRIPTOR._options = None

_LOGGINGSERVICE = _descriptor.ServiceDescriptor(
  name='LoggingService',
  full_name='carbon.logging.LoggingService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=209,
  serialized_end=293,
  methods=[
  _descriptor.MethodDescriptor(
    name='SetLevel',
    full_name='carbon.logging.LoggingService.SetLevel',
    index=0,
    containing_service=None,
    input_type=_SETLEVELREQUEST,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_LOGGINGSERVICE)

DESCRIPTOR.services_by_name['LoggingService'] = _LOGGINGSERVICE

# @@protoc_insertion_point(module_scope)

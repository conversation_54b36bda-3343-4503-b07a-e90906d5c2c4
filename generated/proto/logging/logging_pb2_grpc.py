# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.proto.logging import logging_pb2 as proto_dot_logging_dot_logging__pb2


class LoggingServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetLevel = channel.unary_unary(
                '/carbon.logging.LoggingService/SetLevel',
                request_serializer=proto_dot_logging_dot_logging__pb2.SetLevelRequest.SerializeToString,
                response_deserializer=proto_dot_logging_dot_logging__pb2.Empty.FromString,
                )


class LoggingServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SetLevel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LoggingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetLevel': grpc.unary_unary_rpc_method_handler(
                    servicer.SetLevel,
                    request_deserializer=proto_dot_logging_dot_logging__pb2.SetLevelRequest.FromString,
                    response_serializer=proto_dot_logging_dot_logging__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.logging.LoggingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class LoggingService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SetLevel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.logging.LoggingService/SetLevel',
            proto_dot_logging_dot_logging__pb2.SetLevelRequest.SerializeToString,
            proto_dot_logging_dot_logging__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

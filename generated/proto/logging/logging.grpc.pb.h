// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/logging/logging.proto
#ifndef GRPC_proto_2flogging_2flogging_2eproto__INCLUDED
#define GRPC_proto_2flogging_2flogging_2eproto__INCLUDED

#include "proto/logging/logging.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace logging {

class LoggingService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.logging.LoggingService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status SetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::carbon::logging::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::logging::Empty>> AsyncSetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::logging::Empty>>(AsyncSetLevelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::logging::Empty>> PrepareAsyncSetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::logging::Empty>>(PrepareAsyncSetLevelRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void SetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest* request, ::carbon::logging::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest* request, ::carbon::logging::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::logging::Empty>* AsyncSetLevelRaw(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::logging::Empty>* PrepareAsyncSetLevelRaw(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status SetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::carbon::logging::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::logging::Empty>> AsyncSetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::logging::Empty>>(AsyncSetLevelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::logging::Empty>> PrepareAsyncSetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::logging::Empty>>(PrepareAsyncSetLevelRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void SetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest* request, ::carbon::logging::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetLevel(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest* request, ::carbon::logging::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::logging::Empty>* AsyncSetLevelRaw(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::logging::Empty>* PrepareAsyncSetLevelRaw(::grpc::ClientContext* context, const ::carbon::logging::SetLevelRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_SetLevel_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status SetLevel(::grpc::ServerContext* context, const ::carbon::logging::SetLevelRequest* request, ::carbon::logging::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_SetLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetLevel() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_SetLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLevel(::grpc::ServerContext* /*context*/, const ::carbon::logging::SetLevelRequest* /*request*/, ::carbon::logging::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLevel(::grpc::ServerContext* context, ::carbon::logging::SetLevelRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::logging::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_SetLevel<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_SetLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetLevel() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::logging::SetLevelRequest, ::carbon::logging::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::logging::SetLevelRequest* request, ::carbon::logging::Empty* response) { return this->SetLevel(context, request, response); }));}
    void SetMessageAllocatorFor_SetLevel(
        ::grpc::MessageAllocator< ::carbon::logging::SetLevelRequest, ::carbon::logging::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::logging::SetLevelRequest, ::carbon::logging::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLevel(::grpc::ServerContext* /*context*/, const ::carbon::logging::SetLevelRequest* /*request*/, ::carbon::logging::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLevel(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::logging::SetLevelRequest* /*request*/, ::carbon::logging::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_SetLevel<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_SetLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetLevel() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_SetLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLevel(::grpc::ServerContext* /*context*/, const ::carbon::logging::SetLevelRequest* /*request*/, ::carbon::logging::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetLevel() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_SetLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLevel(::grpc::ServerContext* /*context*/, const ::carbon::logging::SetLevelRequest* /*request*/, ::carbon::logging::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLevel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetLevel() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetLevel(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLevel(::grpc::ServerContext* /*context*/, const ::carbon::logging::SetLevelRequest* /*request*/, ::carbon::logging::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLevel(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetLevel() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::logging::SetLevelRequest, ::carbon::logging::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::logging::SetLevelRequest, ::carbon::logging::Empty>* streamer) {
                       return this->StreamedSetLevel(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetLevel(::grpc::ServerContext* /*context*/, const ::carbon::logging::SetLevelRequest* /*request*/, ::carbon::logging::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetLevel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::logging::SetLevelRequest,::carbon::logging::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_SetLevel<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_SetLevel<Service > StreamedService;
};

}  // namespace logging
}  // namespace carbon


#endif  // GRPC_proto_2flogging_2flogging_2eproto__INCLUDED

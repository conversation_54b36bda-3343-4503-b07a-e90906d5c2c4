// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/logging/logging.proto

#include "proto/logging/logging.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace logging {
constexpr Empty::Empty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct EmptyDefaultTypeInternal {
  constexpr EmptyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EmptyDefaultTypeInternal() {}
  union {
    Empty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EmptyDefaultTypeInternal _Empty_default_instance_;
constexpr SetLevelRequest::SetLevelRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : loglevel_(0)
{}
struct SetLevelRequestDefaultTypeInternal {
  constexpr SetLevelRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetLevelRequestDefaultTypeInternal() {}
  union {
    SetLevelRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetLevelRequestDefaultTypeInternal _SetLevelRequest_default_instance_;
}  // namespace logging
}  // namespace carbon
namespace carbon {
namespace logging {
bool LogLevel_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> LogLevel_strings[7] = {};

static const char LogLevel_names[] =
  "DEBUG"
  "ERROR"
  "FATAL"
  "INFO"
  "PANIC"
  "TRACE"
  "WARNING";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry LogLevel_entries[] = {
  { {LogLevel_names + 0, 5}, 1 },
  { {LogLevel_names + 5, 5}, 4 },
  { {LogLevel_names + 10, 5}, 5 },
  { {LogLevel_names + 15, 4}, 2 },
  { {LogLevel_names + 19, 5}, 6 },
  { {LogLevel_names + 24, 5}, 0 },
  { {LogLevel_names + 29, 7}, 3 },
};

static const int LogLevel_entries_by_number[] = {
  5, // 0 -> TRACE
  0, // 1 -> DEBUG
  3, // 2 -> INFO
  6, // 3 -> WARNING
  1, // 4 -> ERROR
  2, // 5 -> FATAL
  4, // 6 -> PANIC
};

const std::string& LogLevel_Name(
    LogLevel value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          LogLevel_entries,
          LogLevel_entries_by_number,
          7, LogLevel_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      LogLevel_entries,
      LogLevel_entries_by_number,
      7, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     LogLevel_strings[idx].get();
}
bool LogLevel_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LogLevel* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      LogLevel_entries, 7, name, &int_value);
  if (success) {
    *value = static_cast<LogLevel>(int_value);
  }
  return success;
}

// ===================================================================

class Empty::_Internal {
 public:
};

Empty::Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.logging.Empty)
}
Empty::Empty(const Empty& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite() {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.logging.Empty)
}

inline void Empty::SharedCtor() {
}

Empty::~Empty() {
  // @@protoc_insertion_point(destructor:carbon.logging.Empty)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void Empty::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Empty::ArenaDtor(void* object) {
  Empty* _this = reinterpret_cast< Empty* >(object);
  (void)_this;
}
void Empty::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Empty::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Empty::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.logging.Empty)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<std::string>();
}

const char* Empty::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Empty::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.logging.Empty)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.logging.Empty)
  return target;
}

size_t Empty::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.logging.Empty)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Empty::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const Empty*>(
      &from));
}

void Empty::MergeFrom(const Empty& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.logging.Empty)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void Empty::CopyFrom(const Empty& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.logging.Empty)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Empty::IsInitialized() const {
  return true;
}

void Empty::InternalSwap(Empty* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
}

std::string Empty::GetTypeName() const {
  return "carbon.logging.Empty";
}


// ===================================================================

class SetLevelRequest::_Internal {
 public:
};

SetLevelRequest::SetLevelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.logging.SetLevelRequest)
}
SetLevelRequest::SetLevelRequest(const SetLevelRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite() {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  loglevel_ = from.loglevel_;
  // @@protoc_insertion_point(copy_constructor:carbon.logging.SetLevelRequest)
}

inline void SetLevelRequest::SharedCtor() {
loglevel_ = 0;
}

SetLevelRequest::~SetLevelRequest() {
  // @@protoc_insertion_point(destructor:carbon.logging.SetLevelRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void SetLevelRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetLevelRequest::ArenaDtor(void* object) {
  SetLevelRequest* _this = reinterpret_cast< SetLevelRequest* >(object);
  (void)_this;
}
void SetLevelRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetLevelRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetLevelRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.logging.SetLevelRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  loglevel_ = 0;
  _internal_metadata_.Clear<std::string>();
}

const char* SetLevelRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.logging.LogLevel logLevel = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_loglevel(static_cast<::carbon::logging::LogLevel>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetLevelRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.logging.SetLevelRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.logging.LogLevel logLevel = 1;
  if (this->_internal_loglevel() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_loglevel(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.logging.SetLevelRequest)
  return target;
}

size_t SetLevelRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.logging.SetLevelRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.logging.LogLevel logLevel = 1;
  if (this->_internal_loglevel() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_loglevel());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SetLevelRequest::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const SetLevelRequest*>(
      &from));
}

void SetLevelRequest::MergeFrom(const SetLevelRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.logging.SetLevelRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_loglevel() != 0) {
    _internal_set_loglevel(from._internal_loglevel());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void SetLevelRequest::CopyFrom(const SetLevelRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.logging.SetLevelRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetLevelRequest::IsInitialized() const {
  return true;
}

void SetLevelRequest::InternalSwap(SetLevelRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(loglevel_, other->loglevel_);
}

std::string SetLevelRequest::GetTypeName() const {
  return "carbon.logging.SetLevelRequest";
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace logging
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::logging::Empty* Arena::CreateMaybeMessage< ::carbon::logging::Empty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::logging::Empty >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::logging::SetLevelRequest* Arena::CreateMaybeMessage< ::carbon::logging::SetLevelRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::logging::SetLevelRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

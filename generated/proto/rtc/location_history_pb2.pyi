"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from google.protobuf.timestamp_pb2 import (
    Timestamp as google___protobuf___timestamp_pb2___Timestamp,
)

from generated.proto.geo.geo_pb2 import (
    Point as proto___geo___geo_pb2___Point,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class RobotData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    task_id: builtin___int = ...
    active: builtin___bool = ...
    objective_id: builtin___int = ...

    def __init__(self,
        *,
        task_id : typing___Optional[builtin___int] = None,
        active : typing___Optional[builtin___bool] = None,
        objective_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active",b"active",u"objective_id",b"objective_id",u"task_id",b"task_id"]) -> None: ...
type___RobotData = RobotData

class LocationHistoryRecord(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    heading_degrees: builtin___float = ...

    @property
    def point(self) -> proto___geo___geo_pb2___Point: ...

    @property
    def timestamp(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    @property
    def data(self) -> type___RobotData: ...

    def __init__(self,
        *,
        point : typing___Optional[proto___geo___geo_pb2___Point] = None,
        heading_degrees : typing___Optional[builtin___float] = None,
        timestamp : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        data : typing___Optional[type___RobotData] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_heading_degrees",b"_heading_degrees",u"data",b"data",u"heading_degrees",b"heading_degrees",u"point",b"point",u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_heading_degrees",b"_heading_degrees",u"data",b"data",u"heading_degrees",b"heading_degrees",u"point",b"point",u"timestamp",b"timestamp"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_heading_degrees",b"_heading_degrees"]) -> typing_extensions___Literal["heading_degrees"]: ...
type___LocationHistoryRecord = LocationHistoryRecord

class LocationHistoryRecordList(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def records(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___LocationHistoryRecord]: ...

    def __init__(self,
        *,
        records : typing___Optional[typing___Iterable[type___LocationHistoryRecord]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"records",b"records"]) -> None: ...
type___LocationHistoryRecordList = LocationHistoryRecordList

class LogLocationHistoryRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def history(self) -> type___LocationHistoryRecordList: ...

    def __init__(self,
        *,
        history : typing___Optional[type___LocationHistoryRecordList] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"history",b"history"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"history",b"history"]) -> None: ...
type___LogLocationHistoryRequest = LogLocationHistoryRequest

class ListRobotsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page_size: builtin___int = ...
    page_token: typing___Text = ...
    robot_serials: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        page_size : typing___Optional[builtin___int] = None,
        page_token : typing___Optional[typing___Text] = None,
        robot_serials : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"page_size",b"page_size",u"page_token",b"page_token",u"robot_serials",b"robot_serials"]) -> None: ...
type___ListRobotsRequest = ListRobotsRequest

class ListRobotsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    next_page_token: typing___Text = ...

    @property
    def robots(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___RobotSummary]: ...

    def __init__(self,
        *,
        next_page_token : typing___Optional[typing___Text] = None,
        robots : typing___Optional[typing___Iterable[type___RobotSummary]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"next_page_token",b"next_page_token",u"robots",b"robots"]) -> None: ...
type___ListRobotsResponse = ListRobotsResponse

class RobotSummary(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    serial: typing___Text = ...

    @property
    def last_seen(self) -> type___LocationHistoryRecord: ...

    def __init__(self,
        *,
        serial : typing___Optional[typing___Text] = None,
        last_seen : typing___Optional[type___LocationHistoryRecord] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"last_seen",b"last_seen"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"last_seen",b"last_seen",u"serial",b"serial"]) -> None: ...
type___RobotSummary = RobotSummary

class ListLocationHistoryRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    page_size: builtin___int = ...
    page_token: typing___Text = ...
    robot_serial: typing___Text = ...
    desc: builtin___bool = ...
    include_closest: builtin___bool = ...
    task_id: builtin___int = ...
    objective_id: builtin___int = ...

    @property
    def start(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    @property
    def end(self) -> google___protobuf___timestamp_pb2___Timestamp: ...

    def __init__(self,
        *,
        page_size : typing___Optional[builtin___int] = None,
        page_token : typing___Optional[typing___Text] = None,
        robot_serial : typing___Optional[typing___Text] = None,
        start : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        end : typing___Optional[google___protobuf___timestamp_pb2___Timestamp] = None,
        desc : typing___Optional[builtin___bool] = None,
        include_closest : typing___Optional[builtin___bool] = None,
        task_id : typing___Optional[builtin___int] = None,
        objective_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"end",b"end",u"start",b"start"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"desc",b"desc",u"end",b"end",u"include_closest",b"include_closest",u"objective_id",b"objective_id",u"page_size",b"page_size",u"page_token",b"page_token",u"robot_serial",b"robot_serial",u"start",b"start",u"task_id",b"task_id"]) -> None: ...
type___ListLocationHistoryRequest = ListLocationHistoryRequest

class ListLocationHistoryResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    next_page_token: typing___Text = ...

    @property
    def history(self) -> type___LocationHistoryRecordList: ...

    def __init__(self,
        *,
        next_page_token : typing___Optional[typing___Text] = None,
        history : typing___Optional[type___LocationHistoryRecordList] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"history",b"history"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"history",b"history",u"next_page_token",b"next_page_token"]) -> None: ...
type___ListLocationHistoryResponse = ListLocationHistoryResponse

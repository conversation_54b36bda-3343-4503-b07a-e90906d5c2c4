// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/rtc/broadcaster.proto

#include "proto/rtc/broadcaster.pb.h"
#include "proto/rtc/broadcaster.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace rtc {

static const char* Broadcaster_method_names[] = {
  "/carbon.rtc.Broadcaster/MessageBus",
  "/carbon.rtc.Broadcaster/LocalSignalServer",
  "/carbon.rtc.Broadcaster/GetStreamList",
};

std::unique_ptr< Broadcaster::Stub> Broadcaster::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< Broadcaster::Stub> stub(new Broadcaster::Stub(channel, options));
  return stub;
}

Broadcaster::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_MessageBus_(Broadcaster_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  , rpcmethod_LocalSignalServer_(Broadcaster_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  , rpcmethod_GetStreamList_(Broadcaster_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::ClientReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* Broadcaster::Stub::MessageBusRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>::Create(channel_.get(), rpcmethod_MessageBus_, context);
}

void Broadcaster::Stub::async::MessageBus(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::carbon::rtc::RtcMessage,::carbon::rtc::RtcMessage>* reactor) {
  ::grpc::internal::ClientCallbackReaderWriterFactory< ::carbon::rtc::RtcMessage,::carbon::rtc::RtcMessage>::Create(stub_->channel_.get(), stub_->rpcmethod_MessageBus_, context, reactor);
}

::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* Broadcaster::Stub::AsyncMessageBusRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>::Create(channel_.get(), cq, rpcmethod_MessageBus_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* Broadcaster::Stub::PrepareAsyncMessageBusRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>::Create(channel_.get(), cq, rpcmethod_MessageBus_, context, false, nullptr);
}

::grpc::ClientReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* Broadcaster::Stub::LocalSignalServerRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>::Create(channel_.get(), rpcmethod_LocalSignalServer_, context);
}

void Broadcaster::Stub::async::LocalSignalServer(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::carbon::rtc::SignalingMsg,::carbon::rtc::SignalingMsg>* reactor) {
  ::grpc::internal::ClientCallbackReaderWriterFactory< ::carbon::rtc::SignalingMsg,::carbon::rtc::SignalingMsg>::Create(stub_->channel_.get(), stub_->rpcmethod_LocalSignalServer_, context, reactor);
}

::grpc::ClientAsyncReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* Broadcaster::Stub::AsyncLocalSignalServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>::Create(channel_.get(), cq, rpcmethod_LocalSignalServer_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* Broadcaster::Stub::PrepareAsyncLocalSignalServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>::Create(channel_.get(), cq, rpcmethod_LocalSignalServer_, context, false, nullptr);
}

::grpc::Status Broadcaster::Stub::GetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::carbon::rtc::StreamListResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::StreamListRequest, ::carbon::rtc::StreamListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetStreamList_, context, request, response);
}

void Broadcaster::Stub::async::GetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest* request, ::carbon::rtc::StreamListResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::StreamListRequest, ::carbon::rtc::StreamListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetStreamList_, context, request, response, std::move(f));
}

void Broadcaster::Stub::async::GetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest* request, ::carbon::rtc::StreamListResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetStreamList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::StreamListResponse>* Broadcaster::Stub::PrepareAsyncGetStreamListRaw(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc::StreamListResponse, ::carbon::rtc::StreamListRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetStreamList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::StreamListResponse>* Broadcaster::Stub::AsyncGetStreamListRaw(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetStreamListRaw(context, request, cq);
  result->StartCall();
  return result;
}

Broadcaster::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      Broadcaster_method_names[0],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< Broadcaster::Service, ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>(
          [](Broadcaster::Service* service,
             ::grpc::ServerContext* ctx,
             ::grpc::ServerReaderWriter<::carbon::rtc::RtcMessage,
             ::carbon::rtc::RtcMessage>* stream) {
               return service->MessageBus(ctx, stream);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      Broadcaster_method_names[1],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< Broadcaster::Service, ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>(
          [](Broadcaster::Service* service,
             ::grpc::ServerContext* ctx,
             ::grpc::ServerReaderWriter<::carbon::rtc::SignalingMsg,
             ::carbon::rtc::SignalingMsg>* stream) {
               return service->LocalSignalServer(ctx, stream);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      Broadcaster_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< Broadcaster::Service, ::carbon::rtc::StreamListRequest, ::carbon::rtc::StreamListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](Broadcaster::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::StreamListRequest* req,
             ::carbon::rtc::StreamListResponse* resp) {
               return service->GetStreamList(ctx, req, resp);
             }, this)));
}

Broadcaster::Service::~Service() {
}

::grpc::Status Broadcaster::Service::MessageBus(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status Broadcaster::Service::LocalSignalServer(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status Broadcaster::Service::GetStreamList(::grpc::ServerContext* context, const ::carbon::rtc::StreamListRequest* request, ::carbon::rtc::StreamListResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace rtc


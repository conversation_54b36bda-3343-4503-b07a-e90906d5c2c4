"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class AuthStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    read: builtin___bool = ...
    write: builtin___bool = ...

    def __init__(self,
        *,
        read : typing___Optional[builtin___bool] = None,
        write : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"read",b"read",u"write",b"write"]) -> None: ...
type___AuthStatus = AuthStatus

class RtcMessage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...
    msg: builtin___bytes = ...

    @property
    def auth(self) -> type___AuthStatus: ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        msg : typing___Optional[builtin___bytes] = None,
        auth : typing___Optional[type___AuthStatus] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"auth",b"auth"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"auth",b"auth",u"id",b"id",u"msg",b"msg"]) -> None: ...
type___RtcMessage = RtcMessage

class SignalingMsg(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    msg: builtin___bytes = ...

    def __init__(self,
        *,
        msg : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"msg",b"msg"]) -> None: ...
type___SignalingMsg = SignalingMsg

class StreamListRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StreamListRequest = StreamListRequest

class StreamListResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class StreamsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___StreamsEntry = StreamsEntry


    @property
    def streams(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    def __init__(self,
        *,
        streams : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"streams",b"streams"]) -> None: ...
type___StreamListResponse = StreamListResponse

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/rtc/hh.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2frtc_2fhh_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2frtc_2fhh_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/empty.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2frtc_2fhh_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2frtc_2fhh_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frtc_2fhh_2eproto;
namespace carbon {
namespace rtc {
class GetRobotRequiredStateRequest;
struct GetRobotRequiredStateRequestDefaultTypeInternal;
extern GetRobotRequiredStateRequestDefaultTypeInternal _GetRobotRequiredStateRequest_default_instance_;
class GetRobotRequiredStateResponse;
struct GetRobotRequiredStateResponseDefaultTypeInternal;
extern GetRobotRequiredStateResponseDefaultTypeInternal _GetRobotRequiredStateResponse_default_instance_;
class RobotRequiredState;
struct RobotRequiredStateDefaultTypeInternal;
extern RobotRequiredStateDefaultTypeInternal _RobotRequiredState_default_instance_;
class RobotStatusInfo;
struct RobotStatusInfoDefaultTypeInternal;
extern RobotStatusInfoDefaultTypeInternal _RobotStatusInfo_default_instance_;
class SetRobotRequiredStateRequest;
struct SetRobotRequiredStateRequestDefaultTypeInternal;
extern SetRobotRequiredStateRequestDefaultTypeInternal _SetRobotRequiredStateRequest_default_instance_;
}  // namespace rtc
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::rtc::GetRobotRequiredStateRequest* Arena::CreateMaybeMessage<::carbon::rtc::GetRobotRequiredStateRequest>(Arena*);
template<> ::carbon::rtc::GetRobotRequiredStateResponse* Arena::CreateMaybeMessage<::carbon::rtc::GetRobotRequiredStateResponse>(Arena*);
template<> ::carbon::rtc::RobotRequiredState* Arena::CreateMaybeMessage<::carbon::rtc::RobotRequiredState>(Arena*);
template<> ::carbon::rtc::RobotStatusInfo* Arena::CreateMaybeMessage<::carbon::rtc::RobotStatusInfo>(Arena*);
template<> ::carbon::rtc::SetRobotRequiredStateRequest* Arena::CreateMaybeMessage<::carbon::rtc::SetRobotRequiredStateRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace rtc {

enum HHStateStatus : int {
  HH_UNKNOWN = 0,
  HH_DISABLED = 1,
  HH_OPERATIONAL = 2,
  HH_STOPPED = 3,
  HH_SAFE = 4,
  HH_ESTOP = 5,
  HHStateStatus_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  HHStateStatus_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool HHStateStatus_IsValid(int value);
constexpr HHStateStatus HHStateStatus_MIN = HH_UNKNOWN;
constexpr HHStateStatus HHStateStatus_MAX = HH_ESTOP;
constexpr int HHStateStatus_ARRAYSIZE = HHStateStatus_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HHStateStatus_descriptor();
template<typename T>
inline const std::string& HHStateStatus_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HHStateStatus>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HHStateStatus_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HHStateStatus_descriptor(), enum_t_value);
}
inline bool HHStateStatus_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, HHStateStatus* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HHStateStatus>(
    HHStateStatus_descriptor(), name, value);
}
// ===================================================================

class RobotRequiredState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.RobotRequiredState) */ {
 public:
  inline RobotRequiredState() : RobotRequiredState(nullptr) {}
  ~RobotRequiredState() override;
  explicit constexpr RobotRequiredState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RobotRequiredState(const RobotRequiredState& from);
  RobotRequiredState(RobotRequiredState&& from) noexcept
    : RobotRequiredState() {
    *this = ::std::move(from);
  }

  inline RobotRequiredState& operator=(const RobotRequiredState& from) {
    CopyFrom(from);
    return *this;
  }
  inline RobotRequiredState& operator=(RobotRequiredState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RobotRequiredState& default_instance() {
    return *internal_default_instance();
  }
  static inline const RobotRequiredState* internal_default_instance() {
    return reinterpret_cast<const RobotRequiredState*>(
               &_RobotRequiredState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RobotRequiredState& a, RobotRequiredState& b) {
    a.Swap(&b);
  }
  inline void Swap(RobotRequiredState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RobotRequiredState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RobotRequiredState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RobotRequiredState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RobotRequiredState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RobotRequiredState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RobotRequiredState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.RobotRequiredState";
  }
  protected:
  explicit RobotRequiredState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStoppedStateFieldNumber = 1,
  };
  // bool stopped_state = 1;
  void clear_stopped_state();
  bool stopped_state() const;
  void set_stopped_state(bool value);
  private:
  bool _internal_stopped_state() const;
  void _internal_set_stopped_state(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.RobotRequiredState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool stopped_state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fhh_2eproto;
};
// -------------------------------------------------------------------

class RobotStatusInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.RobotStatusInfo) */ {
 public:
  inline RobotStatusInfo() : RobotStatusInfo(nullptr) {}
  ~RobotStatusInfo() override;
  explicit constexpr RobotStatusInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RobotStatusInfo(const RobotStatusInfo& from);
  RobotStatusInfo(RobotStatusInfo&& from) noexcept
    : RobotStatusInfo() {
    *this = ::std::move(from);
  }

  inline RobotStatusInfo& operator=(const RobotStatusInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RobotStatusInfo& operator=(RobotStatusInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RobotStatusInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const RobotStatusInfo* internal_default_instance() {
    return reinterpret_cast<const RobotStatusInfo*>(
               &_RobotStatusInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(RobotStatusInfo& a, RobotStatusInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(RobotStatusInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RobotStatusInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RobotStatusInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RobotStatusInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RobotStatusInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RobotStatusInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RobotStatusInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.RobotStatusInfo";
  }
  protected:
  explicit RobotStatusInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStateFieldNumber = 1,
  };
  // .carbon.rtc.HHStateStatus state = 1;
  void clear_state();
  ::carbon::rtc::HHStateStatus state() const;
  void set_state(::carbon::rtc::HHStateStatus value);
  private:
  ::carbon::rtc::HHStateStatus _internal_state() const;
  void _internal_set_state(::carbon::rtc::HHStateStatus value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.RobotStatusInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fhh_2eproto;
};
// -------------------------------------------------------------------

class SetRobotRequiredStateRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.SetRobotRequiredStateRequest) */ {
 public:
  inline SetRobotRequiredStateRequest() : SetRobotRequiredStateRequest(nullptr) {}
  ~SetRobotRequiredStateRequest() override;
  explicit constexpr SetRobotRequiredStateRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetRobotRequiredStateRequest(const SetRobotRequiredStateRequest& from);
  SetRobotRequiredStateRequest(SetRobotRequiredStateRequest&& from) noexcept
    : SetRobotRequiredStateRequest() {
    *this = ::std::move(from);
  }

  inline SetRobotRequiredStateRequest& operator=(const SetRobotRequiredStateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetRobotRequiredStateRequest& operator=(SetRobotRequiredStateRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetRobotRequiredStateRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetRobotRequiredStateRequest* internal_default_instance() {
    return reinterpret_cast<const SetRobotRequiredStateRequest*>(
               &_SetRobotRequiredStateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SetRobotRequiredStateRequest& a, SetRobotRequiredStateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetRobotRequiredStateRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetRobotRequiredStateRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetRobotRequiredStateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetRobotRequiredStateRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetRobotRequiredStateRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetRobotRequiredStateRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetRobotRequiredStateRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.SetRobotRequiredStateRequest";
  }
  protected:
  explicit SetRobotRequiredStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotSerialFieldNumber = 2,
    kRequiredStateFieldNumber = 3,
    kTimestampMsFieldNumber = 1,
  };
  // string robot_serial = 2;
  void clear_robot_serial();
  const std::string& robot_serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_serial();
  PROTOBUF_NODISCARD std::string* release_robot_serial();
  void set_allocated_robot_serial(std::string* robot_serial);
  private:
  const std::string& _internal_robot_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_serial(const std::string& value);
  std::string* _internal_mutable_robot_serial();
  public:

  // .carbon.rtc.RobotRequiredState required_state = 3;
  bool has_required_state() const;
  private:
  bool _internal_has_required_state() const;
  public:
  void clear_required_state();
  const ::carbon::rtc::RobotRequiredState& required_state() const;
  PROTOBUF_NODISCARD ::carbon::rtc::RobotRequiredState* release_required_state();
  ::carbon::rtc::RobotRequiredState* mutable_required_state();
  void set_allocated_required_state(::carbon::rtc::RobotRequiredState* required_state);
  private:
  const ::carbon::rtc::RobotRequiredState& _internal_required_state() const;
  ::carbon::rtc::RobotRequiredState* _internal_mutable_required_state();
  public:
  void unsafe_arena_set_allocated_required_state(
      ::carbon::rtc::RobotRequiredState* required_state);
  ::carbon::rtc::RobotRequiredState* unsafe_arena_release_required_state();

  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.SetRobotRequiredStateRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_serial_;
  ::carbon::rtc::RobotRequiredState* required_state_;
  int64_t timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fhh_2eproto;
};
// -------------------------------------------------------------------

class GetRobotRequiredStateRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.GetRobotRequiredStateRequest) */ {
 public:
  inline GetRobotRequiredStateRequest() : GetRobotRequiredStateRequest(nullptr) {}
  ~GetRobotRequiredStateRequest() override;
  explicit constexpr GetRobotRequiredStateRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetRobotRequiredStateRequest(const GetRobotRequiredStateRequest& from);
  GetRobotRequiredStateRequest(GetRobotRequiredStateRequest&& from) noexcept
    : GetRobotRequiredStateRequest() {
    *this = ::std::move(from);
  }

  inline GetRobotRequiredStateRequest& operator=(const GetRobotRequiredStateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetRobotRequiredStateRequest& operator=(GetRobotRequiredStateRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetRobotRequiredStateRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetRobotRequiredStateRequest* internal_default_instance() {
    return reinterpret_cast<const GetRobotRequiredStateRequest*>(
               &_GetRobotRequiredStateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetRobotRequiredStateRequest& a, GetRobotRequiredStateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetRobotRequiredStateRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetRobotRequiredStateRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetRobotRequiredStateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetRobotRequiredStateRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetRobotRequiredStateRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetRobotRequiredStateRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetRobotRequiredStateRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.GetRobotRequiredStateRequest";
  }
  protected:
  explicit GetRobotRequiredStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotSerialFieldNumber = 2,
    kTimestampMsFieldNumber = 1,
  };
  // string robot_serial = 2;
  void clear_robot_serial();
  const std::string& robot_serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_serial();
  PROTOBUF_NODISCARD std::string* release_robot_serial();
  void set_allocated_robot_serial(std::string* robot_serial);
  private:
  const std::string& _internal_robot_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_serial(const std::string& value);
  std::string* _internal_mutable_robot_serial();
  public:

  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.GetRobotRequiredStateRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_serial_;
  int64_t timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fhh_2eproto;
};
// -------------------------------------------------------------------

class GetRobotRequiredStateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.GetRobotRequiredStateResponse) */ {
 public:
  inline GetRobotRequiredStateResponse() : GetRobotRequiredStateResponse(nullptr) {}
  ~GetRobotRequiredStateResponse() override;
  explicit constexpr GetRobotRequiredStateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetRobotRequiredStateResponse(const GetRobotRequiredStateResponse& from);
  GetRobotRequiredStateResponse(GetRobotRequiredStateResponse&& from) noexcept
    : GetRobotRequiredStateResponse() {
    *this = ::std::move(from);
  }

  inline GetRobotRequiredStateResponse& operator=(const GetRobotRequiredStateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetRobotRequiredStateResponse& operator=(GetRobotRequiredStateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetRobotRequiredStateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetRobotRequiredStateResponse* internal_default_instance() {
    return reinterpret_cast<const GetRobotRequiredStateResponse*>(
               &_GetRobotRequiredStateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetRobotRequiredStateResponse& a, GetRobotRequiredStateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetRobotRequiredStateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetRobotRequiredStateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetRobotRequiredStateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetRobotRequiredStateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetRobotRequiredStateResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetRobotRequiredStateResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetRobotRequiredStateResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.GetRobotRequiredStateResponse";
  }
  protected:
  explicit GetRobotRequiredStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRequiredStateFieldNumber = 2,
    kTimestampMsFieldNumber = 1,
  };
  // .carbon.rtc.RobotRequiredState required_state = 2;
  bool has_required_state() const;
  private:
  bool _internal_has_required_state() const;
  public:
  void clear_required_state();
  const ::carbon::rtc::RobotRequiredState& required_state() const;
  PROTOBUF_NODISCARD ::carbon::rtc::RobotRequiredState* release_required_state();
  ::carbon::rtc::RobotRequiredState* mutable_required_state();
  void set_allocated_required_state(::carbon::rtc::RobotRequiredState* required_state);
  private:
  const ::carbon::rtc::RobotRequiredState& _internal_required_state() const;
  ::carbon::rtc::RobotRequiredState* _internal_mutable_required_state();
  public:
  void unsafe_arena_set_allocated_required_state(
      ::carbon::rtc::RobotRequiredState* required_state);
  ::carbon::rtc::RobotRequiredState* unsafe_arena_release_required_state();

  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.GetRobotRequiredStateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::rtc::RobotRequiredState* required_state_;
  int64_t timestamp_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fhh_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RobotRequiredState

// bool stopped_state = 1;
inline void RobotRequiredState::clear_stopped_state() {
  stopped_state_ = false;
}
inline bool RobotRequiredState::_internal_stopped_state() const {
  return stopped_state_;
}
inline bool RobotRequiredState::stopped_state() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RobotRequiredState.stopped_state)
  return _internal_stopped_state();
}
inline void RobotRequiredState::_internal_set_stopped_state(bool value) {
  
  stopped_state_ = value;
}
inline void RobotRequiredState::set_stopped_state(bool value) {
  _internal_set_stopped_state(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.RobotRequiredState.stopped_state)
}

// -------------------------------------------------------------------

// RobotStatusInfo

// .carbon.rtc.HHStateStatus state = 1;
inline void RobotStatusInfo::clear_state() {
  state_ = 0;
}
inline ::carbon::rtc::HHStateStatus RobotStatusInfo::_internal_state() const {
  return static_cast< ::carbon::rtc::HHStateStatus >(state_);
}
inline ::carbon::rtc::HHStateStatus RobotStatusInfo::state() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RobotStatusInfo.state)
  return _internal_state();
}
inline void RobotStatusInfo::_internal_set_state(::carbon::rtc::HHStateStatus value) {
  
  state_ = value;
}
inline void RobotStatusInfo::set_state(::carbon::rtc::HHStateStatus value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.RobotStatusInfo.state)
}

// -------------------------------------------------------------------

// SetRobotRequiredStateRequest

// int64 timestamp_ms = 1;
inline void SetRobotRequiredStateRequest::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t SetRobotRequiredStateRequest::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t SetRobotRequiredStateRequest::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.SetRobotRequiredStateRequest.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void SetRobotRequiredStateRequest::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void SetRobotRequiredStateRequest::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.SetRobotRequiredStateRequest.timestamp_ms)
}

// string robot_serial = 2;
inline void SetRobotRequiredStateRequest::clear_robot_serial() {
  robot_serial_.ClearToEmpty();
}
inline const std::string& SetRobotRequiredStateRequest::robot_serial() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.SetRobotRequiredStateRequest.robot_serial)
  return _internal_robot_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetRobotRequiredStateRequest::set_robot_serial(ArgT0&& arg0, ArgT... args) {
 
 robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.SetRobotRequiredStateRequest.robot_serial)
}
inline std::string* SetRobotRequiredStateRequest::mutable_robot_serial() {
  std::string* _s = _internal_mutable_robot_serial();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.SetRobotRequiredStateRequest.robot_serial)
  return _s;
}
inline const std::string& SetRobotRequiredStateRequest::_internal_robot_serial() const {
  return robot_serial_.Get();
}
inline void SetRobotRequiredStateRequest::_internal_set_robot_serial(const std::string& value) {
  
  robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetRobotRequiredStateRequest::_internal_mutable_robot_serial() {
  
  return robot_serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetRobotRequiredStateRequest::release_robot_serial() {
  // @@protoc_insertion_point(field_release:carbon.rtc.SetRobotRequiredStateRequest.robot_serial)
  return robot_serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetRobotRequiredStateRequest::set_allocated_robot_serial(std::string* robot_serial) {
  if (robot_serial != nullptr) {
    
  } else {
    
  }
  robot_serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.SetRobotRequiredStateRequest.robot_serial)
}

// .carbon.rtc.RobotRequiredState required_state = 3;
inline bool SetRobotRequiredStateRequest::_internal_has_required_state() const {
  return this != internal_default_instance() && required_state_ != nullptr;
}
inline bool SetRobotRequiredStateRequest::has_required_state() const {
  return _internal_has_required_state();
}
inline void SetRobotRequiredStateRequest::clear_required_state() {
  if (GetArenaForAllocation() == nullptr && required_state_ != nullptr) {
    delete required_state_;
  }
  required_state_ = nullptr;
}
inline const ::carbon::rtc::RobotRequiredState& SetRobotRequiredStateRequest::_internal_required_state() const {
  const ::carbon::rtc::RobotRequiredState* p = required_state_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::rtc::RobotRequiredState&>(
      ::carbon::rtc::_RobotRequiredState_default_instance_);
}
inline const ::carbon::rtc::RobotRequiredState& SetRobotRequiredStateRequest::required_state() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.SetRobotRequiredStateRequest.required_state)
  return _internal_required_state();
}
inline void SetRobotRequiredStateRequest::unsafe_arena_set_allocated_required_state(
    ::carbon::rtc::RobotRequiredState* required_state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(required_state_);
  }
  required_state_ = required_state;
  if (required_state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.SetRobotRequiredStateRequest.required_state)
}
inline ::carbon::rtc::RobotRequiredState* SetRobotRequiredStateRequest::release_required_state() {
  
  ::carbon::rtc::RobotRequiredState* temp = required_state_;
  required_state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::rtc::RobotRequiredState* SetRobotRequiredStateRequest::unsafe_arena_release_required_state() {
  // @@protoc_insertion_point(field_release:carbon.rtc.SetRobotRequiredStateRequest.required_state)
  
  ::carbon::rtc::RobotRequiredState* temp = required_state_;
  required_state_ = nullptr;
  return temp;
}
inline ::carbon::rtc::RobotRequiredState* SetRobotRequiredStateRequest::_internal_mutable_required_state() {
  
  if (required_state_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::rtc::RobotRequiredState>(GetArenaForAllocation());
    required_state_ = p;
  }
  return required_state_;
}
inline ::carbon::rtc::RobotRequiredState* SetRobotRequiredStateRequest::mutable_required_state() {
  ::carbon::rtc::RobotRequiredState* _msg = _internal_mutable_required_state();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.SetRobotRequiredStateRequest.required_state)
  return _msg;
}
inline void SetRobotRequiredStateRequest::set_allocated_required_state(::carbon::rtc::RobotRequiredState* required_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete required_state_;
  }
  if (required_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::RobotRequiredState>::GetOwningArena(required_state);
    if (message_arena != submessage_arena) {
      required_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, required_state, submessage_arena);
    }
    
  } else {
    
  }
  required_state_ = required_state;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.SetRobotRequiredStateRequest.required_state)
}

// -------------------------------------------------------------------

// GetRobotRequiredStateRequest

// int64 timestamp_ms = 1;
inline void GetRobotRequiredStateRequest::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t GetRobotRequiredStateRequest::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t GetRobotRequiredStateRequest::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.GetRobotRequiredStateRequest.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void GetRobotRequiredStateRequest::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void GetRobotRequiredStateRequest::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.GetRobotRequiredStateRequest.timestamp_ms)
}

// string robot_serial = 2;
inline void GetRobotRequiredStateRequest::clear_robot_serial() {
  robot_serial_.ClearToEmpty();
}
inline const std::string& GetRobotRequiredStateRequest::robot_serial() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.GetRobotRequiredStateRequest.robot_serial)
  return _internal_robot_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetRobotRequiredStateRequest::set_robot_serial(ArgT0&& arg0, ArgT... args) {
 
 robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.GetRobotRequiredStateRequest.robot_serial)
}
inline std::string* GetRobotRequiredStateRequest::mutable_robot_serial() {
  std::string* _s = _internal_mutable_robot_serial();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.GetRobotRequiredStateRequest.robot_serial)
  return _s;
}
inline const std::string& GetRobotRequiredStateRequest::_internal_robot_serial() const {
  return robot_serial_.Get();
}
inline void GetRobotRequiredStateRequest::_internal_set_robot_serial(const std::string& value) {
  
  robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetRobotRequiredStateRequest::_internal_mutable_robot_serial() {
  
  return robot_serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetRobotRequiredStateRequest::release_robot_serial() {
  // @@protoc_insertion_point(field_release:carbon.rtc.GetRobotRequiredStateRequest.robot_serial)
  return robot_serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetRobotRequiredStateRequest::set_allocated_robot_serial(std::string* robot_serial) {
  if (robot_serial != nullptr) {
    
  } else {
    
  }
  robot_serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.GetRobotRequiredStateRequest.robot_serial)
}

// -------------------------------------------------------------------

// GetRobotRequiredStateResponse

// int64 timestamp_ms = 1;
inline void GetRobotRequiredStateResponse::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t GetRobotRequiredStateResponse::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t GetRobotRequiredStateResponse::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.GetRobotRequiredStateResponse.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void GetRobotRequiredStateResponse::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void GetRobotRequiredStateResponse::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.GetRobotRequiredStateResponse.timestamp_ms)
}

// .carbon.rtc.RobotRequiredState required_state = 2;
inline bool GetRobotRequiredStateResponse::_internal_has_required_state() const {
  return this != internal_default_instance() && required_state_ != nullptr;
}
inline bool GetRobotRequiredStateResponse::has_required_state() const {
  return _internal_has_required_state();
}
inline void GetRobotRequiredStateResponse::clear_required_state() {
  if (GetArenaForAllocation() == nullptr && required_state_ != nullptr) {
    delete required_state_;
  }
  required_state_ = nullptr;
}
inline const ::carbon::rtc::RobotRequiredState& GetRobotRequiredStateResponse::_internal_required_state() const {
  const ::carbon::rtc::RobotRequiredState* p = required_state_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::rtc::RobotRequiredState&>(
      ::carbon::rtc::_RobotRequiredState_default_instance_);
}
inline const ::carbon::rtc::RobotRequiredState& GetRobotRequiredStateResponse::required_state() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.GetRobotRequiredStateResponse.required_state)
  return _internal_required_state();
}
inline void GetRobotRequiredStateResponse::unsafe_arena_set_allocated_required_state(
    ::carbon::rtc::RobotRequiredState* required_state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(required_state_);
  }
  required_state_ = required_state;
  if (required_state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.GetRobotRequiredStateResponse.required_state)
}
inline ::carbon::rtc::RobotRequiredState* GetRobotRequiredStateResponse::release_required_state() {
  
  ::carbon::rtc::RobotRequiredState* temp = required_state_;
  required_state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::rtc::RobotRequiredState* GetRobotRequiredStateResponse::unsafe_arena_release_required_state() {
  // @@protoc_insertion_point(field_release:carbon.rtc.GetRobotRequiredStateResponse.required_state)
  
  ::carbon::rtc::RobotRequiredState* temp = required_state_;
  required_state_ = nullptr;
  return temp;
}
inline ::carbon::rtc::RobotRequiredState* GetRobotRequiredStateResponse::_internal_mutable_required_state() {
  
  if (required_state_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::rtc::RobotRequiredState>(GetArenaForAllocation());
    required_state_ = p;
  }
  return required_state_;
}
inline ::carbon::rtc::RobotRequiredState* GetRobotRequiredStateResponse::mutable_required_state() {
  ::carbon::rtc::RobotRequiredState* _msg = _internal_mutable_required_state();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.GetRobotRequiredStateResponse.required_state)
  return _msg;
}
inline void GetRobotRequiredStateResponse::set_allocated_required_state(::carbon::rtc::RobotRequiredState* required_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete required_state_;
  }
  if (required_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::RobotRequiredState>::GetOwningArena(required_state);
    if (message_arena != submessage_arena) {
      required_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, required_state, submessage_arena);
    }
    
  } else {
    
  }
  required_state_ = required_state;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.GetRobotRequiredStateResponse.required_state)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace rtc
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::rtc::HHStateStatus> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::rtc::HHStateStatus>() {
  return ::carbon::rtc::HHStateStatus_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2frtc_2fhh_2eproto

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/rtc/hh.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/rtc/hh.proto',
  package='carbon.rtc',
  syntax='proto3',
  serialized_options=b'Z\tproto/rtc',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x12proto/rtc/hh.proto\x12\ncarbon.rtc\x1a\x1bgoogle/protobuf/empty.proto\"+\n\x12RobotRequiredState\x12\x15\n\rstopped_state\x18\x01 \x01(\x08\";\n\x0fRobotStatusInfo\x12(\n\x05state\x18\x01 \x01(\x0e\x32\x19.carbon.rtc.HHStateStatus\"\x82\x01\n\x1cSetRobotRequiredStateRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x14\n\x0crobot_serial\x18\x02 \x01(\t\x12\x36\n\x0erequired_state\x18\x03 \x01(\x0b\x32\x1e.carbon.rtc.RobotRequiredState\"J\n\x1cGetRobotRequiredStateRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x14\n\x0crobot_serial\x18\x02 \x01(\t\"m\n\x1dGetRobotRequiredStateResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x36\n\x0erequired_state\x18\x02 \x01(\x0b\x32\x1e.carbon.rtc.RobotRequiredState*o\n\rHHStateStatus\x12\x0e\n\nHH_UNKNOWN\x10\x00\x12\x0f\n\x0bHH_DISABLED\x10\x01\x12\x12\n\x0eHH_OPERATIONAL\x10\x02\x12\x0e\n\nHH_STOPPED\x10\x03\x12\x0b\n\x07HH_SAFE\x10\x04\x12\x0c\n\x08HH_ESTOP\x10\x05\x32\xaf\x02\n\nRobotState\x12Y\n\x15SetRobotRequiredState\x12(.carbon.rtc.SetRobotRequiredStateRequest\x1a\x16.google.protobuf.Empty\x12k\n\x14GetNextRequiredState\x12(.carbon.rtc.GetRobotRequiredStateRequest\x1a).carbon.rtc.GetRobotRequiredStateResponse\x12Y\n\x16RobotRequirementStream\x12\x1b.carbon.rtc.RobotStatusInfo\x1a\x1e.carbon.rtc.RobotRequiredState(\x01\x30\x01\x42\x0bZ\tproto/rtcb\x06proto3'
  ,
  dependencies=[google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,])

_HHSTATESTATUS = _descriptor.EnumDescriptor(
  name='HHStateStatus',
  full_name='carbon.rtc.HHStateStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='HH_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_DISABLED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_OPERATIONAL', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_STOPPED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_SAFE', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='HH_ESTOP', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=489,
  serialized_end=600,
)
_sym_db.RegisterEnumDescriptor(_HHSTATESTATUS)

HHStateStatus = enum_type_wrapper.EnumTypeWrapper(_HHSTATESTATUS)
HH_UNKNOWN = 0
HH_DISABLED = 1
HH_OPERATIONAL = 2
HH_STOPPED = 3
HH_SAFE = 4
HH_ESTOP = 5



_ROBOTREQUIREDSTATE = _descriptor.Descriptor(
  name='RobotRequiredState',
  full_name='carbon.rtc.RobotRequiredState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='stopped_state', full_name='carbon.rtc.RobotRequiredState.stopped_state', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=63,
  serialized_end=106,
)


_ROBOTSTATUSINFO = _descriptor.Descriptor(
  name='RobotStatusInfo',
  full_name='carbon.rtc.RobotStatusInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.rtc.RobotStatusInfo.state', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=108,
  serialized_end=167,
)


_SETROBOTREQUIREDSTATEREQUEST = _descriptor.Descriptor(
  name='SetRobotRequiredStateRequest',
  full_name='carbon.rtc.SetRobotRequiredStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.rtc.SetRobotRequiredStateRequest.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_serial', full_name='carbon.rtc.SetRobotRequiredStateRequest.robot_serial', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='required_state', full_name='carbon.rtc.SetRobotRequiredStateRequest.required_state', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=170,
  serialized_end=300,
)


_GETROBOTREQUIREDSTATEREQUEST = _descriptor.Descriptor(
  name='GetRobotRequiredStateRequest',
  full_name='carbon.rtc.GetRobotRequiredStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.rtc.GetRobotRequiredStateRequest.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_serial', full_name='carbon.rtc.GetRobotRequiredStateRequest.robot_serial', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=302,
  serialized_end=376,
)


_GETROBOTREQUIREDSTATERESPONSE = _descriptor.Descriptor(
  name='GetRobotRequiredStateResponse',
  full_name='carbon.rtc.GetRobotRequiredStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.rtc.GetRobotRequiredStateResponse.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='required_state', full_name='carbon.rtc.GetRobotRequiredStateResponse.required_state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=378,
  serialized_end=487,
)

_ROBOTSTATUSINFO.fields_by_name['state'].enum_type = _HHSTATESTATUS
_SETROBOTREQUIREDSTATEREQUEST.fields_by_name['required_state'].message_type = _ROBOTREQUIREDSTATE
_GETROBOTREQUIREDSTATERESPONSE.fields_by_name['required_state'].message_type = _ROBOTREQUIREDSTATE
DESCRIPTOR.message_types_by_name['RobotRequiredState'] = _ROBOTREQUIREDSTATE
DESCRIPTOR.message_types_by_name['RobotStatusInfo'] = _ROBOTSTATUSINFO
DESCRIPTOR.message_types_by_name['SetRobotRequiredStateRequest'] = _SETROBOTREQUIREDSTATEREQUEST
DESCRIPTOR.message_types_by_name['GetRobotRequiredStateRequest'] = _GETROBOTREQUIREDSTATEREQUEST
DESCRIPTOR.message_types_by_name['GetRobotRequiredStateResponse'] = _GETROBOTREQUIREDSTATERESPONSE
DESCRIPTOR.enum_types_by_name['HHStateStatus'] = _HHSTATESTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RobotRequiredState = _reflection.GeneratedProtocolMessageType('RobotRequiredState', (_message.Message,), {
  'DESCRIPTOR' : _ROBOTREQUIREDSTATE,
  '__module__' : 'proto.rtc.hh_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.RobotRequiredState)
  })
_sym_db.RegisterMessage(RobotRequiredState)

RobotStatusInfo = _reflection.GeneratedProtocolMessageType('RobotStatusInfo', (_message.Message,), {
  'DESCRIPTOR' : _ROBOTSTATUSINFO,
  '__module__' : 'proto.rtc.hh_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.RobotStatusInfo)
  })
_sym_db.RegisterMessage(RobotStatusInfo)

SetRobotRequiredStateRequest = _reflection.GeneratedProtocolMessageType('SetRobotRequiredStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETROBOTREQUIREDSTATEREQUEST,
  '__module__' : 'proto.rtc.hh_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.SetRobotRequiredStateRequest)
  })
_sym_db.RegisterMessage(SetRobotRequiredStateRequest)

GetRobotRequiredStateRequest = _reflection.GeneratedProtocolMessageType('GetRobotRequiredStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETROBOTREQUIREDSTATEREQUEST,
  '__module__' : 'proto.rtc.hh_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GetRobotRequiredStateRequest)
  })
_sym_db.RegisterMessage(GetRobotRequiredStateRequest)

GetRobotRequiredStateResponse = _reflection.GeneratedProtocolMessageType('GetRobotRequiredStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETROBOTREQUIREDSTATERESPONSE,
  '__module__' : 'proto.rtc.hh_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.GetRobotRequiredStateResponse)
  })
_sym_db.RegisterMessage(GetRobotRequiredStateResponse)


DESCRIPTOR._options = None

_ROBOTSTATE = _descriptor.ServiceDescriptor(
  name='RobotState',
  full_name='carbon.rtc.RobotState',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=603,
  serialized_end=906,
  methods=[
  _descriptor.MethodDescriptor(
    name='SetRobotRequiredState',
    full_name='carbon.rtc.RobotState.SetRobotRequiredState',
    index=0,
    containing_service=None,
    input_type=_SETROBOTREQUIREDSTATEREQUEST,
    output_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextRequiredState',
    full_name='carbon.rtc.RobotState.GetNextRequiredState',
    index=1,
    containing_service=None,
    input_type=_GETROBOTREQUIREDSTATEREQUEST,
    output_type=_GETROBOTREQUIREDSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='RobotRequirementStream',
    full_name='carbon.rtc.RobotState.RobotRequirementStream',
    index=2,
    containing_service=None,
    input_type=_ROBOTSTATUSINFO,
    output_type=_ROBOTREQUIREDSTATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_ROBOTSTATE)

DESCRIPTOR.services_by_name['RobotState'] = _ROBOTSTATE

# @@protoc_insertion_point(module_scope)

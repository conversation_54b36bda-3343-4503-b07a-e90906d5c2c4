"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

HHStateStatusValue = typing___NewType('HHStateStatusValue', builtin___int)
type___HHStateStatusValue = HHStateStatusValue
HHStateStatus: _HHStateStatus
class _HHStateStatus(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[HHStateStatusValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    HH_UNKNOWN = typing___cast(HHStateStatusValue, 0)
    HH_DISABLED = typing___cast(HHStateStatusValue, 1)
    HH_OPERATIONAL = typing___cast(HHStateStatusValue, 2)
    HH_STOPPED = typing___cast(HHStateStatusValue, 3)
    HH_SAFE = typing___cast(HHStateStatusValue, 4)
    HH_ESTOP = typing___cast(HHStateStatusValue, 5)
HH_UNKNOWN = typing___cast(HHStateStatusValue, 0)
HH_DISABLED = typing___cast(HHStateStatusValue, 1)
HH_OPERATIONAL = typing___cast(HHStateStatusValue, 2)
HH_STOPPED = typing___cast(HHStateStatusValue, 3)
HH_SAFE = typing___cast(HHStateStatusValue, 4)
HH_ESTOP = typing___cast(HHStateStatusValue, 5)

class RobotRequiredState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    stopped_state: builtin___bool = ...

    def __init__(self,
        *,
        stopped_state : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"stopped_state",b"stopped_state"]) -> None: ...
type___RobotRequiredState = RobotRequiredState

class RobotStatusInfo(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    state: type___HHStateStatusValue = ...

    def __init__(self,
        *,
        state : typing___Optional[type___HHStateStatusValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"state",b"state"]) -> None: ...
type___RobotStatusInfo = RobotStatusInfo

class SetRobotRequiredStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    robot_serial: typing___Text = ...

    @property
    def required_state(self) -> type___RobotRequiredState: ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        robot_serial : typing___Optional[typing___Text] = None,
        required_state : typing___Optional[type___RobotRequiredState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"required_state",b"required_state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"required_state",b"required_state",u"robot_serial",b"robot_serial",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___SetRobotRequiredStateRequest = SetRobotRequiredStateRequest

class GetRobotRequiredStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    robot_serial: typing___Text = ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        robot_serial : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"robot_serial",b"robot_serial",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetRobotRequiredStateRequest = GetRobotRequiredStateRequest

class GetRobotRequiredStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...

    @property
    def required_state(self) -> type___RobotRequiredState: ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        required_state : typing___Optional[type___RobotRequiredState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"required_state",b"required_state"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"required_state",b"required_state",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
type___GetRobotRequiredStateResponse = GetRobotRequiredStateResponse

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/rtc/jobs.proto

#include "proto/rtc/jobs.pb.h"
#include "proto/rtc/jobs.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace rtc {

static const char* JobService_method_names[] = {
  "/carbon.rtc.JobService/CreateIntervention",
  "/carbon.rtc.JobService/GetActiveTask",
  "/carbon.rtc.JobService/GetTask",
  "/carbon.rtc.JobService/GetNextActiveObjective",
  "/carbon.rtc.JobService/UpdateTask",
};

std::unique_ptr< JobService::Stub> JobService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< JobService::Stub> stub(new JobService::Stub(channel, options));
  return stub;
}

JobService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_CreateIntervention_(JobService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetActiveTask_(JobService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTask_(JobService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextActiveObjective_(JobService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UpdateTask_(JobService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status JobService::Stub::CreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::carbon::rtc::CreateInterventionResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::CreateInterventionRequest, ::carbon::rtc::CreateInterventionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CreateIntervention_, context, request, response);
}

void JobService::Stub::async::CreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest* request, ::carbon::rtc::CreateInterventionResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::CreateInterventionRequest, ::carbon::rtc::CreateInterventionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CreateIntervention_, context, request, response, std::move(f));
}

void JobService::Stub::async::CreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest* request, ::carbon::rtc::CreateInterventionResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CreateIntervention_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::CreateInterventionResponse>* JobService::Stub::PrepareAsyncCreateInterventionRaw(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc::CreateInterventionResponse, ::carbon::rtc::CreateInterventionRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CreateIntervention_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::CreateInterventionResponse>* JobService::Stub::AsyncCreateInterventionRaw(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCreateInterventionRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobService::Stub::GetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::carbon::rtc::GetActiveTaskResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::GetActiveTaskRequest, ::carbon::rtc::GetActiveTaskResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetActiveTask_, context, request, response);
}

void JobService::Stub::async::GetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest* request, ::carbon::rtc::GetActiveTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::GetActiveTaskRequest, ::carbon::rtc::GetActiveTaskResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetActiveTask_, context, request, response, std::move(f));
}

void JobService::Stub::async::GetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest* request, ::carbon::rtc::GetActiveTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetActiveTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetActiveTaskResponse>* JobService::Stub::PrepareAsyncGetActiveTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc::GetActiveTaskResponse, ::carbon::rtc::GetActiveTaskRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetActiveTask_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetActiveTaskResponse>* JobService::Stub::AsyncGetActiveTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetActiveTaskRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobService::Stub::GetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::carbon::rtc::GetTaskResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::GetTaskRequest, ::carbon::rtc::GetTaskResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTask_, context, request, response);
}

void JobService::Stub::async::GetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest* request, ::carbon::rtc::GetTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::GetTaskRequest, ::carbon::rtc::GetTaskResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTask_, context, request, response, std::move(f));
}

void JobService::Stub::async::GetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest* request, ::carbon::rtc::GetTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetTaskResponse>* JobService::Stub::PrepareAsyncGetTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc::GetTaskResponse, ::carbon::rtc::GetTaskRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTask_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetTaskResponse>* JobService::Stub::AsyncGetTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTaskRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobService::Stub::GetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::carbon::rtc::GetNextActiveObjectiveResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::GetNextActiveObjectiveRequest, ::carbon::rtc::GetNextActiveObjectiveResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextActiveObjective_, context, request, response);
}

void JobService::Stub::async::GetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::carbon::rtc::GetNextActiveObjectiveResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::GetNextActiveObjectiveRequest, ::carbon::rtc::GetNextActiveObjectiveResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveObjective_, context, request, response, std::move(f));
}

void JobService::Stub::async::GetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::carbon::rtc::GetNextActiveObjectiveResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveObjective_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetNextActiveObjectiveResponse>* JobService::Stub::PrepareAsyncGetNextActiveObjectiveRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc::GetNextActiveObjectiveResponse, ::carbon::rtc::GetNextActiveObjectiveRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextActiveObjective_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetNextActiveObjectiveResponse>* JobService::Stub::AsyncGetNextActiveObjectiveRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextActiveObjectiveRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobService::Stub::UpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::carbon::rtc::UpdateTaskResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::UpdateTaskRequest, ::carbon::rtc::UpdateTaskResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UpdateTask_, context, request, response);
}

void JobService::Stub::async::UpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest* request, ::carbon::rtc::UpdateTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::UpdateTaskRequest, ::carbon::rtc::UpdateTaskResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateTask_, context, request, response, std::move(f));
}

void JobService::Stub::async::UpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest* request, ::carbon::rtc::UpdateTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::UpdateTaskResponse>* JobService::Stub::PrepareAsyncUpdateTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc::UpdateTaskResponse, ::carbon::rtc::UpdateTaskRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UpdateTask_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::UpdateTaskResponse>* JobService::Stub::AsyncUpdateTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUpdateTaskRaw(context, request, cq);
  result->StartCall();
  return result;
}

JobService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobService::Service, ::carbon::rtc::CreateInterventionRequest, ::carbon::rtc::CreateInterventionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::CreateInterventionRequest* req,
             ::carbon::rtc::CreateInterventionResponse* resp) {
               return service->CreateIntervention(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobService::Service, ::carbon::rtc::GetActiveTaskRequest, ::carbon::rtc::GetActiveTaskResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::GetActiveTaskRequest* req,
             ::carbon::rtc::GetActiveTaskResponse* resp) {
               return service->GetActiveTask(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobService::Service, ::carbon::rtc::GetTaskRequest, ::carbon::rtc::GetTaskResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::GetTaskRequest* req,
             ::carbon::rtc::GetTaskResponse* resp) {
               return service->GetTask(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobService::Service, ::carbon::rtc::GetNextActiveObjectiveRequest, ::carbon::rtc::GetNextActiveObjectiveResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::GetNextActiveObjectiveRequest* req,
             ::carbon::rtc::GetNextActiveObjectiveResponse* resp) {
               return service->GetNextActiveObjective(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobService::Service, ::carbon::rtc::UpdateTaskRequest, ::carbon::rtc::UpdateTaskResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::UpdateTaskRequest* req,
             ::carbon::rtc::UpdateTaskResponse* resp) {
               return service->UpdateTask(ctx, req, resp);
             }, this)));
}

JobService::Service::~Service() {
}

::grpc::Status JobService::Service::CreateIntervention(::grpc::ServerContext* context, const ::carbon::rtc::CreateInterventionRequest* request, ::carbon::rtc::CreateInterventionResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobService::Service::GetActiveTask(::grpc::ServerContext* context, const ::carbon::rtc::GetActiveTaskRequest* request, ::carbon::rtc::GetActiveTaskResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobService::Service::GetTask(::grpc::ServerContext* context, const ::carbon::rtc::GetTaskRequest* request, ::carbon::rtc::GetTaskResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobService::Service::GetNextActiveObjective(::grpc::ServerContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::carbon::rtc::GetNextActiveObjectiveResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobService::Service::UpdateTask(::grpc::ServerContext* context, const ::carbon::rtc::UpdateTaskRequest* request, ::carbon::rtc::UpdateTaskResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace rtc


# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/rtc/broadcaster.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/rtc/broadcaster.proto',
  package='carbon.rtc',
  syntax='proto3',
  serialized_options=b'Z\tproto/rtc',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1bproto/rtc/broadcaster.proto\x12\ncarbon.rtc\")\n\nAuthStatus\x12\x0c\n\x04read\x18\x01 \x01(\x08\x12\r\n\x05write\x18\x02 \x01(\x08\"K\n\nRtcMessage\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03msg\x18\x02 \x01(\x0c\x12$\n\x04\x61uth\x18\x03 \x01(\x0b\x32\x16.carbon.rtc.AuthStatus\"\x1b\n\x0cSignalingMsg\x12\x0b\n\x03msg\x18\x01 \x01(\x0c\"\x13\n\x11StreamListRequest\"\x82\x01\n\x12StreamListResponse\x12<\n\x07streams\x18\x01 \x03(\x0b\x32+.carbon.rtc.StreamListResponse.StreamsEntry\x1a.\n\x0cStreamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x32\xec\x01\n\x0b\x42roadcaster\x12@\n\nMessageBus\x12\x16.carbon.rtc.RtcMessage\x1a\x16.carbon.rtc.RtcMessage(\x01\x30\x01\x12K\n\x11LocalSignalServer\x12\x18.carbon.rtc.SignalingMsg\x1a\x18.carbon.rtc.SignalingMsg(\x01\x30\x01\x12N\n\rGetStreamList\x12\x1d.carbon.rtc.StreamListRequest\x1a\x1e.carbon.rtc.StreamListResponseB\x0bZ\tproto/rtcb\x06proto3'
)




_AUTHSTATUS = _descriptor.Descriptor(
  name='AuthStatus',
  full_name='carbon.rtc.AuthStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='read', full_name='carbon.rtc.AuthStatus.read', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='write', full_name='carbon.rtc.AuthStatus.write', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=43,
  serialized_end=84,
)


_RTCMESSAGE = _descriptor.Descriptor(
  name='RtcMessage',
  full_name='carbon.rtc.RtcMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.rtc.RtcMessage.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='msg', full_name='carbon.rtc.RtcMessage.msg', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='auth', full_name='carbon.rtc.RtcMessage.auth', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=86,
  serialized_end=161,
)


_SIGNALINGMSG = _descriptor.Descriptor(
  name='SignalingMsg',
  full_name='carbon.rtc.SignalingMsg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='carbon.rtc.SignalingMsg.msg', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=163,
  serialized_end=190,
)


_STREAMLISTREQUEST = _descriptor.Descriptor(
  name='StreamListRequest',
  full_name='carbon.rtc.StreamListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=192,
  serialized_end=211,
)


_STREAMLISTRESPONSE_STREAMSENTRY = _descriptor.Descriptor(
  name='StreamsEntry',
  full_name='carbon.rtc.StreamListResponse.StreamsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.rtc.StreamListResponse.StreamsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.rtc.StreamListResponse.StreamsEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=298,
  serialized_end=344,
)

_STREAMLISTRESPONSE = _descriptor.Descriptor(
  name='StreamListResponse',
  full_name='carbon.rtc.StreamListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='streams', full_name='carbon.rtc.StreamListResponse.streams', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_STREAMLISTRESPONSE_STREAMSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=214,
  serialized_end=344,
)

_RTCMESSAGE.fields_by_name['auth'].message_type = _AUTHSTATUS
_STREAMLISTRESPONSE_STREAMSENTRY.containing_type = _STREAMLISTRESPONSE
_STREAMLISTRESPONSE.fields_by_name['streams'].message_type = _STREAMLISTRESPONSE_STREAMSENTRY
DESCRIPTOR.message_types_by_name['AuthStatus'] = _AUTHSTATUS
DESCRIPTOR.message_types_by_name['RtcMessage'] = _RTCMESSAGE
DESCRIPTOR.message_types_by_name['SignalingMsg'] = _SIGNALINGMSG
DESCRIPTOR.message_types_by_name['StreamListRequest'] = _STREAMLISTREQUEST
DESCRIPTOR.message_types_by_name['StreamListResponse'] = _STREAMLISTRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AuthStatus = _reflection.GeneratedProtocolMessageType('AuthStatus', (_message.Message,), {
  'DESCRIPTOR' : _AUTHSTATUS,
  '__module__' : 'proto.rtc.broadcaster_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.AuthStatus)
  })
_sym_db.RegisterMessage(AuthStatus)

RtcMessage = _reflection.GeneratedProtocolMessageType('RtcMessage', (_message.Message,), {
  'DESCRIPTOR' : _RTCMESSAGE,
  '__module__' : 'proto.rtc.broadcaster_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.RtcMessage)
  })
_sym_db.RegisterMessage(RtcMessage)

SignalingMsg = _reflection.GeneratedProtocolMessageType('SignalingMsg', (_message.Message,), {
  'DESCRIPTOR' : _SIGNALINGMSG,
  '__module__' : 'proto.rtc.broadcaster_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.SignalingMsg)
  })
_sym_db.RegisterMessage(SignalingMsg)

StreamListRequest = _reflection.GeneratedProtocolMessageType('StreamListRequest', (_message.Message,), {
  'DESCRIPTOR' : _STREAMLISTREQUEST,
  '__module__' : 'proto.rtc.broadcaster_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.StreamListRequest)
  })
_sym_db.RegisterMessage(StreamListRequest)

StreamListResponse = _reflection.GeneratedProtocolMessageType('StreamListResponse', (_message.Message,), {

  'StreamsEntry' : _reflection.GeneratedProtocolMessageType('StreamsEntry', (_message.Message,), {
    'DESCRIPTOR' : _STREAMLISTRESPONSE_STREAMSENTRY,
    '__module__' : 'proto.rtc.broadcaster_pb2'
    # @@protoc_insertion_point(class_scope:carbon.rtc.StreamListResponse.StreamsEntry)
    })
  ,
  'DESCRIPTOR' : _STREAMLISTRESPONSE,
  '__module__' : 'proto.rtc.broadcaster_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.StreamListResponse)
  })
_sym_db.RegisterMessage(StreamListResponse)
_sym_db.RegisterMessage(StreamListResponse.StreamsEntry)


DESCRIPTOR._options = None
_STREAMLISTRESPONSE_STREAMSENTRY._options = None

_BROADCASTER = _descriptor.ServiceDescriptor(
  name='Broadcaster',
  full_name='carbon.rtc.Broadcaster',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=347,
  serialized_end=583,
  methods=[
  _descriptor.MethodDescriptor(
    name='MessageBus',
    full_name='carbon.rtc.Broadcaster.MessageBus',
    index=0,
    containing_service=None,
    input_type=_RTCMESSAGE,
    output_type=_RTCMESSAGE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='LocalSignalServer',
    full_name='carbon.rtc.Broadcaster.LocalSignalServer',
    index=1,
    containing_service=None,
    input_type=_SIGNALINGMSG,
    output_type=_SIGNALINGMSG,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetStreamList',
    full_name='carbon.rtc.Broadcaster.GetStreamList',
    index=2,
    containing_service=None,
    input_type=_STREAMLISTREQUEST,
    output_type=_STREAMLISTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_BROADCASTER)

DESCRIPTOR.services_by_name['Broadcaster'] = _BROADCASTER

# @@protoc_insertion_point(module_scope)

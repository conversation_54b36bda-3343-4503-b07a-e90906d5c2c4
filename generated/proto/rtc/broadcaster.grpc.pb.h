// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/rtc/broadcaster.proto
#ifndef GRPC_proto_2frtc_2fbroadcaster_2eproto__INCLUDED
#define GRPC_proto_2frtc_2fbroadcaster_2eproto__INCLUDED

#include "proto/rtc/broadcaster.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace rtc {

class Broadcaster final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.rtc.Broadcaster";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>> MessageBus(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>>(MessageBusRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>> AsyncMessageBus(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>>(AsyncMessageBusRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>> PrepareAsyncMessageBus(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>>(PrepareAsyncMessageBusRaw(context, cq));
    }
    // Note requires metadata field data_channel to be set
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>> LocalSignalServer(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>>(LocalSignalServerRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>> AsyncLocalSignalServer(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>>(AsyncLocalSignalServerRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>> PrepareAsyncLocalSignalServer(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>>(PrepareAsyncLocalSignalServerRaw(context, cq));
    }
    virtual ::grpc::Status GetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::carbon::rtc::StreamListResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::StreamListResponse>> AsyncGetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::StreamListResponse>>(AsyncGetStreamListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::StreamListResponse>> PrepareAsyncGetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::StreamListResponse>>(PrepareAsyncGetStreamListRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void MessageBus(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::carbon::rtc::RtcMessage,::carbon::rtc::RtcMessage>* reactor) = 0;
      // Note requires metadata field data_channel to be set
      virtual void LocalSignalServer(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::carbon::rtc::SignalingMsg,::carbon::rtc::SignalingMsg>* reactor) = 0;
      virtual void GetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest* request, ::carbon::rtc::StreamListResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest* request, ::carbon::rtc::StreamListResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientReaderWriterInterface< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* MessageBusRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* AsyncMessageBusRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* PrepareAsyncMessageBusRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderWriterInterface< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* LocalSignalServerRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* AsyncLocalSignalServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* PrepareAsyncLocalSignalServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::StreamListResponse>* AsyncGetStreamListRaw(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::StreamListResponse>* PrepareAsyncGetStreamListRaw(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    std::unique_ptr< ::grpc::ClientReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>> MessageBus(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>>(MessageBusRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>> AsyncMessageBus(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>>(AsyncMessageBusRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>> PrepareAsyncMessageBus(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>>(PrepareAsyncMessageBusRaw(context, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>> LocalSignalServer(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>>(LocalSignalServerRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>> AsyncLocalSignalServer(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>>(AsyncLocalSignalServerRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>> PrepareAsyncLocalSignalServer(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>>(PrepareAsyncLocalSignalServerRaw(context, cq));
    }
    ::grpc::Status GetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::carbon::rtc::StreamListResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::StreamListResponse>> AsyncGetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::StreamListResponse>>(AsyncGetStreamListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::StreamListResponse>> PrepareAsyncGetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::StreamListResponse>>(PrepareAsyncGetStreamListRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void MessageBus(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::carbon::rtc::RtcMessage,::carbon::rtc::RtcMessage>* reactor) override;
      void LocalSignalServer(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::carbon::rtc::SignalingMsg,::carbon::rtc::SignalingMsg>* reactor) override;
      void GetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest* request, ::carbon::rtc::StreamListResponse* response, std::function<void(::grpc::Status)>) override;
      void GetStreamList(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest* request, ::carbon::rtc::StreamListResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* MessageBusRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* AsyncMessageBusRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* PrepareAsyncMessageBusRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* LocalSignalServerRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* AsyncLocalSignalServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* PrepareAsyncLocalSignalServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::StreamListResponse>* AsyncGetStreamListRaw(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::StreamListResponse>* PrepareAsyncGetStreamListRaw(::grpc::ClientContext* context, const ::carbon::rtc::StreamListRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_MessageBus_;
    const ::grpc::internal::RpcMethod rpcmethod_LocalSignalServer_;
    const ::grpc::internal::RpcMethod rpcmethod_GetStreamList_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status MessageBus(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* stream);
    // Note requires metadata field data_channel to be set
    virtual ::grpc::Status LocalSignalServer(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* stream);
    virtual ::grpc::Status GetStreamList(::grpc::ServerContext* context, const ::carbon::rtc::StreamListRequest* request, ::carbon::rtc::StreamListResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_MessageBus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_MessageBus() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_MessageBus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MessageBus(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMessageBus(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(0, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_LocalSignalServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_LocalSignalServer() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_LocalSignalServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LocalSignalServer(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLocalSignalServer(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(1, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetStreamList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetStreamList() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetStreamList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetStreamList(::grpc::ServerContext* /*context*/, const ::carbon::rtc::StreamListRequest* /*request*/, ::carbon::rtc::StreamListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetStreamList(::grpc::ServerContext* context, ::carbon::rtc::StreamListRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc::StreamListResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_MessageBus<WithAsyncMethod_LocalSignalServer<WithAsyncMethod_GetStreamList<Service > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_MessageBus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_MessageBus() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackBidiHandler< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>(
            [this](
                   ::grpc::CallbackServerContext* context) { return this->MessageBus(context); }));
    }
    ~WithCallbackMethod_MessageBus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MessageBus(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerBidiReactor< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* MessageBus(
      ::grpc::CallbackServerContext* /*context*/)
      { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_LocalSignalServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_LocalSignalServer() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackBidiHandler< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>(
            [this](
                   ::grpc::CallbackServerContext* context) { return this->LocalSignalServer(context); }));
    }
    ~WithCallbackMethod_LocalSignalServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LocalSignalServer(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerBidiReactor< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* LocalSignalServer(
      ::grpc::CallbackServerContext* /*context*/)
      { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetStreamList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetStreamList() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::StreamListRequest, ::carbon::rtc::StreamListResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::StreamListRequest* request, ::carbon::rtc::StreamListResponse* response) { return this->GetStreamList(context, request, response); }));}
    void SetMessageAllocatorFor_GetStreamList(
        ::grpc::MessageAllocator< ::carbon::rtc::StreamListRequest, ::carbon::rtc::StreamListResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::StreamListRequest, ::carbon::rtc::StreamListResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetStreamList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetStreamList(::grpc::ServerContext* /*context*/, const ::carbon::rtc::StreamListRequest* /*request*/, ::carbon::rtc::StreamListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetStreamList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::StreamListRequest* /*request*/, ::carbon::rtc::StreamListResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_MessageBus<WithCallbackMethod_LocalSignalServer<WithCallbackMethod_GetStreamList<Service > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_MessageBus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_MessageBus() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_MessageBus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MessageBus(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_LocalSignalServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_LocalSignalServer() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_LocalSignalServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LocalSignalServer(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetStreamList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetStreamList() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetStreamList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetStreamList(::grpc::ServerContext* /*context*/, const ::carbon::rtc::StreamListRequest* /*request*/, ::carbon::rtc::StreamListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_MessageBus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_MessageBus() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_MessageBus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MessageBus(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMessageBus(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(0, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_LocalSignalServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_LocalSignalServer() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_LocalSignalServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LocalSignalServer(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLocalSignalServer(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(1, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetStreamList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetStreamList() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetStreamList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetStreamList(::grpc::ServerContext* /*context*/, const ::carbon::rtc::StreamListRequest* /*request*/, ::carbon::rtc::StreamListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetStreamList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_MessageBus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_MessageBus() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackBidiHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context) { return this->MessageBus(context); }));
    }
    ~WithRawCallbackMethod_MessageBus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MessageBus(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RtcMessage, ::carbon::rtc::RtcMessage>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerBidiReactor< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* MessageBus(
      ::grpc::CallbackServerContext* /*context*/)
      { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_LocalSignalServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_LocalSignalServer() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackBidiHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context) { return this->LocalSignalServer(context); }));
    }
    ~WithRawCallbackMethod_LocalSignalServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LocalSignalServer(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::SignalingMsg, ::carbon::rtc::SignalingMsg>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerBidiReactor< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* LocalSignalServer(
      ::grpc::CallbackServerContext* /*context*/)
      { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetStreamList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetStreamList() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetStreamList(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetStreamList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetStreamList(::grpc::ServerContext* /*context*/, const ::carbon::rtc::StreamListRequest* /*request*/, ::carbon::rtc::StreamListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetStreamList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetStreamList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetStreamList() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::StreamListRequest, ::carbon::rtc::StreamListResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::StreamListRequest, ::carbon::rtc::StreamListResponse>* streamer) {
                       return this->StreamedGetStreamList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetStreamList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetStreamList(::grpc::ServerContext* /*context*/, const ::carbon::rtc::StreamListRequest* /*request*/, ::carbon::rtc::StreamListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetStreamList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::StreamListRequest,::carbon::rtc::StreamListResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetStreamList<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetStreamList<Service > StreamedService;
};

}  // namespace rtc
}  // namespace carbon


#endif  // GRPC_proto_2frtc_2fbroadcaster_2eproto__INCLUDED

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from generated.proto.rtc import hh_pb2 as proto_dot_rtc_dot_hh__pb2


class RobotStateStub(object):
    """RobotState service is for informing the robot about cloud side state
    requirements.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetRobotRequiredState = channel.unary_unary(
                '/carbon.rtc.RobotState/SetRobotRequiredState',
                request_serializer=proto_dot_rtc_dot_hh__pb2.SetRobotRequiredStateRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.GetNextRequiredState = channel.unary_unary(
                '/carbon.rtc.RobotState/GetNextRequiredState',
                request_serializer=proto_dot_rtc_dot_hh__pb2.GetRobotRequiredStateRequest.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_hh__pb2.GetRobotRequiredStateResponse.FromString,
                )
        self.RobotRequirementStream = channel.stream_stream(
                '/carbon.rtc.RobotState/RobotRequirementStream',
                request_serializer=proto_dot_rtc_dot_hh__pb2.RobotStatusInfo.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_hh__pb2.RobotRequiredState.FromString,
                )


class RobotStateServicer(object):
    """RobotState service is for informing the robot about cloud side state
    requirements.
    """

    def SetRobotRequiredState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextRequiredState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RobotRequirementStream(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RobotStateServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetRobotRequiredState': grpc.unary_unary_rpc_method_handler(
                    servicer.SetRobotRequiredState,
                    request_deserializer=proto_dot_rtc_dot_hh__pb2.SetRobotRequiredStateRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'GetNextRequiredState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextRequiredState,
                    request_deserializer=proto_dot_rtc_dot_hh__pb2.GetRobotRequiredStateRequest.FromString,
                    response_serializer=proto_dot_rtc_dot_hh__pb2.GetRobotRequiredStateResponse.SerializeToString,
            ),
            'RobotRequirementStream': grpc.stream_stream_rpc_method_handler(
                    servicer.RobotRequirementStream,
                    request_deserializer=proto_dot_rtc_dot_hh__pb2.RobotStatusInfo.FromString,
                    response_serializer=proto_dot_rtc_dot_hh__pb2.RobotRequiredState.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.rtc.RobotState', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class RobotState(object):
    """RobotState service is for informing the robot about cloud side state
    requirements.
    """

    @staticmethod
    def SetRobotRequiredState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.RobotState/SetRobotRequiredState',
            proto_dot_rtc_dot_hh__pb2.SetRobotRequiredStateRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextRequiredState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.RobotState/GetNextRequiredState',
            proto_dot_rtc_dot_hh__pb2.GetRobotRequiredStateRequest.SerializeToString,
            proto_dot_rtc_dot_hh__pb2.GetRobotRequiredStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RobotRequirementStream(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/carbon.rtc.RobotState/RobotRequirementStream',
            proto_dot_rtc_dot_hh__pb2.RobotStatusInfo.SerializeToString,
            proto_dot_rtc_dot_hh__pb2.RobotRequiredState.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/rtc/location_history.proto
#ifndef GRPC_proto_2frtc_2flocation_5fhistory_2eproto__INCLUDED
#define GRPC_proto_2frtc_2flocation_5fhistory_2eproto__INCLUDED

#include "proto/rtc/location_history.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace rtc {

class LocationHistory final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.rtc.LocationHistory";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status LogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::google::protobuf::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>> AsyncLogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>>(AsyncLogLocationHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>> PrepareAsyncLogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>>(PrepareAsyncLogLocationHistoryRaw(context, request, cq));
    }
    virtual ::grpc::Status ListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::carbon::rtc::ListRobotsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListRobotsResponse>> AsyncListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListRobotsResponse>>(AsyncListRobotsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListRobotsResponse>> PrepareAsyncListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListRobotsResponse>>(PrepareAsyncListRobotsRaw(context, request, cq));
    }
    virtual ::grpc::Status ListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::carbon::rtc::ListLocationHistoryResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListLocationHistoryResponse>> AsyncListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListLocationHistoryResponse>>(AsyncListLocationHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListLocationHistoryResponse>> PrepareAsyncListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListLocationHistoryResponse>>(PrepareAsyncListLocationHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientWriterInterface< ::carbon::rtc::LocationHistoryRecord>> StreamLocation(::grpc::ClientContext* context, ::google::protobuf::Empty* response) {
      return std::unique_ptr< ::grpc::ClientWriterInterface< ::carbon::rtc::LocationHistoryRecord>>(StreamLocationRaw(context, response));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::carbon::rtc::LocationHistoryRecord>> AsyncStreamLocation(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::carbon::rtc::LocationHistoryRecord>>(AsyncStreamLocationRaw(context, response, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::carbon::rtc::LocationHistoryRecord>> PrepareAsyncStreamLocation(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::carbon::rtc::LocationHistoryRecord>>(PrepareAsyncStreamLocationRaw(context, response, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void LogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest* request, ::google::protobuf::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void LogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest* request, ::google::protobuf::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest* request, ::carbon::rtc::ListRobotsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest* request, ::carbon::rtc::ListRobotsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest* request, ::carbon::rtc::ListLocationHistoryResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest* request, ::carbon::rtc::ListLocationHistoryResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StreamLocation(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::ClientWriteReactor< ::carbon::rtc::LocationHistoryRecord>* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>* AsyncLogLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>* PrepareAsyncLogLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListRobotsResponse>* AsyncListRobotsRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListRobotsResponse>* PrepareAsyncListRobotsRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListLocationHistoryResponse>* AsyncListLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::ListLocationHistoryResponse>* PrepareAsyncListLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientWriterInterface< ::carbon::rtc::LocationHistoryRecord>* StreamLocationRaw(::grpc::ClientContext* context, ::google::protobuf::Empty* response) = 0;
    virtual ::grpc::ClientAsyncWriterInterface< ::carbon::rtc::LocationHistoryRecord>* AsyncStreamLocationRaw(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncWriterInterface< ::carbon::rtc::LocationHistoryRecord>* PrepareAsyncStreamLocationRaw(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status LogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::google::protobuf::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>> AsyncLogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>>(AsyncLogLocationHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>> PrepareAsyncLogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>>(PrepareAsyncLogLocationHistoryRaw(context, request, cq));
    }
    ::grpc::Status ListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::carbon::rtc::ListRobotsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListRobotsResponse>> AsyncListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListRobotsResponse>>(AsyncListRobotsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListRobotsResponse>> PrepareAsyncListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListRobotsResponse>>(PrepareAsyncListRobotsRaw(context, request, cq));
    }
    ::grpc::Status ListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::carbon::rtc::ListLocationHistoryResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListLocationHistoryResponse>> AsyncListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListLocationHistoryResponse>>(AsyncListLocationHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListLocationHistoryResponse>> PrepareAsyncListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListLocationHistoryResponse>>(PrepareAsyncListLocationHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientWriter< ::carbon::rtc::LocationHistoryRecord>> StreamLocation(::grpc::ClientContext* context, ::google::protobuf::Empty* response) {
      return std::unique_ptr< ::grpc::ClientWriter< ::carbon::rtc::LocationHistoryRecord>>(StreamLocationRaw(context, response));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriter< ::carbon::rtc::LocationHistoryRecord>> AsyncStreamLocation(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncWriter< ::carbon::rtc::LocationHistoryRecord>>(AsyncStreamLocationRaw(context, response, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriter< ::carbon::rtc::LocationHistoryRecord>> PrepareAsyncStreamLocation(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncWriter< ::carbon::rtc::LocationHistoryRecord>>(PrepareAsyncStreamLocationRaw(context, response, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void LogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest* request, ::google::protobuf::Empty* response, std::function<void(::grpc::Status)>) override;
      void LogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest* request, ::google::protobuf::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest* request, ::carbon::rtc::ListRobotsResponse* response, std::function<void(::grpc::Status)>) override;
      void ListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest* request, ::carbon::rtc::ListRobotsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest* request, ::carbon::rtc::ListLocationHistoryResponse* response, std::function<void(::grpc::Status)>) override;
      void ListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest* request, ::carbon::rtc::ListLocationHistoryResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StreamLocation(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::ClientWriteReactor< ::carbon::rtc::LocationHistoryRecord>* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* AsyncLogLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* PrepareAsyncLogLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListRobotsResponse>* AsyncListRobotsRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListRobotsResponse>* PrepareAsyncListRobotsRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListLocationHistoryResponse>* AsyncListLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListLocationHistoryResponse>* PrepareAsyncListLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientWriter< ::carbon::rtc::LocationHistoryRecord>* StreamLocationRaw(::grpc::ClientContext* context, ::google::protobuf::Empty* response) override;
    ::grpc::ClientAsyncWriter< ::carbon::rtc::LocationHistoryRecord>* AsyncStreamLocationRaw(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncWriter< ::carbon::rtc::LocationHistoryRecord>* PrepareAsyncStreamLocationRaw(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_LogLocationHistory_;
    const ::grpc::internal::RpcMethod rpcmethod_ListRobots_;
    const ::grpc::internal::RpcMethod rpcmethod_ListLocationHistory_;
    const ::grpc::internal::RpcMethod rpcmethod_StreamLocation_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status LogLocationHistory(::grpc::ServerContext* context, const ::carbon::rtc::LogLocationHistoryRequest* request, ::google::protobuf::Empty* response);
    virtual ::grpc::Status ListRobots(::grpc::ServerContext* context, const ::carbon::rtc::ListRobotsRequest* request, ::carbon::rtc::ListRobotsResponse* response);
    virtual ::grpc::Status ListLocationHistory(::grpc::ServerContext* context, const ::carbon::rtc::ListLocationHistoryRequest* request, ::carbon::rtc::ListLocationHistoryResponse* response);
    virtual ::grpc::Status StreamLocation(::grpc::ServerContext* context, ::grpc::ServerReader< ::carbon::rtc::LocationHistoryRecord>* reader, ::google::protobuf::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_LogLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_LogLocationHistory() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_LogLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::LogLocationHistoryRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLogLocationHistory(::grpc::ServerContext* context, ::carbon::rtc::LogLocationHistoryRequest* request, ::grpc::ServerAsyncResponseWriter< ::google::protobuf::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ListRobots : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ListRobots() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_ListRobots() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListRobots(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListRobotsRequest* /*request*/, ::carbon::rtc::ListRobotsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestListRobots(::grpc::ServerContext* context, ::carbon::rtc::ListRobotsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc::ListRobotsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ListLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ListLocationHistory() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_ListLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListLocationHistoryRequest* /*request*/, ::carbon::rtc::ListLocationHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestListLocationHistory(::grpc::ServerContext* context, ::carbon::rtc::ListLocationHistoryRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc::ListLocationHistoryResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StreamLocation : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StreamLocation() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_StreamLocation() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamLocation(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::rtc::LocationHistoryRecord>* /*reader*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamLocation(::grpc::ServerContext* context, ::grpc::ServerAsyncReader< ::google::protobuf::Empty, ::carbon::rtc::LocationHistoryRecord>* reader, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncClientStreaming(3, context, reader, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_LogLocationHistory<WithAsyncMethod_ListRobots<WithAsyncMethod_ListLocationHistory<WithAsyncMethod_StreamLocation<Service > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_LogLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_LogLocationHistory() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::LogLocationHistoryRequest, ::google::protobuf::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::LogLocationHistoryRequest* request, ::google::protobuf::Empty* response) { return this->LogLocationHistory(context, request, response); }));}
    void SetMessageAllocatorFor_LogLocationHistory(
        ::grpc::MessageAllocator< ::carbon::rtc::LogLocationHistoryRequest, ::google::protobuf::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::LogLocationHistoryRequest, ::google::protobuf::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_LogLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::LogLocationHistoryRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LogLocationHistory(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::LogLocationHistoryRequest* /*request*/, ::google::protobuf::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ListRobots : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ListRobots() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::ListRobotsRequest, ::carbon::rtc::ListRobotsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::ListRobotsRequest* request, ::carbon::rtc::ListRobotsResponse* response) { return this->ListRobots(context, request, response); }));}
    void SetMessageAllocatorFor_ListRobots(
        ::grpc::MessageAllocator< ::carbon::rtc::ListRobotsRequest, ::carbon::rtc::ListRobotsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::ListRobotsRequest, ::carbon::rtc::ListRobotsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ListRobots() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListRobots(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListRobotsRequest* /*request*/, ::carbon::rtc::ListRobotsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ListRobots(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::ListRobotsRequest* /*request*/, ::carbon::rtc::ListRobotsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ListLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ListLocationHistory() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::ListLocationHistoryRequest, ::carbon::rtc::ListLocationHistoryResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::ListLocationHistoryRequest* request, ::carbon::rtc::ListLocationHistoryResponse* response) { return this->ListLocationHistory(context, request, response); }));}
    void SetMessageAllocatorFor_ListLocationHistory(
        ::grpc::MessageAllocator< ::carbon::rtc::ListLocationHistoryRequest, ::carbon::rtc::ListLocationHistoryResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::ListLocationHistoryRequest, ::carbon::rtc::ListLocationHistoryResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ListLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListLocationHistoryRequest* /*request*/, ::carbon::rtc::ListLocationHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ListLocationHistory(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::ListLocationHistoryRequest* /*request*/, ::carbon::rtc::ListLocationHistoryResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StreamLocation : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StreamLocation() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackClientStreamingHandler< ::carbon::rtc::LocationHistoryRecord, ::google::protobuf::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, ::google::protobuf::Empty* response) { return this->StreamLocation(context, response); }));
    }
    ~WithCallbackMethod_StreamLocation() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamLocation(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::rtc::LocationHistoryRecord>* /*reader*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerReadReactor< ::carbon::rtc::LocationHistoryRecord>* StreamLocation(
      ::grpc::CallbackServerContext* /*context*/, ::google::protobuf::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_LogLocationHistory<WithCallbackMethod_ListRobots<WithCallbackMethod_ListLocationHistory<WithCallbackMethod_StreamLocation<Service > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_LogLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_LogLocationHistory() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_LogLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::LogLocationHistoryRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ListRobots : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ListRobots() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_ListRobots() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListRobots(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListRobotsRequest* /*request*/, ::carbon::rtc::ListRobotsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ListLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ListLocationHistory() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_ListLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListLocationHistoryRequest* /*request*/, ::carbon::rtc::ListLocationHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StreamLocation : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StreamLocation() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_StreamLocation() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamLocation(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::rtc::LocationHistoryRecord>* /*reader*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_LogLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_LogLocationHistory() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_LogLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::LogLocationHistoryRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLogLocationHistory(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ListRobots : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ListRobots() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_ListRobots() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListRobots(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListRobotsRequest* /*request*/, ::carbon::rtc::ListRobotsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestListRobots(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ListLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ListLocationHistory() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_ListLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListLocationHistoryRequest* /*request*/, ::carbon::rtc::ListLocationHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestListLocationHistory(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StreamLocation : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StreamLocation() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_StreamLocation() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamLocation(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::rtc::LocationHistoryRecord>* /*reader*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamLocation(::grpc::ServerContext* context, ::grpc::ServerAsyncReader< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* reader, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncClientStreaming(3, context, reader, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_LogLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_LogLocationHistory() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->LogLocationHistory(context, request, response); }));
    }
    ~WithRawCallbackMethod_LogLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LogLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::LogLocationHistoryRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LogLocationHistory(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ListRobots : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ListRobots() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ListRobots(context, request, response); }));
    }
    ~WithRawCallbackMethod_ListRobots() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListRobots(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListRobotsRequest* /*request*/, ::carbon::rtc::ListRobotsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ListRobots(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ListLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ListLocationHistory() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ListLocationHistory(context, request, response); }));
    }
    ~WithRawCallbackMethod_ListLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListLocationHistoryRequest* /*request*/, ::carbon::rtc::ListLocationHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ListLocationHistory(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StreamLocation : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StreamLocation() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackClientStreamingHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, ::grpc::ByteBuffer* response) { return this->StreamLocation(context, response); }));
    }
    ~WithRawCallbackMethod_StreamLocation() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamLocation(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::rtc::LocationHistoryRecord>* /*reader*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerReadReactor< ::grpc::ByteBuffer>* StreamLocation(
      ::grpc::CallbackServerContext* /*context*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_LogLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_LogLocationHistory() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::LogLocationHistoryRequest, ::google::protobuf::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::LogLocationHistoryRequest, ::google::protobuf::Empty>* streamer) {
                       return this->StreamedLogLocationHistory(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_LogLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status LogLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::LogLocationHistoryRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedLogLocationHistory(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::LogLocationHistoryRequest,::google::protobuf::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ListRobots : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ListRobots() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::ListRobotsRequest, ::carbon::rtc::ListRobotsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::ListRobotsRequest, ::carbon::rtc::ListRobotsResponse>* streamer) {
                       return this->StreamedListRobots(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ListRobots() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ListRobots(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListRobotsRequest* /*request*/, ::carbon::rtc::ListRobotsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedListRobots(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::ListRobotsRequest,::carbon::rtc::ListRobotsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ListLocationHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ListLocationHistory() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::ListLocationHistoryRequest, ::carbon::rtc::ListLocationHistoryResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::ListLocationHistoryRequest, ::carbon::rtc::ListLocationHistoryResponse>* streamer) {
                       return this->StreamedListLocationHistory(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ListLocationHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ListLocationHistory(::grpc::ServerContext* /*context*/, const ::carbon::rtc::ListLocationHistoryRequest* /*request*/, ::carbon::rtc::ListLocationHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedListLocationHistory(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::ListLocationHistoryRequest,::carbon::rtc::ListLocationHistoryResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_LogLocationHistory<WithStreamedUnaryMethod_ListRobots<WithStreamedUnaryMethod_ListLocationHistory<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_LogLocationHistory<WithStreamedUnaryMethod_ListRobots<WithStreamedUnaryMethod_ListLocationHistory<Service > > > StreamedService;
};

}  // namespace rtc
}  // namespace carbon


#endif  // GRPC_proto_2frtc_2flocation_5fhistory_2eproto__INCLUDED

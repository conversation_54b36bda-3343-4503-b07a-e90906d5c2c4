# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.proto.rtc import broadcaster_pb2 as proto_dot_rtc_dot_broadcaster__pb2


class BroadcasterStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.MessageBus = channel.stream_stream(
                '/carbon.rtc.Broadcaster/MessageBus',
                request_serializer=proto_dot_rtc_dot_broadcaster__pb2.RtcMessage.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_broadcaster__pb2.RtcMessage.FromString,
                )
        self.LocalSignalServer = channel.stream_stream(
                '/carbon.rtc.Broadcaster/LocalSignalServer',
                request_serializer=proto_dot_rtc_dot_broadcaster__pb2.SignalingMsg.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_broadcaster__pb2.SignalingMsg.FromString,
                )
        self.GetStreamList = channel.unary_unary(
                '/carbon.rtc.Broadcaster/GetStreamList',
                request_serializer=proto_dot_rtc_dot_broadcaster__pb2.StreamListRequest.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_broadcaster__pb2.StreamListResponse.FromString,
                )


class BroadcasterServicer(object):
    """Missing associated documentation comment in .proto file."""

    def MessageBus(self, request_iterator, context):
        """Note requires metadata field data_channel to be set
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LocalSignalServer(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStreamList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BroadcasterServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'MessageBus': grpc.stream_stream_rpc_method_handler(
                    servicer.MessageBus,
                    request_deserializer=proto_dot_rtc_dot_broadcaster__pb2.RtcMessage.FromString,
                    response_serializer=proto_dot_rtc_dot_broadcaster__pb2.RtcMessage.SerializeToString,
            ),
            'LocalSignalServer': grpc.stream_stream_rpc_method_handler(
                    servicer.LocalSignalServer,
                    request_deserializer=proto_dot_rtc_dot_broadcaster__pb2.SignalingMsg.FromString,
                    response_serializer=proto_dot_rtc_dot_broadcaster__pb2.SignalingMsg.SerializeToString,
            ),
            'GetStreamList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStreamList,
                    request_deserializer=proto_dot_rtc_dot_broadcaster__pb2.StreamListRequest.FromString,
                    response_serializer=proto_dot_rtc_dot_broadcaster__pb2.StreamListResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.rtc.Broadcaster', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Broadcaster(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def MessageBus(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/carbon.rtc.Broadcaster/MessageBus',
            proto_dot_rtc_dot_broadcaster__pb2.RtcMessage.SerializeToString,
            proto_dot_rtc_dot_broadcaster__pb2.RtcMessage.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LocalSignalServer(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/carbon.rtc.Broadcaster/LocalSignalServer',
            proto_dot_rtc_dot_broadcaster__pb2.SignalingMsg.SerializeToString,
            proto_dot_rtc_dot_broadcaster__pb2.SignalingMsg.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetStreamList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.Broadcaster/GetStreamList',
            proto_dot_rtc_dot_broadcaster__pb2.StreamListRequest.SerializeToString,
            proto_dot_rtc_dot_broadcaster__pb2.StreamListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

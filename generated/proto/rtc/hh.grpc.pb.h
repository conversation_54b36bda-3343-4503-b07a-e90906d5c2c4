// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/rtc/hh.proto
#ifndef GRPC_proto_2frtc_2fhh_2eproto__INCLUDED
#define GRPC_proto_2frtc_2fhh_2eproto__INCLUDED

#include "proto/rtc/hh.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace rtc {

// RobotState service is for informing the robot about cloud side state
// requirements.
class RobotState final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.rtc.RobotState";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status SetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::google::protobuf::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>> AsyncSetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>>(AsyncSetRobotRequiredStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>> PrepareAsyncSetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>>(PrepareAsyncSetRobotRequiredStateRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::carbon::rtc::GetRobotRequiredStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetRobotRequiredStateResponse>> AsyncGetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetRobotRequiredStateResponse>>(AsyncGetNextRequiredStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetRobotRequiredStateResponse>> PrepareAsyncGetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetRobotRequiredStateResponse>>(PrepareAsyncGetNextRequiredStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>> RobotRequirementStream(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>>(RobotRequirementStreamRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>> AsyncRobotRequirementStream(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>>(AsyncRobotRequirementStreamRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>> PrepareAsyncRobotRequirementStream(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>>(PrepareAsyncRobotRequirementStreamRaw(context, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void SetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest* request, ::google::protobuf::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest* request, ::google::protobuf::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest* request, ::carbon::rtc::GetRobotRequiredStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest* request, ::carbon::rtc::GetRobotRequiredStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void RobotRequirementStream(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::carbon::rtc::RobotStatusInfo,::carbon::rtc::RobotRequiredState>* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>* AsyncSetRobotRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::google::protobuf::Empty>* PrepareAsyncSetRobotRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetRobotRequiredStateResponse>* AsyncGetNextRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetRobotRequiredStateResponse>* PrepareAsyncGetNextRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderWriterInterface< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* RobotRequirementStreamRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* AsyncRobotRequirementStreamRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* PrepareAsyncRobotRequirementStreamRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status SetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::google::protobuf::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>> AsyncSetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>>(AsyncSetRobotRequiredStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>> PrepareAsyncSetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>>(PrepareAsyncSetRobotRequiredStateRaw(context, request, cq));
    }
    ::grpc::Status GetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::carbon::rtc::GetRobotRequiredStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetRobotRequiredStateResponse>> AsyncGetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetRobotRequiredStateResponse>>(AsyncGetNextRequiredStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetRobotRequiredStateResponse>> PrepareAsyncGetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetRobotRequiredStateResponse>>(PrepareAsyncGetNextRequiredStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>> RobotRequirementStream(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>>(RobotRequirementStreamRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>> AsyncRobotRequirementStream(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>>(AsyncRobotRequirementStreamRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>> PrepareAsyncRobotRequirementStream(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>>(PrepareAsyncRobotRequirementStreamRaw(context, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void SetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest* request, ::google::protobuf::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest* request, ::google::protobuf::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest* request, ::carbon::rtc::GetRobotRequiredStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest* request, ::carbon::rtc::GetRobotRequiredStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void RobotRequirementStream(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::carbon::rtc::RobotStatusInfo,::carbon::rtc::RobotRequiredState>* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* AsyncSetRobotRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* PrepareAsyncSetRobotRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetRobotRequiredStateResponse>* AsyncGetNextRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetRobotRequiredStateResponse>* PrepareAsyncGetNextRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* RobotRequirementStreamRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* AsyncRobotRequirementStreamRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* PrepareAsyncRobotRequirementStreamRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_SetRobotRequiredState_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextRequiredState_;
    const ::grpc::internal::RpcMethod rpcmethod_RobotRequirementStream_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status SetRobotRequiredState(::grpc::ServerContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest* request, ::google::protobuf::Empty* response);
    virtual ::grpc::Status GetNextRequiredState(::grpc::ServerContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest* request, ::carbon::rtc::GetRobotRequiredStateResponse* response);
    virtual ::grpc::Status RobotRequirementStream(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::carbon::rtc::RobotRequiredState, ::carbon::rtc::RobotStatusInfo>* stream);
  };
  template <class BaseClass>
  class WithAsyncMethod_SetRobotRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetRobotRequiredState() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_SetRobotRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRobotRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::SetRobotRequiredStateRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetRobotRequiredState(::grpc::ServerContext* context, ::carbon::rtc::SetRobotRequiredStateRequest* request, ::grpc::ServerAsyncResponseWriter< ::google::protobuf::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextRequiredState() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetRobotRequiredStateRequest* /*request*/, ::carbon::rtc::GetRobotRequiredStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextRequiredState(::grpc::ServerContext* context, ::carbon::rtc::GetRobotRequiredStateRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc::GetRobotRequiredStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_RobotRequirementStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_RobotRequirementStream() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_RobotRequirementStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RobotRequirementStream(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RobotRequiredState, ::carbon::rtc::RobotStatusInfo>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRobotRequirementStream(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::carbon::rtc::RobotRequiredState, ::carbon::rtc::RobotStatusInfo>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(2, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_SetRobotRequiredState<WithAsyncMethod_GetNextRequiredState<WithAsyncMethod_RobotRequirementStream<Service > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_SetRobotRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetRobotRequiredState() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::SetRobotRequiredStateRequest, ::google::protobuf::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest* request, ::google::protobuf::Empty* response) { return this->SetRobotRequiredState(context, request, response); }));}
    void SetMessageAllocatorFor_SetRobotRequiredState(
        ::grpc::MessageAllocator< ::carbon::rtc::SetRobotRequiredStateRequest, ::google::protobuf::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::SetRobotRequiredStateRequest, ::google::protobuf::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetRobotRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRobotRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::SetRobotRequiredStateRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetRobotRequiredState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::SetRobotRequiredStateRequest* /*request*/, ::google::protobuf::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextRequiredState() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::GetRobotRequiredStateRequest, ::carbon::rtc::GetRobotRequiredStateResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest* request, ::carbon::rtc::GetRobotRequiredStateResponse* response) { return this->GetNextRequiredState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextRequiredState(
        ::grpc::MessageAllocator< ::carbon::rtc::GetRobotRequiredStateRequest, ::carbon::rtc::GetRobotRequiredStateResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::GetRobotRequiredStateRequest, ::carbon::rtc::GetRobotRequiredStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetRobotRequiredStateRequest* /*request*/, ::carbon::rtc::GetRobotRequiredStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextRequiredState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::GetRobotRequiredStateRequest* /*request*/, ::carbon::rtc::GetRobotRequiredStateResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_RobotRequirementStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_RobotRequirementStream() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackBidiHandler< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>(
            [this](
                   ::grpc::CallbackServerContext* context) { return this->RobotRequirementStream(context); }));
    }
    ~WithCallbackMethod_RobotRequirementStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RobotRequirementStream(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RobotRequiredState, ::carbon::rtc::RobotStatusInfo>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerBidiReactor< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* RobotRequirementStream(
      ::grpc::CallbackServerContext* /*context*/)
      { return nullptr; }
  };
  typedef WithCallbackMethod_SetRobotRequiredState<WithCallbackMethod_GetNextRequiredState<WithCallbackMethod_RobotRequirementStream<Service > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_SetRobotRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetRobotRequiredState() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_SetRobotRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRobotRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::SetRobotRequiredStateRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextRequiredState() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetRobotRequiredStateRequest* /*request*/, ::carbon::rtc::GetRobotRequiredStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_RobotRequirementStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_RobotRequirementStream() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_RobotRequirementStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RobotRequirementStream(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RobotRequiredState, ::carbon::rtc::RobotStatusInfo>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetRobotRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetRobotRequiredState() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_SetRobotRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRobotRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::SetRobotRequiredStateRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetRobotRequiredState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextRequiredState() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetRobotRequiredStateRequest* /*request*/, ::carbon::rtc::GetRobotRequiredStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextRequiredState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_RobotRequirementStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_RobotRequirementStream() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_RobotRequirementStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RobotRequirementStream(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RobotRequiredState, ::carbon::rtc::RobotStatusInfo>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRobotRequirementStream(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(2, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetRobotRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetRobotRequiredState() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetRobotRequiredState(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetRobotRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRobotRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::SetRobotRequiredStateRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetRobotRequiredState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextRequiredState() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextRequiredState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetRobotRequiredStateRequest* /*request*/, ::carbon::rtc::GetRobotRequiredStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextRequiredState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_RobotRequirementStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_RobotRequirementStream() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackBidiHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context) { return this->RobotRequirementStream(context); }));
    }
    ~WithRawCallbackMethod_RobotRequirementStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RobotRequirementStream(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::carbon::rtc::RobotRequiredState, ::carbon::rtc::RobotStatusInfo>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerBidiReactor< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* RobotRequirementStream(
      ::grpc::CallbackServerContext* /*context*/)
      { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetRobotRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetRobotRequiredState() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::SetRobotRequiredStateRequest, ::google::protobuf::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::SetRobotRequiredStateRequest, ::google::protobuf::Empty>* streamer) {
                       return this->StreamedSetRobotRequiredState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetRobotRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetRobotRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::SetRobotRequiredStateRequest* /*request*/, ::google::protobuf::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetRobotRequiredState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::SetRobotRequiredStateRequest,::google::protobuf::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextRequiredState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextRequiredState() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::GetRobotRequiredStateRequest, ::carbon::rtc::GetRobotRequiredStateResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::GetRobotRequiredStateRequest, ::carbon::rtc::GetRobotRequiredStateResponse>* streamer) {
                       return this->StreamedGetNextRequiredState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextRequiredState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextRequiredState(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetRobotRequiredStateRequest* /*request*/, ::carbon::rtc::GetRobotRequiredStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextRequiredState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::GetRobotRequiredStateRequest,::carbon::rtc::GetRobotRequiredStateResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_SetRobotRequiredState<WithStreamedUnaryMethod_GetNextRequiredState<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_SetRobotRequiredState<WithStreamedUnaryMethod_GetNextRequiredState<Service > > StreamedService;
};

}  // namespace rtc
}  // namespace carbon


#endif  // GRPC_proto_2frtc_2fhh_2eproto__INCLUDED

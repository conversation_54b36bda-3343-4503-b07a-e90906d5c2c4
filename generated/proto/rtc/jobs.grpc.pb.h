// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/rtc/jobs.proto
#ifndef GRPC_proto_2frtc_2fjobs_2eproto__INCLUDED
#define GRPC_proto_2frtc_2fjobs_2eproto__INCLUDED

#include "proto/rtc/jobs.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace rtc {

class JobService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.rtc.JobService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Robot API
    virtual ::grpc::Status CreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::carbon::rtc::CreateInterventionResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::CreateInterventionResponse>> AsyncCreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::CreateInterventionResponse>>(AsyncCreateInterventionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::CreateInterventionResponse>> PrepareAsyncCreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::CreateInterventionResponse>>(PrepareAsyncCreateInterventionRaw(context, request, cq));
    }
    virtual ::grpc::Status GetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::carbon::rtc::GetActiveTaskResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetActiveTaskResponse>> AsyncGetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetActiveTaskResponse>>(AsyncGetActiveTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetActiveTaskResponse>> PrepareAsyncGetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetActiveTaskResponse>>(PrepareAsyncGetActiveTaskRaw(context, request, cq));
    }
    virtual ::grpc::Status GetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::carbon::rtc::GetTaskResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetTaskResponse>> AsyncGetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetTaskResponse>>(AsyncGetTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetTaskResponse>> PrepareAsyncGetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetTaskResponse>>(PrepareAsyncGetTaskRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::carbon::rtc::GetNextActiveObjectiveResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetNextActiveObjectiveResponse>> AsyncGetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetNextActiveObjectiveResponse>>(AsyncGetNextActiveObjectiveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetNextActiveObjectiveResponse>> PrepareAsyncGetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetNextActiveObjectiveResponse>>(PrepareAsyncGetNextActiveObjectiveRaw(context, request, cq));
    }
    virtual ::grpc::Status UpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::carbon::rtc::UpdateTaskResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::UpdateTaskResponse>> AsyncUpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::UpdateTaskResponse>>(AsyncUpdateTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::UpdateTaskResponse>> PrepareAsyncUpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::UpdateTaskResponse>>(PrepareAsyncUpdateTaskRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      // Robot API
      virtual void CreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest* request, ::carbon::rtc::CreateInterventionResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest* request, ::carbon::rtc::CreateInterventionResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest* request, ::carbon::rtc::GetActiveTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest* request, ::carbon::rtc::GetActiveTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest* request, ::carbon::rtc::GetTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest* request, ::carbon::rtc::GetTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::carbon::rtc::GetNextActiveObjectiveResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::carbon::rtc::GetNextActiveObjectiveResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void UpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest* request, ::carbon::rtc::UpdateTaskResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest* request, ::carbon::rtc::UpdateTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::CreateInterventionResponse>* AsyncCreateInterventionRaw(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::CreateInterventionResponse>* PrepareAsyncCreateInterventionRaw(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetActiveTaskResponse>* AsyncGetActiveTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetActiveTaskResponse>* PrepareAsyncGetActiveTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetTaskResponse>* AsyncGetTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetTaskResponse>* PrepareAsyncGetTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetNextActiveObjectiveResponse>* AsyncGetNextActiveObjectiveRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::GetNextActiveObjectiveResponse>* PrepareAsyncGetNextActiveObjectiveRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::UpdateTaskResponse>* AsyncUpdateTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::rtc::UpdateTaskResponse>* PrepareAsyncUpdateTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status CreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::carbon::rtc::CreateInterventionResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::CreateInterventionResponse>> AsyncCreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::CreateInterventionResponse>>(AsyncCreateInterventionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::CreateInterventionResponse>> PrepareAsyncCreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::CreateInterventionResponse>>(PrepareAsyncCreateInterventionRaw(context, request, cq));
    }
    ::grpc::Status GetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::carbon::rtc::GetActiveTaskResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetActiveTaskResponse>> AsyncGetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetActiveTaskResponse>>(AsyncGetActiveTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetActiveTaskResponse>> PrepareAsyncGetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetActiveTaskResponse>>(PrepareAsyncGetActiveTaskRaw(context, request, cq));
    }
    ::grpc::Status GetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::carbon::rtc::GetTaskResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetTaskResponse>> AsyncGetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetTaskResponse>>(AsyncGetTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetTaskResponse>> PrepareAsyncGetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetTaskResponse>>(PrepareAsyncGetTaskRaw(context, request, cq));
    }
    ::grpc::Status GetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::carbon::rtc::GetNextActiveObjectiveResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetNextActiveObjectiveResponse>> AsyncGetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetNextActiveObjectiveResponse>>(AsyncGetNextActiveObjectiveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetNextActiveObjectiveResponse>> PrepareAsyncGetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetNextActiveObjectiveResponse>>(PrepareAsyncGetNextActiveObjectiveRaw(context, request, cq));
    }
    ::grpc::Status UpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::carbon::rtc::UpdateTaskResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::UpdateTaskResponse>> AsyncUpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::UpdateTaskResponse>>(AsyncUpdateTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::UpdateTaskResponse>> PrepareAsyncUpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::rtc::UpdateTaskResponse>>(PrepareAsyncUpdateTaskRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void CreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest* request, ::carbon::rtc::CreateInterventionResponse* response, std::function<void(::grpc::Status)>) override;
      void CreateIntervention(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest* request, ::carbon::rtc::CreateInterventionResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest* request, ::carbon::rtc::GetActiveTaskResponse* response, std::function<void(::grpc::Status)>) override;
      void GetActiveTask(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest* request, ::carbon::rtc::GetActiveTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest* request, ::carbon::rtc::GetTaskResponse* response, std::function<void(::grpc::Status)>) override;
      void GetTask(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest* request, ::carbon::rtc::GetTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::carbon::rtc::GetNextActiveObjectiveResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextActiveObjective(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::carbon::rtc::GetNextActiveObjectiveResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest* request, ::carbon::rtc::UpdateTaskResponse* response, std::function<void(::grpc::Status)>) override;
      void UpdateTask(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest* request, ::carbon::rtc::UpdateTaskResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::CreateInterventionResponse>* AsyncCreateInterventionRaw(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::CreateInterventionResponse>* PrepareAsyncCreateInterventionRaw(::grpc::ClientContext* context, const ::carbon::rtc::CreateInterventionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetActiveTaskResponse>* AsyncGetActiveTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetActiveTaskResponse>* PrepareAsyncGetActiveTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetActiveTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetTaskResponse>* AsyncGetTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetTaskResponse>* PrepareAsyncGetTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetNextActiveObjectiveResponse>* AsyncGetNextActiveObjectiveRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetNextActiveObjectiveResponse>* PrepareAsyncGetNextActiveObjectiveRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::UpdateTaskResponse>* AsyncUpdateTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::rtc::UpdateTaskResponse>* PrepareAsyncUpdateTaskRaw(::grpc::ClientContext* context, const ::carbon::rtc::UpdateTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_CreateIntervention_;
    const ::grpc::internal::RpcMethod rpcmethod_GetActiveTask_;
    const ::grpc::internal::RpcMethod rpcmethod_GetTask_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextActiveObjective_;
    const ::grpc::internal::RpcMethod rpcmethod_UpdateTask_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Robot API
    virtual ::grpc::Status CreateIntervention(::grpc::ServerContext* context, const ::carbon::rtc::CreateInterventionRequest* request, ::carbon::rtc::CreateInterventionResponse* response);
    virtual ::grpc::Status GetActiveTask(::grpc::ServerContext* context, const ::carbon::rtc::GetActiveTaskRequest* request, ::carbon::rtc::GetActiveTaskResponse* response);
    virtual ::grpc::Status GetTask(::grpc::ServerContext* context, const ::carbon::rtc::GetTaskRequest* request, ::carbon::rtc::GetTaskResponse* response);
    virtual ::grpc::Status GetNextActiveObjective(::grpc::ServerContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::carbon::rtc::GetNextActiveObjectiveResponse* response);
    virtual ::grpc::Status UpdateTask(::grpc::ServerContext* context, const ::carbon::rtc::UpdateTaskRequest* request, ::carbon::rtc::UpdateTaskResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_CreateIntervention : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CreateIntervention() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_CreateIntervention() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateIntervention(::grpc::ServerContext* /*context*/, const ::carbon::rtc::CreateInterventionRequest* /*request*/, ::carbon::rtc::CreateInterventionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCreateIntervention(::grpc::ServerContext* context, ::carbon::rtc::CreateInterventionRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc::CreateInterventionResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetActiveTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetActiveTask() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetActiveTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetActiveTaskRequest* /*request*/, ::carbon::rtc::GetActiveTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetActiveTask(::grpc::ServerContext* context, ::carbon::rtc::GetActiveTaskRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc::GetActiveTaskResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetTask() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetTaskRequest* /*request*/, ::carbon::rtc::GetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTask(::grpc::ServerContext* context, ::carbon::rtc::GetTaskRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc::GetTaskResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextActiveObjective : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextActiveObjective() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_GetNextActiveObjective() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveObjective(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetNextActiveObjectiveRequest* /*request*/, ::carbon::rtc::GetNextActiveObjectiveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveObjective(::grpc::ServerContext* context, ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc::GetNextActiveObjectiveResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UpdateTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UpdateTask() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_UpdateTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::UpdateTaskRequest* /*request*/, ::carbon::rtc::UpdateTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateTask(::grpc::ServerContext* context, ::carbon::rtc::UpdateTaskRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::rtc::UpdateTaskResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_CreateIntervention<WithAsyncMethod_GetActiveTask<WithAsyncMethod_GetTask<WithAsyncMethod_GetNextActiveObjective<WithAsyncMethod_UpdateTask<Service > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_CreateIntervention : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CreateIntervention() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::CreateInterventionRequest, ::carbon::rtc::CreateInterventionResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::CreateInterventionRequest* request, ::carbon::rtc::CreateInterventionResponse* response) { return this->CreateIntervention(context, request, response); }));}
    void SetMessageAllocatorFor_CreateIntervention(
        ::grpc::MessageAllocator< ::carbon::rtc::CreateInterventionRequest, ::carbon::rtc::CreateInterventionResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::CreateInterventionRequest, ::carbon::rtc::CreateInterventionResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CreateIntervention() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateIntervention(::grpc::ServerContext* /*context*/, const ::carbon::rtc::CreateInterventionRequest* /*request*/, ::carbon::rtc::CreateInterventionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CreateIntervention(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::CreateInterventionRequest* /*request*/, ::carbon::rtc::CreateInterventionResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetActiveTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetActiveTask() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::GetActiveTaskRequest, ::carbon::rtc::GetActiveTaskResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::GetActiveTaskRequest* request, ::carbon::rtc::GetActiveTaskResponse* response) { return this->GetActiveTask(context, request, response); }));}
    void SetMessageAllocatorFor_GetActiveTask(
        ::grpc::MessageAllocator< ::carbon::rtc::GetActiveTaskRequest, ::carbon::rtc::GetActiveTaskResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::GetActiveTaskRequest, ::carbon::rtc::GetActiveTaskResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetActiveTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetActiveTaskRequest* /*request*/, ::carbon::rtc::GetActiveTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetActiveTask(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::GetActiveTaskRequest* /*request*/, ::carbon::rtc::GetActiveTaskResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetTask() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::GetTaskRequest, ::carbon::rtc::GetTaskResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::GetTaskRequest* request, ::carbon::rtc::GetTaskResponse* response) { return this->GetTask(context, request, response); }));}
    void SetMessageAllocatorFor_GetTask(
        ::grpc::MessageAllocator< ::carbon::rtc::GetTaskRequest, ::carbon::rtc::GetTaskResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::GetTaskRequest, ::carbon::rtc::GetTaskResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetTaskRequest* /*request*/, ::carbon::rtc::GetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTask(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::GetTaskRequest* /*request*/, ::carbon::rtc::GetTaskResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextActiveObjective : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextActiveObjective() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::GetNextActiveObjectiveRequest, ::carbon::rtc::GetNextActiveObjectiveResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::GetNextActiveObjectiveRequest* request, ::carbon::rtc::GetNextActiveObjectiveResponse* response) { return this->GetNextActiveObjective(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextActiveObjective(
        ::grpc::MessageAllocator< ::carbon::rtc::GetNextActiveObjectiveRequest, ::carbon::rtc::GetNextActiveObjectiveResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::GetNextActiveObjectiveRequest, ::carbon::rtc::GetNextActiveObjectiveResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextActiveObjective() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveObjective(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetNextActiveObjectiveRequest* /*request*/, ::carbon::rtc::GetNextActiveObjectiveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveObjective(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::GetNextActiveObjectiveRequest* /*request*/, ::carbon::rtc::GetNextActiveObjectiveResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UpdateTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UpdateTask() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::UpdateTaskRequest, ::carbon::rtc::UpdateTaskResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::rtc::UpdateTaskRequest* request, ::carbon::rtc::UpdateTaskResponse* response) { return this->UpdateTask(context, request, response); }));}
    void SetMessageAllocatorFor_UpdateTask(
        ::grpc::MessageAllocator< ::carbon::rtc::UpdateTaskRequest, ::carbon::rtc::UpdateTaskResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::rtc::UpdateTaskRequest, ::carbon::rtc::UpdateTaskResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UpdateTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::UpdateTaskRequest* /*request*/, ::carbon::rtc::UpdateTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateTask(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::rtc::UpdateTaskRequest* /*request*/, ::carbon::rtc::UpdateTaskResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_CreateIntervention<WithCallbackMethod_GetActiveTask<WithCallbackMethod_GetTask<WithCallbackMethod_GetNextActiveObjective<WithCallbackMethod_UpdateTask<Service > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_CreateIntervention : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CreateIntervention() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_CreateIntervention() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateIntervention(::grpc::ServerContext* /*context*/, const ::carbon::rtc::CreateInterventionRequest* /*request*/, ::carbon::rtc::CreateInterventionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetActiveTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetActiveTask() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetActiveTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetActiveTaskRequest* /*request*/, ::carbon::rtc::GetActiveTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetTask() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetTaskRequest* /*request*/, ::carbon::rtc::GetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextActiveObjective : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextActiveObjective() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_GetNextActiveObjective() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveObjective(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetNextActiveObjectiveRequest* /*request*/, ::carbon::rtc::GetNextActiveObjectiveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UpdateTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UpdateTask() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_UpdateTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::UpdateTaskRequest* /*request*/, ::carbon::rtc::UpdateTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_CreateIntervention : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CreateIntervention() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_CreateIntervention() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateIntervention(::grpc::ServerContext* /*context*/, const ::carbon::rtc::CreateInterventionRequest* /*request*/, ::carbon::rtc::CreateInterventionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCreateIntervention(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetActiveTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetActiveTask() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetActiveTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetActiveTaskRequest* /*request*/, ::carbon::rtc::GetActiveTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetActiveTask(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetTask() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetTaskRequest* /*request*/, ::carbon::rtc::GetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTask(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextActiveObjective : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextActiveObjective() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_GetNextActiveObjective() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveObjective(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetNextActiveObjectiveRequest* /*request*/, ::carbon::rtc::GetNextActiveObjectiveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveObjective(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UpdateTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UpdateTask() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_UpdateTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::UpdateTaskRequest* /*request*/, ::carbon::rtc::UpdateTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateTask(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CreateIntervention : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CreateIntervention() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CreateIntervention(context, request, response); }));
    }
    ~WithRawCallbackMethod_CreateIntervention() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateIntervention(::grpc::ServerContext* /*context*/, const ::carbon::rtc::CreateInterventionRequest* /*request*/, ::carbon::rtc::CreateInterventionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CreateIntervention(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetActiveTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetActiveTask() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetActiveTask(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetActiveTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetActiveTaskRequest* /*request*/, ::carbon::rtc::GetActiveTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetActiveTask(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetTask() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetTask(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetTaskRequest* /*request*/, ::carbon::rtc::GetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTask(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextActiveObjective : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextActiveObjective() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextActiveObjective(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextActiveObjective() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveObjective(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetNextActiveObjectiveRequest* /*request*/, ::carbon::rtc::GetNextActiveObjectiveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveObjective(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UpdateTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UpdateTask() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UpdateTask(context, request, response); }));
    }
    ~WithRawCallbackMethod_UpdateTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::UpdateTaskRequest* /*request*/, ::carbon::rtc::UpdateTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateTask(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CreateIntervention : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CreateIntervention() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::CreateInterventionRequest, ::carbon::rtc::CreateInterventionResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::CreateInterventionRequest, ::carbon::rtc::CreateInterventionResponse>* streamer) {
                       return this->StreamedCreateIntervention(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CreateIntervention() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CreateIntervention(::grpc::ServerContext* /*context*/, const ::carbon::rtc::CreateInterventionRequest* /*request*/, ::carbon::rtc::CreateInterventionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCreateIntervention(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::CreateInterventionRequest,::carbon::rtc::CreateInterventionResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetActiveTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetActiveTask() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::GetActiveTaskRequest, ::carbon::rtc::GetActiveTaskResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::GetActiveTaskRequest, ::carbon::rtc::GetActiveTaskResponse>* streamer) {
                       return this->StreamedGetActiveTask(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetActiveTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetActiveTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetActiveTaskRequest* /*request*/, ::carbon::rtc::GetActiveTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetActiveTask(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::GetActiveTaskRequest,::carbon::rtc::GetActiveTaskResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetTask() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::GetTaskRequest, ::carbon::rtc::GetTaskResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::GetTaskRequest, ::carbon::rtc::GetTaskResponse>* streamer) {
                       return this->StreamedGetTask(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetTaskRequest* /*request*/, ::carbon::rtc::GetTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetTask(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::GetTaskRequest,::carbon::rtc::GetTaskResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextActiveObjective : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextActiveObjective() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::GetNextActiveObjectiveRequest, ::carbon::rtc::GetNextActiveObjectiveResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::GetNextActiveObjectiveRequest, ::carbon::rtc::GetNextActiveObjectiveResponse>* streamer) {
                       return this->StreamedGetNextActiveObjective(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextActiveObjective() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextActiveObjective(::grpc::ServerContext* /*context*/, const ::carbon::rtc::GetNextActiveObjectiveRequest* /*request*/, ::carbon::rtc::GetNextActiveObjectiveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextActiveObjective(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::GetNextActiveObjectiveRequest,::carbon::rtc::GetNextActiveObjectiveResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UpdateTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UpdateTask() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::rtc::UpdateTaskRequest, ::carbon::rtc::UpdateTaskResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::rtc::UpdateTaskRequest, ::carbon::rtc::UpdateTaskResponse>* streamer) {
                       return this->StreamedUpdateTask(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UpdateTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UpdateTask(::grpc::ServerContext* /*context*/, const ::carbon::rtc::UpdateTaskRequest* /*request*/, ::carbon::rtc::UpdateTaskResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUpdateTask(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::rtc::UpdateTaskRequest,::carbon::rtc::UpdateTaskResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_CreateIntervention<WithStreamedUnaryMethod_GetActiveTask<WithStreamedUnaryMethod_GetTask<WithStreamedUnaryMethod_GetNextActiveObjective<WithStreamedUnaryMethod_UpdateTask<Service > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_CreateIntervention<WithStreamedUnaryMethod_GetActiveTask<WithStreamedUnaryMethod_GetTask<WithStreamedUnaryMethod_GetNextActiveObjective<WithStreamedUnaryMethod_UpdateTask<Service > > > > > StreamedService;
};

}  // namespace rtc
}  // namespace carbon


#endif  // GRPC_proto_2frtc_2fjobs_2eproto__INCLUDED

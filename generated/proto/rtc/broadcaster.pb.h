// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/rtc/broadcaster.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2frtc_2fbroadcaster_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2frtc_2fbroadcaster_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2frtc_2fbroadcaster_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2frtc_2fbroadcaster_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frtc_2fbroadcaster_2eproto;
namespace carbon {
namespace rtc {
class AuthStatus;
struct AuthStatusDefaultTypeInternal;
extern AuthStatusDefaultTypeInternal _AuthStatus_default_instance_;
class RtcMessage;
struct RtcMessageDefaultTypeInternal;
extern RtcMessageDefaultTypeInternal _RtcMessage_default_instance_;
class SignalingMsg;
struct SignalingMsgDefaultTypeInternal;
extern SignalingMsgDefaultTypeInternal _SignalingMsg_default_instance_;
class StreamListRequest;
struct StreamListRequestDefaultTypeInternal;
extern StreamListRequestDefaultTypeInternal _StreamListRequest_default_instance_;
class StreamListResponse;
struct StreamListResponseDefaultTypeInternal;
extern StreamListResponseDefaultTypeInternal _StreamListResponse_default_instance_;
class StreamListResponse_StreamsEntry_DoNotUse;
struct StreamListResponse_StreamsEntry_DoNotUseDefaultTypeInternal;
extern StreamListResponse_StreamsEntry_DoNotUseDefaultTypeInternal _StreamListResponse_StreamsEntry_DoNotUse_default_instance_;
}  // namespace rtc
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::rtc::AuthStatus* Arena::CreateMaybeMessage<::carbon::rtc::AuthStatus>(Arena*);
template<> ::carbon::rtc::RtcMessage* Arena::CreateMaybeMessage<::carbon::rtc::RtcMessage>(Arena*);
template<> ::carbon::rtc::SignalingMsg* Arena::CreateMaybeMessage<::carbon::rtc::SignalingMsg>(Arena*);
template<> ::carbon::rtc::StreamListRequest* Arena::CreateMaybeMessage<::carbon::rtc::StreamListRequest>(Arena*);
template<> ::carbon::rtc::StreamListResponse* Arena::CreateMaybeMessage<::carbon::rtc::StreamListResponse>(Arena*);
template<> ::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace rtc {

// ===================================================================

class AuthStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.AuthStatus) */ {
 public:
  inline AuthStatus() : AuthStatus(nullptr) {}
  ~AuthStatus() override;
  explicit constexpr AuthStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AuthStatus(const AuthStatus& from);
  AuthStatus(AuthStatus&& from) noexcept
    : AuthStatus() {
    *this = ::std::move(from);
  }

  inline AuthStatus& operator=(const AuthStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline AuthStatus& operator=(AuthStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AuthStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const AuthStatus* internal_default_instance() {
    return reinterpret_cast<const AuthStatus*>(
               &_AuthStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AuthStatus& a, AuthStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(AuthStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AuthStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AuthStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AuthStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AuthStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AuthStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AuthStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.AuthStatus";
  }
  protected:
  explicit AuthStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kReadFieldNumber = 1,
    kWriteFieldNumber = 2,
  };
  // bool read = 1;
  void clear_read();
  bool read() const;
  void set_read(bool value);
  private:
  bool _internal_read() const;
  void _internal_set_read(bool value);
  public:

  // bool write = 2;
  void clear_write();
  bool write() const;
  void set_write(bool value);
  private:
  bool _internal_write() const;
  void _internal_set_write(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.AuthStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool read_;
  bool write_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fbroadcaster_2eproto;
};
// -------------------------------------------------------------------

class RtcMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.RtcMessage) */ {
 public:
  inline RtcMessage() : RtcMessage(nullptr) {}
  ~RtcMessage() override;
  explicit constexpr RtcMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RtcMessage(const RtcMessage& from);
  RtcMessage(RtcMessage&& from) noexcept
    : RtcMessage() {
    *this = ::std::move(from);
  }

  inline RtcMessage& operator=(const RtcMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline RtcMessage& operator=(RtcMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RtcMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const RtcMessage* internal_default_instance() {
    return reinterpret_cast<const RtcMessage*>(
               &_RtcMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(RtcMessage& a, RtcMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(RtcMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RtcMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RtcMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RtcMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RtcMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RtcMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RtcMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.RtcMessage";
  }
  protected:
  explicit RtcMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMsgFieldNumber = 2,
    kAuthFieldNumber = 3,
    kIdFieldNumber = 1,
  };
  // bytes msg = 2;
  void clear_msg();
  const std::string& msg() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_msg(ArgT0&& arg0, ArgT... args);
  std::string* mutable_msg();
  PROTOBUF_NODISCARD std::string* release_msg();
  void set_allocated_msg(std::string* msg);
  private:
  const std::string& _internal_msg() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_msg(const std::string& value);
  std::string* _internal_mutable_msg();
  public:

  // .carbon.rtc.AuthStatus auth = 3;
  bool has_auth() const;
  private:
  bool _internal_has_auth() const;
  public:
  void clear_auth();
  const ::carbon::rtc::AuthStatus& auth() const;
  PROTOBUF_NODISCARD ::carbon::rtc::AuthStatus* release_auth();
  ::carbon::rtc::AuthStatus* mutable_auth();
  void set_allocated_auth(::carbon::rtc::AuthStatus* auth);
  private:
  const ::carbon::rtc::AuthStatus& _internal_auth() const;
  ::carbon::rtc::AuthStatus* _internal_mutable_auth();
  public:
  void unsafe_arena_set_allocated_auth(
      ::carbon::rtc::AuthStatus* auth);
  ::carbon::rtc::AuthStatus* unsafe_arena_release_auth();

  // uint64 id = 1;
  void clear_id();
  uint64_t id() const;
  void set_id(uint64_t value);
  private:
  uint64_t _internal_id() const;
  void _internal_set_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.RtcMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr msg_;
  ::carbon::rtc::AuthStatus* auth_;
  uint64_t id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fbroadcaster_2eproto;
};
// -------------------------------------------------------------------

class SignalingMsg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.SignalingMsg) */ {
 public:
  inline SignalingMsg() : SignalingMsg(nullptr) {}
  ~SignalingMsg() override;
  explicit constexpr SignalingMsg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SignalingMsg(const SignalingMsg& from);
  SignalingMsg(SignalingMsg&& from) noexcept
    : SignalingMsg() {
    *this = ::std::move(from);
  }

  inline SignalingMsg& operator=(const SignalingMsg& from) {
    CopyFrom(from);
    return *this;
  }
  inline SignalingMsg& operator=(SignalingMsg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SignalingMsg& default_instance() {
    return *internal_default_instance();
  }
  static inline const SignalingMsg* internal_default_instance() {
    return reinterpret_cast<const SignalingMsg*>(
               &_SignalingMsg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SignalingMsg& a, SignalingMsg& b) {
    a.Swap(&b);
  }
  inline void Swap(SignalingMsg* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SignalingMsg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SignalingMsg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SignalingMsg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SignalingMsg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SignalingMsg& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SignalingMsg* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.SignalingMsg";
  }
  protected:
  explicit SignalingMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMsgFieldNumber = 1,
  };
  // bytes msg = 1;
  void clear_msg();
  const std::string& msg() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_msg(ArgT0&& arg0, ArgT... args);
  std::string* mutable_msg();
  PROTOBUF_NODISCARD std::string* release_msg();
  void set_allocated_msg(std::string* msg);
  private:
  const std::string& _internal_msg() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_msg(const std::string& value);
  std::string* _internal_mutable_msg();
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.SignalingMsg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr msg_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fbroadcaster_2eproto;
};
// -------------------------------------------------------------------

class StreamListRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.rtc.StreamListRequest) */ {
 public:
  inline StreamListRequest() : StreamListRequest(nullptr) {}
  explicit constexpr StreamListRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StreamListRequest(const StreamListRequest& from);
  StreamListRequest(StreamListRequest&& from) noexcept
    : StreamListRequest() {
    *this = ::std::move(from);
  }

  inline StreamListRequest& operator=(const StreamListRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline StreamListRequest& operator=(StreamListRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StreamListRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const StreamListRequest* internal_default_instance() {
    return reinterpret_cast<const StreamListRequest*>(
               &_StreamListRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(StreamListRequest& a, StreamListRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(StreamListRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StreamListRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StreamListRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StreamListRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const StreamListRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const StreamListRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.StreamListRequest";
  }
  protected:
  explicit StreamListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.rtc.StreamListRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fbroadcaster_2eproto;
};
// -------------------------------------------------------------------

class StreamListResponse_StreamsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<StreamListResponse_StreamsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<StreamListResponse_StreamsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  StreamListResponse_StreamsEntry_DoNotUse();
  explicit constexpr StreamListResponse_StreamsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit StreamListResponse_StreamsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const StreamListResponse_StreamsEntry_DoNotUse& other);
  static const StreamListResponse_StreamsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const StreamListResponse_StreamsEntry_DoNotUse*>(&_StreamListResponse_StreamsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.rtc.StreamListResponse.StreamsEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.rtc.StreamListResponse.StreamsEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class StreamListResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.StreamListResponse) */ {
 public:
  inline StreamListResponse() : StreamListResponse(nullptr) {}
  ~StreamListResponse() override;
  explicit constexpr StreamListResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StreamListResponse(const StreamListResponse& from);
  StreamListResponse(StreamListResponse&& from) noexcept
    : StreamListResponse() {
    *this = ::std::move(from);
  }

  inline StreamListResponse& operator=(const StreamListResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline StreamListResponse& operator=(StreamListResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StreamListResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const StreamListResponse* internal_default_instance() {
    return reinterpret_cast<const StreamListResponse*>(
               &_StreamListResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(StreamListResponse& a, StreamListResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(StreamListResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StreamListResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StreamListResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StreamListResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StreamListResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StreamListResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StreamListResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.StreamListResponse";
  }
  protected:
  explicit StreamListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kStreamsFieldNumber = 1,
  };
  // map<string, string> streams = 1;
  int streams_size() const;
  private:
  int _internal_streams_size() const;
  public:
  void clear_streams();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_streams() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_streams();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      streams() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_streams();

  // @@protoc_insertion_point(class_scope:carbon.rtc.StreamListResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      StreamListResponse_StreamsEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> streams_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2fbroadcaster_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AuthStatus

// bool read = 1;
inline void AuthStatus::clear_read() {
  read_ = false;
}
inline bool AuthStatus::_internal_read() const {
  return read_;
}
inline bool AuthStatus::read() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.AuthStatus.read)
  return _internal_read();
}
inline void AuthStatus::_internal_set_read(bool value) {
  
  read_ = value;
}
inline void AuthStatus::set_read(bool value) {
  _internal_set_read(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.AuthStatus.read)
}

// bool write = 2;
inline void AuthStatus::clear_write() {
  write_ = false;
}
inline bool AuthStatus::_internal_write() const {
  return write_;
}
inline bool AuthStatus::write() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.AuthStatus.write)
  return _internal_write();
}
inline void AuthStatus::_internal_set_write(bool value) {
  
  write_ = value;
}
inline void AuthStatus::set_write(bool value) {
  _internal_set_write(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.AuthStatus.write)
}

// -------------------------------------------------------------------

// RtcMessage

// uint64 id = 1;
inline void RtcMessage::clear_id() {
  id_ = uint64_t{0u};
}
inline uint64_t RtcMessage::_internal_id() const {
  return id_;
}
inline uint64_t RtcMessage::id() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RtcMessage.id)
  return _internal_id();
}
inline void RtcMessage::_internal_set_id(uint64_t value) {
  
  id_ = value;
}
inline void RtcMessage::set_id(uint64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.RtcMessage.id)
}

// bytes msg = 2;
inline void RtcMessage::clear_msg() {
  msg_.ClearToEmpty();
}
inline const std::string& RtcMessage::msg() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RtcMessage.msg)
  return _internal_msg();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RtcMessage::set_msg(ArgT0&& arg0, ArgT... args) {
 
 msg_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.RtcMessage.msg)
}
inline std::string* RtcMessage::mutable_msg() {
  std::string* _s = _internal_mutable_msg();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.RtcMessage.msg)
  return _s;
}
inline const std::string& RtcMessage::_internal_msg() const {
  return msg_.Get();
}
inline void RtcMessage::_internal_set_msg(const std::string& value) {
  
  msg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RtcMessage::_internal_mutable_msg() {
  
  return msg_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RtcMessage::release_msg() {
  // @@protoc_insertion_point(field_release:carbon.rtc.RtcMessage.msg)
  return msg_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RtcMessage::set_allocated_msg(std::string* msg) {
  if (msg != nullptr) {
    
  } else {
    
  }
  msg_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), msg,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (msg_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.RtcMessage.msg)
}

// .carbon.rtc.AuthStatus auth = 3;
inline bool RtcMessage::_internal_has_auth() const {
  return this != internal_default_instance() && auth_ != nullptr;
}
inline bool RtcMessage::has_auth() const {
  return _internal_has_auth();
}
inline void RtcMessage::clear_auth() {
  if (GetArenaForAllocation() == nullptr && auth_ != nullptr) {
    delete auth_;
  }
  auth_ = nullptr;
}
inline const ::carbon::rtc::AuthStatus& RtcMessage::_internal_auth() const {
  const ::carbon::rtc::AuthStatus* p = auth_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::rtc::AuthStatus&>(
      ::carbon::rtc::_AuthStatus_default_instance_);
}
inline const ::carbon::rtc::AuthStatus& RtcMessage::auth() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RtcMessage.auth)
  return _internal_auth();
}
inline void RtcMessage::unsafe_arena_set_allocated_auth(
    ::carbon::rtc::AuthStatus* auth) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(auth_);
  }
  auth_ = auth;
  if (auth) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.RtcMessage.auth)
}
inline ::carbon::rtc::AuthStatus* RtcMessage::release_auth() {
  
  ::carbon::rtc::AuthStatus* temp = auth_;
  auth_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::rtc::AuthStatus* RtcMessage::unsafe_arena_release_auth() {
  // @@protoc_insertion_point(field_release:carbon.rtc.RtcMessage.auth)
  
  ::carbon::rtc::AuthStatus* temp = auth_;
  auth_ = nullptr;
  return temp;
}
inline ::carbon::rtc::AuthStatus* RtcMessage::_internal_mutable_auth() {
  
  if (auth_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::rtc::AuthStatus>(GetArenaForAllocation());
    auth_ = p;
  }
  return auth_;
}
inline ::carbon::rtc::AuthStatus* RtcMessage::mutable_auth() {
  ::carbon::rtc::AuthStatus* _msg = _internal_mutable_auth();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.RtcMessage.auth)
  return _msg;
}
inline void RtcMessage::set_allocated_auth(::carbon::rtc::AuthStatus* auth) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete auth_;
  }
  if (auth) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::AuthStatus>::GetOwningArena(auth);
    if (message_arena != submessage_arena) {
      auth = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, auth, submessage_arena);
    }
    
  } else {
    
  }
  auth_ = auth;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.RtcMessage.auth)
}

// -------------------------------------------------------------------

// SignalingMsg

// bytes msg = 1;
inline void SignalingMsg::clear_msg() {
  msg_.ClearToEmpty();
}
inline const std::string& SignalingMsg::msg() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.SignalingMsg.msg)
  return _internal_msg();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SignalingMsg::set_msg(ArgT0&& arg0, ArgT... args) {
 
 msg_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.SignalingMsg.msg)
}
inline std::string* SignalingMsg::mutable_msg() {
  std::string* _s = _internal_mutable_msg();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.SignalingMsg.msg)
  return _s;
}
inline const std::string& SignalingMsg::_internal_msg() const {
  return msg_.Get();
}
inline void SignalingMsg::_internal_set_msg(const std::string& value) {
  
  msg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SignalingMsg::_internal_mutable_msg() {
  
  return msg_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SignalingMsg::release_msg() {
  // @@protoc_insertion_point(field_release:carbon.rtc.SignalingMsg.msg)
  return msg_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SignalingMsg::set_allocated_msg(std::string* msg) {
  if (msg != nullptr) {
    
  } else {
    
  }
  msg_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), msg,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (msg_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.SignalingMsg.msg)
}

// -------------------------------------------------------------------

// StreamListRequest

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// StreamListResponse

// map<string, string> streams = 1;
inline int StreamListResponse::_internal_streams_size() const {
  return streams_.size();
}
inline int StreamListResponse::streams_size() const {
  return _internal_streams_size();
}
inline void StreamListResponse::clear_streams() {
  streams_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
StreamListResponse::_internal_streams() const {
  return streams_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
StreamListResponse::streams() const {
  // @@protoc_insertion_point(field_map:carbon.rtc.StreamListResponse.streams)
  return _internal_streams();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
StreamListResponse::_internal_mutable_streams() {
  return streams_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
StreamListResponse::mutable_streams() {
  // @@protoc_insertion_point(field_mutable_map:carbon.rtc.StreamListResponse.streams)
  return _internal_mutable_streams();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace rtc
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2frtc_2fbroadcaster_2eproto

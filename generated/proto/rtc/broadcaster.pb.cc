// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/rtc/broadcaster.proto

#include "proto/rtc/broadcaster.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace rtc {
constexpr AuthStatus::AuthStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : read_(false)
  , write_(false){}
struct AuthStatusDefaultTypeInternal {
  constexpr AuthStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AuthStatusDefaultTypeInternal() {}
  union {
    AuthStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AuthStatusDefaultTypeInternal _AuthStatus_default_instance_;
constexpr RtcMessage::RtcMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : msg_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , auth_(nullptr)
  , id_(uint64_t{0u}){}
struct RtcMessageDefaultTypeInternal {
  constexpr RtcMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RtcMessageDefaultTypeInternal() {}
  union {
    RtcMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RtcMessageDefaultTypeInternal _RtcMessage_default_instance_;
constexpr SignalingMsg::SignalingMsg(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : msg_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SignalingMsgDefaultTypeInternal {
  constexpr SignalingMsgDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SignalingMsgDefaultTypeInternal() {}
  union {
    SignalingMsg _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SignalingMsgDefaultTypeInternal _SignalingMsg_default_instance_;
constexpr StreamListRequest::StreamListRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct StreamListRequestDefaultTypeInternal {
  constexpr StreamListRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StreamListRequestDefaultTypeInternal() {}
  union {
    StreamListRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StreamListRequestDefaultTypeInternal _StreamListRequest_default_instance_;
constexpr StreamListResponse_StreamsEntry_DoNotUse::StreamListResponse_StreamsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct StreamListResponse_StreamsEntry_DoNotUseDefaultTypeInternal {
  constexpr StreamListResponse_StreamsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StreamListResponse_StreamsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    StreamListResponse_StreamsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StreamListResponse_StreamsEntry_DoNotUseDefaultTypeInternal _StreamListResponse_StreamsEntry_DoNotUse_default_instance_;
constexpr StreamListResponse::StreamListResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : streams_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}){}
struct StreamListResponseDefaultTypeInternal {
  constexpr StreamListResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StreamListResponseDefaultTypeInternal() {}
  union {
    StreamListResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StreamListResponseDefaultTypeInternal _StreamListResponse_default_instance_;
}  // namespace rtc
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2frtc_2fbroadcaster_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2frtc_2fbroadcaster_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2frtc_2fbroadcaster_2eproto = nullptr;

const uint32_t TableStruct_proto_2frtc_2fbroadcaster_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::AuthStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::AuthStatus, read_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::AuthStatus, write_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RtcMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RtcMessage, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RtcMessage, msg_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RtcMessage, auth_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SignalingMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SignalingMsg, msg_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::StreamListRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::StreamListResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::StreamListResponse, streams_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::rtc::AuthStatus)},
  { 8, -1, -1, sizeof(::carbon::rtc::RtcMessage)},
  { 17, -1, -1, sizeof(::carbon::rtc::SignalingMsg)},
  { 24, -1, -1, sizeof(::carbon::rtc::StreamListRequest)},
  { 30, 38, -1, sizeof(::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse)},
  { 40, -1, -1, sizeof(::carbon::rtc::StreamListResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_AuthStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_RtcMessage_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_SignalingMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_StreamListRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_StreamListResponse_StreamsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_StreamListResponse_default_instance_),
};

const char descriptor_table_protodef_proto_2frtc_2fbroadcaster_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033proto/rtc/broadcaster.proto\022\ncarbon.rt"
  "c\")\n\nAuthStatus\022\014\n\004read\030\001 \001(\010\022\r\n\005write\030\002"
  " \001(\010\"K\n\nRtcMessage\022\n\n\002id\030\001 \001(\004\022\013\n\003msg\030\002 "
  "\001(\014\022$\n\004auth\030\003 \001(\0132\026.carbon.rtc.AuthStatu"
  "s\"\033\n\014SignalingMsg\022\013\n\003msg\030\001 \001(\014\"\023\n\021Stream"
  "ListRequest\"\202\001\n\022StreamListResponse\022<\n\007st"
  "reams\030\001 \003(\0132+.carbon.rtc.StreamListRespo"
  "nse.StreamsEntry\032.\n\014StreamsEntry\022\013\n\003key\030"
  "\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\0012\354\001\n\013Broadcaster"
  "\022@\n\nMessageBus\022\026.carbon.rtc.RtcMessage\032\026"
  ".carbon.rtc.RtcMessage(\0010\001\022K\n\021LocalSigna"
  "lServer\022\030.carbon.rtc.SignalingMsg\032\030.carb"
  "on.rtc.SignalingMsg(\0010\001\022N\n\rGetStreamList"
  "\022\035.carbon.rtc.StreamListRequest\032\036.carbon"
  ".rtc.StreamListResponseB\013Z\tproto/rtcb\006pr"
  "oto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2frtc_2fbroadcaster_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frtc_2fbroadcaster_2eproto = {
  false, false, 604, descriptor_table_protodef_proto_2frtc_2fbroadcaster_2eproto, "proto/rtc/broadcaster.proto", 
  &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_once, nullptr, 0, 6,
  schemas, file_default_instances, TableStruct_proto_2frtc_2fbroadcaster_2eproto::offsets,
  file_level_metadata_proto_2frtc_2fbroadcaster_2eproto, file_level_enum_descriptors_proto_2frtc_2fbroadcaster_2eproto, file_level_service_descriptors_proto_2frtc_2fbroadcaster_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2frtc_2fbroadcaster_2eproto_getter() {
  return &descriptor_table_proto_2frtc_2fbroadcaster_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2frtc_2fbroadcaster_2eproto(&descriptor_table_proto_2frtc_2fbroadcaster_2eproto);
namespace carbon {
namespace rtc {

// ===================================================================

class AuthStatus::_Internal {
 public:
};

AuthStatus::AuthStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.AuthStatus)
}
AuthStatus::AuthStatus(const AuthStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&read_, &from.read_,
    static_cast<size_t>(reinterpret_cast<char*>(&write_) -
    reinterpret_cast<char*>(&read_)) + sizeof(write_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.AuthStatus)
}

inline void AuthStatus::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&read_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&write_) -
    reinterpret_cast<char*>(&read_)) + sizeof(write_));
}

AuthStatus::~AuthStatus() {
  // @@protoc_insertion_point(destructor:carbon.rtc.AuthStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AuthStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void AuthStatus::ArenaDtor(void* object) {
  AuthStatus* _this = reinterpret_cast< AuthStatus* >(object);
  (void)_this;
}
void AuthStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AuthStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AuthStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.AuthStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&read_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&write_) -
      reinterpret_cast<char*>(&read_)) + sizeof(write_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AuthStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool read = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          read_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool write = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          write_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AuthStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.AuthStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool read = 1;
  if (this->_internal_read() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_read(), target);
  }

  // bool write = 2;
  if (this->_internal_write() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_write(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.AuthStatus)
  return target;
}

size_t AuthStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.AuthStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool read = 1;
  if (this->_internal_read() != 0) {
    total_size += 1 + 1;
  }

  // bool write = 2;
  if (this->_internal_write() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AuthStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AuthStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AuthStatus::GetClassData() const { return &_class_data_; }

void AuthStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AuthStatus *>(to)->MergeFrom(
      static_cast<const AuthStatus &>(from));
}


void AuthStatus::MergeFrom(const AuthStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.AuthStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_read() != 0) {
    _internal_set_read(from._internal_read());
  }
  if (from._internal_write() != 0) {
    _internal_set_write(from._internal_write());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AuthStatus::CopyFrom(const AuthStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.AuthStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AuthStatus::IsInitialized() const {
  return true;
}

void AuthStatus::InternalSwap(AuthStatus* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AuthStatus, write_)
      + sizeof(AuthStatus::write_)
      - PROTOBUF_FIELD_OFFSET(AuthStatus, read_)>(
          reinterpret_cast<char*>(&read_),
          reinterpret_cast<char*>(&other->read_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AuthStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_getter, &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_once,
      file_level_metadata_proto_2frtc_2fbroadcaster_2eproto[0]);
}

// ===================================================================

class RtcMessage::_Internal {
 public:
  static const ::carbon::rtc::AuthStatus& auth(const RtcMessage* msg);
};

const ::carbon::rtc::AuthStatus&
RtcMessage::_Internal::auth(const RtcMessage* msg) {
  return *msg->auth_;
}
RtcMessage::RtcMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.RtcMessage)
}
RtcMessage::RtcMessage(const RtcMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_msg().empty()) {
    msg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_msg(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_auth()) {
    auth_ = new ::carbon::rtc::AuthStatus(*from.auth_);
  } else {
    auth_ = nullptr;
  }
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.RtcMessage)
}

inline void RtcMessage::SharedCtor() {
msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&auth_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&id_) -
    reinterpret_cast<char*>(&auth_)) + sizeof(id_));
}

RtcMessage::~RtcMessage() {
  // @@protoc_insertion_point(destructor:carbon.rtc.RtcMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RtcMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  msg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete auth_;
}

void RtcMessage::ArenaDtor(void* object) {
  RtcMessage* _this = reinterpret_cast< RtcMessage* >(object);
  (void)_this;
}
void RtcMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RtcMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RtcMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.RtcMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  msg_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && auth_ != nullptr) {
    delete auth_;
  }
  auth_ = nullptr;
  id_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RtcMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes msg = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_msg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.AuthStatus auth = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_auth(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RtcMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.RtcMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_id(), target);
  }

  // bytes msg = 2;
  if (!this->_internal_msg().empty()) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_msg(), target);
  }

  // .carbon.rtc.AuthStatus auth = 3;
  if (this->_internal_has_auth()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::auth(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.RtcMessage)
  return target;
}

size_t RtcMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.RtcMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes msg = 2;
  if (!this->_internal_msg().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_msg());
  }

  // .carbon.rtc.AuthStatus auth = 3;
  if (this->_internal_has_auth()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *auth_);
  }

  // uint64 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RtcMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RtcMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RtcMessage::GetClassData() const { return &_class_data_; }

void RtcMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RtcMessage *>(to)->MergeFrom(
      static_cast<const RtcMessage &>(from));
}


void RtcMessage::MergeFrom(const RtcMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.RtcMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_msg().empty()) {
    _internal_set_msg(from._internal_msg());
  }
  if (from._internal_has_auth()) {
    _internal_mutable_auth()->::carbon::rtc::AuthStatus::MergeFrom(from._internal_auth());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RtcMessage::CopyFrom(const RtcMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.RtcMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RtcMessage::IsInitialized() const {
  return true;
}

void RtcMessage::InternalSwap(RtcMessage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &msg_, lhs_arena,
      &other->msg_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RtcMessage, id_)
      + sizeof(RtcMessage::id_)
      - PROTOBUF_FIELD_OFFSET(RtcMessage, auth_)>(
          reinterpret_cast<char*>(&auth_),
          reinterpret_cast<char*>(&other->auth_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RtcMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_getter, &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_once,
      file_level_metadata_proto_2frtc_2fbroadcaster_2eproto[1]);
}

// ===================================================================

class SignalingMsg::_Internal {
 public:
};

SignalingMsg::SignalingMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.SignalingMsg)
}
SignalingMsg::SignalingMsg(const SignalingMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_msg().empty()) {
    msg_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_msg(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.SignalingMsg)
}

inline void SignalingMsg::SharedCtor() {
msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SignalingMsg::~SignalingMsg() {
  // @@protoc_insertion_point(destructor:carbon.rtc.SignalingMsg)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SignalingMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  msg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SignalingMsg::ArenaDtor(void* object) {
  SignalingMsg* _this = reinterpret_cast< SignalingMsg* >(object);
  (void)_this;
}
void SignalingMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SignalingMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SignalingMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.SignalingMsg)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  msg_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SignalingMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes msg = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_msg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SignalingMsg::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.SignalingMsg)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes msg = 1;
  if (!this->_internal_msg().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_msg(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.SignalingMsg)
  return target;
}

size_t SignalingMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.SignalingMsg)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes msg = 1;
  if (!this->_internal_msg().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_msg());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SignalingMsg::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SignalingMsg::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SignalingMsg::GetClassData() const { return &_class_data_; }

void SignalingMsg::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SignalingMsg *>(to)->MergeFrom(
      static_cast<const SignalingMsg &>(from));
}


void SignalingMsg::MergeFrom(const SignalingMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.SignalingMsg)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_msg().empty()) {
    _internal_set_msg(from._internal_msg());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SignalingMsg::CopyFrom(const SignalingMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.SignalingMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SignalingMsg::IsInitialized() const {
  return true;
}

void SignalingMsg::InternalSwap(SignalingMsg* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &msg_, lhs_arena,
      &other->msg_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SignalingMsg::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_getter, &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_once,
      file_level_metadata_proto_2frtc_2fbroadcaster_2eproto[2]);
}

// ===================================================================

class StreamListRequest::_Internal {
 public:
};

StreamListRequest::StreamListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.StreamListRequest)
}
StreamListRequest::StreamListRequest(const StreamListRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.StreamListRequest)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StreamListRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StreamListRequest::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata StreamListRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_getter, &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_once,
      file_level_metadata_proto_2frtc_2fbroadcaster_2eproto[3]);
}

// ===================================================================

StreamListResponse_StreamsEntry_DoNotUse::StreamListResponse_StreamsEntry_DoNotUse() {}
StreamListResponse_StreamsEntry_DoNotUse::StreamListResponse_StreamsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void StreamListResponse_StreamsEntry_DoNotUse::MergeFrom(const StreamListResponse_StreamsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata StreamListResponse_StreamsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_getter, &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_once,
      file_level_metadata_proto_2frtc_2fbroadcaster_2eproto[4]);
}

// ===================================================================

class StreamListResponse::_Internal {
 public:
};

StreamListResponse::StreamListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  streams_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.StreamListResponse)
}
StreamListResponse::StreamListResponse(const StreamListResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  streams_.MergeFrom(from.streams_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.StreamListResponse)
}

inline void StreamListResponse::SharedCtor() {
}

StreamListResponse::~StreamListResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.StreamListResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StreamListResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void StreamListResponse::ArenaDtor(void* object) {
  StreamListResponse* _this = reinterpret_cast< StreamListResponse* >(object);
  (void)_this;
  _this->streams_. ~MapField();
}
inline void StreamListResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &StreamListResponse::ArenaDtor);
  }
}
void StreamListResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StreamListResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.StreamListResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  streams_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StreamListResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, string> streams = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&streams_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StreamListResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.StreamListResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, string> streams = 1;
  if (!this->_internal_streams().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.rtc.StreamListResponse.StreamsEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.rtc.StreamListResponse.StreamsEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_streams().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_streams().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_streams().begin();
          it != this->_internal_streams().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = StreamListResponse_StreamsEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_streams().begin();
          it != this->_internal_streams().end(); ++it) {
        target = StreamListResponse_StreamsEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.StreamListResponse)
  return target;
}

size_t StreamListResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.StreamListResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> streams = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_streams_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_streams().begin();
      it != this->_internal_streams().end(); ++it) {
    total_size += StreamListResponse_StreamsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StreamListResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StreamListResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StreamListResponse::GetClassData() const { return &_class_data_; }

void StreamListResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StreamListResponse *>(to)->MergeFrom(
      static_cast<const StreamListResponse &>(from));
}


void StreamListResponse::MergeFrom(const StreamListResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.StreamListResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  streams_.MergeFrom(from.streams_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StreamListResponse::CopyFrom(const StreamListResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.StreamListResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StreamListResponse::IsInitialized() const {
  return true;
}

void StreamListResponse::InternalSwap(StreamListResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  streams_.InternalSwap(&other->streams_);
}

::PROTOBUF_NAMESPACE_ID::Metadata StreamListResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_getter, &descriptor_table_proto_2frtc_2fbroadcaster_2eproto_once,
      file_level_metadata_proto_2frtc_2fbroadcaster_2eproto[5]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace rtc
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::rtc::AuthStatus* Arena::CreateMaybeMessage< ::carbon::rtc::AuthStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::AuthStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::RtcMessage* Arena::CreateMaybeMessage< ::carbon::rtc::RtcMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::RtcMessage >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::SignalingMsg* Arena::CreateMaybeMessage< ::carbon::rtc::SignalingMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::SignalingMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::StreamListRequest* Arena::CreateMaybeMessage< ::carbon::rtc::StreamListRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::StreamListRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::StreamListResponse_StreamsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::StreamListResponse* Arena::CreateMaybeMessage< ::carbon::rtc::StreamListResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::StreamListResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/rtc/location_history.proto

#include "proto/rtc/location_history.pb.h"
#include "proto/rtc/location_history.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace rtc {

static const char* LocationHistory_method_names[] = {
  "/carbon.rtc.LocationHistory/LogLocationHistory",
  "/carbon.rtc.LocationHistory/ListRobots",
  "/carbon.rtc.LocationHistory/ListLocationHistory",
  "/carbon.rtc.LocationHistory/StreamLocation",
};

std::unique_ptr< LocationHistory::Stub> LocationHistory::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< LocationHistory::Stub> stub(new LocationHistory::Stub(channel, options));
  return stub;
}

LocationHistory::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_LogLocationHistory_(LocationHistory_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ListRobots_(LocationHistory_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ListLocationHistory_(LocationHistory_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StreamLocation_(LocationHistory_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::CLIENT_STREAMING, channel)
  {}

::grpc::Status LocationHistory::Stub::LogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::google::protobuf::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::LogLocationHistoryRequest, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LogLocationHistory_, context, request, response);
}

void LocationHistory::Stub::async::LogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest* request, ::google::protobuf::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::LogLocationHistoryRequest, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LogLocationHistory_, context, request, response, std::move(f));
}

void LocationHistory::Stub::async::LogLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest* request, ::google::protobuf::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LogLocationHistory_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* LocationHistory::Stub::PrepareAsyncLogLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::google::protobuf::Empty, ::carbon::rtc::LogLocationHistoryRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LogLocationHistory_, context, request);
}

::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* LocationHistory::Stub::AsyncLogLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::LogLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLogLocationHistoryRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status LocationHistory::Stub::ListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::carbon::rtc::ListRobotsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::ListRobotsRequest, ::carbon::rtc::ListRobotsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ListRobots_, context, request, response);
}

void LocationHistory::Stub::async::ListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest* request, ::carbon::rtc::ListRobotsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::ListRobotsRequest, ::carbon::rtc::ListRobotsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListRobots_, context, request, response, std::move(f));
}

void LocationHistory::Stub::async::ListRobots(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest* request, ::carbon::rtc::ListRobotsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListRobots_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListRobotsResponse>* LocationHistory::Stub::PrepareAsyncListRobotsRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc::ListRobotsResponse, ::carbon::rtc::ListRobotsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ListRobots_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListRobotsResponse>* LocationHistory::Stub::AsyncListRobotsRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListRobotsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncListRobotsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status LocationHistory::Stub::ListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::carbon::rtc::ListLocationHistoryResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::ListLocationHistoryRequest, ::carbon::rtc::ListLocationHistoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ListLocationHistory_, context, request, response);
}

void LocationHistory::Stub::async::ListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest* request, ::carbon::rtc::ListLocationHistoryResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::ListLocationHistoryRequest, ::carbon::rtc::ListLocationHistoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListLocationHistory_, context, request, response, std::move(f));
}

void LocationHistory::Stub::async::ListLocationHistory(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest* request, ::carbon::rtc::ListLocationHistoryResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListLocationHistory_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListLocationHistoryResponse>* LocationHistory::Stub::PrepareAsyncListLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc::ListLocationHistoryResponse, ::carbon::rtc::ListLocationHistoryRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ListLocationHistory_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::ListLocationHistoryResponse>* LocationHistory::Stub::AsyncListLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::rtc::ListLocationHistoryRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncListLocationHistoryRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::ClientWriter< ::carbon::rtc::LocationHistoryRecord>* LocationHistory::Stub::StreamLocationRaw(::grpc::ClientContext* context, ::google::protobuf::Empty* response) {
  return ::grpc::internal::ClientWriterFactory< ::carbon::rtc::LocationHistoryRecord>::Create(channel_.get(), rpcmethod_StreamLocation_, context, response);
}

void LocationHistory::Stub::async::StreamLocation(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::ClientWriteReactor< ::carbon::rtc::LocationHistoryRecord>* reactor) {
  ::grpc::internal::ClientCallbackWriterFactory< ::carbon::rtc::LocationHistoryRecord>::Create(stub_->channel_.get(), stub_->rpcmethod_StreamLocation_, context, response, reactor);
}

::grpc::ClientAsyncWriter< ::carbon::rtc::LocationHistoryRecord>* LocationHistory::Stub::AsyncStreamLocationRaw(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::carbon::rtc::LocationHistoryRecord>::Create(channel_.get(), cq, rpcmethod_StreamLocation_, context, response, true, tag);
}

::grpc::ClientAsyncWriter< ::carbon::rtc::LocationHistoryRecord>* LocationHistory::Stub::PrepareAsyncStreamLocationRaw(::grpc::ClientContext* context, ::google::protobuf::Empty* response, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::carbon::rtc::LocationHistoryRecord>::Create(channel_.get(), cq, rpcmethod_StreamLocation_, context, response, false, nullptr);
}

LocationHistory::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LocationHistory_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LocationHistory::Service, ::carbon::rtc::LogLocationHistoryRequest, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LocationHistory::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::LogLocationHistoryRequest* req,
             ::google::protobuf::Empty* resp) {
               return service->LogLocationHistory(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LocationHistory_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LocationHistory::Service, ::carbon::rtc::ListRobotsRequest, ::carbon::rtc::ListRobotsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LocationHistory::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::ListRobotsRequest* req,
             ::carbon::rtc::ListRobotsResponse* resp) {
               return service->ListRobots(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LocationHistory_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LocationHistory::Service, ::carbon::rtc::ListLocationHistoryRequest, ::carbon::rtc::ListLocationHistoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LocationHistory::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::ListLocationHistoryRequest* req,
             ::carbon::rtc::ListLocationHistoryResponse* resp) {
               return service->ListLocationHistory(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LocationHistory_method_names[3],
      ::grpc::internal::RpcMethod::CLIENT_STREAMING,
      new ::grpc::internal::ClientStreamingHandler< LocationHistory::Service, ::carbon::rtc::LocationHistoryRecord, ::google::protobuf::Empty>(
          [](LocationHistory::Service* service,
             ::grpc::ServerContext* ctx,
             ::grpc::ServerReader<::carbon::rtc::LocationHistoryRecord>* reader,
             ::google::protobuf::Empty* resp) {
               return service->StreamLocation(ctx, reader, resp);
             }, this)));
}

LocationHistory::Service::~Service() {
}

::grpc::Status LocationHistory::Service::LogLocationHistory(::grpc::ServerContext* context, const ::carbon::rtc::LogLocationHistoryRequest* request, ::google::protobuf::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LocationHistory::Service::ListRobots(::grpc::ServerContext* context, const ::carbon::rtc::ListRobotsRequest* request, ::carbon::rtc::ListRobotsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LocationHistory::Service::ListLocationHistory(::grpc::ServerContext* context, const ::carbon::rtc::ListLocationHistoryRequest* request, ::carbon::rtc::ListLocationHistoryResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LocationHistory::Service::StreamLocation(::grpc::ServerContext* context, ::grpc::ServerReader< ::carbon::rtc::LocationHistoryRecord>* reader, ::google::protobuf::Empty* response) {
  (void) context;
  (void) reader;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace rtc


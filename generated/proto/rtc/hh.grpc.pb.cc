// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: proto/rtc/hh.proto

#include "proto/rtc/hh.pb.h"
#include "proto/rtc/hh.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace rtc {

static const char* RobotState_method_names[] = {
  "/carbon.rtc.RobotState/SetRobotRequiredState",
  "/carbon.rtc.RobotState/GetNextRequiredState",
  "/carbon.rtc.RobotState/RobotRequirementStream",
};

std::unique_ptr< RobotState::Stub> RobotState::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< RobotState::Stub> stub(new RobotState::Stub(channel, options));
  return stub;
}

RobotState::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_SetRobotRequiredState_(RobotState_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextRequiredState_(RobotState_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_RobotRequirementStream_(RobotState_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  {}

::grpc::Status RobotState::Stub::SetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::google::protobuf::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::SetRobotRequiredStateRequest, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetRobotRequiredState_, context, request, response);
}

void RobotState::Stub::async::SetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest* request, ::google::protobuf::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::SetRobotRequiredStateRequest, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetRobotRequiredState_, context, request, response, std::move(f));
}

void RobotState::Stub::async::SetRobotRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest* request, ::google::protobuf::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetRobotRequiredState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* RobotState::Stub::PrepareAsyncSetRobotRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::google::protobuf::Empty, ::carbon::rtc::SetRobotRequiredStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetRobotRequiredState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::google::protobuf::Empty>* RobotState::Stub::AsyncSetRobotRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetRobotRequiredStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RobotState::Stub::GetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::carbon::rtc::GetRobotRequiredStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::rtc::GetRobotRequiredStateRequest, ::carbon::rtc::GetRobotRequiredStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextRequiredState_, context, request, response);
}

void RobotState::Stub::async::GetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest* request, ::carbon::rtc::GetRobotRequiredStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::rtc::GetRobotRequiredStateRequest, ::carbon::rtc::GetRobotRequiredStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextRequiredState_, context, request, response, std::move(f));
}

void RobotState::Stub::async::GetNextRequiredState(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest* request, ::carbon::rtc::GetRobotRequiredStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextRequiredState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetRobotRequiredStateResponse>* RobotState::Stub::PrepareAsyncGetNextRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::rtc::GetRobotRequiredStateResponse, ::carbon::rtc::GetRobotRequiredStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextRequiredState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::rtc::GetRobotRequiredStateResponse>* RobotState::Stub::AsyncGetNextRequiredStateRaw(::grpc::ClientContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextRequiredStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::ClientReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* RobotState::Stub::RobotRequirementStreamRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>::Create(channel_.get(), rpcmethod_RobotRequirementStream_, context);
}

void RobotState::Stub::async::RobotRequirementStream(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::carbon::rtc::RobotStatusInfo,::carbon::rtc::RobotRequiredState>* reactor) {
  ::grpc::internal::ClientCallbackReaderWriterFactory< ::carbon::rtc::RobotStatusInfo,::carbon::rtc::RobotRequiredState>::Create(stub_->channel_.get(), stub_->rpcmethod_RobotRequirementStream_, context, reactor);
}

::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* RobotState::Stub::AsyncRobotRequirementStreamRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>::Create(channel_.get(), cq, rpcmethod_RobotRequirementStream_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>* RobotState::Stub::PrepareAsyncRobotRequirementStreamRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>::Create(channel_.get(), cq, rpcmethod_RobotRequirementStream_, context, false, nullptr);
}

RobotState::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RobotState_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RobotState::Service, ::carbon::rtc::SetRobotRequiredStateRequest, ::google::protobuf::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RobotState::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::SetRobotRequiredStateRequest* req,
             ::google::protobuf::Empty* resp) {
               return service->SetRobotRequiredState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RobotState_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RobotState::Service, ::carbon::rtc::GetRobotRequiredStateRequest, ::carbon::rtc::GetRobotRequiredStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RobotState::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::rtc::GetRobotRequiredStateRequest* req,
             ::carbon::rtc::GetRobotRequiredStateResponse* resp) {
               return service->GetNextRequiredState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RobotState_method_names[2],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< RobotState::Service, ::carbon::rtc::RobotStatusInfo, ::carbon::rtc::RobotRequiredState>(
          [](RobotState::Service* service,
             ::grpc::ServerContext* ctx,
             ::grpc::ServerReaderWriter<::carbon::rtc::RobotRequiredState,
             ::carbon::rtc::RobotStatusInfo>* stream) {
               return service->RobotRequirementStream(ctx, stream);
             }, this)));
}

RobotState::Service::~Service() {
}

::grpc::Status RobotState::Service::SetRobotRequiredState(::grpc::ServerContext* context, const ::carbon::rtc::SetRobotRequiredStateRequest* request, ::google::protobuf::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RobotState::Service::GetNextRequiredState(::grpc::ServerContext* context, const ::carbon::rtc::GetRobotRequiredStateRequest* request, ::carbon::rtc::GetRobotRequiredStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RobotState::Service::RobotRequirementStream(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::carbon::rtc::RobotRequiredState, ::carbon::rtc::RobotStatusInfo>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace rtc


# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from generated.proto.rtc import location_history_pb2 as proto_dot_rtc_dot_location__history__pb2


class LocationHistoryStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.LogLocationHistory = channel.unary_unary(
                '/carbon.rtc.LocationHistory/LogLocationHistory',
                request_serializer=proto_dot_rtc_dot_location__history__pb2.LogLocationHistoryRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ListRobots = channel.unary_unary(
                '/carbon.rtc.LocationHistory/ListRobots',
                request_serializer=proto_dot_rtc_dot_location__history__pb2.ListRobotsRequest.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_location__history__pb2.ListRobotsResponse.FromString,
                )
        self.ListLocationHistory = channel.unary_unary(
                '/carbon.rtc.LocationHistory/ListLocationHistory',
                request_serializer=proto_dot_rtc_dot_location__history__pb2.ListLocationHistoryRequest.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_location__history__pb2.ListLocationHistoryResponse.FromString,
                )
        self.StreamLocation = channel.stream_unary(
                '/carbon.rtc.LocationHistory/StreamLocation',
                request_serializer=proto_dot_rtc_dot_location__history__pb2.LocationHistoryRecord.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )


class LocationHistoryServicer(object):
    """Missing associated documentation comment in .proto file."""

    def LogLocationHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListRobots(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListLocationHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StreamLocation(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LocationHistoryServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'LogLocationHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.LogLocationHistory,
                    request_deserializer=proto_dot_rtc_dot_location__history__pb2.LogLocationHistoryRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ListRobots': grpc.unary_unary_rpc_method_handler(
                    servicer.ListRobots,
                    request_deserializer=proto_dot_rtc_dot_location__history__pb2.ListRobotsRequest.FromString,
                    response_serializer=proto_dot_rtc_dot_location__history__pb2.ListRobotsResponse.SerializeToString,
            ),
            'ListLocationHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.ListLocationHistory,
                    request_deserializer=proto_dot_rtc_dot_location__history__pb2.ListLocationHistoryRequest.FromString,
                    response_serializer=proto_dot_rtc_dot_location__history__pb2.ListLocationHistoryResponse.SerializeToString,
            ),
            'StreamLocation': grpc.stream_unary_rpc_method_handler(
                    servicer.StreamLocation,
                    request_deserializer=proto_dot_rtc_dot_location__history__pb2.LocationHistoryRecord.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.rtc.LocationHistory', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class LocationHistory(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def LogLocationHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.LocationHistory/LogLocationHistory',
            proto_dot_rtc_dot_location__history__pb2.LogLocationHistoryRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListRobots(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.LocationHistory/ListRobots',
            proto_dot_rtc_dot_location__history__pb2.ListRobotsRequest.SerializeToString,
            proto_dot_rtc_dot_location__history__pb2.ListRobotsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListLocationHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.LocationHistory/ListLocationHistory',
            proto_dot_rtc_dot_location__history__pb2.ListLocationHistoryRequest.SerializeToString,
            proto_dot_rtc_dot_location__history__pb2.ListLocationHistoryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StreamLocation(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(request_iterator, target, '/carbon.rtc.LocationHistory/StreamLocation',
            proto_dot_rtc_dot_location__history__pb2.LocationHistoryRecord.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/rtc/location_history.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_proto_2frtc_2flocation_5fhistory_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_proto_2frtc_2flocation_5fhistory_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/empty.pb.h>
#include <google/protobuf/timestamp.pb.h>
#include "proto/geo/geo.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_proto_2frtc_2flocation_5fhistory_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_proto_2frtc_2flocation_5fhistory_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[9]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto;
namespace carbon {
namespace rtc {
class ListLocationHistoryRequest;
struct ListLocationHistoryRequestDefaultTypeInternal;
extern ListLocationHistoryRequestDefaultTypeInternal _ListLocationHistoryRequest_default_instance_;
class ListLocationHistoryResponse;
struct ListLocationHistoryResponseDefaultTypeInternal;
extern ListLocationHistoryResponseDefaultTypeInternal _ListLocationHistoryResponse_default_instance_;
class ListRobotsRequest;
struct ListRobotsRequestDefaultTypeInternal;
extern ListRobotsRequestDefaultTypeInternal _ListRobotsRequest_default_instance_;
class ListRobotsResponse;
struct ListRobotsResponseDefaultTypeInternal;
extern ListRobotsResponseDefaultTypeInternal _ListRobotsResponse_default_instance_;
class LocationHistoryRecord;
struct LocationHistoryRecordDefaultTypeInternal;
extern LocationHistoryRecordDefaultTypeInternal _LocationHistoryRecord_default_instance_;
class LocationHistoryRecordList;
struct LocationHistoryRecordListDefaultTypeInternal;
extern LocationHistoryRecordListDefaultTypeInternal _LocationHistoryRecordList_default_instance_;
class LogLocationHistoryRequest;
struct LogLocationHistoryRequestDefaultTypeInternal;
extern LogLocationHistoryRequestDefaultTypeInternal _LogLocationHistoryRequest_default_instance_;
class RobotData;
struct RobotDataDefaultTypeInternal;
extern RobotDataDefaultTypeInternal _RobotData_default_instance_;
class RobotSummary;
struct RobotSummaryDefaultTypeInternal;
extern RobotSummaryDefaultTypeInternal _RobotSummary_default_instance_;
}  // namespace rtc
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::rtc::ListLocationHistoryRequest* Arena::CreateMaybeMessage<::carbon::rtc::ListLocationHistoryRequest>(Arena*);
template<> ::carbon::rtc::ListLocationHistoryResponse* Arena::CreateMaybeMessage<::carbon::rtc::ListLocationHistoryResponse>(Arena*);
template<> ::carbon::rtc::ListRobotsRequest* Arena::CreateMaybeMessage<::carbon::rtc::ListRobotsRequest>(Arena*);
template<> ::carbon::rtc::ListRobotsResponse* Arena::CreateMaybeMessage<::carbon::rtc::ListRobotsResponse>(Arena*);
template<> ::carbon::rtc::LocationHistoryRecord* Arena::CreateMaybeMessage<::carbon::rtc::LocationHistoryRecord>(Arena*);
template<> ::carbon::rtc::LocationHistoryRecordList* Arena::CreateMaybeMessage<::carbon::rtc::LocationHistoryRecordList>(Arena*);
template<> ::carbon::rtc::LogLocationHistoryRequest* Arena::CreateMaybeMessage<::carbon::rtc::LogLocationHistoryRequest>(Arena*);
template<> ::carbon::rtc::RobotData* Arena::CreateMaybeMessage<::carbon::rtc::RobotData>(Arena*);
template<> ::carbon::rtc::RobotSummary* Arena::CreateMaybeMessage<::carbon::rtc::RobotSummary>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace rtc {

// ===================================================================

class RobotData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.RobotData) */ {
 public:
  inline RobotData() : RobotData(nullptr) {}
  ~RobotData() override;
  explicit constexpr RobotData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RobotData(const RobotData& from);
  RobotData(RobotData&& from) noexcept
    : RobotData() {
    *this = ::std::move(from);
  }

  inline RobotData& operator=(const RobotData& from) {
    CopyFrom(from);
    return *this;
  }
  inline RobotData& operator=(RobotData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RobotData& default_instance() {
    return *internal_default_instance();
  }
  static inline const RobotData* internal_default_instance() {
    return reinterpret_cast<const RobotData*>(
               &_RobotData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RobotData& a, RobotData& b) {
    a.Swap(&b);
  }
  inline void Swap(RobotData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RobotData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RobotData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RobotData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RobotData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RobotData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RobotData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.RobotData";
  }
  protected:
  explicit RobotData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTaskIdFieldNumber = 1,
    kObjectiveIdFieldNumber = 3,
    kActiveFieldNumber = 2,
  };
  // uint64 task_id = 1;
  void clear_task_id();
  uint64_t task_id() const;
  void set_task_id(uint64_t value);
  private:
  uint64_t _internal_task_id() const;
  void _internal_set_task_id(uint64_t value);
  public:

  // uint64 objective_id = 3;
  void clear_objective_id();
  uint64_t objective_id() const;
  void set_objective_id(uint64_t value);
  private:
  uint64_t _internal_objective_id() const;
  void _internal_set_objective_id(uint64_t value);
  public:

  // bool active = 2;
  void clear_active();
  bool active() const;
  void set_active(bool value);
  private:
  bool _internal_active() const;
  void _internal_set_active(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.RobotData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t task_id_;
  uint64_t objective_id_;
  bool active_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2flocation_5fhistory_2eproto;
};
// -------------------------------------------------------------------

class LocationHistoryRecord final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.LocationHistoryRecord) */ {
 public:
  inline LocationHistoryRecord() : LocationHistoryRecord(nullptr) {}
  ~LocationHistoryRecord() override;
  explicit constexpr LocationHistoryRecord(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LocationHistoryRecord(const LocationHistoryRecord& from);
  LocationHistoryRecord(LocationHistoryRecord&& from) noexcept
    : LocationHistoryRecord() {
    *this = ::std::move(from);
  }

  inline LocationHistoryRecord& operator=(const LocationHistoryRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline LocationHistoryRecord& operator=(LocationHistoryRecord&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LocationHistoryRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const LocationHistoryRecord* internal_default_instance() {
    return reinterpret_cast<const LocationHistoryRecord*>(
               &_LocationHistoryRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(LocationHistoryRecord& a, LocationHistoryRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(LocationHistoryRecord* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LocationHistoryRecord* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LocationHistoryRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LocationHistoryRecord>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LocationHistoryRecord& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LocationHistoryRecord& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LocationHistoryRecord* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.LocationHistoryRecord";
  }
  protected:
  explicit LocationHistoryRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointFieldNumber = 1,
    kTimestampFieldNumber = 2,
    kDataFieldNumber = 3,
    kHeadingDegreesFieldNumber = 4,
  };
  // .carbon.geo.Point point = 1;
  bool has_point() const;
  private:
  bool _internal_has_point() const;
  public:
  void clear_point();
  const ::carbon::geo::Point& point() const;
  PROTOBUF_NODISCARD ::carbon::geo::Point* release_point();
  ::carbon::geo::Point* mutable_point();
  void set_allocated_point(::carbon::geo::Point* point);
  private:
  const ::carbon::geo::Point& _internal_point() const;
  ::carbon::geo::Point* _internal_mutable_point();
  public:
  void unsafe_arena_set_allocated_point(
      ::carbon::geo::Point* point);
  ::carbon::geo::Point* unsafe_arena_release_point();

  // .google.protobuf.Timestamp timestamp = 2;
  bool has_timestamp() const;
  private:
  bool _internal_has_timestamp() const;
  public:
  void clear_timestamp();
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& timestamp() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Timestamp* release_timestamp();
  ::PROTOBUF_NAMESPACE_ID::Timestamp* mutable_timestamp();
  void set_allocated_timestamp(::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& _internal_timestamp() const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _internal_mutable_timestamp();
  public:
  void unsafe_arena_set_allocated_timestamp(
      ::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp);
  ::PROTOBUF_NAMESPACE_ID::Timestamp* unsafe_arena_release_timestamp();

  // .carbon.rtc.RobotData data = 3;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const ::carbon::rtc::RobotData& data() const;
  PROTOBUF_NODISCARD ::carbon::rtc::RobotData* release_data();
  ::carbon::rtc::RobotData* mutable_data();
  void set_allocated_data(::carbon::rtc::RobotData* data);
  private:
  const ::carbon::rtc::RobotData& _internal_data() const;
  ::carbon::rtc::RobotData* _internal_mutable_data();
  public:
  void unsafe_arena_set_allocated_data(
      ::carbon::rtc::RobotData* data);
  ::carbon::rtc::RobotData* unsafe_arena_release_data();

  // optional double heading_degrees = 4;
  bool has_heading_degrees() const;
  private:
  bool _internal_has_heading_degrees() const;
  public:
  void clear_heading_degrees();
  double heading_degrees() const;
  void set_heading_degrees(double value);
  private:
  double _internal_heading_degrees() const;
  void _internal_set_heading_degrees(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.LocationHistoryRecord)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::carbon::geo::Point* point_;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp_;
  ::carbon::rtc::RobotData* data_;
  double heading_degrees_;
  friend struct ::TableStruct_proto_2frtc_2flocation_5fhistory_2eproto;
};
// -------------------------------------------------------------------

class LocationHistoryRecordList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.LocationHistoryRecordList) */ {
 public:
  inline LocationHistoryRecordList() : LocationHistoryRecordList(nullptr) {}
  ~LocationHistoryRecordList() override;
  explicit constexpr LocationHistoryRecordList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LocationHistoryRecordList(const LocationHistoryRecordList& from);
  LocationHistoryRecordList(LocationHistoryRecordList&& from) noexcept
    : LocationHistoryRecordList() {
    *this = ::std::move(from);
  }

  inline LocationHistoryRecordList& operator=(const LocationHistoryRecordList& from) {
    CopyFrom(from);
    return *this;
  }
  inline LocationHistoryRecordList& operator=(LocationHistoryRecordList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LocationHistoryRecordList& default_instance() {
    return *internal_default_instance();
  }
  static inline const LocationHistoryRecordList* internal_default_instance() {
    return reinterpret_cast<const LocationHistoryRecordList*>(
               &_LocationHistoryRecordList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(LocationHistoryRecordList& a, LocationHistoryRecordList& b) {
    a.Swap(&b);
  }
  inline void Swap(LocationHistoryRecordList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LocationHistoryRecordList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LocationHistoryRecordList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LocationHistoryRecordList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LocationHistoryRecordList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LocationHistoryRecordList& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LocationHistoryRecordList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.LocationHistoryRecordList";
  }
  protected:
  explicit LocationHistoryRecordList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRecordsFieldNumber = 1,
  };
  // repeated .carbon.rtc.LocationHistoryRecord records = 1;
  int records_size() const;
  private:
  int _internal_records_size() const;
  public:
  void clear_records();
  ::carbon::rtc::LocationHistoryRecord* mutable_records(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::LocationHistoryRecord >*
      mutable_records();
  private:
  const ::carbon::rtc::LocationHistoryRecord& _internal_records(int index) const;
  ::carbon::rtc::LocationHistoryRecord* _internal_add_records();
  public:
  const ::carbon::rtc::LocationHistoryRecord& records(int index) const;
  ::carbon::rtc::LocationHistoryRecord* add_records();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::LocationHistoryRecord >&
      records() const;

  // @@protoc_insertion_point(class_scope:carbon.rtc.LocationHistoryRecordList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::LocationHistoryRecord > records_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2flocation_5fhistory_2eproto;
};
// -------------------------------------------------------------------

class LogLocationHistoryRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.LogLocationHistoryRequest) */ {
 public:
  inline LogLocationHistoryRequest() : LogLocationHistoryRequest(nullptr) {}
  ~LogLocationHistoryRequest() override;
  explicit constexpr LogLocationHistoryRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LogLocationHistoryRequest(const LogLocationHistoryRequest& from);
  LogLocationHistoryRequest(LogLocationHistoryRequest&& from) noexcept
    : LogLocationHistoryRequest() {
    *this = ::std::move(from);
  }

  inline LogLocationHistoryRequest& operator=(const LogLocationHistoryRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline LogLocationHistoryRequest& operator=(LogLocationHistoryRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LogLocationHistoryRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const LogLocationHistoryRequest* internal_default_instance() {
    return reinterpret_cast<const LogLocationHistoryRequest*>(
               &_LogLocationHistoryRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(LogLocationHistoryRequest& a, LogLocationHistoryRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(LogLocationHistoryRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LogLocationHistoryRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LogLocationHistoryRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LogLocationHistoryRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LogLocationHistoryRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LogLocationHistoryRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogLocationHistoryRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.LogLocationHistoryRequest";
  }
  protected:
  explicit LogLocationHistoryRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHistoryFieldNumber = 1,
  };
  // .carbon.rtc.LocationHistoryRecordList history = 1;
  bool has_history() const;
  private:
  bool _internal_has_history() const;
  public:
  void clear_history();
  const ::carbon::rtc::LocationHistoryRecordList& history() const;
  PROTOBUF_NODISCARD ::carbon::rtc::LocationHistoryRecordList* release_history();
  ::carbon::rtc::LocationHistoryRecordList* mutable_history();
  void set_allocated_history(::carbon::rtc::LocationHistoryRecordList* history);
  private:
  const ::carbon::rtc::LocationHistoryRecordList& _internal_history() const;
  ::carbon::rtc::LocationHistoryRecordList* _internal_mutable_history();
  public:
  void unsafe_arena_set_allocated_history(
      ::carbon::rtc::LocationHistoryRecordList* history);
  ::carbon::rtc::LocationHistoryRecordList* unsafe_arena_release_history();

  // @@protoc_insertion_point(class_scope:carbon.rtc.LogLocationHistoryRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::rtc::LocationHistoryRecordList* history_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2flocation_5fhistory_2eproto;
};
// -------------------------------------------------------------------

class ListRobotsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.ListRobotsRequest) */ {
 public:
  inline ListRobotsRequest() : ListRobotsRequest(nullptr) {}
  ~ListRobotsRequest() override;
  explicit constexpr ListRobotsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListRobotsRequest(const ListRobotsRequest& from);
  ListRobotsRequest(ListRobotsRequest&& from) noexcept
    : ListRobotsRequest() {
    *this = ::std::move(from);
  }

  inline ListRobotsRequest& operator=(const ListRobotsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListRobotsRequest& operator=(ListRobotsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListRobotsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListRobotsRequest* internal_default_instance() {
    return reinterpret_cast<const ListRobotsRequest*>(
               &_ListRobotsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ListRobotsRequest& a, ListRobotsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ListRobotsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListRobotsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListRobotsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListRobotsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListRobotsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ListRobotsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListRobotsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.ListRobotsRequest";
  }
  protected:
  explicit ListRobotsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotSerialsFieldNumber = 3,
    kPageTokenFieldNumber = 2,
    kPageSizeFieldNumber = 1,
  };
  // repeated string robot_serials = 3;
  int robot_serials_size() const;
  private:
  int _internal_robot_serials_size() const;
  public:
  void clear_robot_serials();
  const std::string& robot_serials(int index) const;
  std::string* mutable_robot_serials(int index);
  void set_robot_serials(int index, const std::string& value);
  void set_robot_serials(int index, std::string&& value);
  void set_robot_serials(int index, const char* value);
  void set_robot_serials(int index, const char* value, size_t size);
  std::string* add_robot_serials();
  void add_robot_serials(const std::string& value);
  void add_robot_serials(std::string&& value);
  void add_robot_serials(const char* value);
  void add_robot_serials(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& robot_serials() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_robot_serials();
  private:
  const std::string& _internal_robot_serials(int index) const;
  std::string* _internal_add_robot_serials();
  public:

  // string page_token = 2;
  void clear_page_token();
  const std::string& page_token() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_page_token(ArgT0&& arg0, ArgT... args);
  std::string* mutable_page_token();
  PROTOBUF_NODISCARD std::string* release_page_token();
  void set_allocated_page_token(std::string* page_token);
  private:
  const std::string& _internal_page_token() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_page_token(const std::string& value);
  std::string* _internal_mutable_page_token();
  public:

  // int32 page_size = 1;
  void clear_page_size();
  int32_t page_size() const;
  void set_page_size(int32_t value);
  private:
  int32_t _internal_page_size() const;
  void _internal_set_page_size(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.ListRobotsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> robot_serials_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr page_token_;
  int32_t page_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2flocation_5fhistory_2eproto;
};
// -------------------------------------------------------------------

class ListRobotsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.ListRobotsResponse) */ {
 public:
  inline ListRobotsResponse() : ListRobotsResponse(nullptr) {}
  ~ListRobotsResponse() override;
  explicit constexpr ListRobotsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListRobotsResponse(const ListRobotsResponse& from);
  ListRobotsResponse(ListRobotsResponse&& from) noexcept
    : ListRobotsResponse() {
    *this = ::std::move(from);
  }

  inline ListRobotsResponse& operator=(const ListRobotsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListRobotsResponse& operator=(ListRobotsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListRobotsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListRobotsResponse* internal_default_instance() {
    return reinterpret_cast<const ListRobotsResponse*>(
               &_ListRobotsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ListRobotsResponse& a, ListRobotsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ListRobotsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListRobotsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListRobotsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListRobotsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListRobotsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ListRobotsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListRobotsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.ListRobotsResponse";
  }
  protected:
  explicit ListRobotsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotsFieldNumber = 2,
    kNextPageTokenFieldNumber = 1,
  };
  // repeated .carbon.rtc.RobotSummary robots = 2;
  int robots_size() const;
  private:
  int _internal_robots_size() const;
  public:
  void clear_robots();
  ::carbon::rtc::RobotSummary* mutable_robots(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::RobotSummary >*
      mutable_robots();
  private:
  const ::carbon::rtc::RobotSummary& _internal_robots(int index) const;
  ::carbon::rtc::RobotSummary* _internal_add_robots();
  public:
  const ::carbon::rtc::RobotSummary& robots(int index) const;
  ::carbon::rtc::RobotSummary* add_robots();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::RobotSummary >&
      robots() const;

  // string next_page_token = 1;
  void clear_next_page_token();
  const std::string& next_page_token() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_next_page_token(ArgT0&& arg0, ArgT... args);
  std::string* mutable_next_page_token();
  PROTOBUF_NODISCARD std::string* release_next_page_token();
  void set_allocated_next_page_token(std::string* next_page_token);
  private:
  const std::string& _internal_next_page_token() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_next_page_token(const std::string& value);
  std::string* _internal_mutable_next_page_token();
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.ListRobotsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::RobotSummary > robots_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr next_page_token_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2flocation_5fhistory_2eproto;
};
// -------------------------------------------------------------------

class RobotSummary final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.RobotSummary) */ {
 public:
  inline RobotSummary() : RobotSummary(nullptr) {}
  ~RobotSummary() override;
  explicit constexpr RobotSummary(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RobotSummary(const RobotSummary& from);
  RobotSummary(RobotSummary&& from) noexcept
    : RobotSummary() {
    *this = ::std::move(from);
  }

  inline RobotSummary& operator=(const RobotSummary& from) {
    CopyFrom(from);
    return *this;
  }
  inline RobotSummary& operator=(RobotSummary&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RobotSummary& default_instance() {
    return *internal_default_instance();
  }
  static inline const RobotSummary* internal_default_instance() {
    return reinterpret_cast<const RobotSummary*>(
               &_RobotSummary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(RobotSummary& a, RobotSummary& b) {
    a.Swap(&b);
  }
  inline void Swap(RobotSummary* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RobotSummary* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RobotSummary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RobotSummary>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RobotSummary& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RobotSummary& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RobotSummary* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.RobotSummary";
  }
  protected:
  explicit RobotSummary(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSerialFieldNumber = 1,
    kLastSeenFieldNumber = 2,
  };
  // string serial = 1;
  void clear_serial();
  const std::string& serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serial();
  PROTOBUF_NODISCARD std::string* release_serial();
  void set_allocated_serial(std::string* serial);
  private:
  const std::string& _internal_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serial(const std::string& value);
  std::string* _internal_mutable_serial();
  public:

  // .carbon.rtc.LocationHistoryRecord last_seen = 2;
  bool has_last_seen() const;
  private:
  bool _internal_has_last_seen() const;
  public:
  void clear_last_seen();
  const ::carbon::rtc::LocationHistoryRecord& last_seen() const;
  PROTOBUF_NODISCARD ::carbon::rtc::LocationHistoryRecord* release_last_seen();
  ::carbon::rtc::LocationHistoryRecord* mutable_last_seen();
  void set_allocated_last_seen(::carbon::rtc::LocationHistoryRecord* last_seen);
  private:
  const ::carbon::rtc::LocationHistoryRecord& _internal_last_seen() const;
  ::carbon::rtc::LocationHistoryRecord* _internal_mutable_last_seen();
  public:
  void unsafe_arena_set_allocated_last_seen(
      ::carbon::rtc::LocationHistoryRecord* last_seen);
  ::carbon::rtc::LocationHistoryRecord* unsafe_arena_release_last_seen();

  // @@protoc_insertion_point(class_scope:carbon.rtc.RobotSummary)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_;
  ::carbon::rtc::LocationHistoryRecord* last_seen_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2flocation_5fhistory_2eproto;
};
// -------------------------------------------------------------------

class ListLocationHistoryRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.ListLocationHistoryRequest) */ {
 public:
  inline ListLocationHistoryRequest() : ListLocationHistoryRequest(nullptr) {}
  ~ListLocationHistoryRequest() override;
  explicit constexpr ListLocationHistoryRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListLocationHistoryRequest(const ListLocationHistoryRequest& from);
  ListLocationHistoryRequest(ListLocationHistoryRequest&& from) noexcept
    : ListLocationHistoryRequest() {
    *this = ::std::move(from);
  }

  inline ListLocationHistoryRequest& operator=(const ListLocationHistoryRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListLocationHistoryRequest& operator=(ListLocationHistoryRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListLocationHistoryRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListLocationHistoryRequest* internal_default_instance() {
    return reinterpret_cast<const ListLocationHistoryRequest*>(
               &_ListLocationHistoryRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(ListLocationHistoryRequest& a, ListLocationHistoryRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ListLocationHistoryRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListLocationHistoryRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListLocationHistoryRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListLocationHistoryRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListLocationHistoryRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ListLocationHistoryRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListLocationHistoryRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.ListLocationHistoryRequest";
  }
  protected:
  explicit ListLocationHistoryRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPageTokenFieldNumber = 2,
    kRobotSerialFieldNumber = 3,
    kStartFieldNumber = 4,
    kEndFieldNumber = 5,
    kPageSizeFieldNumber = 1,
    kDescFieldNumber = 6,
    kIncludeClosestFieldNumber = 7,
    kTaskIdFieldNumber = 8,
    kObjectiveIdFieldNumber = 9,
  };
  // string page_token = 2;
  void clear_page_token();
  const std::string& page_token() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_page_token(ArgT0&& arg0, ArgT... args);
  std::string* mutable_page_token();
  PROTOBUF_NODISCARD std::string* release_page_token();
  void set_allocated_page_token(std::string* page_token);
  private:
  const std::string& _internal_page_token() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_page_token(const std::string& value);
  std::string* _internal_mutable_page_token();
  public:

  // string robot_serial = 3;
  void clear_robot_serial();
  const std::string& robot_serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_serial();
  PROTOBUF_NODISCARD std::string* release_robot_serial();
  void set_allocated_robot_serial(std::string* robot_serial);
  private:
  const std::string& _internal_robot_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_serial(const std::string& value);
  std::string* _internal_mutable_robot_serial();
  public:

  // .google.protobuf.Timestamp start = 4;
  bool has_start() const;
  private:
  bool _internal_has_start() const;
  public:
  void clear_start();
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& start() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Timestamp* release_start();
  ::PROTOBUF_NAMESPACE_ID::Timestamp* mutable_start();
  void set_allocated_start(::PROTOBUF_NAMESPACE_ID::Timestamp* start);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& _internal_start() const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _internal_mutable_start();
  public:
  void unsafe_arena_set_allocated_start(
      ::PROTOBUF_NAMESPACE_ID::Timestamp* start);
  ::PROTOBUF_NAMESPACE_ID::Timestamp* unsafe_arena_release_start();

  // .google.protobuf.Timestamp end = 5;
  bool has_end() const;
  private:
  bool _internal_has_end() const;
  public:
  void clear_end();
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& end() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Timestamp* release_end();
  ::PROTOBUF_NAMESPACE_ID::Timestamp* mutable_end();
  void set_allocated_end(::PROTOBUF_NAMESPACE_ID::Timestamp* end);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Timestamp& _internal_end() const;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _internal_mutable_end();
  public:
  void unsafe_arena_set_allocated_end(
      ::PROTOBUF_NAMESPACE_ID::Timestamp* end);
  ::PROTOBUF_NAMESPACE_ID::Timestamp* unsafe_arena_release_end();

  // int32 page_size = 1;
  void clear_page_size();
  int32_t page_size() const;
  void set_page_size(int32_t value);
  private:
  int32_t _internal_page_size() const;
  void _internal_set_page_size(int32_t value);
  public:

  // bool desc = 6;
  void clear_desc();
  bool desc() const;
  void set_desc(bool value);
  private:
  bool _internal_desc() const;
  void _internal_set_desc(bool value);
  public:

  // bool include_closest = 7;
  void clear_include_closest();
  bool include_closest() const;
  void set_include_closest(bool value);
  private:
  bool _internal_include_closest() const;
  void _internal_set_include_closest(bool value);
  public:

  // uint64 task_id = 8;
  void clear_task_id();
  uint64_t task_id() const;
  void set_task_id(uint64_t value);
  private:
  uint64_t _internal_task_id() const;
  void _internal_set_task_id(uint64_t value);
  public:

  // uint64 objective_id = 9;
  void clear_objective_id();
  uint64_t objective_id() const;
  void set_objective_id(uint64_t value);
  private:
  uint64_t _internal_objective_id() const;
  void _internal_set_objective_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.rtc.ListLocationHistoryRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr page_token_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_serial_;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* start_;
  ::PROTOBUF_NAMESPACE_ID::Timestamp* end_;
  int32_t page_size_;
  bool desc_;
  bool include_closest_;
  uint64_t task_id_;
  uint64_t objective_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2flocation_5fhistory_2eproto;
};
// -------------------------------------------------------------------

class ListLocationHistoryResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.rtc.ListLocationHistoryResponse) */ {
 public:
  inline ListLocationHistoryResponse() : ListLocationHistoryResponse(nullptr) {}
  ~ListLocationHistoryResponse() override;
  explicit constexpr ListLocationHistoryResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListLocationHistoryResponse(const ListLocationHistoryResponse& from);
  ListLocationHistoryResponse(ListLocationHistoryResponse&& from) noexcept
    : ListLocationHistoryResponse() {
    *this = ::std::move(from);
  }

  inline ListLocationHistoryResponse& operator=(const ListLocationHistoryResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListLocationHistoryResponse& operator=(ListLocationHistoryResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListLocationHistoryResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListLocationHistoryResponse* internal_default_instance() {
    return reinterpret_cast<const ListLocationHistoryResponse*>(
               &_ListLocationHistoryResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(ListLocationHistoryResponse& a, ListLocationHistoryResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ListLocationHistoryResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListLocationHistoryResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListLocationHistoryResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListLocationHistoryResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListLocationHistoryResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ListLocationHistoryResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListLocationHistoryResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.rtc.ListLocationHistoryResponse";
  }
  protected:
  explicit ListLocationHistoryResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNextPageTokenFieldNumber = 1,
    kHistoryFieldNumber = 2,
  };
  // string next_page_token = 1;
  void clear_next_page_token();
  const std::string& next_page_token() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_next_page_token(ArgT0&& arg0, ArgT... args);
  std::string* mutable_next_page_token();
  PROTOBUF_NODISCARD std::string* release_next_page_token();
  void set_allocated_next_page_token(std::string* next_page_token);
  private:
  const std::string& _internal_next_page_token() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_next_page_token(const std::string& value);
  std::string* _internal_mutable_next_page_token();
  public:

  // .carbon.rtc.LocationHistoryRecordList history = 2;
  bool has_history() const;
  private:
  bool _internal_has_history() const;
  public:
  void clear_history();
  const ::carbon::rtc::LocationHistoryRecordList& history() const;
  PROTOBUF_NODISCARD ::carbon::rtc::LocationHistoryRecordList* release_history();
  ::carbon::rtc::LocationHistoryRecordList* mutable_history();
  void set_allocated_history(::carbon::rtc::LocationHistoryRecordList* history);
  private:
  const ::carbon::rtc::LocationHistoryRecordList& _internal_history() const;
  ::carbon::rtc::LocationHistoryRecordList* _internal_mutable_history();
  public:
  void unsafe_arena_set_allocated_history(
      ::carbon::rtc::LocationHistoryRecordList* history);
  ::carbon::rtc::LocationHistoryRecordList* unsafe_arena_release_history();

  // @@protoc_insertion_point(class_scope:carbon.rtc.ListLocationHistoryResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr next_page_token_;
  ::carbon::rtc::LocationHistoryRecordList* history_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_proto_2frtc_2flocation_5fhistory_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RobotData

// uint64 task_id = 1;
inline void RobotData::clear_task_id() {
  task_id_ = uint64_t{0u};
}
inline uint64_t RobotData::_internal_task_id() const {
  return task_id_;
}
inline uint64_t RobotData::task_id() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RobotData.task_id)
  return _internal_task_id();
}
inline void RobotData::_internal_set_task_id(uint64_t value) {
  
  task_id_ = value;
}
inline void RobotData::set_task_id(uint64_t value) {
  _internal_set_task_id(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.RobotData.task_id)
}

// bool active = 2;
inline void RobotData::clear_active() {
  active_ = false;
}
inline bool RobotData::_internal_active() const {
  return active_;
}
inline bool RobotData::active() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RobotData.active)
  return _internal_active();
}
inline void RobotData::_internal_set_active(bool value) {
  
  active_ = value;
}
inline void RobotData::set_active(bool value) {
  _internal_set_active(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.RobotData.active)
}

// uint64 objective_id = 3;
inline void RobotData::clear_objective_id() {
  objective_id_ = uint64_t{0u};
}
inline uint64_t RobotData::_internal_objective_id() const {
  return objective_id_;
}
inline uint64_t RobotData::objective_id() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RobotData.objective_id)
  return _internal_objective_id();
}
inline void RobotData::_internal_set_objective_id(uint64_t value) {
  
  objective_id_ = value;
}
inline void RobotData::set_objective_id(uint64_t value) {
  _internal_set_objective_id(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.RobotData.objective_id)
}

// -------------------------------------------------------------------

// LocationHistoryRecord

// .carbon.geo.Point point = 1;
inline bool LocationHistoryRecord::_internal_has_point() const {
  return this != internal_default_instance() && point_ != nullptr;
}
inline bool LocationHistoryRecord::has_point() const {
  return _internal_has_point();
}
inline const ::carbon::geo::Point& LocationHistoryRecord::_internal_point() const {
  const ::carbon::geo::Point* p = point_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::geo::Point&>(
      ::carbon::geo::_Point_default_instance_);
}
inline const ::carbon::geo::Point& LocationHistoryRecord::point() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.LocationHistoryRecord.point)
  return _internal_point();
}
inline void LocationHistoryRecord::unsafe_arena_set_allocated_point(
    ::carbon::geo::Point* point) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(point_);
  }
  point_ = point;
  if (point) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.LocationHistoryRecord.point)
}
inline ::carbon::geo::Point* LocationHistoryRecord::release_point() {
  
  ::carbon::geo::Point* temp = point_;
  point_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::geo::Point* LocationHistoryRecord::unsafe_arena_release_point() {
  // @@protoc_insertion_point(field_release:carbon.rtc.LocationHistoryRecord.point)
  
  ::carbon::geo::Point* temp = point_;
  point_ = nullptr;
  return temp;
}
inline ::carbon::geo::Point* LocationHistoryRecord::_internal_mutable_point() {
  
  if (point_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::geo::Point>(GetArenaForAllocation());
    point_ = p;
  }
  return point_;
}
inline ::carbon::geo::Point* LocationHistoryRecord::mutable_point() {
  ::carbon::geo::Point* _msg = _internal_mutable_point();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.LocationHistoryRecord.point)
  return _msg;
}
inline void LocationHistoryRecord::set_allocated_point(::carbon::geo::Point* point) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(point_);
  }
  if (point) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(point));
    if (message_arena != submessage_arena) {
      point = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, point, submessage_arena);
    }
    
  } else {
    
  }
  point_ = point;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.LocationHistoryRecord.point)
}

// optional double heading_degrees = 4;
inline bool LocationHistoryRecord::_internal_has_heading_degrees() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool LocationHistoryRecord::has_heading_degrees() const {
  return _internal_has_heading_degrees();
}
inline void LocationHistoryRecord::clear_heading_degrees() {
  heading_degrees_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline double LocationHistoryRecord::_internal_heading_degrees() const {
  return heading_degrees_;
}
inline double LocationHistoryRecord::heading_degrees() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.LocationHistoryRecord.heading_degrees)
  return _internal_heading_degrees();
}
inline void LocationHistoryRecord::_internal_set_heading_degrees(double value) {
  _has_bits_[0] |= 0x00000001u;
  heading_degrees_ = value;
}
inline void LocationHistoryRecord::set_heading_degrees(double value) {
  _internal_set_heading_degrees(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.LocationHistoryRecord.heading_degrees)
}

// .google.protobuf.Timestamp timestamp = 2;
inline bool LocationHistoryRecord::_internal_has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != nullptr;
}
inline bool LocationHistoryRecord::has_timestamp() const {
  return _internal_has_timestamp();
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& LocationHistoryRecord::_internal_timestamp() const {
  const ::PROTOBUF_NAMESPACE_ID::Timestamp* p = timestamp_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Timestamp&>(
      ::PROTOBUF_NAMESPACE_ID::_Timestamp_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& LocationHistoryRecord::timestamp() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.LocationHistoryRecord.timestamp)
  return _internal_timestamp();
}
inline void LocationHistoryRecord::unsafe_arena_set_allocated_timestamp(
    ::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  timestamp_ = timestamp;
  if (timestamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.LocationHistoryRecord.timestamp)
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* LocationHistoryRecord::release_timestamp() {
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* LocationHistoryRecord::unsafe_arena_release_timestamp() {
  // @@protoc_insertion_point(field_release:carbon.rtc.LocationHistoryRecord.timestamp)
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = timestamp_;
  timestamp_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* LocationHistoryRecord::_internal_mutable_timestamp() {
  
  if (timestamp_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Timestamp>(GetArenaForAllocation());
    timestamp_ = p;
  }
  return timestamp_;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* LocationHistoryRecord::mutable_timestamp() {
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _msg = _internal_mutable_timestamp();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.LocationHistoryRecord.timestamp)
  return _msg;
}
inline void LocationHistoryRecord::set_allocated_timestamp(::PROTOBUF_NAMESPACE_ID::Timestamp* timestamp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp_);
  }
  if (timestamp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timestamp));
    if (message_arena != submessage_arena) {
      timestamp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, timestamp, submessage_arena);
    }
    
  } else {
    
  }
  timestamp_ = timestamp;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.LocationHistoryRecord.timestamp)
}

// .carbon.rtc.RobotData data = 3;
inline bool LocationHistoryRecord::_internal_has_data() const {
  return this != internal_default_instance() && data_ != nullptr;
}
inline bool LocationHistoryRecord::has_data() const {
  return _internal_has_data();
}
inline void LocationHistoryRecord::clear_data() {
  if (GetArenaForAllocation() == nullptr && data_ != nullptr) {
    delete data_;
  }
  data_ = nullptr;
}
inline const ::carbon::rtc::RobotData& LocationHistoryRecord::_internal_data() const {
  const ::carbon::rtc::RobotData* p = data_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::rtc::RobotData&>(
      ::carbon::rtc::_RobotData_default_instance_);
}
inline const ::carbon::rtc::RobotData& LocationHistoryRecord::data() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.LocationHistoryRecord.data)
  return _internal_data();
}
inline void LocationHistoryRecord::unsafe_arena_set_allocated_data(
    ::carbon::rtc::RobotData* data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_);
  }
  data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.LocationHistoryRecord.data)
}
inline ::carbon::rtc::RobotData* LocationHistoryRecord::release_data() {
  
  ::carbon::rtc::RobotData* temp = data_;
  data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::rtc::RobotData* LocationHistoryRecord::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_release:carbon.rtc.LocationHistoryRecord.data)
  
  ::carbon::rtc::RobotData* temp = data_;
  data_ = nullptr;
  return temp;
}
inline ::carbon::rtc::RobotData* LocationHistoryRecord::_internal_mutable_data() {
  
  if (data_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::rtc::RobotData>(GetArenaForAllocation());
    data_ = p;
  }
  return data_;
}
inline ::carbon::rtc::RobotData* LocationHistoryRecord::mutable_data() {
  ::carbon::rtc::RobotData* _msg = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.LocationHistoryRecord.data)
  return _msg;
}
inline void LocationHistoryRecord::set_allocated_data(::carbon::rtc::RobotData* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete data_;
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::RobotData>::GetOwningArena(data);
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.LocationHistoryRecord.data)
}

// -------------------------------------------------------------------

// LocationHistoryRecordList

// repeated .carbon.rtc.LocationHistoryRecord records = 1;
inline int LocationHistoryRecordList::_internal_records_size() const {
  return records_.size();
}
inline int LocationHistoryRecordList::records_size() const {
  return _internal_records_size();
}
inline void LocationHistoryRecordList::clear_records() {
  records_.Clear();
}
inline ::carbon::rtc::LocationHistoryRecord* LocationHistoryRecordList::mutable_records(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.rtc.LocationHistoryRecordList.records)
  return records_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::LocationHistoryRecord >*
LocationHistoryRecordList::mutable_records() {
  // @@protoc_insertion_point(field_mutable_list:carbon.rtc.LocationHistoryRecordList.records)
  return &records_;
}
inline const ::carbon::rtc::LocationHistoryRecord& LocationHistoryRecordList::_internal_records(int index) const {
  return records_.Get(index);
}
inline const ::carbon::rtc::LocationHistoryRecord& LocationHistoryRecordList::records(int index) const {
  // @@protoc_insertion_point(field_get:carbon.rtc.LocationHistoryRecordList.records)
  return _internal_records(index);
}
inline ::carbon::rtc::LocationHistoryRecord* LocationHistoryRecordList::_internal_add_records() {
  return records_.Add();
}
inline ::carbon::rtc::LocationHistoryRecord* LocationHistoryRecordList::add_records() {
  ::carbon::rtc::LocationHistoryRecord* _add = _internal_add_records();
  // @@protoc_insertion_point(field_add:carbon.rtc.LocationHistoryRecordList.records)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::LocationHistoryRecord >&
LocationHistoryRecordList::records() const {
  // @@protoc_insertion_point(field_list:carbon.rtc.LocationHistoryRecordList.records)
  return records_;
}

// -------------------------------------------------------------------

// LogLocationHistoryRequest

// .carbon.rtc.LocationHistoryRecordList history = 1;
inline bool LogLocationHistoryRequest::_internal_has_history() const {
  return this != internal_default_instance() && history_ != nullptr;
}
inline bool LogLocationHistoryRequest::has_history() const {
  return _internal_has_history();
}
inline void LogLocationHistoryRequest::clear_history() {
  if (GetArenaForAllocation() == nullptr && history_ != nullptr) {
    delete history_;
  }
  history_ = nullptr;
}
inline const ::carbon::rtc::LocationHistoryRecordList& LogLocationHistoryRequest::_internal_history() const {
  const ::carbon::rtc::LocationHistoryRecordList* p = history_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::rtc::LocationHistoryRecordList&>(
      ::carbon::rtc::_LocationHistoryRecordList_default_instance_);
}
inline const ::carbon::rtc::LocationHistoryRecordList& LogLocationHistoryRequest::history() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.LogLocationHistoryRequest.history)
  return _internal_history();
}
inline void LogLocationHistoryRequest::unsafe_arena_set_allocated_history(
    ::carbon::rtc::LocationHistoryRecordList* history) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(history_);
  }
  history_ = history;
  if (history) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.LogLocationHistoryRequest.history)
}
inline ::carbon::rtc::LocationHistoryRecordList* LogLocationHistoryRequest::release_history() {
  
  ::carbon::rtc::LocationHistoryRecordList* temp = history_;
  history_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::rtc::LocationHistoryRecordList* LogLocationHistoryRequest::unsafe_arena_release_history() {
  // @@protoc_insertion_point(field_release:carbon.rtc.LogLocationHistoryRequest.history)
  
  ::carbon::rtc::LocationHistoryRecordList* temp = history_;
  history_ = nullptr;
  return temp;
}
inline ::carbon::rtc::LocationHistoryRecordList* LogLocationHistoryRequest::_internal_mutable_history() {
  
  if (history_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::rtc::LocationHistoryRecordList>(GetArenaForAllocation());
    history_ = p;
  }
  return history_;
}
inline ::carbon::rtc::LocationHistoryRecordList* LogLocationHistoryRequest::mutable_history() {
  ::carbon::rtc::LocationHistoryRecordList* _msg = _internal_mutable_history();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.LogLocationHistoryRequest.history)
  return _msg;
}
inline void LogLocationHistoryRequest::set_allocated_history(::carbon::rtc::LocationHistoryRecordList* history) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete history_;
  }
  if (history) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::LocationHistoryRecordList>::GetOwningArena(history);
    if (message_arena != submessage_arena) {
      history = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, history, submessage_arena);
    }
    
  } else {
    
  }
  history_ = history;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.LogLocationHistoryRequest.history)
}

// -------------------------------------------------------------------

// ListRobotsRequest

// int32 page_size = 1;
inline void ListRobotsRequest::clear_page_size() {
  page_size_ = 0;
}
inline int32_t ListRobotsRequest::_internal_page_size() const {
  return page_size_;
}
inline int32_t ListRobotsRequest::page_size() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListRobotsRequest.page_size)
  return _internal_page_size();
}
inline void ListRobotsRequest::_internal_set_page_size(int32_t value) {
  
  page_size_ = value;
}
inline void ListRobotsRequest::set_page_size(int32_t value) {
  _internal_set_page_size(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.ListRobotsRequest.page_size)
}

// string page_token = 2;
inline void ListRobotsRequest::clear_page_token() {
  page_token_.ClearToEmpty();
}
inline const std::string& ListRobotsRequest::page_token() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListRobotsRequest.page_token)
  return _internal_page_token();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ListRobotsRequest::set_page_token(ArgT0&& arg0, ArgT... args) {
 
 page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.ListRobotsRequest.page_token)
}
inline std::string* ListRobotsRequest::mutable_page_token() {
  std::string* _s = _internal_mutable_page_token();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListRobotsRequest.page_token)
  return _s;
}
inline const std::string& ListRobotsRequest::_internal_page_token() const {
  return page_token_.Get();
}
inline void ListRobotsRequest::_internal_set_page_token(const std::string& value) {
  
  page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ListRobotsRequest::_internal_mutable_page_token() {
  
  return page_token_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ListRobotsRequest::release_page_token() {
  // @@protoc_insertion_point(field_release:carbon.rtc.ListRobotsRequest.page_token)
  return page_token_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ListRobotsRequest::set_allocated_page_token(std::string* page_token) {
  if (page_token != nullptr) {
    
  } else {
    
  }
  page_token_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), page_token,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (page_token_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.ListRobotsRequest.page_token)
}

// repeated string robot_serials = 3;
inline int ListRobotsRequest::_internal_robot_serials_size() const {
  return robot_serials_.size();
}
inline int ListRobotsRequest::robot_serials_size() const {
  return _internal_robot_serials_size();
}
inline void ListRobotsRequest::clear_robot_serials() {
  robot_serials_.Clear();
}
inline std::string* ListRobotsRequest::add_robot_serials() {
  std::string* _s = _internal_add_robot_serials();
  // @@protoc_insertion_point(field_add_mutable:carbon.rtc.ListRobotsRequest.robot_serials)
  return _s;
}
inline const std::string& ListRobotsRequest::_internal_robot_serials(int index) const {
  return robot_serials_.Get(index);
}
inline const std::string& ListRobotsRequest::robot_serials(int index) const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListRobotsRequest.robot_serials)
  return _internal_robot_serials(index);
}
inline std::string* ListRobotsRequest::mutable_robot_serials(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListRobotsRequest.robot_serials)
  return robot_serials_.Mutable(index);
}
inline void ListRobotsRequest::set_robot_serials(int index, const std::string& value) {
  robot_serials_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.ListRobotsRequest.robot_serials)
}
inline void ListRobotsRequest::set_robot_serials(int index, std::string&& value) {
  robot_serials_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.rtc.ListRobotsRequest.robot_serials)
}
inline void ListRobotsRequest::set_robot_serials(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  robot_serials_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.rtc.ListRobotsRequest.robot_serials)
}
inline void ListRobotsRequest::set_robot_serials(int index, const char* value, size_t size) {
  robot_serials_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.rtc.ListRobotsRequest.robot_serials)
}
inline std::string* ListRobotsRequest::_internal_add_robot_serials() {
  return robot_serials_.Add();
}
inline void ListRobotsRequest::add_robot_serials(const std::string& value) {
  robot_serials_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.rtc.ListRobotsRequest.robot_serials)
}
inline void ListRobotsRequest::add_robot_serials(std::string&& value) {
  robot_serials_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.rtc.ListRobotsRequest.robot_serials)
}
inline void ListRobotsRequest::add_robot_serials(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  robot_serials_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.rtc.ListRobotsRequest.robot_serials)
}
inline void ListRobotsRequest::add_robot_serials(const char* value, size_t size) {
  robot_serials_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.rtc.ListRobotsRequest.robot_serials)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ListRobotsRequest::robot_serials() const {
  // @@protoc_insertion_point(field_list:carbon.rtc.ListRobotsRequest.robot_serials)
  return robot_serials_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ListRobotsRequest::mutable_robot_serials() {
  // @@protoc_insertion_point(field_mutable_list:carbon.rtc.ListRobotsRequest.robot_serials)
  return &robot_serials_;
}

// -------------------------------------------------------------------

// ListRobotsResponse

// string next_page_token = 1;
inline void ListRobotsResponse::clear_next_page_token() {
  next_page_token_.ClearToEmpty();
}
inline const std::string& ListRobotsResponse::next_page_token() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListRobotsResponse.next_page_token)
  return _internal_next_page_token();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ListRobotsResponse::set_next_page_token(ArgT0&& arg0, ArgT... args) {
 
 next_page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.ListRobotsResponse.next_page_token)
}
inline std::string* ListRobotsResponse::mutable_next_page_token() {
  std::string* _s = _internal_mutable_next_page_token();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListRobotsResponse.next_page_token)
  return _s;
}
inline const std::string& ListRobotsResponse::_internal_next_page_token() const {
  return next_page_token_.Get();
}
inline void ListRobotsResponse::_internal_set_next_page_token(const std::string& value) {
  
  next_page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ListRobotsResponse::_internal_mutable_next_page_token() {
  
  return next_page_token_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ListRobotsResponse::release_next_page_token() {
  // @@protoc_insertion_point(field_release:carbon.rtc.ListRobotsResponse.next_page_token)
  return next_page_token_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ListRobotsResponse::set_allocated_next_page_token(std::string* next_page_token) {
  if (next_page_token != nullptr) {
    
  } else {
    
  }
  next_page_token_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), next_page_token,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (next_page_token_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    next_page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.ListRobotsResponse.next_page_token)
}

// repeated .carbon.rtc.RobotSummary robots = 2;
inline int ListRobotsResponse::_internal_robots_size() const {
  return robots_.size();
}
inline int ListRobotsResponse::robots_size() const {
  return _internal_robots_size();
}
inline void ListRobotsResponse::clear_robots() {
  robots_.Clear();
}
inline ::carbon::rtc::RobotSummary* ListRobotsResponse::mutable_robots(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListRobotsResponse.robots)
  return robots_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::RobotSummary >*
ListRobotsResponse::mutable_robots() {
  // @@protoc_insertion_point(field_mutable_list:carbon.rtc.ListRobotsResponse.robots)
  return &robots_;
}
inline const ::carbon::rtc::RobotSummary& ListRobotsResponse::_internal_robots(int index) const {
  return robots_.Get(index);
}
inline const ::carbon::rtc::RobotSummary& ListRobotsResponse::robots(int index) const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListRobotsResponse.robots)
  return _internal_robots(index);
}
inline ::carbon::rtc::RobotSummary* ListRobotsResponse::_internal_add_robots() {
  return robots_.Add();
}
inline ::carbon::rtc::RobotSummary* ListRobotsResponse::add_robots() {
  ::carbon::rtc::RobotSummary* _add = _internal_add_robots();
  // @@protoc_insertion_point(field_add:carbon.rtc.ListRobotsResponse.robots)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::rtc::RobotSummary >&
ListRobotsResponse::robots() const {
  // @@protoc_insertion_point(field_list:carbon.rtc.ListRobotsResponse.robots)
  return robots_;
}

// -------------------------------------------------------------------

// RobotSummary

// string serial = 1;
inline void RobotSummary::clear_serial() {
  serial_.ClearToEmpty();
}
inline const std::string& RobotSummary::serial() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RobotSummary.serial)
  return _internal_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RobotSummary::set_serial(ArgT0&& arg0, ArgT... args) {
 
 serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.RobotSummary.serial)
}
inline std::string* RobotSummary::mutable_serial() {
  std::string* _s = _internal_mutable_serial();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.RobotSummary.serial)
  return _s;
}
inline const std::string& RobotSummary::_internal_serial() const {
  return serial_.Get();
}
inline void RobotSummary::_internal_set_serial(const std::string& value) {
  
  serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RobotSummary::_internal_mutable_serial() {
  
  return serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RobotSummary::release_serial() {
  // @@protoc_insertion_point(field_release:carbon.rtc.RobotSummary.serial)
  return serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RobotSummary::set_allocated_serial(std::string* serial) {
  if (serial != nullptr) {
    
  } else {
    
  }
  serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.RobotSummary.serial)
}

// .carbon.rtc.LocationHistoryRecord last_seen = 2;
inline bool RobotSummary::_internal_has_last_seen() const {
  return this != internal_default_instance() && last_seen_ != nullptr;
}
inline bool RobotSummary::has_last_seen() const {
  return _internal_has_last_seen();
}
inline void RobotSummary::clear_last_seen() {
  if (GetArenaForAllocation() == nullptr && last_seen_ != nullptr) {
    delete last_seen_;
  }
  last_seen_ = nullptr;
}
inline const ::carbon::rtc::LocationHistoryRecord& RobotSummary::_internal_last_seen() const {
  const ::carbon::rtc::LocationHistoryRecord* p = last_seen_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::rtc::LocationHistoryRecord&>(
      ::carbon::rtc::_LocationHistoryRecord_default_instance_);
}
inline const ::carbon::rtc::LocationHistoryRecord& RobotSummary::last_seen() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.RobotSummary.last_seen)
  return _internal_last_seen();
}
inline void RobotSummary::unsafe_arena_set_allocated_last_seen(
    ::carbon::rtc::LocationHistoryRecord* last_seen) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(last_seen_);
  }
  last_seen_ = last_seen;
  if (last_seen) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.RobotSummary.last_seen)
}
inline ::carbon::rtc::LocationHistoryRecord* RobotSummary::release_last_seen() {
  
  ::carbon::rtc::LocationHistoryRecord* temp = last_seen_;
  last_seen_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::rtc::LocationHistoryRecord* RobotSummary::unsafe_arena_release_last_seen() {
  // @@protoc_insertion_point(field_release:carbon.rtc.RobotSummary.last_seen)
  
  ::carbon::rtc::LocationHistoryRecord* temp = last_seen_;
  last_seen_ = nullptr;
  return temp;
}
inline ::carbon::rtc::LocationHistoryRecord* RobotSummary::_internal_mutable_last_seen() {
  
  if (last_seen_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::rtc::LocationHistoryRecord>(GetArenaForAllocation());
    last_seen_ = p;
  }
  return last_seen_;
}
inline ::carbon::rtc::LocationHistoryRecord* RobotSummary::mutable_last_seen() {
  ::carbon::rtc::LocationHistoryRecord* _msg = _internal_mutable_last_seen();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.RobotSummary.last_seen)
  return _msg;
}
inline void RobotSummary::set_allocated_last_seen(::carbon::rtc::LocationHistoryRecord* last_seen) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete last_seen_;
  }
  if (last_seen) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::LocationHistoryRecord>::GetOwningArena(last_seen);
    if (message_arena != submessage_arena) {
      last_seen = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, last_seen, submessage_arena);
    }
    
  } else {
    
  }
  last_seen_ = last_seen;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.RobotSummary.last_seen)
}

// -------------------------------------------------------------------

// ListLocationHistoryRequest

// int32 page_size = 1;
inline void ListLocationHistoryRequest::clear_page_size() {
  page_size_ = 0;
}
inline int32_t ListLocationHistoryRequest::_internal_page_size() const {
  return page_size_;
}
inline int32_t ListLocationHistoryRequest::page_size() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryRequest.page_size)
  return _internal_page_size();
}
inline void ListLocationHistoryRequest::_internal_set_page_size(int32_t value) {
  
  page_size_ = value;
}
inline void ListLocationHistoryRequest::set_page_size(int32_t value) {
  _internal_set_page_size(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.ListLocationHistoryRequest.page_size)
}

// string page_token = 2;
inline void ListLocationHistoryRequest::clear_page_token() {
  page_token_.ClearToEmpty();
}
inline const std::string& ListLocationHistoryRequest::page_token() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryRequest.page_token)
  return _internal_page_token();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ListLocationHistoryRequest::set_page_token(ArgT0&& arg0, ArgT... args) {
 
 page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.ListLocationHistoryRequest.page_token)
}
inline std::string* ListLocationHistoryRequest::mutable_page_token() {
  std::string* _s = _internal_mutable_page_token();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListLocationHistoryRequest.page_token)
  return _s;
}
inline const std::string& ListLocationHistoryRequest::_internal_page_token() const {
  return page_token_.Get();
}
inline void ListLocationHistoryRequest::_internal_set_page_token(const std::string& value) {
  
  page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ListLocationHistoryRequest::_internal_mutable_page_token() {
  
  return page_token_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ListLocationHistoryRequest::release_page_token() {
  // @@protoc_insertion_point(field_release:carbon.rtc.ListLocationHistoryRequest.page_token)
  return page_token_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ListLocationHistoryRequest::set_allocated_page_token(std::string* page_token) {
  if (page_token != nullptr) {
    
  } else {
    
  }
  page_token_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), page_token,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (page_token_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.ListLocationHistoryRequest.page_token)
}

// string robot_serial = 3;
inline void ListLocationHistoryRequest::clear_robot_serial() {
  robot_serial_.ClearToEmpty();
}
inline const std::string& ListLocationHistoryRequest::robot_serial() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryRequest.robot_serial)
  return _internal_robot_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ListLocationHistoryRequest::set_robot_serial(ArgT0&& arg0, ArgT... args) {
 
 robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.ListLocationHistoryRequest.robot_serial)
}
inline std::string* ListLocationHistoryRequest::mutable_robot_serial() {
  std::string* _s = _internal_mutable_robot_serial();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListLocationHistoryRequest.robot_serial)
  return _s;
}
inline const std::string& ListLocationHistoryRequest::_internal_robot_serial() const {
  return robot_serial_.Get();
}
inline void ListLocationHistoryRequest::_internal_set_robot_serial(const std::string& value) {
  
  robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ListLocationHistoryRequest::_internal_mutable_robot_serial() {
  
  return robot_serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ListLocationHistoryRequest::release_robot_serial() {
  // @@protoc_insertion_point(field_release:carbon.rtc.ListLocationHistoryRequest.robot_serial)
  return robot_serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ListLocationHistoryRequest::set_allocated_robot_serial(std::string* robot_serial) {
  if (robot_serial != nullptr) {
    
  } else {
    
  }
  robot_serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.ListLocationHistoryRequest.robot_serial)
}

// .google.protobuf.Timestamp start = 4;
inline bool ListLocationHistoryRequest::_internal_has_start() const {
  return this != internal_default_instance() && start_ != nullptr;
}
inline bool ListLocationHistoryRequest::has_start() const {
  return _internal_has_start();
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& ListLocationHistoryRequest::_internal_start() const {
  const ::PROTOBUF_NAMESPACE_ID::Timestamp* p = start_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Timestamp&>(
      ::PROTOBUF_NAMESPACE_ID::_Timestamp_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& ListLocationHistoryRequest::start() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryRequest.start)
  return _internal_start();
}
inline void ListLocationHistoryRequest::unsafe_arena_set_allocated_start(
    ::PROTOBUF_NAMESPACE_ID::Timestamp* start) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_);
  }
  start_ = start;
  if (start) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.ListLocationHistoryRequest.start)
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* ListLocationHistoryRequest::release_start() {
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = start_;
  start_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* ListLocationHistoryRequest::unsafe_arena_release_start() {
  // @@protoc_insertion_point(field_release:carbon.rtc.ListLocationHistoryRequest.start)
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = start_;
  start_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* ListLocationHistoryRequest::_internal_mutable_start() {
  
  if (start_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Timestamp>(GetArenaForAllocation());
    start_ = p;
  }
  return start_;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* ListLocationHistoryRequest::mutable_start() {
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _msg = _internal_mutable_start();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListLocationHistoryRequest.start)
  return _msg;
}
inline void ListLocationHistoryRequest::set_allocated_start(::PROTOBUF_NAMESPACE_ID::Timestamp* start) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(start_);
  }
  if (start) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(start));
    if (message_arena != submessage_arena) {
      start = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, start, submessage_arena);
    }
    
  } else {
    
  }
  start_ = start;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.ListLocationHistoryRequest.start)
}

// .google.protobuf.Timestamp end = 5;
inline bool ListLocationHistoryRequest::_internal_has_end() const {
  return this != internal_default_instance() && end_ != nullptr;
}
inline bool ListLocationHistoryRequest::has_end() const {
  return _internal_has_end();
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& ListLocationHistoryRequest::_internal_end() const {
  const ::PROTOBUF_NAMESPACE_ID::Timestamp* p = end_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Timestamp&>(
      ::PROTOBUF_NAMESPACE_ID::_Timestamp_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Timestamp& ListLocationHistoryRequest::end() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryRequest.end)
  return _internal_end();
}
inline void ListLocationHistoryRequest::unsafe_arena_set_allocated_end(
    ::PROTOBUF_NAMESPACE_ID::Timestamp* end) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_);
  }
  end_ = end;
  if (end) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.ListLocationHistoryRequest.end)
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* ListLocationHistoryRequest::release_end() {
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = end_;
  end_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* ListLocationHistoryRequest::unsafe_arena_release_end() {
  // @@protoc_insertion_point(field_release:carbon.rtc.ListLocationHistoryRequest.end)
  
  ::PROTOBUF_NAMESPACE_ID::Timestamp* temp = end_;
  end_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* ListLocationHistoryRequest::_internal_mutable_end() {
  
  if (end_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Timestamp>(GetArenaForAllocation());
    end_ = p;
  }
  return end_;
}
inline ::PROTOBUF_NAMESPACE_ID::Timestamp* ListLocationHistoryRequest::mutable_end() {
  ::PROTOBUF_NAMESPACE_ID::Timestamp* _msg = _internal_mutable_end();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListLocationHistoryRequest.end)
  return _msg;
}
inline void ListLocationHistoryRequest::set_allocated_end(::PROTOBUF_NAMESPACE_ID::Timestamp* end) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(end_);
  }
  if (end) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(end));
    if (message_arena != submessage_arena) {
      end = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, end, submessage_arena);
    }
    
  } else {
    
  }
  end_ = end;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.ListLocationHistoryRequest.end)
}

// bool desc = 6;
inline void ListLocationHistoryRequest::clear_desc() {
  desc_ = false;
}
inline bool ListLocationHistoryRequest::_internal_desc() const {
  return desc_;
}
inline bool ListLocationHistoryRequest::desc() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryRequest.desc)
  return _internal_desc();
}
inline void ListLocationHistoryRequest::_internal_set_desc(bool value) {
  
  desc_ = value;
}
inline void ListLocationHistoryRequest::set_desc(bool value) {
  _internal_set_desc(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.ListLocationHistoryRequest.desc)
}

// bool include_closest = 7;
inline void ListLocationHistoryRequest::clear_include_closest() {
  include_closest_ = false;
}
inline bool ListLocationHistoryRequest::_internal_include_closest() const {
  return include_closest_;
}
inline bool ListLocationHistoryRequest::include_closest() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryRequest.include_closest)
  return _internal_include_closest();
}
inline void ListLocationHistoryRequest::_internal_set_include_closest(bool value) {
  
  include_closest_ = value;
}
inline void ListLocationHistoryRequest::set_include_closest(bool value) {
  _internal_set_include_closest(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.ListLocationHistoryRequest.include_closest)
}

// uint64 task_id = 8;
inline void ListLocationHistoryRequest::clear_task_id() {
  task_id_ = uint64_t{0u};
}
inline uint64_t ListLocationHistoryRequest::_internal_task_id() const {
  return task_id_;
}
inline uint64_t ListLocationHistoryRequest::task_id() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryRequest.task_id)
  return _internal_task_id();
}
inline void ListLocationHistoryRequest::_internal_set_task_id(uint64_t value) {
  
  task_id_ = value;
}
inline void ListLocationHistoryRequest::set_task_id(uint64_t value) {
  _internal_set_task_id(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.ListLocationHistoryRequest.task_id)
}

// uint64 objective_id = 9;
inline void ListLocationHistoryRequest::clear_objective_id() {
  objective_id_ = uint64_t{0u};
}
inline uint64_t ListLocationHistoryRequest::_internal_objective_id() const {
  return objective_id_;
}
inline uint64_t ListLocationHistoryRequest::objective_id() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryRequest.objective_id)
  return _internal_objective_id();
}
inline void ListLocationHistoryRequest::_internal_set_objective_id(uint64_t value) {
  
  objective_id_ = value;
}
inline void ListLocationHistoryRequest::set_objective_id(uint64_t value) {
  _internal_set_objective_id(value);
  // @@protoc_insertion_point(field_set:carbon.rtc.ListLocationHistoryRequest.objective_id)
}

// -------------------------------------------------------------------

// ListLocationHistoryResponse

// string next_page_token = 1;
inline void ListLocationHistoryResponse::clear_next_page_token() {
  next_page_token_.ClearToEmpty();
}
inline const std::string& ListLocationHistoryResponse::next_page_token() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryResponse.next_page_token)
  return _internal_next_page_token();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ListLocationHistoryResponse::set_next_page_token(ArgT0&& arg0, ArgT... args) {
 
 next_page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.rtc.ListLocationHistoryResponse.next_page_token)
}
inline std::string* ListLocationHistoryResponse::mutable_next_page_token() {
  std::string* _s = _internal_mutable_next_page_token();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListLocationHistoryResponse.next_page_token)
  return _s;
}
inline const std::string& ListLocationHistoryResponse::_internal_next_page_token() const {
  return next_page_token_.Get();
}
inline void ListLocationHistoryResponse::_internal_set_next_page_token(const std::string& value) {
  
  next_page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ListLocationHistoryResponse::_internal_mutable_next_page_token() {
  
  return next_page_token_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ListLocationHistoryResponse::release_next_page_token() {
  // @@protoc_insertion_point(field_release:carbon.rtc.ListLocationHistoryResponse.next_page_token)
  return next_page_token_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ListLocationHistoryResponse::set_allocated_next_page_token(std::string* next_page_token) {
  if (next_page_token != nullptr) {
    
  } else {
    
  }
  next_page_token_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), next_page_token,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (next_page_token_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    next_page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.ListLocationHistoryResponse.next_page_token)
}

// .carbon.rtc.LocationHistoryRecordList history = 2;
inline bool ListLocationHistoryResponse::_internal_has_history() const {
  return this != internal_default_instance() && history_ != nullptr;
}
inline bool ListLocationHistoryResponse::has_history() const {
  return _internal_has_history();
}
inline void ListLocationHistoryResponse::clear_history() {
  if (GetArenaForAllocation() == nullptr && history_ != nullptr) {
    delete history_;
  }
  history_ = nullptr;
}
inline const ::carbon::rtc::LocationHistoryRecordList& ListLocationHistoryResponse::_internal_history() const {
  const ::carbon::rtc::LocationHistoryRecordList* p = history_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::rtc::LocationHistoryRecordList&>(
      ::carbon::rtc::_LocationHistoryRecordList_default_instance_);
}
inline const ::carbon::rtc::LocationHistoryRecordList& ListLocationHistoryResponse::history() const {
  // @@protoc_insertion_point(field_get:carbon.rtc.ListLocationHistoryResponse.history)
  return _internal_history();
}
inline void ListLocationHistoryResponse::unsafe_arena_set_allocated_history(
    ::carbon::rtc::LocationHistoryRecordList* history) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(history_);
  }
  history_ = history;
  if (history) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.rtc.ListLocationHistoryResponse.history)
}
inline ::carbon::rtc::LocationHistoryRecordList* ListLocationHistoryResponse::release_history() {
  
  ::carbon::rtc::LocationHistoryRecordList* temp = history_;
  history_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::rtc::LocationHistoryRecordList* ListLocationHistoryResponse::unsafe_arena_release_history() {
  // @@protoc_insertion_point(field_release:carbon.rtc.ListLocationHistoryResponse.history)
  
  ::carbon::rtc::LocationHistoryRecordList* temp = history_;
  history_ = nullptr;
  return temp;
}
inline ::carbon::rtc::LocationHistoryRecordList* ListLocationHistoryResponse::_internal_mutable_history() {
  
  if (history_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::rtc::LocationHistoryRecordList>(GetArenaForAllocation());
    history_ = p;
  }
  return history_;
}
inline ::carbon::rtc::LocationHistoryRecordList* ListLocationHistoryResponse::mutable_history() {
  ::carbon::rtc::LocationHistoryRecordList* _msg = _internal_mutable_history();
  // @@protoc_insertion_point(field_mutable:carbon.rtc.ListLocationHistoryResponse.history)
  return _msg;
}
inline void ListLocationHistoryResponse::set_allocated_history(::carbon::rtc::LocationHistoryRecordList* history) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete history_;
  }
  if (history) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::rtc::LocationHistoryRecordList>::GetOwningArena(history);
    if (message_arena != submessage_arena) {
      history = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, history, submessage_arena);
    }
    
  } else {
    
  }
  history_ = history;
  // @@protoc_insertion_point(field_set_allocated:carbon.rtc.ListLocationHistoryResponse.history)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace rtc
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_proto_2frtc_2flocation_5fhistory_2eproto

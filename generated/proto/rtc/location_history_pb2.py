# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/rtc/location_history.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from generated.proto.geo import geo_pb2 as proto_dot_geo_dot_geo__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/rtc/location_history.proto',
  package='carbon.rtc',
  syntax='proto3',
  serialized_options=b'Z\tproto/rtc',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n proto/rtc/location_history.proto\x12\ncarbon.rtc\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x13proto/geo/geo.proto\"B\n\tRobotData\x12\x0f\n\x07task_id\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tive\x18\x02 \x01(\x08\x12\x14\n\x0cobjective_id\x18\x03 \x01(\x04\"\xbf\x01\n\x15LocationHistoryRecord\x12 \n\x05point\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x1c\n\x0fheading_degrees\x18\x04 \x01(\x01H\x00\x88\x01\x01\x12-\n\ttimestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12#\n\x04\x64\x61ta\x18\x03 \x01(\x0b\x32\x15.carbon.rtc.RobotDataB\x12\n\x10_heading_degrees\"O\n\x19LocationHistoryRecordList\x12\x32\n\x07records\x18\x01 \x03(\x0b\x32!.carbon.rtc.LocationHistoryRecord\"S\n\x19LogLocationHistoryRequest\x12\x36\n\x07history\x18\x01 \x01(\x0b\x32%.carbon.rtc.LocationHistoryRecordList\"Q\n\x11ListRobotsRequest\x12\x11\n\tpage_size\x18\x01 \x01(\x05\x12\x12\n\npage_token\x18\x02 \x01(\t\x12\x15\n\rrobot_serials\x18\x03 \x03(\t\"W\n\x12ListRobotsResponse\x12\x17\n\x0fnext_page_token\x18\x01 \x01(\t\x12(\n\x06robots\x18\x02 \x03(\x0b\x32\x18.carbon.rtc.RobotSummary\"T\n\x0cRobotSummary\x12\x0e\n\x06serial\x18\x01 \x01(\t\x12\x34\n\tlast_seen\x18\x02 \x01(\x0b\x32!.carbon.rtc.LocationHistoryRecord\"\xfb\x01\n\x1aListLocationHistoryRequest\x12\x11\n\tpage_size\x18\x01 \x01(\x05\x12\x12\n\npage_token\x18\x02 \x01(\t\x12\x14\n\x0crobot_serial\x18\x03 \x01(\t\x12)\n\x05start\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x64\x65sc\x18\x06 \x01(\x08\x12\x17\n\x0finclude_closest\x18\x07 \x01(\x08\x12\x0f\n\x07task_id\x18\x08 \x01(\x04\x12\x14\n\x0cobjective_id\x18\t \x01(\x04\"n\n\x1bListLocationHistoryResponse\x12\x17\n\x0fnext_page_token\x18\x01 \x01(\t\x12\x36\n\x07history\x18\x02 \x01(\x0b\x32%.carbon.rtc.LocationHistoryRecordList2\xea\x02\n\x0fLocationHistory\x12S\n\x12LogLocationHistory\x12%.carbon.rtc.LogLocationHistoryRequest\x1a\x16.google.protobuf.Empty\x12K\n\nListRobots\x12\x1d.carbon.rtc.ListRobotsRequest\x1a\x1e.carbon.rtc.ListRobotsResponse\x12\x66\n\x13ListLocationHistory\x12&.carbon.rtc.ListLocationHistoryRequest\x1a\'.carbon.rtc.ListLocationHistoryResponse\x12M\n\x0eStreamLocation\x12!.carbon.rtc.LocationHistoryRecord\x1a\x16.google.protobuf.Empty(\x01\x42\x0bZ\tproto/rtcb\x06proto3'
  ,
  dependencies=[google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,proto_dot_geo_dot_geo__pb2.DESCRIPTOR,])




_ROBOTDATA = _descriptor.Descriptor(
  name='RobotData',
  full_name='carbon.rtc.RobotData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='carbon.rtc.RobotData.task_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active', full_name='carbon.rtc.RobotData.active', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='objective_id', full_name='carbon.rtc.RobotData.objective_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=131,
  serialized_end=197,
)


_LOCATIONHISTORYRECORD = _descriptor.Descriptor(
  name='LocationHistoryRecord',
  full_name='carbon.rtc.LocationHistoryRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='point', full_name='carbon.rtc.LocationHistoryRecord.point', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='heading_degrees', full_name='carbon.rtc.LocationHistoryRecord.heading_degrees', index=1,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='carbon.rtc.LocationHistoryRecord.timestamp', index=2,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='carbon.rtc.LocationHistoryRecord.data', index=3,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_heading_degrees', full_name='carbon.rtc.LocationHistoryRecord._heading_degrees',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=200,
  serialized_end=391,
)


_LOCATIONHISTORYRECORDLIST = _descriptor.Descriptor(
  name='LocationHistoryRecordList',
  full_name='carbon.rtc.LocationHistoryRecordList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='records', full_name='carbon.rtc.LocationHistoryRecordList.records', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=393,
  serialized_end=472,
)


_LOGLOCATIONHISTORYREQUEST = _descriptor.Descriptor(
  name='LogLocationHistoryRequest',
  full_name='carbon.rtc.LogLocationHistoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='history', full_name='carbon.rtc.LogLocationHistoryRequest.history', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=474,
  serialized_end=557,
)


_LISTROBOTSREQUEST = _descriptor.Descriptor(
  name='ListRobotsRequest',
  full_name='carbon.rtc.ListRobotsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page_size', full_name='carbon.rtc.ListRobotsRequest.page_size', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='page_token', full_name='carbon.rtc.ListRobotsRequest.page_token', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_serials', full_name='carbon.rtc.ListRobotsRequest.robot_serials', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=559,
  serialized_end=640,
)


_LISTROBOTSRESPONSE = _descriptor.Descriptor(
  name='ListRobotsResponse',
  full_name='carbon.rtc.ListRobotsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='next_page_token', full_name='carbon.rtc.ListRobotsResponse.next_page_token', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robots', full_name='carbon.rtc.ListRobotsResponse.robots', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=642,
  serialized_end=729,
)


_ROBOTSUMMARY = _descriptor.Descriptor(
  name='RobotSummary',
  full_name='carbon.rtc.RobotSummary',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='serial', full_name='carbon.rtc.RobotSummary.serial', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_seen', full_name='carbon.rtc.RobotSummary.last_seen', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=731,
  serialized_end=815,
)


_LISTLOCATIONHISTORYREQUEST = _descriptor.Descriptor(
  name='ListLocationHistoryRequest',
  full_name='carbon.rtc.ListLocationHistoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='page_size', full_name='carbon.rtc.ListLocationHistoryRequest.page_size', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='page_token', full_name='carbon.rtc.ListLocationHistoryRequest.page_token', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_serial', full_name='carbon.rtc.ListLocationHistoryRequest.robot_serial', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start', full_name='carbon.rtc.ListLocationHistoryRequest.start', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end', full_name='carbon.rtc.ListLocationHistoryRequest.end', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='desc', full_name='carbon.rtc.ListLocationHistoryRequest.desc', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='include_closest', full_name='carbon.rtc.ListLocationHistoryRequest.include_closest', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='task_id', full_name='carbon.rtc.ListLocationHistoryRequest.task_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='objective_id', full_name='carbon.rtc.ListLocationHistoryRequest.objective_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=818,
  serialized_end=1069,
)


_LISTLOCATIONHISTORYRESPONSE = _descriptor.Descriptor(
  name='ListLocationHistoryResponse',
  full_name='carbon.rtc.ListLocationHistoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='next_page_token', full_name='carbon.rtc.ListLocationHistoryResponse.next_page_token', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='history', full_name='carbon.rtc.ListLocationHistoryResponse.history', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1071,
  serialized_end=1181,
)

_LOCATIONHISTORYRECORD.fields_by_name['point'].message_type = proto_dot_geo_dot_geo__pb2._POINT
_LOCATIONHISTORYRECORD.fields_by_name['timestamp'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LOCATIONHISTORYRECORD.fields_by_name['data'].message_type = _ROBOTDATA
_LOCATIONHISTORYRECORD.oneofs_by_name['_heading_degrees'].fields.append(
  _LOCATIONHISTORYRECORD.fields_by_name['heading_degrees'])
_LOCATIONHISTORYRECORD.fields_by_name['heading_degrees'].containing_oneof = _LOCATIONHISTORYRECORD.oneofs_by_name['_heading_degrees']
_LOCATIONHISTORYRECORDLIST.fields_by_name['records'].message_type = _LOCATIONHISTORYRECORD
_LOGLOCATIONHISTORYREQUEST.fields_by_name['history'].message_type = _LOCATIONHISTORYRECORDLIST
_LISTROBOTSRESPONSE.fields_by_name['robots'].message_type = _ROBOTSUMMARY
_ROBOTSUMMARY.fields_by_name['last_seen'].message_type = _LOCATIONHISTORYRECORD
_LISTLOCATIONHISTORYREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTLOCATIONHISTORYREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTLOCATIONHISTORYRESPONSE.fields_by_name['history'].message_type = _LOCATIONHISTORYRECORDLIST
DESCRIPTOR.message_types_by_name['RobotData'] = _ROBOTDATA
DESCRIPTOR.message_types_by_name['LocationHistoryRecord'] = _LOCATIONHISTORYRECORD
DESCRIPTOR.message_types_by_name['LocationHistoryRecordList'] = _LOCATIONHISTORYRECORDLIST
DESCRIPTOR.message_types_by_name['LogLocationHistoryRequest'] = _LOGLOCATIONHISTORYREQUEST
DESCRIPTOR.message_types_by_name['ListRobotsRequest'] = _LISTROBOTSREQUEST
DESCRIPTOR.message_types_by_name['ListRobotsResponse'] = _LISTROBOTSRESPONSE
DESCRIPTOR.message_types_by_name['RobotSummary'] = _ROBOTSUMMARY
DESCRIPTOR.message_types_by_name['ListLocationHistoryRequest'] = _LISTLOCATIONHISTORYREQUEST
DESCRIPTOR.message_types_by_name['ListLocationHistoryResponse'] = _LISTLOCATIONHISTORYRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RobotData = _reflection.GeneratedProtocolMessageType('RobotData', (_message.Message,), {
  'DESCRIPTOR' : _ROBOTDATA,
  '__module__' : 'proto.rtc.location_history_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.RobotData)
  })
_sym_db.RegisterMessage(RobotData)

LocationHistoryRecord = _reflection.GeneratedProtocolMessageType('LocationHistoryRecord', (_message.Message,), {
  'DESCRIPTOR' : _LOCATIONHISTORYRECORD,
  '__module__' : 'proto.rtc.location_history_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.LocationHistoryRecord)
  })
_sym_db.RegisterMessage(LocationHistoryRecord)

LocationHistoryRecordList = _reflection.GeneratedProtocolMessageType('LocationHistoryRecordList', (_message.Message,), {
  'DESCRIPTOR' : _LOCATIONHISTORYRECORDLIST,
  '__module__' : 'proto.rtc.location_history_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.LocationHistoryRecordList)
  })
_sym_db.RegisterMessage(LocationHistoryRecordList)

LogLocationHistoryRequest = _reflection.GeneratedProtocolMessageType('LogLocationHistoryRequest', (_message.Message,), {
  'DESCRIPTOR' : _LOGLOCATIONHISTORYREQUEST,
  '__module__' : 'proto.rtc.location_history_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.LogLocationHistoryRequest)
  })
_sym_db.RegisterMessage(LogLocationHistoryRequest)

ListRobotsRequest = _reflection.GeneratedProtocolMessageType('ListRobotsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTROBOTSREQUEST,
  '__module__' : 'proto.rtc.location_history_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListRobotsRequest)
  })
_sym_db.RegisterMessage(ListRobotsRequest)

ListRobotsResponse = _reflection.GeneratedProtocolMessageType('ListRobotsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTROBOTSRESPONSE,
  '__module__' : 'proto.rtc.location_history_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListRobotsResponse)
  })
_sym_db.RegisterMessage(ListRobotsResponse)

RobotSummary = _reflection.GeneratedProtocolMessageType('RobotSummary', (_message.Message,), {
  'DESCRIPTOR' : _ROBOTSUMMARY,
  '__module__' : 'proto.rtc.location_history_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.RobotSummary)
  })
_sym_db.RegisterMessage(RobotSummary)

ListLocationHistoryRequest = _reflection.GeneratedProtocolMessageType('ListLocationHistoryRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTLOCATIONHISTORYREQUEST,
  '__module__' : 'proto.rtc.location_history_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListLocationHistoryRequest)
  })
_sym_db.RegisterMessage(ListLocationHistoryRequest)

ListLocationHistoryResponse = _reflection.GeneratedProtocolMessageType('ListLocationHistoryResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTLOCATIONHISTORYRESPONSE,
  '__module__' : 'proto.rtc.location_history_pb2'
  # @@protoc_insertion_point(class_scope:carbon.rtc.ListLocationHistoryResponse)
  })
_sym_db.RegisterMessage(ListLocationHistoryResponse)


DESCRIPTOR._options = None

_LOCATIONHISTORY = _descriptor.ServiceDescriptor(
  name='LocationHistory',
  full_name='carbon.rtc.LocationHistory',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1184,
  serialized_end=1546,
  methods=[
  _descriptor.MethodDescriptor(
    name='LogLocationHistory',
    full_name='carbon.rtc.LocationHistory.LogLocationHistory',
    index=0,
    containing_service=None,
    input_type=_LOGLOCATIONHISTORYREQUEST,
    output_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ListRobots',
    full_name='carbon.rtc.LocationHistory.ListRobots',
    index=1,
    containing_service=None,
    input_type=_LISTROBOTSREQUEST,
    output_type=_LISTROBOTSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ListLocationHistory',
    full_name='carbon.rtc.LocationHistory.ListLocationHistory',
    index=2,
    containing_service=None,
    input_type=_LISTLOCATIONHISTORYREQUEST,
    output_type=_LISTLOCATIONHISTORYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StreamLocation',
    full_name='carbon.rtc.LocationHistory.StreamLocation',
    index=3,
    containing_service=None,
    input_type=_LOCATIONHISTORYRECORD,
    output_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_LOCATIONHISTORY)

DESCRIPTOR.services_by_name['LocationHistory'] = _LOCATIONHISTORY

# @@protoc_insertion_point(module_scope)

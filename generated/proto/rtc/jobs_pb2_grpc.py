# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.proto.rtc import jobs_pb2 as proto_dot_rtc_dot_jobs__pb2


class JobServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateIntervention = channel.unary_unary(
                '/carbon.rtc.JobService/CreateIntervention',
                request_serializer=proto_dot_rtc_dot_jobs__pb2.CreateInterventionRequest.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_jobs__pb2.CreateInterventionResponse.FromString,
                )
        self.GetActiveTask = channel.unary_unary(
                '/carbon.rtc.JobService/GetActiveTask',
                request_serializer=proto_dot_rtc_dot_jobs__pb2.GetActiveTaskRequest.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_jobs__pb2.GetActiveTaskResponse.FromString,
                )
        self.GetTask = channel.unary_unary(
                '/carbon.rtc.JobService/GetTask',
                request_serializer=proto_dot_rtc_dot_jobs__pb2.GetTaskRequest.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_jobs__pb2.GetTaskResponse.FromString,
                )
        self.GetNextActiveObjective = channel.unary_unary(
                '/carbon.rtc.JobService/GetNextActiveObjective',
                request_serializer=proto_dot_rtc_dot_jobs__pb2.GetNextActiveObjectiveRequest.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_jobs__pb2.GetNextActiveObjectiveResponse.FromString,
                )
        self.UpdateTask = channel.unary_unary(
                '/carbon.rtc.JobService/UpdateTask',
                request_serializer=proto_dot_rtc_dot_jobs__pb2.UpdateTaskRequest.SerializeToString,
                response_deserializer=proto_dot_rtc_dot_jobs__pb2.UpdateTaskResponse.FromString,
                )


class JobServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateIntervention(self, request, context):
        """Robot API
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetActiveTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextActiveObjective(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_JobServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateIntervention': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateIntervention,
                    request_deserializer=proto_dot_rtc_dot_jobs__pb2.CreateInterventionRequest.FromString,
                    response_serializer=proto_dot_rtc_dot_jobs__pb2.CreateInterventionResponse.SerializeToString,
            ),
            'GetActiveTask': grpc.unary_unary_rpc_method_handler(
                    servicer.GetActiveTask,
                    request_deserializer=proto_dot_rtc_dot_jobs__pb2.GetActiveTaskRequest.FromString,
                    response_serializer=proto_dot_rtc_dot_jobs__pb2.GetActiveTaskResponse.SerializeToString,
            ),
            'GetTask': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTask,
                    request_deserializer=proto_dot_rtc_dot_jobs__pb2.GetTaskRequest.FromString,
                    response_serializer=proto_dot_rtc_dot_jobs__pb2.GetTaskResponse.SerializeToString,
            ),
            'GetNextActiveObjective': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextActiveObjective,
                    request_deserializer=proto_dot_rtc_dot_jobs__pb2.GetNextActiveObjectiveRequest.FromString,
                    response_serializer=proto_dot_rtc_dot_jobs__pb2.GetNextActiveObjectiveResponse.SerializeToString,
            ),
            'UpdateTask': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateTask,
                    request_deserializer=proto_dot_rtc_dot_jobs__pb2.UpdateTaskRequest.FromString,
                    response_serializer=proto_dot_rtc_dot_jobs__pb2.UpdateTaskResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.rtc.JobService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class JobService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateIntervention(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.JobService/CreateIntervention',
            proto_dot_rtc_dot_jobs__pb2.CreateInterventionRequest.SerializeToString,
            proto_dot_rtc_dot_jobs__pb2.CreateInterventionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetActiveTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.JobService/GetActiveTask',
            proto_dot_rtc_dot_jobs__pb2.GetActiveTaskRequest.SerializeToString,
            proto_dot_rtc_dot_jobs__pb2.GetActiveTaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.JobService/GetTask',
            proto_dot_rtc_dot_jobs__pb2.GetTaskRequest.SerializeToString,
            proto_dot_rtc_dot_jobs__pb2.GetTaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextActiveObjective(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.JobService/GetNextActiveObjective',
            proto_dot_rtc_dot_jobs__pb2.GetNextActiveObjectiveRequest.SerializeToString,
            proto_dot_rtc_dot_jobs__pb2.GetNextActiveObjectiveResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.rtc.JobService/UpdateTask',
            proto_dot_rtc_dot_jobs__pb2.UpdateTaskRequest.SerializeToString,
            proto_dot_rtc_dot_jobs__pb2.UpdateTaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

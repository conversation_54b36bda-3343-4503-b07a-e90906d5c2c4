// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/rtc/location_history.proto

#include "proto/rtc/location_history.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace rtc {
constexpr RobotData::RobotData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : task_id_(uint64_t{0u})
  , objective_id_(uint64_t{0u})
  , active_(false){}
struct RobotDataDefaultTypeInternal {
  constexpr RobotDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RobotDataDefaultTypeInternal() {}
  union {
    RobotData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RobotDataDefaultTypeInternal _RobotData_default_instance_;
constexpr LocationHistoryRecord::LocationHistoryRecord(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : point_(nullptr)
  , timestamp_(nullptr)
  , data_(nullptr)
  , heading_degrees_(0){}
struct LocationHistoryRecordDefaultTypeInternal {
  constexpr LocationHistoryRecordDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LocationHistoryRecordDefaultTypeInternal() {}
  union {
    LocationHistoryRecord _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LocationHistoryRecordDefaultTypeInternal _LocationHistoryRecord_default_instance_;
constexpr LocationHistoryRecordList::LocationHistoryRecordList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : records_(){}
struct LocationHistoryRecordListDefaultTypeInternal {
  constexpr LocationHistoryRecordListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LocationHistoryRecordListDefaultTypeInternal() {}
  union {
    LocationHistoryRecordList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LocationHistoryRecordListDefaultTypeInternal _LocationHistoryRecordList_default_instance_;
constexpr LogLocationHistoryRequest::LogLocationHistoryRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : history_(nullptr){}
struct LogLocationHistoryRequestDefaultTypeInternal {
  constexpr LogLocationHistoryRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LogLocationHistoryRequestDefaultTypeInternal() {}
  union {
    LogLocationHistoryRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LogLocationHistoryRequestDefaultTypeInternal _LogLocationHistoryRequest_default_instance_;
constexpr ListRobotsRequest::ListRobotsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_serials_()
  , page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , page_size_(0){}
struct ListRobotsRequestDefaultTypeInternal {
  constexpr ListRobotsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListRobotsRequestDefaultTypeInternal() {}
  union {
    ListRobotsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListRobotsRequestDefaultTypeInternal _ListRobotsRequest_default_instance_;
constexpr ListRobotsResponse::ListRobotsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robots_()
  , next_page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ListRobotsResponseDefaultTypeInternal {
  constexpr ListRobotsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListRobotsResponseDefaultTypeInternal() {}
  union {
    ListRobotsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListRobotsResponseDefaultTypeInternal _ListRobotsResponse_default_instance_;
constexpr RobotSummary::RobotSummary(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , last_seen_(nullptr){}
struct RobotSummaryDefaultTypeInternal {
  constexpr RobotSummaryDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RobotSummaryDefaultTypeInternal() {}
  union {
    RobotSummary _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RobotSummaryDefaultTypeInternal _RobotSummary_default_instance_;
constexpr ListLocationHistoryRequest::ListLocationHistoryRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , robot_serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , start_(nullptr)
  , end_(nullptr)
  , page_size_(0)
  , desc_(false)
  , include_closest_(false)
  , task_id_(uint64_t{0u})
  , objective_id_(uint64_t{0u}){}
struct ListLocationHistoryRequestDefaultTypeInternal {
  constexpr ListLocationHistoryRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListLocationHistoryRequestDefaultTypeInternal() {}
  union {
    ListLocationHistoryRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListLocationHistoryRequestDefaultTypeInternal _ListLocationHistoryRequest_default_instance_;
constexpr ListLocationHistoryResponse::ListLocationHistoryResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : next_page_token_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , history_(nullptr){}
struct ListLocationHistoryResponseDefaultTypeInternal {
  constexpr ListLocationHistoryResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListLocationHistoryResponseDefaultTypeInternal() {}
  union {
    ListLocationHistoryResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListLocationHistoryResponseDefaultTypeInternal _ListLocationHistoryResponse_default_instance_;
}  // namespace rtc
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[9];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2frtc_2flocation_5fhistory_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2frtc_2flocation_5fhistory_2eproto = nullptr;

const uint32_t TableStruct_proto_2frtc_2flocation_5fhistory_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotData, task_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotData, active_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotData, objective_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LocationHistoryRecord, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LocationHistoryRecord, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LocationHistoryRecord, point_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LocationHistoryRecord, heading_degrees_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LocationHistoryRecord, timestamp_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LocationHistoryRecord, data_),
  ~0u,
  0,
  ~0u,
  ~0u,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LocationHistoryRecordList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LocationHistoryRecordList, records_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LogLocationHistoryRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::LogLocationHistoryRequest, history_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListRobotsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListRobotsRequest, page_size_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListRobotsRequest, page_token_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListRobotsRequest, robot_serials_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListRobotsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListRobotsResponse, next_page_token_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListRobotsResponse, robots_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotSummary, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotSummary, serial_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotSummary, last_seen_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, page_size_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, page_token_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, robot_serial_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, start_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, end_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, desc_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, include_closest_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, task_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryRequest, objective_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryResponse, next_page_token_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::ListLocationHistoryResponse, history_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::rtc::RobotData)},
  { 9, 19, -1, sizeof(::carbon::rtc::LocationHistoryRecord)},
  { 23, -1, -1, sizeof(::carbon::rtc::LocationHistoryRecordList)},
  { 30, -1, -1, sizeof(::carbon::rtc::LogLocationHistoryRequest)},
  { 37, -1, -1, sizeof(::carbon::rtc::ListRobotsRequest)},
  { 46, -1, -1, sizeof(::carbon::rtc::ListRobotsResponse)},
  { 54, -1, -1, sizeof(::carbon::rtc::RobotSummary)},
  { 62, -1, -1, sizeof(::carbon::rtc::ListLocationHistoryRequest)},
  { 77, -1, -1, sizeof(::carbon::rtc::ListLocationHistoryResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_RobotData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_LocationHistoryRecord_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_LocationHistoryRecordList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_LogLocationHistoryRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListRobotsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListRobotsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_RobotSummary_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListLocationHistoryRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_ListLocationHistoryResponse_default_instance_),
};

const char descriptor_table_protodef_proto_2frtc_2flocation_5fhistory_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n proto/rtc/location_history.proto\022\ncarb"
  "on.rtc\032\033google/protobuf/empty.proto\032\037goo"
  "gle/protobuf/timestamp.proto\032\023proto/geo/"
  "geo.proto\"B\n\tRobotData\022\017\n\007task_id\030\001 \001(\004\022"
  "\016\n\006active\030\002 \001(\010\022\024\n\014objective_id\030\003 \001(\004\"\277\001"
  "\n\025LocationHistoryRecord\022 \n\005point\030\001 \001(\0132\021"
  ".carbon.geo.Point\022\034\n\017heading_degrees\030\004 \001"
  "(\001H\000\210\001\001\022-\n\ttimestamp\030\002 \001(\0132\032.google.prot"
  "obuf.Timestamp\022#\n\004data\030\003 \001(\0132\025.carbon.rt"
  "c.RobotDataB\022\n\020_heading_degrees\"O\n\031Locat"
  "ionHistoryRecordList\0222\n\007records\030\001 \003(\0132!."
  "carbon.rtc.LocationHistoryRecord\"S\n\031LogL"
  "ocationHistoryRequest\0226\n\007history\030\001 \001(\0132%"
  ".carbon.rtc.LocationHistoryRecordList\"Q\n"
  "\021ListRobotsRequest\022\021\n\tpage_size\030\001 \001(\005\022\022\n"
  "\npage_token\030\002 \001(\t\022\025\n\rrobot_serials\030\003 \003(\t"
  "\"W\n\022ListRobotsResponse\022\027\n\017next_page_toke"
  "n\030\001 \001(\t\022(\n\006robots\030\002 \003(\0132\030.carbon.rtc.Rob"
  "otSummary\"T\n\014RobotSummary\022\016\n\006serial\030\001 \001("
  "\t\0224\n\tlast_seen\030\002 \001(\0132!.carbon.rtc.Locati"
  "onHistoryRecord\"\373\001\n\032ListLocationHistoryR"
  "equest\022\021\n\tpage_size\030\001 \001(\005\022\022\n\npage_token\030"
  "\002 \001(\t\022\024\n\014robot_serial\030\003 \001(\t\022)\n\005start\030\004 \001"
  "(\0132\032.google.protobuf.Timestamp\022\'\n\003end\030\005 "
  "\001(\0132\032.google.protobuf.Timestamp\022\014\n\004desc\030"
  "\006 \001(\010\022\027\n\017include_closest\030\007 \001(\010\022\017\n\007task_i"
  "d\030\010 \001(\004\022\024\n\014objective_id\030\t \001(\004\"n\n\033ListLoc"
  "ationHistoryResponse\022\027\n\017next_page_token\030"
  "\001 \001(\t\0226\n\007history\030\002 \001(\0132%.carbon.rtc.Loca"
  "tionHistoryRecordList2\352\002\n\017LocationHistor"
  "y\022S\n\022LogLocationHistory\022%.carbon.rtc.Log"
  "LocationHistoryRequest\032\026.google.protobuf"
  ".Empty\022K\n\nListRobots\022\035.carbon.rtc.ListRo"
  "botsRequest\032\036.carbon.rtc.ListRobotsRespo"
  "nse\022f\n\023ListLocationHistory\022&.carbon.rtc."
  "ListLocationHistoryRequest\032\'.carbon.rtc."
  "ListLocationHistoryResponse\022M\n\016StreamLoc"
  "ation\022!.carbon.rtc.LocationHistoryRecord"
  "\032\026.google.protobuf.Empty(\001B\013Z\tproto/rtcb"
  "\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_deps[3] = {
  &::descriptor_table_google_2fprotobuf_2fempty_2eproto,
  &::descriptor_table_google_2fprotobuf_2ftimestamp_2eproto,
  &::descriptor_table_proto_2fgeo_2fgeo_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto = {
  false, false, 1567, descriptor_table_protodef_proto_2frtc_2flocation_5fhistory_2eproto, "proto/rtc/location_history.proto", 
  &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once, descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_deps, 3, 9,
  schemas, file_default_instances, TableStruct_proto_2frtc_2flocation_5fhistory_2eproto::offsets,
  file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto, file_level_enum_descriptors_proto_2frtc_2flocation_5fhistory_2eproto, file_level_service_descriptors_proto_2frtc_2flocation_5fhistory_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter() {
  return &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2frtc_2flocation_5fhistory_2eproto(&descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto);
namespace carbon {
namespace rtc {

// ===================================================================

class RobotData::_Internal {
 public:
};

RobotData::RobotData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.RobotData)
}
RobotData::RobotData(const RobotData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&task_id_, &from.task_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&active_) -
    reinterpret_cast<char*>(&task_id_)) + sizeof(active_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.RobotData)
}

inline void RobotData::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&task_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&active_) -
    reinterpret_cast<char*>(&task_id_)) + sizeof(active_));
}

RobotData::~RobotData() {
  // @@protoc_insertion_point(destructor:carbon.rtc.RobotData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RobotData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RobotData::ArenaDtor(void* object) {
  RobotData* _this = reinterpret_cast< RobotData* >(object);
  (void)_this;
}
void RobotData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RobotData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RobotData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.RobotData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&task_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&active_) -
      reinterpret_cast<char*>(&task_id_)) + sizeof(active_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RobotData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 task_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          task_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool active = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          active_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 objective_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          objective_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RobotData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.RobotData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 task_id = 1;
  if (this->_internal_task_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_task_id(), target);
  }

  // bool active = 2;
  if (this->_internal_active() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_active(), target);
  }

  // uint64 objective_id = 3;
  if (this->_internal_objective_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_objective_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.RobotData)
  return target;
}

size_t RobotData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.RobotData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 task_id = 1;
  if (this->_internal_task_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_task_id());
  }

  // uint64 objective_id = 3;
  if (this->_internal_objective_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_objective_id());
  }

  // bool active = 2;
  if (this->_internal_active() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RobotData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RobotData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RobotData::GetClassData() const { return &_class_data_; }

void RobotData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RobotData *>(to)->MergeFrom(
      static_cast<const RobotData &>(from));
}


void RobotData::MergeFrom(const RobotData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.RobotData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_task_id() != 0) {
    _internal_set_task_id(from._internal_task_id());
  }
  if (from._internal_objective_id() != 0) {
    _internal_set_objective_id(from._internal_objective_id());
  }
  if (from._internal_active() != 0) {
    _internal_set_active(from._internal_active());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RobotData::CopyFrom(const RobotData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.RobotData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RobotData::IsInitialized() const {
  return true;
}

void RobotData::InternalSwap(RobotData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RobotData, active_)
      + sizeof(RobotData::active_)
      - PROTOBUF_FIELD_OFFSET(RobotData, task_id_)>(
          reinterpret_cast<char*>(&task_id_),
          reinterpret_cast<char*>(&other->task_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RobotData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter, &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once,
      file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[0]);
}

// ===================================================================

class LocationHistoryRecord::_Internal {
 public:
  using HasBits = decltype(std::declval<LocationHistoryRecord>()._has_bits_);
  static const ::carbon::geo::Point& point(const LocationHistoryRecord* msg);
  static void set_has_heading_degrees(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& timestamp(const LocationHistoryRecord* msg);
  static const ::carbon::rtc::RobotData& data(const LocationHistoryRecord* msg);
};

const ::carbon::geo::Point&
LocationHistoryRecord::_Internal::point(const LocationHistoryRecord* msg) {
  return *msg->point_;
}
const ::PROTOBUF_NAMESPACE_ID::Timestamp&
LocationHistoryRecord::_Internal::timestamp(const LocationHistoryRecord* msg) {
  return *msg->timestamp_;
}
const ::carbon::rtc::RobotData&
LocationHistoryRecord::_Internal::data(const LocationHistoryRecord* msg) {
  return *msg->data_;
}
void LocationHistoryRecord::clear_point() {
  if (GetArenaForAllocation() == nullptr && point_ != nullptr) {
    delete point_;
  }
  point_ = nullptr;
}
void LocationHistoryRecord::clear_timestamp() {
  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
}
LocationHistoryRecord::LocationHistoryRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.LocationHistoryRecord)
}
LocationHistoryRecord::LocationHistoryRecord(const LocationHistoryRecord& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_point()) {
    point_ = new ::carbon::geo::Point(*from.point_);
  } else {
    point_ = nullptr;
  }
  if (from._internal_has_timestamp()) {
    timestamp_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.timestamp_);
  } else {
    timestamp_ = nullptr;
  }
  if (from._internal_has_data()) {
    data_ = new ::carbon::rtc::RobotData(*from.data_);
  } else {
    data_ = nullptr;
  }
  heading_degrees_ = from.heading_degrees_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.LocationHistoryRecord)
}

inline void LocationHistoryRecord::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&point_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&heading_degrees_) -
    reinterpret_cast<char*>(&point_)) + sizeof(heading_degrees_));
}

LocationHistoryRecord::~LocationHistoryRecord() {
  // @@protoc_insertion_point(destructor:carbon.rtc.LocationHistoryRecord)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LocationHistoryRecord::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete point_;
  if (this != internal_default_instance()) delete timestamp_;
  if (this != internal_default_instance()) delete data_;
}

void LocationHistoryRecord::ArenaDtor(void* object) {
  LocationHistoryRecord* _this = reinterpret_cast< LocationHistoryRecord* >(object);
  (void)_this;
}
void LocationHistoryRecord::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LocationHistoryRecord::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LocationHistoryRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.LocationHistoryRecord)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && point_ != nullptr) {
    delete point_;
  }
  point_ = nullptr;
  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
  if (GetArenaForAllocation() == nullptr && data_ != nullptr) {
    delete data_;
  }
  data_ = nullptr;
  heading_degrees_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LocationHistoryRecord::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.geo.Point point = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_point(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp timestamp = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_timestamp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.RobotData data = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_data(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional double heading_degrees = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _Internal::set_has_heading_degrees(&has_bits);
          heading_degrees_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LocationHistoryRecord::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.LocationHistoryRecord)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.geo.Point point = 1;
  if (this->_internal_has_point()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::point(this), target, stream);
  }

  // .google.protobuf.Timestamp timestamp = 2;
  if (this->_internal_has_timestamp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::timestamp(this), target, stream);
  }

  // .carbon.rtc.RobotData data = 3;
  if (this->_internal_has_data()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::data(this), target, stream);
  }

  // optional double heading_degrees = 4;
  if (_internal_has_heading_degrees()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_heading_degrees(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.LocationHistoryRecord)
  return target;
}

size_t LocationHistoryRecord::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.LocationHistoryRecord)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.geo.Point point = 1;
  if (this->_internal_has_point()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *point_);
  }

  // .google.protobuf.Timestamp timestamp = 2;
  if (this->_internal_has_timestamp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *timestamp_);
  }

  // .carbon.rtc.RobotData data = 3;
  if (this->_internal_has_data()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *data_);
  }

  // optional double heading_degrees = 4;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LocationHistoryRecord::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LocationHistoryRecord::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LocationHistoryRecord::GetClassData() const { return &_class_data_; }

void LocationHistoryRecord::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LocationHistoryRecord *>(to)->MergeFrom(
      static_cast<const LocationHistoryRecord &>(from));
}


void LocationHistoryRecord::MergeFrom(const LocationHistoryRecord& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.LocationHistoryRecord)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_point()) {
    _internal_mutable_point()->::carbon::geo::Point::MergeFrom(from._internal_point());
  }
  if (from._internal_has_timestamp()) {
    _internal_mutable_timestamp()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_timestamp());
  }
  if (from._internal_has_data()) {
    _internal_mutable_data()->::carbon::rtc::RobotData::MergeFrom(from._internal_data());
  }
  if (from._internal_has_heading_degrees()) {
    _internal_set_heading_degrees(from._internal_heading_degrees());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LocationHistoryRecord::CopyFrom(const LocationHistoryRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.LocationHistoryRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LocationHistoryRecord::IsInitialized() const {
  return true;
}

void LocationHistoryRecord::InternalSwap(LocationHistoryRecord* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LocationHistoryRecord, heading_degrees_)
      + sizeof(LocationHistoryRecord::heading_degrees_)
      - PROTOBUF_FIELD_OFFSET(LocationHistoryRecord, point_)>(
          reinterpret_cast<char*>(&point_),
          reinterpret_cast<char*>(&other->point_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LocationHistoryRecord::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter, &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once,
      file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[1]);
}

// ===================================================================

class LocationHistoryRecordList::_Internal {
 public:
};

LocationHistoryRecordList::LocationHistoryRecordList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  records_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.LocationHistoryRecordList)
}
LocationHistoryRecordList::LocationHistoryRecordList(const LocationHistoryRecordList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      records_(from.records_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.LocationHistoryRecordList)
}

inline void LocationHistoryRecordList::SharedCtor() {
}

LocationHistoryRecordList::~LocationHistoryRecordList() {
  // @@protoc_insertion_point(destructor:carbon.rtc.LocationHistoryRecordList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LocationHistoryRecordList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LocationHistoryRecordList::ArenaDtor(void* object) {
  LocationHistoryRecordList* _this = reinterpret_cast< LocationHistoryRecordList* >(object);
  (void)_this;
}
void LocationHistoryRecordList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LocationHistoryRecordList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LocationHistoryRecordList::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.LocationHistoryRecordList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  records_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LocationHistoryRecordList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.rtc.LocationHistoryRecord records = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_records(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LocationHistoryRecordList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.LocationHistoryRecordList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.rtc.LocationHistoryRecord records = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_records_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_records(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.LocationHistoryRecordList)
  return target;
}

size_t LocationHistoryRecordList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.LocationHistoryRecordList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.LocationHistoryRecord records = 1;
  total_size += 1UL * this->_internal_records_size();
  for (const auto& msg : this->records_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LocationHistoryRecordList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LocationHistoryRecordList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LocationHistoryRecordList::GetClassData() const { return &_class_data_; }

void LocationHistoryRecordList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LocationHistoryRecordList *>(to)->MergeFrom(
      static_cast<const LocationHistoryRecordList &>(from));
}


void LocationHistoryRecordList::MergeFrom(const LocationHistoryRecordList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.LocationHistoryRecordList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  records_.MergeFrom(from.records_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LocationHistoryRecordList::CopyFrom(const LocationHistoryRecordList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.LocationHistoryRecordList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LocationHistoryRecordList::IsInitialized() const {
  return true;
}

void LocationHistoryRecordList::InternalSwap(LocationHistoryRecordList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  records_.InternalSwap(&other->records_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LocationHistoryRecordList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter, &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once,
      file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[2]);
}

// ===================================================================

class LogLocationHistoryRequest::_Internal {
 public:
  static const ::carbon::rtc::LocationHistoryRecordList& history(const LogLocationHistoryRequest* msg);
};

const ::carbon::rtc::LocationHistoryRecordList&
LogLocationHistoryRequest::_Internal::history(const LogLocationHistoryRequest* msg) {
  return *msg->history_;
}
LogLocationHistoryRequest::LogLocationHistoryRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.LogLocationHistoryRequest)
}
LogLocationHistoryRequest::LogLocationHistoryRequest(const LogLocationHistoryRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_history()) {
    history_ = new ::carbon::rtc::LocationHistoryRecordList(*from.history_);
  } else {
    history_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.LogLocationHistoryRequest)
}

inline void LogLocationHistoryRequest::SharedCtor() {
history_ = nullptr;
}

LogLocationHistoryRequest::~LogLocationHistoryRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.LogLocationHistoryRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LogLocationHistoryRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete history_;
}

void LogLocationHistoryRequest::ArenaDtor(void* object) {
  LogLocationHistoryRequest* _this = reinterpret_cast< LogLocationHistoryRequest* >(object);
  (void)_this;
}
void LogLocationHistoryRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LogLocationHistoryRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LogLocationHistoryRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.LogLocationHistoryRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && history_ != nullptr) {
    delete history_;
  }
  history_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LogLocationHistoryRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.LocationHistoryRecordList history = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_history(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LogLocationHistoryRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.LogLocationHistoryRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.LocationHistoryRecordList history = 1;
  if (this->_internal_has_history()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::history(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.LogLocationHistoryRequest)
  return target;
}

size_t LogLocationHistoryRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.LogLocationHistoryRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc.LocationHistoryRecordList history = 1;
  if (this->_internal_has_history()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *history_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LogLocationHistoryRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LogLocationHistoryRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LogLocationHistoryRequest::GetClassData() const { return &_class_data_; }

void LogLocationHistoryRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LogLocationHistoryRequest *>(to)->MergeFrom(
      static_cast<const LogLocationHistoryRequest &>(from));
}


void LogLocationHistoryRequest::MergeFrom(const LogLocationHistoryRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.LogLocationHistoryRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_history()) {
    _internal_mutable_history()->::carbon::rtc::LocationHistoryRecordList::MergeFrom(from._internal_history());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LogLocationHistoryRequest::CopyFrom(const LogLocationHistoryRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.LogLocationHistoryRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LogLocationHistoryRequest::IsInitialized() const {
  return true;
}

void LogLocationHistoryRequest::InternalSwap(LogLocationHistoryRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(history_, other->history_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LogLocationHistoryRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter, &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once,
      file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[3]);
}

// ===================================================================

class ListRobotsRequest::_Internal {
 public:
};

ListRobotsRequest::ListRobotsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  robot_serials_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListRobotsRequest)
}
ListRobotsRequest::ListRobotsRequest(const ListRobotsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      robot_serials_(from.robot_serials_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_page_token().empty()) {
    page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_page_token(), 
      GetArenaForAllocation());
  }
  page_size_ = from.page_size_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListRobotsRequest)
}

inline void ListRobotsRequest::SharedCtor() {
page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
page_size_ = 0;
}

ListRobotsRequest::~ListRobotsRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListRobotsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListRobotsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListRobotsRequest::ArenaDtor(void* object) {
  ListRobotsRequest* _this = reinterpret_cast< ListRobotsRequest* >(object);
  (void)_this;
}
void ListRobotsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListRobotsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListRobotsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListRobotsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_serials_.Clear();
  page_token_.ClearToEmpty();
  page_size_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListRobotsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 page_size = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          page_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string page_token = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListRobotsRequest.page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string robot_serials = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_robot_serials();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListRobotsRequest.robot_serials"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListRobotsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListRobotsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 page_size = 1;
  if (this->_internal_page_size() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_page_size(), target);
  }

  // string page_token = 2;
  if (!this->_internal_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_page_token().data(), static_cast<int>(this->_internal_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListRobotsRequest.page_token");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_page_token(), target);
  }

  // repeated string robot_serials = 3;
  for (int i = 0, n = this->_internal_robot_serials_size(); i < n; i++) {
    const auto& s = this->_internal_robot_serials(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListRobotsRequest.robot_serials");
    target = stream->WriteString(3, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListRobotsRequest)
  return target;
}

size_t ListRobotsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListRobotsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string robot_serials = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(robot_serials_.size());
  for (int i = 0, n = robot_serials_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      robot_serials_.Get(i));
  }

  // string page_token = 2;
  if (!this->_internal_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_page_token());
  }

  // int32 page_size = 1;
  if (this->_internal_page_size() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_page_size());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListRobotsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListRobotsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListRobotsRequest::GetClassData() const { return &_class_data_; }

void ListRobotsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListRobotsRequest *>(to)->MergeFrom(
      static_cast<const ListRobotsRequest &>(from));
}


void ListRobotsRequest::MergeFrom(const ListRobotsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListRobotsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  robot_serials_.MergeFrom(from.robot_serials_);
  if (!from._internal_page_token().empty()) {
    _internal_set_page_token(from._internal_page_token());
  }
  if (from._internal_page_size() != 0) {
    _internal_set_page_size(from._internal_page_size());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListRobotsRequest::CopyFrom(const ListRobotsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListRobotsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListRobotsRequest::IsInitialized() const {
  return true;
}

void ListRobotsRequest::InternalSwap(ListRobotsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  robot_serials_.InternalSwap(&other->robot_serials_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &page_token_, lhs_arena,
      &other->page_token_, rhs_arena
  );
  swap(page_size_, other->page_size_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ListRobotsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter, &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once,
      file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[4]);
}

// ===================================================================

class ListRobotsResponse::_Internal {
 public:
};

ListRobotsResponse::ListRobotsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  robots_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListRobotsResponse)
}
ListRobotsResponse::ListRobotsResponse(const ListRobotsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      robots_(from.robots_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  next_page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    next_page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_next_page_token().empty()) {
    next_page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_next_page_token(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListRobotsResponse)
}

inline void ListRobotsResponse::SharedCtor() {
next_page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  next_page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ListRobotsResponse::~ListRobotsResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListRobotsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListRobotsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  next_page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListRobotsResponse::ArenaDtor(void* object) {
  ListRobotsResponse* _this = reinterpret_cast< ListRobotsResponse* >(object);
  (void)_this;
}
void ListRobotsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListRobotsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListRobotsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListRobotsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robots_.Clear();
  next_page_token_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListRobotsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string next_page_token = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_next_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListRobotsResponse.next_page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.rtc.RobotSummary robots = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_robots(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListRobotsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListRobotsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string next_page_token = 1;
  if (!this->_internal_next_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_next_page_token().data(), static_cast<int>(this->_internal_next_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListRobotsResponse.next_page_token");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_next_page_token(), target);
  }

  // repeated .carbon.rtc.RobotSummary robots = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_robots_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_robots(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListRobotsResponse)
  return target;
}

size_t ListRobotsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListRobotsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.rtc.RobotSummary robots = 2;
  total_size += 1UL * this->_internal_robots_size();
  for (const auto& msg : this->robots_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string next_page_token = 1;
  if (!this->_internal_next_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_next_page_token());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListRobotsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListRobotsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListRobotsResponse::GetClassData() const { return &_class_data_; }

void ListRobotsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListRobotsResponse *>(to)->MergeFrom(
      static_cast<const ListRobotsResponse &>(from));
}


void ListRobotsResponse::MergeFrom(const ListRobotsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListRobotsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  robots_.MergeFrom(from.robots_);
  if (!from._internal_next_page_token().empty()) {
    _internal_set_next_page_token(from._internal_next_page_token());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListRobotsResponse::CopyFrom(const ListRobotsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListRobotsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListRobotsResponse::IsInitialized() const {
  return true;
}

void ListRobotsResponse::InternalSwap(ListRobotsResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  robots_.InternalSwap(&other->robots_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &next_page_token_, lhs_arena,
      &other->next_page_token_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ListRobotsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter, &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once,
      file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[5]);
}

// ===================================================================

class RobotSummary::_Internal {
 public:
  static const ::carbon::rtc::LocationHistoryRecord& last_seen(const RobotSummary* msg);
};

const ::carbon::rtc::LocationHistoryRecord&
RobotSummary::_Internal::last_seen(const RobotSummary* msg) {
  return *msg->last_seen_;
}
RobotSummary::RobotSummary(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.RobotSummary)
}
RobotSummary::RobotSummary(const RobotSummary& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_serial().empty()) {
    serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serial(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_last_seen()) {
    last_seen_ = new ::carbon::rtc::LocationHistoryRecord(*from.last_seen_);
  } else {
    last_seen_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.RobotSummary)
}

inline void RobotSummary::SharedCtor() {
serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
last_seen_ = nullptr;
}

RobotSummary::~RobotSummary() {
  // @@protoc_insertion_point(destructor:carbon.rtc.RobotSummary)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RobotSummary::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete last_seen_;
}

void RobotSummary::ArenaDtor(void* object) {
  RobotSummary* _this = reinterpret_cast< RobotSummary* >(object);
  (void)_this;
}
void RobotSummary::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RobotSummary::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RobotSummary::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.RobotSummary)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  serial_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && last_seen_ != nullptr) {
    delete last_seen_;
  }
  last_seen_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RobotSummary::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string serial = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.RobotSummary.serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.LocationHistoryRecord last_seen = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_last_seen(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RobotSummary::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.RobotSummary)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string serial = 1;
  if (!this->_internal_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_serial().data(), static_cast<int>(this->_internal_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.RobotSummary.serial");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_serial(), target);
  }

  // .carbon.rtc.LocationHistoryRecord last_seen = 2;
  if (this->_internal_has_last_seen()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::last_seen(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.RobotSummary)
  return target;
}

size_t RobotSummary::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.RobotSummary)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string serial = 1;
  if (!this->_internal_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_serial());
  }

  // .carbon.rtc.LocationHistoryRecord last_seen = 2;
  if (this->_internal_has_last_seen()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *last_seen_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RobotSummary::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RobotSummary::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RobotSummary::GetClassData() const { return &_class_data_; }

void RobotSummary::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RobotSummary *>(to)->MergeFrom(
      static_cast<const RobotSummary &>(from));
}


void RobotSummary::MergeFrom(const RobotSummary& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.RobotSummary)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_serial().empty()) {
    _internal_set_serial(from._internal_serial());
  }
  if (from._internal_has_last_seen()) {
    _internal_mutable_last_seen()->::carbon::rtc::LocationHistoryRecord::MergeFrom(from._internal_last_seen());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RobotSummary::CopyFrom(const RobotSummary& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.RobotSummary)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RobotSummary::IsInitialized() const {
  return true;
}

void RobotSummary::InternalSwap(RobotSummary* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serial_, lhs_arena,
      &other->serial_, rhs_arena
  );
  swap(last_seen_, other->last_seen_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RobotSummary::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter, &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once,
      file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[6]);
}

// ===================================================================

class ListLocationHistoryRequest::_Internal {
 public:
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& start(const ListLocationHistoryRequest* msg);
  static const ::PROTOBUF_NAMESPACE_ID::Timestamp& end(const ListLocationHistoryRequest* msg);
};

const ::PROTOBUF_NAMESPACE_ID::Timestamp&
ListLocationHistoryRequest::_Internal::start(const ListLocationHistoryRequest* msg) {
  return *msg->start_;
}
const ::PROTOBUF_NAMESPACE_ID::Timestamp&
ListLocationHistoryRequest::_Internal::end(const ListLocationHistoryRequest* msg) {
  return *msg->end_;
}
void ListLocationHistoryRequest::clear_start() {
  if (GetArenaForAllocation() == nullptr && start_ != nullptr) {
    delete start_;
  }
  start_ = nullptr;
}
void ListLocationHistoryRequest::clear_end() {
  if (GetArenaForAllocation() == nullptr && end_ != nullptr) {
    delete end_;
  }
  end_ = nullptr;
}
ListLocationHistoryRequest::ListLocationHistoryRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListLocationHistoryRequest)
}
ListLocationHistoryRequest::ListLocationHistoryRequest(const ListLocationHistoryRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_page_token().empty()) {
    page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_page_token(), 
      GetArenaForAllocation());
  }
  robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_serial().empty()) {
    robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_serial(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_start()) {
    start_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.start_);
  } else {
    start_ = nullptr;
  }
  if (from._internal_has_end()) {
    end_ = new ::PROTOBUF_NAMESPACE_ID::Timestamp(*from.end_);
  } else {
    end_ = nullptr;
  }
  ::memcpy(&page_size_, &from.page_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&objective_id_) -
    reinterpret_cast<char*>(&page_size_)) + sizeof(objective_id_));
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListLocationHistoryRequest)
}

inline void ListLocationHistoryRequest::SharedCtor() {
page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&start_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&objective_id_) -
    reinterpret_cast<char*>(&start_)) + sizeof(objective_id_));
}

ListLocationHistoryRequest::~ListLocationHistoryRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListLocationHistoryRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListLocationHistoryRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  robot_serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete start_;
  if (this != internal_default_instance()) delete end_;
}

void ListLocationHistoryRequest::ArenaDtor(void* object) {
  ListLocationHistoryRequest* _this = reinterpret_cast< ListLocationHistoryRequest* >(object);
  (void)_this;
}
void ListLocationHistoryRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListLocationHistoryRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListLocationHistoryRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListLocationHistoryRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  page_token_.ClearToEmpty();
  robot_serial_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && start_ != nullptr) {
    delete start_;
  }
  start_ = nullptr;
  if (GetArenaForAllocation() == nullptr && end_ != nullptr) {
    delete end_;
  }
  end_ = nullptr;
  ::memset(&page_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&objective_id_) -
      reinterpret_cast<char*>(&page_size_)) + sizeof(objective_id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListLocationHistoryRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 page_size = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          page_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string page_token = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListLocationHistoryRequest.page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string robot_serial = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_robot_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListLocationHistoryRequest.robot_serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp start = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_start(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .google.protobuf.Timestamp end = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_end(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool desc = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          desc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool include_closest = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          include_closest_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 task_id = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          task_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 objective_id = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          objective_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListLocationHistoryRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListLocationHistoryRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 page_size = 1;
  if (this->_internal_page_size() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_page_size(), target);
  }

  // string page_token = 2;
  if (!this->_internal_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_page_token().data(), static_cast<int>(this->_internal_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListLocationHistoryRequest.page_token");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_page_token(), target);
  }

  // string robot_serial = 3;
  if (!this->_internal_robot_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_serial().data(), static_cast<int>(this->_internal_robot_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListLocationHistoryRequest.robot_serial");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_robot_serial(), target);
  }

  // .google.protobuf.Timestamp start = 4;
  if (this->_internal_has_start()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::start(this), target, stream);
  }

  // .google.protobuf.Timestamp end = 5;
  if (this->_internal_has_end()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::end(this), target, stream);
  }

  // bool desc = 6;
  if (this->_internal_desc() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_desc(), target);
  }

  // bool include_closest = 7;
  if (this->_internal_include_closest() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_include_closest(), target);
  }

  // uint64 task_id = 8;
  if (this->_internal_task_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(8, this->_internal_task_id(), target);
  }

  // uint64 objective_id = 9;
  if (this->_internal_objective_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(9, this->_internal_objective_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListLocationHistoryRequest)
  return target;
}

size_t ListLocationHistoryRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListLocationHistoryRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string page_token = 2;
  if (!this->_internal_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_page_token());
  }

  // string robot_serial = 3;
  if (!this->_internal_robot_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_serial());
  }

  // .google.protobuf.Timestamp start = 4;
  if (this->_internal_has_start()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *start_);
  }

  // .google.protobuf.Timestamp end = 5;
  if (this->_internal_has_end()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *end_);
  }

  // int32 page_size = 1;
  if (this->_internal_page_size() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_page_size());
  }

  // bool desc = 6;
  if (this->_internal_desc() != 0) {
    total_size += 1 + 1;
  }

  // bool include_closest = 7;
  if (this->_internal_include_closest() != 0) {
    total_size += 1 + 1;
  }

  // uint64 task_id = 8;
  if (this->_internal_task_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_task_id());
  }

  // uint64 objective_id = 9;
  if (this->_internal_objective_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_objective_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListLocationHistoryRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListLocationHistoryRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListLocationHistoryRequest::GetClassData() const { return &_class_data_; }

void ListLocationHistoryRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListLocationHistoryRequest *>(to)->MergeFrom(
      static_cast<const ListLocationHistoryRequest &>(from));
}


void ListLocationHistoryRequest::MergeFrom(const ListLocationHistoryRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListLocationHistoryRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_page_token().empty()) {
    _internal_set_page_token(from._internal_page_token());
  }
  if (!from._internal_robot_serial().empty()) {
    _internal_set_robot_serial(from._internal_robot_serial());
  }
  if (from._internal_has_start()) {
    _internal_mutable_start()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_start());
  }
  if (from._internal_has_end()) {
    _internal_mutable_end()->::PROTOBUF_NAMESPACE_ID::Timestamp::MergeFrom(from._internal_end());
  }
  if (from._internal_page_size() != 0) {
    _internal_set_page_size(from._internal_page_size());
  }
  if (from._internal_desc() != 0) {
    _internal_set_desc(from._internal_desc());
  }
  if (from._internal_include_closest() != 0) {
    _internal_set_include_closest(from._internal_include_closest());
  }
  if (from._internal_task_id() != 0) {
    _internal_set_task_id(from._internal_task_id());
  }
  if (from._internal_objective_id() != 0) {
    _internal_set_objective_id(from._internal_objective_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListLocationHistoryRequest::CopyFrom(const ListLocationHistoryRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListLocationHistoryRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListLocationHistoryRequest::IsInitialized() const {
  return true;
}

void ListLocationHistoryRequest::InternalSwap(ListLocationHistoryRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &page_token_, lhs_arena,
      &other->page_token_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_serial_, lhs_arena,
      &other->robot_serial_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ListLocationHistoryRequest, objective_id_)
      + sizeof(ListLocationHistoryRequest::objective_id_)
      - PROTOBUF_FIELD_OFFSET(ListLocationHistoryRequest, start_)>(
          reinterpret_cast<char*>(&start_),
          reinterpret_cast<char*>(&other->start_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ListLocationHistoryRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter, &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once,
      file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[7]);
}

// ===================================================================

class ListLocationHistoryResponse::_Internal {
 public:
  static const ::carbon::rtc::LocationHistoryRecordList& history(const ListLocationHistoryResponse* msg);
};

const ::carbon::rtc::LocationHistoryRecordList&
ListLocationHistoryResponse::_Internal::history(const ListLocationHistoryResponse* msg) {
  return *msg->history_;
}
ListLocationHistoryResponse::ListLocationHistoryResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.ListLocationHistoryResponse)
}
ListLocationHistoryResponse::ListLocationHistoryResponse(const ListLocationHistoryResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  next_page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    next_page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_next_page_token().empty()) {
    next_page_token_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_next_page_token(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_history()) {
    history_ = new ::carbon::rtc::LocationHistoryRecordList(*from.history_);
  } else {
    history_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.ListLocationHistoryResponse)
}

inline void ListLocationHistoryResponse::SharedCtor() {
next_page_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  next_page_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
history_ = nullptr;
}

ListLocationHistoryResponse::~ListLocationHistoryResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.ListLocationHistoryResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListLocationHistoryResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  next_page_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete history_;
}

void ListLocationHistoryResponse::ArenaDtor(void* object) {
  ListLocationHistoryResponse* _this = reinterpret_cast< ListLocationHistoryResponse* >(object);
  (void)_this;
}
void ListLocationHistoryResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListLocationHistoryResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListLocationHistoryResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.ListLocationHistoryResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  next_page_token_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && history_ != nullptr) {
    delete history_;
  }
  history_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListLocationHistoryResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string next_page_token = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_next_page_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.ListLocationHistoryResponse.next_page_token"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.LocationHistoryRecordList history = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_history(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListLocationHistoryResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.ListLocationHistoryResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string next_page_token = 1;
  if (!this->_internal_next_page_token().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_next_page_token().data(), static_cast<int>(this->_internal_next_page_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.ListLocationHistoryResponse.next_page_token");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_next_page_token(), target);
  }

  // .carbon.rtc.LocationHistoryRecordList history = 2;
  if (this->_internal_has_history()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::history(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.ListLocationHistoryResponse)
  return target;
}

size_t ListLocationHistoryResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.ListLocationHistoryResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string next_page_token = 1;
  if (!this->_internal_next_page_token().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_next_page_token());
  }

  // .carbon.rtc.LocationHistoryRecordList history = 2;
  if (this->_internal_has_history()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *history_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListLocationHistoryResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListLocationHistoryResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListLocationHistoryResponse::GetClassData() const { return &_class_data_; }

void ListLocationHistoryResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListLocationHistoryResponse *>(to)->MergeFrom(
      static_cast<const ListLocationHistoryResponse &>(from));
}


void ListLocationHistoryResponse::MergeFrom(const ListLocationHistoryResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.ListLocationHistoryResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_next_page_token().empty()) {
    _internal_set_next_page_token(from._internal_next_page_token());
  }
  if (from._internal_has_history()) {
    _internal_mutable_history()->::carbon::rtc::LocationHistoryRecordList::MergeFrom(from._internal_history());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListLocationHistoryResponse::CopyFrom(const ListLocationHistoryResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.ListLocationHistoryResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListLocationHistoryResponse::IsInitialized() const {
  return true;
}

void ListLocationHistoryResponse::InternalSwap(ListLocationHistoryResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &next_page_token_, lhs_arena,
      &other->next_page_token_, rhs_arena
  );
  swap(history_, other->history_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ListLocationHistoryResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_getter, &descriptor_table_proto_2frtc_2flocation_5fhistory_2eproto_once,
      file_level_metadata_proto_2frtc_2flocation_5fhistory_2eproto[8]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace rtc
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::rtc::RobotData* Arena::CreateMaybeMessage< ::carbon::rtc::RobotData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::RobotData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::LocationHistoryRecord* Arena::CreateMaybeMessage< ::carbon::rtc::LocationHistoryRecord >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::LocationHistoryRecord >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::LocationHistoryRecordList* Arena::CreateMaybeMessage< ::carbon::rtc::LocationHistoryRecordList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::LocationHistoryRecordList >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::LogLocationHistoryRequest* Arena::CreateMaybeMessage< ::carbon::rtc::LogLocationHistoryRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::LogLocationHistoryRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListRobotsRequest* Arena::CreateMaybeMessage< ::carbon::rtc::ListRobotsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListRobotsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListRobotsResponse* Arena::CreateMaybeMessage< ::carbon::rtc::ListRobotsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListRobotsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::RobotSummary* Arena::CreateMaybeMessage< ::carbon::rtc::RobotSummary >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::RobotSummary >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListLocationHistoryRequest* Arena::CreateMaybeMessage< ::carbon::rtc::ListLocationHistoryRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListLocationHistoryRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::ListLocationHistoryResponse* Arena::CreateMaybeMessage< ::carbon::rtc::ListLocationHistoryResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::ListLocationHistoryResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

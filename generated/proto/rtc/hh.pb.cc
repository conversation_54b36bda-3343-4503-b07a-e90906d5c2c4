// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/rtc/hh.proto

#include "proto/rtc/hh.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace rtc {
constexpr RobotRequiredState::RobotRequiredState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : stopped_state_(false){}
struct RobotRequiredStateDefaultTypeInternal {
  constexpr RobotRequiredStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RobotRequiredStateDefaultTypeInternal() {}
  union {
    RobotRequiredState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RobotRequiredStateDefaultTypeInternal _RobotRequiredState_default_instance_;
constexpr RobotStatusInfo::RobotStatusInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : state_(0)
{}
struct RobotStatusInfoDefaultTypeInternal {
  constexpr RobotStatusInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RobotStatusInfoDefaultTypeInternal() {}
  union {
    RobotStatusInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RobotStatusInfoDefaultTypeInternal _RobotStatusInfo_default_instance_;
constexpr SetRobotRequiredStateRequest::SetRobotRequiredStateRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , required_state_(nullptr)
  , timestamp_ms_(int64_t{0}){}
struct SetRobotRequiredStateRequestDefaultTypeInternal {
  constexpr SetRobotRequiredStateRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetRobotRequiredStateRequestDefaultTypeInternal() {}
  union {
    SetRobotRequiredStateRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetRobotRequiredStateRequestDefaultTypeInternal _SetRobotRequiredStateRequest_default_instance_;
constexpr GetRobotRequiredStateRequest::GetRobotRequiredStateRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0}){}
struct GetRobotRequiredStateRequestDefaultTypeInternal {
  constexpr GetRobotRequiredStateRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetRobotRequiredStateRequestDefaultTypeInternal() {}
  union {
    GetRobotRequiredStateRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetRobotRequiredStateRequestDefaultTypeInternal _GetRobotRequiredStateRequest_default_instance_;
constexpr GetRobotRequiredStateResponse::GetRobotRequiredStateResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : required_state_(nullptr)
  , timestamp_ms_(int64_t{0}){}
struct GetRobotRequiredStateResponseDefaultTypeInternal {
  constexpr GetRobotRequiredStateResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetRobotRequiredStateResponseDefaultTypeInternal() {}
  union {
    GetRobotRequiredStateResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetRobotRequiredStateResponseDefaultTypeInternal _GetRobotRequiredStateResponse_default_instance_;
}  // namespace rtc
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2frtc_2fhh_2eproto[5];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_proto_2frtc_2fhh_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2frtc_2fhh_2eproto = nullptr;

const uint32_t TableStruct_proto_2frtc_2fhh_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotRequiredState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotRequiredState, stopped_state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotStatusInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::RobotStatusInfo, state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SetRobotRequiredStateRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SetRobotRequiredStateRequest, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SetRobotRequiredStateRequest, robot_serial_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::SetRobotRequiredStateRequest, required_state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetRobotRequiredStateRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetRobotRequiredStateRequest, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetRobotRequiredStateRequest, robot_serial_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetRobotRequiredStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetRobotRequiredStateResponse, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::rtc::GetRobotRequiredStateResponse, required_state_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::rtc::RobotRequiredState)},
  { 7, -1, -1, sizeof(::carbon::rtc::RobotStatusInfo)},
  { 14, -1, -1, sizeof(::carbon::rtc::SetRobotRequiredStateRequest)},
  { 23, -1, -1, sizeof(::carbon::rtc::GetRobotRequiredStateRequest)},
  { 31, -1, -1, sizeof(::carbon::rtc::GetRobotRequiredStateResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_RobotRequiredState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_RobotStatusInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_SetRobotRequiredStateRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GetRobotRequiredStateRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::rtc::_GetRobotRequiredStateResponse_default_instance_),
};

const char descriptor_table_protodef_proto_2frtc_2fhh_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\022proto/rtc/hh.proto\022\ncarbon.rtc\032\033google"
  "/protobuf/empty.proto\"+\n\022RobotRequiredSt"
  "ate\022\025\n\rstopped_state\030\001 \001(\010\";\n\017RobotStatu"
  "sInfo\022(\n\005state\030\001 \001(\0162\031.carbon.rtc.HHStat"
  "eStatus\"\202\001\n\034SetRobotRequiredStateRequest"
  "\022\024\n\014timestamp_ms\030\001 \001(\003\022\024\n\014robot_serial\030\002"
  " \001(\t\0226\n\016required_state\030\003 \001(\0132\036.carbon.rt"
  "c.RobotRequiredState\"J\n\034GetRobotRequired"
  "StateRequest\022\024\n\014timestamp_ms\030\001 \001(\003\022\024\n\014ro"
  "bot_serial\030\002 \001(\t\"m\n\035GetRobotRequiredStat"
  "eResponse\022\024\n\014timestamp_ms\030\001 \001(\003\0226\n\016requi"
  "red_state\030\002 \001(\0132\036.carbon.rtc.RobotRequir"
  "edState*o\n\rHHStateStatus\022\016\n\nHH_UNKNOWN\020\000"
  "\022\017\n\013HH_DISABLED\020\001\022\022\n\016HH_OPERATIONAL\020\002\022\016\n"
  "\nHH_STOPPED\020\003\022\013\n\007HH_SAFE\020\004\022\014\n\010HH_ESTOP\020\005"
  "2\257\002\n\nRobotState\022Y\n\025SetRobotRequiredState"
  "\022(.carbon.rtc.SetRobotRequiredStateReque"
  "st\032\026.google.protobuf.Empty\022k\n\024GetNextReq"
  "uiredState\022(.carbon.rtc.GetRobotRequired"
  "StateRequest\032).carbon.rtc.GetRobotRequir"
  "edStateResponse\022Y\n\026RobotRequirementStrea"
  "m\022\033.carbon.rtc.RobotStatusInfo\032\036.carbon."
  "rtc.RobotRequiredState(\0010\001B\013Z\tproto/rtcb"
  "\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_proto_2frtc_2fhh_2eproto_deps[1] = {
  &::descriptor_table_google_2fprotobuf_2fempty_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2frtc_2fhh_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2frtc_2fhh_2eproto = {
  false, false, 927, descriptor_table_protodef_proto_2frtc_2fhh_2eproto, "proto/rtc/hh.proto", 
  &descriptor_table_proto_2frtc_2fhh_2eproto_once, descriptor_table_proto_2frtc_2fhh_2eproto_deps, 1, 5,
  schemas, file_default_instances, TableStruct_proto_2frtc_2fhh_2eproto::offsets,
  file_level_metadata_proto_2frtc_2fhh_2eproto, file_level_enum_descriptors_proto_2frtc_2fhh_2eproto, file_level_service_descriptors_proto_2frtc_2fhh_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2frtc_2fhh_2eproto_getter() {
  return &descriptor_table_proto_2frtc_2fhh_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2frtc_2fhh_2eproto(&descriptor_table_proto_2frtc_2fhh_2eproto);
namespace carbon {
namespace rtc {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HHStateStatus_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_proto_2frtc_2fhh_2eproto);
  return file_level_enum_descriptors_proto_2frtc_2fhh_2eproto[0];
}
bool HHStateStatus_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class RobotRequiredState::_Internal {
 public:
};

RobotRequiredState::RobotRequiredState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.RobotRequiredState)
}
RobotRequiredState::RobotRequiredState(const RobotRequiredState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  stopped_state_ = from.stopped_state_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.RobotRequiredState)
}

inline void RobotRequiredState::SharedCtor() {
stopped_state_ = false;
}

RobotRequiredState::~RobotRequiredState() {
  // @@protoc_insertion_point(destructor:carbon.rtc.RobotRequiredState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RobotRequiredState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RobotRequiredState::ArenaDtor(void* object) {
  RobotRequiredState* _this = reinterpret_cast< RobotRequiredState* >(object);
  (void)_this;
}
void RobotRequiredState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RobotRequiredState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RobotRequiredState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.RobotRequiredState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  stopped_state_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RobotRequiredState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool stopped_state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          stopped_state_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RobotRequiredState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.RobotRequiredState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool stopped_state = 1;
  if (this->_internal_stopped_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_stopped_state(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.RobotRequiredState)
  return target;
}

size_t RobotRequiredState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.RobotRequiredState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool stopped_state = 1;
  if (this->_internal_stopped_state() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RobotRequiredState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RobotRequiredState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RobotRequiredState::GetClassData() const { return &_class_data_; }

void RobotRequiredState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RobotRequiredState *>(to)->MergeFrom(
      static_cast<const RobotRequiredState &>(from));
}


void RobotRequiredState::MergeFrom(const RobotRequiredState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.RobotRequiredState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_stopped_state() != 0) {
    _internal_set_stopped_state(from._internal_stopped_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RobotRequiredState::CopyFrom(const RobotRequiredState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.RobotRequiredState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RobotRequiredState::IsInitialized() const {
  return true;
}

void RobotRequiredState::InternalSwap(RobotRequiredState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(stopped_state_, other->stopped_state_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RobotRequiredState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fhh_2eproto_getter, &descriptor_table_proto_2frtc_2fhh_2eproto_once,
      file_level_metadata_proto_2frtc_2fhh_2eproto[0]);
}

// ===================================================================

class RobotStatusInfo::_Internal {
 public:
};

RobotStatusInfo::RobotStatusInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.RobotStatusInfo)
}
RobotStatusInfo::RobotStatusInfo(const RobotStatusInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  state_ = from.state_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.RobotStatusInfo)
}

inline void RobotStatusInfo::SharedCtor() {
state_ = 0;
}

RobotStatusInfo::~RobotStatusInfo() {
  // @@protoc_insertion_point(destructor:carbon.rtc.RobotStatusInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RobotStatusInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RobotStatusInfo::ArenaDtor(void* object) {
  RobotStatusInfo* _this = reinterpret_cast< RobotStatusInfo* >(object);
  (void)_this;
}
void RobotStatusInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RobotStatusInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RobotStatusInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.RobotStatusInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  state_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RobotStatusInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.rtc.HHStateStatus state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_state(static_cast<::carbon::rtc::HHStateStatus>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RobotStatusInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.RobotStatusInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.rtc.HHStateStatus state = 1;
  if (this->_internal_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_state(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.RobotStatusInfo)
  return target;
}

size_t RobotStatusInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.RobotStatusInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc.HHStateStatus state = 1;
  if (this->_internal_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_state());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RobotStatusInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RobotStatusInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RobotStatusInfo::GetClassData() const { return &_class_data_; }

void RobotStatusInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RobotStatusInfo *>(to)->MergeFrom(
      static_cast<const RobotStatusInfo &>(from));
}


void RobotStatusInfo::MergeFrom(const RobotStatusInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.RobotStatusInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_state() != 0) {
    _internal_set_state(from._internal_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RobotStatusInfo::CopyFrom(const RobotStatusInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.RobotStatusInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RobotStatusInfo::IsInitialized() const {
  return true;
}

void RobotStatusInfo::InternalSwap(RobotStatusInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(state_, other->state_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RobotStatusInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fhh_2eproto_getter, &descriptor_table_proto_2frtc_2fhh_2eproto_once,
      file_level_metadata_proto_2frtc_2fhh_2eproto[1]);
}

// ===================================================================

class SetRobotRequiredStateRequest::_Internal {
 public:
  static const ::carbon::rtc::RobotRequiredState& required_state(const SetRobotRequiredStateRequest* msg);
};

const ::carbon::rtc::RobotRequiredState&
SetRobotRequiredStateRequest::_Internal::required_state(const SetRobotRequiredStateRequest* msg) {
  return *msg->required_state_;
}
SetRobotRequiredStateRequest::SetRobotRequiredStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.SetRobotRequiredStateRequest)
}
SetRobotRequiredStateRequest::SetRobotRequiredStateRequest(const SetRobotRequiredStateRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_serial().empty()) {
    robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_serial(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_required_state()) {
    required_state_ = new ::carbon::rtc::RobotRequiredState(*from.required_state_);
  } else {
    required_state_ = nullptr;
  }
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.SetRobotRequiredStateRequest)
}

inline void SetRobotRequiredStateRequest::SharedCtor() {
robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&required_state_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&timestamp_ms_) -
    reinterpret_cast<char*>(&required_state_)) + sizeof(timestamp_ms_));
}

SetRobotRequiredStateRequest::~SetRobotRequiredStateRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.SetRobotRequiredStateRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetRobotRequiredStateRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete required_state_;
}

void SetRobotRequiredStateRequest::ArenaDtor(void* object) {
  SetRobotRequiredStateRequest* _this = reinterpret_cast< SetRobotRequiredStateRequest* >(object);
  (void)_this;
}
void SetRobotRequiredStateRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetRobotRequiredStateRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetRobotRequiredStateRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.SetRobotRequiredStateRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_serial_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && required_state_ != nullptr) {
    delete required_state_;
  }
  required_state_ = nullptr;
  timestamp_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetRobotRequiredStateRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string robot_serial = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_robot_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.SetRobotRequiredStateRequest.robot_serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.RobotRequiredState required_state = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_required_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetRobotRequiredStateRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.SetRobotRequiredStateRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // string robot_serial = 2;
  if (!this->_internal_robot_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_serial().data(), static_cast<int>(this->_internal_robot_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.SetRobotRequiredStateRequest.robot_serial");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_robot_serial(), target);
  }

  // .carbon.rtc.RobotRequiredState required_state = 3;
  if (this->_internal_has_required_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::required_state(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.SetRobotRequiredStateRequest)
  return target;
}

size_t SetRobotRequiredStateRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.SetRobotRequiredStateRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot_serial = 2;
  if (!this->_internal_robot_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_serial());
  }

  // .carbon.rtc.RobotRequiredState required_state = 3;
  if (this->_internal_has_required_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *required_state_);
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetRobotRequiredStateRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetRobotRequiredStateRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetRobotRequiredStateRequest::GetClassData() const { return &_class_data_; }

void SetRobotRequiredStateRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetRobotRequiredStateRequest *>(to)->MergeFrom(
      static_cast<const SetRobotRequiredStateRequest &>(from));
}


void SetRobotRequiredStateRequest::MergeFrom(const SetRobotRequiredStateRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.SetRobotRequiredStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot_serial().empty()) {
    _internal_set_robot_serial(from._internal_robot_serial());
  }
  if (from._internal_has_required_state()) {
    _internal_mutable_required_state()->::carbon::rtc::RobotRequiredState::MergeFrom(from._internal_required_state());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetRobotRequiredStateRequest::CopyFrom(const SetRobotRequiredStateRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.SetRobotRequiredStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetRobotRequiredStateRequest::IsInitialized() const {
  return true;
}

void SetRobotRequiredStateRequest::InternalSwap(SetRobotRequiredStateRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_serial_, lhs_arena,
      &other->robot_serial_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SetRobotRequiredStateRequest, timestamp_ms_)
      + sizeof(SetRobotRequiredStateRequest::timestamp_ms_)
      - PROTOBUF_FIELD_OFFSET(SetRobotRequiredStateRequest, required_state_)>(
          reinterpret_cast<char*>(&required_state_),
          reinterpret_cast<char*>(&other->required_state_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SetRobotRequiredStateRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fhh_2eproto_getter, &descriptor_table_proto_2frtc_2fhh_2eproto_once,
      file_level_metadata_proto_2frtc_2fhh_2eproto[2]);
}

// ===================================================================

class GetRobotRequiredStateRequest::_Internal {
 public:
};

GetRobotRequiredStateRequest::GetRobotRequiredStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GetRobotRequiredStateRequest)
}
GetRobotRequiredStateRequest::GetRobotRequiredStateRequest(const GetRobotRequiredStateRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot_serial().empty()) {
    robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot_serial(), 
      GetArenaForAllocation());
  }
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GetRobotRequiredStateRequest)
}

inline void GetRobotRequiredStateRequest::SharedCtor() {
robot_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
timestamp_ms_ = int64_t{0};
}

GetRobotRequiredStateRequest::~GetRobotRequiredStateRequest() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GetRobotRequiredStateRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetRobotRequiredStateRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetRobotRequiredStateRequest::ArenaDtor(void* object) {
  GetRobotRequiredStateRequest* _this = reinterpret_cast< GetRobotRequiredStateRequest* >(object);
  (void)_this;
}
void GetRobotRequiredStateRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetRobotRequiredStateRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetRobotRequiredStateRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GetRobotRequiredStateRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_serial_.ClearToEmpty();
  timestamp_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetRobotRequiredStateRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string robot_serial = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_robot_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.rtc.GetRobotRequiredStateRequest.robot_serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetRobotRequiredStateRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GetRobotRequiredStateRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // string robot_serial = 2;
  if (!this->_internal_robot_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot_serial().data(), static_cast<int>(this->_internal_robot_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.rtc.GetRobotRequiredStateRequest.robot_serial");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_robot_serial(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GetRobotRequiredStateRequest)
  return target;
}

size_t GetRobotRequiredStateRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GetRobotRequiredStateRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot_serial = 2;
  if (!this->_internal_robot_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot_serial());
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetRobotRequiredStateRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetRobotRequiredStateRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetRobotRequiredStateRequest::GetClassData() const { return &_class_data_; }

void GetRobotRequiredStateRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetRobotRequiredStateRequest *>(to)->MergeFrom(
      static_cast<const GetRobotRequiredStateRequest &>(from));
}


void GetRobotRequiredStateRequest::MergeFrom(const GetRobotRequiredStateRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GetRobotRequiredStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot_serial().empty()) {
    _internal_set_robot_serial(from._internal_robot_serial());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetRobotRequiredStateRequest::CopyFrom(const GetRobotRequiredStateRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GetRobotRequiredStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetRobotRequiredStateRequest::IsInitialized() const {
  return true;
}

void GetRobotRequiredStateRequest::InternalSwap(GetRobotRequiredStateRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_serial_, lhs_arena,
      &other->robot_serial_, rhs_arena
  );
  swap(timestamp_ms_, other->timestamp_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetRobotRequiredStateRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fhh_2eproto_getter, &descriptor_table_proto_2frtc_2fhh_2eproto_once,
      file_level_metadata_proto_2frtc_2fhh_2eproto[3]);
}

// ===================================================================

class GetRobotRequiredStateResponse::_Internal {
 public:
  static const ::carbon::rtc::RobotRequiredState& required_state(const GetRobotRequiredStateResponse* msg);
};

const ::carbon::rtc::RobotRequiredState&
GetRobotRequiredStateResponse::_Internal::required_state(const GetRobotRequiredStateResponse* msg) {
  return *msg->required_state_;
}
GetRobotRequiredStateResponse::GetRobotRequiredStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.rtc.GetRobotRequiredStateResponse)
}
GetRobotRequiredStateResponse::GetRobotRequiredStateResponse(const GetRobotRequiredStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_required_state()) {
    required_state_ = new ::carbon::rtc::RobotRequiredState(*from.required_state_);
  } else {
    required_state_ = nullptr;
  }
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:carbon.rtc.GetRobotRequiredStateResponse)
}

inline void GetRobotRequiredStateResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&required_state_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&timestamp_ms_) -
    reinterpret_cast<char*>(&required_state_)) + sizeof(timestamp_ms_));
}

GetRobotRequiredStateResponse::~GetRobotRequiredStateResponse() {
  // @@protoc_insertion_point(destructor:carbon.rtc.GetRobotRequiredStateResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetRobotRequiredStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete required_state_;
}

void GetRobotRequiredStateResponse::ArenaDtor(void* object) {
  GetRobotRequiredStateResponse* _this = reinterpret_cast< GetRobotRequiredStateResponse* >(object);
  (void)_this;
}
void GetRobotRequiredStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetRobotRequiredStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetRobotRequiredStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.rtc.GetRobotRequiredStateResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && required_state_ != nullptr) {
    delete required_state_;
  }
  required_state_ = nullptr;
  timestamp_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetRobotRequiredStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.rtc.RobotRequiredState required_state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_required_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetRobotRequiredStateResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.rtc.GetRobotRequiredStateResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // .carbon.rtc.RobotRequiredState required_state = 2;
  if (this->_internal_has_required_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::required_state(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.rtc.GetRobotRequiredStateResponse)
  return target;
}

size_t GetRobotRequiredStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.rtc.GetRobotRequiredStateResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.rtc.RobotRequiredState required_state = 2;
  if (this->_internal_has_required_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *required_state_);
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetRobotRequiredStateResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetRobotRequiredStateResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetRobotRequiredStateResponse::GetClassData() const { return &_class_data_; }

void GetRobotRequiredStateResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetRobotRequiredStateResponse *>(to)->MergeFrom(
      static_cast<const GetRobotRequiredStateResponse &>(from));
}


void GetRobotRequiredStateResponse::MergeFrom(const GetRobotRequiredStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.rtc.GetRobotRequiredStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_required_state()) {
    _internal_mutable_required_state()->::carbon::rtc::RobotRequiredState::MergeFrom(from._internal_required_state());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetRobotRequiredStateResponse::CopyFrom(const GetRobotRequiredStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.rtc.GetRobotRequiredStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetRobotRequiredStateResponse::IsInitialized() const {
  return true;
}

void GetRobotRequiredStateResponse::InternalSwap(GetRobotRequiredStateResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetRobotRequiredStateResponse, timestamp_ms_)
      + sizeof(GetRobotRequiredStateResponse::timestamp_ms_)
      - PROTOBUF_FIELD_OFFSET(GetRobotRequiredStateResponse, required_state_)>(
          reinterpret_cast<char*>(&required_state_),
          reinterpret_cast<char*>(&other->required_state_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetRobotRequiredStateResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2frtc_2fhh_2eproto_getter, &descriptor_table_proto_2frtc_2fhh_2eproto_once,
      file_level_metadata_proto_2frtc_2fhh_2eproto[4]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace rtc
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::rtc::RobotRequiredState* Arena::CreateMaybeMessage< ::carbon::rtc::RobotRequiredState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::RobotRequiredState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::RobotStatusInfo* Arena::CreateMaybeMessage< ::carbon::rtc::RobotStatusInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::RobotStatusInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::SetRobotRequiredStateRequest* Arena::CreateMaybeMessage< ::carbon::rtc::SetRobotRequiredStateRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::SetRobotRequiredStateRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GetRobotRequiredStateRequest* Arena::CreateMaybeMessage< ::carbon::rtc::GetRobotRequiredStateRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GetRobotRequiredStateRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::rtc::GetRobotRequiredStateResponse* Arena::CreateMaybeMessage< ::carbon::rtc::GetRobotRequiredStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::rtc::GetRobotRequiredStateResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

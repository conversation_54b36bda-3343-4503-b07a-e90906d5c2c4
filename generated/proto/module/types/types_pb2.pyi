"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___Empty = Empty

class ModuleIdentity(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...
    serial: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        serial : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"serial",b"serial"]) -> None: ...
type___ModuleIdentity = ModuleIdentity

class ModuleIPs(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    mcb_ip: typing___Text = ...
    pc_ip: typing___Text = ...
    ipmi_ip: typing___Text = ...

    def __init__(self,
        *,
        mcb_ip : typing___Optional[typing___Text] = None,
        pc_ip : typing___Optional[typing___Text] = None,
        ipmi_ip : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ipmi_ip",b"ipmi_ip",u"mcb_ip",b"mcb_ip",u"pc_ip",b"pc_ip"]) -> None: ...
type___ModuleIPs = ModuleIPs

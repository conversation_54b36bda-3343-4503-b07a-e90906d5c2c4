// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/module/types/types.proto

#include "proto/module/types/types.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace module {
namespace types {
constexpr Empty::Empty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct EmptyDefaultTypeInternal {
  constexpr EmptyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EmptyDefaultTypeInternal() {}
  union {
    Empty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EmptyDefaultTypeInternal _Empty_default_instance_;
constexpr ModuleIdentity::ModuleIdentity(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(0u){}
struct ModuleIdentityDefaultTypeInternal {
  constexpr ModuleIdentityDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModuleIdentityDefaultTypeInternal() {}
  union {
    ModuleIdentity _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModuleIdentityDefaultTypeInternal _ModuleIdentity_default_instance_;
constexpr ModuleIPs::ModuleIPs(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : mcb_ip_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , pc_ip_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ipmi_ip_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ModuleIPsDefaultTypeInternal {
  constexpr ModuleIPsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModuleIPsDefaultTypeInternal() {}
  union {
    ModuleIPs _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModuleIPsDefaultTypeInternal _ModuleIPs_default_instance_;
}  // namespace types
}  // namespace module
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_proto_2fmodule_2ftypes_2ftypes_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_proto_2fmodule_2ftypes_2ftypes_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_proto_2fmodule_2ftypes_2ftypes_2eproto = nullptr;

const uint32_t TableStruct_proto_2fmodule_2ftypes_2ftypes_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::module::types::Empty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::module::types::ModuleIdentity, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::module::types::ModuleIdentity, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::module::types::ModuleIdentity, serial_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::module::types::ModuleIPs, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::module::types::ModuleIPs, mcb_ip_),
  PROTOBUF_FIELD_OFFSET(::carbon::module::types::ModuleIPs, pc_ip_),
  PROTOBUF_FIELD_OFFSET(::carbon::module::types::ModuleIPs, ipmi_ip_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::module::types::Empty)},
  { 6, -1, -1, sizeof(::carbon::module::types::ModuleIdentity)},
  { 14, -1, -1, sizeof(::carbon::module::types::ModuleIPs)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::module::types::_Empty_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::module::types::_ModuleIdentity_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::module::types::_ModuleIPs_default_instance_),
};

const char descriptor_table_protodef_proto_2fmodule_2ftypes_2ftypes_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\036proto/module/types/types.proto\022\023carbon"
  ".module.types\"\007\n\005Empty\",\n\016ModuleIdentity"
  "\022\n\n\002id\030\001 \001(\r\022\016\n\006serial\030\002 \001(\t\";\n\tModuleIP"
  "s\022\016\n\006mcb_ip\030\001 \001(\t\022\r\n\005pc_ip\030\002 \001(\t\022\017\n\007ipmi"
  "_ip\030\003 \001(\tB\024Z\022proto/module/typesb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto = {
  false, false, 199, descriptor_table_protodef_proto_2fmodule_2ftypes_2ftypes_2eproto, "proto/module/types/types.proto", 
  &descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto_once, nullptr, 0, 3,
  schemas, file_default_instances, TableStruct_proto_2fmodule_2ftypes_2ftypes_2eproto::offsets,
  file_level_metadata_proto_2fmodule_2ftypes_2ftypes_2eproto, file_level_enum_descriptors_proto_2fmodule_2ftypes_2ftypes_2eproto, file_level_service_descriptors_proto_2fmodule_2ftypes_2ftypes_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto_getter() {
  return &descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_proto_2fmodule_2ftypes_2ftypes_2eproto(&descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto);
namespace carbon {
namespace module {
namespace types {

// ===================================================================

class Empty::_Internal {
 public:
};

Empty::Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.module.types.Empty)
}
Empty::Empty(const Empty& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.module.types.Empty)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Empty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Empty::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata Empty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto_getter, &descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto_once,
      file_level_metadata_proto_2fmodule_2ftypes_2ftypes_2eproto[0]);
}

// ===================================================================

class ModuleIdentity::_Internal {
 public:
};

ModuleIdentity::ModuleIdentity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.module.types.ModuleIdentity)
}
ModuleIdentity::ModuleIdentity(const ModuleIdentity& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_serial().empty()) {
    serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serial(), 
      GetArenaForAllocation());
  }
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:carbon.module.types.ModuleIdentity)
}

inline void ModuleIdentity::SharedCtor() {
serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
id_ = 0u;
}

ModuleIdentity::~ModuleIdentity() {
  // @@protoc_insertion_point(destructor:carbon.module.types.ModuleIdentity)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModuleIdentity::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ModuleIdentity::ArenaDtor(void* object) {
  ModuleIdentity* _this = reinterpret_cast< ModuleIdentity* >(object);
  (void)_this;
}
void ModuleIdentity::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModuleIdentity::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModuleIdentity::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.module.types.ModuleIdentity)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  serial_.ClearToEmpty();
  id_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModuleIdentity::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string serial = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.module.types.ModuleIdentity.serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModuleIdentity::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.module.types.ModuleIdentity)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_id(), target);
  }

  // string serial = 2;
  if (!this->_internal_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_serial().data(), static_cast<int>(this->_internal_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.module.types.ModuleIdentity.serial");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_serial(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.module.types.ModuleIdentity)
  return target;
}

size_t ModuleIdentity::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.module.types.ModuleIdentity)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string serial = 2;
  if (!this->_internal_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_serial());
  }

  // uint32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModuleIdentity::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModuleIdentity::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModuleIdentity::GetClassData() const { return &_class_data_; }

void ModuleIdentity::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModuleIdentity *>(to)->MergeFrom(
      static_cast<const ModuleIdentity &>(from));
}


void ModuleIdentity::MergeFrom(const ModuleIdentity& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.module.types.ModuleIdentity)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_serial().empty()) {
    _internal_set_serial(from._internal_serial());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModuleIdentity::CopyFrom(const ModuleIdentity& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.module.types.ModuleIdentity)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModuleIdentity::IsInitialized() const {
  return true;
}

void ModuleIdentity::InternalSwap(ModuleIdentity* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serial_, lhs_arena,
      &other->serial_, rhs_arena
  );
  swap(id_, other->id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ModuleIdentity::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto_getter, &descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto_once,
      file_level_metadata_proto_2fmodule_2ftypes_2ftypes_2eproto[1]);
}

// ===================================================================

class ModuleIPs::_Internal {
 public:
};

ModuleIPs::ModuleIPs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.module.types.ModuleIPs)
}
ModuleIPs::ModuleIPs(const ModuleIPs& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  mcb_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    mcb_ip_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_mcb_ip().empty()) {
    mcb_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_mcb_ip(), 
      GetArenaForAllocation());
  }
  pc_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    pc_ip_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_pc_ip().empty()) {
    pc_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_pc_ip(), 
      GetArenaForAllocation());
  }
  ipmi_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    ipmi_ip_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_ipmi_ip().empty()) {
    ipmi_ip_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_ipmi_ip(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.module.types.ModuleIPs)
}

inline void ModuleIPs::SharedCtor() {
mcb_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  mcb_ip_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
pc_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  pc_ip_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ipmi_ip_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  ipmi_ip_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ModuleIPs::~ModuleIPs() {
  // @@protoc_insertion_point(destructor:carbon.module.types.ModuleIPs)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModuleIPs::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  mcb_ip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  pc_ip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ipmi_ip_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ModuleIPs::ArenaDtor(void* object) {
  ModuleIPs* _this = reinterpret_cast< ModuleIPs* >(object);
  (void)_this;
}
void ModuleIPs::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModuleIPs::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModuleIPs::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.module.types.ModuleIPs)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  mcb_ip_.ClearToEmpty();
  pc_ip_.ClearToEmpty();
  ipmi_ip_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModuleIPs::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string mcb_ip = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_mcb_ip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.module.types.ModuleIPs.mcb_ip"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string pc_ip = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_pc_ip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.module.types.ModuleIPs.pc_ip"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string ipmi_ip = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_ipmi_ip();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.module.types.ModuleIPs.ipmi_ip"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModuleIPs::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.module.types.ModuleIPs)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string mcb_ip = 1;
  if (!this->_internal_mcb_ip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_mcb_ip().data(), static_cast<int>(this->_internal_mcb_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.module.types.ModuleIPs.mcb_ip");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_mcb_ip(), target);
  }

  // string pc_ip = 2;
  if (!this->_internal_pc_ip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_pc_ip().data(), static_cast<int>(this->_internal_pc_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.module.types.ModuleIPs.pc_ip");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_pc_ip(), target);
  }

  // string ipmi_ip = 3;
  if (!this->_internal_ipmi_ip().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_ipmi_ip().data(), static_cast<int>(this->_internal_ipmi_ip().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.module.types.ModuleIPs.ipmi_ip");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_ipmi_ip(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.module.types.ModuleIPs)
  return target;
}

size_t ModuleIPs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.module.types.ModuleIPs)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string mcb_ip = 1;
  if (!this->_internal_mcb_ip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_mcb_ip());
  }

  // string pc_ip = 2;
  if (!this->_internal_pc_ip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_pc_ip());
  }

  // string ipmi_ip = 3;
  if (!this->_internal_ipmi_ip().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ipmi_ip());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModuleIPs::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModuleIPs::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModuleIPs::GetClassData() const { return &_class_data_; }

void ModuleIPs::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModuleIPs *>(to)->MergeFrom(
      static_cast<const ModuleIPs &>(from));
}


void ModuleIPs::MergeFrom(const ModuleIPs& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.module.types.ModuleIPs)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_mcb_ip().empty()) {
    _internal_set_mcb_ip(from._internal_mcb_ip());
  }
  if (!from._internal_pc_ip().empty()) {
    _internal_set_pc_ip(from._internal_pc_ip());
  }
  if (!from._internal_ipmi_ip().empty()) {
    _internal_set_ipmi_ip(from._internal_ipmi_ip());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModuleIPs::CopyFrom(const ModuleIPs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.module.types.ModuleIPs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModuleIPs::IsInitialized() const {
  return true;
}

void ModuleIPs::InternalSwap(ModuleIPs* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &mcb_ip_, lhs_arena,
      &other->mcb_ip_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &pc_ip_, lhs_arena,
      &other->pc_ip_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &ipmi_ip_, lhs_arena,
      &other->ipmi_ip_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ModuleIPs::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto_getter, &descriptor_table_proto_2fmodule_2ftypes_2ftypes_2eproto_once,
      file_level_metadata_proto_2fmodule_2ftypes_2ftypes_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace types
}  // namespace module
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::module::types::Empty* Arena::CreateMaybeMessage< ::carbon::module::types::Empty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::module::types::Empty >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::module::types::ModuleIdentity* Arena::CreateMaybeMessage< ::carbon::module::types::ModuleIdentity >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::module::types::ModuleIdentity >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::module::types::ModuleIPs* Arena::CreateMaybeMessage< ::carbon::module::types::ModuleIPs >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::module::types::ModuleIPs >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/module/types/types.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/module/types/types.proto',
  package='carbon.module.types',
  syntax='proto3',
  serialized_options=b'Z\022proto/module/types',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1eproto/module/types/types.proto\x12\x13\x63\x61rbon.module.types\"\x07\n\x05\x45mpty\",\n\x0eModuleIdentity\x12\n\n\x02id\x18\x01 \x01(\r\x12\x0e\n\x06serial\x18\x02 \x01(\t\";\n\tModuleIPs\x12\x0e\n\x06mcb_ip\x18\x01 \x01(\t\x12\r\n\x05pc_ip\x18\x02 \x01(\t\x12\x0f\n\x07ipmi_ip\x18\x03 \x01(\tB\x14Z\x12proto/module/typesb\x06proto3'
)




_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='carbon.module.types.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=55,
  serialized_end=62,
)


_MODULEIDENTITY = _descriptor.Descriptor(
  name='ModuleIdentity',
  full_name='carbon.module.types.ModuleIdentity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.module.types.ModuleIdentity.id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='serial', full_name='carbon.module.types.ModuleIdentity.serial', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=64,
  serialized_end=108,
)


_MODULEIPS = _descriptor.Descriptor(
  name='ModuleIPs',
  full_name='carbon.module.types.ModuleIPs',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='mcb_ip', full_name='carbon.module.types.ModuleIPs.mcb_ip', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pc_ip', full_name='carbon.module.types.ModuleIPs.pc_ip', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ipmi_ip', full_name='carbon.module.types.ModuleIPs.ipmi_ip', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=110,
  serialized_end=169,
)

DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['ModuleIdentity'] = _MODULEIDENTITY
DESCRIPTOR.message_types_by_name['ModuleIPs'] = _MODULEIPS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'proto.module.types.types_pb2'
  # @@protoc_insertion_point(class_scope:carbon.module.types.Empty)
  })
_sym_db.RegisterMessage(Empty)

ModuleIdentity = _reflection.GeneratedProtocolMessageType('ModuleIdentity', (_message.Message,), {
  'DESCRIPTOR' : _MODULEIDENTITY,
  '__module__' : 'proto.module.types.types_pb2'
  # @@protoc_insertion_point(class_scope:carbon.module.types.ModuleIdentity)
  })
_sym_db.RegisterMessage(ModuleIdentity)

ModuleIPs = _reflection.GeneratedProtocolMessageType('ModuleIPs', (_message.Message,), {
  'DESCRIPTOR' : _MODULEIPS,
  '__module__' : 'proto.module.types.types_pb2'
  # @@protoc_insertion_point(class_scope:carbon.module.types.ModuleIPs)
  })
_sym_db.RegisterMessage(ModuleIPs)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
